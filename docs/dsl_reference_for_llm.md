# Manim DSL v2 核心参考 (用于生成分镜视觉建议)

## 1. 引言

本文档为大型语言模型 (LLM) 生成 Manim DSL v2 **分镜视觉动效建议** 提供核心参考。你的任务是根据分镜内容，生成一系列描述视觉动作意图的字符串列表，并遵循本文档的规则指定动作类型、目标区域和关键参数。

## 2. 可用动作及核心参数

在生成 `视觉动效建议` 列表时，每条建议应清晰描述一个视觉动作的**意图**，并建议各参数的取值。

---

### 2.1 `animate_image`

*   **意图**: 显示图片，并可以配上注释或配套文案。
*   **核心参数**:
    *   `image_path` (string): 要显示的图片路径。
    *   `narration` (string): 旁白文本。
*   **常用可选参数**:
    *   `annotation` (string): 指定图片的markdown格式的注释或者配套文案，会以文本形式展示在图片右侧。

---

### 2.2 `highlight_content`

*   **意图**: 按顺序高亮一个或多个**已存在**的元素 (通过 `id` 引用)。**不清除内容。**
*   **核心参数**:
    *   `elements` (list[string]): 要高亮的元素 ID 列表。
    *   `narration` (string): 旁白文本。
*   **常用可选参数**:
    *   `lines` (string): **仅对代码元素有效**，指定高亮行号范围 (e.g., `"1-3,5"`)。会覆盖 `highlight_type`。
    *   `highlight_type` (string): 高亮类型 (e.g., `"box"`, `"underline"`, `"flash"`)，`lines` 优先。
    *   `color` (string): 高亮颜色。
    *   `duration_per_item` (number): 每个元素高亮持续秒数。

---

### 2.3 `animate_counter`

*   **意图**: 显示一个从起始值变化到目标值的数字计数器，或者增长曲线。
*   **核心参数**:
    *   `target_value` (number): 计数结束值。
    *   `narration` (string): 旁白文本。
    *   `counter_type` (string): 计数器类型 (e.g., `"counter"`, `"curve"`)。
*   **常用可选参数**:
    *   `start_value` (number): 起始值 (默认 0)。
    *   `target_value` (number): 计数结束值。
    *   `duration` (number): 动画时长 (默认 2.0 秒)。
    *   `label` (string): 数字左侧标签。
    *   `unit` (string): 数字右侧单位。
    *   `effect` (string): 结束效果 (e.g., `"zoom"`, `"flash"`)。

---

### 2.4 `animate_video`

*   **意图**: 播放视频。
*   **核心参数**:
    *   `video_path` (string): 视频文件路径。
    *   `narration` (string): 旁白文本。
*   **常用可选参数**:
    *   `overlay_text` (string): 叠加在视频上的文本 (用 `\n` 分行)。
    *   `overlay_animation_delay` (number): 叠加文本动画延迟 (默认 1.0 秒)。

---

### 2.5 `animate_side_by_side_comparison`

*   **意图**: 创建左右并排布局比较两种内容。
*   **核心参数**:
    *   `left_content` (string): 左侧内容 (Markdown 格式文本或图片路径)。
    *   `left_type` (string): 左侧内容类型 (`"markdown"`, `"image"`)。
    *   `right_content` (string): 右侧内容 (Markdown 格式文本或图片路径)。
    *   `right_type` (string): 右侧内容类型 (`"markdown"`, `"image"`)。
    *   `narration` (string): 旁白文本。
*   **常用可选参数**:
    *   `left_title` (string): 左侧标题。
    *   `right_title` (string): 右侧标题。
    *   `transition` (string): 入场动画 (e.g., `"fadeIn"`, `"slideUp"`)。

---

### 2.6 `animate_chart`

*   **意图**: 创建并动画展示图表 (条形图、折线图、雷达图)。
*   **核心参数**:
    *   `chart_type` (string): 图表类型 (`"bar"`, `"line"`, `"radar"`)。
    *   `data` (list[dict]): 图表数据，格式为 [{"key1": value1, "key2": value2, ...}, {"key1": value1, "key2": value2, ...}, ...]。
    *   `dataset_names` (list[string]): 数据集名称列表，与data中的dict对应，每个dict是一个数据集。
    *   `narration` (string): 旁白文本。
*   **常用可选参数**:
    *   `title` (string): 图表标题。
    *   `animation_style` (string): 动画风格 (e.g., `"grow"`, `"fadeIn"`)。
    *   `x_label` (string): x轴标签，`bar`和`line`图表有效。
    *   `y_label` (string): y轴标签，`bar`和`line`图表有效。

---

### 2.7 `animate_timeline`

*   **意图**: 创建水平时间轴动画。
*   **核心参数**:
    *   `events` (list[dict]): 事件列表，每个包含 `year` (必需, string) 和可选的 `title`, `description`, `emoji`, `color`。
    *   `narration` (string): 旁白文本。
    *   `title` (string): 时间轴标题。
*   **常用可选参数**:
    *   `subtitle` (string): 时间轴副标题。

---

### 2.8 `animate_architecture_diagram`

*   **意图**: 根据文本描述生成并播放架构图绘制动画视频。
*   **核心参数**:
    *   `content_description` (string): 描述架构图内容和动画步骤的文本。
    *   `narration` (string): 旁白文本。

---

### 2.9 `animate_markdown`

*   **意图**: 显示 Markdown 格式的内容。
*   **核心参数**:
    *   `content` (string): 要显示的 Markdown 格式的内容。
    *   `narration` (string): 旁白文本。
