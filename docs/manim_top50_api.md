# Manim Top-50 常用接口 & 常量速查表

> 面向 *LLM 自动生成 / 修改 Manim 脚本* 的工程化备忘单。
> 
> - **版本**：Manim Community ≥ v0.18 兼容。
> - **结构**：每条接口包含【定位】【核心参数】【典型代码】【易错点】。
> - **选型原则**：覆盖 2D/3D 绘制、动画、交互 95% 场景。

---

## 目录
1. 场景基类 (Scene 系)
2. 文本 / TeX
3. 形状几何
4. 坐标系 / 曲面
5. 动画类 (Animation 系)
6. Updater 与动态对象
7. 布尔运算 & 高级形状
8. 相机 / 相机帧
9. 辅助组件
10. 全局常量

<details>
<summary>点击展开完整条目列表 (50)</summary>

| # | 接口 | 所属模块 |
|---|-------|-----------|
|01|Scene|`manim.scene.scene`|
|02|MovingCameraScene|`manim.camera.moving_camera`|
|03|ThreeDScene|`manim.scene.three_d_scene`|
|04|ZoomedScene|`manim.scene.zoomed_scene`|
|05|Text|`manim.mobject.text.text_mobject`|
|06|MarkupText|〃|
|07|Tex|`manim.mobject.text.tex_mobject`|
|08|MathTex|〃|
|09|VGroup|`manim.mobject.types.vectorized_mobject`|
|10|VMobject.animate|`VMobject` 链式动画|
|11|Circle|`manim.mobject.geometry.arc`|
|12|Square|`manim.mobject.geometry.polygram`|
|13|Rectangle|〃|
|14|RoundedRectangle|〃|
|15|Ellipse|`manim.mobject.geometry.arc`|
|16|Polygon|`manim.mobject.geometry.polygram`|
|17|Line|`manim.mobject.geometry.line`|
|18|Arrow|〃|
|19|DoubleArrow|〃|
|20|Brace|`manim.mobject.svg.brace`|
|21|Dot|`manim.mobject.geometry.arc`|
|22|Axes|`manim.mobject.graphing.coordinate_systems`|
|23|NumberPlane|〃|
|24|ThreeDAxes|〃|
|25|Surface|`manim.mobject.three_d.three_dimensions`|
|26|ValueTracker|`manim.mobject.value_tracker`|
|27|always_redraw|`manim.animation.updaters`|
|28|add_updater|`Mobject` 方法|
|29|Write|`manim.animation.creation`|
|30|Create|〃|
|31|FadeIn / FadeOut|`manim.animation.fading`|
|32|GrowFromCenter|`manim.animation.growing`|
|33|GrowFromEdge|〃|
|34|MoveAlongPath|`manim.animation.movement`|
|35|Rotate / Rotating|`manim.animation.rotation`|
|36|Scale / ScaleInPlace|`manim.animation.transform`|
|37|Transform|〃|
|38|ReplacementTransform|〃|
|39|ApplyMatrix|〃|
|40|ApplyPointwiseFunctionToCenter|〃|
|41|LaggedStart|`manim.animation.composition`|
|42|AnimationGroup|〃|
|43|Indicate|`manim.animation.indication`|
|44|FocusOn|〃|
|45|SurroundingRectangle|`manim.mobject.geometry.shape_matchers`|
|46|BackgroundRectangle|〃|
|47|BraceBetweenLines|`manim.mobject.svg.brace`|
|48|ImageMobject|`manim.mobject.types.image_mobject`|
|49|Union / Intersection / Difference / Exclusion|`manim.mobject.geometry.boolean_ops`|
|50|Camera.frame.animate|`self.camera.frame` (Scene) |

</details>

---

## 1. 场景基类 (Scene 系)

### 1.1 `Scene`
- **定位**：所有 2D 场景基类，必须实现 `construct(self)`。
- **核心参数 / 属性**
  | 名称 | 类型 | 默认 | 描述 |
  |------|------|------|------|
  | `camera` | `Camera` | 自动 | 访问背景色、像素比例等，例如 `self.camera.background_color = BLACK`. |
  | `renderer` | 渲染器 | 自动 | 强制离屏渲染或自定义 pipe 用。 |
- **典型用法**
  ```python
  class Intro(Scene):
      def construct(self):
          txt = Text("Hello Manim", color=YELLOW)
          self.play(Write(txt))
          self.wait(1)
  ```
- **易错点**
  1. `self.play()` 只能接收 **Animation**，不能直接传入 `Text`。
  2. 忘记 `wait()` 可能导致结尾帧过快闪过。

### 1.2 `MovingCameraScene`
- **用途**：2D 相机可平移/缩放；常配合 `self.camera.frame.animate`。适用 “全景 -> 局部” 镜头。  
- **关键 API**
  | 方法 | 说明 |
  |-------|------|
  | `self.camera.frame.save_state()` / `Restore()` | 记录并还原视图。 |
  | `self.camera.frame.animate.move_to(target)` | 平移。 |
  | `scale(f)` | 缩放；`scale(0.5)` 放大 2 倍。 |
- **片段**
  ```python
  class PanZoom(MovingCameraScene):
      def construct(self):
          grid = NumberPlane()
          self.add(grid)
          dot = Dot(RIGHT*3+UP*2)
          self.add(dot)
          self.play(self.camera.frame.animate.scale(0.6).move_to(dot))
          self.wait()
          self.play(Restore(self.camera.frame))
  ```
- **坑**：缩放后若加新物体需注意其 **绝对像素大小** 会跟随相机比例。

### 1.3 `ThreeDScene`
- **用途**：开启 3D 相机；支持旋转、光源。  
- **核心方法**
  | 方法 | 说明 |
  |------|------|
  | `set_camera_orientation(phi, theta, focal_distance)` | 初始角度。|
  | `begin_ambient_camera_rotation(rate)` / `stop_ambient_camera_rotation()` | 自转。|
  | `move_camera(phi, theta, run_time)` | 动态切换。|
- **示例**
  ```python
  class SphereDemo(ThreeDScene):
      def construct(self):
          s = Surface(lambda u,v: np.array([
              np.cos(u)*np.cos(v),
              np.cos(u)*np.sin(v),
              np.sin(u)
          ]), u_range=[-PI/2, PI/2], v_range=[0, TAU])
          self.set_camera_orientation(phi=60*DEGREES, theta=30*DEGREES)
          self.play(Create(s))
          self.begin_ambient_camera_rotation(rate=0.1)
          self.wait(3)
  ```
- **坑**：未设置 `phi/theta` 时初始视角可能与平面场景一致导致看不见厚度；Surface 需较低 `resolution` 否则渲染缓慢。

### 1.4 `ZoomedScene`
- **用途**：创建放大镜视图，同时展示缩放窗口。
- **主要参数**：`zoom_factor`, `zoomed_display_height`, `zoomed_display_width`。
- **代码片段**
  ```python
  class Zoom(ZoomedScene):
      def construct(self):
          dot = Dot().shift(LEFT*3+UP)
          self.add(dot)
          frame = self.zoomed_camera.frame
          frame.move_to(dot)
          self.play(Create(frame))
          self.activate_zooming()
          self.play(self.get_zoomed_display_pop_out_animation())
          self.wait()
  ```
- **坑**：需要 `activate_zooming()` 之后才能调用 `get_zoomed_display_pop_out_animation()`。

---

## 2. 文本 / TeX

### 2.1 `Text`
- **定位**：快速渲染系统字体文字。支持 emoji / 中文（须字体）。
- **核心参数**
  | 名称 | 默认 | 说明 |
  |------|-------|------|
  | `font_size` | 48 | 字号 pt。|
  | `font` | None | 指定字体名；中文建议指定。|
  | `color` | WHITE | 文本颜色。|
  | `weight` | NORMAL | 可 `BOLD`。|
- **典型代码**
  ```python
  title = Text("字节对编码", font_size=60, weight=BOLD, color=YELLOW)
  title.set(max_width=6)  # 自动换行
  ```
- **易错**
  1. Windows / macOS 中文默认字体不一定存在；`Text("你好", font="SimHei")`。
  2. 超长行需要 `set(max_width=...)`，否则溢出画面。

### 2.2 `MarkupText`
- **功能**：支持简单 HTML 富文本，如 `<u>下划线</u>`、`<span fgcolor="red">`。
- **差异**：渲染较慢；不支持所有 CSS 样式。

### 2.3 `Tex` / `MathTex`
- **用途**：LaTeX 渲染 (标题 / 数学)。
- **核心参数**：`tex_template`, `font_size`, `fill_color`。
- **代码**
  ```python
  eq = MathTex(r"\sum_{n=1}^\infty 1/n^2 = \frac{\pi^2}{6}")
  ```
- **坑**：
  - 需本地 LaTeX (`xelatex`/`latex`)；在线环境需 docker 镜像。
  - 反斜杠需转义：Python 字符串中 `"\\alpha"` 或使用原字符串 `r"\alpha"`。

---

## 3. 形状几何 (Geometry)

> 本章涵盖最常用的平面/立体几何对象，创建后多与动画类配合使用。

### 3.1 `Circle`
- **模块**：`manim.mobject.geometry.arc`
- **作用**：生成圆；可设置半径、填充颜色等。
- **核心参数**
  | 名称 | 默认 | 说明 |
  |------|-------|------|
  | `radius` | 1 | 圆半径 (场景坐标)。|
  | `color` | WHITE | 描边颜色。|
  | `fill_opacity` | 0 | 填充透明度；>0 才会填充。|
  | `stroke_width` | 4 | 线宽。|
- **典型代码**
  ```python
  circle = Circle(radius=2, color=BLUE, fill_opacity=0.5)
  self.play(Create(circle))
  ```
- **常见坑**
  1. 忘记 `fill_opacity` 导致 `set_fill` 不生效。
  2. 大半径超出画布：可 `scale_to_fit_height()`。

### 3.2 `Square` / `Rectangle` / `RoundedRectangle`
- **模块**：`manim.mobject.geometry.polygram`
- **作用**：正方形、矩形、圆角矩形。
- **核心参数**（`Square` 除 `side_length` 其余同上）
  | 名称 | 默认 | 说明 |
  |------|-------|------|
  | `side_length` / `width, height` | 2 / 4,2 | 形状规格 |
  | `corner_radius` (RoundedRectangle) | 0 | 圆角半径 |
  | `fill_opacity` | 0 | 填充透明度 |
- **代码片段**
  ```python
  rect = RoundedRectangle(width=4, height=2, corner_radius=0.2,
                          fill_color=GREEN, fill_opacity=0.6)
  ```
- **易错**：`corner_radius` 过大可能 > min(width,height)/2 报错。

### 3.3 `Line` / `Arrow` / `DoubleArrow`
- **模块**：`manim.mobject.geometry.line`
- **作用**：直线与带箭头的线段。
- **关键参数**
  | 名称 | 默认 | 说明 |
  |------|-------|------|
  | `start`, `end` | LEFT, RIGHT | 两端坐标或向量。|
  | `buff` | 0.25 | 与起止点间距；为 0 线段正好到点。|
  | `stroke_width` | 4 | 线宽。|
  | `tip_length` | 0.35 | 箭头长度（Arrow）。|
- **典型代码**
  ```python
  arr = Arrow(LEFT*3, RIGHT*2, buff=0, color=YELLOW)
  line = Line(UP, DOWN, stroke_width=8)
  double_arr = DoubleArrow(LEFT*2, RIGHT*2, color=RED)
  ```
- **易错**：默认 `buff=0.25`，若画很短的箭头可能看不到；显式设 `buff=0`。

### 3.4 `Polygon` / `Ellipse` / `Dot`
- **模块**：`manim.mobject.geometry.polygram` / `arc`
- **作用**：多边形、椭圆、点标记。
- **核心参数**
  | 类型 | 参数 | 说明 |
  |------|------|------|
  | `Polygon` | `*vertices` | 顶点列表，首尾自动闭合 |
  | `Ellipse` | `width, height` | 椭圆宽高 |
  | `Dot` | `point, radius` | 位置，半径（默认0.08） |
- **典型代码**
  ```python
  triangle = Polygon(UP, LEFT, RIGHT, fill_opacity=0.5)
  ellipse = Ellipse(width=4, height=2, color=PURPLE)
  dot = Dot(RIGHT*2 + UP, radius=0.1, color=YELLOW)
  ```
- **坑**：
  - Polygon 顶点需 **二维/三维统一**；混用 z 值会错位。
  - Dot 半径缩放使用 `scale` 而非直接修改 `radius`。

### 3.5 `VGroup`
- **模块**：`manim.mobject.types.vectorized_mobject`
- **作用**：将多个 Mobject 组合成一个整体，支持批量操作。
- **核心方法**
  | 方法 | 说明 |
  |------|------|
  | `add(*mobjects)` | 添加对象到组 |
  | `remove(*mobjects)` | 移除对象 |
  | `arrange(direction, buff)` | 排列组内对象 |
  | `submobjects` | 访问子对象列表 |
- **典型代码**
  ```python
  group = VGroup(
      Circle(radius=0.5, color=RED),
      Square(side_length=1, color=BLUE),
      Text("Hello")
  ).arrange(RIGHT, buff=0.5)
  self.play(Create(group))
  ```
- **易错**：
  1. `arrange()` 会修改子对象位置，需在最终位置前调用。
  2. 对 VGroup 的变换会影响所有子对象。

### 3.6 `VMobject.animate`
- **模块**：`VMobject` 链式动画
- **作用**：对任意 VMobject 进行链式动画操作。
- **核心方法**
  | 方法 | 说明 |
  |------|------|
  | `animate.move_to(point)` | 移动到指定位置 |
  | `animate.shift(vector)` | 相对移动 |
  | `animate.rotate(angle)` | 旋转 |
  | `animate.scale(factor)` | 缩放 |
- **典型代码**
  ```python
  circle = Circle()
  self.play(
      circle.animate.move_to(RIGHT*2).scale(2).rotate(PI/4),
      run_time=2
  )
  ```
- **易错**：
  1. 链式调用顺序影响最终结果。
  2. 不能在同一 `play()` 中对同一对象使用多个 `animate`。

---

## 4. 坐标系 / 曲面

### 4.1 `Axes` / `NumberPlane`
- **模块**：`manim.mobject.graphing.coordinate_systems`
- **用途**：2D 直角坐标系；提供 `plot`, `get_graph_label`, `get_vertical_line` 等方法。
- **核心参数**
  | 参数 | 默认 | 说明 |
  |------|------|------|
  | `x_range` | `[-10,10,1]` | `[min,max,step]`；`step` 为刻度间隔。|
  | `y_range` | `[-6,6,1]` | 同上 |
  | `axis_config` | `{}` | 统一配置诸如 `color`, `include_numbers`。|
  | `tips` | True | 端点箭头。|
- **典型代码**
  ```python
  axes = Axes(x_range=[-3,3,1], y_range=[0,9,1], tips=False)
  parabola = axes.plot(lambda x: x**2, color=RED)
  label = axes.get_graph_label(parabola, "y=x^2")
  self.add(axes, parabola, label)
  ```
- **易错**：`get_vertical_line` 返回 **Line** 需 `self.play(Create(line))`；`plot` 默认平滑，对高频函数需 `use_smoothing=False`。

### 4.2 `ThreeDAxes` & `Surface`
- **模块**：`manim.mobject.graphing.coordinate_systems` / `three_d.three_dimensions`
- **用途**：3D 坐标系与参数曲面。
- **核心参数**
  | 类型 | 参数 | 说明 |
  |------|------|------|
  | `ThreeDAxes` | `x_range, y_range, z_range` | 三轴范围 |
  | `Surface` | `func, u_range, v_range` | 参数方程与参数域 |
- **典型代码**
  ```python
  axes = ThreeDAxes(x_range=[-3,3], y_range=[-3,3], z_range=[-2,2])
  surface = Surface(
      lambda u, v: np.array([u, v, u**2 + v**2]),
      u_range=[-2, 2], v_range=[-2, 2]
  )
  ```
- **坑**：高 `resolution` 指数级增面数；先草图后精细。

### 4.3 `MathTable`, `IntegerMatrix`, `MobjectTable`
- **模块**：`manim.mobject.table`
- **作用**：表格/矩阵可视化。
- **核心参数**
  | 类型 | 参数 | 说明 |
  |------|------|------|
  | `MathTable` | `table, include_outer_lines` | 数学表格 |
  | `IntegerMatrix` | `matrix, left_bracket, right_bracket` | 整数矩阵 |
  | `MobjectTable` | `table, row_labels, col_labels` | 通用表格 |
- **典型代码**
  ```python
  table = MathTable([
      ["x", "x^2"],
      ["1", "1"],
      ["2", "4"]
  ])
  matrix = IntegerMatrix([[1, 2], [3, 4]])
  ```
- **常见误区**：表格过宽需 `scale_to_fit_width`；矩阵元素太大需 `bracket_h_buff` 调整。

---

## 5. 动画类 (Animations)

> 所有动画继承 `Animation`，`self.play()` 调用。

### 5.1 `Write`
- **模块**：`manim.animation.creation`
- **作用**：模拟手写文字/线条效果。
- **核心参数**
  | 参数 | 默认 | 说明 |
  |------|------|------|
  | `mobject` | 必需 | 要书写的对象 |
  | `run_time` | 1 | 动画时长 |
  | `rate_func` | `smooth` | 速率函数 |
  | `lag_ratio` | 0.01 | 多元素间延迟比例 |
- **典型代码**
  ```python
  text = Text("Hello World")
  self.play(Write(text, run_time=2))
  
  # 对复杂对象
  equations = VGroup(*[MathTex(f"x^{i}") for i in range(3)])
  self.play(Write(equations, lag_ratio=0.5))
  ```
- **易错**：
  1. 对复杂 mobject (大矩阵) 较慢，可用 `FadeIn` 替代。
  2. `lag_ratio` 过大会导致动画时间过长。

### 5.2 `Create`
- **模块**：`manim.animation.creation`
- **作用**：沿轮廓描边创建对象。
- **核心参数**
  | 参数 | 默认 | 说明 |
  |------|------|------|
  | `mobject` | 必需 | 要创建的对象 |
  | `run_time` | 1 | 动画时长 |
  | `rate_func` | `smooth` | 速率函数 |
- **典型代码**
  ```python
  circle = Circle(radius=2, color=BLUE)
  square = Square(side_length=3, color=RED)
  self.play(Create(circle))
  self.play(Create(square))
  ```
- **易错**：
  1. 仅适合线条或边界清晰对象。
  2. 填充对象建议用 `GrowFromCenter`。

### 5.3 `FadeIn` / `FadeOut`
- **模块**：`manim.animation.fading`
- **作用**：淡入淡出效果。
- **核心参数**
  | 参数 | 默认 | 说明 |
  |------|------|------|
  | `mobject` | 必需 | 目标对象 |
  | `shift` | ORIGIN | 滑动方向 |
  | `scale` | 1 | 缩放系数 |
  | `run_time` | 1 | 动画时长 |
- **典型代码**
  ```python
  text = Text("Fade In")
  self.play(FadeIn(text, shift=UP))
  self.wait(1)
  self.play(FadeOut(text, shift=DOWN, scale=0.5))
  ```
- **易错**：
  1. `shift` 和 `scale` 可组合使用。
  2. `FadeOut` 后对象仍在场景中，需 `remove()` 彻底删除。

### 5.4 `GrowFromCenter` / `GrowFromEdge`
- **模块**：`manim.animation.growing`
- **作用**：从中心或边缘缩放进入。
- **核心参数**
  | 参数 | 默认 | 说明 |
  |------|------|------|
  | `mobject` | 必需 | 目标对象 |
  | `point_color` | None | 生长点颜色 |
  | `edge` | LEFT | 生长边缘（GrowFromEdge） |
- **典型代码**
  ```python
  circle = Circle(radius=2, fill_opacity=0.5)
  square = Square(side_length=2, fill_opacity=0.5)
  
  self.play(GrowFromCenter(circle))
  self.play(GrowFromEdge(square, edge=LEFT))
  ```
- **易错**：
  1. `GrowFromEdge` 需指定方向常量。
  2. 对线条对象效果不明显。

### 5.5 `Transform` / `ReplacementTransform`
- **模块**：`manim.animation.transform`
- **作用**：对象间变换。
- **核心参数**
  | 参数 | 默认 | 说明 |
  |------|------|------|
  | `mobject` | 必需 | 源对象 |
  | `target_mobject` | 必需 | 目标对象 |
  | `run_time` | 1 | 动画时长 |
  | `path_func` | `straight_path` | 变换路径 |
- **典型代码**
  ```python
  # Transform: 保留原对象
  text1 = Text("Hello")
  text2 = Text("World")
  self.play(Transform(text1, text2))
  
  # ReplacementTransform: 替换为新对象
  circle = Circle()
  square = Square()
  self.play(ReplacementTransform(circle, square))
  ```
- **易错**：
  1. A、B 必须顶点数一致或可内插，否则报 `UnmatchedMobjectError`。
  2. `Transform` 保留原对象引用，`ReplacementTransform` 替换。

### 5.6 `Rotate` / `Rotating`
- **模块**：`manim.animation.rotation`
- **作用**：旋转动画。
- **核心参数**
  | 参数 | 默认 | 说明 |
  |------|------|------|
  | `mobject` | 必需 | 旋转对象 |
  | `angle` | PI/2 | 旋转角度 |
  | `about_point` | ORIGIN | 旋转中心 |
  | `axis` | OUT | 旋转轴（3D） |
- **典型代码**
  ```python
  square = Square()
  # 绕中心旋转
  self.play(Rotate(square, PI/2))
  
  # 绕指定点旋转
  self.play(Rotate(square, PI, about_point=RIGHT))
  
  # 持续旋转
  self.play(Rotating(square, radians=2*PI, run_time=3))
  ```
- **易错**：
  1. 默认 `about_point=ORIGIN`；某些对象期望 `about_edge`。
  2. `Rotating` 用于持续旋转，`Rotate` 用于一次性旋转。

### 5.7 `Scale` / `ScaleInPlace`
- **模块**：`manim.animation.transform`
- **作用**：缩放动画。
- **核心参数**
  | 参数 | 默认 | 说明 |
  |------|------|------|
  | `mobject` | 必需 | 缩放对象 |
  | `scale_factor` | 2 | 缩放系数 |
  | `about_point` | ORIGIN | 缩放中心 |
- **典型代码**
  ```python
  circle = Circle()
  # 围绕原点缩放
  self.play(Scale(circle, 2))
  
  # 围绕自身中心缩放
  self.play(ScaleInPlace(circle, 0.5))
  ```
- **易错**：
  1. `ScaleInPlace` 围绕对象中心，`Scale` 围绕指定点。
  2. 与 `scale()` 方法不同，后者立即生效而非动画。

### 5.8 `MoveAlongPath`
- **模块**：`manim.animation.movement`
- **作用**：沿路径移动对象。
- **核心参数**
  | 参数 | 默认 | 说明 |
  |------|------|------|
  | `mobject` | 必需 | 移动对象 |
  | `path` | 必需 | 路径（VMobject） |
  | `run_time` | 1 | 动画时长 |
  | `rate_func` | `smooth` | 速率函数 |
- **典型代码**
  ```python
  dot = Dot()
  path = Arc(radius=2, angle=PI)
  self.play(MoveAlongPath(dot, path, run_time=2))
  
  # 复杂路径
  bezier = CubicBezier(LEFT, UP, RIGHT, DOWN)
  self.play(MoveAlongPath(dot, bezier))
  ```
- **易错**：
  1. Path 必须是 `VMobject`。
  2. `run_time` 决定移动速度。

### 5.9 `ApplyMatrix` / `ApplyPointwiseFunctionToCenter`
- **模块**：`manim.animation.transform`
- **作用**：矩阵变换和点函数变换。
- **核心参数**
  | 类型 | 参数 | 说明 |
  |------|------|------|
  | `ApplyMatrix` | `matrix, mobject` | 变换矩阵 |
  | `ApplyPointwiseFunction` | `function, mobject` | 点变换函数 |
- **典型代码**
  ```python
  # 矩阵变换
  square = Square()
  matrix = [[1, 0.5], [0, 1]]  # 剪切变换
  self.play(ApplyMatrix(matrix, square))
  
  # 点函数变换
  def wave_func(point):
      return point + 0.1 * np.sin(2 * point[0]) * UP
  
  grid = NumberPlane()
  self.play(ApplyPointwiseFunction(wave_func, grid))
  ```
- **易错**：
  1. 矩阵必须是 2x2 或 3x3。
  2. 点函数必须返回相同维度的点。

### 5.10 `LaggedStart` / `AnimationGroup`
- **模块**：`manim.animation.composition`
- **作用**：动画组合与延迟。
- **核心参数**
  | 类型 | 参数 | 说明 |
  |------|------|------|
  | `LaggedStart` | `*animations, lag_ratio` | 串行动画 |
  | `AnimationGroup` | `*animations` | 并行动画 |
- **典型代码**
  ```python
  # 串行动画
  squares = VGroup(*[Square().shift(i*RIGHT) for i in range(3)])
  self.play(LaggedStart(
      *[Create(sq) for sq in squares],
      lag_ratio=0.5
  ))
  
  # 并行动画
  circle = Circle()
  text = Text("Hello")
  self.play(AnimationGroup(
      Create(circle),
      Write(text.next_to(circle, DOWN))
  ))
  ```
- **易错**：
  1. `LaggedStart` 中每个子动画 `run_time` 共享。
  2. 常用 `lag_ratio=0.1` 避免过长延迟。

### 5.11 `Indicate` / `FocusOn`
- **模块**：`manim.animation.indication`
- **作用**：强调和聚焦动画。
- **核心参数**
  | 类型 | 参数 | 说明 |
  |------|------|------|
  | `Indicate` | `mobject, color, scale_factor` | 强调闪烁 |
  | `FocusOn` | `focus_point, opacity, color` | 聚焦点 |
- **典型代码**
  ```python
  text = Text("Important!")
  self.play(Indicate(text, color=RED, scale_factor=1.2))
  
  # 聚焦特定点
  dot = Dot(RIGHT*2)
  self.play(FocusOn(dot.get_center()))
  ```
- **易错**：
  1. `Indicate` 默认红色；调整 `color` 与场景对比。
  2. `FocusOn` 需要传入坐标点而非对象。

---

## 6. Updater 与动态对象

### 6.1 `ValueTracker` & `add_updater`
- **模块**：`manim.mobject.value_tracker` / `Mobject` 方法
- **用法**：驱动属性随时间变化。
- **核心参数**
  | 类型 | 参数 | 说明 |
  |------|------|------|
  | `ValueTracker` | `value` | 初始值 |
  | `add_updater` | `update_function` | 更新函数 |
- **典型代码**
  ```python
  t = ValueTracker(0)
  dot = Dot()
  dot.add_updater(lambda m: m.move_to(RIGHT * t.get_value()))
  
  self.add(dot)
  self.play(t.animate.set_value(3), run_time=2)
  ```
- **坑**：
  1. Updater 会一直执行直到 `remove_updater` 或场景结束。
  2. `ValueTracker` 初始值必须可浮点化。

### 6.2 `always_redraw`
- **模块**：`manim.animation.updaters`
- **功能**：每帧重新生成对象（适合依赖外部变量尺寸）。
- **典型代码**
  ```python
  dot = Dot()
  line = always_redraw(lambda: Line(ORIGIN, dot.get_center()))
  
  self.add(dot, line)
  self.play(dot.animate.move_to(RIGHT*3))
  ```
- **坑**：内部函数需返回 **Mobject**，不能 `return None`。

### 6.3 `UpdateFromFunc`, `UpdateFromAlphaFunc`
- **模块**：`manim.animation.updaters`
- **描述**：对现有 mobject 逐帧变形或按补间值更新。
- **核心参数**
  | 类型 | 参数 | 说明 |
  |------|------|------|
  | `UpdateFromFunc` | `mobject, update_function` | 基于时间更新 |
  | `UpdateFromAlphaFunc` | `mobject, update_function` | 基于进度更新 |
- **典型代码**
  ```python
  # 基于时间
  def update_func(mob, dt):
      mob.rotate(dt * PI/2)
  
  square = Square()
  self.play(UpdateFromFunc(square, update_func), run_time=2)
  
  # 基于进度
  def alpha_func(mob, alpha):
      mob.set_opacity(alpha)
  
  circle = Circle()
  self.play(UpdateFromAlphaFunc(circle, alpha_func))
  ```
- **易错**：`dt` 与 `alpha` 参数不同；不要混用。

---

## 7. 布尔运算 & 高级形状

### 7.1 `Union / Intersection / Difference / Exclusion`
- **模块**：`manim.mobject.geometry.boolean_ops`
- **用途**：几何布尔运算，返回新 `VMobject`。
- **典型代码**
  ```python
  circle = Circle(radius=1).shift(LEFT*0.5)
  square = Square(side_length=1.5).shift(RIGHT*0.5)
  
  union = Union(circle, square, color=BLUE, fill_opacity=0.5)
  intersection = Intersection(circle, square, color=RED, fill_opacity=0.5)
  difference = Difference(circle, square, color=GREEN, fill_opacity=0.5)
  exclusion = Exclusion(circle, square, color=YELLOW, fill_opacity=0.5)
  ```
- **坑**：
  - 输入对象应为闭合路径；开口线会抛异常。
  - 结果复杂度高，渲染时间随顶点数激增。

### 7.2 `BraceBetweenLines` / `Brace`
- **模块**：`manim.mobject.svg.brace`
- **功能**：在两条线或对象之间自动生成括号。
- **核心参数**
  | 类型 | 参数 | 说明 |
  |------|------|------|
  | `Brace` | `mobject, direction` | 对象的括号 |
  | `BraceBetweenLines` | `line1, line2` | 两线间括号 |
- **典型代码**
  ```python
  # 对象括号
  square = Square()
  brace = Brace(square, DOWN)
  label = brace.get_text("Side Length")
  
  # 线间括号
  line1 = Line(LEFT, RIGHT).shift(UP)
  line2 = Line(LEFT, RIGHT).shift(DOWN)
  brace = BraceBetweenLines(line1, line2)
  ```
- **易错**：线段需要平行；斜率相同但不重叠会位置怪异。

### 7.3 `SurroundingRectangle` / `BackgroundRectangle`
- **模块**：`manim.mobject.geometry.shape_matchers`
- **用途**：高亮或背景底色。
- **核心参数**
  | 参数 | 默认 | 说明 |
  |------|------|------|
  | `mobject` | 必需 | 目标对象 |
  | `buff` | 0.25 | 留白距离 |
  | `color` | WHITE | 边框颜色 |
  | `stroke_width` | 2 | 线宽 |
- **典型代码**
  ```python
  text = Text("Highlight Me")
  rect = SurroundingRectangle(text, color=YELLOW, buff=0.1)
  bg = BackgroundRectangle(text, color=BLACK, fill_opacity=0.8)
  
  self.add(bg, text, rect)
  ```
- **坑**：目标动态变化需 `always_redraw`。

---

## 8. 相机 / 帧

### 8.1 `Camera.frame.animate`
- **模块**：`self.camera.frame` (Scene)
- **功能**：直接对相机帧做动画（平移/缩放/旋转）。
- **核心方法**
  | 方法 | 说明 |
  |------|------|
  | `animate.move_to(point)` | 移动相机 |
  | `animate.scale(factor)` | 缩放视野 |
  | `save_state()` / `restore()` | 保存/恢复状态 |
- **典型代码**
  ```python
  # 需要 MovingCameraScene
  dot = Dot(RIGHT*3)
  self.camera.frame.save_state()
  self.play(self.camera.frame.animate.scale(0.5).move_to(dot))
  self.wait(1)
  self.play(Restore(self.camera.frame))
  ```
- **坑**：缩放后需要还原：`self.camera.frame.save_state()` + `Restore()`。

### 8.2 `begin_ambient_camera_rotation`
- **模块**：`ThreeDScene` 方法
- **用途**：让 3D 相机持续旋转。
- **核心参数**
  | 参数 | 默认 | 说明 |
  |------|------|------|
  | `rate` | 0.02 | 旋转速率 |
  | `about` | ORIGIN | 旋转中心 |
- **典型代码**
  ```python
  # 在 ThreeDScene 中
  sphere = Sphere()
  self.add(sphere)
  self.begin_ambient_camera_rotation(rate=0.1)
  self.wait(5)
  self.stop_ambient_camera_rotation()
  ```
- **坑**：长时旋转易造成视频压缩模糊，帧率需适当提高。

### 8.3 `ZoomedScene` 相关
- **模块**：`ZoomedScene` 方法
- **功能**：缩放场景相关动画。
- **核心方法**
  | 方法 | 说明 |
  |------|------|
  | `activate_zooming()` | 激活缩放 |
  | `get_zoomed_display_pop_out_animation()` | 弹出动画 |
  | `get_zoom_in_animation()` | 放大动画 |
- **典型代码**
  ```python
  # 在 ZoomedScene 中
  self.activate_zooming()
  self.play(self.get_zoomed_display_pop_out_animation())
  ```
- **易错**：必须先 `activate_zooming()` 并添加缩放帧。

---

## 9. 辅助组件

### 9.1 `ImageMobject`
- **模块**：`manim.mobject.types.image_mobject`
- **作用**：显示图片或 ndarray。
- **核心参数**
  | 参数 | 默认 | 说明 |
  |------|------|------|
  | `filename` | 必需 | 图片文件路径 |
  | `height` | None | 显示高度 |
  | `width` | None | 显示宽度 |
- **典型代码**
  ```python
  # 从文件加载
  img = ImageMobject("path/to/image.png")
  img.scale_to_fit_height(4)
  self.add(img)
  
  # 从 numpy 数组
  import numpy as np
  array = np.random.rand(100, 100, 3)
  img = ImageMobject(array)
  ```
- **坑**：
  - 路径错误无提示，渲染时空白。
  - 大图需 `.scale()` 适配帧。

### 9.2 `Code` / `SVGMobject` / `TexturedSurface`
- **模块**：`manim.mobject.text.code_mobject` / `svg.svg_mobject` / `three_d.three_dimensions`
- **用途**：代码高亮、SVG 显示、贴图曲面。
- **核心参数**
  | 类型 | 参数 | 说明 |
  |------|------|------|
  | `Code` | `code, language, style` | 代码字符串、语言、样式 |
  | `SVGMobject` | `file_name, height, width` | SVG 文件路径 |
  | `TexturedSurface` | `uv_surface, image_file` | 曲面与贴图 |
- **典型代码**
  ```python
  # 代码高亮
  code = Code(
      code="def hello():\n    print('Hello, Manim!')",
      language="python",
      style="monokai"
  )
  
  # SVG 显示
  svg = SVGMobject("logo.svg").scale(2)
  
  # 贴图曲面（3D 场景）
  surface = Surface(lambda u, v: [u, v, 0])
  textured = TexturedSurface(surface, "texture.jpg")
  ```
- **易错**：
  - SVG 必须路径闭合。
  - 代码高亮需 `language=` 正确。
  - 贴图需要在 `ThreeDScene` 中使用。

---

## 10. 全局常量速查

### 10.1 方向 / 位置向量
| 常量 | 值 | 说明 |
|-------|--------------------|------|
| `ORIGIN` | `[0,0,0]` | 场景中心 |
| `CENTER` | 同 ORIGIN | 同义 |
| `UP` | `[0,1,0]` | 上 |
| `DOWN` | `[0,-1,0]` | 下 |
| `LEFT` | `[-1,0,0]` | 左 |
| `RIGHT` | `[1,0,0]` | 右 |
| `IN` | `[0,0,-1]` | 屏内 |
| `OUT` | `[0,0,1]` | 屏外 |
| `UL` / `UR` | `UP+LEFT`, `UP+RIGHT` | 左上 / 右上 |
| `DL` / `DR` | `DOWN+LEFT`, `DOWN+RIGHT` | 左下 / 右下 |

### 10.2 角度 / 时间
| 常量 | 描述 |
|-------|------|
| `PI` | π |
| `TAU` | 2π |
| `DEGREES` | 度→弧度比例 (π/180) |

### 10.3 颜色 (内置常用)
`WHITE, BLACK, GREY_A–E, RED_A–E, GREEN_A–E, BLUE_A–E, YELLOW, ORANGE, PURPLE, PINK, MAROON, GOLD, TEAL` 等。每种 *_A → *_E* 表示从浅到深 5 个梯度。

> **自定义**：`color="#3498DB"` 可直接用 HEX。

---

## 结尾建议
- 调用前查阅"易错点"可减少 80% 渲染报错。
- 对大对象使用 `set_z_index()` 控制遮挡层级。
- 渲染长动画 (`>30s`) 时可加 `--fps 30 --movie_writer ffmpeg` 调优。

> 文档生成日期：2024年 