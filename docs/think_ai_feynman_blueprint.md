# 愿景定位
## 愿景
    通过AI费曼，对任何复杂知识，都能快速生成易懂易记住的短视频
## 目标
    解决视频知识创作难，视频消费互动弱的问题，让知识生产和消费能无缝衔接，形成交互式的消费
## 理想态：
    -任何知识来源: 无论有素材无素材，无论什么体裁视频、图片、文字，语音，还是链接，无论什么题材，数理化还是政史地生还是专业知识，公司业务文档，是最新的还是经典的，是通用还是专业
    -任何内容诉求:知识的洞察、梳理、关联、解释、延展、提炼、剪辑等
    -易懂易记住: 以费曼为原则加工内容，便于易懂，通过恰当的可视化方式呈现便于理解和记忆
    -快速生成: 有所想就有所得, 和消费内容速度差不多，比如30s

# 未来技术体系演进
技术始终围绕：速度、费曼、动效展开
技术前瞻性：具有足够鲁棒性，大模型能力进化后，框架反而要能如虎添翼，弹性转移重点
新技术关注：实时关注相关大模型技术的进展
## 技术体系框壁垒
1 费曼的灵魂
  1.1 自建知识库，做关联、延展，对比，比如论文的认知结论之间图谱
  1.2 内容的稀缺性，时效性，比如AI知识体系，名师讲解体系，Github项目，前沿论文的解读
  1.3 用户个性化的理解，最适合的费曼方式，结合生成方式的个性化推荐
  1.4 丰富的tool，作为费曼Agent内容的丝滑完善，比如思维模型、对比类比举例方式、时间线/思维导图等组织方式，包括用户自己的RAG、DeepRearch的联网内容
2 动效渲染
  2.1 针对tool和内容形式，对应专业美观的实现，比如动画流程图
  2.2 衔接视频生成能力，插画生成，示意视频，场景视频，背景视频生成
  2.3 录屏->GUIAgent把非API引擎扩展起来，JS,CSS等能力，未来推动厂商合作MCP或者API形成壁垒
  2.4 CodeAgent，对专业引起微调过比如manim
  2.5 多元渲染引擎，除manim,增加地图。3D等
3 极致的速度
  3.1 Code微调模型，依赖专业领域，以小搏大，提升无中生有能力
  3.2 渲染方法多元化，浏览器录屏,ffmpeg深度使用,manim，其他引擎录屏之间的无缝衔接
  3.3 整体框架链路极致化，合并没必要环节，渲染极致并行化
4 短视频爆点
  4.1 copy爆款体系结构，定义体系->AI视频分析->转化为费曼效果模板
  4.2 多模态综合能力：BGM/音色TTS-Clone/音效, 数字人讲解模式单人/双人，这样形成视觉+文案+音+数字人等多模态
  4.3 新的交互方式，消费+生产无缝衔接
5 生产的便捷性
  5.1 修改部分内容，快速可视化图像，极快修改原来视频生成，做到即改即见
  5.2 可视化渲染的模块化，只需修改素材，点选模块，模块参数可调可改

## 大模型进步带来应对
- 巨头知识类视频的产品，比如OpenAI Chatgpt+study模式，Google NotebookLM的视频能力
    预测：会冲击1.4，2整体，3整体，但1.1-1.3、4、5看定位影响不大，侧重差异内容和短视频
- 视频生成模型，能直接生成精细化知识类视频，比如Kling, Hailuo等
    预测：会冲击2，其他不会，性能3，1费曼反而是优势，基于生成能力迅速把插画、视频内容真实感提高一层
- CodeAgent能精准生成知识动画类代码，比如manim
    预测：会冲击2，其他影响不大，充分利用CodeAgent能力迅速扩张其他引擎，复杂内容体系建设
- 基于MAS等DeepResearch在知识联想、关联、费曼讲解易懂直接生成
    预测：会冲击1，结合前面几点会对2，3冲击，侧重4，5、1.2的产品能力建设
- 竞对产品成熟，在速度、效果上带来的挑战
    预测：1，2，3，4，5都是优势和差异点

# 未来产品形态逻辑
1 有哪些需要视频、能视频的？
2 有哪些可能的需求？


## 创作者需求
## 消费者需求
## 平台能力