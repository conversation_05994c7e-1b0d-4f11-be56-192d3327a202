# Scene Code Generation Agent

## 概述

Scene Code Generation Agent 是一个专门用于根据场景描述文件生成 Manim 代码的智能代理。它参考了现有的 `process_storyboard.py` 中的 `process_storyboard_entry_wrapper` 函数的逻辑，实现了类似的重试机制和错误处理。该代理已转换为同步执行，提供更简单的使用方式。

## 主要特性

1. **智能代码生成**: 根据场景描述自动生成完整的 Manim Python 代码
2. **MCP 集成**: 通过 Context7 MCP 查询 Manim 文档，提高代码质量
3. **重试机制**: 如果代码生成或渲染失败，会自动重试并传递错误信息给 LLM 进行修复
4. **自动渲染**: 生成代码后自动调用 Manim 进行视频渲染，支持多种质量选项
5. **错误处理**: 完善的错误处理和日志记录
6. **命令行界面**: 支持命令行参数，包括质量设置

## 架构设计

### 核心组件

1. **SceneCodeGenerationToolkit**: 负责代码生成和 MCP 集成
2. **SceneCodeProcessor**: 负责处理场景描述文件和重试逻辑
3. **Context7 MCP 集成**: 查询 Manim 文档以提高代码质量

### 工作流程

```
场景描述文件 → 读取内容 → 查询文档 → 生成代码 → 渲染视频
                          ↑                 ↓
                       错误处理 <--------- 重试机制
```

## 使用方法

### 1. 基本使用

```python
from agents.scene_code_generation_agent import process_scene_file

# 处理单个场景描述文件
result = process_scene_file(
    scene_file="output/BPE算法/scene2/scene2_technical_implementation.txt",
    output_dir="output/generated_code",
    mcp_config_path="config/mcp_servers_config.json",
    max_retries=3,
    quality="h"  # 可选: l, m, h, q, k
)

if result:
    print(f"生成的代码文件: {result['code_file']}")
    print(f"渲染的视频文件: {result['video_file']}")
```

### 2. 命令行使用

```bash
# 基本使用（默认低质量）
python agents/scene_code_generation_agent.py path/to/scene_description.txt

# 指定渲染质量
python agents/scene_code_generation_agent.py path/to/scene_description.txt --quality h

# 使用短参数
python agents/scene_code_generation_agent.py path/to/scene_description.txt -q m
```

#### 质量选项说明

- `l` (low): 低质量，480p15fps，渲染速度快
- `m` (medium): 中等质量，720p30fps
- `h` (high): 高质量，1080p60fps
- `q` (2K): 2K质量，1440p60fps
- `k` (4K): 4K质量，2160p60fps

#### 查看帮助

```bash
python agents/scene_code_generation_agent.py --help
```

### 3. 批量处理

```python
from agents.scene_code_generation_agent import SceneCodeProcessor

processor = SceneCodeProcessor(
    output_dir="output/generated_code",
    mcp_config_path="config/mcp_servers_config.json"
)

try:
    for scene_file in scene_files:
        result = processor.process_scene_description_wrapper(
            scene_file=scene_file,
            max_retries=3,
            quality="h"  # 可选择渲染质量
        )
        if result:
            print(f"成功处理: {scene_file}")
        else:
            print(f"处理失败: {scene_file}")
finally:
    processor.cleanup()
```

## API 变更说明

### 同步执行

该代理已从异步执行转换为同步执行，主要变更包括：

1. **函数签名变更**:
   - 移除了所有 `async` 和 `await` 关键字
   - 所有函数现在都是同步执行

2. **调用方式变更**:
   ```python
   # 旧版本（异步）
   result = await process_scene_file(scene_file)

   # 新版本（同步）
   result = process_scene_file(scene_file)
   ```

3. **MCP 连接变更**:
   - 使用 `connect_sync()` 和 `disconnect_sync()` 方法
   - 简化了连接管理逻辑

### 新增功能

1. **质量参数支持**:
   - 所有处理函数现在都支持 `quality` 参数
   - 可以在函数调用和命令行中指定渲染质量

2. **改进的命令行界面**:
   - 使用 `argparse` 提供完整的参数解析
   - 支持帮助信息和参数验证

## 配置

### MCP 配置

确保 `config/mcp_servers_config.json` 包含 Context7 配置：

```json
{
  "mcpServers": {
    "sequential-thinking": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]
    },
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7"]
    }
  }
}
```

### 环境要求

1. **Python 依赖**:
   - camel-ai
   - loguru
   - python-dotenv
   - argparse (Python 标准库)

2. **外部工具**:
   - Manim Community Edition
   - Node.js (用于 MCP 服务器)

3. **环境变量**:
   - 确保设置了相应的 API 密钥（如 OpenAI API Key）

## 场景描述文件格式

场景描述文件应该包含以下内容：

```markdown
# 场景标题

## 场景描述
详细描述这个场景要展示的内容...

## 内容要点
1. 要点一
2. 要点二
3. 要点三

## 动画要求
- 动画效果要求
- 视觉设计要求
- 交互要求

## 教学目标
描述这个场景的教学目标...
```

## 错误处理和重试机制

### 重试逻辑

1. **第一次尝试**: 使用原始场景描述生成代码
2. **后续重试**: 将前一次的错误信息传递给 LLM，让其修复代码
3. **最大重试次数**: 默认为 3 次，可配置

### 常见错误类型

1. **语法错误**: Python 语法问题
2. **Manim API 错误**: 使用了错误的 Manim 方法或参数
3. **导入错误**: 缺少必要的模块导入
4. **渲染错误**: Manim 渲染过程中的错误

### 错误修复策略

- 将错误信息和堆栈跟踪传递给 LLM
- LLM 分析错误原因并生成修复后的代码
- 保持原有的教学内容和逻辑不变

## 测试

### 运行测试脚本

```bash
python test_scene_code_agent.py
```

测试脚本会：
1. 创建示例场景描述文件
2. 测试代码生成功能
3. 测试现有场景文件处理
4. 显示生成的代码和结果

### 测试不同质量设置

```bash
# 测试低质量渲染（快速）
python agents/scene_code_generation_agent.py test_scene.txt -q l

# 测试高质量渲染
python agents/scene_code_generation_agent.py test_scene.txt -q h

# 测试4K质量渲染（需要更多时间和资源）
python agents/scene_code_generation_agent.py test_scene.txt -q k
```

## 与现有系统的集成

### 与 DSL 生成系统的对比

| 特性 | DSL 生成系统 | 代码生成系统 |
|------|-------------|-------------|
| 输入 | 分镜描述 | 场景描述文件 |
| 输出 | DSL JSON → Python 代码 → 视频 | Python 代码 → 视频 |
| 重试机制 | ✅ | ✅ |
| MCP 集成 | ❌ | ✅ |
| 文档查询 | ❌ | ✅ |
| 执行方式 | 异步 | 同步 |
| 质量选项 | 固定 | 可配置 (l/m/h/q/k) |
| 命令行支持 | 基础 | 完整参数解析 |

### 集成建议

1. **并行使用**: 可以同时使用两个系统处理不同类型的内容
2. **互补功能**: DSL 系统适合结构化内容，代码生成系统适合自由形式的场景描述
3. **统一接口**: 可以创建统一的接口来调用两个系统
4. **质量管理**: 根据需求选择合适的渲染质量，平衡速度和效果

## 未来改进

1. **更智能的文档查询**: 根据场景内容自动选择相关的文档片段
2. **代码质量评估**: 在渲染前评估生成代码的质量
3. **模板系统**: 为常见的场景类型提供代码模板
4. **性能优化**: 缓存文档查询结果，提高处理速度
5. **可视化调试**: 提供代码生成过程的可视化界面
6. **自适应质量**: 根据内容复杂度自动选择合适的渲染质量
7. **批量质量设置**: 支持为不同场景设置不同的渲染质量
8. **渲染进度显示**: 显示渲染进度和预估完成时间

## 故障排除

### 常见问题

1. **MCP 连接失败**
   - 检查 Node.js 是否安装
   - 确认 MCP 服务器配置正确
   - 查看网络连接

2. **Manim 渲染失败**
   - 检查 Manim 是否正确安装
   - 确认生成的代码语法正确
   - 查看 Manim 错误日志
   - 尝试使用较低的质量设置 (`-q l`)

3. **代码生成质量差**
   - 检查场景描述是否足够详细
   - 确认 MCP 文档查询是否正常工作
   - 调整提示词模板

4. **渲染速度慢**
   - 使用较低的质量设置 (`l` 或 `m`)
   - 检查系统资源使用情况
   - 考虑分批处理大量场景

5. **命令行参数错误**
   - 使用 `--help` 查看可用选项
   - 确认质量参数在有效范围内 (l, m, h, q, k)
   - 检查文件路径是否正确

### 调试技巧

1. 启用详细日志记录
2. 检查生成的中间文件
3. 手动测试 Manim 代码
4. 使用测试脚本验证功能
