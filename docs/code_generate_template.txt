# Manim代码生成规范

## 基础要求
- 继承 `ProfessionalScienceTemplate` 类，重写 `construct` 方法
- 使用标准区域接口：
  - `self.create_title_region_content(string)` - 标题区域
  - `self.create_step_region_content(string)` - 步骤区域
  - `self.create_main_region_content(VGroup)` - 主内容区域
  - `self.create_right_auxiliary_content` - 辅助区域
  - `self.create_result_region_content(string)` - 结果区域

## 关键规范
1. **元素组织**：所有元素必须在 `create_main_region_content` 之前定义完成，包括后期出现的高亮框、说明文字等，并且都添加到self.region_elements['main']中
2. **布局定位**：VGroup内元素使用相对位置（`next_to`、`align_to`、`arrange`），禁用绝对位置
3. **动画控制**：关键逻辑分步展示，元素切换使用 `ReplacementTransform()`，而不是fadein+fadeout，**重要**禁止用copy()和Transform()函数
4. **文字限制**：标题≤8字，步骤≤12字，辅助标题≤6字，结果≤20字

在construct的第一步，调用 animate_markdown 通过合适的日常生活现象类比或引导性问题引起观众兴趣， 最后一步调用 animate_step_by_step 函数，根据例子总结简要的步骤说明。

这两个函数已经实现，说明文档如下，只需要直接调用即可：

# animate_markdown

## 效果

将Markdown格式的文本内容渲染为富文本并在Manim场景中播放动画。
支持各种Markdown元素，包括标题、列表、代码块、表格等。


## 使用场景

- 展示格式化的文本内容，如教程说明、演示文稿
- 在动画中展示结构化的信息，如列表和表格
- 显示带有语法高亮的代码片段
- 创建包含文本和图片的混合内容

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| content | str | Markdown格式的文本内容 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |
| title | str | 当前内容的标题，会显示在内容上方 | 是 | - |
| narration | str | 在内容显示时播放的语音旁白文本 | 是 | - |

## DSL示例

### 示例 1

```json
{
  "type": "animate_markdown",
  "params": {
    "content": "# 主标🚗题\n\n⚠️ 这是一段普通文本，支持**粗体**和*斜体*。\n\n> 🚰 这是一个引用💦块。\n\n## 子标题\n\n- 列表🐆 项1\n- 🐇 列表项2\n- $E = mc^2$\n\n$$\na^2 = b^2 + c^2\n$$\n\n```python\ndef hello_world():\n    print(\"Hello, world!\")\n```\n",
    "title": "这是一个Markdown示例",
    "narration": "这是一个Markdown示例，包含标题、文本和代码。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_markdown",
  "params": {
    "content": "## 数据比较📊\n\n| 产品 | 价格 | 评分 |\n| ---- | ---- | ---- |\n| A产品 | ¥199 | 4.5分 |\n| B产品 | ¥299 | 4.8分 |\n| C产品 | ¥399 | 4.9分 |\n",
    "title": "产品价格比较",
    "narration": "这个表格比较了两款产品的价格和评分。"
  }
}
```

## 注意事项

- 支持大多数标准Markdown语法，包括标题、列表、代码块、表格等
- 根据内容会自动调整大小以适应场景
- 必须包含title字段，言简意赅，概括内容要点，content中不要有和title重复的标题部分，重点在内容介绍


# animate_step_by_step

## 效果

在Manim场景中创建并播放一个分步骤讲解的动画。
左侧展示步骤节点，每一步包含序号和操作描述，节点动态生成并向上移动。
右侧展示每一步的具体内容（markdown格式），支持代码、列表、文本等。
最后所有步骤节点缩放移动到画面正中，展示整体概念。


## 使用场景

- 教学演示中的分步骤讲解，如算法步骤、操作流程等
- 产品功能介绍，逐步展示各个功能点
- 项目开发流程演示，突出每个阶段的重点

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| steps | list[StepData | dict[str, Any]] | 步骤列表。每个元素包含step_number(步骤序号), title(步骤标题), content(步骤内容，markdown格式), color(节点颜色，可选), narration(步骤旁白，可选)等属性 | 是 | - |
| intro_narration | str | 开场介绍语音旁白文本 | 否 | None |
| outro_narration | str | 结尾总结语音旁白文本 | 否 | None |
| title | str | 整体标题 | 是 | - |
| subtitle | str | 副标题 | 否 | None |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_step_by_step",
  "params": {
    "steps": [
      {
        "step_number": "1",
        "title": "初始化数据",
        "content": "## 创建数组\n```python\narr = [64, 34, 25, 12, 22, 11, 90]\n```\n- 准备待排序的数组\n- 记录数组长度\n",
        "color": "#FF6B6B",
        "narration": "首先我们初始化一个待排序的数组"
      },
      {
        "step_number": "2",
        "title": "选择最小元素",
        "content": "## 查找最小值\n```python\nmin_idx = 0\nfor i in range(1, len(arr)):\n    if arr[i] < arr[min_idx]:\n        min_idx = i\n```\n- 遍历未排序部分\n- 找到最小元素的索引\n",
        "color": "#4ECDC4",
        "narration": "接下来在未排序部分找到最小的元素"
      },
      {
        "step_number": "3",
        "title": "交换元素",
        "content": "## 元素交换\n```python\narr[0], arr[min_idx] = arr[min_idx], arr[0]\n```\n- 将最小元素移到已排序部分的末尾\n- 扩大已排序区域\n",
        "color": "#45B7D1",
        "narration": "然后将最小元素与第一个位置交换"
      }
    ],
    "title": "选择排序算法演示",
    "subtitle": "逐步理解排序过程",
    "intro_narration": "今天我们来学习选择排序算法的工作原理",
    "outro_narration": "通过这三个步骤，我们完成了选择排序的一轮操作"
  }
}
```

## 注意事项

- 步骤按数组中的顺序呈现，每个步骤的内容支持完整的markdown语法
- 左侧节点会动态生成并向上移动，右侧内容会淡出后显示新内容
- 可以为每个步骤指定颜色，或使用默认颜色方案
- 最后所有步骤节点会缩放移动到画面中央，形成整体概览
- 与timeline的差别是，step_by_step需要讲解每个步骤中的详细内容，因此更适合例子讲解等场景，而timeline只是展示事件的整体脉络
- title, subtitle与step title需要精简，不能字数太多，否则会导致文字重叠等问题


## 生成代码示例
```python
import sys, os
sys.path.insert(0, os.getcwd())  # NOTE 在当前目录运行manim代码，不要切换到项目路径下，否则导入路径会出错
from prompts.professional_science_template import ProfessionalScienceTemplate
from dsl.v2.animation_functions import animate_markdown, animate_step_by_step

class MyExample(ProfessionalScienceTemplate):
    def construct(self):
        self.setup_background()
        animate_markdown(
            scene=self,
            content="# 日落的秘密\n\n- 为什么夕阳是红色的，而正午的太阳是白色的？\n- 为什么天空是蓝色的，而不是绿色或者紫色？",
            title="瑞利散射",
            narration="想象一下：同样的太阳光，为什么在不同时间、不同角度会呈现完全不同的颜色？答案就藏在光线穿越大气层的这段旅程中——这就是瑞利散射现象！"
        )
        self.clear_current_mobj("fade")  # 必须，清除animate_markdown创建的对象，确保画面干净
        self.create_stage1_content()
        self.create_stage2_content()

        self.play(*[FadeOut(mob) for mob in self.mobjects])  # 必须，清除所有元素
        animate_step_by_step(
            scene=self,
            steps=[
                {
                    "step_number": "1",
                    "title": "初始化数据",
                    "content": "## 创建数组\n```python\narr = [64, 34, 25, 12, 22, 11, 90]\n```\n- 准备待排序的数组\n- 记录数组长度\n",
                    "color": "#FF6B6B",
                    "narration": "首先我们初始化一个待排序的数组"
                },
                {
                    "step_number": "2",
                    "title": "选择最小元素",
                    "content": "## 查找最小值\n```python\nmin_idx = 0\nfor i in range(1, len(arr)):\n    if arr[i] < arr[min_idx]:\n        min_idx = i\n```\n- 遍历未排序部分\n- 找到最小元素的索引\n",
                    "color": "#4ECDC4",
                    "narration": "接下来在未排序部分找到最小的元素"
                },
                # ...
            ],
            title="选择排序算法演示",
            subtitle="逐步理解排序过程",
            intro_narration="今天我们来学习选择排序算法的工作原理",
            outro_narration="通过这三个步骤，我们完成了选择排序的一轮操作"
        )

    def create_stage2_content(self):
        # 标题和步骤区域内容
        title = self.create_title_region_content("标题")
        step = self.create_step_region_content("步骤")

        # 定义所有主区域元素
        element1 = Text("主要内容")
        element2 = Rectangle()
        highlight_box = Rectangle(color=RED)

        # 组织到VGroup并创建主区域
        main_group = VGroup(element1, element2, highlight_box)
        main = self.create_main_region_content(main_group)

        # 动画播放，替换原区域元素
        self.play(ReplacementTransform(self.region_elements['title'], title))
        self.region_elements['title'] = title
        self.play(ReplacementTransform(self.region_elements['step'], step))
        self.region_elements['step'] = step
        self.play(ReplacementTransform(self.region_elements['main'], main))
        self.region_elements['main'] = main

```
