# 内容收集系统重构设计

## 1. 背景与目标

当前的内容收集系统（`src/video_agent/tools/info_collector`）使用了较为复杂的数据模型来表示从不同来源获取的内容。本次重构的目标是：

- 将所有信息源的结果统一为 Markdown 格式
- 建立缓存机制避免重复处理
- 简化 `UnifiedContent` 数据模型
- 支持多媒体内容（图片、视频）的本地缓存和引用管理

## 2. 简化的数据模型

### 2.1 核心数据结构

#### ContentResource（内容资源）

```python
@dataclass
class ContentResource:
    """表示内容中的媒体资源（图片、视频等）"""

    original_uri: str             # 原始URI
    resource_type: str            # 资源类型: "image", "video", "audio" 等
    local_path: str               # 本地存储路径
    filename: str                 # 文件名
    mime_type: Optional[str] = None      # MIME类型
    metadata: dict = field(default_factory=dict)  # 额外元数据（尺寸、格式等）
```

#### ContentMetadata（内容元数据）

```python
@dataclass
class ContentMetadata:
    """内容的元数据信息"""

    source_type: str              # 来源类型: "web", "pdf", "text", "api", "search" 等
    source_id: str                # 来源标识: URL, 文件路径, 或自定义ID
    timestamp: datetime           # 处理时间戳
    hash_key: str                 # 缓存用的哈希键
    title: Optional[str] = None   # 内容标题
    tags: list[str] = field(default_factory=list)  # 标签
    processing_stats: dict = field(default_factory=dict)  # 处理统计信息
```

#### MarkdownContent（Markdown内容）

```python
@dataclass
class MarkdownContent:
    """简化的统一内容模型, 以Markdown为中心"""

    metadata: ContentMetadata                      # 元数据
    content: str                                   # Markdown格式的内容
    resources: list[ContentResource] = field(default_factory=list)  # 嵌入的媒体资源
    cached_path: Optional[str] = None             # 缓存的Markdown文件路径
    error: Optional[str] = None                   # 错误信息（如有）
```

## 3. 简化的缓存机制

### 3.1 缓存设计原则

- **简单直接**：专注于核心功能，避免过度设计
- **基于文件系统**：直接使用文件系统存储内容和资源，便于访问和调试
- **资源引用管理**：跟踪资源与内容的引用关系，支持垃圾回收

### 3.2 缓存目录结构

```
cache/
├── index.json          # 简化的缓存索引文件
├── content/            # Markdown内容目录
│   ├── [hash_key].md   # 缓存的Markdown文件
│   └── ...
└── resources/          # 媒体资源目录
    ├── images/         # 图片资源
    │   ├── [filename]  # 缓存的图片文件
    │   └── ...
    ├── videos/         # 视频资源
    │   └── ...
    └── ...
```

### 3.3 缓存索引结构

索引文件(`index.json`)存储内容和资源的元数据和关系：

```json
{
  "content": {
    "web:https://example.com": {
      "hash_key": "a1b2c3d4",
      "timestamp": 1633456789,
      "path": "cache/content/a1b2c3d4.md"
    },
    "pdf:/path/to/doc.pdf": {
      "hash_key": "e5f6g7h8",
      "timestamp": 1633456790,
      "path": "cache/content/e5f6g7h8.md"
    }
  },
  "resources": {
    "https://example.com/image.jpg": {
      "path": "cache/resources/images/image_a1b2c3.jpg",
      "refs": ["a1b2c3d4", "e5f6g7h8"],
      "last_access": 1633456789
    },
    "https://example.com/video.mp4": {
      "path": "cache/resources/videos/video_e5f6g7.mp4",
      "refs": ["e5f6g7h8"],
      "last_access": 1633456790
    }
  }
}
```

## 4. 主要接口设计

### 4.1 内容处理器接口

```python
class ContentProcessor(Protocol):
    """内容处理器接口"""

    def process(self, source_id: str, **kwargs) -> MarkdownContent:
        """
        处理指定来源的内容，返回统一的MarkdownContent

        Args:
            source_id: 内容来源标识
            **kwargs: 处理器特定的参数

        Returns:
            MarkdownContent: 处理后的统一内容
        """
        ...
```

### 4.2 资源管理器

```python
class ResourceManager:
    """媒体资源管理器"""

    def download_resource(self, uri: str, resource_type: str) -> ContentResource:
        """下载资源并返回ContentResource对象"""
        ...

    def get_resource_path(self, resource: ContentResource) -> str:
        """获取资源的本地路径"""
        ...
```

### 4.3 简化的缓存系统

```python
class ContentCache:
    """简化的内容缓存系统"""

    def get_content(self, source_type: str, source_id: str) -> Optional[MarkdownContent]:
        """获取缓存的内容，如不存在或已过期则返回None"""
        ...

    def cache_content(self, content: MarkdownContent) -> str:
        """缓存内容及其资源，返回缓存文件路径"""
        ...

    def invalidate(self, source_type: str, source_id: str) -> bool:
        """使指定内容的缓存失效，返回是否成功"""
        ...

    def cleanup_resources(self, days_threshold: int = 30) -> int:
        """清理超过指定天数未使用且无引用的资源，返回清理数量"""
        ...

    def get_stats(self) -> dict:
        """获取缓存统计信息"""
        ...
```

### 4.4 统一内容收集器

```python
class ContentCollector:
    """统一内容收集器"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化内容收集器

        Args:
            config: 配置参数，包括缓存目录等
        """
        ...

    def collect(self, source_type: str, source_id: str,
                force_refresh: bool = False, **kwargs) -> MarkdownContent:
        """
        收集并处理指定来源的内容

        Args:
            source_type: 来源类型
            source_id: 来源标识
            force_refresh: 是否强制刷新（不使用缓存）
            **kwargs: 传递给处理器的额外参数

        Returns:
            MarkdownContent: 处理后的内容
        """
        ...

    def get_markdown_path(self, source_type: str, source_id: str) -> Optional[str]:
        """获取指定内容的Markdown文件路径，如不存在则返回None"""
        ...
```

## 5. 配置选项

系统支持以下配置选项：

```python
DEFAULT_CONFIG = {
    "cache_dir": "cache",                 # 缓存根目录
    "content_dir": "cache/content",       # Markdown内容目录
    "resource_dir": "cache/resources",    # 资源根目录
    "image_dir": "cache/resources/images",  # 图片资源目录
    "video_dir": "cache/resources/videos",  # 视频资源目录
    "cache_ttl": 86400 * 7,               # 缓存生存时间（秒），默认7天
    "cleanup_threshold": 30,              # 资源清理阈值（天）
    "max_cache_size_mb": 1024,            # 最大缓存大小（MB）
}
```

## 6. 主要流程

### 6.1 内容处理流程

```
1. 用户请求内容(source_type, source_id)
   │
   ├─ 检查缓存是否存在且有效
   │  ├─ 是 → 返回缓存内容
   │  └─ 否 → 继续
   │
   ├─ 选择合适的内容处理器
   │  └─ 处理内容并生成MarkdownContent
   │     │
   │     └─ 处理嵌入资源
   │        ├─ 下载/转换资源
   │        └─ 更新资源引用
   │
   ├─ 缓存处理结果
   │  ├─ 保存Markdown到文件
   │  └─ 更新缓存索引
   │
   └─ 返回内容及文件路径
```

### 6.2 资源清理流程

```
1. 触发资源清理(定时/手动)
   │
   ├─ 扫描资源索引
   │  └─ 找出无引用且超过阈值未访问的资源
   │
   ├─ 删除未使用资源文件
   │
   └─ 更新资源索引
```

## 7. 实现计划

1. 创建新的数据模型（`models.py`）
2. 实现简化的缓存系统（`cache.py`）
3. 实现资源管理器（`resource_manager.py`）
4. 为各种源类型实现内容处理器（`processors/`）
   - 网页处理器（`web_processor.py`）
   - PDF处理器（`pdf_processor.py`）
   - 文本处理器（`text_processor.py`）
   - 搜索结果处理器（`tavily_processor.py`）
5. 实现统一的内容收集器（`collector.py`）
6. 编写单元测试
7. 更新文档

## 8. 扩展性考虑

### 8.1 添加新内容源

系统设计支持轻松添加新的内容源，只需：
1. 创建实现`ContentProcessor`接口的新处理器
2. 在`ContentCollector`中注册处理器
3. 实现特定于源的处理逻辑

### 8.2 内容转换与导出

可以扩展系统支持不同格式的内容导出：
1. Markdown到HTML的转换
2. 生成PDF文档
3. 格式化为结构化数据（JSON/XML）

### 8.3 高级缓存策略

未来可以根据需要实现更复杂的缓存策略：
1. 基于内容变化的增量更新
2. 分布式缓存支持
3. 压缩存储以节省空间
