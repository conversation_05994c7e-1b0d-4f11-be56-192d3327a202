# 为动画函数添加转场效果指南

## 概述

转场效果允许两个动画场景之间平滑过渡，增强视觉连续性。本文档详细介绍如何为动画函数添加转场效果支持。

## 整体思路

1. 每个动画函数最后保存当前场景状态（将画面里所有的mobj组合成一个group，然后通过pickle保存字典 `{"type": "content_type", "mobj": mobj}`）。其中`type`用于标记当前动画类型，会在`load_scene_state`中用于选择合适的转场对象。
2. 如果一个分镜中有多个动画函数，需要保存第一个和最后一个动画函数的场景状态，第一个作为上一个分镜转场的结束状态，最后一个作为下一个分镜转场的开始状态。所以保存的文件名有对应的后缀`_first`或者`_last`
3. 转场动画生成函数根据指定的分镜id，加载前一个分镜的最后一个动画函数的场景状态，和下一个分镜的第一个动画函数的场景状态，然后生成转场动画。比如[`inter_scene_transition.py`](../dsl/v2/core/inter_scene_transition.py)最后的`generate_inter_scene_transition("Storyboard_2", "Storyboard_2", "wipe_left", 4.0)`
4. 拼接视频的时候将转场动画拼接到两个分镜之间


## 转场效果系统组件

转场效果系统由以下几个关键部分组成：

1. **转场效果库** (`dsl/v2/core/transition_effects.py`)：提供各种视觉转场效果
2. **转场辅助工具** (`utils/transition_helper.py`)：处理场景状态的保存和加载
3. **分镜间转场** (`dsl/v2/core/inter_scene_transition.py`)：处理不同场景之间的转场

## 实现步骤

为动画函数添加转场效果，需要完成以下步骤：

### 1. 确定内容类型标识符

每种动画函数应该有一个唯一的内容类型标识符`content_type`，用于转场系统识别。例如：

- `markdown`：用于Markdown内容
- `timeline`：用于时间轴
- `text_only`：用于纯文本

### 2. 保存场景状态

在动画函数的结尾部分，调用`save_scene_state`方法保存当前场景状态：

```python
# mobject_id 是创建的Manim对象的唯一标识符
scene.save_scene_state(content_type, mobject_id)
```

其中：
- `content_type`：内容类型标识符，如"markdown"、"timeline"等
- `mobject_id`：当前对象的唯一标识符

例如，在`animate_markdown`函数中的实现：

```python
def animate_markdown(scene: "FeynmanScene", content: str, **kwargs):
    # ...动画逻辑...

    # 保存结束状态
    scene.save_scene_state("markdown", mobject_id)
```

### 3. 处理转场对象

在`transition_helper.py`的`load_scene_state`函数中，需要为你的内容类型添加处理逻辑：

```python
def load_scene_state(save_dir: str, scene_id: str, is_old_mobject: bool = True) -> Optional[Mobject]:
    # ...其他代码...

    # 如果当前mobj是转场的初始状态对象，用整个mobj作为转场对象，否则需要返回适合转场的对象。
    # 通常是某个固定位置的子对象，不会在后续的动画播放阶段发生变化，否则转场会发生错位等，显得不自然
    if is_old_mobject:
        return mobj

    # 每个动效函数创建的对象中，第一个子对象都是当前内容的标题
    return mobj.submobjects[0]
```

选择合适的转场对象非常重要：
- 对象应当是场景中一个稳定的、代表性的部分
- 对象应当有适当的尺寸和位置，以便转场效果看起来自然
- 对象不应该是一个会消失或大幅变化的临时对象

所以为每个场景都添加一个title字段，满足上述要求

### 4. 场景清理和对象跟踪

在动画函数中，使用`scene.clear_current_mobj()`来准备场景，并正确设置`current_mobj`：

```python
# 清除当前对象，设置新对象
# 这里只会在同一个分镜的多个动画之间生效，分镜之间的转场由TransitionManager处理
scene.clear_current_mobj(new_mobj=display_group.submobjects[0])

# ...动画执行...

# 更新当前对象引用
scene.current_mobj = display_group
```

## 可用的转场效果

转场管理器(`TransitionManager`)支持以下效果：

- `fade`：淡入淡出效果
- `slide_left`：向左滑动
- `slide_right`：向右滑动
- `slide_up`：向上滑动
- `slide_down`：向下滑动
- `zoom`：缩放效果
- `wipe_left`：从左向右擦除

## 注意事项

1. **保存的对象状态**：确保保存的是完整、最终的对象状态
2. **转场对象选择**：选择合适的对象进行转场，通常是场景中最主要的视觉元素
3. **动画顺序**：对于参与转场的对象，在下一个场景中应避免用动画引入，而应该直接添加到场景中
4. **兼容性**：确保你的动画函数与转场系统兼容，特别是在处理对象层次结构时

## 示例：为新动画函数添加转场效果

假设我们要为`animate_custom_content`函数添加转场效果支持：

```python
def animate_custom_content(scene: "FeynmanScene", title: str, content: str, **kwargs):
    # 生成唯一ID
    mobject_id = kwargs.get("id", f"custom_{str(uuid.uuid4())[:8]}")

    # 创建主要内容对象
    main_content = ...  # 创建主要内容对象，第一个子对象是title

    # 准备场景，清除当前对象
    scene.clear_current_mobj(new_mobj=main_content.submobjects[0])

    # 执行动画
    with scene.voiceover(text=kwargs.get("narration", "")) as tracker:
        # 动画逻辑...
        # ...
        # NOTE 对转场结束状态的mobj特殊处理，因为这个mobj会由转场函数生成动画效果，因此在函数内部一般就直接add，而不需要动态出现了

    # 更新当前对象引用
    scene.current_mobj = main_content
    # 保存场景状态用于转场
    scene.save_scene_state("custom_content", mobject_id)
```

## 转场效果开发

如果现有的转场效果不满足需求，可以在`TransitionEffects`类中添加新的转场效果：

```python
@staticmethod
def your_new_transition(old_mobj: Mobject, new_mobj: Optional[Mobject] = None, run_time: float = 1.0) -> list[Animation]:
    # 创建转场动画
    animations = [...]
    return animations
```

然后在`TransitionManager.TRANSITION_EFFECTS`字典中注册：

```python
TRANSITION_EFFECTS = {
    # 现有效果...
    "your_effect_name": TransitionEffects.your_new_transition,
}
```
