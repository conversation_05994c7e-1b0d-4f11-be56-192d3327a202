# 内容管理系统整合方案

## 1. 概述

本文档描述了将现有的`content_manager`模块与`info_collector`模块整合的方案。主要目标是创建一个清晰、合理的新接口，直接整合现有适配器功能，替代原有的`info_collector`系统。

## 2. 核心设计原则

- **简洁明了**：接口设计简单直观，易于理解和使用
- **职责分离**：每个组件职责明确，不重叠
- **以Markdown为中心**：统一所有内容为Markdown格式
- **直接集成**：直接整合adapter功能，不再依赖旧代码
- **单一缓存**：使用简单高效的基于文件系统的缓存

## 3. 系统架构

```
content_manager/
├── models.py            # 数据模型（MarkdownContent等）
├── collector.py         # 内容收集协调器（主入口）
├── cache.py             # 文件系统缓存
├── resource_manager.py  # 资源下载和管理
└── processors/          # 内容处理器目录
    ├── __init__.py      # 处理器接口定义
    ├── text_processor.py       # 文本处理器
    ├── web_processor.py        # 网页处理器（整合WebContentAdapter功能）
    ├── pdf_processor.py        # PDF处理器（整合PDFAdapter功能）
    ├── tavily_processor.py     # Tavily搜索处理器
    ├── duckduckgo_processor.py # DuckDuckGo搜索处理器
    └── searxng_processor.py    # SearXNG搜索处理器
```

## 4. 接口设计

### 4.1 统一的处理器接口

```python
# processors/__init__.py
from typing import Protocol, runtime_checkable
from ..models import MarkdownContent

@runtime_checkable
class ContentProcessor(Protocol):
    """明确的内容处理器接口"""

    @property
    def source_type(self) -> str:
        """处理器支持的内容类型标识符"""
        ...

    def process(self, source_id: str, **kwargs) -> MarkdownContent:
        """
        处理指定来源的内容

        Args:
            source_id: 来源标识符（URL、文件路径等）
            **kwargs: 处理器特定参数

        Returns:
            MarkdownContent: 处理后的统一内容
        """
        ...
```

### 4.2 数据模型

```python
# models.py
@dataclass
class ContentMetadata:
    """内容元数据"""
    source_type: str                         # 来源类型
    source_id: str                           # 来源标识
    timestamp: datetime = field(default_factory=datetime.now)
    title: Optional[str] = None              # 内容标题
    tags: List[str] = field(default_factory=list)
    processing_stats: Dict[str, Any] = field(default_factory=dict)

    @property
    def hash_key(self) -> str:
        """生成用于缓存的哈希键"""
        key_str = f"{self.source_type}:{self.source_id}"
        return hashlib.md5(key_str.encode("utf-8")).hexdigest()

@dataclass
class ContentResource:
    """媒体资源"""
    original_uri: str            # 原始URI
    resource_type: str           # 资源类型
    local_path: str              # 本地路径
    filename: str                # 文件名
    mime_type: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class MarkdownContent:
    """以Markdown为中心的内容表示"""
    metadata: ContentMetadata               # 元数据
    content: str                            # Markdown内容
    resources: List[ContentResource] = field(default_factory=list)
    cached_path: Optional[str] = None       # 缓存文件路径
    error: Optional[str] = None             # 错误信息
```

### 4.3 资源管理器

资源管理器专注于资源的下载、存储和引用替换，不涉及具体内容提取和转换：

```python
# resource_manager.py
class ResourceManager:
    """资源管理器 - 专注于资源下载和管理"""

    def download_resource(self, uri: str, resource_type: str) -> ContentResource:
        """下载资源并返回ContentResource"""
        # 实现资源下载，保持单一职责
        ...

    def extract_resources_from_html(self, html: str, base_url: str) -> List[ContentResource]:
        """从HTML中提取和下载所有资源"""
        # HTML资源提取是通用功能
        ...

    def replace_markdown_references(self, markdown: str, resources: List[ContentResource]) -> str:
        """替换Markdown中的资源引用为本地路径"""
        # 处理资源引用替换
        ...
```

### 4.4 缓存系统

缓存系统负责统一的内容和资源缓存管理，所有处理器都通过ContentCollector与缓存交互：

```python
# cache.py
class ContentCache:
    """基于文件系统的内容缓存"""

    def get_content(self, source_type: str, source_id: str) -> Optional[MarkdownContent]:
        """获取缓存的内容"""
        # 生成缓存键: {source_type}:{source_id}
        # 检查缓存是否存在有效
        # 读取缓存内容
        ...

    def cache_content(self, content: MarkdownContent) -> str:
        """缓存内容并返回缓存路径"""
        # 保存内容到文件系统
        # 维护资源引用关系
        # 更新索引
        ...

    def invalidate(self, source_type: str, source_id: str) -> bool:
        """使缓存失效"""
        # 删除缓存文件
        # 更新资源引用
        ...

    def cleanup_resources(self, days_threshold: int = 30) -> int:
        """清理未使用的资源"""
        # 清理无引用且超过阈值的资源
        ...
```

### 4.5 内容收集器

内容收集器是用户交互的主要入口，负责协调处理器和缓存系统：

```python
# collector.py
class ContentCollector:
    """内容收集的主入口"""

    def __init__(self, config: dict[str, Any] = None):
        """初始化收集器"""
        # 初始化缓存系统
        self.cache = ContentCache(config or {})
        # 初始化资源管理器
        self.resource_manager = ResourceManager(config or {})
        # 注册处理器
        self.processors = {}
        self._register_default_processors()

    def register_processor(self, processor: ContentProcessor) -> None:
        """注册内容处理器"""
        ...

    def collect(self, source_type: str, source_id: str,
                force_refresh: bool = False, **kwargs) -> MarkdownContent:
        """收集并处理内容

        1. 检查缓存(除非强制刷新)
        2. 如无缓存，调用处理器处理
        3. 缓存处理结果
        4. 返回内容
        """
        ...

    def get_markdown_path(self, source_type: str, source_id: str) -> Optional[str]:
        """获取缓存的Markdown文件路径"""
        ...
```

## 5. 处理器实现

### 5.1 文本处理器

```python
# text_processor.py
class TextProcessor:
    """处理文本内容"""

    source_type = "text"

    def process(self, source_id: str, **kwargs) -> MarkdownContent:
        """处理文本内容或文件"""
        ...
```

### 5.2 网页处理器

网页处理器直接实现网页获取和转换，不依赖旧的WebContentAdapter：

```python
# web_processor.py
class WebProcessor:
    """处理网页内容"""

    source_type = "web"

    def process(self, url: str, **kwargs) -> MarkdownContent:
        """处理网页内容

        1. 获取HTML (支持JavaScript渲染)
        2. 提取资源并下载
        3. 转换为Markdown
        4. 替换资源引用
        5. 返回统一格式
        """
        ...
```

### 5.3 PDF处理器

PDF处理器直接使用docling处理PDF文件，支持URL和本地文件，利用统一的缓存机制避免重复处理：

```python
# pdf_processor.py
class PDFProcessor:
    """处理PDF文档"""

    source_type = "pdf"

    def process(self, source_id: str, **kwargs) -> MarkdownContent:
        """处理PDF文档

        支持URL和本地文件路径：
        1. 使用docling直接处理PDF (docling支持URL自动下载)
        2. 提取图片资源
        3. 转换为统一的Markdown格式
        4. 返回结果 (缓存由ContentCollector管理)
        """
        ...
```

### 5.4 搜索处理器

```python
# tavily_processor.py
class TavilyProcessor:
    """处理Web搜索请求"""

    source_type = "search"

    def process(self, query: str, **kwargs) -> MarkdownContent:
        """处理搜索请求

        1. 执行搜索查询
        2. 格式化搜索结果为Markdown
        3. 返回统一格式

        注意：搜索处理器职责是返回搜索结果，不负责获取完整内容
        如需完整内容，应由调用方使用搜索结果中的URL再次调用网页处理器
        """
        ...
```

## 6. 缓存整合机制

所有处理器与缓存的整合由ContentCollector层统一管理：

1. **缓存键生成**：使用 `{source_type}:{source_id}` 格式，然后进行MD5哈希
2. **缓存条件**：由ContentCollector决定是否使用缓存，处理器无需关心
3. **资源引用追踪**：缓存系统自动跟踪内容与资源的引用关系
4. **优点**：
   - 处理器专注于内容处理
   - 统一的缓存机制对所有处理器有效
   - 简化接口和实现

对于耗时的处理（如PDF处理），缓存系统能有效避免重复处理。

## 7. 整合实施步骤

### 7.1 核心组件实现

1. **实现核心数据模型**:
   - 完善`models.py`，优化字段定义和方法

2. **实现资源管理器**:
   - 专注资源下载和管理职责
   - 实现资源引用替换功能

3. **实现缓存系统**:
   - 优化缓存机制
   - 添加资源引用跟踪

4. **定义处理器接口**:
   - 明确接口要求
   - 实现基础处理器

### 7.2 处理器实现

1. **实现WebProcessor**:
   - 直接实现网页获取和解析
   - 添加资源处理

2. **实现PDFProcessor**:
   - 直接使用docling处理PDF
   - 支持URL和本地文件
   - 优化图片提取

3. **实现TavilyProcessor**:
   - 直接实现搜索功能
   - 添加搜索结果处理

4. **实现更多处理器**:
   - DuckDuckGoProcessor
   - SearXNGProcessor

### 7.3 内容收集器实现

1. **实现ContentCollector**:
   - 处理器注册机制
   - 缓存管理整合
   - 统一的collect接口

## 8. 代码修改清单

### 8.1 新建文件:

- [X] `content_manager/processors/duckduckgo_processor.py` - DuckDuckGo处理器
- [X] `content_manager/processors/searxng_processor.py` - SearXNG处理器

### 8.2 修改文件:

- [X] `content_manager/models.py` - 完善数据模型
- [X] `content_manager/resource_manager.py` - 简化为专注于资源下载和管理
- [X] `content_manager/processors/web_processor.py` - 直接实现网页处理
- [X] `content_manager/processors/pdf_processor.py` - 直接使用docling处理PDF
- [X] `content_manager/processors/tavily_processor.py` - 直接实现搜索功能
- [X] `content_manager/processors/__init__.py` - 定义统一接口
- [X] `content_manager/collector.py` - 完善收集器，整合缓存机制
- [X] `content_manager/example.py` - 更新示例

## 9. 使用示例

```python
# 基本使用
from src.video_agent.tools.content_manager import ContentCollector

# 创建收集器
collector = ContentCollector({
    "cache_dir": "cache",
    "tavily_api_key": "your-api-key"
})

# 处理网页 (自动缓存)
web_content = collector.collect(
    source_type="web",
    source_id="https://example.com",
    use_javascript=True
)
print(f"标题: {web_content.metadata.title}")
print(f"内容缓存路径: {web_content.cached_path}")

# 处理PDF (支持URL和本地文件)
pdf_content = collector.collect(
    source_type="pdf",
    source_id="https://example.org/document.pdf"  # 或本地路径
)

# 搜索内容
search_content = collector.collect(
    source_type="search",
    source_id="Python programming",
    search_depth="basic"
)

# 使用缓存
# 重复调用会使用缓存，除非指定force_refresh=True
cached_content = collector.collect(
    source_type="web",
    source_id="https://example.com"
)  # 直接返回缓存内容

# 强制刷新
fresh_content = collector.collect(
    source_type="web",
    source_id="https://example.com",
    force_refresh=True
)  # 忽略缓存，重新处理
```

## 10. 搜索和详细内容获取

搜索处理器仅返回搜索结果，不直接获取完整内容。如需获取详细内容，可以结合使用：

```python
# 搜索并获取详细内容
# 1. 先搜索
search_content = collector.collect("search", "Python programming")

# 2. 从结果中提取URLs (例如使用正则表达式)
import re
urls = re.findall(r'\[查看完整内容\]\((https?://[^\)]+)\)', search_content.content)

# 3. 获取感兴趣结果的详细内容
details = {}
for url in urls[:3]:  # 只处理前3个
    try:
        web_content = collector.collect("web", url)
        details[url] = web_content
    except Exception as e:
        print(f"无法获取 {url} 的内容: {str(e)}")

# 4. 现在details包含了详细内容
```

这种解耦方式的优点是：
- 保持处理器职责单一
- 灵活控制详细内容获取范围
- 可以处理搜索结果中不同类型的链接（网页、PDF等）

## 11. 现有代码适配

对于现有使用`info_collector`的代码，我们将采用以下适配方案：

```python
# 在需要使用info_collector的地方
from src.video_agent.tools.content_manager import ContentCollector

# 创建新的收集器实例
collector = ContentCollector(config)

# 获取内容 (替换原来的info_collector调用)
content = collector.collect(source_type, source_id, **kwargs)

# 使用内容 (Markdown格式)
markdown_content = content.content
```

## 12. 优势与收益

1. **更清晰的接口**：职责明确，易于理解和使用
2. **代码简化**：减少重复实现，提高可维护性
3. **统一输出格式**：所有内容都转换为Markdown，简化后续处理
4. **资源管理**：统一处理媒体资源，避免重复下载
5. **简单高效**：单一缓存系统，无过度设计
6. **可扩展性**：轻松添加新的内容处理器
7. **缓存整合**：处理器与缓存解耦，通过统一接口协作
