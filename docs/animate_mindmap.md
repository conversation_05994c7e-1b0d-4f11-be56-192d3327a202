# animate_mindmap

## 效果

创建交互式思维导图，支持节点聚焦、子树展开和动态布局调整。


## 使用场景

- 展示知识结构和概念层次关系
- 教学中的概念图解和思路梳理
- 项目规划和任务分解可视化
- 复杂信息的结构化展示

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| title | str | 当前内容的标题，会显示在内容上方 | 是 | - |
| data_source | str | dict | 思维导图数据源，可以是JSON文件路径或字典数据. 如果复用之前已创建的mindmap（需要提供id），可以不设置 data_source | 否 | None |
| layout_style | str | 布局样式。可选值：'balance'（平衡布局）, 'left_to_right'（从左到右）, 'right_to_left'（从右到左） | 否 | balance |
| max_depth | int | 显示的最大层级深度 | 否 | 3 |
| focus_sequence | list[list[str]] | 按顺序聚焦的节点文本列表，用于引导观众注意力。list中的元素为节点文本和对应的旁白文本 | 否 | None |
| narration | str | 在思维导图显示时播放的语音旁白文本，对思维导图进行整体介绍 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_mindmap",
  "params": {
    "title": "人工智能知识体系",
    "data_source": "{\n  \"标题\": \"人工智能\",\n  \"子章节\": [\n    {\n      \"标题\": \"机器学习\",\n      \"子章节\": [\n        {\"标题\": \"监督学习\"},\n        {\"标题\": \"无监督学习\"},\n        {\"标题\": \"强化学习\"}\n      ]\n    },\n    {\n      \"标题\": \"深度学习\",\n      \"子章节\": [\n        {\"标题\": \"神经网络\"},\n        {\"标题\": \"卷积网络\"},\n        {\"标题\": \"循环网络\"}\n      ]\n    }\n  ]\n}\n",
    "layout_style": "balance",
    "max_depth": 3,
    "id": "AI_mindmap",
    "focus_sequence": [
      [
        "人工智能",
        "让我们从人工智能开始"
      ],
      [
        "机器学习",
        "然后是机器学习"
      ],
      [
        "深度学习",
        "最后是深度学习"
      ]
    ],
    "narration": "让我们通过这个思维导图来了解人工智能的主要分支和技术体系。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_mindmap",
  "params": {
    "title": "机器学习深入分析",
    "id": "AI_mindmap",
    "focus_sequence": [
      [
        "机器学习",
        "让我们从机器学习开始"
      ],
      [
        "监督学习",
        "然后是监督学习"
      ],
      [
        "无监督学习",
        "最后是无监督学习"
      ]
    ],
    "narration": "让我们更深入地了解一下机器学习的两个主要分支，监督学习和无监督学习。"
  }
}
```

### 示例 3

```json
{
  "type": "animate_mindmap",
  "params": {
    "title": "项目结构图",
    "data_source": "assets/mindmap_data.json",
    "layout_style": "left_to_right",
    "max_depth": 2,
    "narration": "这是一个从文件加载的思维导图结构。"
  }
}
```

## 注意事项

- 数据源支持JSON文件路径或直接传入字典格式
- focus_sequence参数可以创建引导式的节点聚焦动画，如果有需要强调的节点才用，没有可以不用。根结点无需作为第一个focus元素，如果有整体的总结性narration，可以将根节点作为最后一个focus元素。
- 如果有focus_sequence元素，每个元素节点尽量配合对应的旁白，并和整体的narration要连贯，过渡流畅，不要有重复内容。
- 思维导图会自动调整布局以适应屏幕尺寸
- 支持中文文本和自动换行
- 思维导图的最佳视觉效果是3级内容（包括根节点），其中第一级和第二级节点的文字不要太多，而第三级节点的文字不要太少，需要能完整体现关键信息。
- 除根节点外，每一级节点的数量尽量不少于3个，一般在3-6个比较好。
