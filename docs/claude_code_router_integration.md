# Claude Code Router 集成文档

## 概述

本文档描述了如何使用Claude Code Router (CCR) 来替代现有的`process_scene_file_enhanced`函数，用于生成Manim代码和视频。

## 文件结构

```
scripts/
├── run_claude_code_router.py    # 主要的CCR运行脚本
├── ccr_integration.py           # 集成模块，提供兼容接口
config/
├── claude_code_router_config.json  # CCR配置文件
├── manim_common_errors.txt         # 常见错误和注意事项
```

## 主要功能

### 1. 自动安装和配置
- 自动检查`ccr`命令是否存在
- 如果不存在，自动安装`@musistudio/claude-code-router`
- 自动复制`config/claude_code_router_config.json`到`~/.claude-code-router/config.json`
- 自动启动CCR服务（`ccr start`）

### 2. 高效任务描述生成
- 读取场景描述文件
- 读取`config/manim_common_errors.txt`，避免重复错误
- 构建高效的任务描述，强调一次性完成，减少token消耗
- 提供具体代码模板和执行策略
- 使用结构化格式和视觉标记提高理解效率

### 3. 优化的执行流程
- 启动CCR服务作为代理
- 通过环境变量配置claude命令
- 直接调用`claude`而不是`ccr code`，避免参数传递问题

### 4. 精确的文件路径处理
- 直接使用传入的`output_file`参数构建代码文件路径
- 不依赖输出日志解析，提高可靠性
- 准确定位生成的视频文件

### 5. 兼容的返回格式
返回与`process_scene_file_enhanced`相同的格式：
```python
{
    "final_code_path": "生成的代码文件路径",
    "final_video_path": "生成的视频文件路径",
    "success": True/False
}
```

## 使用方法

### 方法1：命令行使用
```bash
python scripts/run_claude_code_router.py <scene_file> <output_file>
```

示例：
```bash
python scripts/run_claude_code_router.py scene_vision_v2w.txt v2w
```

### 方法2：Python代码集成
```python
from scripts.ccr_integration import process_scene_file_enhanced_ccr

# 直接替换原有的process_scene_file_enhanced调用
result = process_scene_file_enhanced_ccr(
    scene_description=scene_description,
    output_dir=os.path.join(output_dir, "code"),
    max_iterations=3,
    quality="l",
    scene_num=scene_number,
    topic=topic,
)
```

### 方法3：在theorem_explain_workflow.py中使用
只需要修改导入语句：
```python
# 原来的导入
# from agents.scene_code_generation_agent import process_scene_file_enhanced

# 新的导入
from scripts.ccr_integration import process_scene_file_enhanced_ccr as process_scene_file_enhanced
```

## 配置文件

### claude_code_router_config.json
包含了OpenRouter API配置，支持多种模型：
- Claude Sonnet 4 (默认)
- Gemini 2.5 Flash (后台任务)
- DeepSeek R1 (思考任务)
- 等等

### manim_common_errors.txt
包含了常见的Manim代码错误和注意事项：
- MathTex中不能使用中文
- 导入语句要完整
- 动画对象使用前要添加到场景
- 等等

## 优势

1. **更好的错误处理**：集成了常见错误知识，减少重复错误
2. **更高效的生成**：使用优化的提示词，减少迭代次数
3. **更稳定的输出**：基于成熟的CCR框架
4. **完全兼容**：可以直接替换现有的函数调用
5. **自动化程度高**：自动安装、配置和运行
6. **智能视频检测**：参考原有逻辑，准确检测生成的视频文件路径
7. **精确文件定位**：直接使用输出文件名，不依赖日志解析
8. **优化的提示词**：结构化任务描述，强调效率，减少token消耗
9. **稳定的执行方式**：通过CCR服务代理，避免命令行参数传递问题

## 测试

运行测试脚本：
```bash
python test_ccr_script.py
```

或者测试集成功能：
```bash
python scripts/ccr_integration.py
```

## 注意事项

1. 需要Node.js和npm环境
2. 需要有效的OpenRouter API密钥
3. 确保网络连接正常（可能需要代理）
4. 生成的文件会保存在指定的输出目录中

## 故障排除

### 常见问题

1. **ccr命令未找到**
   - 检查Node.js和npm是否正确安装
   - 检查全局npm包安装路径是否在PATH中

2. **配置文件复制失败**
   - 检查源配置文件是否存在
   - 检查目标目录权限

3. **API调用失败**
   - 检查API密钥是否有效
   - 检查网络连接和代理设置
   - 检查API配额是否充足

4. **CCR命令执行失败**
   - 检查任务描述是否包含shell特殊字符
   - 确保使用正确的CCR命令格式：`ccr code --dangerously-skip-permissions --print [prompt]`
   - 检查CCR版本：`ccr version`

5. **生成的代码有错误**
   - 检查场景描述是否清晰
   - 查看常见错误文件是否需要更新
   - 检查生成的代码是否符合Manim语法

## 扩展

可以通过以下方式扩展功能：
1. 更新`manim_common_errors.txt`添加新的错误模式
2. 修改`claude_code_router_config.json`调整模型配置
3. 在`build_task_description`函数中添加更多上下文信息
4. 添加更多的后处理逻辑来优化生成结果
