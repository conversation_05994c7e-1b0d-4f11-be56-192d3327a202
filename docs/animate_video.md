# animate_video

## 效果

在Manim场景中播放视频文件，支持可选的文本叠加和语音旁白。


## 使用场景

- 在教程或演示中展示屏幕录制或外部视频片段
- 播放动画片段作为更复杂场景的一部分
- 叠加解释性文本或字幕到视频内容上
- 配合旁白同步视频演示

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| video_path | str | 要播放的视频文件的本地路径 | 是 | - |
| title | str | 视频标题 | 否 | None |
| overlay_text | str | 可选的叠加文本，显示在视频之上。多行文本用"\n"分隔 | 否 | None |
| overlay_animation_delay | float | 每行文本动画之间的延迟（秒） | 否 | 1.0 |
| narration | str | 在视频播放时同步播放的语音旁白文本 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_video",
  "params": {
    "video_path": "assets/demo.mp4",
    "narration": "这是一个演示视频。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_video",
  "params": {
    "video_path": "assets/demo.mp4",
    "overlay_text": "重要提示！\n请注意这个关键点",
    "overlay_animation_delay": 0.5,
    "narration": "请注意视频中的这个重点部分。"
  }
}
```

## 注意事项

- 视频文件必须存在且路径正确，否则函数会记录错误并返回
- 视频会自动缩放以适应场景
- 叠加文本会依次显示，每行之间有指定的延迟
