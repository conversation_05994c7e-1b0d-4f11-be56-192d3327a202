
5.13 
按需交付：输入需求->拆解步骤（需求洞察）->{每步骤的功能详细描述->框架选择->代码实现（包括human in the loop）->测试优化}（费曼分镜）-> 可视化呈现
特点：1 端到端交付 2 知识库提准确率，工具/模型-例子代码-API-框架原理-背景知识构建知识库；3 可视化呈现(录屏、代码、例子、基础、框架等)
路径：1 使用一个框架实现具体功能，跑通子环节（6.15）；2 候选多框架实现一个功能，跑通按需交付链路，生成视频（7.15）；3 建立知识库，通过Rag、Agent优化、代码模型微调提准确率，提升链路效果和效率（9.15）； 4 App和平台交互式优化（10.15）
卡点：
    1 单框架自动生成代码效果（聚焦头部框架，跑通的例子做案例，API和自带例子，RAG代码库，theorem_agents分层，手写例子选择）
    2 任务拆解到多个框架选择准确性(框架的描述，框架知识库)；
    3 多框架多环节代码连调准确性；
趋势：MCP取代各种项目框架，减少代码开发量；多种能力逐渐被头部几个模型覆盖，那退化成优化prompt,多次调用模型. 参考windsurf最新功能

5.22
1 框架逻辑: xx2markdown + prompt(含画像意图目的侧重、prompt学习) -> materialAgent(提炼、费曼)->renderAgent(渲染)
2 知识库建设、prompt学习、费曼优化

5.27 manim
1 icon/svg library
2 Wolfram Alpha 等MCP验证准确性
3 丰富manim-physics、manim-chemistry、manim-circuit、manim-ml插件、完善的manim文档、例子RAG库！
4 生成非常详细的prompt适合manim，类似TheromExplainer的流程
5 类似的kodisc、video tutor

7.4 code agent(for manim)
核心功能：根据自然语言生成manim代码，要求运行通过，布局合理，逻辑正确，美观生动
1 自然语言->生成技术描述文案
    1.1 暂时复用theorem agent生成的详细技术文案
        -高度结构化的中间表示 (Intermediate Representation, IR)。这份IR应包含：
            --场景列表 (Scene List): 定义每个场景的主题和时长。
            --对象清单 (Mobject Manifest): 描述每个数学对象（Mobject）的类型、文本（LaTeX）、初始位置、颜色等。
            --动画序列 (Animation Sequence): 按时间线描述每个动画动作，例如 Create(object1)、Transform(object1, object2)、FadeOut(all)。
            --镜头指令 (Camera Instructions): 如平移、缩放等。
            --风格标签 (Style Tags): 如 [style: elegant], [emphasis: highlight_A]，用于指导下面模块。
    1.2 进一步简化描述，以manim友好的伪代码方式IDL描述，让大模型更容易生成manim代码
        -LLM只需要将这些声明式的指令，一对一地翻译成Manim的API调用即可
        -便于后续的“自愈”和“迭代”,直接修改Python代码，可能会因为字符串操作、逻辑依赖等问题引入新的错误。而修改作为“唯一事实来源”（Single Source of Truth）的IDL，可以保证生成过程的一致性
```yaml
scene:
  name: PythagorasTheorem
  duration_seconds: 20
  intent: "Visually prove the Pythagorean theorem."

mobjects:
  - id: triangle
    type: Polygon
    vertices: [[0,0,0], [4,0,0], [0,3,0]]
    style: {stroke_color: WHITE, fill_color: BLUE, fill_opacity: 0.5}
    label:
      text: "Right Triangle"
      position: "bottom"
  - id: sq_a
    type: Square
    side_length: 3
    attaches_to: {edge: "side_a", of_mobject: "triangle"}
    style: {fill_color: RED_C, fill_opacity: 0.7}
    label: {text: "a²", font_size: 48}
  - id: sq_b
    # ... similar definition for square on side b
  - id: sq_c
    # ... similar definition for square on hypotenuse

timeline:
  - timestamp: 0s
    action: Create
    target: triangle
    run_time: 1.5
  - timestamp: 1.5s
    action: Create
    target: [sq_a, sq_b]
    run_time: 2.0
    group: true # AnimationGroup
  - timestamp: 4s
    action: Transform
    source: [sq_a, sq_b]
    target: sq_c # This implies a complex rearrangement animation
    run_time: 3.0
    narrative_link: "Show that area of a² + b² equals c²"

camera:
  - timestamp: 3.5s
    action: zoom
    scale: 0.7
    focus_on: triangle
```

2 技术描述文案->生成manim代码 （先通过非训练方式，再到训练方式）
    2.0 CodeAgent SystemPromt, 把manim常见的坑、最佳规范描述清楚
        -整理常见的错误写法、性能陷阱、布局丑陋的例子
        -布局最佳实践、动画最佳实践、代码风格最佳实践
    2.1 CodeAgent根据技术描述，通过AgenticRAG, 检索函数接口和使用例子，检索相关代码片段、检索manim文档
        -智能检索，当IDL中出现action: Transform, source: [sq_a, sq_b], target: sq_c时，Agent应该主动查询："Manim transform multiple objects into one"
    2.2 使用Dspy优化输入内容做上下文工程，以更贴近manim代码要求
    2.3 使用Feynman的animation库和布局、颜色、字体风格等约束条件，做代码生成
        -美学组件库 (Aesthetics Library): 将漂亮的颜色方案、字体组合、动画曲线、布局模式封装成可复用的高级函数或类。这是风格注入 (Style Injection) 的核心
        - 主题包 (Themes): 创建不同的主题，如Theme3B1B, ThemeElegant, ThemeChalkboard。一个主题包含：
            --调色板 (Color Palette): 定义主色、辅色、高亮色、背景色。
            --字体方案 (Typography): 标题、正文、公式的字体、大小、粗细。
            --动画节奏 (Animation Timing): 默认的run_time、默认的缓动函数（如EASE_IN_OUT_SINE）。
            --布局原语 (Layout Primitives): 封装好的布局函数，如 arrange_in_grid_with_margins。
        -高级动画函数库 (Feynman's Animation Library)
            -FocusOn(mobject, buffer): 自动处理镜头的平移和缩放，以聚焦于某个对象。
            -ExplainThenFade(mobject, text): 创建一个对象，旁边出现解释文字，然后一起淡出。
            -MorphShapeWithText(source, target): 在形状变换的同时，平滑地改变关联的文本。
    2.4 CodeAgent调用大模型生成manim代码
    +训练方式
    2.5 继续训练: 训练数据集，manim文档、manim代码、manim论坛、manim issues
    2.6 后训练: SFT、RLVR、RM

3 代码->运行通过（学习codex,gemini-cli方法）
    3.1 代码能保存到文件
    3.2 先静态检查文件修复基本问题，减少LLM访问
    3.3 CodeAgent能执行文件代码，把错误信息整理发给LLM
    3.4 返回部分修改意见，给出行号区间做替换，降低LLM输出tokens
    3.5 回到3.3直到代码通过,或者达到最大次数

4 视频内容检查
    4.1 多模态大模型检查视频的视觉效果
        -布局合理性: 对象是否重叠？是否居中对齐？留白是否足够？
        -视觉清晰度: 字体大小是否易读？颜色对比度是否足够？
        -动画流畅度: 动画节奏是否过快或过慢？运动曲线是否自然？
        -逻辑一致性: 视频中展示的概念是否与模块一的动画蓝图中的核心意图一致？（例如，蓝图要求“突出圆A”，但视频中圆A并不显眼）
        关键帧提取 (Keyframe Extraction): 不必分析整个视频。在每次self.play结束后提取一帧图像。
    4.2 不符合要求的视频内容，CodeAgent给出修改意见
        -反馈的可操作性，利用IDL进行修改： 最理想的方式是，多模态模型的反馈不是直接修改Python代码，而是生成对IDL的修改建议。例如，返回一个JSON Patch：[{ "op": "replace", "path": "/mobjects/1/style/fill_color", "value": "YELLOW" }]。然后，让模块二根据更新后的IDL重新生成代码。这使得整个流程更加清晰和鲁棒
    4.3 回到3.2,继续执行直到4.2视频内容通过

5 代码优化和进化
    5.1 怎样减少tokens数量和LLM交互？openevovle、codex、gemini-cli
    -从“让LLM做所有事”转变为“让LLM做最关键的事”
        --高度抽象与封装 (Use High-Level Abstractions),反例（低效）：IDL详细描述每个对象的坐标。这会消耗大量Token来描述布局;正例（高效）：IDL只描述对象间的布局关系。具体的坐标计算交给一个确定性的“布局引擎”来处理。LLM只需要生成一行代码 self.add(VGroup(circle, square, triangle).arrange(RIGHT, buff=1.0))，而不是三行独立的 .move_to()。
        --利用默认值和主题 (Defaults & Theming),反例（低效）：每个对象都明确指定颜色、字体大小、动画时长等。正例（高效）：在IDL的顶层定义一个theme或style_defaults。对象描述中只包含与默认值不同的部分。
        --使用“代码脚手架” (Scaffolding),Agent使用模板生成一个“脚手架”文件

        ```python
            from manim import *
            from my_aesthetics_library import * # 导入我们的美学库
            class GeneratedScene(Scene):
                 def construct(self):
                # --- LLM_CODE_START ---
                # LLM只需填充这里
                # --- LLM_CODE_END ---
        ```
        --确定性转换器 (Deterministic Converters),DL中的很多部分根本不需要LLM。例如，一个简单的Circle对象的创建，可以用一个固定的Python函数来处理：对于简单的Create, FadeIn, FadeOut动画，同样可以用模板直接生成
        --引入缓存机制 (Caching),可以为“IDL片段 -> Manim代码”的生成结果建立缓存。当系统再次遇到完全相同的IDL片段时，直接从缓存中读取已生成的代码，完全跳过LLM调用
    -

    5.2 如何设计主题包, 个性化定制manim

    5.3 无中生有+有的放矢如何无缝衔接