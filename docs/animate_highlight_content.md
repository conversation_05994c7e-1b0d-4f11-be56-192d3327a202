# animate_highlight_content

## 效果

按顺序高亮一系列元素，或者对代码对象高亮特定行。


## 使用场景

- 逐步引导观众注意场景中的特定对象
- 逐行解释代码片段，高亮当前讨论的行
- 强调流程图或架构图中的特定组件序列

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| title | str | 当前内容的标题，会显示在内容上方 | 是 | - |
| elements | list[str] | 要高亮的元素ID列表。如果指定lines参数，则此列表应只包含一个Code对象的ID | 是 | - |
| highlight_type | str | 高亮效果类型。可选值：'flash'（闪烁）, 'box'（边框）, 'underline'（下划线）, 'color'（颜色变化） | 否 | box |
| color | str | 高亮效果的颜色（十六进制或颜色名称） | 否 | #FFFF00 |
| duration_per_item | float | 每个元素或代码行组的高亮持续时间（秒） | 否 | 1.0 |
| lines | str | 要高亮的代码行范围，格式如"1-3,5,7-10"。如果提供此参数，elements应只有一个Code对象ID | 否 | None |
| narration | str | 播放动画时同步播放的语音旁白文本 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_highlight_content",
  "params": {
    "elements": [
      "text_obj1",
      "shape_obj2"
    ],
    "highlight_type": "flash",
    "color": "RED",
    "duration_per_item": 0.5,
    "narration": "首先看左边的文本，然后看右边的形状。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_highlight_content",
  "params": {
    "elements": [
      "code_block"
    ],
    "lines": "1-2,3",
    "highlight_type": "box",
    "color": "GREEN",
    "duration_per_item": 1.5,
    "narration": "现在我们来看这段代码。首先是函数定义，然后是注释。"
  }
}
```

## 注意事项

- 被高亮的元素必须已经在场景中存在并且有指定的ID
- 对于代码高亮，lines参数优先于highlight_type
- 高亮效果是暂时的，结束后元素会恢复原始状态
