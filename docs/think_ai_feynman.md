## AI-Feynman定位
### 背景
短视频改变了内容消费习惯，大家适应了快节奏，快思考地获取信息，但知识学习是长时间、深度思考的过程，两者存在矛盾。如果能把复杂的知识，以丰富的多模态方式简单地呈现出来，让知识变得易懂易记住，用户就能用很短的时间来吸收；如果复杂知识能根据用户的知识储备、兴趣偏好等个性化的表达，不在是千篇一律的方式讲解，更容易懂易记住；如果有工具能让用户主动对自己创作感兴趣，不太懂的内容，那获取知识的主动性、灵活性也会更高。新一代AI内容创作和消费的飞轮就能转起来。
### 定位
**通过AI驱动，让知识创作简单，让知识易懂易记住、获取知识平权**。这样需要AI-Feynman能做出一套*AI驱动*的知识生产和消费平台，让任何人在平台上能创作*专业易懂*的内容, 能消费*个性化*讲解内容，能看到*丰富全面*的内容。

## AI-Feynman整体模式（待完善）
AI引擎逻辑、AI内容产品形态、商业模式
### 产品逻辑
产品一：AI教育体系-知识体系完整，新的交互方式
1 AI知识介绍
    定位
    - 面向在读大学生、非AI专业小白、想入门AI的群体, 在零碎时间快速学习AI知识
    功能
    - 5min以内*微课程*讲知识点, 配简单Quiz, 积分闯关, 提升等级
    - 知识点体现*费曼易懂易记住*特点，增强知识之间对比、关联、类比、举例等
    - 完善的*知识分类体系*, 并且不断从AI前沿部分吸纳*新知识*,建立知识之间*关联*形成图谱, 新知识和关联知识收费
    - 鼓励用户主动生成新知识点, 给到积分奖励, 提升等级,别人消费视频给到反馈形成UGC环路
    - 视频侧旁开辟讨论区, 问答区, 呼声高的点，能重新生成视频，个性化内容生成
    素材来源
    - 顶级高校的课程、竞品的课程
    - 经典论文、书籍
    - Github汇总的知识体系，survey等

2 AI应用介绍
    定位
    - 面向求职大学生、想动手应用开发的群体，弥补知识和真实应用之间的鸿沟
    功能
    - 经典*框架的tutorial*, 分3-5期讲解, 讲解基本功能, 框架原理, 核心代码, 安装使用等，具有交互性，参考pocket
    - 基于经典框架*生成真实项目*, 描述场景，自动生成框架内真实应用代码, 运行录屏形成视频,代码+视频售卖给学生，参考codeagent
    - 形成统一的评测体系，自动横向评测对比，*自动生成评测报告*, 参考paperwithcode, 售卖给公司做技术选型参考，撮合招聘优质作者
    - 评论区增加AIbot做答疑，解决安装使用等各种问题，付费1v1咨询
    - 前沿新开源项目引入，讲解原理和使用案例
    素材来源
    - github项目, huggingface项目
    - 付费的平台App,目前需要手动使用录屏

3 AI前沿信息
    定位
    - 面向大众, 解决AI领域信息过载问题, *实时关注前沿信息,缓解焦虑*
    功能
    3.1 新产品体验
        - 实时抓取app排行榜给出简单的介绍，比如toolify, 比如一周新应用排行榜
        - 注重时效性和一手使用体验,付费的需要购买，人工操作
    3.2 新论文发表
        - 前沿论文讲解, 开辟普通论文、综述、技术报告等*类型*，覆盖视觉、金融、Agent、语音、推荐等*方向*
        - 从X、arXiv、huggingface等渠道*自动抓取*前沿论文和对应大佬评价
        - 讲解费曼化易懂易记住, 传递影响力，动机，核心原理和效果等
        - 沉淀成知识图谱，包括认知、实验结论、一句话核心原理、论文meta信息等，便于延展，时间线，综述，对比等，链接到“AI基本知识”
    3.3 新项目开源
        - 从huggingface、github等渠道自动抓取AI相关新项目
        - 开源项目排行榜,能分类检索,增长速度等,链接到”AI应用知识“对应内容
    3.4 新的观点认知
        - 国内外优质博客、公众号、medium、X等渠道抓取
        - 沉淀认知图谱，定期给到独特的综合性分析，比如Agent设计五大原则等，输出有价值的报告，付费获取类似app分析报告

### 产品形态
    生成的视频有什么优势 
        -易懂易记？ 费曼讲解通俗易懂，适合快节奏,高密度快餐
        -更易热门？自动选择或生成封面、标题文案、、整体视频长短（痛点视频怎样才能火）
            --勾子：前几秒动画和话术，引入的问题，惊人的数据，强大的背书
            --情节：提出问题,起因 -> 分析背景和原因（经过）-> 揭示核心论点/解决方案（高潮）-> 总结并引导互动（结尾）
            --可视化：数据表格动态增长、箭头流程图、概念图标化（imoji,icon）、文字（动起来打印机、放大、强调，艺术字）
            --风格：统一颜色/字体/版式/logo/布局等；
            --素材：图标和插画，图标库（Flaticon、Iconfont），插画生成（flux,wan）,图像平移，放大缩小等；
            --听觉：BGM、TTS音色、音效（如Whoosh、Pop、Click，https://mixkit.co/）
        -生产便捷？已有图文搬运成视频（痛点有视频素材的太少，图文能否用起来），自动抓取优质内容（痛点在哪获取创作源）
        -生产更快？2min以内（制作视频时间太长，购买又太贵）
    1 面向AI知识创作者，比如发布论文、最新项目、前沿资讯的需求
        -实时发现论文、项目，比如金融论文，AIgithub
        -提交论文、项目、博客等链接、离线PDF自动生成，Chat去搜索抓取整理次优先级
        -风格可定制，内容要求可定制, 具体编辑内容次优先级
        -音色可定制，包括个性化clone
        -速度快成本低，优化速度到2min以内
    2 面向工作中想学AI知识和技能，比如AI知识体系，应用工具，前沿的信息
    3 面向大学生求职，比如AI面试知识讲解，特别是职场AI应用例子

###产品形态V1
思路： 卖点自主生成，复杂的内容生成易懂的视频；其次是丰富的AI知识体系，信息的时效性
输入区
	输入chat文本: 输入概念，组合概念解释，（无中生有合并到Chat模式） 
		-学生学习知识
	输入arxiv链接：论文，综述；+辅助chat可以是指定部分内容
		-关注前沿进展
		-解惑部分内容
	输入网页链接：技术博客，产品介绍+辅助chat可以是部分 +chat辅助
		-必须保留原创信息，注意版权
	输入本地内容：PDF、Doc、Image、video（视频剪辑暂不支持）+chat作为辅助输入目的；
		-学生视频解题和延展
		-解读读书和卖书
		-短视频用于展现工作
	输入GITHUB项目，想了解什么，项目生成代码例子等，+辅助chat可以是部分，侧重内容
		- AI实践人对项目有什么用，怎么用
		- AI开发项目原理解读
内容区
	分TAB展现概念知识、项目、前沿等，体现时效性【痛点】；
	增加开源排行榜和对应视频；-卖成品
	增加分类热门论文和对应视频；-卖成品
	增加热门网页资讯-卖成品
	增加一些混合内容，比如top5的项目、一周热点新闻等-卖成品
	隐含增加个性化-生成风格，阅读偏好

产品能力突出
    个性化讲解，不同的purpose讲解不一样

收费：免费阅读头部内容，部分优质视频付费阅读、下载视频付费（注意修改和版权安全）、生成视频按月/条，下载给到作者奖励；

和NotebookLM定位差异，定位在短视频
始终重要：@lb
	1 所有讲解要有“费曼灵魂”-简单击中本质
	2 动画效果要“突出”且丝滑-讲解工具动效精美，文案衔接顺畅 
    3 短视频爆款元素建设，吸引人的标题、内容怎么突出、结构简练等
    4 速度机制优化

8月高优:
    4 【产品】无中生有，在AI基础知识、数学概念、计算机算法等丝滑可用 @wt @lb
    5 【基础】音效优化，BGM和动效，TTS音色 @wt
    6 【基础】网页平台的建设，生产、消费、账号体系，前后端打通现在的功能 @ql @xd
    7 【产品】有的放矢，中webpage链接、Doc/PDF的遵循讲解视频（提炼、动效、费曼），支持辅助输入Chat功能 @xd @lb
    8 【基础】有的放矢，速度tokens优化，3min以内，0.5元以内 @xd
    9 【产品】有的放矢，上传图片解题，讲解视频 @lb
    10【产品】整体大模型分流模型 @lb

9月高优:
    11【基础】icon/imoji/animate icon表达抽象概念的关系图 @xd @lb
    12【产品】内容风格可模板化配置，字体，logo,背景图等 @xd
    13【基础】内容增强，搜索wiki，搜索引擎-deepresearch等来加强无中生有的内容灵魂 @wt
    14【基础】通用的图片和视频搜索的素材面向多媒体素材少情况 @ql
    15【基础】录屏工具结合更多网页交互工具补充视频素材 @wt
	16【产品】自动Github AI榜单、论文榜单，形成内容优势 @ql
 
10月高优
    17【基础】根据讲解内容，语义定位到屏幕内容对应地方
    18【产品】支持视频上传功能，比如长视频讲解的自动剪辑摘要，比如youtube内容 
    19【产品】可定制数字人形象的讲解
    20【基础】copy视频生成对应描述prompt
    21【基础】prompt自动学习提升dspy
    22【产品】Github项目生产例子
    


## AI-Feynman核心能力
核心框架为四大块：发现->洞察->费曼->呈现
### 发现
**用户潜在意图**
- 意图补全丰富, NL很难描述清楚复杂需求，AI*准且全面*的补全用户意图，如“BPE算法”，可以是讲原理、讲历史、讲应用
- 画像兴趣能力, AI描述人群对内容体裁、深浅、方式偏好差异做*个性化*内容生产, 如同一主题不同讲解方式

**素材在哪**
- 有价值的内容, AI发掘人工很难获取到的内容，*时效性、权威性内容*，如github热点项目、热点论文、专业博客、书籍
- 内容知识体系, AI建设人工很难获取全的内容，*系统性内容*，如ai知识讲解体系、ai项目的实战教程，计算机算法体系
- 素材类型丰富, AI支持人工很难处理全的内容，*万物markdown*，如支持文本、语音、图片、视频、网页、书籍、项目等多模态素材
[!需要具备能力] 对应系统素材抓取、万物素材解析成markdown、素材初步提炼比如Source Agent，用户意图扩展识别IntentionAgent、Purpos
- 缺少有价值的内容获取能力，目前都人工选和下载，比如github,论文,X热点等，要建设抓取、重要性分析等能力
- 万物markdown，还缺少视频下载、分析能力，有些素材抓取不到，有些解析不全


### 洞察
**内容洞察体系**
- 洞察角度, 同样的素材但purpose不一样，通过AI选择合适*切入角度*，才能有信息量，如同样是哪吒，但百亿票房背后的产业影响和电影里隐喻完全不一样的角度
- 洞察模型, 以AI RolePlay方式支持多角度的*思维模型*，如六顶思考帽子、批判性思考、芒格思考模型等

**内容解析工具**
- 配合内容洞察和费曼讲解，丰富*enhance工具*，在原素材上丰富内容，通过深度思考洞察、内容组织、内容扩充（搜索/图谱/模拟）、举例等工具实现

**丰富素材知识库**
- 洞察需要更高级的内容处理，需要沉淀*知识库*，包括知识体系、知识图谱关联、代码片段库示例、视频或图片素材丰富可视化等等

[!需要具备能力] 对应Material Agent 和Enhance工具，产生一个xx_intro.md文件


### 费曼
**讲解方式易懂**
- 在发现素材和洞察扩充后，怎样打磨*表述和材料组织*，才能让内容讲解和呈现更易懂易吸收，如设计分镜讲述、动效、关系

**凸显内容价值**
- 内容要准确, 知识内容底线是*内容要准确*，如何确保内容准确性，交叉验证，提供出处等
- 内容逻辑完整，表述是连贯，丝滑的，讲解虽然方式易懂，但知识讲解必须具有*完整的逻辑*
- 内容重点突出，分镜的内容需要突出重点，以*尽量短的时间传递重要的信息*

[!需要具备能力] 对应DSL的storyboard.json 生成的Agent

### 呈现
**多模态渲染**
- manim单元库, 以工具内容单元为粒度封装函数，通过AI辅助生成*丰富渲染库*，比如思维导图、时间线，根据*AI复刻UI效果*
- manim无中生有,以用户purpose为目标*AI完整生成*动画代码，打造的是一套CodeAgent+RAG+ICL等*代码生成系统*
- 更多引擎, 通过*自动录屏*或者*代码生成*，调用更多的渲染引擎，比如3d引擎，d3.js库，地图渲染库、neoj4可视化库等
- 多模态元素, TTS语音、数字人、手写动作等

**多模态交互**
- 可交互式内容, 平台互动的重要能力，要易懂易记住，动手实践是不可或缺能力，通过网页交互式引擎，生成*可互动内容*
- 智能问答交互, 通过自然语言*聊内容*的问题，通过*Nocode修改内容*的创作（模板内容->动效参数->动效生成）
- 简单测试答题, 通过问题驱动易记住，简单的问题卡片，形成*微课程*学习方式

[!需要具备能力] 对应现在Render Agent生成manim代码和生成含TTS的视频


### 系统能力
**性能极致优化**
- 视频生产*速度要尽量快*，比如5min以内的视频2min以内完成
- 视频生成*成本要尽量低*，节省不必要的token，不必要的计算资源
- 是否能否*扛起高并发*，失败率尽量低

**数据集建设**
- *评测数据集*建设，便于后续性能和效果优化做回测
- *知识库建设*，沉淀优秀案例、讲解知识体系、优秀代码库，便于进化系统

**系统的智能化**
- 系统的*自我进化环路*，知识-数据-模型-Agent-MultiAgent的自我进化环路
- 持续打磨，打造代码模型、渲染模型、Agent扩展学习的*效果壁垒*

## AI-Feynman平台建设


## AI-Feynman整体节奏

---

# AI产品力的思考
## 第一部分：理科领域 (AI、计算机、数学、物理)
对于这类“硬核”理科知识，短视频的角色是 **“翻译官”和“可视化引擎”**，它将抽象、非直观的逻辑，转化为人类大脑最容易理解的视觉信号。

**核心刚需**：降低认知门槛，对抗“反人性”的学习过程
理科学习天然是“反人性”的，它要求高度的抽象思维和逻辑能力。这直接导致了三个核心痛点，而短视频正是解决这些痛点的“特效药”。

### 痛点一：“抽象”的恐惧感

**用户画像**： 初学者、学生、跨专业学习者。

**真实写照**： 看到满篇的数学公式（如麦克斯韦方程组）、复杂的算法（如动态规划）、物理学概念（如波函数坍缩），大脑会本能地产生畏惧和抗拒，这是学习的第一道“劝退”门槛。

**短视频解决方案**： 可视化。用一个60秒的manim风格动画，将一个排序算法的执行过程、一个神经网络的梯度下降、一个函数的傅里叶变换过程清晰地呈现出来。视频把“抽象的逻辑”翻译成了“直观的动画”，瞬间瓦解了用户的恐惧感，建立了学习的兴趣和直觉。

### 痛点二：“迭代”的焦虑感

**用户画像**： 程序员、AI工程师、科研人员。

**真实写照**： AI和计算机领域日新月异，每天都有新的论文、框架和工具发布。从业者普遍存在“知识焦虑”，担心自己跟不上时代，但又没有精力去阅读每一篇长篇论文或官方文档。

**短视频解决方案**： 高密度信息快餐。例如“一分钟速读一篇CVPR论文”、“30秒看懂GPT-4o的新特性”、“一个让你代码效率翻倍的Python新库”。短视频以其极高的制作和传播效率，成为追踪前沿动态的最佳媒介。

### 痛点三：“脱节”的无力感

**用户画像**： 理论学习者（学生）和产业实践者（工程师）。

**真实写照**： 学校里学的理论知识（如操作系统原理）和工作中实际用的工具（如Docker、K8s）之间存在鸿沟。学生不知道理论有何用，工程师不清楚底层原理。

**短视频解决方案**： 连接理论与实践的桥梁。用一个短视频演示“如何用Docker容器化一个你刚用Python写的Web应用”，或者解释“你每天用的HTTPS加密，背后是这些数学原理”。它能快速建立起理论与实践的联系。

### 适合的内容形态
- 核心王牌：过程可视化。 算法执行、数据结构变化、物理定律演示、公式推演。
- 代码片段秀 (Code Snippet Demo): 一个实用的函数、一个惊艳的数据可视化图表代码。
- “论文/概念一分钟”: 快速解读一篇前沿论文或一个技术概念。
- 工具“神操作”: 一个IDE的提效插件、一个AI工具的隐藏用法。
- 思想实验与悖论: 如“薛定谔的猫”、“双缝干涉实验”，用动画呈现引人深思。

## 第二部分：文科领域 (历史、政治知识)
对于这类信息庞杂、强调叙事和关联的文科知识，短视频的角色是 **“时间机器”和“关系图谱”**，它将散落、枯燥的知识点，重构为引人入胜的故事线和清晰的逻辑链。

**核心刚需**：重构叙事方式，对抗“枯燥”和“混乱”
文科学习的核心痛点在于信息量巨大，且各元素之间关系复杂，容易让人感到枯燥和混乱。

### 痛点一：“线性”叙事的枯燥感

用户画像： 学生、历史爱好者。

真实写照： 传统历史教科书按照时间线平铺直叙，充满了需要死记硬背的人名、地名、年份，容易让人感到乏味。

短视频解决方案： 非线性、多维度的动态叙事。最强大的工具就是动态地图（Animated Map）。例如，“一张地图看懂三国分合”、“2分钟看完罗马帝国的扩张与衰落”。动态地图将时间压缩，将空间变化可视化，带来了上帝视角般的震撼感和清晰感，远比文字描述更吸引人。

### 痛点二：“碎片化”的混乱感

用户画像： 新闻关注者、普通大众。

真实写照： 看到一则国际新闻（如某地冲突），想了解其来龙去脉，但背景信息分散在各种文章里，盘根错节，理不清头绪。

短视频解决方案： “知识背景”快速打包。用一个90秒的短视频，通过简洁的动画和逻辑图，快速梳理清楚一个事件的“前情提要”，比如“读懂中东局势，你必须了解的三个历史节点”。它提供了理解当下时事所必需的“即时知识背景板”。

### 痛点三：“距离感”导致的无感

用户画像： 对历史政治不感兴趣的年轻人。

真实写照： 觉得历史人物和政治概念离自己很遥远，是“老古董”，与自己无关。

短视频解决方案： 建立“与我有关”的连接。例如，“如果你是古代的皇帝，你会如何应对这次天灾？”、“用《权力的游戏》看懂欧洲中世纪政治”。通过代入感强的视角和流行文化的类比，拉近用户与知识的距离。

### 适合的内容形态
- 核心王牌：动态地图和时间轴。 战争推演、疆域变迁、人物迁徙路线。
- 人物关系图谱: 用动画清晰展示复杂的人物关系，如“一张图看懂清朝九子夺嫡”。
- “名词解释”: “一分钟看懂什么是‘地缘政治’”、“30秒区分‘社会主义’与‘社会民主主义’”。
- “如果……会怎样？”: 提出有趣的历史假设，进行推演。
- 文物/古迹活化: 让一件文物“开口说话”，讲述它背后的历史故事。

## 总结：殊途同归
无论是理科还是文科，知识短视频的成功都遵循一个核心逻辑：将深度、复杂的知识，转化为符合现代人信息接收习惯的、高密度、可视化的产品。

理科的核心是 **“化抽象为直观”**，解决的是“看不懂、难想象”的痛点。

文科的核心是 **“化混乱为秩序”**，解决的是“记不住、理不清”的痛T点。

一个能够同时服务好这两类需求的知识视频创作工具，其潜力无疑是巨大的。它既需要有manim那样的逻辑可视化能力，也需要有动态地图和叙事动画的引擎，本质上是为这个时代最高价值的资产——知识，提供了最高效的传播解决方案。
