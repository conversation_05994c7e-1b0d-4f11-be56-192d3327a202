# Claude Code Router 集成使用指南

## 快速开始

### 1. 命令行使用
```bash
# 基本用法
python scripts/run_claude_code_router.py scene_description.txt output_filename

# 示例
python scripts/run_claude_code_router.py test_scene_description.txt hello_manim
```

### 2. 在现有代码中替换
只需要修改一行导入语句：

**原来的代码：**
```python
from agents.scene_code_generation_agent import process_scene_file_enhanced
```

**新的代码：**
```python
from scripts.ccr_integration import process_scene_file_enhanced_ccr as process_scene_file_enhanced
```

其他代码完全不需要修改！

### 3. 直接调用
```python
from scripts.ccr_integration import process_scene_file_enhanced_ccr

result = process_scene_file_enhanced_ccr(
    scene_description="你的场景描述",
    output_dir="output/generated",
    scene_num=1,
    topic="demo"
)

print(f"成功: {result['success']}")
print(f"代码: {result['final_code_path']}")
print(f"视频: {result['final_video_path']}")
```

## 文件说明

| 文件 | 说明 |
|------|------|
| `scripts/run_claude_code_router.py` | 主脚本，处理CCR安装、配置和运行 |
| `scripts/ccr_integration.py` | 集成模块，提供兼容接口 |
| `config/claude_code_router_config.json` | CCR配置文件 |
| `config/manim_common_errors.txt` | 常见错误和注意事项 |
| `examples/theorem_explain_workflow_ccr.py` | 使用示例 |

**注意**：所有路径都使用相对路径，基于当前工作目录。

## 返回格式

```python
{
    "final_code_path": "生成的Python代码文件路径",
    "final_video_path": "生成的MP4视频文件路径",
    "success": True  # 是否成功生成代码和视频
}
```

## 优势

- ✅ **零配置**：自动安装和配置CCR
- ✅ **智能提示**：集成常见错误知识
- ✅ **完全兼容**：可直接替换现有函数
- ✅ **更稳定**：基于成熟的CCR框架
- ✅ **更高效**：优化的提示词减少迭代
- ✅ **更精确**：直接使用文件名，不依赖日志解析
- ✅ **更智能**：优化的提示词设计，减少token消耗
- ✅ **更稳定**：通过CCR服务代理，避免命令行参数问题

## 测试

```bash
# 测试基本功能
python test_ccr_script.py

# 测试集成功能
python scripts/ccr_integration.py

# 运行完整演示
python examples/theorem_explain_workflow_ccr.py
```

## 故障排除

1. **安装问题**：确保有Node.js和npm
2. **API问题**：检查OpenRouter API密钥
3. **网络问题**：可能需要配置代理
4. **权限问题**：确保有文件写入权限

## 配置自定义

可以修改以下文件来自定义行为：
- `config/claude_code_router_config.json`：API配置
- `config/manim_common_errors.txt`：错误知识库
- `scripts/run_claude_code_router.py`：任务描述模板
