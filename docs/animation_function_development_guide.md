# 新增动画函数开发指南

本文档为开发新的动画函数提供指导，确保IDE（如Windsurf、Cursor）和我们的自动化文档工具能够保持一致性并正确记录。

## 参考rule文件开发规范文档

在开发新的动画函数时，可以在IDE（Windsurf、Cursor）的对话框中，引用这个rule文件`/.cursor/rules/add-new-animation-function.mdc`，然后描述需要开发的函数的具体功能，让大模型根据这个规则生成符合我们项目标准的代码。

## 效果优化

生成的文件是一个独立的动画函数，包括文档、测试用例、具体实现。

要运行测试用例，可以从根目录运行以下命令：

```bash
python -m scripts.dsl_tool --module 新函数名 -r
```

如果测试效果不理想，可以让大模型修改动画函数的实现，然后再次运行测试用例。

## 更新文档

在添加或修改动画函数后，更新主要的动画函数文档至关重要。这可以通过 `dsl_tool.py` 脚本完成。

从项目根目录运行以下命令：

```bash
python -m scripts.dsl_tool -a
```

此命令将：
1.  解析所有动画函数。
2.  提取YAML格式的文档。
3.  使用最新信息更新 `docs/animation_functions.md` 文件。

然后提交这个文件即可。

通过遵循这些步骤，您可以确保我们的代码库保持良好的文档记录、一致性且易于维护。
