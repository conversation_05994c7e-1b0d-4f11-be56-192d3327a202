
# Manim代码生成规范

## 基础要求
- 继承 `ProfessionalScienceTemplate` 类，重写 `construct` 方法
- 使用标准区域接口：
  - `self.create_title_region_content(string)` - 标题区域
  - `self.create_step_region_content(string)` - 步骤区域  
  - `self.create_main_region_content(VGroup)` - 主内容区域
  - `self.create_right_auxiliary_content` - 辅助区域
  - `self.create_result_region_content(string)` - 结果区域

## 关键规范
1. **元素组织**：所有元素必须在 `create_main_region_content` 之前定义完成，包括后期出现的高亮框、说明文字等，并且都添加到self.region_elements['main']中用于退场
2. **布局定位**：VGroup内元素使用相对位置（`next_to`、`align_to`、`arrange`），禁用绝对位置(`move_to`)
3. **动画控制**：使用`ReplacementTransform()`进行元素变化，而不是fadein+fadeout，**重要**禁止用copy()和Transform()函数
4. **文字限制**：标题≤8字，步骤≤12字，辅助标题≤6字，结果≤20字
5. **代码逻辑**：必须遵循代码模版的6个逻辑编写代码，1. 定义标题和步骤区域内容，2.定义主区域元素内容(数学图形提前计算坐标)，3. 计算主区域元素动画坐标(animate函数必须有这一步)，4. 标题和步骤区域内容动画，5. 主内容区域动画，6.记录函数结束时各区域元素状态
6. **时间记录**：在阶段开始时必须通过log_animation_timestamp记录阶段开始的时间戳。

## 代码模板
```python
import sys, os
sys.path.insert(0, os.getcwd())
from prompts.professional_science_template import ProfessionalScienceTemplate

class MyExample(ProfessionalScienceTemplate):
    def construct(self):
        # 背景
        self.setup_background()
        # 阶段一
        self.create_stage1_content()
        # 阶段二
        self.create_stage2_content()

    def create_stage2_content(self):

        # 1. 阶段开始时间戳记录
        self.log_animation_timestamp("阶段记录：阶段二")

        # 2. 定义标题和步骤区域内容
        title = self.create_title_region_content("标题")
        step = self.create_step_region_content("步骤")

        # 3. 定义主区域元素内容
        // 获取当前缓存的主区域元素
        main_group = self.region_elements['main']
        // 使用缓存的主区域元素
        node = main_group[0][1]
        // 新创建主区域元素
        main_group = VGroup(element1, element2, highlight_box)
        main = self.create_main_region_content(main_group)
        
        # 4. 标题和步骤区域内容动画
        self.play(ReplacementTransform(self.region_elements['title'], title))
        self.play(ReplacementTransform(self.region_elements['step'], step))

        # 5. 主内容区域动画
        // 主内容区域元素切换
        self.play(ReplacementTransform(self.region_elements['main'], element1))
        // 剩余元素入场
        self.play(FadeIn(element2))
        self.play(FadeIn(highlight_box))

        # 6.记录函数结束时各区域元素状态
        self.region_elements['title'] = title
        self.region_elements['step'] = step
        self.region_elements['main'] = main

    
```