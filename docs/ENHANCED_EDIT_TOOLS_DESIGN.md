# Enhanced File Editing Tools 设计方案

## 📋 概述

基于对Augment和Cursor编辑工具的深入分析，设计一个融合两者精华的文件编辑工具体系。该方案旨在将编辑成功率从当前的50-65%提升到90-95%，从而实现整体代码生成成功率25-40%的显著提升。

## 🔍 现状分析

### 当前工具的问题
1. **精确定位缺失**：只能做模糊字符串匹配，容易匹配错位置
2. **批量编辑不支持**：一次只能做一个修改，需要多次调用
3. **错误恢复能力差**：失败后没有智能重试机制
4. **空白字符敏感**：缩进、空格不匹配就失败

### 失败场景分析
- **重复代码匹配错误**：15-20%的编辑失败
- **空白字符问题**：10-15%的编辑失败
- **大块修改失败**：5-10%的编辑失败
- **错误恢复困难**：5%的任务彻底失败
- **总计**：35-50%的编辑相关失败

## 🏗️ Augment vs Cursor 工具分析

### Augment str-replace-editor 优势
```python
# 精确行号定位，避免匹配歧义
old_str_start_line_1: int
old_str_end_line_1: int

# 批量编辑支持
old_str_1, new_str_1  # 第一个编辑
old_str_2, new_str_2  # 第二个编辑

# 严格匹配要求
# 要求EXACT match，包括空白字符

# 150行限制，防止过大修改
instruction_reminder: "ALWAYS BREAK DOWN EDITS..."
```

**推测实现方式**：
- 使用行号进行精确定位
- 先验证old_str是否在指定行号范围内完全匹配
- 按行号倒序执行批量编辑，避免偏移问题
- 严格的验证机制和错误提示

### Cursor 双工具策略优势

#### edit_file
```python
# 智能上下文理解
code_edit: str  # 使用 "// ... existing code ..." 标记
instructions: str  # 编辑说明

# 专门的应用模型
# 有"less intelligent model"来应用编辑
```

#### search_replace
```python
# 强制上下文要求
old_string: str  # 必须包含3-5行上下文
new_string: str
# 确保唯一性匹配，一次只处理一个实例
```

**推测实现方式**：
- 使用模板匹配和上下文理解
- 智能解析特殊标记
- 专门的应用模型处理编辑逻辑
- reapply机制处理失败

## 🎯 融合设计方案

### 核心架构：三层工具体系

```
Enhanced File Editing System
├── 🎯 主工具: enhanced_str_replace_editor
│   ├── 精确行号定位（基于Augment）
│   ├── 批量编辑支持
│   ├── 严格匹配验证
│   └── 智能错误分析
├── 🔍 辅助工具: context_search_replace
│   ├── 上下文感知匹配（基于Cursor）
│   ├── 模糊匹配支持
│   ├── 大文件优化
│   └── 唯一性保证
├── 🧠 智能选择器: smart_file_editor
│   ├── 自动策略选择
│   ├── 场景适应性
│   ├── 失败自动fallback
│   └── 性能优化
└── 🛡️ 兼容层: 现有工具fallback
    ├── 向后兼容保证
    ├── 最后安全网
    └── 渐进式迁移
```

## 🔧 详细API设计

### 1. 主工具：enhanced_str_replace_editor

```python
def enhanced_str_replace_editor(
    path: str,
    command: str,  # "str_replace" or "insert"
    instruction_reminder: str,  # 强制提醒，防止过大修改

    # 第一个编辑操作
    old_str_1: str = "",
    new_str_1: str = "",
    old_str_start_line_1: int = 0,
    old_str_end_line_1: int = 0,

    # 第二个编辑操作（可选）
    old_str_2: str = "",
    new_str_2: str = "",
    old_str_start_line_2: int = 0,
    old_str_end_line_2: int = 0,

    # 插入操作
    insert_line_1: int = 0,
    insert_str_1: str = "",

    # 高级选项
    verify_context: bool = True,
    max_edit_lines: int = 150,
    dry_run: bool = False
) -> str:
    """
    精确的文件编辑工具，融合Augment的精确性

    核心特性：
    1. 精确行号定位，避免匹配歧义
    2. 批量编辑支持，一次完成多个修改
    3. 严格的EXACT match验证
    4. 智能的行号偏移处理
    5. 详细的错误分析和建议

    使用示例：
    enhanced_str_replace_editor(
        path="scene.py",
        command="str_replace",
        instruction_reminder="ALWAYS BREAK DOWN EDITS INTO SMALLER CHUNKS OF AT MOST 150 LINES EACH.",
        old_str_1="def construct(self):",
        new_str_1="def construct(self):\n        # Initialize scene objects",
        old_str_start_line_1=15,
        old_str_end_line_1=15,
        old_str_2="self.play(Write(text))",
        new_str_2="self.play(Write(text), run_time=2)",
        old_str_start_line_2=25,
        old_str_end_line_2=25
    )
    """
```

### 2. 辅助工具：context_search_replace

```python
def context_search_replace(
    file_path: str,
    old_string: str,  # 包含3-5行上下文的完整字符串
    new_string: str,
    context_lines_before: int = 3,
    context_lines_after: int = 3,
    verify_unique: bool = True,
    fuzzy_match: bool = False
) -> str:
    """
    上下文感知的搜索替换，融合Cursor的智能性

    核心特性：
    1. 强制上下文要求，确保唯一性
    2. 智能模糊匹配（可选）
    3. 多实例检测和处理
    4. 适合大文件和复杂匹配场景

    使用示例：
    context_search_replace(
        file_path="scene.py",
        old_string="    def construct(self):\n        text = Text('Hello')\n        self.add(text)",
        new_string="    def construct(self):\n        text = Text('Hello World')\n        self.add(text)",
        context_lines_before=2,
        context_lines_after=2
    )
    """
```

### 3. 智能选择器：smart_file_editor

```python
def smart_file_editor(
    path: str,
    edit_description: str,
    **edit_params
) -> str:
    """
    智能选择最适合的编辑工具

    决策逻辑：
    - 小文件 + 精确编辑 → enhanced_str_replace_editor
    - 大文件 + 模糊匹配 → context_search_replace
    - 批量编辑 → enhanced_str_replace_editor
    - 失败时自动fallback

    自动策略选择：
    1. 分析文件大小和编辑复杂度
    2. 选择最适合的工具
    3. 执行编辑，支持自动fallback
    4. 记录性能数据，持续优化
    """
```

## 🔄 核心实现逻辑

### 1. 精确行号编辑器核心算法

```python
class EnhancedStrReplaceEditor:
    def execute_edits(self, path: str, edits: List[EditOperation]) -> str:
        """执行批量编辑操作的核心流程"""

        # 1. 输入验证阶段
        self._validate_edits(edits)
        self._check_edit_limits(edits)
        self._detect_overlapping_ranges(edits)

        # 2. 文件读取和索引建立
        file_lines = self._read_file_with_line_numbers(path)
        original_content = '\n'.join(file_lines)

        # 3. 精确匹配验证
        for edit in edits:
            if not self._verify_exact_match(file_lines, edit):
                error_analysis = self._analyze_match_failure(edit, file_lines)
                raise EditMatchError(error_analysis)

        # 4. 智能批量编辑执行
        # 按行号倒序排序，避免行号偏移问题
        sorted_edits = sorted(edits, key=lambda x: x.start_line, reverse=True)

        for edit in sorted_edits:
            file_lines = self._apply_single_edit(file_lines, edit)
            self._validate_edit_result(file_lines, edit)

        # 5. 结果验证和输出
        new_content = '\n'.join(file_lines)
        self._write_file_safely(path, new_content)

        return self._generate_edit_summary(original_content, new_content, edits)
```

### 2. 智能错误分析和恢复

```python
def _analyze_match_failure(self, expected: str, actual: str) -> str:
    """分析匹配失败的原因，提供具体的修复建议"""

    suggestions = []

    # 1. 空白字符差异检测
    if expected.strip() == actual.strip():
        suggestions.append("🔍 空白字符不匹配：请检查缩进、空格和换行符")
        suggestions.append(f"   期望缩进：{self._analyze_indentation(expected)}")
        suggestions.append(f"   实际缩进：{self._analyze_indentation(actual)}")

    # 2. 行数差异检测
    expected_lines = expected.split('\n')
    actual_lines = actual.split('\n')
    if len(expected_lines) != len(actual_lines):
        suggestions.append(f"📏 行数不匹配：期望{len(expected_lines)}行，实际{len(actual_lines)}行")

    # 3. 内容相似度分析
    similarity = self._calculate_similarity(expected, actual)
    if similarity > 0.8:
        suggestions.append(f"✨ 内容相似度{similarity:.1%}，可能是微小差异导致")
        suggestions.append("💡 建议：复制实际内容作为old_str，或使用context_search_replace")

    # 4. 生成详细的差异对比
    import difflib
    diff = '\n'.join(difflib.unified_diff(
        expected.splitlines(), actual.splitlines(),
        fromfile='期望内容', tofile='实际内容', lineterm=''
    ))

    # 5. 提供可行的修复方案
    fix_suggestions = self._generate_fix_suggestions(expected, actual)

    return f"""
❌ 编辑匹配失败详细分析：

📊 差异对比：
{diff}

🔧 问题诊断：
{chr(10).join(suggestions)}

💡 修复建议：
{chr(10).join(fix_suggestions)}

🔄 替代方案：
1. 使用 context_search_replace 工具进行模糊匹配
2. 重新获取准确的行号和内容
3. 分解为更小的编辑操作
"""
```

### 3. 智能工具选择逻辑

```python
class SmartFileEditor:
    def _choose_edit_strategy(self, file_size: int, edit_complexity: str, params: dict) -> str:
        """智能选择编辑策略的决策树"""

        # 决策因子分析
        factors = {
            'file_size': file_size,
            'has_line_numbers': 'old_str_start_line_1' in params,
            'is_batch_edit': 'old_str_2' in params or len([k for k in params if k.startswith('old_str_')]) > 1,
            'has_context': len(params.get('old_str_1', '').split('\n')) > 3,
            'is_fuzzy_match': edit_complexity in ['fuzzy', 'approximate']
        }

        # 决策逻辑
        if factors['file_size'] > 2500:  # 大文件
            if factors['is_fuzzy_match'] or not factors['has_line_numbers']:
                return "context_search_replace"
            else:
                return "enhanced_str_replace_editor"

        elif factors['is_batch_edit']:  # 批量编辑
            return "enhanced_str_replace_editor"

        elif factors['has_line_numbers']:  # 有精确行号
            return "enhanced_str_replace_editor"

        elif factors['has_context']:  # 有丰富上下文
            return "context_search_replace"

        else:  # 默认策略
            return "enhanced_str_replace_editor"

    def _try_fallback_strategy(self, path: str, edit_description: str, params: dict) -> str:
        """失败时的智能fallback策略"""

        fallback_sequence = [
            "context_search_replace",  # 先尝试上下文匹配
            "enhanced_str_replace_editor",  # 再尝试精确编辑
            "simple_file_edit_diff"  # 最后使用现有工具
        ]

        for strategy in fallback_sequence:
            try:
                logger.info(f"尝试fallback策略: {strategy}")
                return self._execute_strategy(strategy, path, params)
            except Exception as e:
                logger.warning(f"Fallback策略 {strategy} 失败: {e}")
                continue

        raise AllStrategiesFailedError("所有编辑策略都失败了")
```

## 📊 预期效果和验证

### 性能提升目标
| 指标 | 当前状态 | 优化目标 | 提升幅度 |
|------|----------|----------|----------|
| 编辑成功率 | 50-65% | 90-95% | +25-40% |
| 批量编辑效率 | 需要多次调用 | 一次完成 | +300% |
| 错误恢复率 | 5% | 80% | +1500% |
| 大文件处理 | 效率低 | 专门优化 | +200% |
| 用户体验 | 频繁失败 | 稳定可靠 | +400% |

### 关键成功因素
1. **精确性**：行号定位彻底解决匹配歧义
2. **效率性**：批量编辑减少工具调用次数
3. **智能性**：自动选择最适合的编辑策略
4. **可靠性**：多层fallback机制确保稳定
5. **兼容性**：保持向后兼容，渐进式升级

## 🚀 实施计划

### Phase 1: 核心工具实现（Week 1）
- [ ] 实现 `enhanced_str_replace_editor` 核心功能
- [ ] 精确行号定位和批量编辑
- [ ] 严格匹配验证和错误分析
- [ ] 基础测试和验证

### Phase 2: 辅助工具和智能选择（Week 2）
- [ ] 实现 `context_search_replace` 工具
- [ ] 开发 `smart_file_editor` 智能选择器
- [ ] 集成fallback机制
- [ ] 性能优化和测试

### Phase 3: 集成和部署（Week 3）
- [ ] 与现有系统集成
- [ ] 更新提示词和使用指导
- [ ] 全面测试和验证
- [ ] 渐进式部署和监控

## 🔧 技术实现规范

### 1. 数据结构定义

```python
@dataclass
class EditOperation:
    """编辑操作的数据结构"""
    operation_type: str  # "str_replace" or "insert"
    old_str: str
    new_str: str
    start_line: int
    end_line: int
    operation_id: str = field(default_factory=lambda: str(uuid.uuid4()))

@dataclass
class MatchLocation:
    """匹配位置的数据结构"""
    start_line: int
    end_line: int
    content: str
    confidence: float
    context_before: str
    context_after: str

@dataclass
class EditResult:
    """编辑结果的数据结构"""
    success: bool
    message: str
    operations_applied: List[EditOperation]
    lines_changed: int
    execution_time: float
    fallback_used: bool = False
```

### 2. 错误处理体系

```python
class EditToolError(Exception):
    """编辑工具基础异常类"""
    pass

class EditMatchError(EditToolError):
    """匹配失败异常"""
    def __init__(self, message: str, expected: str, actual: str, suggestions: List[str]):
        super().__init__(message)
        self.expected = expected
        self.actual = actual
        self.suggestions = suggestions

class MultipleMatchError(EditToolError):
    """多重匹配异常"""
    def __init__(self, message: str, matches: List[MatchLocation]):
        super().__init__(message)
        self.matches = matches

class NoMatchError(EditToolError):
    """无匹配异常"""
    pass

class EditValidationError(EditToolError):
    """编辑验证异常"""
    pass
```

### 3. 配置管理

```python
@dataclass
class EditToolConfig:
    """编辑工具配置"""
    max_edit_lines: int = 150
    max_batch_operations: int = 10
    verify_context: bool = True
    enable_fuzzy_match: bool = False
    fuzzy_match_threshold: float = 0.8
    context_lines_before: int = 3
    context_lines_after: int = 3
    enable_auto_fallback: bool = True
    large_file_threshold: int = 2500
    backup_original: bool = True
    dry_run_by_default: bool = False

    # 性能配置
    max_file_size_mb: int = 50
    timeout_seconds: int = 30
    enable_caching: bool = True

    # 安全配置
    allowed_file_extensions: List[str] = field(default_factory=lambda: ['.py', '.js', '.ts', '.java', '.cpp'])
    forbidden_patterns: List[str] = field(default_factory=lambda: ['rm -rf', 'del /f'])
```

### 4. 性能优化策略

```python
class PerformanceOptimizer:
    """性能优化器"""

    def __init__(self):
        self.file_cache = {}
        self.line_index_cache = {}

    def optimize_batch_edits(self, edits: List[EditOperation]) -> List[EditOperation]:
        """优化批量编辑的执行顺序"""

        # 1. 按行号倒序排序，避免偏移问题
        sorted_edits = sorted(edits, key=lambda x: x.start_line, reverse=True)

        # 2. 检测和合并相邻的编辑操作
        merged_edits = self._merge_adjacent_edits(sorted_edits)

        # 3. 优化重叠区域的处理
        optimized_edits = self._resolve_overlapping_edits(merged_edits)

        return optimized_edits

    def build_line_index(self, file_path: str) -> Dict[int, str]:
        """构建文件的行号索引，提高大文件处理效率"""

        if file_path in self.line_index_cache:
            return self.line_index_cache[file_path]

        line_index = {}
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line_index[line_num] = line.rstrip('\n\r')

        self.line_index_cache[file_path] = line_index
        return line_index
```

### 5. 测试和验证框架

```python
class EditToolValidator:
    """编辑工具验证器"""

    def __init__(self):
        self.test_cases = []
        self.performance_metrics = {}

    def validate_edit_accuracy(self, tool_instance, test_cases: List[dict]) -> Dict[str, float]:
        """验证编辑准确性"""

        results = {
            'success_rate': 0.0,
            'exact_match_rate': 0.0,
            'error_recovery_rate': 0.0,
            'performance_score': 0.0
        }

        successful_edits = 0
        exact_matches = 0
        recovered_errors = 0
        total_time = 0

        for test_case in test_cases:
            start_time = time.time()

            try:
                result = tool_instance.execute_edit(**test_case['params'])
                execution_time = time.time() - start_time
                total_time += execution_time

                if result.success:
                    successful_edits += 1

                    # 验证结果的准确性
                    if self._verify_edit_result(test_case, result):
                        exact_matches += 1

            except Exception as e:
                # 测试错误恢复能力
                if self._test_error_recovery(tool_instance, test_case, e):
                    recovered_errors += 1

        total_tests = len(test_cases)
        results['success_rate'] = successful_edits / total_tests
        results['exact_match_rate'] = exact_matches / total_tests
        results['error_recovery_rate'] = recovered_errors / (total_tests - successful_edits) if total_tests > successful_edits else 0
        results['performance_score'] = total_tests / total_time if total_time > 0 else 0

        return results

    def benchmark_performance(self, tools: Dict[str, Any], test_scenarios: List[dict]) -> Dict[str, Dict[str, float]]:
        """性能基准测试"""

        benchmark_results = {}

        for tool_name, tool_instance in tools.items():
            tool_results = {}

            for scenario in test_scenarios:
                scenario_name = scenario['name']
                test_data = scenario['data']

                # 执行性能测试
                start_time = time.time()
                success_count = 0

                for test_case in test_data:
                    try:
                        result = tool_instance.execute_edit(**test_case)
                        if result.success:
                            success_count += 1
                    except Exception:
                        pass

                execution_time = time.time() - start_time

                tool_results[scenario_name] = {
                    'success_rate': success_count / len(test_data),
                    'avg_time_per_edit': execution_time / len(test_data),
                    'throughput': len(test_data) / execution_time
                }

            benchmark_results[tool_name] = tool_results

        return benchmark_results
```

### 6. 监控和日志系统

```python
class EditToolMonitor:
    """编辑工具监控系统"""

    def __init__(self, config: EditToolConfig):
        self.config = config
        self.metrics = {
            'total_edits': 0,
            'successful_edits': 0,
            'failed_edits': 0,
            'fallback_used': 0,
            'avg_execution_time': 0.0,
            'error_types': defaultdict(int)
        }

    def log_edit_operation(self, operation: EditOperation, result: EditResult):
        """记录编辑操作"""

        self.metrics['total_edits'] += 1

        if result.success:
            self.metrics['successful_edits'] += 1
        else:
            self.metrics['failed_edits'] += 1

        if result.fallback_used:
            self.metrics['fallback_used'] += 1

        # 更新平均执行时间
        current_avg = self.metrics['avg_execution_time']
        total_ops = self.metrics['total_edits']
        self.metrics['avg_execution_time'] = (current_avg * (total_ops - 1) + result.execution_time) / total_ops

        # 记录详细日志
        logger.info(f"编辑操作完成: {operation.operation_id}", extra={
            'operation_type': operation.operation_type,
            'success': result.success,
            'execution_time': result.execution_time,
            'lines_changed': result.lines_changed,
            'fallback_used': result.fallback_used
        })

    def generate_performance_report(self) -> Dict[str, Any]:
        """生成性能报告"""

        total_edits = self.metrics['total_edits']
        if total_edits == 0:
            return {'message': '暂无编辑操作数据'}

        success_rate = self.metrics['successful_edits'] / total_edits
        fallback_rate = self.metrics['fallback_used'] / total_edits

        return {
            'summary': {
                'total_operations': total_edits,
                'success_rate': f"{success_rate:.2%}",
                'fallback_rate': f"{fallback_rate:.2%}",
                'avg_execution_time': f"{self.metrics['avg_execution_time']:.3f}s"
            },
            'detailed_metrics': self.metrics,
            'recommendations': self._generate_recommendations(success_rate, fallback_rate)
        }

    def _generate_recommendations(self, success_rate: float, fallback_rate: float) -> List[str]:
        """基于性能数据生成优化建议"""

        recommendations = []

        if success_rate < 0.9:
            recommendations.append("成功率低于90%，建议检查匹配逻辑和错误处理")

        if fallback_rate > 0.3:
            recommendations.append("Fallback使用率过高，建议优化主要编辑策略")

        if self.metrics['avg_execution_time'] > 1.0:
            recommendations.append("平均执行时间过长，建议优化性能或增加缓存")

        return recommendations
```

## 📋 部署和维护指南

### 1. 部署检查清单

- [ ] **环境准备**
  - [ ] Python 3.8+ 环境
  - [ ] 必要依赖包安装
  - [ ] 文件权限配置
  - [ ] 日志目录创建

- [ ] **配置验证**
  - [ ] 编辑工具配置文件
  - [ ] 性能参数调优
  - [ ] 安全策略设置
  - [ ] 监控系统配置

- [ ] **功能测试**
  - [ ] 单元测试通过
  - [ ] 集成测试通过
  - [ ] 性能基准测试
  - [ ] 错误恢复测试

- [ ] **生产部署**
  - [ ] 渐进式部署策略
  - [ ] 实时监控启动
  - [ ] 错误告警配置
  - [ ] 回滚方案准备

### 2. 维护和优化

```python
class EditToolMaintenance:
    """编辑工具维护系统"""

    def __init__(self):
        self.health_checks = []
        self.optimization_tasks = []

    def daily_health_check(self) -> Dict[str, str]:
        """每日健康检查"""

        checks = {
            'cache_size': self._check_cache_size(),
            'error_rate': self._check_error_rate(),
            'performance': self._check_performance(),
            'disk_space': self._check_disk_space()
        }

        return checks

    def weekly_optimization(self):
        """每周优化任务"""

        # 清理缓存
        self._cleanup_cache()

        # 优化配置
        self._optimize_config_based_on_metrics()

        # 更新测试用例
        self._update_test_cases()

        # 生成性能报告
        self._generate_weekly_report()
```

---

*这个增强的编辑工具设计方案提供了完整的技术实现规范、性能优化策略、测试验证框架和部署维护指南，确保系统的高可靠性和可维护性。*
