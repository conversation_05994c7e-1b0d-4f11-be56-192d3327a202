# animate_markdown

## 效果

将Markdown格式的文本内容渲染为富文本并在Manim场景中播放动画。
支持各种Markdown元素，包括标题、列表、代码块、表格等。


## 使用场景

- 展示格式化的文本内容，如教程说明、演示文稿
- 在动画中展示结构化的信息，如列表和表格
- 显示带有语法高亮的代码片段
- 创建包含文本和图片的混合内容

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| content | str | Markdown格式的文本内容 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |
| title | str | 当前内容的标题，会显示在内容上方 | 是 | - |
| narration | str | 在内容显示时播放的语音旁白文本 | 是 | - |

## DSL示例

### 示例 1

```json
{
  "type": "animate_markdown",
  "params": {
    "content": "# 主标🚗题\n\n⚠️ 这是一段普通文本，支持**粗体**和*斜体*。\n\n> 🚰 这是一个引用💦块。\n\n## 子标题\n\n- 列表🐆 项1\n- 🐇 列表项2\n- $E = mc^2$\n\n$$\na^2 = b^2 + c^2\n$$\n\n```python\ndef hello_world():\n    print(\"Hello, world!\")\n```\n",
    "title": "这是一个Markdown示例",
    "narration": "这是一个Markdown示例，包含标题、文本和代码。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_markdown",
  "params": {
    "content": "## 数据比较📊\n\n| 产品 | 价格 | 评分 |\n| ---- | ---- | ---- |\n| A产品 | ¥199 | 4.5分 |\n| B产品 | ¥299 | 4.8分 |\n| C产品 | ¥399 | 4.9分 |\n",
    "title": "产品价格比较",
    "narration": "这个表格比较了两款产品的价格和评分。"
  }
}
```

## 注意事项

- 支持大多数标准Markdown语法，包括标题、列表、代码块、表格等
- 根据内容会自动调整大小以适应场景
- 必须包含title字段，言简意赅，概括内容要点，content中不要有和title重复的标题部分，重点在内容介绍
