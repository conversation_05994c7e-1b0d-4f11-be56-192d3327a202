# Manim DSL v2 Introduction for LLMs

## 1. 引言 (Introduction)

本文档旨在为大型语言模型 (LLM) 提供生成 Manim DSL v2 JSON 数据的完整指南。LLM 的任务是根据用户需求，生成一个符合本文档描述的、包含 `metadata`, 和 `actions` 的完整 JSON 对象。

整个 JSON 结构定义了动画的元信息以及按顺序执行的动画动作。

## 2. 顶层 JSON 结构 (Top-Level JSON Structure)

一个有效的 Manim DSL v2 JSON 数据应包含以下顶层字段：

```json
{
  "metadata": { ... },       // 动画的元数据
  "actions": [ ... ]         // 按顺序执行的动画动作列表
}
```

## 3. 元数据 (`metadata`)

`metadata` 对象包含关于整个动画场景的基本信息。

*   **结构**:
    ```json
    {
      "title": "string (Python Class Name)",
      "author": "string",
    }
    ```
*   **参数说明**:
    | 参数名 (Parameter)   | 类型 (Type) | 描述 (Description)                          | 必需? (Required?) | 默认值 (Default) |
    | :------------------- | :---------- | :------------------------------------------ | :---------------: | :--------------: |
    | `title`              | string      | 动画的标题，将用作生成的 Python 类名        |        是         |       N/A        |
    | `author`             | string      | 动画作者名称                                |        是         |       N/A        |

## 3.1 关键约束总结 (Key Constraints Summary)

**请务必遵守以下核心规则：**

*   `metadata`: 必须包含 `title` 和 `author` 字段。
*   `target_region_id` (用于 `display_image`, `display_markdown`, `animate_counter`, `display_video` 等动作): **必须** 从以下预定义列表中选择：`full_screen`, `left_half`, `right_half`, `upper_half`, `bottom_half`。**严禁** 使用任何其他值。如果没有特别原因，尽量使用 `full_screen`。
*   Object IDs (`id` 参数): 如果指定，必须在整个 JSON 中保持唯一。

## 4. 预定义布局区域 (Predefined Layout Regions)

为了方便内容的定位，框架内置了一组预定义的布局区域。这些区域总是可用的，可以在动作的 `target_region_id` 参数中引用。
**[强制约束]** `target_region_id` 参数 **必须** 且 **只能** 从以下列表中选择：`full_screen`, `left_half`, `right_half`, `upper_half`, `bottom_half`。**严禁** 使用任何其他值（例如 `title_region`, `content_area` 等）。

| 区域 ID (`region_id`) | 描述                                        |
| :-------------------- | :------------------------------------------ |
| `full_screen`         | 覆盖整个屏幕。                             |
| `left_half`           | 屏幕左半边区域。                           |
| `right_half`          | 屏幕右半边区域。                           |
| `upper_half`          | 屏幕上半边区域。                           |
| `bottom_half`         | 屏幕下半边区域。                           |

## 5. 动作 (`actions`)

`actions` 是一个数组，包含了按顺序执行的动画步骤。每个动作都是一个 JSON 对象，其 `type` 字段决定了该动作的功能和所需参数。

### 5.1 动作通用概念与参数说明

*   **`type`**: (string, **必需**) 每个动作对象都必须包含 `type` 字段，其值是本文档后续章节中描述的动作名称之一 (e.g., `"side_by_side_comparison"`).
*   **`narration`**: (string, **可选**) 对大多数视觉呈现动作有效。提供此字段的文本将用于生成同步的语音旁白 (TTS)。如果为 `null` 或空字符串，则不生成语音。
*   **Object IDs (`xxx_content_id`, `left_content_id`, etc.)**: 当动作需要引用先前在其他action中创建的内容（文本、图像等）时，会使用类似 `*_id` 的参数。其值必须是对应元素的 `id` 字符串。
*   **颜色 (Color)**: 接受 Manim 支持的颜色字符串 (e.g., `"RED"`, `"BLUE"`, `"#FFFFFF"`). 如果未指定，通常有合理的默认值（具体见各动作说明）。
*   **持续时间 (Duration)**: 动画或等待的持续时间，单位通常为秒 (seconds)。
*   **动画类型 (Animation Type / Transition)**: 指定动作执行时的视觉效果。具体可选值取决于动作本身 (e.g., `'fadeIn'`, `'slideUp'`, `'grow'`).

### 5.2 可用动作列表

以下是当前支持的动作及其参数：

#### 5.2.1 `side_by_side_comparison` **(全屏覆盖动作)**

*   **来源**: `dsl/v2/visitors/side_by_side_comparison.py`
*   **功能描述**: 创建一个并排（左右）布局，用于比较两个内容对象。可以为两侧内容添加标题。支持多种内容类型和入场动画。会自动淡出上一个同类对比内容（如果有）。**注意：如果要用表格对比两个或多个内容，应该调用 `display_markdown`，将对比内容放在表格内，而不是用 `side_by_side_comparison`**
*   **参数**:
    | 参数名 (Parameter)  | 类型 (Type)           | 描述 (Description)                                   | 必需? (Required?) | 默认值 (Default) |
    | :------------------ | :-------------------- | :--------------------------------------------------- | :---------------: | :--------------: |
    | `type`              | string                | 固定为 `"side_by_side_comparison"`                     |        是         |       N/A        |
    | `target_region_id`  | string                | *(此动作覆盖全屏，忽略此参数)*                         |        N/A        |       N/A        |
    | `left_content`      | string                | 左侧内容 (文本字符串, 代码, JSON 字符串, 或图像文件路径) |        是         |       N/A        |
    | `right_content`     | string                | 右侧内容 (同上)                                      |        是         |       N/A        |
    | `left_type`         | string                | 左侧内容类型: `"text"`, `"code"`, `"json"`, `"image"`  |        是         |       N/A        |
    | `right_type`        | string                | 右侧内容类型: `"text"`, `"code"`, `"json"`, `"image"`  |        是         |       N/A        |
    | `left_title`        | string                | 左侧内容的标题 (可选)                                  |        否         |      `null`      |
    | `right_title`       | string                | 右侧内容的标题 (可选)                                  |        否         |      `null`      |
    | `vs_symbol`         | boolean               | 是否在中间显示 "VS" 符号                               |        否         |     `false`      |
    | `transition`        | string                | 入场动画效果: `"fadeIn"`, `"slideUp"`, `"none"`        |        否         |     `"none"`     |
    | `narration`         | string                | 用于生成语音旁白的文本 (可选)                            |        否         |      `null`      |
*   **注意**:
    *   此动作生成的 Manim 对象会自动限制宽度，以适应屏幕。
    *   `left/right_content` 的值直接传入 Manim 的 `Text`, `Code`, `ImageMobject` 等。对于 `image` 类型，应为有效的文件路径。对于 `code` 和 `json`，应为包含代码/JSON 的字符串。
*   **示例**:
    ```json
    {
      "type": "side_by_side_comparison",
      "left_content": "def greet(name):\n    print(f'Hello, {name}!')",
      "left_type": "code",
      "left_title": "Python 代码",
      "right_content": "{\n  \"message\": \"Hello!\"\n}",
      "right_type": "json",
      "right_title": "JSON 输出",
      "vs_symbol": true,
      "transition": "fadeIn",
      "narration": "让我们来比较一下Python代码和对应的JSON输出。"
    }
    ```

#### 5.2.2 `animate_chart` **(全屏覆盖动作)**

*   **来源**: `dsl/v2/visitors/animate_chart.py`
*   **功能描述**: 创建并动画展示指定类型的图表（目前支持条形图、折线图、雷达图）。**作为全屏覆盖动作，它会在开始执行时清除屏幕上的所有先前内容。** 支持单数据集或多数据集，可自定义标题、动画风格和多种图表选项。会自动添加图例，并进行缩放以适应屏幕。**动画完成后，图表会保持可见状态并等待1秒，不会自动淡出。后续的动作负责管理屏幕内容。**
*   **参数**:
    | 参数名 (Parameter)  | 类型 (Type)              | 描述 (Description)                                                                                                  | 必需? (Required?) | 默认值 (Default) |
    | :------------------ | :----------------------- | :------------------------------------------------------------------------------------------------------------------ | :---------------: | :--------------: |
    | `type`              | string                   | 固定为 `"animate_chart"`                                                                                              |        是         |       N/A        |
    | `target_region_id`  | string                   | *(此动作覆盖全屏，忽略此参数)*                                                                                      |        N/A        |       N/A        |
    | `chart_type`        | string                   | 图表类型: `"bar"`, `"line"`, `"radar"`                                                                                |        是         |       N/A        |
    | `data`              | dict 或 list[dict]       | 图表数据。单数据集为 `{"label1": val1, ...}`，多数据集为 `[{"label1": valA1, ...}, {"label1": valB1, ...}]`         |        是         |       N/A        |
    | `title`             | string                   | 图表标题 (可选)                                                                                                     |        否         |      `null`      |
    | `animation_style`   | string                   | 图表出现动画: `"grow"`, `"fadeIn"`, `"draw"`, `"update"` (update 对 bar chart 有特殊效果)                               |        否         |     `"grow"`     |
    | `options`           | dict                     | 其他图表配置项 (可选)                                                                                             |        否         |       {}         |
    | `narration`         | string                   | 用于生成语音旁白的文本 (可选)                                                                                       |        否         |      `null`      |
*   **`options` 参数详解**:
    | `options` 内 Key    | 类型 (Type)     | 适用图表     | 描述 (Description)                                                                   |
    | :------------------ | :-------------- | :----------- | :----------------------------------------------------------------------------------- |
    | `dataset_names`     | list[string]    | bar, line, radar | 为多数据集指定图例名称，顺序需与 `data` 数组对应。 e.g., `["Sales", "Profit"]`     |
    | `y_range`           | list[number, number, number] | bar, line    | Y 轴范围 `[min, max, step]`                                                           |
    | `x_range`           | list[number, number, number] | line         | X 轴范围 `[min, max, step]`                                                           |
    | `bar_width`         | number          | bar          | 条形宽度 (Manim units)                                                               |
    | `width`             | number          | bar, line    | 图表区域的 X 轴长度 (Manim units)                                                       |
    | `height`            | number          | bar, line    | 图表区域的 Y 轴长度 (Manim units)                                                       |
    | `x_label`           | string          | bar, line    | X 轴的标签/单位文字                                                                  |
    | `y_label`           | string          | bar, line    | Y 轴的标签/单位文字                                                                  |
*   **示例 (简单条形图带旁白)**:
    ```json
    {
      "type": "animate_chart",
      "chart_type": "bar",
      "data": {
        "Category A": 15,
        "Category B": 25,
        "Category C": 10
      },
      "title": "示例条形图",
      "animation_style": "fadeIn",
      "options": {
        "y_label": "数量"
      },
      "narration": "这是一个展示不同类别数量的条形图。"
    }
    ```
*   **示例 (多数据集折线图带旁白)**:
    ```json
    {
      "type": "animate_chart",
      "chart_type": "line",
      "data": [
        { "Jan": 5, "Feb": 8, "Mar": 12 },
        { "Jan": 3, "Feb": 6, "Mar": 9 }
      ],
      "title": "月度趋势对比",
      "animation_style": "draw",
      "options": {
        "dataset_names": ["产品 A", "产品 B"],
        "y_label": "销售额 (万)",
        "x_label": "月份"
      },
      "narration": "图表显示了不同产品的月度销售额趋势。"
    }
    ```

#### 5.2.3 `timeline` **(全屏覆盖动作)**

*   **来源**: `dsl/v2/visitors/timeline.py`
*   **功能描述**: 创建一个水平时间轴动画。自动排列事件节点、时间标签、可选的文本/图像内容和注释。动画会自动平移以聚焦当前事件，顺序显示元素，并应用可选的强调效果。最后整个时间轴会淡出。
*   **参数**:
    | 参数名 (Parameter) | 类型 (Type)          | 描述 (Description)                                                                                                     | 必需? (Required?) | 默认值 (Default)      |
    | :----------------- | :------------------- | :--------------------------------------------------------------------------------------------------------------------- | :---------------: | :-------------------: |
    | `type`             | string               | 固定为 `"timeline"`                                                                                                    |        是         |          N/A          |
    | `target_region_id` | string               | *(此动作覆盖全屏，忽略此参数)*                                                                                           |        N/A        |          N/A        |
    | `events`           | list[dict]           | 时间轴上的事件列表。每个字典包含 `time` (必需, string), `text` (可选, string), `image` (可选, string), `annotation` (可选, string) |        是         |          N/A          |
    | `duration`         | number               | 每个事件高亮后暂停的持续时间 (秒)                                                                                            |        否         |          1.0          |
    | `timeline_color`   | string               | 时间轴主线和默认节点的颜色 (Manim color string)                                                                              |        否         |      `'#6666FF'`      |
    | `label_font`       | string               | 用于时间标签、事件文本、注释的字体名称                                                                                         |        否         | `'Maple Mono NF CN'` |
    | `event_style`      | dict                 | 事件节点的样式选项 (可选)                                                                                                |        否         |          {}           |
    | `focus_effect`     | string               | 当前事件的强调动画效果: `"zoom"`, `"flash"`, `"color"`, `"none"`                                                             |        否         |       `"none"`        |
    | `narration`        | string               | 用于生成语音旁白的文本 (可选)                                                                                          |        否         |         `null`        |
*   **`event_style` 参数详解**:
    | `event_style` 内 Key | 类型 (Type) | 描述 (Description)                     | 默认值 (Default)     |
    | :------------------- | :---------- | :------------------------------------- | :------------------: |
    | `shape`              | string      | 事件节点形状: `"circle"` 或 `"square"` |      `"circle"`      |
    | `color`              | string      | 事件节点颜色 (覆盖 `timeline_color`)   | `timeline_color` 值 |
*   **`events` 列表内字典结构**:
    | Key          | 类型 (Type) | 描述 (Description)                       | 必需? (Required?) |
    | :----------- | :---------- | :--------------------------------------- | :---------------: |
    | `time`       | string      | 显示在节点下方的时间点/步骤标签            |        是         |
    | `text`       | string      | 显示在节点上方的主要文本内容 (可选)      |        否         |
    | `image`      | string      | 显示在节点上方的图片文件路径 (可选)      |        否         |
    | `annotation` | string      | 显示在 `time` 下方的黄色注释文本 (可选) |        否         |
*   **示例**:
    ```json
    {
      "type": "timeline",
      "events": [
        { "time": "Phase 1", "text": "Initial planning" },
        { "time": "Phase 2", "text": "Development", "image": "images/code.png", "annotation": "Coding phase" },
        { "time": "Phase 3", "text": "Testing & Launch" }
      ],
      "duration": 1.5,
      "timeline_color": "GREEN",
      "event_style": {
        "shape": "square"
      },
      "focus_effect": "flash",
      "narration": "接下来我们看看项目的三个主要阶段。"
    }
    ```

#### 5.2.4 `animate_architecture_diagram` **(全屏覆盖动作)**

*   **来源**: `dsl/v2/visitors/animate_architecture_diagram.py`
*   **功能描述**: 根据提供的文本描述，调用 `ExcalidrawToolkit` 生成一个表现架构图绘制过程的视频，并将该视频嵌入到 Manim 动画场景中进行播放。视频会自动调整大小以适应屏幕，播放完毕后会自动淡出。
*   **参数**:
    | 参数名 (Parameter)    | 类型 (Type) | 描述 (Description)                                                                                             | 必需? (Required?) | 默认值 (Default) |
    | :-------------------- | :---------- | :------------------------------------------------------------------------------------------------------------- | :---------------: | :--------------: |
    | `type`                | string      | 固定为 `"animate_architecture_diagram"`                                                                          |        是         |       N/A        |
    | `target_region_id`    | string      | *(此动作覆盖全屏，忽略此参数)*                                                                                    |        N/A        |       N/A        |
    | `content_description` | string      | 架构图的内容和动画描述。此文本将传递给 `ExcalidrawToolkit` 以生成 Excalidraw 绘图和动画视频。需要清晰描述组件、连接和动画步骤。 |        是         |       N/A        |
    | `narration`           | string      | 用于生成语音旁白的文本 (可选)                                                                                  |        否         |       `null`       |
*   **注意**:
    *   此动作依赖于外部的 `ExcalidrawToolkit`。确保该工具可用且配置正确。
    *   生成的视频播放时长至少为 5 秒，或者视频本身的实际时长（取两者中的较大值）。
*   **示例**:
    ```json
    {
      "type": "animate_architecture_diagram",
      "content_description": "场景：用户请求通过 API 网关访问后端服务。\n1. 绘制一个表示用户的方块。\n2. 绘制一个表示 API 网关的六边形。\n3. 绘制一个表示后端服务的圆圈。\n4. 从用户向 API 网关画一条带箭头的线。\n5. 从 API 网关向后端服务画一条带箭头的线，并使其虚线化。\n动画：首先显示用户，然后 API 网关滑入，最后后端服务淡入。箭头依次绘制。",
      "narration": "这是一个典型的用户请求流程示意图。"
    }
    ```

#### 5.2.5 `display_image`

*   **来源**: `dsl/v2/animation_functions/display_image.py`
*   **功能描述**: 在指定区域显示图片，并可以配上注释或配套文案。
*   **参数**:
    | 参数名 (Parameter) | 类型 (Type) | 描述 (Description)                                                                                     | 必需? (Required?) | 默认值 (Default) |
    | :----------------- | :---------- | :----------------------------------------------------------------------------------------------------- | :---------------: | :--------------: |
    | `type`             | string      | 固定为 `"display_image"`                                                                               |        是         |       N/A        |
    | `target_region_id` | string      | 内容要放置的预定义区域 ID (可选, 参考 "预定义布局区域" 章节)。 默认为 `"full_screen"`。(必须是: `full_screen`, `left_half`, `right_half`, `upper_half`, `bottom_half` 之一) |        否         |    `"full_screen"`      |
    | `content`          | string      | 要显示的图片路径                                                                                       |        是         |       N/A        |
    | `annotation`       | string      | 指定图片的markdown格式的注释或者配套文案，会以文本形式展示在图片右侧。                                                                               |        否         |       N/A        |
    | `narration`        | string      | 用于生成语音旁白的文本 (可选)                                                                          |        否         |       `null`       |
    | `id`               | string      | 为此内容对象分配一个唯一 ID，供后续动作引用 (可选)                                                           |        否         |   (自动生成)     |
*   **注意**:
    *   如果提供了 `id`，生成的 Manim 对象会存储在场景的 `self.{id}_content` 属性中。
*   **示例 (显示图片带注释)**:
    ```json
    {
      "type": "display_image",
      "target_region_id": "full_screen",  // 如果是 image，会自动使用 full_screen
      "content": "path/to/image.jpg",
      "annotation": "这是图片的注释",
      "narration": "这是本次演示的主要内容。"
    }
    ```

#### 5.2.6 `highlight_sequence`

*   **来源**: `dsl/v2/visitors/highlight_sequence.py`
*   **功能描述**: 按顺序高亮显示一系列已存在的 Manim 对象。这些对象通常由 `display_formatted_content` 创建并指定了 `id`。支持多种高亮效果，并且可以特别针对代码对象高亮指定的行范围。
*   **参数**:
    | 参数名 (Parameter)    | 类型 (Type)     | 描述 (Description)                                                                                                                               | 必需? (Required?) | 默认值 (Default) |
    | :-------------------- | :-------------- | :----------------------------------------------------------------------------------------------------------------------------------------------- | :---------------: | :--------------: |
    | `type`                | string          | 固定为 `"highlight_sequence"`                                                                                                                   |        是         |       N/A        |
    | `target_region_id`    | string          |  *(此参数对 highlight 无效，高亮作用于已有对象)*                                                                                                   |        N/A        |       N/A        |
    | `elements`            | list[string]    | 要依次高亮的元素的 ID 列表。ID 必须对应先前动作（如 `display_formatted_content`）创建并指定了 `id` 的对象。                                         |        是         |       N/A        |
    | `highlight_type`      | string          | 高亮效果类型: `"box"` (环绕框), `"underline"`, `"flash"`, `"color"` (变色)。若指定 `lines` 且目标是代码，则此参数无效。                               |        否         |     `"box"`     |
    | `lines`               | string          | 仅对代码对象生效。指定要高亮的行号或范围，格式如 `"1-3,5,7-10"`。若指定，将覆盖 `highlight_type`，使用背景高亮。对非代码对象指定此参数无效（会回退到 `box`）。 |        否         |      `null`      |
    | `color`               | string          | 高亮颜色 (Manim color string)                                                                                                                      |        否         |    `"YELLOW"`    |
    | `duration_per_item`   | number          | 每个元素高亮效果的持续时间 (秒)                                                                                                                   |        否         |       1.0        |
    | `narration`           | string          | 用于生成语音旁白的文本 (可选)                                                                                                                    |        否         |       `null`       |
*   **示例 (高亮代码的不同行带旁白)**:
    ```json
    // 假设之前已用 display_formatted_content 创建了 id="factorial_code" 的代码对象
    {
      "type": "highlight_sequence",
      "elements": ["factorial_code"],
      "lines": "2", // 高亮第 2 行
      "color": "GREEN",
      "duration_per_item": 1.5,
      "narration": "注意构造函数部分。"
    },
    {
      "type": "highlight_sequence",
      "elements": ["factorial_code"],
      "lines": "4-5", // 高亮第 4 到 5 行
      "color": "BLUE",
      "duration_per_item": 2.0,
      "narration": "再看函数体部分。"
    }
    ```
*   **示例 (依次高亮不同对象带旁白)**:
    ```json
    // 假设之前创建了 id="text_intro" 和 id="code_example" 的对象
    {
      "type": "highlight_sequence",
      "elements": ["text_intro", "code_example"],
      "highlight_type": "flash",
      "color": "RED",
      "duration_per_item": 0.8,
      "narration": "首先注意引言部分，然后看代码示例。"
    }
    ```

#### 5.2.7 `animate_counter`

*   **来源**: `dsl/v2/visitors/animate_counter.py`
*   **功能描述**: 创建一个从起始值动态变化到目标值的数字计数器。可以附带文本标签（在数字左侧）和单位（在数字右侧）。计数动画完成后，可以添加一个可选的结尾强调效果。最后整个计数器会淡出。
*   **参数**:
    | 参数名 (Parameter) | 类型 (Type) | 描述 (Description)                                                            | 必需? (Required?) | 默认值 (Default) |
    | :----------------- | :---------- | :---------------------------------------------------------------------------- | :---------------: | :--------------: |
    | `type`             | string      | 固定为 `"animate_counter"`                                                      |        是         |       N/A        |
    | `target_region_id` | string      | 计数器要放置的预定义区域 ID (可选, 参考 "预定义布局区域" 章节)。 默认为 `"full_screen"`。 |        否         |    `"full_screen"`      |
    | `target_value`     | number      | 计数器动画结束的目标值                                                          |        是         |       N/A        |
    | `start_value`      | number      | 计数器动画开始的初始值                                                          |        否         |        0         |
    | `duration`         | number      | 数字从 `start_value` 变化到 `target_value` 的动画时长 (秒)                      |        否         |       2.0        |
    | `label`            | string      | 显示在数字左侧的文本标签 (可选)                                                 |        否         |      `null`      |
    | `unit`             | string      | 显示在数字右侧的单位文本 (可选)                                                 |        否         |      `null`      |
    | `effect`           | string      | 到达目标值后的结尾强调动画: `"zoom"` (放大指示), `"flash"` (闪烁), `"none"` |        否         |     `"none"`     |
    | `narration`        | string      | 用于生成语音旁白的文本 (可选)                                                 |        否         |      `null`      |
*   **注意**:
    *   自动处理目标值的小数位数。
    *   使用 `ChangeDecimalToValue` 实现平滑的数字变化动画。
*   **示例**:
    ```json
    {
      "type": "animate_counter",
      "target_value": 1500.75,
      "start_value": 100,
      "duration": 3.0,
      "label": "收入",
      "unit": "万元",
      "effect": "zoom",
      "narration": "让我们看一下收入的变化情况。"
    }
    ```

#### 5.2.8 `display_video`

*   **来源**: `dsl/v2/visitors/display_video.py`
*   **功能描述**: 在指定区域显示一个视频文件。可以选择性地在视频内容上方叠加文本信息，并支持文本的动态逐行出现。
*   **参数**:
    | 参数名 (Parameter)             | 类型 (Type)        | 描述 (Description)                                                                                              | 必需? (Required?) | 默认值 (Default)        |
    | :----------------------------- | :----------------- | :-------------------------------------------------------------------------------------------------------------- | :---------------: | :---------------------: |
    | `type`                         | string             | 固定为 `"display_video"`                                                                                         |        是         |          N/A          |
    | `video_path`                   | string             | 视频文件的路径                                                                                                  |        是         |          N/A          |
    | `target_region_id`             | string             | 视频要放置的预定义区域 ID (可选, 参考 "预定义布局区域" 章节)。 (必须是: `full_screen`, `left_half`, `right_half`, `upper_half`, `bottom_half` 之一) |        否         |       `"full_screen"`        |
    | `overlay_text`                 | string             | 叠加在视频上方的文本 (可选, 使用 `\n` 分隔多行)                                                                   |        否         |         `null`          |
    | `overlay_animation_style`      | string             | 叠加文本的动画风格: `"sequential"` (逐行出现) 或 `"simultaneous"` (与视频一起/根据`text_position`出现)         |        否         |    `"simultaneous"`   |
    | `overlay_animation_delay`      | number             | 当 `overlay_animation_style` 为 `"sequential"` 时，每行文本动画的延迟时间 (秒)                                       |        否         |          0.5          |
    | `id`                           | string             | 为此 Mobject 组指定一个唯一 ID (可选)                                                                                |        否         |       (自动生成)        |
    | `narration`                    | string             | 用于生成语音旁白的文本                                                                                        |        否         |         `null`        |
*   **注意**:
    *   当 `overlay_animation_style` 为 `"sequential"` 时，`overlay_text` 中的换行符 (`\n`) 会被用来分隔文本行，文本将默认出现在视频的中心区域。`text_position` 参数在此模式下会被忽略。
    *   当 `overlay_animation_style` 为 `"simultaneous"` 时，`overlay_text` 会作为一个整体根据 `text_position` 定位（`CENTER` 表示直接叠加在视频中心）。
    *   视频会根据 `target_region_id` 的大小自动缩放（保持宽高比），并留有一定边距。
    *   overlay_text 应该是短小精炼的文本，不能包括长文本（超过10个字符），否则视觉效果会非常差。
*   **示例 (视频中心逐行叠加带旁白)**:
    ```json
    {
      "type": "display_video",
      "video_path": "assets/screencast.mp4",
      "overlay_text": "步骤 1: xxx\n步骤 2: yyy\n作者: AI助手",
      "overlay_animation_style": "sequential",
      "overlay_animation_delay": 1.0,
      "narration": "现在播放演示视频，请注意关键步骤。"
    }
    ```

### 5.2.9 `display_markdown`

*   **来源**: `dsl/v2/visitors/display_markdown.py`
*   **功能描述**: 在指定区域显示 Markdown 格式的内容。主要是文本内容，如果是图片或视频，优先用 `display_image` 或 `display_video`。
*   **参数**:
    | 参数名 (Parameter) | 类型 (Type) | 描述 (Description)                                                                                     | 必需? (Required?) | 默认值 (Default) |
    | :----------------- | :---------- | :----------------------------------------------------------------------------------------------------- | :---------------: | :--------------: |
    | `type`             | string      | 固定为 `"display_markdown"`                                                                               |        是         |       N/A        |
    | `target_region_id` | string      | 内容要放置的预定义区域 ID (可选, 参考 "预定义布局区域" 章节)。 默认为 `"full_screen"`。(必须是: `full_screen`, `left_half`, `right_half`, `upper_half`, `bottom_half` 之一) |        否         |    `"full_screen"`      |
    | `content`          | string      | 要显示的 Markdown 格式的内容。                                                                                       |        是         |       N/A        |
    | `narration`        | string      | 用于生成语音旁白的文本 (可选)                                                                          |        否         |       `null`       |
    | `id`               | string      | 为此内容对象分配一个唯一 ID，供后续动作引用 (可选)                                                           |        否         |   (自动生成)     |
*   **注意**:
    *   如果提供了 `id`，生成的 Manim 对象会存储在场景的 `self.{id}_content` 属性中。
*   **示例 (显示 Markdown 内容)**:
    ```json
    {
      "type": "display_markdown",
      "target_region_id": "full_screen",  // 如果是 markdown，会自动使用 full_screen
      "content": "# 欢迎!\n这是一个 **Manim DSL v2** 的演示。\n\n- 展示代码\n- 展示Markdown\n- 执行高亮",
      "narration": "这是本次演示的主要内容。"
    }
    ```

## 6. 完整示例 (Complete Example)

以下是一个演示如何组合使用 `metadata` 和多种 `actions` 的完整 JSON 示例：

```json
{
  "metadata": {
    "title": "综合演示动画",
    "author": "智能助理"
  },
  "actions": [
    {
      "type": "display_markdown",
      "target_region_id": "left_half",
      "content": "class Greeter:\n    def __init__(self, name):\n        self.name = name\n\n    def say_hello(self):\n        print(f'Hello, {self.name}!')",
      "id": "greeter_code",
      "narration": "首先，我们定义一个简单的 Greeter 类。"
    },
    {
      "type": "display_markdown",
      "target_region_id": "right_half",
      "content": "# 欢迎!\n这是一个 **Manim DSL v2** 的演示。\n\n- 展示代码\n- 展示Markdown\n- 执行高亮",
      "id": "intro_md",
      "narration": "这是本次演示的主要内容。"
    },
    {
      "type": "highlight_sequence",
      "elements": ["greeter_code"],
      "lines": "2",
      "color": "ORANGE",
      "duration_per_item": 1.5,
      "narration": "注意构造函数部分。"
    },
    {
      "type": "highlight_sequence",
      "elements": ["intro_md"],
      "highlight_type": "box",
      "color": "LIGHT_BLUE",
      "duration_per_item": 1.0
    },
    {
      "type": "side_by_side_comparison",
      "left_content": "Original Text",
      "left_type": "text",
      "left_title": "Left Side",
      "right_content": "{\n  \"key\": \"value\",\n  \"number\": 123\n}",
      "right_type": "json",
      "right_title": "Right Side (JSON)",
      "vs_symbol": true,
      "transition": "slideUp",
      "narration": "现在进行一个简单的对比。"
    },
    {
      "type": "animate_chart",
      "chart_type": "bar",
      "data": {
        "Apples": 50,
        "Oranges": 80,
        "Bananas": 30
      },
      "title": "水果销售量",
      "animation_style": "grow",
      "options": {
        "y_label": "数量 (箱)",
        "y_range": [0, 100, 20]
      },
      "narration": "图表显示了各种水果的销售情况。"
    },
    {
       "type": "animate_counter",
       "target_value": 987,
       "start_value": 100,
       "duration": 2.5,
       "label": "得分:",
       "unit": "分",
       "effect": "flash",
       "narration": "最后看一个得分动画。"
    },
    {
      "type": "animate_chart",
      "chart_type": "radar",
      "data": [
        {
          "用户体验": 85,
          "性能": 75,
          "安全性": 92,
          "可扩展性": 65,
          "成本效益": 70
        },
        {
          "用户体验": 80,
          "性能": 85,
          "安全性": 75,
          "可扩展性": 80,
          "成本效益": 65
        }
      ],
      "title": "产品评估对比图",
      "animation_style": "fadeIn",
      "options": {
        "dataset_names": ["我们的产品", "竞品"]
      },
      "narration": "产品评估对比图，我们的产品用户体验85%，性能75%，安全性92%，可扩展性65%，成本效益70%，竞品用户体验80%，性能85%，安全性75%，可扩展性80%，成本效益65%"
    },
    {
      "type": "display_video",
      "video_path": "media/videos/placeholder_video.mp4",
      "overlay_text": "第一行文本\n第二行出现\n最后一行",
      "overlay_animation_style": "sequential",
      "overlay_animation_delay": 0.8,
      "narration": "演示结束，感谢观看。"
    }
  ]
}
```
