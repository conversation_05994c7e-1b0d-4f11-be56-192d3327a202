#!/usr/bin/env python3
"""
意图分类Agent - 智能内容分析

主要功能：
1. 从config.yaml的intent_input读取url、file、chat三个字段
2. 智能意图识别和内容分类
3. 内容安全检查（黑名单过滤、风险内容检测）
4. 返回分类结果和提取的信息，供后续流程直接调用对应功能

用法：
python feynman_workflow_refactored.py  # 自动从config读取intent_input
"""

import argparse
import json
import os
import re
import sys
import time
import urllib.parse
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any

import yaml
from loguru import logger

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from utils.create_llm_model import create_model
from camel.messages import BaseMessage
from utils.common import AgentFactory


class ContentSafetyChecker:
    """内容安全检查器"""
    
    def __init__(self):
        # 黑名单关键词
        self.blacklist_keywords = {
            'political': [
                '习近平', '毛泽东', '邓小平', '江泽民', '胡锦涛', '温家宝', '李克强',
                '中共', '共产党', '国民党', '台独', '西藏独立', '新疆独立', '法轮功',
                '六四', '天安门', '89民运', '反政府', '颠覆国家', '分裂国家',
                '民主运动', '人权组织', 'dalai lama', 'xi jinping', 'ccp'
            ],
            'violence': [
                '恐怖主义', '恐怖分子', '爆炸', '炸弹', '枪击', '暗杀', '杀害', 
                '屠杀', '血腥', '暴力', '武器制造', '毒品制造', '自杀', '自残',
                'terrorism', 'bomb', 'explosive', 'assassination', 'massacre'
            ],
            'sexual': [
                '色情', '黄色', '性交', '做爱', '性器官', '裸体', '情色', '淫秽',
                '性虐待', '强奸', '性侵', '卖淫', '嫖娼', 'porn', 'sex', 'nude',
                'sexual', 'prostitution', 'rape'
            ],
            'illegal': [
                '贩毒', '洗钱', '诈骗', '赌博', '非法集资', '传销', '黑客攻击',
                '盗取信息', '网络犯罪', '偷税漏税', '走私', 'hacking', 'fraud',
                'money laundering', 'illegal gambling'
            ]
        }
        
        self.whitelist_patterns = [
            r'.*解释.*', r'.*介绍.*', r'.*分析.*', r'.*讲解.*', r'.*学习.*',
            r'.*教学.*', r'.*科普.*', r'.*原理.*', r'.*概念.*', r'.*理论.*',
            r'.*算法.*', r'.*技术.*', r'.*知识.*', r'.*研究.*', r'.*论文.*'
        ]

    def check_text_safety(self, text: str) -> Tuple[bool, Optional[str]]:
        """检查文本内容安全性"""
        if not text:
            return True, None
            
        text_lower = text.lower()
        
        for category, keywords in self.blacklist_keywords.items():
            for keyword in keywords:
                if keyword.lower() in text_lower:
                    return False, f"检测到{category}相关的敏感内容：'{keyword}'"
        
        return True, None

    def check_chat_instruction_safety(self, instruction: str) -> Tuple[bool, Optional[str]]:
        """检查聊天指令的安全性"""
        if not instruction:
            return True, None
            
        is_safe, error_msg = self.check_text_safety(instruction)
        if not is_safe:
            return False, error_msg
        
        instruction_lower = instruction.lower()
        has_educational_keywords = any(
            re.search(pattern, instruction_lower) 
            for pattern in self.whitelist_patterns
        )
        
        if not has_educational_keywords:
            educational_indicators = [
                '什么是', '如何', '为什么', '怎么', '原理', '概念', '定义',
                '介绍', '解释', '分析', '讲解', '科普', '学习', '理解',
                '解决', '解答', '解析', '解题', '求解', '计算', '推导',
                '证明', '论证', '示范', '演示', '展示', '阐述', '说明',
                '题目', '练习', '习题', '作业', '问题', '方程', '公式',
                'what is', 'how to', 'why', 'explain', 'introduce', 'analyze',
                'solve', 'demonstrate', 'illustrate', 'calculate', 'derive'
            ]
            
            has_indicators = any(
                indicator.lower() in instruction_lower 
                for indicator in educational_indicators
            )
            
            if not has_indicators:
                return False, (
                    "指令必须是知识性的解读、分析或讲解内容。"
                    "请使用如'解释'、'介绍'、'分析'、'讲解'、'解决'等教育性关键词。"
                )
        
        return True, None


class SmartIntentClassifier:
    """智能意图分类器 - 支持精细分类"""
    
    def __init__(self):
        # URL模式识别
        self.url_patterns = {
            'github': [r'github\.com', r'raw\.githubusercontent\.com'],
            'arxiv': [r'arxiv\.org', r'ar5iv\.labs\.arxiv\.org'],
            'academic': [r'ieee\.org', r'acm\.org', r'springer\.com', r'nature\.com', r'science\.org']
        }
        
        # 文件扩展名分类
        self.file_extensions = {
            'document': ['.pdf', '.doc', '.docx', '.txt', '.md'],
            'image': ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.svg', '.webp'],
            'presentation': ['.ppt', '.pptx'],
            'spreadsheet': ['.xls', '.xlsx', '.csv'],
            'video': ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv'],
            'audio': ['.mp3', '.wav', '.flac', '.aac', '.m4a']
        }
        
        # 论文类型关键词
        self.paper_type_keywords = {
            'survey': ['综述', '调研', 'survey', 'review', 'comprehensive', 'overview'],
            'technical_report': ['技术报告', '报告', 'report', 'technical', 'whitepaper'],
            'research': ['研究', '论文', 'paper', 'research', 'study', 'analysis'],
            'book': ['书籍', '教材', '手册', 'book', 'manual', 'handbook', 'textbook']
        }
        
        # 图片内容类型关键词
        self.image_content_keywords = {
            'math_problem': ['题目', '习题', '练习', '数学', '几何', '代数', 'problem', 'exercise', 'math'],
            'diagram': ['图表', '图解', '流程图', '架构图', 'diagram', 'chart', 'flowchart'],
            'screenshot': ['截图', '界面', '屏幕', 'screenshot', 'interface', 'ui'],
            'document': ['文档', '资料', 'document', 'material', 'text']
        }

    def classify_content(self, url: str = None, file: str = None, chat: str = None) -> Dict[str, Any]:
        """
        综合分类内容类型
        
        Returns:
            {
                'type': str,  # 处理类型：github/pdf/webpage/local_file/math_problem/chat
                'category': str,  # 主分类：github/pdf/webpage/local_file/image/chat
                'subcategory': str,  # 子分类：如paper_survey/math_problem等
                'extracted_info': dict,  # 提取的信息
                'confidence': float  # 置信度
            }
        """
        result = {
            'type': 'chat',
            'category': 'unknown',
            'subcategory': 'general',
            'extracted_info': {},
            'confidence': 0.5
        }
        
        # 优先级处理：url > file > chat
        if url:
            result.update(self._classify_url(url, chat))
        elif file:
            result.update(self._classify_file(file, chat))
        else:
            result.update(self._classify_chat(chat))
        
        return result

    def _classify_url(self, url: str, chat: str = None) -> Dict[str, Any]:
        """分类URL内容"""
        result = {'type': 'webpage', 'category': 'webpage', 'subcategory': 'general'}
        
        try:
            # GitHub项目
            if any(re.search(pattern, url, re.IGNORECASE) for pattern in self.url_patterns['github']):
                result.update({
                    'type': 'github',
                    'category': 'github',
                    'subcategory': 'repository',
                    'confidence': 0.9
                })
                
                # 提取仓库信息
                repo_match = re.search(r'github\.com/([^/]+)/([^/]+)', url)
                if repo_match:
                    result['extracted_info'] = {
                        'owner': repo_match.group(1),
                        'repo': repo_match.group(2),
                        'url': url,
                        'purpose': self._generate_github_purpose(repo_match.group(2), chat)
                    }
                
                return result
            
            # ArXiv论文
            if any(re.search(pattern, url, re.IGNORECASE) for pattern in self.url_patterns['arxiv']):
                paper_type = self._classify_paper_type(chat or "")
                result.update({
                    'type': 'pdf',
                    'category': 'pdf',
                    'subcategory': f'paper_{paper_type}',
                    'confidence': 0.95
                })
                
                # 提取论文信息
                arxiv_match = re.search(r'(\d{4}\.\d{4,5})', url)
                result['extracted_info'] = {
                    'url': url,
                    'arxiv_id': arxiv_match.group(1) if arxiv_match else '',
                    'paper_type': paper_type,
                    'purpose': self._generate_paper_purpose(paper_type, chat)
                }
                
                return result
            
            # PDF文件URL
            if url.lower().endswith('.pdf'):
                content_type = self._classify_pdf_content(chat or "")
                result.update({
                    'type': 'pdf',
                    'category': 'pdf',
                    'subcategory': content_type,
                    'confidence': 0.8
                })
                
                result['extracted_info'] = {
                    'url': url,
                    'content_type': content_type,
                    'purpose': self._generate_pdf_purpose(content_type, chat)
                }
                
                return result
        
        except Exception as e:
            logger.warning(f"URL分析失败: {e}")
            
        # 通用网页
        result['extracted_info'] = {
            'url': url,
            'purpose': self._generate_webpage_purpose(chat)
        }
        return result

    def _classify_file(self, file_path: str, chat: str = None) -> Dict[str, Any]:
        """分类本地文件"""
        result = {'type': 'local_file', 'category': 'local_file', 'subcategory': 'unknown'}
        
        try:
            path_obj = Path(file_path)
            ext = path_obj.suffix.lower()
            
            # 图片文件
            if ext in self.file_extensions['image']:
                image_type = self._classify_image_content(chat or "")
                
                if image_type == 'math_problem':
                    result.update({
                        'type': 'math_problem',
                        'category': 'image',
                        'subcategory': 'math_problem',
                        'confidence': 0.9
                    })
                    
                    result['extracted_info'] = {
                        'file_path': str(path_obj.resolve()) if path_obj.exists() else file_path,
                        'image_type': image_type,
                        'topic': self._extract_math_topic(chat or ""),
                        'purpose': self._generate_math_problem_purpose(chat)
                    }
                else:
                    result.update({
                        'type': 'local_file',
                        'category': 'image',
                        'subcategory': image_type,
                        'confidence': 0.8
                    })
                    
                    result['extracted_info'] = {
                        'file_path': str(path_obj.resolve()) if path_obj.exists() else file_path,
                        'image_type': image_type,
                        'purpose': self._generate_image_purpose(image_type, chat)
                    }
                
                return result
            
            # PDF文件
            elif ext == '.pdf':
                content_type = self._classify_pdf_content(chat or "")
                result.update({
                    'type': 'local_file',
                    'category': 'pdf',
                    'subcategory': content_type,
                    'confidence': 0.8
                })
                
                result['extracted_info'] = {
                    'file_path': str(path_obj.resolve()) if path_obj.exists() else file_path,
                    'content_type': content_type,
                    'purpose': self._generate_pdf_purpose(content_type, chat)
                }
                
                return result
            
            # 其他文档
            elif ext in self.file_extensions['document']:
                result.update({
                    'type': 'local_file',
                    'subcategory': 'document',
                    'confidence': 0.7
                })
                
                result['extracted_info'] = {
                    'file_path': str(path_obj.resolve()) if path_obj.exists() else file_path,
                    'file_type': ext[1:],  # 去掉点号
                    'purpose': self._generate_document_purpose(chat)
                }
        
        except Exception as e:
            logger.warning(f"文件分析失败: {e}")
        
        return result

    def _classify_chat(self, chat: str) -> Dict[str, Any]:
        """分类聊天指令"""
        result = {
            'type': 'chat',
            'category': 'chat',
            'subcategory': 'general',
            'confidence': 1.0
        }
        
        if chat:
            # 检查是否是概念介绍类型（需要走example_explain流程）
            concept_keywords = [
                '什么是', '介绍', '解释', '概念', '定义', '原理', '基本原理',
                '工作原理', '基础知识', '入门', '科普', '理论', '机制',
                'what is', 'introduce', 'explain', 'concept', 'principle',
                'basic', 'fundamental', 'theory', 'mechanism'
            ]
            
            chat_lower = chat.lower()
            is_concept_introduction = any(
                keyword.lower() in chat_lower 
                for keyword in concept_keywords
            )
            
            if is_concept_introduction:
                # 概念介绍类型，需要走example_explain流程
                result.update({
                    'type': 'concept_explain',
                    'category': 'chat',
                    'subcategory': 'concept_introduction',
                    'confidence': 0.9
                })
            
            # 提取主题
            topic = self._extract_topic_from_chat(chat)
            result['extracted_info'] = {
                'topic': topic,
                'instruction': chat,
                'purpose': chat  # 直接使用用户指令作为purpose
            }
        
        return result

    def _classify_paper_type(self, text: str) -> str:
        """分类论文类型"""
        if not text:
            return 'research'
        
        text_lower = text.lower()
        for paper_type, keywords in self.paper_type_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                return paper_type
        
        return 'research'  # 默认为研究论文

    def _classify_pdf_content(self, text: str) -> str:
        """分类PDF内容类型"""
        if not text:
            return 'document'
        
        text_lower = text.lower()
        if any(keyword in text_lower for keyword in self.paper_type_keywords['book']):
            return 'book'
        elif any(keyword in text_lower for keyword in self.paper_type_keywords['survey']):
            return 'paper_survey'
        elif any(keyword in text_lower for keyword in self.paper_type_keywords['technical_report']):
            return 'paper_report'
        
        return 'document'

    def _classify_image_content(self, text: str) -> str:
        """分类图片内容类型"""
        if not text:
            return 'general'
        
        text_lower = text.lower()
        for image_type, keywords in self.image_content_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                return image_type
        
        return 'general'

    def _extract_topic_from_chat(self, chat: str) -> str:
        """从聊天指令中提取主题"""
        if not chat:
            return 'chat_content'
        
        # 先尝试匹配常见的表达模式
        import re
        
        # 模式1: "介绍/解释/分析/讲解 + 主题 + 的/原理/工作/机制等"
        patterns = [
            r'(?:介绍|解释|分析|讲解|科普|阐述|说明|演示)\s*(.+?)(?:的(?:原理|工作|机制|概念|基础|方法|技术|流程|过程)|$)',
            r'(?:什么是|怎么|如何)\s*(.+?)(?:\?|？|$)',
            r'(.+?)(?:的原理|的工作原理|的机制|的概念|的基础|的方法|的技术|的流程|的过程)',
            r'(.+?)(?:算法|技术|原理|概念|方法|系统|模型|理论|框架)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, chat, re.IGNORECASE)
            if match:
                topic = match.group(1).strip()
                # 清理提取的主题
                topic = re.sub(r'^(?:这个|那个|一个|有关|关于|的)\s*', '', topic)
                topic = re.sub(r'\s*(?:是什么|怎么样|如何|为什么)$', '', topic)
                if topic and len(topic) > 1:
                    return topic
        
        # 预定义的技术关键词映射
        topic_keywords = {
            '深度学习': '深度学习', '机器学习': '机器学习', '人工智能': '人工智能',
            '区块链': '区块链', '量子计算': '量子计算', '算法': '算法',
            '数据结构': '数据结构', '编程': '编程', '网络安全': '网络安全',
            '云计算': '云计算', '大数据': '大数据', 'BPE': 'BPE算法',
            'CNN': 'CNN', 'RNN': 'RNN', 'LSTM': 'LSTM', 'GAN': 'GAN',
            'Transformer': 'Transformer', 'BERT': 'BERT', 'GPT': 'GPT',
            '神经网络': '神经网络', '卷积': '卷积神经网络', '循环神经网络': 'RNN',
            '自注意力': '自注意力机制', '注意力': '注意力机制',
            '快速排序': '快速排序', '归并排序': '归并排序', '堆排序': '堆排序',
            '二叉树': '二叉树', '哈希表': '哈希表', '图论': '图论',
            '动态规划': '动态规划', '贪心算法': '贪心算法'
        }
        
        # 按长度降序排序，优先匹配长关键词
        sorted_keywords = sorted(topic_keywords.items(), key=lambda x: len(x[0]), reverse=True)
        for keyword, topic_name in sorted_keywords:
            if keyword.lower() in chat.lower():
                return topic_name
        
        # 提取核心词汇（中文或英文连续词汇）
        words = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z]+', chat)
        
        # 过滤掉常见的无意义词汇
        stop_words = {'介绍', '解释', '分析', '讲解', '科普', '阐述', '说明', '演示', 
                     '什么是', '怎么', '如何', '为什么', '这个', '那个', '一个', 
                     '有关', '关于', '的', '是', '吗', '呢', '啊', '吧'}
        
        meaningful_words = [word for word in words if word not in stop_words and len(word) > 1]
        
        if meaningful_words:
            # 优先选择较长的词汇
            topic = max(meaningful_words, key=len)
            return topic
        
        # 如果都没有匹配到，返回原始文本的前20个字符
        return chat[:20] if len(chat) > 20 else chat

    def _extract_math_topic(self, text: str) -> str:
        """从文本中提取数学主题"""
        math_topics = {
            '几何': '几何', '代数': '代数', '微积分': '微积分', '概率': '概率',
            '统计': '统计', '函数': '函数', '三角': '三角函数', '勾股': '勾股定理'
        }
        
        for keyword, topic in math_topics.items():
            if keyword in text:
                return topic
        
        return '数学题目'

    # Purpose生成方法
    def _generate_github_purpose(self, repo_name: str, chat: str = None) -> str:
        if chat:
            return f"为技术爱好者介绍{repo_name}项目，{chat}，采用通俗易懂的风格，视频长度5分钟"
        return f"为技术爱好者介绍{repo_name}开源项目，解析核心功能、技术架构和应用价值，采用通俗易懂的风格，视频长度5分钟"

    def _generate_paper_purpose(self, paper_type: str, chat: str = None) -> str:
        type_map = {
            'survey': '综述论文',
            'technical_report': '技术报告',
            'research': '研究论文'
        }
        paper_name = type_map.get(paper_type, '学术论文')
        
        if chat:
            return f"为研究人员深度解读这篇{paper_name}，{chat}，采用学术严谨的风格，视频长度5分钟"
        return f"为研究人员深度解读这篇{paper_name}，分析研究方法、核心贡献和实验结果，采用学术严谨的风格，视频长度5分钟"

    def _generate_pdf_purpose(self, content_type: str, chat: str = None) -> str:
        if content_type == 'book':
            if chat:
                return f"解析这本书的内容，{chat}，采用通俗易懂的风格，视频长度5分钟"
            return "解析书籍内容，提炼核心思想和关键观点，采用通俗易懂的风格，视频长度5分钟"
        
        if chat:
            return f"分析PDF文档内容，{chat}，提取关键信息，视频长度5分钟"
        return "分析PDF文档内容，提取关键信息和核心观点，视频长度5分钟"

    def _generate_math_problem_purpose(self, chat: str = None) -> str:
        if chat:
            return f"{chat}，用通俗易懂的语言，结合可视化演示，帮助理解解题思路和数学概念，视频长度5分钟"
        return "针对这道数学题目，分析解题思路，用通俗易懂的语言结合可视化演示，帮助理解核心数学概念，视频长度5分钟"

    def _generate_image_purpose(self, image_type: str, chat: str = None) -> str:
        if chat:
            return f"分析图片内容，{chat}，提供结构化的信息解读，视频长度5分钟"
        return "分析图片内容，识别关键信息并提供结构化的解读，视频长度5分钟"

    def _generate_webpage_purpose(self, chat: str = None) -> str:
        if chat:
            return f"分析网页内容，{chat}，为读者提供核心信息的总结，视频长度5分钟"
        return "分析网页内容，为读者提供核心信息的总结和见解，视频长度5分钟"

    def _generate_document_purpose(self, chat: str = None) -> str:
        if chat:
            return f"分析文档内容，{chat}，提取关键信息，视频长度5分钟"
        return "分析文档内容，提取关键信息和核心观点，视频长度5分钟"


class SmartIntentClassificationAgent:
    """智能意图分类代理主类"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        self.config_path = config_path
        self.safety_checker = ContentSafetyChecker()
        self.classifier = SmartIntentClassifier()

    def _load_config(self) -> Dict[str, Any]:
        """加载原始配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return {}

    def process_intent_from_config(self) -> Dict[str, Any]:
        """
        从配置文件的material.intent_input读取输入并处理意图分类
        
        Returns:
            处理结果字典
        """
        config = self._load_config()
        material_config = config.get('material', {})
        intent_input = material_config.get('intent_input', {})
        url = intent_input.get('url', '').strip()
        file = intent_input.get('file', '').strip()
        chat = intent_input.get('chat', '').strip()
        
        # 将空字符串转换为None
        url = url if url else None
        file = file if file else None
        chat = chat if chat else None
        
        return self.process_intent(url, file, chat)

    def process_intent(self, url: str = None, file: str = None, chat: str = None) -> Dict[str, Any]:
        """
        处理意图分类的主流程
        
        Args:
            url: 输入的URL
            file: 输入的文件路径  
            chat: 输入的聊天指令
            
        Returns:
            处理结果字典
        """
        try:
            # 1. 输入验证
            if not any([url, file, chat]):
                return {'error': '必须提供url、file或chat中的至少一个参数'}
            
            # 2. 内容安全检查
            if chat:
                is_safe, error_msg = self.safety_checker.check_chat_instruction_safety(chat)
                if not is_safe:
                    return {'error': f'聊天指令安全检查失败: {error_msg}'}
            
            # 3. 智能分类
            classification = self.classifier.classify_content(url, file, chat)
            
            # 4. URL可访问性检查
            if url and not self._check_url_accessibility(url):
                return {'error': f'URL无法访问或下载失败: {url}'}
            
            # 5. 文件存在性检查
            if file and not Path(file).exists():
                return {'error': f'文件不存在: {file}'}
            
            return {
                'success': True,
                'classification': classification,
                'safety_check': '通过',
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            logger.exception(f"意图处理失败: {e}")
            return {'error': f'处理过程中发生错误: {str(e)}'}

    def _check_url_accessibility(self, url: str) -> bool:
        """检查URL可访问性"""
        try:
            import requests
            response = requests.head(url, timeout=10, allow_redirects=True)
            return response.status_code < 400
        except Exception as e:
            logger.warning(f"URL可访问性检查失败: {e}")
            return False


def main():
    """主函数 - 测试各种意图分类场景"""
    
    # 测试用例定义
    test_cases = [
        # 数学题目测试（重点测试）
        {
            "name": "数学题目 - 几何题",
            "url": None,
            "file": "./output/初中几何题_compressed_3.png",
            "chat": "讲解这道几何题的解题思路",
            "expected_type": "math_problem",
            "expected_category": "image",
            "expected_subcategory": "math_problem"
        },
        {
            "name": "数学题目 - 代数题",
            "url": None,
            "file": "./output/初中几何题_compressed_3.png",  # 使用实际存在的文件
            "chat": "解决这道代数方程题目",
            "expected_type": "math_problem",
            "expected_category": "image",
            "expected_subcategory": "math_problem"
        },
        {
            "name": "数学题目 - 习题练习",
            "url": None,
            "file": "./output/初中几何题_compressed_3.png",  # 使用实际存在的文件
            "chat": "分析这道数学练习题",
            "expected_type": "math_problem",
            "expected_category": "image",
            "expected_subcategory": "math_problem"
        },
        
        # GitHub项目测试
        {
            "name": "GitHub项目",
            "url": "https://github.com/openai/whisper",  # 使用可访问的URL
            "file": None,
            "chat": "介绍这个AI项目的核心功能",
            "expected_type": "github",
            "expected_category": "github",
            "expected_subcategory": "repository"
        },
        {
            "name": "GitHub项目 - 无chat",
            "url": "https://github.com/openai/whisper",
            "file": None,
            "chat": None,
            "expected_type": "github",
            "expected_category": "github",
            "expected_subcategory": "repository"
        },
        
        # ArXiv论文测试（跳过实际URL检查的测试）
        {
            "name": "ArXiv研究论文",
            "url": "https://arxiv.org/pdf/2507.16632",
            "file": None,
            "chat": "深度分析这篇研究论文的方法",
            "expected_type": "pdf",
            "expected_category": "pdf",
            "expected_subcategory": "paper_research",
            "skip_url_check": True  # 跳过URL检查，只测试分类逻辑
        },
        {
            "name": "ArXiv综述论文",
            "url": "https://arxiv.org/pdf/2304.08485",
            "file": None,
            "chat": "这是一篇综述论文，请详细介绍",
            "expected_type": "pdf",
            "expected_category": "pdf",
            "expected_subcategory": "paper_survey",
            "skip_url_check": True
        },
        {
            "name": "ArXiv技术报告",
            "url": "https://arxiv.org/pdf/2103.00020",
            "file": None,
            "chat": "分析这篇技术报告的贡献",
            "expected_type": "pdf",
            "expected_category": "pdf",
            "expected_subcategory": "paper_technical_report",
            "skip_url_check": True
        },
        
        # 图片文件测试（非数学题目）
        {
            "name": "普通图片 - 截图",
            "url": None,
            "file": "./output/初中几何题_compressed_3.png",  # 使用实际存在的文件
            "chat": "分析这个界面截图",
            "expected_type": "local_file",
            "expected_category": "image",
            "expected_subcategory": "screenshot"
        },
        {
            "name": "普通图片 - 图表",
            "url": None,
            "file": "./output/初中几何题_compressed_3.png",  # 使用实际存在的文件
            "chat": "解释这个图表的数据",
            "expected_type": "local_file",
            "expected_category": "image",
            "expected_subcategory": "diagram"
        },
        
        # 聊天指令测试
        {
            "name": "聊天 - 深度学习概念",
            "url": None,
            "file": None,
            "chat": "解释深度学习的基本原理",
            "expected_type": "concept_explain",
            "expected_category": "chat",
            "expected_subcategory": "concept_introduction"
        },
        {
            "name": "聊天 - 算法原理介绍",
            "url": None,
            "file": None,
            "chat": "介绍快速排序算法的工作原理",
            "expected_type": "concept_explain",
            "expected_category": "chat",
            "expected_subcategory": "concept_introduction"
        },
        {
            "name": "聊天 - 什么是概念",
            "url": None,
            "file": None,
            "chat": "什么是机器学习？",
            "expected_type": "concept_explain",
            "expected_category": "chat",
            "expected_subcategory": "concept_introduction"
        },
        {
            "name": "聊天 - 科普理论",
            "url": None,
            "file": None,
            "chat": "科普一下量子计算的基础知识",
            "expected_type": "concept_explain",
            "expected_category": "chat",
            "expected_subcategory": "concept_introduction"
        },
        {
            "name": "聊天 - 数据分析方法",
            "url": None,
            "file": None,
            "chat": "解析Excel数据的方法",
            "expected_type": "chat",
            "expected_category": "chat",
            "expected_subcategory": "general"
        },
        {
            "name": "聊天 - 普通指令",
            "url": None,
            "file": None,
            "chat": "分析这个问题的解决方案",
            "expected_type": "chat",
            "expected_category": "chat",
            "expected_subcategory": "general"
        }
    ]
    
    # 执行测试
    print("🧪 开始意图分类测试")
    print("=" * 80)
    
    agent = SmartIntentClassificationAgent()
    total_tests = len(test_cases)
    passed_tests = 0
    failed_tests = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}/{total_tests}: {test_case['name']}")
        print(f"   输入: url={test_case['url']}, file={test_case['file']}, chat={test_case['chat']}")
        
        try:
            # 执行意图分类
            result = agent.process_intent(
                url=test_case['url'],
                file=test_case['file'],
                chat=test_case['chat']
            )
            
            # 如果是跳过URL检查的测试，且错误是URL访问问题，则手动执行分类逻辑
            if 'error' in result and test_case.get('skip_url_check', False) and 'URL无法访问' in result['error']:
                print(f"   🔄 跳过URL检查，直接测试分类逻辑")
                # 直接调用分类器测试逻辑
                classification = agent.classifier.classify_content(
                    url=test_case['url'],
                    file=test_case['file'],
                    chat=test_case['chat']
                )
                result = {
                    'success': True,
                    'classification': classification,
                    'safety_check': '通过'
                }
            
            if 'error' in result:
                print(f"   ❌ 错误: {result['error']}")
                failed_tests.append({
                    'name': test_case['name'],
                    'error': result['error'],
                    'type': 'error'
                })
                continue
            
            classification = result['classification']
            
            # 验证分类结果
            actual_type = classification['type']
            actual_category = classification['category']
            actual_subcategory = classification['subcategory']
            
            expected_type = test_case['expected_type']
            expected_category = test_case['expected_category']
            expected_subcategory = test_case['expected_subcategory']
            
            type_match = actual_type == expected_type
            category_match = actual_category == expected_category
            subcategory_match = actual_subcategory == expected_subcategory
            
            all_match = type_match and category_match and subcategory_match
            
            if all_match:
                print(f"   ✅ 通过")
                print(f"      类型: {actual_type} | 分类: {actual_category} | 子分类: {actual_subcategory}")
                passed_tests += 1
            else:
                print(f"   ❌ 失败")
                print(f"      期望: type={expected_type}, category={expected_category}, subcategory={expected_subcategory}")
                print(f"      实际: type={actual_type}, category={actual_category}, subcategory={actual_subcategory}")
                failed_tests.append({
                    'name': test_case['name'],
                    'expected': f"{expected_type}/{expected_category}/{expected_subcategory}",
                    'actual': f"{actual_type}/{actual_category}/{actual_subcategory}",
                    'type': 'mismatch'
                })
            
            # 显示提取的信息
            extracted_info = classification.get('extracted_info', {})
            if extracted_info:
                print(f"      信息: topic={extracted_info.get('topic', 'N/A')}")
                if 'purpose' in extracted_info and len(extracted_info['purpose']) > 50:
                    print(f"            purpose={extracted_info['purpose'][:50]}...")
                else:
                    print(f"            purpose={extracted_info.get('purpose', 'N/A')}")
                    
        except Exception as e:
            print(f"   ❌ 异常: {str(e)}")
            failed_tests.append({
                'name': test_case['name'],
                'error': str(e),
                'type': 'exception'
            })
    
    # 测试总结
    print("\n" + "=" * 80)
    print("🎯 测试总结")
    print(f"   总测试数: {total_tests}")
    print(f"   通过数: {passed_tests}")
    print(f"   失败数: {len(failed_tests)}")
    print(f"   成功率: {passed_tests/total_tests*100:.1f}%")
    
    if failed_tests:
        print("\n❌ 失败的测试:")
        for failure in failed_tests:
            print(f"   • {failure['name']}")
            if failure['type'] == 'mismatch':
                print(f"     期望: {failure['expected']}")
                print(f"     实际: {failure['actual']}")
            else:
                print(f"     {failure['type']}: {failure.get('error', 'Unknown error')}")
    
    print("\n🏁 测试完成")
    
    return passed_tests == total_tests


if __name__ == "__main__":
    main() 