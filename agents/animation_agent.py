from dotenv import load_dotenv

load_dotenv()

import json
import os
import re
from pathlib import Path
from typing import Any, Optional

import yaml
from camel.agents import ChatAgent
from camel.models import ModelFactory
from camel.toolkits.base import BaseToolkit
from camel.toolkits.function_tool import FunctionTool
from camel.types import ModelPlatformType
from loguru import logger

from dsl.ast_builder import ASTBuilder
from dsl.manim_code import ManimCodeGenerator
from dsl.parser import ManimDSLParser
from prompts.animation_agent.prompt_templates import (
    ANIMATION_TYPE_SELECTION_PROMPT,
    DSL_GENERATION_PROMPT,
    HYBRID_MANIM_CODE_PROMPT_TEMPLATE,
    MANIM_CODE_PROMPT_TEMPLATE,
    MANIM_FUNCTION_REFERENCE,
)


class ManimCodeToolkit(BaseToolkit):
    """
    Toolkit for generating Manim code using LLM-based code generation.
    """

    def __init__(self):
        """Initialize the ManimCodeToolkit."""
        self._initialize_config()
        self._initialize_model()

    def _initialize_config(self):
        """Load configuration from config.yaml."""
        # Get project root directory
        current_dir = Path(__file__).resolve().parent
        root_dir = current_dir.parent

        # Load configuration
        config_path = os.path.join(root_dir, "config/config.yaml")
        try:
            with open(config_path) as file:
                config = yaml.safe_load(file)

            # Get model config
            self.model_config = config.get("model", {})
            self.api_config = config.get("api", {})

            # Get other configs
            self.file_config = config.get("files", {})
            self.agent_config = config.get("agents", {})
        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")
            # Set default values
            self.model_config = {"type": "google/gemini-2.0-flash-lite-001"}
            self.api_config = {}

    def _initialize_model(self):
        """Initialize the LLM model."""
        self.model = ModelFactory.create(
            model_platform=ModelPlatformType.OPENROUTER,
            model_type=self.model_config.get("type", "google/gemini-2.0-flash-lite-001"),
            api_key=self.api_config.get("openrouter_api_key", os.getenv("OPENROUTER_API_KEY")),
            url=self.api_config.get("openrouter_api_base_url", "https://openrouter.ai/api/v1"),
        )

    def _extract_code(self, content: str) -> str:
        """Extract Python code from text content."""
        # Try to extract code blocks
        code_blocks = re.findall(r"```(?:python)?\s*([\s\S]*?)\s*```", content)
        if code_blocks:
            # Return the first found code block
            return code_blocks[0].strip()

        # If no code blocks found, return the whole content
        return content.strip()

    def generate_manim_code(
        self, title: str, content: str, storyboard: str, media_assets: Optional[list[str]] = None, step: int = 1
    ) -> str:
        """
        Generate Manim code based on the provided information.

        Args:
            title (str): Title of the animation
            content (str): Content description
            storyboard (str): Full storyboard content
            media_assets (Optional[List[str]]): List of media asset paths
            step (int): Step number

        Returns:
            str: The path to the generated Manim code file
        """
        if media_assets is None:
            media_assets = []

        # Create an LLM agent
        agent = ChatAgent(model=self.model)

        # Format the prompt
        prompt = MANIM_CODE_PROMPT_TEMPLATE.format(
            title=title,
            content=content,
            storyboard=storyboard,
            media_assets=json.dumps(media_assets, ensure_ascii=False),
            step=step,
        )

        # Get the LLM response
        response = agent.step(prompt)
        code = self._extract_code(response.msgs[0].content)

        # Save the generated code to a file
        filename = f"render_storyboard_{step}.py"
        with open(filename, "w", encoding="utf-8") as f:
            f.write(code)

        logger.info(f"Generated Manim code saved to {filename}")
        return filename

    def generate_from_storyboard_json(
        self, storyboard_json_path: str, start_step: int = 1, end_step: Optional[int] = None
    ) -> list[str]:
        """从JSON格式的分镜描述生成多个manim代码文件

        Args:
            storyboard_json_path (str): 分镜JSON文件路径
            start_step (int): 起始分镜索引
            end_step (Optional[int]): 结束分镜索引

        Returns:
            List[str]: 生成的代码文件路径列表
        """
        try:
            with open(storyboard_json_path, encoding="utf-8") as f:
                storyboard_data = json.load(f)

            # Handle both list and dictionary format for storyboard
            if isinstance(storyboard_data, dict):
                storyboard = storyboard_data.get("storyboard", [])
            else:
                storyboard = storyboard_data  # Assume it's already a list

            if not storyboard:
                logger.error("Storyboard is empty or invalid")
                return []

            if end_step is None:
                end_step = len(storyboard)

            output_files = []

            for step in range(start_step, min(end_step + 1, len(storyboard) + 1)):
                frame = storyboard[step - 1]

                # Extract title
                title = frame.get("分镜名", "")

                # Get content from either "分镜内容" or "讲解文案"
                content = frame.get("分镜内容", frame.get("讲解文案", ""))

                # Create a summary of the storyboard frame
                frame_description = json.dumps(frame, ensure_ascii=False, indent=2)

                # Extract media assets
                media_assets = []
                primary_asset = frame.get("素材名", "")
                if primary_asset:
                    media_assets.append(primary_asset)

                # Generate the manim code
                output_file = self.generate_manim_code(
                    title=title, content=content, storyboard=frame_description, media_assets=media_assets, step=step
                )

                if output_file:
                    output_files.append(output_file)
                    logger.info(f"Generated manim code for step {step}: {output_file}")
                else:
                    logger.warning(f"Failed to generate manim code for step {step}")

            return output_files

        except Exception as e:
            logger.error(f"Error generating code from storyboard: {str(e)}")
            return []

    def get_tools(self) -> list[FunctionTool]:
        """Return toolkit functions"""
        return [
            FunctionTool(self.generate_manim_code),
            FunctionTool(self.generate_from_storyboard_json),
        ]


class AnimationAgent(BaseToolkit):
    """
    Agent for generating animation content from storyboard frames
    using either Excalidraw or Manim based on content analysis.
    """

    def __init__(self):
        """Initialize the AnimationAgent."""
        super().__init__()
        self._initialize_config()
        self._initialize_model()
        self._create_directories()

    def _initialize_config(self):
        """Load configuration from config.yaml."""
        # Get project root directory
        current_dir = Path(__file__).resolve().parent
        root_dir = current_dir.parent

        # Load configuration
        config_path = os.path.join(root_dir, "config/config.yaml")
        try:
            with open(config_path) as file:
                config = yaml.safe_load(file)

            # Get model config
            self.model_config = config.get("model", {})
            self.api_config = self.model_config.get("api", {})

            # Get other configs
            self.file_config = config.get("files", {})
            self.agent_config = config.get("agents", {})
        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")
            # Set default values
            self.model_config = {"type": "google/gemini-2.0-flash-lite-001"}
            self.api_config = {}

    def _initialize_model(self):
        """Initialize the LLM model."""
        self.model = ModelFactory.create(
            model_platform=ModelPlatformType.OPENROUTER,
            model_type=self.model_config.get("type", "google/gemini-2.0-flash-lite-001"),
            api_key=self.api_config.get("openrouter_api_key"),
            url=self.api_config.get("openrouter_api_base_url"),
        )

    def _create_directories(self):
        """Create necessary directories."""
        os.makedirs("prompts/animation_agent", exist_ok=True)
        os.makedirs("output/dsl", exist_ok=True)

    def _save_function_references(self):
        """Save function references to a file for potential future use."""
        function_ref_file = "prompts/animation_agent/function_reference.md"
        with open(function_ref_file, "w") as f:
            f.write(MANIM_FUNCTION_REFERENCE)
        return function_ref_file

    def determine_animation_type(self, storyboard_content: str) -> str:
        """
        Determine whether to use Excalidraw or Manim for the given storyboard content.

        Args:
            storyboard_content (str): Content of a storyboard frame

        Returns:
            str: Either "excalidraw" or "manim"
        """
        agent = ChatAgent(model=self.model)
        prompt = ANIMATION_TYPE_SELECTION_PROMPT.format(storyboard_content=storyboard_content)

        response = agent.step(prompt)
        response_content = response.msgs[0].content.lower()

        # Determine the animation type from the response
        if "excalidraw" in response_content:
            result = "excalidraw"
        else:
            result = "manim"

        logger.info(f"Determined animation type: {result}")

        # Save the decision for debugging purposes
        with open(f"output/animation_decision_{hash(storyboard_content)}.txt", "w") as f:
            f.write(f"Decision: {result}\n\nReasoning:\n{response_content}")

        return result

    def generate_dsl_for_manim(self, storyboard_content: str, frame_index: int) -> str:
        """
        Generate a DSL description for Manim based on storyboard content.

        Args:
            storyboard_content (str): Content of a storyboard frame
            frame_index (int): Index of the frame

        Returns:
            str: Path to the generated DSL file
        """
        # First save the function reference once
        self._save_function_references()

        agent = ChatAgent(model=self.model)
        prompt = DSL_GENERATION_PROMPT.format(
            storyboard_content=storyboard_content, available_functions=MANIM_FUNCTION_REFERENCE
        )

        response = agent.step(prompt)
        response_content = response.msgs[0].content

        # Extract JSON from the response
        dsl_json = self._extract_json(response_content)

        if not dsl_json:
            logger.error("Failed to extract DSL JSON from the response")
            return None

        # Save the DSL to a file
        dsl_file = f"output/dsl/frame_{frame_index}.json"
        with open(dsl_file, "w") as f:
            json.dump(dsl_json, f, indent=2)

        logger.info(f"Generated DSL saved to {dsl_file}")
        return dsl_file

    def _extract_json(self, content: str) -> dict[str, Any]:
        """Extract JSON from text content."""
        try:
            # First try to find JSON within ```json blocks
            json_matches = re.findall(r"```(?:json)?\s*([\s\S]*?)\s*```", content)
            if json_matches:
                for match in json_matches:
                    return json.loads(match)
            # If not found, try to extract JSON from the entire content
            return json.loads(content)
        except Exception as e:
            logger.error(f"Error extracting JSON: {str(e)}")
            return None

    def generate_manim_from_dsl(self, dsl_file_path: str, frame_index: int) -> str:
        """
        Generate Manim code from a DSL file.

        Args:
            dsl_file_path (str): Path to the DSL JSON file
            frame_index (int): Index of the frame

        Returns:
            str: Path to the generated Manim file
        """
        try:
            # Parse DSL file
            dsl = ManimDSLParser.parse_from_file(dsl_file_path)

            # Build AST
            ast = ASTBuilder.build_ast(dsl)

            # Generate code
            generator = ManimCodeGenerator()
            code = generator.visit_scene(ast)

            # Write to file
            output_file = f"render_storyboard_{frame_index}.py"
            with open(output_file, "w") as f:
                f.write(code)

            logger.info(f"Generated Manim code: {output_file}")
            return output_file

        except Exception as e:
            logger.error(f"Error generating Manim code: {str(e)}")
            return None

    def generate_hybrid_manim_code(
        self, title: str, content: str, storyboard: str, media_assets: Optional[list[str]] = None, step: int = 1
    ) -> str:
        """
        Generate Manim code using a hybrid approach that combines function calls with DSL-generated code.
        This is used when the standard function library doesn't have all required functions.

        Args:
            title (str): Title of the animation
            content (str): Content description
            storyboard (str): Full storyboard content
            media_assets (Optional[List[str]]): List of media asset paths
            step (int): Step number

        Returns:
            str: The path to the generated Manim code file
        """
        try:
            if media_assets is None:
                media_assets = []

            # Create an LLM agent
            agent = ChatAgent(model=self.model)

            # Format the prompt for hybrid code generation
            prompt = HYBRID_MANIM_CODE_PROMPT_TEMPLATE.format(
                title=title,
                content=content,
                storyboard=storyboard,
                media_assets=json.dumps(media_assets, ensure_ascii=False),
                step=step,
            )

            # Get the LLM response
            response = agent.step(prompt)
            code = self._extract_code(response.msgs[0].content)

            # Ensure the code is valid
            if not code or not code.strip():
                logger.warning("生成的代码为空，尝试再次获取")
                response = agent.step(prompt)
                code = self._extract_code(response.msgs[0].content)

                if not code or not code.strip():
                    logger.error("无法生成有效的Manim代码")
                    return None

            # Save the generated code to a file
            filename = f"render_storyboard_{step}.py"
            with open(filename, "w", encoding="utf-8") as f:
                f.write(code)

            logger.info(f"Generated hybrid Manim code saved to {filename}")
            return filename

        except Exception as e:
            logger.error(f"生成混合Manim代码时出错: {str(e)}")
            return None

    def process_storyboard_frame(
        self, frame: dict[str, Any], frame_index: int, manim_toolkit=None, excalidraw_toolkit=None
    ) -> str:
        """
        Process a single storyboard frame by determining the animation type and generating the animation.

        Args:
            frame (Dict[str, Any]): Storyboard frame data
            frame_index (int): Index of the frame
            manim_toolkit: Optional ManimCodeToolkit instance
            excalidraw_toolkit: Optional ExcalidrawToolkit instance

        Returns:
            str: Path to the generated animation file
        """
        # Convert frame to string for processing
        frame_content = json.dumps(frame, ensure_ascii=False, indent=2)

        # Determine the animation type
        animation_type = self.determine_animation_type(frame_content)

        # Extract necessary information from the frame
        title = frame.get("分镜名", "")
        content = frame.get("分镜内容", frame.get("讲解文案", ""))

        # Extract media assets
        media_assets = []
        primary_asset = frame.get("素材名", "")
        if primary_asset:
            media_assets.append(primary_asset)

        if animation_type == "manim":
            # Check if the content requires custom animations not available in function library
            requires_custom_code = self._check_requires_custom_code(frame_content)

            if requires_custom_code:
                # Use hybrid approach with both functions and DSL
                return self.generate_hybrid_manim_code(
                    title=title,
                    content=content,
                    storyboard=frame_content,
                    media_assets=media_assets,
                    step=frame_index + 1,
                )
            elif manim_toolkit:
                # Use the provided ManimCodeToolkit for standard function calls
                return manim_toolkit.generate_manim_code(
                    title=title,
                    content=content,
                    storyboard=frame_content,
                    media_assets=media_assets,
                    step=frame_index + 1,
                )
            else:
                # Generate DSL and convert to Manim code
                dsl_file = self.generate_dsl_for_manim(frame_content, frame_index + 1)
                if dsl_file:
                    return self.generate_manim_from_dsl(dsl_file, frame_index + 1)

        elif animation_type == "excalidraw" and excalidraw_toolkit:
            # Use the provided ExcalidrawToolkit
            return excalidraw_toolkit.generate_excalidraw_video(content_description=content, step=frame_index + 1)

        return None

    def _check_requires_custom_code(self, storyboard_content: str) -> bool:
        """
        Check if the storyboard content requires custom code beyond available functions.

        Args:
            storyboard_content (str): Content of a storyboard frame

        Returns:
            bool: True if custom code is needed, False if standard functions are sufficient
        """
        try:
            agent = ChatAgent(model=self.model)
            prompt = """
请分析以下分镜内容，判断是否需要使用自定义的Manim代码，还是现有函数库就足够实现所需的动画效果。

可用的函数有：
1. render_media_display - 展示图片和视频
2. render_keywords - 显示关键词列表
3. render_sort_animation - 数组排序动画
4. render_code_snippet - 代码片段展示
5. render_text_element - 文本元素展示
6. display_slogo - 显示标题和logo

<storyboard_content>
{}
</storyboard_content>

请只回答"yes"或"no"：
- 如果现有函数足以实现所需动画效果，回答"no"
- 如果需要自定义Manim代码来实现特殊效果，回答"yes"
""".format(storyboard_content)

            response = agent.step(prompt)
            response_text = response.msgs[0].content.lower().strip()

            # 更可靠的判断方式，处理各种可能的回答
            if "yes" in response_text or "需要自定义" in response_text or "需要使用自定义" in response_text:
                logger.info("分析结果：需要使用自定义Manim代码")
                return True
            else:
                logger.info("分析结果：现有函数库足够实现所需动画效果")
                return False

        except Exception as e:
            logger.warning(f"判断是否需要自定义代码时出错: {str(e)}，默认使用标准函数")
            return False

    def process_storyboard(
        self,
        storyboard_json_path: str,
        manim_toolkit=None,
        excalidraw_toolkit=None,
        start_index: int = 0,
        end_index: Optional[int] = None,
    ) -> list[str]:
        """
        Process a storyboard JSON file and generate animations for each frame.

        Args:
            storyboard_json_path (str): Path to the storyboard JSON file
            manim_toolkit: Optional ManimCodeToolkit instance
            excalidraw_toolkit: Optional ExcalidrawToolkit instance
            start_index (int): Index of the first frame to process (0-based)
            end_index (Optional[int]): Index of the last frame to process (0-based)

        Returns:
            List[str]: List of paths to the generated animation files
        """
        try:
            # Load the storyboard
            with open(storyboard_json_path, encoding="utf-8") as f:
                storyboard_data = json.load(f)

            # Handle both list and dictionary format
            if isinstance(storyboard_data, dict):
                frames = storyboard_data.get("storyboard", [])
            else:
                frames = storyboard_data  # Assume it's already a list

            if not frames:
                logger.error("Storyboard is empty or invalid")
                return []

            # Apply index limits
            if end_index is None:
                end_index = len(frames) - 1

            start_index = max(0, start_index)
            end_index = min(end_index, len(frames) - 1)

            output_files = []

            # Process each frame
            for i in range(start_index, end_index + 1):
                frame = frames[i]
                logger.info(f"Processing frame {i+1}/{len(frames)}: {frame.get('分镜名', '')}")

                output_file = self.process_storyboard_frame(
                    frame=frame, frame_index=i, manim_toolkit=manim_toolkit, excalidraw_toolkit=excalidraw_toolkit
                )

                if output_file:
                    output_files.append(output_file)
                    logger.info(f"Generated animation for frame {i+1}: {output_file}")
                else:
                    logger.warning(f"Failed to generate animation for frame {i+1}")

            return output_files

        except Exception as e:
            logger.error(f"Error processing storyboard: {str(e)}")
            return []

    def get_tools(self) -> list[FunctionTool]:
        """Return toolkit functions"""
        return [
            FunctionTool(self.determine_animation_type),
            FunctionTool(self.generate_dsl_for_manim),
            FunctionTool(self.generate_manim_from_dsl),
            FunctionTool(self.generate_hybrid_manim_code),
            FunctionTool(self.process_storyboard_frame),
            FunctionTool(self.process_storyboard),
        ]

    def _extract_code(self, content: str) -> str:
        """
        Extract Python code from LLM response text content.

        Args:
            content (str): Response text from LLM

        Returns:
            str: Extracted Python code
        """
        # Try to extract code blocks with Python syntax highlighting
        code_blocks = re.findall(r"```(?:python)?\s*([\s\S]*?)\s*```", content)
        if code_blocks:
            # Return the first found code block
            return code_blocks[0].strip()

        # Try to extract any code blocks without language specification
        code_blocks = re.findall(r"```\s*([\s\S]*?)\s*```", content)
        if code_blocks:
            return code_blocks[0].strip()

        # If no code blocks found, return the whole content
        logger.warning("未找到代码块，将整个响应内容作为代码处理")
        return content.strip()


if __name__ == "__main__":
    from tools.excalidraw_toolkit import ExcalidrawToolkit

    agent = AnimationAgent()
    manim_toolkit = ManimCodeToolkit()
    excalidraw_toolkit = ExcalidrawToolkit()

    # Process the storyboard
    storyboard_path = "output/paper_content.json"
    if os.path.exists(storyboard_path):
        logger.info("Processing storyboard...")
        output_files = agent.process_storyboard(
            storyboard_json_path=storyboard_path, manim_toolkit=manim_toolkit, excalidraw_toolkit=excalidraw_toolkit
        )

        if output_files:
            logger.info(f"Generated {len(output_files)} animation files")
            for file in output_files:
                logger.info(f"- {file}")
