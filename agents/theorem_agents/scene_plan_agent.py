import os
import logging
from typing import Optional, Dict, Any

from camel.agents import ChatAgent
from camel.messages import BaseMessage
from camel.models import BaseModelBackend, ModelFactory
from camel.types import ModelPlatformType
import yaml

logger = logging.getLogger(__name__)

class ScenePlanAgent:
    """场景规划代理，负责生成概念算法原理解释视频的整体场景规划"""
    
    def __init__(self, model: BaseModelBackend, config_path: str = "config/config.yaml"):
        """
        初始化场景规划代理
        
        Args:
            model: 使用的模型后端
            config_path: 配置文件路径
        """
        self.model = model
        self.config_path = config_path
        
        # 加载配置
        self.load_config(config_path)
        
        # 创建代理
        self.agent = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="Concept Algorithm Explanation Expert",
                content="你是一位专业的概念算法原理解释专家，擅长将复杂的概念、算法、定理分解为连贯、易于理解的场景序列，通过清晰的逻辑结构和具体的例子来阐述核心原理。"
            ),
            model=model
        )
    
    def load_config(self, config_path: str):
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
        """
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            # 设置配置属性
            self.agent_config = config.get("agents", {}).get("scene_plan", {})
            self.prompts = self.agent_config.get("prompts", {})
            
            logger.info(f"场景规划代理配置加载成功: {config_path}")
        except Exception as e:
            logger.error(f"加载配置失败: {str(e)}")
            # 设置默认值
            self.agent_config = {}
            self.prompts = {}
    
    def generate(
        self, 
        topic: str, 
        description: str, 
        rag_context: Optional[str] = None,
        session_id: Optional[str] = None,
        file_path: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        生成场景规划
        
        Args:
            topic: 概念算法主题
            description: 概念算法描述
            rag_context: RAG检索的上下文
            session_id: 会话ID
            file_path: 可选的文件路径，如果提供则读取文件内容作为主要依据进行分镜设计
            
        Returns:
            Dict[str, Any]: 包含生成的场景规划的字典
        """
        logger.info(f"生成场景规划: {topic}")
        
        try:
            # 读取文件内容（如果提供了文件路径）
            file_content = None
            if file_path:
                file_content = self._read_file_content(file_path)
                if file_content:
                    logger.info(f"成功读取文件内容: {file_path}, 长度: {len(file_content)} 字符")
            
            # 构建提示
            prompt = self._build_prompt(topic, description, rag_context, file_content)
            
            # 生成响应
            message = BaseMessage.make_user_message(role_name="User", content=prompt)
            response = self.agent.step(message)
            
            # 提取内容
            scene_plan = response.msg.content
            logger.info(f"场景规划生成完成: {len(scene_plan)} 字符")
            
            return {
                "status": "success",
                "scene_outline": scene_plan
            }
        except Exception as e:
            logger.error(f"生成场景规划失败: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }
    
    def generate_scene_plan(
        self, 
        topic: str, 
        description: str, 
        rag_context: Optional[str] = None,
        session_id: Optional[str] = None,
        file_path: Optional[str] = None
    ) -> str:
        """
        生成场景规划
        
        Args:
            topic: 概念算法主题
            description: 概念算法描述
            rag_context: RAG检索的上下文
            session_id: 会话ID
            file_path: 可选的文件路径，如果提供则读取文件内容作为主要依据进行分镜设计
            
        Returns:
            str: 生成的场景规划
        """
        logger.info(f"生成场景规划: {topic}")
        
        # 读取文件内容（如果提供了文件路径）
        file_content = None
        if file_path:
            file_content = self._read_file_content(file_path)
            if file_content:
                logger.info(f"成功读取文件内容: {file_path}, 长度: {len(file_content)} 字符")
        
        # 构建提示
        prompt = self._build_prompt(topic, description, rag_context, file_content)
        
        # 生成响应
        message = BaseMessage.make_user_message(role_name="User", content=prompt)
        response = self.agent.step(message)
        
        # 提取内容
        scene_plan = response.msg.content
        logger.info(f"场景规划生成完成: {len(scene_plan)} 字符")
        
        return scene_plan
    
    def _build_prompt(self, topic: str, description: str, rag_context: Optional[str] = None, file_content: Optional[str] = None) -> str:
        """
        构建场景规划提示
        
        Args:
            topic: 概念算法主题
            description: 概念算法描述
            rag_context: RAG检索的上下文
            file_content: 文件内容（如果提供，将作为主要依据进行分镜设计）
            
        Returns:
            str: 构建的提示
        """
        # 获取自定义提示模板（如果有）
        template = self.prompts.get("scene_plan_template", "")
        
        if not template:
            # 默认提示模板
            template = """
你是一位专业的概念算法原理解释专家，需要为以下主题创建一个清晰的场景规划：

主题：{topic}
描述：{description}

请为概念算法原理解释视频设计一个场景大纲，将概念的讲解**结构化**为3-5个逻辑清晰、环环相扣的场景。每个场景应有**明确的解释目标**和**聚焦的内容重点**。

你的场景规划需包含：
1.  **场景数量与顺序：** 清晰列出场景总数（3-5个）及其排列次序。
2.  **场景内容与重点：** 为每个场景定义核心解释内容及其关键要点。
3.  **场景衔接与过渡：** 说明每个场景如何自然引出下一个场景，确保理解流顺畅。
4.  **整体逻辑连贯性：** 确保所有场景共同构成一个**完整、自洽**的叙事，覆盖概念的**核心原理、关键算法步骤、具体例子以及实际应用**。

设计原则：
- 从简单到复杂：先介绍基本概念，再深入细节
- 理论结合实例：每个抽象概念都要有具体的例子支撑
- 逐步递进：每个场景都在前一个场景的基础上进一步深化
- 突出核心：重点突出最关键的原理和算法思想

请使用以下格式输出你的场景大纲：

<SCENE_OUTLINE>
场景1: [场景名称]
-   [关键内容点1]
-   [关键内容点2]
-   ... (根据实际需要列出)

场景2: [场景名称]
-   [关键内容点1]
-   [关键内容点2]
-   ... (根据实际需要列出)

... (列出所有场景)
</SCENE_OUTLINE>

"""
        
        # 替换占位符
        prompt = template.format(
            topic=topic,
            description=description
        )
        
        # 添加文件内容 - 如果有文件输入，以文件内容为主要依据
        if file_content:
            prompt += f"\n\n**重要：以下文件内容是分镜设计的主要依据，请基于文件内容来设计场景规划**\n"
            prompt += f"文件内容：\n{file_content}\n"
            prompt += "\n**请仔细分析文件内容，根据其结构和逻辑来设计场景规划。场景规划应该完整覆盖文件中的核心概念和关键信息点。**"
            prompt += "\n**核心要求：**"
            prompt += "\n1. **具体例子场景（通常是场景3）**：详细步骤**必须原封不动**的从文档里拷过来，不能遗漏"
            prompt += "\n2. **其他场景（概念介绍、原理解释、总结等）**：适当简化表述，避免冗余重复"
            prompt += "\n**严格禁止：**"
            prompt += "\n- 在具体例子场景中省略任何细节步骤或数据"
            prompt += "\n- 在其他场景中出现过度冗长或重复的内容"
            prompt += "\n- 改写或重新表述原文中例子的精确表达"
            prompt += "\n**验证要求：确保具体例子场景包含文档中对应部分的所有详细信息，其他场景简洁明了。**"
        
        # 添加RAG上下文
        if rag_context:
            prompt += f"\n\n参考信息（用于辅助规划）：\n{rag_context}"
        
        return prompt 
    
    def _read_file_content(self, file_path: str) -> Optional[str]:
        """
        读取文件内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            Optional[str]: 文件内容，如果读取失败则返回None
        """
        try:
            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            logger.info(f"成功读取文件: {file_path}")
            return content
            
        except Exception as e:
            logger.error(f"读取文件失败 {file_path}: {str(e)}")
            return None


def main():
    """
    主函数，用于测试场景规划代理
    """
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    
    # 配置文件路径
    config_path = "config/config.yaml"
    
    try:
        # 读取配置文件
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 从配置文件获取topic
        topic = config.get("example_explain", {}).get("topic", "默认主题")
        logger.info(f"从配置文件读取到主题: {topic}")
        
        # 构建输入文件路径
        input_file_path = f"output/{topic}/example_explain.md"
        
        # 检查文件是否存在
        if not os.path.exists(input_file_path):
            logger.warning(f"输入文件不存在: {input_file_path}")
            logger.info("将使用默认描述而不是文件内容")
            input_file_path = None
        else:
            logger.info(f"找到输入文件: {input_file_path}")
        
        # 创建模型实例
        model_config = config.get("model", {})
        api_config = model_config.get("api", {})
        
        model = ModelFactory.create(
            model_platform=ModelPlatformType.OPENROUTER,
            model_type=model_config.get("type", "google/gemini-2.5-flash-preview-05-20"),
            api_key=api_config.get("openrouter_api_key"),
            url=api_config.get("openrouter_api_base_url"),
        )
        logger.info("模型创建成功")
        
        # 创建场景规划代理
        scene_plan_agent = ScenePlanAgent(model=model, config_path=config_path)
        logger.info("场景规划代理创建成功")
        
        # 准备描述
        description = f"详细解释{topic}的核心原理和应用"
        
        # 生成场景规划
        logger.info("开始生成场景规划...")
        result = scene_plan_agent.generate(
            topic=topic,
            description=description,
            file_path=input_file_path
        )
        
        if result["status"] == "success":
            logger.info("场景规划生成成功！")
            print("\n=== 场景规划结果 ===")
            print(result["scene_outline"])
            
            # 保存结果到文件
            output_dir = f"output/{topic}"
            os.makedirs(output_dir, exist_ok=True)
            output_file = os.path.join(output_dir, "scene_outline.txt")
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(result["scene_outline"])
            
            logger.info(f"场景规划已保存到: {output_file}")
        else:
            logger.error(f"场景规划生成失败: {result['message']}")
            
    except Exception as e:
        logger.error(f"测试执行失败: {str(e)}")


if __name__ == "__main__":
    main()