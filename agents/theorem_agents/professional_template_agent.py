import os
import logging
from typing import Optional, Dict, Any
import asyncio

from camel.agents import ChatAgent
from camel.messages import BaseMessage
from camel.models import BaseModelBackend
import yaml

logger = logging.getLogger(__name__)

class ProfessionalTemplateAgent:
    """专业模板代理，负责生成基于ProfessionalScienceTemplate的教学动画实现描述"""
    
    def __init__(self, model: BaseModelBackend, config_path: str = "config/config.yaml"):
        """
        初始化专业模板代理
        
        Args:
            model: 使用的模型后端
            config_path: 配置文件路径
        """
        self.model = model
        self.config_path = config_path
        
        # 加载配置
        self.load_config(config_path)
        
        # 创建代理
        self.agent = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="Professional Template Expert",
                content="你是一位专业的Manim动画模板专家，擅长使用ProfessionalScienceTemplate创建高质量的教育动画。"
            ),
            model=model
        )
    
    def load_config(self, config_path: str):
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
        """
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            # 设置配置属性
            self.agent_config = config.get("agents", {}).get("professional_template", {})
            self.prompts = self.agent_config.get("prompts", {})
            
            logger.info(f"专业模板代理配置加载成功: {config_path}")
        except Exception as e:
            logger.error(f"加载配置失败: {str(e)}")
            # 设置默认值
            self.agent_config = {}
            self.prompts = {}

    async def generate(
        self, 
        topic: str, 
        description: str, 
        scene_number: int,
        scene_outline: str,
        relevant_plugins: list = None,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        生成专业模板实现描述（与工作流接口兼容）
        
        Args:
            topic: 定理主题
            description: 定理描述
            scene_number: 场景编号
            scene_outline: 场景大纲
            relevant_plugins: 相关插件列表 
            session_id: 会话ID
            
        Returns:
            Dict[str, Any]: 包含生成的模板实现描述的字典
        """
        logger.info(f"为场景{scene_number}生成专业模板实现描述: {topic}")
        
        try:
            if relevant_plugins is None:
                relevant_plugins = []
                
            # 生成模板实现描述
            template_implementation = await self.generate_template_implementation(
                scene_number=scene_number,
                topic=topic,
                description=description,
                scene_outline=scene_outline,
                relevant_plugins=relevant_plugins,
                rag_context=None,
                session_id=session_id
            )
            
            return {
                "status": "success",
                "template_implementation": template_implementation
            }
        except Exception as e:
            logger.error(f"生成模板实现描述失败: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }
            
    async def generate_template_implementation(
        self, 
        scene_number: int,
        topic: str, 
        description: str, 
        scene_outline: str,
        relevant_plugins: list,
        rag_context: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> str:
        """
        生成专业模板实现描述
        
        Args:
            scene_number: 场景编号
            topic: 定理主题
            description: 定理描述
            scene_outline: 场景大纲
            relevant_plugins: 相关插件列表
            rag_context: RAG检索的上下文
            session_id: 会话ID
            
        Returns:
            str: 生成的模板实现描述
        """
        logger.info(f"生成场景{scene_number}的模板实现描述: {topic}")
        
        # 构建提示
        prompt = self._build_prompt(
            scene_number=scene_number,
            topic=topic,
            description=description,
            scene_outline=scene_outline,
            relevant_plugins=relevant_plugins,
            rag_context=rag_context
        )
        
        # 生成响应
        message = BaseMessage.make_user_message(role_name="User", content=prompt)
        response = self.agent.step(message)
        
        # 提取内容
        template_implementation = response.msg.content
        logger.info(f"模板实现描述生成完成: {len(template_implementation)} 字符")
        
        return template_implementation
    
    def _build_prompt(
        self, 
        scene_number: int,
        topic: str, 
        description: str, 
        scene_outline: str,
        relevant_plugins: list,
        rag_context: Optional[str] = None
    ) -> str:
        """
        构建专业模板实现提示
        
        Args:
            scene_number: 场景编号
            topic: 定理主题
            description: 定理描述
            scene_outline: 场景大纲
            relevant_plugins: 相关插件列表
            rag_context: RAG检索的上下文
            
        Returns:
            str: 构建的提示
        """
        # 获取自定义提示模板（如果有）
        template = self.prompts.get("template_implementation", "")
        
        if not template:
            # 默认提示模板
            template = """
你是一位专业的Manim动画模板专家，需要基于ProfessionalScienceTemplate为数学定理教学视频的场景{scene_number}创建详细的实现描述。

主题：{topic}
描述：{description}
场景大纲：{scene_outline}

【核心模板接口】

你必须使用ProfessionalScienceTemplate提供的标准接口方法：

**1. 区域内容创建接口（严格字数限制）**
- `create_title_region_content(title)`: 标题区域，限制8个字以内
- `create_step_region_content(step)`: 步骤区域，限制12个字以内  
- `create_main_region_content(content)`: 主内容区域，自动适配6.0×3.5空间
- `create_left_auxiliary_content(title, items)`: 左辅助区域，标题6字内，最多5项×15字/项
- `create_right_auxiliary_content(title, items)`: 右辅助区域，标题6字内，最多5项×15字/项
- `create_result_region_content(result)`: 结果区域，限制40个字以内

**2. 布局策略选择**
- 完整布局：使用左右两个辅助区域，适合概念丰富的内容
- 纯净布局：不使用辅助区域，主内容享有最大空间，适合复杂动画
- 左侧布局：仅使用左辅助区域，适合重点突出关键信息
- 右侧布局：仅使用右辅助区域，适合公式展示

**3. 内容建议指南（必须严格遵守）**
- 标题：8个字以内（如"函数原理"、"排序算法"）
- 步骤：12个字以内（如"第一步：数据预处理"）
- 辅助标题：6个字以内（如"要点"、"公式"、"特点"）
- 辅助项目：5项以内，每项15个字以内
- 结果描述：40个字以内

**4. 智能缩放特性**
- 标题和步骤：使用固定字体大小，保持视觉一致性
- 主内容和辅助区域：自动适配区域大小，内容过大时缩小，过小时放大
- 所有接口调用时无需考虑缩放问题，模板自动处理

【主内容区域精细化设计原则】

**1. 统一区域管理原则 ⭐⭐⭐（核心要求）**
- **所有主场景的VGroup必须统一放在create_main_region_content中**
- **禁止在主内容区域外单独定位任何核心动画元素**
- **所有数据结构、图表、公式、几何图形等都必须作为一个统一的VGroup传递给create_main_region_content**
- 模板会自动处理区域内的布局和缩放，无需手动定位

**2. 数据结构优先原则**
- 优先考虑核心数据结构的可视化：表格、图表、公式、几何图形等
- 数据结构的变化应作为动画的核心驱动力
- 避免装饰性元素，专注于信息传达
- 所有数据结构必须组织成VGroup，统一传递给主内容区域

**3. Transform动作原则**
- 优先使用Manim的Transform系列函数：
  - `ReplacementTransform`: 用于内容替换和状态转换
  - `TransformFromCopy`: 用于复制和衍生操作
  - `FadeTransform`: 用于淡入淡出过渡
  - `MorphShape`: 用于形状变化
- 核心动作识别：
  - 变换(Transform): 数据/公式/图形的状态改变
  - 组合(Combine): 多个元素合并成一个
  - 分解(Decompose): 一个元素分解成多个
  - 映射(Map): 元素间的对应关系
  - 突出(Highlight): 关键信息的强调
- 减少琐碎动画：只保留与核心概念直接相关的动画

**4. Group组织原则**
- 相关元素必须组织成Group：
  - 数学公式及其组成部分 → FormulaGroup
  - 表格及其行列 → TableGroup
  - 图形及其标注 → FigureGroup
  - 操作步骤及其说明 → StepGroup
- **所有主要Group必须合并为一个总的MainContentGroup**
- **MainContentGroup作为整体传递给create_main_region_content**
- Group必须精简，避免冗余元素
- 所有操作以Group为单位进行，避免对单个元素做细粒度控制
- 同类Group之间保持一致的视觉风格和动画节奏

**5. 区域职责分离原则**
- 辅助文字、解释性公式都放到左右辅助区域，保持主内容区域的简洁性
- 主内容区域仅保留与动画直接绑定的核心元素（如数据结构、图形变换等）
- 除非文字/公式是动画变换的直接对象，否则一律移至辅助区域
- 通过区域分工实现：主区域专注动画，辅助区域提供解释

【输出格式要求】

你的输出必须包含以下结构：

</SCENE_TEMPLATE_IMPLEMENTATION>
场景{scene_number}：{topic}教学动画实现

=== 模板继承设计 ===
继承类：ProfessionalScienceTemplate
场景主题：[主题名称]
布局策略：[完整布局/纯净布局/左侧布局/右侧布局]
总时长估计：[X秒]
动画复杂度：[简单/中等/复杂]

=== 区域内容设计 ===

【标题区域设计】
- 接口调用：create_title_region_content("[标题内容]")
- 内容：[8个字以内的标题]
- 设计理念：[为什么选择这个标题]
- 视觉效果：[字体效果、颜色搭配、出现方式]

【步骤区域设计】  
- 接口调用：create_step_region_content("[步骤描述]")
- 内容：[12个字以内的步骤描述]
- 动态更新：[如何在不同阶段更新步骤]
- 过渡效果：[更新时的动画效果]

【主内容区域精细化设计】
- 接口调用：create_main_region_content(main_content_group)
- **核心要求：所有主场景元素必须合并为一个main_content_group，统一传递给create_main_region_content**
- **禁止在主内容区域外单独定位元素，模板负责统一管理所有主内容的位置和缩放**

**统一主内容组构建模式：**
```python
# 正确的主内容区域使用方式
data_structure_group = VGroup(table, chart, formula)  # 数据结构组
visualization_group = VGroup(graph, animation_elements)  # 可视化组
operation_group = VGroup(arrows, highlights, indicators)  # 操作组

# 将所有主要Group合并为统一的主内容组
main_content_group = VGroup(
    data_structure_group,
    visualization_group, 
    operation_group
).arrange(DOWN, buff=0.5)  # 或其他适当的排列方式

# 统一传递给模板接口
main_region = self.create_main_region_content(main_content_group)
```

**数据结构定义：**
- [核心数据结构1]Group：Manim [Mobject类型]，[具体描述和包含的元素]
- [核心数据结构2]Group：Manim [Mobject类型]，[具体描述和包含的元素]
- [核心数据结构3]Group：Manim [Mobject类型]，[具体描述和包含的元素]
- **MainContentGroup：包含所有上述Group的统一容器，传递给create_main_region_content**

**主内容区域内部布局规划：**
- **重要：以下布局由MainContentGroup内部的arrange()方法控制，不是屏幕绝对位置**
- 上方区域：[用于放置什么数据结构，如表格、图表等]
- 中心区域：[用于放置核心动画元素，如公式变换、图形演示等]
- 下方区域：[用于放置操作结果、状态展示等]
- 左右区域：[用于放置对比元素、相关数据等]

**阶段化实体演进：**

阶段1：[阶段名称] (时间：X-Y秒)
- **主内容组构建**：
    ```python
    # 数据结构组
    [数据结构Group] = VGroup([具体元素列表])
    # 可视化组  
    [可视化Group] = VGroup([具体元素列表])
    # 操作组
    [操作Group] = VGroup([具体元素列表])
    
    # 统一主内容组
    stage1_main_content = VGroup(
        [数据结构Group],
        [可视化Group], 
        [操作Group]
    ).arrange([排列方式], buff=[缓冲距离])
    
    # 传递给模板
    main_region = self.create_main_region_content(stage1_main_content)
    ```
- 核心动作：
    - `self.play(Create(main_region))`：创建主内容区域
    - `[具体的Transform函数]([源Group] -> [目标Group])`：[动作描述]
    - `[具体的Transform函数]([参数])`：[动作描述]
- 内部布局关系：[MainContentGroup内各子Group的相对位置关系]

阶段2：[阶段名称] (时间：X-Y秒)
- **主内容组更新**：
    ```python
    # 更新或新增的组
    [更新的Group] = VGroup([新的元素列表])
    
    # 重新构建主内容组
    stage2_main_content = VGroup(
        [保留的Group],
        [更新的Group],
        [新增的Group]
    ).arrange([新的排列方式], buff=[缓冲距离])
    
    # 更新主内容区域
    new_main_region = self.create_main_region_content(stage2_main_content)
    ```
- 核心动作：
    - `self.play(ReplacementTransform(main_region, new_main_region))`：更新主内容区域
    - `[具体的Transform函数]([源Group] -> [目标Group])`：[动作描述]
- 内部布局关系：[各Group在MainContentGroup内的新的相对位置关系]

阶段3：[阶段名称] (时间：X-Y秒)
- **主内容组演进**：
    ```python
    # 继续按照统一模式构建主内容组
    stage3_main_content = VGroup([所有相关Group]).arrange([排列方式])
    new_main_region = self.create_main_region_content(stage3_main_content)
    ```
- 核心动作：[继续类似的结构...]
- 内部布局关系：[说明组内关系]

[继续按此模式设计更多阶段...]

【左辅助区域设计】（如果使用）
- 接口调用：create_left_auxiliary_content("[标题]", [项目列表])
- 标题：[6个字以内]
- 项目列表：[最多5项，每项15字以内]
- 主要用途：承载辅助文字说明、关键概念解释等非动画元素
- 动态效果：[项目依次出现、高亮显示等]

【右辅助区域设计】（如果使用）
- 接口调用：create_right_auxiliary_content("[标题]", [项目列表])
- 标题：[6个字以内]
- 项目列表：[最多5项，每项15字以内，支持MathTex]
- 主要用途：承载解释性公式、数学表达式等非动画元素
- 数学动画：[公式展开、变换、高亮等效果]

【结果区域设计】
- 接口调用：create_result_region_content("[结果描述]")
- 内容：[40个字以内的结果总结]
- 更新策略：[如何动态更新结果描述]
- 强调效果：[闪烁、放大、颜色变化等]

=== 详细动画效果设计 ===

【阶段1：开场引入】
时长：[X秒]
```python
# 模板接口调用
title_group = self.create_title_region_content("[具体标题]")
step_group = self.create_step_region_content("[具体步骤]")

# 构建统一主内容组
data_structure_group = VGroup([核心数据结构元素])
visualization_group = VGroup([可视化元素])
operation_group = VGroup([操作指示元素])

# 统一主内容组
stage1_main_content = VGroup(
    data_structure_group,
    visualization_group,
    operation_group
).arrange(DOWN, buff=0.5)  # 根据需要调整排列方式

# 传递给模板 - 核心要求
main_group = self.create_main_region_content(stage1_main_content)

[可选辅助区域]
result_group = self.create_result_region_content("[具体结果]")

# 动画序列设计
self.play(Write(title_group), Write(step_group))  # [时长X秒]
self.play(Create(main_group))  # [时长X秒] - 注意：所有主内容统一创建
[辅助区域动画]
self.play(Write(result_group))  # [时长X秒]
```

动画细节描述：
- 标题出现：[如Write逐字显示/FadeIn淡入/GrowFromCenter放大等]
- 主内容展示：[如何吸引注意力，是否有预告效果]
- 辅助区域：[左右区域是否同时出现，还是有先后顺序]
- 视觉节奏：[快慢搭配，重点强调]

【阶段2：核心展示】
时长：[X秒]
```python
# 步骤更新
new_step = self.create_step_region_content("[新步骤]")
self.play(Transform(step_group, new_step))  # [时长X秒]

# 构建新的统一主内容组
updated_data_group = VGroup([更新的数据结构元素])
enhanced_visualization_group = VGroup([增强的可视化元素])
dynamic_operation_group = VGroup([动态操作元素])

# 重新构建统一主内容组
stage2_main_content = VGroup(
    updated_data_group,
    enhanced_visualization_group,
    dynamic_operation_group
).arrange(DOWN, buff=0.4)  # 可能需要调整布局

# 传递给模板 - 统一更新
new_main_group = self.create_main_region_content(stage2_main_content)
self.play(ReplacementTransform(main_group, new_main_group))  # [时长X秒]

# 更新引用
main_group = new_main_group

# 结果更新
new_result = self.create_result_region_content("[新结果]")
self.play(Transform(result_group, new_result))  # [时长X秒]
```

动画细节描述：
- 内容转换：[Transform的流畅度，是否需要中间过渡]
- 重点突出：[哪些元素需要特殊强调，用什么方式]
- 视觉引导：[观众视线如何从一个区域移到另一个]
- 数据变化：[数值、图形变化的动画表现]

【阶段3：动态演示】
时长：[X秒]
```python
# 具体动画实现
[详细的动画代码示例]
```

动画细节描述：
- 核心动作：[主要演示的动画效果]
- 辅助动作：[配合的小动画和过渡]
- 时机控制：[各个动画的启动时机和持续时间]
- 强调技巧：[如何突出关键时刻]
- 连贯性：[动画之间的衔接和流畅度]

【阶段4：总结回顾】（如果需要）
时长：[X秒]
描述：[如何优雅地结束和总结]

动画设计说明：
- 核心Transform动作总结：[列出3-5个核心动作及其作用]
- Group管理策略：[如何组织和管理各个Group]
- 布局策略：[如何合理利用各区域空间]
- 区域职责分离：[主内容区域专注动画，辅助区域提供解释和公式]
- 视觉连贯性：[如何保持整体动画的连贯性和节奏感]

=== 专业动画技巧应用 ===

【Transform变换技巧】
- Transform：[普通变换的使用场景和效果]
- ReplacementTransform：[完全替换的使用场景]
- TransformFromCopy：[复制变换的创意应用]
- MorphShape：[形状变形的高级效果]

【出现消失技巧】
- Write/Create：[文字和图形的书写效果]
- FadeIn/FadeOut：[淡入淡出的优雅过渡]
- GrowFromCenter/ShrinkToCenter：[缩放效果的应用]
- DrawBorderThenFill：[描边填充的专业效果]

【运动动画技巧】
- 移动路径：[直线、曲线、圆弧运动的设计]
- 速度控制：[快慢变化创造的视觉冲击]
- 旋转缩放：[立体感和动态感的营造]
- 跟随动画：[TracedPath等高级效果]

【强调突出技巧】
- 颜色变化：[高亮、闪烁、渐变等]
- 大小变化：[放大缩小的节奏感]
- 位置移动：[突出显示的位移效果]
- 包围强调：[框选、圈选等标记方式]

【节奏控制技巧】
- wait时间：[停顿的艺术和节奏感]
- run_time：[动画时长的精确控制]
- rate_func：[缓动函数创造的自然感]
- lag_ratio：[序列动画的错位美感]

=== 视觉效果优化 ===

【颜色搭配方案】
- 主色调：[基于模板的专业配色]
- 强调色：[重点内容的颜色选择]
- 对比度：[确保清晰度和可读性]
- 一致性：[整体风格的统一性]

【字体效果设计】
- 层次感：[不同级别内容的字体区分]
- 可读性：[字号大小的合理配置]
- 美观性：[字体选择和效果处理]
- 动画性：[文字动画的创意设计]

【空间布局优化】
- 留白艺术：[合理的空间分配]
- 视觉平衡：[各区域内容的协调]
- 焦点控制：[引导观众注意力]
- 层次清晰：[前景背景的层次感]

【交互体验设计】
- 反馈及时：[动画响应的即时性]
- 过渡自然：[状态切换的流畅性]
- 预期管理：[符合用户直觉的设计]
- 惊喜元素：[适度的创意和亮点]



=== 字数限制检查 ===
- 标题字数：[实际字数] ≤ 8 ✓/✗
- 步骤字数：[实际字数] ≤ 12 ✓/✗
- 辅助标题字数：[实际字数] ≤ 6 ✓/✗
- 辅助项目：[实际项目数] ≤ 5，每项[实际字数] ≤ 15 ✓/✗
- 结果字数：[实际字数] ≤ 40 ✓/✗

=== 模板优势利用 ===
- 自动缩放：[如何利用智能缩放功能]
- 布局灵活：[如何选择最适合的布局策略]
- 视觉一致：[如何保持专业的视觉效果]
- 内容适配：[如何让内容完美适配各个区域]

=== 实现注意事项 ===
- **核心要求**：必须调用 self.setup_background()
- **统一区域管理**：所有主场景VGroup必须统一放在create_main_region_content中，禁止在外部单独定位
- **主内容组构建**：所有数据结构、可视化、操作元素必须合并为一个MainContentGroup传递给模板
- **字数严格控制**：超出限制会影响显示效果
- **布局选择**：根据内容复杂度选择合适的布局
- **动画顺序**：标题→步骤→主内容(统一)→辅助→结果
- **Group管理**：确保相关元素组织成Group，以Group为单位进行操作，最终合并为MainContentGroup
- **区域职责分离**：辅助文字、解释性公式放到左右辅助区域，主内容区域仅保留动画核心元素
- **模板接口规范**：严格使用create_main_region_content(unified_group)模式，不要手动定位主内容元素
- **测试验证**：确保所有内容在区域内完整显示且动画流畅
- **引用更新**：每次更新主内容组时，记得更新main_group引用
</SCENE_TEMPLATE_IMPLEMENTATION>
"""
        
        # 格式化相关插件
        plugins_str = "、".join(relevant_plugins) if relevant_plugins else "无"
        
        # 替换占位符
        prompt = template.format(
            scene_number=scene_number,
            topic=topic,
            description=description,
            scene_outline=scene_outline,
            relevant_plugins=plugins_str
        )
        
        # 添加RAG上下文
        if rag_context:
            prompt += f"\n\n参考信息（用于辅助设计）：\n{rag_context}"
        
        return prompt

    async def generate_from_file(self, outline_file: str, output_file: str = None) -> str:
        """
        从文件生成模板实现描述
        
        Args:
            outline_file: 场景大纲文件路径
            output_file: 输出文件路径（可选）
            
        Returns:
            str: 生成的模板实现描述
        """
        # 读取场景大纲文件
        with open(outline_file, 'r', encoding='utf-8') as f:
            scene_outline = f.read()
        
        # 从文件名推断场景信息
        filename = os.path.basename(outline_file)
        scene_number = 1  # 默认场景1
        topic = "数学定理"  # 默认主题
        
        # 尝试从文件名或内容中提取信息
        if 'scene' in filename.lower():
            try:
                scene_number = int(filename.lower().split('scene')[1].split('_')[0])
            except:
                pass
        
        # 生成模板实现描述
        template_implementation = await self.generate_template_implementation(
            scene_number=scene_number,
            topic=topic,
            description="基于文件内容生成的模板实现描述",
            scene_outline=scene_outline,
            relevant_plugins=[]
        )
        
        # 保存到文件
        if output_file:
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(template_implementation)
            logger.info(f"模板实现描述已保存到: {output_file}")
        
        return template_implementation


async def main():
    """测试主函数"""
    import sys
    
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    
    # 检查命令行参数
    if len(sys.argv) < 2:
        print("使用方法: python professional_template_agent.py <场景大纲文件> [输出文件]")
        print("示例: python professional_template_agent.py scene_outline.txt template_implementation.txt")
        return
    
    outline_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    # 检查文件是否存在
    if not os.path.exists(outline_file):
        print(f"错误: 文件 {outline_file} 不存在")
        return
    
    try:        
        # 创建代理
        from utils.common import AgentFactory, Config
        model = AgentFactory.create_model(Config(config_path="config/config.yaml"))
        agent = ProfessionalTemplateAgent(model=model)
        
        # 生成模板实现描述
        print(f"正在为文件 {outline_file} 生成模板实现描述...")
        template_implementation = await agent.generate_from_file(outline_file, output_file)
        
        # 输出结果
        if not output_file:
            print("\n生成的模板实现描述:")
            print("=" * 50)
            print(template_implementation)
        else:
            print(f"模板实现描述已保存到: {output_file}")
            
    except Exception as e:
        print(f"生成失败: {str(e)}")
        logger.error(f"生成失败: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main()) 