import os
import logging
from typing import Optional, Dict, Any, List

from camel.agents import ChatAgent
from camel.messages import BaseMessage
from camel.models import BaseModelBackend
import yaml

logger = logging.getLogger(__name__)

class AnimationNarrationAgent:
    """动画内容代理，负责生成定理教学视频的内容规划和动画时间安排"""
    
    def __init__(self, model: BaseModelBackend, config_path: str = "config/config.yaml"):
        """
        初始化动画内容代理
        
        Args:
            model: 使用的模型后端
            config_path: 配置文件路径
        """
        self.model = model
        self.config_path = config_path
        
        # 加载配置
        self.load_config(config_path)
        
        # 创建代理
        self.agent = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="Animation Content Expert",
                content="你是一位专业的教育视频内容设计专家，擅长创作清晰、引人入胜的数学教学内容，并规划视觉动画的呈现逻辑。"
            ),
            model=model
        )
    
    def load_config(self, config_path: str):
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
        """
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            # 设置配置属性
            self.agent_config = config.get("agents", {}).get("animation_narration", {})
            self.prompts = self.agent_config.get("prompts", {})
            
            logger.info(f"动画内容代理配置加载成功: {config_path}")
        except Exception as e:
            logger.error(f"加载配置失败: {str(e)}")
            # 设置默认值
            self.agent_config = {}
            self.prompts = {}
    
    async def generate(
        self, 
        topic: str, 
        description: str, 
        scene_number: int,
        scene_outline: str,
        vision_storyboard: str,
        technical_implementation: str,
        relevant_plugins: List[str] = None,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        生成动画内容（与工作流接口兼容）
        
        Args:
            topic: 定理主题
            description: 定理描述
            scene_number: 场景编号
            scene_outline: 场景大纲
            vision_storyboard: 视觉故事板
            technical_implementation: 技术实现计划 
            relevant_plugins: 相关插件列表
            session_id: 会话ID
            
        Returns:
            Dict[str, Any]: 包含生成的动画内容的字典
        """
        logger.info(f"为场景{scene_number}生成动画内容: {topic}")
        
        try:
            if relevant_plugins is None:
                relevant_plugins = []
                
            # 生成动画内容
            animation_narration = await self.generate_animation_narration(
                scene_number=scene_number,
                topic=topic,
                description=description,
                scene_outline=scene_outline,
                vision_storyboard=vision_storyboard,
                technical_implementation=technical_implementation,
                relevant_plugins=relevant_plugins,
                rag_context=None,
                session_id=session_id
            )
            
            return {
                "status": "success",
                "animation_narration": animation_narration
            }
        except Exception as e:
            logger.error(f"生成动画内容失败: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }
    
    async def generate_animation_narration(
        self, 
        scene_number: int,
        topic: str, 
        description: str, 
        scene_outline: str,
        vision_storyboard: str,
        technical_implementation: str,
        relevant_plugins: List[str],
        rag_context: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> str:
        """
        生成动画内容
        
        Args:
            scene_number: 场景编号
            topic: 定理主题
            description: 定理描述
            scene_outline: 场景大纲
            vision_storyboard: 视觉故事板
            technical_implementation: 技术实现计划
            relevant_plugins: 相关插件列表
            rag_context: RAG检索的上下文
            session_id: 会话ID
            
        Returns:
            str: 生成的动画内容
        """
        logger.info(f"生成场景{scene_number}的动画内容: {topic}")
        
        # 构建提示
        prompt = self._build_prompt(
            scene_number=scene_number,
            topic=topic,
            description=description,
            scene_outline=scene_outline,
            vision_storyboard=vision_storyboard,
            technical_implementation=technical_implementation,
            relevant_plugins=relevant_plugins,
            rag_context=rag_context
        )
        
        # 生成响应
        message = BaseMessage.make_user_message(role_name="User", content=prompt)
        response = self.agent.step(message)
        
        # 提取内容
        animation_narration = response.msg.content
        logger.info(f"动画内容生成完成: {len(animation_narration)} 字符")
        
        return animation_narration
    
    def _build_prompt(
        self, 
        scene_number: int,
        topic: str, 
        description: str, 
        scene_outline: str,
        vision_storyboard: str,
        technical_implementation: str,
        relevant_plugins: List[str],
        rag_context: Optional[str] = None
    ) -> str:
        """
        构建动画内容提示
        
        Args:
            scene_number: 场景编号
            topic: 定理主题
            description: 定理描述
            scene_outline: 场景大纲
            vision_storyboard: 视觉故事板
            technical_implementation: 技术实现计划
            relevant_plugins: 相关插件列表
            rag_context: RAG检索的上下文
            
        Returns:
            str: 构建的提示
        """
        # 获取自定义提示模板（如果有）
        template = self.prompts.get("animation_narration_template", "")
        
        if not template:
            # 默认提示模板
            template = """
你是一位专业的教育视频内容设计专家，需要为数学定理教学视频的场景{scene_number}创建一个详细的动画内容计划。

主题：{topic}
描述：{description}
场景大纲：{scene_outline}
视觉故事板：{vision_storyboard}
技术实现计划：{technical_implementation}

请创建一个详细的动画内容计划，专注于视觉呈现和教学逻辑。

你的动画内容计划应该包括：
1. 教学内容的逻辑顺序
2. 每个阶段的重点内容
3. 视觉展示的时间安排
4. 关键概念的强调方式

请以以下格式输出动画内容计划：

<ANIMATION_NARRATION>
场景{scene_number}：[场景名称]

阶段1：[阶段名称]
- 内容要点：[核心教学内容]
- 时间安排：[该阶段持续时间]
- 强调重点：[需要重点突出的概念]
        
阶段2：[阶段名称]
- 内容要点：[核心教学内容]
- 时间安排：[该阶段持续时间]
- 强调重点：[需要重点突出的概念]
        
...

教学说明：
- [节奏控制建议]
- [关键概念展示方法]
- [视觉呈现风格建议]
</ANIMATION_NARRATION>

请确保内容清晰、有逻辑性、易于理解，注重视觉教学效果。
"""
        
        # 格式化相关插件
        plugins_str = "、".join(relevant_plugins) if relevant_plugins else "无"
        
        # 替换占位符
        prompt = template.format(
            scene_number=scene_number,
            topic=topic,
            description=description,
            scene_outline=scene_outline,
            vision_storyboard=vision_storyboard,
            technical_implementation=technical_implementation,
            relevant_plugins=plugins_str
        )
        
        # 添加RAG上下文
        if rag_context:
            prompt += f"\n\n参考资料（用于辅助内容设计）：\n{rag_context}"
        
        return prompt 