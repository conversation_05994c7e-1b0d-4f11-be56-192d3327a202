import os
import sys

sys.path.insert(0, os.getcwd())
import asyncio
import logging
from typing import Any, Optional

import yaml
from camel.agents import ChatAgent
from camel.messages import BaseMessage
from camel.models import BaseModelBackend
from loguru import logger


class VisionStoryboardAgent:
    """视觉故事板代理，负责生成定理教学视频的视觉故事板"""

    def __init__(self, model: BaseModelBackend, config_path: str = "config/config.yaml"):
        """
        初始化视觉故事板代理

        Args:
            model: 使用的模型后端
            config_path: 配置文件路径
        """
        self.model = model
        self.config_path = config_path

        # 加载配置
        self.load_config(config_path)

        # 创建代理
        self.agent = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="Vision Storyboard Expert",
                content="你是一位专业的教育动画视觉设计专家，擅长将抽象的数学概念转化为引人入胜的视觉故事板。",
            ),
            model=model,
        )

    def load_config(self, config_path: str):
        """
        加载配置文件

        Args:
            config_path: 配置文件路径
        """
        try:
            with open(config_path) as f:
                config = yaml.safe_load(f)

            # 设置配置属性
            self.agent_config = config.get("agents", {}).get("vision_storyboard", {})
            self.prompts = self.agent_config.get("prompts", {})

            logger.info(f"视觉故事板代理配置加载成功: {config_path}")
        except Exception as e:
            logger.error(f"加载配置失败: {str(e)}")
            # 设置默认值
            self.agent_config = {}
            self.prompts = {}

    async def generate(
        self,
        topic: str,
        description: str,
        scene_number: int,
        scene_outline: str,
        relevant_plugins: list = None,
        session_id: Optional[str] = None,
    ) -> dict[str, Any]:
        """
        生成视觉故事板（与工作流接口兼容）

        Args:
            topic: 定理主题
            description: 定理描述
            scene_number: 场景编号
            scene_outline: 场景大纲
            relevant_plugins: 相关插件列表
            session_id: 会话ID

        Returns:
            Dict[str, Any]: 包含生成的视觉故事板的字典
        """
        logger.info(f"为场景{scene_number}生成视觉故事板: {topic}")

        try:
            if relevant_plugins is None:
                relevant_plugins = []

            # 生成视觉故事板
            vision_storyboard = await self.generate_vision_storyboard(
                scene_number=scene_number,
                topic=topic,
                description=description,
                scene_outline=scene_outline,
                relevant_plugins=relevant_plugins,
                rag_context=None,
                session_id=session_id,
            )

            return {"status": "success", "vision_storyboard": vision_storyboard}
        except Exception as e:
            logger.error(f"生成视觉故事板失败: {str(e)}")
            return {"status": "error", "message": str(e)}

    async def generate_vision_storyboard(
        self,
        scene_number: int,
        topic: str,
        description: str,
        scene_outline: str,
        relevant_plugins: list,
        rag_context: Optional[str] = None,
        session_id: Optional[str] = None,
    ) -> str:
        """
        生成视觉故事板

        Args:
            scene_number: 场景编号
            topic: 定理主题
            description: 定理描述
            scene_outline: 场景大纲
            relevant_plugins: 相关插件列表
            rag_context: RAG检索的上下文
            session_id: 会话ID

        Returns:
            str: 生成的视觉故事板
        """
        logger.info(f"生成场景{scene_number}的视觉故事板: {topic}")

        # 构建提示
        prompt = self._build_prompt(
            scene_number=scene_number,
            topic=topic,
            description=description,
            scene_outline=scene_outline,
            relevant_plugins=relevant_plugins,
            rag_context=rag_context,
        )

        # 生成响应
        message = BaseMessage.make_user_message(role_name="User", content=prompt)
        response = self.agent.step(message)

        # 提取内容
        vision_storyboard = response.msg.content
        logger.info(f"视觉故事板生成完成: {len(vision_storyboard)} 字符")

        return vision_storyboard

    def _build_prompt(
        self,
        scene_number: int,
        topic: str,
        description: str,
        scene_outline: str,
        relevant_plugins: list,
        rag_context: Optional[str] = None,
    ) -> str:
        """
        构建视觉故事板提示

        Args:
            scene_number: 场景编号
            topic: 定理主题
            description: 定理描述
            scene_outline: 场景大纲
            relevant_plugins: 相关插件列表
            rag_context: RAG检索的上下文

        Returns:
            str: 构建的提示
        """
        # 获取自定义提示模板（如果有）
        template = self.prompts.get("vision_storyboard_template", "")

        if not template:
            # 默认提示模板
            template = """
你是一位专业的教育动画视觉设计专家，需要为数学定理教学视频的场景{scene_number}创建一个详细的视觉故事板。

主题：{topic}
描述：{description}
场景大纲：{scene_outline}

【核心设计原则】

你必须使用ProfessionalScienceTemplate提供的标准接口方法：

**1. 区域内容创建接口（严格字数限制）**
- `create_title_region_content(title)`: 标题区域，限制8个字以内
- `create_step_region_content(step)`: 步骤区域，限制12个字以内
- `create_main_region_content(content)`: 主内容区域，自动适配6.0×3.5空间
- `create_left_auxiliary_content(title, items)`: 左辅助区域，标题6字内，最多5项×15字/项
- `create_right_auxiliary_content(title, items)`: 右辅助区域，标题6字内，最多5项×15字/项
- `create_result_region_content(result)`: 结果区域，限制40个字以内

**2. Group组织原则**
- 相关元素必须组织成Group：
  - 数学公式及其组成部分 → FormulaGroup
  - 表格及其行列 → TableGroup
  - 图形及其标注 → FigureGroup
  - 操作步骤及其说明 → StepGroup
- Group必须精简，避免冗余元素
  - 非必要不添加,每个新出现的元素必须服务于当前核心概念的理解
  - 添加即精简,所有元素都应尽可能简化表达形式,
  - 避免元素数量过多的情况，单行不超过4个元素，不超过4行
  - 表格元素也需要保证不超过4行，不超过4列，做精简展示
- 标题和步骤介绍分别设计一个Group，通过Transform进行文字内容切换；其他区域的Group需要按具体动画内容创建，描述清楚其包含的元素
- 主内容区域的动画适当展开，强调关键动作；其他区域所有操作尽量以Group为单位进行，避免对单个元素做细粒度控制
- 同类Group之间保持一致的视觉风格和动画节奏

**3. 数据结构优先原则**
- 优先考虑核心数据结构的可视化：表格、图表、公式、几何图形等
- 数据结构的变化应作为动画的核心驱动力
- 避免装饰性元素，专注于信息传达，特别是在背景介绍中，避免生成机器人、书籍等纯装饰性元素
- 所有数据结构必须组织成VGroup，统一传递给主内容区域，同一个区域只能同时展示1个VGroup

**4. 核心动作原则**
- 优先使用Manim的Transform系列函数：
  - `ReplacementTransform`: 用于内容替换和状态转换
  - `TransformFromCopy`: 用于复制和衍生操作
  - `FadeTransform`: 用于淡入淡出过渡
  - `MorphShape`: 用于形状变化
- 核心动作识别：
  - 变换(Transform): 数据/公式/图形的状态改变
  - 组合(Combine): 多个元素合并成一个
  - 分解(Decompose): 一个元素分解成多个
  - 映射(Map): 元素间的对应关系
  - 突出(Highlight): 关键信息的强调
- 减少琐碎动画：只保留与核心概念直接相关的动画

**您的视觉故事板必须包含以下结构：**

</SCENE_VISION_STORYBOARD>
场景{scene_number}：[场景名称]

Group定义：
- [Group名称1]：[Group组织方式及其包含的元素]
- [Group名称2]：[Group组织方式及其包含的元素]
- [...]

阶段1：[阶段名称]
- 区域占位情况：[各区域Group的占位情况，用于检查是否存在占位冲突，若有冲突则及时退场]
    - 标题区域：create_title_region_content(Group)
    - 步骤介绍区域：create_step_region_content(Group)
    - 主内容区域：create_main_region_content(Group)
    - 左辅助区域：create_left_auxiliary_content(Group)
    - 右辅助区域：create_right_auxiliary_content(Group)
    - 结果区域：create_result_region_content(Group)
- 核心动作：[按时间顺序描述核心动作]
    - 动作1:[动作描述]
    - 动作2:[动作描述]
    - [...]


阶段2：[阶段名称]
...

...

注意事项：
- 元素精简策略：[元素是否足够精简？是否满足字数限制？是否满足宽度限制？]
- 区域占位规避策略：[所有区域是否存在占位冲突？是否合理规划Group占位？]
</SCENE_VISION_STORYBOARD>
"""

        # 格式化相关插件
        plugins_str = "、".join(relevant_plugins) if relevant_plugins else "无"

        # 替换占位符
        prompt = template.format(
            scene_number=scene_number,
            topic=topic,
            description=description,
            scene_outline=scene_outline,
            relevant_plugins=plugins_str,
        )

        # 添加RAG上下文
        if rag_context:
            prompt += f"\n\n参考信息（用于辅助设计）：\n{rag_context}"

        return prompt

    async def generate_from_file(self, outline_file: str, output_file: str = None) -> str:
        """
        从文件生成视觉故事板

        Args:
            outline_file: 场景大纲文件路径
            output_file: 输出文件路径（可选）

        Returns:
            str: 生成的视觉故事板
        """
        # 读取场景大纲文件
        with open(outline_file, encoding="utf-8") as f:
            scene_outline = f.read()

        # 从文件名推断场景信息
        filename = os.path.basename(outline_file)
        scene_number = 1  # 默认场景1
        topic = "数学定理"  # 默认主题

        # 尝试从文件名或内容中提取信息
        if "scene" in filename.lower():
            try:
                scene_number = int(filename.lower().split("scene")[1].split("_")[0])
            except Exception:
                pass

        # 生成视觉故事板
        vision_storyboard = await self.generate_vision_storyboard(
            scene_number=scene_number,
            topic=topic,
            description="基于文件内容生成的视觉故事板",
            scene_outline=scene_outline,
            relevant_plugins=[],
        )

        # 保存到文件
        if output_file:
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(vision_storyboard)
            logger.info(f"视觉故事板已保存到: {output_file}")

        return vision_storyboard


async def main():
    """测试主函数"""
    import sys

    # 配置日志
    logging.basicConfig(level=logging.INFO)

    # 检查命令行参数
    if len(sys.argv) < 2:
        print("使用方法: python vision_storyboard_agent.py <场景大纲文件> [输出文件]")
        print("示例: python vision_storyboard_agent.py scene_outline.txt vision_storyboard.txt")
        return

    outline_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None

    # 检查文件是否存在
    if not os.path.exists(outline_file):
        print(f"错误: 文件 {outline_file} 不存在")
        return

    try:
        # 创建代理
        from utils.common import AgentFactory, Config

        model = AgentFactory.create_model(Config(config_path="config/config.yaml"))
        agent = VisionStoryboardAgent(model=model)

        # 生成视觉故事板
        print(f"正在为文件 {outline_file} 生成视觉故事板...")
        vision_storyboard = await agent.generate_from_file(outline_file, output_file)

        # 输出结果
        if not output_file:
            print("\n生成的视觉故事板:")
            print("=" * 50)
            print(vision_storyboard)
        else:
            print(f"视觉故事板已保存到: {output_file}")

    except Exception as e:
        print(f"生成失败: {str(e)}")
        logger.error(f"生成失败: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
