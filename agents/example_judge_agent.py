#!/usr/bin/env python3
"""
例子评估代理 - 评估例子生成的好坏并给出修改建议
"""

import datetime
import os
import sys
from pathlib import Path
from typing import Any, Dict, Optional

import yaml
from loguru import logger

# 添加父目录到导入路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from camel.agents import ChatAgent
from camel.messages import BaseMessage
from camel.types import RoleType
from utils.create_llm_model import create_model


class ExampleJudgeAgent:
    """例子评估代理类"""

    def __init__(self, config_path: str = "config/config.yaml"):
        """初始化评估代理"""
        self.config_path = config_path
        self.config = self._load_config(config_path)
        
        # 创建专用的评估模型
        self.judge_model = self._create_judge_model()
        
        self.results_dir = Path("output/example_evaluations")
        self.results_dir.mkdir(parents=True, exist_ok=True)

        # 设置日志
        logger.add("logs/example_judge.log", rotation="1 day", retention="30 days", level="INFO")

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, encoding="utf-8") as f:
                config = yaml.safe_load(f)
            logger.info(f"从 {config_path} 加载配置")
            return config
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            raise

    def _create_judge_model(self):
        """创建评估专用模型"""
        try:
            # 检查是否有专用的评估模型配置
            if "example_judge_model" in self.config and "type" in self.config["example_judge_model"]:
                # 创建临时配置，只修改模型类型
                judge_config = self.config.copy()
                judge_config["model"] = self.config["model"].copy()
                judge_config["model"]["type"] = self.config["example_judge_model"]["type"]
                
                # 创建临时配置文件
                import tempfile
                with tempfile.NamedTemporaryFile(mode="w", suffix=".yaml", delete=False) as f:
                    yaml.dump(judge_config, f)
                    temp_config_path = f.name
                
                try:
                    model = create_model(temp_config_path)
                    logger.info(f"已创建专用评估模型: {self.config['example_judge_model']['type']}")
                    return model
                finally:
                    # 清理临时文件
                    try:
                        os.unlink(temp_config_path)
                    except Exception:
                        pass
            else:
                # 使用默认模型
                model = create_model(self.config_path)
                logger.info("使用默认模型进行评估")
                return model
                
        except Exception as e:
            logger.error(f"创建评估模型失败: {e}")
            # 降级使用默认模型
            return create_model(self.config_path)

    def _get_default_file_path(self) -> str:
        """从配置文件中获取默认的例子文件路径"""
        default_topic, _ = self._get_default_values()
        # 将主题中的空格替换为下划线，以构造有效的文件路径
        topic_for_path = default_topic.replace(" ", "_").replace("：", "_").replace(":", "_")
        file_path = f"output/{topic_for_path}/example_explain.md"
        logger.info(f"构造默认文件路径: {file_path}")
        return file_path

    def _read_example_file(self, file_path: str) -> str:
        """读取例子文件内容"""
        try:
            with open(file_path, encoding="utf-8") as f:
                content = f.read()
            logger.info(f"成功读取例子文件: {file_path}")
            return content
        except FileNotFoundError:
            error_msg = f"文件未找到: {file_path}"
            logger.error(error_msg)
            raise
        except Exception as e:
            error_msg = f"读取文件失败: {e}"
            logger.error(error_msg)
            raise

    def _get_default_values(self) -> tuple[str, str]:
        """从配置文件中获取默认的主题和目的"""
        topic = "未知主题"
        purpose = "教学演示"
        
        if "example_explain" in self.config:
            config_section = self.config["example_explain"]
            topic = config_section.get("topic", topic)
            purpose = config_section.get("purpose", purpose)
        
        logger.info(f"默认主题: {topic}, 默认目的: {purpose}")
        return topic, purpose

    def _extract_topic_and_purpose(self, content: str) -> tuple[str, str]:
        """从内容中提取主题和目的"""
        # 尝试从内容中提取主题信息
        lines = content.split('\n')
        topic = None
        
        for line in lines[:20]:  # 只查看前20行
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['场景', 'scene', '主题', 'topic', '标题']):
                # 提取可能的主题信息
                if '：' in line:
                    potential_topic = line.split('：', 1)[1].strip()
                    if potential_topic and len(potential_topic) < 100:
                        topic = potential_topic
                elif ':' in line:
                    potential_topic = line.split(':', 1)[1].strip()
                    if potential_topic and len(potential_topic) < 100:
                        topic = potential_topic
                break
        
        # 如果没有提取到主题，使用配置文件的默认值
        default_topic, default_purpose = self._get_default_values()
        topic = topic or default_topic
        purpose = default_purpose  # 目的总是使用配置文件的值
        
        logger.info(f"提取的主题: {topic}, 目的: {purpose}")
        return topic, purpose

    def evaluate_example(self, file_path: str, topic: str = None, purpose: str = None) -> str:
        """评估例子文件
        
        Args:
            file_path: 例子文件路径
            topic: 主题（可选，如果不提供会从配置文件的 example_explain 部分读取）
            purpose: 目的（可选，如果不提供会从配置文件的 example_explain 部分读取）
            
        Returns:
            评估结果（Markdown格式）
        """
        logger.info(f"开始评估例子文件: {file_path}")
        
        try:
            # 读取例子内容
            example_content = self._read_example_file(file_path)
            
            # 获取配置文件中的默认值
            default_topic, default_purpose = self._get_default_values()
            
            # 如果用户没有提供参数，则使用配置文件中的默认值
            topic = topic or default_topic
            purpose = purpose or default_purpose
            
            logger.info(f"使用的主题: {topic}")
            logger.info(f"使用的目的: {purpose}")
            
            # 生成评估
            evaluation_result = self._analyze_example(topic, purpose, example_content)
            
            logger.info("例子评估完成")
            return evaluation_result
            
        except Exception as e:
            logger.error(f"评估例子失败: {e}")
            return f"❌ **评估失败**: {str(e)}"

    def _analyze_example(self, topic: str, purpose: str, example_content: str) -> str:
        """分析评估例子内容"""
        
        evaluation_prompt = f"""
你是一位资深的教学设计评估专家，请对以下教学例子进行全面深入的评估分析。

评估主题：{topic}
评估目的：{purpose}

## 待评估例子：
{example_content}

请从以下维度进行详细评估分析：

### 1. 例子具体性与数据完整性（35分）
- **数据具体性**（15分）：每个步骤是否包含详细的具体数据（如具体数值、矩阵、向量必须包含具体数字），关键数据是否省略？
- **步骤详实性**（10分）：每个步骤的操作是否具体明确，有没有遗漏关键操作？
- **量化表达**（10分）：结果是否量化，有具体的测量指标或数值？

### 2. 动画描述质量与连贯性（35分）
- **关键变化描述**（15分）：关键元素的位置、颜色、大小、数量等变化是否详细描述？是否缺少关键变化元素？
- **Manim适配性**（10分）：动画描述是否适合Manim生成，元素变化是否可编程实现？
- **整体连贯性**（10分）：动画步骤之间是否连贯，有无跳跃过大的变化？

### 3. 讲解完整性与本质揭示（30分）
- **概念本质描述**（15分）：是否直接了当地揭示概念本质，避免绕弯子？
- **讲解完整性**（10分）：是否完整覆盖核心概念，是否讲清晰了，不要只描述具体操作，没表述为什么这么做？
- **原理阐述深度**（5分）：是否深入解释为什么这样做，机制是否清楚？

### 评估要求：
1. **客观公正**: 基于具体内容进行评估，避免主观偏见
2. **详细分析**: 每个维度都要给出具体的分析说明和评分理由
3. **建设性建议**: 针对发现的问题提出可操作的改进建议
4. **实用导向**: 建议要考虑Manim动画制作的实际需求

请按以下Markdown格式输出评估结果：
```markdown
# 📊 例子质量评估报告
## 📋 基本信息
- **主题**: {topic}
- **评估目的**: {purpose}

## 🏆 总体评分
**总分**: XX/100 分  

## 📈 详细分析
### 1. 例子具体性与数据完整性 (XX/35分)
#### 详细分析
[优点整体一段描述]
[待改进的第1条]
[待改进的第2条]
...

### 2. 动画描述质量与连贯性 (XX/35分)

#### 详细分析
[优点整体一段描述]
[待改进的第1条]
[待改进的第2条]
...

### 3. 讲解完整性与本质揭示 (XX/30分)
#### 详细分析
[优点整体一段描述]
[待改进的第1条]
[待改进的第2条]
...
```

"""

        try:
            # 创建评估agent
            system_message = BaseMessage(
                role_name="教学评估专家",
                role_type=RoleType.ASSISTANT,
                meta_dict=None,
                content="你是一位资深的教学设计评估专家，擅长客观公正地评估教学内容的质量，并提供建设性的改进建议。请按照Markdown格式输出评估结果。"
            )

            agent = ChatAgent(
                system_message=system_message, 
                model=self.judge_model, 
                message_window_size=10
            )

            user_message = BaseMessage(
                role_name="用户", 
                role_type=RoleType.USER, 
                meta_dict=None, 
                content=evaluation_prompt
            )

            response = agent.step(user_message)
            return response.msg.content

        except Exception as e:
            logger.error(f"分析例子失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return f"❌ **评估失败**: {str(e)}"

    def print_evaluation_result(self, result: str):
        """打印评估结果"""
        print(result)

    def save_evaluation_result(self, result: str, file_path: str):
        """保存评估结果到文件"""
        try:
            # 生成结果文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            file_name = Path(file_path).stem
            result_file = self.results_dir / f"evaluation_{file_name}_{timestamp}.md"
            
            # 保存评估结果
            with open(result_file, "w", encoding="utf-8") as f:
                f.write(result)

            logger.info(f"评估结果已保存到: {result_file}")
            return str(result_file)

        except Exception as e:
            logger.error(f"保存评估结果失败: {e}")
            return None


def main():
    """主函数 - 命令行接口"""
    import argparse
    from loguru import logger as main_logger

    parser = argparse.ArgumentParser(description="例子评估工具")
    parser.add_argument("file_path", nargs='?', help="要评估的例子文件路径（可选，默认使用 output/{topic}/example_explain.md）")
    parser.add_argument("--topic", help="主题（可选，默认从配置文件读取）")
    parser.add_argument("--purpose", help="目的（可选，默认从配置文件读取）")
    parser.add_argument("--save", default=True, action="store_true", help="保存评估结果到文件")
    parser.add_argument("--config", default="config/config.yaml", help="配置文件路径")

    args = parser.parse_args()

    judge = ExampleJudgeAgent(config_path=args.config)

    # 如果没有提供文件路径，使用默认路径
    file_path = args.file_path or judge._get_default_file_path()
    
    main_logger.info(f"将评估文件: {file_path}")

    # 执行评估
    result = judge.evaluate_example(file_path, args.topic, args.purpose)
    
    # 打印结果
    #judge.print_evaluation_result(result)
    
    # 可选保存到文件
    if args.save:
        saved_file = judge.save_evaluation_result(result, file_path)
        if saved_file:
            print(f"\n💾 评估结果已保存到: {saved_file}")


if __name__ == "__main__":
    main() 