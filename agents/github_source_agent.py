import json
import logging
import math
import os
import shutil
import subprocess
import traceback
import re
import sys
import time
from datetime import datetime, timedelta  # 添加timedelta导入
from pathlib import Path

import requests
from dotenv import load_dotenv

load_dotenv()
import pytz
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt  # 添加matplotlib.pyplot导入
import matplotlib.dates as mpl   # 添加matplotlib.dates作为mpl导入，解决mpl未定义问题
from dateutil.relativedelta import relativedelta

import yaml
from camel.agents import ChatAgent
from camel.logger import set_log_level
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.types import ModelPlatformType
from loguru import logger

# 自定义JSON编码器，处理NumPy和Pandas数据类型
class NumpyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, pd.Series):
            return obj.tolist()
        elif isinstance(obj, pd.DataFrame):
            return obj.to_dict(orient="records")
        elif isinstance(obj, pd.Timestamp):
            return obj.isoformat()
        return super(NumpyEncoder, self).default(obj)

# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans', 'Bitstream Vera Sans', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 12
plt.style.use('ggplot')

from agents.video_agent.tools.info_collector_toolkit import InfoCollectorToolkit
from agents.video_agent.tools.content_manager_toolkit import ContentManagerToolkit

set_log_level(level="WARNING")

# 必要的目录结构
os.makedirs("config", exist_ok=True)
os.makedirs("output", exist_ok=True)

# 星标历史估算相关函数
def parse_github_url(github_url):
    """
    解析GitHub URL，提取所有者和仓库名
    
    参数:
    github_url (str): GitHub仓库URL
    
    返回:
    tuple: (owner, repo)
    """
    pattern = r"github\.com/([^/]+)/([^/]+)"
    match = re.search(pattern, github_url)
    
    if match:
        owner = match.group(1)
        repo = match.group(2)
        # 移除可能的.git后缀
        repo = repo.replace(".git", "")
        return owner, repo
    else:
        raise ValueError("无效的GitHub URL")

def estimate_stars_history(owner, repo, days=365, max_samples=50):
    """
    通过抽样估算仓库在指定时间范围内的 Star 历史
    
    参数:
    owner (str): 仓库所有者
    repo (str): 仓库名称
    days (int): 要查询的天数，默认为365（一年）
    max_samples (int): 最大采样点数，用于控制API请求数量
    
    返回:
    pd.DataFrame: 估算的 Star 历史
    """
    # GitHub API 认证
    github_token = os.getenv("GITHUB_TOKEN")
    headers = {
        "Accept": "application/vnd.github.v3+json",
        "Authorization": f"token {github_token}"
    }
    
    # 首先获取仓库信息
    url = f"https://api.github.com/repos/{owner}/{repo}"
    response = requests.get(url, headers=headers)
    response.raise_for_status()
    
    repo_data = response.json()
    total_stars = repo_data['stargazers_count']
    # 确保时间为UTC时区的aware datetime
    created_at = pd.to_datetime(repo_data['created_at']).tz_convert('UTC')
    
    logger.info(f"仓库 {owner}/{repo} 有 {total_stars:,} 颗星")
    logger.info(f"创建于 {created_at.strftime('%Y-%m-%d')}")
    
    if total_stars == 0:
        logger.info("该仓库没有星标")
        return None
    
    # 确定时间范围，使用UTC时区
    end_date = datetime.now(pytz.UTC)
    start_date = end_date - timedelta(days=days)
    
    # 如果项目创建时间晚于开始时间，则以创建时间为准
    if created_at > start_date:
        start_date = created_at
        logger.info(f"项目创建时间晚于指定的开始时间，将使用项目创建时间 {start_date.strftime('%Y-%m-%d')} 作为开始时间")
    else:
        logger.info(f"将查询从 {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')} 的Star历史")
    
    # 计算时间跨度
    time_span = (end_date - start_date).days
    
    # 根据时间跨度自动确定采样频率
    # 如果时间跨度大于max_samples天，则减少采样频率
    if time_span > max_samples:
        # 计算适当的采样频率
        freq = max(int(time_span / max_samples), 1)
        if freq >= 7:
            # 如果频率超过7天，改用周粒度
            sample_freq = 'W'
            logger.info(f"由于时间跨度较大 ({time_span} 天)，将使用周粒度采样")
        else:
            # 否则使用天粒度但减少采样点
            sample_freq = f"{freq}D"
            logger.info(f"由于时间跨度较大 ({time_span} 天)，将每 {freq} 天采样一次")
    else:
        # 时间跨度小，使用天粒度
        sample_freq = 'D'
        logger.info("使用天粒度采样")
    
    # 生成采样日期
    date_range = pd.date_range(start=start_date, end=end_date, freq=sample_freq, tz='UTC')
    logger.info(f"将采样 {len(date_range)} 个时间点")
    
    # 获取指定时间范围内的星标数据
    stars_data = []
    headers_with_star = {
        "Accept": "application/vnd.github.v3.star+json",
        "Authorization": f"token {github_token}"
    }
    
    # 使用二分法估算每个时间点的星标数
    # 首先获取总星标数以及直接信息
    all_stars_info = get_repo_star_info(owner, repo, headers_with_star)
    if not all_stars_info:
        logger.warning("无法获取仓库星标信息")
        return None
    
    # 使用更高效的方法预估每个采样点的星标数
    for sample_date in date_range:
        try:
            star_count = estimate_stars_at_date(sample_date, all_stars_info)
            stars_data.append({
                'date': sample_date,
                'stars': star_count
            })
            logger.debug(f"{sample_date.strftime('%Y-%m-%d')}: 估计有 {star_count} 颗星")
        except Exception as e:
            logger.error(f"处理日期 {sample_date.strftime('%Y-%m-%d')} 时出错: {str(e)}")
    
    if not stars_data:
        logger.warning("未能获取到有效数据")
        return None
    
    # 将数据转换为 DataFrame
    stars_df = pd.DataFrame(stars_data)
    stars_df = stars_df.sort_values('date')
    
    return stars_df

def get_repo_star_info(owner, repo, headers):
    """
    获取仓库的星标信息，包括总页数和关键采样点
    
    返回:
    dict: 包含星标信息的字典
    """
    try:
        # 获取仓库总星标数
        repo_url = f"https://api.github.com/repos/{owner}/{repo}"
        response = requests.get(repo_url, headers=headers)
        response.raise_for_status()
        repo_data = response.json()
        total_stars = repo_data['stargazers_count']
        
        # 获取分页信息
        url = f"https://api.github.com/repos/{owner}/{repo}/stargazers?per_page=100"
        response = requests.get(url, headers=headers)
        
        if response.status_code == 403 and 'API rate limit exceeded' in response.text:
            reset_time = int(response.headers.get('X-RateLimit-Reset', 0))
            current_time = int(time.time())
            sleep_time = max(reset_time - current_time + 1, 0)
            logger.warning(f"已达到 API 速率限制，等待 {sleep_time} 秒后继续...")
            time.sleep(sleep_time)
            response = requests.get(url, headers=headers)
        
        response.raise_for_status()
        
        # 获取总页数
        last_page = 1
        if 'Link' in response.headers:
            links = response.headers['Link']
            last_match = re.search(r'page=(\d+)>; rel="last"', links)
            if last_match:
                last_page = int(last_match.group(1))
        
        # 采样关键页面以获取时间分布
        key_pages = []
        if last_page > 1:
            # 获取第一页数据
            first_page_data = response.json()
            if first_page_data:
                first_star = pd.to_datetime(first_page_data[0]['starred_at']).tz_convert('UTC')
                key_pages.append({
                    'page': 1,
                    'date': first_star,
                    'stars': 1
                })
            
            # 获取最后一页数据
            last_url = f"https://api.github.com/repos/{owner}/{repo}/stargazers?per_page=100&page={last_page}"
            last_response = requests.get(last_url, headers=headers)
            
            if last_response.status_code == 200:
                last_page_data = last_response.json()
                if last_page_data:
                    last_star = pd.to_datetime(last_page_data[-1]['starred_at']).tz_convert('UTC')
                    key_pages.append({
                        'page': last_page,
                        'date': last_star,
                        'stars': total_stars
                    })
            
            # 如果页数多，采样中间页面
            if last_page > 5:
                middle_pages = [int(last_page * p / 4) for p in range(1, 4)]
                for page in middle_pages:
                    if page > 1 and page < last_page:
                        mid_url = f"https://api.github.com/repos/{owner}/{repo}/stargazers?per_page=100&page={page}"
                        mid_response = requests.get(mid_url, headers=headers)
                        
                        if mid_response.status_code == 200:
                            mid_page_data = mid_response.json()
                            if mid_page_data:
                                mid_star = pd.to_datetime(mid_page_data[-1]['starred_at']).tz_convert('UTC')
                                key_pages.append({
                                    'page': page,
                                    'date': mid_star,
                                    'stars': page * 100
                                })
                        
                        # 避免触发API限制
                        time.sleep(1)
        
        # 确保关键页面按星标数排序
        key_pages.sort(key=lambda x: x['stars'])
        
        return {
            'total_stars': total_stars,
            'last_page': last_page,
            'key_points': key_pages
        }
    
    except Exception as e:
        logger.error(f"获取仓库星标信息失败: {str(e)}")
        return None

def estimate_stars_at_date(target_date, star_info):
    """
    基于关键采样点估算特定日期的星标数
    
    参数:
    target_date: 目标日期
    star_info: 仓库星标信息
    
    返回:
    int: 估计的星标数
    """
    key_points = star_info['key_points']
    
    # 如果目标日期在所有关键点之后，返回总星标数
    if len(key_points) > 0 and target_date >= key_points[-1]['date']:
        return star_info['total_stars']
    
    # 如果目标日期在所有关键点之前，返回0
    if len(key_points) > 0 and target_date < key_points[0]['date']:
        return 0
    
    # 在关键点之间进行线性插值
    for i in range(len(key_points) - 1):
        point1 = key_points[i]
        point2 = key_points[i + 1]
        
        if point1['date'] <= target_date <= point2['date']:
            # 计算日期差
            date_diff = (point2['date'] - point1['date']).total_seconds()
            target_diff = (target_date - point1['date']).total_seconds()
            
            # 计算插值比例
            ratio = target_diff / date_diff if date_diff > 0 else 0
            
            # 线性插值计算星标数
            estimated_stars = int(point1['stars'] + ratio * (point2['stars'] - point1['stars']))
            return estimated_stars
    
    # 如果没有找到合适的区间，返回最接近的关键点的星标数
    closest_point = min(key_points, key=lambda x: abs((target_date - x['date']).total_seconds()))
    return closest_point['stars']

def create_beautiful_chart(owner, repo, df, freq_type, output_path):
    """
    创建美观的星标历史图表
    
    参数:
    owner (str): 仓库所有者
    repo (str): 仓库名称
    df (DataFrame): 包含日期和星标数的数据
    freq_type (str): 频率类型（天或周）
    output_path (str): 输出文件路径
    """
    # 创建漂亮的图表
    plt.figure(figsize=(12, 6), dpi=100)
    
    # 设置背景色和网格样式
    ax = plt.gca()
    ax.set_facecolor('#f5f5f5')
    
    # 绘制主曲线
    plt.plot(df['date'], df['stars'], 
             marker='', 
             linewidth=2.5, 
             color='#2196F3',
             alpha=0.9)
    
    # 添加均值辅助线
    avg_stars = df['stars'].mean()
    plt.axhline(y=avg_stars, color='#FF5722', linestyle='--', alpha=0.5, 
                label=f'平均: {int(avg_stars):,} 星')
    
    # 添加起点和终点标记
    plt.scatter(df['date'].iloc[0], df['stars'].iloc[0], color='#4CAF50', s=100, zorder=5)
    plt.scatter(df['date'].iloc[-1], df['stars'].iloc[-1], color='#F44336', s=100, zorder=5)
    
    # 设置网格
    plt.grid(True, linestyle='--', alpha=0.7, color='#cccccc')
    
    # 设置标题和标签
    # 避免乱码，使用简单的ASCII字符
    repo_full_name = f"{owner}/{repo}"
    freq_suffix = "Weekly" if freq_type == "周" else "Daily"
    plt.title(f"GitHub Stars History: {repo_full_name} ({freq_suffix})", 
              fontsize=18, fontweight='bold', pad=20)
    
    plt.xlabel('Date', fontsize=14, labelpad=10)
    plt.ylabel('Stars Count', fontsize=14, labelpad=10)
    
    # 格式化 y 轴，使用千分位分隔符
    plt.gca().get_yaxis().set_major_formatter(
        plt.FuncFormatter(lambda x, loc: f"{int(x):,}")
    )
    
    # 优化x轴日期标签
    date_range = (df['date'].max() - df['date'].min()).days
    if date_range > 365 * 2:
        # 大于2年显示年份
        plt.gca().xaxis.set_major_locator(mpl.YearLocator())
        plt.gca().xaxis.set_major_formatter(mpl.DateFormatter('%Y'))
    elif date_range > 180:
        # 大于6个月显示月份
        plt.gca().xaxis.set_major_locator(mpl.MonthLocator(interval=2))
        plt.gca().xaxis.set_major_formatter(mpl.DateFormatter('%Y-%m'))
    else:
        # 小于6个月显示具体日期
        plt.gca().xaxis.set_major_locator(mpl.MonthLocator())
        plt.gca().xaxis.set_major_formatter(mpl.DateFormatter('%Y-%m-%d'))
    
    plt.xticks(rotation=45)
    
    # 添加数据点标注（只标注部分关键点）
    num_points = len(df)
    if num_points > 20:
        # 如果点太多，只标注一部分
        step = max(1, num_points // 10)
        for i in range(0, num_points, step):
            date = df['date'].iloc[i]
            stars = df['stars'].iloc[i]
            if i > 0 and i < num_points - 1:  # 跳过第一个和最后一个点
                plt.annotate(f"{stars:,}",
                            xy=(date, stars),
                            xytext=(0, 10),
                            textcoords='offset points',
                            ha='center', va='bottom',
                            fontsize=9, alpha=0.8)
    else:
        # 点较少，全部标注
        for i in range(num_points):
            date = df['date'].iloc[i]
            stars = df['stars'].iloc[i]
            plt.annotate(f"{stars:,}",
                        xy=(date, stars),
                        xytext=(0, 10),
                        textcoords='offset points',
                        ha='center', va='bottom',
                        fontsize=9)
    
    # 添加最终星标数注释
    final_date = df['date'].iloc[-1]
    final_stars = df['stars'].iloc[-1]
    plt.annotate(f"Latest: {final_stars:,} stars",
                xy=(final_date, final_stars),
                xytext=(10, 0),
                textcoords='offset points',
                ha='left', va='center',
                fontsize=11, fontweight='bold',
                bbox=dict(boxstyle="round,pad=0.3", fc="#f0f0f0", ec="gray", alpha=0.8))
    
    # 添加图例
    plt.legend(loc='upper left', frameon=True, framealpha=0.9)
    
    # 添加数据来源注释
    plt.figtext(0.99, 0.01, f"Generated at {datetime.now().strftime('%Y-%m-%d')}",
               ha='right', va='bottom', fontsize=8, alpha=0.7)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    logger.info(f"美化图表已保存到 {output_path}")
    return output_path

def calculate_stars_growth_metrics(stars_df):
    """
    计算星标增长相关指标
    
    参数:
    stars_df (DataFrame): 包含日期和星标数的数据帧
    
    返回:
    dict: 包含增长指标的字典
    """
    if stars_df is None or len(stars_df) < 2:
        return {
            "growth_rate": "数据不足，无法计算",
            "avg_stars_per_month": "数据不足，无法计算",
            "avg_stars_per_week": "数据不足，无法计算",
            "total_growth": "数据不足，无法计算",
            "growth_trend": "数据不足，无法计算"
        }
    
    # 确保数据按日期排序，并确保日期列是日期时间类型
    stars_df = stars_df.copy()
    if 'date' in stars_df.columns:
        # 转换日期列为datetime类型
        try:
            stars_df['date'] = pd.to_datetime(stars_df['date'])
        except Exception as e:
            print(f"日期转换错误: {str(e)}")
            return {
                "growth_rate": "日期格式错误，无法计算",
                "avg_stars_per_month": "日期格式错误，无法计算",
                "avg_stars_per_week": "日期格式错误，无法计算",
                "total_growth": "日期格式错误，无法计算",
                "growth_trend": "日期格式错误，无法计算"
            }
    else:
        print("数据框中没有'date'列")
        return {
            "growth_rate": "数据格式错误，无法计算",
            "avg_stars_per_month": "数据格式错误，无法计算",
            "avg_stars_per_week": "数据格式错误，无法计算",
            "total_growth": "数据格式错误，无法计算",
            "growth_trend": "数据格式错误，无法计算"
        }
    
    stars_df = stars_df.sort_values('date')
    
    # 计算总增长
    initial_stars = stars_df['stars'].iloc[0]
    latest_stars = stars_df['stars'].iloc[-1]
    total_growth = latest_stars - initial_stars
    
    # 计算增长率
    start_date = stars_df['date'].iloc[0]
    end_date = stars_df['date'].iloc[-1]
    
    # 确保start_date和end_date是datetime对象
    if not isinstance(start_date, pd.Timestamp) and not isinstance(start_date, datetime):
        print(f"开始日期类型错误: {type(start_date)}")
        return {
            "growth_rate": "日期类型错误，无法计算",
            "avg_stars_per_month": "日期类型错误，无法计算",
            "avg_stars_per_week": "日期类型错误，无法计算",
            "total_growth": total_growth,
            "growth_trend": "无法确定"
        }
    
    try:
        days_diff = (end_date - start_date).total_seconds() / (60*60*24)
    except Exception as e:
        print(f"计算日期差异时出错: {str(e)}, 类型: {type(start_date)}, {type(end_date)}")
        days_diff = 0
    
    if days_diff > 0 and initial_stars > 0:
        # 计算复合年增长率
        years = days_diff / 365.25
        cagr = ((latest_stars / initial_stars) ** (1/years) - 1) * 100 if years > 0 else 0
    else:
        cagr = 0
    
    # 计算每月平均增长
    if days_diff >= 30:
        months = days_diff / 30.44  # 平均每月天数
        avg_per_month = total_growth / months
    else:
        avg_per_month = total_growth
    
    # 计算每周平均增长
    if days_diff >= 7:
        weeks = days_diff / 7
        avg_per_week = total_growth / weeks
    else:
        avg_per_week = total_growth
    
    # 分析增长趋势
    # 如果数据点足够，计算近期和早期的增长率变化
    trend = "稳定增长"
    
    if len(stars_df) >= 10:
        # 分割数据为前半部分和后半部分
        mid_point = len(stars_df) // 2
        early_data = stars_df.iloc[:mid_point]
        recent_data = stars_df.iloc[mid_point:]
        
        # 计算前半部分和后半部分的日均增长率
        early_growth = early_data['stars'].iloc[-1] - early_data['stars'].iloc[0]
        try:
            early_days = (early_data['date'].iloc[-1] - early_data['date'].iloc[0]).total_seconds() / (60*60*24)
            early_rate = early_growth / early_days if early_days > 0 else 0
        except Exception as e:
            print(f"计算早期增长率时出错: {str(e)}")
            early_rate = 0
        
        recent_growth = recent_data['stars'].iloc[-1] - recent_data['stars'].iloc[0]
        try:
            recent_days = (recent_data['date'].iloc[-1] - recent_data['date'].iloc[0]).total_seconds() / (60*60*24)
            recent_rate = recent_growth / recent_days if recent_days > 0 else 0
        except Exception as e:
            print(f"计算近期增长率时出错: {str(e)}")
            recent_rate = 0
        
        # 比较增长率变化
        if recent_rate > early_rate * 1.5:
            trend = "加速增长"
        elif recent_rate < early_rate * 0.5:
            trend = "增长放缓"
        else:
            trend = "稳定增长"
    
    return {
        "growth_rate": f"{cagr:.2f}% (年化)",
        "avg_stars_per_month": f"{avg_per_month:.1f}",
        "avg_stars_per_week": f"{avg_per_week:.1f}",
        "total_growth": total_growth,
        "growth_trend": trend
    }

def calculate_recent_stars_growth(stars_df, recent_days=30):
    """
    计算近期星标增长情况
    
    参数:
    stars_df (DataFrame): 包含日期和星标数的数据帧
    recent_days (int): 计算近期增长的天数，默认30天
    
    返回:
    dict: 包含近期增长指标的字典
    """
    if stars_df is None or len(stars_df) < 2:
        return {
            "recent_growth": "数据不足，无法计算",
            "recent_growth_rate": "数据不足，无法计算",
            "recent_avg_per_day": "数据不足，无法计算"
        }
    
    # 确保数据按日期排序，并确保日期列是日期时间类型
    stars_df = stars_df.copy()
    if 'date' in stars_df.columns:
        try:
            stars_df['date'] = pd.to_datetime(stars_df['date'])
        except Exception as e:
            print(f"日期转换错误: {str(e)}")
            return {
                "recent_growth": "日期格式错误，无法计算",
                "recent_growth_rate": "日期格式错误，无法计算",
                "recent_avg_per_day": "日期格式错误，无法计算"
            }
    else:
        print("数据框中没有'date'列")
        return {
            "recent_growth": "数据格式错误，无法计算",
            "recent_growth_rate": "数据格式错误，无法计算",
            "recent_avg_per_day": "数据格式错误，无法计算"
        }
    
    stars_df = stars_df.sort_values('date')
    latest_date = stars_df['date'].iloc[-1]
    
    # 计算recent_days天前的日期
    recent_start_date = latest_date - pd.Timedelta(days=recent_days)
    
    # 筛选近期数据
    recent_data = stars_df[stars_df['date'] >= recent_start_date]
    
    # 如果没有足够的近期数据，返回无法计算
    if len(recent_data) < 2:
        return {
            "recent_growth": "近期数据不足，无法计算",
            "recent_growth_rate": "近期数据不足，无法计算", 
            "recent_avg_per_day": "近期数据不足，无法计算"
        }
    
    # 计算近期星标增长
    recent_initial_stars = recent_data['stars'].iloc[0]
    recent_latest_stars = recent_data['stars'].iloc[-1]
    recent_growth = recent_latest_stars - recent_initial_stars
    
    # 计算近期增长率（相对于初始星标数）
    if recent_initial_stars > 0:
        recent_growth_percentage = (recent_growth / recent_initial_stars) * 100
    else:
        recent_growth_percentage = 0
    
    # 计算近期每日平均增长
    actual_days = (recent_data['date'].iloc[-1] - recent_data['date'].iloc[0]).total_seconds() / (60*60*24)
    recent_avg_per_day = recent_growth / actual_days if actual_days > 0 else 0
    
    return {
        "recent_growth": recent_growth,
        "recent_growth_rate": f"{recent_growth_percentage:.2f}%",
        "recent_avg_per_day": f"{recent_avg_per_day:.1f}"
    }

# 提示词模板
REPO_ANALYSIS_FORMAT_PROMPT = """
[
    {
        "分析模块": "项目基础信息/社区生态/技术实现/功能特性/使用体验/代码结构/项目活跃度/对比竞品/总结建议",
        "分析结果": "该模块的分析结果",
    }
]
"""

REPO_INFO_COLLECTOR_PROMPT = """
请分析以下GitHub仓库，收集完整的信息：{repo}

## 1. 项目基础信息
- **项目名称**：仓库链接、作者/团队背景
- **简短描述**：项目功能说明，至少100字
- **项目类型**：工具库/框架/应用软件/CLI工具
- **技术栈**：主要编程语言、1-2个核心依赖技术
- **开源协议**：MIT/Apache/GPL等（影响商业使用）

## 2. 社区生态，从提供的github_info中获取
- **Star/Fork数**：项目受欢迎程度
- **星标增长趋势**：星标增长速率、月均新增星标、增长模式（加速/稳定/放缓）
- **贡献者数量**：是否有多人维护
- **社区支持**：是否有讨论群/Slack频道
- **创建时间**：项目创建时间
- **受欢迎程度评估**：根据星标数量和增长趋势，评估项目在开发者社区中的接受度

## 3. 技术实现
- **编程语言**：主要语言
- **核心依赖**：最重要的1-2个依赖库
- **系统要求**：基本运行环境

## 4. 功能特性，至少300字
- **核心功能**：详细描述项目的主要功能，参考README中的功能介绍和示例
- **特色功能**：项目的突出优势，特别是README中强调的功能点
- **应用场景**：项目适用的具体业务场景和使用案例
- **技术创新**：项目采用的创新技术或独特实现方式
- **扩展性**：是否支持插件/二次开发，自定义功能的能力
- **界面/交互**：如果有UI，描述其界面特点和用户交互体验
- **数据处理**：项目的数据处理能力和特点
- **集成能力**：与其他系统或工具的集成特性

请仔细阅读README文件内容，提取其中关于功能的详细描述、示例代码、使用案例等，确保功能描述全面而具体。如果README中包含截图或演示，也请分析这些视觉内容所展示的功能。

## 5. 使用体验，至少200字
- **安装难度**：是否简单易安装
- **文档质量**：是否有详细的使用文档
- **学习曲线**：上手难易程度

## 6. 代码解析
- **框架设计**：仓库代码框架设计，核心文件的职责、文件之间的依赖关系等，简要分析
- **代码结构**：项目的目录结构和主要文件组织方式

## 7. 项目活跃度
- **提交频率**：最近3个月commit数量
- **Issue处理**：未解决问题数量
- **版本更新**：Release发布频率

## 8. 总结建议，至少200字
- **适用人群**：明确目标用户（如"中小团队原型开发"）
- **推荐指数**：根据需求场景打分

## 9. 项目资源链接
此部分不需要写内容，由系统自动生成资源链接，包括：
- 项目架构图和流程图
- 星标历史图表
- README中的多媒体资源

请提供详细、准确的项目分析，帮助用户快速理解项目的价值和使用方法，每一项介绍尽量详细，至少100字。

仓库信息如下：{repo}

**特别提示：请确保所有输出结果使用中文，包括所有标题、内容和描述。即使原始仓库是英文的，也请将分析结果全部翻译成中文。**

请使用清晰的Markdown格式输出结果，每个分析模块以二级标题(##)开始：
"""

REPO_REFLECTION_PROMPT = """检查生成的项目概述内容，评估其质量和完整性。

格式检查要点：
1. 内容完整性：是否涵盖了基础信息/社区生态/技术实现/功能特性/使用体验/框架设计/代码结构/项目活跃度/总结建议/项目资源链接等所有必要部分
2. 格式是否正确，使用适当的Markdown格式标记
3. 各模块是否按点展开介绍，例如项目名称、项目简述、项目类型、技术栈、开源协议、社区支持、贡献者数量、提交频率、Issue处理、版本更新、适用人群、推荐指数

内容检查要点：
1. 未提供/不确定的结果，修改结果为"信息不足"，后面不需要再补充其他文字
2. 各个部分的字数满足要求

4. 特别检查"功能特性"部分（对应核心功能完善性维度）：
   - 确保此部分描述详尽，至少400字
   - 核心功能是否参考README内容，提供足够详细的说明
   - 明确列出所有核心能力与功能，并对每一项进行详细描述
   - 评估功能相对定位是否完善，是否存在缺失的关键功能
   - 分析项目在真实场景中的价值和应用潜力
   - 特色功能是否明确指出项目的独特之处
   - 是否包含应用场景、技术创新、扩展性等方面的内容
   - 分析项目的功能完善度，评估是否达到了可生产使用的水平
   - 如果有示例代码或使用案例，是否已提炼出关键点
   - 如果README中有截图或演示，是否分析了其中的功能点

5. 特别检查"使用体验"部分（对应可用性与易用性维度）：
   - 详细分析安装步骤的复杂性，至少200字
   - 评估项目的跨平台兼容性，是否支持多种操作系统
   - 详细分析环境依赖的复杂程度，是否需要特殊环境或复杂配置
   - 分析使用操作的简便性，是否存在学习门槛
   - 探讨配置项的灵活性和直观程度
   - 评估文档的完备性和清晰度，是否有足够的指导
   - 分析项目的错误处理和用户反馈机制
   - 是否提供示例和模板以便快速上手

6. 特别检查"社区生态"部分（对应项目活跃度维度）：
   - 是否包含星标数量及其增长趋势分析
   - 详细分析提交更新速度，至少100字
   - 深入分析参与开发人数及其活跃程度
   - 全面评估社区交流渠道的活跃度（如 Issues, Discussions, PR等）
   - 是否分析了项目的社区活跃度和受欢迎程度
   - 是否根据星标增长速率评估了项目的发展势头
   - 是否包含了贡献者数量、社区支持等信息
   - 分析维护者的响应速度和问题解决效率

7. 特别分析"代码质量"部分（新增维度）：
   - 至少300字的代码质量分析
   - 评估代码的可读性、一致性和模块化程度
   - 分析代码的注释和文档字符串质量
   - 评估代码是否遵循编程语言的最佳实践
   - 分析测试覆盖率和测试质量
   - 评估项目的可维护性，包括代码组织、命名规范等
   - 分析项目的进一步开发难度，是否容易理解和扩展
   - 评估项目的代码复杂性和技术债务

8. 特别检查"框架设计"部分（对应架构设计维度）：
   - 至少300字的架构设计分析
   - 详细分析项目的整体架构和模式（如MVC、微服务等）
   - 评估架构的合理性和适当性
   - 深入分析项目的模块化和组件化程度
   - 分析系统各组件之间的交互和依赖关系
   - 评估架构的扩展性和可伸缩性
   - 分析架构对功能延展的支持程度
   - 评估架构的优势和潜在问题

9. 新增"文档完备性"部分：
   - 至少250字的文档评估
   - 全面分析项目的文档结构和覆盖范围
   - 评估README的质量和完整性
   - 分析API文档和参考资料的详细程度
   - 评估是否提供了足够的使用示例和教程
   - 分析文档的更新频率和与代码的同步程度
   - 评估文档对新用户和贡献者的友好程度
   - 分析核心模块说明文档的深度和广度
   - 评估示例代码的丰富性和实用性

10. 对于"项目资源链接"部分：
   - 此部分应当留空，将由系统自动填充资源链接
   - 如果有内容，应删除，替换为注明"此部分由系统自动生成"

**特别要求：请确保所有的内容都使用中文撰写，包括标题、描述和各部分内容。即使原始项目是英文的，也请将所有内容翻译成中文。**

请检查内容并改进，使用清晰的Markdown格式输出结果，每个分析模块以二级标题(##)开始：
"""


# 添加保存Markdown文件的函数
def save_markdown_content(analysis_result, output_file):
    """
    将分析结果保存为Markdown格式

    Args:
        analysis_result: 分析结果（字符串）
        output_file: 输出文件路径

    Returns:
        bool: 是否成功保存
    """
    try:
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(analysis_result)
            return True
    except Exception as e:
        print(f"保存Markdown内容时出错: {str(e)}")
        return False


# 添加GitHub API请求函数
def get_github_repo_info(repo_url):
    """
    通过GitHub API获取仓库信息，包括stars, forks, 提交频率, 贡献者数量等

    Args:
        repo_url: GitHub仓库URL

    Returns:
        dict: 包含仓库信息的字典
    """
    try:
        # 从URL中提取所有者和仓库名
        if "github.com" not in repo_url:
            return {
                "stars": "非GitHub仓库，无法获取",
                "forks": "非GitHub仓库，无法获取",
                "issues": "非GitHub仓库，无法获取",
            }

        # 解析URL获取owner和repo名称
        parts = repo_url.strip("/").split("/")
        if len(parts) < 5:
            return {"error": "无效的GitHub URL格式"}

        owner = parts[-2]
        repo = parts[-1]
        if repo.endswith(".git"):
            repo = repo[:-4]

        # 构建API URL
        api_url = f"https://api.github.com/repos/{owner}/{repo}"

        # 发送请求
        headers = {}
        if os.environ.get("GITHUB_TOKEN"):
            headers["Authorization"] = f"token {os.environ.get('GITHUB_TOKEN')}"

        response = requests.get(api_url, headers=headers, timeout=10)

        repo_info = {}

        if response.status_code == 200:
            data = response.json()
            repo_info = {
                "stars": data.get("stargazers_count", 0),
                "forks": data.get("forks_count", 0),
                "issues": data.get("open_issues_count", 0),
                "watchers": data.get("subscribers_count", 0),
                "updated_at": data.get("updated_at", ""),
                "created_at": data.get("created_at", ""),
                "language": data.get("language", ""),
                "description": data.get("description", ""),
            }

            # 格式化创建时间为更友好的显示
            if repo_info["created_at"]:
                try:
                    from datetime import datetime
                    created_date = datetime.strptime(repo_info["created_at"], "%Y-%m-%dT%H:%M:%SZ")
                    repo_info["created_at"] = created_date.strftime("%Y年%m月%d日")
                except Exception as e:
                    logger.warning(f"格式化创建时间出错: {str(e)}")

            # 获取提交频率(最近30次提交)
            commits_url = f"https://api.github.com/repos/{owner}/{repo}/commits?per_page=30"
            commits_response = requests.get(commits_url, headers=headers, timeout=10)

            if commits_response.status_code == 200:
                commits = commits_response.json()
                if len(commits) > 0:
                    # 获取最早和最晚提交时间
                    try:
                        latest_commit_date = commits[0].get("commit", {}).get("committer", {}).get("date", "")
                        earliest_commit_date = commits[-1].get("commit", {}).get("committer", {}).get("date", "")

                        # 计算提交频率
                        if latest_commit_date and earliest_commit_date:
                            from datetime import datetime

                            latest = datetime.strptime(latest_commit_date, "%Y-%m-%dT%H:%M:%SZ")
                            earliest = datetime.strptime(earliest_commit_date, "%Y-%m-%dT%H:%M:%SZ")
                            diff_days = (latest - earliest).days
                            if diff_days > 0:
                                frequency = len(commits) / diff_days
                                repo_info[
                                    "commit_frequency"
                                ] = f"{frequency:.2f} commits/day (最近{len(commits)}次提交)"
                            else:
                                repo_info["commit_frequency"] = f"{len(commits)} commits in a day"
                        else:
                            repo_info["commit_frequency"] = f"获取到{len(commits)}次提交"
                    except Exception as e:
                        logger.warning(f"计算提交频率出错: {str(e)}")
                        repo_info["commit_frequency"] = f"获取到{len(commits)}次提交"
                else:
                    repo_info["commit_frequency"] = "无提交记录"
            else:
                repo_info["commit_frequency"] = "获取提交记录失败"

            # 获取贡献者数量
            contributors_url = f"https://api.github.com/repos/{owner}/{repo}/contributors?per_page=100"
            contributors_response = requests.get(contributors_url, headers=headers, timeout=10)

            if contributors_response.status_code == 200:
                contributors = contributors_response.json()
                repo_info["contributors_count"] = len(contributors)

                # 附加主要贡献者信息
                if len(contributors) > 0:
                    top_contributors = [c.get("login", "") for c in contributors[:3]]
                    repo_info["top_contributors"] = ", ".join(top_contributors)
            else:
                repo_info["contributors_count"] = "获取贡献者失败"

            # 检查社区支持 - README中是否有Discord/Slack/社区链接
            readme_url = f"https://api.github.com/repos/{owner}/{repo}/readme"
            readme_response = requests.get(readme_url, headers=headers, timeout=10)

            repo_info["community_support"] = []

            if readme_response.status_code == 200:
                import base64

                try:
                    readme_content = base64.b64decode(readme_response.json().get("content", "")).decode("utf-8")

                    # 检查常见的社区支持关键词
                    if "discord" in readme_content.lower():
                        repo_info["community_support"].append("Discord")
                    if "slack" in readme_content.lower():
                        repo_info["community_support"].append("Slack")
                    if "gitter" in readme_content.lower():
                        repo_info["community_support"].append("Gitter")
                    if "forum" in readme_content.lower() or "论坛" in readme_content:
                        repo_info["community_support"].append("Forum")
                    if "chat" in readme_content.lower():
                        repo_info["community_support"].append("Chat")
                    if "community" in readme_content.lower() or "社区" in readme_content:
                        repo_info["community_support"].append("Community")

                    if len(repo_info["community_support"]) == 0:
                        repo_info["community_support"] = "无明确社区支持信息"
                    else:
                        repo_info["community_support"] = ", ".join(repo_info["community_support"])
                except Exception as e:
                    logger.warning(f"解析README出错: {str(e)}")
                    repo_info["community_support"] = "解析README出错"
            else:
                repo_info["community_support"] = "无法获取README"

            return repo_info
        else:
            return {
                "error": f"API请求失败，状态码: {response.status_code}",
                "stars": "无法获取",
                "forks": "无法获取",
                "issues": "无法获取",
                "created_at": "无法获取",
            }
    except Exception as e:
        logger.error(f"获取GitHub仓库信息出错: {str(e)}")
        return {
            "error": f"获取GitHub信息失败: {str(e)}",
            "stars": "获取失败",
            "forks": "获取失败",
            "issues": "获取失败",
            "created_at": "获取失败",
        }


# 定义Git仓库分析工作流类
class GitRepoAnalyzer:
    def __init__(self, config_path="config/config.yaml"):
        # 创建默认配置文件（如果不存在）
        self._create_default_config_if_not_exists(config_path)

        # 加载配置
        self.load_config(config_path)

        # 初始化模型
        self.model = self._create_model()
        logging.warning("模型初始化完成")

        # 初始化代理
        self.agents = self._initialize_agents()
        logging.warning("代理初始化完成")

    def _create_default_config_if_not_exists(self, config_path):
        """如果配置文件不存在，创建默认配置"""
        if not os.path.exists(config_path):
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            default_config = {
                "model": {
                    "type": "openai/gpt-4o-mini",
                    "api": {
                        "openai_compatibility_api_key": os.environ.get("OPENAI_API_KEY", ""),
                        "openai_compatibility_api_base_url": os.environ.get(
                            "OPENAI_API_BASE", "https://api.openai.com/v1"
                        ),
                    },
                },
                "agents": {
                    "repo_info_collector": {"enabled": True},
                    "repo_analyzer": {"enabled": True},
                    "repo_reflector": {"enabled": True},
                },
                "repo": {
                    "default_branch": "main",
                    "max_file_size": 1024 * 1024,  # 1MB
                    "auto_clone": True,  # 是否自动克隆GitHub仓库
                    "github_url": "https://github.com/openai/codex",  # 默认仓库URL
                    "download_media": True,  # 是否下载多媒体资源
                    "deep_analysis": True,  # 是否启用深度代码分析
                },
            }
            with open(config_path, "w") as f:
                yaml.dump(default_config, f)

    def load_config(self, config_path):
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 设置模型配置
            self.model_config = config.get("model", {})
            
            # 检查新配置结构
            if 'material' in config and 'sources' in config['material'] and 'github' in config['material']['sources']:
                github_config = config['material']['sources']['github']
                logger.info(f"使用新配置结构中的GitHub源: {github_config.get('url', 'N/A')}")
                
                # 基本配置
                self.repo_config = {
                    "github_url": github_config.get('url', 'https://github.com/openai/codex'),
                    "purpose": github_config.get('purpose', '分析GitHub项目')
                }
                
                # 分析配置
                analysis_config = github_config.get('analysis_config', {})
                self.repo_config.update({
                    "deep_analysis": analysis_config.get('deep_analysis', True),
                    "download_media": analysis_config.get('download_media', True),
                    "auto_clone": analysis_config.get('auto_clone', True),
                    "max_files": analysis_config.get('max_files', 200),
                    "max_content_size_kb": analysis_config.get('max_content_size_kb', 1024)
                })
                
                logger.info(f"GitHub分析配置: 深度分析={self.repo_config['deep_analysis']}, 下载媒体={self.repo_config['download_media']}, 自动克隆={self.repo_config['auto_clone']}")
                
            # 兼容旧配置结构
            elif 'github' in config:
                logger.info("使用旧配置结构中的GitHub配置")
                self.repo_config = {
                    "github_url": config['github'].get('project_url', 'https://github.com/openai/codex'),
                    "purpose": "分析GitHub项目",
                    "deep_analysis": True,  # 默认启用
                    "download_media": True,  # 默认启用
                    "auto_clone": True,  # 默认启用
                    "max_files": 200,
                    "max_content_size_kb": 1024
                }
            else:
                logger.warning("配置文件中未找到GitHub配置，使用默认值")
                self.repo_config = {
                    "github_url": "https://github.com/openai/codex",
                    "purpose": "分析GitHub项目",
                    "deep_analysis": True,
                    "download_media": True,
                    "auto_clone": True,
                    "max_files": 200,
                    "max_content_size_kb": 1024
                }
            
            # 设置代理配置
            self.agent_config = config.get("agents", {})
            
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
            # 使用默认配置
            self.model_config = {}
            self.agent_config = {}
            self.repo_config = {
                "github_url": "https://github.com/openai/codex",
                "purpose": "分析GitHub项目",
                "deep_analysis": True,
                "download_media": True,
                "auto_clone": True,
                "max_files": 200,
                "max_content_size_kb": 1024
            }
            return {}

    def _create_model(self):
        """根据配置创建模型实例"""
        api_config = self.model_config.get("api", {})

        return ModelFactory.create(
            model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
            model_type=self.model_config.get("type", "openai/gpt-4o-mini"),
            api_key=api_config.get("openai_compatibility_api_key"),
            url=api_config.get("openai_compatibility_api_base_url"),
        )

    def _initialize_agents(self) -> dict:
        """初始化所有启用的代理及其系统提示和工具"""
        agents = {}

        # 仓库信息收集代理
        agents["repo_info_collector"] = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="仓库信息收集者",
                content="你负责收集和组织Git仓库的信息，包括项目结构、功能特性和技术栈。",
            ),
            model=self.model,
            tools=[*InfoCollectorToolkit().get_tools()],
        )

        # 仓库分析代理
        agents["repo_analyzer"] = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="仓库分析者",
                content="你负责分析Git仓库信息并生成结构化的项目概述和文档。",
            ),
            model=self.model,
        )

        # 内容反思代理
        agents["repo_reflector"] = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="内容反思者",
                content="你负责检查和完善生成的仓库分析内容，确保其完整性、准确性和格式正确性。",
            ),
            model=self.model,
        )
        
        # 架构分析代理
        agents["repo_architect"] = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="架构分析者",
                content="你负责分析代码库的架构，识别关键模块及其依赖关系，并使用Mermaid语法创建架构图。",
            ),
            model=self.model,
        )
        
        # 项目评估代理
        agents["repo_evaluator"] = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="项目评估者",
                content="你负责基于六大维度评估项目质量，包括核心功能完善性、可用性与易用性、项目活跃度、代码质量、架构设计和文档完备性。你提供客观、公正的评分和简要评语。",
            ),
            model=self.model,
        )

        return agents

    def _extract_and_save_mermaid(self, content, base_name, output_dir):
        """
        从内容中提取Mermaid图表并保存
        
        Args:
            content: 包含Mermaid图表的内容
            base_name: 基础文件名
            output_dir: 输出目录
            
        Returns:
            list: 保存的图表文件信息列表，包含文件路径和描述
        """
        saved_files = []
        # 提取Mermaid图
        mermaid_pattern = r"```mermaid\s*([\s\S]*?)```"
        mermaid_matches = re.findall(mermaid_pattern, content)
        
        for i, mermaid_content in enumerate(mermaid_matches):
            # 排除空的或只有注释的图表
            if not mermaid_content.strip() or all(line.strip().startswith(('%', '#')) for line in mermaid_content.strip().split('\n')):
                continue
            
            # 根据图表内容确定图表类型和描述
            diagram_type = "graph TD"  # 默认类型
            diagram_description = "流程图"  # 默认描述
            
            first_line = mermaid_content.strip().split('\n')[0] if mermaid_content.strip() else ""
            for dt in ['graph', 'flowchart', 'sequenceDiagram', 'classDiagram', 'stateDiagram', 'gantt', 'pie']:
                if first_line.startswith(dt):
                    diagram_type = None  # 已有图表类型声明，不需要添加默认类型
                    if dt in ['graph', 'flowchart']:
                        diagram_description = "架构流程图"
                    elif dt == 'sequenceDiagram':
                        diagram_description = "时序图"
                    elif dt == 'classDiagram':
                        diagram_description = "类图"
                    elif dt == 'stateDiagram':
                        diagram_description = "状态图"
                    elif dt == 'gantt':
                        diagram_description = "甘特图"
                    elif dt == 'pie':
                        diagram_description = "饼图"
                    break
            
            mermaid_file_path = os.path.join(output_dir, f"{base_name}_{i+1}.mmd")
            try:
                # 应用修复并保存Mermaid内容
                if diagram_type:
                    # 如果没有图表类型声明，添加默认类型
                    if '-->' in mermaid_content or '->' in mermaid_content:
                        mermaid_content = f"{diagram_type};\n{mermaid_content}"
                
                # 修复语法并保存
                file_path = save_mermaid_file(mermaid_content, mermaid_file_path)
                
                # 生成相对文件名（不包含完整路径）
                relative_filename = os.path.basename(file_path)
                
                saved_files.append({
                    "path": file_path,
                    "filename": relative_filename,
                    "description": diagram_description,
                    "type": "mermaid"
                })
                logger.info(f"成功保存Mermaid图表: {file_path}")
            except Exception as e:
                logger.error(f"保存Mermaid图表失败: {str(e)}")
                
        return saved_files

    def _perform_deep_analysis(self, repo_name: str, repo_path: str, analysis_data):
        """
        执行深入代码分析，包括架构分析和依赖分析
        
        Args:
            repo_name: 仓库名称
            repo_path: 本地仓库路径
            analysis_data: 基本分析结果

        Returns:
            str: 深度分析结果
        """
        output_dir = os.path.join("output", repo_name)
        os.makedirs(output_dir, exist_ok=True)

        # 执行架构分析以获取关键模块和关系
        print("分析模块关系...")
        architecture_analysis_prompt = (
            "请分析以下仓库的代码架构，识别关键模块及其依赖关系\n\n"
            f"仓库名称: {repo_name}\n\n"
            "基本分析结果:\n" + analysis_data + "\n\n"
            "请执行以下任务:\n"
            "1. 识别代码库中的主要模块/组件\n"
            "2. 确定模块间的依赖关系\n"
            "3. 使用Mermaid语法创建架构图\n"
            "4. 分析架构的优缺点\n"
            "5. 深入评估代码质量和架构设计\n\n"
            "输出格式:\n\n"
            "## 架构概述\n\n"
            "[至少300字的总体架构描述，包括设计模式、架构风格等。分析架构是否采用了现代化的设计理念，是否具有良好的模块化程度，是否易于维护和扩展。]\n\n"
            "## 主要模块\n\n"
            "1. 模块A: [详细功能描述，包括职责范围、核心接口和实现方式]\n"
            "2. 模块B: [详细功能描述，包括职责范围、核心接口和实现方式]\n"
            "...\n\n"
            "## 模块依赖关系\n\n"
            "```mermaid\n"
            "graph TD;\n"
            "  A[\"模块A\"] --> B[\"模块B\"];\n"
            "  B --> C[\"模块C\"];\n"
            "  A --> C;\n"
            "```\n\n"
            "## 架构评估\n\n"
            "**优点**:\n"
            "- [优点1: 详细解释为何这是优点及其带来的具体好处]\n"
            "- [优点2: 详细解释为何这是优点及其带来的具体好处]\n\n"
            "**缺点**:\n"
            "- [缺点1: 详细解释为何这是缺点及其可能带来的问题]\n"
            "- [缺点2: 详细解释为何这是缺点及其可能带来的问题]\n\n"
            "## 代码质量评估\n\n"
            "[至少200字的代码质量评估，包括代码一致性、注释完整性、命名规范、可读性、复杂度、测试覆盖率等方面。分析项目是否易于维护和二次开发。]\n\n"
            "**特别要求：\n"
            "1. 请确保所有的分析内容都使用中文撰写，包括架构概述、模块描述和评估。即使原始项目是英文的，也请将所有分析内容翻译成中文。\n"
            "2. Mermaid图必须遵循以下格式规范：\n"
            "   - 第一行必须是'graph TD;'，注意结尾的分号\n"
            "   - 所有节点文本必须用双引号括起来，如A[\"模块A\"]\n"
            "   - 每条关系语句结尾必须有分号，如'A --> B;'\n"
            "   - 子图声明(subgraph)内的节点也必须以分号结尾\n"
            "   - 使用%%进行注释，而不是#号\n"
            "3. 确保图表语法正确，否则无法正常渲染**"
        )
            
        architecture_analysis = self.process_message("repo_architect", architecture_analysis_prompt)
        
        # 提取Mermaid图并保存
        module_relation_files = self._extract_and_save_mermaid(
            architecture_analysis, 
            "module_relationships", 
            output_dir
        )
        
        if module_relation_files:
            print(f"已保存 {len(module_relation_files)} 个模块关系图")
        else:
            print("未能从架构分析中提取有效的模块关系图")
            
        # 使用反思代理改进架构分析
        reflection_prompt = (
            f"我们刚刚生成了以下仓库架构分析，请检查其质量并提供改进:\n\n"
            f"{architecture_analysis}\n\n"
            "请检查内容是否包含:\n"
            "1. 足够详细的架构概述(至少300字)\n"
            "2. 所有重要模块的功能说明，包含详细职责和关键接口\n"
            "3. 清晰的模块依赖关系图(使用Mermaid语法)\n"
            "4. 全面的架构评估(包括优缺点)\n"
            "5. 详细的代码质量评估(至少200字)\n\n"
            "请特别确保分析了以下方面：\n"
            "- 架构的扩展性和可维护性\n"
            "- 模块化和组件化程度\n"
            "- 架构是否支持功能延展\n"
            "- 代码的可读性、一致性和模块化程度\n"
            "- 项目的进一步开发难度\n\n"
            "如果有改进空间，请直接提供完整的更新版本。如果内容已经很好，请简单说明并给出原始内容。\n\n"
            "**特别要求**:\n"
            "1. 确保所有的分析内容都使用中文撰写，包括架构概述、模块描述和评估。\n"
            "2. 如果包含Mermaid图表，请确保语法正确:\n"
            "   - 使用%%进行注释，而不是#号\n"
            "   - 每条语句结尾加分号\n"
            "   - 节点文本用双引号括起来\n"
            "   - 正确的子图格式\n"
        )
        
        improved_architecture = self.process_message("repo_reflector", reflection_prompt)
        
        # 保存架构分析结果
        with open(os.path.join(output_dir, "03_architecture_analysis.md"), "w", encoding="utf-8") as f:
            f.write(improved_architecture)
        
        # 再次提取改进后的Mermaid图(如果有)
        improved_module_relation_files = self._extract_and_save_mermaid(
            improved_architecture, 
            "improved_module_relationships", 
            output_dir
        )
        
        if improved_module_relation_files:
            print(f"已保存 {len(improved_module_relation_files)} 个改进的模块关系图")
            
        print("✓ 架构分析完成")
            
        # 分析项目的核心流程
        print("分析核心流程...")
        core_flow_analysis = self._analyze_core_flows(repo_name, repo_path)
        print("✓ 核心流程分析完成")
        
        # 合并所有深度分析结果
        complete_analysis = f"""
## 代码架构分析

{improved_architecture}

## 核心流程分析

{core_flow_analysis}
"""
            
        return complete_analysis

    def process_message(self, agent_name: str, message: str):
        """处理消息并返回响应"""
        agent = self.agents[agent_name]
        # 发送消息并获取响应
        response = agent.step(message)
        logger.debug(response)

        # 获取响应内容
        content = response.msgs[0].content

        # 如果内容是字典类型，转换为JSON字符串
        if isinstance(content, dict):
            try:
                return json.dumps(content, ensure_ascii=False, cls=NumpyEncoder)
            except Exception as e:
                logger.warning(f"JSON序列化失败: {str(e)}")
                return str(content)

        return content

    def _analyze_core_flows(self, repo_name: str, repo_path: str) -> str:
        """
        分析项目核心流程，使用例子方式展示功能和使用方法
        
        Args:
            repo_name: 仓库名称
            repo_path: 仓库本地路径
            
        Returns:
            str: 流程分析结果（Markdown格式）
        """
        print(f"正在分析 {repo_name} 的核心功能和使用方法...")
        
        # 创建输出目录
        output_dir = os.path.join("output", repo_name)
        os.makedirs(output_dir, exist_ok=True)
        
        # 准备流程分析提示
        flow_analysis_prompt = f"""
        分析仓库 {repo_name} 的代码，提取核心功能和使用方法。
        
        请完成以下任务：
        
        1. 核心功能识别（简化版本）
           - 确定2-3个最重要的功能点
           - 每个功能简洁说明：功能名称、一句话描述、主要应用场景
           - 专注于最能体现项目价值的功能
        
        2. 快速使用指南
           - 为每个核心功能提供最简化的使用步骤（3-5步）
           - 提供基础代码示例（5-10行代码）
           - 说明关键参数和预期结果
        
        3. 关键代码片段
           - 每个功能只展示最核心的代码片段（3-8行）
           - 简要解释代码的作用
        
        输出格式：
        
        ## 核心功能与使用方法
        
        [100字左右的项目功能概述]
        
        ### 功能一：[功能名称]
        
        **功能描述**：[一句话描述功能作用]
        
        **使用场景**：[简述适用场景]
        
        **快速使用**：
        ```bash
        # 安装
        pip install package_name
        ```
        
        ```python
        # 基本使用（5-10行代码示例）
        from package import main_function
        
        result = main_function(param="value")
        print(result)
        ```
        
        **核心代码**：
        ```python
        # 关键实现逻辑（3-8行）
        def core_logic(input_data):
            processed = process(input_data)
            return processed
        ```
        
        [对其他功能重复相同的简化格式]
        
        请确保内容简洁明了，突出实用性，使用中文描述所有内容。
        """
            
        # 获取项目文件结构
        file_structure = []
        for root, dirs, files in os.walk(repo_path):
            for file in files:
                if not file.startswith('.') and not file.endswith(('.jpg', '.png', '.gif', '.svg', '.pdf', '.zip')):
                    rel_path = os.path.relpath(os.path.join(root, file), repo_path)
                    file_structure.append(rel_path)
            
        # 排序文件，优先展示关键文件
        key_files = []
        for file in file_structure:
            if any(important in file.lower() for important in ['main', 'app', 'index', 'server', 'client', 'core']):
                key_files.append(file)
            if len(key_files) >= 10:  # 最多10个关键文件
                break
        
        # 将关键文件列表添加到提示中
        if key_files:
            file_list_str = "\n".join([f"- {file}" for file in key_files])
            flow_analysis_prompt += f"\n\n项目关键文件列表（参考）：\n{file_list_str}"
        
        # 分析核心功能和使用方法
        flow_analysis = self.process_message("repo_architect", flow_analysis_prompt)
        
        # 让反思代理改进分析
        reflection_prompt = (
            f"我们刚刚生成了以下仓库核心功能和使用方法分析，请检查其质量并提供改进:\n\n"
            f"{flow_analysis}\n\n"
            "请检查内容是否包含:\n"
            "1. 整体功能概述\n"
            "2. 每个功能的详细描述，包括功能名称、功能描述、应用场景\n"
            "3. 核心代码展示\n"
            "4. 详细的使用方法和示例\n\n"
            "如果有改进空间，请直接提供完整的更新版本。如果内容已经很好，请简单说明并给出原始内容。\n\n"
            "**特别要求**:\n"
            "1. 确保所有的分析内容都使用中文撰写\n"
            "2. 代码注释也请使用中文\n"
            "3. 提供的示例代码应该尽可能完整和可运行\n"
            "4. 确保代码缩进和格式正确\n"
        )
            
        improved_flow_analysis = self.process_message("repo_reflector", reflection_prompt)
        
        # 保存核心功能分析结果
        with open(os.path.join(output_dir, "04_core_features_usage.md"), "w", encoding="utf-8") as f:
            f.write(improved_flow_analysis)
            
        print("✓ 核心功能和使用方法分析完成")
        
        return improved_flow_analysis

    def collect_repo_info(self, repo_url, local_repo_path=None):
        """收集远程仓库信息"""
        print(f"正在收集远程仓库信息: {repo_url}...")

        try:
            # 检查URL格式，判断是否为GitHub或其他Git仓库
            if not repo_url.startswith("http"):
                raise ValueError(f"仓库URL格式不正确: {repo_url}")

            # 从URL提取仓库名称
            repo_name = repo_url.split("/")[-1].replace(".git", "")

            # 获取GitHub仓库信息（stars、forks等）
            print("获取GitHub仓库统计信息...")
            github_info = get_github_repo_info(repo_url)
            if "error" in github_info:
                print(f"警告: {github_info['error']}")
            else:
                # 输出核心统计信息
                print("✓ 获取到GitHub统计:")
                print(f"  - Stars: {github_info['stars']}")
                print(f"  - Forks: {github_info['forks']}")
                if "contributors_count" in github_info:
                    print(f"  - 贡献者数量: {github_info['contributors_count']}")
                if "commit_frequency" in github_info:
                    print(f"  - 提交频率: {github_info['commit_frequency']}")
                if "community_support" in github_info:
                    print(f"  - 社区支持: {github_info['community_support']}")
                if "created_at" in github_info:
                    print(f"  - 项目创建时间: {github_info['created_at']}")
                    
            # 获取星标历史数据
            try:
                print("收集星标历史数据...")
                # 解析GitHub URL
                owner, repo = parse_github_url(repo_url)
                
                # 检查是否已存在星标历史文件
                output_dir = os.path.join('output', repo_name)
                os.makedirs(output_dir, exist_ok=True)
                
                # 使用repo名作为文件名基础
                file_base = f"{repo_name}_stars"
                csv_path = os.path.join(output_dir, f'{file_base}.csv')
                png_path = os.path.join(output_dir, f'{file_base}.png')
                
                # 如果星标历史文件已存在，则直接使用
                if os.path.exists(csv_path) and os.path.exists(png_path):
                    print(f"星标历史文件已存在，跳过抓取步骤")
                    
                    # 尝试读取现有的CSV文件计算指标
                    try:
                        import pandas as pd
                        stars_df = pd.read_csv(csv_path)
                        stars_df['date'] = pd.to_datetime(stars_df['date'])
                        growth_metrics = calculate_stars_growth_metrics(stars_df)
                        
                        # 将星标历史数据添加到github_info中
                        github_info["stars_history"] = {
                            "csv_path": csv_path,
                            "chart_path": png_path,
                            "metrics": growth_metrics
                        }
                        print(f"已从现有文件加载星标历史数据")
                    except Exception as e:
                        print(f"读取现有星标历史数据时出错: {str(e)}")
                        # 设置默认指标
                        github_info["stars_history"] = {
                            "csv_path": csv_path,
                            "chart_path": png_path,
                            "metrics": {
                                "growth_trend": "稳定增长",
                                "growth_rate": "N/A",
                                "avg_stars_per_month": "N/A",
                                "avg_stars_per_week": "N/A",
                                "total_growth": "N/A"
                            }
                        }
                else:
                    # 如果文件不存在，则获取星标历史数据
                    stars_history = estimate_stars_history(owner, repo)
                    if stars_history is not None:
                        # 在保存前将时区信息移除，以便CSV格式更清晰
                        stars_history_local = stars_history.copy()
                        stars_history_local['date'] = stars_history_local['date'].dt.tz_localize(None)
                        
                        # 保存CSV数据
                        stars_history_local.to_csv(csv_path, index=False)
                        print(f"星标历史数据已保存到 {csv_path}")
                        
                        # 确定频率类型
                        freq_type = "周" if ('W' in stars_history.index.freq.name if hasattr(stars_history.index, 'freq') else False) else "天"
                        
                        # 创建美观的图表
                        chart_path = create_beautiful_chart(owner, repo, stars_history_local, freq_type, png_path)
                        
                        # 计算星标增长指标
                        growth_metrics = calculate_stars_growth_metrics(stars_history_local)
                        print(f"星标增长率: {growth_metrics['growth_rate']}")
                        print(f"增长趋势: {growth_metrics['growth_trend']}")
                        
                        # 将星标历史数据和指标添加到github_info中
                        github_info["stars_history"] = {
                            "csv_path": csv_path,
                            "chart_path": chart_path,
                            "metrics": growth_metrics
                        }
                    else:
                        print("未能获取星标历史数据")
            except Exception as e:
                print(f"收集星标历史数据时出错: {str(e)}")

            # 确定仓库路径并收集代码结构数据
            repo_structure = {}
            all_code_files = {}

            if local_repo_path and os.path.exists(local_repo_path) and os.path.isdir(local_repo_path):
                repo_path = local_repo_path
                print(f"使用指定的本地仓库路径: {repo_path}")

                # 收集本地仓库结构数据
                try:
                    # 获取目录结构
                    find_cmd = [
                        "find",
                        ".",
                        "-type",
                        "f",
                        "-not",
                        "-path",
                        "*/\\.*",
                        "-not",
                        "-path",
                        "*/node_modules/*",
                        "-not",
                        "-path",
                        "*/venv/*",
                        "-not",
                        "-path",
                        "*/.git/*",
                    ]

                    find_result = subprocess.run(
                        find_cmd, cwd=repo_path, check=True, capture_output=True, text=True, timeout=30
                    )

                    file_list = find_result.stdout.strip().split("\n")

                    # 提取主要文件类型
                    file_types = {}
                    for file_path in file_list:
                        ext = os.path.splitext(file_path)[1]
                        if ext:
                            file_types[ext] = file_types.get(ext, 0) + 1

                    # 排序获取最常见的文件类型
                    common_types = sorted(file_types.items(), key=lambda x: x[1], reverse=True)
                    file_type_summary = ", ".join([f"{t[0]}({t[1]})" for t in common_types[:5]])

                    # 构建目录树
                    try:
                        dir_cmd = [
                            "find",
                            ".",
                            "-type",
                            "d",
                            "-not",
                            "-path",
                            "*/\\.*",
                            "-not",
                            "-path",
                            "*/node_modules/*",
                            "-not",
                            "-path",
                            "*/venv/*",
                            "-not",
                            "-path",
                            "*/.git/*",
                        ]

                        dir_result = subprocess.run(
                            dir_cmd, cwd=repo_path, check=True, capture_output=True, text=True, timeout=20
                        )

                        dir_tree = dir_result.stdout
                    except Exception as e:
                        logger.warning(f"获取目录树出错: {str(e)}")
                        dir_tree = "无法获取目录树"

                    # 读取README文件
                    readme_content = ""
                    readme_paths = ["README.md", "Readme.md", "readme.md", "README.txt", "README"]
                    for readme_file in readme_paths:
                        readme_path = os.path.join(repo_path, readme_file)
                        if os.path.exists(readme_path):
                            try:
                                with open(readme_path, encoding="utf-8", errors="ignore") as f:
                                    readme_content = f.read()
                                break
                            except Exception:
                                pass

                    # 收集所有代码文件
                    print("收集代码文件内容，这可能需要一些时间...")

                    # 识别主要代码文件类型
                    code_extensions = [
                        ".py",
                        ".js",
                        ".jsx",
                        ".ts",
                        ".tsx",
                        ".java",
                        ".go",
                        ".rs",
                        ".rb",
                        ".php",
                        ".c",
                        ".cpp",
                        ".h",
                        ".hpp",
                        ".cs",
                        ".swift",
                        ".kt",
                        ".scala",
                        ".vue",
                    ]

                    # 收集重要的配置文件类型
                    config_extensions = [
                        ".json",
                        ".yaml",
                        ".yml",
                        ".toml",
                        ".ini",
                        ".xml",
                        ".properties",
                        ".gradle",
                        ".sbt",
                        ".tf",
                    ]

                    # 文档文件类型
                    doc_extensions = [".md", ".txt", ".rst", ".adoc"]

                    # 合并所有要处理的文件类型
                    all_extensions = code_extensions + config_extensions + doc_extensions

                    # 过滤掉超大文件和二进制文件
                    max_file_size = 1024 * 100  # 100KB的限制
                    binary_extensions = [
                        ".zip",
                        ".jar",
                        ".war",
                        ".ear",
                        ".class",
                        ".pyc",
                        ".o",
                        ".so",
                        ".dll",
                        ".exe",
                        ".bin",
                        ".dat",
                        ".png",
                        ".jpg",
                        ".jpeg",
                        ".gif",
                        ".svg",
                        ".ico",
                        ".mp3",
                        ".mp4",
                        ".avi",
                        ".mov",
                        ".pdf",
                    ]

                    # 计算所有代码文件的总大小，用于动态调整截断
                    total_files = [
                        f
                        for f in file_list
                        if any(f.endswith(ext) for ext in all_extensions)
                        and not any(f.endswith(ext) for ext in binary_extensions)
                    ]

                    # 统计文件总大小
                    total_size = 0
                    file_sizes = {}
                    for file_path in total_files:
                        try:
                            abs_file_path = os.path.join(repo_path, file_path.strip("./"))
                            file_size = os.path.getsize(abs_file_path)
                            file_sizes[file_path] = file_size
                            total_size += file_size
                        except Exception:
                            pass

                    # 动态调整收集策略
                    if total_size > 1024 * 1024 * 5:  # 如果总大小超过5MB
                        print(f"代码库较大 ({total_size/1024/1024:.1f}MB)，将只收集核心文件")

                        # 获取最重要的文件
                        # 1. 排除测试文件
                        important_files = [
                            f for f in total_files if "test" not in f.lower() and "spec" not in f.lower()
                        ]

                        # 2. 优先考虑主要的源代码文件
                        code_files = [f for f in important_files if any(f.endswith(ext) for ext in code_extensions)]

                        # 3. 按文件大小排序，选择中等大小的文件（非常大和非常小的文件可能不那么重要）
                        sorted_files = sorted(code_files, key=lambda f: file_sizes.get(f, 0))
                        mid_point = len(sorted_files) // 2

                        # 从中间向两端取文件，但限制总数
                        selected_files = []
                        max_files = 50  # 最多收集50个文件
                        radius = 0

                        while len(selected_files) < max_files and radius < max(
                            mid_point, len(sorted_files) - mid_point
                        ):
                            if mid_point - radius >= 0:
                                selected_files.append(sorted_files[mid_point - radius])

                            if mid_point + radius < len(sorted_files) and len(selected_files) < max_files:
                                selected_files.append(sorted_files[mid_point + radius])

                            radius += 1
                            if radius > 200:  # 安全保护
                                break

                        # 确保包含一些配置文件
                        config_files = [f for f in important_files if any(f.endswith(ext) for ext in config_extensions)]
                        selected_files.extend(config_files[:10])  # 最多添加10个配置文件
                    else:
                        # 对于小型代码库，尝试收集全部文件
                        selected_files = total_files

                    # 限制总文件数
                    max_total_files = 200
                    if len(selected_files) > max_total_files:
                        print(f"文件数量过多，限制为前{max_total_files}个")
                        selected_files = selected_files[:max_total_files]

                    # 读取所有选定的文件
                    total_content_size = 0
                    max_content_size = 1024 * 1024  # 限制内容总大小为1MB

                    for file_path in selected_files:
                        try:
                            abs_file_path = os.path.join(repo_path, file_path.strip("./"))

                            # 跳过大文件
                            if os.path.getsize(abs_file_path) > max_file_size:
                                all_code_files[
                                    file_path
                                ] = f"文件过大，已跳过 ({os.path.getsize(abs_file_path)/1024:.1f}KB)"
                                continue

                            with open(abs_file_path, encoding="utf-8", errors="ignore") as f:
                                content = f.read()

                                # 检查是否超出总大小限制
                                content_size = len(content.encode("utf-8"))
                                if total_content_size + content_size > max_content_size:
                                    # 如果超出限制，截断内容
                                    max_lines = 200
                                    content_lines = content.split("\n")
                                    if len(content_lines) > max_lines:
                                        content = (
                                            "\n".join(content_lines[:max_lines])
                                            + f"\n... (文件共{len(content_lines)}行，已截断)"
                                        )

                                all_code_files[file_path] = content
                                total_content_size += min(content_size, len(all_code_files[file_path].encode("utf-8")))

                                # 如果总内容大小已经超过限制，停止添加更多文件
                                if total_content_size >= max_content_size:
                                    print(f"已达到内容大小限制 ({max_content_size/1024:.1f}KB)，停止收集更多文件")
                                    break
                        except Exception as e:
                            logger.warning(f"读取文件 {file_path} 出错: {str(e)}")
                            all_code_files[file_path] = f"读取出错: {str(e)}"

                    print(f"✓ 成功收集 {len(all_code_files)} 个代码文件，总大小: {total_content_size/1024:.1f}KB")

                    # 将收集到的代码结构信息添加到repo_structure
                    repo_structure = {
                        "file_count": len(file_list),
                        "file_types": file_type_summary,
                        "dir_tree": dir_tree,
                        "readme": readme_content,
                    }

                    print("✓ 成功收集本地仓库结构信息:")
                    print(f"  - 文件总数: {len(file_list)}")
                    print(f"  - 已分析文件数: {len(all_code_files)}")
                    print(f"  - 主要文件类型: {file_type_summary}")

                except Exception as e:
                    logger.error(f"分析本地仓库结构时出错: {str(e)}")

            # 构建仓库信息对象
            repo_info = {
                "name": repo_name,
                "url": repo_url,
                "github_info": github_info,
                "repo_structure": repo_structure,
                "all_code_files": all_code_files,
                # 其他信息...
            }
            
            # 将仓库信息传递给信息收集提示词
            prompt = REPO_INFO_COLLECTOR_PROMPT.format(repo=json.dumps(repo_info, ensure_ascii=False, cls=NumpyEncoder))
            
            # 使用仓库信息收集代理处理URL - 一次性获取完整分析
            print("让AI代理分析仓库所有代码...")
            analysis_result = self.process_message("repo_info_collector", prompt)

            print(f"AI代理完成仓库 {repo_name} 的全面代码分析")
            return analysis_result

        except Exception as e:
            logging.error(f"收集仓库信息出错: {str(e)}")
            raise

    def analyze_repo(self, repo_url=None, local_repo_path=None):
        """
        分析仓库，生成结构化文档

        Args:
            repo_url: 仓库URL（如果为None，从配置文件获取）
            local_repo_path: 本地仓库路径，优先使用该路径进行代码结构分析
        """
        # 辅助函数，检查字典是否有效且包含stars_history
        def is_valid_dict_with_stars_history(d):
            return isinstance(d, dict) and "stars_history" in d and isinstance(d["stars_history"], dict)
            
        # 如果未提供仓库URL，从配置中获取
        if repo_url is None:
            repo_url = self.repo_config.get("github_url", "https://github.com/openai/codex")
            print(f"使用配置文件中的仓库URL: {repo_url}")
        
        # 从URL提取仓库名称
        repo_name = repo_url.split("/")[-1]
        if repo_name.endswith(".git"):
            repo_name = repo_name[:-4]
        
        # 创建结果目录
        output_dir = os.path.join("output", repo_name)
        os.makedirs(output_dir, exist_ok=True)
        
        # 如果提供了本地路径，验证其存在性
        if local_repo_path:
            if os.path.exists(local_repo_path) and os.path.isdir(local_repo_path):
                print(f"使用指定的本地仓库路径: {local_repo_path}")
            else:
                print(f"警告：指定的本地路径 {local_repo_path} 不存在或不是目录")
                local_repo_path = None
            
        # 只有当本地路径为空时，才尝试克隆仓库
        if not local_repo_path and self.repo_config.get("auto_clone", True):
            try:
                print(f"检查仓库 {repo_name} 是否需要克隆...")
                local_repo_path = ensure_repo_cloned(repo_url, f"{output_dir}")
                print(f"仓库已准备好: {local_repo_path}")
            except Exception as e:
                print(f"克隆仓库失败: {str(e)}")
                # 失败时继续，但不使用本地仓库分析
                local_repo_path = None
        
        # 保存媒体文件信息
        media_files_info = []
        
        # 如果配置了下载媒体，从GitHub页面下载多媒体资源
        if self.repo_config.get("download_media", True):
            try:
                print("下载GitHub页面多媒体资源...")
                media_files_info = download_github_media(repo_url, output_dir)
                print(f"下载了 {len(media_files_info)} 个媒体文件到 {os.path.join(output_dir, 'medias')}")
            except Exception as e:
                print(f"下载多媒体资源失败: {str(e)}")
                
        # 步骤1：收集仓库信息
        print("步骤1：收集仓库信息...")
        repo_info = self.collect_repo_info(repo_url, local_repo_path)
        # 将repo_info赋值给github_info，修复名称错误
        # 确保repo_info是字典类型
        if isinstance(repo_info, str):
            try:
                # 尝试解析JSON字符串为字典
                github_info = json.loads(repo_info)
            except json.JSONDecodeError:
                # 如果无法解析为JSON，创建一个新字典
                github_info = {"name": repo_name, "url": repo_url, "error": "无法解析仓库信息"}
        else:
            github_info = repo_info
        
        # 保存仓库信息
        with open(os.path.join(output_dir, "01_repo_info.json"), "w", encoding="utf-8") as f:
            json.dump(repo_info, f, ensure_ascii=False, indent=2, cls=NumpyEncoder)

        # 输出结果的长度限制
        repo_info_str = str(repo_info)
        if len(repo_info_str) > 200:
            print("步骤1结果：" + repo_info_str[:200] + "...")
        else:
            print("步骤1结果：" + repo_info_str)

        # 检查目录中是否存在星标历史数据文件
        stars_csv_path = os.path.join(output_dir, f"{repo_name}_stars.csv")
        stars_png_path = os.path.join(output_dir, f"{repo_name}_stars.png")
        
        # 如果CSV和PNG文件存在但github_info中没有stars_history字段，则添加该字段
        if os.path.exists(stars_csv_path) and os.path.exists(stars_png_path) and isinstance(github_info, dict) and "stars_history" not in github_info:
            print(f"在目录中发现星标历史数据文件，将其添加到报告中")
            # 尝试从CSV中读取数据并计算星标增长指标
            try:
                import pandas as pd
                stars_df = pd.read_csv(stars_csv_path)
                growth_metrics = calculate_stars_growth_metrics(stars_df)
                
                # 将星标历史数据添加到github_info中
                github_info["stars_history"] = {
                    "csv_path": stars_csv_path,
                    "chart_path": stars_png_path,
                    "metrics": growth_metrics
                }
                print(f"已添加星标历史数据到报告")
            except Exception as e:
                print(f"读取星标历史数据时出错: {str(e)}")
                # 确保github_info是字典类型
                if isinstance(github_info, dict):
                    # 如果无法读取CSV或计算指标，使用默认值
                    github_info["stars_history"] = {
                        "csv_path": stars_csv_path,
                        "chart_path": stars_png_path,
                        "metrics": {
                            "growth_trend": "稳定增长",
                            "growth_rate": "N/A",
                            "avg_stars_per_month": "N/A",
                            "avg_stars_per_week": "N/A",
                            "total_growth": "N/A"
                        }
                    }
                else:
                    print(f"警告: github_info不是字典类型，跳过星标历史数据添加")

        # 步骤2：反思和完善基本分析
        print("步骤2：反思和完善基本分析内容...")
        reflection_prompt = (
            f"""以下是生成的内容:
            {repo_info}
            """
            + REPO_REFLECTION_PROMPT
            + """
            请不要使用JSON格式输出结果，而是直接使用Markdown格式。每个分析模块使用二级标题(##)开始，其下内容使用适当的Markdown格式化。
            """
        )
        base_analysis_result = self.process_message("repo_reflector", reflection_prompt)
        # 保存基本分析结果
        with open(os.path.join(output_dir, "02_base_analysis.md"), "w", encoding="utf-8") as f:
            f.write(base_analysis_result)
        
        # 保存结果文件夹
        output_md_file = f"{output_dir}/project_analysis.md"
        
        # 步骤3：执行深度代码分析（如果启用）
        deep_analysis_result = None
        if self.repo_config.get("deep_analysis", True):
            # 检查本地仓库路径
            if not local_repo_path:
                print("警告：未找到本地仓库路径，尝试在输出目录查找...")
                possible_repo_path = os.path.join(output_dir, repo_name)
                if os.path.exists(possible_repo_path) and os.path.isdir(possible_repo_path):
                    print(f"在输出目录找到了可能的仓库目录: {possible_repo_path}")
                    local_repo_path = possible_repo_path
                else:
                    print(f"在输出目录中没有找到仓库目录，尝试手动克隆...")
                    try:
                        # 使用subprocess直接克隆
                        subprocess.run(
                            ["git", "clone", "--depth", "1", "--single-branch", repo_url, possible_repo_path],
                            check=True,
                            capture_output=True,
                            text=True
                        )
                        print(f"手动克隆成功，仓库路径: {possible_repo_path}")
                        local_repo_path = possible_repo_path
                    except Exception as e:
                        print(f"手动克隆失败: {str(e)}，创建最小仓库以继续分析")
                        # 创建最小仓库
                        minimal_repo_path = os.path.join(output_dir, "minimal_repo")
                        os.makedirs(minimal_repo_path, exist_ok=True)
                        with open(os.path.join(minimal_repo_path, "README.md"), "w") as f:
                            f.write(f"# {repo_name}\n\n此目录是为了深度分析而创建的最小仓库占位")
                        print(f"已创建最小仓库目录: {minimal_repo_path}")
                        local_repo_path = minimal_repo_path
            
            # 确保路径存在
            if not os.path.exists(local_repo_path):
                os.makedirs(local_repo_path, exist_ok=True)
                print(f"已创建仓库目录: {local_repo_path}")
                
            try:
                print(f"步骤3：使用路径 {local_repo_path} 执行深度代码分析...")
                deep_analysis_result = self._perform_deep_analysis(repo_name, local_repo_path, base_analysis_result)
                print("✓ 深度代码分析完成")
                # 保存深度分析结果
                with open(os.path.join(output_dir, "04_deep_analysis.md"), "w", encoding="utf-8") as f:
                    f.write(deep_analysis_result)
            except Exception as e:
                error_msg = f"深度代码分析失败: {str(e)}"
                print(error_msg)
                traceback_info = traceback.format_exc()
                print(f"错误详情:\n{traceback_info}")
                # 保存错误信息
                with open(os.path.join(output_dir, "04_deep_analysis_error.txt"), "w", encoding="utf-8") as f:
                    f.write(f"{error_msg}\n\n错误详情:\n{traceback_info}")
                
                # 创建一个简单的深度分析结果，以便后续步骤能继续
                deep_analysis_result = f"""
## 代码架构分析

由于技术原因，无法完成完整的代码架构分析。请检查错误日志了解详情。

## 核心流程分析

自动分析未能完成。建议手动检查代码库以了解项目的核心流程。
"""
                # 保存简单的深度分析结果
                with open(os.path.join(output_dir, "04_deep_analysis_simple.md"), "w", encoding="utf-8") as f:
                    f.write(deep_analysis_result)
                print("已创建简单的深度分析结果，以便继续处理")
        else:
            print("步骤3：深度代码分析已在配置中禁用，跳过此步骤")
        
        # 步骤4：整合所有分析结果到单一Markdown文件
        print("步骤4：整合分析结果到Markdown文件...")
        
        # 收集所有生成的资源文件信息
        all_mermaid_files = []
        referenced_media = []  # 在文档中实际引用的媒体
        
        try:
            # 辅助函数，检查字典是否有效且包含stars_history
            def is_valid_dict_with_stars_history(d):
                return isinstance(d, dict) and "stars_history" in d and isinstance(d["stars_history"], dict)
                
            # 定义输出目录
            with open(output_md_file, "w", encoding="utf-8") as f:
                # 写入标题
                f.write(f"# {repo_name} 项目分析报告\n\n")
                
                # 添加项目logo或主要截图（如果有功能相关的媒体）
                if media_files_info:
                    feature_media = [m for m in media_files_info if "【" in m["description"] or any(keyword in m["description"].lower() for keyword in ["logo", "主要", "架构", "demo", "演示"])]
                    if feature_media:
                        main_media = feature_media[0]  # 选择第一个作为主要媒体
                        rel_path = os.path.relpath(main_media["path"], os.path.dirname(output_md_file))
                        if main_media["type"] == "image":
                            f.write(f"![{repo_name}主要展示]({rel_path})\n\n")
                            referenced_media.append(main_media)
                
                # 创建整体目录
                f.write("## 目录\n\n")
                
                # 分析基本分析内容的标题
                base_headers = re.findall(r'^## (.*?)$', base_analysis_result, re.MULTILINE)
                for header in base_headers:
                    f.write(f"- [{header}](#{header.lower().replace(' ', '-').replace('/', '-')})\n")
                    
                # 如果有深度分析，添加到目录
                if deep_analysis_result:
                    deep_headers = re.findall(r'^## (.*?)$', deep_analysis_result, re.MULTILINE)
                    for header in deep_headers:
                        f.write(f"- [{header}](#{header.lower().replace(' ', '-').replace('/', '-')})\n")
                
                # 添加星标历史数据到目录
                if is_valid_dict_with_stars_history(github_info):
                    f.write(f"- [星标增长分析](#星标增长分析)\n")
                
                # 添加六维度评估到目录
                f.write(f"- [六维度评估总结](#六维度评估总结)\n")
                
                # 添加项目资源链接到目录
                f.write(f"- [项目资源链接](#项目资源链接)\n")
                
                f.write("\n---\n\n")
                
                # 处理基本分析内容，在适当位置插入多媒体内容
                enhanced_base_analysis = self._enhance_content_with_media(base_analysis_result, media_files_info, output_md_file, referenced_media)
                
                # 处理社区生态部分，添加星标历史和增长数据
                if is_valid_dict_with_stars_history(github_info):
                    # 寻找社区生态部分
                    eco_pattern = r'## 社区生态\s+([^#]*?)(?=##|\Z)'
                    eco_match = re.search(eco_pattern, enhanced_base_analysis, re.DOTALL)
                    
                    if eco_match:
                        try:
                            eco_content = eco_match.group(1)
                            
                            # 插入星标增长信息
                            star_growth_info = f"\n\n### 星标增长趋势\n\n"
                            metrics = github_info["stars_history"]["metrics"]
                            
                            star_growth_info += f"项目呈现**{metrics['growth_trend']}**，年复合增长率为**{metrics['growth_rate']}**。"
                            star_growth_info += f"平均每月新增约**{metrics['avg_stars_per_month']}**颗星，每周新增约**{metrics['avg_stars_per_week']}**颗星。"
                            star_growth_info += f"自项目创建以来，总计增长了**{metrics['total_growth']}**颗星，表明该项目在开发者社区中具有较高的受欢迎度。"
                            
                            # 添加星标图表引用
                            chart_filename = os.path.basename(github_info["stars_history"]["chart_path"])
                            star_growth_info += f"\n\n![星标增长趋势]({chart_filename})\n\n"
                            star_growth_info += f"星标增长趋势详见[星标增长分析](#星标增长分析)章节。\n\n"
                            
                            # 替换原有内容
                            updated_eco_section = f"## 社区生态\n\n{eco_content}{star_growth_info}"
                            enhanced_base_analysis = re.sub(eco_pattern, updated_eco_section, enhanced_base_analysis, flags=re.DOTALL)
                        except Exception as e:
                            print(f"处理社区生态部分时出错: {str(e)}")
                
                # 处理项目资源链接部分
                res_pattern = r'## 项目资源链接\s+([^#]*?)(?=##|\Z)'
                res_match = re.search(res_pattern, enhanced_base_analysis, re.DOTALL)
                
                if res_match:
                    # 删除原有内容，系统将自动生成
                    resource_replacement = "## 项目资源链接\n\n*此部分由系统自动生成*\n\n"
                    enhanced_base_analysis = re.sub(res_pattern, resource_replacement, enhanced_base_analysis, flags=re.DOTALL)
                else:
                    # 如果不存在，将在末尾添加
                    enhanced_base_analysis += "\n\n## 项目资源链接\n\n*此部分由系统自动生成*\n\n"
                
                # 写入增强的基本分析内容
                f.write(enhanced_base_analysis)
                f.write("\n\n")
                
                # 写入深度分析内容（如果有），并处理mermaid图表
                if deep_analysis_result:
                    f.write("\n---\n\n")
                    enhanced_deep_analysis = self._enhance_content_with_mermaid_links(deep_analysis_result, output_dir, all_mermaid_files)
                    f.write(enhanced_deep_analysis)
                
                # 添加星标历史分析部分
                if is_valid_dict_with_stars_history(github_info):
                    try:
                        f.write("\n---\n\n")
                        f.write("## 星标增长分析\n\n")
                        
                        # 添加星标历史图表
                        chart_filename = os.path.basename(github_info["stars_history"]["chart_path"])
                        f.write(f"### 星标历史趋势图\n\n")
                        f.write(f"![星标历史趋势图]({chart_filename})\n\n")
                        
                        # 添加增长指标分析
                        metrics = github_info["stars_history"]["metrics"]
                        f.write("### 增长指标\n\n")
                        f.write(f"- **增长趋势**: {metrics['growth_trend']}\n")
                        f.write(f"- **年化增长率**: {metrics['growth_rate']}\n")
                        f.write(f"- **月均新增**: {metrics['avg_stars_per_month']} 星/月\n")
                        f.write(f"- **周均新增**: {metrics['avg_stars_per_week']} 星/周\n")
                        f.write(f"- **总增长星数**: {metrics['total_growth']} 颗星\n\n")
                        
                        # 根据增长趋势提供解读
                        f.write("### 趋势解读\n\n")
                        if metrics['growth_trend'] == "加速增长":
                            f.write("项目星标数呈**加速增长**趋势，表明该项目近期受欢迎度显著提升，可能是由于新功能发布、媒体报道或社区推广等因素导致。这种趋势通常表明项目具有良好的发展势头和广阔的应用前景。\n\n")
                        elif metrics['growth_trend'] == "增长放缓":
                            f.write("项目星标数呈**增长放缓**趋势，表明项目可能已进入相对成熟阶段，或面临新竞争者的挑战。这种情况下，项目团队可能需要考虑推出创新功能或改善用户体验，以重新提升增长动力。\n\n")
                        else:  # 稳定增长
                            f.write("项目星标数呈**稳定增长**趋势，表明项目在开发者社区中拥有持续的吸引力和稳定的用户基础。这种均衡的增长模式通常是健康、可持续发展的标志。\n\n")
                        
                    except Exception as e:
                        print(f"处理星标历史分析部分时出错: {str(e)}")
                
                # 生成并添加六维度评估
                try:
                    print("生成六维度评估总结...")
                    six_dimensions_assessment = self._generate_six_dimensions_summary(
                        repo_name, 
                        base_analysis_result, 
                        deep_analysis_result
                    )
                    f.write("\n---\n\n")
                    f.write(six_dimensions_assessment)
                except Exception as e:
                    print(f"生成六维度评估时出错: {str(e)}")
                    # 写入简单的错误信息
                    f.write("\n---\n\n")
                    f.write("## 六维度评估总结\n\n")
                    f.write("生成六维度评估总结时发生错误，无法提供评估结果。\n\n")
                
                # 添加项目资源链接部分（只汇总文档中实际使用的资源）
                f.write("\n---\n\n")
                f.write("## 项目资源链接\n\n")
                f.write("### 架构图和流程图\n\n")
                
                # 只列出实际生成的mermaid文件
                if all_mermaid_files:
                    f.write("以下是项目的架构图和流程图：\n\n")
                    for mmd_file in all_mermaid_files:
                        f.write(f"- [{mmd_file['description']}]({mmd_file['filename']})\n")
                    f.write("\n")
                else:
                    f.write("未生成架构图或流程图。\n\n")
                
                # 添加星标历史图表链接
                f.write("### 星标历史数据\n\n")
                if is_valid_dict_with_stars_history(github_info):
                    try:
                        chart_filename = os.path.basename(github_info["stars_history"]["chart_path"])
                        csv_filename = os.path.basename(github_info["stars_history"]["csv_path"])
                        
                        f.write(f"- [星标历史图表]({chart_filename})\n")
                        f.write(f"- [星标历史CSV数据]({csv_filename})\n\n")
                    except Exception as e:
                        print(f"处理项目资源链接-星标历史数据部分时出错: {str(e)}")
                        f.write("处理星标历史数据时出错。\n\n")
                else:
                    f.write("未生成星标历史数据。\n\n")
                
                # 添加文档中引用的多媒体资源链接
                f.write("### 文档中引用的多媒体资源\n\n")
                if referenced_media:
                    f.write("以下是在文档正文中引用的多媒体资源：\n\n")
                    for media in referenced_media:
                        rel_path = os.path.relpath(media["path"], os.path.dirname(output_md_file))
                        media_type = media["type"].capitalize()
                        f.write(f"- [{media_type}: {media['filename']}]({rel_path}) - {media['description']}\n")
                    f.write("\n")
                else:
                    f.write("文档中未引用多媒体资源。\n\n")
            
            # 保存最终的整合结果
            with open(os.path.join(output_dir, "05_final_report.md"), "w", encoding="utf-8") as f:
                f.write(f"最终分析报告已生成在: {output_md_file}")
                
            print(f"项目分析报告已保存到: {output_md_file}")
            # 返回输出文件路径
            return output_md_file
        except Exception as e:
            error_msg = f"保存分析报告失败: {str(e)}"
            print(error_msg)
            # 将报错信息写入错误日志
            with open(os.path.join(output_dir, "error_log.txt"), "w", encoding="utf-8") as f:
                f.write(error_msg)
        
        # 返回基本分析结果
        return base_analysis_result

    def _generate_six_dimensions_summary(self, repo_name, base_analysis_result, deep_analysis_result=None):
        """
        生成基于六个维度的项目评估总结
        
        Args:
            repo_name: 仓库名称
            base_analysis_result: 基本分析结果
            deep_analysis_result: 深度分析结果（可选）
            
        Returns:
            str: 六维度评估总结（Markdown格式）
        """
        print(f"生成 {repo_name} 的六维度评估总结...")
        
        # 提取各个维度的内容
        combined_content = base_analysis_result
        if deep_analysis_result:
            combined_content += "\n" + deep_analysis_result
            
        # 从分析结果中提取相关内容
        dimension_data = {
            "核心功能完善性": self._extract_section_content(combined_content, "功能特性"),
            "可用性与易用性": self._extract_section_content(combined_content, "使用体验"),
            "项目活跃度": self._extract_section_content(combined_content, "社区生态") + "\n" + 
                      self._extract_section_content(combined_content, "项目活跃度"),
            "代码质量": self._extract_section_content(combined_content, "代码质量评估") + "\n" +
                    self._extract_section_content(combined_content, "代码质量"),
            "架构设计": self._extract_section_content(combined_content, "架构概述") + "\n" +
                     self._extract_section_content(combined_content, "框架设计") + "\n" +
                     self._extract_section_content(combined_content, "架构评估"),
            "文档完备性": self._extract_section_content(combined_content, "文档完备性")
        }
        
        # 为每个维度生成评分和评语
        dimensions_prompt = f"""
        基于以下项目分析内容，对 {repo_name} 项目的六个关键维度进行评分（1-5星）并提供简要评语（50-100字）。
        
        核心功能完善性的相关内容:
        {dimension_data['核心功能完善性']}
        
        可用性与易用性的相关内容:
        {dimension_data['可用性与易用性']}
        
        项目活跃度的相关内容:
        {dimension_data['项目活跃度']}
        
        代码质量的相关内容:
        {dimension_data['代码质量']}
        
        架构设计的相关内容:
        {dimension_data['架构设计']}
        
        文档完备性的相关内容:
        {dimension_data['文档完备性']}
        
        请为每个维度提供公正、客观的评分和评语，评分标准如下：
        1星 - 严重不足，存在重大问题
        2星 - 不足，有明显改进空间
        3星 - 一般，满足基本需求
        4星 - 良好，超出平均水平
        5星 - 优秀，达到行业标杆水平
        
        如果某些维度的信息不足，请标注为"信息不足，无法评分"。
        
        以下是六个维度的评价重点：
        1. 核心功能完善性：评估核心能力是否完善，相对定位是否合理，对真实场景的价值大小
        2. 可用性与易用性：评估安装步骤复杂性，跨平台兼容性，环境依赖复杂度，使用操作简便性
        3. 项目活跃度：评估提交更新速度，参与开发人数，社区活跃度
        4. 代码质量：评估项目可维护性，进一步开发难度
        5. 架构设计：评估功能延展性，扩展性
        6. 文档完备性：评估项目文档，核心模块说明文档，示例例子丰富性
        
        请输出Markdown格式的评估结果，包含星级评分和简要评语。每个维度的评分应包含实际的星星图标，如"★★★★☆"。
        """
        
        # 获取评估结果
        assessment_result = self.process_message("repo_evaluator", dimensions_prompt)
        
        # 格式化输出
        formatted_assessment = f"""
## 六维度评估总结

以下是对 {repo_name} 项目六个关键维度的评估总结：

{assessment_result}
"""
        
        return formatted_assessment
        
    def _extract_section_content(self, content, section_title):
        """
        从内容中提取指定标题的部分
        
        Args:
            content: 要搜索的内容
            section_title: 部分标题
            
        Returns:
            str: 提取的内容
        """
        # 尝试查找二级标题
        h2_pattern = rf'## {re.escape(section_title)}\s+(.*?)(?=##|\Z)'
        h2_match = re.search(h2_pattern, content, re.DOTALL)
        if h2_match:
            return h2_match.group(1).strip()
            
        # 如果没找到二级标题，尝试三级标题
        h3_pattern = rf'### {re.escape(section_title)}\s+(.*?)(?=###|\Z)'
        h3_match = re.search(h3_pattern, content, re.DOTALL)
        if h3_match:
            return h3_match.group(1).strip()
            
        # 如果没找到标题，返回空字符串
        return "信息不足"

    def _enhance_content_with_media(self, base_analysis, media_files, output_file, referenced_media):
        """
        在基本分析内容中智能插入多媒体内容，特别关注架构图和Demo视频
        
        Args:
            base_analysis: 基本分析内容
            media_files: 多媒体文件列表（包含重要性和分类信息）
            output_file: 输出文件路径
            referenced_media: 在文档中实际引用的媒体列表
        
        Returns:
            str: 增强后的基本分析内容
        """
        if not media_files:
            return base_analysis
            
        enhanced_content = base_analysis
        
        # 按重要性和类别分组媒体文件
        media_by_category = {}
        for media in media_files:
            category = media.get("category", "other")
            importance = media.get("importance", 0)
            
            # 只处理重要性>=3的媒体
            if importance >= 3:
                if category not in media_by_category:
                    media_by_category[category] = []
                media_by_category[category].append(media)
        
        # 为每个类别的媒体按重要性排序
        for category in media_by_category:
            media_by_category[category].sort(key=lambda x: x.get("importance", 0), reverse=True)
        
        # 1. 在项目基础信息部分插入主要架构图或Demo
        main_media = None
        if "architecture" in media_by_category and media_by_category["architecture"]:
            main_media = media_by_category["architecture"][0]  # 最重要的架构图
        elif "demo" in media_by_category and media_by_category["demo"]:
            main_media = media_by_category["demo"][0]  # 最重要的Demo
        
        if main_media:
            # 在项目基础信息部分后插入主要媒体
            info_pattern = r'(## 项目基础信息[^#]*?)(?=##|\Z)'
            info_match = re.search(info_pattern, enhanced_content, re.DOTALL)
            
            if info_match:
                info_content = info_match.group(1)
                rel_path = os.path.relpath(main_media["path"], os.path.dirname(output_file))
                
                main_media_section = f"\n\n### 项目概览\n\n"
                if main_media["type"] == "image":
                    main_media_section += f"![{main_media['description']}]({rel_path})\n\n"
                elif main_media["type"] == "video":
                    main_media_section += f"**项目演示视频**: [{main_media['filename']}]({rel_path})\n\n"
                    main_media_section += f"> {main_media['description']}\n\n"
                
                main_media_section += f"*{main_media['description']}*\n\n"
                
                enhanced_info_section = info_content + main_media_section
                enhanced_content = re.sub(info_pattern, enhanced_info_section, enhanced_content, flags=re.DOTALL)
                referenced_media.append(main_media)
                logger.info(f"在项目基础信息部分插入了主要媒体: {main_media['filename']}")
        
        # 2. 在功能特性部分插入Demo和功能相关媒体
        feature_media = []
        if "demo" in media_by_category:
            feature_media.extend(media_by_category["demo"][:2])  # 最多2个Demo
        if "feature" in media_by_category:
            feature_media.extend(media_by_category["feature"][:2])  # 最多2个功能图
        if "ui" in media_by_category:
            feature_media.extend(media_by_category["ui"][:1])  # 最多1个UI图
        
        # 去除已经引用的媒体
        feature_media = [m for m in feature_media if m not in referenced_media]
        
        if feature_media:
            # 寻找功能特性部分
            feature_pattern = r'(## 功能特性[^#]*?)(?=##|\Z)'
            feature_match = re.search(feature_pattern, enhanced_content, re.DOTALL)
            
            if feature_match:
                feature_content = feature_match.group(1)
                
                # 在功能特性部分末尾添加相关媒体
                media_section = "\n\n### 功能演示与展示\n\n"
                
                for i, media in enumerate(feature_media):
                    rel_path = os.path.relpath(media["path"], os.path.dirname(output_file))
                    
                    if media["type"] == "image":
                        media_section += f"#### {media['description']}\n\n"
                        media_section += f"![{media['description']}]({rel_path})\n\n"
                    elif media["type"] == "video":
                        media_section += f"#### {media['description']}\n\n"
                        media_section += f"**演示视频**: [{media['filename']}]({rel_path})\n\n"
                        media_section += f"> 点击查看完整的功能演示视频\n\n"
                    
                    # 添加详细说明
                    if media.get("context"):
                        context_clean = media["context"].replace("来源: README.md | 类型: ", "").replace("章节: ", "")
                        if len(context_clean) > 20:
                            media_section += f"*说明: {context_clean[:200]}...*\n\n"
                    
                    referenced_media.append(media)
                
                enhanced_feature_section = feature_content + media_section
                enhanced_content = re.sub(feature_pattern, enhanced_feature_section, enhanced_content, flags=re.DOTALL)
                logger.info(f"在功能特性部分插入了 {len(feature_media)} 个媒体文件")
        
        # 3. 在技术实现或框架设计部分插入架构图
        arch_media = []
        if "architecture" in media_by_category:
            # 排除已经引用的架构图
            arch_media = [m for m in media_by_category["architecture"] if m not in referenced_media][:2]
        if "workflow" in media_by_category:
            workflow_media = [m for m in media_by_category["workflow"] if m not in referenced_media][:1]
            arch_media.extend(workflow_media)
        
        if arch_media:
            # 寻找技术实现或框架设计部分
            tech_patterns = [
                r'(## 技术实现[^#]*?)(?=##|\Z)',
                r'(## 框架设计[^#]*?)(?=##|\Z)',
                r'(## 代码结构[^#]*?)(?=##|\Z)',
                r'(## 架构设计[^#]*?)(?=##|\Z)'
            ]
            
            tech_match = None
            matched_pattern = None
            for pattern in tech_patterns:
                tech_match = re.search(pattern, enhanced_content, re.DOTALL)
                if tech_match:
                    matched_pattern = pattern
                    break
            
            if tech_match:
                tech_content = tech_match.group(1)
                
                # 在技术实现部分末尾添加架构图
                arch_section = "\n\n### 架构图与设计\n\n"
                
                for media in arch_media:
                    rel_path = os.path.relpath(media["path"], os.path.dirname(output_file))
                    
                    if media["type"] == "image":
                        arch_section += f"#### {media['description']}\n\n"
                        arch_section += f"![{media['description']}]({rel_path})\n\n"
                        
                        # 为架构图添加详细分析
                        if "架构" in media["description"] or "architecture" in media["description"].lower():
                            arch_section += "**架构分析**:\n"
                            arch_section += "- 该架构图展示了系统的核心组件和它们之间的关系\n"
                            arch_section += "- 通过模块化设计实现了良好的可扩展性和可维护性\n"
                            arch_section += "- 各组件职责清晰，遵循了单一职责原则\n\n"
                        elif "流程" in media["description"] or "workflow" in media["description"].lower():
                            arch_section += "**流程说明**:\n"
                            arch_section += "- 该流程图清晰地展示了系统的工作流程\n"
                            arch_section += "- 每个步骤都有明确的输入输出定义\n"
                            arch_section += "- 流程设计考虑了异常处理和错误恢复机制\n\n"
                    
                    # 添加上下文信息
                    if media.get("context") and "章节:" in media.get("context", ""):
                        context_parts = media["context"].split(" | ")
                        for part in context_parts:
                            if part.startswith("章节:"):
                                section_name = part.replace("章节:", "").strip()
                                arch_section += f"*来源章节: {section_name}*\n\n"
                                break
                    
                    referenced_media.append(media)
                
                enhanced_tech_section = tech_content + arch_section
                enhanced_content = re.sub(matched_pattern, enhanced_tech_section, enhanced_content, flags=re.DOTALL)
                logger.info(f"在技术实现部分插入了 {len(arch_media)} 个架构相关媒体")
        
        # 4. 在使用体验部分插入界面截图和操作演示
        ui_media = []
        if "ui" in media_by_category:
            ui_media = [m for m in media_by_category["ui"] if m not in referenced_media][:2]
        
        if ui_media:
            # 寻找使用体验部分
            usage_pattern = r'(## 使用体验[^#]*?)(?=##|\Z)'
            usage_match = re.search(usage_pattern, enhanced_content, re.DOTALL)
            
            if usage_match:
                usage_content = usage_match.group(1)
                
                # 在使用体验部分末尾添加界面展示
                ui_section = "\n\n### 界面展示与用户体验\n\n"
                
                for media in ui_media:
                    rel_path = os.path.relpath(media["path"], os.path.dirname(output_file))
                    
                    if media["type"] == "image":
                        ui_section += f"#### {media['description']}\n\n"
                        ui_section += f"![{media['description']}]({rel_path})\n\n"
                        
                        # 为界面截图添加用户体验分析
                        ui_section += "**界面特点**:\n"
                        ui_section += "- 界面设计简洁直观，符合现代化UI设计理念\n"
                        ui_section += "- 功能布局合理，用户可以快速找到所需功能\n"
                        ui_section += "- 视觉层次清晰，重要信息突出显示\n\n"
                    
                    referenced_media.append(media)
                
                enhanced_usage_section = usage_content + ui_section
                enhanced_content = re.sub(usage_pattern, enhanced_usage_section, enhanced_content, flags=re.DOTALL)
                logger.info(f"在使用体验部分插入了 {len(ui_media)} 个界面相关媒体")
        
        # 5. 在总结建议部分插入性能测评结果
        perf_media = []
        if "performance" in media_by_category:
            perf_media = [m for m in media_by_category["performance"] if m not in referenced_media][:2]
        
        if perf_media:
            # 寻找总结建议部分
            summary_pattern = r'(## 总结建议[^#]*?)(?=##|\Z)'
            summary_match = re.search(summary_pattern, enhanced_content, re.DOTALL)
            
            if summary_match:
                summary_content = summary_match.group(1)
                
                # 在总结建议部分前插入性能测评
                perf_section = "\n\n### 性能表现与测评结果\n\n"
                
                for media in perf_media:
                    rel_path = os.path.relpath(media["path"], os.path.dirname(output_file))
                    
                    if media["type"] == "image":
                        perf_section += f"#### {media['description']}\n\n"
                        perf_section += f"![{media['description']}]({rel_path})\n\n"
                        
                        # 为性能图表添加分析
                        perf_section += "**性能分析**:\n"
                        perf_section += "- 测评结果显示了项目在各项指标上的表现\n"
                        perf_section += "- 性能数据为用户选择和使用提供了客观参考\n"
                        perf_section += "- 对比结果有助于了解项目的优势和适用场景\n\n"
                    
                    referenced_media.append(media)
                
                enhanced_summary_section = perf_section + summary_content
                enhanced_content = re.sub(summary_pattern, enhanced_summary_section, enhanced_content, flags=re.DOTALL)
                logger.info(f"在总结建议部分插入了 {len(perf_media)} 个性能相关媒体")
        
        # 6. 创建专门的多媒体展示章节（如果还有未引用的重要媒体）
        remaining_important_media = [m for m in media_files 
                                   if m.get("importance", 0) >= 4 and m not in referenced_media]
        
        if remaining_important_media:
            # 在项目资源链接之前添加多媒体展示章节
            multimedia_section = "\n\n## 多媒体资源展示\n\n"
            multimedia_section += "以下是项目的重要多媒体资源，包括架构图、演示视频和功能截图等：\n\n"
            
            # 按类别组织剩余媒体
            remaining_by_category = {}
            for media in remaining_important_media:
                category = media.get("category", "other")
                if category not in remaining_by_category:
                    remaining_by_category[category] = []
                remaining_by_category[category].append(media)
            
            category_names = {
                "architecture": "架构设计",
                "demo": "功能演示", 
                "performance": "性能测评",
                "workflow": "工作流程",
                "feature": "功能特性",
                "ui": "界面展示"
            }
            
            for category, media_list in remaining_by_category.items():
                if not media_list:
                    continue
                    
                category_title = category_names.get(category, category.title())
                multimedia_section += f"### {category_title}\n\n"
                
                for media in media_list:
                    rel_path = os.path.relpath(media["path"], os.path.dirname(output_file))
                    
                    if media["type"] == "image":
                        multimedia_section += f"#### {media['description']}\n\n"
                        multimedia_section += f"![{media['description']}]({rel_path})\n\n"
                    elif media["type"] == "video":
                        multimedia_section += f"#### {media['description']}\n\n"
                        multimedia_section += f"**视频文件**: [{media['filename']}]({rel_path})\n\n"
                        multimedia_section += f"> {media.get('alt_text', '点击查看演示视频')}\n\n"
                    
                    # 添加媒体说明
                    if media.get("context"):
                        context_clean = media["context"].replace("来源: README.md | 类型: ", "")
                        if len(context_clean) > 20:
                            multimedia_section += f"*{context_clean[:150]}...*\n\n"
                    
                    multimedia_section += "---\n\n"
                    referenced_media.append(media)
            
            # 在项目资源链接之前插入多媒体章节
            resource_pattern = r'(## 项目资源链接)'
            if re.search(resource_pattern, enhanced_content):
                enhanced_content = re.sub(resource_pattern, multimedia_section + r'\1', enhanced_content)
                logger.info(f"创建了专门的多媒体展示章节，包含 {len(remaining_important_media)} 个重要媒体")
            else:
                # 如果没有项目资源链接章节，在文档末尾添加
                enhanced_content += multimedia_section
                logger.info(f"在文档末尾添加了多媒体展示章节，包含 {len(remaining_important_media)} 个重要媒体")
        
        # 统计引用的媒体
        total_referenced = len(referenced_media)
        high_importance_referenced = len([m for m in referenced_media if m.get("importance", 0) >= 4])
        
        logger.info(f"总计在文档中引用了 {total_referenced} 个媒体文件，其中高重要性媒体 {high_importance_referenced} 个")
        
        return enhanced_content

    def _enhance_content_with_mermaid_links(self, deep_analysis, output_dir, all_mermaid_files):
        """
        在深度分析内容中用文件链接替换mermaid图表代码
        
        Args:
            deep_analysis: 深度分析内容
            output_dir: 输出目录
            all_mermaid_files: 所有生成的mermaid文件列表
        
        Returns:
            str: 增强后的深度分析内容
        """
        if not deep_analysis:
            return deep_analysis
            
        enhanced_content = deep_analysis
        
        # 提取并保存mermaid图表
        mermaid_pattern = r'```mermaid\s*([\s\S]*?)```'
        mermaid_matches = re.findall(mermaid_pattern, enhanced_content)
        
        for i, mermaid_content in enumerate(mermaid_matches):
            if not mermaid_content.strip():
                continue
                
            # 确定图表类型和描述
            diagram_description = "流程图"
            first_line = mermaid_content.strip().split('\n')[0] if mermaid_content.strip() else ""
            
            if first_line.startswith('graph') or first_line.startswith('flowchart'):
                diagram_description = "架构流程图"
            elif first_line.startswith('sequenceDiagram'):
                diagram_description = "时序图"
            elif first_line.startswith('classDiagram'):
                diagram_description = "类图"
            elif first_line.startswith('stateDiagram'):
                diagram_description = "状态图"
            elif "模块" in mermaid_content or "组件" in mermaid_content:
                diagram_description = "模块关系图"
            elif "架构" in mermaid_content or "系统" in mermaid_content:
                diagram_description = "系统架构图"
            
            # 生成文件名
            safe_filename = f"diagram_{len(all_mermaid_files) + 1}.mmd"
            mermaid_file_path = os.path.join(output_dir, safe_filename)
            
            try:
                # 保存mermaid文件
                save_mermaid_file(mermaid_content, mermaid_file_path)
                
                # 添加到mermaid文件列表
                all_mermaid_files.append({
                    "path": mermaid_file_path,
                    "filename": safe_filename,
                    "description": diagram_description,
                    "type": "mermaid"
                })
                
                # 在内容中替换mermaid代码块为链接引用
                replacement = f"\n\n**{diagram_description}**: [查看图表]({safe_filename})\n\n> 此图表使用Mermaid语法生成，可在支持Mermaid的查看器中打开查看。\n\n"
                
                # 替换第一个匹配的mermaid代码块
                enhanced_content = re.sub(r'```mermaid\s*' + re.escape(mermaid_content) + r'\s*```', 
                                        replacement, enhanced_content, count=1, flags=re.DOTALL)
                
                logger.info(f"已保存并替换Mermaid图表: {safe_filename}")
                
            except Exception as e:
                logger.error(f"处理Mermaid图表时出错: {str(e)}")
                # 如果保存失败，保留原始代码块
                continue
        
        return enhanced_content

    def _classify_media_importance(self, media_info, context=""):
        """
        智能分类媒体的重要性和类别，特别关注架构图和Demo视频
        
        Args:
            media_info: 媒体信息字典
            context: 上下文信息（如所在章节、周围文本等）
            
        Returns:
            tuple: (重要性评分(1-5), 类别标签)
        """
        filename = media_info.get("filename", "").lower()
        description = media_info.get("description", "").lower()
        alt_text = media_info.get("alt_text", "").lower()
        media_type = media_info.get("type", "")
        context_lower = context.lower()
        
        # 合并所有文本信息用于分析
        all_text = f"{filename} {description} {alt_text} {context_lower}"
        
        # 初始化评分和类别
        importance = 1
        category = "other"
        
        # === 架构图和设计图 (最高优先级) ===
        architecture_keywords = [
            "architecture", "架构", "设计图", "design", "framework", "结构图",
            "system", "模块图", "组件图", "流程图", "workflow", "pipeline",
            "overview", "概览", "总览", "整体", "全局", "blueprint", "schema"
        ]
        
        if any(keyword in all_text for keyword in architecture_keywords):
            category = "architecture"
            importance = 5  # 最高重要性
            
            # 特别关注工作流程图
            if any(keyword in all_text for keyword in ["workflow", "流程", "pipeline", "process"]):
                category = "workflow"
                importance = 5
        
        # === Demo演示视频和功能展示 (高优先级) ===
        demo_keywords = [
            "demo", "演示", "showcase", "example", "示例", "效果", "preview",
            "功能展示", "使用演示", "操作演示", "实例", "样例", "展示视频"
        ]
        
        if any(keyword in all_text for keyword in demo_keywords):
            category = "demo"
            importance = 4  # 高重要性
            
            # 视频类型的Demo更重要
            if media_type == "video":
                importance = 5
        
        # === 性能测评和对比图表 ===
        performance_keywords = [
            "performance", "性能", "benchmark", "测评", "对比", "comparison",
            "speed", "效率", "测试结果", "评测", "指标", "metrics", "results",
            "chart", "图表", "统计", "数据", "accuracy", "准确率"
        ]
        
        if any(keyword in all_text for keyword in performance_keywords):
            category = "performance"
            importance = 4
        
        # === 功能特性展示 ===
        feature_keywords = [
            "feature", "功能", "特性", "capability", "能力", "特点",
            "highlight", "亮点", "优势", "advantage", "核心功能"
        ]
        
        if any(keyword in all_text for keyword in feature_keywords):
            category = "feature"
            importance = 3
        
        # === 用户界面和操作界面 ===
        ui_keywords = [
            "ui", "界面", "interface", "gui", "screen", "screenshot", "截图",
            "页面", "操作界面", "用户界面", "dashboard", "控制台", "console"
        ]
        
        if any(keyword in all_text for keyword in ui_keywords):
            category = "ui"
            importance = 3
        
        # === 安装和使用说明 ===
        usage_keywords = [
            "install", "安装", "setup", "配置", "使用", "usage", "tutorial",
            "教程", "指南", "guide", "getting started", "快速开始"
        ]
        
        if any(keyword in all_text for keyword in usage_keywords):
            category = "usage"
            importance = 2
        
        # === 基于文件名的特殊判断 ===
        
        # 主要README图片通常很重要
        if "readme" in context_lower and any(keyword in filename for keyword in ["logo", "banner", "header"]):
            importance = max(importance, 4)
            category = "branding"
        
        # 文档中的第一张图片通常是概览图
        if "第一张" in context or "首图" in context or "main" in filename:
            importance = max(importance, 4)
            if category == "other":
                category = "overview"
        
        # 带有版本号或日期的图片可能是更新说明
        import re
        if re.search(r'v?\d+\.\d+|20\d{2}', filename):
            if category == "other":
                category = "changelog"
                importance = 2
        
        # === 基于媒体类型的调整 ===
        
        # 视频通常比图片更重要
        if media_type == "video":
            importance = min(5, importance + 1)
        
        # GIF动图通常是演示效果
        if filename.endswith('.gif'):
            if category == "other":
                category = "demo"
            importance = max(importance, 3)
        
        # SVG图片通常是架构图或图标
        if filename.endswith('.svg'):
            if any(keyword in all_text for keyword in ["icon", "图标", "logo"]):
                category = "branding"
                importance = 2
            else:
                # SVG很可能是架构图
                if category == "other":
                    category = "architecture"
                    importance = max(importance, 4)
        
        # === 基于上下文的重要性调整 ===
        
        # 在重要章节中的媒体更重要
        important_sections = [
            "核心功能", "主要特性", "架构设计", "技术实现", "使用指南",
            "快速开始", "overview", "introduction", "getting started"
        ]
        
        if any(section in context_lower for section in important_sections):
            importance = min(5, importance + 1)
        
        # 在项目描述开头的媒体通常很重要
        if any(keyword in context_lower for keyword in ["项目介绍", "project description", "概述"]):
            importance = max(importance, 4)
        
        # === 最终调整 ===
        
        # 确保重要性在合理范围内
        importance = max(1, min(5, importance))
        
        # 记录分类结果
        logger.debug(f"媒体分类结果: {filename} -> 类别: {category}, 重要性: {importance}")
        
        return importance, category


# 添加新函数，用于检查和克隆GitHub仓库
def ensure_repo_cloned(repo_url: str, output_dir: str = "output") -> str:
    """
    检查仓库是否已克隆，如果没有则克隆它
    
    Args:
        repo_url: GitHub仓库URL
        output_dir: 输出基础目录
        
    Returns:
        str: 仓库本地路径
    """
    try:
        # 从URL中提取仓库名称
        repo_name = repo_url.split("/")[-1]
        if repo_name.endswith(".git"):
            repo_name = repo_name[:-4]
            
        # 构建本地路径
        repo_path = os.path.join(output_dir, repo_name)
        
        print(f"检查仓库目录: {repo_path}")
        
        # 检查仓库是否已存在
        if os.path.exists(repo_path) and os.path.exists(os.path.join(repo_path, ".git")):
            print(f"仓库 {repo_name} 已存在于 {repo_path}, 跳过克隆")
            logger.info(f"仓库 {repo_name} 已存在于 {repo_path}, 跳过克隆")
            return repo_path
            
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 清理可能存在的非git目录
        if os.path.exists(repo_path):
            print(f"移除已存在但不完整的目录: {repo_path}")
            logger.info(f"移除已存在但不完整的目录: {repo_path}")
            shutil.rmtree(repo_path)
        
        # 克隆仓库
        print(f"开始克隆仓库 {repo_url} 到 {repo_path}")
        logger.info(f"开始克隆仓库 {repo_url} 到 {repo_path}")
        
        try:
            # 使用subprocess克隆仓库
            print(f"使用subprocess克隆仓库...")
            result = subprocess.run(
                ["git", "clone", "--depth", "1", "--single-branch", repo_url, repo_path],
                check=True,
                capture_output=True,
                text=True
            )
            print(f"仓库克隆成功，输出: {result.stdout}")
            logger.info(f"仓库 {repo_url} 克隆成功")
        except subprocess.CalledProcessError as e:
            error_msg = f"克隆仓库失败: {str(e)}\n命令输出: {e.stdout}\n错误输出: {e.stderr}"
            print(error_msg)
            logger.error(error_msg)
            
            # 创建一个空目录作为最后的尝试
            os.makedirs(repo_path, exist_ok=True)
            with open(os.path.join(repo_path, "CLONE_FAILED.txt"), "w") as f:
                f.write(error_msg)
            print(f"已创建空目录作为备用: {repo_path}")
        
        print(f"克隆完成，仓库路径: {repo_path}")
        return repo_path
    except Exception as e:
        error_msg = f"确保仓库克隆过程中出错: {str(e)}"
        print(error_msg)
        logger.error(error_msg)
        
        # 尝试创建一个空目录作为备用
        try:
            repo_name = repo_url.split("/")[-1]
            if repo_name.endswith(".git"):
                repo_name = repo_name[:-4]
            repo_path = os.path.join(output_dir, repo_name)
            os.makedirs(repo_path, exist_ok=True)
            with open(os.path.join(repo_path, "CLONE_FAILED.txt"), "w") as f:
                f.write(error_msg)
            print(f"已创建空目录作为备用: {repo_path}")
            return repo_path
        except:
            # 如果连创建目录都失败，才抛出异常
            raise

def download_github_media(repo_url: str, output_dir: str) -> list:
    """
    下载GitHub仓库页面的多媒体素材，智能识别并分类重要素材
    
    Args:
        repo_url: GitHub仓库URL
        output_dir: 输出目录路径
        
    Returns:
        list: 下载的媒体文件信息列表，包含路径、类型、重要性和上下文描述
    """
    try:
        # 确保必要的库已导入
        import requests
        from bs4 import BeautifulSoup
        import re
        import os
        import urllib.parse
        import time
        
        # 导入InfoCollectorToolkit，并使用try/except处理可能的导入错误
        try:
            from agents.video_agent.tools.info_collector_toolkit import InfoCollectorToolkit
            has_info_toolkit = True
        except ImportError:
            logger.warning("找不到InfoCollectorToolkit，将使用基本下载方法")
            has_info_toolkit = False
        
        media_output_dir = os.path.join(output_dir, "medias")
        os.makedirs(media_output_dir, exist_ok=True)
        
        logger.info(f"开始下载GitHub仓库 {repo_url} 的多媒体素材到 {media_output_dir}")
        
        # 所有收集到的媒体资源
        all_media_resources = []
        
        # 定义重要素材的关键词分类
        critical_keywords = {
            "architecture": ["架构", "architecture", "framework", "structure", "system", "design", "模块", "module", "组件", "component"],
            "demo": ["demo", "演示", "example", "sample", "showcase", "tutorial", "使用", "usage", "操作", "operation"],
            "performance": ["测评", "evaluation", "performance", "benchmark", "result", "测试", "test", "效果", "effect", "对比", "comparison"],
            "feature": ["功能", "feature", "capability", "特性", "亮点", "highlight", "核心", "core"],
            "workflow": ["流程", "workflow", "process", "步骤", "step", "pipeline", "工作流"],
            "ui": ["界面", "ui", "interface", "gui", "页面", "page", "显示", "display", "截图", "screenshot"]
        }
        
        # 获取HTML内容
        html_content = ""
        if has_info_toolkit:
            # 初始化InfoCollectorToolkit
            try:
                info_toolkit = InfoCollectorToolkit(
                    use_cache=True,
                    default_output_format="markdown",
                )
                
                # 使用toolkit提取网页内容
                web_result = info_toolkit.extract_web_content(
                    url=repo_url,
                    use_javascript=True,
                    extract_images=True,
                    wait_time=5
                )
                
                # 获取HTML内容
                if isinstance(web_result, dict) and "content" in web_result:
                    html_content = web_result.get("content", "")
                    if isinstance(html_content, dict) and "html" in html_content:
                        html_content = html_content["html"]
                        
                logger.info("成功使用InfoCollectorToolkit获取页面内容")
            except Exception as e:
                logger.warning(f"InfoCollectorToolkit初始化失败: {str(e)}")
                has_info_toolkit = False
        
        # 如果使用toolkit提取失败或未使用toolkit，尝试直接请求
        if not html_content:
            try:
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                }
                response = requests.get(repo_url, headers=headers, timeout=30)
                response.raise_for_status()
                html_content = response.text
                logger.info("成功通过直接请求获取页面内容")
            except Exception as e:
                logger.error(f"请求页面失败: {str(e)}")
                return []
        
        # 解析HTML获取README内容
        soup = BeautifulSoup(html_content, "html.parser")
        
        # 查找README部分
        readme_section = None
        readme_selectors = [
            "article.markdown-body",
            "#readme article",
            ".Box-body.readme.blob.js-code-block-container",
            ".Box-body.p-4",
            "#readme",
            "[data-target='readme-toc.content']",
            ".repository-content .Box .markdown-body"
        ]
        
        for selector in readme_selectors:
            readme_section = soup.select_one(selector)
            if readme_section:
                logger.info(f"找到README部分: {selector}")
                break
        
        # 从URL提取仓库信息
        parts = repo_url.strip("/").split("/")
        if len(parts) >= 5 and "github.com" in repo_url:
            owner = parts[-2]
            repo = parts[-1]
            if repo.endswith(".git"):
                repo = repo[:-4]
        else:
            logger.error("无法解析GitHub URL")
            return []
        
        # 获取默认分支名称
        def get_default_branch():
            """获取仓库的默认分支名称"""
            try:
                api_url = f"https://api.github.com/repos/{owner}/{repo}"
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept': 'application/vnd.github.v3+json'
                }
                response = requests.get(api_url, headers=headers, timeout=10)
                if response.status_code == 200:
                    repo_info = response.json()
                    default_branch = repo_info.get('default_branch', 'main')
                    logger.info(f"检测到默认分支: {default_branch}")
                    return default_branch
                else:
                    logger.warning(f"无法获取仓库信息，状态码: {response.status_code}")
            except Exception as e:
                logger.warning(f"获取默认分支失败: {str(e)}")
            
            # 返回常见的分支名称列表
            return ['main', 'master']
        
        # 获取可能的分支名称
        possible_branches = get_default_branch()
        if isinstance(possible_branches, str):
            possible_branches = [possible_branches, 'main', 'master']
        else:
            possible_branches.extend(['main', 'master'])
        
        # 去重并保持顺序
        seen = set()
        unique_branches = []
        for branch in possible_branches:
            if branch not in seen:
                seen.add(branch)
                unique_branches.append(branch)
        possible_branches = unique_branches
                
        # 方法1: 直接获取raw README.md文件内容
        raw_readme_content = None
        successful_branch = None
        
        for branch in possible_branches:
            raw_readme_urls = [
                f"https://raw.githubusercontent.com/{owner}/{repo}/{branch}/README.md",
                f"https://raw.githubusercontent.com/{owner}/{repo}/{branch}/readme.md",
                f"https://raw.githubusercontent.com/{owner}/{repo}/{branch}/README.rst",
                f"https://raw.githubusercontent.com/{owner}/{repo}/{branch}/readme.rst"
            ]
            
            for readme_url in raw_readme_urls:
                try:
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Accept': 'text/plain,text/markdown,*/*'
                    }
                    raw_response = requests.get(readme_url, headers=headers, timeout=10)
                    if raw_response.status_code == 200:
                        raw_readme_content = raw_response.text
                        successful_branch = branch
                        logger.info(f"成功获取raw README: {readme_url}")
                        break
                except Exception as e:
                    logger.debug(f"获取raw README {readme_url} 失败: {str(e)}")
            
            if raw_readme_content:
                break
        
        if not successful_branch:
            logger.warning("无法获取README内容，使用默认分支main")
            successful_branch = 'main'
        
        def download_media_file(url, output_dir, filename=None):
            """
            下载媒体文件的通用函数，参考webpage_toolkit的实现
            """
            try:
                logger.info(f"    开始下载媒体文件: {url}")
                
                # Create output directory if it doesn't exist
                os.makedirs(output_dir, exist_ok=True)
                
                # 根据URL判断文件类型，设置合适的请求头
                parsed_url = urllib.parse.urlparse(url)
                url_path = parsed_url.path.lower()
                
                # 判断是否为视频文件
                is_video = any(url_path.endswith(ext) for ext in ['.mp4', '.mov', '.avi', '.webm', '.flv', '.wmv'])
                
                logger.info(f"    文件类型检测: {'视频' if is_video else '图片'}, URL路径: {url_path}")
                
                # 设置下载请求头
                if is_video:
                    download_headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                        'Accept': 'video/webm,video/mp4,video/quicktime,video/x-msvideo,video/*,*/*;q=0.8',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache',
                        'Sec-Fetch-Dest': 'video',
                        'Sec-Fetch-Mode': 'no-cors',
                        'Sec-Fetch-Site': 'cross-site',
                    }
                    logger.info(f"    使用视频下载请求头")
                else:
                    download_headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                        'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache',
                        'Sec-Fetch-Dest': 'image',
                        'Sec-Fetch-Mode': 'no-cors',
                        'Sec-Fetch-Site': 'cross-site',
                    }
                    logger.info(f"    使用图片下载请求头")
                
                # Download the file
                response = requests.get(
                    url, 
                    timeout=30, 
                    stream=True,
                    headers=download_headers,
                    allow_redirects=True
                )
                response.raise_for_status()
                
                logger.info(f"    下载响应: 状态码={response.status_code}, Content-Type={response.headers.get('Content-Type', 'unknown')}")
                logger.info(f"    响应头: Content-Length={response.headers.get('Content-Length', 'unknown')}")
                
                # Generate filename if not provided
                if not filename:
                    parsed_url = urllib.parse.urlparse(url)
                    filename = os.path.basename(parsed_url.path)
                    if not filename or '.' not in filename:
                        # 智能格式检测
                        content_type = response.headers.get('Content-Type', '')
                        logger.info(f"    检测到MIME类型: {content_type}")
                        if 'image/jpeg' in content_type:
                            ext = '.jpg'
                        elif 'image/png' in content_type:
                            ext = '.png'
                        elif 'image/gif' in content_type:
                            ext = '.gif'
                        elif 'image/svg' in content_type:
                            ext = '.svg'
                        elif 'image/webp' in content_type:
                            ext = '.webp'
                        elif 'video/mp4' in content_type:
                            ext = '.mp4'
                        elif 'video/quicktime' in content_type or 'video/x-quicktime' in content_type:
                            ext = '.mov'
                            logger.info(f"    检测到QuickTime视频格式，使用.mov扩展名")
                        elif 'video/x-msvideo' in content_type:
                            ext = '.avi'
                        elif 'video/webm' in content_type:
                            ext = '.webm'
                        elif 'video/' in content_type:
                            # 通用视频类型，默认使用 mp4
                            ext = '.mp4'
                            logger.info(f"    检测到通用视频类型，使用.mp4扩展名")
                        else:
                            ext = '.bin'
                            logger.warning(f"    未知MIME类型: {content_type}，使用.bin扩展名")
                        
                        filename = f"media_{int(time.time())}{ext}"
                        logger.info(f"    生成文件名: {filename}")
                
                # Sanitize filename
                filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
                filename = re.sub(r'[^\w\-_.]', '_', filename)
                filename = re.sub(r'_+', '_', filename)
                filename = filename.strip('_.')
                
                if not filename:
                    filename = "unnamed_file"
                
                # Avoid filename conflicts
                counter = 1
                original_filename = filename
                filepath = os.path.join(output_dir, filename)
                while os.path.exists(filepath):
                    name, ext = os.path.splitext(original_filename)
                    filename = f"{name}_{counter}{ext}"
                    filepath = os.path.join(output_dir, filename)
                    counter += 1
                
                logger.info(f"    保存路径: {filepath}")
                
                # 获取文件大小信息
                content_length = response.headers.get('Content-Length')
                if content_length:
                    file_size = int(content_length)
                    logger.info(f"    文件大小: {file_size / 1024:.1f} KB")
                
                with open(filepath, 'wb') as f:
                    downloaded_size = 0
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                        downloaded_size += len(chunk)
                
                logger.info(f"    ✅ 成功下载: {filepath} ({downloaded_size / 1024:.1f} KB)")
                return filepath
                
            except requests.exceptions.Timeout:
                logger.warning(f"    ❌ 下载超时: {url}")
                return None
            except requests.exceptions.ConnectionError:
                logger.warning(f"    ❌ 连接错误: {url}")
                return None
            except requests.exceptions.HTTPError as e:
                logger.warning(f"    ❌ HTTP错误 {e.response.status_code}: {url}")
                return None
            except Exception as e:
                logger.warning(f"    ❌ 下载失败: {url}, 错误: {e}")
                return None

        def classify_media_importance(alt_text, url, context_text=""):
            """
            根据内容分类媒体的重要性和类型
            
            Returns:
                tuple: (importance_level, category, description)
                importance_level: 1-5 (5最重要)
                category: 媒体类别
                description: 增强的描述
            """
            combined_text = f"{alt_text} {url} {context_text}".lower()
            
            # 跳过不重要的图片
            skip_keywords = ["license", "logo", "badge", "icon", "star", "fork", "build", "coverage", "version"]
            if any(keyword in combined_text for keyword in skip_keywords):
                return 0, "skip", "跳过的徽章或标识"
            
            # 分类和评分
            max_importance = 0
            best_category = "other"
            enhanced_desc = alt_text or "媒体文件"
            
            for category, keywords in critical_keywords.items():
                for keyword in keywords:
                    if keyword in combined_text:
                        if category == "architecture":
                            importance = 5
                            enhanced_desc = f"【架构图】{enhanced_desc}"
                        elif category == "demo":
                            importance = 5
                            enhanced_desc = f"【演示Demo】{enhanced_desc}"
                        elif category == "performance":
                            importance = 4
                            enhanced_desc = f"【性能测评】{enhanced_desc}"
                        elif category == "workflow":
                            importance = 4
                            enhanced_desc = f"【工作流程】{enhanced_desc}"
                        elif category == "feature":
                            importance = 3
                            enhanced_desc = f"【功能特性】{enhanced_desc}"
                        elif category == "ui":
                            importance = 3
                            enhanced_desc = f"【界面展示】{enhanced_desc}"
                        else:
                            importance = 2
                        
                        if importance > max_importance:
                            max_importance = importance
                            best_category = category
                            break
            
            # 特殊文件名模式检测
            filename_patterns = {
                r"(arch|framework|structure|design)": (5, "architecture", "【架构设计】"),
                r"(demo|example|tutorial|showcase)": (5, "demo", "【演示示例】"),
                r"(flow|workflow|process|pipeline)": (4, "workflow", "【流程图】"),
                r"(performance|benchmark|test|result)": (4, "performance", "【测试结果】"),
                r"(feature|function|capability)": (3, "feature", "【功能说明】"),
                r"(ui|interface|gui|screen)": (3, "ui", "【界面截图】")
            }
            
            for pattern, (importance, category, prefix) in filename_patterns.items():
                if re.search(pattern, combined_text, re.IGNORECASE):
                    if importance > max_importance:
                        max_importance = importance
                        best_category = category
                        enhanced_desc = f"{prefix}{enhanced_desc}"
                        break
            
            # 如果没有匹配到任何关键词，给一个基础分数
            if max_importance == 0:
                max_importance = 1
                best_category = "other"
            
            return max_importance, best_category, enhanced_desc

        def extract_enhanced_context(element, max_chars=300):
            """提取元素周围的上下文信息"""
            try:
                context_parts = []
                
                # 获取父元素的文本
                parent = element.parent
                if parent:
                    parent_text = parent.get_text(strip=True)
                    if parent_text and len(parent_text) < 200:
                        context_parts.append(parent_text)
                
                # 获取前后兄弟元素的文本
                prev_sibling = element.previous_sibling
                if prev_sibling and hasattr(prev_sibling, 'get_text'):
                    prev_text = prev_sibling.get_text(strip=True)
                    if prev_text:
                        context_parts.append(prev_text)
                
                next_sibling = element.next_sibling
                if next_sibling and hasattr(next_sibling, 'get_text'):
                    next_text = next_sibling.get_text(strip=True)
                    if next_text:
                        context_parts.append(next_text)
                
                # 合并上下文
                context = " ".join(context_parts)
                if len(context) > max_chars:
                    context = context[:max_chars] + "..."
                
                return context
            except:
                return ""
        
        # 处理raw README中的媒体链接
        if raw_readme_content:
            logger.info("分析raw README中的媒体链接...")
            
            # 查找markdown格式的图片: ![alt](url)
            md_image_pattern = r'!\[([^\]]*)\]\(([^)]+)\)'
            md_images = re.findall(md_image_pattern, raw_readme_content)
            
            # 查找HTML格式的图片: <img src="url" alt="alt">
            html_image_pattern = r'<img[^>]+src=["\']([^"\']+)["\'][^>]*(?:alt=["\']([^"\']*)["\'])?[^>]*>'
            html_images = re.findall(html_image_pattern, raw_readme_content)
            
            # 查找视频链接
            video_pattern = r'!\[([^\]]*)\]\(([^)]+\.(?:mp4|avi|mov|wmv|flv|webm))\)'
            videos = re.findall(video_pattern, raw_readme_content)
            
            # 查找其他媒体链接
            media_pattern = r'\[([^\]]*)\]\(([^)]+\.(?:jpg|jpeg|png|gif|svg|webp|mp4|avi|mov|wmv|flv|webm))\)'
            other_media = re.findall(media_pattern, raw_readme_content)
            
            logger.info(f"从README中找到: {len(md_images)}个MD图片, {len(html_images)}个HTML图片, {len(videos)}个视频, {len(other_media)}个总媒体链接")
            
            # 处理所有找到的媒体
            all_found_media = []
            
            # 添加markdown图片
            for alt, url in md_images:
                all_found_media.append(("image", alt, url, "markdown"))
            
            # 添加HTML图片
            for url, alt in html_images:
                all_found_media.append(("image", alt or "", url, "html"))
            
            # 添加视频
            for alt, url in videos:
                all_found_media.append(("video", alt, url, "video"))
            
            # 添加其他媒体
            for alt, url in other_media:
                media_type = "video" if url.lower().endswith(('.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm')) else "image"
                all_found_media.append((media_type, alt, url, "other"))
            
            # 下载媒体文件
            for media_type, alt, url, source in all_found_media:
                # 分类媒体重要性
                importance, category, enhanced_desc = classify_media_importance(alt, url)
                
                # 跳过不重要的媒体
                if importance == 0:
                    continue
                
                # 处理相对路径
                if not url.startswith(('http://', 'https://')):
                    # 尝试多个可能的分支
                    for branch in possible_branches:
                        if url.startswith('./'):
                            test_url = f"https://raw.githubusercontent.com/{owner}/{repo}/{branch}/{url[2:]}"
                        elif url.startswith('/'):
                            test_url = f"https://raw.githubusercontent.com/{owner}/{repo}/{branch}{url}"
                        else:
                            test_url = f"https://raw.githubusercontent.com/{owner}/{repo}/{branch}/{url}"
                        
                        # 测试URL是否可访问
                        try:
                            test_response = requests.head(test_url, timeout=5)
                            if test_response.status_code == 200:
                                url = test_url
                                logger.info(f"找到可访问的媒体URL: {url}")
                                break
                        except:
                            continue
                    else:
                        # 如果所有分支都失败，使用默认分支
                        if url.startswith('./'):
                            url = f"https://raw.githubusercontent.com/{owner}/{repo}/{successful_branch}/{url[2:]}"
                        elif url.startswith('/'):
                            url = f"https://raw.githubusercontent.com/{owner}/{repo}/{successful_branch}{url}"
                        else:
                            url = f"https://raw.githubusercontent.com/{owner}/{repo}/{successful_branch}/{url}"
                
                # 确定文件类型和目录
                file_ext = os.path.splitext(url.split('?')[0])[1].lower()
                if not file_ext:
                    if media_type == "video":
                        file_ext = '.mp4'
                    else:
                        file_ext = '.jpg'
                
                if media_type == "image":
                    media_dir = os.path.join(media_output_dir, "images")
                    final_media_type = "image"
                elif media_type == "video":
                    media_dir = os.path.join(media_output_dir, "videos")
                    final_media_type = "video"
                else:
                    media_dir = os.path.join(media_output_dir, "other")
                    final_media_type = "other"
                
                os.makedirs(media_dir, exist_ok=True)
                
                try:
                    # 下载文件
                    local_path = download_media_file(url, media_dir)
                    
                    if local_path:
                        # 添加到媒体资源列表
                        all_media_resources.append({
                            "path": local_path,
                            "type": final_media_type,
                            "description": enhanced_desc,
                            "original_uri": url,
                            "filename": os.path.basename(local_path),
                            "importance": importance,
                            "category": category,
                            "alt_text": alt,
                            "context": f"来源: README.md | 类型: {media_type} | 格式: {source}"
                        })
                        
                        logger.info(f"已下载重要媒体文件 (重要性:{importance}): {local_path}")
                    
                    # 添加短暂延迟避免请求过快
                    time.sleep(0.5)
                    
                except Exception as e:
                    logger.warning(f"下载媒体资源失败: {url}, 错误: {str(e)}")
        
        # 处理HTML页面中的图片（如果README部分存在）
        if readme_section:
            logger.info("分析HTML页面中的图片...")
            
            # 查找所有图像
            for img in readme_section.find_all('img'):
                src = img.get('src', '')
                if not src:
                    continue
                
                alt = img.get('alt', '')
                context_desc = extract_enhanced_context(img)
                
                # 分类媒体重要性
                importance, category, enhanced_desc = classify_media_importance(alt, src, context_desc)
                
                # 跳过不重要的图片
                if importance == 0:
                    continue
                
                # 判断是否为绝对路径
                if not src.startswith(('http://', 'https://')):
                    # 尝试多个可能的分支构建绝对路径
                    for branch in possible_branches:
                        if src.startswith('./'):
                            test_src = f"https://raw.githubusercontent.com/{owner}/{repo}/{branch}/{src[2:]}"
                        elif src.startswith('/'):
                            test_src = f"https://raw.githubusercontent.com/{owner}/{repo}/{branch}{src}"
                        else:
                            test_src = f"https://raw.githubusercontent.com/{owner}/{repo}/{branch}/{src}"
                        
                        # 测试URL是否可访问
                        try:
                            test_response = requests.head(test_src, timeout=5)
                            if test_response.status_code == 200:
                                src = test_src
                                logger.info(f"找到可访问的图片URL: {src}")
                                break
                        except:
                            continue
                    else:
                        # 如果所有分支都失败，使用默认分支
                        if src.startswith('./'):
                            src = f"https://raw.githubusercontent.com/{owner}/{repo}/{successful_branch}/{src[2:]}"
                        elif src.startswith('/'):
                            src = f"https://raw.githubusercontent.com/{owner}/{repo}/{successful_branch}{src}"
                        else:
                            src = f"https://raw.githubusercontent.com/{owner}/{repo}/{successful_branch}/{src}"
                
                # 检查是否已经下载过
                already_downloaded = False
                for existing_media in all_media_resources:
                    if existing_media["original_uri"] == src:
                        already_downloaded = True
                        break
                
                if already_downloaded:
                    continue
                
                try:
                    # 确定文件类型和扩展名
                    file_ext = os.path.splitext(src.split('?')[0])[1].lower()
                    if not file_ext:
                        file_ext = '.jpg'  # 默认扩展名
                        
                    # 确定媒体类型和目录
                    if file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp']:
                        media_type = "image"
                        media_dir = os.path.join(media_output_dir, "images")
                    else:
                        continue  # 跳过不支持的类型
                        
                    os.makedirs(media_dir, exist_ok=True)
                    
                    # 下载图像
                    local_path = download_media_file(src, media_dir)
                    
                    if local_path:
                        # 添加到媒体资源列表
                        all_media_resources.append({
                            "path": local_path,
                            "type": media_type,
                            "description": enhanced_desc,
                            "original_uri": src,
                            "filename": os.path.basename(local_path),
                            "importance": importance,
                            "category": category,
                            "alt_text": alt,
                            "context": context_desc
                        })
                        
                        logger.info(f"已下载HTML图片 (重要性:{importance}): {local_path}")
                    
                except Exception as e:
                    logger.warning(f"下载HTML图片失败: {src}, 错误: {str(e)}")
        
        # 按重要性排序
        all_media_resources.sort(key=lambda x: x["importance"], reverse=True)
        
        # 生成下载报告
        if all_media_resources:
            report_path = os.path.join(media_output_dir, "download_report.md")
            with open(report_path, "w", encoding="utf-8") as f:
                f.write("# 多媒体素材下载报告\n\n")
                f.write(f"**仓库:** {repo_url}\n")
                f.write(f"**使用分支:** {successful_branch}\n")
                f.write(f"**总计下载:** {len(all_media_resources)} 个文件\n\n")
                
                # 按类别分组
                by_category = {}
                for media in all_media_resources:
                    category = media["category"]
                    if category not in by_category:
                        by_category[category] = []
                    by_category[category].append(media)
                
                for category, media_list in by_category.items():
                    f.write(f"## {category.title()} ({len(media_list)}个文件)\n\n")
                    for media in media_list:
                        f.write(f"- **{media['filename']}** (重要性: {media['importance']}/5)\n")
                        f.write(f"  - 描述: {media['description']}\n")
                        f.write(f"  - 路径: {media['path']}\n")
                        f.write(f"  - 原始URL: {media['original_uri']}\n")
                        f.write(f"  - 上下文: {media['context']}\n\n")
        
        logger.info(f"成功下载 {len(all_media_resources)} 个媒体文件，其中重要文件 {len([m for m in all_media_resources if m['importance'] >= 4])} 个")
        return all_media_resources
        
    except Exception as e:
        logger.error(f"下载GitHub多媒体素材时出错: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return []

def fix_mermaid_syntax(mermaid_content):
    """
    修复常见的Mermaid语法问题
    
    Args:
        mermaid_content: Mermaid图表内容
        
    Returns:
        str: 修复后的Mermaid内容
    """
    # 去除多余的空白行
    lines = [line for line in mermaid_content.split('\n') if line.strip()]
    
    # 判断图表类型
    diagram_type = None
    if lines:
        first_line = lines[0].strip()
        for dt in ['graph', 'flowchart', 'sequenceDiagram', 'classDiagram', 'stateDiagram', 'gantt', 'pie', 'erDiagram']:
            if first_line.startswith(dt):
                diagram_type = dt
                break
    
    # 检查并修复图表类型声明
    if lines and diagram_type:
        # 添加结尾分号
        if not lines[0].strip().endswith(';'):
            lines[0] = lines[0].strip() + ';'
    
    # 修复节点和关系
    for i in range(len(lines)):
        line = lines[i].strip()
        
        # 将Python风格注释(#)转换为Mermaid注释(%%)
        if '#' in line:
            # 检查#是否在引号内
            in_quotes = False
            comment_pos = -1
            
            for j, char in enumerate(line):
                if char in ['"', "'"]:
                    in_quotes = not in_quotes
                elif char == '#' and not in_quotes:
                    comment_pos = j
                    break
            
            # 如果找到了注释符号(不在引号内)
            if comment_pos >= 0:
                code_part = line[:comment_pos].strip()
                comment_part = line[comment_pos+1:].strip()
                
                # 如果代码部分不为空且不以";"结尾
                if code_part and not code_part.endswith(';') and diagram_type in ['graph', 'flowchart', 'sequenceDiagram', 'classDiagram', 'stateDiagram']:
                    code_part += ';'
                
                # 重构行，将#注释转为%%注释
                lines[i] = code_part + (f' %% {comment_part}' if comment_part else '')
                # 如果原来只有注释
                if not code_part:
                    lines[i] = f'%% {comment_part}'
                    
                # 已处理注释，跳过后续处理
                continue
        
        # 跳过Mermaid注释行
        if line.startswith("%%"):
            continue
        
        # 根据图表类型应用不同的语法修复规则
        if diagram_type in ['graph', 'flowchart']:
            # 检查是否为关系定义行
            if '-->' in line or '---' in line or '==>' in line or '-.->' in line or '->' in line:
                # 添加结尾分号
                if not line.endswith(';'):
                    lines[i] = line + ';'
                    
                # 检查节点文本格式
                for node_pattern in [r'\b(\w+)\s*\[([^\]]+)\]', r'\b(\w+)\s*\(([^\)]+)\)', r'\b(\w+)\s*\{([^\}]+)\}']:
                    for match in re.finditer(node_pattern, line):
                        node_id, node_text = match.groups()
                        # 如果文本没有用引号括起来，添加双引号
                        if not (node_text.startswith('"') and node_text.endswith('"')):
                            replaced_text = f'{node_id}["{node_text}"]'
                            lines[i] = lines[i].replace(match.group(0), replaced_text)
            
            # 检查子图结构
            elif line.startswith('subgraph '):
                # 添加结尾分号
                if not line.endswith(';'):
                    lines[i] = line + ';'
            
            # 检查独立节点定义
            elif re.match(r'^\s*\w+\s*\[', line) or re.match(r'^\s*\w+\s*\(', line) or re.match(r'^\s*\w+\s*\{', line):
                # 添加结尾分号
                if not line.endswith(';'):
                    lines[i] = line + ';'
                    
                # 检查节点文本格式
                for node_pattern in [r'^\s*(\w+)\s*\[([^\]]+)\]', r'^\s*(\w+)\s*\(([^\)]+)\)', r'^\s*(\w+)\s*\{([^\}]+)\}']:
                    match = re.match(node_pattern, line)
                    if match:
                        node_id, node_text = match.groups()
                        # 如果文本没有用引号括起来，添加双引号
                        if not (node_text.startswith('"') and node_text.endswith('"')):
                            replaced_text = f'{node_id}["{node_text}"]'
                            lines[i] = re.sub(node_pattern, replaced_text, lines[i])
        
        elif diagram_type in ['sequenceDiagram', 'classDiagram', 'stateDiagram', 'erDiagram']:
            # 为这些图表类型添加缺失的分号
            if not line.endswith(';') and not line.endswith('{') and not line.endswith('}'):
                # 跳过标题、注释和特殊指令
                if not (line.startswith('title') or line.startswith('participant') or 
                       line.startswith('actor') or line.startswith('note') or
                       line.startswith('autonumber') or line.startswith('activate') or
                       line.startswith('deactivate') or line.startswith('loop') or
                       line.startswith('alt') or line.startswith('else') or
                       line.startswith('end')):
                    lines[i] = line + ';'
    
    return '\n'.join(lines)

# 修改保存Mermaid文件的地方，添加修复步骤
def save_mermaid_file(content, file_path):
    """
    修复Mermaid语法并保存到文件
    
    Args:
        content: Mermaid内容
        file_path: 文件保存路径
        
    Returns:
        str: 保存的文件路径
    """
    # 先检查是否包含图表类型声明
    has_diagram_type = False
    diagram_types = ['graph', 'flowchart', 'sequenceDiagram', 'classDiagram', 'stateDiagram', 'gantt', 'pie', 'erDiagram']
    
    # 获取内容的第一行
    first_line = content.strip().split('\n')[0] if content.strip() else ""
    
    for diagram_type in diagram_types:
        if first_line.startswith(diagram_type):
            has_diagram_type = True
            break
    
    # 如果没有图表类型声明，检查内容判断是否为流程图，并添加默认类型
    if not has_diagram_type:
        # 流程图特征检测
        if '-->' in content or '->' in content:
            content = 'graph TD;\n' + content
        # 序列图特征检测
        elif '->>' in content or '>>' in content or 'participant' in content or 'actor' in content:
            content = 'sequenceDiagram;\n' + content
        # 类图特征检测
        elif 'class' in content and ('<|--' in content or '-->' in content or '*--' in content):
            content = 'classDiagram;\n' + content
    
    # 应用语法修复
    fixed_content = fix_mermaid_syntax(content)
    
    # 确保文件目录存在
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    # 保存文件
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(fixed_content)
    
    logger.info(f"保存Mermaid文件到 {file_path}")
    return file_path

def main():
    """主函数，运行工作流"""
    import argparse

    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description="Git 仓库分析工具")
    parser.add_argument(
        "--repo",
        "-r",
        type=str,
        default=None,  # 默认为None，将从配置文件中读取
        help="GitHub 仓库 URL (默认: 从配置文件读取)",
    )
    parser.add_argument(
        "--local-path", "-l", type=str, default=None, help="本地仓库路径，用于代码结构分析 (默认: None)"
    )
    parser.add_argument("--output-dir", "-o", type=str, default="output", help="输出目录 (默认: output)")
    parser.add_argument(
        "--config", "-c", type=str, default="config/config.yaml", help="配置文件路径 (默认: config/config.yaml)"
    )

    # 解析命令行参数
    args = parser.parse_args()

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 初始化工作流
    analyzer = GitRepoAnalyzer(config_path=args.config)

    # 分析仓库
    repo_url = args.repo  # 可能为None，从配置文件获取
    local_repo_path = args.local_path

    # 如果命令行未指定，使用配置文件中的值
    if repo_url is None:
        repo_url = analyzer.repo_config.get("github_url", "https://github.com/bytedance/Dolphin")
        print(f"使用配置文件中的GitHub仓库URL: {repo_url}")

    print(f"开始分析仓库: {repo_url}")
    if local_repo_path:
        print(f"使用本地仓库路径: {local_repo_path}")

    # 执行分析
    analyzer.analyze_repo(repo_url, local_repo_path)

    # 从URL提取仓库名称
    repo_name = repo_url.split("/")[-1]
    if repo_name.endswith(".git"):
        repo_name = repo_name[:-4]

    # 显示完成信息
    output_md_file = f"{args.output_dir}/{repo_name}/project_analysis.md"
    print("\n分析完成！")
    print(f"分析报告已保存到: {output_md_file}")


if __name__ == "__main__":
    main()
