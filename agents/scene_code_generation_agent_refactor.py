"""
Enhanced Scene Code Generation Agent (Refactor)

This is the refactored version of the scene code generation agent with enhanced support for
professional_science_template.py framework and improved prompt engineering:

1. Integration of professional science template framework
2. Enhanced template-based code generation approach
3. Improved structured workflow with template awareness
4. Quality control mechanisms for template compliance
"""
import argparse
import hashlib
import os
import subprocess
import sys
from pathlib import Path
from typing import Optional

from camel.agents import ChatAgent
from camel.toolkits import FunctionTool
from camel.toolkits.base import BaseToolkit
from loguru import logger

# Add project root to path
cwd = os.getcwd()
sys.path.insert(0, cwd)
from tools.code_agent_tools import (
    bash_execute,
    check_code_issues,
    file_create,
    file_view,
    get_library_docs,
    replace_in_file,
    resolve_library_id,
    search_files,
    sequential_thinking,
)
from utils.common import Config
from utils.create_llm_model import create_model

# Import smolagents toolkit for framework switching
try:
    from agents.smolagents_scene_code_generation_agent_refactor import SmolagentsSceneCodeGenerationToolkitRefactor

    SMOLAGENTS_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Smolagents refactor not available: {e}")
    SMOLAGENTS_AVAILABLE = False


class EnhancedSceneCodeGenerationToolkitRefactor(BaseToolkit):
    """
    Enhanced toolkit for generating Manim code with professional science template integration.

    This refactored toolkit implements:
    - Professional science template framework integration
    - Template-aware code generation process
    - Enhanced prompt engineering with template examples
    - Quality control for template compliance
    - Structured workflow phases with template guidance
    """

    def __init__(self, working_dir: Optional[str] = None):
        """
        Initialize the enhanced refactor toolkit.

        Args:
            working_dir: Working directory for file operations (default: current directory)
        """
        self.working_dir = Path(working_dir or os.getcwd()).resolve()

        config = Config().config.get("workflow", {}).get("code_agent", {})
        self.agent_framework = config.get("agent_framework", "camel")
        self.enable_sequential_thinking = config.get("enable_sequential_thinking", False)
        self.enable_get_docs = config.get("enable_get_docs", False)
        max_iteration_per_step = config.get("max_iteration_per_step", 20)

        # Check if smolagents framework is requested and available
        if self.agent_framework == "smolagents":
            if SMOLAGENTS_AVAILABLE:
                logger.info("Using smolagents refactor framework")
                self._smolagents_toolkit = SmolagentsSceneCodeGenerationToolkitRefactor(working_dir)
                self.agent = None  # Will use smolagents toolkit directly
            else:
                logger.warning("Smolagents refactor requested but not available, falling back to camel")
                self.agent_framework = "camel"

        # Create camel agent if using camel framework or as fallback
        if self.agent_framework == "camel":
            logger.info("Using camel refactor framework")
            self.agent = ChatAgent(
                system_message=self._create_system_prompt(),
                model=create_model(),
                tools=self.get_all_tools(),  # Framework automatically handles tool descriptions
                max_iteration=max_iteration_per_step,
            )
            self._smolagents_toolkit = None

        logger.info(
            f"Enhanced refactor toolkit initialized with framework: {self.agent_framework}, "
            f"working directory: {self.working_dir}, "
            f"max_iteration_per_step: {max_iteration_per_step}, "
            f"sequential_thinking: {self.enable_sequential_thinking}, get_docs: {self.enable_get_docs}"
        )

    def get_all_tools(self) -> list[FunctionTool]:
        """
        Get all tools as FunctionTool objects for camel framework integration.

        Returns:
            List of FunctionTool objects
        """
        tools = []

        # Add sequential thinking tool conditionally
        if self.enable_sequential_thinking:
            tools.append(FunctionTool(sequential_thinking))

        # Add core tools
        tools.extend(
            [
                FunctionTool(file_view),
                FunctionTool(file_create),
                FunctionTool(replace_in_file),  # New precise file editor
                FunctionTool(search_files),  # File/directory search with regex support
                FunctionTool(bash_execute),
                FunctionTool(check_code_issues),
            ]
        )

        # Add documentation tools conditionally
        if self.enable_get_docs:
            tools.extend(
                [
                    FunctionTool(resolve_library_id),
                    FunctionTool(get_library_docs),
                ]
            )

        return tools

    def _summarize_conversation_history(self, messages, current_error: str) -> str:
        """
        使用小模型总结对话历史，提取关键的尝试、错误和工具调用信息。

        Args:
            memory_context: Agent的记忆上下文
            current_error: 当前遇到的错误信息

        Returns:
            总结后的历史信息字符串
        """
        try:
            if not messages or len(messages) <= 1:
                return "这是第一次尝试，没有历史记录。"

            # 构建历史内容字符串，包括工具调用中的思考内容
            history_content = ""
            for msg in messages[1:]:  # 去掉第一条消息
                if isinstance(msg, dict):
                    role = msg.get("role", "unknown")
                    content = msg.get("content", "")

                    # 处理普通消息内容
                    history_content += f"{role}: {content}\n\n"

                    # 处理工具调用中的思考内容
                    tool_calls = msg.get("tool_calls", [])
                    if tool_calls:
                        for tool_call in tool_calls:
                            if isinstance(tool_call, dict):
                                function = tool_call.get("function", {})
                                if function.get("name") == "sequential_thinking":
                                    try:
                                        import json

                                        args = json.loads(function.get("arguments", "{}"))
                                        thought = args.get("thought", "")
                                        if thought:
                                            history_content += f"思考过程: {thought}\n\n"
                                    except Exception:
                                        pass

            if not history_content.strip():
                return "没有找到有效的历史对话记录。"

            # 创建总结用的小模型
            summary_agent = ChatAgent(
                system_message="你是一个专业的对话历史分析师，擅长总结技术问题解决过程。",
                model=create_model("google/gemini-2.5-flash-lite-preview-06-17"),  # 使用小模型节省成本
            )

            summary_prompt = f"""
请分析以下Manim代码生成过程的对话历史，提取关键信息：

## 对话历史
{history_content}

## 当前错误
{current_error}

请总结：
1. **已尝试的解决方案**：列出之前尝试了哪些方法
2. **查询到的文档或示例**：有哪些可以参考的文档或示例
2. **工具调用结果**：提到了哪些重要的工具调用和结果
3. **遇到的错误类型**：之前解决了哪些错误
4. **当前代码状态**：代码目前的状态如何
5. **学到的经验**：从之前的尝试中学到了什么

请用简洁的中文回答，控制在500字以内。
"""

            response = summary_agent.step(summary_prompt)
            if response and hasattr(response, "msgs") and response.msgs:
                summary = response.msgs[-1].content
                logger.info(f"历史总结生成成功: {summary}")
                return summary
            else:
                return "没有找到有效的历史对话记录。"

        except Exception as e:
            logger.warning(f"历史总结生成失败: {e}")
            return "没有找到有效的历史对话记录。"

    def _create_enhanced_prompt_with_history(
        self, original_prompt: str, history_summary: str = "", current_error: str = ""
    ) -> str:
        """
        创建包含历史总结的增强提示词

        Args:
            original_prompt: 原始的完整提示词
            history_summary: 历史总结信息
            current_error: 当前错误信息

        Returns:
            格式化后的提示词
        """
        if not history_summary and not current_error:
            return original_prompt

        template = """
{original_prompt}

## 📋 上轮迭代总结
{history_summary}

## 🔧 当前需要修复的问题
{current_error}

请基于以上历史经验和当前问题，继续优化代码。重点关注之前没有解决的问题，避免重复之前已经尝试过的无效方案。
"""

        return template.format(
            original_prompt=original_prompt,
            history_summary=history_summary or "这是第一次尝试。",
            current_error=current_error or "继续按照原计划执行。",
        ).strip()

    def _to_absolute_path(self, path: str) -> str:
        """Convert relative path to absolute path"""
        path_obj = Path(path)
        if path_obj.is_absolute():
            return str(path_obj)
        return str(self.working_dir / path_obj)

    def _create_system_prompt(self) -> str:
        """
        Create system prompt with universal code generation guidance and professional template integration.

        This contains all the general tool usage instructions, workflow,
        error handling, completion criteria, and professional science template guidance.
        """
        return """# 专业科学模板化代码生成专家系统（重构版）

## 🎯 角色定义
你是一个专业的基于模板的代码生成专家，专门基于 `ProfessionalScienceTemplate` 框架生成高质量的Manim教学动画代码，具备以下核心能力：
- 深度理解 `ProfessionalScienceTemplate` 模板架构和设计原则
- 熟练使用模板的标准化区域接口和便捷方法
- 能够生成符合世界级教学标准的可视化代码
- 具备自主调试和错误修复能力，确保模板规范遵循
- 遵循结构化工作流程，确保代码质量和模板一致性

## 🏗️ ProfessionalScienceTemplate 核心架构理解

### 模板布局系统
```
┌─────────────────┬─────────────────┐
│  标题区域       │   步骤介绍区域   │  <- 各占顶部10%
│  (TOP_LEFT)     │   (TOP_RIGHT)   │
├─────────────────┼─────────────────┤
│                 │                 │
│     辅助区域    │    主内容区域    │  <- 左15%，中60%
│    (LEFT)       │    (CENTER)     │
│                 │                 │
│                 ├─────────────────┤
│                 │    辅助区域      │  <- 右15%
│                 │    (RIGHT)      │
├─────────────────┴─────────────────┤
│           结果区域                │  <- 底部10%
│           (DOWN)                  │
└───────────────────────────────────┘
```

### 标准化区域接口（必须掌握）
1. **`create_title_region_content(title_text)`** - 标题区域，固定字体，蓝色主题
2. **`create_step_region_content(step_text)`** - 步骤区域，固定字体，紫色主题  
3. **`create_main_region_content(main_content)`** - 主内容区域，自动适配，支持任何对象
4. **`create_left_auxiliary_content(title, items)`** - 左辅助区域，概念要点
5. **`create_right_auxiliary_content(title, items)`** - 右辅助区域，公式特点
6. **`create_result_region_content(result_text)`** - 结果区域，成功色突出

### 专业色彩系统
- **主要色**: `#FDE047` (亮黄色) - 标题等主要元素
- **次要色**: `#FACC15` (金黄色) - 强调元素  
- **重点色**: `#F59E0B` (橙黄色) - 重点标记
- **成功色**: `#EAB308` (金色) - 结果展示
- **文字色**: `WHITE` - 所有文字使用纯白色
- **辅助背景**: `#2D3436` - 辅助区域深色背景

## 🔄 基于模板的结构化执行流程

### 阶段1：模板框架分析与规划 📚
**必须完成的步骤**：
1. 分析技术描述，确定需要使用的模板区域组合
2. 规划每个区域的具体内容：
   - 标题区域：简洁概括（8字以内）
   - 步骤区域：当前操作描述（12字以内）
   - 主内容区域：核心可视化内容
   - 左辅助区域：概念要点（5项以内，每项15字）
   - 右辅助区域：公式特点（5项以内）
   - 结果区域：总结陈述（40字以内）
3. 选择合适的布局模式：
   - 完整布局（左右辅助区域都有）
   - 纯净布局（仅主内容区域）
   - 左侧布局（仅左辅助区域）

**验证条件**：明确每个区域的内容规划和模板接口调用方案

### 阶段2：模板代码框架创建 💻
**必须完成的步骤**：
1. 使用 `file_create` 创建继承自 `ProfessionalScienceTemplate` 的类
2. 包含正确的导入语句：`from manim import *` 和模板导入
3. 实现 `construct` 方法，调用 `setup_background()`
4. 立即调用 `check_code_issues` 验证语法

**验证条件**：
- 文件成功创建
- 继承关系正确
- 无语法错误
- 模板导入成功

### 阶段3：模板区域内容实现 🔧
**必须完成的步骤**：
1. 使用标准化区域接口实现各区域内容
2. 遵循模板的内容建议指南
3. 每次修改后立即调用 `check_code_issues`
4. 确保所有动画序列符合模板设计原则

**验证条件**：
- 每次修改后无新的语法错误
- 模板接口调用正确
- 内容符合区域尺寸建议
- 动画流程清晰连贯

### 阶段4：模板质量保证与验证 ✅
**必须完成的步骤**：
1. 编译检查：`python -m py_compile 文件路径`
2. Manim干运行：`manim --dry_run --progress_bar none 文件路径`
3. 模板规范检查：确认所有区域接口正确使用
4. 视觉效果验证：确认符合专业教学标准

**验证条件**：
- 编译无错误
- Manim验证成功
- 模板接口使用规范
- 代码符合所有质量标准

## 🎨 模板使用最佳实践

### 内容建议遵循
- **标题**: 8个字以内（如"函数原理"、"算法流程"）
- **步骤**: 12个字以内（如"第一步：初始化参数"）
- **辅助项目**: 5项以内，每项15字以内
- **结果**: 40个字以内的总结

### 常用模板模式
```python
# 模式1：完整布局（适合复杂概念讲解）
title = self.create_title_region_content("概念名称")
step = self.create_step_region_content("第一步：初始化")
main = self.create_main_region_content(main_visual_content)
left = self.create_left_auxiliary_content("要点:", ["要点1", "要点2"])
right = self.create_right_auxiliary_content("公式:", [MathTex("公式")])
result = self.create_result_region_content("结论：...")

# 模式2：纯净布局（适合专注主内容）
title = self.create_title_region_content("可视化名称")
step = self.create_step_region_content("演示过程")  
main = self.create_main_region_content(complex_visualization)
result = self.create_result_region_content("总结：...")

# 模式3：左侧布局（适合突出要点）
title = self.create_title_region_content("算法名称")
step = self.create_step_region_content("执行阶段")
main = self.create_main_region_content(algorithm_visual)
left = self.create_left_auxiliary_content("步骤:", ["步骤1", "步骤2"])
result = self.create_result_region_content("完成：...")
```

## 🚨 模板规范错误处理

### 错误分类与处理策略
1. **模板导入错误**：
   - 检测：找不到 `ProfessionalScienceTemplate`
   - 处理：确认模板文件路径，修复导入语句
   
2. **区域接口调用错误**：
   - 检测：方法名错误或参数不匹配
   - 处理：参考模板源码，使用正确的接口签名
   
3. **内容格式错误**：
   - 检测：内容超出建议长度或格式不当
   - 处理：根据内容建议指南调整文字长度和格式
   
4. **布局组合错误**：
   - 检测：区域组合不当或视觉效果差
   - 处理：选择更合适的布局模式

## ✅ 模板化任务完成标准

### 必须满足的条件（缺一不可）
1. **模板继承正确**：正确继承 `ProfessionalScienceTemplate`
2. **接口使用规范**：所有区域使用标准化接口创建
3. **内容符合建议**：遵循内容建议指南的长度和格式要求
4. **语法检查通过**：`check_code_issues` 返回"No issues found"
5. **编译检查通过**：`python -m py_compile` 无错误输出
6. **Manim验证通过**：`manim --dry_run` 成功执行
7. **视觉质量达标**：符合专业教学视觉标准

### 模板化代码示例参考
参考 `ProfessionalScienceTemplate` 中的示例方法：
- `demonstrate_template_usage()` - 整体使用示例
- `demonstrate_full_layout()` - 完整布局示例  
- `demonstrate_main_only_layout()` - 纯净布局示例
- `ComputerScienceExample` - 具体应用示例

### 🎓 具体实现例子详解

#### 例子1：计算机科学算法演示（ComputerScienceExample）

**快速排序可视化实现要点：**

```python
class ComputerScienceExample(ProfessionalScienceTemplate):
    def construct(self):
        self.setup_background()  # ⚠️ 必须调用
        self.demonstrate_quicksort_algorithm()
    
    def demonstrate_quicksort_algorithm(self):
        # 1. 创建标题和步骤（严格字数限制）
        title_group = self.create_title_region_content("快速排序")  # ✅ 4字，合适
        step_group = self.create_step_region_content("算法初始化")   # ✅ 5字，合适
        
        # 2. 创建主内容（动态排序可视化）
        main_content = self.create_dynamic_sorting_animation()
        main_group = self.create_main_region_content(main_content)
        
        # 3. 使用纯净布局（无辅助区域，主内容享有全部6.0×3.5空间）
        result_group = self.create_result_region_content(
            "快速排序演示：分治算法，平均时间复杂度O(nlogn)"
        )
        
        # 4. 正确的动画顺序
        self.play(Write(title_group), Write(step_group))
        self.play(FadeIn(main_group))
        self.play(Write(result_group))
```

**关键技术特点：**
- 使用圆圈可视化数组元素，支持动态交换动画
- 实现递归分区过程的步进式演示
- 高亮显示基准元素、比较元素、已排序元素
- 弧形路径交换动画，增强视觉效果
- 实时操作说明更新，帮助理解算法步骤

#### 例子2：模板规范骨架（TemplateSkeletonExample）

**完整布局标准实现：**

```python
class TemplateSkeletonExample(ProfessionalScienceTemplate):
    def construct(self):
        # === 第0步：必须先设置背景 ===
        self.setup_background()  # ⚠️ 常见错误：忘记调用此方法
        
        # === 第1步：创建标题（8字以内） ===
        title_good = self.create_title_region_content("数学原理")  # ✅ 4字，完美
        
        # === 第2步：创建步骤描述（12字以内） ===
        step_good = self.create_step_region_content("第一步：函数定义")  # ✅ 8字，合适
        
        # === 第3步：创建主内容（核心展示区域） ===
        main_content = self.create_simple_main_content()
        main_group = self.create_main_region_content(main_content)
        
        # === 第4步：创建辅助区域（完整布局示例） ===
        left_aux_good = self.create_left_auxiliary_content(
            "要点:",  # ✅ 3字标题，合适
            [
                "• 开口向上",        # ✅ 5字，合适
                "• 顶点在原点",      # ✅ 6字，合适
                "• 关于y轴对称",     # ✅ 7字，合适
                "• 最小值为0"        # ✅ 6字，合适
            ]  # ✅ 4个项目，合适
        )
        
        right_aux_good = self.create_right_auxiliary_content(
            "公式:",  # ✅ 3字标题，合适
            [
                MathTex(r"f(x) = x^2"),      # ✅ 数学公式，推荐
                MathTex(r"f'(x) = 2x"),      # ✅ 导数公式，推荐
                "定义域: ℝ",                  # ✅ 7字，合适
                "值域: [0,+∞)"               # ✅ 9字，合适
            ]  # ✅ 4个项目，合适
        )
        
        # === 第5步：创建结果区域（40字以内） ===
        result_good = self.create_result_region_content(
            "结论：二次函数y=x²开口向上，顶点(0,0)，关于y轴对称"  # ✅ 28字，合适
        )
        
        # === 第6步：正确的动画顺序 ===
        self.play(Write(title_good), Write(step_good))
        self.play(FadeIn(main_group))
        self.play(FadeIn(left_aux_good), FadeIn(right_aux_good))
        self.play(Write(result_good))
```

**⚠️ 常见错误和正确做法对比：**

```python
# ❌ 错误示例：
# title_bad = self.create_title_region_content("高等数学中的复杂函数原理详解")  # 16字，太长！
# step_bad = self.create_step_region_content("第一步：建立复杂的数学函数模型并进行详细分析")  # 22字，太长！

# ✅ 正确示例：
title_good = self.create_title_region_content("函数原理")    # 4字，完美
step_good = self.create_step_region_content("第一步：建立模型")  # 8字，合适
```

#### 例子3：三种布局模式演示

**1. 完整布局（左右辅助区域都有）**
```python
# 适用场景：需要显示概念要点和相关公式
left_aux = self.create_left_auxiliary_content("概念:", [...])
right_aux = self.create_right_auxiliary_content("公式:", [...])
```

**2. 纯净布局（无辅助区域）**
```python
# 适用场景：复杂图形展示，需要更大空间
# 主内容享有完整的6.0×3.5空间
main_group = self.create_main_region_content(complex_visualization)
```

**3. 单侧布局（仅左辅助区域）**
```python
# 适用场景：重点突出关键信息或步骤说明
left_aux = self.create_left_auxiliary_content("要点:", [...])
```

### 🎨 专业色彩系统详解

```python
self.colors = {
    'primary': "#FDE047",      # 主要黄色 - 突出内容
    'secondary': "#FACC15",    # 次要金黄色 - 强调色
    'accent': "#F59E0B",       # 强调橙黄色 - 重点标记
    'success': "#EAB308",      # 成功金色 - 结果展示
    'text_primary': WHITE,     # 主要文字颜色
    'text_secondary': WHITE,   # 次要文字颜色
    'auxiliary_text': WHITE,   # 辅助区域文字颜色
    'auxiliary_bg': "#2D3436", # 辅助区域背景颜色
    'continuity': "#10B981",   # 连贯性标记颜色
    'transition': "#8B5CF6",   # 过渡动画颜色
    'persistent': "#EF4444"    # 持久化元素颜色
}
```

### 📐 区域布局精确尺寸

```python
self.regions = {
    'title_width': 3.5,         # 标题区域宽度
    'title_height': 0.8,        # 标题区域高度
    'step_width': 3.5,          # 步骤区域宽度
    'step_height': 0.8,         # 步骤区域高度
    'main_width': 6.0,          # 主内容区域宽度（增大）
    'main_height': 3.5,         # 主内容区域高度（增大）
    'auxiliary_width': 1.4,     # 辅助区域宽度（减小）
    'auxiliary_height': 2.5,    # 辅助区域高度
    'result_width': 7.0,        # 结果区域宽度
    'result_height': 0.8        # 结果区域高度
}
```

## 🎯 重要提醒
- **严格遵循模板架构**：不要偏离 `ProfessionalScienceTemplate` 的设计原则
- **使用标准化接口**：避免手动布局，必须使用模板提供的区域接口
- **遵循内容建议**：确保文字长度和格式符合模板建议
- **保持视觉一致性**：使用模板的专业色彩系统和字体配置
- **确保教学效果**：生成的动画应符合世界级教学视觉标准"""

    def _create_task_prompt(self, scene_description: str, output_path: str) -> str:
        """
        Create task-specific prompt for Manim code generation with professional template integration.

        This contains only the Manim-specific requirements and task description,
        with enhanced template usage guidance.
        """
        return f"""# 基于ProfessionalScienceTemplate的Manim代码生成任务

## 🎯 技术栈要求
- **框架**：Manim Community Edition
- **模板**：ProfessionalScienceTemplate (必须继承)
- **语言**：Python 3.8+
- **导入方式**：
  ```python
  from manim import *
  from prompts.professional_science_template import ProfessionalScienceTemplate
  ```

## 📋 专业模板集成指导

### 模板导入与继承
```python
class YourSceneClass(ProfessionalScienceTemplate):
    def construct(self):
        # 必须调用父类背景设置
        self.setup_background()
        
        # 使用标准化区域接口创建内容
        title = self.create_title_region_content("你的标题")
        # ... 其他区域内容
```

### 必须使用的标准化接口
1. **`self.create_title_region_content("标题文字")`** - 标题区域（固定字体大小）
2. **`self.create_step_region_content("步骤描述")`** - 步骤区域（固定字体大小）
3. **`self.create_main_region_content(内容对象)`** - 主内容区域（自动适配）
4. **`self.create_left_auxiliary_content("标题", ["项目1", "项目2"])`** - 左辅助区域
5. **`self.create_right_auxiliary_content("标题", ["项目1", "项目2"])`** - 右辅助区域
6. **`self.create_result_region_content("结果描述")`** - 结果区域

### 内容规划建议
- **标题区域**：概念名称，8字以内（如"函数原理"、"排序算法"）
- **步骤区域**：当前操作，12字以内（如"第一步：数据初始化"）
- **主内容区域**：核心可视化（图形、动画、坐标系等）
- **左辅助区域**：概念要点、关键步骤（5项以内，每项15字）
- **右辅助区域**：相关公式、特征参数（5项以内）
- **结果区域**：总结陈述，40字以内

### 推荐布局选择
1. **完整布局**：适合复杂概念，需要详细辅助信息
2. **纯净布局**：适合专注主内容，无辅助区域干扰
3. **左侧布局**：适合突出要点，右侧留白

## 🔧 模板验证命令
- **编译检查**：`python -m py_compile {output_path}`
- **Manim验证**：`manim --dry_run --progress_bar none {output_path}`
- **语法检查**：`check_code_issues(["{output_path}"], "error")`

## 📖 参考模板示例
参考 `ProfessionalScienceTemplate` 中的示例：
- `demonstrate_full_layout()` - 完整布局实现
- `demonstrate_main_only_layout()` - 纯净布局实现  
- `ComputerScienceExample` - 算法可视化应用

## ✅ 模板化代码质量标准
1. **模板继承**：正确继承 `ProfessionalScienceTemplate`
2. **接口规范**：使用标准化区域接口，避免手动布局
3. **内容适配**：遵循内容建议，确保良好视觉效果
4. **语法正确**：无Python语法错误
5. **编译通过**：`python -m py_compile` 成功
6. **Manim验证**：`manim --dry_run` 成功执行
7. **功能完整**：实现描述中的所有视觉元素和动画
8. **质量达标**：符合世界级专业教学视觉标准

## 🎯 当前任务目标
基于以下技术实现描述，生成继承自 `ProfessionalScienceTemplate` 的高质量Manim代码，保存到文件：`{output_path}`

### 技术实现描述
```
{scene_description}
```

## 🚀 执行要求
1. **严格遵循模板架构**：必须继承 `ProfessionalScienceTemplate`
2. **使用标准化接口**：所有区域内容必须通过模板接口创建
3. **内容规划清晰**：根据描述合理分配各区域内容
4. **视觉效果专业**：确保生成的动画符合教学标准
5. **代码质量优秀**：通过所有验证检查

**开始执行任务，严格按照系统提示的4阶段流程进行，确保生成符合ProfessionalScienceTemplate规范的高质量代码。**"""

    def generate_manim_code_enhanced(
        self, scene_description: str, output_file: str, max_iterations: int = 3
    ) -> Optional[str]:
        """
        Enhanced Manim code generation with professional template integration.

        This method implements the four-phase enhanced workflow with template awareness:
        1. Template Framework Analysis Phase (using template-aware planning)
        2. Template Code Framework Creation Phase (using template inheritance)
        3. Template Content Implementation Phase (using standardized interfaces)
        4. Template Quality Assurance Phase (template compliance checking)

        Args:
            scene_description: Description of the scene to generate
            output_file: Output file path for the generated code
            max_iterations: Maximum number of iterations for quality improvement

        Returns:
            Path to generated and debugged code file, or None on failure
        """
        # Delegate to appropriate framework implementation
        if self.agent_framework == "smolagents" and self._smolagents_toolkit:
            logger.info("Using smolagents refactor framework for code generation")
            return self._smolagents_toolkit.generate_manim_code_enhanced(scene_description, output_file, max_iterations)
        else:
            logger.info("Using camel refactor framework for code generation")
            return self._generate_manim_code_camel(scene_description, output_file, max_iterations)

    def _generate_manim_code_camel(
        self, scene_description: str, output_file: str, max_iterations: int = 3
    ) -> Optional[str]:
        """
        Camel-based Manim code generation implementation with template integration.
        Enhanced version with professional template awareness.
        """
        output_path = self._to_absolute_path(output_file)

        # Create task-specific prompt (system prompt is already set in agent initialization)
        task_prompt = self._create_task_prompt(scene_description, output_path)

        try:
            logger.info("Starting camel-based template-aware code generation...")

            # 保存原始prompt，用于后续迭代
            original_task_prompt = task_prompt

            for iteration in range(max_iterations):
                logger.info(f"Template-aware iteration {iteration + 1}/{max_iterations}")

                response = self.agent.step(task_prompt)

                if not response:
                    logger.error("Agent did not return a response.")
                    continue

                # Handle the response properly
                if hasattr(response, "msgs") and response.msgs:
                    # Handle multiple messages - use the last message
                    if len(response.msgs) > 1:
                        logger.info(f"Agent returned {len(response.msgs)} messages, using the last one")

                    last_msg = response.msgs[-1]
                    logger.info(f"Agent response: {last_msg.content}")
                else:
                    logger.error("Agent response has no messages.")
                    continue

                # Check if file was created and has no critical issues
                if Path(output_path).exists():
                    issues_result = check_code_issues([output_path], "error")
                    logger.info(f"Template iteration {iteration + 1}, Code issues: {issues_result}")

                    if "No issues found" in issues_result or "❌" not in issues_result:
                        # Additional template compliance check
                        template_check = self._check_template_compliance(output_path)
                        if template_check:
                            logger.success(f"Template-aware code generation completed after {iteration + 1} iterations")
                            return output_path
                        else:
                            logger.warning(f"Template compliance check failed on iteration {iteration + 1}")

                # 每次 agent.step 后都生成历史总结，用于下一轮迭代
                if iteration < max_iterations - 1:  # 不是最后一次迭代
                    try:
                        memory_context = self.agent.memory.get_context()
                        # 提取消息列表
                        if isinstance(memory_context, (list, tuple)) and len(memory_context) >= 1:
                            messages = memory_context[0] if isinstance(memory_context[0], list) else []
                        else:
                            messages = []

                        # 生成历史总结
                        history_summary = self._summarize_conversation_history(messages, "")
                    except Exception as e:
                        logger.warning(f"获取历史总结失败: {e}")
                        history_summary = ""

                    # 准备下一轮的错误信息
                    if Path(output_path).exists():
                        current_error = f"文件路径: {output_path}\n检测到的问题: {issues_result}"
                        # Add template compliance error if applicable
                        template_check = self._check_template_compliance(output_path)
                        if not template_check:
                            current_error += "\n模板合规性检查失败：请确保正确继承ProfessionalScienceTemplate并使用标准化接口"
                    else:
                        current_error = f"代码需要写入文件 {output_path} 中，但文件尚未创建。请确保使用 file_create 工具创建继承自ProfessionalScienceTemplate的代码文件。"

                    # 构造下一轮的 task_prompt
                    task_prompt = self._create_enhanced_prompt_with_history(
                        original_task_prompt, history_summary, current_error
                    )

            return output_path if Path(output_path).exists() else None

        except Exception as e:
            logger.error(f"Error in enhanced template-aware code generation: {e}")
            return None

    def _check_template_compliance(self, code_file_path: str) -> bool:
        """
        Check if the generated code complies with ProfessionalScienceTemplate standards.
        
        Args:
            code_file_path: Path to the generated code file
            
        Returns:
            True if template compliance is satisfied, False otherwise
        """
        try:
            with open(code_file_path, 'r', encoding='utf-8') as f:
                code_content = f.read()
            
            # Basic template compliance checks
            compliance_checks = [
                'ProfessionalScienceTemplate' in code_content,  # Template inheritance
                'def construct(self):' in code_content,  # Required method
                'self.setup_background()' in code_content,  # Required setup call
            ]
            
            # Check for at least one standardized interface usage
            interface_usage = any([
                'create_title_region_content' in code_content,
                'create_step_region_content' in code_content,
                'create_main_region_content' in code_content,
                'create_left_auxiliary_content' in code_content,
                'create_right_auxiliary_content' in code_content,
                'create_result_region_content' in code_content,
            ])
            
            compliance_checks.append(interface_usage)
            
            compliance_result = all(compliance_checks)
            
            if compliance_result:
                logger.info("Template compliance check passed")
            else:
                logger.warning("Template compliance check failed")
                
            return compliance_result
            
        except Exception as e:
            logger.error(f"Template compliance check error: {e}")
            return False

    def render_manim_code(self, code_file: str, quality: str = "l") -> Optional[str]:
        """
        Render Manim code to video.

        Args:
            code_file: Path to the Python code file
            quality: Rendering quality (l, m, h)

        Returns:
            Path to rendered video file, or None on failure
        """
        # Delegate to appropriate framework implementation
        if self.agent_framework == "smolagents" and self._smolagents_toolkit:
            return self._smolagents_toolkit.render_manim_code(code_file, quality)
        else:
            return self._render_manim_code_camel(code_file, quality)

    def _render_manim_code_camel(self, code_file: str, quality: str = "l") -> Optional[str]:
        """
        Camel-based Manim code rendering implementation.
        This is the original implementation moved to a separate method.
        """
        try:
            code_path = Path(code_file)
            if not code_path.exists():
                logger.error(f"Code file not found: {code_file}")
                return None

            # Prepare manim command
            cmd = [
                "manim",
                str(code_path),
                "--quality",
                quality,
            ]

            logger.info(f"Rendering Manim code: {' '.join(cmd)}")

            # Run manim command
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
            )

            if result.returncode == 0:
                resolution = {
                    "l": "480p15",
                    "m": "720p30",
                    "h": "1080p60",
                    "q": "1440p60",
                    "k": "2160p60",
                }[quality]
                output_path = Path("media") / "videos" / code_path.stem / resolution
                # Find the generated video file
                video_files = list(output_path.glob("*.mp4"))
                if video_files:
                    # Sort by modification time (newest first) and take the most recent one
                    video_file = max(video_files, key=lambda f: f.stat().st_mtime)
                    logger.success(f"Video rendered successfully: {video_file}")
                    return str(video_file)
                else:
                    logger.error(f"No video file found at {output_path} after rendering")
                    return None
            else:
                error_msg = result.stderr or result.stdout
                logger.error(f"Manim rendering failed: {error_msg}")
                raise subprocess.CalledProcessError(result.returncode, cmd, error_msg)

        except subprocess.TimeoutExpired:
            logger.error("Manim rendering timed out")
            return None
        except Exception as e:
            logger.error(f"Failed to render Manim code: {e}")
            raise


# ============================================================================
# ENHANCED SCENE CODE PROCESSOR (REFACTOR)
# ============================================================================


class EnhancedSceneCodeProcessorRefactor:
    """
    Enhanced processor for generating Manim code from scene description files with template integration.

    This refactored processor uses the enhanced toolkit with structured workflow,
    professional template integration, and autonomous debugging capabilities.
    """

    def __init__(self, output_dir: str = "output/generated_code"):
        """
        Initialize the enhanced scene code processor refactor.

        Args:
            output_dir: Directory to save generated code and videos
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.code_toolkit = EnhancedSceneCodeGenerationToolkitRefactor(str(self.output_dir))

    def process_scene_with_enhanced_workflow(
        self,
        scene_file: str,
        scene_description: str,
        max_iterations: int = 3,
        quality: str = "l",
        scene_num: int = 0,
        topic: str = "",
    ) -> Optional[dict[str, Optional[str]]]:
        """
        Process a scene description file with enhanced template-aware workflow.

        Args:
            scene_file: Path to the scene description file
            max_iterations: Maximum number of iterations for code improvement
            quality: Rendering quality (l, m, h, q, k)

        Returns:
            A dictionary containing paths to generated files, or None if processing failed
        """
        try:
            logger.info(f"[Scene {scene_file}] Starting enhanced template-aware processing")

            # Read scene description
            scene_path = Path(scene_file)
            if not scene_path.is_file():
                if not scene_description:
                    raise FileNotFoundError(
                        f"Scene description file not found: {scene_file}, and no scene description provided"
                    )
            else:
                with open(scene_path, encoding="utf-8") as f:
                    scene_description = f.read()

            # Generate output file paths
            scene_name = scene_path.stem
            if not scene_name:
                if topic:
                    scene_name = f"{topic}_{scene_num}"
                else:
                    scene_name = hashlib.md5(scene_description.encode("utf-8")).hexdigest()[:8]
            code_filename = self.output_dir / f"{scene_name}_enhanced_template_code.py"

            # Generate Manim code with enhanced template-aware workflow
            logger.info(f"Generating template-aware Manim code for scene: {scene_name}")
            generated_code_file = self.code_toolkit.generate_manim_code_enhanced(
                scene_description=scene_description,
                output_file=str(code_filename),
                max_iterations=max_iterations,
            )

            if not generated_code_file:
                raise ValueError(f"Failed to generate template-aware Manim code for scene: {scene_name}")

            result_paths = {"final_code_path": generated_code_file, "final_video_path": None, "success": False}

            # Render the code to video
            logger.info(f"Rendering template-aware Manim code to video: {generated_code_file}")
            rendered_video_file = self.code_toolkit.render_manim_code(code_file=generated_code_file, quality=quality)

            if rendered_video_file:
                result_paths["final_video_path"] = rendered_video_file
                result_paths["success"] = True
                logger.success(f"Enhanced template-aware scene processing completed: {scene_name}")
            else:
                logger.warning(f"Template-aware code generated but video rendering failed: {scene_name}")

            return result_paths

        except Exception as e:
            logger.error(f"[Scene {scene_file}] Enhanced template-aware processing failed: {e}")
            return None


# ============================================================================
# CONVENIENCE FUNCTIONS (REFACTOR)
# ============================================================================


def process_scene_file_enhanced_refactor(
    scene_file: str = "",
    scene_description: str = "",
    output_dir: str = "output/generated_code",
    max_iterations: int = 3,
    quality: str = "l",
    scene_num: int = 0,
    topic: str = "",
) -> Optional[dict[str, Optional[str]]]:
    """
    Process a single scene description file with enhanced template-aware workflow.

    Args:
        scene_file: Path to the scene description file
        scene_description: Scene description content
        output_dir: Directory to save generated files
        max_iterations: Maximum number of iterations for code improvement
        quality: Rendering quality (l, m, h, q, k)
        scene_num: Scene number for naming
        topic: Topic for naming

    Returns:
        Dictionary containing paths to generated files, or None on failure
    """
    processor = EnhancedSceneCodeProcessorRefactor(output_dir)

    result = processor.process_scene_with_enhanced_workflow(
        scene_file=scene_file,
        scene_description=scene_description,
        max_iterations=max_iterations,
        quality=quality,
        scene_num=scene_num,
        topic=topic,
    )
    return result


def test_enhanced_toolkit_refactor():
    """Test function to verify enhanced refactor toolkit initialization."""
    toolkit = EnhancedSceneCodeGenerationToolkitRefactor()
    tools = toolkit.get_all_tools()
    logger.info(f"Enhanced refactor toolkit test passed. Available tools: {len(tools)}")
    for tool in tools:
        logger.info(f"  - {tool.get_function_name()}")


# ============================================================================
# MAIN PROGRAM (REFACTOR)
# ============================================================================


def main():
    """Main execution function with enhanced template-aware workflow support."""
    parser = argparse.ArgumentParser(
        description="Enhanced Manim code generation with professional template integration and structured workflow"
    )
    parser.add_argument("scene_file", nargs="?", help="Path to the scene description file")
    parser.add_argument(
        "--quality",
        "-q",
        choices=["l", "m", "h", "q", "k"],
        default="l",
        help="Rendering quality: l (low), m (medium), h (high), q (2K), k (4K). Default: l",
    )
    parser.add_argument(
        "--max-iterations", "-i", type=int, default=3, help="Maximum iterations for code improvement. Default: 3"
    )
    parser.add_argument(
        "--output-dir",
        "-o",
        default="output/generated_code",
        help="Output directory for generated files. Default: output/generated_code",
    )

    parser.add_argument("--test", "-t", action="store_true", help="Test toolkit initialization and exit")

    args = parser.parse_args()

    if args.test:
        logger.info("Testing enhanced refactor toolkit initialization...")
        test_enhanced_toolkit_refactor()
        return

    if not args.scene_file:
        parser.error("scene_file is required when not in test mode")

    result = process_scene_file_enhanced_refactor(
        scene_file=args.scene_file,
        output_dir=args.output_dir,
        max_iterations=args.max_iterations,
        quality=args.quality,
    )

    if result:
        print("✅ Success! Generated files:")
        for key, path in result.items():
            print(f"  {key}: {path}")
        return result
    else:
        print("❌ Failed to process scene description.")
        return {}


if __name__ == "__main__":
    main() 