import json
import re
import os
import sys
from datetime import datetime
from dotenv import load_dotenv
import requests
from typing import Dict, Any, Tuple

load_dotenv()
import logging
from loguru import logger

from camel.agents import ChatAgent
from camel.logger import set_log_level
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.types import ModelPlatformType
import yaml

# 设置日志级别
set_log_level(level="WARNING")

# 确保必要的目录存在
os.makedirs("config", exist_ok=True)
os.makedirs("output", exist_ok=True)
os.makedirs("output/tests", exist_ok=True)


# FEATURE_DESCRIPTION = """
# 步骤一：执行mcp-cli chat --server markdownify，并交互输入"把test.pdf转换成markdown格式，并展示结果"
# 步骤二：在屏幕左侧打开输入的PNG文件用于演示
# 步骤三：在屏幕左侧打开转换后的Markdown文件用于演示，输出文件是/tmp/中的最新生成的文件
# 步骤四：关闭所有窗口完成演示

# 不同步骤之间需设置停顿间隔
# """
FEATURE_DESCRIPTION = """
步骤一：演示Qwen3的两种思考模式，用复杂的逻辑题和简单数据题进行演示
步骤二：演示Qwen3的多语言模式，用粤语、韩语和日语进行演示
步骤三：演示Qwen3的MCP兼容，但发现目前还不支持

不同步骤之间需设置停顿间隔
"""

STORYBOARD_FORMAT = """
[
    {{
        "分镜名": "分镜的简短标题",
        "分镜内容": "在这个开场中需要讲解的详细脚本内容",
        "素材名": "null",
    }}
]
"""

# 测试脚本生成提示词
TEST_SCRIPT_PROMPT = """
请根据以下功能描述，利用生成一个简洁的测试脚本：

{feature_description}

测试脚本应该包括以下内容：
1. 清屏并显示演示标题
2. 功能中的描述步骤，例如执行交互命令、打开文件、关闭文件等操作
3. 演示完成推出


#!/bin/bash
# 清屏并显示演示标题
clear
echo "======================================"
echo "       PPT转Markdown演示"
echo "======================================"
echo ""
sleep 1

# 步骤1：启动MCP-CLI并执行ppt转markdown命令
echo "1. 运行 mcp-cli chat --server markdownify 将PPT转换为Markdown"
sleep 1
echo "2. 输入转换指令..."
echo "把test.pptx转换成markdown格式，并展示结果" > /tmp/input_$$.txt
# 执行转换命令
(cat /tmp/input_$$.txt; sleep 5) | mcp-cli chat --server markdownify
# 清理临时输入文件
rm -f /tmp/input_$$.txt

# 步骤2：在屏幕左侧打开输入的PPT文件用于演示
# 获取屏幕宽度用于窗口位置设置
SCREEN_WIDTH=$(osascript -e 'tell application "Finder" to get bounds of window of desktop' | sed 's/, /\\n/g' | sed -n '3p')
WINDOW_WIDTH=$(($SCREEN_WIDTH / 2 - 50))
sleep 1
echo "3. 打开源PPT文件..."
# 先打开文件
open test.pptx
sleep 2
# 设置窗口位置到屏幕左侧
osascript -e "
tell application \\"System Events\\"
    set frontApp to first application process whose frontmost is true
    tell frontApp
        set position of first window to {{0, 50}}
        set size of first window to {{$WINDOW_WIDTH, 800}}
    end tell
end tell
"
sleep 1

# 向下滚动演示PPT内容
osascript -e '
tell application "System Events"
    set frontApp to first application process whose frontmost is true
    tell frontApp
        repeat 8 times
            key code 125 -- 向下箭头键
            delay 0.2 -- 滚动间隔
        end repeat
    end tell
end tell
'
sleep 1


# 步骤3：在屏幕右侧打开转换后的Markdown文件用于演示
echo "4. 打开转换后的Markdown文件..."
LATEST_FILE=$$(ls -t /tmp | head -n 1)
# 打开最新生成的文件
open "/tmp/${{LATEST_FILE}}"
sleep 2
# 设置窗口位置到屏幕右侧
RIGHT_X=$$(($SCREEN_WIDTH / 2))
osascript -e "
tell application \\"System Events\\"
    set frontApp to first application process whose frontmost is true
    tell frontApp
        set position of first window to {{$RIGHT_X, 50}}
        set size of first window to {{$WINDOW_WIDTH, 800}}
    end tell
end tell
"
sleep 1

# 滚动展示转换后的Markdown内容
osascript -e '
tell application "System Events"
    set frontApp to first application process whose frontmost is true
    tell frontApp
        repeat 50 times
            key code 125 -- 向下箭头键
            delay 0.01 -- 快速滚动
        end repeat
    end tell
end tell
'

# 关闭所有预览窗口
osascript -e 'tell application "Preview" to close every window' || 
osascript -e 'tell application "TextEdit" to close every window' || true

# 完成演示
echo "5. 演示完成!"
sleep 1
exit 0
"""

# 测试过程描述提示词
TEST_PROCESS_PROMPT = """
请根据以下功能描述，生成一个测试过程文档：

{feature_description}

示例如下
```json
[
    {{
        "分镜名": "开场介绍",
        "分镜内容": "41万星标开源神器MCP，轻松实现PDF文件转化Markdown格式",
        "素材路径": "/Users/<USER>/Documents/git/agentic-feynman/output/mcp-markdown/web.mp4"
    }},
    {{
        "分镜名": "演示讲解",
        "分镜内容": "现在，我们使用mcp-cli工具，只需一条命令，就能将PDF转换成Markdown格式。左侧是原始PDF文件，右侧是转换后的Markdown文件，整个过程快速、准确，轻松实现PDF内容文本化！",
        "素材路径": "/Users/<USER>/Documents/git/agentic-feynman/output/mcp-markdown/pdf2md.mp4"
    }},
    {{
        "分镜名": "片尾总结",
        "分镜内容": "这个MCP不止能转PDF！图片、音频、PPT一键变Markdown，快去试试吧",
        "素材路径": "/Users/<USER>/Documents/git/agentic-feynman/output/mcp-markdown/web.mp4"
    }}
]  
```

请严格按照以上JSON格式和要求输出结果，只返回JSON格式内容，格式必须与示例完全一致，同时保证满足以下要求：
1. 一共3个分镜，开场介绍、演示讲解和片尾总结。
2. 开场介绍：强调项目能力，吸引眼球，保证字数20-30字
3. 演示讲解：从功能角度介绍即可，不需要详细到具体命令，保证字数75-85字
4. 片尾总结：进一步强调其他能力，勾起兴趣，保证20-30字
"""

class TestScriptAgent:
    def __init__(self, config_path="config/config.yaml"):
        """初始化测试脚本生成代理"""
        # 加载配置
        self.load_config(config_path)
        
        # 初始化模型
        self.model = self._create_model()
        logging.warning("模型已初始化")
        
        # 初始化代理
        self.agents = self._initialize_agents()
        logging.warning("代理已初始化")
    
    def _create_model(self):
        """根据配置创建模型实例"""
        api_config = self.model_config.get("api", {})

        return ModelFactory.create(
            model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
            model_type=self.model_config.get("type", "openai/gpt-4o-mini"),
            api_key=api_config.get("openai_compatibility_api_key"),
            url=api_config.get("openai_compatibility_api_base_url"),
        )

    def load_config(self, config_path):
        """从YAML文件加载配置"""
        try:
            with open(config_path) as file:
                config = yaml.safe_load(file)
            # 设置配置属性
            self.model_config = config.get("model", {})
            self.repo_config = config.get("repo", {})
            self.agent_config = config.get("agents", {})
        except Exception as e:
            logging.error(f"加载配置出错: {str(e)}")
            raise
    
    def _initialize_agents(self):
        """初始化代理"""
        agents = {}
        
        # 测试脚本生成代理
        agents["test_script_generator"] = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="测试脚本生成者",
                content="你负责创建简洁明了的测试脚本，包括自动化执行的shell脚本。",
            ),
            model=self.model,
        )
        
        # 测试过程生成代理
        agents["test_process_generator"] = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="讲解内容生成者",
                content="你负责生成项目讲解内容的生成，包括开场介绍、演示讲解和片尾总结。",
            ),
            model=self.model,
        )
        
        return agents
    
    def process_message(self, agent_name, message):
        """处理消息并获取响应"""
        agent = self.agents.get(agent_name)
        if not agent:
            logging.error(f"未找到代理 {agent_name}")
            return f"错误: 未找到代理 {agent_name}"
        
        # 发送消息并获取响应
        response = agent.step(message)
        
        # 获取响应内容
        content = response.msgs[0].content
        
        # 查找代码块（Shell脚本）
        code_block_pattern = r"```(?:bash|sh)?\s*([\s\S]*?)```"
        matches = re.findall(code_block_pattern, content)
        if matches and agent_name == "test_script_generator":
            # 保存Shell脚本到文件
            script_content = matches[0]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            script_file = f"output/tests/test_script_{timestamp}.sh"
            
            try:
                with open(script_file, "w") as f:
                    f.write(script_content)
                os.chmod(script_file, 0o755)  # 使脚本可执行
                logging.info(f"Shell脚本已保存到 {script_file}")
                print(f"Shell脚本已保存到: {script_file}")
            except Exception as e:
                logging.error(f"保存Shell脚本时出错: {str(e)}")
                
        return content
    
    def generate_test_script(self, feature_description):
        """生成测试脚本"""
        print(f"正在为以下功能生成测试脚本:\n{feature_description[:100]}...")
        
        # 使用功能描述格式化提示词
        prompt = TEST_SCRIPT_PROMPT.format(feature_description=feature_description)
        
        # 处理消息
        result = self.process_message("test_script_generator", prompt)
        
        # 保存结果到文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"output/tests/test_script_{timestamp}.md"
        
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(result)
            
        print(f"测试脚本已生成并保存到: {output_file}")
        return result, output_file
    
    def generate_test_process(self, feature_description):
        """生成讲解内容"""
        print(f"正在为以下功能生成讲解内容:\n{feature_description[:100]}...")
        
        # 使用功能描述格式化提示词
        prompt = TEST_PROCESS_PROMPT.format(feature_description=feature_description)
        
        # 处理消息
        result = self.process_message("test_process_generator", prompt)
        
        # 保存结果到文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"output/tests/test_process_{timestamp}.md"
        
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(result)
            
        print(f"测试过程文档已生成并保存到: {output_file}")
        return result, output_file
    
    def fetch_repo_info(self, repo_url: str) -> Tuple[str, Dict[str, Any]]:
        """获取GitHub仓库信息并使用代理总结功能和热度"""
        print(f"正在获取仓库信息: {repo_url}")
        
        # 从URL中提取所有者和仓库名
        match = re.search(r'github\.com/([^/]+)/([^/]+)', repo_url)
        if not match:
            error_msg = f"无效的GitHub URL: {repo_url}"
            print(error_msg)
            return error_msg, {}
            
        owner, repo = match.groups()
        repo = repo.rstrip('.git')
        
        # 获取仓库基本信息
        try:
            headers = {}
            if github_token := os.getenv("GITHUB_TOKEN"):
                headers["Authorization"] = f"token {github_token}"
                
            repo_response = requests.get(
                f"https://api.github.com/repos/{owner}/{repo}", 
                headers=headers
            )
            repo_response.raise_for_status()
            repo_data = repo_response.json()
            
            # 获取仓库README
            readme_response = requests.get(
                f"https://api.github.com/repos/{owner}/{repo}/readme",
                headers=headers
            )
            readme_content = ""
            if readme_response.status_code == 200:
                import base64
                readme_data = readme_response.json()
                if readme_data.get("content"):
                    readme_content = base64.b64decode(readme_data["content"]).decode("utf-8")
            
            # 整合仓库信息
            repo_info = {
                "name": repo_data.get("name"),
                "full_name": repo_data.get("full_name"),
                "description": repo_data.get("description"),
                "stars": repo_data.get("stargazers_count"),
                "forks": repo_data.get("forks_count"),
                "issues": repo_data.get("open_issues_count"),
                "language": repo_data.get("language"),
                "created_at": repo_data.get("created_at"),
                "updated_at": repo_data.get("updated_at"),
                "readme": readme_content[:2000] if readme_content else ""  # 限制README长度
            }
            
            # 使用代理总结项目信息
            summary = self._summarize_repo_info(repo_info)
            
            return summary, repo_info
            
        except requests.RequestException as e:
            error_msg = f"获取仓库信息失败: {str(e)}"
            print(error_msg)
            return error_msg, {}
    
    def _summarize_repo_info(self, repo_info: Dict[str, Any]) -> str:
        """使用代理总结仓库信息"""
        # 初始化仓库信息总结代理
        if "repo_summarizer" not in self.agents:
            self.agents["repo_summarizer"] = ChatAgent(
                system_message=BaseMessage.make_assistant_message(
                    role_name="仓库信息总结者",
                    content="你负责总结GitHub仓库的核心功能和项目热度，提供简洁明了的项目描述。",
                ),
                model=self.model,
            )
        
        # 构建提示词
        prompt = f"""
        请根据以下GitHub仓库信息，总结该项目的核心功能和项目热度：
        
        仓库名称: {repo_info.get('name')}
        仓库描述: {repo_info.get('description')}
        星标数量: {repo_info.get('stars')}
        分叉数量: {repo_info.get('forks')}
        主要语言: {repo_info.get('language')}
        创建时间: {repo_info.get('created_at')}
        最近更新: {repo_info.get('updated_at')}
        
        README摘要:
        {repo_info.get('readme', '无README信息')}
        
        请提供：
        1. 该项目的核心功能和主要用途（2-3句话）
        2. 项目热度描述，包括星标数和社区活跃度（1句话）
        3. 该项目可以解决的主要问题（1-2句话）
        
        回答格式应为简洁的段落，不要使用标题或编号。
        """
        
        # 处理消息并获取响应
        response = self.agents["repo_summarizer"].step(prompt)
        summary = response.msgs[0].content
        
        # 保存结果到文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"output/tests/repo_summary_{timestamp}.md"
        
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(summary)
            
        print(f"仓库信息总结已保存到: {output_file}")
        return summary
    
    def generate_all(self, feature_description, repo_url=None):
        """生成全部测试文档"""
        # 获取仓库信息（优先使用参数传入的URL，其次从配置中读取）
        if repo_url is None:
            repo_url = self.repo_config.get("url", "")
        
        repo_summary = ""
        if repo_url:
            repo_summary, repo_info = self.fetch_repo_info(repo_url)
            # 如果成功获取到仓库信息，将其添加到功能描述中
            if repo_summary:
                enhanced_description = f"""
项目信息：
{repo_summary}

测试内容：
{feature_description}
"""
                feature_description = enhanced_description
                print("已将仓库信息添加到功能描述中")
        
        # 生成测试脚本
        script_result, script_file = self.generate_test_script(feature_description)
        
        # 生成讲解内容
        process_result, process_file = self.generate_test_process(feature_description)
        
        # 返回生成的文件路径
        return {
            "test_script": script_file,
            "test_process": process_file,
            "repo_summary": repo_summary if repo_url else "未提供仓库URL",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

def main():
    """主函数"""
    import argparse
    
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description="测试脚本和讲解内容生成器")
    parser.add_argument("--repo-url", type=str, default="https://github.com/zcaceres/markdownify-mcp",
                     help="GitHub仓库URL，用于获取项目信息")
    parser.add_argument("--feature", type=str, default=FEATURE_DESCRIPTION,
                      help="功能描述文本，用于生成测试脚本")
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 初始化测试脚本代理
    agent = TestScriptAgent()
    
    # 生成测试文档
    result = agent.generate_all(args.feature, repo_url=args.repo_url)
    
    print("\n测试文档生成完成！")
    print(f"测试脚本: {result['test_script']}")
    print(f"讲解内容: {result['test_process']}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 