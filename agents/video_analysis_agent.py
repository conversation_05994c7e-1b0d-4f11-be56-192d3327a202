#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频分析Agent

该Agent具备四个功能：
1. 客观详细分析视频内容和逻辑
2. 视频视觉评估功能（布局合理性、视觉清晰度、动画流畅度、逻辑一致性等美学维度）
3. 基于视频描述，评估视频内容哪些做的非常惊艳值得学习，并给出详细的复刻意见
4. 专门为manim生成的视频，针对美学评估功能，给出详细的修改意见
"""

import argparse
import base64
import cv2
import datetime
import logging
import os
import sys
import tempfile
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# 添加父目录到导入路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from camel.messages import BaseMessage
from utils.common import AgentFactory, Config

# 配置日志
logger = logging.getLogger(__name__)

class VideoAnalysisAgent:
    def __init__(self, config_path="config/config.yaml"):
        """初始化视频分析Agent"""
        self.config_path = config_path
        self.load_config()
        
        # 初始化模型
        self.config = Config(config_path)
        self.model = AgentFactory.create_model(self.config)
        
        # 创建不同功能的分析代理
        self.content_analyzer = AgentFactory.create_analyzer_agent(
            self.model,
            "视频内容分析专家",
            "你是一个专业的视频内容分析专家，能够详细分析视频中的内容、逻辑结构和信息传达方式。"
        )
        
        self.aesthetics_evaluator = AgentFactory.create_analyzer_agent(
            self.model,
            "视觉美学评估专家",
            "你是一个专业的视觉美学评估专家，能够从布局合理性、视觉清晰度、动画流畅度、逻辑一致性等维度评估视频的美学质量。"
        )
        
        self.highlight_analyzer = AgentFactory.create_analyzer_agent(
            self.model,
            "视频亮点分析专家",
            "你是一个专业的视频亮点分析专家，能够识别视频中的精彩设计和创新点，并提供详细的复刻建议。"
        )
        
        self.manim_optimizer = AgentFactory.create_analyzer_agent(
            self.model,
            "Manim优化专家",
            "你是一个专业的Manim动画优化专家，能够针对Manim生成的视频提出具体的代码和设计改进建议。"
        )
        
        logger.info("视频分析Agent初始化完成")

    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            self.video_analysis_config = config.get("video_analysis", {})
            
            # 默认配置
            default_config = {
                "content_analysis": True,
                "aesthetics_evaluation": True,
                "highlight_analysis": True,
                "manim_optimization": True,
                "frame_extraction": {
                    "max_frames": 10,
                    "interval_seconds": 5
                }
            }
            
            # 合并默认配置
            for key, value in default_config.items():
                if key not in self.video_analysis_config:
                    self.video_analysis_config[key] = value
                    
            logger.info(f"视频分析配置加载完成: {self.video_analysis_config}")
            
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            # 使用默认配置
            self.video_analysis_config = {
                "content_analysis": True,
                "aesthetics_evaluation": True,
                "highlight_analysis": True,
                "manim_optimization": True,
                "frame_extraction": {
                    "max_frames": 10,
                    "interval_seconds": 5
                }
            }

    def extract_video_frames(self, video_path: str) -> List[Tuple[str, float]]:
        """从视频中提取关键帧"""
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise Exception(f"无法打开视频文件: {video_path}")
            
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = frame_count / fps
            
            max_frames = self.video_analysis_config["frame_extraction"]["max_frames"]
            interval_seconds = self.video_analysis_config["frame_extraction"]["interval_seconds"]
            
            # 计算帧间隔
            if duration <= interval_seconds * max_frames:
                # 如果视频较短，平均分布帧
                frame_interval = max(1, frame_count // max_frames)
            else:
                # 按时间间隔提取帧
                frame_interval = int(fps * interval_seconds)
            
            frames = []
            frame_number = 0
            
            with tempfile.TemporaryDirectory() as temp_dir:
                while len(frames) < max_frames:
                    cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
                    ret, frame = cap.read()
                    
                    if not ret:
                        break
                    
                    # 保存帧到临时文件
                    timestamp = frame_number / fps
                    frame_path = os.path.join(temp_dir, f"frame_{len(frames):03d}.jpg")
                    cv2.imwrite(frame_path, frame)
                    
                    # 读取并转换为base64
                    with open(frame_path, 'rb') as f:
                        frame_data = f.read()
                    
                    frame_base64 = base64.b64encode(frame_data).decode('utf-8')
                    frames.append((frame_base64, timestamp))
                    
                    frame_number += frame_interval
                    
                    if frame_number >= frame_count:
                        break
            
            cap.release()
            logger.info(f"从视频中提取了 {len(frames)} 帧")
            return frames
            
        except Exception as e:
            logger.error(f"提取视频帧失败: {e}")
            return []

    def analyze_content(self, video_path: str, frames: List[Tuple[str, float]]) -> str:
        """功能1: 客观详细分析视频内容和逻辑"""
        if not self.video_analysis_config.get("content_analysis", True):
            return "内容分析功能已禁用"
        
        try:
            # 构建分析提示
            analysis_prompt = """
请对这个视频进行客观详细的内容分析，包括：

1. **视频概览**
   - 视频时长和整体结构
   - 主要内容主题和目标
   - 信息传达方式和风格

2. **内容逻辑分析**
   - 内容组织结构和逻辑脉络
   - 知识点呈现顺序和递进关系
   - 重点内容的强调方式

3. **视觉元素分析**
   - 主要视觉元素和图形类型
   - 色彩运用和视觉层次
   - 文字信息的呈现方式

4. **叙事结构**
   - 开场、发展、高潮、结尾的安排
   - 过渡和衔接的处理方式
   - 节奏控制和时间分配

5. **教学效果**
   - 概念解释的清晰度
   - 示例和类比的恰当性
   - 互动性和参与度设计

请基于提供的视频帧进行分析，生成详细的markdown报告。
"""
            
            # 构建消息内容
            message_content = [{"type": "text", "text": analysis_prompt}]
            
            # 添加关键帧
            for i, (frame_base64, timestamp) in enumerate(frames):
                message_content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{frame_base64}",
                        "detail": "high"
                    }
                })
                message_content.append({
                    "type": "text",
                    "text": f"**帧 {i+1}** (时间: {timestamp:.2f}s)"
                })
            
            # 调用AI分析
            response = self.content_analyzer.step(
                BaseMessage.make_user_message(
                    role_name="User",
                    content=message_content
                )
            )
            
            result = response.msg.content
            logger.info(f"视频内容分析完成，生成内容长度: {len(result)} 字符")
            return result
            
        except Exception as e:
            logger.error(f"视频内容分析失败: {e}")
            return f"# 视频内容分析报告\n\n## 错误信息\n分析失败: {str(e)}"

    def evaluate_aesthetics(self, video_path: str, frames: List[Tuple[str, float]]) -> str:
        """功能2: 视频视觉评估功能"""
        if not self.video_analysis_config.get("aesthetics_evaluation", True):
            return "美学评估功能已禁用"
        
        try:
            analysis_prompt = """
请对这个视频进行专业的视觉美学评估，重点关注以下维度：

1. **布局合理性**
   - 对象是否重叠？是否居中对齐？
   - 留白是否足够？版面是否平衡？
   - 元素分布是否合理？

2. **视觉清晰度**
   - 字体大小是否易读？
   - 颜色对比度是否足够？
   - 图形元素是否清晰可辨？

3. **动画流畅度**
   - 动画节奏是否过快或过慢？
   - 运动曲线是否自然？
   - 转场效果是否流畅？

4. **逻辑一致性**
   - 视觉设计是否与内容逻辑匹配？
   - 颜色运用是否一致？
   - 风格是否统一？

5. **整体美学质量**
   - 视觉吸引力和专业度
   - 创意性和独特性
   - 用户体验和观看舒适度

请对每个维度给出详细评估和改进建议，并给出1-10分的评分。
"""
            
            message_content = [{"type": "text", "text": analysis_prompt}]
            
            for i, (frame_base64, timestamp) in enumerate(frames):
                message_content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{frame_base64}",
                        "detail": "high"
                    }
                })
                message_content.append({
                    "type": "text",
                    "text": f"**帧 {i+1}** (时间: {timestamp:.2f}s)"
                })
            
            response = self.aesthetics_evaluator.step(
                BaseMessage.make_user_message(
                    role_name="User",
                    content=message_content
                )
            )
            
            result = response.msg.content
            logger.info(f"视频美学评估完成，生成内容长度: {len(result)} 字符")
            return result
            
        except Exception as e:
            logger.error(f"视频美学评估失败: {e}")
            return f"# 视频美学评估报告\n\n## 错误信息\n评估失败: {str(e)}"

    def analyze_highlights(self, video_path: str, frames: List[Tuple[str, float]], content_analysis: str) -> str:
        """功能3: 评估视频亮点并给出复刻建议"""
        if not self.video_analysis_config.get("highlight_analysis", True):
            return "亮点分析功能已禁用"
        
        try:
            analysis_prompt = f"""
基于以下视频内容分析，请识别视频中的亮点和值得学习的设计：

## 视频内容分析参考
{content_analysis}

## 分析要求
请识别并分析以下方面的亮点：

1. **创新设计亮点**
   - 独特的视觉表现手法
   - 创新的信息呈现方式
   - 巧妙的交互设计

2. **技术实现亮点**
   - 复杂的动画效果
   - 精巧的视觉技巧
   - 高质量的制作工艺

3. **教学设计亮点**
   - 出色的概念解释方式
   - 有效的学习引导
   - 优秀的节奏控制

4. **复刻建议**
   对于每个亮点，请提供：
   - 具体的实现思路
   - 关键技术要点
   - 适用场景和注意事项
   - 如何改进和创新

请基于提供的视频帧进行分析，重点关注那些让人印象深刻的设计。
"""
            
            message_content = [{"type": "text", "text": analysis_prompt}]
            
            for i, (frame_base64, timestamp) in enumerate(frames):
                message_content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{frame_base64}",
                        "detail": "high"
                    }
                })
                message_content.append({
                    "type": "text",
                    "text": f"**帧 {i+1}** (时间: {timestamp:.2f}s)"
                })
            
            response = self.highlight_analyzer.step(
                BaseMessage.make_user_message(
                    role_name="User",
                    content=message_content
                )
            )
            
            result = response.msg.content
            logger.info(f"视频亮点分析完成，生成内容长度: {len(result)} 字符")
            return result
            
        except Exception as e:
            logger.error(f"视频亮点分析失败: {e}")
            return f"# 视频亮点分析报告\n\n## 错误信息\n分析失败: {str(e)}"

    def optimize_manim(self, video_path: str, frames: List[Tuple[str, float]], aesthetics_evaluation: str) -> str:
        """功能4: 专门为manim生成的视频提供优化建议"""
        if not self.video_analysis_config.get("manim_optimization", True):
            return "Manim优化功能已禁用"
        
        try:
            analysis_prompt = f"""
基于以下美学评估结果，请为这个Manim生成的视频提供具体的优化建议：

## 美学评估参考
{aesthetics_evaluation}

## 优化建议要求
请针对Manim动画的特点，提供以下方面的具体优化建议：

1. **代码层面优化**
   - 动画参数调整建议
   - 布局和定位优化
   - 颜色和样式改进

2. **视觉效果优化**
   - 动画曲线和时间控制
   - 元素出现和消失效果
   - 强调和突出效果

3. **性能优化**
   - 渲染效率提升
   - 内存使用优化
   - 文件大小控制

4. **用户体验优化**
   - 观看舒适度提升
   - 信息传达效率
   - 互动性增强

5. **具体实现建议**
   - 提供伪代码或代码片段
   - 推荐使用的Manim功能
   - 参数设置建议

请基于视频帧的实际表现，给出具体可操作的优化方案。
"""
            
            message_content = [{"type": "text", "text": analysis_prompt}]
            
            for i, (frame_base64, timestamp) in enumerate(frames):
                message_content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{frame_base64}",
                        "detail": "high"
                    }
                })
                message_content.append({
                    "type": "text",
                    "text": f"**帧 {i+1}** (时间: {timestamp:.2f}s)"
                })
            
            response = self.manim_optimizer.step(
                BaseMessage.make_user_message(
                    role_name="User",
                    content=message_content
                )
            )
            
            result = response.msg.content
            logger.info(f"Manim优化建议完成，生成内容长度: {len(result)} 字符")
            return result
            
        except Exception as e:
            logger.error(f"Manim优化建议失败: {e}")
            return f"# Manim优化建议报告\n\n## 错误信息\n分析失败: {str(e)}"

    def analyze_video(self, video_path: str) -> Dict[str, str]:
        """分析视频的主要入口函数"""
        logger.info(f"开始分析视频: {video_path}")
        
        # 检查视频文件是否存在
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")
        
        # 创建输出目录
        video_name = os.path.splitext(os.path.basename(video_path))[0]
        output_dir = os.path.join("output", video_name)
        os.makedirs(output_dir, exist_ok=True)
        
        # 创建日志目录
        log_dir = os.path.join(output_dir, "logs")
        os.makedirs(log_dir, exist_ok=True)
        
        # 提取视频帧
        frames = self.extract_video_frames(video_path)
        if not frames:
            raise Exception("无法提取视频帧")
        
        results = {}
        
        # 功能1: 内容分析
        if self.video_analysis_config.get("content_analysis", True):
            logger.info("执行功能1: 内容分析")
            content_analysis = self.analyze_content(video_path, frames)
            content_file = os.path.join(output_dir, "content_analysis.md")
            with open(content_file, 'w', encoding='utf-8') as f:
                f.write(content_analysis)
            results["content_analysis"] = content_file
        
        # 功能2: 美学评估
        if self.video_analysis_config.get("aesthetics_evaluation", True):
            logger.info("执行功能2: 美学评估")
            aesthetics_evaluation = self.evaluate_aesthetics(video_path, frames)
            aesthetics_file = os.path.join(output_dir, "aesthetics_evaluation.md")
            with open(aesthetics_file, 'w', encoding='utf-8') as f:
                f.write(aesthetics_evaluation)
            results["aesthetics_evaluation"] = aesthetics_file
        
        # 功能3: 亮点分析
        if self.video_analysis_config.get("highlight_analysis", True):
            logger.info("执行功能3: 亮点分析")
            content_analysis_text = content_analysis if 'content_analysis' in locals() else "内容分析未执行"
            highlight_analysis = self.analyze_highlights(video_path, frames, content_analysis_text)
            highlight_file = os.path.join(output_dir, "highlight_analysis.md")
            with open(highlight_file, 'w', encoding='utf-8') as f:
                f.write(highlight_analysis)
            results["highlight_analysis"] = highlight_file
        
        # 功能4: Manim优化
        if self.video_analysis_config.get("manim_optimization", True):
            logger.info("执行功能4: Manim优化")
            aesthetics_text = aesthetics_evaluation if 'aesthetics_evaluation' in locals() else "美学评估未执行"
            manim_optimization = self.optimize_manim(video_path, frames, aesthetics_text)
            manim_file = os.path.join(output_dir, "manim_optimization.md")
            with open(manim_file, 'w', encoding='utf-8') as f:
                f.write(manim_optimization)
            results["manim_optimization"] = manim_file
        
        logger.info(f"视频分析完成，结果保存在: {output_dir}")
        return results


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="视频分析Agent")
    parser.add_argument("--video", help="视频文件路径")
    parser.add_argument("--config", default="config/config.yaml", help="配置文件路径")
    parser.add_argument("--test", action="store_true", help="运行测试用例")
    args = parser.parse_args()
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(f"output/video_analysis_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
            logging.StreamHandler()
        ]
    )
    
    try:
        # 初始化Agent
        agent = VideoAnalysisAgent(args.config)
        
        # 确定要分析的视频
        video_path = None
        
        if args.test:
            # 测试用例：使用指定的测试视频
            test_video_path = "output/2507.00432/2507.00432_with_music.mp4"
            if os.path.exists(test_video_path):
                video_path = test_video_path
                print(f"🧪 运行测试用例")
                print(f"📁 测试视频: {test_video_path}")
            else:
                print(f"❌ 测试视频不存在: {test_video_path}")
                print("请确保测试视频文件存在，或使用 --video 参数指定其他视频")
                return
        elif args.video:
            if os.path.exists(args.video):
                video_path = args.video
            else:
                print(f"❌ 指定的视频文件不存在: {args.video}")
                return
        else:
            print("❌ 请指定视频文件 (--video) 或运行测试用例 (--test)")
            parser.print_help()
            return
        
        # 分析视频
        print(f"\n🎬 开始分析视频...")
        print(f"📁 视频路径: {video_path}")
        
        results = agent.analyze_video(video_path)
        
        # 输出结果
        print(f"\n✅ 视频分析完成！")
        print(f"📊 分析结果:")
        
        for func_name, file_path in results.items():
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"  ✓ {func_name}: {file_path} ({file_size} bytes)")
            else:
                print(f"  ✗ {func_name}: {file_path} (文件不存在)")
        
        # 显示功能统计
        total_functions = len(results)
        successful_functions = sum(1 for file_path in results.values() if os.path.exists(file_path))
        print(f"\n📈 功能统计: {successful_functions}/{total_functions} 个功能成功完成")
        
    except Exception as e:
        logger.error(f"视频分析失败: {e}")
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 