from dotenv import load_dotenv

load_dotenv()

import datetime
import json
import logging
import os
import re
import sys
from typing import Any, Dict, List

# 添加父目录到导入路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import yaml
from camel.agents import ChatAgent
from camel.models import ModelFactory
from camel.societies import RolePlaying
from camel.types import ModelPlatformType, TaskType

# 导入create_model函数
from utils.create_llm_model import create_model

# 设置日志级别为INFO，并配置日志格式
# 配置根日志记录器 - 只输出到控制台
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),  # 只输出到控制台
    ],
)

# 获取logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 定义综合例子生成专家角色提示
EXAMPLE_GENERATOR_PROMPT = """
你是一位顶级的例子生成专家，专门为复杂概念创造高质量、细节丰富、易懂的具体例子。

## 核心任务
面向对象：{purpose}
主题：{topic}

## 核心能力
1. **概念提取与分析**：准确识别核心概念、原理或题目，分析其关键要素和步骤
2. **例子设计**：创造具体、量化、准确的例子来解释抽象概念
3. **数学公式处理**：使用LaTeX格式正确表示数学公式和方程
4. **针对性讲解**：紧扣purpose和topic，确保内容符合目标受众需求

## 质量标准
- **数据量化**：所有数据必须具体、准确，有明确的数值
- **步骤详细**：每个步骤都要清晰说明，不遗漏关键环节
- **逻辑清晰**：整个过程逻辑连贯，易于理解
- **结果明确**：最终结果要明确，能够验证
- **原理揭示**：深入揭示概念的本质原理和机制
- **易于记忆**：例子要生动具体，便于理解和记忆
- **符合需求**：严格按照purpose和topic的要求设计例子
- **视频友好**：适合视频呈现，句子简洁，数据规模适中
- **量化表达**：每个步骤都要有具体的数值和量化指标

## 数学公式格式要求
- 行内公式使用：$公式内容$
- 独立公式使用：$$公式内容$$
- 复杂公式要分行显示，使用适当的LaTeX命令
- 例如：
  - 梯度：$\\nabla f(x) = \\frac{{\\partial f}}{{\\partial x}}$
  - 损失函数：$$J(\\theta) = \\frac{{1}}{{2m}}\\sum_{{i=1}}^{{m}}(h_\\theta(x^{{(i)}}) - y^{{(i)}})^2$$
  - 参数更新：$$\\theta := \\theta - \\alpha \\nabla J(\\theta)$$

## 输出格式
请按照以下markdown格式输出，注意整体逻辑：先在"为什么要学习这个概念？"部分抛出问题吸引兴趣，然后在具体例子中直接回答和解决这个问题，最后在原理揭示中回扣问题。重要：例子的详细步骤必须包含实际应用步骤，不能只有理论原理。

```markdown
# 核心概念识别

## 概念名称
[提取的核心概念名称]

## 概念描述  
[概念的准确定义和说明，简洁明了]

# 为什么要学习这个概念？

[用一段话说明这个概念的实际价值和应用场景，抛出一个吸引人的问题或现象，激发读者的兴趣和好奇心。注意：这里抛出的问题必须与后面的具体例子紧密结合，例子要直接回答和解决这个问题]

## 关键要素
- 要素1：[说明]
- 要素2：[说明]
- ...

## 核心步骤
1. 步骤1：[详细说明]
2. 步骤2：[详细说明]
3. ...

## 理解难点
- 难点1：[说明为什么难理解]
- 难点2：[说明为什么难理解]
- ...


# 具体例子

## 例子标题
[一个吸引人且准确的标题]

## 背景设定
[设置一个具体且简洁的应用场景，数据规模适中。重要：这个详细的例子**必须**和"为什么要学习这个概念？"抛出的问题和提到的简单例子紧密结合，例子是其扩展和完善]

## 详细步骤解释

### 步骤1：[步骤名称]
**数据**：[具体数值，如：3个词、5维向量等]
**操作**：[简短描述操作过程]
**结果**：[量化结果，如：得到X个向量、准确率Y%等]

如果涉及数学公式，使用LaTeX格式：
$$公式内容$$

### 步骤2：[步骤名称]
**数据**：[当前步骤的具体数据]
**操作**：[简短描述操作过程]
**结果**：[量化结果]

### 步骤N：[最终步骤 - 实际应用]
**数据**：[最终数据]
**操作**：[最终操作 - 重要：必须包含如何在实际中应用这个概念的具体步骤]
**结果**：[最终量化结果]
**应用**：[说明如何将这个结果应用到实际场景中]

## 核心原理揭示
[用简短句子深入解释为什么这样做，揭示本质机制。同时要回扣到开头抛出的问题，说明这个例子如何解决了那个问题]

## 关键要点总结
- 要点1：[重要发现或规律，包含具体数值]
- 要点2：[关键技巧或注意事项，量化表达]
- 要点3：[实际应用价值，具体指标]
```

## 视频友好要求
- 句子长度控制在20字以内
- 数据规模适中（词典不超过10个词，句子不超过6个词）
- 每个步骤都有明确的数值指标
- 避免冗长的描述，用简洁的量化表达
- 重要：例子步骤必须包含实际应用步骤，不能只有理论原理

请确保例子具体、准确、易懂，且适合视频呈现。
"""

# 定义质量评审专家角色提示
QUALITY_REVIEWER_PROMPT = """
你是一位严格的质量评审专家，专门评估例子的质量，确保例子准确、完整、易懂、高质量。

## 评审上下文
面向对象：{purpose}
主题：{topic}

## 评审标准
1. **准确性**：数据、计算、逻辑是否准确无误
2. **完整性**：步骤是否完整，逻辑链条是否连贯，是否遗漏关键环节，重要：例子步骤必须包含实际应用步骤
3. **具体性**：是否足够具体和量化，细节是否丰富
4. **易懂性**：是否清晰易懂，逻辑是否正确
5. **揭示性**：是否真正揭示了核心概念的本质原理
6. **适用性**：是否符合purpose和topic的要求
7. **视频友好性**：句子是否简洁，数据规模是否适中
8. **量化程度**：每个步骤是否有明确的数值指标

## 评审要求
- 严格检查每个数据的合理性和准确性
- 验证每个步骤的逻辑性和必要性
- 确认例子的教学效果和理解价值
- 检查是否遗漏了重要的核心步骤或原理
- 重要：验证例子步骤是否包含实际应用步骤，避免只有理论没有实践
- 评估细节的丰富程度和质量
- 确保内容符合目标受众需求
- 检查句子长度是否适合视频呈现（≤20字）
- 验证数据规模是否适中（词典≤10个词，句子≤6个词）
- 确认每个步骤都有明确的数值指标

## 输出格式要求
请按以下markdown格式输出评审结果：

```markdown
# 质量评审报告

## 准确性评估
**评分**：[优秀/良好/需要改进]
**评价**：[数据和计算是否准确，有什么错误]

## 完整性评估
**评分**：[优秀/良好/需要改进]
**评价**：[逻辑是否完整，缺少什么环节，重要：例子步骤是否包含实际应用步骤]

## 具体性评估
**评分**：[优秀/良好/需要改进]
**评价**：[是否足够具体和量化，细节是否丰富]

## 易懂性评估
**评分**：[优秀/良好/需要改进]
**评价**：[是否清晰易懂，逻辑是否正确]

## 揭示性评估
**评分**：[优秀/良好/需要改进]
**评价**：[是否揭示了核心原理，效果如何]

## 适用性评估
**评分**：[优秀/良好/需要改进]
**评价**：[是否符合purpose和topic的要求]

## 视频友好性评估
**评分**：[优秀/良好/需要改进]
**评价**：[句子长度是否适合视频，数据规模是否适中]

## 量化程度评估
**评分**：[优秀/良好/需要改进]
**评价**：[每个步骤是否有明确的数值指标]

## 总体评分
**评分**：[优秀/良好/需要改进]

## 具体改进建议
[详细的改进建议，包括：]
- 需要修正的数据或计算
- 需要补充的步骤或逻辑（重要：确保例子步骤包含实际应用步骤）
- 需要增加的细节
- 需要强化的原理解释
- 如何更好地符合purpose和topic要求
- 句子长度优化建议（控制在20字以内）
- 数据规模调整建议（词典≤10个词，句子≤6个词）
- 量化指标补充建议（每个步骤都要有具体数值）
- 其他改进建议

## 是否通过质量检查
**结果**：[通过/需要修改]
```

请严格按照标准进行评审，确保例子达到高质量要求。
"""

class ExampleExplainAgent:
    """
    例子解释代理
    
    职责：
    1. 从配置文件读取purpose和topic
    2. 通过roleplay方式生成高质量例子
    3. 质量评审和迭代改进
    4. 输出最终的markdown文件
    """

    class Config:
        """例子解释代理配置子模块"""

        def __init__(self, config_dict=None):
            """初始化配置"""
            if not config_dict:
                config_dict = {}

            # 模型配置
            self.model = {
                "type": config_dict.get("model", {}).get("type", "google/gemini-2.5-flash-preview-05-20"),
                "temperature": config_dict.get("model", {}).get("temperature", 0.7),
                "api": config_dict.get("model", {}).get("api", {}),
            }

            # 例子解释代理特定配置
            example_config = config_dict.get("example_explain", {})
            self.purpose = example_config.get("purpose", "为一般受众解释复杂概念")
            self.topic = example_config.get("topic", "通用概念")
            self.max_rounds = example_config.get("max_rounds", 2)
            self.quality_threshold = example_config.get("quality_threshold", "良好")
            self.output_dir = f"output/{self.topic}"

    def __init__(self, config_path="config/config.yaml"):
        """初始化例子解释代理"""
        # 加载配置
        config_dict = self._load_yaml_config(config_path)

        # 初始化配置子模块
        self.config = self.Config(config_dict)

        # 初始化模型
        self.model = self._create_model()

        logger.info("例子解释代理初始化完成")

    def _load_yaml_config(self, config_path):
        """从YAML文件加载配置"""
        try:
            with open(config_path, encoding="utf-8") as file:
                config = yaml.safe_load(file)
            logger.info("从 %s 加载配置", config_path)
            return config
        except Exception as e:
            logger.error(f"加载配置出错: {str(e)}")
            return {}

    def _create_model(self):
        """创建模型实例"""
        # 直接使用utils中的create_model函数
        return create_model(config_file="config/config.yaml")

    def generate_example_with_roleplay(self, max_rounds: int = None) -> str:
        """
        通过角色扮演生成和优化例子

        参数:
        - max_rounds: 最大迭代轮数

        返回:
        - str: 最终的例子内容
        """
        if max_rounds is None:
            max_rounds = self.config.max_rounds

        logger.info(f"开始通过角色扮演生成例子，最大轮数: {max_rounds}")

        try:
            # 格式化角色提示
            formatted_generator_prompt = EXAMPLE_GENERATOR_PROMPT.format(
                purpose=self.config.purpose,
                topic=self.config.topic
            )
            
            formatted_reviewer_prompt = QUALITY_REVIEWER_PROMPT.format(
                purpose=self.config.purpose,
                topic=self.config.topic
            )

            # 准备任务内容
            task_content = f"""
            请生成一个高质量的例子来解释以下主题：

            主题：{self.config.topic}
            面向对象：{self.config.purpose}

            ## 角色定义

            例子生成专家(Example Generator)：
            {formatted_generator_prompt}

            质量评审专家(Quality Reviewer)：
            {formatted_reviewer_prompt}

            ## 对话流程
            1. 首先由例子生成专家创建一个详细的例子
            2. 然后由质量评审专家评估这个例子的质量
            3. 根据评审反馈进行修改优化
            4. 最终输出一个高质量的例子

            重要格式要求：
            当例子生成专家提出修改后的最终例子内容时，必须使用以下格式标记：

            ===开始：最终例子===
            [完整的Markdown内容]
            ===结束：最终例子===

            这将确保我们准确识别出最终例子内容。
            """

            # 设置角色扮演
            role_playing = RolePlaying(
                # 设置例子生成专家为助手角色
                assistant_role_name="Example Generator",
                assistant_agent_kwargs={"model": self.model},
                # 设置质量评审专家为用户角色
                user_role_name="Quality Reviewer",
                user_agent_kwargs={"model": self.model},
                # 任务参数
                task_prompt=task_content,
                task_type=TaskType.AI_SOCIETY,
                with_task_specify=False,  # 禁用task_specify避免报错
                # 附加配置
                output_language="chinese",
            )

            # 开始对话
            logger.info("开始角色对话")
            messages = []

            # 初始化对话
            chat_history = role_playing.init_chat()
            messages.append(chat_history)

            # 收集所有可能的例子版本
            potential_examples = []

            # 最后一轮提前特别提醒
            is_final_reminder_sent = False

            # 进行多轮对话
            for round_num in range(max_rounds * 2):  # 每轮包含两步对话
                logger.info(f"对话步骤 {round_num + 1}")

                # 在最后一轮对话前，提醒生成专家使用正确的格式标记
                if round_num == max_rounds * 2 - 2 and not is_final_reminder_sent:
                    # 这是最后一轮对话的前一步
                    remind_message = """
                    这是最后一轮对话。请记住，您必须在回复中提供最终版本的完整例子，
                    并使用以下格式标记：

                    ===开始：最终例子===
                    [完整的Markdown内容]
                    ===结束：最终例子===

                    这样我才能准确识别最终例子内容。
                    """
                    chat_history.content += "\n\n" + remind_message
                    is_final_reminder_sent = True

                try:
                    # 进行对话步骤
                    assistant_response, user_response = role_playing.step(chat_history)

                    # 从响应中获取消息
                    assistant_message = assistant_response.msg
                    user_message = user_response.msg

                    # 添加到历史记录
                    chat_history = assistant_message
                    messages.append(assistant_message)
                    messages.append(user_message)

                    # 处理助手消息中的内容
                    if hasattr(assistant_message, "content") and assistant_message.role_name == "Example Generator":
                        content = assistant_message.content

                        # 首先检查是否有明确标记的例子段落
                        if "===开始：最终例子===" in content and "===结束：最终例子===" in content:
                            parts = content.split("===开始：最终例子===", 1)
                            if len(parts) > 1:
                                marked_content = parts[1].split("===结束：最终例子===", 1)[0].strip()
                                logger.info(f"步骤 {round_num + 1} 从明确标记中提取到例子内容")
                                potential_examples.append(marked_content)

                                # 如果是最后一轮并使用了正确的标记，可以直接结束对话
                                if round_num >= max_rounds * 2 - 2:
                                    logger.info("获取到最终标记的例子内容，结束对话")
                                    break

                        # 尝试提取Markdown文本（作为备选）
                        extracted_markdown = self._extract_markdown(content)
                        if extracted_markdown and len(extracted_markdown) > 100:
                            logger.info(f"步骤 {round_num + 1} 尝试提取Markdown例子")
                            # 检查提取的内容是否为有效Markdown（有标题结构）
                            if "#" in extracted_markdown[:50] or re.search(r"^#\s+", extracted_markdown, re.MULTILINE):
                                logger.info(f"步骤 {round_num + 1} 提取到可能的Markdown例子")
                                potential_examples.append(extracted_markdown)

                    # 如果已经完成了足够的轮次
                    if round_num >= max_rounds * 2 - 1:
                        logger.info("对话已完成足够的轮次")
                        break

                except Exception as e:
                    logger.error(f"对话步骤出错: {str(e)}")
                    break

            logger.info("角色对话完成，例子生成结束")

            # 筛选并选择最佳例子内容
            valid_examples = []

            # 按质量筛选例子（从最后一轮向前）
            for example in reversed(potential_examples):
                # 跳过太短的内容或明显非Markdown格式的内容
                if len(example) < 100 or (
                    not example.startswith("#") and not re.search(r"^#\s+", example, re.MULTILINE)
                ):
                    continue

                # 清理并格式化Markdown
                example = example.strip()
                # 确保以标题开始
                if not example.startswith("#"):
                    title_match = re.search(r"^#\s+", example, re.MULTILINE)
                    if title_match:
                        start_idx = title_match.start()
                        example = example[start_idx:]

                valid_examples.append(example)

            # 如果没有找到任何有效例子，生成一个简单的例子
            if not valid_examples:
                logger.warning("未找到任何有效的例子，生成默认例子")
                return self._generate_fallback_example()

            # 选择最长的有效例子（通常是最完整的）
            final_example = max(valid_examples, key=len)
            logger.info(f"选择了长度为 {len(final_example)} 字符的最终例子")

            return final_example

        except Exception as e:
            import traceback

            logger.error(f"通过角色扮演生成例子失败: {str(e)}")
            logger.error(traceback.format_exc())
            # 出错时返回备用例子
            return self._generate_fallback_example()

    def _generate_fallback_example(self) -> str:
        """生成备用例子"""
        return f"""# {self.config.topic} 例子解释

## 概念说明
{self.config.topic} 是一个重要的概念，需要通过具体例子来理解。

## 基本例子
针对 {self.config.purpose}，我们可以从以下角度来理解：

1. **基本概念**：{self.config.topic} 的核心思想
2. **应用场景**：在实际中如何使用
3. **关键要点**：需要注意的重要事项

## 总结
通过以上例子，我们可以更好地理解 {self.config.topic} 的本质和应用。
"""

    def save_example(self, content: str, output_file: str = None) -> str:
        """
        保存例子内容到文件

        参数:
        - content: 例子内容
        - output_file: 输出文件路径

        返回:
        - str: 保存的文件路径
        """
        if output_file is None:
            output_file = f"{self.config.output_dir}/example_explain.md"

        # 确保输出目录存在
        output_dir = os.path.dirname(output_file)
        os.makedirs(output_dir, exist_ok=True)

        # 写入文件
        try:
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(content)
            logger.info(f"例子内容已保存到 {output_file}")
            return output_file
        except Exception as e:
            logger.error(f"保存例子内容失败: {str(e)}")
            # 尝试使用备用路径
            backup_file = f"output/example_backup.md"
            try:
                with open(backup_file, "w", encoding="utf-8") as f:
                    f.write(content)
                logger.info(f"例子内容已保存到备用文件 {backup_file}")
                return backup_file
            except Exception as e2:
                logger.error(f"保存到备用文件也失败: {str(e2)}")
                return ""

    def run(self, output_file: str = None, max_rounds: int = None) -> dict[str, Any]:
        """
        运行例子解释的完整流程

        参数:
        - output_file: 输出文件路径
        - max_rounds: 最大迭代轮数

        返回:
        - Dict: 包含处理结果的字典
        """
        result = {}

        try:
            logger.info(f"开始处理主题: {self.config.topic}")
            logger.info(f"面向对象: {self.config.purpose}")

            # 通过角色对话生成例子
            logger.info("开始通过角色对话生成例子")
            final_example = self.generate_example_with_roleplay(max_rounds)
            result["final_example_length"] = len(final_example)

            # 保存结果
            logger.info("保存生成的例子")
            saved_file = self.save_example(final_example, output_file)
            result["saved_file"] = saved_file
            result["success"] = True
            result["topic"] = self.config.topic
            result["purpose"] = self.config.purpose
            
            logger.info("例子解释处理完成")
            return result

        except Exception as e:
            logger.error(f"例子解释处理出错: {str(e)}")
            import traceback

            logger.error(traceback.format_exc())
            result["error"] = str(e)
            result["success"] = False
            return result

    def _extract_markdown(self, text: str) -> str:
        """从文本中提取Markdown内容"""
        # 首先检查是否有明确标记的Markdown段落
        markdown_markers = [
            "===开始：最终例子===",
            "```markdown",
            "# 核心概念识别",
            "## 核心概念识别",
            "# 核心概念例子解释",
        ]

        # 尝试通过明确的标记寻找Markdown起始位置
        start_index = -1
        for marker in markdown_markers:
            if marker in text:
                possible_start = text.find(marker)
                if start_index == -1 or possible_start < start_index:
                    start_index = possible_start

        # 如果找到了标记，从标记处开始提取
        if start_index != -1:
            # 从标记处开始，但跳过标记本身（除非是Markdown标题）
            if text[start_index:start_index + 2] == "# ":
                extracted_text = text[start_index:]
            elif text[start_index:start_index + 3] == "## ":
                extracted_text = text[start_index:]
            else:
                # 找到标记后的第一个换行
                next_line = text.find("\n", start_index)
                if next_line != -1:
                    extracted_text = text[next_line + 1:]
                else:
                    extracted_text = text[start_index:]

            # 寻找终止标记
            end_markers = ["===结束：最终例子===", "```", "---", "以上是生成的例子"]
            for marker in end_markers:
                if marker in extracted_text:
                    extracted_text = extracted_text.split(marker)[0]

            return extracted_text.strip()

        # 如果没有明确的标记，尝试通过Markdown代码块提取
        markdown_pattern = r"```(?:markdown)?\s*([\s\S]*?)```"
        matches = re.findall(markdown_pattern, text, re.DOTALL)

        if matches:
            # 选择最长的匹配结果
            longest_match = max(matches, key=len).strip()
            # 确保这是真正的Markdown内容而不是代码
            if "#" in longest_match and len(longest_match) > 200:
                return longest_match

        # 如果没有明确的Markdown代码块，但文本看起来已经是Markdown格式
        if re.search(r"^#\s+", text, re.MULTILINE):
            # 尝试找到第一个标题
            match = re.search(r"^#\s+", text, re.MULTILINE)
            if match:
                start = match.start()
                extracted = text[start:].strip()
                
                # 检查是否包含完整的结构（不只是代码）
                if "##" in extracted and len(extracted) > 500:
                    return extracted

        # 如果以上都失败，检查是否整个文本就是Markdown格式
        if text.strip().startswith("#") and "##" in text:
            return text.strip()

        # 无法提取有效的Markdown，返回空字符串让调用者使用原始文本
        logger.warning("无法提取有效的Markdown内容")
        return ""


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="例子解释代理 - 生成高质量的概念解释例子")
    parser.add_argument("--config", type=str, default="config/config.yaml", help="配置文件路径")
    parser.add_argument("--output", type=str, help="输出文件路径")
    parser.add_argument("--max-rounds", type=int, help="最大迭代轮数")

    args = parser.parse_args()

    try:
        # 创建代理
        print("🚀 正在初始化例子解释代理...")
        agent = ExampleExplainAgent(config_path=args.config)
        
        print(f"📁 项目名称: {agent.config.topic}")
        print(f"📂 输出目录: {agent.config.output_dir}")

        # 运行处理
        print("⚡ 开始处理，请稍候...")
        result = agent.run(
            output_file=args.output,
            max_rounds=args.max_rounds
        )

        # 输出结果
        if result.get("success"):
            print(f"✅ 例子解释处理成功！")
            print(f"📋 主题: {result.get('topic', '未知')}")
            print(f"👥 面向对象: {result.get('purpose', '未知')}")
            print(f"📝 最终例子长度: {result.get('final_example_length', 0)} 字符")
            print(f"💾 保存文件: {result.get('saved_file', '未保存')}")
        else:
            print(f"❌ 例子解释处理失败: {result.get('error', '未知错误')}")

    except Exception as e:
        print(f"❌ 程序运行出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 