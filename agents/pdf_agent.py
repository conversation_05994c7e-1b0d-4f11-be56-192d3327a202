"""
This example demonstrates how to use a tool-using agent with the PDFToolkit to extract
and analyze content from PDF files.
"""

from dotenv import load_dotenv

load_dotenv()

import argparse
import os
import yaml
from pathlib import Path

from camel.agents import ChatAgent
from camel.messages.base import BaseMessage
from camel.models import ModelFactory
from camel.types import ModelPlatformType
from loguru import logger

from tools.pdf_toolkit import PDFToolkit


def main(
    pdf_path: str = None,
    output_dir: str = "pdf_output",
):
    """Run the PDF tool agent example.

    Args:
        pdf_path: Path to the PDF file. If None, the agent will ask for it.
        output_dir: Directory to save the extracted content.
    """
    # 获取项目根目录
    current_dir = Path(__file__).resolve().parent
    root_dir = current_dir.parent

    # 加载配置
    config_path = os.path.join(root_dir, "config/config.yaml")
    with open(config_path) as file:
        config = yaml.safe_load(file)

    # 从配置中获取API设置
    model_config = config.get("model", {})
    api_config = model_config.get("api", {})

    model = ModelFactory.create(
        model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
        model_type=model_config.get("type", "openai/gpt-4o-mini"),
        api_key=api_config.get("openai_compatibility_api_key"),
        url=api_config.get("openai_compatibility_api_base_url"),
    )

    # Initialize the ChatAgent with the PDFToolkit
    # Create the agent with the PDFToolkit
    agent = ChatAgent(
        system_message=BaseMessage.make_assistant_message(
            role_name="PDF analyzing agent",
            content="You can analyze PDF documents and extract content from them",
        ),
        model=model,
        tools=[*PDFToolkit().get_tools()],
    )
    agent.reset()

    # Start the conversation
    if pdf_path:
        # If a PDF path is provided, ask the agent to analyze it
        user_message = f"""I have a PDF file at {pdf_path} that I'd like to analyze.
Please extract the content from this PDF and provide an analysis of what it contains.
Save the extracted content to the directory: {output_dir}."""
    else:
        # If no PDF path is provided, just start a general conversation
        user_message = """I'd like to analyze a PDF document. Can you help me with that?
What information do you need from me to get started?"""

    # Get the agent's response
    logger.info("Starting conversation with the agent")
    agent_response = agent.step(user_message)

    # Print the agent's response
    print("\n" + "=" * 50)
    print("Agent's Response:")
    print("=" * 50)
    print(agent_response.msg.content)
    print("=" * 50 + "\n")

    # Continue the conversation if needed
    if not pdf_path:
        # If no PDF path was provided, ask the user for it
        pdf_path_input = input("Please enter the path to your PDF file: ")

        if pdf_path_input:
            follow_up_message = f"""Thank you. I'd like to analyze the PDF file at {pdf_path_input}.
Please extract the content from this PDF and provide an analysis of what it contains.
Save the extracted content to the directory: {output_dir}."""

            # Get the agent's response
            logger.info(f"Asking the agent to analyze the PDF at {pdf_path_input}")
            follow_up_response = agent.step(follow_up_message)

            # Print the agent's follow-up response
            print("\n" + "=" * 50)
            print("Agent's Analysis:")
            print("=" * 50)
            print(follow_up_response.msg.content)
            print("=" * 50 + "\n")


if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Analyze a PDF file using an AI agent with tools")
    parser.add_argument("--pdf_path", type=str, help="Path to the PDF file (optional)")
    parser.add_argument(
        "--output_dir",
        type=str,
        default="pdf_output",
        help="Directory to save the extracted content and analysis",
    )

    args = parser.parse_args()

    main(args.pdf_path, args.output_dir)
