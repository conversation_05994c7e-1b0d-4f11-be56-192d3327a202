from dotenv import load_dotenv

load_dotenv()

import os
import re
import sys
from typing import Any

from camel.messages import BaseMessage
from loguru import logger

# 添加父目录到导入路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import yaml
from camel.agents import ChatAgent

# 导入create_model函数
from utils.create_llm_model import create_model

"""
markdown格式要求：
# 为什么要学习这个概念？(≤100字)
[用一段话先说明这个概念名称、定义、实际价值和应用场景，然后抛出一个吸引人的问题或现象，激发读者的兴趣和好奇心]

## 背景设定
[设置一个具体且简洁的应用场景, 数据规模适合视频呈现]

# 总结
## 核心原理揭示(≤100字)
[用简短句子深入解释为什么这样做，揭示本质机制。同时要回扣到开头抛出的问题，说明这个例子如何解决了那个问题]

## 核心步骤总结(≤100字)
1. 步骤1：[详细说明]
2. 步骤2：[详细说明]
3. ...

"""

# 定义综合例子生成专家角色提示
EXAMPLE_GENERATOR_PROMPT = """
你是一位顶级的例子生成专家，专门为复杂概念创造高质量、细节丰富、易懂的具体例子。

## 核心任务
面向对象：{purpose}
主题：{topic}

## 核心能力
1. **概念提取与分析**：准确识别核心概念、原理或题目，分析其关键要素和步骤
2. **例子设计**：根据核心概念的分析，设计一个具体、量化、准确的例子来解释抽象概念
3. **动画设计**：根据整体例子的描述，设计一个主体动画方案(动画主要载体)，每一步都围绕这个主体来完善，比如介绍排序算法，主体动画是一些数字之间的位置变化，比如RNN算法介绍，围绕RNN的结构流程来设计动画
3. **数学公式处理**：使用LaTeX格式正确表示数学公式或方程
4. **针对性讲解**：紧扣purpose和topic，确保内容符合目标受众需求

## 质量标准
- **数据量化**：所有数据必须具体、准确，有明确的数值
- **步骤详细**：每个步骤都要清晰说明，不遗漏任何环节步骤
- **逻辑清晰**：整个过程逻辑连贯，易于理解
- **结果明确**：最终结果要明确，能够验证
- **原理揭示**：深入揭示概念的本质原理和机制
- **易于记忆**：例子要生动具体，便于理解和记忆
- **符合需求**：严格按照purpose和topic的要求设计例子
- **视频友好**：适合视频呈现，句子简洁，数据规模适中
- **量化表达**：每个步骤都要有具体的数值和量化指标
- **辅助信息**：有关键公式的，可以作为辅助信息补充上

## 视频友好要求
- 句子长度控制在20字以内
- 公式使用LaTeX格式：$$公式内容$$
- 数据规模适中（所有词典、语料大小不超过10个词，句子不超过6个词）
- 每个步骤都有明确的数值指标
- 避免冗长的描述，用简洁的量化表达
- 例子步骤**必须**包含实际应用步骤，不能只有理论原理

#数据表示要求
-数学公式使用对应的LaTeX命令，注意转义字符使用,注意中文不要使用LaTeX命令
例如：
  - 梯度：$\\nabla f(x) = \\frac{{\\partial f}}{{\\partial x}}$
  - 损失函数：$$J(\\theta) = \\frac{{1}}{{2m}}\\sum_{{i=1}}^{{m}}(h_\\theta(x^{{(i)}}) - y^{{(i)}})^2$$
  - 参数更新：$$\\theta := \\theta - \\alpha \\nabla J(\\theta)$$
-数据类型使用manim适配的数据(用中文描述就)、 Matrix、Vector、Text、Code、Table、MathTex、Graph等
-每一步的矩阵、向量等必须包含具体数字，不能用变量简单替代，比如x表示3维向量，需要把3维向量数字展现出来
-中文字符串带引号的，需要增加转义字符，例如 "我在学习“勾股定理”的原理“ 需要写成 “我在学习\\“勾股定理\\”的原理"

## 输出格式
严格遵守以下要求：
- 例子的详细步骤必须包含实际应用步骤，不能只有理论原理
--例子详细步骤的第一步必须是先解释这个问题，抛出吸引人的问题
- 每一步必须包含具体的数据和操作描述，关键数据不能省略，比如x表示的3维向量，需要把向量数字展现出来，遵循数据表示要求
- 每一步增加最灵魂、最关键的动画关键点的描述，便于展现这步本质，把原来什么元素经过什么动作变成什么描述清楚，一定要包含关键数据
  --比如：“四个a底b高c斜边，同样大小的四个直角三角形和一个c边上正方形，移动并旋转形成一个边长为a+b的大正方形，内部围成一个边长c的正方形)。”
- 每一步动画灵魂地方描述，不要过度描述不重要的形状，特效，聚焦在核心原理动画上，客观描述，比如不要使用一个齿轮，一个小人，像流水一样等无实际意义的描述
- 每一步动画灵魂地方描述，每个元素（包含关键的数据元素）涉及到位置变化的，一定详细描述清楚原来在哪后在哪，可以是相对位置，涉及到颜色变化的，一定详细描述清楚原来是什么颜色后是什么颜色，涉及到大小变化的，一定详细描述清楚原来是什么大小后是什么大小,涉及到数量变化的，一定描述清楚有多个个数，比如4维向量，5个数字等
请按照以下markdown格式输出

```markdown
<SCENE_OUTLINE>
场景1:[场景名称]

## 详细步骤解释
### 步骤1：[开场白]
**主题解释**：[解释这个问题的核心原理，存在的价值和重要意义，引出例子，30个字以内]
**问题**：[抛出一个震惊体吸引人的疑问句，激发读者的兴趣和好奇心，问题要和主题相关，20个字以内]

### 步骤2：[步骤名称]
**数据**：[具体数值，如：3个词、5维向量等]
**操作**：[简短描述操作过程]
**结果**：[量化结果，如：得到X个向量、准确率Y%等]
**辅助信息**：[该步一句话核心原理本质的解释或者重要认知发现等，不强制需要]
**动画灵魂地方**: [最灵魂、最关键的动画关键点的详细描述]

### 步骤3：[步骤名称]
**数据**：[当前步骤的具体数据，如：3个词、5维向量等,参考数据表示要求]
**操作**：[简短描述操作过程]
**结果**：[量化结果]
**辅助信息**：[该步一句话核心原理本质的解释或者重要认知发现等，不强制需要]
**动画灵魂地方**: [最灵魂、最关键的动画关键点的详细描述]

### 步骤N：[最终步骤-实际应用]
**数据**：[最终数据,参考数据表示要求]
**操作**：[最终操作 - 重要：必须包含如何在实际中应用这个概念的具体步骤]
**结果**：[最终量化结果]
**辅助信息**：[该步一句话核心原理本质的解释或者重要认知发现等，不强制需要]
**动画灵魂地方**: [最灵魂、最关键的动画关键点的详细描述]

</SCENE_OUTLINE>
```

"""


class ExampleExplainAgent:
    """
    例子解释代理

    职责：
    1. 从配置文件读取purpose和topic
    2. 通过roleplay方式生成高质量例子
    3. 质量评审和迭代改进
    4. 输出最终的markdown文件
    """

    class Config:
        """例子解释代理配置子模块"""

        def __init__(self, config_dict=None):
            """初始化配置"""
            if not config_dict:
                config_dict = {}

            # 模型配置
            self.model = {
                "type": config_dict.get("model", {}).get("type", "google/gemini-2.5-flash-preview-05-20"),
                "temperature": config_dict.get("model", {}).get("temperature", 0.7),
                "api": config_dict.get("model", {}).get("api", {}),
            }

            # 例子解释代理特定配置
            example_config = config_dict.get("example_explain", {})
            self.purpose = example_config.get("purpose", "为一般受众解释复杂概念")
            self.topic = example_config.get("topic", "通用概念")
            self.max_rounds = example_config.get("max_rounds", 2)
            self.quality_threshold = example_config.get("quality_threshold", "良好")
            self.output_dir = f"output/{self.topic}"

    def __init__(self, config_path="config/config.yaml"):
        """初始化例子解释代理"""
        # 加载配置
        config_dict = self._load_yaml_config(config_path)

        # 初始化配置子模块
        self.config = self.Config(config_dict)

        # 初始化模型
        self.model = self._create_model()

        logger.info("例子解释代理初始化完成")

    def _load_yaml_config(self, config_path):
        """从YAML文件加载配置"""
        try:
            with open(config_path, encoding="utf-8") as file:
                config = yaml.safe_load(file)
            logger.info("从 %s 加载配置", config_path)
            return config
        except Exception as e:
            logger.error(f"加载配置出错: {str(e)}")
            return {}

    def _create_model(self):
        """创建模型实例"""
        # 直接使用utils中的create_model函数
        return create_model(config_file="config/config.yaml")

    def generate_example(self) -> str:
        """
        直接用ChatAgent单轮生成高质量例子

        返回:
        - str: 最终的例子内容
        """
        try:
            # 构造 system_message
            formatted_generator_prompt = EXAMPLE_GENERATOR_PROMPT.format(
                purpose=self.config.purpose, topic=self.config.topic
            )
            system_message = BaseMessage.make_assistant_message(
                role_name="例子生成专家", content=formatted_generator_prompt
            )
            # 构造 user prompt
            user_prompt = (
                f"请为主题“{self.config.topic}”面向对象“{self.config.purpose}”生成高质量例子，严格按上述格式输出。"
            )
            # 实例化 ChatAgent
            agent = ChatAgent(system_message=system_message, model=self.model, output_language="chinese")
            logger.info("开始单轮ChatAgent生成例子")
            response = agent.step(user_prompt)
            content = response.msg.content
            # 尝试提取Markdown
            extracted_markdown = self._extract_markdown(content)
            if extracted_markdown and len(extracted_markdown) > 100:
                return extracted_markdown.strip()
            # 若未能提取，直接返回原内容
            return content.strip()
        except Exception as e:
            import traceback

            logger.error(f"ChatAgent生成例子失败: {str(e)}")
            logger.error(traceback.format_exc())
            return ""

    def save_example(self, content: str, output_file: str = None) -> str:
        """
        保存例子内容到文件

        参数:
        - content: 例子内容
        - output_file: 输出文件路径

        返回:
        - str: 保存的文件路径
        """
        if output_file is None:
            output_file = f"{self.config.output_dir}/example_explain.md"

        # 确保输出目录存在
        output_dir = os.path.dirname(output_file)
        os.makedirs(output_dir, exist_ok=True)

        # 写入文件
        try:
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(content)
            logger.info(f"例子内容已保存到 {output_file}")
            return output_file
        except Exception as e:
            logger.error(f"保存例子内容失败: {str(e)}")
            # 尝试使用备用路径
            backup_file = "output/example_backup.md"
            try:
                with open(backup_file, "w", encoding="utf-8") as f:
                    f.write(content)
                logger.info(f"例子内容已保存到备用文件 {backup_file}")
                return backup_file
            except Exception as e2:
                logger.error(f"保存到备用文件也失败: {str(e2)}")
                return ""

    def run(self, output_file: str = None, max_rounds: int = None) -> dict[str, Any]:
        """
        运行例子解释的完整流程

        参数:
        - output_file: 输出文件路径
        - max_rounds: 最大迭代轮数

        返回:
        - Dict: 包含处理结果的字典
        """
        result = {}

        try:
            logger.info(f"开始处理主题: {self.config.topic}")
            logger.info(f"面向对象: {self.config.purpose}")

            # 通过角色对话生成例子
            logger.info("开始通过角色对话生成例子")
            final_example = self.generate_example()
            result["final_example_length"] = len(final_example)

            # 保存结果
            logger.info("保存生成的例子")
            saved_file = self.save_example(final_example, output_file)
            result["saved_file"] = saved_file
            result["success"] = True
            result["topic"] = self.config.topic
            result["purpose"] = self.config.purpose

            logger.info("例子解释处理完成")
            return result

        except Exception as e:
            logger.error(f"例子解释处理出错: {str(e)}")
            import traceback

            logger.error(traceback.format_exc())
            result["error"] = str(e)
            result["success"] = False
            return result

    def _extract_markdown(self, text: str) -> str:
        """从文本中提取Markdown内容"""
        # 首先检查是否有明确标记的Markdown段落
        markdown_markers = [
            "===开始：最终例子===",
            "```markdown",
            "# 核心概念识别",
            "## 核心概念识别",
            "# 核心概念例子解释",
        ]

        # 尝试通过明确的标记寻找Markdown起始位置
        start_index = -1
        for marker in markdown_markers:
            if marker in text:
                possible_start = text.find(marker)
                if start_index == -1 or possible_start < start_index:
                    start_index = possible_start

        # 如果找到了标记，从标记处开始提取
        if start_index != -1:
            # 从标记处开始，但跳过标记本身（除非是Markdown标题）
            if text[start_index : start_index + 2] == "# ":
                extracted_text = text[start_index:]
            elif text[start_index : start_index + 3] == "## ":
                extracted_text = text[start_index:]
            else:
                # 找到标记后的第一个换行
                next_line = text.find("\n", start_index)
                if next_line != -1:
                    extracted_text = text[next_line + 1 :]
                else:
                    extracted_text = text[start_index:]

            # 寻找终止标记
            end_markers = ["===结束：最终例子===", "```", "---", "以上是生成的例子"]
            for marker in end_markers:
                if marker in extracted_text:
                    extracted_text = extracted_text.split(marker)[0]

            return extracted_text.strip()

        # 如果没有明确的标记，尝试通过Markdown代码块提取
        markdown_pattern = r"```(?:markdown)?\s*([\s\S]*?)```"
        matches = re.findall(markdown_pattern, text, re.DOTALL)

        if matches:
            # 选择最长的匹配结果
            longest_match = max(matches, key=len).strip()
            # 确保这是真正的Markdown内容而不是代码
            if "#" in longest_match and len(longest_match) > 200:
                return longest_match

        # 如果没有明确的Markdown代码块，但文本看起来已经是Markdown格式
        if re.search(r"^#\s+", text, re.MULTILINE):
            # 尝试找到第一个标题
            match = re.search(r"^#\s+", text, re.MULTILINE)
            if match:
                start = match.start()
                extracted = text[start:].strip()

                # 检查是否包含完整的结构（不只是代码）
                if "##" in extracted and len(extracted) > 500:
                    return extracted

        # 如果以上都失败，检查是否整个文本就是Markdown格式
        if text.strip().startswith("#") and "##" in text:
            return text.strip()

        # 无法提取有效的Markdown，返回空字符串让调用者使用原始文本
        logger.warning("无法提取有效的Markdown内容")
        return ""


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="例子解释代理 - 生成高质量的概念解释例子")
    parser.add_argument("--config", type=str, default="config/config.yaml", help="配置文件路径")
    parser.add_argument("--output", type=str, help="输出文件路径")
    parser.add_argument("--max-rounds", type=int, help="最大迭代轮数")

    args = parser.parse_args()

    try:
        # 创建代理
        print("🚀 正在初始化例子解释代理...")
        agent = ExampleExplainAgent(config_path=args.config)

        print(f"📁 项目名称: {agent.config.topic}")
        print(f"📂 输出目录: {agent.config.output_dir}")

        # 运行处理
        print("⚡ 开始处理，请稍候...")
        result = agent.run(output_file=args.output, max_rounds=args.max_rounds)

        # 输出结果
        if result.get("success"):
            print("✅ 例子解释处理成功！")
            print(f"📋 主题: {result.get('topic', '未知')}")
            print(f"👥 面向对象: {result.get('purpose', '未知')}")
            print(f"📝 最终例子长度: {result.get('final_example_length', 0)} 字符")
            print(f"💾 保存文件: {result.get('saved_file', '未保存')}")
        else:
            print(f"❌ 例子解释处理失败: {result.get('error', '未知错误')}")

    except Exception as e:
        print(f"❌ 程序运行出错: {str(e)}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
