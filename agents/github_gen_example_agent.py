import datetime
import json
import re
import subprocess
from pathlib import Path
from typing import Any

import yaml
from camel.agents import ChatAgent
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.types import ModelPlatformType
from loguru import logger


class GitHubGenExampleAgent:
    def __init__(self, config_path: str = "config/config.yaml"):
        self.load_config(config_path)
        self.repo_path = Path(self.github_config.get("local_repo_path"))
        self.project_name = self.repo_path.name
        self.model = self._create_model()
        self.agent = self._create_agent()
        logger.info(f"初始化完成，仓库路径: {self.repo_path}")

    def load_config(self, config_path: str):
        with open(config_path) as f:
            self.config = yaml.safe_load(f)
        self.model_config = self.config.get("model", {})
        self.github_config = self.config.get("github", {})
        logger.info(f"从 {config_path} 加载配置")

    def _create_model(self):
        api_config = self.model_config.get("api", {})
        return ModelFactory.create(
            model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
            model_type=self.model_config.get("type", "openai/gpt-4o-mini"),
            api_key=api_config.get("openai_compatibility_api_key"),
            url=api_config.get("openai_compatibility_api_base_url"),
        )

    def _create_agent(self):
        return ChatAgent(model=self.model, system_message="你是一个专业的代码示例生成与调试助手。")

    def collect_examples_info(self) -> dict[str, Any]:
        """收集examples下的所有example及其readme、注释"""
        example_names = ["examples", "cookbook", "samples", "demo", "tutorials", "example"]
        examples_dir = None

        # 搜索第一层和第二层目录
        for name in example_names:
            # 检查第一层
            first_level = self.repo_path / name
            if first_level.exists():
                examples_dir = first_level
                break

            # 检查第二层
            for subdir in self.repo_path.iterdir():
                if subdir.is_dir():
                    second_level = subdir / name
                    if second_level.exists():
                        examples_dir = second_level
                        break
            if examples_dir:
                break

        if not examples_dir:
            examples_dir = self.repo_path / "examples"
        all_examples = []
        if not examples_dir.exists():
            logger.warning("examples 目录不存在")
            return {"examples": []}
        for example in examples_dir.iterdir():
            example_info = {
                "path": str(example.relative_to(self.repo_path)),
                "files": [],
                "readme": "",
                "comments": [],
            }

            if example.is_dir():
                # 收集readme
                for readme_name in ["README.md", "readme.md", "Readme.md"]:
                    readme_path = example / readme_name
                    if readme_path.exists():
                        with open(readme_path, encoding="utf-8") as f:
                            example_info["readme"] = f.read()
                        break

                # 遍历 example 目录下的所有文件
                for file in example.rglob("*"):
                    if file.is_file():
                        # 获取相对路径
                        rel_path = str(file.relative_to(self.repo_path))
                        example_info["files"].append(rel_path)

                        # 只收集代码文件的注释
                        if file.suffix in [".py", ".sh", ".ipynb"]:
                            try:
                                with open(file, encoding="utf-8") as f:
                                    content = f.read()
                                    # 收集注释
                                    comments = re.findall(r"^\s*#.*", content, re.MULTILINE)
                                    example_info["comments"].extend(comments)
                            except Exception as e:
                                logger.warning(f"读取文件 {rel_path} 失败: {e}")
            else:  # example 是文件
                # 将文件本身添加到 files 列表
                example_info["files"].append(str(example.relative_to(self.repo_path)))

                # 如果是代码文件，收集注释
                if example.suffix in [".py", ".sh", ".ipynb"]:
                    try:
                        with open(example, encoding="utf-8") as f:
                            content = f.read()
                            # 收集注释
                            comments = re.findall(r"^\s*#.*", content, re.MULTILINE)
                            example_info["comments"].extend(comments)
                    except Exception as e:
                        logger.warning(f"读取文件 {example} 失败: {e}")

                all_examples.append(example_info)
        # 收集主readme
        repo_readme = ""
        for readme_name in ["README.md", "readme.md", "Readme.md"]:
            readme_path = self.repo_path / readme_name
            if readme_path.exists():
                with open(readme_path, encoding="utf-8") as f:
                    repo_readme = f.read()
                break
        return {"repo_readme": repo_readme, "examples": all_examples}

    def select_and_describe_examples(self, examples_info: dict[str, Any]) -> list[dict[str, Any]]:
        """让chatAgent从examples中选1个简单实用的，并补充描述"""
        prompt = f"""
你是一个开源项目的代码示例专家。以下是该项目的主README和examples目录下每个example的路径、readme和部分代码注释。请从中挑选最多3个最简单、最实用的example，并对每个example补充一句简明的中文描述，说明它的用途和亮点。输出格式如下：
[
  {{
    "path": "repo下的相对路径名，可能是一个文件或一个子目录",
    "description": "一句中文描述",
    "files": [...],
    "readme": "...",
    "comments": [...]
  }},
  ...
]
主README:
{examples_info['repo_readme']}

examples信息:
{json.dumps(examples_info['examples'], ensure_ascii=False, indent=2)}
"""
        user_message = BaseMessage.make_user_message(role_name="User", content=prompt)
        response = self.agent.step(user_message)
        try:
            # 尝试提取JSON
            json_start = response.msg.content.find("[")
            json_end = response.msg.content.rfind("]") + 1
            return json.loads(response.msg.content[json_start:json_end])
        except Exception as e:
            logger.error(f"解析examples选择结果失败: {e}")
            return []

    def collect_example_files(self, example_path: str) -> dict[str, str]:
        """收集某个example目录下所有文件内容"""
        abs_path = self.repo_path / example_path
        files_content = {}
        # 检查 abs_path 本身是否是文件
        if abs_path.is_file():
            try:
                with open(abs_path, encoding="utf-8") as f:
                    content = f.read()
                files_content[abs_path.name] = content
            except Exception as e:
                logger.warning(f"读取文件 {abs_path} 失败: {e}")

        # 如果是目录，则遍历其下的所有文件
        elif abs_path.is_dir():
            for file in abs_path.rglob("*"):
                if file.is_file():
                    try:
                        with open(file, encoding="utf-8") as f:
                            content = f.read()
                            # 使用相对路径作为 key
                            rel_path = str(file.relative_to(abs_path))
                            files_content[rel_path] = content
                    except Exception as e:
                        logger.warning(f"读取文件 {file} 失败: {e}")
        return files_content

    def _extract_files_from_message(self, message: str, test_dir: Path):
        import json
        import re

        # 提取 ```json ... ``` 之间的内容
        json_match = re.search(r"```json\s*([\s\S]+?)\s*```", message)
        if json_match:
            json_str = json_match.group(1)
        else:
            # 兜底：直接找第一个 { 到最后一个 }
            json_start = message.find("{")
            json_end = message.rfind("}") + 1
            json_str = message[json_start:json_end]

        try:
            data = json.loads(json_str)
        except Exception as e:
            logger.error(f"解析代码生成器返回的json失败: {e}")
            return [], None

        created_files = []
        execute_py_file_name = None

        if "files" in data:
            for file_info in data["files"]:
                file_name = file_info["file_name"]
                file_content = file_info["file_content"]
                file_path = test_dir / file_name
                file_path.parent.mkdir(parents=True, exist_ok=True)
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(file_content)
                created_files.append(str(file_path))
        if "execute_py_file_name" in data and data["execute_py_file_name"]:
            execute_py_file_name = data["execute_py_file_name"]
        else:
            logger.error("代码生成器返回的json缺少 execute_py_file_name 字段或其值为 None")
            execute_py_file_name = None
        return created_files, execute_py_file_name

    def _execute_python_files(self, execute_py_file_name: str, test_dir: Path):
        """
        在 test_dir 下执行 execute_py_file_name，返回输出和是否成功
        """
        import os

        logger.info(f"execute_py_file_name:{execute_py_file_name}")
        test_dir_exeucte_file_path = os.path.join(str(test_dir), execute_py_file_name)
        if not execute_py_file_name or not os.path.exists(test_dir_exeucte_file_path):
            return f"没有找到可执行的Python文件 execute_py_file_name: {test_dir_exeucte_file_path}", False

        original_dir = os.getcwd()
        try:
            os.chdir(str(test_dir))
            # 语法检查
            syntax_check = subprocess.run(
                ["python", "-m", "py_compile", execute_py_file_name], capture_output=True, text=True, check=False
            )
            if syntax_check.returncode != 0:
                return f"--- 语法错误 ---\n{syntax_check.stderr}\n", False
            # 执行
            result = subprocess.run(
                ["python", execute_py_file_name], capture_output=True, text=True, check=False, timeout=60
            )
            print(f"execute: python {execute_py_file_name}")
            # 生成 execute.sh 文件
            with open("execute.sh", "w", encoding="utf-8") as f:
                f.write(f"python {test_dir_exeucte_file_path}\n")
            # 设置可执行权限
            os.chmod("execute.sh", 0o755)

            output = f"--- 标准输出 ---\n{result.stdout}\n\n--- 错误输出 ---\n{result.stderr}\n"
            error_keywords = [
                "SyntaxError",
                "ImportError",
                "ModuleNotFoundError",
                "NameError",
                "TypeError",
                "ValueError",
                "Exception",
                "Error",
                "错误",
                "失败",
                "测试失败",
            ]
            has_error = any(k in (result.stdout + result.stderr) for k in error_keywords)
            success = result.returncode == 0 and not has_error
            return output, success
        except Exception as e:
            return f"执行出错: {e}, test_dir:{test_dir}", False
        finally:
            os.chdir(original_dir)

    def make_example_runnable(self, example: dict[str, Any], files_content: dict[str, str]) -> dict[str, Any]:
        """让example可运行，最多4轮，每轮都真实写文件并运行"""
        test_dir = Path(f"output/{self.project_name}/1_test_example")
        test_dir.mkdir(parents=True, exist_ok=True)
        base_prompt = f"""
        你是一位专业的部署工程师，你的任务是针对给定的 example，通过一些简单的修改（如配置的修改），保证 example 可以运行成功。

        请分析以下示例代码，生成最精简、最核心的测试代码：

        example路径: {example['path']}
        example描述: {example.get('description', '')}
                example files 内容:
        {json.dumps(files_content, ensure_ascii=False, indent=2)}

        你需要：
        1. 通过readme文件等非代码文件，解析example功能和执行步骤
        2. 对 example 代码进行必要的修改，使得它可以运行起来，并且将 example 的输出转为**中文**
        3. 生成最精简的可执行的 python 代码 execute_py_file_name，使得它遵循 example 跑起来的执行步骤，确保example能够在目标环境中正常运行
        4. 提供清晰的错误处理和输出，帮助调试
        5. 仔细分析错误信息，针对性修复问题
        6. **确保一定要提供 execute_py_file_name，并且它一定要存在于 files 里**
        7. **必须严格按照下方JSON格式输出，字段名不要换行、不要加下划线、不要加反斜杠，字段名必须是 file_name 和 execute_py_file_name，且与示例完全一致。**


        **以下是你每一轮要生成的完整输出格式模板，务必严格遵循：**
        ---
        ```json
        {{
          "files": [
            {{
              "file_name": "路径/xxx.py",
              "file_content": "......",
              "file_description": {{
                "file_abstract": "这个文件的代码在做什么，跟其他文件之间是什么关系",
                "code_descriptions": [
                        # 对文件代码进行解读，分块描述每一块代码在做什么，帮助读代码的人能了解这个 repo 的主要功能是什么，以及如何使用。
                        # 一个文件的 code_descriptions 不要超过 3 块
                        {{
                            "code_lines" : "a行-b行",
                            "codes": "这段代码块，如果代码行数超过20行，则进行简单的简化，但需要完整保留这段代码里面最重要的和repo相关的代码",
                            "code_description": "描述这段代码"
                        }},
                        ...
                    ]
                }}
            }}
          ],
          "execute_py_file_name": "路径/xxx.py"
        }}
        ```
        ---
        """

        with open(test_dir / "task_prompt.txt", "w", encoding="utf-8") as f:
            f.write(base_prompt)

        prompt = base_prompt
        messages = []
        run_output = ""
        run_success = False
        codegen_content = ""

        for round_idx in range(5):
            # 1. 喂给 agent
            user_message = BaseMessage.make_user_message(role_name="User", content=prompt)
            response = self.agent.step(user_message)
            codegen_content = response.msg.content
            messages.append(codegen_content)

            # 保存本轮 agent 返回
            with open(test_dir / f"codegen_round{round_idx+1}.txt", "w", encoding="utf-8") as f:
                f.write(codegen_content)

            # 1. 解析代码生成器返回，写入文件
            created_files, execute_py_file_name = self._extract_files_from_message(codegen_content, test_dir)
            # 2. 实际运行
            run_output, run_success = self._execute_python_files(execute_py_file_name, test_dir)
            # 保存测试执行者消息
            with open(test_dir / f"test_round{round_idx+1}.txt", "w", encoding="utf-8") as f:
                f.write(run_output)
            if "运行成功" in run_output or "成功运行" in run_output or run_success:
                break
            # 5. 如果失败，下一轮 prompt 加入运行输出
            prompt = (
                codegen_content
                + "\n\n"
                + f"【上轮运行结果】\n{run_output}\n\n请根据上轮运行结果修正你的输出，务必保证代码可以运行成功。"
            )
        return {
            "final_code": codegen_content,
            "test_log": run_output,
            "all_messages": messages,
        }

    def save_markdown_report(self, selected_examples: list[dict[str, Any]], results: list[dict[str, Any]]):
        output_dir = Path(f"output/{self.project_name}")
        output_dir.mkdir(parents=True, exist_ok=True)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = output_dir / f"example_evaluation_{timestamp}.md"
        md = f"# {self.project_name} 示例自动化评测报告\n\n"
        for idx, (ex, res) in enumerate(zip(selected_examples, results)):
            md += f"## 示例{idx+1}: {ex['path']}\n"
            md += f"**描述**: {ex.get('description','')}\n\n"
            md += "**涉及文件**:\n"
            for f in ex.get("files", []):
                md += f"- `{f}`\n"
            md += "\n**最终可执行代码/修改建议**:\n\n"
            md += f"```\n{res['final_code']}\n```\n"
            md += "\n**运行日志**:\n\n"
            md += f"```\n{res['test_log']}\n```\n"
            md += "\n---\n"
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(md)
        logger.info(f"评测报告已保存到 {output_file}")
        return str(output_file)

    def generate_file_descriptions(self, files_dict: dict, output_json_path: str):
        """
        生成文件描述并保存为 JSON

        Args:
            files_dict: 包含 files 列表的字典，格式同参考文档
            output_json_path: 输出 JSON 文件的路径
        """
        import json

        # 准备发送给 chatAgent 的代码内容
        code_contents = []
        for file_info in files_dict["files"]:
            file_path = file_info["file_name"]
            try:
                with open(file_path, encoding="utf-8") as f:
                    content = f.read()
                code_contents.append({"file_name": file_path, "content": content})
            except Exception as e:
                logger.warning(f"读取文件 {file_path} 失败: {e}")

        # 构建 prompt 让 chatAgent 解读代码
        prompt = f"""请分析以下代码文件，为每个文件生成详细描述。
            对于每个文件，请提供：
            1. file_abstract: 简要说明这个文件的功能和与其他文件的关系
            2. code_descriptions: 将代码分成最多3个主要部分，每部分包含：
            - code_lines: 代码行号范围
            - codes: 关键代码片段（如果超过20行，请简化但保留重要部分）
            - code_description: 详细说明这段代码的功能

            以下是需要分析的文件：
            {json.dumps(code_contents, ensure_ascii=False, indent=2)}

            请按照以下 JSON 格式返回结果：
            {{
            "files": [
                {{
                "file_name": "文件路径",
                "file_description": {{
                    "file_abstract": "文件功能概述",
                    "code_descriptions": [
                    {{
                        "code_lines": "行号范围",
                        "codes": "代码片段",
                        "code_description": "代码说明"
                    }}
                    ]
                }}
                }}
            ]
            }}"""

        # 调用 chatAgent 获取分析结果
        try:
            response = self.agent.step(prompt)
            response = response.msg.content
            # 提取 JSON 部分
            json_match = re.search(r"```json\s*([\s\S]+?)\s*```", response)
            if json_match:
                result = json.loads(json_match.group(1))
            else:
                # 兜底：直接找第一个 { 到最后一个 }
                json_start = response.find("{")
                json_end = response.rfind("}") + 1
                result = json.loads(response[json_start:json_end])

            # 将结果写入 JSON 文件
            with open(output_json_path, "w", encoding="utf-8") as f:
                json.dump(result, f, ensure_ascii=False, indent=2)

            logger.info(f"文件描述已保存到: {output_json_path}")

        except Exception as e:
            logger.error(f"生成文件描述失败: {e}")

    def test_gen_examples(self):
        logger.info("开始收集examples信息")
        output_dir = Path(f"output/{self.project_name}")
        output_dir.mkdir(parents=True, exist_ok=True)

        # 1. 抽取 examples_info
        examples_info_file = output_dir / "1_examples_info.json"
        if examples_info_file.exists():
            logger.info("发现已有examples信息,直接读取")
            with open(examples_info_file, encoding="utf-8") as f:
                examples_info = json.load(f)
        else:
            logger.info("收集examples信息")
            examples_info = self.collect_examples_info()
            with open(examples_info_file, "w", encoding="utf-8") as f:
                json.dump(examples_info, f, ensure_ascii=False, indent=2)

        # 2. 选择合适的 examples
        selected_examples_file = output_dir / "2_selected_examples.json"
        if selected_examples_file.exists():
            logger.info("发现已有selected_examples信息,直接读取")
            with open(selected_examples_file, encoding="utf-8") as f:
                selected_examples = json.load(f)
        else:
            logger.info("选择并描述examples")
            selected_examples = self.select_and_describe_examples(examples_info)
            # 保存selected_examples
            with open(selected_examples_file, "w", encoding="utf-8") as f:
                json.dump(selected_examples, f, ensure_ascii=False, indent=2)

        if not selected_examples:
            logger.error("未能选出合适的examples")
            return

        # 3. 收集第一个 example 的所有文件
        logger.info("收集第一个example的所有文件")
        base_example = selected_examples[0]
        base_files = self.collect_example_files(base_example["path"])
        # 保存base_files
        with open(output_dir / "3_base_example_files.json", "w", encoding="utf-8") as f:
            json.dump(base_files, f, ensure_ascii=False, indent=2)

        # 4. 让第一个 example 可运行
        logger.info("让第一个example可运行")
        base_result = self.make_example_runnable(base_example, base_files)
        # 保存base_result
        with open(output_dir / "4_base_example_result.json", "w", encoding="utf-8") as f:
            json.dump(base_result, f, ensure_ascii=False, indent=2)

        # 6. 保存最终的报告
        logger.info("保存markdown报告")
        # self.save_markdown_report(selected_examples, all_results)
        self.save_markdown_report(selected_examples, [base_result])
        print("✅ 评测流程完成！")

    def test_describe_codes(self):
        # 假设你有一个包含文件信息的字典
        files_dict = {
            "files": [
                {
                    "file_name": "/Users/<USER>/Documents/Code/agentic-feynman/output/smolagents/1_test_example/examples/multi_llm_agent.py",
                    "file_content": "...",
                }
            ],
            "execute_py_file_name": "/Users/<USER>/Documents/Code/agentic-feynman/output/smolagents/1_test_example/examples/multi_llm_agent.py",
        }

        # 调用函数生成描述
        self.generate_file_descriptions(files_dict, "output/smolagents/5_code_descriptions.json")

    def run(self):
        # self.test_gen_examples()
        self.test_describe_codes()


def main():
    agent = GitHubGenExampleAgent()
    agent.run()


if __name__ == "__main__":
    main()
