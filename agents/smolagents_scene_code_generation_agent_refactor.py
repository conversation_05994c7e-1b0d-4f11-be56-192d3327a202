"""
Smolagents-based Universal Coding Agent (Refactor)

This module provides a smolagents-based implementation of a universal coding agent
with enhanced support for professional_science_template.py framework:

1. Professional science template framework integration
2. Template-aware code generation process  
3. Enhanced prompt engineering with template examples
4. Quality control for template compliance
5. Improved memory management with template experiences
"""

import os
import subprocess
import sys
from pathlib import Path
from typing import Optional

from loguru import logger

# from openinference.instrumentation.smolagents import SmolagentsInstrumentor
# from phoenix.otel import register
# register()
# SmolagentsInstrumentor().instrument()
from smolagents import ActionStep, ChatMessage, CodeAgent, InferenceClientModel, OpenAIServerModel, Tool

# Add project root to path
cwd = os.getcwd()
sys.path.insert(0, cwd)

# Import smolagents-compatible tools
from tools.code_agent_tools.smolagents_adapters import (
    bash_execute,
    check_code_issues,
    file_create,
    file_view,
    get_library_docs,
    list_files,
    replace_in_file,
    resolve_library_id,
    search_files,
    sequential_thinking,
)
from utils.common import Config


class SmolagentsUniversalCodingAgentRefactor:
    """
    Smolagents-based universal coding agent with professional template integration.

    This refactored agent can handle various programming tasks with enhanced support for
    ProfessionalScienceTemplate framework, incorporating best practices from leading AI 
    coding assistants and template-aware code generation.
    """

    def __init__(self, working_dir: Optional[str] = None):
        """
        Initialize the smolagents-based refactor toolkit.

        Args:
            working_dir: Working directory for file operations (default: current directory)
        """
        self.working_dir = Path(working_dir or os.getcwd()).resolve()

        config = Config().config.get("workflow", {}).get("code_agent", {})
        self.enable_sequential_thinking = config.get("enable_sequential_thinking", False)
        self.enable_get_docs = config.get("enable_get_docs", False)
        self.enable_memory = config.get("enable_memory", True)  # Enable memory by default
        self.memory_file_path = config.get("memory_file_path", "universal_coding_agent_memory_refactor.md")
        self.model_type = config.get("model", "google/gemini-2.5-flash-preview-05-20")
        self.memory_model_type = config.get("memory_model", "google/gemini-2.5-flash-lite-preview-06-17")
        self.summary_model_type = config.get("summary_model", "google/gemini-2.5-flash-lite-preview-06-17")
        max_iteration_per_step = config.get("max_iteration_per_step", 20)

        # Initialize memory system
        if self.enable_memory:
            self._ensure_memory_directory()

        # Create smolagents-compatible model
        self.model = self._create_smolagents_model(self.model_type)

        # Create agent with tools and template-aware instructions
        self.agent = CodeAgent(
            tools=self._get_all_tools(),
            model=self.model,
            instructions=self._create_system_instructions(),
            max_steps=max_iteration_per_step,
            max_print_outputs_length=1000,
        )
        self.memory_agent = self._create_memory_agent()

        logger.info(
            f"Universal coding agent refactor initialized with working directory: {self.working_dir},"
            f" max_steps: {max_iteration_per_step},"
            f" sequential_thinking: {self.enable_sequential_thinking}, get_docs: {self.enable_get_docs},"
            f" memory: {self.enable_memory}"
        )

    def _create_smolagents_model(self, model: str = None):
        """Create a smolagents-compatible model from the existing model configuration."""
        try:
            # Get model configuration from config file
            config = Config().config
            model_config = config.get("model", {})
            api_config = model_config.get("api", {})

            # Extract OpenRouter configuration
            model_type = model or model_config.get("type", "google/gemini-2.5-flash-preview-05-20")
            api_key = api_config.get("openai_compatibility_api_key")
            api_base = api_config.get("openai_compatibility_api_base_url", "https://openrouter.ai/api/v1")
            # temperature = model_config.get("temperature", 0.7)
            max_tokens = model_config.get("max_tokens", 32768)

            if not api_key:
                raise RuntimeError("No OpenRouter API key found in config, using default InferenceClientModel")

            # Create OpenAI-compatible model for OpenRouter
            model = OpenAIServerModel(
                model_id=model_type,
                api_base=api_base,
                api_key=api_key,
                temperature=0.0,
                max_tokens=max_tokens,
            )

            logger.info(f"Created smolagents OpenAIServerModel with model: {model_type}")
            return model

        except Exception as e:
            logger.warning(f"Failed to create smolagents model from config: {e}")
            # Fallback to default model
            return InferenceClientModel()

    def _get_all_tools(self) -> list:
        """
        Get all tools for smolagents framework integration.

        Returns:
            List of smolagents-compatible tools
        """
        tools: list[Tool] = []

        # Add sequential thinking tool conditionally
        if self.enable_sequential_thinking:
            tools.append(sequential_thinking)

        # Add core tools
        tools.extend(
            [
                file_view,
                file_create,
                list_files,
                replace_in_file,
                search_files,
                bash_execute,
                check_code_issues,
            ]
        )

        # Add documentation tools conditionally
        if self.enable_get_docs:
            tools.extend(
                [
                    resolve_library_id,
                    get_library_docs,
                ]
            )

        logger.info(f"Total tools registered for smolagents refactor: {len(tools)}")
        for tool in tools:
            logger.info(tool.name)
        return tools

    def _create_system_instructions(self) -> str:
        """
        Create system instructions for the smolagents agent with professional template integration.
        Enhanced version with ProfessionalScienceTemplate framework awareness.
        """
        return """# 专业科学模板化编程专家（Smolagents重构版）

## 角色定义
专业编程专家，具备跨多种语言、框架和技术栈的自主调试能力，特别专精于基于 `ProfessionalScienceTemplate` 框架的高质量、可维护和可扩展代码生成，遵循行业最佳实践和世界级教学视觉标准。

## 🏗️ ProfessionalScienceTemplate 核心架构理解

### 模板布局系统精通
你必须完全理解以下布局架构：
```
┌─────────────────┬─────────────────┐
│  标题区域       │   步骤介绍区域   │  <- 各占顶部10%
│  (TOP_LEFT)     │   (TOP_RIGHT)   │
├─────────────────┼─────────────────┤
│                 │                 │
│     辅助区域    │    主内容区域    │  <- 左15%，中60%
│    (LEFT)       │    (CENTER)     │
│                 │                 │
│                 ├─────────────────┤
│                 │    辅助区域      │  <- 右15%
│                 │    (RIGHT)      │
├─────────────────┴─────────────────┤
│           结果区域                │  <- 底部10%
│           (DOWN)                  │
└───────────────────────────────────┘
```

### 标准化区域接口掌握（关键技能）
你必须熟练使用以下接口，这是模板化代码生成的核心：

1. **`create_title_region_content(title_text)`**
   - 位置：左上角标题区域
   - 特点：固定字体大小，蓝色主题
   - 内容建议：8字以内（如"函数原理"、"排序算法"）

2. **`create_step_region_content(step_text)`**
   - 位置：右上角步骤区域  
   - 特点：固定字体大小，紫色主题
   - 内容建议：12字以内（如"第一步：数据初始化"）

3. **`create_main_region_content(main_content)`**
   - 位置：屏幕中央主内容区域
   - 特点：自动适配大小，支持任何Mobject对象
   - 用途：核心可视化内容（图形、动画、坐标系等）

4. **`create_left_auxiliary_content(title, items)`**
   - 位置：左侧辅助区域
   - 特点：概念要点展示，深色背景，白色文字
   - 内容建议：5项以内，每项15字以内

5. **`create_right_auxiliary_content(title, items)`**
   - 位置：右侧辅助区域
   - 特点：公式特点展示，与左侧对称
   - 内容建议：5项以内，支持MathTex

6. **`create_result_region_content(result_text)`**
   - 位置：底部结果区域
   - 特点：成功色突出，横跨全屏
   - 内容建议：40字以内的总结陈述

### 专业色彩系统记忆
- **主要色**: `#FDE047` (亮黄色) - 标题等主要元素
- **次要色**: `#FACC15` (金黄色) - 强调元素  
- **重点色**: `#F59E0B` (橙黄色) - 重点标记
- **成功色**: `#EAB308` (金色) - 结果展示
- **文字色**: `WHITE` - 所有文字统一纯白色
- **辅助背景**: `#2D3436` - 辅助区域深色背景

## 核心开发工作流程

### 1. 模板框架上下文与规划
- **评估任务复杂性**: 对于大型任务（>100行或不熟悉框架），投入时间进行系统性模板规划
- **模板区域内容规划**: 分析描述，确定各区域的具体内容分配
  - 标题区域：概念名称（8字以内）
  - 步骤区域：当前操作（12字以内）
  - 主内容区域：核心可视化
  - 辅助区域：概念要点或公式特点
  - 结果区域：总结陈述（40字以内）
- **布局模式选择**: 
  - 完整布局（左右辅助区域都有）- 复杂概念
  - 纯净布局（仅主内容区域）- 专注展示
  - 左侧布局（仅左辅助区域）- 突出要点
- **验证模板假设**: 检查模板文档，确认接口使用方法，特别是不熟悉的区域

### 2. 模板化增量开发
- **模板框架起步**: 从继承`ProfessionalScienceTemplate`的工作框架开始
- **标准接口构建**: 逐一使用标准化区域接口添加内容
- **模板合规测试**: 每个里程碑验证模板规范性
- **基于反馈适应**: 使用验证结果指导下一步和课程修正

### 3. 模板质量保证
- **系统化验证**: 运行语法检查、编译和模板特定验证
- **完整测试**: 在需求中提到时执行验证步骤
- **专业标准**: 确保代码成功运行并符合教学视觉标准
- **验证驱动完成**: 指定验证时任务完成需要成功验证

## 模板决策指南

### 何时进行高级模板规划:
- 任务涉及多个区域内容或复杂动画序列
- 使用不熟悉的模板接口或API
- 需要复杂逻辑或算法可视化实现
- 多个互连区域需要协调

### 何时查阅模板文档:
- 使用模板特定功能或接口
- 不确定区域接口的参数要求
- 需要验证最佳实践或设计模式
- 处理版本特定的模板实现

### 何时执行模板验证:
- 增量开发里程碑后
- 声明任务完成前
- 需求中明确指定验证命令时
- 重大代码修改后

## 模板质量标准

### 避免的做法:
- 生成大量代码库（>100行）而不进行增量模板测试
- 需求中明确要求时跳过模板验证
- 在没有充分理解模板架构的情况下进行
- 不运行指定验证步骤就声明完成

### 优先考虑的实践:
- 复杂任务编码前系统性模板规划
- 彻底研究不熟悉的模板接口和框架
- 通过验证检查点进行构建和测试
- 执行需求中提到的所有模板验证步骤
- 在逻辑里程碑验证模板功能

## 模板化成功标准
任务被认为完成当:
1. **模板实现工作**: 代码编译运行无错误，正确继承模板
2. **需求完成**: 实现所有指定功能，使用标准化接口
3. **模板验证通过**: 指定时验证命令成功执行
4. **质量维护**: 代码遵循模板设计模式并符合教学标准
5. **规划充分**: 复杂任务显示系统性模板方法的证据

## 模板错误恢复方法
- 系统分析模板合规性失败以了解根本原因
- 增量修复模板问题而不是批量重写
- 每次修复后重新验证以确保模板规范进展
- 从模式中学习以防止类似的模板问题
- 优先考虑可用模板解决方案而不是完美代码

## 模板化工具使用理念
- **选择适当的工具** 基于任务特征和模板复杂性
- **通过并行操作最大化效率** 收集模板信息时
- **使用有针对性的编辑** 而不是在可能时完整文件重写
- **利用模板文档** 验证框架特定实现
- **系统性验证** 特别是当指定模板验证步骤时

## 🎯 特殊要求：Manim + ProfessionalScienceTemplate

### 模板化代码生成要求
当生成Manim代码时，你必须：

1. **强制模板继承**: 
   ```python
   class YourSceneClass(ProfessionalScienceTemplate):
       def construct(self):
           self.setup_background()  # 必须调用
   ```

2. **模板导入正确性**:
   ```python
   from manim import *
   from prompts.professional_science_template import ProfessionalScienceTemplate
   ```

3. **标准化接口使用**: 避免手动布局，必须使用模板提供的区域接口

4. **内容规划合规**: 严格遵循内容建议指南的长度和格式要求

5. **模板验证命令**: 
   - 编译检查：`python -m py_compile file_path`
   - Manim验证：`manim --dry_run --progress_bar none file_path`

### 模板化布局模式选择指南
```python
# 完整布局模式 - 适合复杂概念讲解
title = self.create_title_region_content("概念名称")
step = self.create_step_region_content("第一步：初始化") 
main = self.create_main_region_content(visualization_content)
left = self.create_left_auxiliary_content("要点:", ["要点1", "要点2"])
right = self.create_right_auxiliary_content("公式:", [MathTex("公式")])
result = self.create_result_region_content("结论：...")

# 纯净布局模式 - 适合专注主内容
title = self.create_title_region_content("可视化名称")
step = self.create_step_region_content("演示过程")
main = self.create_main_region_content(complex_visualization)  
result = self.create_result_region_content("总结：...")

# 左侧布局模式 - 适合突出要点
title = self.create_title_region_content("算法名称")
step = self.create_step_region_content("执行阶段")
main = self.create_main_region_content(algorithm_visual)
left = self.create_left_auxiliary_content("步骤:", ["步骤1", "步骤2"])
result = self.create_result_region_content("完成：...")
```

### 🎓 模板具体实现例子详解

#### 例子1：计算机科学算法演示（ComputerScienceExample）

**快速排序可视化实现要点：**

```python
class ComputerScienceExample(ProfessionalScienceTemplate):
    def construct(self):
        self.setup_background()  # ⚠️ 必须调用
        self.demonstrate_quicksort_algorithm()
    
    def demonstrate_quicksort_algorithm(self):
        # 1. 创建标题和步骤（严格字数限制）
        title_group = self.create_title_region_content("快速排序")  # ✅ 4字，合适
        step_group = self.create_step_region_content("算法初始化")   # ✅ 5字，合适
        
        # 2. 创建主内容（动态排序可视化）
        main_content = self.create_dynamic_sorting_animation()
        main_group = self.create_main_region_content(main_content)
        
        # 3. 使用纯净布局（无辅助区域，主内容享有全部6.0×3.5空间）
        result_group = self.create_result_region_content(
            "快速排序演示：分治算法，平均时间复杂度O(nlogn)"
        )
        
        # 4. 正确的动画顺序
        self.play(Write(title_group), Write(step_group))
        self.play(FadeIn(main_group))
        self.play(Write(result_group))
```

**关键技术特点：**
- 使用圆圈可视化数组元素，支持动态交换动画
- 实现递归分区过程的步进式演示
- 高亮显示基准元素、比较元素、已排序元素
- 弧形路径交换动画，增强视觉效果
- 实时操作说明更新，帮助理解算法步骤

#### 例子2：模板规范骨架（TemplateSkeletonExample）

**完整布局标准实现：**

```python
class TemplateSkeletonExample(ProfessionalScienceTemplate):
    def construct(self):
        # === 第0步：必须先设置背景 ===
        self.setup_background()  # ⚠️ 常见错误：忘记调用此方法
        
        # === 第1步：创建标题（8字以内） ===
        title_good = self.create_title_region_content("数学原理")  # ✅ 4字，完美
        
        # === 第2步：创建步骤描述（12字以内） ===
        step_good = self.create_step_region_content("第一步：函数定义")  # ✅ 8字，合适
        
        # === 第3步：创建主内容（核心展示区域） ===
        main_content = self.create_simple_main_content()
        main_group = self.create_main_region_content(main_content)
        
        # === 第4步：创建辅助区域（完整布局示例） ===
        left_aux_good = self.create_left_auxiliary_content(
            "要点:",  # ✅ 3字标题，合适
            [
                "• 开口向上",        # ✅ 5字，合适
                "• 顶点在原点",      # ✅ 6字，合适
                "• 关于y轴对称",     # ✅ 7字，合适
                "• 最小值为0"        # ✅ 6字，合适
            ]  # ✅ 4个项目，合适
        )
        
        right_aux_good = self.create_right_auxiliary_content(
            "公式:",  # ✅ 3字标题，合适
            [
                MathTex(r"f(x) = x^2"),      # ✅ 数学公式，推荐
                MathTex(r"f'(x) = 2x"),      # ✅ 导数公式，推荐
                "定义域: ℝ",                  # ✅ 7字，合适
                "值域: [0,+∞)"               # ✅ 9字，合适
            ]  # ✅ 4个项目，合适
        )
        
        # === 第5步：创建结果区域（40字以内） ===
        result_good = self.create_result_region_content(
            "结论：二次函数y=x²开口向上，顶点(0,0)，关于y轴对称"  # ✅ 28字，合适
        )
        
        # === 第6步：正确的动画顺序 ===
        self.play(Write(title_good), Write(step_good))
        self.play(FadeIn(main_group))
        self.play(FadeIn(left_aux_good), FadeIn(right_aux_good))
        self.play(Write(result_good))
```

**⚠️ 常见错误和正确做法对比：**

```python
# ❌ 错误示例：
# title_bad = self.create_title_region_content("高等数学中的复杂函数原理详解")  # 16字，太长！
# step_bad = self.create_step_region_content("第一步：建立复杂的数学函数模型并进行详细分析")  # 22字，太长！

# ✅ 正确示例：
title_good = self.create_title_region_content("函数原理")    # 4字，完美
step_good = self.create_step_region_content("第一步：建立模型")  # 8字，合适
```

#### 例子3：三种布局模式对比

**1. 完整布局（左右辅助区域都有）**
```python
# 适用场景：需要显示概念要点和相关公式
left_aux = self.create_left_auxiliary_content("概念:", [...])
right_aux = self.create_right_auxiliary_content("公式:", [...])
# 主内容区域：6.0×3.5，辅助区域：1.4×2.5
```

**2. 纯净布局（无辅助区域）**
```python
# 适用场景：复杂图形展示，需要更大空间
# 主内容享有完整的6.0×3.5空间，视觉冲击力更强
main_group = self.create_main_region_content(complex_visualization)
```

**3. 单侧布局（仅左辅助区域）**
```python
# 适用场景：重点突出关键信息或步骤说明
left_aux = self.create_left_auxiliary_content("要点:", [...])
# 主内容获得更多横向空间
```

### 🎨 模板专业色彩系统

```python
self.colors = {
    'primary': "#FDE047",      # 主要黄色 - 突出内容
    'secondary': "#FACC15",    # 次要金黄色 - 强调色
    'accent': "#F59E0B",       # 强调橙黄色 - 重点标记
    'success': "#EAB308",      # 成功金色 - 结果展示
    'text_primary': WHITE,     # 主要文字颜色
    'text_secondary': WHITE,   # 次要文字颜色
    'auxiliary_text': WHITE,   # 辅助区域文字颜色
    'auxiliary_bg': "#2D3436", # 辅助区域背景颜色
    'continuity': "#10B981",   # 连贯性标记颜色
    'transition': "#8B5CF6",   # 过渡动画颜色
    'persistent': "#EF4444"    # 持久化元素颜色
}
```

### 📐 模板区域布局精确尺寸

```python
self.regions = {
    'title_width': 3.5,         # 标题区域宽度
    'title_height': 0.8,        # 标题区域高度
    'step_width': 3.5,          # 步骤区域宽度
    'step_height': 0.8,         # 步骤区域高度
    'main_width': 6.0,          # 主内容区域宽度（增大）
    'main_height': 3.5,         # 主内容区域高度（增大）
    'auxiliary_width': 1.4,     # 辅助区域宽度（减小）
    'auxiliary_height': 2.5,    # 辅助区域高度
    'result_width': 7.0,        # 结果区域宽度
    'result_height': 0.8        # 结果区域高度
}
```

### 📝 模板内容建议指南（严格遵循）

| 区域类型 | 建议限制 | 示例 |
|---------|---------|-------|
| 标题区域 | 8个字以内 | "数学原理"、"物理定律" |
| 步骤区域 | 12个字以内 | "第一步：数据预处理" |
| 辅助标题 | 6个字以内 | "要点"、"公式"、"特点" |
| 辅助项目 | 5项×15字/项 | "• 开口向上"、"• 时间复杂度O(n²)" |
| 结果区域 | 40个字以内 | "结论：二次函数具有抛物线形状..." |

记住: **基于模板的有效解决方案胜过僵化流程**。根据任务复杂性和模板要求调整你的方法，始终确保符合ProfessionalScienceTemplate的设计原则和视觉标准。"""

    def _to_absolute_path(self, path: str) -> str:
        """Convert relative path to absolute path"""
        path_obj = Path(path)
        if path_obj.is_absolute():
            return str(path_obj)
        return str(self.working_dir / path_obj)

    def _get_memory_file_path(self) -> Path:
        """Get the path to the memory file."""
        return self.working_dir / "memory" / self.memory_file_path

    def _ensure_memory_directory(self):
        """Ensure the memory directory exists."""
        memory_file = self._get_memory_file_path()
        memory_file.parent.mkdir(exist_ok=True)

    def _read_memory(self) -> str:
        """
        Read memory file content.

        Returns:
            Memory content as string, empty string if file doesn't exist or on error
        """
        if not self.enable_memory:
            return ""

        try:
            memory_file = self._get_memory_file_path()
            if memory_file.exists():
                return memory_file.read_text(encoding="utf-8")
            else:
                return ""
        except Exception as e:
            logger.warning(f"Failed to read memory file: {e}")
            return ""

    def _create_memory_agent(self):
        """
        Create a specialized agent for memory updates with file operation tools and template awareness.

        Returns:
            CodeAgent configured for memory management with template experience
        """
        # Import file operation tools
        from tools.code_agent_tools.smolagents_adapters import file_create, file_view, replace_in_file

        # Create memory-specific model (use smaller model for cost efficiency)
        memory_model = self._create_smolagents_model(self.memory_model_type)

        # Create memory agent with file operation tools
        memory_agent = CodeAgent(
            tools=[file_view, file_create, replace_in_file],
            model=memory_model,
            instructions="""# 专业科学模板化编程经验记忆管理专家

你是一个专业的AI助手，负责管理基于ProfessionalScienceTemplate的编程开发经验记忆。你的任务是分析编程开发过程的对话历史，提取有价值的模板化经验和教训，并更新记忆文件。

## 🎯 核心职责
1. **分析对话历史** - 识别模板使用模式、成功策略、技术解决方案和最佳实践
2. **管理记忆文件** - 使用文件操作工具读取、创建和更新记忆文件
3. **结构化存储** - 按技术栈、问题类型和解决方案在markdown格式中分类存储经验

## 📋 推荐记忆文件结构
- **模板相关经验** - ProfessionalScienceTemplate使用技巧和常见问题
- **区域接口使用** - 各区域接口的最佳实践和参数配置
- **布局模式选择** - 不同场景下的最优布局模式
- **内容规划策略** - 如何合理分配各区域内容
- **语言特定经验** - Python、JavaScript、Java等特殊考虑
- **框架和库经验** - Manim、React、Django等最佳实践
- **常见错误模式** - 语法错误、逻辑错误、性能问题的解决方案
- **工具使用技巧** - 开发工具、调试技术、测试策略
- **架构设计原则** - 代码组织、模块划分、接口设计
- **问题-解决方案映射** - 特定问题及其经过验证的解决方案
- **性能优化** - 代码优化技术和模式

## 🔧 工作流程
1. 使用 `file_view` 读取当前记忆文件内容
2. 分析提供的对话历史以提取有价值的编程经验和模板使用经验
3. 使用 `replace_in_file` 更新相关部分，或如果目标文件不存在使用 `file_create` 创建新文件
4. 确保只添加新的、有价值的信息，避免重复
5. 保持内容为中文以确保一致性

## ✅ 更新原则
- **价值导向**: 只记录真正有价值的编程经验和教训，特别关注模板使用模式和成功策略
- **解决方案导向**: 记录有效解决方案和最佳实践，避免引入误导信息
- **准确性和实用性**: 保持信息准确性和实用性，以便future编程任务参考
- **分类组织**: 按技术栈和问题类型组织，便于快速搜索和使用
- **简洁描述**: 使用清晰简洁的语言，理想情况下每个关键点一行
- **增量价值**: 避免重复现有信息，专注于增量价值
- **可操作洞察**: 确保记录的经验可以直接应用于future任务

当开始工作时，我将提供对话历史和记忆文件路径。请自主决定如何更新记忆。""",
            max_steps=10,
        )

        return memory_agent

    def _update_memory(self, conversation_history: str):
        """
        Update memory file using a specialized memory agent with template awareness.

        Args:
            conversation_history: Complete conversation history from current session
        """
        if not self.enable_memory:
            return

        try:
            memory_file_path = str(self._get_memory_file_path())

            # Create task prompt for memory agent with template focus
            memory_task = f"""请分析以下基于ProfessionalScienceTemplate的编程开发对话历史并更新记忆文件。

对话历史:
```
{conversation_history}
```

记忆文件路径: `{memory_file_path}`

**关键约束**:
- 你只能修改上面指定的记忆文件
- **绝不可**修改对话历史中提到的任何其他文件
- 专注于提取可操作的编程洞察和模板使用经验
- 特别关注ProfessionalScienceTemplate相关的经验和教训
- 组织信息以在future编程任务中获得最大效用
"""

            # Create and run memory agent
            self.memory_agent.memory.reset()
            self.memory_agent.run(memory_task)

            logger.info(f"Memory agent completed template-aware update for: {memory_file_path}")

        except Exception as e:
            logger.warning(f"Failed to update memory using template-aware agent: {e}")

    def _get_conversation_history(self, skip_initial_prompts: bool = True, include_full_details: bool = True) -> str:
        """
        Extract complete conversation history from agent memory.

        Args:
            skip_initial_prompts: Whether to skip system prompt and initial task
            include_full_details: Whether to include complete tool calling details

        Returns:
            Formatted conversation history string
        """
        if not hasattr(self.agent, "memory") or not self.agent.memory.steps:
            return "No previous conversation history."

        history_parts = []

        # Add system prompt (unless skipping)
        if not skip_initial_prompts and hasattr(self.agent.memory, "system_prompt"):
            history_parts.append(f"System Prompt: {self.agent.memory.system_prompt.system_prompt}")

        # Process conversation steps
        for i, step in enumerate(self.agent.memory.steps):
            if hasattr(step, "task"):
                # Task step - skip if requested
                if not skip_initial_prompts:
                    history_parts.append(f"Task {i+1}: {step.task}")
            elif isinstance(step, ActionStep):
                # Action step - get complete details
                step_info = f"Step {step.step_number}:"

                # Add model input messages (what was sent to LLM)
                if include_full_details and hasattr(step, "model_input_messages") and step.model_input_messages:
                    step_info += f"\n  Model Input Messages: {len(step.model_input_messages)} messages"
                    # Optionally include the last few messages for context
                    for msg in step.model_input_messages[-2:]:  # Last 2 messages
                        if isinstance(msg, dict):
                            role = msg.get("role", "unknown")
                            content = (
                                str(msg.get("content", ""))[:200] + "..."
                                if len(str(msg.get("content", ""))) > 200
                                else str(msg.get("content", ""))
                            )
                            step_info += f"\n    {role}: {content}"

                # Add model output (LLM's raw response with thoughts and actions)
                if hasattr(step, "model_output_message") and step.model_output_message:
                    output_content = str(step.model_output_message.content)
                    if include_full_details:
                        step_info += f"\n  Model Output: {output_content}"
                    else:
                        step_info += (
                            f"\n  Model Output: {output_content[:500]}..."
                            if len(output_content) > 500
                            else f"\n  Model Output: {output_content}"
                        )

                # Add tool calls (complete information)
                if hasattr(step, "tool_calls") and step.tool_calls:
                    step_info += f"\n  Tool Calls: {len(step.tool_calls)} calls"
                    for j, tool_call in enumerate(step.tool_calls):
                        if hasattr(tool_call, "name"):
                            tool_name = tool_call.name
                            tool_args = getattr(tool_call, "arguments", {})
                            if include_full_details:
                                step_info += f"\n    Call {j+1}: {tool_name}({tool_args})"
                            else:
                                # Truncate long arguments
                                args_str = str(tool_args)
                                if len(args_str) > 200:
                                    args_str = args_str[:200] + "..."
                                step_info += f"\n    Call {j+1}: {tool_name}({args_str})"

                # Add observations (tool results)
                if step.observations:
                    obs_str = str(step.observations)
                    if include_full_details:
                        step_info += f"\n  Observations: {obs_str}"
                    else:
                        step_info += (
                            f"\n  Observations: {obs_str[:300]}..."
                            if len(obs_str) > 300
                            else f"\n  Observations: {obs_str}"
                        )

                # Add errors
                if step.error:
                    step_info += f"\n  Error: {step.error}"

                # Add timing information if available
                if hasattr(step, "duration") and step.duration:
                    step_info += f"\n  Duration: {step.duration:.2f}s"

                history_parts.append(step_info)

        return "\n\n".join(history_parts)

    def get_complete_conversation_history(self) -> str:
        """
        Get complete conversation history including all tool calls and model outputs.
        This is the method you should use to get full records.

        Returns:
            Complete formatted conversation history
        """
        return self._get_conversation_history(skip_initial_prompts=False, include_full_details=True)

    def get_action_steps_only(self) -> str:
        """
        Get only the action steps (tool calls and results), skipping system prompts and tasks.
        Useful for analyzing the actual execution flow.

        Returns:
            Action steps only conversation history
        """
        return self._get_conversation_history(skip_initial_prompts=True, include_full_details=True)

    def get_compact_history(self) -> str:
        """
        Get a compact version of conversation history with truncated details.
        Useful for summaries or when dealing with long conversations.

        Returns:
            Compact conversation history
        """
        return self._get_conversation_history(skip_initial_prompts=True, include_full_details=False)

    def _summarize_previous_iteration(self, iteration_num: int, current_iteration_error: str = None) -> str:
        """
        Summarize the previous iteration using the model with template awareness.

        Args:
            iteration_num: Current iteration number

        Returns:
            Summary of previous iteration
        """
        if iteration_num <= 1:
            return ""

        # Get complete conversation history, skipping initial prompts for cleaner summary
        history = self._get_conversation_history(skip_initial_prompts=True, include_full_details=False)

        # Create a summary prompt with template focus
        summary_prompt = f"""
分析以下基于ProfessionalScienceTemplate的编程开发历史并提供简洁总结，重点关注：

## 📋 关键分析领域:
1. **模板使用方法** - 尝试了哪些模板接口和布局模式
2. **区域内容规划** - 各区域内容如何分配和优化
3. **技术方法尝试** - 尝试了哪些解决方案和实现步骤
4. **错误和问题遇到** - 面临的具体问题及其根本原因
5. **解决方案和修复应用** - 哪些有效，哪些无效
6. **成功实现** - 成功完成的功能和模块
7. **模板合规性** - 模板规范遵循情况和改进
8. **技术和架构决策** - 关键技术选择及其结果
9. **教训学习** - 对future迭代的关键洞察

## 📚 开发历史:
```
{history}
```

## 🌐 当前状态:
{current_iteration_error or "无当前错误。"}

## 🎯 所需输出:
提供结构化总结，帮助下一次迭代：
- **避免重复失败的方法**
- **基于成功的模板实现构建**
- **解决识别的具体模板问题**
- **提高开发效率和代码质量**

专注于直接指导下一个开发迭代的可操作洞察，特别是ProfessionalScienceTemplate相关的经验。
"""

        # logger.debug(f"Template-aware summary prompt for iteration {iteration_num}:\n{summary_prompt}")
        try:
            summarize_model = self._create_smolagents_model(self.summary_model_type)
            summary = summarize_model.generate([ChatMessage(role="user", content=summary_prompt)]).content
            with open(self.working_dir / f"template_history_{iteration_num}.txt", "w", encoding="utf-8") as f:
                f.write(history)
            with open(self.working_dir / f"template_summary_{iteration_num}.txt", "w", encoding="utf-8") as f:
                f.write(summary)
            return summary
        except Exception as e:
            logger.warning(f"Failed to generate template-aware summary for iteration {iteration_num}: {e}")
            return f"Previous iteration encountered issues. Key points from template history: {history[:500]}..."

    def _create_task_prompt(
        self, task_description: str, output_path: str, iteration_num: int = 1, previous_summary: str = ""
    ) -> str:
        """
        Create task-specific prompt for programming tasks with professional template integration.

        Args:
            task_description: Description of the programming task to complete
            output_path: Output file path (if applicable)
            iteration_num: Current iteration number
            previous_summary: Summary of previous iterations
        """
        # Read memory content
        memory_content = self._read_memory()

        # Start with core task description with template awareness
        base_prompt = f"""# 任务: {task_description}

根据视觉描述，生成继承自ProfessionalScienceTemplate的manim代码，并确保最终代码的正确性和完整性，能通过 `manim --dry_run --progress_bar none {output_path}` 的检查。

## 🏗️ 必须遵循的模板要求
1. **继承模板**: 必须继承自 `ProfessionalScienceTemplate`
2. **正确导入**: 
   ```python
   from manim import *
   from prompts.professional_science_template import ProfessionalScienceTemplate
   ```
3. **标准化接口**: 使用模板提供的区域接口，避免手动布局
4. **内容规划**: 遵循模板的内容建议指南
5. **模板验证**: 确保通过模板合规性检查

{f'**输出文件**: `{output_path}`' if output_path else ''}"""

        # Add memory content if available
        if memory_content.strip():
            base_prompt += f"""

## 📚 相关模板经验
```
{memory_content}
```"""

        # Add previous iteration summary if available
        if iteration_num > 1 and previous_summary:
            base_prompt += f"""

## 📝 上一次迭代 (#{iteration_num-1})
```
{previous_summary}
```

**关键点**: 从以上学习，避免重复失败，基于模板成功经验构建。"""

        return base_prompt

    def execute_programming_task(
        self, task_description: str, output_file: str = None, max_iterations: int = 3
    ) -> Optional[str]:
        """
        Execute programming task using smolagents framework with template-aware iteration history.

        Args:
            task_description: Description of the programming task to complete
            output_file: Output file path for the generated code (optional)
            max_iterations: Maximum number of iterations for quality improvement

        Returns:
            Path to generated code file if applicable, or success message, or None on failure
        """
        output_path = self._to_absolute_path(output_file) if output_file else None

        try:
            logger.info("Starting smolagents-based template-aware programming task execution...")
            current_iteration_error = ""

            for iteration in range(max_iterations):
                logger.info(f"Template-aware iteration {iteration + 1}/{max_iterations}")

                # Generate summary of previous iteration if not the first iteration
                previous_summary = ""
                if iteration > 0:
                    logger.info(f"Generating template-aware summary for iteration {iteration + 1}...")
                    previous_summary = self._summarize_previous_iteration(iteration + 1, current_iteration_error)

                # Create task prompt with iteration context and template awareness
                task_prompt = self._create_task_prompt(task_description, output_path, iteration + 1, previous_summary)

                self.agent.memory.reset()
                # Run the agent with the task prompt
                _ = self.agent.run(task_prompt)

                logger.info(f"Agent completed template-aware iteration {iteration + 1}")

                # Update memory with conversation history from this iteration
                if self.enable_memory:
                    try:
                        current_history = self.get_compact_history()
                        with open(self.working_dir / f"template_memory_{iteration + 1}.txt", "w", encoding="utf-8") as f:
                            f.write(current_history)
                        full_history = self.get_complete_conversation_history()
                        with open(self.working_dir / f"template_full_history_{iteration + 1}.txt", "w", encoding="utf-8") as f:
                            f.write(full_history)
                        self._update_memory(current_history)
                        logger.debug(f"Updated template-aware memory after iteration {iteration + 1}")
                    except Exception as e:
                        logger.warning(f"Failed to update template-aware memory after iteration {iteration + 1}: {e}")

                # Check if task was completed successfully with template compliance
                task_completed = True
                if output_path and Path(output_path).exists():
                    # Check for code issues if there's an output file
                    issues_result = check_code_issues([output_path], "error")
                    logger.info(f"Template iteration {iteration + 1}, Code issues: {issues_result}")

                    if "No issues found" in issues_result or "❌" not in issues_result:
                        # Additional template compliance check
                        template_compliance = self._check_template_compliance(output_path)
                        if template_compliance:
                            manim_dryrun_result = bash_execute(f"manim --dry_run --progress_bar none {output_path}")
                            if manim_dryrun_result.startswith(
                                "Command executed successfully"
                            ) or manim_dryrun_result.startswith("Command execution successfully"):
                                logger.success(f"Template-aware programming task completed after {iteration + 1} iterations")
                                return output_path
                            else:
                                logger.error(f"Manim dry run failed: {manim_dryrun_result}")
                                task_completed = False
                                current_iteration_error = manim_dryrun_result
                        else:
                            logger.error(f"Template compliance check failed")
                            task_completed = False
                            current_iteration_error = "Template compliance check failed: ensure proper ProfessionalScienceTemplate inheritance and interface usage"
                    else:
                        task_completed = False
                        current_iteration_error = issues_result
                else:
                    # For tasks without specific output files, assume completion based on agent execution
                    logger.success(f"Template-aware programming task completed after {iteration + 1} iterations")
                    return "Task completed successfully"

                # If not successful, continue to next iteration
                if not task_completed and iteration < max_iterations - 1:
                    logger.info(f"Template iteration {iteration + 1} not successful, continuing...")

            # Return result based on whether there was an output file
            if output_path:
                return output_path if Path(output_path).exists() else None
            else:
                return "Task execution completed"

        except Exception as e:
            logger.error(f"Error in template-aware programming task execution: {e}")
            return None

    def _check_template_compliance(self, code_file_path: str) -> bool:
        """
        Check if the generated code complies with ProfessionalScienceTemplate standards.
        
        Args:
            code_file_path: Path to the generated code file
            
        Returns:
            True if template compliance is satisfied, False otherwise
        """
        try:
            with open(code_file_path, 'r', encoding='utf-8') as f:
                code_content = f.read()
            
            # Basic template compliance checks
            compliance_checks = [
                'ProfessionalScienceTemplate' in code_content,  # Template inheritance
                'def construct(self):' in code_content,  # Required method
                'self.setup_background()' in code_content,  # Required setup call
            ]
            
            # Check for at least one standardized interface usage
            interface_usage = any([
                'create_title_region_content' in code_content,
                'create_step_region_content' in code_content,
                'create_main_region_content' in code_content,
                'create_left_auxiliary_content' in code_content,
                'create_right_auxiliary_content' in code_content,
                'create_result_region_content' in code_content,
            ])
            
            compliance_checks.append(interface_usage)
            
            compliance_result = all(compliance_checks)
            
            if compliance_result:
                logger.info("Template compliance check passed")
            else:
                logger.warning("Template compliance check failed")
                
            return compliance_result
            
        except Exception as e:
            logger.error(f"Template compliance check error: {e}")
            return False

    def generate_manim_code_enhanced(
        self, scene_description: str, output_file: str, max_iterations: int = 3
    ) -> Optional[str]:
        """
        Backward compatibility method for Manim code generation with template awareness.

        Args:
            scene_description: Description of the scene to generate
            output_file: Output file path for the generated code
            max_iterations: Maximum number of iterations for quality improvement

        Returns:
            Path to generated and debugged code file, or None on failure
        """
        return self.execute_programming_task(scene_description, output_file, max_iterations)

    def render_manim_code(self, code_file: str, quality: str = "l") -> Optional[str]:
        """
        Render Manim code to video.
        This method is identical to the camel implementation.
        """
        try:
            code_path = Path(code_file)
            if not code_path.exists():
                logger.error(f"Code file not found: {code_file}")
                return None

            # Prepare manim command
            cmd = [
                "manim",
                str(code_path),
                "--quality",
                quality,
            ]

            logger.info(f"Rendering Manim code: {' '.join(cmd)}")

            # Run manim command
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
            )

            if result.returncode == 0:
                resolution = {
                    "l": "480p15",
                    "m": "720p30",
                    "h": "1080p60",
                    "q": "1440p60",
                    "k": "2160p60",
                }[quality]
                output_path = Path("media") / "videos" / code_path.stem / resolution
                # Find the generated video file
                video_files = list(output_path.glob("*.mp4"))
                if video_files:
                    # Sort by modification time (newest first) and take the most recent one
                    video_file = max(video_files, key=lambda f: f.stat().st_mtime)
                    logger.success(f"Video rendered successfully: {video_file}")
                    return str(video_file)
                else:
                    logger.error(f"No video file found at {output_path} after rendering")
                    return None
            else:
                error_msg = result.stderr or result.stdout
                logger.error(f"Manim rendering failed: {error_msg}")
                raise subprocess.CalledProcessError(result.returncode, cmd, error_msg)

        except subprocess.TimeoutExpired:
            logger.error("Manim rendering timed out")
            return None
        except Exception as e:
            logger.error(f"Failed to render Manim code: {e}")
            raise


# Alias for backward compatibility with enhanced template awareness
SmolagentsSceneCodeGenerationToolkitRefactor = SmolagentsUniversalCodingAgentRefactor 