"""
Git仓库信息收集代理（本地版）

负责解析和分析本地Git仓库，提取核心信息，包括代码结构、README、Star数据等
"""

import datetime
import json
import logging
import os
import re
import subprocess
import sys
from typing import Any, Optional

import requests
import yaml
from camel.agents import ChatAgent
from camel.logger import set_log_level
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.types import ModelPlatformType

# 从环境变量加载配置
from dotenv import load_dotenv
from PIL import Image, ImageDraw, ImageFont

load_dotenv()

# 设置日志级别为INFO，并配置日志格式
log_file = f"output/git_info_collector_log_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
os.makedirs(os.path.dirname(log_file), exist_ok=True)

# 配置根日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(),  # 同时输出到控制台
    ],
)

# 获取logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 必要的目录结构
os.makedirs("config", exist_ok=True)
os.makedirs("output", exist_ok=True)
os.makedirs("output/images", exist_ok=True)

# 设置日志级别
set_log_level(level="WARNING")


# 分析结果
REPO_ANALYSIS_FORMAT_PROMPT = """
[
    {
        "模块": "分析的模块名，如项目基础信息/功能性评测/技术实现/代码结构/测试用例/易用性/社区与生态/安全性/对比竞品/总结建议等",
        "模块内容": "这个模块对应仓库的具体内容总结",
        "优点": "这个模块的优点",
        "缺点": "这个模块的缺点"
    }
]
"""


# 系统提示词
SYSTEM_PROMPT = """
你是一个专业的Git仓库分析代理，你的职责是分析GitHub仓库，并给出详细的分析报告，报告包括以下信息：

## 1. 项目基础信息
## 2. 功能性评测
## 3. 技术实现
## 4. 代码结构
## 5. 测试用例
## 6. 易用性
## 7. 社区与生态
## 8. 安全性
## 9. 对比竞品
## 10. 总结建议

请提供详细、准确的项目分析，帮助用户快速理解项目的价值和使用方法。
"""

# 仓库信息收集提示词
REPO_INFO_COLLECTOR_PROMPT = """
请分析以下GitHub仓库，收集完整的信息：{repo}

## 1. 项目基础信息
- **项目名称**：仓库链接、作者/团队背景
- **项目类型**：工具库/框架/应用软件/CLI工具
- **技术栈**：核心语言、依赖技术（如React/TensorFlow）
- **项目热度**：
  - GitHub Stars/Forks数量
  - 提交频率（`git commit`活跃度）
  - Issues/PRs响应速度

## 2. 功能性评测
### 优点
- **核心功能**：是否解决标称问题？功能是否完整？
- **扩展性**：是否支持插件/API集成？
- **兼容性**：支持的操作系统、语言版本等
### 缺点
- **功能缺失**：相比竞品缺少的关键功能
- **过度设计**：是否存在冗余功能？

## 3. 技术实现
### 优点
- **代码质量**：结构清晰度、单元测试覆盖率
- **性能**：吞吐量/延迟/内存占用基准数据
- **架构设计**：是否模块化/微服务？

### 缺点
- **技术债务**：未解决的`TODO`或临时补丁
- **依赖风险**：是否依赖过时库？


## 4. 代码结构
### 优点
- **模块化程度**
  - 是否采用清晰的模块划分（如按功能/层级划分）
  - 模块间依赖关系是否合理（低耦合高内聚）
- **代码组织**
  - 目录结构是否逻辑清晰（如`src/`、`tests/`分离）
  - 文件命名是否规范统一（遵循项目约定）
- **代码可读性**
  - 注释是否完整准确（特别是复杂逻辑）
  - 代码风格是否一致（遵循语言规范）

### 缺点
- **结构混乱**
  - 是否存在"上帝类"或超大文件（>1000行）
  - 是否存在重复代码（可复用但未抽象）
- **依赖管理**
  - 是否存在循环依赖问题
  - 第三方依赖是否过多或版本混乱


## 5. 测试用例
### 优点
- **测试覆盖率**
  - 核心功能是否全面覆盖（单元测试≥80%）
  - 边界条件是否充分测试
- **测试质量**
  - 是否使用专业测试框架（如Jest/pytest）
  - 测试用例是否包含真实场景数据
- **自动化程度**
  - 是否集成CI/CD流程
  - 测试失败是否阻断部署

### 缺点
- **测试缺失**
  - 关键模块是否存在测试盲区
  - Mock数据是否过于简单
- **维护成本**
  - 测试代码是否同步更新
  - 测试执行是否耗时过长


## 6. 易用性
### 优点
- **入门体验**：5分钟内能否跑通Demo？
- **文档质量**：API文档/故障排查指南
- **交互设计**：CLI/UI是否直观？

### 缺点
- **配置复杂**：需手动编辑多个文件？
- **学习曲线**：是否需要前置知识？

## 7. 社区与生态
### 优点
- **社区活跃度**：Discord/Slack互动频率
- **生态支持**：第三方插件/模板数量
- **长期维护**：更新频率/背后支持方
### 缺点
- **维护风险**：作者是否停止更新？
- **生态薄弱**：缺乏扩展工具？

## 8. 安全性
### 优点
- **安全实践**：依赖更新/漏洞扫描（如CodeQL）
- **权限控制**：细粒度访问管理

### 缺点
- **已知漏洞**：未修复的GitHub Security Advisories
- **敏感数据**：是否明文存储密码？

## 9. 对比竞品
- **优势场景**：何时比竞品更优？（性能/成本）
- **劣势场景**：何时应选择其他工具？

## 10. 总结建议
- **适用人群**：明确目标用户（如"中小团队原型开发"）
- **推荐指数**：根据需求场景打分


每个部分详细展开，必要情况下提供具体例子和代码示例。对于技术栈，请详细列出所有主要的依赖库和工具。
结果应包含仓库的核心信息，帮助理解项目的目的和架构。
"""


class GitInfoCollectorAgent:
    """
    Git仓库信息收集代理（本地版）

    负责解析和分析本地Git仓库，提取核心信息包括：
    - 代码结构和组织
    - 技术栈和依赖关系
    - 项目文档和README
    - Stars和贡献者数据
    - 可视化表示
    """

    def __init__(self, config_path="config/config.yaml"):
        """初始化Git信息收集代理"""
        # 创建默认配置文件（如果不存在）
        self._create_default_config_if_not_exists(config_path)

        # 加载配置
        self.load_config(config_path)

        # 输出目录
        self.output_dir = "output"
        os.makedirs(self.output_dir, exist_ok=True)

        # 初始化模型和代理（如果API密钥有效）
        try:
            # 初始化模型
            self.model = self._create_model()
            logging.info("模型初始化完成")

            # 初始化代理
            self.agent = self._create_agent()
            logging.info("代理初始化完成")
        except ValueError as e:
            if "API密钥" in str(e):
                logging.warning(f"API初始化失败: {str(e)}")
                self.model = None
                self.agent = None
            else:
                raise

    def _create_default_config_if_not_exists(self, config_path):
        """如果配置文件不存在，创建默认配置"""
        if not os.path.exists(config_path):
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            default_config = {
                "model": {
                    "type": "gpt-3.5-turbo",  # 使用更可靠的模型ID
                    "api": {
                        "openai_compatibility_api_key": os.environ.get("OPENAI_API_KEY", ""),
                        "openai_compatibility_api_base_url": os.environ.get(
                            "OPENAI_API_BASE", "https://api.openai.com/v1"
                        ),
                    },
                },
                "repo": {
                    "default_branch": "main",
                    "max_file_size": 1024 * 1024,  # 1MB
                    "max_files_per_dir": 5,
                    "max_total_files": 20,
                    "important_extensions": [".py", ".js", ".ts", ".java", ".c", ".cpp", ".go", ".rs", ".json"],
                },
                "github_api": {
                    "use_token": False,
                    "token": "",
                },
                "output": {
                    "images_dir": "output/images",
                    "json_output": True,
                    "md_output": True,
                },
            }
            with open(config_path, "w") as f:
                yaml.dump(default_config, f)

    def load_config(self, config_path):
        """从YAML文件加载配置"""
        try:
            with open(config_path) as file:
                config = yaml.safe_load(file)
            # 设置配置属性
            self.model_config = config.get("model", {})
            self.repo_config = config.get("repo", {})
            self.github_api_config = config.get("github_api", {})
            self.output_config = config.get("output", {})
            logging.info(f"从 {config_path} 加载配置")
        except Exception as e:
            logging.error(f"加载配置出错: {str(e)}")
            raise

    def _create_model(self):
        """根据配置创建模型实例"""
        api_config = self.model_config.get("api", {})

        return ModelFactory.create(
            model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
            model_type=self.model_config.get("type", "openai/gpt-4o-mini"),
            api_key=api_config.get("openai_compatibility_api_key"),
            url=api_config.get("openai_compatibility_api_base_url"),
        )

    def _create_agent(self):
        """创建ChatAgent实例"""
        system_message = BaseMessage.make_assistant_message(
            role_name="Git仓库分析者",
            content=SYSTEM_PROMPT,
        )
        return ChatAgent(model=self.model, system_message=system_message)

    def process_message(self, message: str) -> str:
        """
        处理消息并返回响应

        Args:
            message: 提示消息

        Returns:
            str: 代理响应内容
        """
        # 检查代理是否可用
        if self.agent is None:
            return "AI分析未进行: 无有效的API密钥或模型"

        try:
            # 发送消息并获取响应
            user_message = BaseMessage.make_user_message(role_name="User", content=message)
            response = self.agent.step(user_message)
            logger.debug(response)

            # 获取响应内容
            return response.msg.content
        except Exception as e:
            logger.error(f"处理消息失败: {str(e)}")

            # 返回错误信息
            return f"处理消息时出错: {str(e)}"

    def clone_repository(self, repo_path: str) -> tuple[bool, str, str]:
        """
        验证本地Git仓库路径

        Args:
            repo_path: 本地Git仓库路径

        Returns:
            Tuple[bool, str, str]: 成功状态, 仓库路径, 错误信息
        """
        try:
            # 验证路径是否存在
            print(f"正在验证本地仓库路径: {repo_path}")

            if not os.path.exists(repo_path):
                return False, "", f"仓库路径不存在: {repo_path}"

            # 验证是否为有效的Git仓库
            git_dir = os.path.join(repo_path, ".git")
            if not os.path.exists(git_dir):
                return False, "", f"提供的路径不是一个有效的Git仓库: {repo_path}"

            # 验证仓库是否可访问
            try:
                subprocess.run(["git", "-C", repo_path, "status"], capture_output=True, check=True, timeout=10)
            except subprocess.CalledProcessError:
                return False, "", f"无法访问Git仓库: {repo_path}"
            except subprocess.TimeoutExpired:
                return False, "", f"访问Git仓库超时: {repo_path}"

            logger.info(f"本地仓库 {repo_path} 验证成功")
            return True, repo_path, ""

        except Exception as e:
            error_msg = f"验证本地仓库路径过程中出现异常: {str(e)}"
            logger.error(error_msg)
            return False, "", error_msg

    def collect_repo_content(self, repo_path: str) -> dict[str, Any]:
        """
        收集仓库内容结构和关键文件

        Args:
            repo_path: 仓库本地路径

        Returns:
            Dict[str, Any]: 收集到的仓库内容信息
        """
        # 验证仓库路径存在
        if not os.path.exists(repo_path):
            logger.error(f"仓库路径不存在: {repo_path}")
            return {"error": f"仓库路径不存在: {repo_path}"}

        if not os.path.exists(os.path.join(repo_path, ".git")):
            logger.error(f"路径不是有效的Git仓库: {repo_path}")
            return {"error": f"路径不是有效的Git仓库: {repo_path}"}

        content = {
            "name": os.path.basename(repo_path),
            "files": {},
            "directories": [],
            "readme": None,
            "license": None,
            "package_info": None,
            "github_info": None,
            "screenshots": [],
        }

        # 读取README文件
        readme_files = ["README.md", "README", "Readme.md", "readme.md"]
        for readme in readme_files:
            readme_path = os.path.join(repo_path, readme)
            if os.path.exists(readme_path):
                try:
                    with open(readme_path, encoding="utf-8", errors="ignore") as f:
                        readme_content = f.read()
                        content["readme"] = readme_content

                        # 创建README截图
                        screenshot_path = self._create_readme_screenshot(readme_content, repo_path)
                        if screenshot_path:
                            content["screenshots"].append({"type": "readme_preview", "path": screenshot_path})
                    break
                except Exception as e:
                    logging.warning(f"读取README文件失败: {str(e)}")

        # 检查package.json、setup.py、requirements.txt等依赖文件
        package_files = ["package.json", "setup.py", "requirements.txt", "Cargo.toml", "go.mod"]
        for package_file in package_files:
            package_path = os.path.join(repo_path, package_file)
            if os.path.exists(package_path):
                try:
                    with open(package_path, encoding="utf-8", errors="ignore") as f:
                        content["package_info"] = {"type": package_file, "content": f.read()}
                    break
                except Exception as e:
                    logging.warning(f"读取依赖文件失败: {str(e)}")

        # 获取目录结构
        max_files_per_dir = self.repo_config.get("max_files_per_dir", 5)
        max_total_files = self.repo_config.get("max_total_files", 20)
        important_extensions = self.repo_config.get(
            "important_extensions", [".py", ".js", ".ts", ".java", ".c", ".cpp", ".go", ".rs", ".json"]
        )

        for root, dirs, files in os.walk(repo_path, topdown=True):
            # 跳过.git目录
            if ".git" in dirs:
                dirs.remove(".git")

            # 限制遍历深度和文件数量，防止处理过大的仓库
            if root.replace(repo_path, "").count(os.sep) > 2:
                continue

            rel_path = os.path.relpath(root, repo_path)
            if rel_path != ".":
                content["directories"].append(rel_path)

            for file in files[:max_files_per_dir]:  # 限制每个目录最多文件数
                file_path = os.path.join(root, file)
                file_rel_path = os.path.relpath(file_path, repo_path)

                # 只读取重要文件内容
                file_ext = os.path.splitext(file)[1]
                max_file_size = self.repo_config.get("max_file_size", 30 * 1024)  # 默认30KB

                if file_ext in important_extensions and os.path.getsize(file_path) < max_file_size:
                    try:
                        with open(file_path, encoding="utf-8", errors="ignore") as f:
                            content["files"][file_rel_path] = f.read()
                    except Exception as e:
                        logging.warning(f"读取文件失败: {file_rel_path}, {str(e)}")

                # 记录超过max_total_files个文件后停止
                if len(content["files"]) >= max_total_files:
                    break

        # 获取git提交信息
        try:
            # 获取最近5次提交
            git_log = subprocess.run(
                ["git", "-C", repo_path, "log", "--pretty=format:%h|%an|%ad|%s", "--date=short", "-n", "5"],
                check=True,
                capture_output=True,
                text=True,
            )
            content["commits"] = []
            for line in git_log.stdout.splitlines():
                parts = line.split("|", 3)
                if len(parts) == 4:
                    commit = {"hash": parts[0], "author": parts[1], "date": parts[2], "message": parts[3]}
                    content["commits"].append(commit)
        except Exception as e:
            logging.warning(f"获取git提交信息失败: {str(e)}")

        # 获取GitHub仓库信息
        github_info = self._get_github_info(repo_path)
        if github_info:
            content["github_info"] = github_info

            # 添加可视化结果
            if "stars" in github_info:
                star_image_path = self._create_star_visualization(github_info, repo_path)
                if star_image_path:
                    content["screenshots"].append({"type": "stars_visualization", "path": star_image_path})

        return content

    def _get_github_info(self, repo_path: str) -> Optional[dict[str, Any]]:
        """
        获取GitHub仓库信息

        Args:
            repo_path: 仓库本地路径

        Returns:
            Optional[Dict[str, Any]]: GitHub仓库信息，包括stars、forks等
        """
        try:
            # 从.git/config中提取GitHub仓库URL
            git_config_path = os.path.join(repo_path, ".git", "config")
            github_repo_url = None

            if os.path.exists(git_config_path):
                with open(git_config_path, encoding="utf-8", errors="ignore") as f:
                    git_config = f.read()
                    # 提取GitHub URL
                    url_match = re.search(
                        r"url\s*=\s*(?:https?://github\.com/|git@github\.com:)([^/]+)/([^.]+)(?:\.git)?", git_config
                    )
                    if url_match:
                        owner = url_match.group(1)
                        repo = url_match.group(2)
                        github_repo_url = f"https://github.com/{owner}/{repo}"

                        # 尝试获取GitHub仓库信息
                        github_api_url = f"https://api.github.com/repos/{owner}/{repo}"

                        # 使用GitHub API获取仓库信息
                        headers = {}
                        if self.github_api_config.get("use_token", False) and self.github_api_config.get("token"):
                            headers["Authorization"] = f"token {self.github_api_config['token']}"

                        try:
                            response = requests.get(github_api_url, headers=headers, timeout=10)
                            if response.status_code == 200:
                                repo_data = response.json()

                                # 获取star历史
                                stars_history = self._get_stars_history(owner, repo)

                                return {
                                    "url": github_repo_url,
                                    "stars": repo_data.get("stargazers_count", 0),
                                    "forks": repo_data.get("forks_count", 0),
                                    "watchers": repo_data.get("subscribers_count", 0),
                                    "open_issues": repo_data.get("open_issues_count", 0),
                                    "created_at": repo_data.get("created_at", ""),
                                    "updated_at": repo_data.get("updated_at", ""),
                                    "pushed_at": repo_data.get("pushed_at", ""),
                                    "description": repo_data.get("description", ""),
                                    "language": repo_data.get("language", ""),
                                    "stars_history": stars_history,
                                }
                            else:
                                logging.warning(f"获取GitHub仓库信息失败，状态码: {response.status_code}")
                        except requests.exceptions.RequestException as e:
                            logging.warning(f"请求GitHub API时出错: {str(e)}")
                    else:
                        logging.warning("无法从git config中提取GitHub仓库URL")

            return None
        except Exception as e:
            logging.warning(f"获取GitHub信息失败: {str(e)}")
            return None

    def _get_stars_history(self, owner: str, repo: str) -> list[dict[str, Any]]:
        """
        获取GitHub仓库的star历史

        Args:
            owner: 仓库所有者
            repo: 仓库名称

        Returns:
            List[Dict[str, Any]]: star历史记录列表
        """
        try:
            # 使用star-history API或其他方法获取star历史
            # 这里使用简化的模拟数据，实际应用中可以使用GitHub API或第三方服务
            # 如 https://star-history.com/
            url = "https://api.star-history.com/graphql"
            query = """
            query StarHistory($owner: String!, $repo: String!) {
                repository(owner: $owner, name: $repo) {
                    starHistory {
                        nodes {
                            date
                            starNumber
                        }
                    }
                }
            }
            """

            try:
                response = requests.post(
                    url, json={"query": query, "variables": {"owner": owner, "repo": repo}}, timeout=10
                )

                if response.status_code == 200:
                    data = response.json()
                    history = data.get("data", {}).get("repository", {}).get("starHistory", {}).get("nodes", [])
                    return history
            except Exception as e:
                logging.warning(f"获取star历史失败: {str(e)}")

            # 获取失败时返回模拟数据
            return [
                {"date": "2023-01-01", "starNumber": 0},
                {"date": datetime.datetime.now().strftime("%Y-%m-%d"), "starNumber": 0},
            ]
        except Exception as e:
            logging.warning(f"获取star历史失败: {str(e)}")
            return []

    def _create_readme_screenshot(self, readme_content: str, repo_path: str) -> Optional[str]:
        """
        创建README的可视化截图

        Args:
            readme_content: README的内容
            repo_path: 仓库路径

        Returns:
            Optional[str]: 截图文件路径
        """
        try:
            # 提取README的前500个字符作为截图内容
            preview_text = readme_content[:500]

            # 创建图像
            width, height = 800, 600
            image = Image.new("RGB", (width, height), color=(255, 255, 255))
            draw = ImageDraw.Draw(image)

            # 尝试加载字体，失败时使用默认字体
            try:
                title_font = ImageFont.truetype("Arial", 24)
                body_font = ImageFont.truetype("Arial", 14)
            except Exception:
                title_font = ImageFont.load_default()
                body_font = ImageFont.load_default()

            # 提取标题（假设是第一行或者# 开头的行）
            title_match = re.search(r"^#\s+(.+)$", readme_content, re.MULTILINE)
            if title_match:
                title = title_match.group(1)
            else:
                title = os.path.basename(repo_path)

            # 绘制标题
            draw.text((20, 20), title, fill=(0, 0, 0), font=title_font)

            # 绘制内容（简单换行处理）
            y_position = 70
            words = preview_text.split()
            line = ""
            for word in words:
                test_line = line + word + " "
                # 使用更兼容的方法获取文本宽度
                try:
                    line_width = draw.textlength(test_line, font=body_font)
                except AttributeError:
                    # Pillow < 9.2.0 使用的是 textsize 而不是 textlength
                    line_width, _ = draw.textsize(test_line, font=body_font)

                if line_width < width - 40:
                    line = test_line
                else:
                    draw.text((20, y_position), line, fill=(0, 0, 0), font=body_font)
                    y_position += 20
                    line = word + " "

            # 绘制最后一行
            if line:
                draw.text((20, y_position), line, fill=(0, 0, 0), font=body_font)

            # 保存图像
            repo_name = os.path.basename(repo_path)
            image_path = os.path.join(self.output_config.get("images_dir", "output/images"), f"{repo_name}_readme.png")
            image.save(image_path)

            return image_path
        except Exception as e:
            logging.warning(f"创建README截图失败: {str(e)}")
            return None

    def _create_star_visualization(self, github_info: dict[str, Any], repo_path: str) -> Optional[str]:
        """
        创建stars数据的可视化图像

        Args:
            github_info: GitHub仓库信息
            repo_path: 仓库路径

        Returns:
            Optional[str]: 图像文件路径
        """
        try:
            # 使用stars历史数据创建可视化
            stars_history = github_info.get("stars_history", [])
            if not stars_history:
                return None

            # 创建简单的star统计图
            width, height = 600, 400
            image = Image.new("RGB", (width, height), color=(255, 255, 255))
            draw = ImageDraw.Draw(image)

            # 绘制标题
            repo_name = os.path.basename(repo_path)
            title = f"{repo_name} - Stars: {github_info.get('stars', 0)}"

            try:
                title_font = ImageFont.truetype("Arial", 20)
            except Exception:
                title_font = ImageFont.load_default()

            draw.text((20, 20), title, fill=(0, 0, 0), font=title_font)

            # 绘制坐标轴
            draw.line([(50, height - 50), (width - 50, height - 50)], fill=(0, 0, 0), width=2)  # X轴
            draw.line([(50, 50), (50, height - 50)], fill=(0, 0, 0), width=2)  # Y轴

            # 绘制数据点和线条
            if len(stars_history) > 1:
                # 计算最大值以确定缩放
                max_stars = max(item.get("starNumber", 0) for item in stars_history)
                if max_stars == 0:
                    max_stars = github_info.get("stars", 100)

                # 计算绘图区域
                plot_width = width - 100
                plot_height = height - 100

                # 绘制数据点
                points = []
                for i, data_point in enumerate(stars_history):
                    x = 50 + (i / (len(stars_history) - 1)) * plot_width
                    y = height - 50 - (data_point.get("starNumber", 0) / max_stars) * plot_height
                    points.append((x, y))
                    draw.ellipse([(x - 3, y - 3), (x + 3, y + 3)], fill=(255, 0, 0))

                # 连接数据点
                if len(points) > 1:
                    draw.line(points, fill=(255, 0, 0), width=2)

            # 保存图像
            image_path = os.path.join(self.output_config.get("images_dir", "output/images"), f"{repo_name}_stars.png")
            image.save(image_path)

            return image_path
        except Exception as e:
            logging.warning(f"创建star可视化失败: {str(e)}")
            return None

    def analyze_repository(self, repo_path: str) -> dict[str, Any]:
        """
        分析本地Git仓库并生成完整报告

        Args:
            repo_path: 本地仓库路径

        Returns:
            Dict[str, Any]: 分析结果
        """
        print(f"\n{'='*50}")
        print(f"    开始分析本地仓库: {repo_path}")
        print(f"{'='*50}\n")

        result = {
            "repo_path": repo_path,
            "timestamp": datetime.datetime.now().strftime("%Y%m%d_%H%M%S"),
            "success": False,
            "steps_completed": [],
            "repo_info": None,
            "analysis": None,
            "screenshots": [],
            "error": None,
        }

        try:
            # 步骤1: 验证本地仓库
            print("\n🔍 [步骤1] 验证本地仓库...")
            validate_success, repo_path, validate_error = self.clone_repository(repo_path)

            if not validate_success:
                print(f"❌ 验证失败: {validate_error}")
                result["error"] = f"验证本地仓库失败: {validate_error}"
                return result

            print(f"✅ 验证成功，使用本地仓库: {repo_path}")
            result["steps_completed"].append("validate")
            result["repo_path"] = repo_path

            # 步骤2: 收集仓库内容
            print("\n🔍 [步骤2] 收集仓库内容...")
            repo_content = self.collect_repo_content(repo_path)

            # 检查是否成功收集内容
            if not repo_content.get("files") and not repo_content.get("readme"):
                print("❌ 收集仓库内容失败")
                result["error"] = "未能收集到有效的仓库内容"
                return result

            print(f"✅ 成功收集仓库内容，包含 {len(repo_content.get('files', {}))} 个文件")
            result["steps_completed"].append("collect")
            result["repo_content"] = repo_content

            # 添加截图路径
            for screenshot in repo_content.get("screenshots", []):
                result["screenshots"].append(screenshot)

            # 步骤3: 使用AI分析仓库 (如果有可用的模型)
            print("\n🔍 [步骤3] 使用AI分析仓库...")

            # 检查是否有可用的AI模型
            if self.model is None or self.agent is None:
                print("⚠️ 跳过AI分析步骤 - 无有效的API密钥或模型")
                result["analysis"] = "由于API密钥问题，跳过AI分析步骤"
            else:
                prompt = REPO_INFO_COLLECTOR_PROMPT.format(repo=json.dumps(repo_content, ensure_ascii=False))
                analysis = self.process_message(prompt)

                if not analysis or "错误" in analysis or "API请求失败" in analysis:
                    print(f"⚠️ AI分析受限: {analysis}")
                    result["analysis"] = f"AI分析受限: {analysis}"
                else:
                    print("✅ AI分析完成")
                    result["steps_completed"].append("analyze")
                    result["analysis"] = analysis

            # 步骤4: 保存分析结果
            print("\n🔍 [步骤4] 保存分析结果...")

            # 获取仓库名称
            repo_name = os.path.basename(repo_path)

            # 保存JSON输出
            if self.output_config.get("json_output", True):
                output_file = os.path.join(self.output_dir, f"{repo_name}_analysis.json")
                with open(output_file, "w", encoding="utf-8") as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)
                print(f"✅ 分析结果已保存到: {output_file}")

            # 保存Markdown输出
            if self.output_config.get("md_output", True):
                output_file = os.path.join(self.output_dir, f"{repo_name}_analysis.md")
                try:
                    # 提取分析结果中的JSON部分（如果有）
                    json_pattern = r"```json\s*([\s\S]*?)```"
                    json_matches = re.findall(json_pattern, str(result["analysis"]))

                    with open(output_file, "w", encoding="utf-8") as f:
                        f.write(f"# {repo_name} 仓库分析\n\n")
                        f.write(f"仓库路径: {repo_path}\n\n")

                        # 添加GitHub信息
                        if repo_content.get("github_info"):
                            github_info = repo_content["github_info"]
                            f.write("## GitHub统计\n\n")
                            f.write(f"- Stars: {github_info.get('stars', 0)}\n")
                            f.write(f"- Forks: {github_info.get('forks', 0)}\n")
                            f.write(f"- Open Issues: {github_info.get('open_issues', 0)}\n")
                            f.write(f"- 最近更新: {github_info.get('updated_at', '')}\n\n")

                        # 添加截图
                        if result["screenshots"]:
                            f.write("## 可视化\n\n")
                            for screenshot in result["screenshots"]:
                                if "path" in screenshot:
                                    rel_path = os.path.relpath(screenshot["path"], os.getcwd())
                                    f.write(f"![{screenshot.get('type', '截图')}]({rel_path})\n\n")

                        # 添加README摘要(如果没有AI分析结果)
                        if self.model is None or "API" in str(result["analysis"]):
                            f.write("## README摘要\n\n")
                            if repo_content.get("readme"):
                                readme_lines = repo_content["readme"].split("\n")
                                # 只包含前20行README内容
                                readme_summary = "\n".join(readme_lines[: min(20, len(readme_lines))])
                                f.write(f"{readme_summary}\n\n")
                                if len(readme_lines) > 20:
                                    f.write("*README内容较长，仅显示部分内容...*\n\n")
                            else:
                                f.write("*未找到README文件*\n\n")

                            # 添加文件结构信息
                            f.write("## 文件结构\n\n")
                            for dir_name in repo_content.get("directories", [])[:10]:
                                f.write(f"- {dir_name}\n")
                            if len(repo_content.get("directories", [])) > 10:
                                f.write("*目录过多，仅显示部分内容...*\n\n")

                        # 添加分析内容(如果有AI分析结果)
                        if self.model is not None and "API" not in str(result["analysis"]):
                            f.write("## 详细分析\n\n")
                            if json_matches:
                                # 使用JSON格式的分析结果
                                for json_str in json_matches:
                                    try:
                                        analysis_data = json.loads(json_str)
                                        if isinstance(analysis_data, list):
                                            for item in analysis_data:
                                                if "分镜名" in item and "分镜内容" in item:
                                                    f.write(f"### {item['分镜名']}\n\n")
                                                    f.write(f"{item['分镜内容']}\n\n")

                                                    if "核心代码" in item and item["核心代码"]:
                                                        f.write("```\n")
                                                        f.write(f"{item['核心代码']}\n")
                                                        f.write("```\n\n")
                                    except json.JSONDecodeError:
                                        # 如果JSON解析失败，使用原始文本
                                        f.write(str(result["analysis"]))
                            else:
                                # 直接使用原始分析文本
                                f.write(str(result["analysis"]))

                    print(f"✅ Markdown分析报告已保存到: {output_file}")
                except Exception as e:
                    print(f"❌ 保存Markdown报告失败: {str(e)}")

            result["success"] = True

            # 总结
            print(f"\n{'='*50}")
            print("    分析总结")
            print(f"{'='*50}")
            print(f"📂 本地仓库路径: {repo_path}")
            print(f"📊 收集的文件数: {len(repo_content.get('files', {}))}")
            print(f"📸 生成的截图数: {len(result['screenshots'])}")
            print(f"✅ 状态: {'成功' if result['success'] else '失败'}")

            if not result["success"] and "error" in result:
                print(f"❌ 错误: {result['error']}")

            print(f"\n{'='*50}")
            print("    分析完成")
            print(f"{'='*50}")

            return result

        except Exception as e:
            logger.error(f"分析过程出错: {str(e)}")
            result["error"] = f"分析过程出错: {str(e)}"
            result["success"] = False
            return result


def main():
    """主函数，运行Git仓库分析"""
    # 打印欢迎信息
    print("\n" + "=" * 60)
    print("    Git仓库信息收集与分析工具（本地版）")
    print("=" * 60)

    # 检查git命令是否可用
    try:
        subprocess.run(["git", "--version"], check=True, capture_output=True)
    except Exception:
        print("\n❌ 错误: 未检测到Git命令。请确保Git已安装并添加到系统路径中。")
        sys.exit(1)

    # 检查API密钥（只检查一次）
    if not os.environ.get("OPENAI_API_KEY"):
        print("\n⚠️ 未找到OpenAI API密钥，请输入你的API密钥：")
        api_key = input("OPENAI_API_KEY: ").strip()

        if api_key:
            os.environ["OPENAI_API_KEY"] = api_key
            print("✅ API密钥已设置")
        else:
            print("\n⚠️ 未提供API密钥，将只执行仓库内容收集，不进行AI分析")

    # 检查命令行参数
    if len(sys.argv) > 1:
        repo_path = sys.argv[1]
    else:
        # 提示输入
        repo_path = input("\n请输入本地Git仓库路径: ")

    # 验证路径格式
    if not os.path.exists(repo_path):
        print("\n❌ 错误: 提供的路径不存在")
        sys.exit(1)

    # 验证是否为Git仓库
    git_dir = os.path.join(repo_path, ".git")
    if not os.path.exists(git_dir):
        print("\n❌ 错误: 提供的路径不是一个有效的Git仓库")
        print("请提供包含.git目录的仓库路径")
        sys.exit(1)

    # 初始化Git信息收集代理
    try:
        # 创建代理实例
        agent = GitInfoCollectorAgent()

        # 分析仓库
        result = agent.analyze_repository(repo_path)

        # 显示输出位置
        if result["success"]:
            repo_name = os.path.basename(repo_path)
            print("\n✅ 分析结果已保存到以下文件:")
            print(f"   - JSON: output/{repo_name}_analysis.json")
            print(f"   - Markdown: output/{repo_name}_analysis.md")

            # 显示截图路径
            if result["screenshots"]:
                print("\n📸 生成的截图:")
                for screenshot in result["screenshots"]:
                    if "path" in screenshot:
                        print(f"   - {screenshot['type']}: {screenshot['path']}")

    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"\n❌ 发生错误: {str(e)}")
        logger.exception("分析过程中发生异常")


if __name__ == "__main__":
    main()
