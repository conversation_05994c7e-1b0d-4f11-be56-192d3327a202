#!/usr/bin/env python3
"""
增强版自主性GitHub项目分析Agent
集成大模型进行智能分析，专注于提炼项目核心精华
"""

import os
import yaml
import json
import requests
import re
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging
from datetime import datetime
from openai import OpenAI

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedAutonomousGitHubAgent:
    """增强版自主性GitHub项目分析Agent"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        self.config = self._load_config(config_path)
        self.github_config = self.config.get('material', {}).get('sources', {}).get('github', {})
        self.model_config = self.config.get('model', {})
        self.output_dir = Path("output")
        self.output_dir.mkdir(exist_ok=True)
        
        # 初始化LLM客户端
        self.client = self._init_llm_client()
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            return {}
    
    def _init_llm_client(self) -> Optional[OpenAI]:
        """初始化LLM客户端"""
        try:
            api_config = self.model_config.get('api', {})
            return OpenAI(
                api_key=api_config.get('openai_compatibility_api_key'),
                base_url=api_config.get('openai_compatibility_api_base_url')
            )
        except Exception as e:
            logger.error(f"LLM客户端初始化失败: {e}")
            return None
    
    def _parse_github_url(self, url: str) -> Dict[str, str]:
        """解析GitHub URL"""
        pattern = r'https://github\.com/([^/]+)/([^/]+)'
        match = re.match(pattern, url.rstrip('/'))
        if match:
            return {"owner": match.group(1), "repo": match.group(2)}
        return {}
    
    def _get_repo_info(self, owner: str, repo: str) -> Dict:
        """获取仓库基本信息"""
        try:
            api_url = f"https://api.github.com/repos/{owner}/{repo}"
            headers = {'Accept': 'application/vnd.github.v3+json'}
            response = requests.get(api_url, headers=headers, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"API请求失败: {response.status_code}")
                return {}
        except Exception as e:
            logger.error(f"获取仓库信息失败: {e}")
            return {}
    
    def _get_readme_content(self, owner: str, repo: str, default_branch: str = "main") -> str:
        """获取README内容"""
        readme_urls = [
            f"https://raw.githubusercontent.com/{owner}/{repo}/{default_branch}/README.md",
            f"https://raw.githubusercontent.com/{owner}/{repo}/master/README.md"
        ]
        
        for url in readme_urls:
            try:
                response = requests.get(url, timeout=30)
                if response.status_code == 200:
                    logger.info(f"成功获取README: {url}")
                    return response.text
            except Exception as e:
                logger.debug(f"README获取失败 {url}: {e}")
        
        return ""
    
    def _extract_media_from_readme(self, readme_content: str, owner: str, repo: str, branch: str = "main") -> List[Dict]:
        """从README中提取多媒体资源"""
        media_list = []
        
        # 提取图片和视频链接
        patterns = {
            'image': r'!\[([^\]]*)\]\(([^)]+\.(?:png|jpg|jpeg|gif|svg|webp))\)',
            'video': r'!\[([^\]]*)\]\(([^)]+\.(?:mp4|mov|avi|webm))\)',
            'link_media': r'\[([^\]]*)\]\(([^)]+\.(?:png|jpg|jpeg|gif|svg|webp|mp4|mov|avi|webm))\)'
        }
        
        for media_type, pattern in patterns.items():
            matches = re.findall(pattern, readme_content, re.IGNORECASE)
            for alt_text, url in matches:
                # 处理相对路径
                if not url.startswith(('http://', 'https://')):
                    if url.startswith('./'):
                        url = f"https://raw.githubusercontent.com/{owner}/{repo}/{branch}/{url[2:]}"
                    elif url.startswith('/'):
                        url = f"https://raw.githubusercontent.com/{owner}/{repo}/{branch}{url}"
                    else:
                        url = f"https://raw.githubusercontent.com/{owner}/{repo}/{branch}/{url}"
                
                # 判断重要性
                importance = self._assess_media_importance(alt_text, url)
                if importance > 0:
                    media_list.append({
                        'type': 'video' if any(ext in url.lower() for ext in ['.mp4', '.mov', '.avi', '.webm']) else 'image',
                        'alt_text': alt_text,
                        'url': url,
                        'importance': importance
                    })
        
        # 按重要性排序
        media_list.sort(key=lambda x: x['importance'], reverse=True)
        return media_list[:8]  # 最多保留8个最重要的媒体
    
    def _assess_media_importance(self, alt_text: str, url: str) -> int:
        """评估媒体重要性 (1-5分，5最重要)"""
        text = f"{alt_text} {url}".lower()
        
        # 跳过徽章、图标等
        skip_keywords = ['badge', 'logo', 'icon', 'license', 'build', 'coverage', 'version', 'star', 'fork']
        if any(keyword in text for keyword in skip_keywords):
            return 0
        
        # 高重要性关键词
        high_priority = ['demo', 'screenshot', 'architecture', 'workflow', 'example', 'preview', 'showcase', 'factor']
        if any(keyword in text for keyword in high_priority):
            return 5
        
        # 中等重要性
        medium_priority = ['feature', 'usage', 'install', 'config', 'interface', 'ui']
        if any(keyword in text for keyword in medium_priority):
            return 3
        
        return 2  # 默认重要性
    
    def _analyze_core_files(self, owner: str, repo: str, branch: str = "main") -> Dict:
        """分析核心文件"""
        core_analysis = {
            'package_info': {},
            'tech_stack': [],
            'dependencies': []
        }
        
        # 分析package.json
        try:
            package_url = f"https://raw.githubusercontent.com/{owner}/{repo}/{branch}/package.json"
            response = requests.get(package_url, timeout=15)
            if response.status_code == 200:
                package_data = response.json()
                core_analysis['package_info'] = {
                    'name': package_data.get('name', ''),
                    'description': package_data.get('description', ''),
                    'version': package_data.get('version', ''),
                    'main': package_data.get('main', ''),
                    'dependencies': list(package_data.get('dependencies', {}).keys())[:10],
                    'scripts': package_data.get('scripts', {})
                }
                core_analysis['tech_stack'].extend(['Node.js', 'JavaScript/TypeScript'])
                core_analysis['dependencies'] = list(package_data.get('dependencies', {}).keys())[:10]
        except:
            pass
        
        # 分析requirements.txt
        try:
            req_url = f"https://raw.githubusercontent.com/{owner}/{repo}/{branch}/requirements.txt"
            response = requests.get(req_url, timeout=15)
            if response.status_code == 200:
                deps = [line.split('==')[0].split('>=')[0].strip() 
                       for line in response.text.split('\n') 
                       if line.strip() and not line.startswith('#')]
                core_analysis['dependencies'] = deps[:10]
                core_analysis['tech_stack'].append('Python')
        except:
            pass
        
        return core_analysis
    
    def _llm_analyze_project_essence(self, repo_info: Dict, readme_content: str, 
                                   core_analysis: Dict, purpose: str) -> Dict:
        """使用LLM分析项目核心精华"""
        if not self.client:
            return self._fallback_analysis(repo_info, readme_content, core_analysis)
        
        # 构建分析prompt
        analysis_prompt = f"""
作为一个技术项目分析专家，请分析以下GitHub项目的核心价值和精华：

项目名称: {repo_info.get('name', 'N/A')}
项目描述: {repo_info.get('description', 'N/A')}
主要语言: {repo_info.get('language', 'N/A')}
Star数量: {repo_info.get('stargazers_count', 0):,}
技术栈: {', '.join(core_analysis.get('tech_stack', []))}

目标受众和分析目的:
{purpose}

README内容前2000字符:
{readme_content[:2000]}

请用中文输出以下JSON格式的分析结果：
{{
    "core_value": "项目的核心价值和解决的问题（50-80字）",
    "key_innovations": ["创新点1", "创新点2", "创新点3"],
    "technical_highlights": ["技术亮点1", "技术亮点2", "技术亮点3"],
    "use_cases": ["应用场景1", "应用场景2", "应用场景3"],
    "why_attractive": "为什么技术爱好者应该关注这个项目（100-150字）",
    "quick_start_summary": "快速上手要点（50-80字）"
}}

请确保分析深入且具有吸引力，突出项目的独特价值和实用性。
"""
        
        try:
            response = self.client.chat.completions.create(
                model=self.model_config.get('type', 'gpt-3.5-turbo'),
                messages=[
                    {"role": "system", "content": "你是一个专业的技术项目分析师，擅长提炼项目核心价值并用吸引人的方式展示给技术爱好者。"},
                    {"role": "user", "content": analysis_prompt}
                ],
                temperature=self.model_config.get('temperature', 0.7),
                max_tokens=self.model_config.get('max_tokens', 4096)
            )
            
            result = response.choices[0].message.content
            logger.info("✅ LLM分析完成")
            
            # 解析JSON结果
            try:
                return json.loads(result)
            except json.JSONDecodeError:
                logger.warning("LLM返回的不是有效JSON，尝试提取内容")
                # 尝试提取JSON部分
                import re
                json_match = re.search(r'\{.*\}', result, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
                else:
                    return self._fallback_analysis(repo_info, readme_content, core_analysis)
            
        except Exception as e:
            logger.error(f"LLM分析失败: {e}")
            return self._fallback_analysis(repo_info, readme_content, core_analysis)
    
    def _fallback_analysis(self, repo_info: Dict, readme_content: str, core_analysis: Dict) -> Dict:
        """回退分析方法"""
        return {
            "core_value": f"{repo_info.get('name', '项目')}提供了创新的解决方案，帮助开发者构建更高效的应用",
            "key_innovations": ["现代化架构设计", "优秀的开发体验", "高度可扩展性"],
            "technical_highlights": ["先进的技术栈", "完善的文档", "活跃的社区"],
            "use_cases": ["企业级应用开发", "快速原型构建", "技术学习和研究"],
            "why_attractive": "该项目结合了最新的技术趋势和最佳实践，为开发者提供了强大而易用的工具集，值得技术爱好者深入了解和应用。",
            "quick_start_summary": "按照官方文档的安装指南，几分钟即可完成环境搭建并开始使用核心功能。"
        }
    
    def _generate_enhanced_report(self, repo_info: Dict, readme_content: str,
                                media_list: List[Dict], core_analysis: Dict,
                                llm_analysis: Dict, purpose: str) -> str:
        """生成增强版分析报告"""
        
        project_name = repo_info.get('name', '未知项目')
        stars = repo_info.get('stargazers_count', 0)
        language = repo_info.get('language', '未知')
        
        # 生成markdown报告
        report = f"""# {project_name} - 核心价值深度分析

> **分析目标**: {purpose}

## 🌟 项目概览

**{project_name}** 是一个基于{language}的开源项目，在GitHub上获得了 **{stars:,}** 个星标的认可。

### 💡 核心价值
{llm_analysis.get('core_value', '创新的技术解决方案')}

### 🎯 为什么值得关注
{llm_analysis.get('why_attractive', '提供了优秀的开发体验和强大的功能')}

## 🚀 核心创新点

{self._format_list_items(llm_analysis.get('key_innovations', []), '🔥')}

## 💻 技术亮点

{self._format_list_items(llm_analysis.get('technical_highlights', []), '⚡')}

## 📸 核心展示素材

{self._format_media_showcase_enhanced(media_list)}

## 🎯 应用场景

{self._format_list_items(llm_analysis.get('use_cases', []), '🌐')}

## ⚡ 快速上手

{llm_analysis.get('quick_start_summary', '参考官方文档快速开始')}

{self._format_tech_details(core_analysis)}

## 📊 项目数据

| 指标 | 数值 |
|------|------|
| ⭐ GitHub Stars | {stars:,} |
| 💻 主要语言 | {language} |
| 🔧 技术栈 | {', '.join(core_analysis.get('tech_stack', ['现代化技术']))} |
| 📦 版本 | {core_analysis.get('package_info', {}).get('version', 'N/A')} |
| 🔗 仓库地址 | [{repo_info.get('html_url', '')}]({repo_info.get('html_url', '')}) |

## 🎬 视频制作建议

**推荐时长**: 4分钟
**内容重点**:
1. 开场展示项目解决的核心问题 (30秒)
2. 核心创新点和技术亮点演示 (2分钟)
3. 实际应用场景和效果展示 (1分钟)
4. 快速上手和资源链接 (30秒)

---
*分析生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
*分析引擎: Enhanced Autonomous Agent with LLM*
"""
        
        return report
    
    def _format_list_items(self, items: List[str], emoji: str) -> str:
        """格式化列表项"""
        if not items:
            return f"{emoji} 暂无数据"
        
        formatted = ""
        for item in items:
            formatted += f"{emoji} **{item}**\n"
        return formatted
    
    def _format_media_showcase_enhanced(self, media_list: List[Dict]) -> str:
        """格式化增强版媒体展示"""
        if not media_list:
            return "*📷 该项目暂无重要的多媒体展示素材*"
        
        showcase = ""
        for i, media in enumerate(media_list[:6], 1):
            if media['type'] == 'video':
                showcase += f"### 🎥 视频素材 {i}: {media['alt_text'] or '演示视频'}\n"
                showcase += f"![{media['alt_text']}]({media['url']})\n"
                showcase += f"*重要性: {media['importance']}/5 | 建议在视频中重点展示*\n\n"
            else:
                showcase += f"### 📷 图片素材 {i}: {media['alt_text'] or '功能截图'}\n"
                showcase += f"![{media['alt_text']}]({media['url']})\n"
                showcase += f"*重要性: {media['importance']}/5*\n\n"
        
        return showcase
    
    def _format_tech_details(self, core_analysis: Dict) -> str:
        """格式化技术细节"""
        details = "\n## 🔧 技术实现细节\n\n"
        
        package_info = core_analysis.get('package_info', {})
        if package_info:
            details += "### 📦 项目配置\n"
            if package_info.get('name'):
                details += f"- **包名**: {package_info['name']}\n"
            if package_info.get('version'):
                details += f"- **版本**: {package_info['version']}\n"
            if package_info.get('main'):
                details += f"- **入口文件**: {package_info['main']}\n"
        
        dependencies = core_analysis.get('dependencies', [])
        if dependencies:
            details += f"\n### 🔗 核心依赖\n"
            for dep in dependencies[:8]:
                details += f"- `{dep}`\n"
        
        return details
    
    def analyze_project(self) -> str:
        """分析GitHub项目并生成增强版报告"""
        # 检查配置
        if not self.github_config.get('enabled'):
            logger.error("GitHub分析未启用")
            return ""
        
        url = self.github_config.get('url')
        purpose = self.github_config.get('purpose', '项目分析')
        
        if not url:
            logger.error("未配置GitHub URL")
            return ""
        
        # 解析URL
        repo_info_basic = self._parse_github_url(url)
        if not repo_info_basic:
            logger.error("无效的GitHub URL")
            return ""
        
        owner, repo = repo_info_basic['owner'], repo_info_basic['repo']
        logger.info(f"🔍 开始深度分析项目: {owner}/{repo}")
        
        # 获取仓库信息
        repo_info = self._get_repo_info(owner, repo)
        default_branch = repo_info.get('default_branch', 'main')
        
        # 获取README
        readme_content = self._get_readme_content(owner, repo, default_branch)
        
        # 提取多媒体素材
        media_list = self._extract_media_from_readme(readme_content, owner, repo, default_branch)
        logger.info(f"📸 发现 {len(media_list)} 个重要媒体资源")
        
        # 分析核心文件
        core_analysis = self._analyze_core_files(owner, repo, default_branch)
        
        # LLM智能分析
        logger.info("🤖 启动LLM智能分析...")
        llm_analysis = self._llm_analyze_project_essence(repo_info, readme_content, core_analysis, purpose)
        
        # 生成增强版报告
        report = self._generate_enhanced_report(repo_info, readme_content, media_list, core_analysis, llm_analysis, purpose)
        
        # 保存报告
        output_file = self.output_dir / f"{repo}_enhanced_analysis.md"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"📄 增强版分析报告已保存到: {output_file}")
        return str(output_file)

def main():
    """主函数"""
    agent = EnhancedAutonomousGitHubAgent()
    result = agent.analyze_project()
    if result:
        print(f"✅ 项目深度分析完成，报告保存至: {result}")
    else:
        print("❌ 项目分析失败")

if __name__ == "__main__":
    main() 