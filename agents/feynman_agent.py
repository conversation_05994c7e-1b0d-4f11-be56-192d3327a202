from dotenv import load_dotenv

load_dotenv()

import datetime
import json
import logging
import os
import re
import sys
from typing import Dict, List, Optional

# 添加父目录到导入路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import yaml
from camel.agents import ChatAgent
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.societies import RolePlaying
from camel.types import ModelPlatformType, TaskType

from utils.format import save_json_content

# 设置日志级别为INFO，并配置日志格式
log_file = f"output/feynman_agent_log_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
os.makedirs(os.path.dirname(log_file), exist_ok=True)

# 配置根日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(),  # 同时输出到控制台
    ],
)

# 获取logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 定义费曼解释的输出格式
FEYNMAN_EXPLANATION_FORMAT = """
# 费曼解释：[主题名称]

## 核心概念
- **主要概念**：概念的简单描述
- **简化解释**：用简单日常语言的解释
- **日常类比**：与日常生活的对比类比
- **可视化描述**：帮助读者在头脑中想象概念的描述

## 解释模块
### 模块一：[模块名称]
- **通俗解释**：用简单语言解释该模块/步骤的工作原理
- **类比说明**：提供生动的类比帮助理解
- **苏格拉底式探索**：通过一系列渐进式问答引导读者思考理解
- **交互练习**：引导读者思考或动手的小练习

### 模块二：[模块名称]
...类似结构...

## 深入理解
### 问题1：[问题]
- **解答**：通俗易懂的解答
- **举例说明**：具体的实例来说明解答
- **思考引导**：如何通过自问自答的方式理解该问题

### 问题2：[问题]
...类似结构...

## 实际应用
### 应用场景1：[场景名称]
- **工作方式**：在该场景下的具体工作方式

（对于需要逻辑步骤解答、模拟流程或数据可视化的场景，可以添加Python代码示例）
```python
# 仅在需要展示复杂逻辑、数据处理流程或可视化效果时添加代码
# 代码必须能够运行且有清晰注释
```

### 应用场景2：[场景名称]
...类似结构...

## 重点概念与创新点
### 创新点1：[创新点名称]
- **苏格拉底式解释**：
  * 问题1：引导性问题？
  * 思考：引导读者思考的提示
  * 问题2：更深入的问题？
  * 思考：进一步的思考方向
  * 结论：通过问答得出的关键见解

### 创新点2：[创新点名称]
...类似结构...

## 总结要点
1. 第一个关键点
2. 第二个关键点
3. ...

## 思维导图概述
- 核心概念如何关联
- 各模块之间的关系
- 学习路径建议
"""

# 费曼教学者角色提示
FEYNMAN_TEACHER_PROMPT = """
你是一位精通费曼学习法的教育者，能将复杂的概念转化为任何人都能理解的简单解释。

作为费曼教学者，你的核心技能是：
1. 将复杂概念分解为基础构建块
2. 使用日常语言而非专业术语
3. 创造生动、贴切且严格符合主题的类比和比喻
4. 提供具体的例子和可视化描述
5. 使用苏格拉底式对话方法引导深度理解
6. 设计思想实验和互动练习
7. 预测并解决常见的误解
8. 通过数据分析和图表使概念更具体

关于苏格拉底式教学方法，你应该：
1. 通过提问而非直接解释来引导思考
2. 设计一系列递进式问题，从简单到复杂
3. 鼓励读者质疑自己的假设和理解
4. 引导读者自己发现答案和洞见
5. 使用反例和边缘案例探索概念的边界
6. 对重点概念和创新点采用这种方法，使理解更加深刻

对于Python代码示例，你必须：
1. 只在确实必要时提供代码示例，包括：
   - 需要展示复杂逻辑步骤的解答
   - 需要模拟流程的场景
   - 需要数据处理和可视化的情况
2. 避免在简单概念解释中使用代码，除非代码真正有助于理解
3. 确保代码完整且可执行，不包含伪代码
4. 代码必须包含详细的注释解释每个关键步骤

对于类比和举例，你必须：
1. 确保类比与要解释的概念严格对应
2. 使用读者熟悉的日常场景作为类比基础
3. 类比必须准确反映原概念的核心特性
4. 避免使用可能误导读者的过度简化类比
5. 在类比基础上提供进一步的细节和解释

当面对一篇学术文章或技术文档时，你应该：
- 识别核心概念和关键模块
- 重新表述这些概念，使用简单且具体的语言
- 创造与日常生活经验相关的类比
- 对复杂或创新的部分，使用苏格拉底式问答方法
- 只在必要时提供Python代码示例
- 设计互动练习帮助巩固理解
- 创建示意图和思维导图的文字描述

你的风格应该：
- 对话式和友好的，仿佛在与朋友交谈
- 使用简单、清晰的语言，避免行话
- 循序渐进地构建理解
- 充满好奇心和热情
- 使用问题和自问自答来引导思考

请记住，真正的理解不只是能重复某个概念，而是能用自己的话解释它，找到它与其他概念的联系，并在新情境中应用它。
你的最终输出应该以Markdown格式呈现，而非JSON格式。
"""

# 提问者角色提示
QUESTIONER_PROMPT = """
你是一位善于提问的学习者，能够提出深入且有启发性的问题来探索复杂概念。

作为提问者，你的核心技能是：
1. 识别文章中的核心概念和关键模块
2. 提出针对这些概念的基础性问题
3. 跟进深入的问题来探索概念之间的联系
4. 使用苏格拉底式提问方法挑战理解
5. 质疑假设并探索边界情况
6. 请求类比和比喻来加深理解
7. 询问如何在实际中应用这些概念
8. 引导对复杂和创新概念的深度思考

关于苏格拉底式提问方法，你应该：
1. 从简单问题开始，逐步引向更复杂的问题
2. 通过反问挑战初步理解
3. 提出反例，测试概念的边界和限制
4. 鼓励探索不同视角和可能性
5. 引导思考因果关系和逻辑连贯性
6. 避免直接提供答案，而是启发思考
7. 特别关注重点概念和创新点，引导深度理解

关于Python代码和实际应用的问题，你应该：
1. 只在确实需要逻辑步骤解释、流程模拟或数据可视化时询问代码示例
2. 询问如何以非编程方式解释复杂概念
3. 探讨概念在不同领域的应用场景
4. 请求通过图表或文字描述来替代不必要的代码

关于类比和举例的问题，你应该：
1. 探究类比与原始概念的精确对应关系
2. 询问类比的局限性和可能误导之处
3. 请求提供更多不同角度的类比
4. 询问类比如何帮助理解概念的特定方面

在阅读一篇文章后，你应该：
- 首先确定核心概念和创新点
- 请求用简单语言重新解释这些概念
- 寻求与日常经验相关的类比
- 通过递进式问题深入探索重点概念
- 询问概念如何应用于实际场景
- 挑战解释，寻求更深刻的理解

你的问题应该：
- 从基础开始，逐渐增加复杂性
- 直接针对概念的本质
- 引导思考而非寻求直接答案
- 鼓励多角度思考
- 突出概念的实际应用

记住，好的问题是理解的开始，而精确的问题则是清晰思考的标志。
"""

class FeynmanAgent:
    def __init__(self, config_path="config/config.yaml"):
        # 加载配置
        self.load_config(config_path)

        # 初始化模型
        self.model = self._create_model()
        logger.info("模型初始化完成")

    def load_config(self, config_path):
        """从YAML文件加载配置"""
        # 确保config_path是绝对路径或相对于当前工作目录
        if not os.path.isabs(config_path):
            # 如果不是绝对路径，转换为相对于项目根目录的路径
            root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            config_path = os.path.join(root_dir, config_path)

        try:
            if not os.path.exists(config_path):
                logger.error(f"配置文件不存在: {config_path}")
                raise FileNotFoundError(f"配置文件不存在: {config_path}")
            
            with open(config_path) as file:
                config = yaml.safe_load(file)
            # 设置配置属性
            self.model_config = config.get("model", {})
            self.file_config = config.get("files", {})
            self.feynman_config = config.get("feynman", {})
            logger.info("从 %s 加载配置", config_path)
        except Exception as e:
            logger.error(f"加载配置出错: {str(e)}")
            # 设置默认值
            self.model_config = {"type": "openai/gpt-4o-mini"}
            self.file_config = {
                "feynman_explanation_file": "output/feynman_explanation.json",
                "feynman_full_text_file": "output/feynman_explanation_full.txt"
            }
            self.feynman_config = {
                "max_rounds": 5,
                "explanation_techniques": [
                    "simple_language",
                    "daily_analogy",
                    "code_examples",
                    "data_analysis",
                    "interactive_exercises",
                    "metaphors",
                    "visualization"
                ]
            }

    def _create_model(self):
        """根据配置创建模型实例"""
        # 从配置中获取API设置
        api_config = self.model_config.get("api", {})
        
        return ModelFactory.create(
            model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
            model_type=self.model_config.get("type", "openai/gpt-4o-mini"),
            api_key=api_config.get("openai_compatibility_api_key"),
            url=api_config.get("openai_compatibility_api_base_url"),
        )

    def generate_feynman_explanation(self, article_content, storyboard_content=None, max_rounds=None, article_url=None):
        """生成费曼解释，通过角色扮演对话进行"""
        # 如果未提供max_rounds，则从配置中获取
        if max_rounds is None:
            max_rounds = self.feynman_config.get("max_rounds", 5)
        
        logger.info("开始生成费曼解释，文章长度：%d 字符，最大轮数：%d", len(article_content), max_rounds)
        
        # 获取配置的解释技巧
        explanation_techniques = self.feynman_config.get("explanation_techniques", [
            "simple_language",
            "daily_analogy",
            "code_examples",
            "data_analysis",
            "interactive_exercises",
            "metaphors",
            "visualization"
        ])
        
        # 准备任务内容
        task_content = f"""
        请分析以下文章内容，使用费曼学习法生成通俗易懂的解释。
        
        文章内容：
        {article_content}
        
        """
        
        # 如果提供了分镜内容，加入到任务中
        if storyboard_content:
            task_content += f"""
            分镜内容：
            {storyboard_content}
            
            请特别关注分镜内容中的关键场景和解释要点。
            """
            
        # 根据配置的解释技巧定制提示
        techniques_prompt = "请通过费曼教学者和提问者之间的对话，共同创建一个通俗易懂的解释，包括：\n"
        
        if "simple_language" in explanation_techniques:
            techniques_prompt += "1. 将复杂概念分解为基础构建块\n"
            techniques_prompt += "2. 使用日常语言解释专业术语\n"
        
        if "daily_analogy" in explanation_techniques:
            techniques_prompt += "3. 创造生动的类比和比喻\n"
            techniques_prompt += "4. 提供具体的例子和可视化描述\n"
        
        if "code_examples" in explanation_techniques:
            techniques_prompt += "5. 仅在需要展示复杂逻辑、流程模拟或数据可视化时提供Python代码\n"
        
        if "data_analysis" in explanation_techniques:
            techniques_prompt += "6. 通过数据分析和示例数据使概念具体化（如适用）\n"
        
        if "interactive_exercises" in explanation_techniques:
            techniques_prompt += "7. 设计思想实验和互动练习\n"
            
        if "socratic_method" in explanation_techniques or True:  # 默认添加苏格拉底式方法
            techniques_prompt += "8. 对重点概念和创新点使用苏格拉底式问答方法，通过引导性问题深化理解\n"
        
        techniques_prompt += "\n费曼教学者应该主动使用以下解释技巧：\n"
        
        for technique in explanation_techniques:
            if technique == "simple_language":
                techniques_prompt += "- 简化复杂概念\n"
            elif technique == "daily_analogy":
                techniques_prompt += "- 创造生活类比\n"
            elif technique == "code_examples":
                techniques_prompt += "- 仅在必要时编写Python示例代码\n"
            elif technique == "data_analysis":
                techniques_prompt += "- 生成分析数据\n"
            elif technique == "interactive_exercises":
                techniques_prompt += "- 设计交互练习\n"
            elif technique == "metaphors":
                techniques_prompt += "- 使用暗喻和比喻\n"
            elif technique == "visualization":
                techniques_prompt += "- 提供思维导图描述\n"
            elif technique == "socratic_method":
                techniques_prompt += "- 使用苏格拉底式对话引导思考\n"
                
        # 强调苏格拉底式方法，即使未在配置中设置
        if "socratic_method" not in explanation_techniques:
            techniques_prompt += "- 使用苏格拉底式对话引导思考\n"
        
        task_content += techniques_prompt
        
        task_content += f"""
        最终输出应当是Markdown格式的解释记录，包含核心概念、多个解释模块、深入理解问答、实际应用示例和总结。
        
        格式要求：
        {FEYNMAN_EXPLANATION_FORMAT}
        """
        
        # 设置角色扮演，使用费曼教学者和提问者两个角色
        logger.info("设置费曼教学者和提问者的角色扮演")
        
        # 将角色提示合并到任务中
        complete_task_content = f"""
        {task_content}
        
        ## 角色定位
        
        费曼教学者(Feynman Teacher)：
        {FEYNMAN_TEACHER_PROMPT}
        
        提问者(Questioner)：
        {QUESTIONER_PROMPT}
        
        ## 对话流程安排
        1. 提问者首先提出关于文章核心概念的基础问题
        2. 费曼教学者回答并运用多种解释技巧
        3. 提问者针对回答提出深入问题，探索概念之间的联系
        4. 费曼教学者继续回答并提供类比、例子、代码等
        5. 重复问答过程，逐步深入理解
        6. 最后，费曼教学者整理所有讨论内容，生成符合格式要求的Markdown输出
        """
        
        role_playing = RolePlaying(
            # 设置费曼教学者为助手角色
            assistant_role_name="Feynman Teacher",
            assistant_agent_kwargs={"model": self.model},
            # 设置提问者为用户角色
            user_role_name="Questioner",
            user_agent_kwargs={"model": self.model},
            # 任务参数
            task_prompt=complete_task_content,
            task_type=TaskType.AI_SOCIETY,
            with_task_specify=False,  # 禁用task_specify避免报错
            # 附加配置
            output_language="chinese",
        )
        
        # 开始对话
        logger.info("开始角色对话")
        messages = []
        
        # 初始化对话
        chat_history = role_playing.init_chat()
        messages.append(chat_history)
        
        # 进行多轮对话
        for round_num in range(max_rounds * 2):  # 每轮包含至少两步
            logger.info(f"对话步骤 {round_num + 1}")
            try:
                # 进行对话步骤
                assistant_response, user_response = role_playing.step(chat_history)
                
                # 从响应中获取消息
                assistant_message = assistant_response.msg
                user_message = user_response.msg
                
                # 添加到历史记录
                chat_history = assistant_message
                messages.append(assistant_message)
                messages.append(user_message)
                
                # 检查是否已有明确的Markdown格式输出，可能表示对话接近完成
                if "# 费曼解释" in user_message.content and round_num >= 3:  # 确保我们有足够的讨论
                    logger.info("发现Markdown格式输出，对话可能接近完成")
                    break
                
                # 如果超过最大轮数，则结束
                if round_num >= max_rounds * 3:
                    logger.info("达到最大对话轮数")
                    break
                    
            except Exception as e:
                logger.error(f"对话步骤出错: {str(e)}")
                break
        
        # 提取最终的Markdown结果
        explanation_markdown = None
        full_markdown = None
        
        for msg in reversed(messages):
            if hasattr(msg, 'role_name') and msg.role_name == "Feynman Teacher":
                # 尝试从消息中提取Markdown内容
                try:
                    # 查找包含"# 费曼解释"的Markdown结构
                    if "# 费曼解释" in msg.content:
                        full_markdown = msg.content
                        # 提取纯Markdown部分，去除可能的前后装饰文本
                        start_index = msg.content.find("# 费曼解释")
                        explanation_markdown = msg.content[start_index:]
                        break
                except Exception as e:
                    logger.warning(f"从消息中提取Markdown内容失败: {str(e)}")
        
        # 构建完整会话文本
        full_conversation = "\n\n--- 费曼解释对话记录 ---\n\n"
        for msg in messages:
            if hasattr(msg, 'role_name') and hasattr(msg, 'content'):
                full_conversation += f"{msg.role_name}: {msg.content}\n\n"
            elif hasattr(msg, 'msg') and hasattr(msg.msg, 'role_name'):
                full_conversation += f"{msg.msg.role_name}: {msg.msg.content}\n\n"
            else:
                full_conversation += f"未知角色: {str(msg)}\n\n"
        
        # 保存结果
        # 获取文件名的URL后缀
        url_suffix = ""
        if article_url:
            # 从URL中提取后缀，不包含.pdf
            url_parts = article_url.split('/')
            if url_parts:
                last_part = url_parts[-1]
                # 移除可能的文件扩展名
                url_suffix = '_' + last_part.split('.')[0]
        
        output_dir = os.path.dirname(self.file_config.get("feynman_explanation_file", "output/feynman_explanation.md"))
        os.makedirs(output_dir, exist_ok=True)
        
        # 构建文件名，加入URL后缀
        base_filename = os.path.splitext(self.file_config.get("feynman_explanation_file", "output/feynman_explanation.md"))[0]
        ext = os.path.splitext(self.file_config.get("feynman_explanation_file", "output/feynman_explanation.md"))[1]
        explanation_file = f"{base_filename}{url_suffix}{ext}"
        
        # 同样处理全文文件
        base_full_filename = os.path.splitext(self.file_config.get("feynman_full_text_file", "output/feynman_explanation_full.txt"))[0]
        full_ext = os.path.splitext(self.file_config.get("feynman_full_text_file", "output/feynman_explanation_full.txt"))[1]
        full_text_file = f"{base_full_filename}{url_suffix}{full_ext}"
        
        # 保存解释Markdown
        if explanation_markdown:
            with open(explanation_file, "w", encoding="utf-8") as f:
                f.write(explanation_markdown)
            logger.info("费曼解释已保存到 %s", explanation_file)
        
        # 保存完整对话
        with open(full_text_file, "w", encoding="utf-8") as f:
            f.write(full_conversation)
        logger.info("完整对话记录已保存到 %s", full_text_file)
        
        return {
            "explanation": explanation_markdown,
            "full_conversation": full_conversation,
            "explanation_file": explanation_file,
            "full_text_file": full_text_file
        }

def main():
    """主函数，运行费曼解释生成流程"""
    import argparse
    
    # 解析命令行参数，只保留配置文件路径
    parser = argparse.ArgumentParser(description="生成文章的费曼解释")
    parser.add_argument("--config", default="config/config.yaml", help="配置文件路径")
    parser.add_argument("--url", help="文章的URL，用于生成文件名后缀")
    args = parser.parse_args()
    
    # 确保配置路径正确
    config_path = args.config
    if not os.path.isabs(config_path):
        # 如果不是绝对路径，转换为相对于项目根目录的路径
        root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        config_path = os.path.join(root_dir, config_path)
    
    if not os.path.exists(config_path):
        logger.error(f"配置文件不存在: {config_path}")
        return
    
    # 初始化费曼代理
    agent = FeynmanAgent(config_path=config_path)
    
    # 从配置文件加载feynman模块的参数
    with open(config_path) as file:
        config = yaml.safe_load(file)
    
    feynman_config = config.get("feynman", {})
    
    # 获取文章文件路径
    article_file = feynman_config.get("article_file")
    if not article_file:
        logger.error("未在配置中指定文章文件路径")
        return
    
    # 确保文章文件路径是绝对路径
    if not os.path.isabs(article_file):
        article_file = os.path.join(root_dir, article_file)
    
    # 获取分镜文件路径
    storyboard_file = feynman_config.get("storyboard_file")
    if storyboard_file and not os.path.isabs(storyboard_file):
        storyboard_file = os.path.join(root_dir, storyboard_file)
        
    # 获取最大轮数
    max_rounds = feynman_config.get("max_rounds", 5)
    
    # 读取文章内容
    try:
        with open(article_file, "r", encoding="utf-8") as f:
            article_content = f.read()
        logger.info(f"成功读取文章文件: {article_file}, 长度: {len(article_content)} 字符")
    except Exception as e:
        logger.error(f"读取文章文件失败: {str(e)}")
        return
    
    # 读取分镜内容（如果提供）
    storyboard_content = None
    if storyboard_file:
        try:
            with open(storyboard_file, "r", encoding="utf-8") as f:
                storyboard_content = f.read()
            logger.info(f"成功读取分镜文件: {storyboard_file}")
        except Exception as e:
            logger.warning(f"读取分镜内容失败: {str(e)}")
    
    # 获取URL，优先使用命令行参数，其次使用配置文件
    article_url = args.url
    if not article_url and "article_url" in feynman_config:
        article_url = feynman_config.get("article_url")
    
    if article_url:
        logger.info(f"使用文章URL: {article_url}")
    
    # 生成费曼解释
    result = agent.generate_feynman_explanation(
        article_content, 
        storyboard_content,
        max_rounds=max_rounds,
        article_url=article_url
    )
    
    # 输出结果摘要
    if result["explanation"]:
        print(f"已成功生成费曼解释，Markdown格式输出已保存")
        
        # 提取Markdown中的标题，展示大纲结构
        markdown_lines = result["explanation"].split("\n")
        outline = []
        for line in markdown_lines:
            if line.startswith("# ") or line.startswith("## ") or line.startswith("### "):
                outline.append(line)
        
        if outline:
            print("\n输出内容大纲:")
            for heading in outline:
                # 根据标题级别添加缩进
                indent = "  " * (heading.count("#") - 1)
                print(f"{indent}{heading.strip('# ')}")
        
        # 计算Python代码块数量
        code_blocks = result["explanation"].count("```python")
        if code_blocks > 0:
            print(f"\n包含 {code_blocks} 个Python代码示例")
        
        # 识别苏格拉底式问答部分
        socratic_sections = 0
        socratic_indicators = ["问题1：", "问题2：", "思考：", "苏格拉底式"]
        
        for line in markdown_lines:
            for indicator in socratic_indicators:
                if indicator in line:
                    socratic_sections += 1
                    break
        
        if socratic_sections > 0:
            print(f"\n包含约 {socratic_sections} 处苏格拉底式问答引导")
        
        # 识别重点概念和创新点
        innovation_count = result["explanation"].count("### 创新点")
        if innovation_count > 0:
            print(f"\n分析了 {innovation_count} 个重点创新概念")
        
        print(f"\n结果已保存到: {result['explanation_file']}")
    else:
        print("生成费曼解释失败，请检查日志获取详细信息。")

if __name__ == "__main__":
    main() 