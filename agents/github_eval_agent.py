"""
GitHub 仓库分析和示例运行代理

提供以下功能：
1. 分析和总结 GitHub 仓库的主要功能和亮点
2. 提取和展示仓库中的示例代码
3. 运行选定的示例并录制过程
"""

import hashlib
import os
import subprocess
import time
import datetime
import json
from pathlib import Path
from typing import Any, Optional
import re
import shutil

# 将 notebook 相关的导入移到一个尝试块中
try:
    import nbformat
    from nbconvert.exporters import PythonExporter
    NOTEBOOK_CONVERSION_AVAILABLE = True
except ImportError:
    NOTEBOOK_CONVERSION_AVAILABLE = False

import yaml
from camel.agents import ChatAgent
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.types import ModelPlatformType, TaskType
from camel.societies import RolePlaying
from loguru import logger

from dotenv import load_dotenv

load_dotenv()

# 系统提示词
SYSTEM_PROMPT = """
你是一个专业的代码分析代理，负责分析 GitHub 仓库并帮助用户运行示例代码。

你的职责包括：
1. 分析仓库结构和功能
2. 提取主要功能和亮点
3. 识别和分析示例代码
4. 提供示例运行方法
5. 协助用户运行示例

请根据提供的仓库信息，提供详细的分析报告，包括：
- 项目概述
- 主要功能
- 项目亮点
- 可用示例
- 运行方法
"""

# 代码生成器提示词
CODE_GENERATOR_PROMPT = """
你是一位专业的代码生成器，擅长编写简洁、核心的测试脚本。你的职责是根据项目需求生成最小化的、易于调试的测试代码。

作为代码生成器，你的核心技能是：
1. 解析项目需求和功能规范
2. 生成最精简的可执行测试代码
3. 专注于直接验证功能而非复杂的框架结构
4. 确保代码清晰易读，便于错误定位
5. 设计可自动排查和修复的测试案例

在生成测试代码时，你必须：
1. 只使用必要的导入语句，避免冗余依赖
2. 编写结构简单、线性执行的代码，避免复杂嵌套
3. 使用清晰的变量命名和基本控制流程
4. 添加简洁的注释说明关键步骤
5. 使用明确的错误处理机制，提供详细错误信息
6. 专注于核心功能验证，避免过度设计

特别注意：
1. 确保代码直接运行核心功能，不要添加复杂的测试框架
2. 使用简单的Python标准库功能，尽量减少外部依赖
3. 分离测试准备、执行和验证步骤，便于问题定位
4. 输出明确的成功/失败状态和详细日志
5. 代码应易于在单个文件中执行，避免多文件依赖
6. 使用显式的功能调用而非隐式调用，便于调试

请生成最精简、最核心的测试代码，优先考虑可执行性和可调试性。
"""

# 测试执行者提示词
TEST_EXECUTOR_PROMPT = """
你是一位专业的测试执行者，专注于运行和评估测试案例的执行结果。你的职责是验证测试代码的正确性，并提供详细的反馈意见。

作为测试执行者，你的核心技能是：
1. 执行测试脚本并观察运行结果
2. 分析测试失败的原因并提供详细错误报告
3. 判断测试结果是否符合预期
4. 提出改进测试代码的建议
5. 验证测试覆盖率和质量

在执行测试时，你需要特别关注：
1. 语法错误和运行时异常
2. 逻辑错误和业务规则实现
3. 边界条件和特殊情况处理
4. 性能问题和资源使用
5. 与预期结果的一致性

当发现测试失败时，你必须提供：
1. 具体的错误消息和堆栈跟踪
2. 错误发生的上下文和条件
3. 可能的根本原因分析
4. 具体的修复建议
5. 改进代码的具体方法

你需要以客观、专业的态度评估测试结果，不做无根据的假设，并基于事实提供建设性的反馈。
"""

class GitHubEvalAgent:
    """GitHub 仓库分析和示例运行代理"""

    def __init__(self, repo_path_str: str = None, config_path: str = "config/config.yaml"):
        """
        初始化 GitHub 代理

        Args:
            repo_path: 本地仓库路径，如果为 None 则使用当前目录
            config_path: 配置文件路径
        """
        # 加载配置
        self.load_config(config_path)
        
        # 解析GitHub URL为项目名称
        self.project_name = self._extract_project_name_from_url(self.github_config.get("project_url", ""))
        
        # 设置仓库路径 - 改为双层项目名称路径
        if repo_path_str is None:
            repo_path_str = os.path.join("output", self.project_name, self.project_name)
        self.repo_path = Path(repo_path_str)
        
        # 设置缓存路径 - 放在output/项目名称/下
        self.cache_path = Path(os.path.join("output", self.project_name, "info_cache"))
        self.cache_path.mkdir(parents=True, exist_ok=True)
        
        # 设置默认大小限制参数
        if "max_docs" not in self.github_config:
            self.github_config["max_docs"] = 5
        if "max_doc_content_size" not in self.github_config:
            self.github_config["max_doc_content_size"] = 20000
        if "max_readme_size" not in self.github_config:
            self.github_config["max_readme_size"] = 50000
        if "max_example_size" not in self.github_config:
            self.github_config["max_example_size"] = 10000
        if "max_examples" not in self.github_config:
            self.github_config["max_examples"] = 10
        
        # 克隆GitHub仓库（如果不存在）
        self._ensure_repo_exists()

        # 初始化模型
        self.model = self._create_model()

        # 初始化 ChatAgent
        self.agent = self._create_agent()

        # 初始化 repo_info
        self.repo_info = self._collect_repo_info()

        logger.info(f"GitHub 代理已初始化，仓库路径: {self.repo_path}，项目名称: {self.project_name}")
    
    def _extract_project_name_from_url(self, url: str) -> str:
        """从GitHub URL中提取项目名称"""
        if not url:
            logger.warning("GitHub URL未设置，使用默认项目名称")
            return "unknown_project"
            
        # 提取项目名称 (例如 https://github.com/username/project_name -> project_name)
        match = re.search(r'github\.com/[^/]+/([^/]+)', url)
        if match:
            return match.group(1)
        else:
            logger.warning(f"无法从URL {url} 提取项目名称，使用默认项目名称")
            return "unknown_project"

    def load_config(self, config_path: str):
        """加载配置文件"""
        try:
            with open(config_path) as f:
                self.config = yaml.safe_load(f)
            # 设置配置属性
            self.model_config = self.config.get("model", {})
            self.github_config = self.config.get("github", {})
            logger.info(f"从 {config_path} 加载配置")
        except Exception as e:
            logger.error(f"加载配置出错: {str(e)}")
            # 设置默认值
            self.model_config = {"temperature": 0.1, "max_tokens": 4096}
            self.github_config = {}

    def _create_model(self):
        """创建模型实例"""
        # 从配置中获取API设置
        api_config = self.model_config.get("api", {})

        return ModelFactory.create(
            model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
            model_type=self.model_config.get("type", "openai/gpt-4o-mini"),
            api_key=api_config.get("openai_compatibility_api_key"),
            url=api_config.get("openai_compatibility_api_base_url"),
        )

    def _create_agent(self):
        """创建ChatAgent实例"""
        return ChatAgent(model=self.model, system_message=SYSTEM_PROMPT)

    def _read_json(self, file_name: str) -> dict[str, Any]:
        cache_file = self.cache_path / f"{file_name}.json"
        logger.info(f"从 {cache_file} 读数据")
        if cache_file.exists():
            # 如果缓存存在，直接返回缓存的结果
            with open(cache_file, encoding="utf-8") as f:
                return json.load(f)
        return None

    def _write_json(self, content: dict[str, Any], file_name: str):
        # 检查是否有缓存的结果
        cache_file = self.cache_path / f"{file_name}.json"
        logger.info(f"往 {cache_file} 写数据")

        cache_file.parent.mkdir(parents=True, exist_ok=True)
        with open(cache_file, "w", encoding="utf-8") as f:
            json.dump(content, f, ensure_ascii=False, indent=2)

    def _analyze_repo_base(self, prompt: str, cache_name: str) -> dict[str, Any]:
        """基础仓库分析函数

        Args:
            prompt: 分析提示词
            cache_name: 缓存文件名前缀

        Returns:
            Dict: 包含分析结果的字典
        """
        try:
            # 检查是否有缓存的结果
            cache_key = hashlib.md5(prompt.encode()).hexdigest()
            cache_file_name = f"{self.project_name}_{cache_name}_{cache_key}"

            result = self._read_json(cache_file_name)
            if result is not None:
                # 如果缓存存在，直接返回缓存的结果
                return result

            # 发送消息给代理
            user_message = BaseMessage.make_user_message(role_name="User", content=prompt)
            response = self.agent.step(user_message)

            # 解析响应
            result = self._extract_json(response.msg.content)

            # 保存结果到缓存
            self._write_json(result, cache_file_name)

            return result

        except Exception as e:
            logger.error(f"分析仓库出错: {e}")
            return {"name": self.project_name, "error": str(e)}

    def analyze_repo(self) -> dict[str, Any]:
        """
        使用 ChatAgent 分析仓库，提取主要功能和亮点

        Returns:
            Dict: 包含仓库分析结果的字典
        """
        try:
            # 增强文档分析，重点关注README和关键文档
            docs_analysis = self._analyze_documentation()
            
            prompt = f"""
            你是一名资深开源项目评估专家，需要从技术深度和实用性的角度对 GitHub 仓库进行全面评测。请基于以下维度进行分析：

            ### 核心任务
            1. **主要功能** - 提取这个仓库的主要功能，包括同类工具的主要通能有哪些，这个仓库独特的功能有哪些
            2. **技术原理** - 列举实现这些功能的技术原理
            3. **应用场景** - 分析这个仓库的主要应用场景
            4. **差异化分析** - 列举同类工具有哪些，对比同类工具找出2个独特创新点
            5. **可操作性验证** - 参考同类工具的主要功能和仓库中的examples，设计5个关键测试案例，其中3个用于通用能力的验证，2个用于比较常用的独特功能的验证

            ### 仓库信息:
            {self.repo_info}
            
            ### 文档分析结果:
            {docs_analysis}

            ### 输出格式:
            ```json
            {{
                "name": "项目名称",
                "technical_analysis": {{
                    "common_features": [
                        {{
                            "name": "功能名称",
                            "description": "不超过15字的功能说明"
                        }}
                    ],
                    "unique_features": [
                        {{
                            "name": "功能名称",
                            "description": "不超过15字的功能说明"
                        }}
                    ],
                    "technical_implementations": [
                        "实现方案，不超过15个字"
                    ],
                    "similar_products": [
                        "同类工具有哪些"
                    ]
                }},
                "evaluation": {{
                    "strategy": "从哪些维度进行评测及原因（不超过100字）",
                    "test_cases": [
                        {{
                            "type": "功能类型（通用能力/独特能力）",
                            "purpose": "验证什么能力",
                            "preparation": {{
                                "files": [
                                    {{
                                        "file_path": "文件的目录和文件名",
                                        "file_content": "文件内容"
                                    }}
                                ]
                            }},
                            "execution": {{
                                "command": "可直接运行的命令（避免使用占位符）"
                            }},
                            "validation": {{
                                "expected_output": "应包含的关键字/数据结构",
                                "metrics": ["可量化的指标（如：耗时<200ms）"]
                            }}
                        }}
                    ]
                }}
            }}
            ```

            请确保：
            1. 功能描述简洁明了
            2. 突出项目特色和亮点
            3. 示例说明包含完整的运行命令
            4. 列出所有必要的依赖
            5. 对于每个示例，提供准确的运行命令和工作目录
            6. 测试命令应该是实际可以直接复制粘贴执行的，避免使用占位符
            7. 确保所有JSON字段都符合要求，尤其是test_cases部分
            """
            result = self._analyze_repo_base(prompt, "analyze_repo")
            
            # 验证结果中是否包含必要的字段
            if not self._validate_repo_analysis(result):
                logger.warning("分析结果验证失败，尝试重新生成")
                # 第二次尝试，使用更加简化的提示
                simplified_prompt = f"""
                分析GitHub仓库 {self.project_name} 并提供以下信息：
                
                1. 主要功能和特点
                2. 同类工具对比
                3. 至少5个可执行的测试用例，确保每个测试用例包含:
                   - 测试目的
                   - 准备阶段（需要的文件）
                   - 执行命令（使用确切的命令，不要有占位符）
                   - 验证标准
                
                仓库信息摘要:
                {self.repo_info.get('readme', '')[:1000]}
                
                必须使用JSON格式响应，包含name, technical_analysis和evaluation字段，其中evaluation必须包含test_cases数组。
                """
                result = self._analyze_repo_base(simplified_prompt, "analyze_repo_retry")
                
                # 再次验证
                if not self._validate_repo_analysis(result):
                    logger.error("多次尝试后，仍未能生成有效的分析结果，使用备选方案")
                    result = self._create_fallback_analysis_result()
            
            return result
            
        except Exception as e:
            logger.error(f"分析仓库时出错: {str(e)}")
            return self._create_fallback_analysis_result()
        
    def _validate_repo_analysis(self, result: dict[str, Any]) -> bool:
        """验证仓库分析结果是否包含必要的字段"""
        try:
            # 检查顶级字段
            if not result or not isinstance(result, dict):
                logger.error("分析结果不是有效的字典")
                return False
            
            # 检查是否包含必要的字段
            if "name" not in result:
                logger.error("分析结果缺少 'name' 字段")
                return False
            
            if "technical_analysis" not in result:
                logger.error("分析结果缺少 'technical_analysis' 字段")
                return False
            
            if "evaluation" not in result:
                logger.error("分析结果缺少 'evaluation' 字段")
                return False
            
            # 检查 evaluation 字段
            evaluation = result.get("evaluation", {})
            if not isinstance(evaluation, dict):
                logger.error("'evaluation' 字段不是有效的字典")
                return False
            
            # 检查 test_cases 字段
            if "test_cases" not in evaluation:
                logger.error("'evaluation' 字段缺少 'test_cases' 字段")
                return False
            
            test_cases = evaluation.get("test_cases", [])
            if not isinstance(test_cases, list) or len(test_cases) == 0:
                logger.error("'test_cases' 字段不是有效的非空列表")
                return False
            
            # 检查每个测试用例是否包含必要的字段
            for i, test_case in enumerate(test_cases):
                if not isinstance(test_case, dict):
                    logger.error(f"测试用例 {i} 不是有效的字典")
                    return False
                
                # 检查测试用例必要字段
                required_fields = ["type", "purpose", "preparation", "execution", "validation"]
                for field in required_fields:
                    if field not in test_case:
                        logger.error(f"测试用例 {i} 缺少 '{field}' 字段")
                        return False
                    
                # 检查 preparation 字段
                preparation = test_case.get("preparation", {})
                if "files" not in preparation or not isinstance(preparation["files"], list):
                    logger.error(f"测试用例 {i} 的 'preparation' 字段缺少有效的 'files' 列表")
                    return False
                
                # 检查 execution 字段
                execution = test_case.get("execution", {})
                if "command" not in execution or not execution["command"]:
                    logger.error(f"测试用例 {i} 的 'execution' 字段缺少有效的 'command'")
                    return False
                
                # 检查 validation 字段
                validation = test_case.get("validation", {})
                if "expected_output" not in validation:
                    logger.error(f"测试用例 {i} 的 'validation' 字段缺少 'expected_output'")
                    return False
        
            # 所有检查通过
            return True
            
        except Exception as e:
            logger.error(f"验证分析结果时出错: {str(e)}")
            return False
        
    def _create_fallback_analysis_result(self) -> dict[str, Any]:
        """创建备选的仓库分析结果"""
        logger.info(f"为 {self.project_name} 创建备选分析结果")
        
        # 从readme中提取一些基本信息
        readme = self.repo_info.get("readme", "")
        first_paragraph = readme.split("\n\n")[0] if readme else f"{self.project_name} 项目"
        
        # 创建基本的分析结果
        return {
            "name": self.project_name,
            "technical_analysis": {
                "common_features": [
                    {
                        "name": "基本功能",
                        "description": "核心功能"
                    },
                    {
                        "name": "数据处理",
                        "description": "处理数据"
                    }
                ],
                "unique_features": [
                    {
                        "name": "独特功能",
                        "description": "项目特点"
                    }
                ],
                "technical_implementations": [
                    "Python实现",
                    "模块化设计"
                ],
                "similar_products": [
                    "类似开源项目"
                ]
            },
            "evaluation": {
                "strategy": f"评估 {self.project_name} 的功能完整性、易用性和性能",
                "test_cases": [
                    {
                        "type": "通用能力",
                        "purpose": "验证基本功能",
                        "preparation": {
                            "files": [
                                {
                                    "file_path": "test_basic.py",
                                    "file_content": f"""
# 基本功能测试
print("测试 {self.project_name} 的基本功能")
try:
    import sys
    print(f"Python版本: {{sys.version}}")
    print("测试成功")
except Exception as e:
    print(f"测试失败: {{e}}")
"""
                                }
                            ]
                        },
                        "execution": {
                            "command": "python test_basic.py"
                        },
                        "validation": {
                            "expected_output": "测试成功",
                            "metrics": ["执行时间 < 1s"]
                        }
                    },
                    {
                        "type": "通用能力",
                        "purpose": "验证环境配置",
                        "preparation": {
                            "files": [
                                {
                                    "file_path": "test_env.py",
                                    "file_content": f"""
# 环境配置测试
import sys
import os

print(f"Python版本: {{sys.version}}")
print(f"当前工作目录: {{os.getcwd()}}")
print(f"环境变量数量: {{len(os.environ)}}")
print("环境配置正常")
"""
                                }
                            ]
                        },
                        "execution": {
                            "command": "python test_env.py"
                        },
                        "validation": {
                            "expected_output": "环境配置正常",
                            "metrics": ["显示Python版本"]
                        }
                    }
                ]
            }
        }

    def _analyze_documentation(self) -> str:
        """分析项目README和文档，生成项目能力概述"""
        try:
            # 检查是否有缓存的结果
            cache_file_name = f"{self.project_name}_docs_analysis"
            result = self._read_json(cache_file_name)
            if result is not None:
                return json.dumps(result, ensure_ascii=False, indent=2)
            
            # 准备用于分析的文档内容
            docs_content = ""
            
            # 添加README内容
            if self.repo_info.get("readme"):
                docs_content += f"## README\n{self.repo_info['readme']}\n\n"
            
            # 添加最多5个主要文档文件内容
            for idx, doc in enumerate(self.repo_info.get("documentation", [])[:5]):
                docs_content += f"## {doc['name']} ({doc['path']})\n{doc['content'][:2000]}...\n\n"
            
            # 创建用于文档分析的提示词
            prompt = f"""
            你是一名专业文档分析专家，需要分析项目文档并提取项目的核心能力和特性。请基于以下文档内容：

            {docs_content}

            请提供以下分析：
            1. 项目的主要目标和用途
            2. 核心功能和特性列表
            3. 技术架构简述
            4. 独特优势或创新点
            5. 使用方法概述

            请以JSON格式返回分析结果，包含上述5个方面。
            """
            
            # 发送消息给代理
            user_message = BaseMessage.make_user_message(role_name="User", content=prompt)
            response = self.agent.step(user_message)
            
            # 解析响应
            analysis_result = self._extract_json(response.msg.content)
            
            # 保存结果到缓存
            self._write_json(analysis_result, cache_file_name)
            
            return json.dumps(analysis_result, ensure_ascii=False, indent=2)
            
        except Exception as e:
            logger.error(f"分析文档出错: {e}")
            return f"文档分析失败: {str(e)}"

    def _collect_repo_info(self) -> dict[str, Any]:
        """收集仓库信息"""
        info = {"name": self.project_name, "structure": {}, "readme": "", "examples": [], "documentation": []}
        cache_file_name = f'{self.project_name}_repo_info'

        try:
            # 先尝试从缓存读取
            info_cache = self._read_json(cache_file_name)
            if info_cache is not None:
                return info_cache

            # 读取 README
            # 尝试多种可能的README文件名
            readme_files = ["README.md", "README", "README.txt", "Readme.md", "readme.md"]
            readme_path = None
            for readme_file in readme_files:
                path = self.repo_path / readme_file
                if path.exists():
                    readme_path = path
                    break
            if readme_path and readme_path.exists():
                readme_content = self._read_file_with_fallback_encoding(readme_path)
                # 限制README大小
                max_readme_size = self.github_config.get("max_readme_size", 50000)
                if len(readme_content) > max_readme_size:
                    logger.info(f"README内容过大，将被截断")
                    readme_content = readme_content[:max_readme_size] + "... [内容已截断]"
                info["readme"] = readme_content
            
            # 收集项目文档
            info["documentation"] = self._collect_documentation_files()

            # 获取配置中的最大示例数量
            max_examples = self.github_config.get("max_examples", 10)
            
            # 获取示例内容最大大小
            max_example_size = self.github_config.get("max_example_size", 10000)
            
            # 收集目录结构和示例代码
            example_dirs = [
                "examples",
                "demo",
                "samples",
                "tutorials",
                "getting-started",
                "quickstart",
                "example-projects",
                "boilerplate",
                "starters",
                "templates",
                "docs",
                "doc",
            ]

            """递归找示例文件目录"""
            examples_found = 0
            for item in self.repo_path.rglob("*"):
                # 如果达到最大示例数量限制，则停止收集
                if examples_found >= max_examples:
                    logger.info(f"已达到最大示例数量限制 ({max_examples})")
                    break
                    
                relative_path = item.relative_to(self.repo_path)
                if item.is_file() and any(
                    keyword.lower() == part.lower() for part in relative_path.parts for keyword in example_dirs
                ):
                    try:
                        # 处理 ipynb 文件
                        if item.suffix.lower() == '.ipynb':
                            content = self._convert_notebook_to_python(item)
                        else:
                            content = self._read_file_with_fallback_encoding(item)
                            
                        # 限制示例文件内容大小
                        if len(content) > max_example_size:
                            logger.info(f"示例 {item} 内容过大，将被截断")
                            content = content[:max_example_size] + "... [内容已截断]"
                        
                        info["examples"].append({"name": item.stem, "path": str(relative_path), "content": content})
                        examples_found += 1
                    except Exception as e:
                        logger.warning(f"读取文件 {item} 失败: {e}")
            logger.info(f'收集完 {info["name"]} repo 的信息，找到 {examples_found} 个示例')
            self._write_json(info, cache_file_name)
            return info

        except Exception as e:
            logger.error(f"收集仓库信息出错: {e}")
            return info
            
    def _collect_documentation_files(self) -> list[dict[str, Any]]:
        """收集项目文档文件"""
        documentation = []
        doc_dirs = ["docs", "doc", "documentation", "wiki", "guide", "guides", "manual", "reference"]
        doc_extensions = [".md", ".rst", ".txt", ".html"]
        priority_files = ["overview.md", "introduction.md", "guide.md", "tutorial.md", "api.md", "usage.md"]
        
        # 首先检查是否要优先考虑文档文件
        prioritize_docs = self.github_config.get("prioritize_doc_files", True)
        
        # 获取文档文件数量限制
        max_docs = self.github_config.get("max_docs", 5)
        
        # 查找项目中的文档文件
        for item in self.repo_path.rglob("*"):
            # 如果已达到文档文件数量限制，则停止收集
            if len(documentation) >= max_docs:
                logger.info(f"已达到最大文档文件数量限制 ({max_docs})")
                break
            
            if item.is_file() and (
                # 优先考虑常见文档文件名
                (prioritize_docs and item.name.lower() in [f.lower() for f in priority_files]) or
                # 检查文件扩展名和路径中是否含有文档目录关键字
                (item.suffix.lower() in doc_extensions and any(
                    doc_dir.lower() in part.lower() for part in item.parts for doc_dir in doc_dirs
                ))
            ):
                try:
                    relative_path = item.relative_to(self.repo_path)
                    content = self._read_file_with_fallback_encoding(item)
                    
                    # 如果文件内容过大，限制其大小
                    max_content_size = self.github_config.get("max_doc_content_size", 20000)
                    if len(content) > max_content_size:
                        logger.info(f"文档 {item} 内容过大，将被截断")
                        content = content[:max_content_size] + "... [内容已截断]"
                    
                    documentation.append({
                        "name": item.stem,
                        "path": str(relative_path),
                        "content": content
                    })
                except Exception as e:
                    logger.warning(f"读取文档文件 {item} 失败: {e}")
        
        logger.info(f"收集到 {len(documentation)} 个文档文件")
        return documentation

    def _convert_notebook_to_python(self, notebook_path: Path) -> str:
        """将Jupyter Notebook转换为Python代码"""
        # 检查是否有可用的转换功能
        if not NOTEBOOK_CONVERSION_AVAILABLE:
            logger.warning(f"Notebook转换依赖不可用，将使用替代方法读取: {notebook_path}")
            return self._fallback_notebook_read(notebook_path)
            
        try:
            logger.info(f"正在转换Jupyter Notebook: {notebook_path}")
            
            # 读取notebook文件
            with open(notebook_path, 'r', encoding='utf-8') as f:
                notebook_content = f.read()
            
            # 解析notebook
            notebook = nbformat.reads(notebook_content, as_version=4)
            
            # 转换为Python
            python_exporter = PythonExporter()
            python_code, _ = python_exporter.from_notebook_node(notebook)
            
            # 添加注释标明这是从notebook转换来的
            header = f"# 从Jupyter Notebook转换: {notebook_path.name}\n# 转换时间: {datetime.datetime.now()}\n\n"
            return header + python_code
            
        except Exception as e:
            logger.error(f"转换Notebook失败 {notebook_path}: {e}")
            return self._fallback_notebook_read(notebook_path)
            
    def _fallback_notebook_read(self, notebook_path: Path) -> str:
        """当无法转换notebook时的备选方法，直接提取代码单元格内容"""
        try:
            # 简单读取JSON格式的notebook并提取代码单元格
            with open(notebook_path, 'r', encoding='utf-8') as f:
                nb_content = json.load(f)
            
            code_cells = []
            
            # 尝试提取代码单元格
            if 'cells' in nb_content:
                for cell in nb_content['cells']:
                    if cell.get('cell_type') == 'code' and 'source' in cell:
                        source = cell['source']
                        # 处理source可能是字符串或字符串列表的情况
                        if isinstance(source, list):
                            code_cells.append(''.join(source))
                        else:
                            code_cells.append(source)
            
            # 如果提取到代码，返回合并的代码
            if code_cells:
                header = f"# 从Jupyter Notebook简易提取: {notebook_path.name}\n# 提取时间: {datetime.datetime.now()}\n\n"
                return header + "\n\n# ---- 下一个代码单元格 ----\n\n".join(code_cells)
            else:
                return f"# 未能从Notebook中提取代码: {notebook_path.name}"
                
        except Exception as e:
            logger.error(f"备选Notebook读取失败 {notebook_path}: {e}")
            return f"# 无法读取Notebook: {str(e)}"

    def _extract_json(self, text: str) -> dict[str, Any]:
        """从文本中提取JSON内容"""
        try:
            # 寻找JSON块
            if "```json" in text and "```" in text[text.find("```json") + 7 :]:
                json_start = text.find("```json") + 7
                json_end = text.find("```", json_start)
                json_str = text[json_start:json_end].strip()
                try:
                    return json.loads(json_str)
                except json.JSONDecodeError as je:
                    logger.error(f"JSON解析错误: {str(je)}, 尝试其他方法")

            # 尝试找到{}括起来的JSON
            json_start = text.find("{")
            json_end = text.rfind("}") + 1

            if json_start >= 0 and json_end > json_start:
                json_str = text[json_start:json_end]
                try:
                    return json.loads(json_str)
                except json.JSONDecodeError as je:
                    logger.error(f"JSON解析错误: {str(je)}, 尝试使用备选逻辑")

            # 备选方案：构建一个基本结构的JSON
            logger.warning("未在响应中找到有效的JSON，使用备选方案")
            return self._create_fallback_json(text)

        except Exception as e:
            logger.error(f"提取JSON错误: {str(e)}")
            return self._create_fallback_json(text)

    def _create_fallback_json(self, text: str) -> dict[str, Any]:
        """当无法从响应中提取JSON时，创建一个基本结构的JSON"""
        try:
            fallback_json = {
                "name": self.project_name,
                "technical_analysis": {
                    "common_features": [
                        {
                            "name": "无法解析",
                            "description": "未能从响应中提取功能"
                        }
                    ],
                    "unique_features": [
                        {
                            "name": "无法解析",
                            "description": "未能从响应中提取功能"
                        }
                    ],
                    "technical_implementations": [
                        "未能从响应中提取实现方案"
                    ],
                    "similar_products": [
                        "未能从响应中提取同类工具"
                    ]
                },
                "evaluation": {
                    "strategy": "未能从响应中提取评测策略",
                    "test_cases": [
                        {
                            "type": "通用能力",
                            "purpose": "基本功能验证",
                            "preparation": {
                                "files": [
                                    {
                                        "file_path": "test.py",
                                        "file_content": "print('Hello, world!')"
                                    }
                                ]
                            },
                            "execution": {
                                "command": "python test.py"
                            },
                            "validation": {
                                "expected_output": "Hello, world!",
                                "metrics": ["执行时间 < 1s"]
                            }
                        }
                    ]
                }
            }
            
            # 尝试从文本中提取一些有用的信息
            logger.info("尝试从文本中提取一些基本信息")
            
            # 提取这些片段可能存在于模型生成的有关项目的文本中
            features_match = re.search(r'功能[：:](.*?)(?=\n\n|\Z)', text, re.DOTALL)
            if features_match:
                feature_text = features_match.group(1).strip()
                features = [f.strip() for f in feature_text.split('\n') if f.strip()]
                if features:
                    fallback_json["technical_analysis"]["common_features"] = [
                        {"name": f[:30], "description": f[:15]} for f in features[:3]
                    ]
            
            # 提取可能的独特功能
            unique_match = re.search(r'独特[功能特点][：:](.*?)(?=\n\n|\Z)', text, re.DOTALL)
            if unique_match:
                unique_text = unique_match.group(1).strip()
                unique_features = [f.strip() for f in unique_text.split('\n') if f.strip()]
                if unique_features:
                    fallback_json["technical_analysis"]["unique_features"] = [
                        {"name": f[:30], "description": f[:15]} for f in unique_features[:2]
                    ]
            
            return fallback_json
            
        except Exception as e:
            logger.error(f"创建备选JSON时出错: {str(e)}")
            # 返回最小化的有效JSON结构
            return {
                "name": self.project_name,
                "technical_analysis": {"common_features": [], "unique_features": []},
                "evaluation": {
                    "test_cases": [
                        {
                            "type": "基础测试",
                            "purpose": "验证基本功能",
                            "preparation": {"files": [{"file_path": "test.py", "file_content": "print('Test')"}]},
                            "execution": {"command": "python test.py"},
                            "validation": {"expected_output": "Test", "metrics": []}
                        }
                    ]
                }
            }

    def _run_example(self, example_info: dict[str, Any], recording_dir: str = None) -> dict[str, Any]:
        """
        运行指定的示例并录制过程

        Args:
            example_info: 示例信息字典
            recording_dir: 录制文件保存目录

        Returns:
            Dict: 运行结果
        """
        try:
            example_name = example_info["name"]
            example_path = example_info["path"]

            # 构建示例文件路径
            example_file = self.repo_path / example_path
            if not example_file.exists():
                raise FileNotFoundError(f"示例文件不存在: {example_file}")

            # 使用项目特定的录制目录
            if recording_dir is None:
                recording_dir = str(Path(f"output/{self.project_name}/recordings"))
            
            # 创建录制目录
            os.makedirs(recording_dir, exist_ok=True)

            # 生成录制文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            recording_file = os.path.join(recording_dir, f"{example_name}_{timestamp}.cast")

            # 开始录制
            recording_success, recording_process, recording_error = self.start_recording(recording_file)

            if not recording_success:
                raise RuntimeError(f"开始录制失败: {recording_error}")

            try:
                # 运行示例
                logger.info(f"开始运行示例: {example_name}")

                # 切换到项目根目录
                original_dir = os.getcwd()
                os.chdir(str(self.repo_path))

                # 执行命令
                process = subprocess.Popen(
                    example_info.get("run_command").get("command"),
                    shell=True,
                    stdin=recording_process.stdin,  # 重定向到录制进程的stdin
                    stdout=recording_process.stdin,  # 重定向到录制进程的stdin
                    stderr=recording_process.stdin,  # 重定向到录制进程的stdin
                    text=True,
                    bufsize=1,  # 行缓冲
                )

                # 等待命令完成
                process.wait()

                # 恢复原始目录
                os.chdir(original_dir)

                # 停止录制
                self.stop_recording(recording_process)

                return {
                    "success": process.returncode == 0,
                    "example_name": example_name,
                    "command": example_info.get("run_command").get("command"),
                    "recording_file": recording_file,
                }

            except Exception as e:
                # 确保停止录制
                self.stop_recording(recording_process)
                raise e

        except Exception as e:
            logger.error(f"运行示例出错: {e}")
            return {"success": False, "example_name": example_info["name"], "error": str(e)}

    def start_recording(self, output_file: str) -> tuple[bool, Optional[subprocess.Popen], str]:
        """开始录制"""
        try:
            # 确保asciinema已安装
            try:
                subprocess.run(["asciinema", "--version"], check=True, capture_output=True)
            except (subprocess.SubprocessError, FileNotFoundError):
                logger.warning("asciinema未安装，尝试安装...")
                subprocess.run(["pip", "install", "asciinema"], check=True)

            # 创建输出目录
            os.makedirs(os.path.dirname(output_file), exist_ok=True)

            logger.info(f"开始录制: {output_file}")

            # 启动asciinema录制
            process = subprocess.Popen(
                ["asciinema", "rec", output_file],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
            )

            # 等待录制启动
            time.sleep(1)

            return True, process, ""

        except Exception as e:
            error_msg = f"开始录制失败: {str(e)}"
            logger.error(error_msg)
            return False, None, error_msg

    def stop_recording(self, process: subprocess.Popen) -> bool:
        """停止录制"""
        try:
            if process:
                logger.info("正在停止录制...")

                # 发送Ctrl+D结束录制
                try:
                    process.communicate(input="\x04", timeout=5)
                except subprocess.TimeoutExpired:
                    # 如果超时，尝试强制终止进程
                    process.terminate()
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        process.kill()

                # 等待进程结束
                time.sleep(3)

                logger.info("录制已停止")
                return True
            return False
        except Exception as e:
            logger.error(f"停止录制失败: {str(e)}")
            return False

    def execute_test_cases(self, analysis: dict[str, Any] = None) -> dict[str, Any]:
        """
        使用roleplay方式执行测试用例

        Args:
            analysis: 分析结果包含测试用例，如果为None则使用已有的分析结果

        Returns:
            Dict: 包含测试结果的字典
        """
        # 保存当前工作目录
        original_dir = os.getcwd()

        try:
            if analysis is None:
                analysis = self.analyze_repo()

            if "evaluation" not in analysis or "test_cases" not in analysis["evaluation"]:
                logger.error("分析结果中未包含测试用例")
                return {"success": False, "error": "分析结果中未包含测试用例"}

            test_cases = analysis["evaluation"]["test_cases"]
            logger.info(f"共找到 {len(test_cases)} 个测试用例需要执行")

            # 创建项目特定的评估目录
            eval_base_dir = Path(f"output/{self.project_name}/evaluations")
            eval_base_dir.mkdir(parents=True, exist_ok=True)

            # 测试用例运行状态
            test_results = []

            # 为每个测试用例创建目录并执行
            for idx, test_case in enumerate(test_cases):
                logger.info(f"开始执行测试用例 {idx+1}/{len(test_cases)}")

                # 创建测试目录
                test_dir = self._create_test_dir(eval_base_dir, idx, test_case)
                
                # 使用roleplay方式生成和执行测试代码
                result = self._roleplay_test_execution(test_dir, test_case)

                # 确认当前目录是否为原始目录
                if os.getcwd() != original_dir:
                    logger.warning(f"目录不一致，正在恢复原始目录: {original_dir}")
                    os.chdir(original_dir)

                # 记录结果
                result_info = {"test_case": test_case, "result": result, "test_dir": str(test_dir)}
                test_results.append(result_info)

            # 计算成功和失败的测试用例数量
            successful_tests = sum(1 for result_info in test_results if result_info["result"].get("success", False))
            logger.info(f"测试完成: {successful_tests}/{len(test_results)} 个测试用例成功")

            # 返回测试结果
            all_succeeded = successful_tests == len(test_results)
            return {"success": all_succeeded, "test_results": test_results}
        finally:
            # 确保恢复原始目录
            if os.getcwd() != original_dir:
                logger.info(f"最终恢复原始工作目录: {original_dir}")
                os.chdir(original_dir)

    def _roleplay_test_execution(self, test_dir: Path, test_case: dict[str, Any]) -> dict[str, Any]:
        """使用roleplay方式生成和执行测试代码"""
        logger.info(f"开始roleplay测试执行: {test_case.get('purpose', '未知目的')}")
        
        # 准备roleplay任务内容
        task_content = f"""
        请针对以下测试用例生成并执行测试代码：
        
        测试用例:
        ```json
        {json.dumps(test_case, indent=2, ensure_ascii=False)}
        ```
        
        仓库信息:
        ```json
        {json.dumps({"name": self.project_name, "path": str(self.repo_path)}, indent=2, ensure_ascii=False)}
        ```
        
        测试目录路径: {test_dir}
        
        任务要求:
        1. 代码生成器负责生成简洁、核心的测试代码
        2. 测试执行者负责执行代码并提供反馈
        3. 如果测试失败，代码生成器需要根据反馈修改代码
        4. 最多进行3轮迭代，确保代码能正常运行
        5. 最终结果必须包含测试是否成功以及执行日志
        
        代码要求:
        1. 生成精简的、单一功能的测试脚本
        2. 优先使用标准库，避免复杂依赖
        3. 使用线性执行流程，避免复杂嵌套结构
        4. 确保错误处理明确，便于问题定位
        5. 分离测试步骤，使问题容易发现和修复
        
        每轮对话流程:
        1. 代码生成器生成简洁、易于调试的测试代码
        2. 测试执行者执行代码并提供详细的执行结果和错误日志
        3. 如代码有问题，代码生成器必须根据错误日志修改代码，解决所有语法错误和运行时错误
        
        ## 注意事项
        
        测试执行者必须：
        1. 实际执行代码并提供完整的错误信息
        2. 指出所有语法错误和运行错误的具体位置和原因
        3. 不要假装执行成功，必须真实反馈执行结果
        4. 给出明确的修复建议
        
        代码生成器必须：
        1. 生成核心功能测试代码，避免不必要的抽象和复杂性
        2. 使用简单直接的方法调用，便于错误定位
        3. 提供清晰的错误处理和输出，帮助调试
        4. 仔细分析错误信息，针对性修复问题
        5. 确保代码能够在目标环境中正常运行
        
        ## 角色定位
        
        代码生成器(Code Generator)：
        {CODE_GENERATOR_PROMPT}
        
        测试执行者(Test Executor)：
        {TEST_EXECUTOR_PROMPT}
        """
        
        try:
            # 创建角色扮演
            role_playing = RolePlaying(
                assistant_role_name="Code Generator",
                assistant_agent_kwargs={
                    "model": self.model
                },
                user_role_name="Test Executor",
                user_agent_kwargs={
                    "model": self.model
                },
                task_prompt=task_content,
                task_type=TaskType.AI_SOCIETY,
                with_task_specify=False,
                output_language="chinese",
            )
            
            # 开始对话
            logger.info("开始角色对话")
            messages = []
            
            # 初始化对话
            chat_history = role_playing.init_chat()
            messages.append(chat_history)
            
            # 创建日志文件
            log_file_path = test_dir / "execution.log"
            results_file_path = test_dir / "execution_results.json"
            self._write_log(log_file_path, "--- 开始roleplay测试执行 ---\n\n")
            
            # 跟踪测试文件和成功状态
            created_files = []
            success = False
            log_content = ""
            round_num = 0
            execution_results = []
            previous_error = None
            
            # 进行最多3轮对话
            max_rounds = 3
            for round_num in range(max_rounds):
                logger.info(f"对话轮次 {round_num + 1}/{max_rounds}")
                round_data = {"round": round_num + 1, "code_generator": "", "test_executor": "", "files": [], "execution_output": "", "success": False}
                
                try:
                    # 如果不是第一轮且有错误，增强测试执行者的反馈
                    if round_num > 0 and previous_error:
                        # 强化错误反馈提示
                        error_feedback = f"""
                        上一轮代码执行失败，错误信息如下：
                        
                        ```
                        {previous_error}
                        ```
                        
                        请修复上述问题，确保代码能够正常运行。特别注意语法错误和导入问题。
                        请提供完整修复后的代码。
                        """
                        # 将错误反馈添加到角色扮演中
                        chat_history.content += "\n\n" + error_feedback
                    
                    # 进行对话步骤
                    code_generator_response, test_executor_response = role_playing.step(chat_history)
                    
                    # 从响应中获取消息
                    code_generator_message = code_generator_response.msg
                    test_executor_message = test_executor_response.msg
                    
                    # 添加到历史记录
                    chat_history = code_generator_message
                    messages.append(code_generator_message)
                    messages.append(test_executor_message)
                    
                    # 记录对话到日志
                    self._write_log(log_file_path, f"--- 轮次 {round_num + 1} ---\n")
                    self._write_log(log_file_path, f"Code Generator: {code_generator_message.content}\n\n")
                    self._write_log(log_file_path, f"Test Executor: {test_executor_message.content}\n\n")
                    
                    # 记录到轮次数据中
                    round_data["code_generator"] = code_generator_message.content
                    round_data["test_executor"] = test_executor_message.content
                    
                    # 提取代码生成器消息中的文件创建信息
                    created_files_in_round = self._extract_files_from_message(code_generator_message.content, test_dir)
                    created_files.extend(created_files_in_round)
                    round_data["files"] = created_files_in_round
                    
                    # 自己执行测试并捕获输出
                    execution_output, execution_success = self._execute_test_files(created_files_in_round, test_dir)
                    round_data["execution_output"] = execution_output
                    round_data["success"] = execution_success
                    
                    # 检查输出中是否包含错误信息，即使返回码是0
                    error_keywords = ["SyntaxError", "ImportError", "ModuleNotFoundError", 
                                    "NameError", "TypeError", "ValueError", 
                                    "Exception", "Error", "错误", "失败", "测试失败"]
                    
                    has_error_in_output = any(keyword.lower() in execution_output.lower() for keyword in error_keywords)
                    
                    # 保存错误信息，用于下一轮反馈
                    # 如果执行不成功或输出中包含错误关键词，认为需要修复
                    if not execution_success or has_error_in_output:
                        previous_error = execution_output
                    else:
                        previous_error = None
                    
                    # 添加轮次数据到结果中
                    execution_results.append(round_data)
                    
                    # 判断实际执行结果与测试执行者的反馈是否一致，不一致则矫正
                    test_executor_reports_success = "成功" in test_executor_message.content and "失败" not in test_executor_message.content
                    test_executor_reports_failure = "失败" in test_executor_message.content
                    
                    # 情况1: 实际成功，但测试执行者报告失败
                    if execution_success and test_executor_reports_failure:
                        # 创建一个新的执行成功消息
                        success_message = f"""
                        我已执行了代码，测试成功通过！实际执行结果如下：
                        
                        ```
                        {execution_output}
                        ```
                        
                        代码正常执行，没有发现错误。测试用例通过。
                        """
                        # 将这个消息添加到测试执行者的反馈中
                        chat_history.content = success_message
                        logger.info("测试实际成功，但测试执行者报告失败，已自动纠正反馈")
                    
                    # 情况2: 实际失败，但测试执行者报告成功
                    elif (not execution_success or has_error_in_output) and test_executor_reports_success:
                        # 创建一个新的执行失败消息
                        failure_message = f"""
                        代码执行失败，错误信息如下：
                        
                        ```
                        {execution_output}
                        ```
                        
                        请仔细查看错误信息，修复代码中的问题。特别注意可能的语法错误和导入错误。
                        """
                        # 将这个消息添加到测试执行者的反馈中
                        chat_history.content = failure_message
                        logger.info("测试实际失败，但测试执行者误报成功，已自动纠正反馈")
                    
                    # 判断是否真正成功
                    if execution_success and not has_error_in_output:
                        success = True
                        log_content = f"测试执行成功。\n\n执行输出:\n{execution_output}"
                        break
                    
                    # 如果是最后一轮且未成功，记录最终状态
                    if round_num == max_rounds - 1 and not success:
                        log_content = f"所有轮次测试失败。最后一轮错误:\n{execution_output}"
                    
                except Exception as e:
                    logger.error(f"对话步骤出错: {str(e)}")
                    self._write_log(log_file_path, f"对话步骤出错: {str(e)}\n")
                    round_data["error"] = str(e)
                    execution_results.append(round_data)
                    break
            
            # 保存执行结果到JSON文件
            self._write_json(execution_results, str(results_file_path).replace(".json", ""))
            
            # 返回测试结果
            return {
                "success": success,
                "log_file": str(log_file_path),
                "results_file": str(results_file_path),
                "log_content": log_content,
                "created_files": created_files,
                "rounds": round_num + 1,
                "execution_results": execution_results
            }
            
        except Exception as e:
            logger.error(f"Roleplay测试执行出错: {str(e)}")
            # 确保日志文件存在，即使roleplay执行出错
            log_file_path = test_dir / "execution.log"
            self._write_log(log_file_path, f"Roleplay测试执行出错: {str(e)}\n")
            
            return {"success": False, "error": str(e), "log_file": str(log_file_path)}
    
    def _execute_test_files(self, files: list[str], test_dir: Path) -> tuple[str, bool]:
        """执行测试文件并捕获输出"""
        if not files:
            return "没有找到可执行的测试文件", False
            
        output = ""
        success = False
        
        try:
            # 切换到测试目录
            original_dir = os.getcwd()
            test_dir_abs = test_dir.resolve()  # 获取测试目录的绝对路径
            os.chdir(str(test_dir_abs))
            
            # 查找Python文件
            py_files = [f for f in files if f.endswith('.py')]
            if not py_files:
                output = "没有找到可执行的Python测试文件"
                return output, False
            
            # 执行第一个Python文件 - 确保使用的是文件名而不是全路径
            test_file = py_files[0]
            # 如果传入的是路径而非文件名，提取文件名
            if os.path.sep in test_file:
                test_file = os.path.basename(test_file)
                
            logger.info(f"执行测试文件: {test_file} (在目录 {os.getcwd()})")
            
            # 检查文件是否存在
            if not os.path.exists(test_file):
                output = f"测试文件不存在: {test_file} (当前目录: {os.getcwd()})"
                return output, False
            
            # 使用subprocess执行并捕获输出
            try:
                # 先验证代码语法，以提供更明确的语法错误位置
                syntax_check = subprocess.run(
                    ["python", "-m", "py_compile", test_file],
                    capture_output=True,
                    text=True,
                    check=False
                )
                
                if syntax_check.returncode != 0:
                    # 如果语法检查失败，直接返回详细的语法错误信息
                    output = f"--- 语法错误 ---\n{syntax_check.stderr}\n\n"
                    return output, False
                
                # 语法检查通过，执行实际测试
                result = subprocess.run(
                    ["python", test_file], 
                    check=False,
                    capture_output=True,
                    text=True,
                    timeout=60  # 设置超时时间为60秒
                )
                
                # 收集stdout和stderr
                output = f"--- 标准输出 ---\n{result.stdout}\n\n--- 错误输出 ---\n{result.stderr}\n"
                
                # 定义错误关键词列表
                error_keywords = [
                    "SyntaxError", "ImportError", "ModuleNotFoundError", 
                    "NameError", "TypeError", "ValueError", 
                    "Exception", "Error", "错误", "失败", "测试失败"
                ]
                
                # 检查输出中是否包含错误关键词
                combined_output = (result.stdout + result.stderr).lower()
                has_error_keyword = any(keyword.lower() in combined_output for keyword in error_keywords)
                
                # 如果有ImportError或ModuleNotFoundError，提供更详细的诊断信息
                if "ImportError" in result.stderr or "ModuleNotFoundError" in result.stderr:
                    # 查看可用的模块
                    try:
                        pip_list = subprocess.run(
                            ["pip", "list"], 
                            capture_output=True,
                            text=True,
                            check=False
                        )
                        output += f"\n--- 当前环境可用模块 ---\n{pip_list.stdout}\n"
                    except Exception:
                        pass
                
                # 判断是否成功 - 结合返回码和错误关键词检查
                success = result.returncode == 0 and not has_error_keyword
                
                # 为了更好的日志记录，如果有错误关键词但返回码是0，添加特别说明
                if result.returncode == 0 and has_error_keyword:
                    logger.warning(f"返回码为0但输出中包含错误信息，将执行结果标记为失败")
                    
                if success:
                    output += "\n执行成功！"
                else:
                    # 如果返回码是0但有错误关键词，特别说明
                    if result.returncode == 0:
                        output += "\n执行看似成功，但输出中检测到错误信息，标记为失败"
                    else:
                        output += f"\n执行失败，返回代码: {result.returncode}"
                        
                    # 提供更有帮助的错误修复建议
                    if "SyntaxError" in result.stderr:
                        output += "\n建议：检查语法错误，特别注意缩进、括号匹配和引号"
                    elif "ImportError" in result.stderr or "ModuleNotFoundError" in result.stderr:
                        output += "\n建议：检查导入语句，确保所需模块已安装或路径正确"
                    elif "NameError" in result.stderr:
                        output += "\n建议：检查变量名拼写和作用域"
                    elif "TypeError" in result.stderr:
                        output += "\n建议：检查函数参数类型和数量"
                    
            except subprocess.TimeoutExpired:
                output = "执行超时（超过60秒）"
                success = False
            except Exception as e:
                output = f"执行出错: {str(e)}"
                success = False
                
        except Exception as e:
            output = f"设置执行环境时出错: {str(e)}"
            success = False
        finally:
            # 恢复原始目录
            try:
                os.chdir(original_dir)
            except Exception as e:
                logger.error(f"恢复原始目录失败: {str(e)}")
                
        return output, success
        
    def _extract_files_from_message(self, message: str, test_dir: Path) -> list[str]:
        """从代码生成器消息中提取并创建文件"""
        created_files = []
        
        # 查找特定语言的代码块，优先识别Python代码块
        python_code_blocks = re.findall(r'```python\s*\n(.*?)\n```', message, re.DOTALL)
        
        # 如果没有找到标记为python的代码块，则尝试查找未标记语言的代码块
        if not python_code_blocks:
            generic_code_blocks = re.findall(r'```(?!python)(?:[a-zA-Z]*)\s*\n(.*?)\n```', message, re.DOTALL)
            if generic_code_blocks:
                # 检查这些通用代码块是否包含Python代码特征
                python_code_blocks = [block for block in generic_code_blocks if 
                                     'import ' in block or 
                                     'def ' in block or 
                                     'class ' in block or 
                                     '= ' in block]
        
        # 查找文件名模式
        file_patterns = [
            r'创建文件[：:]\s*[\'"]?([\w\-\.\/]+)[\'"]?',
            r'文件名[：:]\s*[\'"]?([\w\-\.\/]+)[\'"]?',
            r'保存为[：:]\s*[\'"]?([\w\-\.\/]+)[\'"]?',
            r'写入文件[：:]\s*[\'"]?([\w\-\.\/]+)[\'"]?',
            r'创建(\S+\.py)文件',
            r'新建(\S+\.py)文件',
            r'编写(\S+\.py)文件',
            r'@([\w\-\.]+\.py)'  # 新增识别格式如 @test_script.py 的模式
        ]
        
        # 从消息中提取可能的文件名
        potential_files = []
        for pattern in file_patterns:
            matches = re.findall(pattern, message)
            potential_files.extend(matches)
        
        # 检查test_dir目录中已存在的文件
        existing_files = set()
        if test_dir.exists():
            existing_files = {f.name for f in test_dir.iterdir() if f.is_file()}
        
        # 生成当前时间的时间戳，用于确保文件名的唯一性
        timestamp = int(time.time())
        
        # 如果找到Python代码块但没有找到文件名，可以尝试使用一个默认名称
        if python_code_blocks and not potential_files:
            default_filename = f"test_script_{timestamp}.py"
            potential_files.append(default_filename)
        
        # 如果找到Python代码块和文件名，创建文件
        if python_code_blocks:
            # 尝试将每个代码块与文件名配对
            for i, code_block in enumerate(python_code_blocks):
                # 如果有足够的文件名，使用对应的；否则使用最后一个或生成默认值
                base_file_name = potential_files[i] if i < len(potential_files) else \
                                potential_files[-1] if potential_files else f"test_script_{i+1}.py"
                
                # 处理文件名以确保唯一性
                file_stem = Path(os.path.basename(base_file_name)).stem
                file_suffix = Path(os.path.basename(base_file_name)).suffix or ".py"
                
                # 添加时间戳确保文件名唯一
                file_name = f"{file_stem}_{timestamp}_{i}{file_suffix}"
                
                # 确保只有文件名而没有路径
                file_name = os.path.basename(file_name)
                
                # 创建文件路径 - 使用resolve()获取绝对路径，避免路径嵌套
                file_path = test_dir.resolve() / file_name
                
                # 确保父目录存在
                file_path.parent.mkdir(parents=True, exist_ok=True)
                
                # 写入文件内容 - 确保代码块内容是有效的Python代码
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(code_block)
                
                logger.info(f"从消息中创建Python文件: {file_name} (在目录 {test_dir.resolve()})")
                
                # 只存储文件名而非路径，避免路径重复问题
                created_files.append(file_name)
        
        return created_files

    def _write_log(self, log_file_path: Path, content: str):
        """写入日志文件"""
        try:
            # 确保日志目录存在
            log_file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 追加模式写入
            with open(log_file_path, "a", encoding="utf-8") as f:
                f.write(content)
                
        except Exception as e:
            logger.error(f"写入日志文件出错: {str(e)}")

    def _create_test_dir(self, eval_base_dir: Path, idx: int, test_case: dict[str, Any]) -> Path:
        """创建测试目录"""
        # 直接使用数字作为目录名
        test_dir = eval_base_dir / f"{idx+1}"
        test_dir.mkdir(parents=True, exist_ok=True)

        logger.info(f"创建测试目录: {test_dir}")
        return test_dir

    def _ensure_repo_exists(self):
        """确保GitHub仓库已被克隆到本地"""
        if not (self.repo_path / ".git").exists():
            project_url = self.github_config.get("project_url")
            if not project_url:
                logger.error("未设置GitHub项目URL，无法克隆仓库")
                return
                
            logger.info(f"仓库不存在，开始从 {project_url} 克隆到 {self.repo_path}")
            
            # 确保输出目录存在 (output/project_name)
            parent_dir = self.repo_path.parent
            parent_dir.mkdir(parents=True, exist_ok=True)
            
            # 如果目录已存在但不是git仓库，先删除
            if self.repo_path.exists() and not (self.repo_path / ".git").exists():
                logger.info(f"目录 {self.repo_path} 已存在但不是git仓库，正在删除")
                shutil.rmtree(self.repo_path)
            
            try:
                # 克隆仓库
                subprocess.run(
                    ["git", "clone", project_url, str(self.repo_path)], 
                    check=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
                logger.info(f"成功克隆仓库到 {self.repo_path}")
            except subprocess.CalledProcessError as e:
                logger.error(f"克隆仓库失败: {e.stderr.decode() if e.stderr else str(e)}")
        else:
            logger.info(f"仓库已存在于 {self.repo_path}")
            # 可以选择更新仓库
            try:
                original_dir = os.getcwd()
                os.chdir(str(self.repo_path))
                subprocess.run(["git", "pull"], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                os.chdir(original_dir)
                logger.info("仓库已更新到最新状态")
            except Exception as e:
                logger.warning(f"更新仓库失败: {str(e)}")
                if os.getcwd() != original_dir:
                    os.chdir(original_dir)

    def _read_file_with_fallback_encoding(self, file_path: Path) -> str:
        """读取文件内容，自动尝试不同的编码格式"""
        encodings = ['utf-8', 'latin-1', 'cp1252', 'ascii']
        
        for encoding in encodings:
            try:
                with open(file_path, encoding=encoding) as f:
                    return f.read()
            except UnicodeDecodeError:
                continue
            except Exception as e:
                logger.error(f"读取文件 {file_path} 时出错: {str(e)}")
                return ""
                
        logger.warning(f"无法使用已知编码读取文件 {file_path}")
        return ""

    def run_evaluation(self):
        """运行完整评估流程"""
        # 分析仓库
        logger.info("开始分析仓库...")
        analysis = self.analyze_repo()

        if "error" in analysis:
            logger.error(f"分析仓库出错: {analysis['error']}")
            return {"success": False, "error": analysis["error"]}

        # 执行测试用例
        logger.info("开始执行测试用例...")
        execution_result = self.execute_test_cases(analysis)

        if not execution_result.get("success", False):
            logger.warning("有些测试用例执行失败")

        # 评估测试结果
        logger.info("开始评估测试结果...")
        test_results = execution_result.get("test_results", [])
        evaluation_result_obj = self.evaluate_test_results(test_results)

        # 获取评估内容
        evaluation_content = evaluation_result_obj.get("evaluation", "")

        # 保存评估报告
        report_path = self.save_evaluation_report(test_results, evaluation_content)

        # 返回完整结果
        return {
            "success": execution_result.get("success", False),
            "analysis": analysis,
            "execution": execution_result,
            "evaluation": evaluation_result_obj,
            "report_path": report_path,
        }
    
    def evaluate_test_results(self, test_results: list[dict[str, Any]]) -> dict[str, Any]:
        """评估测试结果"""
        if not test_results:
            return {"success": False, "error": "没有测试结果可评估"}

        # 构建提示词
        all_results = []
        for result_info in test_results:
            test_case = result_info["test_case"]
            result = result_info["result"]

            # 获取验证期望
            validation = test_case.get("validation", {})
            expected_output = validation.get("expected_output", "")
            metrics = validation.get("metrics", [])

            # 获取日志内容和执行结果
            log_content = result.get("log_content", "")
            execution_results = result.get("execution_results", [])
            
            # 整理执行结果信息
            rounds_info = []
            for idx, round_data in enumerate(execution_results):
                round_info = {
                    "round": round_data.get("round", idx + 1),
                    "success": round_data.get("success", False),
                    "output": round_data.get("execution_output", "")[:500] + ("..." if len(round_data.get("execution_output", "")) > 500 else "")
                }
                rounds_info.append(round_info)

            # 整理单个测试结果
            test_result = {
                "purpose": test_case.get("purpose", ""),
                "type": test_case.get("type", ""),
                "expected_output": expected_output,
                "metrics": metrics,
                "success": result.get("success", False),
                "log": log_content[:500] + ("..." if len(log_content) > 500 else ""),  # 限制日志长度
                "rounds": len(execution_results),
                "round_details": rounds_info
            }

            all_results.append(test_result)

        # 将测试结果转为文本
        results_text = json.dumps(all_results, indent=2, ensure_ascii=False)

        prompt = f"""
        请评估以下GitHub仓库测试用例的执行结果，并给出综合评价。

        测试结果：
        {results_text}

        对于每个测试用例，请评估：
        1. 测试是否成功执行
        2. 实际输出是否符合预期输出
        3. 是否满足性能指标
        4. 测试过程中遇到的主要问题和解决方法
        5. 测试执行需要的轮次数
        6. 有什么优点和可改进的地方

        最后，请给出对整个GitHub项目的总体评价，包括：
        1. 功能完整性
        2. 易用性
        3. 性能
        4. 独特创新点
        5. 总结评分（1-10分）

        请以Markdown格式返回评估结果。
        """

        # 创建用户消息
        user_message = BaseMessage.make_user_message(role_name="User", content=prompt)

        # 使用代理模型进行对话
        response = self.agent.step(user_message)

        # 获取评估结果
        evaluation_result = response.msg.content

        logger.info("已完成测试结果评估")

        return {"success": True, "evaluation": evaluation_result, "test_results": test_results}

    def save_evaluation_report(self, test_results: list[dict[str, Any]], evaluation_result: str) -> str:
        """
        保存评估报告

        Returns:
            str: 报告文件路径
        """
        # 创建项目特定的输出目录
        output_dir = Path(f"output/{self.project_name}")
        output_dir.mkdir(parents=True, exist_ok=True)

        # 创建Markdown报告
        markdown = "# GitHub 项目评估报告\n\n"

        # 添加测试用例部分
        markdown += "## 测试用例\n\n"
        for idx, result_info in enumerate(test_results):
            test_case = result_info["test_case"]
            result = result_info["result"]

            # 添加测试用例信息
            markdown += f"### 测试用例 {idx+1}: {test_case.get('purpose', '未知目的')}\n\n"
            markdown += f"- **类型**: {test_case.get('type', '未知')}\n"
            markdown += f"- **执行结果**: {'成功' if result.get('success', False) else '失败'}\n"
            markdown += f"- **轮次**: {result.get('rounds', 1)}\n\n"

            # 添加命令
            if "execution" in test_case and "command" in test_case["execution"]:
                markdown += f"**执行命令**:\n\n```bash\n{test_case['execution']['command']}\n```\n\n"

            # 添加验证信息
            if "validation" in test_case:
                validation = test_case["validation"]
                if "expected_output" in validation:
                    markdown += f"**预期输出**:\n\n```\n{validation['expected_output']}\n```\n\n"
                if "metrics" in validation and validation["metrics"]:
                    markdown += "**性能指标**:\n\n"
                    for metric in validation["metrics"]:
                        markdown += f"- {metric}\n"
                    markdown += "\n"

            # 添加创建的文件信息
            if "created_files" in result and result["created_files"]:
                markdown += "**创建的文件**:\n\n"
                for file_path in result["created_files"]:
                    markdown += f"- `{file_path}`\n"
                markdown += "\n"

            # 添加执行轮次信息
            if "execution_results" in result and result["execution_results"]:
                markdown += "**执行轮次详情**:\n\n"
                for round_idx, round_data in enumerate(result["execution_results"]):
                    markdown += f"#### 轮次 {round_data.get('round', round_idx+1)}\n\n"
                    markdown += f"- **状态**: {'成功' if round_data.get('success', False) else '失败'}\n"
                    
                    # 添加文件信息
                    if "files" in round_data and round_data["files"]:
                        markdown += "- **创建的文件**:\n"
                        for file_path in round_data["files"]:
                            markdown += f"  - `{file_path}`\n"
                    
                    # 添加执行输出
                    if "execution_output" in round_data and round_data["execution_output"]:
                        markdown += "\n**执行输出**:\n\n```\n"
                        # 限制输出长度
                        output = round_data["execution_output"]
                        if len(output) > 1000:
                            output = output[:1000] + "\n...(输出被截断)"
                        markdown += output
                        markdown += "\n```\n\n"

            # 添加实际输出
            if "log_content" in result:
                log_content = result["log_content"]
                markdown += f"**最终输出**:\n\n```\n{log_content[:1000]}\n"
                if len(log_content) > 1000:
                    markdown += "...(输出被截断)\n"
                markdown += "```\n\n"

            # 添加日志文件链接
            if "log_file" in result:
                log_file = result["log_file"]
                markdown += f"**详细日志**: [{os.path.basename(log_file)}]({log_file})\n\n"
                
            # 添加结果文件链接
            if "results_file" in result:
                results_file = result["results_file"]
                markdown += f"**执行结果**: [{os.path.basename(results_file)}]({results_file})\n\n"

            markdown += "---\n\n"

        # 添加评估结果
        markdown += "## 评估结果\n\n"
        markdown += evaluation_result

        # 保存Markdown文件
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = output_dir / f"example_evaluation_{timestamp}.md"

        with open(output_file, "w", encoding="utf-8") as f:
            f.write(markdown)

        logger.info(f"评估报告已保存到 {output_file}")
        return str(output_file)


def test_repo():
    """测试函数，从配置中读取GitHub项目URL，分析并执行评估"""
    # 创建代理实例
    agent = GitHubEvalAgent()

    # 执行评估
    print("\n正在执行测试用例和评估...")
    result = agent.run_evaluation()

    if result["success"]:
        print("\n✅ 评估完成！")
        print(f"\n评估报告已保存到: {result.get('report_path')}")
    else:
        print(f"\n❌ 评估过程出现错误: {result.get('error', '未知错误')}")

    print("\n完成!")


def main():
    test_repo()


if __name__ == "__main__":
    main()
