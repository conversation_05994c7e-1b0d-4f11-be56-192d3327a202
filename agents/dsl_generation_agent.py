from dotenv import load_dotenv

load_dotenv()

import json
import re
from pathlib import Path

from camel.agents import ChatAgent
from camel.models import BaseModelBackend
from camel.toolkits.base import BaseToolkit
from camel.toolkits.function_tool import FunctionTool
from loguru import logger

from utils.create_llm_model import create_model

PROMPT_TEMPLATE = """
分析以下分镜内容，生成 Manim DSL JSON 配置，用于生成视频动画。

请严格按照 Manim DSL v2 Schema，输出完整的 JSON 对象，包含 metadata 和 actions 字段。

<分镜内容>
{storyboard_content}
</分镜内容>

<DSL Schema>
{dsl_schema}
</DSL Schema>

请根据分镜内容，选择合适的动作类型和参数，生成一个完整的 DSL JSON 配置。每一个动作应当体现分镜内容的视觉要求和动画效果。确保生成的JSON完全符合上述Schema格式。

需要注意：
1. **结构与格式**:
   - actions 数组必须包含至少一个动作，严格遵循动作类型文档中的规范
   - 确保生成的JSON格式完全符合Schema，包括所有必需字段和正确的数据类型
   - 每个动作必须包含所有必需参数，可选参数根据实际需要添加

2. **旁白(narration)要求**:
   - 每个action必须包含narration字段，作为当前动作的旁白
   - narration应该严格使用输入中的"narration"字段，不要进行任何修改。

3. **内容展示原则**:
   - 使用animate_markdown等函数显示文本时，内容必须简洁精炼
   - **严格禁止**显示过长文本，文本长度应控制在观众可以轻松阅读的范围内
   - 对于长文本，应提取核心信息，保留关键词和主要观点，忽略次要信息
   - 详细解释和背景信息应放在narration中，而非直接显示在屏幕上
   - 输入中的"分镜内容"字段主要用于narration，不要直接作为文本展示；如果必须展示，尽量简练，不能直接复制。
   - 如果使用了雷达图展示多个维度的信息，不要再用文本内容将每个维度的得分或者理由再展示一遍，而是在雷达图的narration中将必要信息都介绍清楚。

4. **动作设计原则**:
   - 每个分镜中的action应当紧凑且信息量充足，避免内容重复或冗余
   - 将多个简短的相关动作合并为一个完整动作，提高观看体验
   - 动画效果应与内容主题相匹配，增强视觉表现力和教学效果
   - 确保动作之间的过渡自然流畅，维持整体叙事的连贯性

5. **错误修正与优化**:
   - 仔细分析上一次的错误信息，确保所有问题都已被修正
   - 避免重复出现相同的错误，特别是结构性和格式性错误
   - 对生成的JSON进行自检，确保符合所有规则要求

上一次的错误信息，如果不为空，确保其中的错误内容都已被修正：
<previous_error>
{error_context_section}
</previous_error>

输出应仅包含合法的JSON格式，无需其他解释。
"""

# Define the block to insert for errors
ERROR_CONTEXT_BLOCK = "\n<先前尝试错误>\n" "{error_context}\n" "</先前尝试错误>\n" "请根据上述错误修正你的生成结果。\n"

# New prompt template for retries, including error context
RETRY_PROMPT_TEMPLATE = PROMPT_TEMPLATE.replace("{error_context_section}", ERROR_CONTEXT_BLOCK)

# Original prompt template without error context section
PROMPT_TEMPLATE_BASE = PROMPT_TEMPLATE.replace("{error_context_section}", "")


class DSLGenerationToolkit(BaseToolkit):
    """
    Toolkit for generating Manim DSL JSON from storyboard descriptions.
    """

    def generate_dsl_json(
        self,
        storyboard_content: str,
        output_file: str,
        dsl_schema: str,
        seq_num: int = None,
        error_context: str | None = None,
    ) -> str | None:
        """根据给定的分镜描述提示生成DSL JSON

        Args:
            storyboard_content (str): 要生成的内容的描述
            output_file (str): 输出文件路径
            dsl_schema (str): DSL Schema描述
            seq_num (int, optional): 分镜序号，用于标题。默认为None
            error_context (str | None, optional): 上次尝试的错误信息. Defaults to None.


        Returns:
            str | None: 生成的DSL JSON文件路径, or None on failure.
        """
        model: BaseModelBackend = create_model()
        agent = ChatAgent(model=model)

        # Select and format the prompt based on whether there's error context
        if error_context:
            prompt = RETRY_PROMPT_TEMPLATE.format(
                seq_num=seq_num,
                storyboard_content=storyboard_content,
                dsl_schema=dsl_schema,
                error_context=error_context,
            )
        else:
            prompt = PROMPT_TEMPLATE_BASE.format(
                seq_num=seq_num,
                storyboard_content=storyboard_content,
                dsl_schema=dsl_schema,
            )

        # Get response from the agent
        try:
            response = agent.step(prompt)
            if not response or not response.msgs:
                logger.error("Agent did not return a valid response.")
                return None
            response_content = response.msgs[0].content
        except Exception as agent_error:
            logger.error(f"Error during agent step: {agent_error}")
            return None

        # Extract JSON from response
        json_match = re.search(r"```json\n(.*?)\n```", response_content, re.DOTALL)
        if json_match:
            json_str = json_match.group(1)
        else:
            # Try without the json tag
            json_match = re.search(r"```\n(.*?)\n```", response_content, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # Just use the whole response
                json_str = response_content

        try:
            # Parse JSON to ensure it's valid
            dsl_json = json.loads(json_str)

            # --- Enforce and clean the title ---
            target_title = f"Storyboard_{seq_num}"
            logger.info(f"Attempting to set title to: {target_title}")

            # Ensure metadata dictionary exists
            if "metadata" not in dsl_json or not isinstance(dsl_json.get("metadata"), dict):
                dsl_json["metadata"] = {}
                logger.warning("Created missing 'metadata' field in DSL JSON.")
            dsl_json["metadata"]["title"] = target_title
            dsl_json["metadata"]["background_color"] = "BLACK"

            # Write JSON to file
            # Ensure output directory exists
            Path(output_file).parent.mkdir(parents=True, exist_ok=True)
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(dsl_json, f, ensure_ascii=False, indent=2)

            logger.info(f"DSL JSON successfully written to {output_file}")
            return output_file
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON received from LLM: {e}")
            logger.debug(f"Invalid JSON string: {json_str}")
            # Return None instead of raising, so the retry loop can handle it
            return None
        except Exception as e:
            logger.error(f"Error processing generated DSL JSON: {e}")
            return None

    def get_tools(self) -> list[FunctionTool]:
        """Return toolkit functions"""
        return [
            FunctionTool(self.generate_dsl_json),
        ]
