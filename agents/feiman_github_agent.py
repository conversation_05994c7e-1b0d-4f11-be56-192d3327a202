import json
import re
import os
import sys
from datetime import datetime
from dotenv import load_dotenv
import requests
from typing import Dict, Any, Tuple

load_dotenv()
import logging
from loguru import logger

from camel.agents import ChatAgent
from camel.logger import set_log_level
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.types import ModelPlatformType
import yaml

# 设置日志级别
set_log_level(level="WARNING")

# 确保必要的目录存在
os.makedirs("config", exist_ok=True)
os.makedirs("output", exist_ok=True)
os.makedirs("output/explanations", exist_ok=True)

# 知识讲解提示词
KNOWLEDGE_EXPLANATION_PROMPT = """
请基于以下GitHub项目信息，创建一个面向开发者的项目知识讲解内容。内容将用于视频讲解，请确保语言生动、专业且富有教育意义。

{repo_summary}

请按照以下JSON格式输出，包含五个关键部分的讲解内容：

```json
[
    {{
        "分镜名": "知识介绍",
        "分镜内容": "简明扼要地介绍本次视频主题是什么、解决什么问题",
        "素材路径": "null"
    }},
    {{
        "分镜名": "项目概述",
        "分镜内容": "详细介绍示例项目最重要功能或特性,
        "素材路径": "null"
    }},
    {{
        "分镜名": "基本使用",
        "分镜内容": "介绍该项目在实际开发中的简单实用",
        "素材路径": "null"
    }},
    {{
        "分镜名": "扩展使用",
        "分镜内容": "介绍该项目在实际开发中的实际应用",
        "素材路径": "null"
    }},
    {{
        "分镜名": "内容总结",
        "分镜内容": "简单总结本次视频内容，并吸引观众联系我们",
        "素材路径": "null"
    }}
]  
```

请严格按照以上JSON格式输出结果，同时确保满足以下要求：
介绍思路是从某个领域开始，然后介绍这个领域中的一个项目，然后介绍这个项目的基本使用，然后介绍这个项目的扩展使用，最后进行总结，不同模块之间注意逻辑衔接。
1. 知识介绍：30-40字，简单明了介绍项目所属领域的介绍，例如Agent是什么，注意不是项目内容，而是项目背后领域的概念
2. 项目概述：30-40字，介绍示例项目最重要功能或特性，使用专业但易于理解的语言，突出项目核心价值和GitHub上的受欢迎程度
3. 基本使用：75-90字，深入浅出地讲解2个简单例子
4. 扩展使用：75-90字，具体说明项目的实际应用场景和典型用例
5. 内容总结：25-35字，简单总结本次介绍内容，强调还有更多使用方法和技巧，吸引观众联系我们

"""

class KnowledgeExplanationAgent:
    def __init__(self, config_path="config/config.yaml"):
        """初始化知识讲解生成代理"""
        # 加载配置
        self.load_config(config_path)
        
        # 初始化模型
        self.model = self._create_model()
        logging.warning("模型已初始化")
        
        # 初始化代理
        self.agents = self._initialize_agents()
        logging.warning("代理已初始化")
    
    def _create_model(self):
        """根据配置创建模型实例"""
        api_config = self.model_config.get("api", {})

        return ModelFactory.create(
            model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
            model_type=self.model_config.get("type", "openai/gpt-4o-mini"),
            api_key=api_config.get("openai_compatibility_api_key"),
            url=api_config.get("openai_compatibility_api_base_url"),
        )

    def load_config(self, config_path):
        """从YAML文件加载配置"""
        try:
            with open(config_path) as file:
                config = yaml.safe_load(file)
            # 设置配置属性
            self.model_config = config.get("model", {})
            self.repo_config = config.get("repo", {})
            self.agent_config = config.get("agents", {})
        except Exception as e:
            logging.error(f"加载配置出错: {str(e)}")
            raise
    
    def _initialize_agents(self):
        """初始化代理"""
        agents = {}
        
        # 知识讲解生成代理
        agents["knowledge_explanation_generator"] = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="知识讲解生成者",
                content="你负责生成GitHub项目的知识讲解内容，采用教学式的语气，如同一位经验丰富的开发者在分享技术见解。",
            ),
            model=self.model,
        )
        
        return agents
    
    def process_message(self, agent_name, message):
        """处理消息并获取响应"""
        agent = self.agents.get(agent_name)
        if not agent:
            logging.error(f"未找到代理 {agent_name}")
            return f"错误: 未找到代理 {agent_name}"
        
        # 发送消息并获取响应
        response = agent.step(message)
        
        # 获取响应内容
        content = response.msgs[0].content
        
        return content
    
    def generate_knowledge_explanation(self, repo_summary):
        """生成知识讲解内容"""
        print(f"正在为项目生成知识讲解内容...")
        
        # 使用仓库信息格式化提示词
        prompt = KNOWLEDGE_EXPLANATION_PROMPT.format(repo_summary=repo_summary)
        
        # 处理消息
        result = self.process_message("knowledge_explanation_generator", prompt)
        
        # 尝试提取JSON内容
        try:
            json_pattern = r'```json\s*([\s\S]*?)\s*```'
            json_match = re.search(json_pattern, result)
            if json_match:
                result = json_match.group(1)
            
            # 验证JSON格式
            json_data = json.loads(result)
            result = json.dumps(json_data, ensure_ascii=False, indent=4)
        except:
            logging.warning("未能解析为标准JSON格式，保留原始输出")
        
        # 保存结果到文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"output/explanations/knowledge_explanation_{timestamp}.json"
        
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(result)
            
        print(f"知识讲解内容已生成并保存到: {output_file}")
        return result, output_file
    
    def fetch_repo_info(self, repo_url: str) -> Tuple[str, Dict[str, Any]]:
        """获取GitHub仓库信息并使用代理总结功能和热度"""
        print(f"正在获取仓库信息: {repo_url}")
        
        # 从URL中提取所有者和仓库名
        match = re.search(r'github\.com/([^/]+)/([^/]+)', repo_url)
        if not match:
            error_msg = f"无效的GitHub URL: {repo_url}"
            print(error_msg)
            return error_msg, {}
            
        owner, repo = match.groups()
        repo = repo.rstrip('.git')
        
        # 获取仓库基本信息
        try:
            headers = {}
            if github_token := os.getenv("GITHUB_TOKEN"):
                headers["Authorization"] = f"token {github_token}"
                
            repo_response = requests.get(
                f"https://api.github.com/repos/{owner}/{repo}", 
                headers=headers
            )
            repo_response.raise_for_status()
            repo_data = repo_response.json()
            
            # 获取仓库README
            readme_response = requests.get(
                f"https://api.github.com/repos/{owner}/{repo}/readme",
                headers=headers
            )
            readme_content = ""
            if readme_response.status_code == 200:
                import base64
                readme_data = readme_response.json()
                if readme_data.get("content"):
                    readme_content = base64.b64decode(readme_data["content"]).decode("utf-8")
            
            # 整合仓库信息
            repo_info = {
                "name": repo_data.get("name"),
                "full_name": repo_data.get("full_name"),
                "description": repo_data.get("description"),
                "stars": repo_data.get("stargazers_count"),
                "forks": repo_data.get("forks_count"),
                "issues": repo_data.get("open_issues_count"),
                "language": repo_data.get("language"),
                "created_at": repo_data.get("created_at"),
                "updated_at": repo_data.get("updated_at"),
                "readme": readme_content[:4000] if readme_content else ""  # 限制README长度但保留更多内容
            }
            
            # 使用代理总结项目信息
            summary = self._summarize_repo_info(repo_info)
            
            return summary, repo_info
            
        except requests.RequestException as e:
            error_msg = f"获取仓库信息失败: {str(e)}"
            print(error_msg)
            return error_msg, {}
    
    def _summarize_repo_info(self, repo_info: Dict[str, Any]) -> str:
        """使用代理总结仓库信息"""
        # 初始化仓库信息总结代理
        if "repo_summarizer" not in self.agents:
            self.agents["repo_summarizer"] = ChatAgent(
                system_message=BaseMessage.make_assistant_message(
                    role_name="仓库信息总结者",
                    content="你负责深入分析GitHub仓库的功能、特性和技术亮点，提供全面且技术性的项目描述。",
                ),
                model=self.model,
            )
        
        # 构建提示词
        prompt = f"""
        请分析以下GitHub仓库信息，提供一个深入且技术性的项目分析：
        
        仓库名称: {repo_info.get('name')}
        仓库描述: {repo_info.get('description')}
        星标数量: {repo_info.get('stars')}
        分叉数量: {repo_info.get('forks')}
        主要语言: {repo_info.get('language')}
        创建时间: {repo_info.get('created_at')}
        最近更新: {repo_info.get('updated_at')}
        
        README摘要:
        {repo_info.get('readme', '无README信息')}
        
        请提供：
        1. 项目的技术架构和核心组件
        2. 主要功能和技术特点的技术分析
        3. 与行业中其他类似工具的技术对比
        4. 项目的技术优势和创新点
        5. 适用的技术场景和开发环境
        
        回答应详细且技术性强，适合技术讲解和知识分享。
        """
        
        # 处理消息并获取响应
        response = self.agents["repo_summarizer"].step(prompt)
        summary = response.msgs[0].content
        
        # 保存结果到文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"output/explanations/repo_analysis_{timestamp}.md"
        
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(summary)
            
        print(f"仓库技术分析已保存到: {output_file}")
        return summary
    
    def generate_all(self, repo_url=None):
        """生成项目知识讲解内容"""
        # 获取仓库信息（优先使用参数传入的URL，其次从配置中读取）
        if repo_url is None:
            repo_url = self.repo_config.get("url", "")
        
        if not repo_url:
            print("错误：未提供GitHub仓库URL")
            return {
                "error": "未提供GitHub仓库URL",
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        
        # 获取仓库信息
        repo_summary, repo_info = self.fetch_repo_info(repo_url)
        
        # 生成知识讲解内容
        explanation_result, explanation_file = self.generate_knowledge_explanation(repo_summary)
        
        # 返回生成的文件路径
        return {
            "knowledge_explanation": explanation_file,
            "repo_analysis": repo_summary,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

def main():
    """主函数"""
    import argparse
    
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description="GitHub项目知识讲解生成器")
    parser.add_argument("--repo-url", type=str, required=True,
                     help="GitHub仓库URL，用于获取项目信息")
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 初始化知识讲解代理
    agent = KnowledgeExplanationAgent()
    
    # 生成知识讲解内容
    result = agent.generate_all(repo_url=args.repo_url)
    
    print("\n知识讲解内容生成完成！")
    print(f"知识讲解内容: {result.get('knowledge_explanation', '生成失败')}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 