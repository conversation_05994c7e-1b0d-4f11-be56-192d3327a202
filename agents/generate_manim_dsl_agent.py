import argparse
import re
from pathlib import Path
from typing import Optional

from camel.agents import ChatAgent
from camel.messages import BaseMessage
from loguru import logger

from process_markdown import process_markdown_files
from utils.create_llm_model import create_model

MANIM_DSL_PROMPT_TEMPLATE = """
你是一个将Markdown文档转换为Manim DSL脚本的专家。

基于以下Markdown内容，生成一个Manim DSL json脚本，用于创建视频。

Markdown内容:
<input_content>
{markdown_content}
</input_content>

**重要：你必须严格按照以下格式要求输出JSON，任何格式错误都会导致解析失败**

**分镜数量控制要求（最高优先级）**：
- **严格控制分镜总数**：整个视频的分镜数量不得超过8个，目标是5-6个分镜
- **同类型分镜强制合并**：
  * 将相似主题的分镜合并为一个综合分镜
  * 多个技术特点可以合并为一个"核心技术"分镜
  * 多个实验结果可以合并为一个"实验验证"分镜
  * 多个应用场景可以合并为一个"应用前景"分镜
- **优先级排序原则**：
  * 保留最重要的3-4个核心主题作为独立分镜
  * 次要内容必须合并到相关的核心分镜中
  * 每个分镜必须承载足够的信息密度
- **严禁创建单薄分镜**：
  * 不允许只有一句话或一个简单概念的分镜
  * 每个分镜必须包含至少2-3个相关要点
  * 分镜内容必须有足够的深度和广度

**纯文字分镜最小化要求（重要）**：
- **严格限制纯文字分镜数量**：纯文字分镜不得超过总分镜数的30%
- **优先使用视觉元素**：
  * 优先使用图表(animate_chart)替代文字列表
  * 优先使用架构图(animate_architecture_diagram)替代文字描述
  * 优先使用对比图(side_by_side_comparison)替代文字对比
  * 优先使用多媒体素材(animate_video/animate_image)替代文字说明
- **文字分镜合并策略**：
  * 将多个相关的文字内容合并为一个综合分镜
  * 在单个分镜中使用markdown格式展示多个要点
  * 通过narration提供详细解释，减少屏幕文字显示
- **视觉优先原则**：
  * 能用图表展示的数据绝不用文字
  * 能用图片说明的概念绝不用纯文字
  * 能用动画演示的过程绝不用静态描述

**特别警告：严禁描述视频展示形式！**
- 绝对不能在narration中出现"自动录屏"、"录屏展示"、"视频演示"、"页面浏览"等描述展示形式的词汇
- 不能描述"通过录屏"、"通过视频"、"观看展示"等观看过程
- narration必须直接描述技术内容、功能特性、研究成果等实质内容
- 对于GitHub项目录屏，应该描述项目的技术特点、功能亮点，而非录屏过程
- 对于论文页面录屏，应该描述研究成果、技术突破，而非页面浏览过程

**特别警告：严禁创建"视频信息"相关分镜！**
- 绝对不能创建名为"视频信息"、"视频介绍"、"视频概述"、"视频内容"等的分镜
- 不能在分镜内容或narration中描述"这个视频"、"视频展示"、"录屏内容"等
- 所有分镜内容必须直接描述具体的技术、研究成果、功能特性等实质内容
- 视频信息部分（目标受众、时长、意图等）仅作背景参考，绝对不能出现在storyboard中

**特别警告：严禁编造机构名和数字！**
- 开篇中提到的任何机构名（如MIT、Google、微软等）必须在Markdown正文中实际出现
- 开篇中的任何量化数字（如速度提升、准确率、Stars数量等）必须基于Markdown正文中的真实数据
- 严禁为了吸引人而编造不存在的机构背景或虚假的性能数据
- 如果Markdown中没有突出的数字或知名机构，则重点突出技术特色，不编造虚假信息

## JSON格式严格要求：
1. **只返回纯JSON代码，不要包含```json```代码块标记**
2. **绝对禁止使用任何中文标点符号**：
   - 禁止使用：""''：；，。！？
   - 只能使用英文标点：""'':;,.!?
3. **所有属性名必须用英文双引号包围**
4. **所有字符串值必须用英文双引号包围**
5. **不要在JSON中使用换行符，所有内容必须在一行内**
6. **确保JSON结构完整，所有括号和引号都正确匹配**

## 输出示例格式：
[{"分镜内容": "这里是内容描述", "视觉动效建议": [{"type": "animate_markdown", "content": "这里是显示内容", "narration": "这里是旁白内容"}]},...]

注意以下要求：
1. 生成的dsl脚本不需要包含所有细节。根据输入内容，提取最核心的内容即可，需要尽量浅显易懂，并且让观众对输入内容有一个相对完整的了解。
2. 请参考以下的Manim DSL介绍，为每个分镜设计最合适的内容和介绍，选择最合适的DSL函数，输出符合要求的dsl json配置

<dsl_reference>
{dsl_reference}
</dsl_reference>

以下是一个参考输出示例：
<dsl_example>
{dsl_example}
</dsl_example>

请根据分镜内容，选择合适的动作类型和参数，生成一个完整的 DSL JSON 配置。每一个动作应当体现分镜内容的视觉要求和动画效果。确保生成的JSON完全符合上述Schema格式。

需要注意：
1. **结构与格式**:
   - actions 数组必须包含至少一个动作，严格遵循动作类型文档中的规范
   - 确保生成的JSON格式完全符合Schema，包括所有必需字段和正确的数据类型
   - 每个动作必须包含所有必需参数，可选参数根据实际需要添加

2. **旁白(narration)要求**:
   - **narration字数严格限制在150个字以内**，确保语音播放时长适中
   - **开篇分镜的narration更加简洁，限制在50个字以内**，快速抓住观众注意力
   - narration应结合当前分镜的内容要点进行优化，确保重点突出、内容完整连贯
   - 旁白应具有教育性和解释性，不仅仅是描述画面内容，而是要深入解释概念和原理
   - 在字数限制内，优先包含最核心的信息和关键概念
   - 旁白语言应简洁明了，同时保持专业性和准确性
   - 避免冗长的描述，每句话都要有价值和意义

2. **内容质量与表达要求**:
   - **narration字数严格限制**：普通分镜150字以内，开篇分镜50字以内
   - **信息密度优先**：每句话都要传达有价值的信息，避免空洞表述
   - **朴实直接的表达风格**：
     * **严格避免震惊体和夸张表述**：禁用"震惊！"、"你知道吗？"、"以惊人速度席卷"、"彻底改变"、"史上最强"等夸张词汇
     * **避免无意义的感叹和修饰**：不使用"真的很棒！"、"简直太好了！"、"毫无疑问"等空洞表达
     * **减少冗余的过渡词**：避免"那么，"、"接下来，"、"让我们一起"等无实际意义的过渡
     * **杜绝空洞描述**：不使用"具有...的特点"、"发挥...的作用"等模糊表述
   - **具体化和量化表达**：
     * 用具体数据替代模糊描述：用"1000+ Stars"替代"很多星标"
     * 用准确词汇替代夸张形容：用"快速"替代"超快"、"优秀"替代"非常好"
     * 直接说明功能和结果：用"可以"替代"能够帮助...实现"
   - **专业性与准确性**：
     * 使用准确的技术术语，避免过度简化或夸大
     * 每个概念都要有清晰的定义和解释
     * 确保事实准确，数据可靠
   - **教育性导向**：
     * 旁白应深入解释概念和原理，不仅仅描述画面
     * 在字数限制内优先包含最核心的信息和关键概念
     * 每句话都要对观众的理解有实际帮助

3. **多媒体素材处理原则（重要）**:
   - **选择性使用多媒体素材**：不需要使用所有检测到的多媒体素材，只选择最重要、最有代表性的素材
   - **素材重要性评估标准**：
     * 架构图、系统图、流程图 > 普通截图
     * 实验结果图表、性能对比图 > 装饰性图片
     * 核心功能演示视频 > 简单操作视频
     * 与核心技术直接相关的素材 > 辅助说明素材
   - **开篇多媒体要求保持不变**：开篇分镜仍然优先使用最重要的多媒体素材
   - **如果分镜中包含多媒体素材（视频、GIF、图片等），该分镜的"视觉动效建议"数组中只能包含一个action**
   - **多媒体素材唯一性原则（严格限制）**：
     * **每个多媒体素材只能使用一次**：同一个图片、视频或GIF文件在整个分镜序列中只能出现在一个分镜中
     * **严禁重复使用素材**：不能在多个分镜中使用相同的文件路径或URL
     * **素材分配优先级**：将多媒体素材分配给最重要、最相关的分镜，其他分镜使用文字或图表展示
     * **合理舍弃次要素材**：如果素材数量过多，优先保留核心技术相关的素材，舍弃装饰性或重复性素材
   - **多媒体素材为分镜主体**：
     * 分镜必须以多媒体素材为核心，所有讲解内容围绕该素材展开
     * 严禁在多媒体分镜中添加其他函数（如animate_markdown等）
     * 该分镜的唯一目的是展示和讲解多媒体素材
   - **必须优先使用专门的多媒体展示函数**：
     * 视频素材：使用 `animate_video` 函数
     * GIF动图：使用 `animate_video` 函数
     * 图片素材：使用 `animate_image` 函数
   - **视频素材overlay_text要求（新增）**：
     * **所有视频素材（包括录屏、demo视频、GIF等）必须提供overlay_text参数**
     * **overlay_text必须包含3-5个关键词，用于视频播放时的文字叠加显示**
     * **关键词要求**：简洁明了，每个关键词2-4个字，突出核心功能或特点
     * **关键词格式**：使用数组格式，如["AI助手", "智能分析", "高效处理", "开源项目"]
     * **关键词内容来源**：必须基于Markdown正文中的真实功能和特点，严禁编造
   - **围绕多媒体素材展开讲解**：
     * 将所有相关的文字说明、关键词、要点都整合到该多媒体函数的参数中
     * 在 `annotation` 参数中添加关键词和要点提示
     * 在 `narration` 中提供完整的讲解，涵盖该分镜的所有内容
     * 不要再添加额外的 `animate_markdown` 或其他函数
   - **annotation格式处理原则**：
     * **统一markdown格式**：所有annotation内容都使用markdown格式，包括标题、列表、粗体等
     * **统一长度限制**：所有annotation内容都限制在150字符以内，确保显示效果最佳
     * **自动格式优化**：系统会自动优化markdown格式，确保列表、标题、粗体等格式正确
     * **智能截断**：超长内容会按行智能截断，优先保留完整的信息点
   - **多媒体素材路径处理**：
     * 直接使用Markdown中提供的完整路径，不要修改
     * 如果路径是相对路径，保持原样
     * 确保路径格式正确，支持常见的图片、视频、GIF格式

   - **GitHub项目特有多媒体元素优先使用（重要新增）**：
     * **架构流程图优先使用animate_architecture_diagram**：
       - 当Markdown中包含架构图、流程图、系统设计图时，优先使用`animate_architecture_diagram`函数
       - 支持的图表类型：系统架构图、模块关系图、数据流图、工作流程图等
       - 图表路径格式：支持.png、.jpg、.svg等格式的架构图文件
       - 必须提供diagram_title（图表标题）和detailed_explanation（详细解释）参数
       - narration应重点解释架构的核心设计理念和技术优势
     * **项目评估雷达图优先使用animate_chart(radar)**：
       - 当Markdown中包含项目评估、质量分析、多维度对比时，优先使用雷达图展示
       - 评估维度示例：代码质量、文档完备性、社区活跃度、技术创新性、易用性、可维护性
       - 使用格式：`{"type": "animate_chart", "chart_type": "radar", "data": {...}, "title": "项目质量评估"}`
       - 数据来源必须基于Markdown中的真实评估内容，严禁编造分数
       - narration应解释各维度的评估标准和项目表现
     * **星标增长图表优先使用animate_chart(line/bar)**：
       - 当Markdown中包含GitHub Stars数据、增长趋势、社区热度时，优先使用图表展示
       - 支持的图表类型：折线图（趋势）、柱状图（对比）、面积图（累积）
       - 使用格式：`{"type": "animate_chart", "chart_type": "line", "data": {...}, "title": "GitHub Stars增长趋势"}`
       - 数据必须来源于Markdown中的真实Stars统计，不能编造虚假数据
       - narration应分析增长趋势背后的技术价值和社区认可度
     * **技术对比图表优先使用animate_chart(bar/comparison)**：
       - 当Markdown中包含性能对比、技术指标、基准测试时，优先使用对比图表
       - 对比维度示例：性能指标、准确率、处理速度、内存占用、兼容性等
       - 使用格式：`{"type": "animate_chart", "chart_type": "bar", "data": {...}, "title": "性能对比分析"}`
       - 所有对比数据必须基于Markdown中的真实测试结果
       - narration应突出技术优势和实际应用价值
     * **GitHub特有元素检测与提取**：
       - 自动识别Markdown中的Stars数量、Fork数量、Issues数量、Contributors数量
       - 提取项目的技术栈信息、依赖关系、架构设计说明
       - 识别性能测试结果、基准对比数据、用户反馈统计
       - 发现项目评估维度和质量分析内容
       - 检测代码示例、API文档、使用教程等技术内容
     * **多媒体元素组合使用策略**：
       - **架构图+雷达图组合**：先展示系统架构，再评估设计质量
       - **Stars图表+技术对比组合**：先展示社区热度，再分析技术优势
       - **流程图+代码统计组合**：先展示工作流程，再分析实现技术
       - 组合使用时确保逻辑连贯，避免重复信息
       - 每个图表都要有独立的教育价值和信息密度

4. **内容展示原则**:
   - 使用animate_markdown等函数显示文本时，内容必须简洁精炼
   - **严格禁止**显示过长文本，文本长度应控制在观众可以轻松阅读的范围内（每行不超过15个字符，总共不超过5行）
   - 对于长文本，应提取核心信息，保留关键词和主要观点，忽略次要信息
   - 详细解释和背景信息应放在narration中，而非直接显示在屏幕上
   - 如果使用了雷达图展示多个维度的信息，不要再用文本内容将每个维度的得分或者理由再展示一遍，而是在雷达图的narration中将必要信息都介绍清楚

5. **动作设计原则**:
   - **分镜数量严格控制（最高优先级）**：
     * **总分镜数不超过8个**，理想情况下控制在5-6个分镜
     * **每3分钟视频对应约5个分镜**，确保每个分镜有足够的展示时间
     * **同类型内容强制合并**：技术特点、实验结果、应用场景等相似内容必须合并为单一分镜
     * **信息密度最大化**：每个分镜必须包含丰富的内容，避免信息稀薄的分镜
   - **纯文字分镜最小化（重要）**：
     * **纯文字分镜占比不超过30%**：在5个分镜中，最多只能有1-2个纯文字分镜
     * **优先使用图表和视觉元素**：数据用图表，架构用图示，对比用表格
     * **文字内容高度浓缩**：如果必须使用文字，确保信息密度极高
   - **分镜合并策略**：
     * **技术特点合并**：将多个技术优势合并为"核心技术创新"分镜
     * **实验数据合并**：将性能测试、对比实验合并为"实验验证"分镜  
     * **应用展示合并**：将使用场景、效果演示合并为"应用前景"分镜
     * **背景介绍合并**：将问题背景、研究动机合并到开篇分镜中
   - **信息密度优化**：每个分镜必须包含足够的信息量，避免单一简单内容（如只有一行文本）
   - **GitHub项目特有图表优先使用（重要新增）**：
     * **积极使用animate_architecture_diagram**：当涉及系统架构、模块关系、工作流程时，优先使用架构图动画而非静态图片
     * **积极使用animate_chart雷达图**：当涉及项目评估、质量分析、多维度对比时，优先使用雷达图展示而非文字列表
     * **积极使用animate_chart星标图表**：当涉及GitHub数据、社区热度、增长趋势时，优先使用动态图表而非静态数字
     * **积极使用animate_chart性能对比**：当涉及技术指标、基准测试、效果对比时，优先使用柱状图或折线图而非表格
     * **图表优于文字原则**：能用图表展示的数据和关系，绝对不要用纯文字描述，图表具有更强的视觉冲击力和教育效果
     * **动态优于静态原则**：能用animate_chart展示的数据，优先使用动态图表而非display_image显示静态图片
   - **强制分镜合并原则（重要）**：
     * **文案分镜与多媒体素材强制合并**：如果存在纯文案分镜（如简介、背景、概述等）且附近有多媒体素材，必须将文案内容合并到多媒体分镜中
     * **避免冗余文案分镜**：严禁创建单独的"项目介绍"、"技术背景"、"功能概述"等纯文字分镜，这些内容应该整合到多媒体展示中
     * **多媒体素材承载更多信息**：每个多媒体分镜应该承载尽可能多的相关信息，通过annotation和narration来展现
     * **减少分镜总数**：优先考虑内容整合，避免过度拆分成多个简单分镜
   - **Star信息简化处理（重要）**：
     * **如果必须创建Star相关分镜，描述必须极简**：只能包含核心数据，如"GitHub 15K⭐"、"2周+2K星"等简洁表述
     * **避免冗长的Star描述**：严禁使用"项目在GitHub上获得了广泛关注"、"开发者社区反响热烈"等冗长表述
     * **Star数据直接呈现**：用图表或简洁文字直接显示数据，不做过多解释
     * **尽量整合到其他分镜**：优先将Star信息整合到项目介绍或功能展示分镜中，避免单独成镜
   - 将相关内容组合在同一个分镜中，而不是分散成多个简单分镜
   - 对于简单概念，考虑使用图表、对比或其他视觉元素丰富呈现，避免纯文本
   - 每个分镜至少应包含2-3个关键信息点，确保信息密度适中
   - **多媒体素材分镜例外**：如果分镜包含多媒体素材，则该分镜专注于该素材的展示和讲解，不需要额外的信息点，严禁添加其他函数
   - 善用组合动作：如在一个分镜中结合文本说明和图表/代码展示，或使用side_by_side_comparison进行对比（但不适用于多媒体素材分镜）
   - 动画效果应与内容主题相匹配，增强视觉表现力和教学效果
   - 文本与视觉元素（图表、代码、图片等）应适当搭配，避免纯文本堆砌

6. **辅助信息**:
   - **严禁将"视频信息"内容用于storyboard**：输入中的"视频信息"部分（包括目标受众、视频时长、核心意图、表达风格等）仅作为背景参考，**绝对不能**出现在分镜内容、narration、annotation等任何storyboard元素中
   - **严禁将"视频标题"用于storyboard**：视频标题仅作为指导参考，不能直接出现在narration或分镜内容中
   - **严禁创建"视频信息"分镜**：绝对不能创建名为"视频信息"、"视频介绍"、"视频概述"、"视频内容"等的分镜
   - **严禁描述视频本身**：不能在分镜内容或narration中出现"这个视频"、"视频展示"、"视频演示"、"录屏内容"等描述
   - **narration内容来源限制**：narration只能基于Markdown正文内容生成，不能包含视频信息中的任何描述性内容
   - **录屏介绍文案要求**：对于录屏视频的介绍，只需使用开篇吸引人的表达方式即可，不需要复述视频信息中的内容
   - **开篇内容要求**：
     * **严禁无意义描述**：不能出现"今天给大家带来XX论文页面的自动录屏浏览"、"让我们一起来看看"等无信息量的话术
     * **必须包含实质内容**：开篇要直接介绍研究成果、效果数据、权威机构等有价值信息
     * **突出核心价值**：用震惊体表达研究的突破性成果、性能提升、权威背景等
   - **内容密度要求**：生成的内容尽量简洁，信息密度高，整体不要太冗长，单个分镜或者单个action的信息也要尽量充实，不要只出现一个标题等
   - **严格内容边界**：确保所有分镜内容都来自Markdown正文，而非视频信息部分

8. **开篇分镜优化原则（重要）**:
   - **多媒体素材优先开篇**：
     * **如果Markdown内容中存在多媒体素材，必须选择最重要的素材作为第一个分镜开篇**
     * **素材重要性排序**：录屏视频 > 架构图/系统图 > 核心功能截图 > 实验结果图 > 其他图片
     * **不是所有素材都要使用**：只选择最能代表项目/论文核心价值的素材用于开篇
     * **严禁创建单独的封面标题或纯文本开篇分镜**，直接进入多媒体素材的展示和讲解
     * **多媒体开篇分镜必须包含开篇介绍内容**，在多媒体展示的同时提供吸引人的介绍
     * **文案信息强制整合**：所有项目背景、技术介绍、核心特点等文案信息必须整合到多媒体开篇分镜中
   - **星标信息处理规则（重要）**：
     * **严禁为星标信息单独创建分镜**：星标数量、增长情况等信息不能作为独立的分镜内容
     * **星标信息只在开篇文案中提及**：将星标数据融入到开篇分镜的narration中，作为项目受欢迎程度的证明
     * **星标表述方式极简化**：如"GitHub 12K⭐"、"热门项目"等，融入项目介绍中，避免冗长描述
     * **避免星标分镜**：绝对不能创建"项目热度"、"社区反响"、"GitHub表现"等以星标为主题的分镜
     * **如果必须单独展示Star信息，使用最简描述**：仅显示核心数据和简单图表，narration控制在30字以内
   - **多媒体开篇处理方式**：
     * **视频素材开篇（最高优先级）**：直接使用`animate_video`函数展示录屏或demo视频，在`narration`中融入开篇介绍和星标信息
     * **架构图开篇（高优先级）**：直接使用`animate_image`或`animate_architecture_diagram`函数展示系统架构，在`narration`中融入开篇介绍和星标信息
     * **重要图片开篇（中优先级）**：直接使用`animate_image`函数展示核心功能截图或实验结果，在`narration`中融入开篇介绍和星标信息
     * **GIF动图开篇（中优先级）**：直接使用`animate_video`函数展示动态演示，在`narration`中融入开篇介绍和星标信息
   - **开篇narration设计**：
     * **结合多媒体内容的开篇narration**：将项目介绍、背景信息、核心价值、星标数据等整合到多媒体素材的讲解中
     * **震惊体开头**：如"震惊！"、"不敢相信！"、"太厉害了！"，但必须与多媒体内容相关
     * **权威机构**：**必须使用Markdown正文中实际出现的机构名**，如Google、微软、MIT、斯坦福等，严禁编造不存在的机构
     * **量化效果**：**必须使用Markdown正文中的真实数据**，如实际的速度提升、效率数据、准确率、星标数量等，严禁编造虚假数字
     * **热点问题**：AI、自动化、性能优化、科学发现等当前热门领域
   - **开篇narration必须简洁有力**，严格限制在50字以内，每个字都要有冲击力
   - **无重要多媒体素材时的处理**：
     * 只有在Markdown内容中完全没有重要多媒体素材时，才可以创建传统的文案开篇分镜
     * 此时仍需遵循震惊体、权威机构、量化效果、星标信息等开篇原则
     * 星标信息同样只在开篇narration中提及，不单独成镜
   - **严禁无意义开篇**：
     * 禁止使用"今天给大家带来XX论文/项目的介绍"、"让我们一起来看看"等无信息量话术
     * 禁止描述录屏过程本身，如"自动录屏浏览"、"页面展示"等
     * 必须直接切入核心价值和突破性成果
   - **开篇内容真实性要求**：
     * **机构名验证**：开篇中提到的任何机构名（如MIT、Google、微软等）必须在Markdown正文中实际出现
     * **数字验证**：开篇中的任何量化数字（如速度快100倍、效率提升300%、星标数量等）必须基于Markdown正文中的真实数据
     * **严禁编造**：绝对不能为了吸引人而编造不存在的机构背景或虚假的性能数据
     * **优先使用真实亮点**：如果Markdown中有真实的亮点数据，优先使用；如果没有突出数据，则重点突出技术特色而非编造数字
   - **多媒体开篇示例**（基于真实素材，包含星标信息）：
     * 录屏视频开篇："震惊！MIT最新研究破万星标，准确率提升30%！"（星标和技术成果并重）
     * 架构图开篇："OpenAI全新架构设计，2周破5K星标，速度提升5倍！"（星标增长和性能提升并重）
     * Demo GIF开篇："Google开源项目实时演示，GitHub狂砍1万星标！"（项目权威性和受欢迎程度并重）

**多媒体素材处理示例**：
如果分镜包含一个论文录屏视频，正确的处理方式：
```json
{
  "分镜内容": "论文介绍",
  "视觉动效建议": [
    {
      "type": "animate_video",
      "params": {
        "video_path": "output/paper_id/paper_screen.mp4",
        "overlay_text": ["AI研究", "深度学习", "算法优化", "性能突破"],
        "narration": "基于Markdown正文中的真实内容生成吸引人的技术介绍！"
      }
    }
  ]
}
```

如果分镜包含一个GitHub项目录屏，正确的处理方式：
```json
{
  "分镜内容": "项目功能展示",
  "视觉动效建议": [
    {
      "type": "animate_video",
      "params": {
        "video_path": "output/project_name/github_screen.mp4",
        "overlay_text": ["开源项目", "智能工具", "高效开发", "社区热门"],
        "narration": "深入了解项目的核心架构和创新功能，体验其强大的技术实力！"
      }
    }
  ]
}
```

如果分镜包含一个GIF动图，正确的处理方式：
```json
{
  "分镜内容": "功能演示",
  "视觉动效建议": [
    {
      "type": "animate_video",
      "params": {
        "video_path": "images/demo.gif",
        "overlay_text": ["实时演示", "功能展示", "交互体验"],
        "narration": "基于Markdown中真实功能特性的震惊体表达！"
      }
    }
  ]
}
```

**长文本annotation示例**（当内容较多时使用markdown格式）：
```json
{
  "分镜内容": "详细技术架构介绍",
  "视觉动效建议": [
    {
      "type": "animate_image",
      "params": {
        "content": "output/project/architecture.png",
        "annotation": "## 核心架构\n\n- **两阶段解析**\n  - 页面布局分析\n  - 元素内容解析\n- **技术优势**\n  - 精准识别元素\n  - 高精度抽取",
        "narration": "Dolphin采用独创的两阶段解析架构，首先进行页面布局分析，然后进行元素内容解析，实现了SOTA级别的文档理解能力。"
      }
    }
  ]
}
```

**注意**：上述示例中的annotation和narration内容必须基于Markdown正文中的真实信息，不能编造机构名或数字。

**错误示例**（包含多媒体素材但使用了多个action）：
```json
{
  "分镜内容": "GitHub项目介绍",
  "视觉动效建议": [
    {
      "type": "animate_video",
      "params": {
        "video_path": "output/project_name/github_screen.mp4"
      }
    },
    {
      "type": "animate_markdown",
      "params": {
        "content": "项目特点:\n- 1.2k Stars\n- Python开发"
      }
    }
  ]
}
```

**错误原因**：
1. 多媒体分镜使用了多个action（违反单一action原则）
2. 在多媒体分镜中添加了animate_markdown函数（违反多媒体为主体原则）
3. 应该将所有信息整合到animate_video的annotation和narration参数中

**严重错误示例**（绝对禁止的"视频信息"分镜）：
```json
{
  "分镜内容": "视频信息",
  "视觉动效建议": [
    {
      "type": "animate_markdown",
      "params": {
        "content": "目标受众: 开发者\n视频时长: 5分钟\n核心意图: 介绍新技术",
        "narration": "这个视频主要面向开发者，时长约5分钟，旨在介绍最新的技术突破。"
      }
    }
  ]
}
```

**严重错误原因**：
1. **创建了"视频信息"分镜**：这是元信息，绝对不应出现在storyboard中
2. **使用了视频信息内容**：目标受众、视频时长、核心意图等都是背景信息，不能用于分镜
3. **描述视频本身**：narration中出现了"这个视频"等描述视频本身的内容
4. **违反内容来源限制**：使用了非Markdown正文的内容

**再次强调：请直接输出JSON数组，不要包含任何代码块标记或其他文本！**

**内容质量自检清单**：
在生成narration内容时，请确保：

**内容来源检查**：
✓ **严禁使用视频信息内容**：不能在narration中包含目标受众、视频时长、核心意图、表达风格等视频信息部分的内容
✓ **严禁使用视频标题**：不能直接在narration或分镜内容中使用视频标题
✓ **严禁创建"视频信息"分镜**：绝对不能创建名为"视频信息"、"视频介绍"、"视频概述"、"视频内容"等的分镜
✓ **严禁描述视频本身**：不能在分镜内容或narration中出现"这个视频"、"视频展示"、"视频演示"、"录屏内容"等描述
✓ **严禁描述展示形式**：不能出现"自动录屏"、"录屏展示"、"页面浏览"、"通过录屏"、"通过视频"、"观看展示"等词汇
✓ **只使用Markdown正文**：narration内容必须完全基于Markdown正文内容生成
✓ **录屏介绍简化**：录屏视频介绍只需开篇吸引人表达，不复述视频信息
✓ **严禁无意义描述**：不能出现"今天给大家带来XX"、"让我们一起来看看"、"自动录屏浏览"等无信息量话术
✓ **直接描述技术内容**：分镜内容必须直接描述具体的技术、研究成果、功能特性等实质内容，而非描述展示形式

**开篇分镜特殊要求**：
✓ **必须使用吸引人的表达**：鼓励使用"震惊！"、"不敢相信！"、"太强了！"等震惊体
✓ **机构名真实性验证**：开篇中提到的任何机构名必须在Markdown正文中实际出现，严禁编造
✓ **数字真实性验证**：开篇中的任何量化数字必须基于Markdown正文中的真实数据，严禁编造
✓ **突出量化数字**：优先使用Markdown中的真实数据，如"2周破1000 Stars！"、"准确率95%！"等
✓ **强调权威背书**：只能使用Markdown中实际出现的机构名，如"微软官方！"、"Google团队！"等
✓ **使用疑问句引入**：用"你知道吗？"、"还在烦恼吗？"等疑问句吸引注意
✓ **营造紧迫感**：用"刚刚发布！"、"最新突破！"等词汇增加时效性
✓ **字数控制**：开篇分镜≤15字，每个字都要有冲击力
✓ **避免编造内容**：如果Markdown中没有突出的数字或机构，则重点突出技术特色，不编造虚假信息

**普通分镜要求**：
✓ 避免使用震惊体："震惊！"、"你知道吗？"、"席卷全球"等
✓ 避免空洞修饰："真的很棒"、"简直太好了"、"毫无疑问"等
✓ 避免冗余过渡："那么，"、"接下来，"、"让我们一起"等
✓ 避免模糊表述："具有...特点"、"发挥...作用"等
✓ 使用具体数据：用"1000+ Stars"替代"很多星标"
✓ 使用准确词汇：用"快速"替代"超快"、"优秀"替代"非常好"
✓ 直接表达结果：用"可以"替代"能够帮助...实现"
✓ 确保每句话都传达有价值的信息
✓ 保持专业性和准确性
✓ 字数控制：普通分镜≤150字

**GitHub项目特有图表使用指导（重要新增）**：
✓ **积极使用animate_architecture_diagram**：当Markdown中涉及系统架构、模块关系、工作流程时，优先使用架构图动画展示
✓ **积极使用animate_chart雷达图**：当涉及项目评估、质量分析、多维度对比时，优先使用雷达图而非文字列表
✓ **积极使用animate_chart星标图表**：当涉及GitHub数据、社区热度、增长趋势时，优先使用动态图表展示
✓ **积极使用animate_chart性能对比**：当涉及技术指标、基准测试、效果对比时，优先使用柱状图或折线图
✓ **积极使用animate_chart代码统计**：当涉及技术栈、语言分布、模块占比时，优先使用饼图或环形图
✓ **图表优于文字原则**：能用图表展示的数据和关系，绝对不要用纯文字描述，图表具有更强的视觉冲击力
✓ **动态优于静态原则**：能用animate_chart展示的数据，优先使用动态图表而非display_image显示静态图片
✓ **数据真实性要求**：所有图表数据必须基于Markdown正文中的真实信息，严禁编造虚假数据
✓ **图表教育价值**：每个图表都要有独立的教育价值和信息密度，通过narration深入解释数据背后的意义

**再次强调：请直接输出JSON数组，不要包含任何代码块标记或其他文本！**

**多媒体开篇处理方式**：
 * **视频素材开篇**：直接使用`animate_video`函数展示录屏或demo视频，在`narration`中融入开篇介绍和星标信息
 * **图片素材开篇**：直接使用`animate_image`函数展示架构图或功能截图，在`narration`中融入开篇介绍和星标信息
 * **GIF动图开篇**：直接使用`animate_video`函数展示动态演示，在`narration`中融入开篇介绍和星标信息
 * **GitHub项目特有开篇方式（重要新增）**：
   - **架构图开篇**：优先使用`animate_architecture_diagram`展示系统架构，narration融入项目背景和技术亮点
   - **Stars图表开篇**：使用`animate_chart`展示GitHub热度趋势，narration强调社区认可和技术价值
   - **雷达图开篇**：使用`animate_chart(radar)`展示项目质量评估，narration突出多维度优势
   - **性能对比开篇**：使用`animate_chart(bar)`展示技术指标对比，narration强调性能突破
   - **代码统计开篇**：使用`animate_chart(pie)`展示技术栈分布，narration介绍技术选型优势
   - **组合开篇策略**：架构图+Stars数据、雷达图+性能对比等，提供更丰富的开篇体验
"""


class ManimDSLGeneratorAgent:
    """A simple agent to generate Manim DSL from Markdown content."""

    def __init__(self):
        """Initializes the agent with a specified LLM."""
        self.model = create_model()

        # System message defines the agent's role
        system_message = BaseMessage.make_assistant_message(
            role_name="Manim DSL Generator",
            content="You are an expert in converting Markdown text into Manim DSL json.",
        )
        self.agent = ChatAgent(system_message=system_message, model=self.model)

    def generate_dsl(self, prompt: str) -> str:
        """Generates Manim DSL using the LLM based on the markdown and prompt."""
        import json

        logger.info("Generating Manim DSL...")

        # 提取Markdown内容进行多媒体检测
        multimedia_detected = False
        multimedia_info = {}

        # 从prompt中提取markdown内容
        markdown_content = ""
        if "<input_content>" in prompt and "</input_content>" in prompt:
            start_idx = prompt.find("<input_content>") + len("<input_content>")
            end_idx = prompt.find("</input_content>")
            markdown_content = prompt[start_idx:end_idx].strip()

        # 检测多媒体素材
        if markdown_content:
            multimedia_info = self._extract_multimedia_info_from_markdown(markdown_content)
            has_videos = len(multimedia_info.get("videos", [])) > 0
            has_images = len(multimedia_info.get("images", [])) > 0
            has_gifs = len(multimedia_info.get("gifs", [])) > 0
            multimedia_detected = has_videos or has_images or has_gifs

            if multimedia_detected:
                logger.info(
                    f"检测到多媒体素材: 视频={len(multimedia_info.get('videos', []))}, 图片={len(multimedia_info.get('images', []))}, GIF={len(multimedia_info.get('gifs', []))}"
                )
                logger.info("将优先使用多媒体素材作为开篇，不创建单独的封面标题分镜")
            else:
                logger.info("未检测到多媒体素材，将使用传统的文案开篇")

        user_message = BaseMessage.make_user_message(role_name="User", content=prompt)

        self.agent.reset()
        response = self.agent.step(user_message)

        if not response.msgs:
            logger.error("Agent returned no response.")
            return "# Error: Agent returned no response"

        generated_content = response.msgs[0].content
        logger.info("Raw response received from LLM.")

        # 详细记录原始响应
        logger.info(f"🔍 LLM原始响应长度: {len(generated_content)} 字符")
        logger.info(f"🔍 LLM原始响应前500字符: {generated_content[:500]}")
        logger.info(f"🔍 LLM原始响应后500字符: {generated_content[-500:]}")

        # 首先尝试改进的JSON解析
        logger.info("🔄 开始尝试改进的JSON解析...")
        improved_result = self._try_parse_raw_json(generated_content)
        if improved_result:
            logger.info("✅ 改进的JSON解析成功")
            return improved_result

        logger.warning("❌ 改进的JSON解析失败，继续使用原有逻辑作为后备")

        # 如果改进解析失败，继续使用原有逻辑作为后备
        # Try to parse the JSON directly first (fastest path)
        logger.info("🔄 尝试直接解析JSON...")
        try:
            parsed_json = json.loads(generated_content)
            logger.info("✅ JSON直接解析成功")
            logger.info(f"🔍 解析后的JSON类型: {type(parsed_json)}")
            
            if isinstance(parsed_json, list):
                logger.info(f"🔍 JSON数组长度: {len(parsed_json)}")
                for i, item in enumerate(parsed_json):
                    logger.info(f"🔍 数组元素{i}: 类型={type(item)}, 键={list(item.keys()) if isinstance(item, dict) else 'N/A'}")
            elif isinstance(parsed_json, dict):
                logger.info(f"🔍 JSON字典键: {list(parsed_json.keys())}")

            # 验证JSON格式是否正确
            if not isinstance(parsed_json, list):
                logger.error(f"❌ JSON格式错误：期望列表，实际得到 {type(parsed_json)}")
                logger.error(f"🔍 内容预览: {str(parsed_json)[:200]}")
                return self._get_fallback_json()
            
            # 检查列表中的元素是否都是字典
            valid_scenes = []
            for i, scene in enumerate(parsed_json):
                if isinstance(scene, dict):
                    logger.info(f"✅ 分镜{i+1}有效: {scene.get('分镜内容', 'NO_CONTENT')[:50]}")
                    valid_scenes.append(scene)
                else:
                    logger.warning(f"⚠️ 跳过第{i}个非字典元素: {type(scene)} - {str(scene)[:100]}")
            
            logger.info(f"🔍 有效分镜总数: {len(valid_scenes)}")
            
            if not valid_scenes:
                logger.error("❌ JSON中没有有效的分镜字典，返回默认结构")
                return self._get_fallback_json()

            # 分镜数量验证
            if len(valid_scenes) < 5:
                logger.error(f"❌ 分镜数量不足: 只有{len(valid_scenes)}个分镜，要求至少5个")
                logger.error("🔍 这可能是LLM没有按照prompt要求生成足够的分镜")
                for i, scene in enumerate(valid_scenes):
                    logger.error(f"🔍 分镜{i+1}: {scene.get('分镜内容', 'NO_CONTENT')}")
            elif len(valid_scenes) > 6:
                logger.warning(f"⚠️ 分镜数量过多: 有{len(valid_scenes)}个分镜，建议不超过6个")
            else:
                logger.info(f"✅ 分镜数量合适: {len(valid_scenes)}个分镜")

            # Apply multimedia validation and fixing
            logger.info("🔄 开始多媒体验证和修复...")
            validated_json = self._validate_and_fix_multimedia_scenes(valid_scenes)
            logger.info(f"✅ 多媒体验证完成，最终分镜数: {len(validated_json)}")

            # Return the validated JSON
            if isinstance(validated_json, list):
                final_json = json.dumps(validated_json, ensure_ascii=False, separators=(",", ":"))
                logger.info("✅ Successfully generated DSL JSON.")
                logger.info(f"🔍 最终JSON长度: {len(final_json)} 字符")
                return final_json
            else:
                return json.dumps(validated_json, ensure_ascii=False, separators=(",", ":"))

        except (json.JSONDecodeError, ValueError) as e:
            logger.error(f"❌ JSON直接解析失败: {e}")
            logger.debug(f"🔍 JSON错误详情: {str(e)}")

        # If raw parsing fails, try to extract json code block with improved regex
        logger.info("🔄 尝试从代码块中提取JSON...")
        # Look for JSON code blocks specifically, not other code blocks
        json_patterns = [
            r"```json\s*(.*?)```",  # Explicit json blocks
            r"```\s*(\[.*?\])\s*```",  # Array in code blocks
            r"```\s*(\{.*?\})\s*```",  # Object in code blocks
        ]

        extracted_code = None
        for i, pattern in enumerate(json_patterns):
            code_match = re.search(pattern, generated_content, re.DOTALL)
            if code_match:
                extracted_code = code_match.group(1).strip()
                logger.info(f"✅ 成功使用模式{i+1}提取到JSON代码块")
                logger.info(f"🔍 提取的代码长度: {len(extracted_code)} 字符")
                logger.info(f"🔍 提取的代码前200字符: {extracted_code[:200]}")
                break

        if extracted_code:
            # Pre-process to remove Chinese quotes before JSON validation
            logger.info("🔄 预处理JSON，修复中文引号...")
            preprocessed_code = self._preprocess_json(extracted_code)
            logger.info(f"🔍 预处理后长度: {len(preprocessed_code)} 字符")

            # Try to fix JSON structure issues
            logger.info("🔄 修复JSON结构问题...")
            fixed_code = self._fix_json_structure(preprocessed_code)
            logger.info(f"🔍 修复后长度: {len(fixed_code)} 字符")

            # Validate JSON format
            try:
                parsed_json = json.loads(fixed_code)
                logger.info("✅ 代码块JSON解析成功")
                logger.info(f"🔍 解析后类型: {type(parsed_json)}")

                # 验证JSON格式是否正确
                if not isinstance(parsed_json, list):
                    logger.error(f"❌ 代码块JSON格式错误：期望列表，实际得到 {type(parsed_json)}")
                    return self._get_fallback_json()
                
                # 检查列表中的元素是否都是字典
                valid_scenes = []
                for i, scene in enumerate(parsed_json):
                    if isinstance(scene, dict):
                        logger.info(f"✅ 代码块分镜{i+1}有效: {scene.get('分镜内容', 'NO_CONTENT')[:50]}")
                        valid_scenes.append(scene)
                    else:
                        logger.warning(f"⚠️ 跳过代码块第{i}个非字典元素: {type(scene)} - {str(scene)[:100]}")
                
                logger.info(f"🔍 代码块有效分镜总数: {len(valid_scenes)}")
                
                if not valid_scenes:
                    logger.error("❌ 代码块JSON中没有有效的分镜字典，返回默认结构")
                    return self._get_fallback_json()

                # 分镜数量验证
                if len(valid_scenes) < 5:
                    logger.error(f"❌ 代码块分镜数量不足: 只有{len(valid_scenes)}个分镜，要求至少5个")

                # Apply multimedia validation and fixing
                logger.info("🔄 开始代码块多媒体验证和修复...")
                validated_json = self._validate_and_fix_multimedia_scenes(valid_scenes)
                logger.info(f"✅ 代码块多媒体验证完成，最终分镜数: {len(validated_json)}")

                # Return the validated JSON
                if isinstance(validated_json, list):
                    final_json = json.dumps(validated_json, ensure_ascii=False, separators=(",", ":"))
                    logger.info("✅ Successfully generated DSL JSON from code block.")
                    return final_json
                else:
                    return json.dumps(validated_json, ensure_ascii=False, separators=(",", ":"))

            except json.JSONDecodeError as e:
                logger.error(f"❌ 代码块JSON格式无效: {e}")
                logger.error(f"🔍 提取的内容前200字符: {extracted_code[:200]}")

                # Try to fix common JSON issues
                logger.info("🔄 尝试修复常见JSON问题...")
                fixed_json = self._try_fix_json(extracted_code)
                if fixed_json:
                    try:
                        parsed_json = json.loads(fixed_json)
                        logger.info("✅ JSON修复成功")

                        # 验证修复后的JSON格式
                        if not isinstance(parsed_json, list):
                            logger.error(f"❌ 修复后JSON格式错误：期望列表，实际得到 {type(parsed_json)}")
                            return self._get_fallback_json()
                        
                        # 检查列表中的元素是否都是字典
                        valid_scenes = []
                        for i, scene in enumerate(parsed_json):
                            if isinstance(scene, dict):
                                logger.info(f"✅ 修复后分镜{i+1}有效: {scene.get('分镜内容', 'NO_CONTENT')[:50]}")
                                valid_scenes.append(scene)
                            else:
                                logger.warning(f"⚠️ 跳过修复后第{i}个非字典元素: {type(scene)} - {str(scene)[:100]}")
                        
                        if not valid_scenes:
                            logger.error("❌ 修复后JSON中没有有效的分镜字典，返回默认结构")
                            return self._get_fallback_json()

                        # Apply multimedia validation and fixing
                        validated_json = self._validate_and_fix_multimedia_scenes(valid_scenes)

                        if isinstance(validated_json, list):
                            final_json = json.dumps(validated_json, ensure_ascii=False, separators=(",", ":"))
                            logger.info("✅ Successfully generated DSL JSON after fixing.")
                            return final_json
                        else:
                            return json.dumps(validated_json, ensure_ascii=False, separators=(",", ":"))
                    except json.JSONDecodeError as e2:
                        logger.error(f"❌ JSON修复后仍然无效: {e2}")
                else:
                    logger.error("❌ JSON修复失败")
        else:
            logger.warning("⚠️ 无法从LLM响应中提取JSON代码块")

            # Try to find JSON in the raw response one more time with different approach
            logger.info("🔄 尝试在原始响应中查找JSON...")
            try:
                # Look for JSON-like content starting with [ or {
                json_start = max(generated_content.find("["), generated_content.find("{"))
                if json_start != -1:
                    logger.info(f"🔍 找到JSON起始位置: {json_start}")
                    # Find the matching closing bracket
                    json_content = generated_content[json_start:]

                    # Try to find the end of JSON
                    bracket_count = 0
                    brace_count = 0
                    end_pos = 0

                    for i, char in enumerate(json_content):
                        if char == "[":
                            bracket_count += 1
                        elif char == "]":
                            bracket_count -= 1
                        elif char == "{":
                            brace_count += 1
                        elif char == "}":
                            brace_count -= 1

                        if bracket_count == 0 and brace_count == 0 and i > 0:
                            end_pos = i + 1
                            break

                    if end_pos > 0:
                        json_content = json_content[:end_pos]
                        logger.info(f"🔍 提取的JSON内容长度: {len(json_content)}")
                        logger.info(f"🔍 JSON内容前200字符: {json_content[:200]}")
                        
                        preprocessed_content = self._preprocess_json(json_content)
                        fixed_content = self._fix_json_structure(preprocessed_content)
                        parsed_json = json.loads(fixed_content)
                        logger.info("✅ 通过括号匹配找到有效JSON")

                        # 验证JSON格式是否正确
                        if not isinstance(parsed_json, list):
                            logger.error(f"❌ 括号匹配JSON格式错误：期望列表，实际得到 {type(parsed_json)}")
                            return self._get_fallback_json()
                        
                        # 检查列表中的元素是否都是字典
                        valid_scenes = []
                        for i, scene in enumerate(parsed_json):
                            if isinstance(scene, dict):
                                valid_scenes.append(scene)
                            else:
                                logger.warning(f"⚠️ 跳过括号匹配第{i}个非字典元素: {type(scene)} - {str(scene)[:100]}")
                        
                        if not valid_scenes:
                            logger.error("❌ 括号匹配JSON中没有有效的分镜字典，返回默认结构")
                            return self._get_fallback_json()

                        # Apply multimedia validation and fixing
                        validated_json = self._validate_and_fix_multimedia_scenes(valid_scenes)

                        # Return the validated JSON
                        if isinstance(validated_json, list):
                            final_json = json.dumps(validated_json, ensure_ascii=False, separators=(",", ":"))
                            logger.info("✅ Successfully generated DSL JSON from bracket matching.")
                            return final_json
                        else:
                            return json.dumps(validated_json, ensure_ascii=False, separators=(",", ":"))
                else:
                    logger.error("❌ 在原始响应中未找到JSON起始位置")

            except (json.JSONDecodeError, ValueError) as e:
                logger.error(f"❌ 括号匹配JSON解析失败: {e}")

            logger.error("❌ 所有JSON解析方法都失败了")
            return self._get_fallback_json()

    def _fix_json_structure(self, json_str: str) -> str:
        """Fix common JSON structure issues like missing commas."""
        import re

        # Fix missing commas between objects in arrays
        # Pattern: }{ -> },{
        json_str = re.sub(r"}\s*{", "},{", json_str)

        # Fix missing commas between array elements
        # Pattern: "] [" -> "], ["
        json_str = re.sub(r"]\s*\[", "],[", json_str)

        # Fix missing commas after closing braces/brackets before opening ones
        # Pattern: } " -> }, "
        json_str = re.sub(r'}\s*"', '},"', json_str)
        json_str = re.sub(r']\s*"', '],"', json_str)

        # Fix missing commas after quoted strings before objects/arrays
        # Pattern: " { -> ", {
        json_str = re.sub(r'"\s*{', '",{', json_str)
        json_str = re.sub(r'"\s*\[', '",[', json_str)

        return json_str

    def _get_fallback_json(self) -> str:
        """Return a basic valid JSON structure when all parsing fails."""
        return """[
  {
    "分镜内容": "内容生成失败，请重新生成",
    "视觉动效建议": [
      {
        "type": "display_markdown",
        "target_region_id": "full_screen",
        "content": "# 内容生成失败\\n\\n请重新运行生成命令",
        "narration": "由于JSON格式问题，内容生成失败。请检查输入内容并重新运行生成命令。"
      }
    ]
  }
]"""

    def _try_fix_json(self, json_str: str) -> str:
        """Try to fix common JSON formatting issues."""
        import json

        try:
            # First, try to parse as-is
            json.loads(json_str)
            return json_str
        except json.JSONDecodeError:
            pass

        try:
            # Simple and effective approach: remove all problematic Chinese quotes
            fixed = json_str

            # Replace Chinese quotes with nothing (remove them)
            fixed = fixed.replace('"', "")
            fixed = fixed.replace('"', "")
            fixed = fixed.replace(
                """, '')
            fixed = fixed.replace(""",
                "",
            )

            # Remove trailing commas
            fixed = re.sub(r",\s*}", "}", fixed)
            fixed = re.sub(r",\s*]", "]", fixed)

            # Try to close unclosed structures
            open_braces = fixed.count("{") - fixed.count("}")
            open_brackets = fixed.count("[") - fixed.count("]")

            if open_braces > 0:
                fixed += "}" * open_braces
            if open_brackets > 0:
                fixed += "]" * open_brackets

            # Validate the fixed JSON
            json.loads(fixed)
            logger.info("Successfully fixed JSON format by removing Chinese quotes.")
            return fixed

        except json.JSONDecodeError as e:
            logger.warning(f"Simple fix failed: {e}")

            # Try a more aggressive approach: extract and rebuild valid JSON
            try:
                # Use regex to extract the main structure and clean it
                # Remove problematic quotes and rebuild
                lines = json_str.split("\n")
                cleaned_lines = []

                for line in lines:
                    # Remove Chinese quotes from content strings
                    if '"content":' in line or '"narration":' in line or '"分镜内容":' in line:
                        # Remove Chinese quotes from these specific fields
                        line = line.replace('"', "").replace('"', "").replace(""", ').replace(""", "")
                    cleaned_lines.append(line)

                cleaned_json = "\n".join(cleaned_lines)

                # Remove trailing commas
                cleaned_json = re.sub(r",\s*}", "}", cleaned_json)
                cleaned_json = re.sub(r",\s*]", "]", cleaned_json)

                # Try to close unclosed structures
                open_braces = cleaned_json.count("{") - cleaned_json.count("}")
                open_brackets = cleaned_json.count("[") - cleaned_json.count("]")

                if open_braces > 0:
                    cleaned_json += "}" * open_braces
                if open_brackets > 0:
                    cleaned_json += "]" * open_brackets

                # Validate the cleaned JSON
                json.loads(cleaned_json)
                logger.info("Successfully fixed JSON format with aggressive cleaning.")
                return cleaned_json

            except Exception as e:
                logger.warning(f"Aggressive fix failed: {e}")
                return None

    def _preprocess_json(self, json_str: str) -> str:
        """预处理JSON字符串，修复常见问题."""
        import re
        
        # 移除可能的无关中文字符或词汇
        # 移除看起来不属于JSON结构的中文词汇
        unwanted_patterns = [
            r'水电费',  # 移除特定的无关词汇
            r'[^\x00-\x7F\u4e00-\u9fff\u3000-\u303f\uff00-\uffef\{\}\[\]:,"\s]',  # 移除非ASCII非中文非JSON符号的字符
        ]
        
        for pattern in unwanted_patterns:
            json_str = re.sub(pattern, '', json_str)
        
        # 移除多余的空白字符
        json_str = re.sub(r'\s+', ' ', json_str).strip()
        
        # 替换中文引号为英文引号
        json_str = json_str.replace('"', '"').replace('"', '"')
        json_str = json_str.replace(''', "'").replace(''', "'")
        
        return json_str

    def _extract_multimedia_info_from_markdown(self, markdown_content: str) -> dict:
        """
        从Markdown内容中提取多媒体素材信息

        Args:
            markdown_content: Markdown内容

        Returns:
            包含多媒体信息的字典
        """
        import re

        multimedia_info = {"videos": [], "images": [], "gifs": []}

        # 提取视频链接
        video_patterns = [
            r"\[([^\]]*)\]\(([^)]*\.mp4[^)]*)\)",  # [text](path.mp4)
            r"\[([^\]]*)\]\(([^)]*\.avi[^)]*)\)",  # [text](path.avi)
            r"\[([^\]]*)\]\(([^)]*\.mov[^)]*)\)",  # [text](path.mov)
        ]

        for pattern in video_patterns:
            matches = re.findall(pattern, markdown_content, re.IGNORECASE)
            for text, path in matches:
                multimedia_info["videos"].append({"text": text.strip(), "path": path.strip()})

        # 提取图片链接
        image_patterns = [
            r"!\[([^\]]*)\]\(([^)]*\.(?:png|jpg|jpeg|gif|svg)[^)]*)\)",  # ![alt](path.ext)
        ]

        for pattern in image_patterns:
            matches = re.findall(pattern, markdown_content, re.IGNORECASE)
            for alt, path in matches:
                if path.lower().endswith(".gif"):
                    multimedia_info["gifs"].append({"alt": alt.strip(), "path": path.strip()})
                else:
                    multimedia_info["images"].append({"alt": alt.strip(), "path": path.strip()})

        return multimedia_info

    def _validate_image_accessibility(self, image_path: str) -> bool:
        """
        验证图片链接是否可以本地访问

        Args:
            image_path: 图片路径

        Returns:
            bool: 图片是否可访问
        """
        import os

        # 检查是否为网络URL
        if image_path.startswith(("http://", "https://", "ftp://")):
            # 网络URL暂时保留，因为我们无法简单验证网络连接
            logger.debug(f"网络图片链接保留: {image_path}")
            return True

        # 检查本地文件路径
        if os.path.isabs(image_path):
            # 绝对路径
            accessible = os.path.exists(image_path) and os.path.isfile(image_path)
        else:
            # 相对路径，检查当前目录和常见的相对路径
            accessible = False
            possible_paths = [
                image_path,
                os.path.join(os.getcwd(), image_path),
                os.path.join(os.getcwd(), "output", image_path.lstrip("./")),
            ]

            for path in possible_paths:
                if os.path.exists(path) and os.path.isfile(path):
                    accessible = True
                    break

        if accessible:
            logger.debug(f"图片链接可访问: {image_path}")
        else:
            logger.warning(f"图片链接不可访问: {image_path}")

        return accessible

    def _convert_image_action_to_text(self, action: dict, scene_content: str) -> dict:
        """
        将无效的图片action转换为文本展示action

        Args:
            action: 原始图片action
            scene_content: 分镜内容

        Returns:
            转换后的文本action
        """
        logger.info(f"将无效图片action转换为文本展示: {action.get('content', 'Unknown')}")

        # 构建文本内容
        text_content = []

        # 使用分镜内容作为标题
        if scene_content:
            text_content.append(f"# {scene_content}")

        # 添加annotation作为要点
        if action.get("annotation"):
            text_content.append("\n## 核心要点")
            annotation = action["annotation"]
            # 如果annotation包含markdown格式，直接使用
            if "##" in annotation or "- " in annotation or "**" in annotation:
                text_content.append(annotation)
            else:
                # 如果是简单文本，转换为列表格式
                points = annotation.split(" | ")
                for point in points:
                    if point.strip():
                        text_content.append(f"- {point.strip()}")

        # 创建新的文本action
        new_action = {
            "type": "display_markdown",
            "target_region_id": action.get("target_region_id", "full_screen"),
            "content": "\n".join(text_content),
            "narration": action.get(
                "narration", f"由于图片素材无法访问，我们通过文字来介绍{scene_content}的相关内容。"
            ),
        }

        return new_action

    def _convert_multimedia_action_to_text(self, action: dict, scene_content: str) -> dict:
        """
        将重复使用的多媒体action转换为文本展示action

        Args:
            action: 原始多媒体action
            scene_content: 分镜内容

        Returns:
            转换后的文本action
        """
        media_type = action.get("type", "")
        media_path = ""

        if media_type == "display_video":
            media_path = action.get("video_path", "")
        elif media_type == "display_image":
            media_path = action.get("content", "")

        logger.info(f"将重复的{media_type}素材转换为文本展示: {media_path}")

        # 构建文本内容
        text_content = []

        # 使用分镜内容作为标题
        if scene_content:
            text_content.append(f"# {scene_content}")

        # 添加素材引用说明
        text_content.append(f"\n*[参考素材: {media_path.split('/')[-1] if '/' in media_path else media_path}]*")

        # 添加annotation作为要点
        if action.get("annotation"):
            text_content.append("\n## 核心要点")
            annotation = action["annotation"]
            # 如果annotation包含markdown格式，直接使用
            if "##" in annotation or "- " in annotation or "**" in annotation:
                text_content.append(annotation)
            else:
                # 如果是简单文本，转换为列表格式
                points = annotation.split(" | ")
                for point in points:
                    if point.strip():
                        text_content.append(f"- {point.strip()}")

        # 对于视频，可以添加overlay_text作为关键词展示
        if media_type == "display_video" and action.get("overlay_text"):
            text_content.append("\n## 关键特性")
            overlay_keywords = action["overlay_text"]
            if isinstance(overlay_keywords, list):
                for keyword in overlay_keywords:
                    text_content.append(f"- **{keyword}**")

        # 创建新的文本action
        new_action = {
            "type": "display_markdown",
            "target_region_id": action.get("target_region_id", "full_screen"),
            "content": "\n".join(text_content),
            "narration": action.get(
                "narration", f"由于多媒体素材已在其他分镜中使用，我们通过文字来介绍{scene_content}的相关内容。"
            ),
        }

        return new_action

    def _validate_and_fix_multimedia_scenes(self, parsed_json: list) -> list:
        """
        验证并修复包含多媒体素材的分镜，检查图片链接可访问性，添加overlay_text，合并left_half和right_half分镜，检测重复素材使用

        Args:
            parsed_json: 解析后的JSON列表

        Returns:
            验证并修复后的JSON列表
        """
        multimedia_functions = ["display_video", "display_image"]
        validated_json = []
        used_multimedia_paths = set()  # 跟踪已使用的多媒体素材路径

        # 第一步：检测重复使用的多媒体素材并处理
        for scene in parsed_json:
            # 添加类型检查，确保scene是字典类型
            if not isinstance(scene, dict):
                logger.warning(f"跳过非字典类型的scene元素: {type(scene)} - {str(scene)[:100]}")
                continue
                
            if not scene.get("视觉动效建议"):
                validated_json.append(scene)
                continue

            actions = scene["视觉动效建议"]
            scene_content = scene.get("分镜内容", "")

            # 检查是否包含多媒体素材
            multimedia_actions = []
            other_actions = []

            for action in actions:
                if action.get("type") in multimedia_functions:
                    # 获取多媒体素材路径
                    media_path = ""
                    if action.get("type") == "display_video":
                        media_path = action.get("video_path", "")
                    elif action.get("type") == "display_image":
                        media_path = action.get("content", "")

                    # 检查是否重复使用
                    if media_path and media_path in used_multimedia_paths:
                        logger.warning(f"检测到重复使用的多媒体素材: {media_path}")
                        logger.info(f"将重复素材分镜转换为文本展示: {scene_content}")
                        # 将重复的多媒体action转换为文本action
                        converted_action = self._convert_multimedia_action_to_text(action, scene_content)
                        other_actions.append(converted_action)
                        continue

                    # 如果是图片action，验证图片可访问性
                    if action.get("type") == "display_image":
                        image_path = action.get("content", "")
                        if image_path and not self._validate_image_accessibility(image_path):
                            # 图片不可访问，转换为文本action
                            logger.info(f"图片不可访问，转换为文本展示: {image_path}")
                            converted_action = self._convert_image_action_to_text(action, scene_content)
                            other_actions.append(converted_action)
                            continue

                    # 为视频action添加overlay_text验证
                    if action.get("type") == "display_video":
                        self._ensure_video_overlay_text(action, scene_content)

                    # 记录已使用的素材路径
                    if media_path:
                        used_multimedia_paths.add(media_path)
                        logger.info(f"记录多媒体素材使用: {media_path}")

                    multimedia_actions.append(action)
                else:
                    other_actions.append(action)

            # 如果包含多媒体素材
            if multimedia_actions:
                if len(actions) > 1:
                    logger.info(f"检测到多媒体分镜包含多个action，正在修复: {scene.get('分镜内容', 'Unknown')}")

                    # 选择第一个多媒体action作为主要action
                    primary_multimedia = multimedia_actions[0]

                    # 将其他文本内容合并到多媒体action的annotation和narration中
                    merged_annotation_parts = []
                    merged_narration_parts = []

                    # 收集现有的annotation和narration
                    if primary_multimedia.get("annotation"):
                        merged_annotation_parts.append(primary_multimedia["annotation"])
                    if primary_multimedia.get("narration"):
                        merged_narration_parts.append(primary_multimedia["narration"])

                    # 从其他action中提取内容
                    for action in other_actions:
                        if action.get("content"):
                            # 将content转换为annotation格式
                            content = action["content"]
                            if isinstance(content, str):
                                # 清理markdown格式，提取关键信息
                                cleaned_content = self._extract_key_points_from_content(content)
                                if cleaned_content:
                                    merged_annotation_parts.append(cleaned_content)

                        if action.get("narration"):
                            merged_narration_parts.append(action["narration"])

                    # 合并annotation（用 | 分隔）
                    if merged_annotation_parts:
                        combined_annotation = " | ".join(merged_annotation_parts)
                        # 检查annotation长度，如果过长则使用markdown格式
                        primary_multimedia["annotation"] = self._process_annotation_format(combined_annotation)

                    # 合并narration（确保不超过150字限制）
                    if merged_narration_parts:
                        combined_narration = " ".join(merged_narration_parts)
                        # 限制narration长度
                        if len(combined_narration) > 150:
                            combined_narration = combined_narration[:147] + "..."
                        primary_multimedia["narration"] = combined_narration

                    # 只保留主要的多媒体action
                    scene["视觉动效建议"] = [primary_multimedia]

                    logger.info(f"已修复多媒体分镜，合并为单一action: {primary_multimedia.get('type')}")
            elif other_actions:
                # 如果没有有效的多媒体素材，但有其他actions（比如转换后的文本action）
                scene["视觉动效建议"] = other_actions
                logger.info(f"分镜已转换为非多媒体展示: {scene_content}")

            validated_json.append(scene)

        # 第二步：检测并合并相邻的left_half和right_half分镜
        merged_json = self._merge_side_by_side_scenes(validated_json)

        return merged_json

    def _extract_key_points_from_content(self, content: str) -> str:
        """
        从content中提取关键信息点，用于annotation
        统一使用markdown格式，限制150字以内

        Args:
            content: 原始content内容

        Returns:
            提取的关键信息（markdown格式，不超过150字）
        """

        if not content:
            return ""

        # 统一使用markdown格式处理，不再区分长短文本
        # 直接调用格式化方法，确保markdown格式和长度限制
        return self._format_long_annotation(content)

    def _format_long_annotation(self, content: str) -> str:
        """
        格式化长文本annotation，统一限制150字，使用markdown格式

        Args:
            content: 原始长文本内容

        Returns:
            格式化后的markdown文本（不超过150字）
        """
        import re

        # 首先进行长度控制，确保不超过150字符
        # 先按行分割，优先保留完整的行
        lines = content.split("\n")
        processed_lines = []
        total_length = 0
        max_length = 150  # 统一限制为150字符
        max_lines = 5  # 设置最大行数限制

        for i, line in enumerate(lines):
            line = line.strip()
            if not line:  # 跳过空行
                continue

            # 检查是否超过行数限制
            if len(processed_lines) >= max_lines:
                break

            # 检查添加这一行是否会超过长度限制
            line_length = len(line) + 1  # +1 for newline
            if total_length + line_length > max_length:
                # 如果会超过，尝试截断这一行
                remaining_length = max_length - total_length - 4  # -4 for "..."
                if remaining_length > 10:  # 如果剩余长度足够，截断这一行
                    truncated_line = line[:remaining_length] + "..."
                    processed_lines.append(truncated_line)
                break

            processed_lines.append(line)
            total_length += line_length

        # 如果原内容被截断，添加省略号
        if len(processed_lines) < len([line for line in lines if line.strip()]) or total_length >= max_length - 10:
            if processed_lines and not processed_lines[-1].endswith("..."):
                processed_lines.append("...")

        # 重新组合内容
        content = "\n".join(processed_lines)

        # 然后应用markdown格式优化
        # 清理多余的空行
        content = re.sub(r"\n\s*\n\s*\n", "\n\n", content)

        # 确保列表项格式正确
        content = re.sub(r"^[-*+]\s*", "- ", content, flags=re.MULTILINE)

        # 确保标题格式正确
        content = re.sub(r"^(#+)\s*", r"\1 ", content, flags=re.MULTILINE)

        # 确保粗体格式正确
        content = re.sub(r"\*\*([^*]+)\*\*", r"**\1**", content)

        # 确保代码格式正确
        content = re.sub(r"`([^`]+)`", r"`\1`", content)

        return content.strip()

    def _process_annotation_format(self, annotation: str) -> str:
        """
        处理annotation格式，统一使用markdown格式，限制150字以内

        Args:
            annotation: 原始annotation内容

        Returns:
            格式化后的annotation（markdown格式，不超过150字）
        """
        # 统一使用markdown格式处理，确保不超过150字符
        return self._format_long_annotation(annotation)

    def _merge_side_by_side_scenes(self, scenes: list) -> list:
        """
        检测并合并相邻的left_half和right_half分镜

        Args:
            scenes: 分镜列表

        Returns:
            合并后的分镜列表
        """
        merged_scenes = []
        i = 0

        while i < len(scenes):
            # 检查是否有下一个分镜
            if i < len(scenes) - 1:
                current_scene = scenes[i]
                next_scene = scenes[i + 1]

                # 检查当前分镜和下一个分镜的action
                current_actions = current_scene.get("视觉动效建议", [])
                next_actions = next_scene.get("视觉动效建议", [])

                # 如果两个分镜都只有一个action且分别是left_half和right_half
                if (
                    len(current_actions) == 1
                    and len(next_actions) == 1
                    and current_actions[0].get("target_region_id") == "left_half"
                    and next_actions[0].get("target_region_id") == "right_half"
                ):
                    logger.info(
                        f"检测到相邻的left_half和right_half分镜，正在合并: {current_scene.get('分镜内容', '')} + {next_scene.get('分镜内容', '')}"
                    )

                    left_action = current_actions[0]
                    right_action = next_actions[0]

                    # 创建合并后的分镜
                    merged_scene = {
                        "分镜内容": f"{current_scene.get('分镜内容', '')}对比",
                        "视觉动效建议": [
                            {
                                "type": "side_by_side_comparison",
                                "target_region_id": "full_screen",
                                "comparison_title": f"{current_scene.get('分镜内容', '')} vs {next_scene.get('分镜内容', '')}",
                                "left_content": left_action.get("content", ""),
                                "right_content": right_action.get("content", ""),
                                "left_narration": left_action.get("narration", ""),
                                "right_narration": right_action.get("narration", ""),
                                "annotation": f"{left_action.get('annotation', '')} | {right_action.get('annotation', '')}",
                            }
                        ],
                    }

                    merged_scenes.append(merged_scene)
                    i += 2  # 跳过下一个分镜，因为已经合并了
                    logger.info("成功合并left_half和right_half分镜为side_by_side_comparison")
                    continue

            # 如果不满足合并条件，直接添加当前分镜
            merged_scenes.append(scenes[i])
            i += 1

        return merged_scenes

    def _ensure_video_overlay_text(self, action: dict, scene_content: str):
        """
        为视频action验证overlay_text参数

        Args:
            action: 视频action
            scene_content: 分镜内容
        """
        if not action.get("overlay_text"):
            logger.warning(f"视频action缺少overlay_text参数: {action.get('video_path', 'Unknown')}")
            # 基于scene_content生成默认的overlay_text
            default_keywords = self._generate_default_overlay_text(scene_content)
            action["overlay_text"] = default_keywords
            logger.info(f"已为视频action添加默认overlay_text: {default_keywords}")
        else:
            overlay_text = action["overlay_text"]
            if not isinstance(overlay_text, list):
                # 如果不是列表，转换为列表
                action["overlay_text"] = [str(overlay_text)]
                logger.info(f"已将overlay_text转换为列表格式: {action['overlay_text']}")
            elif len(overlay_text) < 3 or len(overlay_text) > 5:
                logger.warning(f"overlay_text数量不在3-5个范围内: {len(overlay_text)}个")

            logger.info(f"视频action包含overlay_text: {action['overlay_text']}")

    def _generate_default_overlay_text(self, scene_content: str) -> list:
        """
        基于分镜内容生成默认的overlay_text关键词

        Args:
            scene_content: 分镜内容

        Returns:
            关键词列表
        """
        # 根据分镜内容生成相关关键词
        keywords = []

        # 技术相关关键词
        if any(word in scene_content for word in ["AI", "人工智能", "智能", "算法"]):
            keywords.append("AI技术")
        if any(word in scene_content for word in ["开源", "GitHub", "项目"]):
            keywords.append("开源项目")
        if any(word in scene_content for word in ["演示", "demo", "展示"]):
            keywords.append("功能演示")
        if any(word in scene_content for word in ["架构", "设计", "方案"]):
            keywords.append("系统架构")
        if any(word in scene_content for word in ["性能", "效率", "优化"]):
            keywords.append("性能优化")

        # 如果没有匹配到特定关键词，使用通用关键词
        if not keywords:
            keywords = ["技术展示", "核心功能", "创新方案"]

        # 确保关键词数量在3-5个之间
        if len(keywords) < 3:
            keywords.extend(["高效工具", "实用性强"])
        elif len(keywords) > 5:
            keywords = keywords[:5]

        return keywords[:4]  # 返回最多4个关键词

    def _try_parse_raw_json(self, generated_content: str) -> Optional[str]:
        """改进的JSON解析方法，增强容错能力"""
        import json
        
        try:
            # 1. 首先尝试直接解析
            parsed_json = json.loads(generated_content)
            
            # 检查是否是字典格式包装了数组
            if isinstance(parsed_json, dict):
                # 查找可能的数组字段
                for key, value in parsed_json.items():
                    if isinstance(value, list) and len(value) > 0:
                        # 检查数组中是否包含分镜结构
                        if isinstance(value[0], dict) and ("分镜内容" in value[0] or "视觉动效建议" in value[0]):
                            logger.info(f"发现字典包装的数组，提取字段: {key}")
                            parsed_json = value
                            break
                else:
                    # 如果没找到合适的数组，尝试将字典转换为数组
                    if "分镜内容" in parsed_json or "视觉动效建议" in parsed_json:
                        logger.info("将单个字典转换为数组格式")
                        parsed_json = [parsed_json]
                    else:
                        logger.error(f"字典格式无法转换为分镜数组: {list(parsed_json.keys())}")
                        return None
            
            # 验证是否为有效的分镜数组
            if isinstance(parsed_json, list):
                valid_scenes = []
                for i, scene in enumerate(parsed_json):
                    if isinstance(scene, dict) and ("分镜内容" in scene or "视觉动效建议" in scene):
                        valid_scenes.append(scene)
                    else:
                        logger.warning(f"跳过无效分镜 {i}: {type(scene)}")
                
                if valid_scenes:
                    # Apply multimedia validation and fixing
                    validated_json = self._validate_and_fix_multimedia_scenes(valid_scenes)
                    final_json = json.dumps(validated_json, ensure_ascii=False, separators=(",", ":"))
                    logger.info("Successfully generated DSL JSON.")
                    return final_json
                else:
                    logger.error("没有找到有效的分镜内容")
                    return None
            else:
                logger.error(f"解析结果不是数组: {type(parsed_json)}")
                return None
                
        except json.JSONDecodeError as e:
            logger.debug(f"直接JSON解析失败: {e}")
            
            # 2. 尝试修复不完整的JSON
            return self._try_fix_incomplete_json(generated_content)
    
    def _try_fix_incomplete_json(self, content: str) -> Optional[str]:
        """修复不完整或截断的JSON"""
        import json
        
        try:
            # 查找JSON开始位置
            start_pos = max(content.find("["), content.find("{"))
            if start_pos == -1:
                return None
            
            json_content = content[start_pos:].strip()
            
            # 尝试智能补全JSON结构
            fixed_content = self._smart_complete_json(json_content)
            
            if fixed_content:
                # 验证修复后的JSON
                parsed = json.loads(fixed_content)
                
                # 处理字典包装的情况
                if isinstance(parsed, dict):
                    for key, value in parsed.items():
                        if isinstance(value, list):
                            parsed = value
                            break
                    else:
                        if "分镜内容" in parsed:
                            parsed = [parsed]
                
                if isinstance(parsed, list):
                    validated_json = self._validate_and_fix_multimedia_scenes(parsed)
                    return json.dumps(validated_json, ensure_ascii=False, separators=(",", ":"))
                    
        except Exception as e:
            logger.debug(f"JSON修复失败: {e}")
            
        return None
    
    def _smart_complete_json(self, json_str: str) -> Optional[str]:
        """智能补全不完整的JSON结构"""
        import json
        
        try:
            # 统计未闭合的括号和花括号
            open_brackets = json_str.count("[") - json_str.count("]")
            open_braces = json_str.count("{") - json_str.count("}")
            
            # 如果JSON看起来被截断了，尝试找到最后一个完整的对象
            if open_brackets > 0 or open_braces > 0:
                # 找到最后一个完整的分镜对象
                parts = json_str.split('"分镜内容"')
                if len(parts) > 1:
                    # 保留完整的分镜
                    complete_parts = []
                    for i, part in enumerate(parts[:-1]):  # 排除最后一个可能不完整的部分
                        if i == 0:
                            complete_parts.append(part)
                        else:
                            complete_parts.append('"分镜内容"' + part)
                    
                    # 重新组合并尝试补全
                    if complete_parts:
                        reconstructed = ''.join(complete_parts)
                        
                        # 补全括号
                        if reconstructed.strip().endswith(','):
                            reconstructed = reconstructed.strip()[:-1]  # 移除末尾逗号
                        
                        # 计算需要的闭合符号
                        remaining_brackets = reconstructed.count("[") - reconstructed.count("]")
                        remaining_braces = reconstructed.count("{") - reconstructed.count("}")
                        
                        # 添加闭合符号
                        if remaining_braces > 0:
                            reconstructed += "}" * remaining_braces
                        if remaining_brackets > 0:
                            reconstructed += "]" * remaining_brackets
                        
                        # 验证修复结果
                        json.loads(reconstructed)
                        logger.info("成功修复不完整的JSON")
                        return reconstructed
            
            # 如果没有截断问题，尝试其他修复
            return self._fix_json_structure(json_str)
            
        except Exception as e:
            logger.debug(f"智能补全失败: {e}")
            return None


def main():
    parser = argparse.ArgumentParser(description="Generate Manim DSL from Markdown files.")
    parser.add_argument("-i", "--input-dir", help="Directory containing Markdown files.")
    parser.add_argument(
        "-m",
        "--markdown-output",
        required=True,
        help="Path to save the Markdown file.",
    )
    parser.add_argument(
        "-o",
        "--dsl-output",
        required=True,
        help="Path to save the generated Manim DSL Python file.",
    )

    args = parser.parse_args()

    input_dir = Path(args.input_dir) if args.input_dir else None
    markdown_output_path = Path(args.markdown_output)
    dsl_output_path = Path(args.dsl_output)

    # Ensure output directories exist
    if not markdown_output_path.exists():
        markdown_output_path.parent.mkdir(parents=True, exist_ok=True)
    if not dsl_output_path.exists():
        dsl_output_path.parent.mkdir(parents=True, exist_ok=True)

    # --- Step 1: Process and combine Markdown files ---
    if not markdown_output_path.exists():
        if input_dir is None:
            logger.error("Input directory is required, use -i to specify the input directory.")
            return
        logger.info(f"Processing Markdown files from: {input_dir}")
        try:
            process_markdown_files(input_dir, markdown_output_path)
            logger.info(f"Markdown saved to: {markdown_output_path}")
        except Exception as e:
            logger.error(f"Error processing Markdown files: {e}")
            return
    else:
        logger.info(f"Markdown already exists at: {markdown_output_path}")

    # --- Step 2: Read the combined Markdown file ---
    try:
        with open(markdown_output_path, encoding="utf-8") as f:
            markdown_content = f.read()
        logger.info("Successfully read Markdown file.")
    except Exception as e:
        logger.error(f"Error reading Markdown file {markdown_output_path}: {e}")
        return

    # --- Step 3: Initialize the Agent ---
    agent = ManimDSLGeneratorAgent()

    # --- Step 3.5: Extract multimedia information for better processing ---
    multimedia_info = agent._extract_multimedia_info_from_markdown(markdown_content)
    total_multimedia = len(multimedia_info["videos"]) + len(multimedia_info["images"]) + len(multimedia_info["gifs"])

    if total_multimedia > 0:
        logger.info(
            f"Detected multimedia content: {len(multimedia_info['videos'])} videos, "
            f"{len(multimedia_info['images'])} images, {len(multimedia_info['gifs'])} GIFs"
        )
        logger.info("Will apply strict single-action rule for multimedia scenes")
    else:
        logger.info("No multimedia content detected")

    # --- Step 4: Generate Manim DSL using the Agent ---
    # Make sure to replace the placeholder MANIM_DSL_PROMPT_TEMPLATE with your actual prompt
    try:
        dsl_reference_content = open("docs/animation_functions.md").read()
        dsl_example_content = open("docs/example_dsl.json").read()

        # 使用字符串替换而不是format，避免花括号冲突
        prompt = MANIM_DSL_PROMPT_TEMPLATE
        prompt = prompt.replace("{markdown_content}", markdown_content)
        prompt = prompt.replace("{dsl_reference}", dsl_reference_content)
        prompt = prompt.replace("{dsl_example}", dsl_example_content)
    except Exception as e:
        logger.error(f"Error creating prompt: {e}")
        return

    generated_dsl_code = agent.generate_dsl(prompt)

    # --- Step 5: Save the generated DSL code ---
    try:
        import json

        # Try to parse and format the JSON for better readability
        try:
            parsed_json = json.loads(generated_dsl_code)
            # Save with proper formatting (indented, readable)
            with open(dsl_output_path, "w", encoding="utf-8") as f:
                json.dump(parsed_json, f, ensure_ascii=False, indent=2)
            logger.info(f"Generated Manim DSL saved to: {dsl_output_path} (formatted)")
        except json.JSONDecodeError:
            # If it's not valid JSON, save as-is
            with open(dsl_output_path, "w", encoding="utf-8") as f:
                f.write(generated_dsl_code)
            logger.info(f"Generated Manim DSL saved to: {dsl_output_path} (raw)")
    except Exception as e:
        logger.error(f"Error writing DSL output file {dsl_output_path}: {e}")


if __name__ == "__main__":
    main()
