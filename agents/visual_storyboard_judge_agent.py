#!/usr/bin/env python3
"""
视觉故事板评估代理 - 评估视觉故事板与例子文件的一致性和质量
"""

import datetime
import os
import sys
from pathlib import Path
from typing import Any, Dict, Optional

import yaml
from loguru import logger

# 添加父目录到导入路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from camel.agents import ChatAgent
from camel.messages import BaseMessage
from camel.types import RoleType
from utils.create_llm_model import create_model


class VisualStoryboardJudgeAgent:
    """视觉故事板评估代理类"""

    def __init__(self, config_path: str = "config/config.yaml"):
        """初始化评估代理"""
        self.config_path = config_path
        self.config = self._load_config(config_path)
        
        # 创建专用的评估模型
        self.judge_model = self._create_judge_model()
        
        self.results_dir = Path("output/storyboard_evaluations")
        self.results_dir.mkdir(parents=True, exist_ok=True)

        # 设置日志
        logger.add("logs/visual_storyboard_judge.log", rotation="1 day", retention="30 days", level="INFO")

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, encoding="utf-8") as f:
                config = yaml.safe_load(f)
            logger.info(f"从 {config_path} 加载配置")
            return config
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            raise

    def _create_judge_model(self):
        """创建评估专用模型"""
        try:
            # 添加调试信息
            logger.debug(f"配置中的vision_storyboard_judge_model: {self.config.get('vision_storyboard_judge_model', 'Not found')}")
            
            # 检查是否有专用的评估模型配置
            if "vision_storyboard_judge_model" in self.config and "type" in self.config["vision_storyboard_judge_model"]:
                judge_model_type = self.config["vision_storyboard_judge_model"]["type"]
                logger.info(f"检测到专用评估模型配置: {judge_model_type}")
                
                # 创建临时配置，只修改模型类型
                judge_config = self.config.copy()
                judge_config["model"] = self.config["model"].copy()
                judge_config["model"]["type"] = judge_model_type
                
                # 创建临时配置文件
                import tempfile
                with tempfile.NamedTemporaryFile(mode="w", suffix=".yaml", delete=False) as f:
                    yaml.dump(judge_config, f)
                    temp_config_path = f.name
                
                try:
                    model = create_model(temp_config_path)
                    logger.info(f"✅ 成功创建专用评估模型: {judge_model_type}")
                    return model
                finally:
                    # 清理临时文件
                    try:
                        os.unlink(temp_config_path)
                    except Exception:
                        pass
            else:
                # 使用默认模型
                logger.warning("未找到vision_storyboard_judge_model配置或type字段缺失，使用默认模型")
                model = create_model(self.config_path)
                logger.info("使用默认模型进行评估")
                return model
                
        except Exception as e:
            logger.error(f"创建评估模型失败: {e}")
            # 降级使用默认模型
            logger.info("降级使用默认模型")
            return create_model(self.config_path)

    def _get_default_file_paths(self) -> tuple[str, str]:
        """从配置文件中获取默认的视觉故事板和例子文件路径"""
        default_topic, _ = self._get_default_values()
        # 将主题中的空格替换为下划线，以构造有效的文件路径
        topic_for_path = default_topic.replace(" ", "_").replace("：", "_").replace(":", "_")
        storyboard_path = f"output/{topic_for_path}/vision_storyboard"
        example_path = f"output/{topic_for_path}/example_explain.md"
        logger.info(f"构造默认文件路径 - 故事板: {storyboard_path}, 例子: {example_path}")
        return storyboard_path, example_path

    def _read_file(self, file_path: str) -> str:
        """读取文件内容"""
        try:
            with open(file_path, encoding="utf-8") as f:
                content = f.read()
            logger.info(f"成功读取文件: {file_path}")
            return content
        except FileNotFoundError:
            error_msg = f"文件未找到: {file_path}"
            logger.error(error_msg)
            raise
        except Exception as e:
            error_msg = f"读取文件失败: {e}"
            logger.error(error_msg)
            raise

    def _get_default_values(self) -> tuple[str, str]:
        """从配置文件中获取默认的主题和目的"""
        topic = "未知主题"
        purpose = "教学演示"
        
        if "example_explain" in self.config:
            config_section = self.config["example_explain"]
            topic = config_section.get("topic", topic)
            purpose = config_section.get("purpose", purpose)
        
        logger.info(f"默认主题: {topic}, 默认目的: {purpose}")
        return topic, purpose

    def evaluate_storyboard(self, storyboard_path: str = None, example_path: str = None, topic: str = None) -> str:
        """评估视觉故事板文件
        
        Args:
            storyboard_path: 视觉故事板文件路径（可选，如果不提供会使用默认路径）
            example_path: 例子说明文件路径（可选，如果不提供会使用默认路径）
            topic: 主题（可选，如果不提供会从配置文件读取）
            
        Returns:
            评估结果（Markdown格式）
        """
        logger.info("开始评估视觉故事板文件")
        
        try:
            # 如果没有提供路径，使用默认路径
            if not storyboard_path or not example_path:
                default_storyboard, default_example = self._get_default_file_paths()
                storyboard_path = storyboard_path or default_storyboard
                example_path = example_path or default_example
            
            # 读取文件内容
            storyboard_content = self._read_file(storyboard_path)
            example_content = self._read_file(example_path)
            
            # 获取主题
            default_topic, _ = self._get_default_values()
            topic = topic or default_topic
            
            logger.info(f"使用的主题: {topic}")
            logger.info(f"故事板文件: {storyboard_path}")
            logger.info(f"例子文件: {example_path}")
            
            # 生成评估
            evaluation_result = self._analyze_storyboard(topic, storyboard_content, example_content)
            
            logger.info("视觉故事板评估完成")
            return evaluation_result
            
        except Exception as e:
            logger.error(f"评估视觉故事板失败: {e}")
            return f"❌ **评估失败**: {str(e)}"

    def _analyze_storyboard(self, topic: str, storyboard_content: str, example_content: str) -> str:
        """分析评估视觉故事板内容"""
        
        evaluation_prompt = f"""
你是一位资深的Manim动画设计和教学设计评估专家，请对以下视觉故事板进行全面深入的评估分析。

评估主题：{topic}

## 视觉故事板内容：
{storyboard_content}

## 对比的例子说明文件：
{example_content}

请从以下4个关键维度进行详细评估分析：

### 1. 数据结构映射完整性（25分）
- **数据元素覆盖度**（15分）：检查例子文件中描述的所有数据结构（向量、矩阵、几何图形、数值、公式等）是否都在Group定义中体现
- **数据具体性匹配**（10分）：Group定义中的数据是否与例子文件中的具体数据保持一致（如具体的数值、矩阵维度等）

### 2. 核心动作动画灵魂映射（25分）
- **关键动画覆盖**（15分）：例子文件中每一个关键的动画要点是否都在"核心动作"中体现，不能遗漏任何重要动画
- **动画逻辑连贯性**（10分）：动画序列是否符合教学逻辑，能否有效传达概念本质

### 3. Group退场时机准确性（25分）
- **Manim退场规范**（15分）：每个Group的退场时机是否符合Manim动画制作规范，避免元素冲突
- **阶段切换清晰度**（10分）：相邻阶段之间Group的进退场是否清晰，是否有不相关Group滞留

### 4. 区域设计和组织合理性（25分）
- **区域分配合理性**（15分）：各个区域（标题、步骤、主内容、辅助、结果）的Group分配是否合理
- **设计完整性**（10分）：是否有遗漏的区域设计，各区域功能是否明确

### 评估重点关注：
1. **数据完整性**: 确保例子文件中的每个数据结构都有对应的Group定义
2. **动画连贯性**: 确保故事板能完整再现例子文件的教学意图
3. **技术规范性**: 确保符合Manim动画制作的技术要求
4. **教学有效性**: 确保故事板设计有利于概念理解

请按以下Markdown格式输出评估结果：

```markdown
# 📊 视觉故事板质量评估报告

## 📋 基本信息
- **主题**: {topic}
- **故事板文件**: vision_storyboard
- **对比文件**: example_explain.md

## 🏆 总体评分
**总分**: XX/100 分  

## 📈 详细分析

### 1. 数据结构映射完整性 (XX/25分)
#### 详细分析
[分析例子文件中的数据结构是否完整映射到Group定义中]

**优点：**
- [列出做得好的地方]

**待改进：**
- [具体指出遗漏的数据结构]
- [具体指出数据不一致的地方]

### 2. 核心动作动画灵魂映射 (XX/25分)
#### 详细分析
[分析核心动作是否完整覆盖例子文件的动画要点]

**优点：**
- [列出做得好的地方]

**待改进：**
- [具体指出遗漏的关键动画]
- [具体指出动画逻辑问题]

### 3. Group退场时机准确性 (XX/25分)
#### 详细分析
[分析Group退场时机是否符合Manim规范]

**优点：**
- [列出做得好的地方]

**待改进：**
- [具体指出退场时机问题]
- [具体指出可能的元素冲突]

### 4. 区域设计和组织合理性 (XX/25分)
#### 详细分析
[分析区域设计是否合理完整]

**优点：**
- [列出做得好的地方]

**待改进：**
- [具体指出区域设计问题]
- [具体指出组织不合理的地方]
```

"""

        try:
            # 创建评估agent
            system_message = BaseMessage(
                role_name="视觉故事板评估专家",
                role_type=RoleType.ASSISTANT,
                meta_dict=None,
                content="你是一位资深的Manim动画设计和教学设计评估专家，擅长评估视觉故事板与教学内容的一致性和质量，并提供建设性的改进建议。请按照Markdown格式输出评估结果。"
            )

            agent = ChatAgent(
                system_message=system_message, 
                model=self.judge_model, 
                message_window_size=10
            )

            user_message = BaseMessage(
                role_name="用户", 
                role_type=RoleType.USER, 
                meta_dict=None, 
                content=evaluation_prompt
            )

            response = agent.step(user_message)
            return response.msg.content

        except Exception as e:
            logger.error(f"分析视觉故事板失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return f"❌ **评估失败**: {str(e)}"

    def print_evaluation_result(self, result: str):
        """打印评估结果"""
        print(result)

    def save_evaluation_result(self, result: str, storyboard_path: str):
        """保存评估结果到文件"""
        try:
            # 生成结果文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            file_name = Path(storyboard_path).parent.name  # 使用topic目录名
            result_file = self.results_dir / f"storyboard_evaluation_{file_name}_{timestamp}.md"
            
            # 保存评估结果
            with open(result_file, "w", encoding="utf-8") as f:
                f.write(result)

            logger.info(f"评估结果已保存到: {result_file}")
            return str(result_file)

        except Exception as e:
            logger.error(f"保存评估结果失败: {e}")
            return None


def main():
    """主函数 - 命令行接口"""
    import argparse
    from loguru import logger as main_logger

    parser = argparse.ArgumentParser(description="视觉故事板评估工具")
    parser.add_argument("storyboard_path", nargs='?', help="要评估的视觉故事板文件路径（可选，默认使用 output/{topic}/vision_storyboard）")
    parser.add_argument("example_path", nargs='?', help="对比的例子文件路径（可选，默认使用 output/{topic}/example_explain.md）")
    parser.add_argument("--topic", help="主题（可选，默认从配置文件读取）")
    parser.add_argument("--save", default=True, action="store_true", help="保存评估结果到文件")
    parser.add_argument("--config", default="config/config.yaml", help="配置文件路径")

    args = parser.parse_args()

    judge = VisualStoryboardJudgeAgent(config_path=args.config)

    # 如果没有提供文件路径，使用默认路径
    if not args.storyboard_path or not args.example_path:
        default_storyboard, default_example = judge._get_default_file_paths()
        storyboard_path = args.storyboard_path or default_storyboard
        example_path = args.example_path or default_example
    else:
        storyboard_path = args.storyboard_path
        example_path = args.example_path
    
    main_logger.info(f"将评估故事板文件: {storyboard_path}")
    main_logger.info(f"对比例子文件: {example_path}")

    # 执行评估
    result = judge.evaluate_storyboard(storyboard_path, example_path, args.topic)
    
    # 打印结果
    judge.print_evaluation_result(result)
    
    # 可选保存到文件
    if args.save:
        saved_file = judge.save_evaluation_result(result, storyboard_path)
        if saved_file:
            print(f"\n💾 评估结果已保存到: {saved_file}")


if __name__ == "__main__":
    main() 