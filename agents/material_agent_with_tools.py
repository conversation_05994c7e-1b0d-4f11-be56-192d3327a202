from dotenv import load_dotenv

load_dotenv()

import datetime
import json
import logging
import os
import re
import sys
from typing import Any

# 添加父目录到导入路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import yaml
from camel.agents import ChatAgent
from camel.models import ModelFactory
from camel.societies import RolePlaying
from camel.toolkits import FunctionTool
from camel.types import ModelPlatformType, TaskType

from tools.github_scroller_toolkit import GithubScrollerToolkit
from tools.mermaid_toolkit import MermaidToolkit
from tools.arxiv_recorder_toolkit import ArxivRecorderToolkit

# 设置日志级别为INFO，并配置日志格式
log_file = f"output/material_agent_log_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
os.makedirs(os.path.dirname(log_file), exist_ok=True)

# 配置根日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(),  # 同时输出到控制台
    ],
)

# 获取logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 定义素材规范化输出格式
MATERIAL_FORMAT = """
# 视频标题：[引人注目的标题]

## 视频信息
- **目标受众**：[目标受众详细描述]
- **视频时长**：[预计视频长度]
- **核心意图**：[视频主要目的简述]
- **表达风格**：[视频表达风格描述]

## 内容精华

### [第一部分标题 - 核心概念介绍]
[开场可以直接使用关键视频或图片，配简洁文案]

## 📹 项目介绍视频（如果有录屏视频）

**[观看项目录屏介绍](视频文件路径)**

[简洁描述视频内容，说明视频展示了项目的哪些方面，如：项目主页、功能特色、Star数等，以及观看视频的价值]

![核心概念图](原素材中实际存在的图片路径)

这个[概念/技术/项目]的核心是...（围绕主要内容展开讲解，在关键知识点处自然融入多媒体元素）

**核心能力一：[能力名称]**
- **功能描述**：[用通俗易懂的语言说明这个功能是做什么的]
- **应用场景**：[说明这个功能在什么情况下使用，解决什么实际问题]
- **执行流程**：[简要描述使用这个功能的步骤，如：1. 输入数据 → 2. 配置参数 → 3. 执行处理 → 4. 获得结果]
- **期待结果**：[说明用户使用这个功能后能得到什么样的输出或效果]

**核心能力二：[能力名称]**
- **功能描述**：[功能的主要作用和价值]
- **典型用例**：[具体的使用示例，比如"可以用于分析用户行为数据，生成个性化推荐"]
- **操作方式**：[用户如何使用这个功能，重点是操作逻辑而非代码细节]
- **预期效果**：[使用后的具体成果或改进效果]

### [第二部分标题 - 深入分析]
为了更好地理解[相关概念]，让我们看看这个重要的数据对比：

| 对比项 | 方案A | 方案B |
|--------|-------|-------|
| 原素材中的实际数据... |

从数据可以看出...（基于表格的分析，保持原有的分析逻辑）

同时，通过这个流程图我们可以更清晰地看到整个过程：

![流程示意图](原素材中实际存在的流程图路径)

这个流程包含了...（围绕流程图的详细解释，与前面的内容形成逻辑连接）

### [第三部分标题 - 实际应用/总结]
在实际应用中，我们需要关注...（继续原有的讲解思路）

<原素材中实际存在的演示视频链接>

这个演示视频展示了...（简洁的视频说明，与整体内容融合）

通过以上的分析，我们可以看到...（基于多媒体元素支撑的深入分析，但保持原有的知识结构）

## 关键结论
综合以上分析，我们可以得出：[基于原有讲解逻辑和多媒体展示的综合结论]
"""

# 修改素材编辑者角色提示
MATERIAL_EDITOR_PROMPT = """
作为视频素材编辑专家，你的职责是在保持原始素材讲解逻辑的基础上，优化多媒体素材的匹配和展示效果。

** 核心原则：在原有讲解思路内优先匹配多媒体素材 **

你需要：
1. **保持原有讲解框架**：
   - 分析并保持原素材的知识结构、讲解顺序和内容逻辑
   - 确保核心概念和知识点的完整性和连贯性
   - 维持原有的教学或分析思路

2. **智能匹配多媒体素材**：
   - 在现有内容框架下，为每个知识点寻找最合适的多媒体元素
   - 让多媒体素材起到增强理解和视觉展示的作用
   - 确保多媒体元素与内容高度相关且有助于理解

3. **开场内容优化**：
   - 开场部分可以直接使用核心视频或关键图片，配以简洁的介绍文案
   - 减少冗长的文字开头，快速进入主题
   - 用视觉元素立即抓住观众注意力

4. **GitHub项目星标数处理（重要规则）**：
   - **如果是GitHub项目，开篇第一句必须提到项目的星标数量，使用震撼性表述**
   - 推荐表述方式：
     * "星标狂砍40K！这个项目火爆全网！"
     * "GitHub上超过30K星标的热门项目！"
     * "两周内星标暴涨2000+，开发者都在关注！"
     * "这个项目的星标数让人震惊，已经突破25K！"
     * "Star数量飙升至15K，成为同类项目中的佼佼者！"
   - **星标数信息获取方式**：
     * 优先从原始markdown内容中查找星标相关信息
     * 查找类似"⭐️ 12,345"、"stars: 12345"、"★ 12K"等表述
     * 如果找到对应的xx_stars.csv文件，可以参考其中的数据
     * 如果无法确定具体数字，可以使用"大量星标"、"高人气项目"等表述
   - **关键限制：星标信息只在开篇文案中体现，严禁单独创建星标相关的章节或分镜**
     * 不要创建"项目热度"、"社区反响"、"GitHub表现"等以星标为主题的独立部分
     * 星标数据应该自然融入到开篇的技术介绍中，作为项目背景的一部分
     * 重点应该放在项目的技术价值和功能特色上，星标只是吸引注意力的开场

5. **GitHub项目Demo素材特殊处理（高优先级要求）**：
   - **如果是GitHub项目且原素材中包含Demo相关素材，必须给予最高优先级处理**
   - **Demo素材识别**：
     * 查找包含"demo"、"演示"、"示例"、"example"、"showcase"等关键词的图片或视频
     * 查找项目运行效果图、界面截图、功能展示图片
     * 查找项目使用流程的GIF动图或视频
     * 查找项目输出结果的展示图片
   - **Demo素材处理规则**：
     * **必须为Demo素材单独创建一个专门的章节**，如"### 项目Demo演示"或"### 功能效果展示"
     * Demo章节应该安排在项目介绍之后、技术细节之前的重要位置
     * 为每个Demo素材提供详细的说明，包括展示的功能、使用场景、效果亮点
     * 如果有多个Demo素材，按重要性和展示效果排序
   - **Demo章节内容结构**：
     * 简要介绍Demo的价值和意义
     * 逐一展示Demo素材，每个都配以详细解释
     * 强调Demo展示的核心功能和用户体验
     * 说明Demo如何体现项目的实用价值

6. **核心能力介绍优化**（重要）：
   - **避免过度展示代码细节**，重点介绍功能的实际作用和价值
   - 对每个核心能力，重点说明：
     * 功能描述：用通俗易懂的语言解释功能作用
     * 应用场景：说明什么时候使用这个功能
     * 执行流程：简要描述使用步骤（输入→处理→输出）
     * 期待结果：明确说明用户能得到什么效果
   - **如果需要展示代码，应该简化并重点解释代码的业务逻辑而非技术细节**
   - 更多关注功能的实用性和用户体验

7. **视频素材保留**（关键要求）：
   - **必须保留所有视频链接**，特别是录屏视频作为开篇宣传用，不要删除
   - **不得删除或修改视频素材路径**，如[查看项目介绍视频](output/项目名/github_screen.mp4)
   - 确保视频说明文字与视频内容相符合
   - 视频链接应该保持在适当的位置，如文档开头或相关章节

8. 优先确保markdown文档各部分结构完整，包括标题、视频信息、内容各部分和结论
9. 根据视频长度参考内容密度范围，但不必严格限制字数
10. 根据风格要求调整表述方式，但不改变基础事实
11. 确保内容深度与受众知识水平匹配
12. **多媒体素材使用策略**：
   - 在关键概念处匹配相关图表、示意图来辅助理解
   - 在技术讲解时适时插入简化的代码示例、流程图等
   - 利用表格、对比图等可视化元素展示数据
   - 确保多媒体元素自然融入，不干扰内容流畅性
   - 多媒体元素引用要简洁明确，配以适当说明
   - **特别重点：Demo素材必须单独成章，给予最高展示优先级**

对于不同长度的视频，内容密度仅作参考：
- 1-2分钟视频：3-4个核心要点，约450-600字，配合2-3个关键多媒体元素，必含Demo章节
- 3-5分钟视频：5-7个重要要点，约900-1500字，配合4-6个多媒体元素，Demo章节详细展示
- 5-10分钟视频：更全面的内容，约1500-3000字，充分利用多媒体元素，Demo章节全面覆盖

**关键要求**：保持原有的知识框架和讲解逻辑，在此基础上智能匹配多媒体素材来增强内容效果。最重要的是确保markdown文档结构完整，内容有价值，多媒体元素在合适的位置发挥增强理解的作用。对于核心能力部分，重点关注功能价值而非技术实现细节。

**特别强调**：
1. 如果是GitHub项目，开篇第一句必须以震撼性的星标数据开始吸引注意力，但星标信息只在开篇提及，不要为此单独创建章节！
2. **如果发现Demo素材，必须单独创建Demo展示章节，这是展示项目价值的核心环节！**
3. 重点应该是项目的技术内容、实用价值和Demo效果展示。
"""

# 修改质量检查者角色提示
QUALITY_REVIEWER_PROMPT = """
作为质量检查专家，你需要首先验证markdown文档的完整性，然后审查编辑后的素材内容质量：

## 完整性检查（最高优先级）
1. 文档结构完整性：必须包含以下所有部分
   - 视频标题
   - 视频信息（目标受众、视频时长、核心意图、表达风格）
   - 内容主体（至少包含1-3个主要部分）
   - 关键结论

2. 内容完整性：所有部分是否有实质性内容，不存在仅有标题无内容的情况
   - 每个部分都必须有足够的实质内容
   - 多模态元素是否在正文中直接引用并附有简要说明
   - **必须检查视频素材是否保留**，特别是项目介绍录屏视频链接

## 内容质量检查
1. 事实准确性：内容必须与原始素材中的事实保持一致，不允许有事实性错误
2. 内容密度：根据视频长度，内容量是否恰当，既不过于简略也不过于冗长
3. 受众适配：内容深度和专业程度是否符合目标受众的知识水平
4. 风格一致：表述方式是否符合要求，同时不扭曲基础事实
5. 结构清晰：布局是否合理，标题是否能有效吸引目标受众
6. 多模态元素：引用元素是否与所在段落内容语义匹配
7. 吸引力：内容是否既能吸引目标受众，又不夸大或误导

若发现任何结构不完整问题（如缺少某个完整部分），请首先指出这是一个严重的完整性问题，并要求优先修复。
内容质量问题则可以作为次要问题提出。

请提供具体、建设性的反馈，帮助改进素材质量，首先确保文档完整性，然后是事实准确性和内容表达的优化。
"""


class MaterialAgent:
    """
    视频素材规范化代理

    职责：
    1. 分析输入的素材markdown文件
    2. 解析用户目的描述，包括目标人群、主题内容、分析意图和风格偏好
    3. 根据用户意图和受众特点，重构规范化的视频素材markdown
    4. 通过角色扮演检查和优化生成内容的质量
    5. 输出最终的规范化视频素材markdown文件
    """

    class Config:
        """素材代理配置子模块"""

        def __init__(self, config_dict=None):
            """初始化配置"""
            if not config_dict:
                config_dict = {}

            # 模型配置
            self.model = {
                "type": config_dict.get("model", {}).get("type", "openai/gpt-4o-mini"),
                "temperature": config_dict.get("model", {}).get("temperature", 0.7),
                "api": config_dict.get("model", {}).get("api", {}),
            }

            # 文件配置
            self.files = {
                "output_dir": config_dict.get("files", {}).get("output_dir", "output"),
                "material_file": config_dict.get("files", {}).get("material_file", "output/video_material.md"),
            }

            # 素材代理特定配置
            material_config = config_dict.get("material", {})
            self.max_rounds = material_config.get("max_rounds", 3)
            self.output_dir = material_config.get("output_dir", "output")
            self.material_file = material_config.get("material_file", "output/video_material.md")
            self.analysis_techniques = material_config.get(
                "analysis_techniques",
                ["audience_focus", "content_refinement", "style_adaptation", "multimedia_handling"],
            )
            self.default_video_length = material_config.get("default_video_length", "2分钟")
            self.audience_presets = material_config.get(
                "audience_presets", ["研究人员", "学生", "从业者", "技术爱好者", "管理人员"]
            )
            self.style_presets = material_config.get(
                "style_presets", ["学术严谨", "通俗易懂", "批判性思考", "实用指导", "趣味性解读"]
            )

            # 解析材料源配置
            self.sources = material_config.get("sources", {})

            # 查找启用的源
            self.enabled_source = None
            self.enabled_source_config = None
            for source_type, source_config in self.sources.items():
                if source_config.get("enabled", False):
                    self.enabled_source = source_type
                    self.enabled_source_config = source_config
                    break

            # 兼容性支持：GitHub项目URL配置
            if self.enabled_source == "github":
                self.github_url = self.enabled_source_config.get("url", None)
            else:
                # 向后兼容：如果没有启用的GitHub源，尝试从根级别获取
                github_config = config_dict.get("github", {})
                self.github_url = github_config.get("project_url", None)
                if not self.github_url:
                    self.github_url = config_dict.get("project_url", config_dict.get("github_url", None))

    def __init__(self, config_path="config/config.yaml"):
        """初始化素材规范化代理"""
        # 加载配置
        config_dict = self._load_yaml_config(config_path)

        # 初始化配置子模块
        self.config = self.Config(config_dict)

        # 初始化模型
        self.model = self._create_model()

        logger.info("素材规范化代理初始化完成")

        # 初始化工具包实例，但不直接使用，通过工具代理调用
        self._github_toolkit = GithubScrollerToolkit()
        self._mermaid_toolkit = MermaidToolkit()
        self._arxiv_toolkit = ArxivRecorderToolkit()

        # 创建工具函数
        # 注意：不使用parameter_descriptions参数，而是依赖函数自身的文档字符串和参数注释
        github_tool = FunctionTool(self._github_toolkit.record_github_scroll_video)
        mermaid_tool = FunctionTool(self._mermaid_toolkit.convert_mermaid_to_png)
        arxiv_tool = FunctionTool(self._arxiv_toolkit.record_arxiv_video)

        self._tools = [github_tool, mermaid_tool, arxiv_tool]

        logger.info("已加载github录屏、arxiv录屏和mermaid转png工具")

    def _create_tool_agent(self, system_prompt):
        """创建一个用于工具调用的代理"""
        return ChatAgent(system_message=system_prompt, model=self.model, tools=self._tools)

    def _convert_mermaid_with_agent(self, mmd_file, png_file, mmd_code=None):
        """使用代理调用转换Mermaid的功能"""
        system_prompt = """
        你是一个专业的文档处理助手，负责分析Mermaid图表并决定是否需要将其转换为PNG图像。
        当提供了有效的Mermaid文件或代码时，你应该调用convert_mermaid_to_png工具将其转换为PNG图像。
        请仔细检查Mermaid文件或代码内容，确保它是有效的，才进行转换。
        """

        agent = self._create_tool_agent(system_prompt)

        # 构建用户消息
        if mmd_code:
            prompt = f"""
            请分析以下Mermaid代码，并将其转换为PNG图像:

            文件内容:
            ```
            {mmd_code}
            ```

            需要保存的文件路径: {png_file}
            临时Mermaid文件路径: {mmd_file}

            如果代码有效，请调用convert_mermaid_to_png工具进行转换。
            """
        else:
            prompt = f"""
            请将以下Mermaid文件转换为PNG图像:

            Mermaid文件路径: {mmd_file}
            需要保存的PNG文件路径: {png_file}

            如果文件存在且有效，请调用convert_mermaid_to_png工具进行转换。
            """

        # 获取代理回复
        response = agent.step(prompt)

        # 检查回复中是否包含工具调用结果
        result = None

        # 尝试不同的属性结构来获取工具结果
        if hasattr(response, "tool_results"):
            # 新版API结构
            for key, value in response.tool_results.items():
                if key == "convert_mermaid_to_png":
                    result = value
                    break
        elif hasattr(response, "tool_calls"):
            # 可能的替代结构
            for call in response.tool_calls:
                if call.get("name") == "convert_mermaid_to_png":
                    result = call.get("result")
                    break
        else:
            # 如果找不到工具结果，但PNG文件确实存在，则创建一个成功结果
            if os.path.exists(png_file):
                result = {
                    "status": "success",
                    "message": f"Mermaid图表已成功转换为PNG: {png_file}",
                    "file_path": png_file,
                }
                logger.info(f"未找到工具调用结果，但PNG文件存在，视为成功。文件: {png_file}")

        return result

    def _record_github_with_agent(self, url, output_path, **kwargs):
        """使用代理决定是否录制GitHub项目视频"""
        if not url:
            logger.info("未提供GitHub URL，跳过录屏")
            return None

        # 首先检查文件是否已经存在，如果存在直接返回成功结果
        if os.path.exists(output_path):
            logger.info(f"发现GitHub录屏视频已存在: {output_path}，跳过录制")
            return {"status": "success", "message": f"视频已存在: {output_path}", "file_path": output_path}

        # 尝试直接调用工具包函数，跳过代理
        try:
            logger.info("尝试直接调用GitHub录屏工具...")

            # 构建参数
            default_params = {
                "duration": 12,
                "width": 1920,
                "height": 1080,
                "fps": 15,
                "smooth_factor": 0.2,
                "title_focus": 1,
                "star_focus": 2,
                "zoom_factor": 2.0,
                "readme_pause": 1.0,
            }

            # 更新默认参数
            params = {**default_params, **kwargs}

            # 直接调用工具包函数
            result = self._github_toolkit.record_github_scroll_video(url=url, output_path=output_path, **params)

            if result and result.get("status") == "success":
                logger.info(f"GitHub录屏直接调用成功: {output_path}")
                return result

            logger.info("直接调用GitHub录屏工具失败，尝试通过代理调用...")
        except Exception as e:
            logger.warning(f"直接调用GitHub录屏工具异常: {str(e)}，尝试通过代理调用...")

        # 如果直接调用失败，使用代理调用作为备选方案
        system_prompt = """
        你是一个专业的GitHub项目分析助手，负责决定是否需要为GitHub项目录制视频。
        当提供了有效的GitHub项目URL时，你应该调用record_github_scroll_video工具录制项目浏览视频。
        请仔细检查URL是否有效且指向一个GitHub仓库，才进行录制。
        """

        agent = self._create_tool_agent(system_prompt)

        # 更新默认参数
        default_params = {
            "duration": 12,
            "width": 1920,
            "height": 1080,
            "fps": 15,
            "smooth_factor": 0.2,
            "title_focus": 1,
            "star_focus": 2,
            "zoom_factor": 2.0,
            "readme_pause": 1.0,
        }
        params = {**default_params, **kwargs}
        params_str = ", ".join([f"{k}={v}" for k, v in params.items()])

        # 构建用户消息
        prompt = f"""
        请分析以下GitHub项目URL，并决定是否录制项目视频:

        GitHub URL: {url}
        视频输出路径: {output_path}

        其他参数:
        {params_str}

        如果URL有效且指向GitHub仓库，请调用record_github_scroll_video工具录制视频。
        """

        # 获取代理回复
        response = agent.step(prompt)

        # 记录响应对象的结构和属性，便于调试
        logger.info(f"代理响应类型: {type(response).__name__}")
        logger.info(f"代理响应属性: {', '.join(dir(response))}")

        # 检查回复中是否包含工具调用结果
        result = None

        # 尝试不同的属性结构来获取工具结果
        if hasattr(response, "tool_results") and response.tool_results:
            # 新版API结构
            logger.info(f"工具结果: {response.tool_results}")
            for key, value in response.tool_results.items():
                if key == "record_github_scroll_video":
                    result = value
                    break
        elif hasattr(response, "tool_calls") and response.tool_calls:
            # 可能的替代结构
            logger.info(f"工具调用: {response.tool_calls}")
            for call in response.tool_calls:
                if call.get("name") == "record_github_scroll_video":
                    result = call.get("result")
                    break

        # 检查回复内容中是否有相关信息
        if hasattr(response, "msg") and hasattr(response.msg, "content"):
            content = response.msg.content
            if "成功" in content and output_path in content:
                logger.info(f"从响应内容中检测到成功信息: {content}")

        # 如果找不到工具结果，但视频文件确实存在，则创建一个成功结果
        if result is None and os.path.exists(output_path):
            result = {"status": "success", "message": f"视频已成功保存到 {output_path}", "file_path": output_path}
            logger.info(f"未找到工具调用结果，但视频文件存在，视为成功。文件: {output_path}")

        return result

    def _record_arxiv_with_agent(self, url, output_path, **kwargs):
        """使用代理决定是否录制ArXiv论文视频"""
        if not url:
            logger.info("未提供ArXiv URL，跳过录屏")
            return None

        # 首先检查文件是否已经存在，如果存在直接返回成功结果
        if os.path.exists(output_path):
            logger.info(f"发现ArXiv录屏视频已存在: {output_path}，跳过录制")
            return {"status": "success", "message": f"视频已存在: {output_path}", "file_path": output_path}

        # 尝试直接调用工具包函数，跳过代理
        try:
            logger.info("尝试直接调用ArXiv录屏工具...")

            # 构建参数
            default_params = {
                "duration": 8,
                "width": 1920,
                "height": 1080,
                "fps": 15,
                "smooth_factor": 0.2,
                "title_focus": 4,
                "zoom_factor": 2.0,
                "abstract_pause": 0.0,
            }

            # 更新默认参数
            params = {**default_params, **kwargs}

            # 直接调用工具包函数
            result = self._arxiv_toolkit.record_arxiv_video(url=url, output_path=output_path, **params)

            if result and result.get("status") == "success":
                logger.info(f"ArXiv录屏直接调用成功: {output_path}")
                return result

            logger.info("直接调用ArXiv录屏工具失败，尝试通过代理调用...")
        except Exception as e:
            logger.warning(f"直接调用ArXiv录屏工具异常: {str(e)}，尝试通过代理调用...")

        # 如果直接调用失败，使用代理调用作为备选方案
        system_prompt = """
        你是一个专业的ArXiv论文分析助手，负责决定是否需要为ArXiv论文页面录制视频。
        当提供了有效的ArXiv论文页面URL时，你应该调用record_arxiv_video工具录制论文浏览视频。
        请仔细检查URL是否有效且指向一个ArXiv论文页面，才进行录制。
        """

        agent = self._create_tool_agent(system_prompt)

        # 更新默认参数
        default_params = {
            "duration": 8,
            "width": 1920,
            "height": 1080,
            "fps": 15,
            "smooth_factor": 0.2,
            "title_focus": 4,
            "zoom_factor": 2.0,
            "abstract_pause": 4.0,
        }
        params = {**default_params, **kwargs}
        params_str = ", ".join([f"{k}={v}" for k, v in params.items()])

        # 构建用户消息
        prompt = f"""
        请分析以下ArXiv论文页面URL，并决定是否录制论文视频:

        ArXiv URL: {url}
        视频输出路径: {output_path}

        其他参数:
        {params_str}

        如果URL有效且指向ArXiv论文页面，请调用record_arxiv_video工具录制视频。
        """

        # 获取代理回复
        response = agent.step(prompt)

        # 检查回复中是否包含工具调用结果
        result = None

        # 尝试不同的属性结构来获取工具结果
        if hasattr(response, "tool_results") and response.tool_results:
            # 新版API结构
            logger.info(f"工具结果: {response.tool_results}")
            for key, value in response.tool_results.items():
                if key == "record_arxiv_video":
                    result = value
                    break
        elif hasattr(response, "tool_calls") and response.tool_calls:
            # 可能的替代结构
            logger.info(f"工具调用: {response.tool_calls}")
            for call in response.tool_calls:
                if call.get("name") == "record_arxiv_video":
                    result = call.get("result")
                    break

        # 检查回复内容中是否有相关信息
        if hasattr(response, "msg") and hasattr(response.msg, "content"):
            content = response.msg.content
            if "成功" in content and output_path in content:
                logger.info(f"从响应内容中检测到成功信息: {content}")

        # 如果找不到工具结果，但视频文件确实存在，则创建一个成功结果
        if result is None and os.path.exists(output_path):
            result = {"status": "success", "message": f"视频已成功保存到 {output_path}", "file_path": output_path}
            logger.info(f"未找到工具调用结果，但视频文件存在，视为成功。文件: {output_path}")

        return result

    def _load_yaml_config(self, config_path):
        """从YAML文件加载配置"""
        try:
            with open(config_path) as file:
                config = yaml.safe_load(file)
            logger.info("从 %s 加载配置", config_path)
            return config
        except Exception as e:
            logger.error(f"加载配置出错: {str(e)}")
            return {}

    def _create_model(self):
        """创建模型实例"""
        # 从配置中获取API设置
        api_config = self.config.model["api"]

        return ModelFactory.create(
            model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
            model_type=self.config.model["type"],
            api_key=api_config.get("openai_compatibility_api_key"),
            url=api_config.get("openai_compatibility_api_base_url"),
        )

    def read_material(self, material_file_path: str) -> str:
        """
        读取原始素材文件

        参数:
        - material_file_path: 素材文件路径

        返回:
        - str: 素材内容
        """
        try:
            with open(material_file_path, encoding="utf-8") as f:
                content = f.read()
            logger.info(f"成功读取素材文件: {material_file_path}, 长度: {len(content)} 字符")
            return content
        except Exception as e:
            logger.error(f"读取素材文件失败: {str(e)}")
            raise

    def extract_multimedia_elements(self, content: str) -> dict[str, list[str]]:
        """
        从Markdown内容中提取多模态元素

        参数:
        - content: Markdown内容

        返回:
        - Dict: 包含多种多模态元素的字典
        """
        multimedia = {
            "images": [],
            "videos": [],
            "gifs": [],
            "audios": [],
            "code_blocks": [],
            "tables": [],
            "formulas": [],
            "lists": [],
            "mmd_files": [],  # 添加mmd文件类型
            "project_name": None,  # 添加项目名称，用于构建路径前缀
        }

        try:
            # 提取图片链接
            image_pattern = r"!\[(.*?)\]\((.*?)\)"
            images = re.findall(image_pattern, content)
            multimedia["images"] = [{"alt": img[0], "url": img[1]} for img in images]

            # 尝试区分GIF
            for img in multimedia["images"][:]:
                if img["url"].lower().endswith(".gif"):
                    multimedia["gifs"].append(img)
                    multimedia["images"].remove(img)

            # 提取视频链接（匹配常见的视频嵌入格式）
            video_patterns = [
                r"<video.*?src=[\"\'](.*?)[\"\'].*?>.*?</video>",
                r"\[video\]\((.*?)\)",
                r"!\[video\]\((.*?)\)",
            ]
            for pattern in video_patterns:
                videos = re.findall(pattern, content, re.DOTALL)
                for video in videos:
                    multimedia["videos"].append({"url": video})

            # 提取音频链接
            audio_patterns = [
                r"<audio.*?src=[\"\'](.*?)[\"\'].*?>.*?</audio>",
                r"\[audio\]\((.*?)\)",
                r"!\[audio\]\((.*?)\)",
            ]
            for pattern in audio_patterns:
                audios = re.findall(pattern, content, re.DOTALL)
                for audio in audios:
                    multimedia["audios"].append({"url": audio})

            # 提取代码块
            code_pattern = r"```(.*?)\n(.*?)```"
            code_blocks = re.findall(code_pattern, content, re.DOTALL)
            multimedia["code_blocks"] = [{"language": cb[0].strip(), "code": cb[1].strip()} for cb in code_blocks]

            # 提取表格
            table_pattern = r"(\|.*\|[\r\n]+\|[\s-]*\|[\s-]*\|.*[\r\n]+(\|.*\|[\r\n]+)*)"
            tables = re.findall(table_pattern, content)
            multimedia["tables"] = [{"content": table[0].strip()} for table in tables]

            # 提取数学公式
            formula_patterns = [
                r"\$\$(.*?)\$\$",  # 块级公式
                r"\$(.*?)\$",  # 行内公式
            ]
            formulas = []
            for pattern in formula_patterns:
                matches = re.findall(pattern, content, re.DOTALL)
                formulas.extend(matches)
            multimedia["formulas"] = [{"formula": formula.strip()} for formula in formulas]

            # 提取列表（有序和无序）
            list_patterns = [
                r"((?:^|\n)[\s]*[\-\*].*(?:\n[\s]*[\-\*].*)*)",  # 无序列表
                r"((?:^|\n)[\s]*\d+\..*(?:\n[\s]*\d+\..*)*)",  # 有序列表
            ]
            lists = []
            for pattern in list_patterns:
                matches = re.findall(pattern, content, re.DOTALL)
                lists.extend(matches)
            multimedia["lists"] = [{"content": lst.strip()} for lst in lists]

            # 提取.mmd文件引用
            mmd_files = []
            # 查找直接链接到.mmd文件的引用
            mmd_file_pattern = r"\[([^\]]+)\]\(([^)]+\.mmd)\)"
            mmd_matches = re.findall(mmd_file_pattern, content)
            for text, path in mmd_matches:
                mmd_files.append({"text": text, "path": path})

            # 查找代码引用中的.mmd文件路径
            mmd_code_pattern = r"`([^`]+\.mmd)`"
            mmd_code_matches = re.findall(mmd_code_pattern, content)
            for path in mmd_code_matches:
                mmd_files.append({"text": "Mermaid文件", "path": path})

            # 查找可能嵌入的.mmd文件路径
            embedded_mmd_pattern = r'(?:src|href)=[\'"]([^\'"\s]+\.mmd)[\'"]'
            embedded_matches = re.findall(embedded_mmd_pattern, content)
            for path in embedded_matches:
                mmd_files.append({"text": "嵌入Mermaid文件", "path": path})

            multimedia["mmd_files"] = mmd_files

            logger.info(
                f"从素材中提取多模态元素: {len(multimedia['images'])}张图片, {len(multimedia['gifs'])}个GIF, "
                f"{len(multimedia['videos'])}个视频, {len(multimedia['audios'])}个音频, "
                f"{len(multimedia['code_blocks'])}个代码块, {len(multimedia['tables'])}个表格, "
                f"{len(multimedia['formulas'])}个公式, {len(multimedia['lists'])}个列表, "
                f"{len(multimedia['mmd_files'])}个Mermaid文件"
            )
            return multimedia

        except Exception as e:
            logger.error(f"提取多模态元素失败: {str(e)}")
            return multimedia

    def analyze_purpose(self, purpose_description: str) -> dict[str, Any]:
        """
        分析用户目的描述

        参数:
        - purpose_description: 用户目的描述

        返回:
        - Dict: 包含目标人群、主题内容、意图、风格和视频长度的字典
        """
        logger.info("开始分析用户目的描述")

        try:
            # 创建ChatAgent进行分析
            system_prompt = """
            你是一个精准的内容目的分析专家。你的任务是从用户描述中提取以下关键信息：
            1. 目标人群 - 视频内容面向的具体受众群体
            2. 主题内容 - 视频将要讲述的具体主题或素材内容
            3. 意图分析 - 制作这个视频的目的，如教育、分析、批判等
            4. 风格偏好 - 视频内容应该采用的表达风格
            5. 视频长度 - 视频的时长要求，如1分钟、5分钟等

            请分析提供的描述，并以结构化JSON格式返回结果，包含以上五个关键字段。
            如果描述中未明确指定视频长度，则默认为2分钟。

            示例输入: "给技术爱好者介绍这些AI项目，主要目的是分析趋势和热点，侧重客观中立的表述，视频长度为3分钟"

            示例输出:
            ```json
            {
                "target_audience": "技术爱好者",
                "content_theme": "AI项目",
                "intention": "分析趋势和热点",
                "style_preference": "客观中立",
                "video_length": "3分钟"
            }
            ```

            务必仔细分析输入内容，确保提取出所有明确指定的信息。只有在信息真正缺失时才使用默认值。
            返回的JSON必须包含所有五个字段，确保格式正确无误。
            """

            agent = ChatAgent(system_message=system_prompt, model=self.model)

            # 构建用户消息
            user_message = f"""
            请分析以下视频制作目的描述，并提取关键信息：

            "{purpose_description}"

            请以JSON格式返回分析结果，必须包含目标人群、主题内容、意图分析、风格偏好和视频长度五个字段。
            """

            # 获取回复
            response = agent.step(user_message)

            # 从回复中提取JSON
            purpose_json = self._extract_json(response.msg.content)

            # 调试输出
            logger.info(f"提取到的目的JSON: {json.dumps(purpose_json, ensure_ascii=False)}")

            # 如果JSON提取失败或为空，尝试直接从文本中提取关键信息
            if not purpose_json or len(purpose_json) < 2:
                logger.warning("JSON提取失败，尝试从文本中直接提取关键信息")
                purpose_json = self._extract_purpose_directly(purpose_description)

            # 添加缺失的字段和默认值
            purpose_json = self._ensure_purpose_fields(purpose_json, purpose_description)

            logger.info(
                f"目的分析完成: 目标人群={purpose_json.get('target_audience', '未指定')}, 主题内容={purpose_json.get('content_theme', '未指定')}, 意图={purpose_json.get('intention', '未指定')}, 风格={purpose_json.get('style_preference', '未指定')}, 视频长度={purpose_json.get('video_length', '2分钟')}"
            )
            return purpose_json

        except Exception as e:
            logger.error(f"分析用户目的描述失败: {str(e)}")
            # 返回默认分析结果
            return {
                "target_audience": "一般受众",
                "content_theme": "技术内容",
                "intention": "信息传递",
                "style_preference": "客观分析",
                "video_length": self.config.default_video_length,
            }

    def _ensure_purpose_fields(self, purpose_json: dict[str, Any], purpose_description: str) -> dict[str, Any]:
        """确保目的分析结果包含所有必要字段，并填充默认值"""
        # 检查必要的字段
        required_fields = {
            "target_audience": "一般受众",
            "content_theme": "未指定主题",
            "intention": "信息传递",
            "style_preference": "客观中立",
            "video_length": self.config.default_video_length,
        }

        # 规范化键名
        key_mapping = {
            "目标人群": "target_audience",
            "目标受众": "target_audience",
            "主题内容": "content_theme",
            "内容主题": "content_theme",
            "意图分析": "intention",
            "意图": "intention",
            "风格偏好": "style_preference",
            "表达风格": "style_preference",
            "视频长度": "video_length",
            "时长": "video_length",
        }

        # 规范化键名
        normalized_json = {}
        for k, v in purpose_json.items():
            if k in key_mapping:
                normalized_json[key_mapping[k]] = v
            else:
                normalized_json[k] = v

        # 填充缺失的字段
        for field, default_value in required_fields.items():
            if field not in normalized_json or not normalized_json[field]:
                # 尝试直接从描述中提取
                if field == "video_length" and "分钟" in purpose_description:
                    match = re.search(r"(\d+)\s*分钟", purpose_description)
                    if match:
                        normalized_json[field] = f"{match.group(1)}分钟"
                    else:
                        normalized_json[field] = default_value
                else:
                    normalized_json[field] = default_value

        return normalized_json

    def _extract_purpose_directly(self, purpose_description: str) -> dict[str, Any]:
        """直接从目的描述中提取关键信息"""
        result = {}

        # 提取目标人群
        audience_patterns = [
            r"给(.*?)介绍",
            r"面向(.*?)的",
            r"(.*?)为受众",
            r"目标受众是(.*?)[,，。]",
            r"目标人群是(.*?)[,，。]",
        ]
        for pattern in audience_patterns:
            match = re.search(pattern, purpose_description)
            if match:
                result["target_audience"] = match.group(1).strip()
                break

        # 提取主题内容
        content_patterns = [r"介绍(.*?)，", r"讲解(.*?)，", r"关于(.*?)的", r"主题是(.*?)[,，。]"]
        for pattern in content_patterns:
            match = re.search(pattern, purpose_description)
            if match:
                result["content_theme"] = match.group(1).strip()
                break

        # 提取意图
        intention_patterns = [r"目的是(.*?)[,，。]", r"旨在(.*?)[,，。]", r"为了(.*?)[,，。]", r"意图是(.*?)[,，。]"]
        for pattern in intention_patterns:
            match = re.search(pattern, purpose_description)
            if match:
                result["intention"] = match.group(1).strip()
                break

        # 提取风格
        style_patterns = [r"侧重(.*?)的表述", r"风格(.*?)[,，。]", r"采用(.*?)的方式", r"以(.*?)的风格"]
        for pattern in style_patterns:
            match = re.search(pattern, purpose_description)
            if match:
                result["style_preference"] = match.group(1).strip()
                break

        # 提取视频长度
        length_patterns = [r"(\d+)\s*分钟", r"时长(\d+)", r"长度为(\d+)", r"视频长度[为是](\d+)"]
        for pattern in length_patterns:
            match = re.search(pattern, purpose_description)
            if match:
                result["video_length"] = f"{match.group(1)}分钟"
                break

        return result

    def _extract_json(self, text: str) -> dict[str, Any]:
        """从文本中提取JSON内容"""
        try:
            # 查找JSON代码块
            json_pattern = r"```(?:json)?(.*?)```"
            import re

            matches = re.findall(json_pattern, text, re.DOTALL)

            if matches:
                for match in matches:
                    try:
                        # 尝试解析JSON
                        json_str = match.strip()
                        return json.loads(json_str)
                    except Exception as e:
                        logger.error(f"解析JSON出错: {str(e)}")
                        continue

            # 如果没有在代码块中找到，尝试在文本中查找JSON
            start_idx = text.find("{")
            end_idx = text.rfind("}") + 1

            if start_idx >= 0 and end_idx > start_idx:
                json_str = text[start_idx:end_idx]
                return json.loads(json_str)

            raise ValueError("未找到有效的JSON内容")

        except Exception as e:
            logger.error(f"提取JSON出错: {str(e)}")
            return {}

    def _extract_markdown(self, text: str) -> str:
        """从文本中提取Markdown内容"""
        # 首先检查是否有明确标记的Markdown段落
        markdown_markers = [
            "---MARKDOWN_BEGINS---",
            "```markdown",
            "以下是规范化的Markdown素材：",
            "以下是优化后的Markdown素材：",
            "# ",  # 以标题标记开始的内容也可能是Markdown
        ]

        # 尝试通过明确的标记寻找Markdown起始位置
        start_index = -1
        for marker in markdown_markers:
            if marker in text:
                possible_start = text.find(marker)
                if start_index == -1 or possible_start < start_index:
                    start_index = possible_start

        # 如果找到了标记，从标记处开始提取
        if start_index != -1:
            # 从标记处开始，但跳过标记本身（除非是Markdown标题）
            if text[start_index : start_index + 2] == "# ":
                extracted_text = text[start_index:]
            else:
                # 找到标记后的第一个换行
                next_line = text.find("\n", start_index)
                if next_line != -1:
                    extracted_text = text[next_line + 1 :]
                else:
                    extracted_text = text[start_index:]

            # 寻找终止标记
            end_markers = [
                "---MARKDOWN_ENDS---",
                "```",
                "以上是优化后的素材",
                "以上是我优化的",
                "希望这个优化",
                "这样的修改",
            ]
            for marker in end_markers:
                if marker in extracted_text:
                    extracted_text = extracted_text.split(marker)[0]

            return extracted_text.strip()

        # 如果没有明确的标记，尝试通过Markdown代码块提取
        markdown_pattern = r"```(?:markdown)?\s*([\s\S]*?)```"
        matches = re.findall(markdown_pattern, text, re.DOTALL)

        if matches:
            # 选择最长的匹配结果，避免提取到不完整的片段
            return max(matches, key=len).strip()

        # 如果没有明确的Markdown代码块，但文本看起来已经是Markdown格式（包含标题）
        if re.search(r"^#\s+", text, re.MULTILINE):
            # 尝试找到第一个标题
            match = re.search(r"^#\s+", text, re.MULTILINE)
            if match:
                start = match.start()
                return text[start:].strip()

        # 无法提取有效的Markdown，返回原始文本
        logger.warning("无法提取Markdown代码块，使用完整响应文本")
        return text

    def _validate_material_completeness(self, content: str) -> bool:
        """
        验证生成的素材是否包含所有必要部分

        参数:
        - content: 生成的素材内容

        返回:
        - bool: 是否完整
        """
        # 检查必要的部分是否存在
        required_sections = [
            r"#\s+.*?标题|#\s+.*?题目|#\s+.*?[A-Za-z]",  # 视频标题
            r"##\s+视频信息|##\s+内容信息",  # 视频信息部分
            r"目标受众|受众群体|观众",  # 目标受众信息
            r"视频时长|视频长度|时长",  # 视频时长信息
            r"核心意图|主要目的|目的|意图",  # 核心意图信息
            r"表达风格|风格|表述风格",  # 表达风格信息
            r"##\s+内容|##\s+正文|##\s+内容精华",  # 内容主体部分
            r"###\s+.*?",  # 至少一个内容小节
            r"##\s+.*?结论|##\s+总结|##\s+关键结论",  # 结论部分
        ]

        missing_sections = []
        for section_pattern in required_sections:
            if not re.search(section_pattern, content, re.IGNORECASE):
                missing_sections.append(section_pattern)

        if missing_sections:
            logger.warning(f"素材内容缺少以下部分: {missing_sections}")
            return False

        # 检查内容部分是否有实质内容（不仅仅是标题）
        content_sections = re.findall(r"###\s+.*?\n(.*?)(?=##|\Z)", content, re.DOTALL)
        if not content_sections or all(len(section.strip()) < 50 for section in content_sections):
            logger.warning("素材内容部分缺少实质性内容")
            return False

        return True

    def _validate_material_multimedia(self, content: str, original_multimedia: dict[str, list[Any]]) -> bool:
        """
        验证生成的素材中引用的多媒体元素是否在原素材中存在

        参数:
        - content: 生成的素材内容
        - original_multimedia: 原素材中的多媒体元素

        返回:
        - bool: 是否所有引用的多媒体元素都在原素材中存在
        """
        invalid_references = []

        # 检查图片引用
        image_references = re.findall(r"!\[(.*?)\]\((.*?)\)", content)
        original_image_urls = [img.get("url") for img in original_multimedia.get("images", [])]
        original_image_urls += [gif.get("url") for gif in original_multimedia.get("gifs", [])]

        for _, img_url in image_references:
            if (
                img_url
                and img_url not in original_image_urls
                and not any(url.endswith(img_url) for url in original_image_urls)
            ):
                invalid_references.append(f"图片: {img_url}")

        # 检查视频引用
        video_references = re.findall(r"<(https?://.*?)>", content)
        original_video_urls = [video.get("url") for video in original_multimedia.get("videos", [])]

        for video_url in video_references:
            if (
                video_url
                and video_url not in original_video_urls
                and not any(url.endswith(video_url) for url in original_video_urls)
            ):
                invalid_references.append(f"视频: {video_url}")

        # 检查代码块（更宽松的检查，只确保语言和结构匹配）
        code_blocks = re.findall(r"```(.*?)\n(.*?)```", content, re.DOTALL)
        original_code_languages = {cb.get("language", "").strip() for cb in original_multimedia.get("code_blocks", [])}

        for lang, _ in code_blocks:
            lang = lang.strip()
            if lang and lang not in original_code_languages and lang not in ["", "markdown"]:
                invalid_references.append(f"代码块(语言): {lang}")

        if invalid_references:
            logger.warning(f"素材中包含以下可能不存在于原素材的多媒体引用: {invalid_references}")
            return False

        return True

    # 添加多媒体元素重要性分析方法
    def _analyze_multimedia_importance(
        self, multimedia_elements: dict[str, list[Any]], original_content: str
    ) -> list[dict[str, Any]]:
        """
        分析多媒体元素的重要性，为内容组织提供指导

        参数:
        - multimedia_elements: 多媒体元素字典
        - original_content: 原始内容

        返回:
        - List[Dict]: 按重要性排序的多媒体元素列表
        """
        important_elements = []

        try:
            # 分析图片重要性
            for img in multimedia_elements.get("images", []):
                img_url = img.get("url", "")
                img_alt = img.get("alt", "")

                # 计算重要性分数
                importance_score = 0

                # Demo素材特殊处理 - 最高优先级
                demo_keywords = [
                    "demo", "演示", "示例", "example", "showcase", "效果", "结果", "输出",
                    "界面", "截图", "运行", "使用", "操作", "展示", "preview", "result"
                ]
                for keyword in demo_keywords:
                    if keyword.lower() in img_alt.lower() or keyword.lower() in img_url.lower():
                        importance_score += 10  # Demo素材给予最高分
                        logger.info(f"识别到Demo素材图片: {img_alt} - {img_url}")

                # 关键词权重分析
                key_terms = [
                    "架构",
                    "流程",
                    "结构",
                    "diagram",
                    "architecture",
                    "flow",
                    "structure",
                    "框架",
                    "系统",
                    "模块",
                    "组件",
                    "原理",
                    "机制",
                    "algorithm",
                    "model",
                ]
                for term in key_terms:
                    if term.lower() in img_alt.lower() or term.lower() in img_url.lower():
                        importance_score += 3

                # 在原文中的提及频率
                mentions = original_content.lower().count(img_url.lower()) + original_content.lower().count(
                    img_alt.lower()
                )
                importance_score += mentions * 2

                # 文件名特征分析
                if any(keyword in img_url.lower() for keyword in ["main", "overview", "summary", "key", "important"]):
                    importance_score += 2

                important_elements.append(
                    {
                        "type": "image",
                        "data": img,
                        "importance_score": importance_score,
                        "description": f"图片: {img_alt}",
                        "is_demo": any(keyword.lower() in img_alt.lower() or keyword.lower() in img_url.lower() 
                                     for keyword in demo_keywords),
                    }
                )

            # 分析GIF重要性
            for gif in multimedia_elements.get("gifs", []):
                gif_url = gif.get("url", "")
                gif_alt = gif.get("alt", "")

                importance_score = 5  # GIF通常比静态图片更重要

                # Demo素材特殊处理 - 最高优先级
                demo_keywords = [
                    "demo", "演示", "示例", "example", "showcase", "效果", "结果", "输出",
                    "界面", "截图", "运行", "使用", "操作", "展示", "preview", "result"
                ]
                for keyword in demo_keywords:
                    if keyword.lower() in gif_alt.lower() or keyword.lower() in gif_url.lower():
                        importance_score += 10  # Demo素材给予最高分
                        logger.info(f"识别到Demo素材GIF: {gif_alt} - {gif_url}")

                # 动态演示相关关键词
                demo_terms = ["demo", "演示", "示例", "example", "tutorial", "操作", "process", "步骤"]
                for term in demo_terms:
                    if term.lower() in gif_alt.lower() or term.lower() in gif_url.lower():
                        importance_score += 3

                important_elements.append(
                    {
                        "type": "gif",
                        "data": gif,
                        "importance_score": importance_score,
                        "description": f"GIF动图: {gif_alt}",
                        "is_demo": any(keyword.lower() in gif_alt.lower() or keyword.lower() in gif_url.lower() 
                                     for keyword in demo_keywords),
                    }
                )

            # 分析视频重要性
            for video in multimedia_elements.get("videos", []):
                video_url = video.get("url", "")

                importance_score = 8  # 视频通常是最重要的多媒体元素

                # Demo素材特殊处理 - 最高优先级
                demo_keywords = [
                    "demo", "演示", "示例", "example", "showcase", "效果", "结果", "输出",
                    "界面", "截图", "运行", "使用", "操作", "展示", "preview", "result"
                ]
                for keyword in demo_keywords:
                    if keyword.lower() in video_url.lower():
                        importance_score += 12  # 视频Demo素材给予更高分
                        logger.info(f"识别到Demo素材视频: {video_url}")

                # 视频类型分析
                if any(keyword in video_url.lower() for keyword in ["tutorial", "demo", "introduction", "overview"]):
                    importance_score += 4

                important_elements.append(
                    {
                        "type": "video",
                        "data": video,
                        "importance_score": importance_score,
                        "description": f"视频: {video_url}",
                        "is_demo": any(keyword.lower() in video_url.lower() for keyword in demo_keywords),
                    }
                )

            # 分析代码块重要性
            for code in multimedia_elements.get("code_blocks", []):
                code_lang = code.get("language", "")
                code_content = code.get("code", "")

                importance_score = 4

                # 核心算法或关键函数
                key_code_terms = ["main", "init", "algorithm", "core", "key", "important", "class", "function"]
                for term in key_code_terms:
                    if term.lower() in code_content.lower():
                        importance_score += 2

                # 代码长度影响重要性
                if len(code_content) > 100:
                    importance_score += 1

                important_elements.append(
                    {
                        "type": "code",
                        "data": code,
                        "importance_score": importance_score,
                        "description": f"代码块({code_lang}): {code_content[:50]}...",
                    }
                )

            # 分析表格重要性
            for table in multimedia_elements.get("tables", []):
                table_content = table.get("content", "")

                importance_score = 3

                # 数据表格通常很重要
                data_terms = ["结果", "数据", "对比", "比较", "performance", "result", "comparison", "benchmark"]
                for term in data_terms:
                    if term.lower() in table_content.lower():
                        importance_score += 3

                important_elements.append(
                    {
                        "type": "table",
                        "data": table,
                        "importance_score": importance_score,
                        "description": f"表格: {table_content[:50]}...",
                    }
                )

            # 按重要性分数排序
            important_elements.sort(key=lambda x: x["importance_score"], reverse=True)

            # 统计Demo素材
            demo_elements = [elem for elem in important_elements if elem.get("is_demo", False)]

            logger.info(f"多媒体元素重要性分析完成，找到{len(important_elements)}个元素")
            if demo_elements:
                logger.info(f"🎯 特别识别到{len(demo_elements)}个Demo素材，将给予最高优先级处理")
                for i, element in enumerate(demo_elements):
                    logger.info(f"Demo素材{i+1}: {element['description']} (分数: {element['importance_score']})")
            
            for i, element in enumerate(important_elements[:5]):  # 记录前5个最重要的
                demo_flag = "🎯[Demo]" if element.get("is_demo", False) else ""
                logger.info(f"重要元素{i+1}: {demo_flag}{element['description']} (分数: {element['importance_score']})")

            return important_elements

        except Exception as e:
            logger.error(f"分析多媒体元素重要性失败: {str(e)}")
            return []

    def generate_material(
        self, original_content: str, purpose_analysis: dict[str, Any], multimedia_elements: dict[str, list[Any]]
    ) -> str:
        """
        生成规范化的视频素材

        参数:
        - original_content: 原始素材内容
        - purpose_analysis: 目的分析结果
        - multimedia_elements: 多模态元素

        返回:
        - str: 规范化的视频素材Markdown
        """
        logger.info("开始生成规范化的视频素材 - 采用受众驱动的内容优化策略")
        logger.info("新策略：以目标受众画像、意图目的、视频长度为主导优化素材，重点保留多媒体素材使用")

        try:
            # 检查原始内容长度
            original_length = len(original_content)
            logger.info(f"原始内容长度: {original_length} 字符")

            # 检测是否为GitHub项目
            is_github_project = self._is_github_project(original_content)
            if is_github_project:
                logger.info("检测到GitHub项目，将应用GitHub特定的生成规则")
            else:
                logger.info("检测到非GitHub项目，将使用通用生成规则")

            # 如果原始内容过长，可能需要截断处理
            max_content_length = 300000  # 设置最大处理长度
            truncated_content = original_content
            if original_length > max_content_length:
                logger.info(f"原始内容过长，将截断到前 {max_content_length} 字符进行处理")
                # 保留前面的内容并添加说明
                truncated_content = (
                    original_content[:max_content_length] + "\n\n... [内容过长已截断，完整内容请参考原文] ..."
                )

            # 分析多媒体元素重要性
            logger.info("开始分析多媒体元素重要性")
            important_multimedia = self._analyze_multimedia_importance(multimedia_elements, original_content)

            if important_multimedia:
                logger.info(f"多媒体元素重要性分析完成，识别出{len(important_multimedia)}个多媒体元素")
                logger.info("将在内容优化过程中重点保留和强化这些多媒体素材的使用效果")
                logger.info("多媒体元素将在目标受众适配和意图达成的框架下发挥重要支撑作用")
            else:
                logger.info("未检测到明确的多媒体元素，将在优化过程中注重内容本身的价值挖掘")

            # 准备多模态元素信息
            images = multimedia_elements.get("images", [])
            gifs = multimedia_elements.get("gifs", [])
            videos = multimedia_elements.get("videos", [])
            audios = multimedia_elements.get("audios", [])
            code_blocks = multimedia_elements.get("code_blocks", [])
            tables = multimedia_elements.get("tables", [])
            formulas = multimedia_elements.get("formulas", [])
            lists = multimedia_elements.get("lists", [])

            # 构建多模态元素信息字符串（重点突出重要元素）
            multimedia_info = []

            # 优先展示重要的多媒体元素
            if important_multimedia:
                multimedia_info.append("** 重点多媒体元素（请优先围绕这些元素组织内容）**:")
                for i, element in enumerate(important_multimedia[:6]):  # 前6个最重要的
                    multimedia_info.append(f"  {i+1}. {element['description']} (重要性: {element['importance_score']})")
                multimedia_info.append("")

            # 按类型汇总所有元素
            multimedia_info.append("** 全部多媒体元素统计 **:")
            if images:
                multimedia_info.append(f"- 图片: {len(images)}张，请选择最能说明核心概念的图片作为内容主线")

            if gifs:
                multimedia_info.append(f"- GIF: {len(gifs)}个，请以GIF展示的动态过程为内容重点")

            if videos:
                multimedia_info.append(f"- 视频: {len(videos)}个，请以视频内容为核心展开说明")

            if audios:
                multimedia_info.append(f"- 音频: {len(audios)}个，可选择最关键的音频内容")

            if code_blocks:
                multimedia_info.append(f"- 代码块: {len(code_blocks)}个，请围绕关键代码段展开技术说明")

            if tables:
                multimedia_info.append(f"- 表格: {len(tables)}个，请以表格数据为基础进行分析讲解")

            if formulas:
                multimedia_info.append(f"- 数学公式: {len(formulas)}个，请围绕关键公式展开原理说明")

            if lists:
                multimedia_info.append(f"- 列表内容: {len(lists)}个，可转化为适当的列表形式")

            multimedia_info_str = "\n".join(multimedia_info)

            # 确定视频长度对应的内容量要求
            video_length = purpose_analysis.get("video_length", "2分钟")
            video_minutes = 2  # 默认

            # 提取视频时长的数字部分
            if isinstance(video_length, str):
                match = re.search(r"(\d+)", video_length)
                if match:
                    video_minutes = int(match.group(1))

            # 设置期望内容长度范围
            min_content_length = video_minutes * 300  # 每分钟约300字的下限
            target_content_length = video_minutes * 450  # 每分钟约450字的目标
            max_content_length = video_minutes * 600  # 每分钟约600字的上限

            logger.info(
                f"视频长度 {video_minutes} 分钟，目标内容长度范围: {min_content_length}-{max_content_length} 字符"
            )

            # 根据是否为GitHub项目生成不同的系统提示
            if is_github_project:
                github_specific_rules = f"""
            ## GitHub项目星标数特殊要求（重要）
            **如果原始内容涉及GitHub项目，必须遵循以下要求**：
            - **开篇第一句必须突出项目的星标数量，使用震撼性表述**
            - 推荐表述方式：
              * "星标狂砍40K！这个项目火爆全网！"
              * "GitHub上超过30K星标的热门项目！"
              * "两周内星标暴涨2000+，开发者都在关注！"
              * "这个项目的星标数让人震惊，已经突破25K！"
              * "Star数量飙升至15K，成为同类项目中的佼佼者！"
            - **星标数信息获取方式**：
              * 优先从原始markdown内容中查找星标相关信息（如"⭐️ 12,345"、"stars: 12345"、"★ 12K"等）
              * 如果无法确定具体数字，使用"大量星标"、"高人气项目"、"备受瞩目"等表述
              * 重点是营造项目受欢迎的震撼感，吸引观众注意力
            - **关键限制：星标信息只在开篇文案中体现，严禁单独创建星标相关的章节**
              * 不要创建"项目热度"、"社区反响"、"GitHub表现"等以星标为主题的独立部分
              * 星标数据应该自然融入到开篇的技术介绍中，作为项目背景的一部分
              * 重点应该放在项目的技术价值和功能特色上，星标只是吸引注意力的开场

            ## GitHub项目Demo素材特殊处理（最高优先级）
            **如果原始内容涉及GitHub项目且包含Demo素材，必须特殊处理**：
            - **Demo素材识别标准**：
              * 查找包含"demo"、"演示"、"示例"、"example"、"showcase"关键词的图片或视频
              * 识别项目运行效果图、界面截图、功能展示图片
              * 识别项目使用流程的GIF动图或演示视频
              * 识别项目输出结果的展示图片或对比图
            - **Demo素材处理要求**：
              * **必须为Demo素材单独创建专门章节**，如"### 项目Demo演示"或"### 功能效果展示"
              * Demo章节位置：安排在项目介绍之后、技术实现细节之前
              * 每个Demo素材都要配以详细说明：展示功能、使用场景、效果亮点
              * 多个Demo素材按重要性和视觉效果排序展示
            - **Demo章节结构要求**：
              * 开头简要说明Demo的价值和展示内容
              * 逐一展示每个Demo素材，配以详细解释
              * 强调Demo体现的核心功能和用户体验
              * 说明Demo如何证明项目的实用价值和效果
            """
            else:
                github_specific_rules = f"""
            ## 论文项目特殊要求（重要）
            **如果原始内容涉及学术论文或研究项目，必须遵循以下要求**：
            - **严禁使用GitHub项目相关描述**：
              * 不能使用"星标"、"GitHub热度"、"开源项目火爆"等GitHub特有描述
              * 不能使用"项目Demo演示"、"功能展示"等项目演示相关的章节
              * 不能创建"项目热度"、"社区反响"、"开发者关注度"等以社区热度为主题的内容
            - **学术论文的正确表述方式**：
              * 开篇应突出研究的学术价值、创新性、影响力
              * 使用"最新研究突破"、"学术界瞩目"、"权威期刊发表"等学术性表述
              * 重点介绍研究方法、实验结果、理论贡献
              * 避免商业化或项目推广的语调
            - **内容结构要求**：
              * 重点介绍研究背景、核心贡献、实验验证、应用前景
              * 不创建Demo演示相关章节，而是重点介绍实验结果和效果分析
              * 用学术严谨的方式展示研究成果，而不是项目功能演示
            """

            # 创建ChatAgent - 以受众画像、意图、视频长度为主导优化素材，重点保留多媒体使用
            system_prompt = f"""
            你是一位专业的视频素材编辑专家，擅长根据目标受众画像、内容意图和视频长度要求来优化原始素材内容。

            ** 核心原则：以受众需求、意图目的、视频长度为主导，在优化过程中重点保留多媒体素材的使用和讲解 **

            ## 优化目标（按优先级排序）
            1. **目标受众适配**: 为"{purpose_analysis.get('target_audience', '一般受众')}"量身定制内容深度和表达方式
            2. **意图目的达成**: 确保内容有效实现"{purpose_analysis.get('intention', '信息传递')}"的目标
            3. **视频长度匹配**: 内容密度和结构适合{video_length}的时长要求
            4. **风格统一**: 采用"{purpose_analysis.get('style_preference', '客观分析')}"的表达风格
            5. **多媒体增强**: 在优化过程中重点保留和强化多媒体素材的使用效果
            6. **机构权威背书**: 如果原始内容中提到顶尖机构（如MIT、Google、Nature等），在开篇时要突出这些机构名称用于权威背书

            {github_specific_rules}

            ## 可用多媒体资源
            {multimedia_info_str if multimedia_info_str else "- 未检测到特定多模态元素"}

            请生成符合以下格式的规范化Markdown：
            {MATERIAL_FORMAT}

            ## 内容优化策略

            ### 1. 受众驱动的内容调整
            - 根据"{purpose_analysis.get('target_audience', '一般受众')}"的知识背景调整专业术语使用
            - 选择最适合该受众群体的解释方式和案例
            - 确保内容深度与受众认知水平匹配

            ### 2. 意图导向的结构设计
            - 围绕"{purpose_analysis.get('intention', '信息传递')}"组织核心内容
            - 突出与意图最相关的关键信息
            - 设计符合意图的内容推进逻辑

            ### 3. 时长适配的密度控制
            根据{video_length}调整内容:
            - 1-2分钟视频: 聚焦3-4个最核心要点，约450-600字，搭配2-3个关键多媒体
            - 3-5分钟视频: 覆盖5-7个重要要点，约900-1500字，搭配4-6个多媒体元素
            - 5-10分钟视频: 全面深入讲解，约1500-3000字，充分利用多媒体元素

            ### 4. 多媒体素材重点保留策略
            在内容优化过程中，必须：
            - **选择性使用重要多媒体元素**：优先选择最能说明核心概念和关键技术的多媒体素材
            - **重要性评估原则**：
              * 架构图、流程图 > 普通截图、装饰性图片
              * 实验结果图、对比图 > 示例图、说明图  
              * 核心代码段 > 配置代码、辅助代码
              * 关键数据表格 > 列表型表格、目录型表格
              * 核心算法公式 > 辅助计算公式
            - **开篇多媒体素材优先**：确保视频开篇部分优先使用最重要的多媒体素材
            - **强化重点讲解**：为选中的重要多媒体元素提供充分的解释和分析
            - **优化展示时机**：将重要多媒体元素安排在最能发挥作用的位置
            - **舍弃次要素材**：可以合理舍弃装饰性、重复性或次要的多媒体素材，优先保留核心技术相关内容

            ## 具体实施要求

            1. **完整性保证**: 确保markdown各部分完整，包括标题、视频信息、内容各部分和结论

            2. **多媒体素材处理**:
               - 选择原素材中最重要和最相关的多媒体资源
               - 为选中的重要多媒体元素提供清晰的说明和深入的分析
               - 在内容流中自然融入关键多媒体元素，确保它们得到充分利用
               - 重点讲解核心的多媒体内容，让其成为理解的重要环节
               - 可以合理舍弃次要、装饰性或重复性的多媒体素材

            3. **内容组织原则**:
               - 以受众需求为核心设计内容结构
               - 根据意图目的安排重点内容
               - 按视频长度控制信息密度
               - 在关键位置使用多媒体增强效果
               {"- **GitHub项目必须以星标数作为震撼开篇，但不单独成章**" if is_github_project else ""}
               {"- **如果有Demo素材，必须单独创建Demo展示章节，作为重点内容突出**" if is_github_project else ""}

            4. **质量控制**:
               - 保持原始素材中所有事实的准确性
               - 确保多媒体引用的有效性和相关性
               - 维持内容的逻辑连贯性和表达清晰度
               - 设计吸引目标受众的标题和结构

            5. **多媒体元素格式规范**:
               - 图片：![描述](路径) + 详细解析
               - 视频：<链接> + 内容说明
               - 代码：```语言 代码内容 ``` + 功能解释
               - 表格：完整表格 + 数据分析
               - 公式：$公式$ + 原理阐述

            请确保最终输出的内容既满足受众需求、实现预期意图、符合时长要求，又有效利用和强化了重要多媒体素材的使用效果。{f"**特别注意：如果是GitHub项目，必须以震撼性的星标数据作为开篇（不单独成章），如果有Demo素材必须单独创建Demo展示章节作为核心亮点**。" if is_github_project else ""}目标是生成约{target_content_length}字符的高质量视频素材。
            """

            # 构建用户消息
            user_message = f"""
            请根据目标受众画像、内容意图和视频长度要求，优化以下原始素材内容，在优化过程中**选择性使用重要的多媒体素材并强化其使用和讲解**:

            ```
            {truncated_content}
            ```

            ## 优化要求（按重要性排序）

            ### 1. 目标受众适配 (最高优先级)
            - 受众群体："{purpose_analysis.get('target_audience', '一般受众')}"
            - 请调整内容深度、专业术语使用和解释方式以匹配该受众的知识水平
            - 选择最适合该群体的案例和说明方式

            ### 2. 意图目的达成
            - 核心意图："{purpose_analysis.get('intention', '信息传递')}"
            - 请围绕此意图重新组织内容重点和结构
            - 确保所有关键信息都服务于这个目标

            ### 3. 视频时长匹配
            - 视频长度：{video_length}
            - 请控制内容密度和信息量以适配时长要求
            - 重点突出最核心的要点，适当精简次要信息

            ### 4. 表达风格统一
            - 风格要求："{purpose_analysis.get('style_preference', '客观分析')}"
            - 在保持事实准确的前提下，采用相应的表达方式

            ### 5. 多媒体素材重点保留 (关键要求)
            {'''
            ** 重要多媒体资源（必须充分利用和重点讲解）**:
            ''' + chr(10).join([f"• {elem['description']} (重要性: {elem['importance_score']})" for elem in important_multimedia[:6]]) if important_multimedia else "原素材中未检测到明确的多媒体元素"}

            **多媒体处理要求**：
            - **选择性使用**：优先选择最重要和最相关的多媒体元素进行保留和使用
            - **重要性评估**：
              * 架构图、流程图 > 普通截图、装饰性图片
              * 实验结果图、对比图 > 示例图、说明图  
              * 核心代码段 > 配置代码、辅助代码
              * 关键数据表格 > 列表型表格、目录型表格
            - **开篇优先**：确保视频开篇部分优先使用最重要的多媒体素材
            - **重点讲解**：为选中的重要多媒体元素提供充分的解释、分析和深入说明
            - **战略性安排**：将重要多媒体元素安排在最能发挥作用的关键位置
            - **合理舍弃**：可以舍弃次要、装饰性或重复性的多媒体素材，保留核心技术相关内容

            ## 具体执行标准

            1. **内容完整性**：确保markdown文档结构完整（标题、视频信息、内容各部分、结论）
            2. **事实准确性**：只调整表述方式，不改变原始素材中的任何事实
            3. **受众匹配度**：内容复杂度与"{purpose_analysis.get('target_audience', '一般受众')}"的认知水平完全匹配
            4. **意图实现度**：内容组织确保有效达成"{purpose_analysis.get('intention', '信息传递')}"目标
            5. **时长适配性**：信息密度适合{video_length}的播放要求
            6. **多媒体选择性使用**：选择最重要和最相关的多媒体资源得到充分利用和深入讲解
            7. **视觉吸引力**：开场直接使用核心视频/图片，配简洁有力的文案

            ## 多媒体元素处理规范
            - 图片：![描述](路径) + 详细分析说明
            - 视频：<链接> + 重点内容解读
            - 代码：```语言 代码 ``` + 功能原理解释
            - 表格：完整表格 + 数据深度分析
            - 公式：$公式$ + 原理详细阐述

            **重要提醒**：只使用原素材中实际存在的多媒体元素，优先选择最重要和最相关的素材，在优化过程中让这些关键元素发挥最大价值。

            ** 最终目标 **：生成既完全适配目标受众和意图要求，又有效展现重要多媒体素材价值的高质量视频内容。
            """

            # 创建ChatAgent实例
            agent = ChatAgent(system_message=system_prompt, model=self.model)

            # 获取回复
            logger.info("正在生成规范化素材内容...")
            response = agent.step(user_message)
            logger.info(f"获取到响应，长度: {len(response.msg.content)} 字符")

            # 提取Markdown内容
            material_markdown = self._extract_markdown(response.msg.content)

            if not material_markdown:
                logger.warning("无法从响应中提取有效的Markdown，使用完整响应")
                material_markdown = response.msg.content

            # 检查生成内容的长度
            generated_length = len(material_markdown)
            logger.info(f"生成内容长度: {generated_length} 字符")

            # 验证内容完整性
            is_complete = self._validate_material_completeness(material_markdown)

            # 验证多媒体引用
            is_valid_multimedia = self._validate_material_multimedia(material_markdown, multimedia_elements)
            if not is_valid_multimedia:
                logger.warning("生成内容中包含可能不存在于原素材的多媒体引用，将进行修正")

            # 如果内容不完整或存在无效多媒体引用或生成的内容太短，尝试补充内容
            if not is_complete or not is_valid_multimedia or generated_length < min_content_length:
                reasons = []
                if not is_complete:
                    reasons.append("结构不完整")
                if not is_valid_multimedia:
                    reasons.append("包含无效多媒体引用")
                if generated_length < min_content_length:
                    reasons.append("长度不足")

                reason_str = "、".join(reasons)
                logger.warning(f"生成内容存在以下问题：{reason_str}，尝试补充完善内容")

                # 准备补充内容的提示
                supplement_prompt = f"""
                你生成的内容存在以下问题，需要修正：

                {'- 内容结构不完整，缺少必要的部分' if not is_complete else ''}
                {'- 包含可能不存在于原素材的多媒体引用，请仅使用原素材中已有的多媒体元素' if not is_valid_multimedia else ''}
                {'- 内容长度不足，仅为 ' + str(generated_length) + ' 字符' if generated_length < min_content_length else ''}

                请根据以下已生成的内容，完善所有必要的结构部分，确保markdown文档完整，并添加更多细节：

                ```markdown
                {material_markdown}
                ```

                必须确保输出包含完整的markdown文档结构：
                1. 视频标题
                2. 视频信息部分（包含目标受众、视频时长、核心意图和表达风格）
                3. 内容主体部分（至少1-3个子部分，以###开头）
                4. 关键结论部分（##关键结论）

                关于多媒体元素的重要说明：
                - 只引用原素材中实际存在的多媒体素材，不要创建不存在的素材引用
                - 如果不确定素材是否存在，则不要引用
                - 在正文中直接嵌入适当的多媒体元素，并附上简要说明

                原素材中可用的多媒体元素：
                {multimedia_info_str if multimedia_info_str else "- 未检测到特定多模态元素"}

                确保最终输出是一个结构完整、内容充实的Markdown文档。
                """

                # 再次生成内容
                logger.info("正在补充完善内容...")
                supplement_response = agent.step(supplement_prompt)
                logger.info(f"获取到补充响应，长度: {len(supplement_response.msg.content)} 字符")

                # 提取补充的Markdown内容
                supplemented_markdown = self._extract_markdown(supplement_response.msg.content)

                # 再次验证内容完整性
                if supplemented_markdown:
                    is_supplemented_complete = self._validate_material_completeness(supplemented_markdown)

                    if is_supplemented_complete and len(supplemented_markdown) > len(material_markdown):
                        logger.info("补充内容完整性良好，使用补充后的内容")
                        material_markdown = supplemented_markdown
                    else:
                        # 如果补充内容仍不完整但比原内容长，尝试合并两者取长补短
                        if len(supplemented_markdown) > len(material_markdown):
                            logger.warning("补充内容仍不完整，但内容更丰富，尝试合并内容取长补短")
                            material_markdown = self._merge_markdown_contents(material_markdown, supplemented_markdown)
                else:
                    logger.warning("补充内容提取失败，将继续使用原始内容")

            logger.info("规范化视频素材生成完成")
            return material_markdown

        except Exception as e:
            logger.error(f"生成规范化视频素材失败: {str(e)}")
            import traceback

            logger.error(traceback.format_exc())

            # 返回一个基础模板
            return f"""
            # 视频素材（生成失败）

            ## 视频信息
            - **目标受众**：{purpose_analysis.get('target_audience', '一般受众')}
            - **视频长度**：{purpose_analysis.get('video_length', '2分钟')}
            - **核心意图**：{purpose_analysis.get('intention', '信息传递')}
            - **表达风格**：{purpose_analysis.get('style_preference', '客观分析')}

            ## 原始内容
            请参考原始素材内容。

            ## 错误信息
            生成视频素材时发生错误: {str(e)}
            """

    def _merge_markdown_contents(self, original: str, supplement: str) -> str:
        """
        合并两个Markdown内容，取长补短

        参数:
        - original: 原始Markdown内容
        - supplement: 补充的Markdown内容

        返回:
        - str: 合并后的Markdown内容
        """
        # 如果某一个为空，返回另一个
        if not original:
            return supplement
        if not supplement:
            return original

        merged = ""

        # 提取标题部分
        orig_title_match = re.search(r"(#\s+.*?)\n", original)
        supp_title_match = re.search(r"(#\s+.*?)\n", supplement)

        if supp_title_match:
            merged += supp_title_match.group(1) + "\n\n"
        elif orig_title_match:
            merged += orig_title_match.group(1) + "\n\n"

        # 提取视频信息部分
        orig_info_match = re.search(r"(##\s+视频信息.*?)(?=##|\Z)", original, re.DOTALL)
        supp_info_match = re.search(r"(##\s+视频信息.*?)(?=##|\Z)", supplement, re.DOTALL)

        if supp_info_match and len(supp_info_match.group(1)) > 50:
            merged += supp_info_match.group(1) + "\n\n"
        elif orig_info_match:
            merged += orig_info_match.group(1) + "\n\n"

        # 提取内容部分
        merged += "## 内容精华\n\n"

        orig_sections = re.findall(r"(###\s+.*?(?=###|\Z))", original, re.DOTALL)
        supp_sections = re.findall(r"(###\s+.*?(?=###|\Z))", supplement, re.DOTALL)

        # 取两者中小节数量较多的一个
        if len(supp_sections) >= len(orig_sections):
            for section in supp_sections:
                merged += section + "\n\n"
        else:
            for section in orig_sections:
                merged += section + "\n\n"

        # 提取结论部分
        orig_conclusion_match = re.search(r"(##\s+.*?结论.*?)(?=##|\Z)", original, re.DOTALL)
        supp_conclusion_match = re.search(r"(##\s+.*?结论.*?)(?=##|\Z)", supplement, re.DOTALL)

        if supp_conclusion_match:
            merged += supp_conclusion_match.group(1)
        elif orig_conclusion_match:
            merged += orig_conclusion_match.group(1)
        else:
            # 如果都没有结论部分，添加一个基本的结论部分
            merged += "## 关键结论\n\n本视频内容围绕核心主题进行了详细讲解，希望能为目标受众提供有价值的信息和见解。"

        return merged

    def refine_material(
        self,
        material_content: str,
        purpose_analysis: dict[str, Any],
        multimedia_elements: dict[str, list[Any]],
        max_rounds: int = None,
    ) -> str:
        """
        通过角色扮演迭代优化素材内容

        参数:
        - material_content: 初始素材内容
        - purpose_analysis: 目的分析结果
        - multimedia_elements: 多模态元素
        - max_rounds: 最大迭代轮数

        返回:
        - str: 优化后的素材内容
        """
        # 如果未提供max_rounds，则从配置中获取
        if max_rounds is None:
            max_rounds = self.config.max_rounds

        logger.info(f"开始通过角色扮演迭代优化素材内容，最大轮数: {max_rounds}")

        # 视频长度从目的分析中获取
        video_length = purpose_analysis.get("video_length", "2分钟")
        match = re.search(r"(\d+)", video_length)
        video_minutes = 2  # 默认值
        if match:
            video_minutes = int(match.group(1))

        # 设置期望内容长度范围
        min_content_length = video_minutes * 300  # 每分钟约300字的下限
        target_content_length = video_minutes * 450  # 每分钟约450字的目标

        # 准备任务内容
        task_content = f"""
        请优化以下视频素材内容，使其更好地适应目标受众、视频长度和意图，同时保持所有事实的准确性。

        视频素材内容：
        ```markdown
        {material_content}
        ```

        视频信息:
        - 目标人群: {purpose_analysis.get('target_audience', '一般受众')}
        - 主题内容: {purpose_analysis.get('content_theme', '未指定')}
        - 分析意图: {purpose_analysis.get('intention', '信息传递')}
        - 风格偏好: {purpose_analysis.get('style_preference', '客观分析')}
        - 视频长度: {video_length}（需要生成约{min_content_length}-{target_content_length}字的内容）

        素材中可用的多模态元素:
        {json.dumps(multimedia_elements, ensure_ascii=False, indent=2) if multimedia_elements else "未检测到特定多模态元素"}

        请通过素材编辑者和质量检查者的对话，共同优化这个素材，确保它：
        1. 完全保持原始素材中事实的准确性，仅调整表述方式
        2. 确保结构完整，包含标题、视频信息、内容各部分和结论
        3. 根据视频长度限制提供恰当的内容密度和深度
        4. 完美适配目标受众的知识水平和兴趣
        5. 表述风格符合要求但不扭曲事实
        6. 内容结构清晰，便于观众理解
        7. 只引用原素材中实际存在的多媒体元素，不创建不存在的引用
        8. 标题和内容能有效吸引目标受众，但不夸大或误导

        请记住，优化的目标是调整表述方式和结构，不是改变或编造事实。

        重要格式要求：
        当素材编辑者提出修改后的最终素材内容时，必须使用以下格式：

        ===开始：规范化Markdown素材===
        [完整的Markdown内容]
        ===结束：规范化Markdown素材===

        这将确保我们准确识别出最终素材内容。
        """

        # 将角色提示合并到任务中
        complete_task_content = f"""
        {task_content}

        ## 角色定位

        素材编辑者(Material Editor)：
        {MATERIAL_EDITOR_PROMPT}

        质量检查者(Quality Reviewer)：
        {QUALITY_REVIEWER_PROMPT}

        ## 对于多模态元素的重要说明
        只引用原素材中实际存在的多媒体素材，不要创建不存在的素材引用。以下是原素材中可用的多模态元素:
        {json.dumps(multimedia_elements, ensure_ascii=False, indent=2) if multimedia_elements else "未检测到特定多模态元素"}

        ## 对话流程安排
        1. 素材编辑者首先分析当前素材，并提出初步优化后的完整Markdown素材
        2. 质量检查者评估内容是否符合视频长度和目标受众要求，检查多媒体引用是否有效，并检查事实准确性
        3. 素材编辑者根据反馈修改优化内容，并再次提供完整的Markdown素材
        4. 最后一轮对话时，素材编辑者必须提供最终版本的完整Markdown素材，使用格式标记：

        ===开始：规范化Markdown素材===
        [完整的Markdown内容]
        ===结束：规范化Markdown素材===

        请严格遵循这个格式规范，确保最终输出是完整的Markdown格式素材，而不是评估或讨论内容。
        """

        try:
            # 设置角色扮演
            role_playing = RolePlaying(
                # 设置素材编辑者为助手角色
                assistant_role_name="Material Editor",
                assistant_agent_kwargs={"model": self.model},
                # 设置质量检查者为用户角色
                user_role_name="Quality Reviewer",
                user_agent_kwargs={"model": self.model},
                # 任务参数
                task_prompt=complete_task_content,
                task_type=TaskType.AI_SOCIETY,
                with_task_specify=False,  # 禁用task_specify避免报错
                # 附加配置
                output_language="chinese",
            )

            # 开始对话
            logger.info("开始角色对话")
            messages = []

            # 初始化对话
            chat_history = role_playing.init_chat()
            messages.append(chat_history)

            # 收集所有可能的素材版本，包括原始版本
            potential_materials = [material_content]

            # 最后一轮提前特别提醒
            is_final_reminder_sent = False

            # 进行多轮对话
            for round_num in range(max_rounds * 2):  # 每轮包含两步对话
                logger.info(f"对话步骤 {round_num + 1}")

                # 在最后一轮对话前，提醒编辑者使用正确的格式标记
                if round_num == max_rounds * 2 - 2 and not is_final_reminder_sent:
                    # 这是最后一轮对话的前一步
                    remind_message = """
                    这是最后一轮对话。请记住，您必须在回复中提供最终版本的完整Markdown素材，
                    并使用以下格式标记：

                    ===开始：规范化Markdown素材===
                    [完整的Markdown内容]
                    ===结束：规范化Markdown素材===

                    这样我才能准确识别最终素材内容。
                    """
                    chat_history.content += "\n\n" + remind_message
                    is_final_reminder_sent = True

                try:
                    # 进行对话步骤
                    assistant_response, user_response = role_playing.step(chat_history)

                    # 从响应中获取消息
                    assistant_message = assistant_response.msg
                    user_message = user_response.msg

                    # 添加到历史记录
                    chat_history = assistant_message
                    messages.append(assistant_message)
                    messages.append(user_message)

                    # 处理助手消息中的内容
                    if hasattr(assistant_message, "content") and assistant_message.role_name == "Material Editor":
                        content = assistant_message.content

                        # 首先检查是否有明确标记的Markdown段落
                        if "===开始：规范化Markdown素材===" in content and "===结束：规范化Markdown素材===" in content:
                            parts = content.split("===开始：规范化Markdown素材===", 1)
                            if len(parts) > 1:
                                marked_content = parts[1].split("===结束：规范化Markdown素材===", 1)[0].strip()
                                logger.info(f"步骤 {round_num + 1} 从明确标记中提取到Markdown素材")
                                potential_materials.append(marked_content)

                                # 如果是最后一轮并使用了正确的标记，可以直接结束对话
                                if round_num >= max_rounds * 2 - 2:
                                    logger.info("获取到最终标记的素材内容，结束对话")
                                    break

                        # 尝试提取Markdown文本（作为备选）
                        extracted_markdown = self._extract_markdown(content)
                        if extracted_markdown and len(extracted_markdown) > 100:
                            logger.info(f"步骤 {round_num + 1} 尝试提取Markdown素材")
                            # 检查提取的内容是否为有效Markdown（有标题结构）
                            if "#" in extracted_markdown[:50] or re.search(r"^#\s+", extracted_markdown, re.MULTILINE):
                                logger.info(f"步骤 {round_num + 1} 提取到可能的Markdown素材")
                                potential_materials.append(extracted_markdown)

                    # 如果已经完成了足够的轮次
                    if round_num >= max_rounds * 2 - 1:
                        logger.info("对话已完成足够的轮次")
                        break

                except Exception as e:
                    logger.error(f"对话步骤出错: {str(e)}")
                    break

            logger.info("角色对话完成，迭代优化结束")

            # 筛选并选择最佳素材内容
            valid_materials = []

            # 按质量筛选素材（从最后一轮向前）
            for material in reversed(potential_materials):
                # 跳过太短的内容或明显非Markdown格式的内容
                if len(material) < 100 or (
                    not material.startswith("#") and not re.search(r"^#\s+", material, re.MULTILINE)
                ):
                    continue

                # 清理并格式化Markdown
                material = material.strip()
                # 确保以标题开始
                if not material.startswith("#"):
                    title_match = re.search(r"^#\s+", material, re.MULTILINE)
                    if title_match:
                        start_idx = title_match.start()
                        material = material[start_idx:]

                valid_materials.append(material)

            # 如果没有找到任何有效素材，回退到原始素材
            if not valid_materials:
                logger.warning("未找到任何有效的优化素材，回退到原始素材")
                return material_content

            # 选择最长的有效素材（通常是最完整的）
            final_material = max(valid_materials, key=len)
            logger.info(f"选择了长度为 {len(final_material)} 字符的最终素材")

            # 验证最终内容的完整性
            is_final_complete = self._validate_material_completeness(final_material)
            is_valid_multimedia = self._validate_material_multimedia(final_material, multimedia_elements)

            if not is_final_complete or not is_valid_multimedia:
                logger.warning(
                    f"最终优化的内容存在问题：{'结构不完整' if not is_final_complete else ''} {'有无效多媒体引用' if not is_valid_multimedia else ''}"
                )

                # 创建ChatAgent实例，用于修复不完整的内容
                system_prompt = f"""
                你是一位专业的markdown文档修复专家。你需要检查并修复提供的视频素材markdown文档，确保其结构完整且所有多媒体引用有效。

                提供的markdown文档可能存在以下问题：
                1. 结构不完整，缺少某些必要部分
                2. 包含不存在于原素材的多媒体引用

                请确保修复后的文档：
                1. 包含以下所有必要部分：
                   - 视频标题（以单个#开头）
                   - 视频信息部分（##视频信息），包含目标受众、视频时长、核心意图和表达风格
                   - 内容主体部分（##内容精华），包含至少1-3个子部分（以###开头）
                   - 关键结论部分（##关键结论）

                2. 只使用原素材中实际存在的多媒体素材，移除所有可能不存在的引用

                原素材中可用的多模态元素：
                {json.dumps(multimedia_elements, ensure_ascii=False, indent=2) if multimedia_elements else "未检测到特定多模态元素"}

                请保留原文档中所有已有的有效内容，只添加缺失的部分或修正无效引用。如果某部分完全缺失，请根据文档上下文合理创建。
                对于视频信息部分，可参考以下信息：
                - 目标受众：{purpose_analysis.get('target_audience', '一般受众')}
                - 视频时长：{purpose_analysis.get('video_length', '2分钟')}
                - 核心意图：{purpose_analysis.get('intention', '信息传递')}
                - 表达风格：{purpose_analysis.get('style_preference', '客观分析')}

                请输出修复后的完整markdown文档，不要添加任何解释或说明。
                """

                repair_agent = ChatAgent(system_message=system_prompt, model=self.model)

                repair_message = f"""
                请修复以下markdown文档，确保其结构完整且所有多媒体引用有效：

                ```markdown
                {final_material}
                ```

                {'文档结构不完整，请确保包含所有必要部分。' if not is_final_complete else ''}
                {'文档中可能包含不存在于原素材的多媒体引用，请移除或替换这些引用。' if not is_valid_multimedia else ''}

                请只输出修复后的完整markdown文档，不要添加任何解释或额外内容。
                """

                # 获取修复响应
                logger.info("尝试修复内容结构...")
                repair_response = repair_agent.step(repair_message)

                # 提取修复后的Markdown
                repaired_markdown = self._extract_markdown(repair_response.msg.content)

                # 验证修复后内容的完整性
                if repaired_markdown and self._validate_material_completeness(repaired_markdown):
                    logger.info("成功修复内容结构，使用修复后的内容")
                    return repaired_markdown
                else:
                    logger.warning("修复尝试失败，使用原始优化内容")

            return final_material

        except Exception as e:
            import traceback

            logger.error(f"迭代优化素材内容失败: {str(e)}")
            logger.error(traceback.format_exc())
            # 出错时返回原始素材
            return material_content

    def save_material(self, content: str, output_file: str = None) -> str:
        """
        保存素材内容到文件

        参数:
        - content: 素材内容
        - output_file: 输出文件路径，如果为None则使用配置中的默认路径

        返回:
        - str: 保存的文件路径
        """
        if output_file is None:
            output_file = self.config.material_file

        # 确保输出目录存在
        output_dir = os.path.dirname(output_file)
        os.makedirs(output_dir, exist_ok=True)

        # 在保存之前验证和清理图片链接
        logger.info("保存前验证和清理图片链接")
        cleaned_content = self._validate_and_clean_image_links(content)

        # 写入文件
        try:
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(cleaned_content)
            logger.info(f"素材内容已保存到 {output_file}")
            return output_file
        except Exception as e:
            logger.error(f"保存素材内容失败: {str(e)}")
            # 尝试使用备用路径
            backup_file = f"output/material_backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            try:
                with open(backup_file, "w", encoding="utf-8") as f:
                    f.write(cleaned_content)
                logger.info(f"素材内容已保存到备用文件 {backup_file}")
                return backup_file
            except Exception as e2:
                logger.error(f"保存到备用文件也失败: {str(e2)}")
                return ""

    def run(
        self, material_file_path: str, purpose_description: str, output_file: str = None, max_rounds: int = None
    ) -> dict[str, Any]:
        """
        运行素材规范化的完整流程，自动判断是否需要调用GitHub录屏和Mermaid转图工具

        参数:
        - material_file_path: 原始素材文件路径（如果是chat模式，可传入"CHAT_MODE"）
        - purpose_description: 目的描述
        - output_file: 输出文件路径
        - max_rounds: 最大迭代轮数

        返回:
        - Dict: 包含处理结果的字典
        """
        import re  # 导入re模块，解决UnboundLocalError错误

        result = {}

        try:
            # 检查是否为chat模式（没有实际的markdown文件）
            is_chat_mode = (
                material_file_path == "CHAT_MODE"
                or not os.path.exists(material_file_path)
                or not material_file_path.endswith(".md")
            )

            if is_chat_mode:
                logger.info("检测到Chat模式，将直接使用大模型生成内容")
                return self._run_chat_mode_generation(purpose_description, output_file, max_rounds)

            # 1. 读取原始素材
            logger.info(f"开始处理素材文件: {material_file_path}")
            original_content = self.read_material(material_file_path)
            result["original_length"] = len(original_content)

            # 基于配置中的材料源开关决定是否录屏
            enabled_source = self.config.enabled_source
            enabled_source_config = self.config.enabled_source_config

            github_url = None
            project_name = None
            github_video_path = None
            webpage_url = None

            # 根据启用的源类型确定录屏目标和项目名称
            if enabled_source == "github" and enabled_source_config:
                github_url = enabled_source_config.get("url")
                # 修改：只有当URL存在且不为空时才进行录屏准备
                if github_url and github_url.strip():
                    logger.info(f"从配置中读取启用的GitHub源: {github_url}")
                    # 从github_url提取项目名
                    match = re.search(r"github\.com/([\w\-]+)/([\w\-]+)", github_url)
                    if match:
                        project_name = match.group(2)
                    else:
                        # fallback: 用文件夹名
                        project_name = os.path.basename(os.path.dirname(material_file_path))
                else:
                    logger.info("GitHub源已启用但URL为空，跳过录屏")
                    github_url = None
                    project_name = os.path.basename(os.path.dirname(material_file_path))

            elif enabled_source == "webpage" and enabled_source_config:
                webpage_url = enabled_source_config.get("url")
                # 修改：只有当URL存在且不为空时才处理
                if webpage_url and webpage_url.strip():
                    logger.info(f"从配置中读取启用的网页源: {webpage_url}")
                    # 从网页URL提取项目名
                    from urllib.parse import urlparse

                    parsed_url = urlparse(webpage_url)
                    hostname = parsed_url.hostname or "webpage"
                    path = parsed_url.path.strip("/").replace("/", "_") or "content"
                    project_name = f"{hostname}_{path}"[:50]  # 限制长度
                else:
                    logger.info("网页源已启用但URL为空，跳过处理")
                    webpage_url = None
                    project_name = "webpage_content"

            elif enabled_source == "pdf" and enabled_source_config:
                pdf_url = enabled_source_config.get("url")
                # 修改：只有当URL存在且不为空时才处理
                if pdf_url and pdf_url.strip():
                    logger.info(f"从配置中读取启用的PDF源: {pdf_url}")
                    # 从PDF URL或路径提取项目名
                    if "arxiv.org" in pdf_url:
                        # 直接提取arxiv论文ID作为项目名，不添加前缀
                        match = re.search(r"(\d{4}\.\d{5})", pdf_url)
                        if match:
                            project_name = match.group(1)
                        else:
                            project_name = "arxiv_paper"
                    else:
                        # 从文件名提取
                        from urllib.parse import urlparse

                        parsed_url = urlparse(pdf_url)
                        filename = os.path.basename(parsed_url.path)
                        project_name = os.path.splitext(filename)[0] or "pdf_content"
                else:
                    logger.info("PDF源已启用但URL为空，跳过录屏")
                    project_name = "pdf_content"

            elif enabled_source == "local_file" and enabled_source_config:
                local_path = enabled_source_config.get("path")
                if local_path and local_path.strip():
                    logger.info(f"从配置中读取启用的本地文件源: {local_path}")
                    # 从本地文件路径提取项目名
                    filename = os.path.basename(local_path)
                    project_name = os.path.splitext(filename)[0] or "local_content"
                else:
                    logger.info("本地文件源已启用但路径为空")
                    project_name = "local_content"

            elif enabled_source == "chat" and enabled_source_config:
                # Chat模式使用时间戳作为项目名
                import time

                timestamp = time.strftime("%Y%m%d_%H%M%S")
                project_name = f"chat_{timestamp}"
                logger.info(f"Chat源已启用，使用时间戳作为项目名: {project_name}")

            else:
                # 兼容模式：使用传统方式获取GitHub URL
                github_url = self.config.github_url
                # 修改：只有当URL存在且不为空时才处理
                if github_url and github_url.strip():
                    logger.info(f"兼容模式：从配置中读取GitHub URL: {github_url}")
                    # 从github_url提取项目名
                    match = re.search(r"github\.com/([\w\-]+)/([\w\-]+)", github_url)
                    if match:
                        project_name = match.group(2)
                    else:
                        project_name = os.path.basename(os.path.dirname(material_file_path))
                else:
                    logger.info("未启用任何特定源或URL为空，使用文件夹名作为项目名")
                    github_url = None
                    project_name = os.path.basename(os.path.dirname(material_file_path))

            # 记录项目名称，用于后续处理
            logger.info(f"项目名称确定为: {project_name}")

            # 设置项目输出目录
            project_output_dir = f"output/{project_name}"
            os.makedirs(project_output_dir, exist_ok=True)

            # 修改：根据启用的源类型和URL是否存在执行录屏
            if enabled_source == "github" and github_url and github_url.strip():
                github_video_path = f"{project_output_dir}/github_screen.mp4"
                logger.info(f"GitHub源已启用且URL有效，准备录屏到: {github_video_path}")
                try:
                    # 获取GitHub录屏参数，提供默认值
                    github_params = {
                        "duration": enabled_source_config.get("duration", 12),
                        "width": enabled_source_config.get("width", 1920),
                        "height": enabled_source_config.get("height", 1080),
                        "fps": enabled_source_config.get("fps", 15),
                        "smooth_factor": enabled_source_config.get("smooth_factor", 0.2),
                        "title_focus": enabled_source_config.get("title_focus", 1),
                        "star_focus": enabled_source_config.get("star_focus", 2),
                        "zoom_factor": enabled_source_config.get("zoom_factor", 2.0),
                        "readme_pause": enabled_source_config.get("readme_pause", 1.0),
                    }

                    # 使用代理录制GitHub项目视频
                    github_result = self._record_github_with_agent(
                        url=github_url, output_path=github_video_path, **github_params
                    )

                    if github_result and github_result.get("status") == "success":
                        result["github_video_path"] = github_video_path
                        logger.info(f"GitHub项目录屏完成: {github_video_path}")
                    else:
                        github_video_path = None
                        error_msg = "未知错误"
                        if github_result:
                            error_msg = github_result.get("error", "未知错误")
                        logger.warning(f"GitHub录屏未完成: {error_msg}")
                except Exception as e:
                    logger.warning(f"GitHub录屏工具调用失败: {e}")
                    github_video_path = None

            elif enabled_source == "pdf" and enabled_source_config:
                pdf_url = enabled_source_config.get("url")
                # 修改：只有当URL存在且不为空时才录屏
                if pdf_url and pdf_url.strip():
                    # 为PDF源创建录屏，文件名包含项目名称
                    pdf_video_path = f"{project_output_dir}/{project_name}_pdf_screen.mp4"
                    logger.info(f"PDF源已启用且URL有效，准备录屏到: {pdf_video_path}")

                    # 尝试将PDF URL转换为可录屏的页面URL
                    screen_url = None
                    if "arxiv.org" in pdf_url:
                        # 将PDF URL转换为arxiv论文页面URL
                        # 从 https://arxiv.org/pdf/2411.01747 转换为 https://arxiv.org/abs/2411.01747
                        match = re.search(r"arxiv\.org/pdf/(\d{4}\.\d{5})", pdf_url)
                        if match:
                            paper_id = match.group(1)
                            screen_url = f"https://arxiv.org/abs/{paper_id}"
                            logger.info(f"将PDF URL转换为页面URL: {screen_url}")

                    if screen_url:
                        try:
                            # 获取PDF录屏参数，提供默认值
                            pdf_params = {
                                "duration": enabled_source_config.get("duration", 8),
                                "width": enabled_source_config.get("width", 1920),
                                "height": enabled_source_config.get("height", 1080),
                                "fps": enabled_source_config.get("fps", 15),
                                "smooth_factor": enabled_source_config.get("smooth_factor", 0.2),
                                "title_focus": enabled_source_config.get("title_focus", 4),
                                "zoom_factor": enabled_source_config.get("zoom_factor", 2.0),
                                "abstract_pause": enabled_source_config.get("abstract_pause", 0.0),
                            }

                            # 使用ArxivRecorderToolkit录制PDF页面
                            pdf_result = self._record_arxiv_with_agent(
                                url=screen_url, output_path=pdf_video_path, **pdf_params
                            )

                            if pdf_result and pdf_result.get("status") == "success":
                                result["pdf_video_path"] = pdf_video_path
                                logger.info(f"Arxiv论文页面录屏完成: {pdf_video_path}")
                                # 将PDF录屏视频路径也存储为github_video_path，以便后续处理
                                github_video_path = pdf_video_path
                            else:
                                error_msg = "未知错误"
                                if pdf_result:
                                    error_msg = pdf_result.get("error", "未知错误")
                                logger.warning(f"Arxiv论文页面录屏未完成: {error_msg}")
                        except Exception as e:
                            logger.warning(f"Arxiv论文页面录屏工具调用失败: {e}")
                    else:
                        logger.info(f"PDF URL无法转换为可录屏的页面URL: {pdf_url}")
                else:
                    logger.info("PDF源已启用但URL为空，跳过录屏")

            elif enabled_source == "webpage" and webpage_url and webpage_url.strip():
                # 网页录屏功能实现
                webpage_video_path = f"{project_output_dir}/{project_name}_webpage_screen.mp4"
                logger.info(f"网页源已启用且URL有效: {webpage_url}，准备录屏到: {webpage_video_path}")
                
                try:
                    # 获取网页录屏参数，提供默认值
                    webpage_params = {
                        "duration": enabled_source_config.get("duration", 10),
                        "width": enabled_source_config.get("width", 1920),
                        "height": enabled_source_config.get("height", 1080),
                        "fps": enabled_source_config.get("fps", 15),
                        "smooth_factor": enabled_source_config.get("smooth_factor", 0.3),
                        "title_focus": enabled_source_config.get("title_focus", 1),
                        "star_focus": enabled_source_config.get("star_focus", 0),  # 网页通常没有star
                        "zoom_factor": enabled_source_config.get("zoom_factor", 1.5),
                        "readme_pause": enabled_source_config.get("readme_pause", 1.5),
                    }

                    # 使用GitHub录屏工具录制网页（工具本身是通用的网页录屏）
                    webpage_result = self._record_github_with_agent(
                        url=webpage_url, output_path=webpage_video_path, **webpage_params
                    )

                    if webpage_result and webpage_result.get("status") == "success":
                        result["webpage_video_path"] = webpage_video_path
                        logger.info(f"网页录屏完成: {webpage_video_path}")
                        # 将网页录屏视频路径也存储为github_video_path，以便后续处理
                        github_video_path = webpage_video_path
                    else:
                        error_msg = "未知错误"
                        if webpage_result:
                            error_msg = webpage_result.get("error", "未知错误")
                        logger.warning(f"网页录屏未完成: {error_msg}")
                except Exception as e:
                    logger.warning(f"网页录屏工具调用失败: {e}")

            elif github_url and github_url.strip():  # 兼容模式的GitHub录屏
                github_video_path = f"{project_output_dir}/github_screen.mp4"
                logger.info(f"兼容模式：GitHub URL有效，准备录屏到: {github_video_path}")
                try:
                    github_result = self._record_github_with_agent(
                        url=github_url,
                        output_path=github_video_path,
                        duration=12,
                        width=1920,
                        height=1080,
                        fps=15,
                        smooth_factor=0.2,
                        title_focus=1,
                        star_focus=2,
                        zoom_factor=2.0,
                        readme_pause=1.0,
                    )

                    if github_result and github_result.get("status") == "success":
                        result["github_video_path"] = github_video_path
                        logger.info(f"GitHub项目录屏完成: {github_video_path}")
                    else:
                        github_video_path = None
                        error_msg = "未知错误"
                        if github_result:
                            error_msg = github_result.get("error", "未知错误")
                        logger.warning(f"GitHub录屏未完成: {error_msg}")
                except Exception as e:
                    logger.warning(f"GitHub录屏工具调用失败: {e}")
                    github_video_path = None
            else:
                logger.info("没有有效的URL配置，跳过录屏")

            # 3. 提取多媒体元素，仅包括mmd文件，不包括mermaid代码块
            logger.info("提取素材中的多媒体元素")
            multimedia_elements = self.extract_multimedia_elements(original_content)
            # 将项目名称添加到多媒体元素中
            multimedia_elements["project_name"] = project_name
            mmd_files = multimedia_elements.get("mmd_files", [])
            mmd_png_map = {}

            # 首先尝试找出原始素材中已有的mermaid图片链接
            mermaid_resource_links = {}
            try:
                # 查找格式为![描述](路径)的图片引用，其中路径可能包含mermaid相关信息
                image_links = re.findall(r"!\[(.*?)\]\((.*?)\)", original_content)
                for alt_text, img_path in image_links:
                    # 检查是否是mermaid相关图片
                    if (
                        "mermaid" in alt_text.lower()
                        or "mermaid" in img_path.lower()
                        or "流程图" in alt_text.lower()
                        or "架构图" in alt_text.lower()
                    ):
                        # 提取图片标识，可能是完整路径或部分路径
                        img_id = os.path.basename(img_path)
                        mermaid_resource_links[img_id] = img_path
                        logger.info(f"找到可能的mermaid资源链接: {img_path}")
            except Exception as e:
                logger.warning(f"提取mermaid资源链接时出错: {e}")

            # 处理素材中的.mmd文件
            if mmd_files:
                logger.info(f"检测到{len(mmd_files)}个.mmd文件引用，开始处理")
                for idx, mmd_file_info in enumerate(mmd_files):
                    mmd_path = mmd_file_info["path"]

                    # 优先在output/项目名称/目录下查找.mmd文件
                    actual_path = None
                    basename = os.path.basename(mmd_path)
                    project_output_path = os.path.join("output", project_name, basename)

                    if os.path.exists(project_output_path):
                        actual_path = project_output_path
                        logger.info(f"在output/{project_name}/目录下找到.mmd文件: {actual_path}")
                    # 如果项目输出目录中没找到，尝试其他可能的路径
                    elif not os.path.isabs(mmd_path):
                        # 尝试不同的相对路径基准
                        material_dir = os.path.dirname(material_file_path)
                        potential_paths = [
                            mmd_path,  # 直接相对于当前目录
                            os.path.join(material_dir, mmd_path),  # 相对于素材文件所在目录
                            os.path.join(material_dir, basename),  # 相对于素材文件所在目录的文件名
                            os.path.join("output", project_name, basename),  # 输出目录下的项目名文件夹
                            os.path.join("output", basename),  # 输出目录下
                            os.path.join(".", mmd_path),  # 显式相对于当前目录
                        ]

                        # 尝试找到实际存在的文件路径
                        for path in potential_paths:
                            if os.path.exists(path):
                                actual_path = path
                                logger.info(f"找到.mmd文件的实际路径: {actual_path}")
                                break

                    if not actual_path:
                        logger.warning(f"无法找到.mmd文件的实际路径: {mmd_path}，将跳过处理")
                        continue

                    try:
                        # 使用项目名作为目录前缀
                        output_dir = f"output/{project_name}"
                        os.makedirs(output_dir, exist_ok=True)

                        # 保持原始文件名，只改变扩展名为.png
                        base_name = os.path.basename(actual_path)  # 获取文件名
                        name_without_ext = os.path.splitext(base_name)[0]  # 移除扩展名
                        png_file = f"{output_dir}/{name_without_ext}.png"

                        # 使用代理调用Mermaid转换工具
                        mermaid_result = self._convert_mermaid_with_agent(actual_path, png_file)

                        if mermaid_result and mermaid_result.get("status") == "success":
                            # 添加到映射中，使用文件路径作为键以便替换引用
                            mmd_png_map[mmd_path] = png_file
                            # 也用basename作为键，方便替换相对路径引用
                            mmd_png_map[basename] = png_file
                            # 确保也可以用相对路径匹配
                            original_path = mmd_file_info["path"]
                            if original_path != mmd_path:
                                mmd_png_map[original_path] = png_file

                            logger.info(f"Mermaid文件转PNG成功: {actual_path} -> {png_file}")
                        else:
                            error_msg = "未知错误"
                            if mermaid_result:
                                error_msg = mermaid_result.get("message", mermaid_result.get("error", "未知错误"))
                            logger.warning(f"Mermaid文件转PNG失败: {actual_path}，错误: {error_msg}")
                    except Exception as e:
                        logger.warning(f"处理Mermaid文件时出错: {actual_path}，错误: {str(e)}")

            result["multimedia_elements"] = {
                "images_count": len(multimedia_elements.get("images", [])),
                "gifs_count": len(multimedia_elements.get("gifs", [])),
                "videos_count": len(multimedia_elements.get("videos", [])),
                "audios_count": len(multimedia_elements.get("audios", [])),
                "code_blocks_count": len(multimedia_elements.get("code_blocks", [])),
                "mermaid_files_count": len(mmd_files),
                "mermaid_converted_count": len(mmd_png_map),
                "tables_count": len(multimedia_elements.get("tables", [])),
                "formulas_count": len(multimedia_elements.get("formulas", [])),
                "lists_count": len(multimedia_elements.get("lists", [])),
            }

            # 4. 分析用户目的
            logger.info("分析用户目的描述")

            # 确定要使用的目的描述
            final_purpose_description = purpose_description

            # 如果没有提供外部目的描述，则尝试从配置中获取启用源的目的描述
            if not purpose_description and enabled_source_config:
                config_purpose = enabled_source_config.get("purpose")
                if config_purpose:
                    final_purpose_description = config_purpose
                    logger.info(f"使用配置中{enabled_source}源的目的描述: {config_purpose[:100]}...")
                else:
                    # 使用默认目的描述
                    final_purpose_description = getattr(
                        self.config, "default_purpose", "提供简单易懂的内容分析，面向一般受众，以客观中立的风格呈现"
                    )
                    logger.info("使用默认目的描述")
            elif purpose_description:
                logger.info(f"使用外部提供的目的描述: {purpose_description[:100]}...")
            else:
                # 使用默认目的描述
                final_purpose_description = getattr(
                    self.config, "default_purpose", "提供简单易懂的内容分析，面向一般受众，以客观中立的风格呈现"
                )
                logger.info("使用默认目的描述")

            purpose_analysis = self.analyze_purpose(final_purpose_description)
            result["purpose_analysis"] = purpose_analysis

            # 5. 生成初始规范化素材
            logger.info("生成初始规范化素材")
            initial_material = self.generate_material(original_content, purpose_analysis, multimedia_elements)
            result["initial_material_length"] = len(initial_material)

            # 6. 插入录屏视频和mermaid图片引用
            final_material = initial_material
            insert_parts = []

            # 插入录屏视频并添加解说
            if github_video_path and os.path.exists(github_video_path):
                # 根据源类型确定视频描述
                if enabled_source == "pdf":
                    # PDF源的视频描述
                    paper_title = project_name or "该论文"
                    description = f"# {paper_title} 论文页面浏览\n\n"
                    description += f"这是 **{paper_title}** 论文页面的自动录屏浏览，"
                    description += "通过视频您可以快速了解论文的基本信息和结构。\n\n"
                    description += f"[查看论文页面视频]({github_video_path})\n\n"
                    logger.info("成功添加PDF论文页面视频和详细解说到文档开头")
                elif enabled_source == "webpage":
                    # 网页源的视频描述
                    page_title = project_name or "该网页"
                    description = f"# {page_title} 网页内容浏览\n\n"
                    description += f"这是 **{page_title}** 的自动录屏浏览，"
                    description += "通过视频您可以快速了解网页的主要内容和布局结构。\n\n"
                    description += f"[查看网页浏览视频]({github_video_path})\n\n"
                    logger.info("成功添加网页浏览视频和详细解说到文档开头")
                else:
                    # GitHub源的视频描述
                    repo_name = project_name or "该项目"
                    repo_owner = ""
                    if github_url:
                        match = re.search(r"github.com/([\w\-]+)/([\w\-]+)", github_url)
                        if match:
                            repo_owner = match.group(1)
                            repo_name = match.group(2)

                    description = f"# {repo_name} 项目概览\n\n"

                    # 添加简短描述
                    if repo_owner:
                        description += f"这是 **{repo_owner}/{repo_name}** 项目的自动录屏浏览，"
                    else:
                        description += f"这是 **{repo_name}** 项目的自动录屏浏览，"

                    description += "通过视频您可以快速了解项目结构和主要功能。\n\n"
                    description += f"[查看项目介绍视频]({github_video_path})\n\n"
                    logger.info("成功添加GitHub项目视频和详细解说到文档开头")

                # 将视频描述放在最前面
                insert_parts.insert(0, description)

            # 统一处理多媒体元素路径，添加output/项目名/前缀
            def standardize_path(path):
                """为路径添加output/项目名/前缀，保留原始路径结构"""
                if not path:
                    return path

                # 确保我们有项目名称
                actual_project_name = multimedia_elements.get("project_name", project_name)

                # 如果路径已经以output/项目名开头，直接返回
                project_prefix = f"output/{actual_project_name}/"
                if path.startswith(project_prefix):
                    return path

                # 如果是绝对路径，提取相对路径部分
                if os.path.isabs(path):
                    # 尝试保留最后几级目录结构
                    parts = path.split(os.sep)
                    # 取最后3级目录（如果存在的话）
                    rel_path = os.path.join(*parts[-3:]) if len(parts) > 1 else parts[-1]
                    return f"{project_prefix}{rel_path}"

                # 如果是相对路径但不在项目目录下，保留整个路径并添加前缀
                if not path.startswith("output/"):
                    return f"{project_prefix}{path}"

                return path

            # 标准化所有多媒体元素路径
            for idx, img in enumerate(multimedia_elements.get("images", [])):
                if "url" in img:
                    img["url"] = standardize_path(img["url"])

            for idx, gif in enumerate(multimedia_elements.get("gifs", [])):
                if "url" in gif:
                    gif["url"] = standardize_path(gif["url"])

            for idx, video in enumerate(multimedia_elements.get("videos", [])):
                if "url" in video:
                    video["url"] = standardize_path(video["url"])

            for idx, audio in enumerate(multimedia_elements.get("audios", [])):
                if "url" in audio:
                    audio["url"] = standardize_path(audio["url"])

            # 替换mmd代码块和mmd文件引用为图片引用
            if mmd_png_map:
                logger.info(f"开始替换mmd引用，映射关系: {mmd_png_map}")

                # 替换.mmd文件引用（包括链接形式和直接引用）
                def mmd_file_replacer(match):
                    link_text = match.group(1)
                    mmd_path = match.group(2)

                    # 尝试不同的映射方式
                    png_path = None

                    # 1. 直接路径匹配
                    if mmd_path in mmd_png_map:
                        png_path = mmd_png_map[mmd_path]
                    else:
                        # 2. 尝试用文件名匹配
                        basename = os.path.basename(mmd_path)
                        if basename in mmd_png_map:
                            png_path = mmd_png_map[basename]
                        else:
                            # 3. 构建标准路径
                            name_without_ext = os.path.splitext(basename)[0]
                            standard_png_path = f"output/{project_name}/{name_without_ext}.png"
                            if os.path.exists(standard_png_path):
                                png_path = standard_png_path
                                logger.info(f"通过标准路径找到PNG文件: {standard_png_path}")

                    if png_path:
                        logger.info(f"替换mmd文件引用: {mmd_path} -> {png_path}")
                        return f"![{link_text}]({png_path})"
                    else:
                        logger.warning(f"未找到mmd文件对应的PNG: {mmd_path}")
                        return match.group(0)

                # 替换代码中的.mmd引用
                def mmd_code_replacer(match):
                    mmd_path = match.group(1)

                    # 尝试映射
                    png_path = None
                    if mmd_path in mmd_png_map:
                        png_path = mmd_png_map[mmd_path]
                    else:
                        basename = os.path.basename(mmd_path)
                        if basename in mmd_png_map:
                            png_path = mmd_png_map[basename]
                        else:
                            name_without_ext = os.path.splitext(basename)[0]
                            standard_png_path = f"output/{project_name}/{name_without_ext}.png"
                            if os.path.exists(standard_png_path):
                                png_path = standard_png_path

                    if png_path:
                        logger.info(f"替换代码中的mmd引用: {mmd_path} -> {png_path}")
                        return f"![架构图]({png_path})"
                    else:
                        return match.group(0)

                import re

                # 执行替换
                # 1. 替换链接形式的引用: [text](file.mmd)
                final_material = re.sub(r"\[([^\]]+)\]\(([^)]+\.mmd)\)", mmd_file_replacer, final_material)

                # 2. 替换代码中的引用: `file.mmd`
                final_material = re.sub(r"`([^`]+\.mmd)`", mmd_code_replacer, final_material)

                logger.info("完成mmd文件引用替换")
            else:
                logger.info("没有mmd文件需要替换")

            # 替换最终材料中的所有多媒体路径，确保统一使用output/项目名/前缀
            def path_standardizer(match):
                """替换路径为标准格式"""
                alt_text = match.group(1)
                path = match.group(2)
                # 如果路径看起来不像是URL
                if not path.startswith(("http://", "https://", "file://")):
                    path = standardize_path(path)
                return f"![{alt_text}]({path})"

            # 替换图片路径
            final_material = re.sub(r"!\[(.*?)\]\((.*?)\)", path_standardizer, final_material)

            # 在文档最前面插入项目介绍视频
            if insert_parts:
                final_material = "\n".join(insert_parts) + final_material

            logger.info("完成多媒体素材路径标准化，统一添加output/项目名/前缀")

            # 7. 通过角色对话优化素材
            logger.info("开始通过角色对话优化素材")
            final_material = self.refine_material(final_material, purpose_analysis, multimedia_elements, max_rounds)
            result["final_material_length"] = len(final_material)

            # 8. 保存结果
            logger.info("保存优化后的素材")
            saved_file = self.save_material(final_material, output_file)
            result["saved_file"] = saved_file
            result["success"] = True
            logger.info("素材规范化处理完成")
            return result

        except Exception as e:
            logger.error(f"素材规范化处理出错: {str(e)}")
            import traceback

            logger.error(traceback.format_exc())
            result["error"] = str(e)
            result["success"] = False
            return result

    def _run_chat_mode_generation(
        self, purpose_description: str, output_file: str = None, max_rounds: int = None
    ) -> dict[str, Any]:
        """
        Chat模式的内容生成，直接使用大模型生成规范化的视频素材

        参数:
        - purpose_description: 目的描述/主题
        - output_file: 输出文件路径
        - max_rounds: 最大迭代轮数

        返回:
        - Dict: 包含处理结果的字典
        """
        logger.info("开始Chat模式内容生成")
        result = {}

        try:
            # Chat模式使用时间戳作为项目名
            import time

            timestamp = time.strftime("%Y%m%d_%H%M%S")
            project_name = f"chat_{timestamp}"
            result["project_name"] = project_name

            # 设置项目输出目录
            project_output_dir = f"output/{project_name}"
            os.makedirs(project_output_dir, exist_ok=True)
            logger.info(f"Chat模式项目名称: {project_name}")

            # 1. 分析用户目的
            logger.info("分析Chat模式目的描述")
            purpose_analysis = self.analyze_purpose(purpose_description)
            result["purpose_analysis"] = purpose_analysis

            # 2. 直接使用大模型生成规范化的视频素材内容
            logger.info("开始生成Chat模式视频素材内容")

            # 获取最大轮数
            if max_rounds is None:
                max_rounds = self.config.max_rounds

            # 创建虚拟的多媒体元素字典（Chat模式没有实际的多媒体素材）
            multimedia_elements = {
                "images": [],
                "gifs": [],
                "videos": [],
                "audios": [],
                "code_blocks": [],
                "tables": [],
                "formulas": [],
                "lists": [],
                "mmd_files": [],
                "project_name": project_name,
            }

            # 直接生成素材内容
            material_content = self._generate_chat_material(purpose_description, purpose_analysis)
            result["initial_material_length"] = len(material_content)

            # 3. 通过角色对话优化素材
            logger.info("开始通过角色对话优化Chat模式素材")
            final_material = self.refine_material(material_content, purpose_analysis, multimedia_elements, max_rounds)
            result["final_material_length"] = len(final_material)

            # 4. 保存结果
            if output_file is None:
                output_file = f"{project_output_dir}/chat_material.md"

            logger.info("保存Chat模式优化后的素材")
            saved_file = self.save_material(final_material, output_file)
            result["saved_file"] = saved_file
            result["success"] = True
            result["multimedia_elements"] = {
                "images_count": 0,
                "gifs_count": 0,
                "videos_count": 0,
                "audios_count": 0,
                "code_blocks_count": 0,
                "mermaid_files_count": 0,
                "mermaid_converted_count": 0,
                "tables_count": 0,
                "formulas_count": 0,
                "lists_count": 0,
            }

            logger.info("Chat模式素材生成完成")
            return result

        except Exception as e:
            logger.error(f"Chat模式素材生成出错: {str(e)}")
            import traceback

            logger.error(traceback.format_exc())
            result["error"] = str(e)
            result["success"] = False
            return result

    def _generate_chat_material(self, purpose: str, purpose_analysis: dict[str, Any]) -> str:
        """
        为Chat模式生成初始的视频素材内容

        参数:
        - purpose: 用户输入的主题描述
        - purpose_analysis: 目的分析结果

        返回:
        - str: 生成的视频素材Markdown内容
        """
        logger.info("开始为Chat模式生成初始素材内容")

        try:
            # 确定视频长度对应的内容量要求
            video_length = purpose_analysis.get("video_length", "2分钟")
            video_minutes = 2  # 默认

            # 提取视频时长的数字部分
            if isinstance(video_length, str):
                match = re.search(r"(\d+)", video_length)
                if match:
                    video_minutes = int(match.group(1))

            # 设置期望内容长度范围
            target_content_length = video_minutes * 450  # 每分钟约450字的目标

            logger.info(f"Chat模式视频长度 {video_minutes} 分钟，目标内容长度: {target_content_length} 字符")

            # 创建ChatAgent生成素材内容
            system_prompt = f"""
            你是一位专业的视频内容创作专家，擅长根据用户提供的主题生成高质量的教育视频素材。

            ## 核心任务
            根据用户提供的主题："{purpose}"，生成完整的规范化视频素材内容。

            ## 目标受众和要求
            - 目标受众：{purpose_analysis.get('target_audience', '一般受众')}
            - 内容意图：{purpose_analysis.get('intention', '信息传递')}
            - 表达风格：{purpose_analysis.get('style_preference', '客观分析')}
            - 视频长度：{video_length}
            - 目标内容长度：约{target_content_length}字符

            ## 素材格式要求
            {MATERIAL_FORMAT}

            ## 内容生成策略

            ### 1. 主题深度分析
            - 围绕"{purpose}"展开全面分析
            - 包括核心概念、原理解释、应用场景等
            - 确保内容深度适合{purpose_analysis.get('target_audience', '一般受众')}

            ### 2. 结构化内容组织
            - 采用清晰的层次结构，便于视频制作
            - 每个部分都有明确的教学目标
            - 内容推进逻辑符合认知规律

            ### 3. 实用性导向
            - 提供具体的例子和应用场景
            - 包含实用的知识点和技巧
            - 确保观众能够理解并应用所学内容

            ### 4. 视觉元素考虑
            - 在适当位置提示可以使用图表、示意图等视觉元素
            - 为关键概念设计清晰的解释方式
            - 考虑视频制作时的视觉呈现需求

            ## 质量标准
            1. **完整性**：确保包含所有必要的markdown结构部分
            2. **准确性**：所有事实和概念必须准确无误
            3. **适配性**：内容深度和表达方式完全适合目标受众
            4. **实用性**：提供有价值的知识和见解
            5. **吸引力**：标题和内容能够吸引目标受众

            请生成完整的规范化视频素材markdown文档，确保内容丰富、结构清晰、适合制作成教育视频。
            """

            # 构建用户消息
            user_message = f"""
            请为以下主题生成完整的视频素材内容：

            主题：{purpose}

            ## 具体要求

            ### 受众分析
            - 目标受众：{purpose_analysis.get('target_audience', '一般受众')}
            - 需要调整内容深度和专业术语使用以匹配该受众的知识水平

            ### 内容目标
            - 核心意图：{purpose_analysis.get('intention', '信息传递')}
            - 确保所有内容都服务于这个目标

            ### 表达要求
            - 表达风格：{purpose_analysis.get('style_preference', '客观分析')}
            - 在保持准确性的前提下采用相应的表达方式

            ### 时长控制
            - 视频长度：{video_length}
            - 请控制内容密度，使其适合该时长的视频制作

            ## 内容组织要求

            1. **标题设计**：创建吸引{purpose_analysis.get('target_audience', '一般受众')}的视频标题

            2. **视频信息**：包含目标受众、视频时长、核心意图、表达风格四个要素

            3. **内容主体**：
               - 至少包含3-5个主要部分（以###开头）
               - 每部分都有清晰的主题和教学目标
               - 内容要有层次，循序渐进
               - 包含具体的例子、应用场景、操作步骤等实用信息

            4. **关键结论**：总结核心要点，强化学习效果

            请确保生成的内容既专业准确，又通俗易懂，适合制作成高质量的教育视频。
            """

            # 创建ChatAgent实例
            agent = ChatAgent(system_message=system_prompt, model=self.model)

            # 获取回复
            logger.info("正在为Chat模式生成素材内容...")
            response = agent.step(user_message)
            logger.info(f"获取到Chat模式响应，长度: {len(response.msg.content)} 字符")

            # 提取Markdown内容
            material_markdown = self._extract_markdown(response.msg.content)

            if not material_markdown:
                logger.warning("无法从响应中提取有效的Markdown，使用完整响应")
                material_markdown = response.msg.content

            # 检查生成内容的长度和完整性
            generated_length = len(material_markdown)
            logger.info(f"Chat模式生成内容长度: {generated_length} 字符")

            # 验证内容完整性
            is_complete = self._validate_material_completeness(material_markdown)
            if not is_complete:
                logger.warning("Chat模式生成内容结构不完整，尝试补充")
                # 这里可以添加补充逻辑，类似于generate_material方法中的处理

            logger.info("Chat模式初始素材内容生成完成")
            return material_markdown

        except Exception as e:
            logger.error(f"Chat模式生成初始素材内容失败: {str(e)}")
            import traceback

            logger.error(traceback.format_exc())

            # 返回一个基础模板
            return f"""
            # {purpose} - 视频素材

            ## 视频信息
            - **目标受众**：{purpose_analysis.get('target_audience', '一般受众')}
            - **视频时长**：{purpose_analysis.get('video_length', '2分钟')}
            - **核心意图**：{purpose_analysis.get('intention', '信息传递')}
            - **表达风格**：{purpose_analysis.get('style_preference', '客观分析')}

            ## 内容精华

            ### 主题概述
            {purpose}是一个重要的主题，值得深入了解和学习。

            ### 核心概念
            本部分将详细介绍相关的核心概念和原理。

            ### 实际应用
            探讨该主题在实际生活和工作中的应用场景。

            ## 关键结论
            通过本视频的学习，观众将能够全面了解{purpose}的相关知识，并能够在实际中加以应用。

            ## 错误信息
            生成Chat模式素材时发生错误: {str(e)}
            """

    def _validate_and_clean_image_links(self, content: str) -> str:
        """
        验证markdown内容中的图片链接是否可以本地访问，如果不能访问则删除图片链接和对应的文字描述

        参数:
        - content: markdown内容

        返回:
        - str: 清理后的markdown内容
        """
        import os
        import re

        logger.info("开始验证和清理图片链接")

        # 记录所有无效的图片链接，用于后续清理相关描述
        invalid_images = []

        # 匹配图片链接的正则表达式
        image_pattern = r"!\[([^\]]*)\]\(([^)]+)\)"

        def check_and_clean_image(match):
            alt_text = match.group(1)
            image_url = match.group(2)

            # 检查是否为网络URL
            if image_url.startswith(("http://", "https://", "ftp://")):
                # 网络URL默认保留，因为我们无法简单验证网络连接
                logger.debug(f"保留网络图片链接: {image_url}")
                return match.group(0)

            # 检查本地文件是否存在
            if image_url.startswith("file://"):
                # 去除file://前缀
                local_path = image_url[7:]
            else:
                local_path = image_url

            # 如果是相对路径，尝试相对于当前工作目录
            if not os.path.isabs(local_path):
                # 尝试多种可能的路径
                possible_paths = [
                    local_path,
                    os.path.join(os.getcwd(), local_path),
                    os.path.join("output", local_path),
                    os.path.join(".", local_path),
                ]

                file_exists = False
                actual_path = None
                for path in possible_paths:
                    if os.path.exists(path) and os.path.isfile(path):
                        file_exists = True
                        actual_path = path
                        break
            else:
                # 绝对路径直接检查
                file_exists = os.path.exists(local_path) and os.path.isfile(local_path)
                actual_path = local_path if file_exists else None

            if file_exists:
                logger.debug(f"图片文件存在，保留链接: {actual_path}")
                return match.group(0)
            else:
                logger.warning(f"图片文件不存在，删除链接: {image_url}")
                # 记录无效图片信息，用于后续清理相关描述
                invalid_images.append({"alt_text": alt_text, "url": image_url, "full_match": match.group(0)})
                return "<<INVALID_IMAGE_PLACEHOLDER>>"  # 使用占位符，方便后续处理

        # 第一步：执行图片链接检查和替换为占位符
        content_with_placeholders = re.sub(image_pattern, check_and_clean_image, content)

        # 第二步：清理无效图片的相关描述文字
        if invalid_images:
            logger.info(f"检测到 {len(invalid_images)} 个无效图片，开始清理相关描述文字")

            # 分行处理，清理包含无效图片占位符的行及其前后描述
            lines = content_with_placeholders.split("\n")
            cleaned_lines = []
            i = 0

            while i < len(lines):
                line = lines[i]

                # 如果这行包含无效图片占位符
                if "<<INVALID_IMAGE_PLACEHOLDER>>" in line:
                    logger.debug(f"清理包含无效图片的行: {line[:100]}...")

                    # 检查前一行是否是对该图片的描述（通常包含"图"、"显示"、"如下"等词汇）
                    if (
                        i > 0
                        and any(
                            keyword in lines[i - 1]
                            for keyword in [
                                "如图",
                                "图",
                                "显示",
                                "如下",
                                "下图",
                                "上图",
                                "见图",
                                "参见",
                                "图片",
                                "截图",
                            ]
                        )
                        and len(lines[i - 1].strip()) < 100
                    ):  # 避免删除长段落
                        logger.debug(f"同时清理前一行的图片描述: {lines[i-1][:50]}...")
                        if cleaned_lines:
                            cleaned_lines.pop()  # 移除前一行

                    # 检查后一行是否是对该图片的说明
                    if (
                        i + 1 < len(lines)
                        and any(keyword in lines[i + 1] for keyword in ["图片说明", "说明", "注:", "注：", "备注"])
                        and len(lines[i + 1].strip()) < 100
                    ):
                        i += 1  # 跳过下一行
                        logger.debug(f"同时清理后一行的图片说明: {lines[i][:50]}...")

                    # 跳过当前行（包含占位符的行）
                    i += 1
                    continue
                else:
                    # 正常行，保留
                    cleaned_lines.append(line)

                i += 1

            # 重新组合内容
            cleaned_content = "\n".join(cleaned_lines)
        else:
            # 没有无效图片，只需要移除占位符（虽然不应该有）
            cleaned_content = content_with_placeholders

        # 第三步：清理多余的空行和占位符
        cleaned_content = re.sub(r"<<INVALID_IMAGE_PLACEHOLDER>>", "", cleaned_content)
        cleaned_content = re.sub(r"\n\s*\n\s*\n+", "\n\n", cleaned_content)  # 多个连续空行合并为两个
        cleaned_content = re.sub(r"^\s*\n+", "", cleaned_content)  # 移除开头的空行
        cleaned_content = re.sub(r"\n+\s*$", "\n", cleaned_content)  # 移除结尾多余的空行

        # 统计删除的图片数量
        original_images = len(re.findall(image_pattern, content))
        remaining_images = len(re.findall(image_pattern, cleaned_content))
        deleted_count = len(invalid_images)

        if deleted_count > 0:
            logger.info(f"删除了 {deleted_count} 个无效的图片链接及相关描述，保留了 {remaining_images} 个有效图片链接")
        else:
            logger.info(f"所有 {original_images} 个图片链接均有效，无需删除")

        return cleaned_content

    def _is_github_project(self, content: str) -> bool:
        """
        判断内容是否为GitHub项目分析
        
        直接检查配置文件中GitHub源的enabled开关

        参数:
        - content: 内容字符串

        返回:
        - bool: 是否为GitHub项目分析
        """
        # 检查配置中GitHub源是否启用
        if hasattr(self, 'config') and hasattr(self.config, 'sources'):
            github_config = getattr(self.config.sources, 'github', None)
            if github_config and getattr(github_config, 'enabled', False):
                logger.info("配置中GitHub源已启用，认定为GitHub项目")
                return True
        
        # 检查传统的config.github_url（向后兼容）
        if hasattr(self, 'config') and hasattr(self.config, 'github_url') and self.config.github_url:
            logger.info("配置中存在github_url，认定为GitHub项目")
            return True
            
        logger.info("配置中GitHub源未启用，认定为非GitHub项目")
        return False


def main():
    """主函数，运行素材规范化处理"""
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description="视频素材规范化处理")
    parser.add_argument("--config", default="config/config.yaml", help="配置文件路径")
    parser.add_argument("--material", required=True, help="原始素材文件路径")
    parser.add_argument("--purpose", help="目的描述（可选，如果不提供将使用配置中的默认值）")
    parser.add_argument("--output", help="输出文件路径")
    parser.add_argument("--rounds", type=int, help="优化迭代轮数")
    parser.add_argument("--github_url", help="覆盖配置中的GitHub项目URL")
    args = parser.parse_args()

    # 初始化素材代理
    agent = MaterialAgent(config_path=args.config)

    # 如果命令行有github_url，覆盖配置中的值
    if args.github_url:
        agent.config.github_url = args.github_url

    # 运行处理流程
    print(f"\n===== 开始处理素材: {args.material} =====")
    print("🎯 采用多媒体元素优先策略：")
    print("   - 优先围绕原素材中的图片、视频、代码、表格等多媒体元素展开内容")
    print("   - 以多媒体元素为主线构建内容结构，让它们成为理解的关键入口")
    print("   - 确保每个主要部分都围绕重要的多媒体元素展开\n")

    if args.purpose:
        print(f"目的描述: {args.purpose}")
    else:
        print("将使用配置中的默认目的描述")

    if args.rounds:
        print(f"指定迭代轮数: {args.rounds}")

    try:
        result = agent.run(
            material_file_path=args.material,
            purpose_description=args.purpose or "",  # 如果没有提供purpose，传递空字符串
            output_file=args.output,
            max_rounds=args.rounds,
        )

        if result.get("success", False):
            print("\n处理成功!")

            # 打印多媒体处理结果
            multimedia = result.get("multimedia_elements", {})
            print("\n多媒体处理:")
            if result.get("github_video_path"):
                print(f"- GitHub项目录屏: {result.get('github_video_path')}")

            print(
                f"- 多媒体元素: {multimedia.get('images_count', 0)}张图片, "
                f"{multimedia.get('gifs_count', 0)}个GIF, "
                f"{multimedia.get('videos_count', 0)}个视频, "
                f"{multimedia.get('audios_count', 0)}个音频, "
                f"{multimedia.get('code_blocks_count', 0)}个代码块, "
                f"{multimedia.get('tables_count', 0)}个表格, "
                f"{multimedia.get('formulas_count', 0)}个公式, "
                f"{multimedia.get('lists_count', 0)}个列表"
            )

            mermaid_total = multimedia.get("mermaid_files_count", 0)
            if mermaid_total > 0:
                print(
                    f"- Mermaid图表: 检测到{mermaid_total}个 "
                    f"独立文件，成功转换{multimedia.get('mermaid_converted_count', 0)}个"
                )

            # 打印目的分析结果
            purpose = result.get("purpose_analysis", {})
            print("\n目的分析结果:")
            print(f"- 目标受众: {purpose.get('target_audience', '未指定')}")
            print(f"- 主题内容: {purpose.get('content_theme', '未指定')}")
            print(f"- 意图分析: {purpose.get('intention', '未指定')}")
            print(f"- 风格偏好: {purpose.get('style_preference', '未指定')}")

            # 打印处理统计
            print("\n处理统计:")
            original_length = result.get("original_length", 0)
            final_length = result.get("final_material_length", 0)
            print(f"- 原始素材: {original_length} 字符")
            print(f"- 最终素材: {final_length} 字符")

            if final_length and original_length:
                change_percent = (final_length - original_length) / original_length * 100
                change_type = "增加" if change_percent > 0 else "减少"
                print(f"- 内容变化: {change_type} {abs(change_percent):.1f}%")

            print(f"\n规范化素材已保存至: {result.get('saved_file')}")
        else:
            print(f"\n处理失败: {result.get('error', '未知错误')}")

    except Exception as e:
        import traceback

        print(f"\n处理过程中发生错误: {str(e)}")
        traceback.print_exc()


if __name__ == "__main__":
    main()
