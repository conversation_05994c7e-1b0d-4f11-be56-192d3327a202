"""
Enhanced Scene Code Generation Agent

This is the refactored version of the scene code generation agent following the design
principles outlined in ENHANCED_SCENE_CODE_AGENT_DESIGN.md:

1. Integration of various tools for planning, code generation, editing and debugging
2. Standard camel framework tool integration
3. Structured workflow with four phases
4. Quality control mechanisms
"""
import argparse
import hashlib
import os
import subprocess
import sys
from pathlib import Path
from typing import Optional

from camel.agents import ChatAgent
from camel.toolkits import FunctionTool
from camel.toolkits.base import BaseToolkit
from loguru import logger

# Add project root to path
cwd = os.getcwd()
sys.path.insert(0, cwd)
from tools.code_agent_tools import (
    bash_execute,
    check_code_issues,
    file_create,
    file_view,
    get_library_docs,
    replace_in_file,
    resolve_library_id,
    search_files,
    sequential_thinking,
)
from utils.common import Config
from utils.create_llm_model import create_model

# Import smolagents toolkit for framework switching
try:
    from agents.smolagents_scene_code_generation_agent import SmolagentsSceneCodeGenerationToolkit

    SMOLAGENTS_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Smolagents not available: {e}")
    SMOLAGENTS_AVAILABLE = False


class EnhancedSceneCodeGenerationToolkit(BaseToolkit):
    """
    Enhanced toolkit for generating Manim code with autonomous debugging capabilities.

    This toolkit implements the enhanced design with:
    - Various toolset for code generation, editing and debugging
    - Sequential thinking integration (with optional enable/disable)
    - Standard camel framework tool integration
    - Structured workflow phases
    - Quality control mechanisms
    """

    def __init__(self, working_dir: Optional[str] = None):
        """
        Initialize the enhanced toolkit.

        Args:
            working_dir: Working directory for file operations (default: current directory)
        """
        self.working_dir = Path(working_dir or os.getcwd()).resolve()

        config = Config().config.get("workflow", {}).get("code_agent", {})
        self.agent_framework = config.get("agent_framework", "camel")
        self.enable_sequential_thinking = config.get("enable_sequential_thinking", False)
        self.enable_get_docs = config.get("enable_get_docs", False)
        max_iteration_per_step = config.get("max_iteration_per_step", 20)

        # Check if smolagents framework is requested and available
        if self.agent_framework == "smolagents":
            if SMOLAGENTS_AVAILABLE:
                logger.info("Using smolagents framework")
                self._smolagents_toolkit = SmolagentsSceneCodeGenerationToolkit(working_dir)
                self.agent = None  # Will use smolagents toolkit directly
            else:
                logger.warning("Smolagents requested but not available, falling back to camel")
                self.agent_framework = "camel"

        # Create camel agent if using camel framework or as fallback
        if self.agent_framework == "camel":
            logger.info("Using camel framework")
            self.agent = ChatAgent(
                system_message=self._create_system_prompt(),
                model=create_model(),
                tools=self.get_all_tools(),  # Framework automatically handles tool descriptions
                max_iteration=max_iteration_per_step,
            )
            self._smolagents_toolkit = None

        logger.info(
            f"Enhanced toolkit initialized with framework: {self.agent_framework}, "
            f"working directory: {self.working_dir}, "
            f"max_iteration_per_step: {max_iteration_per_step}, "
            f"sequential_thinking: {self.enable_sequential_thinking}, get_docs: {self.enable_get_docs}"
        )

    def get_all_tools(self) -> list[FunctionTool]:
        """
        Get all tools as FunctionTool objects for camel framework integration.

        Returns:
            List of FunctionTool objects
        """
        tools = []

        # Add sequential thinking tool conditionally
        if self.enable_sequential_thinking:
            tools.append(FunctionTool(sequential_thinking))

        # Add core tools
        tools.extend(
            [
                FunctionTool(file_view),
                FunctionTool(file_create),
                FunctionTool(replace_in_file),  # New precise file editor
                FunctionTool(search_files),  # File/directory search with regex support
                FunctionTool(bash_execute),
                FunctionTool(check_code_issues),
            ]
        )

        # Add documentation tools conditionally
        if self.enable_get_docs:
            tools.extend(
                [
                    FunctionTool(resolve_library_id),
                    FunctionTool(get_library_docs),
                ]
            )

        return tools

    def _summarize_conversation_history(self, messages, current_error: str) -> str:
        """
        使用小模型总结对话历史，提取关键的尝试、错误和工具调用信息。

        Args:
            memory_context: Agent的记忆上下文
            current_error: 当前遇到的错误信息

        Returns:
            总结后的历史信息字符串
        """
        try:
            if not messages or len(messages) <= 1:
                return "这是第一次尝试，没有历史记录。"

            # 构建历史内容字符串，包括工具调用中的思考内容
            history_content = ""
            for msg in messages[1:]:  # 去掉第一条消息
                if isinstance(msg, dict):
                    role = msg.get("role", "unknown")
                    content = msg.get("content", "")

                    # 处理普通消息内容
                    history_content += f"{role}: {content}\n\n"

                    # 处理工具调用中的思考内容
                    tool_calls = msg.get("tool_calls", [])
                    if tool_calls:
                        for tool_call in tool_calls:
                            if isinstance(tool_call, dict):
                                function = tool_call.get("function", {})
                                if function.get("name") == "sequential_thinking":
                                    try:
                                        import json

                                        args = json.loads(function.get("arguments", "{}"))
                                        thought = args.get("thought", "")
                                        if thought:
                                            history_content += f"思考过程: {thought}\n\n"
                                    except Exception:
                                        pass

            if not history_content.strip():
                return "没有找到有效的历史对话记录。"

            # 创建总结用的小模型
            summary_agent = ChatAgent(
                system_message="你是一个专业的对话历史分析师，擅长总结技术问题解决过程。",
                model=create_model("google/gemini-2.5-flash-lite-preview-06-17"),  # 使用小模型节省成本
            )

            summary_prompt = f"""
请分析以下Manim代码生成过程的对话历史，提取关键信息：

## 对话历史
{history_content}

## 当前错误
{current_error}

请总结：
1. **已尝试的解决方案**：列出之前尝试了哪些方法
2. **查询到的文档或示例**：有哪些可以参考的文档或示例
2. **工具调用结果**：提到了哪些重要的工具调用和结果
3. **遇到的错误类型**：之前解决了哪些错误
4. **当前代码状态**：代码目前的状态如何
5. **学到的经验**：从之前的尝试中学到了什么

请用简洁的中文回答，控制在500字以内。
"""

            response = summary_agent.step(summary_prompt)
            if response and hasattr(response, "msgs") and response.msgs:
                summary = response.msgs[-1].content
                logger.info(f"历史总结生成成功: {summary}")
                return summary
            else:
                return "没有找到有效的历史对话记录。"

        except Exception as e:
            logger.warning(f"历史总结生成失败: {e}")
            return "没有找到有效的历史对话记录。"

    def _create_enhanced_prompt_with_history(
        self, original_prompt: str, history_summary: str = "", current_error: str = ""
    ) -> str:
        """
        创建包含历史总结的增强提示词

        Args:
            original_prompt: 原始的完整提示词
            history_summary: 历史总结信息
            current_error: 当前错误信息

        Returns:
            格式化后的提示词
        """
        if not history_summary and not current_error:
            return original_prompt

        template = """
{original_prompt}

## 📋 上轮迭代总结
{history_summary}

## 🔧 当前需要修复的问题
{current_error}

请基于以上历史经验和当前问题，继续优化代码。重点关注之前没有解决的问题，避免重复之前已经尝试过的无效方案。
"""

        return template.format(
            original_prompt=original_prompt,
            history_summary=history_summary or "这是第一次尝试。",
            current_error=current_error or "继续按照原计划执行。",
        ).strip()

    def _to_absolute_path(self, path: str) -> str:
        """Convert relative path to absolute path"""
        path_obj = Path(path)
        if path_obj.is_absolute():
            return str(path_obj)
        return str(self.working_dir / path_obj)

    def _create_system_prompt(self) -> str:
        """
        Create system prompt with universal code generation guidance.

        This contains all the general tool usage instructions, workflow,
        error handling, and completion criteria that apply to any code generation task.
        """
        return """# 代码生成专家系统
## 🎯 角色定义
你是一个专业的代码生成专家，具备以下核心能力：
- 深度理解各种编程框架和API的最佳实践
- 能够生成高质量、可运行的代码
- 具备自主调试和错误修复能力
- 遵循结构化工作流程，确保代码质量
- 判断识别可以或者应该一起调用的工具，如果有可能尽量一次性调用多个工具（比如修改代码之后需要运行代码检查工具），提高效率，返回的多个工具调用会顺序执行

## 🔄 结构化执行流程

### 阶段1：信息收集与规划 📚
**必须完成的步骤**：
1. 基于已有知识分析技术需求
2. 识别需要的核心对象和方法
3. 制定基于经验的实现计划

**验证条件**：获得清晰的实现思路和技术方案
**失败处理**：如果需求不明确，重新分析技术描述

### 阶段2：代码框架创建 💻
**必须完成的步骤**：
1. 使用file_create创建基础代码框架
2. 包含完整的导入语句和主要结构定义
3. 立即调用check_code_issues验证语法

**验证条件**：
- 文件成功创建
- 无语法错误
- 基础结构完整

**失败处理**：
- 文件创建失败：检查路径和权限
- 语法错误：立即修复或查询文档

### 阶段3：功能实现与完善 🔧
**必须完成的步骤**：
1. 使用replace_in_file逐步添加功能
2. 每次修改后立即调用check_code_issues
3. 确保每个功能模块都正确实现

**验证条件**：
- 每次修改后无新的语法错误
- 功能实现符合技术描述要求
- API使用正确

**失败处理**：
- 语法错误：分析错误类型，使用replace_in_file精确修复
- API错误：查询相关文档，确认正确用法
- 逻辑错误：重新分析需求，调整实现方案

### 阶段4：质量保证与验证 ✅
**必须完成的步骤**：
1. 编译检查：bash_execute("python -m py_compile 文件路径")
2. 框架特定验证：执行相应的验证命令
3. 最终代码质量检查

**验证条件**：
- 编译无错误
- 框架验证成功
- 代码符合所有质量标准

**失败处理**：
- 编译错误：根据错误信息修复语法或导入问题
- 框架错误：查询相关API文档，修复使用方法
- 持续失败：分析根本原因，可能需要重新设计实现方案

## 🚨 错误处理机制

### 错误分类与处理策略
1. **语法错误**：
   - 检测方法：check_code_issues返回语法错误信息
   - 处理策略：使用replace_in_file精确修复
   - 重试限制：同一错误最多修复3次

2. **导入错误**：
   - 检测方法：python -m py_compile失败
   - 处理策略：查询文档确认正确的导入方式
   - 常见问题：模块路径、包结构等

3. **API使用错误**：
   - 检测方法：框架验证失败
   - 处理策略：查询相关API文档，使用正确的方法和参数
   - 重试策略：基于文档重新实现相关功能

4. **逻辑错误**：
   - 检测方法：代码运行但效果不符合预期
   - 处理策略：重新分析技术描述，调整实现逻辑
   - 验证方法：确保实现符合原始需求

### 重试和退出机制
- **正常完成**：所有验证通过，任务成功完成
- **达到重试限制**：同一问题修复3次仍失败，查询更多文档或报告具体问题
- **无法解决的错误**：明确说明问题原因和已尝试的解决方案

## ✅ 任务完成标准

### 必须满足的条件（缺一不可）
1. **文件创建成功**：使用file_create成功创建代码文件
2. **语法检查通过**：check_code_issues返回"No issues found"
3. **编译检查通过**：python -m py_compile无错误输出
4. **框架验证通过**：相应的框架验证命令成功执行
5. **功能完整性**：实现技术描述中的所有功能
6. **代码质量**：遵循最佳实践，代码结构清晰

### 停止执行的条件
- ✅ **任务成功完成**：所有验证条件都满足
- ❌ **达到最大迭代次数**：报告失败原因和已尝试的解决方案
- ⚠️ **遇到无法解决的错误**：详细说明问题和建议的解决方向

### 禁止的行为
- 不要在任务完成前提前停止
- 不要跳过任何验证步骤
- 不要假设代码正确而不进行检查
- 不要忽略任何错误信息

**重要提醒**：
- 严格按照上述流程执行，不要跳过任何步骤
- 每个阶段都必须完成验证才能进入下一阶段
- 遇到错误必须立即处理，不能忽略
- 确保最终代码能够成功通过所有验证
- 任务完成前不要停止执行"""

    def _create_task_prompt(self, scene_description: str, output_path: str) -> str:
        """
        Create task-specific prompt for Manim code generation.

        This contains only the Manim-specific requirements and task description,
        while the general tool usage guidance is in the system prompt.
        """
        return f"""# Manim代码生成任务

## 🎯 技术栈要求
- **框架**：Manim Community Edition
- **语言**：Python 3.8+
- **导入方式**：from manim import *

## 📋 Manim特定指导

### 文档查询重点
- 不确定的变量、参数、函数等
- 代码检查或者运行过程中报错的地方

### Manim验证命令
- **编译检查**：`python -m py_compile {output_path}`
- **Manim验证**：`manim --dry_run {output_path}`
- **语法检查**：`check_code_issues(["{output_path}"], "error")`

### 代码质量标准
1. **结构完整性**：
   - 包含完整的Scene类定义
   - 实现construct方法
   - 正确的导入语句

2. **Manim最佳实践**：
   - 使用合适的动画时长和缓动
   - 合理的对象布局和间距
   - 适当的颜色搭配和视觉效果

3. **功能完整性**：
   - 实现所有描述的视觉元素
   - 正确的动画序列
   - 符合教育视频的表现要求

## 🎯 任务目标
为以下技术实现描述生成高质量的Manim代码，保存到文件：`{output_path}`

### 技术实现描述
```
{scene_description}
```

## ✅ 成功标准
1. **代码创建**：成功创建Python文件
2. **语法正确**：无Python语法错误
3. **编译通过**：python -m py_compile成功
4. **Manim验证**：manim --dry_run成功执行
5. **功能完整**：实现描述中的所有视觉元素和动画
6. **质量达标**：遵循Manim最佳实践

**开始执行任务，严格按照系统提示的4阶段流程进行。**"""

    def generate_manim_code_enhanced(
        self, scene_description: str, output_file: str, max_iterations: int = 3
    ) -> Optional[str]:
        """
        Enhanced Manim code generation with structured workflow.

        This method implements the four-phase enhanced workflow:
        1. Deep Planning Phase (using sequential_thinking)
        2. Documentation Query Phase (using context7)
        3. Progressive Implementation Phase (step-by-step code building)
        4. Quality Assurance Phase (checking and fixing)

        Args:
            scene_description: Description of the scene to generate
            output_file: Output file path for the generated code
            max_iterations: Maximum number of iterations for quality improvement

        Returns:
            Path to generated and debugged code file, or None on failure
        """
        # Delegate to appropriate framework implementation
        if self.agent_framework == "smolagents" and self._smolagents_toolkit:
            logger.info("Using smolagents framework for code generation")
            return self._smolagents_toolkit.generate_manim_code_enhanced(scene_description, output_file, max_iterations)
        else:
            logger.info("Using camel framework for code generation")
            return self._generate_manim_code_camel(scene_description, output_file, max_iterations)

    def _generate_manim_code_camel(
        self, scene_description: str, output_file: str, max_iterations: int = 3
    ) -> Optional[str]:
        """
        Camel-based Manim code generation implementation.
        This is the original implementation moved to a separate method.
        """
        output_path = self._to_absolute_path(output_file)

        # Create task-specific prompt (system prompt is already set in agent initialization)
        task_prompt = self._create_task_prompt(scene_description, output_path)

        try:
            logger.info("Starting camel-based code generation...")

            # 保存原始prompt，用于后续迭代
            original_task_prompt = task_prompt

            for iteration in range(max_iterations):
                logger.info(f"Iteration {iteration + 1}/{max_iterations}")

                response = self.agent.step(task_prompt)

                if not response:
                    logger.error("Agent did not return a response.")
                    continue

                # Handle the response properly
                if hasattr(response, "msgs") and response.msgs:
                    # Handle multiple messages - use the last message
                    if len(response.msgs) > 1:
                        logger.info(f"Agent returned {len(response.msgs)} messages, using the last one")

                    last_msg = response.msgs[-1]
                    logger.info(f"Agent response: {last_msg.content}")
                else:
                    logger.error("Agent response has no messages.")
                    continue

                # Check if file was created and has no critical issues
                if Path(output_path).exists():
                    issues_result = check_code_issues([output_path], "error")
                    logger.info(f"Iteration {iteration + 1}, Code issues: {issues_result}")

                    if "No issues found" in issues_result or "❌" not in issues_result:
                        logger.success(f"Code generation completed after {iteration + 1} iterations")
                        return output_path

                # 每次 agent.step 后都生成历史总结，用于下一轮迭代
                if iteration < max_iterations - 1:  # 不是最后一次迭代
                    try:
                        memory_context = self.agent.memory.get_context()
                        # 提取消息列表
                        if isinstance(memory_context, (list, tuple)) and len(memory_context) >= 1:
                            messages = memory_context[0] if isinstance(memory_context[0], list) else []
                        else:
                            messages = []

                        # 生成历史总结
                        history_summary = self._summarize_conversation_history(messages, "")
                    except Exception as e:
                        logger.warning(f"获取历史总结失败: {e}")
                        history_summary = ""

                    # 准备下一轮的错误信息
                    if Path(output_path).exists():
                        current_error = f"文件路径: {output_path}\n检测到的问题: {issues_result}"
                    else:
                        current_error = f"代码需要写入文件 {output_path} 中，但文件尚未创建。请确保使用 file_create 工具创建代码文件。"

                    # 构造下一轮的 task_prompt
                    task_prompt = self._create_enhanced_prompt_with_history(
                        original_task_prompt, history_summary, current_error
                    )

            return output_path if Path(output_path).exists() else None

        except Exception as e:
            logger.error(f"Error in enhanced code generation: {e}")
            return None

    def render_manim_code(self, code_file: str, quality: str = "l") -> Optional[str]:
        """
        Render Manim code to video.

        Args:
            code_file: Path to the Python code file
            quality: Rendering quality (l, m, h)

        Returns:
            Path to rendered video file, or None on failure
        """
        # Delegate to appropriate framework implementation
        if self.agent_framework == "smolagents" and self._smolagents_toolkit:
            return self._smolagents_toolkit.render_manim_code(code_file, quality)
        else:
            return self._render_manim_code_camel(code_file, quality)

    def _render_manim_code_camel(self, code_file: str, quality: str = "l") -> Optional[str]:
        """
        Camel-based Manim code rendering implementation.
        This is the original implementation moved to a separate method.
        """
        try:
            code_path = Path(code_file)
            if not code_path.exists():
                logger.error(f"Code file not found: {code_file}")
                return None

            # Prepare manim command
            cmd = [
                "manim",
                str(code_path),
                "--quality",
                quality,
            ]

            logger.info(f"Rendering Manim code: {' '.join(cmd)}")

            # Run manim command
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
            )

            if result.returncode == 0:
                resolution = {
                    "l": "480p15",
                    "m": "720p30",
                    "h": "1080p60",
                    "q": "1440p60",
                    "k": "2160p60",
                }[quality]
                output_path = Path("media") / "videos" / code_path.stem / resolution
                # Find the generated video file
                video_files = list(output_path.glob("*.mp4"))
                if video_files:
                    # Sort by modification time (newest first) and take the most recent one
                    video_file = max(video_files, key=lambda f: f.stat().st_mtime)
                    logger.success(f"Video rendered successfully: {video_file}")
                    return str(video_file)
                else:
                    logger.error(f"No video file found at {output_path} after rendering")
                    return None
            else:
                error_msg = result.stderr or result.stdout
                logger.error(f"Manim rendering failed: {error_msg}")
                raise subprocess.CalledProcessError(result.returncode, cmd, error_msg)

        except subprocess.TimeoutExpired:
            logger.error("Manim rendering timed out")
            return None
        except Exception as e:
            logger.error(f"Failed to render Manim code: {e}")
            raise


# ============================================================================
# ENHANCED SCENE CODE PROCESSOR
# ============================================================================


class EnhancedSceneCodeProcessor:
    """
    Enhanced processor for generating Manim code from scene description files.

    This processor uses the enhanced toolkit with structured workflow and
    autonomous debugging capabilities.
    """

    def __init__(self, output_dir: str = "output/generated_code"):
        """
        Initialize the enhanced scene code processor.

        Args:
            output_dir: Directory to save generated code and videos
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.code_toolkit = EnhancedSceneCodeGenerationToolkit(str(self.output_dir))

    def process_scene_with_enhanced_workflow(
        self,
        scene_file: str,
        scene_description: str,
        max_iterations: int = 3,
        quality: str = "l",
        scene_num: int = 0,
        topic: str = "",
    ) -> Optional[dict[str, Optional[str]]]:
        """
        Process a scene description file with enhanced workflow.

        Args:
            scene_file: Path to the scene description file
            max_iterations: Maximum number of iterations for code improvement
            quality: Rendering quality (l, m, h, q, k)

        Returns:
            A dictionary containing paths to generated files, or None if processing failed
        """
        try:
            logger.info(f"[Scene {scene_file}] Starting enhanced processing")

            # Read scene description
            scene_path = Path(scene_file)
            if not scene_path.is_file():
                if not scene_description:
                    raise FileNotFoundError(
                        f"Scene description file not found: {scene_file}, and no scene description provided"
                    )
            else:
                with open(scene_path, encoding="utf-8") as f:
                    scene_description = f.read()

            # Generate output file paths
            scene_name = scene_path.stem
            if not scene_name:
                if topic:
                    scene_name = f"{topic}_{scene_num}"
                else:
                    scene_name = hashlib.md5(scene_description.encode("utf-8")).hexdigest()[:8]
            code_filename = self.output_dir / f"{scene_name}_enhanced_code.py"

            # Generate Manim code with enhanced workflow
            logger.info(f"Generating Manim code for scene: {scene_name}")
            generated_code_file = self.code_toolkit.generate_manim_code_enhanced(
                scene_description=scene_description,
                output_file=str(code_filename),
                max_iterations=max_iterations,
            )

            if not generated_code_file:
                raise ValueError(f"Failed to generate Manim code for scene: {scene_name}")

            result_paths = {"final_code_path": generated_code_file, "final_video_path": None, "success": False}

            # Render the code to video
            logger.info(f"Rendering Manim code to video: {generated_code_file}")
            rendered_video_file = self.code_toolkit.render_manim_code(code_file=generated_code_file, quality=quality)

            if rendered_video_file:
                result_paths["final_video_path"] = rendered_video_file
                result_paths["success"] = True
                logger.success(f"Enhanced scene processing completed: {scene_name}")
            else:
                logger.warning(f"Code generated but video rendering failed: {scene_name}")

            return result_paths

        except Exception as e:
            logger.error(f"[Scene {scene_file}] Enhanced processing failed: {e}")
            return None


# ============================================================================
# CONVENIENCE FUNCTIONS
# ============================================================================


def process_scene_file_enhanced(
    scene_file: str = "",
    scene_description: str = "",
    output_dir: str = "output/generated_code",
    max_iterations: int = 3,
    quality: str = "l",
    scene_num: int = 0,
    topic: str = "",
) -> Optional[dict[str, Optional[str]]]:
    """
    Process a single scene description file with enhanced workflow.

    Args:
        scene_file: Path to the scene description file
        scene_description: Scene description content
        output_dir: Directory to save generated files
        max_iterations: Maximum number of iterations for code improvement
        quality: Rendering quality (l, m, h, q, k)
        scene_num: Scene number for naming
        topic: Topic for naming

    Returns:
        Dictionary containing paths to generated files, or None on failure
    """
    processor = EnhancedSceneCodeProcessor(output_dir)

    result = processor.process_scene_with_enhanced_workflow(
        scene_file=scene_file,
        scene_description=scene_description,
        max_iterations=max_iterations,
        quality=quality,
        scene_num=scene_num,
        topic=topic,
    )
    return result


def test_enhanced_toolkit():
    """Test function to verify enhanced toolkit initialization."""
    toolkit = EnhancedSceneCodeGenerationToolkit()
    tools = toolkit.get_all_tools()
    logger.info(f"Enhanced toolkit test passed. Available tools: {len(tools)}")
    for tool in tools:
        logger.info(f"  - {tool.get_function_name()}")


# ============================================================================
# MAIN PROGRAM
# ============================================================================


def main():
    """Main execution function with enhanced workflow support."""
    parser = argparse.ArgumentParser(
        description="Enhanced Manim code generation with structured workflow and autonomous debugging"
    )
    parser.add_argument("scene_file", nargs="?", help="Path to the scene description file")
    parser.add_argument(
        "--quality",
        "-q",
        choices=["l", "m", "h", "q", "k"],
        default="l",
        help="Rendering quality: l (low), m (medium), h (high), q (2K), k (4K). Default: l",
    )
    parser.add_argument(
        "--max-iterations", "-i", type=int, default=3, help="Maximum iterations for code improvement. Default: 3"
    )
    parser.add_argument(
        "--output-dir",
        "-o",
        default="output/generated_code",
        help="Output directory for generated files. Default: output/generated_code",
    )

    parser.add_argument("--test", "-t", action="store_true", help="Test toolkit initialization and exit")

    args = parser.parse_args()

    if args.test:
        logger.info("Testing enhanced toolkit initialization...")
        test_enhanced_toolkit()
        return

    if not args.scene_file:
        parser.error("scene_file is required when not in test mode")

    result = process_scene_file_enhanced(
        scene_file=args.scene_file,
        output_dir=args.output_dir,
        max_iterations=args.max_iterations,
        quality=args.quality,
    )

    if result:
        print("✅ Success! Generated files:")
        for key, path in result.items():
            print(f"  {key}: {path}")
        return result
    else:
        print("❌ Failed to process scene description.")
        return {}


if __name__ == "__main__":
    main()
