"""
统一信息收集工具

提供了从多种来源收集和处理信息的功能
"""

from .adapters import (
    BaseAdapter,
    DuckDuckGoAdapter,
    PDFAdapter,
    SearchAdapter,
    SearXNGAdapter,
    TextAdapter,
    WebContentAdapter,
)
from .cache import CacheSystem
from .collector import InfoCollector
from .format_manager import FormatManager, clean_text
from .models import (
    ContentImage,
    ContentMetadata,
    ContentSection,
    ContentTable,
    UnifiedContent,
)
from .source_manager import SourceManager
from .tavily_adapter import TavilyAdapter

__all__ = [
    # 主组件
    "InfoCollector",
    "SourceManager",
    "FormatManager",
    "CacheSystem",
    # 适配器
    "BaseAdapter",
    "SearchAdapter",
    "DuckDuckGoAdapter",
    "SearXNGAdapter",
    "TavilyAdapter",
    "WebContentAdapter",
    "PDFAdapter",
    "TextAdapter",
    # 数据模型
    "UnifiedContent",
    "ContentMetadata",
    "ContentSection",
    "ContentImage",
    "ContentTable",
    # 工具函数
    "clean_text",
]
