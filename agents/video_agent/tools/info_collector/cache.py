"""
缓存系统模块

提供多级缓存策略，用于存储和检索处理过的内容，避免重复处理
"""

import hashlib
import json
import os
import pickle
import time
from pathlib import Path
from typing import Any, Optional, Union

from loguru import logger

from .models import UnifiedContent

# 默认缓存过期时间（秒）
DEFAULT_CACHE_EXPIRY = {
    "search": 24 * 60 * 60,  # 搜索结果：24小时
    "web": 7 * 24 * 60 * 60,  # 网页内容：7天
    "pdf": 30 * 24 * 60 * 60,  # PDF处理结果：30天
    "default": 24 * 60 * 60,  # 默认：24小时
}


class MemoryCache:
    """内存缓存实现，使用LRU策略管理缓存项"""

    def __init__(self, max_size: int = 100):
        """
        初始化内存缓存

        Args:
            max_size: 最大缓存条目数
        """
        self.max_size = max_size
        self.cache: dict[str, tuple[Any, float]] = {}  # (value, timestamp)
        self.access_order: dict[str, float] = {}  # key -> last_access_time

    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存项

        Args:
            key: 缓存键

        Returns:
            缓存值，如不存在则返回None
        """
        if key in self.cache:
            value, timestamp = self.cache[key]
            self.access_order[key] = time.time()  # 更新访问时间
            return value
        return None

    def set(self, key: str, value: Any, expiry: Optional[int] = None) -> None:
        """
        设置缓存项

        Args:
            key: 缓存键
            value: 缓存值
            expiry: 过期时间（秒），如不指定则不会过期
        """
        # 如果缓存已满，移除最近最少使用的项
        if len(self.cache) >= self.max_size and key not in self.cache:
            self._evict()

        now = time.time()
        expires_at = now + expiry if expiry else float("inf")
        self.cache[key] = (value, expires_at)
        self.access_order[key] = now

    def delete(self, key: str) -> bool:
        """
        删除缓存项

        Args:
            key: 缓存键

        Returns:
            是否成功删除
        """
        if key in self.cache:
            del self.cache[key]
            del self.access_order[key]
            return True
        return False

    def clear(self) -> None:
        """清空缓存"""
        self.cache.clear()
        self.access_order.clear()

    def _evict(self) -> None:
        """移除最近最少使用的缓存项"""
        if not self.access_order:
            return

        # 找出最近最少使用的键
        oldest_key = min(self.access_order.items(), key=lambda x: x[1])[0]
        self.delete(oldest_key)

    def cleanup_expired(self) -> int:
        """
        清理过期的缓存项

        Returns:
            清理的缓存项数量
        """
        now = time.time()
        expired_keys = [key for key, (_, expires_at) in self.cache.items() if expires_at <= now]

        for key in expired_keys:
            self.delete(key)

        return len(expired_keys)


class DiskCache:
    """磁盘缓存实现，支持持久存储"""

    def __init__(self, cache_dir: Optional[str] = "info_cache"):
        """
        初始化磁盘缓存

        Args:
            cache_dir: 缓存目录
        """
        self.cache_dir = Path(cache_dir or "info_cache")
        self.cache_dir.mkdir(parents=True, exist_ok=True)

        # 创建子目录
        self.metadata_dir = self.cache_dir / "metadata"
        self.metadata_dir.mkdir(exist_ok=True)

    def _get_cache_path(self, key: str, source_type: str = "default") -> Path:
        """
        获取缓存文件路径

        Args:
            key: 缓存键
            source_type: 信息源类型

        Returns:
            缓存文件路径
        """
        # 创建源类型子目录
        source_dir = self.cache_dir / source_type
        source_dir.mkdir(exist_ok=True)

        # 将键转换为安全的文件名
        safe_key = hashlib.md5(key.encode()).hexdigest()
        return source_dir / f"{safe_key}.pickle"

    def _get_metadata_path(self, key: str, source_type: str = "default") -> Path:
        """
        获取元数据文件路径

        Args:
            key: 缓存键
            source_type: 信息源类型

        Returns:
            元数据文件路径
        """
        # 将键转换为安全的文件名
        safe_key = hashlib.md5(key.encode()).hexdigest()
        return self.metadata_dir / f"{safe_key}.json"

    def get(self, key: str, source_type: str = "default") -> Optional[Any]:
        """
        获取缓存项

        Args:
            key: 缓存键
            source_type: 信息源类型

        Returns:
            缓存值，如不存在或已过期则返回None
        """
        cache_path = self._get_cache_path(key, source_type)
        metadata_path = self._get_metadata_path(key, source_type)

        # 检查元数据是否存在
        if not metadata_path.exists():
            return None

        # 读取元数据
        try:
            with open(metadata_path, encoding="utf-8") as f:
                metadata = json.load(f)

            # 检查是否过期
            if metadata.get("expires_at"):
                expires_at = metadata["expires_at"]
                if time.time() > expires_at:
                    return None

            # 检查缓存文件是否存在
            if not cache_path.exists():
                return None

            # 读取缓存数据
            with open(cache_path, "rb") as f:
                value = pickle.load(f)

            return value

        except Exception as e:
            logger.error(f"读取缓存出错: {e}")
            return None

    def set(self, key: str, value: Any, source_type: str = "default", expiry: Optional[int] = None) -> None:
        """
        设置缓存项

        Args:
            key: 缓存键
            value: 缓存值
            source_type: 信息源类型
            expiry: 过期时间（秒），如不指定则使用默认过期时间
        """
        cache_path = self._get_cache_path(key, source_type)
        metadata_path = self._get_metadata_path(key, source_type)

        try:
            # 保存缓存数据
            with open(cache_path, "wb") as f:
                pickle.dump(value, f)

            # 计算过期时间
            if expiry is None:
                expiry = DEFAULT_CACHE_EXPIRY.get(source_type, DEFAULT_CACHE_EXPIRY["default"])

            expires_at = time.time() + expiry if expiry > 0 else None

            # 保存元数据
            metadata = {
                "key": key,
                "source_type": source_type,
                "created_at": time.time(),
                "expires_at": expires_at,
                "size": os.path.getsize(cache_path),
            }

            with open(metadata_path, "w", encoding="utf-8") as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"保存缓存出错: {e}")

    def delete(self, key: str, source_type: str = "default") -> bool:
        """
        删除缓存项

        Args:
            key: 缓存键
            source_type: 信息源类型

        Returns:
            是否成功删除
        """
        cache_path = self._get_cache_path(key, source_type)
        metadata_path = self._get_metadata_path(key, source_type)

        deleted = False

        # 删除缓存文件
        if cache_path.exists():
            cache_path.unlink()
            deleted = True

        # 删除元数据
        if metadata_path.exists():
            metadata_path.unlink()
            deleted = True

        return deleted

    def cleanup_expired(self) -> int:
        """
        清理过期的缓存项

        Returns:
            清理的缓存项数量
        """
        count = 0
        now = time.time()

        # 遍历所有元数据文件
        for metadata_file in self.metadata_dir.glob("*.json"):
            try:
                with open(metadata_file, encoding="utf-8") as f:
                    metadata = json.load(f)

                # 检查是否过期
                if metadata.get("expires_at") and metadata["expires_at"] <= now:
                    key = metadata["key"]
                    source_type = metadata.get("source_type", "default")

                    # 删除缓存项
                    if self.delete(key, source_type):
                        count += 1

            except Exception as e:
                logger.error(f"清理缓存出错: {e}, 文件: {metadata_file}")

        return count

    def clear(self, source_type: Optional[str] = None, older_than: Optional[int] = None) -> int:
        """
        清空缓存

        Args:
            source_type: 信息源类型，如指定则只清理该类型的缓存
            older_than: 清理多少秒前的缓存，如指定则只清理该时间之前的缓存

        Returns:
            清理的缓存项数量
        """
        count = 0
        now = time.time()

        # 遍历所有元数据文件
        for metadata_file in self.metadata_dir.glob("*.json"):
            try:
                with open(metadata_file, encoding="utf-8") as f:
                    metadata = json.load(f)

                should_delete = True

                # 检查类型
                if source_type and metadata.get("source_type") != source_type:
                    should_delete = False

                # 检查时间
                if older_than and metadata.get("created_at") and metadata["created_at"] > (now - older_than):
                    should_delete = False

                # 删除缓存项
                if should_delete:
                    key = metadata["key"]
                    source_type = metadata.get("source_type", "default")

                    if self.delete(key, source_type):
                        count += 1

            except Exception as e:
                logger.error(f"清理缓存出错: {e}, 文件: {metadata_file}")

        return count

    def get_stats(self) -> dict[str, Any]:
        """
        获取缓存统计信息

        Returns:
            缓存统计信息
        """
        stats = {
            "total_items": 0,
            "total_size": 0,
            "by_source_type": {},
            "expired_items": 0,
        }

        now = time.time()

        # 遍历所有元数据文件
        for metadata_file in self.metadata_dir.glob("*.json"):
            try:
                with open(metadata_file, encoding="utf-8") as f:
                    metadata = json.load(f)

                source_type = metadata.get("source_type", "default")
                size = metadata.get("size", 0)

                # 更新统计信息
                stats["total_items"] += 1
                stats["total_size"] += size

                # 按源类型统计
                if source_type not in stats["by_source_type"]:
                    stats["by_source_type"][source_type] = {
                        "items": 0,
                        "size": 0,
                    }

                stats["by_source_type"][source_type]["items"] += 1
                stats["by_source_type"][source_type]["size"] += size

                # 统计过期项
                if metadata.get("expires_at") and metadata["expires_at"] <= now:
                    stats["expired_items"] += 1

            except Exception as e:
                logger.error(f"获取缓存统计出错: {e}, 文件: {metadata_file}")

        return stats


class CacheSystem:
    """缓存系统，结合内存缓存和磁盘缓存"""

    def __init__(
        self,
        cache_dir: str = "info_cache",
        enable_memory_cache: bool = True,
        enable_disk_cache: bool = True,
        memory_cache_size: int = 100,
        cache_expiry: dict[str, int] = None,
    ):
        """
        初始化缓存系统

        Args:
            cache_dir: 缓存目录
            enable_memory_cache: 是否启用内存缓存
            enable_disk_cache: 是否启用磁盘缓存
            memory_cache_size: 内存缓存最大条目数
            cache_expiry: 各类型缓存过期时间
        """
        self.enable_memory_cache = enable_memory_cache
        self.enable_disk_cache = enable_disk_cache
        self.cache_expiry = cache_expiry or DEFAULT_CACHE_EXPIRY

        # 初始化缓存
        self.memory_cache = MemoryCache(max_size=memory_cache_size) if enable_memory_cache else None
        self.disk_cache = DiskCache(cache_dir=cache_dir) if enable_disk_cache else None

    def _generate_key(self, obj: Any, prefix: str = "") -> str:
        """
        生成缓存键

        Args:
            obj: 对象
            prefix: 键前缀

        Returns:
            缓存键
        """
        # 简单对象可以直接转为字符串
        if isinstance(obj, (str, int, float, bool, type(None))):
            return f"{prefix}:{obj}"

        # 字典、列表等复杂对象需要序列化后再计算哈希值
        try:
            serialized = json.dumps(obj, sort_keys=True)
            key_hash = hashlib.md5(serialized.encode()).hexdigest()
            return f"{prefix}:{key_hash}"
        except (TypeError, ValueError):
            # 对于不可序列化的对象，使用对象ID和类型
            return f"{prefix}:obj:{id(obj)}:{type(obj).__name__}"

    def get(self, key: str, source_type: str = "default") -> Optional[Union[UnifiedContent, Any]]:
        """
        获取缓存项

        Args:
            key: 缓存键
            source_type: 信息源类型

        Returns:
            缓存值，如不存在则返回None
        """
        # 尝试从内存缓存获取
        if self.enable_memory_cache:
            mem_key = f"{source_type}:{key}"
            value = self.memory_cache.get(mem_key)
            if value is not None:
                logger.debug(f"内存缓存命中: {mem_key}")
                return value

        # 尝试从磁盘缓存获取
        if self.enable_disk_cache:
            value = self.disk_cache.get(key, source_type)
            if value is not None:
                logger.debug(f"磁盘缓存命中: {key} (类型: {source_type})")

                # 更新内存缓存
                if self.enable_memory_cache:
                    mem_key = f"{source_type}:{key}"
                    self.memory_cache.set(
                        mem_key,
                        value,
                        expiry=self.cache_expiry.get(source_type, self.cache_expiry["default"]),
                    )

                return value

        logger.debug(f"缓存未命中: {key} (类型: {source_type})")
        return None

    def set(self, key: str, value: Any, source_type: str = "default", expiry: Optional[int] = None) -> None:
        """
        设置缓存项

        Args:
            key: 缓存键
            value: 缓存值
            source_type: 信息源类型
            expiry: 过期时间（秒），如不指定则使用默认过期时间
        """
        # 计算过期时间
        if expiry is None:
            expiry = self.cache_expiry.get(source_type, self.cache_expiry["default"])

        # 更新内存缓存
        if self.enable_memory_cache:
            mem_key = f"{source_type}:{key}"
            self.memory_cache.set(mem_key, value, expiry=expiry)

        # 更新磁盘缓存
        if self.enable_disk_cache:
            self.disk_cache.set(key, value, source_type=source_type, expiry=expiry)

        logger.debug(f"缓存已更新: {key} (类型: {source_type})")

    def delete(self, key: str, source_type: str = "default") -> bool:
        """
        删除缓存项

        Args:
            key: 缓存键
            source_type: 信息源类型

        Returns:
            是否成功删除
        """
        deleted = False

        # 删除内存缓存
        if self.enable_memory_cache:
            mem_key = f"{source_type}:{key}"
            if self.memory_cache.delete(mem_key):
                deleted = True
                logger.debug(f"内存缓存已删除: {mem_key}")

        # 删除磁盘缓存
        if self.enable_disk_cache:
            if self.disk_cache.delete(key, source_type):
                deleted = True
                logger.debug(f"磁盘缓存已删除: {key} (类型: {source_type})")

        return deleted

    def clear(self, source_type: Optional[str] = None, older_than: Optional[int] = None) -> int:
        """
        清空缓存

        Args:
            source_type: 信息源类型，如指定则只清理该类型的缓存
            older_than: 清理多少秒前的缓存，如指定则只清理该时间之前的缓存

        Returns:
            清理的缓存项数量
        """
        count = 0

        # 清理内存缓存
        if self.enable_memory_cache:
            self.memory_cache.clear()
            count += 1  # 简化计数，只算作一项

        # 清理磁盘缓存
        if self.enable_disk_cache:
            disk_count = self.disk_cache.clear(source_type, older_than)
            count += disk_count

        logger.info(f"缓存已清理: {count} 项")
        return count

    def cleanup_expired(self) -> int:
        """
        清理过期的缓存项

        Returns:
            清理的缓存项数量
        """
        count = 0

        # 清理内存缓存
        if self.enable_memory_cache:
            mem_count = self.memory_cache.cleanup_expired()
            count += mem_count
            logger.debug(f"内存缓存已清理: {mem_count} 项")

        # 清理磁盘缓存
        if self.enable_disk_cache:
            disk_count = self.disk_cache.cleanup_expired()
            count += disk_count
            logger.debug(f"磁盘缓存已清理: {disk_count} 项")

        if count > 0:
            logger.info(f"过期缓存已清理: {count} 项")

        return count

    def get_stats(self) -> dict[str, Any]:
        """
        获取缓存统计信息

        Returns:
            缓存统计信息
        """
        stats = {
            "memory_cache": None,
            "disk_cache": None,
        }

        # 获取内存缓存统计
        if self.enable_memory_cache:
            stats["memory_cache"] = {
                "enabled": True,
                "items": len(self.memory_cache.cache),
                "max_size": self.memory_cache.max_size,
            }
        else:
            stats["memory_cache"] = {"enabled": False}

        # 获取磁盘缓存统计
        if self.enable_disk_cache:
            stats["disk_cache"] = {
                "enabled": True,
                **self.disk_cache.get_stats(),
            }
        else:
            stats["disk_cache"] = {"enabled": False}

        return stats
