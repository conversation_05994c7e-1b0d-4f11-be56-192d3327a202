"""
信息收集器

统一的信息收集和处理系统
"""

import json
import os
import time
from pathlib import Path
from typing import Any, Optional

from loguru import logger

from .format_manager import FormatManager
from .models import UnifiedContent
from .source_manager import SourceManager


class InfoCollector:
    """
    信息收集器

    统一管理不同的信息收集方式，提供一致的接口和输出格式
    """

    def __init__(
        self,
        use_cache: bool = True,
        cache_dir: Optional[str] = None,
        default_output_format: str = "markdown",
    ):
        """
        初始化信息收集器

        Args:
            use_cache: 是否使用缓存
            cache_dir: 缓存目录
            default_output_format: 默认输出格式
        """
        # 初始化组件
        self.source_manager = SourceManager(use_cache=use_cache, cache_dir=cache_dir)
        self.format_manager = FormatManager()
        self.default_output_format = default_output_format
        self.image_resolution_scale = 5.0  # 用于PDF图片处理的分辨率缩放比例

        logger.info(f"信息收集器初始化完成，默认输出格式: {default_output_format}")

        # 记录可用的适配器
        adapters = self.source_manager.list_adapters()
        logger.info(f"已注册的适配器: {', '.join(adapters)}")

    def search(
        self,
        query: str,
        engine: str = "duckduckgo",
        max_results: int = 5,
        output_format: Optional[str] = None,
        **kwargs,
    ) -> Any:
        """
        进行搜索

        Args:
            query: 搜索查询
            engine: 搜索引擎
            max_results: 最大结果数
            output_format: 输出格式
            **kwargs: 其他搜索参数

        Returns:
            搜索结果
        """
        # 设置结果数量
        kwargs["max_results"] = max_results

        # 执行搜索
        try:
            logger.info(f"使用 {engine} 搜索: {query}")
            result = self.source_manager.search(query, engine=engine, **kwargs)

            # 转换格式
            format_type = output_format or self.default_output_format
            return self.format_manager.convert(result, format_type, **kwargs)

        except Exception as e:
            logger.error(f"搜索出错: {e}")
            raise

    def extract_web_content(
        self,
        url: str,
        use_javascript: bool = True,
        output_format: Optional[str] = None,
        **kwargs,
    ) -> Any:
        """
        提取网页内容

        Args:
            url: 网页URL
            use_javascript: 是否渲染JavaScript
            output_format: 输出格式
            **kwargs: 其他参数

        Returns:
            网页内容
        """
        try:
            logger.info(f"提取网页内容: {url}")
            result = self.source_manager.extract_web_content(url, use_javascript=use_javascript, **kwargs)

            # 转换格式
            format_type = output_format or self.default_output_format
            return self.format_manager.convert(result, format_type, **kwargs)

        except Exception as e:
            logger.error(f"提取网页内容出错: {e}")
            raise

    def process_pdf(self, pdf_path: str, output_dir: str = "pdf_output") -> Any:
        """
        处理PDF文件

        Args:
            pdf_path: PDF文件路径
            output_dir: 输出目录

        Returns:
            PDF内容
        """
        try:
            logger.info(f"处理PDF文件: {pdf_path}")

            # 直接使用docling处理PDF，而不是通过source_manager
            # 检查docling是否可用
            try:
                import docling  # noqa: F401
            except ImportError:
                logger.error("docling库未安装，无法处理PDF文件")
                raise ImportError("docling库未安装，无法处理PDF文件")

            # 处理PDF文件
            return self._extract_pdf_content(pdf_path, output_dir)
        except Exception as e:
            logger.error(f"处理PDF文件出错: {e}")
            raise

    def _extract_pdf_content(self, pdf_path: str, output_dir: str) -> dict[str, Any]:
        """
        使用docling提取PDF内容

        Args:
            pdf_path: PDF文件路径
            output_dir: 输出目录

        Returns:
            提取结果字典
        """
        from docling.datamodel.base_models import InputFormat
        from docling.datamodel.pipeline_options import PdfPipelineOptions
        from docling.document_converter import DocumentConverter, PdfFormatOption
        from docling_core.types.doc import ImageRefMode

        try:
            # 创建输出目录
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)

            doc_filename = Path(pdf_path).parts[-1]
            if doc_filename.endswith(".pdf") or doc_filename.endswith(".PDF"):
                doc_filename = doc_filename[:-4]
            markdown_file = output_path / (doc_filename + ".md")
            image_info_file = output_path / (doc_filename + "_images.json")
            if markdown_file.exists() and image_info_file.exists():
                logger.info(f"PDF文件已转换为Markdown: {markdown_file}")
                return {
                    "image_info_file": str(image_info_file),
                    "markdown_file": str(markdown_file),
                }

            # 配置处理选项
            pipeline_options = PdfPipelineOptions()
            pipeline_options.images_scale = self.image_resolution_scale
            pipeline_options.generate_page_images = True
            pipeline_options.generate_picture_images = True

            # 初始化文档转换器
            doc_converter = DocumentConverter(
                format_options={InputFormat.PDF: PdfFormatOption(pipeline_options=pipeline_options)},
            )

            start_time = time.time()

            logger.info("开始PDF转换")
            conv_res = doc_converter.convert(pdf_path)
            logger.info("PDF转换完成")

            # 保存图片和它们的标题
            image_info_file = self._save_pdf_images(conv_res.document, doc_filename, output_path)

            # 导出为Markdown
            markdown = conv_res.document.export_to_markdown(image_mode=ImageRefMode.REFERENCED)
            with open(markdown_file, "w", encoding="utf-8") as f:
                f.write(markdown)

            end_time = time.time() - start_time
            logger.info(f"文档转换和图片导出完成，耗时 {end_time:.2f} 秒")

            # 返回提取结果
            return {
                "image_info_file": str(image_info_file),
                "markdown_file": str(markdown_file),
            }

        except Exception as e:
            logger.error(f"提取PDF内容出错: {e}")
            return {
                "error": str(e),
                "image_info_file": None,
                "markdown_file": None,
            }

    def _save_pdf_images(self, document, doc_filename: str, output_dir: Path) -> str:
        """
        保存PDF文档中的图片

        Args:
            document: docling文档对象
            doc_filename: 文档文件名
            output_dir: 输出目录

        Returns:
            图片信息文件路径
        """
        from docling_core.types.doc import PictureItem, TableItem

        # 创建图片目录
        image_dir = output_dir / f"{doc_filename}_artifacts"
        image_dir.mkdir(parents=True, exist_ok=True)

        img_count = 0
        image_info = []

        # 提取并保存图片
        for item, level in document.iterate_items(with_groups=False):
            if isinstance(item, (PictureItem, TableItem)):
                if item.image is None:
                    item.image = item.get_image(document)
                    image = item.image
                else:
                    image = item.image.pil_image

                page_no = item.prov[0].page_no
                prefix = "image" if isinstance(item, PictureItem) else "table"
                loc_path = image_dir / f"page_{page_no}_{prefix}_{img_count}.png"

                image.save(loc_path)
                item.image.uri = loc_path

                caption = item.caption_text(document)
                image_info.append({"path": str(loc_path), "caption": caption})

                img_count += 1

        # 保存图片信息到JSON文件
        image_info_file = output_dir / f"{doc_filename}_images.json"
        with open(image_info_file, "w", encoding="utf-8") as f:
            json.dump(image_info, f, indent=4, ensure_ascii=False)

        logger.info(f"保存了 {img_count} 张图片信息到 {image_info_file}")
        return str(image_info_file)

    def _pdf_result_to_unified_content(self, extraction_result: dict[str, Any], pdf_path: str) -> UnifiedContent:
        """
        将PDF提取结果转换为统一内容对象

        Args:
            extraction_result: PDF提取结果
            pdf_path: PDF文件路径

        Returns:
            统一内容对象
        """
        from .adapters import BaseAdapter

        # 检查是否有错误
        if "error" in extraction_result:
            # 创建错误内容对象
            return UnifiedContent(
                metadata=self._create_metadata("pdf", pdf_path, extraction_error=extraction_result["error"]),
                title=f"PDF处理失败: {pdf_path}",
                error=extraction_result["error"],
                sections=[],
            )

        # 读取生成的Markdown文件
        markdown_file = extraction_result.get("markdown_file")
        if not markdown_file or not os.path.exists(markdown_file):
            error_msg = f"Markdown文件不存在: {markdown_file}"
            return UnifiedContent(
                metadata=self._create_metadata("pdf", pdf_path, extraction_error=error_msg),
                title=f"PDF处理失败: {pdf_path}",
                error=error_msg,
                sections=[],
            )

        with open(markdown_file, encoding="utf-8") as f:
            content = f.read()

        # 提取文件名作为标题
        filename = os.path.basename(pdf_path)
        title = os.path.splitext(filename)[0]

        # 创建临时适配器以使用其方法
        temp_adapter = BaseAdapter()

        # 分割章节
        sections = temp_adapter._split_into_sections(content)

        # 处理图片
        for img_info in extraction_result.get("images", []):
            img_path = img_info.get("path", "")
            img_caption = img_info.get("caption", "")

            if img_path:
                # 确定插入到哪个章节
                target_section = sections[0] if sections else None

                # 如果有章节，尝试根据图片路径猜测它属于哪个章节
                if len(sections) > 1:
                    img_filename = os.path.basename(img_path)
                    for section in sections:
                        if img_filename in section.text:
                            target_section = section
                            break

                # 如果找到目标章节，添加图片
                if target_section:
                    from .models import ContentImage

                    content_image = ContentImage(
                        url=img_path,
                        caption=img_caption,
                        local_path=img_path,
                    )
                    target_section.images.append(content_image)

        # 提取摘要
        summary = temp_adapter._extract_summary(content)

        # 创建统一内容对象
        return UnifiedContent(
            metadata=self._create_metadata("pdf", pdf_path, image_count=len(extraction_result.get("images", []))),
            title=title,
            summary=summary,
            sections=sections,
            raw_content=None,
            html_content=None,
            markdown_content=content,
            related_sources=[pdf_path],
        )

    def _create_metadata(self, source_type: str, source_id: str, **kwargs):
        """
        创建元数据

        Args:
            source_type: 信息源类型
            source_id: 信息源ID
            **kwargs: 其他元数据

        Returns:
            元数据对象
        """
        from datetime import datetime

        from .models import ContentMetadata

        tags = kwargs.pop("tags", [])
        processing_stats = {"processor": "InfoCollector", "processing_time": time.time(), **kwargs}

        return ContentMetadata(
            source_type=source_type,
            source_id=source_id,
            timestamp=datetime.now(),
            processing_stats=processing_stats,
            tags=tags,
        )

    def process_text(
        self,
        text: str,
        title: Optional[str] = None,
        output_format: Optional[str] = None,
        **kwargs,
    ) -> Any:
        """
        处理文本内容

        Args:
            text: 文本内容
            title: 文本标题
            output_format: 输出格式
            **kwargs: 其他参数

        Returns:
            处理后的内容
        """
        try:
            logger.info(f"处理文本内容: {title or '未命名文本'}")
            result = self.source_manager.process_text(text, title=title, **kwargs)

            # 转换格式
            format_type = output_format or self.default_output_format
            return self.format_manager.convert(result, format_type, **kwargs)

        except Exception as e:
            logger.error(f"处理文本内容出错: {e}")
            raise

    def get_raw_content(self, source: Any, adapter_name: str, **kwargs) -> UnifiedContent:
        """
        获取原始内容对象

        Args:
            source: 信息源
            adapter_name: 适配器名称
            **kwargs: 适配器特定参数

        Returns:
            统一内容对象
        """
        return self.source_manager.get_info(source, adapter_name, **kwargs)

    def convert_content(self, content: UnifiedContent, output_format: Optional[str] = None, **kwargs) -> Any:
        """
        转换内容格式

        Args:
            content: 统一内容对象
            output_format: 输出格式
            **kwargs: 格式特定参数

        Returns:
            转换后的内容
        """
        format_type = output_format or self.default_output_format
        return self.format_manager.convert(content, format_type, **kwargs)

    def clear_cache(self, pattern: Optional[str] = None):
        """
        清除缓存

        Args:
            pattern: 缓存键模式，如果为None则清除所有缓存
        """
        self.source_manager.clear_cache(pattern)

    def get_available_adapters(self) -> list[str]:
        """
        获取可用的适配器列表

        Returns:
            适配器名称列表
        """
        return self.source_manager.list_adapters()

    def get_available_formats(self) -> list[str]:
        """
        获取可用的输出格式列表

        Returns:
            格式名称列表
        """
        return list(self.format_manager.formatters.keys())
