"""
适配器模块

为不同信息源提供统一的接口，处理各种类型的内容源
"""

import hashlib
import os
import re
import time
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Union

from loguru import logger

from .models import ContentImage, ContentMetadata, ContentSection, UnifiedContent


class BaseAdapter(ABC):
    """适配器基类，定义通用接口"""

    def __init__(self):
        """初始化适配器"""
        self.name = self.__class__.__name__

    @abstractmethod
    def process(self, source: Any, **kwargs) -> Union[UnifiedContent, list[UnifiedContent]]:
        """
        处理信息源

        Args:
            source: 信息源对象
            **kwargs: 其他参数

        Returns:
            处理后的统一内容对象或列表
        """
        pass

    def _create_metadata(self, source_type: str, source_id: str, **kwargs) -> ContentMetadata:
        """
        创建元数据

        Args:
            source_type: 信息源类型
            source_id: 信息源ID
            **kwargs: 其他元数据

        Returns:
            元数据对象
        """
        tags = kwargs.pop("tags", [])
        processing_stats = {"processor": self.name, "processing_time": time.time(), **kwargs}

        return ContentMetadata(
            source_type=source_type,
            source_id=source_id,
            timestamp=datetime.now(),
            processing_stats=processing_stats,
            tags=tags,
        )

    def _extract_title(self, text: str) -> str:
        """
        从文本中提取标题

        Args:
            text: 文本内容

        Returns:
            提取的标题
        """
        # 尝试从 Markdown 或 HTML 中提取标题
        lines = text.split("\n")
        for line in lines[:10]:  # 只检查前10行
            # 检查Markdown标题
            if re.match(r"^#\s+(.+)$", line):
                return re.match(r"^#\s+(.+)$", line).group(1)

            # 检查HTML标题
            if re.search(r"<h1[^>]*>(.+?)</h1>", line, re.IGNORECASE):
                return re.search(r"<h1[^>]*>(.+?)</h1>", line, re.IGNORECASE).group(1)

        # 如果没有找到标题，使用第一行非空文本
        for line in lines:
            line = line.strip()
            if line and not line.startswith("#") and not line.startswith("<"):
                # 截取合适长度作为标题
                return line[:100] + ("..." if len(line) > 100 else "")

        return "未知标题"

    def _extract_summary(self, text: str, max_length: int = 200) -> str:
        """
        从文本中提取摘要

        Args:
            text: 文本内容
            max_length: 最大摘要长度

        Returns:
            提取的摘要
        """
        # 去除标题行
        lines = text.split("\n")
        content_lines = []
        for line in lines:
            line = line.strip()
            if line and not re.match(r"^#+\s+", line) and not re.match(r"<h\d[^>]*>", line):
                content_lines.append(line)

        if not content_lines:
            return ""

        # 使用前几行作为摘要
        summary_text = " ".join(content_lines[:3])

        # 截断到最大长度
        if len(summary_text) > max_length:
            summary_text = summary_text[:max_length].rstrip() + "..."

        return summary_text

    def _split_into_sections(self, text: str) -> list[ContentSection]:
        """
        将文本分割为章节

        Args:
            text: 文本内容

        Returns:
            章节列表
        """
        # 使用正则表达式查找所有标题
        header_pattern = re.compile(r"^(#{1,6})\s+(.+)$", re.MULTILINE)
        headers = list(header_pattern.finditer(text))

        sections = []

        # 如果没有标题，将整个文本作为一个章节
        if not headers:
            section = ContentSection(
                id="section_1",
                title=None,
                text=text.strip(),
                level=0,
            )
            sections.append(section)
            return sections

        # 处理每个标题和它的内容
        for i, header in enumerate(headers):
            header_level = len(header.group(1))
            header_text = header.group(2)
            header_start = header.start()

            # 计算章节内容的结束位置
            if i < len(headers) - 1:
                section_end = headers[i + 1].start()
            else:
                section_end = len(text)

            # 提取章节内容
            section_content = text[header_start:section_end]

            # 去除标题行
            section_content = section_content[len(header.group(0)) :].strip()

            # 创建章节对象
            section = ContentSection(
                id=f"section_{i + 1}",
                title=header_text,
                text=section_content,
                level=header_level - 1,  # 转换为0-based级别
            )
            sections.append(section)

        return sections


class SearchAdapter(BaseAdapter):
    """搜索适配器基类"""

    def __init__(self, engine: str = "unknown"):
        """
        初始化搜索适配器

        Args:
            engine: 搜索引擎名称
        """
        super().__init__()
        self.engine = engine

    @abstractmethod
    def search(self, query: str, **kwargs) -> dict:
        """
        执行搜索

        Args:
            query: 搜索查询
            **kwargs: 其他搜索参数

        Returns:
            搜索结果
        """
        pass

    def process(self, source: str, **kwargs) -> UnifiedContent:
        """
        处理搜索查询

        Args:
            source: 搜索查询
            **kwargs: 其他参数

        Returns:
            统一内容对象
        """
        query = source
        max_results = kwargs.pop("max_results", 5)

        # 执行搜索
        search_results = self.search(query, max_results=max_results, **kwargs)

        # 提取搜索结果
        results = search_results.get("results", [])

        # 创建元数据
        metadata = self._create_metadata(
            source_type="search",
            source_id=f"search:{self.engine}:{query}",
            engine=self.engine,
            query=query,
            result_count=len(results),
        )

        # 创建搜索结果章节
        sections = []
        for i, result in enumerate(results):
            # 提取搜索结果字段
            title = result.get("title", "未知标题")
            url = result.get("href") or result.get("url") or result.get("link", "")
            snippet = result.get("body") or result.get("snippet") or result.get("content", "")

            # 创建章节内容
            section_text = f"{snippet}\n\n[查看完整内容]({url})"

            # 创建章节对象
            section = ContentSection(
                id=f"result_{i + 1}",
                title=title,
                text=section_text,
                level=1,  # 搜索结果作为一级标题
            )
            sections.append(section)

        # 创建统一内容对象
        content = UnifiedContent(
            metadata=metadata,
            title=f"搜索结果: {query}",
            summary=f"使用 {self.engine} 搜索 '{query}' 的结果，找到 {len(results)} 条匹配项。",
            sections=sections,
            raw_content=str(search_results),
        )

        return content


class DuckDuckGoAdapter(SearchAdapter):
    """DuckDuckGo搜索适配器"""

    def __init__(self):
        """初始化DuckDuckGo搜索适配器"""
        super().__init__(engine="duckduckgo")
        self.ddgs = self._init_ddgs()

    def _init_ddgs(self):
        """初始化DuckDuckGo搜索客户端"""
        try:
            from duckduckgo_search import DDGS

            return DDGS()
        except ImportError:
            logger.error("未找到 duckduckgo_search 库，请安装: pip install duckduckgo_search")
            return None

    def search(self, query: str, **kwargs) -> dict:
        """
        执行DuckDuckGo搜索

        Args:
            query: 搜索查询
            **kwargs: 其他搜索参数

        Returns:
            搜索结果
        """
        if not self.ddgs:
            return {"error": "DuckDuckGo搜索客户端未初始化", "results": []}

        try:
            # 提取搜索参数
            max_results = kwargs.get("max_results", 5)
            region = kwargs.get("region", "wt-wt")
            time_range = kwargs.get("time_range")

            # 执行搜索
            search_params = {
                "keywords": query,
                "region": region,
                "max_results": max_results,
            }

            # 添加时间范围参数（如果提供）
            if time_range:
                search_params["timelimit"] = time_range

            results = list(self.ddgs.text(**search_params))

            return {
                "results": results,
                "metadata": {
                    "query": query,
                    "engine": "duckduckgo",
                    "result_count": len(results),
                },
            }
        except Exception as e:
            logger.error(f"DuckDuckGo搜索出错: {e}")
            return {"error": str(e), "results": []}


class SearXNGAdapter(SearchAdapter):
    """SearXNG搜索适配器"""

    def __init__(self, instance: str = "https://searx.be"):
        """
        初始化SearXNG搜索适配器

        Args:
            instance: SearXNG实例URL
        """
        super().__init__(engine="searxng")
        self.instance = instance

    def search(self, query: str, **kwargs) -> dict:
        """
        执行SearXNG搜索

        Args:
            query: 搜索查询
            **kwargs: 其他搜索参数

        Returns:
            搜索结果
        """
        try:
            import asyncio

            import aiohttp
        except ImportError:
            logger.error("未找到 aiohttp 库，请安装: pip install aiohttp")
            return {"error": "缺少依赖: aiohttp", "results": []}

        async def _searxng_search():
            # 提取搜索参数
            max_results = kwargs.get("max_results", 5)
            categories = kwargs.get("categories", "general")
            time_range = kwargs.get("time_range")
            include_domains = kwargs.get("include_domains")

            # 准备搜索参数
            params = {
                "q": query,
                "format": "json",
                "categories": categories,
                "results": max_results,
            }

            # 添加时间范围
            if time_range:
                params["time_range"] = time_range

            # 添加域名过滤
            if include_domains:
                params["engines"] = ",".join(include_domains)

            try:
                async with aiohttp.ClientSession() as session:
                    try:
                        # 设置超时时间
                        timeout = aiohttp.ClientTimeout(total=10)
                        async with session.get(f"{self.instance}/search", params=params, timeout=timeout) as response:
                            if response.status != 200:
                                return {"error": f"SearXNG API返回错误状态码: {response.status}", "results": []}

                            data = await response.json()

                            # 处理结果
                            results = data.get("results", [])

                            # 应用过滤
                            exclude_domains = kwargs.get("exclude_domains")
                            if exclude_domains:
                                results = [
                                    r
                                    for r in results
                                    if not any(domain in r.get("url", "") for domain in exclude_domains)
                                ]

                            # 限制结果数量
                            results = results[:max_results]

                            return {
                                "results": results,
                                "metadata": {
                                    "query": query,
                                    "engine": "searxng",
                                    "instance": self.instance,
                                    "result_count": len(results),
                                },
                            }
                    except asyncio.TimeoutError:
                        return {"error": f"连接到SearXNG实例（{self.instance}）超时", "results": []}

            except Exception as e:
                logger.error(f"SearXNG搜索出错: {e}")
                return {"error": str(e), "results": []}

        # 运行异步搜索函数
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        return loop.run_until_complete(_searxng_search())


class WebContentAdapter(BaseAdapter):
    """网页内容适配器，用于提取网页内容"""

    def __init__(self, use_playwright: bool = True):
        """
        初始化网页内容适配器

        Args:
            use_playwright: 是否使用Playwright渲染JavaScript
        """
        super().__init__()
        self.use_playwright = use_playwright

    def extract_content(self, url: str, **kwargs) -> dict:
        """
        提取网页内容

        Args:
            url: 网页URL
            **kwargs: 其他参数

        Returns:
            提取的内容
        """
        try:
            # 检查是否安装依赖库
            try:
                import asyncio

                from bs4 import BeautifulSoup
                from markdownify import markdownify as md
            except ImportError as e:
                return {"error": f"缺少依赖: {e}", "success": False}

            # 提取参数
            wait_time = kwargs.get("wait_time", 3)
            wait_for_selector = kwargs.get("wait_for_selector")
            extract_images = kwargs.get("extract_images", True)

            # 定义异步提取函数
            async def _extract_single_url():
                html_content = ""
                page_title = ""

                # 使用Playwright渲染JavaScript
                if self.use_playwright:
                    try:
                        from playwright.async_api import async_playwright
                    except ImportError:
                        return {"error": "缺少依赖: playwright", "success": False}

                    async with async_playwright() as p:
                        browser = await p.chromium.launch(headless=True)
                        page = await browser.new_page()

                        try:
                            await page.goto(url, wait_until="domcontentloaded", timeout=30000)

                            # 等待特定选择器
                            if wait_for_selector:
                                try:
                                    await page.wait_for_selector(wait_for_selector, timeout=wait_time * 1000)
                                except Exception:
                                    logger.warning(f"选择器 {wait_for_selector} 未在页面上找到")
                            else:
                                # 或等待固定时间
                                await asyncio.sleep(wait_time)

                            # 获取页面标题
                            page_title = await page.title()

                            # 获取HTML内容
                            html_content = await page.content()
                        finally:
                            await browser.close()

                # 使用aiohttp直接获取内容（不渲染JavaScript）
                else:
                    import aiohttp

                    async with aiohttp.ClientSession() as session:
                        async with session.get(url) as response:
                            if response.status != 200:
                                return {
                                    "url": url,
                                    "success": False,
                                    "error": f"HTTP错误: {response.status}",
                                }

                            html_content = await response.text()

                            # 解析标题
                            soup = BeautifulSoup(html_content, "html.parser")
                            title_tag = soup.find("title")
                            page_title = title_tag.get_text() if title_tag else ""

                # 处理HTML内容
                if html_content:
                    soup = BeautifulSoup(html_content, "html.parser")

                    # 提取主要内容（移除不必要的元素）
                    for tag in soup(["script", "style", "meta", "link", "noscript", "iframe"]):
                        tag.decompose()

                    # 提取图片（如果需要）
                    images = []
                    if extract_images:
                        for img in soup.find_all("img"):
                            src = img.get("src", "")
                            alt = img.get("alt", "")

                            if src:
                                # 过滤非内容图片
                                # 跳过数学公式
                                if "wikimedia.org/api/rest_v1/media/math/" in src:
                                    continue

                                # 跳过常见UI元素、图标、徽标
                                if any(
                                    [
                                        # 静态UI元素
                                        "/static/images/" in src,
                                        # 小图标 (通常小于25px)
                                        "15px-" in src
                                        or "16px-" in src
                                        or "17px-" in src
                                        or "18px-" in src
                                        or "19px-" in src
                                        or "20px-" in src
                                        or "21px-" in src
                                        or "22px-" in src
                                        or "23px-" in src
                                        or "24px-" in src
                                        or "25px-" in src,
                                        # 常见图标和徽标文件
                                        "logo" in src.lower(),
                                        "icon" in src.lower(),
                                        "symbol" in src.lower(),
                                        "button" in src.lower(),
                                        # 特定Wikipedia图标
                                        "Commons-logo" in src,
                                        "Wikiquote-logo" in src,
                                        "Wikidata-logo" in src,
                                        "OOjs_UI_icon" in src,
                                    ],
                                ):
                                    continue

                                # 处理相对URL
                                if not src.startswith(("http://", "https://", "data:")):
                                    from urllib.parse import urlparse

                                    parsed_url = urlparse(url)
                                    base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"

                                    # 处理协议相对URL（以//开头）
                                    if src.startswith("//"):
                                        src = f"{parsed_url.scheme}:{src}"
                                    elif src.startswith("/"):
                                        # 绝对路径（相对于域名根目录）
                                        src = f"{base_url}{src}"
                                    else:
                                        # 相对路径（相对于当前URL路径）
                                        # 获取当前URL的目录部分
                                        url_path = parsed_url.path
                                        if url_path and not url_path.endswith("/"):
                                            url_path = url_path.rsplit("/", 1)[0] + "/"
                                        elif not url_path:
                                            url_path = "/"

                                        # 处理 "../" 类型的相对路径
                                        if src.startswith("../"):
                                            path_parts = url_path.rstrip("/").split("/")
                                            src_parts = src.split("/")

                                            # 计算向上访问的层数
                                            up_count = 0
                                            for part in src_parts:
                                                if part == "..":
                                                    up_count += 1
                                                else:
                                                    break

                                            # 移除相应数量的路径段
                                            if up_count > 0:
                                                path_parts = path_parts[:-up_count]
                                                if not path_parts:
                                                    path_parts = [""]
                                                new_base_path = "/".join(path_parts)
                                                if not new_base_path.endswith("/"):
                                                    new_base_path += "/"
                                                src = f"{base_url}{new_base_path}{'/'.join(src_parts[up_count:])}"
                                            else:
                                                src = f"{base_url}{url_path}{src}"
                                        else:
                                            # 普通相对路径
                                            src = f"{base_url}{url_path}{src}"

                                images.append(
                                    {
                                        "url": src,
                                        "alt": alt,
                                    },
                                )

                    # 转换为Markdown
                    markdown_content = md(str(soup))

                    return {
                        "url": url,
                        "title": page_title,
                        "success": True,
                        "content": markdown_content,
                        "raw_html": html_content,
                        "images": images,
                    }
                else:
                    return {
                        "url": url,
                        "success": False,
                        "error": "无法获取页面内容",
                    }

            # 运行异步提取函数
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            result = loop.run_until_complete(_extract_single_url())
            return result

        except Exception as e:
            logger.error(f"提取URL内容出错: {e}")
            return {
                "url": url,
                "success": False,
                "error": str(e),
            }

    def process(self, source: str, **kwargs) -> UnifiedContent:
        """
        处理网页URL

        Args:
            source: 网页URL
            **kwargs: 其他参数

        Returns:
            统一内容对象
        """
        url = source

        # 提取网页内容
        extraction_result = self.extract_content(url, **kwargs)

        # 检查是否成功提取
        if not extraction_result.get("success", False):
            # 创建错误元数据
            metadata = self._create_metadata(
                source_type="web",
                source_id=url,
                extraction_error=extraction_result.get("error", "未知错误"),
            )

            # 创建错误内容对象
            return UnifiedContent(
                metadata=metadata,
                title=f"内容提取失败: {url}",
                error=extraction_result.get("error", "未知错误"),
                sections=[],
            )

        # 提取内容字段
        title = extraction_result.get("title", "未知标题")
        content = extraction_result.get("content", "")
        raw_html = extraction_result.get("raw_html", "")
        images = extraction_result.get("images", [])

        # 创建元数据
        metadata = self._create_metadata(
            source_type="web",
            source_id=url,
            title=title,
            image_count=len(images),
        )

        # 创建章节
        sections = self._split_into_sections(content)

        # 处理图片
        content_images = []
        for img_data in images:
            content_image = ContentImage(
                url=img_data.get("url", ""),
                alt_text=img_data.get("alt", ""),
            )
            content_images.append(content_image)

        # 将图片添加到章节中
        if sections and content_images:
            sections[0].images.extend(content_images)

        # 提取摘要
        summary = self._extract_summary(content)

        # 创建统一内容对象
        unified_content = UnifiedContent(
            metadata=metadata,
            title=title,
            summary=summary,
            sections=sections,
            raw_content=raw_html,
            html_content=raw_html,
            markdown_content=content,
            related_sources=[url],
        )

        return unified_content


class PDFAdapter(BaseAdapter):
    """PDF文件适配器"""

    def __init__(self):
        """初始化PDF文件适配器"""
        super().__init__()
        self.pdf_toolkit = self._init_pdf_toolkit()

    def _init_pdf_toolkit(self):
        """初始化PDF工具包"""
        try:
            # 测试能否导入依赖库
            import docling  # noqa

            from agents.video_agent.tools.pdf_toolkit import PDFToolkit

            return PDFToolkit()
        except ImportError as e:
            logger.error(f"初始化PDF工具包出错: {e}")
            return None

    def process(self, source: str, **kwargs) -> UnifiedContent:
        """
        处理PDF文件

        Args:
            source: PDF文件路径或URL
            **kwargs: 其他参数

        Returns:
            统一内容对象
        """
        if not self.pdf_toolkit:
            # 创建错误元数据
            metadata = self._create_metadata(source_type="pdf", source_id=source, extraction_error="PDF工具包未初始化")

            # 创建错误内容对象
            return UnifiedContent(
                metadata=metadata,
                title=f"PDF处理失败: {source}",
                error="PDF工具包未初始化，请确保安装了必要的依赖库",
                sections=[],
            )

        # 提取参数
        output_dir = kwargs.get("output_dir", "pdf_output")

        try:
            # 提取PDF内容
            extraction_result = self.pdf_toolkit.extract_pdf(source, output_dir)

            # 检查是否有错误
            if "error" in extraction_result:
                # 创建错误元数据
                metadata = self._create_metadata(
                    source_type="pdf",
                    source_id=source,
                    extraction_error=extraction_result["error"],
                )

                # 创建错误内容对象
                return UnifiedContent(
                    metadata=metadata,
                    title=f"PDF处理失败: {source}",
                    error=extraction_result["error"],
                    sections=[],
                )

            # 读取生成的Markdown文件
            markdown_file = extraction_result.get("markdown_file")
            if not markdown_file or not os.path.exists(markdown_file):
                raise FileNotFoundError(f"Markdown文件不存在: {markdown_file}")

            with open(markdown_file, encoding="utf-8") as f:
                content = f.read()

            # 提取文件名作为标题
            filename = os.path.basename(source)
            title = os.path.splitext(filename)[0]

            # 创建元数据
            metadata = self._create_metadata(
                source_type="pdf",
                source_id=source,
                output_dir=output_dir,
                image_count=len(extraction_result.get("images", [])),
            )

            # 创建章节
            sections = self._split_into_sections(content)

            # 处理图片
            for img_info in extraction_result.get("images", []):
                img_path = img_info.get("path", "")
                img_caption = img_info.get("caption", "")

                if img_path:
                    # 确定插入到哪个章节
                    target_section = sections[0] if sections else None

                    # 如果有章节，尝试根据图片路径猜测它属于哪个章节
                    if len(sections) > 1:
                        img_filename = os.path.basename(img_path)
                        for section in sections:
                            if img_filename in section.text:
                                target_section = section
                                break

                    # 如果找到目标章节，添加图片
                    if target_section:
                        content_image = ContentImage(
                            url=img_path,
                            caption=img_caption,
                            local_path=img_path,
                        )
                        target_section.images.append(content_image)

            # 提取摘要
            summary = self._extract_summary(content)

            # 创建统一内容对象
            unified_content = UnifiedContent(
                metadata=metadata,
                title=title,
                summary=summary,
                sections=sections,
                raw_content=None,
                html_content=None,
                markdown_content=content,
                related_sources=[source],
            )

            return unified_content

        except Exception as e:
            logger.error(f"处理PDF文件出错: {e}")

            # 创建错误元数据
            metadata = self._create_metadata(source_type="pdf", source_id=source, extraction_error=str(e))

            # 创建错误内容对象
            return UnifiedContent(
                metadata=metadata,
                title=f"PDF处理失败: {source}",
                error=str(e),
                sections=[],
            )


class TextAdapter(BaseAdapter):
    """纯文本适配器"""

    def process(self, source: str, **kwargs) -> UnifiedContent:
        """
        处理纯文本

        Args:
            source: 文本内容
            **kwargs: 其他参数

        Returns:
            统一内容对象
        """
        text = source

        # 提取参数
        title = kwargs.get("title")
        source_id = kwargs.get("source_id", hashlib.md5(text[:100].encode()).hexdigest())

        # 如果没提供标题，尝试从文本中提取
        if not title:
            title = self._extract_title(text)

        # 创建元数据
        metadata = self._create_metadata(
            source_type="text",
            source_id=source_id,
            text_length=len(text),
        )

        # 创建章节
        sections = self._split_into_sections(text)

        # 提取摘要
        summary = self._extract_summary(text)

        # 创建统一内容对象
        unified_content = UnifiedContent(
            metadata=metadata,
            title=title,
            summary=summary,
            sections=sections,
            raw_content=text,
            markdown_content=text,
        )

        return unified_content
