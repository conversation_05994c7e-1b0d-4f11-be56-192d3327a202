"""
统一信息收集系统的数据模型

定义了处理不同信息源的统一数据结构
"""

import os
import uuid
from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Optional


@dataclass
class ContentMetadata:
    """内容元数据"""

    source_type: str  # "web", "pdf", "text", "api", "search", etc.
    source_id: str  # URL, file path, or custom ID
    timestamp: datetime = field(default_factory=datetime.now)
    processing_stats: dict[str, Any] = field(default_factory=dict)  # 处理时间、Token数等统计信息
    tags: list[str] = field(default_factory=list)

    def __post_init__(self):
        # 确保source_id是有效的
        if not self.source_id:
            self.source_id = f"unknown_{uuid.uuid4().hex[:8]}"


@dataclass
class ContentImage:
    """内容中的图片"""

    url: str
    alt_text: Optional[str] = None
    caption: Optional[str] = None
    local_path: Optional[str] = None
    width: Optional[int] = None
    height: Optional[int] = None
    format: Optional[str] = None  # "jpg", "png", etc.

    def __post_init__(self):
        # 如果没有本地路径但有URL，可以设置默认的本地路径
        if not self.local_path and self.url:
            filename = os.path.basename(self.url.split("?")[0])  # 移除URL参数
            if not filename:
                filename = f"image_{uuid.uuid4().hex[:8]}.{self.format or 'jpg'}"
            self.local_path = os.path.join("image_cache", filename)


@dataclass
class ContentTable:
    """内容中的表格"""

    data: list[list[str]]  # 表格数据（行×列）
    headers: Optional[list[str]] = None  # 表头
    caption: Optional[str] = None  # 表格标题

    def to_markdown(self) -> str:
        """将表格转换为Markdown格式"""
        result = []

        # 添加标题
        if self.caption:
            result.append(f"**{self.caption}**\n")

        # 添加表头
        if self.headers:
            result.append("| " + " | ".join(self.headers) + " |")
            result.append("| " + " | ".join(["---"] * len(self.headers)) + " |")
        elif self.data:
            # 如果没有指定表头但有数据，使用空表头
            result.append("| " + " | ".join([""] * len(self.data[0])) + " |")
            result.append("| " + " | ".join(["---"] * len(self.data[0])) + " |")

        # 添加数据行
        for row in self.data:
            result.append("| " + " | ".join(str(cell) for cell in row) + " |")

        return "\n".join(result)


@dataclass
class ContentSection:
    """内容的章节或片段"""

    id: str  # 章节ID
    title: Optional[str] = None  # 章节标题
    text: str = ""  # 章节文本内容
    level: int = 0  # 章节级别（0表示最高级）
    images: list[ContentImage] = field(default_factory=list)  # 章节包含的图片
    tables: list[ContentTable] = field(default_factory=list)  # 章节包含的表格
    parent_id: Optional[str] = None  # 父章节ID

    def __post_init__(self):
        # 确保id是有效的
        if not self.id:
            self.id = f"section_{uuid.uuid4().hex[:8]}"

    def to_markdown(self) -> str:
        """将章节转换为Markdown格式"""
        result = []

        # 添加标题
        if self.title:
            prefix = "#" * (self.level + 1)
            result.append(f"{prefix} {self.title}")
            result.append("")

        # 添加文本内容
        if self.text:
            result.append(self.text)
            result.append("")

        # 添加图片
        for img in self.images:
            alt = img.alt_text or img.caption or ""
            result.append(f"![{alt}]({img.url})")
            if img.caption:
                result.append(f"*{img.caption}*")
            result.append("")

        # 添加表格
        for table in self.tables:
            result.append(table.to_markdown())
            result.append("")

        return "\n".join(result).strip()


@dataclass
class UnifiedContent:
    """统一内容模型"""

    metadata: ContentMetadata  # 元数据
    title: str = ""  # 内容标题
    summary: Optional[str] = None  # 内容摘要
    sections: list[ContentSection] = field(default_factory=list)  # 内容章节
    raw_content: Optional[str] = None  # 原始内容
    html_content: Optional[str] = None  # HTML格式内容
    markdown_content: Optional[str] = None  # Markdown格式内容
    related_sources: list[str] = field(default_factory=list)  # 相关信息源
    error: Optional[str] = None  # 错误信息（如果有）

    def to_markdown(self) -> str:
        """将整个内容转换为Markdown格式"""
        # 如果已有Markdown内容，直接返回
        if self.markdown_content:
            return self.markdown_content

        result = []

        # 添加标题
        if self.title:
            result.append(f"# {self.title}")
            result.append("")

        # 添加摘要
        if self.summary:
            result.append(f"*{self.summary}*")
            result.append("")
            result.append("---")
            result.append("")

        # 添加章节内容
        for section in self.sections:
            result.append(section.to_markdown())
            result.append("")

        # 添加来源信息
        if self.related_sources:
            result.append("## 相关来源")
            for source in self.related_sources:
                result.append(f"- {source}")

        # 如果有错误信息，添加错误信息
        if self.error:
            result.append("")
            result.append(f"**错误**: {self.error}")

        markdown = "\n".join(result).strip()
        self.markdown_content = markdown  # 缓存结果
        return markdown

    def to_dict(self) -> dict[str, Any]:
        """将内容转换为字典形式"""
        return {
            "metadata": {
                "source_type": self.metadata.source_type,
                "source_id": self.metadata.source_id,
                "timestamp": self.metadata.timestamp.isoformat(),
                "processing_stats": self.metadata.processing_stats,
                "tags": self.metadata.tags,
            },
            "title": self.title,
            "summary": self.summary,
            "sections": [
                {
                    "id": section.id,
                    "title": section.title,
                    "text": section.text,
                    "level": section.level,
                    "images": [
                        {
                            "url": img.url,
                            "alt_text": img.alt_text,
                            "caption": img.caption,
                            "local_path": img.local_path,
                        }
                        for img in section.images
                    ],
                    "tables": [
                        {
                            "headers": table.headers,
                            "data": table.data,
                            "caption": table.caption,
                        }
                        for table in section.tables
                    ],
                    "parent_id": section.parent_id,
                }
                for section in self.sections
            ],
            "raw_content": self.raw_content,
            "html_content": self.html_content,
            "markdown_content": self.to_markdown(),
            "related_sources": self.related_sources,
            "error": self.error,
        }
