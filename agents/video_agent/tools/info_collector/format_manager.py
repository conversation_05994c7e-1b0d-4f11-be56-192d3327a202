"""
格式管理器

处理内容的格式转换，提供不同输出格式的支持
"""

import json
import re
from typing import Any

from loguru import logger

from .models import UnifiedContent


class FormatManager:
    """
    格式管理器

    将统一内容对象转换为不同的输出格式
    """

    def __init__(self):
        """初始化格式管理器"""
        # 注册格式转换器
        self.formatters = {
            "markdown": self.to_markdown,
            "json": self.to_json,
            "text": self.to_text,
            "html": self.to_html,
            "dict": self.to_dict,
        }

    def convert(self, content: UnifiedContent, output_format: str, **kwargs) -> Any:
        """
        转换内容格式

        Args:
            content: 统一内容对象
            output_format: 输出格式
            **kwargs: 格式特定参数

        Returns:
            转换后的内容

        Raises:
            ValueError: 如果输出格式不支持
        """
        if output_format not in self.formatters:
            supported = ", ".join(self.formatters.keys())
            raise ValueError(f"不支持的输出格式: {output_format}，支持的格式: {supported}")

        # 使用对应的格式转换器
        return self.formatters[output_format](content, **kwargs)

    def to_markdown(
        self,
        content: UnifiedContent,
        include_metadata: bool = False,
        include_images: bool = True,
        image_format: str = "markdown",
        **kwargs,
    ) -> str:
        """
        转换为Markdown格式

        Args:
            content: 统一内容对象
            include_metadata: 是否包含元数据
            include_images: 是否包含图片
            image_format: 图片格式（markdown或html）
            **kwargs: 其他参数

        Returns:
            Markdown格式的内容
        """
        lines = []

        # 标题
        if content.title:
            lines.append(f"# {content.title}\n")

        # 摘要
        if content.summary:
            lines.append(f"## 摘要\n\n{content.summary}\n")

        # 错误信息
        if content.error:
            lines.append(f"## 错误\n\n**{content.error}**\n")

        # 元数据（如果需要）
        if include_metadata and content.metadata:
            lines.append("## 元数据\n")

            # 处理元数据
            metadata = content.metadata.dict()
            metadata_lines = []

            for key, value in metadata.items():
                if value is not None:
                    # 处理复杂类型
                    if isinstance(value, dict):
                        value_str = json.dumps(value, ensure_ascii=False, indent=2)
                        metadata_lines.append(f"- **{key}**: \n```json\n{value_str}\n```")
                    elif isinstance(value, list):
                        if not value:
                            metadata_lines.append(f"- **{key}**: []")
                        else:
                            metadata_lines.append(f"- **{key}**: {', '.join(map(str, value))}")
                    else:
                        metadata_lines.append(f"- **{key}**: {value}")

            lines.append("\n".join(metadata_lines) + "\n")

        # 内容章节
        for section in content.sections:
            # 章节标题
            if section.title:
                # 计算标题级别
                level = min(section.level + 2, 6)  # 顶级章节为 ##
                heading = "#" * level
                lines.append(f"{heading} {section.title}\n")

            # 章节内容
            if section.text:
                lines.append(f"{section.text}\n")

            # 章节图片（如果需要）
            if include_images and section.images:
                for img in section.images:
                    if image_format == "markdown":
                        alt_text = img.alt_text or img.caption or "图片"
                        url = img.url or img.local_path or ""
                        if url:
                            lines.append(f"![{alt_text}]({url})")
                            if img.caption:
                                lines.append(f"*{img.caption}*\n")
                            lines.append("")
                    elif image_format == "html":
                        alt_text = img.alt_text or img.caption or "图片"
                        url = img.url or img.local_path or ""
                        caption = f' title="{img.caption}"' if img.caption else ""
                        if url:
                            lines.append(f'<img src="{url}" alt="{alt_text}"{caption} />')
                            if img.caption:
                                lines.append(f"<p><em>{img.caption}</em></p>\n")
                            lines.append("")

            # 章节表格
            if section.tables:
                for table in section.tables:
                    if table.markdown:
                        lines.append(f"{table.markdown}\n")
                    elif table.rows:
                        # 创建表格标题
                        if table.headers:
                            header_row = " | ".join(table.headers)
                            separator_row = " | ".join(["---"] * len(table.headers))
                            lines.append(f"| {header_row} |")
                            lines.append(f"| {separator_row} |")

                        # 创建表格内容
                        for row in table.rows:
                            row_str = " | ".join(map(str, row))
                            lines.append(f"| {row_str} |")

                        # 添加表格标题或脚注
                        if table.caption:
                            lines.append(f"*{table.caption}*\n")

                        lines.append("")

        # 相关来源
        if content.related_sources:
            lines.append("## 相关来源\n")
            for i, source in enumerate(content.related_sources):
                lines.append(f"{i + 1}. [{source}]({source})")
            lines.append("")

        # 将所有行组合成最终文本
        return "\n".join(lines)

    def to_text(self, content: UnifiedContent, include_metadata: bool = False, **kwargs) -> str:
        """
        转换为纯文本格式

        Args:
            content: 统一内容对象
            include_metadata: 是否包含元数据
            **kwargs: 其他参数

        Returns:
            纯文本格式的内容
        """
        # 首先转换为Markdown，然后移除Markdown标记
        markdown = self.to_markdown(
            content,
            include_metadata=include_metadata,
            include_images=False,  # 纯文本不包含图片
            **kwargs,
        )

        # 移除Markdown标记
        text = markdown

        # 移除图片标记
        text = re.sub(r"!\[.*?\]\(.*?\)", "", text)

        # 移除链接标记，但保留链接文本
        text = re.sub(r"\[(.*?)\]\(.*?\)", r"\1", text)

        # 移除标题标记
        text = re.sub(r"^#+\s+(.*)$", r"\1", text, flags=re.MULTILINE)

        # 移除加粗和斜体标记
        text = re.sub(r"\*\*(.*?)\*\*", r"\1", text)
        text = re.sub(r"\*(.*?)\*", r"\1", text)

        # 移除代码块
        text = re.sub(r"```.*?```", "", text, flags=re.DOTALL)

        # 移除多余的空行
        text = re.sub(r"\n{3,}", "\n\n", text)

        return text.strip()

    def to_html(
        self,
        content: UnifiedContent,
        include_metadata: bool = False,
        include_images: bool = True,
        **kwargs,
    ) -> str:
        """
        转换为HTML格式

        Args:
            content: 统一内容对象
            include_metadata: 是否包含元数据
            include_images: 是否包含图片
            **kwargs: 其他参数

        Returns:
            HTML格式的内容
        """
        # 首先转换为Markdown
        markdown = self.to_markdown(
            content,
            include_metadata=include_metadata,
            include_images=include_images,
            image_format="html",
            **kwargs,
        )

        # 将Markdown转换为HTML
        try:
            import markdown as md_converter

            html = md_converter.markdown(markdown, extensions=["tables", "fenced_code"])

            # 添加基本样式
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <title>{content.title or "内容查看器"}</title>
                <style>
                    body {{
                        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
                        line-height: 1.6;
                        padding: 1em;
                        max-width: 50em;
                        margin: 0 auto;
                        color: #333;
                    }}
                    h1, h2, h3, h4, h5, h6 {{
                        margin-top: 1.2em;
                        margin-bottom: 0.5em;
                        font-weight: 600;
                        color: #111;
                    }}
                    a {{ color: #0366d6; text-decoration: none; }}
                    a:hover {{ text-decoration: underline; }}
                    img {{ max-width: 100%; border-radius: 5px; }}
                    pre, code {{
                        background: #f6f8fa;
                        border-radius: 3px;
                        font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
                        font-size: 85%;
                        padding: 0.2em 0.4em;
                    }}
                    pre {{ padding: 1em; overflow: auto; }}
                    pre code {{ background: none; padding: 0; }}
                    blockquote {{
                        border-left: 4px solid #ddd;
                        padding-left: 1em;
                        color: #666;
                        margin-left: 0;
                    }}
                    table {{
                        border-collapse: collapse;
                        width: 100%;
                        margin-bottom: 1em;
                    }}
                    th, td {{
                        border: 1px solid #ddd;
                        padding: 8px;
                        text-align: left;
                    }}
                    th {{ background-color: #f6f8fa; }}
                    tr:nth-child(even) {{ background-color: #f9f9f9; }}
                </style>
            </head>
            <body>
                {html}
            </body>
            </html>
            """

            return html

        except ImportError:
            logger.warning("未找到markdown库，返回原始Markdown内容")
            return f"<pre>{markdown}</pre>"

    def to_dict(self, content: UnifiedContent, **kwargs) -> dict:
        """
        转换为字典格式

        Args:
            content: 统一内容对象
            **kwargs: 其他参数

        Returns:
            字典格式的内容
        """
        # 将UnifiedContent直接转换为字典
        return content.dict()

    def to_json(self, content: UnifiedContent, indent: int = 2, ensure_ascii: bool = False, **kwargs) -> str:
        """
        转换为JSON格式

        Args:
            content: 统一内容对象
            indent: 缩进空格数
            ensure_ascii: 是否确保ASCII编码
            **kwargs: 其他参数

        Returns:
            JSON格式的内容
        """
        # 先转换为字典
        content_dict = self.to_dict(content)

        # 将字典转换为JSON字符串
        return json.dumps(content_dict, indent=indent, ensure_ascii=ensure_ascii)


def clean_text(text: str) -> str:
    """
    清理文本

    Args:
        text: 需要清理的文本

    Returns:
        清理后的文本
    """
    if not text:
        return ""

    # 移除多余的空白字符
    text = re.sub(r"\s+", " ", text)

    # 移除多余的换行符
    text = re.sub(r"\n{3,}", "\n\n", text)

    return text.strip()
