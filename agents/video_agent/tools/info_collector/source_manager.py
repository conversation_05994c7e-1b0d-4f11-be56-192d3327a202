"""
信息源管理器

统一管理各种信息源，协调信息获取和处理
"""

from typing import Any, Optional

from loguru import logger

from .adapters import (
    BaseAdapter,
    DuckDuckGoAdapter,
    PDFAdapter,
    SearchAdapter,
    SearXNGAdapter,
    TextAdapter,
    WebContentAdapter,
)
from .cache import CacheSystem
from .models import UnifiedContent


class SourceManager:
    """
    信息源管理器

    统一管理不同类型的信息源，协调适配器和缓存系统
    """

    def __init__(self, use_cache: bool = True, cache_dir: Optional[str] = None):
        """
        初始化信息源管理器

        Args:
            use_cache: 是否使用缓存
            cache_dir: 缓存目录
        """
        # 注册适配器
        self.adapters: dict[str, BaseAdapter] = {}
        self._register_default_adapters()

        # 初始化缓存系统
        self.use_cache = use_cache
        self.cache = CacheSystem(cache_dir=cache_dir) if use_cache else None

    def _register_default_adapters(self):
        """注册默认适配器"""
        # 搜索引擎适配器
        self.register_adapter("duckduckgo", DuckDuckGoAdapter())
        self.register_adapter("searxng", SearXNGAdapter())

        # 内容处理适配器
        self.register_adapter("web", WebContentAdapter())
        self.register_adapter("pdf", PDFAdapter())
        self.register_adapter("text", TextAdapter())

        # 尝试导入并注册 Tavily 适配器 (如果存在)
        try:
            from .tavily_adapter import TavilyAdapter

            self.register_adapter("tavily", TavilyAdapter())
        except ImportError:
            logger.info("Tavily适配器未注册，如需使用请确保依赖已安装")

    def register_adapter(self, adapter_name: str, adapter: BaseAdapter):
        """
        注册适配器

        Args:
            adapter_name: 适配器名称
            adapter: 适配器实例
        """
        self.adapters[adapter_name] = adapter
        logger.debug(f"已注册适配器: {adapter_name}")

    def get_adapter(self, adapter_name: str) -> Optional[BaseAdapter]:
        """
        获取适配器

        Args:
            adapter_name: 适配器名称

        Returns:
            适配器实例，如果不存在则返回None
        """
        return self.adapters.get(adapter_name)

    def list_adapters(self) -> list[str]:
        """
        列出所有已注册的适配器

        Returns:
            适配器名称列表
        """
        return list(self.adapters.keys())

    def _generate_cache_key(self, adapter_name: str, source: Any, **kwargs) -> str:
        """
        生成缓存键

        Args:
            adapter_name: 适配器名称
            source: 信息源
            **kwargs: 其他参数

        Returns:
            缓存键
        """
        # 基础键
        key_parts = [f"adapter:{adapter_name}"]

        # 添加源信息
        if isinstance(source, str):
            key_parts.append(f"source:{source[:100]}")
        else:
            key_parts.append(f"source:{str(source)[:100]}")

        # 添加重要参数
        important_params = ["max_results", "time_range", "extract_images", "use_playwright"]
        for param in important_params:
            if param in kwargs:
                key_parts.append(f"{param}:{kwargs[param]}")

        # 生成最终键
        return ":".join(key_parts)

    def get_info(self, source: Any, adapter_name: str, use_cache: Optional[bool] = None, **kwargs) -> UnifiedContent:
        """
        获取信息

        Args:
            source: 信息源
            adapter_name: 适配器名称
            use_cache: 是否使用缓存（覆盖默认设置）
            **kwargs: 适配器特定参数

        Returns:
            统一内容对象

        Raises:
            ValueError: 如果适配器不存在
        """
        # 获取适配器
        adapter = self.get_adapter(adapter_name)
        if not adapter:
            raise ValueError(f"未找到适配器: {adapter_name}")

        # 确定是否使用缓存
        should_use_cache = self.use_cache if use_cache is None else use_cache

        # 如果使用缓存，尝试从缓存获取
        if should_use_cache and self.cache:
            cache_key = self._generate_cache_key(adapter_name, source, **kwargs)
            cached_content = self.cache.get(cache_key)

            if cached_content:
                logger.info(f"从缓存获取内容: {cache_key}")
                return cached_content
            else:
                logger.info(f"缓存未命中: {cache_key}")

        # 处理信息源
        try:
            logger.info(f"使用适配器 {adapter_name} 处理信息源")
            content = adapter.process(source, **kwargs)

            # 如果使用缓存，保存到缓存
            if should_use_cache and self.cache and content:
                cache_key = self._generate_cache_key(adapter_name, source, **kwargs)
                self.cache.set(cache_key, content)
                logger.debug(f"内容已缓存: {cache_key}")

            return content
        except Exception as e:
            logger.error(f"处理信息源出错: {e}")

            # 创建错误内容对象
            error_message = f"处理信息源出错: {str(e)}"

            # 尝试从适配器获取元数据方法创建元数据
            try:
                if hasattr(adapter, "_create_metadata"):
                    source_id = str(source)[:100] if isinstance(source, str) else str(source)
                    metadata = adapter._create_metadata(source_type=adapter_name, source_id=source_id, error=str(e))

                    return UnifiedContent(
                        metadata=metadata,
                        title=f"处理失败: {source_id}",
                        error=error_message,
                        sections=[],
                    )
            except Exception:
                pass

            # 如果元数据创建失败，引发原始异常
            raise

    def search(
        self,
        query: str,
        engine: str = "duckduckgo",
        use_cache: Optional[bool] = None,
        **kwargs,
    ) -> UnifiedContent:
        """
        搜索信息

        Args:
            query: 搜索查询
            engine: 搜索引擎名称
            use_cache: 是否使用缓存
            **kwargs: 搜索参数

        Returns:
            搜索结果

        Raises:
            ValueError: 如果搜索引擎适配器不存在
        """
        adapter = self.get_adapter(engine)
        if not adapter:
            raise ValueError(f"未找到搜索引擎适配器: {engine}")

        if not isinstance(adapter, SearchAdapter):
            raise ValueError(f"适配器 {engine} 不是搜索适配器")

        return self.get_info(query, engine, use_cache, **kwargs)

    def extract_web_content(
        self,
        url: str,
        use_javascript: bool = True,
        use_cache: Optional[bool] = None,
        **kwargs,
    ) -> UnifiedContent:
        """
        提取网页内容

        Args:
            url: 网页URL
            use_javascript: 是否渲染JavaScript
            use_cache: 是否使用缓存
            **kwargs: 其他参数

        Returns:
            网页内容
        """
        adapter = self.get_adapter("web")
        if not adapter:
            raise ValueError("未找到网页内容适配器")

        # 设置是否使用Playwright
        adapter.use_playwright = use_javascript

        return self.get_info(url, "web", use_cache, **kwargs)

    def process_pdf(self, pdf_path: str, use_cache: Optional[bool] = None, **kwargs) -> UnifiedContent:
        """
        处理PDF文件

        Args:
            pdf_path: PDF文件路径
            use_cache: 是否使用缓存
            **kwargs: 其他参数

        Returns:
            PDF内容
        """
        return self.get_info(pdf_path, "pdf", use_cache, **kwargs)

    def process_text(
        self,
        text: str,
        title: Optional[str] = None,
        use_cache: Optional[bool] = None,
        **kwargs,
    ) -> UnifiedContent:
        """
        处理文本内容

        Args:
            text: 文本内容
            title: 文本标题
            use_cache: 是否使用缓存
            **kwargs: 其他参数

        Returns:
            处理后的内容
        """
        if title:
            kwargs["title"] = title

        return self.get_info(text, "text", use_cache, **kwargs)

    def clear_cache(self, pattern: Optional[str] = None):
        """
        清除缓存

        Args:
            pattern: 缓存键模式，如果为None则清除所有缓存
        """
        if self.cache:
            if pattern:
                self.cache.delete_pattern(pattern)
                logger.info(f"已清除匹配模式 {pattern} 的缓存")
            else:
                self.cache.clear()
                logger.info("已清除所有缓存")
