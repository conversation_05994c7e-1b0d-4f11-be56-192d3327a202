"""
信息收集器示例

演示如何使用信息收集工具
"""

import argparse
import os
import sys
from typing import Optional

from loguru import logger

from .collector import InfoCollector


def setup_logger():
    """配置日志记录器"""
    logger.remove()  # 移除默认处理程序
    logger.add(
        sys.stderr,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO",
    )


def parse_arguments() -> tuple[argparse.Namespace, list]:
    """
    解析命令行参数

    Returns:
        解析后的参数命名空间和未识别的参数
    """
    parser = argparse.ArgumentParser(description="统一信息收集工具演示")

    # 通用参数
    parser.add_argument("--no-cache", action="store_true", help="禁用缓存")
    parser.add_argument("--cache-dir", help="缓存目录")
    parser.add_argument("--format", default="markdown", help="输出格式（markdown, json, text, html, dict）")
    parser.add_argument("--output", "-o", help="输出文件路径")

    # 子命令
    subparsers = parser.add_subparsers(dest="command", help="命令")

    # 搜索命令
    search_parser = subparsers.add_parser("search", help="搜索信息")
    search_parser.add_argument("query", help="搜索查询")
    search_parser.add_argument("--engine", default="duckduckgo", help="搜索引擎")
    search_parser.add_argument("--max-results", type=int, default=5, help="最大结果数")

    # 网页内容提取命令
    web_parser = subparsers.add_parser("web", help="提取网页内容")
    web_parser.add_argument("url", help="网页URL")
    web_parser.add_argument("--no-js", action="store_true", help="不使用JavaScript渲染")

    # PDF处理命令
    pdf_parser = subparsers.add_parser("pdf", help="处理PDF文件")
    pdf_parser.add_argument("path", help="PDF文件路径")

    # 文本处理命令
    text_parser = subparsers.add_parser("text", help="处理文本内容")
    text_parser.add_argument("text", help="文本内容或文本文件路径")
    text_parser.add_argument("--title", help="文本标题")
    text_parser.add_argument("--is-file", action="store_true", help="指定的是文件路径而不是直接文本")

    # 清除缓存命令
    cache_parser = subparsers.add_parser("clear-cache", help="清除缓存")
    cache_parser.add_argument("--pattern", help="缓存键模式")

    # 列出适配器命令
    subparsers.add_parser("list-adapters", help="列出可用的适配器")

    # 列出格式命令
    subparsers.add_parser("list-formats", help="列出可用的输出格式")

    # 解析已知参数，返回未识别的参数
    return parser.parse_known_args()


def write_output(content: str, output_path: Optional[str] = None):
    """
    写入输出内容

    Args:
        content: 输出内容
        output_path: 输出文件路径，如果为None则输出到控制台
    """
    if output_path:
        # 确保目录存在
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 写入文件
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(content)
        logger.info(f"内容已保存到: {output_path}")
    else:
        # 输出到控制台
        print(content)


def main():
    """主函数"""
    # 配置日志
    setup_logger()

    # 解析参数
    args, unknown_args = parse_arguments()

    # 如果没有指定命令，显示帮助
    if not args.command:
        print("请指定命令。使用 -h 查看帮助。")
        sys.exit(1)

    # 提取额外的关键字参数
    kwargs = {}
    for i in range(0, len(unknown_args), 2):
        if i + 1 < len(unknown_args):
            key = unknown_args[i].lstrip("-")
            value = unknown_args[i + 1]

            # 尝试转换为合适的类型
            if value.lower() == "true":
                value = True
            elif value.lower() == "false":
                value = False
            elif value.isdigit():
                value = int(value)

            kwargs[key] = value

    # 初始化收集器
    collector = InfoCollector(use_cache=not args.no_cache, cache_dir=args.cache_dir, default_output_format=args.format)

    # 根据命令执行不同操作
    result = None

    if args.command == "search":
        # 执行搜索
        result = collector.search(args.query, engine=args.engine, max_results=args.max_results, **kwargs)

    elif args.command == "web":
        # 提取网页内容
        result = collector.extract_web_content(args.url, use_javascript=not args.no_js, **kwargs)

    elif args.command == "pdf":
        # 处理PDF文件
        result = collector.process_pdf(args.path, **kwargs)

    elif args.command == "text":
        # 处理文本内容
        text = args.text

        # 如果指定的是文件
        if args.is_file:
            if not os.path.exists(args.text):
                logger.error(f"文件不存在: {args.text}")
                sys.exit(1)

            # 读取文件内容
            with open(args.text, encoding="utf-8") as f:
                text = f.read()

        # 处理文本
        result = collector.process_text(text, title=args.title, **kwargs)

    elif args.command == "clear-cache":
        # 清除缓存
        collector.clear_cache(args.pattern)
        logger.info("缓存已清除")

    elif args.command == "list-adapters":
        # 列出适配器
        adapters = collector.get_available_adapters()
        print("可用的适配器:")
        for adapter in adapters:
            print(f"  - {adapter}")

    elif args.command == "list-formats":
        # 列出格式
        formats = collector.get_available_formats()
        print("可用的输出格式:")
        for fmt in formats:
            print(f"  - {fmt}")

    # 处理结果
    if result is not None and isinstance(result, str):
        write_output(result, args.output)
    elif result is not None:
        logger.info(f"结果类型: {type(result)}")

        # 如果是字典
        if isinstance(result, dict):
            import json

            result_str = json.dumps(result, ensure_ascii=False, indent=2)
            write_output(result_str, args.output)


if __name__ == "__main__":
    main()
