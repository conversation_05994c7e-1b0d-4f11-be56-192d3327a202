# 统一信息收集工具 (Unified Information Collector)

这是一个统一的信息收集和处理系统，能够从多种来源获取、处理和格式化信息，提供一致的输出格式。

## 主要功能

- 支持多种信息源：网页、PDF文件、文本、搜索引擎等
- 统一的输出格式：Markdown、JSON、纯文本、HTML等
- 智能缓存系统：减少重复请求和处理
- 模块化设计：易于扩展和添加新的数据源和处理方式

## 系统架构

该系统由以下主要组件组成：

1. **InfoCollector**：主类，协调各个组件的工作
2. **SourceManager**：管理各种数据源和适配器
3. **FormatManager**：处理内容格式转换
4. **CacheSystem**：管理缓存系统
5. **各种适配器**：处理特定类型的数据源

## 依赖项

- **必需**：
  - Python 3.8+
  - loguru（日志记录）
  - pydantic（数据模型）
- **可选**（根据使用的适配器）：
  - BeautifulSoup4、markdownify（网页处理）
  - duckduckgo_search（DuckDuckGo搜索）
  - aiohttp（异步HTTP请求）
  - playwright（JavaScript渲染）
  - markdown（Markdown到HTML转换）
  - PDF工具包（PDF处理）

## 安装依赖

```bash
# 安装基本依赖
pip install loguru pydantic

# 安装网页处理依赖
pip install beautifulsoup4 markdownify aiohttp

# 安装搜索依赖
pip install duckduckgo_search

# 安装JavaScript渲染依赖
pip install playwright
playwright install chromium

# 安装格式转换依赖
pip install markdown
```

## 使用示例

### 基本用法

```python
from video_agent.tools.info_collector import InfoCollector

# 初始化信息收集器
collector = InfoCollector(use_cache=True)

# 搜索信息
search_results = collector.search("人工智能最新进展", engine="duckduckgo")
print(search_results)  # 返回Markdown格式的搜索结果

# 提取网页内容
web_content = collector.extract_web_content("https://example.com")
print(web_content)  # 返回Markdown格式的网页内容

# 处理PDF文件
pdf_content = collector.process_pdf("/path/to/document.pdf")
print(pdf_content)  # 返回Markdown格式的PDF内容

# 处理文本
text_content = collector.process_text("这是一段需要处理的文本内容", title="示例文本")
print(text_content)  # 返回Markdown格式的文本内容
```

### 指定输出格式

```python
# 搜索并输出为JSON格式
json_results = collector.search("人工智能最新进展", output_format="json")

# 提取网页内容并输出为HTML格式
html_content = collector.extract_web_content("https://example.com", output_format="html")

# 处理PDF文件并输出为纯文本格式
text_content = collector.process_pdf("/path/to/document.pdf", output_format="text")
```

### 高级选项

```python
# 搜索并指定更多参数
results = collector.search(
    "人工智能最新进展",
    engine="duckduckgo",
    max_results=10,
    time_range="w",  # 一周内的结果
    region="cn-zh"
)

# 提取网页内容并指定更多参数
content = collector.extract_web_content(
    "https://example.com",
    use_javascript=True,  # 使用JavaScript渲染
    wait_time=5,  # 等待渲染的时间（秒）
    extract_images=True,  # 提取图片
)

# 处理PDF并指定输出目录
pdf_content = collector.process_pdf(
    "/path/to/document.pdf",
    output_dir="pdf_output"
)
```

### 转换现有内容

```python
# 获取原始内容对象
raw_content = collector.get_raw_content("https://example.com", "web")

# 转换为不同格式
md_content = collector.convert_content(raw_content, "markdown")
json_content = collector.convert_content(raw_content, "json")
html_content = collector.convert_content(raw_content, "html")
```

### 缓存管理

```python
# 清除所有缓存
collector.clear_cache()

# 清除特定模式的缓存
collector.clear_cache(pattern="adapter:web:*")
```

## 命令行使用

该工具也提供了命令行接口，可以直接从命令行使用：

```bash
# 搜索
python -m video_agent.tools.info_collector.example search "人工智能最新进展" --engine duckduckgo --max-results 5 --format markdown

# 提取网页内容
python -m video_agent.tools.info_collector.example web "https://example.com" --format html --output output.html

# 处理PDF文件
python -m video_agent.tools.info_collector.example pdf "/path/to/document.pdf" --format text

# 处理文本文件
python -m video_agent.tools.info_collector.example text "path/to/text.txt" --is-file --title "文本标题"

# 列出可用的适配器
python -m video_agent.tools.info_collector.example list-adapters

# 列出可用的输出格式
python -m video_agent.tools.info_collector.example list-formats

# 清除缓存
python -m video_agent.tools.info_collector.example clear-cache
```

## 扩展系统

### 添加新的适配器

可以通过继承BaseAdapter类来创建新的适配器：

```python
from video_agent.tools.info_collector import BaseAdapter, UnifiedContent

class MyCustomAdapter(BaseAdapter):
    """自定义适配器"""

    def process(self, source, **kwargs) -> UnifiedContent:
        # 实现处理逻辑
        # ...

        # 返回统一内容对象
        return UnifiedContent(
            metadata=self._create_metadata("custom", "source_id"),
            title="处理结果标题",
            sections=[...],
            # ...
        )

# 注册新适配器
collector = InfoCollector()
collector.source_manager.register_adapter("custom", MyCustomAdapter())

# 使用新适配器
result = collector.get_info("source", "custom")
```

## 注意事项

- 部分适配器需要额外安装相应的依赖
- 网页抓取和搜索要遵守相关网站的使用政策和法律法规
- 缓存数据会保存在本地，确保有足够的磁盘空间
- 使用JavaScript渲染时需要安装Playwright和相应的浏览器引擎
