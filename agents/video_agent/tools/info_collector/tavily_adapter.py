"""
Tavily搜索适配器

提供对Tavily API的访问
"""

from typing import Optional

from loguru import logger

from .adapters import BaseAdapter
from .models import ContentSection, UnifiedContent


class TavilyAdapter(BaseAdapter):
    """Tavily搜索适配器"""

    def __init__(self, api_key: Optional[str] = None):
        """
        初始化Tavily搜索适配器

        Args:
            api_key: Tavily API密钥，如果为None则从环境变量获取
        """
        super().__init__()
        self.toolkit = self._init_toolkit(api_key)

    def _init_toolkit(self, api_key: Optional[str] = None):
        """
        初始化Tavily工具包

        Args:
            api_key: Tavily API密钥

        Returns:
            Tavily工具包实例
        """
        try:
            # 尝试导入Tavily工具包
            from ..tavily_toolkit import TavilyToolkit

            return TavilyToolkit(api_key=api_key)
        except ImportError as e:
            logger.error(f"初始化Tavily工具包出错: {e}")
            return None

    def search(self, query: str, **kwargs) -> dict:
        """
        执行Tavily搜索

        Args:
            query: 搜索查询
            **kwargs: 其他搜索参数

        Returns:
            搜索结果
        """
        if not self.toolkit:
            return {"error": "Tavily工具包未初始化", "results": []}

        try:
            # 提取搜索参数
            search_depth = kwargs.get("search_depth", "basic")
            max_results = kwargs.get("max_results", 5)
            include_domains = kwargs.get("include_domains")
            exclude_domains = kwargs.get("exclude_domains")
            include_answer = kwargs.get("include_answer", False)
            include_raw_content = kwargs.get("include_raw_content", True)
            include_images = kwargs.get("include_images", False)

            # 执行搜索
            search_params = {
                "query": query,
                "search_depth": search_depth,
                "max_results": max_results,
                "include_answer": include_answer,
                "include_raw_content": include_raw_content,
                "include_images": include_images,
            }

            # 添加可选参数
            if include_domains:
                search_params["include_domains"] = include_domains
            if exclude_domains:
                search_params["exclude_domains"] = exclude_domains

            # 执行搜索
            results = self.toolkit.search(**search_params)

            # 如果有错误
            if "error" in results:
                return {"error": results["error"], "results": []}

            return results

        except Exception as e:
            logger.error(f"Tavily搜索出错: {e}")
            return {"error": str(e), "results": []}

    def process(self, source: str, **kwargs) -> UnifiedContent:
        """
        处理搜索查询

        Args:
            source: 搜索查询
            **kwargs: 其他参数

        Returns:
            统一内容对象
        """
        query = source

        # 执行搜索
        search_results = self.search(query, **kwargs)

        # 处理搜索答案（如果有）
        answer = None
        if "answer" in search_results:
            answer = search_results["answer"]

        # 提取搜索结果
        results = search_results.get("results", [])

        # 创建元数据
        metadata = self._create_metadata(
            source_type="search",
            source_id=f"tavily_search:{query}",
            engine="tavily",
            query=query,
            result_count=len(results),
            api_credits_used=search_results.get("metadata", {}).get("api_credits_used", 1),
        )

        # 创建章节
        sections = []

        # 如果有回答，添加为第一个章节
        if answer:
            answer_section = ContentSection(
                id="answer",
                title="搜索回答",
                text=answer,
                level=0,
            )
            sections.append(answer_section)

        # 添加搜索结果章节
        for i, result in enumerate(results):
            # 提取搜索结果字段
            title = result.get("title", "未知标题")
            url = result.get("url", "")
            snippet = result.get("content", "")

            # 如果有原始内容，使用前500个字符作为snippet
            raw_content = result.get("raw_content", "")
            if raw_content and len(raw_content) > len(snippet):
                snippet = raw_content[:500] + ("..." if len(raw_content) > 500 else "")

            # 创建章节内容
            section_text = f"{snippet}\n\n[查看完整内容]({url})"

            # 创建章节对象
            section = ContentSection(
                id=f"result_{i + 1}",
                title=title,
                text=section_text,
                level=1,
            )
            sections.append(section)

        # 创建统一内容对象
        content = UnifiedContent(
            metadata=metadata,
            title=f"Tavily搜索: {query}",
            summary=f"使用Tavily搜索引擎查询 '{query}'，找到 {len(results)} 条结果。"
            + (
                f" 搜索回答: {answer[:100]}..."
                if answer and len(answer) > 100
                else f" 搜索回答: {answer}"
                if answer
                else ""
            ),
            sections=sections,
            raw_content=str(search_results),
        )

        return content
