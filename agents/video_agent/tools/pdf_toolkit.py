import json
import os
from pathlib import Path
from typing import Optional

from camel.toolkits.base import BaseToolkit
from camel.toolkits.function_tool import FunctionTool
from camel.utils import dependencies_required
from docling_core.types.doc import ImageRefMode
from loguru import logger

from .markdown_exporter import export_to_markdown


class PDFToolkit(BaseToolkit):
    r"""A toolkit for extracting content from PDF files, converting them to
    markdown and extracting images.
    """

    @dependencies_required("docling")
    def __init__(self, timeout: Optional[float] = None) -> None:
        r"""Initializes the PDFToolkit."""
        super().__init__(timeout=timeout)
        self.image_resolution_scale = 5.0

    def _parse_pdf_to_markdown(self, input_doc_path: str, output_dir: str) -> str:
        r"""Parses a PDF file to markdown and extracts images.

        Args:
            input_doc_path (str): Path to the PDF file, or a URL.
            output_dir (str): Directory to save the extracted content.

        Returns:
            str: Path to the JSON file containing image information.
        """
        import time

        from docling.datamodel.base_models import InputFormat
        from docling.datamodel.pipeline_options import PdfPipelineOptions
        from docling.document_converter import (
            DocumentConverter,
            PdfFormatOption,
        )

        # Create output directory if it doesn't exist
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        # Configure pipeline options
        pipeline_options = PdfPipelineOptions()
        pipeline_options.images_scale = self.image_resolution_scale
        pipeline_options.generate_page_images = True
        pipeline_options.generate_picture_images = True

        # Initialize document converter
        doc_converter = DocumentConverter(
            format_options={InputFormat.PDF: PdfFormatOption(pipeline_options=pipeline_options)},
        )

        start_time = time.time()

        logger.info("Starting PDF conversion")
        conv_res = doc_converter.convert(input_doc_path)
        logger.info("PDF conversion completed")

        doc_filename = conv_res.input.file.stem

        # Save images and their captions
        image_info_file = self._save_image_path_and_caption(conv_res.document, doc_filename, output_path)

        markdown = export_to_markdown(conv_res.document, image_mode=ImageRefMode.REFERENCED)
        markdown_file = output_path / f"{doc_filename}.md"
        with open(markdown_file, "w") as f:
            f.write(markdown)

        end_time = time.time() - start_time
        logger.info(f"Document converted and figures exported in {end_time:.2f} seconds.")

        return image_info_file, markdown_file

    def _save_image_path_and_caption(self, document, doc_filename: str, output_dir: Path) -> str:
        r"""Saves images from the document and their captions.

        Args:
            document: The DoclingDocument object.
            doc_filename (str): The filename of the document.
            output_dir (Path): Directory to save the images.

        Returns:
            str: Path to the JSON file containing image information.
        """
        from docling_core.types.doc import PictureItem, TableItem

        # Create image directory
        image_dir = output_dir / f"{doc_filename}_artifacts"
        image_dir.mkdir(parents=True, exist_ok=True)

        img_count = 0
        image_info = []

        # Extract and save images
        for item, level in document.iterate_items(with_groups=False):
            if isinstance(item, (PictureItem, TableItem)):
                if item.image is None:
                    item.image = item.get_image(document)
                    image = item.image
                else:
                    image = item.image.pil_image

                page_no = item.prov[0].page_no
                prefix = "image" if isinstance(item, PictureItem) else "table"
                loc_path = image_dir / f"page_{page_no}_{prefix}_{img_count}.png"

                image.save(loc_path)
                item.image.uri = loc_path

                caption = item.caption_text(document)
                image_info.append({"path": str(loc_path), "caption": caption})

                img_count += 1

        # Save image information to JSON file
        image_info_file = output_dir / f"{doc_filename}_images.json"
        with open(image_info_file, "w") as f:
            json.dump(image_info, f, indent=4, ensure_ascii=False)

        logger.info(f"Saved {img_count} images info in {image_info_file}")
        return str(image_info_file)

    def extract_pdf(self, pdf_path: str, output_dir: str = "pdf_output") -> dict[str, list[dict[str, str]]]:
        r"""Extracts content from a PDF file, including text and images.

        Args:
            pdf_path (str): Path to the PDF file, or a URL.
            output_dir (str): Directory to save the extracted content.

        Returns:
            Dict[str, List[Dict[str, str]]]: A dictionary containing the extracted
                images information with their captions.
        """
        try:
            # Create output directory if it doesn't exist
            os.makedirs(output_dir, exist_ok=True)

            # Parse PDF to markdown and extract images
            image_info_file, markdown_file = self._parse_pdf_to_markdown(pdf_path, output_dir)

            # Load image information
            with open(image_info_file) as f:
                image_info = json.load(f)

            # Return extracted content
            return {
                "pdf_path": pdf_path,
                "output_dir": output_dir,
                "image_info_file": image_info_file,
                "markdown_file": str(markdown_file),
                "images": image_info,
            }

        except Exception as e:
            logger.error(f"Error extracting PDF content: {e}")
            return {
                "pdf_path": pdf_path,
                "output_dir": output_dir,
                "error": str(e),
                "images": [],
                "markdown_file": None,
            }

    def get_tools(self) -> list[FunctionTool]:
        r"""Returns a list of FunctionTool objects representing the
        functions in the toolkit.

        Returns:
            List[FunctionTool]: A list of FunctionTool objects
                representing the functions in the toolkit.
        """
        return [
            FunctionTool(self.extract_pdf),
        ]


if __name__ == "__main__":
    pdf_toolkit = PDFToolkit()
    output_directory = "test_output"
    os.makedirs(output_directory, exist_ok=True)
    result = pdf_toolkit.extract_pdf("https://arxiv.org/pdf/2503.10628", output_directory)
    print(result)
