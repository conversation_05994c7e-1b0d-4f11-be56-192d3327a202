"""
内容管理工具包

将ContentManager包装成Camel框架可用的工具集
"""

from dotenv import load_dotenv

load_dotenv()

from typing import Any, Optional

from camel.toolkits.base import BaseToolkit
from camel.toolkits.function_tool import FunctionTool
from loguru import logger

from .content_manager import ContentCollector


class ContentManagerToolkit(BaseToolkit):
    """
    基于ContentManager的Camel工具包

    提供统一的内容管理功能，包括：
    - 处理文本内容
    - 提取和处理网页内容
    - 处理PDF文档
    - 多种搜索引擎支持（Tavily、DuckDuckGo、SearXNG）
    - 统一的缓存管理
    """

    def __init__(
        self,
        config: Optional[dict[str, Any]] = None,
        timeout: Optional[float] = None,
    ) -> None:
        """
        初始化内容管理工具包

        Args:
            config: 配置参数，包括缓存目录、API密钥等
            timeout: 请求超时时间（秒）
        """
        super().__init__(timeout=timeout)

        # 默认配置
        default_config = {
            "cache_dir": "cache",
            "content_dir": "cache/content",
            "resource_dir": "cache/resources",
            "image_dir": "cache/resources/images",
            "video_dir": "cache/resources/videos",
        }

        # 合并配置
        if config:
            default_config.update(config)

        # 初始化内容收集器
        self.collector = ContentCollector(default_config)

        logger.info("内容管理工具包初始化完成")
        logger.info(f"已注册的处理器: {', '.join(self.collector.processors.keys())}")

    def process_text(
        self,
        text: str,
        title: Optional[str] = None,
        is_markdown: bool = False,
        tags: Optional[list[str]] = None,
    ) -> dict[str, Any]:
        """
        处理文本内容，将其转换为统一的Markdown格式

        文本处理器可用于处理简单文本或已有的Markdown内容，它会：
        1. 保存文本内容为Markdown格式
        2. 提取文本的标题和结构
        3. 进行可选的格式调整

        Args:
            text: 文本内容
            title: 文本标题，如果不指定则尝试从内容中提取
            is_markdown: 内容是否已经是Markdown格式
            tags: 内容标签列表

        Returns:
            Dict[str, Any]: 包含处理后文本的字典，包括：
                - content: Markdown格式的内容
                - metadata: 内容元数据
                - cached_path: 缓存文件路径
        """
        try:
            # 处理文本
            result = self.collector.collect(
                source_type="text",
                source_id=title or "text_input",
                text=text,
                is_markdown=is_markdown,
                title=title,
                tags=tags or [],
            )

            # 返回结果
            return {
                "content": result.content,
                "metadata": {
                    "title": result.metadata.title,
                    "tags": result.metadata.tags,
                    "timestamp": result.metadata.timestamp.isoformat(),
                },
                "cached_path": result.cached_path,
            }

        except Exception as e:
            logger.error(f"处理文本内容出错: {e}")
            return {"error": f"处理文本内容出错: {str(e)}", "title": title or "未命名文本"}

    def extract_web_content(
        self,
        url: str,
        use_javascript: bool = True,
        timeout: Optional[int] = None,
        extract_images: bool = True,
        force_refresh: bool = False,
    ) -> dict[str, Any]:
        """
        提取网页内容并转换为Markdown格式

        网页处理器能够：
        1. 获取网页HTML内容（支持JavaScript渲染）
        2. 提取主要文本内容，过滤广告和无关内容
        3. 下载和处理图片资源
        4. 保留内容结构并转换为Markdown
        5. 自动处理相对链接

        Args:
            url: 网页URL
            use_javascript: 是否使用JavaScript渲染（对动态网页有用）
            timeout: 请求超时时间（秒）
            extract_images: 是否提取网页中的图片
            force_refresh: 是否强制刷新（不使用缓存）

        Returns:
            Dict[str, Any]: 包含网页内容的字典，包括：
                - content: Markdown格式的内容
                - metadata: 内容元数据（标题、来源等）
                - resources: 提取的资源列表
                - cached_path: 缓存文件路径
        """
        try:
            # 提取内容
            result = self.collector.collect(
                source_type="web",
                source_id=url,
                use_javascript=use_javascript,
                timeout=timeout,
                extract_images=extract_images,
                force_refresh=force_refresh,
            )

            # 准备资源信息
            resources = []
            for res in result.resources:
                resources.append(
                    {
                        "filename": res.filename,
                        "resource_type": res.resource_type,
                        "local_path": res.local_path,
                    },
                )

            # 返回结果
            return {
                "content": result.content,
                "metadata": {
                    "title": result.metadata.title,
                    "source_id": result.metadata.source_id,
                    "timestamp": result.metadata.timestamp.isoformat(),
                },
                "resources": resources,
                "resources_count": len(resources),
                "cached_path": result.cached_path,
            }

        except Exception as e:
            logger.error(f"提取网页内容出错: {e}")
            return {"error": f"提取网页内容出错: {str(e)}", "url": url}

    def process_pdf(
        self,
        source_id: str,
        extract_images: bool = True,
        force_refresh: bool = False,
    ) -> dict[str, Any]:
        """
        处理PDF文档并转换为Markdown格式

        PDF处理器能够：
        1. 处理本地PDF文件或远程PDF链接
        2. 提取文本内容，保留格式和结构
        3. 提取嵌入的图片
        4. 保留分页信息
        5. 处理表格内容

        Args:
            source_id: PDF文件路径或URL
            extract_images: 是否提取文档中的图片
            force_refresh: 是否强制刷新（不使用缓存）

        Returns:
            Dict[str, Any]: 包含PDF内容的字典，包括：
                - content: Markdown格式的内容
                - metadata: 文档元数据
                - resources: 提取的图片资源列表
                - cached_path: 缓存文件路径
        """
        try:
            # 处理PDF
            result = self.collector.collect(
                source_type="pdf",
                source_id=source_id,
                extract_images=extract_images,
                force_refresh=force_refresh,
            )

            # 准备资源信息
            resources = []
            for res in result.resources:
                resources.append(
                    {
                        "filename": res.filename,
                        "resource_type": res.resource_type,
                        "local_path": res.local_path,
                        "metadata": res.metadata,
                    },
                )

            # 获取处理统计信息
            stats = result.metadata.processing_stats

            # 返回结果
            return {
                "content": result.content,
                "metadata": {
                    "title": result.metadata.title,
                    "source_id": result.metadata.source_id,
                    "timestamp": result.metadata.timestamp.isoformat(),
                },
                "resources": resources,
                "resources_count": len(resources),
                "cached_path": result.cached_path,
                "statistics": {
                    "images_count": stats.get("images_count", 0),
                    "processing_time": stats.get("processing_time_seconds", 0),
                },
            }

        except Exception as e:
            logger.error(f"处理PDF文件出错: {e}")
            return {"error": f"处理PDF文件出错: {str(e)}", "source_id": source_id}

    def search(
        self,
        query: str,
        search_type: str = "tavily",
        max_results: int = 5,
        include_answer: bool = True,
        search_depth: str = "basic",
        force_refresh: bool = False,
    ) -> dict[str, Any]:
        """
        使用搜索引擎查询信息

        支持多种搜索引擎：
        - "tavily": Tavily智能搜索（提供精确摘要和答案）
        - "duckduckgo": DuckDuckGo搜索（无需API密钥）
        - "searxng": SearXNG元搜索引擎（支持自托管实例）

        搜索功能能够：
        1. 执行实时Web搜索
        2. 提供结构化的搜索结果
        3. 根据配置获取答案摘要
        4. 缓存搜索结果以避免重复请求

        Args:
            query: 搜索查询
            search_type: 搜索引擎类型（tavily/duckduckgo/searxng）
            max_results: 最大结果数
            include_answer: 是否包含答案摘要（仅Tavily搜索支持）
            search_depth: 搜索深度，basic或comprehensive（仅Tavily搜索支持）
            force_refresh: 是否强制刷新（不使用缓存）

        Returns:
            Dict[str, Any]: 包含搜索结果的字典，包括：
                - content: Markdown格式的搜索结果
                - metadata: 搜索元数据
                - cached_path: 缓存文件路径
                - statistics: 搜索统计信息
        """
        try:
            # 额外参数
            kwargs = {}

            # Tavily搜索特定参数
            if search_type == "tavily":
                kwargs["include_answer"] = include_answer
                kwargs["search_depth"] = search_depth

            # 通用参数
            kwargs["max_results"] = max_results

            # 执行搜索
            result = self.collector.collect(
                source_type=search_type,
                source_id=query,
                force_refresh=force_refresh,
                **kwargs,
            )

            # 获取处理统计信息
            stats = result.metadata.processing_stats

            # 返回结果
            return {
                "content": result.content,
                "metadata": {
                    "title": result.metadata.title,
                    "query": query,
                    "search_type": search_type,
                    "timestamp": result.metadata.timestamp.isoformat(),
                },
                "cached_path": result.cached_path,
                "statistics": {
                    "results_count": stats.get("results_count", 0),
                    "search_depth": stats.get("search_depth", "basic") if search_type == "search" else None,
                    "processing_time": stats.get("processing_time_seconds", 0),
                },
            }

        except Exception as e:
            logger.error(f"搜索出错: {e}")
            return {"error": f"搜索出错: {str(e)}", "query": query, "search_type": search_type}

    def get_cache_stats(self) -> dict[str, Any]:
        """
        获取缓存统计信息

        返回有关缓存使用情况的详细统计信息，包括：
        - 缓存内容数量
        - 缓存资源数量
        - 总占用空间
        - 未引用资源数量

        Returns:
            Dict[str, Any]: 缓存统计信息
        """
        try:
            return self.collector.get_cache_stats()
        except Exception as e:
            logger.error(f"获取缓存统计出错: {e}")
            return {"error": f"获取缓存统计出错: {str(e)}"}

    def invalidate_cache(self, source_type: str, source_id: str) -> dict[str, Any]:
        """
        使指定内容的缓存失效

        可用于强制重新获取特定内容，确保获取最新版本。

        Args:
            source_type: 来源类型（web/pdf/search/text等）
            source_id: 来源标识（URL/文件路径/查询等）

        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            success = self.collector.invalidate_cache(source_type, source_id)
            return {
                "success": success,
                "message": f"缓存已{'成功' if success else '失败'}失效: {source_type}:{source_id}",
            }
        except Exception as e:
            logger.error(f"使缓存失效出错: {e}")
            return {"success": False, "error": f"使缓存失效出错: {str(e)}"}

    def cleanup_cache(self, days_threshold: int = 30) -> dict[str, Any]:
        """
        清理未使用的缓存资源

        删除长时间未被引用的资源文件，释放磁盘空间。
        资源只有在不被任何内容引用且超过阈值天数时才会被删除。

        Args:
            days_threshold: 清理阈值（天数）

        Returns:
            Dict[str, Any]: 操作结果，包括清理的资源数量
        """
        try:
            count = self.collector.cleanup(days_threshold)
            return {
                "success": True,
                "cleaned_count": count,
                "message": f"已清理 {count} 个未使用资源",
            }
        except Exception as e:
            logger.error(f"清理缓存出错: {e}")
            return {"success": False, "error": f"清理缓存出错: {str(e)}"}

    def get_tools(self) -> list[FunctionTool]:
        """
        返回工具包中的工具列表

        Returns:
            list[FunctionTool]: 工具列表
        """
        return [
            FunctionTool(self.process_text),
            FunctionTool(self.extract_web_content),
            FunctionTool(self.process_pdf),
            FunctionTool(self.search),
            FunctionTool(self.get_cache_stats),
            FunctionTool(self.invalidate_cache),
            FunctionTool(self.cleanup_cache),
        ]


if __name__ == "__main__":
    # 示例：创建工具包并测试
    toolkit = ContentManagerToolkit()

    # 处理文本
    text_result = toolkit.process_text(
        text="# 测试文档\n\n这是一个简单的测试文档，用于演示内容管理工具包的功能。",
        title="测试文档",
        is_markdown=True,
    )
    print(f"文本处理完成，标题: {text_result.get('metadata', {}).get('title')}")

    # 提取网页内容
    web_result = toolkit.extract_web_content("https://python.org")
    print(f"网页处理完成，标题: {web_result.get('metadata', {}).get('title')}")

    # 搜索
    search_result = toolkit.search("Python programming language")
    print(f"搜索完成，找到结果: {search_result.get('statistics', {}).get('results_count', 0)} 条")
