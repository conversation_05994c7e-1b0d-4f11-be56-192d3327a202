"""
内容收集器模块

协调内容处理和缓存的核心组件
"""

import os
from typing import Any, Optional

from loguru import logger

from .cache import ContentCache
from .models import MarkdownContent
from .processors import (
    ContentProcessor,
    DuckDuckGoProcessor,
    PdfProcessor,
    SearXNGProcessor,
    TavilyProcessor,
    TextProcessor,
    WebProcessor,
)
from .resource_manager import ResourceManager


class ContentCollector:
    """统一内容收集器"""

    # 默认配置
    DEFAULT_CONFIG = {
        "cache_dir": "cache",  # 缓存根目录
        "content_dir": "cache/content",  # Markdown内容目录
        "resource_dir": "cache/resources",  # 资源根目录
        "image_dir": "cache/resources/images",  # 图片资源目录
        "video_dir": "cache/resources/videos",  # 视频资源目录
        "cache_ttl": 86400 * 7,  # 缓存生存时间（秒），默认7天
        "cleanup_threshold": 30,  # 资源清理阈值（天）
        "max_cache_size_mb": 1024,  # 最大缓存大小（MB）
        "use_playwright": True,  # 是否使用Playwright获取网页
        "tavily_api_key": os.getenv("TAVILY_API_KEY"),  # Tavily API密钥
        "searxng_instance": "https://searx.be",  # SearXNG实例URL
    }

    def __init__(self, config: dict[str, Any] = None):
        """
        初始化内容收集器

        Args:
            config: 配置参数，包括缓存目录等
        """
        self.config = self.DEFAULT_CONFIG.copy()
        if config:
            self.config.update(config)

        # 初始化资源管理器
        self.resource_manager = ResourceManager(self.config)

        # 初始化缓存系统
        self.cache = ContentCache(self.config)

        # 注册处理器
        self.processors: dict[str, ContentProcessor] = {}
        self._register_default_processors()

    def _register_default_processors(self) -> None:
        """注册默认的内容处理器"""
        # 基础文本处理器
        self.register_processor("text", TextProcessor(self.config, self.resource_manager))

        # 网页处理器
        self.register_processor("web", WebProcessor(self.config, self.resource_manager))

        # PDF处理器
        self.register_processor("pdf", PdfProcessor(self.config, self.resource_manager))

        # 搜索处理器
        if self.config.get("tavily_api_key"):
            self.register_processor("tavily", TavilyProcessor(self.config, self.resource_manager))
            logger.info("已注册Tavily搜索处理器")
        else:
            logger.warning("未提供Tavily API密钥，搜索处理器未注册")

        # DuckDuckGo搜索处理器
        self.register_processor("duckduckgo", DuckDuckGoProcessor(self.config, self.resource_manager))
        logger.info("已注册DuckDuckGo搜索处理器")

        # SearXNG搜索处理器
        self.register_processor("searxng", SearXNGProcessor(self.config, self.resource_manager))
        logger.info(f"已注册SearXNG搜索处理器，使用实例: {self.config.get('searxng_instance')}")

    def register_processor(self, source_type: str, processor: ContentProcessor) -> None:
        """
        注册内容处理器

        Args:
            source_type: 处理器处理的来源类型
            processor: 处理器实例
        """
        self.processors[source_type] = processor
        logger.info(f"Registered processor for source type: {source_type}")

    def get_processor(self, source_type: str) -> Optional[ContentProcessor]:
        """
        获取指定类型的处理器

        Args:
            source_type: 来源类型

        Returns:
            Optional[ContentProcessor]: 处理器实例，如不存在则返回None
        """
        return self.processors.get(source_type)

    def collect(
        self,
        source_type: str,
        source_id: str,
        force_refresh: bool = False,
        **kwargs,
    ) -> MarkdownContent:
        """
        收集并处理指定来源的内容

        Args:
            source_type: 来源类型
            source_id: 来源标识
            force_refresh: 是否强制刷新（不使用缓存）
            **kwargs: 传递给处理器的额外参数

        Returns:
            MarkdownContent: 处理后的内容
        """
        # 检查缓存（除非强制刷新）
        if not force_refresh:
            cached_content = self.cache.get_content(source_type, source_id)
            if cached_content:
                logger.info(f"Using cached content for {source_type}:{source_id}")
                return cached_content

        # 获取处理器
        processor = self.get_processor(source_type)
        if not processor:
            raise ValueError(f"No processor registered for source type: {source_type}")

        # 处理内容
        logger.info(f"Processing content {source_type}:{source_id}")
        content = processor.process(source_id, **kwargs)

        # 缓存处理结果
        self.cache.cache_content(content)

        return content

    def get_markdown_path(self, source_type: str, source_id: str) -> Optional[str]:
        """
        获取指定内容的Markdown文件路径，如不存在则返回None

        Args:
            source_type: 来源类型
            source_id: 来源标识

        Returns:
            Optional[str]: Markdown文件路径，如不存在则返回None
        """
        # 检查缓存
        cached_content = self.cache.get_content(source_type, source_id)
        if cached_content and cached_content.cached_path:
            return cached_content.cached_path
        return None

    def invalidate_cache(self, source_type: str, source_id: str) -> bool:
        """
        使指定内容的缓存失效

        Args:
            source_type: 来源类型
            source_id: 来源标识

        Returns:
            bool: 是否成功
        """
        return self.cache.invalidate(source_type, source_id)

    def cleanup(self, days_threshold: Optional[int] = None) -> int:
        """
        清理缓存中的未使用资源

        Args:
            days_threshold: 清理阈值（天数），不指定则使用配置中的值

        Returns:
            int: 清理的资源数量
        """
        threshold = days_threshold or self.config.get("cleanup_threshold", 30)
        return self.cache.cleanup_resources(threshold)

    def get_cache_stats(self) -> dict[str, Any]:
        """
        获取缓存统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        return self.cache.get_stats()
