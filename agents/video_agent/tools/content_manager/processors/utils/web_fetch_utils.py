"""
网页内容获取工具模块

为搜索处理器提供网页内容获取和处理功能
"""

import asyncio
from typing import Any
from urllib.parse import urlparse

from bs4 import BeautifulSoup
from loguru import logger
from markdownify import markdownify as md


def _should_skip_image(src: str) -> bool:
    """判断是否应该跳过特定图片"""
    # 跳过数学公式
    if "wikimedia.org/api/rest_v1/media/math/" in src:
        return True

    # 跳过常见UI元素、图标、徽标
    if any(
        [
            # 静态UI元素
            "/static/images/" in src,
            # 小图标 (通常小于25px)
            any(f"{size}px-" in src for size in range(15, 26)),
            # 常见图标和徽标文件
            "logo" in src.lower(),
            "icon" in src.lower(),
            "symbol" in src.lower(),
            "button" in src.lower(),
            # 特定Wikipedia图标
            "Commons-logo" in src,
            "Wikiquote-logo" in src,
            "Wikidata-logo" in src,
            "OOjs_UI_icon" in src,
        ],
    ):
        return True

    return False


def _resolve_relative_url(src: str, base_url: str) -> str:
    """解析相对URL为绝对URL"""
    parsed_url = urlparse(base_url)
    base = f"{parsed_url.scheme}://{parsed_url.netloc}"

    # 处理协议相对URL
    if src.startswith("//"):
        return f"{parsed_url.scheme}:{src}"
    elif src.startswith("/"):
        # 绝对路径
        return f"{base}{src}"
    else:
        # 相对路径
        url_path = parsed_url.path
        if url_path and not url_path.endswith("/"):
            url_path = url_path.rsplit("/", 1)[0] + "/"
        elif not url_path:
            url_path = "/"

        # 处理 "../" 类型的相对路径
        if src.startswith("../"):
            path_parts = url_path.rstrip("/").split("/")
            src_parts = src.split("/")

            # 计算向上访问的层数
            up_count = 0
            for part in src_parts:
                if part == "..":
                    up_count += 1
                else:
                    break

            # 移除相应数量的路径段
            if up_count > 0:
                path_parts = path_parts[:-up_count]
                if not path_parts:
                    path_parts = [""]
                new_base_path = "/".join(path_parts)
                if not new_base_path.endswith("/"):
                    new_base_path += "/"
                return f"{base}{new_base_path}{'/'.join(src_parts[up_count:])}"
            else:
                return f"{base}{url_path}{src}"
        else:
            # 普通相对路径
            return f"{base}{url_path}{src}"


def _extract_images_from_html(html_content: str, base_url: str) -> list[dict[str, str]]:
    """
    从HTML内容中提取图片信息

    Args:
        html_content: HTML内容
        base_url: 基础URL

    Returns:
        List[Dict[str, str]]: 图片信息列表
    """
    soup = BeautifulSoup(html_content, "html.parser")
    images = []

    for img in soup.find_all("img"):
        src = img.get("src", "")
        alt = img.get("alt", "")

        if src:
            # 过滤非内容图片
            if _should_skip_image(src):
                continue

            # 处理相对URL
            if not src.startswith(("http://", "https://", "data:")):
                src = _resolve_relative_url(src, base_url)

            images.append(
                {
                    "url": src,
                    "alt": alt,
                },
            )

    return images


async def fetch_html_async(url: str, **kwargs) -> dict[str, Any]:
    """
    异步获取网页HTML内容并转换为Markdown

    Args:
        url: 网页URL
        **kwargs: 其他参数，如timeout, use_javascript等

    Returns:
        Dict: 获取结果
    """
    try:
        # 提取参数
        use_javascript = kwargs.get("use_javascript", False)
        timeout = kwargs.get("timeout", 30)
        wait_time = kwargs.get("wait_time", 3)
        wait_for_selector = kwargs.get("wait_for_selector")
        extract_images = kwargs.get("extract_images", False)

        html_content = ""
        page_title = ""

        # 使用Playwright渲染JavaScript
        if use_javascript:
            try:
                from playwright.async_api import async_playwright
            except ImportError:
                return {"error": "缺少依赖: playwright", "success": False}

            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                page = await browser.new_page()

                try:
                    await page.goto(url, wait_until="domcontentloaded", timeout=timeout * 1000)

                    # 等待特定选择器
                    if wait_for_selector:
                        try:
                            await page.wait_for_selector(wait_for_selector, timeout=wait_time * 1000)
                        except Exception as e:
                            logger.warning(f"选择器 {wait_for_selector} 未在页面上找到: {e}")
                    else:
                        # 或等待固定时间
                        await asyncio.sleep(wait_time)

                    # 获取页面标题
                    page_title = await page.title()

                    # 获取HTML内容
                    html_content = await page.content()
                finally:
                    await browser.close()

        # 直接获取内容（不渲染JavaScript）
        else:
            import aiohttp

            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=timeout) as response:
                    if response.status != 200:
                        return {
                            "url": url,
                            "success": False,
                            "error": f"HTTP错误: {response.status}",
                        }

                    html_content = await response.text()

                    # 解析标题
                    soup = BeautifulSoup(html_content, "html.parser")
                    title_tag = soup.find("title")
                    page_title = title_tag.get_text() if title_tag else ""

        # 处理HTML内容
        if html_content:
            soup = BeautifulSoup(html_content, "html.parser")

            # 提取主要内容（移除不必要的元素）
            for tag in soup(["script", "style", "meta", "link", "noscript", "iframe"]):
                tag.decompose()

            # 转换为Markdown
            markdown_content = md(str(soup))

            result = {
                "url": url,
                "title": page_title,
                "success": True,
                "content": markdown_content,
                "raw_html": html_content,
            }

            # 如果需要提取图片
            if extract_images:
                result["images"] = _extract_images_from_html(html_content, url)

            return result
        else:
            return {
                "url": url,
                "success": False,
                "error": "无法获取页面内容",
            }

    except Exception as e:
        logger.error(f"获取网页内容出错: {url} - {str(e)}")
        return {
            "url": url,
            "success": False,
            "error": str(e),
        }


def fetch_html(url: str, **kwargs) -> dict[str, Any]:
    """
    获取网页HTML内容的同步包装函数

    Args:
        url: 网页URL
        **kwargs: 其他参数

    Returns:
        Dict: 获取结果
    """
    # 运行异步函数
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

    return loop.run_until_complete(fetch_html_async(url, **kwargs))
