"""
文本处理器模块

处理纯文本和Markdown内容
"""

import re
from datetime import datetime
from pathlib import Path
from typing import Any, Optional

from loguru import logger

from ..models import ContentMetadata, MarkdownContent
from ..resource_manager import ResourceManager

# 配置日志
logger = logger.bind(module="text_processor")


class TextProcessor:
    """文本内容处理器，处理原始文本/Markdown"""

    def __init__(self, config: dict[str, Any], resource_manager: ResourceManager):
        """
        初始化处理器

        Args:
            config: 配置参数
            resource_manager: 资源管理器实例
        """
        self.config = config
        self.resource_manager = resource_manager
        self.source_type = "text"

    def _extract_title(self, markdown_content: str) -> Optional[str]:
        """从Markdown内容中提取标题"""
        # 尝试从h1标题中提取
        h1_match = re.search(r"^#\s+(.+)$", markdown_content, re.MULTILINE)
        if h1_match:
            return h1_match.group(1).strip()
        return None

    def process(self, source_id: str, **kwargs) -> MarkdownContent:
        """
        处理原始文本或已格式化的Markdown

        Args:
            source_id: 内容来源标识（可以是标识符或文件路径）
            **kwargs: 处理器特定的参数

        Returns:
            MarkdownContent: 处理后的统一内容
        """
        start_time = datetime.now()

        try:
            text_content = kwargs.get("text", "")
            is_markdown = kwargs.get("is_markdown", False)

            # 如果提供的是文件路径，尝试读取文件
            if not text_content and Path(source_id).is_file():
                try:
                    with open(source_id, encoding="utf-8") as f:
                        text_content = f.read()
                    is_markdown = source_id.endswith((".md", ".markdown"))
                except Exception as e:
                    logger.error(f"Failed to read file {source_id}: {str(e)}")
                    raise ValueError(f"Failed to read file: {str(e)}")

            # 如果仍然没有内容，报错
            if not text_content:
                raise ValueError("No text content provided")

            # 如果不是Markdown，进行简单转换
            if not is_markdown:
                # 简单地将文本包装为Markdown
                paragraphs = text_content.split("\n\n")
                markdown_content = "\n\n".join(paragraphs)

                # 如果没有标题，可以从第一行创建一个
                if not re.search(r"^#\s+", markdown_content):
                    first_line = text_content.strip().split("\n")[0]
                    if first_line:
                        markdown_content = f"# {first_line}\n\n{markdown_content}"
            else:
                markdown_content = text_content

            # 这里我们暂不处理文本中的资源引用
            resources = []

            # 准备处理统计信息
            processing_stats = {
                "char_count": len(text_content),
                "word_count": len(text_content.split()),
                "line_count": len(text_content.splitlines()),
                "is_markdown": is_markdown,
            }

            # 提取标题
            title = kwargs.get("title") or self._extract_title(markdown_content)

            # 创建元数据
            metadata = ContentMetadata(
                source_type=self.source_type,
                source_id=source_id,
                title=title,
                tags=kwargs.get("tags", []),
                processing_stats=processing_stats,
            )

            # 记录处理时间
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            metadata.processing_stats["processing_time_seconds"] = processing_time

            # 创建并返回MarkdownContent
            return MarkdownContent(
                metadata=metadata,
                content=markdown_content,
                resources=resources,
            )

        except Exception as e:
            logger.error(f"Error processing text content {source_id}: {str(e)}")

            # 创建错误报告
            metadata = ContentMetadata(
                source_type=self.source_type,
                source_id=source_id,
                title=f"Error: {str(e)}",
                processing_stats={"error": True},
            )

            # 记录处理时间
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            metadata.processing_stats["processing_time_seconds"] = processing_time

            # 创建并返回带有错误信息的MarkdownContent
            return MarkdownContent(
                metadata=metadata,
                content=f"# Error Processing Text Content\n\nSource: {source_id}\n\nError: {str(e)}",
                error=str(e),
            )
