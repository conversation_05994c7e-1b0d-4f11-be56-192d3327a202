"""
DuckDuckGo搜索处理器模块

使用DuckDuckGo搜索引擎获取信息并处理为统一的Markdown格式
"""

from datetime import datetime
from typing import Any

from loguru import logger

from ..models import ContentMetadata, MarkdownContent
from ..resource_manager import ResourceManager
from .utils.web_fetch_utils import fetch_html

# 配置日志


class DuckDuckGoProcessor:
    """DuckDuckGo搜索处理器，使用DuckDuckGo搜索引擎获取搜索结果"""

    def __init__(self, config: dict[str, Any], resource_manager: ResourceManager = None):
        """
        初始化处理器

        Args:
            config: 配置参数
            resource_manager: 资源管理器实例，搜索处理器通常不需要
        """
        self.config = config
        self.resource_manager = resource_manager  # 保留但标记为可选
        self.source_type = "duckduckgo"

        # 默认搜索配置
        self.max_results = config.get("duckduckgo_max_results", 10)
        self.safesearch = config.get("duckduckgo_safesearch", "moderate")  # moderate, off, strict
        self.include_raw_content = config.get("duckduckgo_include_raw_content", False)

        # 检查是否安装了必要的库
        self._check_ddgs()

    def _check_ddgs(self) -> bool:
        """检查是否安装了duckduckgo_search库"""
        try:
            from duckduckgo_search import DDGS  # noqa: F401

            return True
        except ImportError:
            logger.error("未安装DuckDuckGo搜索库，请使用pip install duckduckgo_search安装")
            return False

    async def _fetch_raw_content(self, url: str) -> dict[str, Any]:
        """
        获取搜索结果链接的原始内容

        Args:
            url: 网页URL

        Returns:
            Dict: 获取结果
        """
        return fetch_html(url, use_javascript=False, timeout=20)

    def process(self, source_id: str, **kwargs) -> MarkdownContent:
        """
        处理搜索查询，返回统一的MarkdownContent

        Args:
            source_id: 搜索查询
            **kwargs: 其他参数，如max_results, safesearch, region, time_range等

        Returns:
            MarkdownContent: 处理后的统一内容
        """
        start_time = datetime.now()
        query = source_id  # 查询就是source_id

        try:
            # 检查是否安装了必要的库
            try:
                from duckduckgo_search import DDGS
            except ImportError:
                raise ImportError("未安装DuckDuckGo搜索库，请使用pip install duckduckgo_search安装")

            # 获取搜索参数
            max_results = kwargs.get("max_results", self.max_results)
            safesearch = kwargs.get("safesearch", self.safesearch)
            region = kwargs.get("region", "wt-wt")  # 默认为全球结果
            time_range = kwargs.get("time_range", None)  # d, w, m, y
            include_raw_content = kwargs.get("include_raw_content", self.include_raw_content)

            # 执行搜索
            ddgs = DDGS()
            results = list(
                ddgs.text(
                    query,
                    region=region,
                    safesearch=safesearch,
                    timelimit=time_range,
                    max_results=max_results,
                ),
            )

            # 如果需要获取原始内容
            if include_raw_content:
                # 为每个结果获取原始内容
                for result in results:
                    url = result.get("href")
                    if url:
                        try:
                            raw_result = fetch_html(url)
                            if raw_result.get("success", False):
                                result["raw_content"] = raw_result.get("content", "")
                                result["raw_title"] = raw_result.get("title", "")
                            else:
                                logger.warning(f"无法获取URL的原始内容: {url}, 错误: {raw_result.get('error')}")
                                result["raw_content"] = f"无法获取内容: {raw_result.get('error', '未知错误')}"
                        except Exception as e:
                            logger.error(f"获取原始内容时出错: {url} - {str(e)}")
                            result["raw_content"] = f"获取内容出错: {str(e)}"

            # 将结果转换为Markdown
            markdown_content = self._results_to_markdown(query, results, include_raw_content)

            # 创建元数据
            metadata = ContentMetadata(
                source_type=self.source_type,
                source_id=query,
                title=f"DuckDuckGo搜索结果: {query}",
                tags=["search", "duckduckgo"] + kwargs.get("tags", []),
                processing_stats={
                    "query": query,
                    "results_count": len(results),
                    "search_engine": "DuckDuckGo",
                    "include_raw_content": include_raw_content,
                    "search_params": {
                        "max_results": max_results,
                        "safesearch": safesearch,
                        "region": region,
                        "time_range": time_range,
                    },
                },
            )

            # 记录处理时间
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            metadata.processing_stats["processing_time_seconds"] = processing_time

            # 创建并返回MarkdownContent
            return MarkdownContent(
                metadata=metadata,
                content=markdown_content,
                resources=[],  # 搜索结果通常不包含需要下载的资源
            )

        except Exception as e:
            logger.error(f"搜索查询处理错误 '{query}': {str(e)}")

            # 创建错误报告
            metadata = ContentMetadata(
                source_type=self.source_type,
                source_id=query,
                title=f"搜索错误: {query}",
                processing_stats={"error": True},
            )

            # 记录处理时间
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            metadata.processing_stats["processing_time_seconds"] = processing_time

            # 创建并返回带有错误信息的MarkdownContent
            return MarkdownContent(
                metadata=metadata,
                content=f"# DuckDuckGo搜索错误\n\n查询: {query}\n\n错误: {str(e)}",
                error=str(e),
            )

    def _results_to_markdown(self, query: str, results: list[dict[str, Any]], include_raw_content: bool = False) -> str:
        """将搜索结果转换为Markdown格式"""
        if not results:
            return f"# DuckDuckGo搜索结果: {query}\n\n**未找到结果**"

        markdown = f"# DuckDuckGo搜索结果: {query}\n\n"
        markdown += f"*共找到 {len(results)} 条结果*\n\n"

        # 添加搜索时间
        search_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        markdown += f"*搜索时间: {search_time}*\n\n"

        # 添加结果列表
        for i, result in enumerate(results, 1):
            title = result.get("title", "无标题")
            url = result.get("href", "")
            snippet = result.get("body", "").strip()

            markdown += f"### {i}. [{title}]({url})\n\n"

            if snippet:
                markdown += f"{snippet}\n\n"

            markdown += f"**来源**: [{url}]({url})\n\n"

            # 如果包含原始内容，添加到结果中
            if include_raw_content and "raw_content" in result:
                markdown += "<details>\n<summary>原始网页内容</summary>\n\n"
                markdown += f"## {result.get('raw_title', title)}\n\n"
                markdown += f"{result.get('raw_content', '')}\n\n"
                markdown += "</details>\n\n"

            # 添加分隔线，但最后一个结果后不加
            if i < len(results):
                markdown += "---\n\n"

        return markdown
