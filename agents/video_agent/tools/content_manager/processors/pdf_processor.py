"""
PDF内容处理器模块

处理PDF文档，将其转换为统一的Markdown格式
"""

import hashlib
import os
from datetime import datetime
from pathlib import Path
from typing import Any

from loguru import logger

from ..models import ContentMetadata, ContentResource, MarkdownContent
from ..resource_manager import ResourceManager

# 初始化logger
logger = logger.bind(module="pdf_processor")


class PdfProcessor:
    """PDF处理器 - 直接使用docling处理PDF文件"""

    def __init__(self, config: dict[str, Any], resource_manager: ResourceManager):
        """
        初始化处理器

        Args:
            config: 配置参数
            resource_manager: 资源管理器实例
        """
        self.config = config
        self.resource_manager = resource_manager
        self.source_type = "pdf"

        # 设置输出目录
        self.output_dir = Path(config.get("pdf_output_dir") or config.get("cache_dir", "cache") + "/pdf_output")
        self.output_dir.mkdir(parents=True, exist_ok=True)

    def _is_url(self, source_id: str) -> bool:
        """判断源标识是否为URL"""
        return source_id.startswith(("http://", "https://"))

    def _get_cache_key(self, source_id: str) -> str:
        """生成PDF处理的缓存键"""
        return hashlib.md5(source_id.encode()).hexdigest()

    def _process_pdf(self, source_id: str, output_dir: str) -> dict[str, Any]:
        """
        使用docling处理PDF文件

        Args:
            source_id: PDF文件路径或URL
            output_dir: 输出目录

        Returns:
            Dict: 处理结果，包含Markdown内容和图片信息
        """
        try:
            # 导入依赖库
            try:
                import docling  # noqa: F401
                from docling.datamodel.base_models import InputFormat
                from docling.datamodel.pipeline_options import PdfPipelineOptions
                from docling.document_converter import DocumentConverter, PdfFormatOption
                from docling_core.types.doc import ImageRefMode, PictureItem, TableItem
            except ImportError as e:
                return {
                    "success": False,
                    "error": f"处理PDF需要docling库: {str(e)}",
                }

            logger.info(f"开始处理PDF: {source_id}")

            # 配置处理选项
            pipeline_options = PdfPipelineOptions()
            pipeline_options.images_scale = self.config.get("pdf_image_scale", 5.0)
            pipeline_options.generate_page_images = True
            pipeline_options.generate_picture_images = True

            # 创建转换器
            doc_converter = DocumentConverter(
                format_options={
                    InputFormat.PDF: PdfFormatOption(pipeline_options=pipeline_options),
                },
            )

            # 转换PDF(docling会自动处理URL)
            conv_res = doc_converter.convert(source_id)

            # 提取标题
            doc_filename = conv_res.input.file.stem
            title = doc_filename

            # 创建图片目录
            image_dir = Path(output_dir) / f"{doc_filename}_artifacts"
            image_dir.mkdir(parents=True, exist_ok=True)

            # 处理图片
            images = []
            for item, level in conv_res.document.iterate_items(with_groups=False):
                if isinstance(item, (PictureItem, TableItem)):
                    if item.image is None:
                        item.image = item.get_image(conv_res.document)
                        image = item.image
                    else:
                        image = item.image.pil_image

                    page_no = item.prov[0].page_no
                    prefix = "image" if isinstance(item, PictureItem) else "table"
                    img_path = image_dir / f"page_{page_no}_{prefix}_{len(images)}.png"

                    # 保存图片
                    image.save(img_path)
                    item.image.uri = str(img_path)

                    # 获取图片说明
                    caption = item.caption_text(conv_res.document)

                    # 添加图片信息
                    images.append(
                        {
                            "path": str(img_path),
                            "caption": caption,
                            "page": page_no,
                        },
                    )

            # 导出为Markdown
            markdown_content = conv_res.document.export_to_markdown(image_mode=ImageRefMode.REFERENCED)
            markdown_file = Path(output_dir) / f"{doc_filename}.md"
            with open(markdown_file, "w", encoding="utf-8") as f:
                f.write(markdown_content)

            logger.info(f"PDF处理完成: {source_id}, 转换了 {len(images)} 张图片")

            return {
                "success": True,
                "title": title,
                "content": markdown_content,
                "images": images,
                "markdown_file": str(markdown_file),
                "output_dir": output_dir,
            }

        except Exception as e:
            logger.error(f"处理PDF出错: {source_id} - {str(e)}")
            return {
                "success": False,
                "error": str(e),
            }

    def _create_resources_from_images(self, images: list[dict]) -> list[ContentResource]:
        """
        从图片信息创建ContentResource对象

        Args:
            images: 图片信息列表

        Returns:
            List[ContentResource]: 资源列表
        """
        resources = []

        for img_info in images:
            path = img_info.get("path", "")
            if not path or not os.path.exists(path):
                continue

            filename = os.path.basename(path)

            # 创建ContentResource对象
            resource = ContentResource(
                original_uri=f"file://{path}",
                resource_type="image",
                local_path=path,
                filename=filename,
                mime_type="image/png",  # 默认为PNG
                metadata={
                    "caption": img_info.get("caption", ""),
                    "page": img_info.get("page", 0),
                    "source": "pdf_extraction",
                },
            )

            resources.append(resource)

        return resources

    def process(self, source_id: str, **kwargs) -> MarkdownContent:
        """
        处理PDF文件并返回MarkdownContent

        Args:
            source_id: PDF文件路径或URL
            **kwargs: 其他参数

        Returns:
            MarkdownContent: 处理后的内容
        """
        start_time = datetime.now()

        try:
            # 确定输出目录
            custom_output_dir = kwargs.get("output_dir")
            if custom_output_dir:
                output_dir = custom_output_dir
            else:
                # 为每个PDF创建唯一的输出目录
                cache_key = self._get_cache_key(source_id)
                output_dir = str(self.output_dir / cache_key)
                os.makedirs(output_dir, exist_ok=True)

            # 处理PDF
            result = self._process_pdf(source_id, output_dir)

            if not result.get("success", False):
                raise Exception(result.get("error", "PDF内容提取失败"))

            # 获取内容
            markdown_content = result.get("content", "")
            title = result.get("title", "")
            images = result.get("images", [])

            # 处理图片资源
            resources = []
            if kwargs.get("extract_images", True):
                resources = self._create_resources_from_images(images)

            # 替换Markdown中的图片引用
            if resources:
                markdown_content = self.resource_manager.replace_markdown_references(
                    markdown_content,
                    resources,
                )

                # 将替换之后的markdown内容保存到文件
                if "markdown_file" in result:
                    with open(result.get("markdown_file"), "w", encoding="utf-8") as f:
                        f.write(markdown_content)
                else:
                    logger.warning("没有找到markdown文件路径，无法保存替换后的内容")

            # 创建元数据
            metadata = ContentMetadata(
                source_type=self.source_type,
                source_id=source_id,
                title=title,
                tags=kwargs.get("tags", []),
                processing_stats={
                    "images_count": len(images),
                    "resources_count": len(resources),
                    "markdown_file": result.get("markdown_file"),
                },
            )

            # 记录处理时间
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            metadata.processing_stats["processing_time_seconds"] = processing_time

            # 创建并返回MarkdownContent
            return MarkdownContent(
                metadata=metadata,
                content=markdown_content,
                resources=resources,
                cached_path=result.get("markdown_file"),
            )

        except Exception as e:
            logger.error(f"处理PDF失败: {source_id} - {str(e)}")

            # 创建错误元数据
            metadata = ContentMetadata(
                source_type=self.source_type,
                source_id=source_id,
                title="PDF处理错误",
                processing_stats={"error": True},
            )

            # 记录处理时间
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            metadata.processing_stats["processing_time_seconds"] = processing_time

            # 创建并返回错误内容
            return MarkdownContent(
                metadata=metadata,
                content=f"# PDF处理错误\n\n**源**: {source_id}\n\n**错误**: {str(e)}",
                error=str(e),
            )
