"""
搜索内容处理器模块

使用Tavily API处理搜索请求，将搜索结果转换为统一的Markdown格式
"""

import json
import os
from datetime import datetime
from typing import Any, Optional

from loguru import logger

from ..models import ContentMetadata, MarkdownContent
from ..resource_manager import ResourceManager

# 初始化logger
logger = logger.bind(module="tavily_processor")


class TavilyProcessor:
    """搜索处理器 - 直接实现搜索功能"""

    def __init__(self, config: dict[str, Any], resource_manager: ResourceManager):
        """
        初始化处理器

        Args:
            config: 配置参数
            resource_manager: 资源管理器实例
        """
        self.config = config
        self.resource_manager = resource_manager
        self.source_type = "search"

        # 初始化Tavily客户端
        self.tavily_client = self._init_tavily_client()

        # 设置存储目录
        self.save_dir = config.get("search_save_dir") or os.path.join(
            config.get("cache_dir", "cache"),
            "search_results",
        )
        os.makedirs(self.save_dir, exist_ok=True)

    def _init_tavily_client(self) -> Optional[Any]:
        """
        初始化Tavily客户端

        Returns:
            Optional[Any]: Tavily客户端实例或None
        """
        api_key = self.config.get("tavily_api_key")
        if not api_key:
            logger.warning("Tavily API密钥未设置，搜索功能将不可用")
            return None

        try:
            from tavily import TavilyClient

            return TavilyClient(api_key=api_key)
        except ImportError as e:
            logger.error(f"初始化Tavily客户端出错: {e}")
            logger.error("请安装tavily-python: pip install tavily-python")
            return None

    def _perform_search(self, query: str, **kwargs) -> dict[str, Any]:
        """
        执行搜索

        Args:
            query: 搜索查询
            **kwargs: 搜索参数

        Returns:
            Dict: 搜索结果
        """
        if not self.tavily_client:
            return {
                "success": False,
                "error": "Tavily客户端未初始化，请确保提供有效的API密钥",
            }

        try:
            # 准备搜索参数
            search_params = {
                "query": query,
                "search_depth": kwargs.get("search_depth", "basic"),
                "max_results": kwargs.get("max_results", 5),
                "include_answer": kwargs.get("include_answer", True),
                "include_raw_content": kwargs.get("include_raw_content", True),
                "include_images": kwargs.get("include_images", False),
            }

            # 添加可选参数
            if "include_domains" in kwargs:
                search_params["include_domains"] = kwargs["include_domains"]
            if "exclude_domains" in kwargs:
                search_params["exclude_domains"] = kwargs["exclude_domains"]

            # 执行搜索
            search_response = self.tavily_client.search(**search_params)

            # 保存搜索结果
            query_hash = self._get_query_hash(query)
            result_path = os.path.join(self.save_dir, f"{query_hash}.json")
            with open(result_path, "w", encoding="utf-8") as f:
                json.dump(search_response, f, indent=2, ensure_ascii=False)

            # 添加元数据
            search_response["metadata"] = {
                "query": query,
                "search_depth": search_params["search_depth"],
                "api_credits_used": 1 if search_params["search_depth"] == "basic" else 2,
                "timestamp": datetime.now().isoformat(),
                "result_path": result_path,
            }

            search_response["success"] = True
            return search_response

        except Exception as e:
            logger.error(f"执行搜索出错: {query} - {str(e)}")
            return {
                "success": False,
                "error": str(e),
            }

    def _get_query_hash(self, query: str) -> str:
        """
        生成查询的哈希值

        Args:
            query: 搜索查询

        Returns:
            str: 哈希值
        """
        import hashlib

        return hashlib.md5(query.encode()).hexdigest()

    def _format_search_results(self, results: dict[str, Any], query: str) -> str:
        """
        将搜索结果格式化为Markdown

        Args:
            results: 搜索结果
            query: 搜索查询

        Returns:
            str: Markdown格式的搜索结果
        """
        # 创建Markdown内容
        markdown_parts = []

        # 添加标题
        markdown_parts.append(f"# 搜索结果: {query}\n")

        # 添加回答（如果有）
        if "answer" in results and results["answer"]:
            markdown_parts.append("## 搜索回答\n")
            markdown_parts.append(f"{results['answer']}\n\n")

        # 添加搜索结果
        search_results = results.get("results", [])
        if search_results:
            markdown_parts.append("## 搜索结果\n")

            for i, result in enumerate(search_results, 1):
                title = result.get("title", "未知标题")
                url = result.get("url", "")
                content = result.get("content", "")
                raw_content = result.get("raw_content", "")

                markdown_parts.append(f"### {i}. {title}\n")
                markdown_parts.append(f"**来源**: [{url}]({url})\n\n")
                markdown_parts.append(f"{content}\n\n")
                if raw_content:
                    markdown_parts.append(f"**网页原始内容**:\n\n{raw_content}\n\n")
                markdown_parts.append(f"[查看完整内容]({url})\n\n")
        else:
            markdown_parts.append("## 无搜索结果\n")
            markdown_parts.append("未找到相关内容。\n\n")

        # 添加元数据
        markdown_parts.append("## 搜索信息\n")
        search_depth = results.get("metadata", {}).get("search_depth", "basic")
        api_credits = results.get("metadata", {}).get("api_credits_used", 1)
        markdown_parts.append(f"- **搜索深度**: {search_depth}\n")
        markdown_parts.append(f"- **结果数量**: {len(search_results)}\n")
        markdown_parts.append(f"- **API积分使用**: {api_credits}\n")

        return "\n".join(markdown_parts)

    def process(self, source_id: str, **kwargs) -> MarkdownContent:
        """
        处理搜索查询并返回结果

        Args:
            source_id: 搜索查询
            **kwargs: 搜索参数

        Returns:
            MarkdownContent: 处理后的内容
        """
        start_time = datetime.now()
        query = source_id

        try:
            # 执行搜索
            search_results = self._perform_search(query, **kwargs)

            if not search_results.get("success", False):
                raise Exception(search_results.get("error", "搜索请求失败"))

            # 格式化为Markdown
            markdown_content = self._format_search_results(search_results, query)

            # 获取结果数量
            results_count = len(search_results.get("results", []))

            # 创建元数据
            metadata = ContentMetadata(
                source_type=self.source_type,
                source_id=query,
                title=f"搜索: {query}",
                tags=kwargs.get("tags", ["search", "tavily"]),
                processing_stats={
                    "results_count": results_count,
                    "search_depth": kwargs.get("search_depth", "basic"),
                    "api_credits_used": search_results.get("metadata", {}).get("api_credits_used", 1),
                    "result_path": search_results.get("metadata", {}).get("result_path"),
                },
            )

            # 记录处理时间
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            metadata.processing_stats["processing_time_seconds"] = processing_time

            # 创建并返回MarkdownContent
            return MarkdownContent(
                metadata=metadata,
                content=markdown_content,
                resources=[],  # 搜索结果通常不包含需要下载的资源
            )

        except Exception as e:
            logger.error(f"处理搜索查询失败: {query} - {str(e)}")

            # 创建错误元数据
            metadata = ContentMetadata(
                source_type=self.source_type,
                source_id=query,
                title="搜索错误",
                processing_stats={"error": True},
            )

            # 记录处理时间
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            metadata.processing_stats["processing_time_seconds"] = processing_time

            # 创建并返回错误内容
            return MarkdownContent(
                metadata=metadata,
                content=f"# 搜索错误\n\n**查询**: {query}\n\n**错误**: {str(e)}",
                error=str(e),
            )
