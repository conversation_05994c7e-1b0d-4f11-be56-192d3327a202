"""
SearXNG搜索处理器模块

使用SearXNG搜索引擎获取信息并处理为统一的Markdown格式
"""

from datetime import datetime
from typing import Any

import requests
from loguru import logger

from ..models import ContentMetadata, MarkdownContent
from ..resource_manager import ResourceManager
from .utils.web_fetch_utils import fetch_html


class SearXNGProcessor:
    """SearXNG搜索处理器，使用SearXNG搜索引擎获取搜索结果"""

    def __init__(self, config: dict[str, Any], resource_manager: ResourceManager = None):
        """
        初始化处理器

        Args:
            config: 配置参数
            resource_manager: 资源管理器实例，搜索处理器通常不需要
        """
        self.config = config
        self.resource_manager = resource_manager  # 保留但标记为可选
        self.source_type = "searxng"

        # SearXNG配置
        self.instance = config.get("searxng_instance", "https://searx.be")
        self.max_results = config.get("searxng_max_results", 10)
        self.timeout = config.get("searxng_timeout", 30)
        self.categories = config.get("searxng_categories", ["general"])
        self.include_raw_content = config.get("searxng_include_raw_content", False)

    def process(self, source_id: str, **kwargs) -> MarkdownContent:
        """
        处理搜索查询，返回统一的MarkdownContent

        Args:
            source_id: 搜索查询
            **kwargs: 其他参数，如instance, max_results, categories, language等

        Returns:
            MarkdownContent: 处理后的统一内容
        """
        start_time = datetime.now()
        query = source_id  # 查询就是source_id

        try:
            # 获取搜索参数
            instance = kwargs.get("instance", self.instance)
            max_results = kwargs.get("max_results", self.max_results)
            categories = kwargs.get("categories", self.categories)
            language = kwargs.get("language", "all")
            time_range = kwargs.get("time_range", None)  # day, week, month, year
            include_raw_content = kwargs.get("include_raw_content", self.include_raw_content)

            # 执行搜索
            try:
                # 构建请求参数
                params = {
                    "q": query,
                    "format": "json",
                    "categories": ",".join(categories),
                    "language": language,
                    "time_range": time_range or "",
                    "pageno": 1,
                }

                # 发送请求
                response = requests.get(
                    f"{instance}/search",
                    params=params,
                    headers={"User-Agent": "Mozilla/5.0"},
                    timeout=self.timeout,
                )
                response.raise_for_status()

                # 解析响应
                data = response.json()
                results = data.get("results", [])

                # 限制结果数量
                results = results[:max_results]

            except requests.RequestException as e:
                logger.error(f"SearXNG请求错误: {e}")
                raise

            # 如果需要获取原始内容
            if include_raw_content:
                # 为每个结果获取原始内容
                for result in results:
                    url = result.get("url")
                    if url:
                        try:
                            raw_result = fetch_html(url)
                            if raw_result.get("success", False):
                                result["raw_content"] = raw_result.get("content", "")
                                result["raw_title"] = raw_result.get("title", "")
                            else:
                                logger.warning(f"无法获取URL的原始内容: {url}, 错误: {raw_result.get('error')}")
                                result["raw_content"] = f"无法获取内容: {raw_result.get('error', '未知错误')}"
                        except Exception as e:
                            logger.error(f"获取原始内容时出错: {url} - {str(e)}")
                            result["raw_content"] = f"获取内容出错: {str(e)}"

            # 将结果转换为Markdown
            markdown_content = self._results_to_markdown(query, results, include_raw_content)

            # 创建元数据
            metadata = ContentMetadata(
                source_type=self.source_type,
                source_id=query,
                title=f"SearXNG搜索结果: {query}",
                tags=["search", "searxng"] + kwargs.get("tags", []),
                processing_stats={
                    "query": query,
                    "results_count": len(results),
                    "search_engine": "SearXNG",
                    "include_raw_content": include_raw_content,
                    "instance": instance,
                    "search_params": {
                        "max_results": max_results,
                        "categories": categories,
                        "language": language,
                        "time_range": time_range,
                    },
                },
            )

            # 记录处理时间
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            metadata.processing_stats["processing_time_seconds"] = processing_time

            # 创建并返回MarkdownContent
            return MarkdownContent(
                metadata=metadata,
                content=markdown_content,
                resources=[],  # 搜索结果通常不包含需要下载的资源
            )

        except Exception as e:
            logger.error(f"搜索查询处理错误 '{query}': {str(e)}")

            # 创建错误报告
            metadata = ContentMetadata(
                source_type=self.source_type,
                source_id=query,
                title=f"搜索错误: {query}",
                processing_stats={"error": True},
            )

            # 记录处理时间
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            metadata.processing_stats["processing_time_seconds"] = processing_time

            # 创建并返回带有错误信息的MarkdownContent
            return MarkdownContent(
                metadata=metadata,
                content=f"# SearXNG搜索错误\n\n查询: {query}\n\n错误: {str(e)}",
                error=str(e),
            )

    def _results_to_markdown(self, query: str, results: list[dict[str, Any]], include_raw_content: bool = False) -> str:
        """将搜索结果转换为Markdown格式"""
        if not results:
            return f"# SearXNG搜索结果: {query}\n\n**未找到结果**"

        markdown = f"# SearXNG搜索结果: {query}\n\n"
        markdown += f"*共找到 {len(results)} 条结果*\n\n"

        # 添加搜索时间
        search_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        markdown += f"*搜索时间: {search_time}*\n\n"

        # 添加结果列表
        for i, result in enumerate(results, 1):
            title = result.get("title", "无标题")
            url = result.get("url", "")
            snippet = result.get("content", "").strip()
            engine = result.get("engine", "未知")

            markdown += f"### {i}. [{title}]({url})\n\n"

            if snippet:
                markdown += f"{snippet}\n\n"

            markdown += f"**来源**: [{url}]({url}) | **搜索引擎**: {engine}\n\n"

            # 如果包含原始内容，添加到结果中
            if include_raw_content and "raw_content" in result:
                markdown += "<details>\n<summary>原始网页内容</summary>\n\n"
                markdown += f"## {result.get('raw_title', title)}\n\n"
                markdown += f"{result.get('raw_content', '')}\n\n"
                markdown += "</details>\n\n"

            # 添加分隔线，但最后一个结果后不加
            if i < len(results):
                markdown += "---\n\n"

        return markdown
