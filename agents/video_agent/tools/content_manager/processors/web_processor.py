"""
网页内容处理器模块

处理网页内容，将其转换为统一的Markdown格式
"""

from datetime import datetime
from typing import Any

from loguru import logger

from ..models import ContentMetadata, ContentResource, MarkdownContent
from ..resource_manager import ResourceManager
from .utils.web_fetch_utils import fetch_html

# 初始化logger
logger = logger.bind(module="web_processor")


class WebProcessor:
    """网页处理器 - 直接实现网页获取和处理功能"""

    def __init__(self, config: dict[str, Any], resource_manager: ResourceManager):
        """
        初始化处理器

        Args:
            config: 配置参数
            resource_manager: 资源管理器实例
        """
        self.config = config
        self.resource_manager = resource_manager
        self.source_type = "web"
        self.use_javascript = config.get("use_javascript", True)

    def fetch_html(self, url: str, **kwargs) -> dict[str, Any]:
        """
        获取网页HTML内容

        Args:
            url: 网页URL
            **kwargs: 其他参数

        Returns:
            Dict: 获取结果
        """
        # 使用共享的web_fetch_utils获取HTML内容
        return fetch_html(
            url,
            use_javascript=self.use_javascript,
            extract_images=kwargs.pop("extract_images", True),
            **kwargs,
        )

    def _download_images(self, images: list[dict[str, str]], base_url: str) -> list[ContentResource]:
        """
        下载图片资源

        Args:
            images: 图片信息列表
            base_url: 基础URL

        Returns:
            List[ContentResource]: 下载的资源列表
        """
        resources = []

        for img in images:
            url = img.get("url")
            if not url:
                continue

            # 下载图片
            try:
                resource = self.resource_manager.download_resource(url, "image")
                if resource.local_path:  # 如果下载成功
                    resources.append(resource)
            except Exception as e:
                logger.error(f"下载图片失败: {url} - {str(e)}")

        return resources

    def process(self, source_id: str, **kwargs) -> MarkdownContent:
        """
        处理网页内容并返回MarkdownContent

        Args:
            source_id: 网页URL
            **kwargs: 其他参数

        Returns:
            MarkdownContent: 处理后的内容
        """
        start_time = datetime.now()
        url = source_id

        try:
            # 提取参数
            use_javascript = kwargs.get("use_javascript", True)
            timeout = kwargs.get("timeout", 30)
            wait_time = kwargs.get("wait_time", 3)

            # 暂存当前设置并根据参数修改
            original_setting = self.use_javascript
            self.use_javascript = use_javascript

            try:
                # 获取网页内容
                result = self.fetch_html(
                    url=url,
                    timeout=timeout,
                    wait_time=wait_time,
                    wait_for_selector=kwargs.get("wait_for_selector"),
                    extract_images=True,
                )
            finally:
                # 恢复原始设置
                self.use_javascript = original_setting

            if not result.get("success", False):
                raise Exception(result.get("error", "获取网页内容失败"))

            # 获取内容
            markdown_content = result.get("content", "")
            title = result.get("title", "")
            images = result.get("images", [])

            # 下载图片资源
            resources = self._download_images(images, url)

            # 替换图片引用
            if resources:
                markdown_content = self.resource_manager.replace_markdown_references(
                    markdown_content,
                    resources,
                )

            # 尝试提取更多资源
            if kwargs.get("extract_all_resources", False) and "raw_html" in result:
                additional_resources = self.resource_manager.extract_resources_from_html(
                    result["raw_html"],
                    url,
                )
                resources.extend(additional_resources)

                # 再次替换引用
                if additional_resources:
                    markdown_content = self.resource_manager.replace_markdown_references(
                        markdown_content,
                        additional_resources,
                    )

            # 创建元数据
            metadata = ContentMetadata(
                source_type=self.source_type,
                source_id=url,
                title=title,
                tags=kwargs.get("tags", []),
                processing_stats={
                    "images_count": len(images),
                    "resources_count": len(resources),
                    "js_rendered": use_javascript,
                },
            )

            # 记录处理时间
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            metadata.processing_stats["processing_time_seconds"] = processing_time

            # 创建并返回结果
            return MarkdownContent(
                metadata=metadata,
                content=markdown_content,
                resources=resources,
            )

        except Exception as e:
            logger.error(f"处理网页失败: {url} - {str(e)}")

            # 创建错误元数据
            metadata = ContentMetadata(
                source_type=self.source_type,
                source_id=url,
                title="网页处理错误",
                processing_stats={"error": True},
            )

            # 记录处理时间
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            metadata.processing_stats["processing_time_seconds"] = processing_time

            # 创建并返回错误内容
            return MarkdownContent(
                metadata=metadata,
                content=f"# 网页处理错误\n\n**URL**: {url}\n\n**错误**: {str(e)}",
                error=str(e),
            )
