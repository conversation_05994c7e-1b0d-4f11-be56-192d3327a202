"""
内容处理器包

提供各种类型内容的处理器实现
"""

from typing import Any, Protocol, runtime_checkable

from loguru import logger

from ..models import MarkdownContent
from .duckduckgo_processor import DuckDuckGoProcessor
from .pdf_processor import PdfProcessor
from .searxng_processor import SearXNGProcessor
from .tavily_processor import TavilyProcessor
from .text_processor import TextProcessor
from .web_processor import WebProcessor

# 初始化logger
logger = logger.bind(module="processors")


@runtime_checkable
class ContentProcessor(Protocol):
    """内容处理器接口 - 所有处理器必须实现的接口"""

    source_type: str  # 处理器支持的内容类型标识符

    def __init__(self, config: dict[str, Any], resource_manager: Any):
        """
        初始化处理器

        Args:
            config: 配置参数
            resource_manager: 资源管理器实例
        """
        ...

    def process(self, source_id: str, **kwargs) -> MarkdownContent:
        """
        处理指定来源的内容，返回统一的MarkdownContent

        Args:
            source_id: 内容来源标识 (URL, 文件路径, 搜索查询等)
            **kwargs: 处理器特定的参数

        Returns:
            MarkdownContent: 处理后的统一内容
        """
        ...


__all__ = [
    "ContentProcessor",
    "TextProcessor",
    "WebProcessor",
    "PdfProcessor",  # 注意这里使用新的类名PdfProcessor
    "TavilyProcessor",
    "DuckDuckGoProcessor",
    "SearXNGProcessor",
    "logger",
]
