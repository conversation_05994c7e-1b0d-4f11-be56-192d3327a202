"""
统一内容管理系统的数据模型

简化的数据模型，以Markdown为中心，集成缓存管理功能
"""

import hashlib
from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Optional


@dataclass
class ContentResource:
    """表示内容中的媒体资源（图片、视频等）"""

    original_uri: str  # 原始URI
    resource_type: str  # 资源类型: "image", "video", "audio" 等
    local_path: str  # 本地存储路径
    filename: str  # 文件名
    mime_type: Optional[str] = None  # MIME类型
    metadata: dict[str, Any] = field(default_factory=dict)  # 额外元数据（尺寸、格式等）


@dataclass
class ContentMetadata:
    """内容的元数据信息"""

    source_type: str  # 来源类型: "web", "pdf", "text", "api", "search" 等
    source_id: str  # 来源标识: URL, 文件路径, 或自定义ID
    timestamp: datetime = field(default_factory=datetime.now)  # 处理时间戳
    title: Optional[str] = None  # 内容标题
    tags: list[str] = field(default_factory=list)  # 标签
    processing_stats: dict[str, Any] = field(default_factory=dict)  # 处理统计信息

    @property
    def hash_key(self) -> str:
        """生成唯一的哈希键，用于缓存标识"""
        key_str = f"{self.source_type}:{self.source_id}"
        return hashlib.md5(key_str.encode("utf-8")).hexdigest()


@dataclass
class MarkdownContent:
    """简化的统一内容模型, 以Markdown为中心"""

    metadata: ContentMetadata  # 元数据
    content: str  # Markdown格式的内容
    resources: list[ContentResource] = field(default_factory=list)  # 嵌入的媒体资源
    cached_path: Optional[str] = None  # 缓存的Markdown文件路径
    error: Optional[str] = None  # 错误信息（如有）

    def to_dict(self) -> dict[str, Any]:
        """将内容转换为字典形式"""
        return {
            "metadata": {
                "source_type": self.metadata.source_type,
                "source_id": self.metadata.source_id,
                "timestamp": self.metadata.timestamp.isoformat(),
                "title": self.metadata.title,
                "tags": self.metadata.tags,
                "processing_stats": self.metadata.processing_stats,
                "hash_key": self.metadata.hash_key,
            },
            "content": self.content,
            "resources": [
                {
                    "original_uri": res.original_uri,
                    "resource_type": res.resource_type,
                    "local_path": res.local_path,
                    "filename": res.filename,
                    "mime_type": res.mime_type,
                    "metadata": res.metadata,
                }
                for res in self.resources
            ],
            "cached_path": self.cached_path,
            "error": self.error,
        }

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "MarkdownContent":
        """从字典创建MarkdownContent对象"""
        metadata_dict = data.get("metadata", {})
        # 创建ContentMetadata对象
        metadata = ContentMetadata(
            source_type=metadata_dict.get("source_type", "unknown"),
            source_id=metadata_dict.get("source_id", "unknown"),
            title=metadata_dict.get("title"),
            tags=metadata_dict.get("tags", []),
            processing_stats=metadata_dict.get("processing_stats", {}),
        )

        # 如果存在时间戳，转换为datetime对象
        if "timestamp" in metadata_dict:
            try:
                metadata.timestamp = datetime.fromisoformat(metadata_dict["timestamp"])
            except (ValueError, TypeError):
                # 如果解析失败，使用当前时间
                metadata.timestamp = datetime.now()

        # 创建资源列表
        resources = []
        for res_dict in data.get("resources", []):
            resource = ContentResource(
                original_uri=res_dict.get("original_uri", ""),
                resource_type=res_dict.get("resource_type", "unknown"),
                local_path=res_dict.get("local_path", ""),
                filename=res_dict.get("filename", ""),
                mime_type=res_dict.get("mime_type"),
                metadata=res_dict.get("metadata", {}),
            )
            resources.append(resource)

        # 创建并返回MarkdownContent对象
        return cls(
            metadata=metadata,
            content=data.get("content", ""),
            resources=resources,
            cached_path=data.get("cached_path"),
            error=data.get("error"),
        )
