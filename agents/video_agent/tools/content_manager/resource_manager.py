"""
资源管理器模块

处理媒体资源（图片、视频等）的下载、存储和引用管理
"""

import hashlib
import mimetypes
import os
import re
import uuid
from pathlib import Path
from typing import Any, Optional
from urllib.parse import urljoin, urlparse

import requests
from bs4 import BeautifulSoup
from loguru import logger

from .models import ContentResource

logger = logger.bind(module="resource_manager")


class ResourceManager:
    """资源管理器 - 专注于资源下载和管理"""

    def __init__(self, config: dict[str, Any]):
        """
        初始化资源管理器

        Args:
            config: 配置参数，包含资源目录等设置
        """
        self.config = config
        self.resource_dir = Path(config.get("resource_dir", "cache/resources"))
        self.image_dir = Path(config.get("image_dir", "cache/resources/images"))
        self.video_dir = Path(config.get("video_dir", "cache/resources/videos"))
        self.audio_dir = Path(config.get("audio_dir", "cache/resources/audio"))

        # 创建资源目录
        self.resource_dir.mkdir(parents=True, exist_ok=True)
        self.image_dir.mkdir(parents=True, exist_ok=True)
        self.video_dir.mkdir(parents=True, exist_ok=True)
        self.audio_dir.mkdir(parents=True, exist_ok=True)

    def _get_resource_dir(self, resource_type: str) -> Path:
        """获取指定资源类型的目录路径"""
        if resource_type == "image":
            return self.image_dir
        elif resource_type == "video":
            return self.video_dir
        elif resource_type == "audio":
            return self.audio_dir
        else:
            # 对于未知类型，使用通用资源目录
            other_dir = self.resource_dir / resource_type
            other_dir.mkdir(exist_ok=True)
            return other_dir

    def _get_mime_type(self, url: str, content: Optional[bytes] = None) -> str:
        """尝试确定资源的MIME类型"""
        # 先从URL路径猜测
        mime_type, _ = mimetypes.guess_type(url)

        # 如果无法从URL确定，尝试从内容确定（如果有）
        if mime_type is None and content:
            # 根据内容特征判断类型
            # 这里只做简单判断，实际实现可能需要更复杂的逻辑
            if content.startswith(b"\xff\xd8\xff"):
                mime_type = "image/jpeg"
            elif content.startswith(b"\x89PNG\r\n\x1a\n"):
                mime_type = "image/png"
            elif content.startswith(b"GIF87a") or content.startswith(b"GIF89a"):
                mime_type = "image/gif"

        return mime_type or "application/octet-stream"

    def _generate_filename(self, url: str, mime_type: Optional[str] = None) -> str:
        """生成唯一的文件名"""
        # 从URL提取原始文件名
        parsed_url = urlparse(url)
        path = parsed_url.path
        base_name = os.path.basename(path)

        # 如果URL中没有有效的文件名，生成一个
        if not base_name or base_name.endswith("/"):
            # 生成一个基于URL的哈希和一个随机UUID
            url_hash = hashlib.md5(url.encode("utf-8")).hexdigest()[:8]
            random_id = uuid.uuid4().hex[:4]

            # 根据MIME类型确定扩展名
            extension = mimetypes.guess_extension(mime_type) if mime_type else ".bin"
            if extension is None:
                extension = ".bin"

            base_name = f"resource_{url_hash}_{random_id}{extension}"

        return base_name

    def download_resource(self, uri: str, resource_type: str) -> ContentResource:
        """
        下载资源并返回ContentResource对象

        Args:
            uri: 资源URI
            resource_type: 资源类型 ("image", "video", "audio" 等)

        Returns:
            ContentResource: 下载的资源对象
        """
        try:
            # 检查URI是否是本地文件路径
            parsed_uri = urlparse(uri)
            if parsed_uri.scheme in ("", "file"):
                # 本地文件，获取元数据但不复制
                local_path = uri if parsed_uri.scheme == "" else parsed_uri.path
                filename = os.path.basename(local_path)
                mime_type, _ = mimetypes.guess_type(local_path)

                resource = ContentResource(
                    original_uri=uri,
                    resource_type=resource_type,
                    local_path=local_path,
                    filename=filename,
                    mime_type=mime_type,
                    metadata={"is_local": True},
                )
                return resource

            # 发起HTTP请求下载资源
            response = requests.get(uri, stream=True, timeout=30)
            response.raise_for_status()

            # 获取内容类型
            content_type = response.headers.get("Content-Type")

            # 生成文件名
            filename = self._generate_filename(uri, content_type)

            # 确定资源目录
            resource_dir = self._get_resource_dir(resource_type)
            local_path = str(resource_dir / filename)

            # 保存资源到本地
            with open(local_path, "wb") as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            # 获取文件大小
            file_size = os.path.getsize(local_path)

            # 创建并返回ContentResource对象
            resource = ContentResource(
                original_uri=uri,
                resource_type=resource_type,
                local_path=local_path,
                filename=filename,
                mime_type=content_type,
                metadata={
                    "size_bytes": file_size,
                    "source": "download",
                },
            )

            logger.info(f"Downloaded resource: {uri} -> {local_path}")
            return resource

        except Exception as e:
            logger.error(f"Failed to download resource {uri}: {str(e)}")
            # 创建一个表示错误的资源对象
            return ContentResource(
                original_uri=uri,
                resource_type=resource_type,
                local_path="",
                filename="",
                mime_type=None,
                metadata={"error": str(e)},
            )

    def extract_resources_from_html(self, html: str, base_url: str) -> list[ContentResource]:
        """
        从HTML中提取和下载所有资源

        Args:
            html: HTML内容
            base_url: 基础URL，用于解析相对路径

        Returns:
            List[ContentResource]: 下载的资源列表
        """
        resources = []
        soup = BeautifulSoup(html, "html.parser")

        # 提取所有图片
        for img in soup.find_all("img"):
            src = img.get("src")
            if not src:
                continue

            # 处理相对URL
            if not src.startswith(("http://", "https://")):
                src = urljoin(base_url, src)

            # 下载图片
            try:
                resource = self.download_resource(src, "image")
                if resource.local_path:  # 如果下载成功
                    resources.append(resource)
            except Exception as e:
                logger.error(f"Error downloading image {src}: {str(e)}")

        # 提取视频资源
        for video in soup.find_all("video"):
            src = video.get("src")
            if src:
                # 处理相对URL
                if not src.startswith(("http://", "https://")):
                    src = urljoin(base_url, src)

                # 下载视频
                try:
                    resource = self.download_resource(src, "video")
                    if resource.local_path:
                        resources.append(resource)
                except Exception as e:
                    logger.error(f"Error downloading video {src}: {str(e)}")

            # 处理video标签内的source标签
            for source in video.find_all("source"):
                src = source.get("src")
                if not src:
                    continue

                # 处理相对URL
                if not src.startswith(("http://", "https://")):
                    src = urljoin(base_url, src)

                # 下载视频
                try:
                    resource = self.download_resource(src, "video")
                    if resource.local_path:
                        resources.append(resource)
                except Exception as e:
                    logger.error(f"Error downloading video source {src}: {str(e)}")

        # 提取音频资源
        for audio in soup.find_all("audio"):
            src = audio.get("src")
            if src:
                # 处理相对URL
                if not src.startswith(("http://", "https://")):
                    src = urljoin(base_url, src)

                # 下载音频
                try:
                    resource = self.download_resource(src, "audio")
                    if resource.local_path:
                        resources.append(resource)
                except Exception as e:
                    logger.error(f"Error downloading audio {src}: {str(e)}")

            # 处理audio标签内的source标签
            for source in audio.find_all("source"):
                src = source.get("src")
                if not src:
                    continue

                # 处理相对URL
                if not src.startswith(("http://", "https://")):
                    src = urljoin(base_url, src)

                # 下载音频
                try:
                    resource = self.download_resource(src, "audio")
                    if resource.local_path:
                        resources.append(resource)
                except Exception as e:
                    logger.error(f"Error downloading audio source {src}: {str(e)}")

        logger.info(f"Extracted {len(resources)} resources from HTML")
        return resources

    def replace_markdown_references(self, markdown: str, resources: list[ContentResource]) -> str:
        """
        替换Markdown中的资源引用为本地路径

        Args:
            markdown: Markdown内容
            resources: 资源列表

        Returns:
            str: 替换后的Markdown内容
        """
        # 创建URL到本地路径的映射
        url_to_path = {}
        for resource in resources:
            if resource.local_path:  # 只包含成功下载的资源
                original_uri = resource.original_uri
                local_path = resource.local_path

                # 添加标准URL映射
                url_to_path[original_uri] = local_path

                # 处理file://前缀
                if original_uri.startswith("file://"):
                    url_to_path[original_uri[7:]] = local_path

                # 添加文件名映射（处理相对路径情况）
                filename = os.path.basename(original_uri)
                if filename:
                    url_to_path[filename] = local_path

        # 替换Markdown中的资源引用
        result = markdown
        for url, path in url_to_path.items():
            # 替换图片引用: ![alt text](url)
            pattern = r"!\[(.*?)\]\(" + re.escape(url) + r"\)"
            replacement = f"![\\1]({path})"
            result = re.sub(pattern, replacement, result)

            # 替换视频引用: [video](url)
            pattern = r"\[video\]\(" + re.escape(url) + r"\)"
            replacement = f"[video]({path})"
            result = re.sub(pattern, replacement, result)

            # 替换音频引用: [audio](url)
            pattern = r"\[audio\]\(" + re.escape(url) + r"\)"
            replacement = f"[audio]({path})"
            result = re.sub(pattern, replacement, result)

        return result

    def get_resource_path(self, resource: ContentResource) -> str:
        """
        获取资源的本地路径

        Args:
            resource: ContentResource对象

        Returns:
            str: 资源的本地路径
        """
        return resource.local_path
