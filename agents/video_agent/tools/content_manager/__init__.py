"""
统一内容管理系统

简化的内容收集、处理和缓存系统，以Markdown为中心
"""

from typing import Any

from .collector import ContentCollector
from .models import ContentMetadata, ContentResource, MarkdownContent
from .processors import ContentProcessor, PdfProcessor, TavilyProcessor, TextProcessor, WebProcessor

__all__ = [
    "MarkdownContent",
    "ContentMetadata",
    "ContentResource",
    "ContentCollector",
    "ContentProcessor",
    "TextProcessor",
    "WebProcessor",
    "PdfProcessor",
    "TavilyProcessor",
    "create_collector",
]


# 创建默认收集器实例的便捷函数
def create_collector(config: dict[str, Any] = None) -> ContentCollector:
    """
    创建一个配置好的ContentCollector实例

    Args:
        config: 可选的配置字典，将覆盖默认配置

    Returns:
        ContentCollector: 配置好的内容收集器实例
    """
    return ContentCollector(config)
