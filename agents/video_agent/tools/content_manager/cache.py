"""
简化的缓存系统

基于文件系统的缓存实现，用于存储Markdown内容和资源文件
"""

import json
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Optional

from loguru import logger

from .models import MarkdownContent

# 配置日志
logger = logger.bind(module="content_cache")


class ContentCache:
    """简化的内容缓存系统"""

    def __init__(self, config: dict[str, Any]):
        """
        初始化缓存系统

        Args:
            config: 缓存配置，包括缓存目录和TTL设置
        """
        self.config = config
        self.cache_dir = Path(config.get("cache_dir", "cache"))
        self.content_dir = Path(config.get("content_dir", "cache/content"))
        self.cache_ttl = config.get("cache_ttl", 86400 * 7)  # 默认7天
        self.index_path = self.cache_dir / "index.json"

        # 创建目录
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.content_dir.mkdir(parents=True, exist_ok=True)

        # 加载或初始化索引
        self.index = self._load_index()

    def _load_index(self) -> dict[str, Any]:
        """加载缓存索引文件"""
        if self.index_path.exists():
            try:
                with open(self.index_path, encoding="utf-8") as f:
                    return json.load(f)
            except (json.JSONDecodeError, OSError) as e:
                logger.error(f"Failed to load cache index: {e}")
                return self._create_empty_index()
        else:
            return self._create_empty_index()

    def _create_empty_index(self) -> dict[str, Any]:
        """创建空的索引结构"""
        return {
            "content": {},  # 内容索引
            "resources": {},  # 资源索引
            "metadata": {
                "created_at": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat(),
                "version": "1.0",
            },
        }

    def _save_index(self) -> None:
        """保存索引文件"""
        try:
            self.index["metadata"]["last_updated"] = datetime.now().isoformat()
            with open(self.index_path, "w", encoding="utf-8") as f:
                json.dump(self.index, f, indent=2, ensure_ascii=False)
        except OSError as e:
            logger.error(f"Failed to save cache index: {e}")

    def _get_content_path(self, hash_key: str) -> Path:
        """获取内容文件的路径"""
        return self.content_dir / f"{hash_key}.md"

    def _is_content_expired(self, content_info: dict[str, Any]) -> bool:
        """检查内容是否已过期"""
        timestamp = content_info.get("timestamp", 0)
        return (time.time() - timestamp) > self.cache_ttl

    def get_content(self, source_type: str, source_id: str) -> Optional[MarkdownContent]:
        """
        获取缓存的内容，如不存在或已过期则返回None

        Args:
            source_type: 来源类型
            source_id: 来源标识

        Returns:
            Optional[MarkdownContent]: 缓存的内容，如不存在或已过期则返回None
        """
        # 生成缓存键
        cache_key = f"{source_type}:{source_id}"

        # 检查内容是否在索引中
        if cache_key not in self.index["content"]:
            return None

        content_info = self.index["content"][cache_key]

        # 检查是否过期
        if self._is_content_expired(content_info):
            logger.info(f"Content cache expired for {cache_key}")
            return None

        # 获取内容文件路径
        content_path = Path(content_info["path"])
        if not content_path.exists():
            logger.warning(f"Content file not found: {content_path}")
            return None

        # 读取内容文件
        try:
            # 读取Markdown内容
            with open(content_path, encoding="utf-8") as f:
                markdown_content = f.read()

            # 读取元数据文件（如果有）
            metadata_path = content_path.with_suffix(".json")
            metadata = {}
            if metadata_path.exists():
                with open(metadata_path, encoding="utf-8") as f:
                    metadata = json.load(f)

            # 创建MarkdownContent对象
            return MarkdownContent.from_dict(
                {
                    "metadata": {
                        "source_type": source_type,
                        "source_id": source_id,
                        "timestamp": datetime.fromtimestamp(content_info["timestamp"]).isoformat(),
                        "title": metadata.get("title"),
                        "tags": metadata.get("tags", []),
                        "processing_stats": metadata.get("processing_stats", {}),
                    },
                    "content": markdown_content,
                    "resources": metadata.get("resources", []),
                    "cached_path": str(content_path),
                    "error": metadata.get("error"),
                },
            )

        except Exception as e:
            logger.error(f"Failed to load cached content {cache_key}: {e}")
            return None

    def cache_content(self, content: MarkdownContent) -> str:
        """
        缓存内容及其资源，返回缓存文件路径

        Args:
            content: 要缓存的内容

        Returns:
            str: 缓存文件路径
        """
        # 获取元数据和哈希键
        metadata = content.metadata
        hash_key = metadata.hash_key
        cache_key = f"{metadata.source_type}:{metadata.source_id}"

        # 确定内容文件路径
        content_path = self._get_content_path(hash_key)

        # 保存Markdown内容
        try:
            with open(content_path, "w", encoding="utf-8") as f:
                f.write(content.content)

            # 保存元数据
            # metadata_dict = content.to_dict()["metadata"]
            resources_dict = content.to_dict()["resources"]

            metadata_path = content_path.with_suffix(".json")
            with open(metadata_path, "w", encoding="utf-8") as f:
                json.dump(
                    {
                        "title": metadata.title,
                        "tags": metadata.tags,
                        "processing_stats": metadata.processing_stats,
                        "resources": resources_dict,
                        "error": content.error,
                    },
                    f,
                    indent=2,
                    ensure_ascii=False,
                )

            # 更新索引
            now = time.time()
            self.index["content"][cache_key] = {
                "hash_key": hash_key,
                "timestamp": now,
                "path": str(content_path),
            }

            # 更新资源引用
            for resource in content.resources:
                resource_key = resource.original_uri
                if resource_key in self.index["resources"]:
                    # 更新现有资源记录
                    resource_info = self.index["resources"][resource_key]
                    if hash_key not in resource_info["refs"]:
                        resource_info["refs"].append(hash_key)
                    resource_info["last_access"] = now
                else:
                    # 添加新资源记录
                    self.index["resources"][resource_key] = {
                        "path": resource.local_path,
                        "refs": [hash_key],
                        "last_access": now,
                    }

            # 保存索引
            self._save_index()

            # 更新内容的缓存路径
            content.cached_path = str(content_path)

            logger.info(f"Cached content {cache_key} to {content_path}")
            return str(content_path)

        except Exception as e:
            logger.error(f"Failed to cache content {cache_key}: {e}")
            raise

    def invalidate(self, source_type: str, source_id: str) -> bool:
        """
        使指定内容的缓存失效，返回是否成功

        Args:
            source_type: 来源类型
            source_id: 来源标识

        Returns:
            bool: 是否成功使缓存失效
        """
        cache_key = f"{source_type}:{source_id}"

        if cache_key not in self.index["content"]:
            logger.warning(f"Content not found in cache: {cache_key}")
            return False

        try:
            # 获取内容信息
            content_info = self.index["content"][cache_key]
            hash_key = content_info["hash_key"]
            path = Path(content_info["path"])

            # 删除内容文件和元数据文件
            if path.exists():
                path.unlink()

            metadata_path = path.with_suffix(".json")
            if metadata_path.exists():
                metadata_path.unlink()

            # 更新资源引用
            for resource_key, resource_info in list(self.index["resources"].items()):
                if hash_key in resource_info["refs"]:
                    resource_info["refs"].remove(hash_key)

                    # 如果没有引用，标记为可能清理
                    if not resource_info["refs"]:
                        resource_info["last_access"] = time.time()

            # 从索引中移除内容
            del self.index["content"][cache_key]

            # 保存索引
            self._save_index()

            logger.info(f"Invalidated cache for {cache_key}")
            return True

        except Exception as e:
            logger.error(f"Failed to invalidate cache for {cache_key}: {e}")
            return False

    def cleanup_resources(self, days_threshold: int = 30) -> int:
        """
        清理超过指定天数未使用且无引用的资源，返回清理数量

        Args:
            days_threshold: 清理阈值（天数）

        Returns:
            int: 清理的资源数量
        """
        if days_threshold <= 0:
            logger.warning("Invalid days threshold for cleanup")
            return 0

        cleanup_count = 0
        threshold_time = time.time() - (days_threshold * 86400)

        try:
            # 遍历资源索引
            for resource_key, resource_info in list(self.index["resources"].items()):
                # 检查资源是否可以清理：无引用且超过阈值
                if not resource_info["refs"] and resource_info.get("last_access", 0) < threshold_time:
                    # 删除资源文件
                    resource_path = Path(resource_info["path"])
                    if resource_path.exists():
                        resource_path.unlink()
                        cleanup_count += 1

                    # 从索引中移除
                    del self.index["resources"][resource_key]

            # 保存索引
            if cleanup_count > 0:
                self._save_index()
                logger.info(f"Cleaned up {cleanup_count} unused resources")

            return cleanup_count

        except Exception as e:
            logger.error(f"Failed to cleanup resources: {e}")
            return cleanup_count

    def get_stats(self) -> dict[str, Any]:
        """
        获取缓存统计信息

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        try:
            content_count = len(self.index["content"])
            resource_count = len(self.index["resources"])

            # 计算内容文件总大小
            content_size = 0
            for content_info in self.index["content"].values():
                path = Path(content_info["path"])
                if path.exists():
                    content_size += path.stat().st_size

            # 计算资源文件总大小
            resource_size = 0
            orphaned_resources = 0
            for resource_info in self.index["resources"].values():
                path = Path(resource_info["path"])
                if path.exists():
                    resource_size += path.stat().st_size

                if not resource_info["refs"]:
                    orphaned_resources += 1

            return {
                "content_count": content_count,
                "resource_count": resource_count,
                "content_size_bytes": content_size,
                "resource_size_bytes": resource_size,
                "total_size_bytes": content_size + resource_size,
                "orphaned_resources": orphaned_resources,
                "last_updated": self.index["metadata"]["last_updated"],
            }

        except Exception as e:
            logger.error(f"Failed to get cache stats: {e}")
            return {
                "error": str(e),
            }
