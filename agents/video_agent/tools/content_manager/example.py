"""
内容管理系统使用示例

展示如何使用统一内容管理系统的基本功能
"""

import os
from pathlib import Path

import dotenv

# 加载环境变量
dotenv.load_dotenv()

from loguru import logger

from src.video_agent.tools.content_manager import ContentCollector

# 配置loguru
LOG_LEVEL = os.environ.get("LOG_LEVEL", "INFO")
logger.remove()  # 移除默认handler
logger.add(
    "content_manager_example.log",
    rotation="10 MB",
    level=LOG_LEVEL,
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {module}:{function}:{line} | {message}",
)
logger.add(
    lambda msg: print(msg),
    level=LOG_LEVEL,
    format="<blue>{time:HH:mm:ss}</blue> | <level>{level}</level> | <cyan>{module}</cyan> | {message}",
)


def text_example(collector):
    """文本处理示例"""
    print("\n=== 示例1：处理简单文本 ===")
    sample_text = """这是一段简单的文本内容。

这是第二段落。

这是第三段落，包含一些格式化内容：
- 项目1
- 项目2
- 项目3
"""

    content1 = collector.collect(
        source_type="text",
        source_id="sample_text_1",
        text=sample_text,
        is_markdown=False,
        title="示例文本",
        tags=["示例", "文本"],
    )

    print(f"处理完成，内容标题: {content1.metadata.title}")
    print(f"内容已缓存到: {content1.cached_path}")

    # 处理Markdown文本
    print("\n=== 示例2：处理Markdown文本 ===")
    sample_markdown = """# Markdown示例

这是一个**Markdown**格式的示例文档。

## 小标题

这里有一个列表：
1. 第一项
2. 第二项
3. 第三项

> 这是一个引用块

```python
def hello():
    print("Hello World!")
```
"""

    content2 = collector.collect(
        source_type="text",
        source_id="sample_markdown_1",
        text=sample_markdown,
        is_markdown=True,
    )

    print(f"处理完成，内容标题: {content2.metadata.title}")
    print(f"内容已缓存到: {content2.cached_path}")


def web_example(collector):
    """网页处理示例"""
    print("\n=== 示例3：处理网页内容 ===")

    try:
        # 处理网页内容
        content = collector.collect(
            source_type="web",
            source_id="https://python.org",
            use_javascript=True,
            timeout=30,
        )

        print(f"处理完成，内容标题: {content.metadata.title}")
        print(f"内容已缓存到: {content.cached_path}")
        print(f"图片资源数量: {len(content.resources)}")

        # 打印资源信息
        if content.resources:
            print("\n图片资源:")
            for i, resource in enumerate(content.resources[:3], 1):  # 只显示前3个
                print(f"  {i}. {resource.filename} - {resource.local_path}")

            if len(content.resources) > 3:
                print(f"  ... 以及其他 {len(content.resources) - 3} 个资源")

        # 测试缓存功能
        print("\n使用缓存获取相同内容:")
        cached_content = collector.collect(
            source_type="web",
            source_id="https://python.org",
        )
        print(f"内容来自缓存: {cached_content.cached_path}")
        print(f"处理时间: {cached_content.metadata.processing_stats.get('processing_time_seconds', 0):.2f}秒")

        # 强制刷新
        print("\n强制刷新内容:")
        fresh_content = collector.collect(
            source_type="web",
            source_id="https://python.org",
            force_refresh=True,
        )
        print(f"内容已更新: {fresh_content.cached_path}")
        print(f"处理时间: {fresh_content.metadata.processing_stats.get('processing_time_seconds', 0):.2f}秒")

    except Exception as e:
        print(f"处理网页时出错: {str(e)}")


def pdf_example(collector):
    """PDF处理示例"""
    print("\n=== 示例4：处理PDF文档 ===")

    # 查找示例PDF文件
    pdf_files = list(Path(".").glob("*.pdf"))
    if not pdf_files:
        print("未找到PDF文件进行测试，尝试使用URL")
        # 使用一个公开的PDF URL进行测试
        pdf_url = "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"
        try:
            # 处理PDF URL
            content = collector.collect(
                source_type="pdf",
                source_id=pdf_url,
                extract_images=True,
            )

            print(f"处理完成，内容标题: {content.metadata.title}")
            print(f"内容已缓存到: {content.cached_path}")
            print(f"图片资源数量: {len(content.resources)}")

            # 打印处理统计信息
            stats = content.metadata.processing_stats
            print("\n处理统计:")
            print(f"  图片数: {stats.get('images_count', 0)}")
            print(f"  资源数: {stats.get('resources_count', 0)}")
            print(f"  处理时间: {stats.get('processing_time_seconds', 0):.2f} 秒")

            return
        except Exception as e:
            print(f"处理PDF URL时出错: {str(e)}")
            return

    # 使用本地PDF文件
    pdf_path = str(pdf_files[0])
    print(f"使用PDF文件: {pdf_path}")

    try:
        # 处理PDF文档
        content = collector.collect(
            source_type="pdf",
            source_id=pdf_path,
            extract_images=True,
        )

        print(f"处理完成，内容标题: {content.metadata.title}")
        print(f"内容已缓存到: {content.cached_path}")
        print(f"图片资源数量: {len(content.resources)}")

        # 打印处理统计信息
        stats = content.metadata.processing_stats
        print("\n处理统计:")
        print(f"  图片数: {stats.get('images_count', 0)}")
        print(f"  资源数: {stats.get('resources_count', 0)}")
        print(f"  处理时间: {stats.get('processing_time_seconds', 0):.2f} 秒")

    except Exception as e:
        print(f"处理PDF时出错: {str(e)}")


def search_example(collector):
    """搜索处理示例"""
    print("\n=== 示例5：处理搜索请求 ===")

    # 检查是否有Tavily API密钥
    if "search" not in collector.processors:
        print("未配置Tavily API密钥，跳过搜索示例")
        return

    try:
        # 进行搜索
        content = collector.collect(
            source_type="search",
            source_id="Python programming language history",
            search_depth="basic",
            max_results=5,
            include_answer=True,
        )

        print(f"处理完成，内容标题: {content.metadata.title}")
        print(f"内容已缓存到: {content.cached_path}")

        # 打印处理统计信息
        stats = content.metadata.processing_stats
        print("\n处理统计:")
        print(f"  结果数量: {stats.get('results_count', 0)}")
        print(f"  搜索深度: {stats.get('search_depth', 'basic')}")
        print(f"  API积分使用: {stats.get('api_credits_used', 1)}")
        print(f"  处理时间: {stats.get('processing_time_seconds', 0):.2f} 秒")

        # 提取搜索结果中的URL
        import re

        urls = re.findall(r"\[查看完整内容\]\((https?://[^\)]+)\)", content.content)

        if urls:
            print(f"\n从搜索结果中找到 {len(urls)} 个URL，获取第一个URL的详细内容:")
            try:
                first_url = urls[0]
                web_content = collector.collect("web", first_url)
                print(f"成功获取网页内容: {web_content.metadata.title}")
                print(f"内容长度: {len(web_content.content)} 字符")
            except Exception as e:
                print(f"获取详细内容失败: {str(e)}")

    except Exception as e:
        print(f"处理搜索请求时出错: {str(e)}")


def duckduckgo_example(collector):
    """DuckDuckGo搜索处理示例"""
    print("\n=== 示例6：处理DuckDuckGo搜索请求 ===")

    if "duckduckgo" not in collector.processors:
        print("DuckDuckGo处理器未注册，跳过示例")
        return

    try:
        # 进行搜索
        content = collector.collect(
            source_type="duckduckgo",
            source_id="artificial intelligence recent developments",
            max_results=5,
        )

        print(f"处理完成，内容标题: {content.metadata.title}")
        print(f"内容已缓存到: {content.cached_path}")

        # 打印处理统计信息
        stats = content.metadata.processing_stats
        print("\n处理统计:")
        print(f"  结果数量: {stats.get('results_count', 0)}")
        print(f"  处理时间: {stats.get('processing_time_seconds', 0):.2f} 秒")

    except Exception as e:
        print(f"处理DuckDuckGo搜索请求时出错: {str(e)}")


def cache_management_example(collector):
    """缓存管理示例"""
    print("\n=== 示例7：缓存管理 ===")

    # 获取缓存统计
    stats = collector.get_cache_stats()
    print("缓存统计:")
    print(f"  内容数量: {stats.get('content_count', 0)}")
    print(f"  资源数量: {stats.get('resource_count', 0)}")
    print(f"  未引用资源: {stats.get('orphaned_resources', 0)}")
    print(f"  总大小: {stats.get('total_size_bytes', 0) / (1024*1024):.2f} MB")

    # 使缓存失效
    if stats.get("content_count", 0) > 0:
        # 尝试使第一个网页缓存失效
        success = collector.invalidate_cache("web", "https://python.org")
        print(f"使缓存失效: {'成功' if success else '失败'}")

    # 清理未使用资源
    cleaned = collector.cleanup(days_threshold=1)  # 使用较小的阈值进行演示
    print(f"清理了 {cleaned} 个未使用资源")


def main():
    """主函数"""
    # 获取Tavily API密钥（如果有）
    tavily_api_key = os.environ.get("TAVILY_API_KEY")

    # 创建内容收集器（使用自定义缓存目录）
    collector = ContentCollector(
        {
            "cache_dir": "example_cache",
            "content_dir": "example_cache/content",
            "resource_dir": "example_cache/resources",
            "image_dir": "example_cache/resources/images",
            "video_dir": "example_cache/resources/videos",
            "pdf_output_dir": "example_cache/pdf_output",
            "search_save_dir": "example_cache/search_results",
            "tavily_api_key": tavily_api_key,
            "searxng_instance": "https://searx.be",
        },
    )

    print("内容收集器已初始化")
    print(f"注册的处理器: {', '.join(collector.processors.keys())}")

    # 运行各种示例
    text_example(collector)
    web_example(collector)
    pdf_example(collector)
    search_example(collector)
    duckduckgo_example(collector)
    cache_management_example(collector)

    print("\n示例运行完成")


if __name__ == "__main__":
    main()
