"""
基于Tavily API的搜索工具包。

提供搜索网络信息和提取网页内容的功能。
"""

import json
import os
from typing import Optional

from camel.toolkits.base import BaseToolkit
from camel.toolkits.function_tool import FunctionTool
from camel.utils import dependencies_required
from loguru import logger


class TavilyToolkit(BaseToolkit):
    """
    基于Tavily API的搜索工具包。

    提供网络搜索和内容提取功能，专门为LLM优化，
    适合作为RAG应用的信息来源。
    """

    @dependencies_required("tavily")
    def __init__(self, api_key: Optional[str] = None, timeout: Optional[float] = None) -> None:
        """
        初始化Tavily工具包。

        Args:
            api_key: Tavily API密钥。如果为None，将尝试从环境变量TAVILY_API_KEY获取。
            timeout: API请求超时时间（秒）。
        """
        super().__init__(timeout=timeout)

        # 设置API密钥
        self.api_key = api_key or os.environ.get("TAVILY_API_KEY", "")
        if not self.api_key:
            logger.warning("Tavily API密钥未设置。请设置环境变量TAVILY_API_KEY或在初始化时提供api_key参数。")

        # 结果保存目录
        self.save_dir = "tavily_results"
        os.makedirs(self.save_dir, exist_ok=True)

        # 初始化客户端
        try:
            from tavily import TavilyClient

            self.client = TavilyClient(api_key=self.api_key)
        except ImportError:
            logger.error("未找到tavily。请使用 'pip install tavily-python' 安装。")
            self.client = None

    def search(
        self,
        query: str,
        search_depth: str = "basic",
        max_results: int = 5,
        include_domains: Optional[list[str]] = None,
        exclude_domains: Optional[list[str]] = None,
        include_answer: bool = False,
        include_raw_content: bool = True,
        include_images: bool = False,
        save_content: bool = True,
    ) -> dict:
        """
        使用Tavily API进行网络搜索。

        Args:
            query: 搜索查询
            search_depth: 搜索深度，'basic'或'advanced'
            max_results: 返回的最大结果数量
            include_domains: 包含的域名列表
            exclude_domains: 排除的域名列表
            include_answer: 是否包含问题回答
            include_raw_content: 是否包含原始内容
            include_images: 是否包含图片
            save_content: 是否保存搜索到的内容

        Returns:
            Dict: 包含搜索结果的字典
        """
        if not self.client:
            return {"error": "Tavily客户端未初始化，请确保正确安装tavily-python并提供有效的API密钥。"}

        if not query:
            return {"error": "需要提供搜索查询。"}

        # 准备搜索参数
        search_params = {
            "query": query,
            "search_depth": search_depth,
            "max_results": max_results,
            "include_answer": include_answer,
            "include_raw_content": include_raw_content,
            "include_images": include_images,
        }

        # 添加可选参数
        if include_domains:
            search_params["include_domains"] = include_domains
        if exclude_domains:
            search_params["exclude_domains"] = exclude_domains

        try:
            # 执行搜索
            search_response = self.client.search(**search_params)

            # 如果需要保存内容
            if save_content:
                self._save_search_results(query, search_response)

            # 添加元数据
            search_response["metadata"] = {
                "query": query,
                "search_depth": search_depth,
                "api_credits_used": 1 if search_depth == "basic" else 2,
            }

            return search_response

        except Exception as e:
            logger.error(f"执行Tavily搜索出错: {e}")
            return {"error": f"搜索'{query}'时发生错误: {str(e)}"}

    def extract(
        self,
        urls: list[str],
        include_images: bool = False,
        save_content: bool = True,
    ) -> dict:
        """
        使用Tavily API提取URL内容。

        Args:
            urls: 要提取内容的URL列表
            include_images: 是否包含图片
            save_content: 是否保存提取的内容

        Returns:
            Dict: 包含提取内容的字典
        """
        if not self.client:
            return {"error": "Tavily客户端未初始化，请确保正确安装tavily-python并提供有效的API密钥。"}

        if not urls:
            return {"error": "需要提供至少一个URL。"}

        # 限制URL数量（Tavily一次最多处理20个URL）
        if len(urls) > 20:
            logger.warning(f"URL数量超过20个，将只处理前20个。提供了{len(urls)}个URL。")
            urls = urls[:20]

        try:
            # 执行内容提取
            extract_params = {
                "urls": urls,
                "include_images": include_images,
            }

            extract_response = self.client.extract(**extract_params)

            # 如果需要保存内容
            if save_content:
                self._save_extract_results(urls, extract_response)

            # 添加元数据
            success_count = len(extract_response.get("results", []))
            failed_count = len(extract_response.get("failed_results", []))
            extract_response["metadata"] = {
                "success_count": success_count,
                "failed_count": failed_count,
                "api_credits_used": max(1, success_count // 5),
            }

            return extract_response

        except Exception as e:
            logger.error(f"执行Tavily内容提取出错: {e}")
            return {"error": f"提取URL内容时发生错误: {str(e)}"}

    def get_search_context(
        self,
        query: str,
        max_tokens: int = 4000,
        search_depth: str = "basic",
        include_domains: Optional[list[str]] = None,
        exclude_domains: Optional[list[str]] = None,
    ) -> dict[str, str]:
        """
        获取搜索上下文，适用于RAG应用。

        Args:
            query: 搜索查询
            max_tokens: 最大令牌数
            search_depth: 搜索深度
            include_domains: 包含的域名列表
            exclude_domains: 排除的域名列表

        Returns:
            Dict[str, str]: 包含搜索上下文的字典
        """
        if not self.client:
            return {"context": "", "error": "Tavily客户端未初始化，请确保正确安装tavily-python并提供有效的API密钥。"}

        try:
            # 准备搜索参数
            search_params = {
                "query": query,
                "max_tokens": max_tokens,
                "search_depth": search_depth,
            }

            # 添加可选参数
            if include_domains:
                search_params["include_domains"] = include_domains
            if exclude_domains:
                search_params["exclude_domains"] = exclude_domains

            context = self.client.get_search_context(**search_params)

            return {
                "context": json.dumps(json.loads(context), ensure_ascii=False),
                "query": query,
                "metadata": {
                    "search_depth": search_depth,
                    "max_tokens": max_tokens,
                    "api_credits_used": 1 if search_depth == "basic" else 2,
                },
            }

        except Exception as e:
            logger.error(f"获取搜索上下文出错: {e}")
            return {"context": "", "error": f"获取搜索上下文时发生错误: {str(e)}"}

    def qna_search(
        self,
        query: str,
        search_depth: str = "advanced",
        include_domains: Optional[list[str]] = None,
        exclude_domains: Optional[list[str]] = None,
        save_content: bool = True,
    ) -> dict[str, str]:
        """
        问答搜索，直接返回问题的答案。

        Args:
            query: 搜索查询
            search_depth: 搜索深度
            include_domains: 包含的域名列表
            exclude_domains: 排除的域名列表
            save_content: 是否保存搜索到的内容
        Returns:
            Dict[str, str]: 包含问题答案的字典
        """
        if not self.client:
            return {"answer": "", "error": "Tavily客户端未初始化，请确保正确安装tavily-python并提供有效的API密钥。"}

        try:
            # 准备搜索参数
            search_params = {
                "query": query,
                "search_depth": search_depth,
            }

            # 添加可选参数
            if include_domains:
                search_params["include_domains"] = include_domains
            if exclude_domains:
                search_params["exclude_domains"] = exclude_domains

            answer = self.client.qna_search(**search_params)

            if save_content:
                self._save_qna_results(query, answer)

            return {
                "answer": answer,
                "query": query,
                "metadata": {
                    "search_depth": search_depth,
                    "api_credits_used": 1 if search_depth == "basic" else 2,
                },
            }

        except Exception as e:
            logger.error(f"问答搜索出错: {e}")
            return {"answer": "", "error": f"问答搜索时发生错误: {str(e)}"}

    def _save_qna_results(self, query: str, answer: str) -> None:
        """
        保存问答搜索结果到文件。

        Args:
            query: 搜索查询
            answer: 搜索答案
        """
        try:
            # 创建保存目录
            query_dir = os.path.join(self.save_dir, f"{'_'.join(query.split())}_qna")
            os.makedirs(query_dir, exist_ok=True)

            # 保存完整响应
            response_path = os.path.join(query_dir, "full_response.json")
            with open(response_path, "w", encoding="utf-8") as f:
                json.dump(answer, f, ensure_ascii=False, indent=2)

            logger.info(f"问答搜索结果已保存到 {query_dir}")
        except Exception as e:
            logger.error(f"保存问答搜索结果出错: {e}")

    def _save_search_results(self, query: str, search_response: dict) -> None:
        """
        保存搜索结果到文件。

        Args:
            query: 搜索查询
            search_response: 搜索响应
        """
        try:
            # 创建保存目录
            query_dir = os.path.join(self.save_dir, f"{'_'.join(query.split())}_search")
            os.makedirs(query_dir, exist_ok=True)

            # 保存完整响应
            response_path = os.path.join(query_dir, "full_response.json")
            with open(response_path, "w", encoding="utf-8") as f:
                json.dump(search_response, f, ensure_ascii=False, indent=2)

            # 保存单独的搜索结果
            if "results" in search_response:
                for i, result in enumerate(search_response["results"]):
                    # 保存原始内容（如果有）
                    if "raw_content" in result and result["raw_content"]:
                        content_path = os.path.join(query_dir, f"result_{i + 1}_content.txt")
                        with open(content_path, "w", encoding="utf-8") as f:
                            f.write(result["raw_content"])

                    # 保存图片链接（如果有）
                    if "images" in result and result["images"]:
                        images_path = os.path.join(query_dir, f"result_{i + 1}_images.txt")
                        with open(images_path, "w", encoding="utf-8") as f:
                            for image_url in result["images"]:
                                f.write(f"{image_url}\n")

            logger.info(f"搜索结果已保存到 {query_dir}")

        except Exception as e:
            logger.error(f"保存搜索结果出错: {e}")

    def _save_extract_results(self, urls: list[str], extract_response: dict) -> None:
        """
        保存内容提取结果到文件。

        Args:
            urls: URL列表
            extract_response: 提取响应
        """
        try:
            # 创建保存目录
            extract_dir = os.path.join(self.save_dir, f"extract_{len(urls)}_urls")
            os.makedirs(extract_dir, exist_ok=True)

            # 保存完整响应
            response_path = os.path.join(extract_dir, "full_response.json")
            with open(response_path, "w", encoding="utf-8") as f:
                json.dump(extract_response, f, ensure_ascii=False, indent=2)

            # 保存单独的提取结果
            if "results" in extract_response:
                for i, result in enumerate(extract_response["results"]):
                    # 创建URL相关目录（使用URL的一部分作为目录名）
                    url = result.get("url", f"unknown_url_{i}")
                    url_dir_name = (
                        url.replace("http://", "").replace("https://", "").replace("/", "_").replace(":", "_")[:50]
                    )
                    url_dir = os.path.join(extract_dir, url_dir_name)
                    os.makedirs(url_dir, exist_ok=True)

                    # 保存原始内容
                    if "raw_content" in result and result["raw_content"]:
                        content_path = os.path.join(url_dir, "content.txt")
                        with open(content_path, "w", encoding="utf-8") as f:
                            f.write(result["raw_content"])

                    # 保存图片链接
                    if "images" in result and result["images"]:
                        images_path = os.path.join(url_dir, "images.txt")
                        with open(images_path, "w", encoding="utf-8") as f:
                            for image_url in result["images"]:
                                f.write(f"{image_url}\n")

            logger.info(f"提取结果已保存到 {extract_dir}")

        except Exception as e:
            logger.error(f"保存提取结果出错: {e}")

    def get_tools(self) -> list[FunctionTool]:
        """
        返回工具包中的工具列表。

        Returns:
            List[FunctionTool]: 工具列表
        """
        return [
            FunctionTool(self.search),
            FunctionTool(self.extract),
            FunctionTool(self.get_search_context),
            FunctionTool(self.qna_search),
        ]


if __name__ == "__main__":
    # 简单示例：执行搜索
    toolkit = TavilyToolkit()
    search_result = toolkit.search("费曼物理学讲义的主要内容", include_answer=True, max_results=3)
    print(json.dumps(search_result, ensure_ascii=False, indent=2))

    # 提取URL内容
    extract_result = toolkit.extract(["https://en.wikipedia.org/wiki/Richard_Feynman"], include_images=True)
    print(json.dumps(extract_result, ensure_ascii=False, indent=2))
