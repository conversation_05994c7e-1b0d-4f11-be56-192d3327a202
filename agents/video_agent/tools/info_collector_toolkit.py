"""
信息收集器工具包

将InfoCollector包装成Camel框架可用的工具集
"""

from dotenv import load_dotenv

load_dotenv()

import os
from typing import Any, Optional

from camel.toolkits.base import BaseToolkit
from camel.toolkits.function_tool import FunctionTool
from loguru import logger

from .info_collector import InfoCollector


class InfoCollectorToolkit(BaseToolkit):
    """
    基于InfoCollector的Camel工具包

    提供从多种来源收集信息的功能，包括：
    - 网络搜索（多种搜索引擎）
    - 网页内容提取
    - PDF文件处理
    - 文本处理
    """

    def __init__(
        self,
        use_cache: bool = True,
        cache_dir: Optional[str] = None,
        default_output_format: str = "markdown",
        timeout: Optional[float] = None,
    ) -> None:
        """
        初始化信息收集工具包

        Args:
            use_cache: 是否使用缓存
            cache_dir: 缓存目录
            default_output_format: 默认输出格式
            timeout: 请求超时时间（秒）
        """
        super().__init__(timeout=timeout)

        # 初始化信息收集器
        self.collector = InfoCollector(
            use_cache=use_cache,
            cache_dir=cache_dir,
            default_output_format=default_output_format,
        )

        logger.info(f"信息收集工具包初始化完成，默认输出格式: {default_output_format}")

        # 记录可用的适配器
        adapters = self.collector.get_available_adapters()
        logger.info(f"已注册的适配器: {', '.join(adapters)}")

    def search(
        self,
        query: str,
        engine: str = "duckduckgo",
        max_results: int = 5,
        output_format: Optional[str] = None,
        time_range: Optional[str] = None,
        region: Optional[str] = None,
    ) -> dict[str, Any]:
        """
        使用搜索引擎查询信息

        Args:
            query: 搜索查询
            engine: 搜索引擎（duckduckgo, searxng, tavily等）
            max_results: 最大结果数
            output_format: 输出格式（默认为初始化时设置的格式）
            time_range: 时间范围（d:天, w:周, m:月, y:年）
            region: 地区设置

        Returns:
            Dict[str, Any]: 包含搜索结果的字典
        """
        try:
            # 准备额外的搜索参数
            kwargs = {}
            if time_range:
                kwargs["time_range"] = time_range
            if region:
                kwargs["region"] = region

            # 执行搜索
            result = self.collector.search(
                query=query,
                engine=engine,
                max_results=max_results,
                output_format=output_format,
                **kwargs,
            )

            # 如果结果是文本，则包装为字典
            if isinstance(result, str):
                return {"content": result, "query": query, "engine": engine, "max_results": max_results}
            return result

        except Exception as e:
            logger.error(f"搜索出错: {e}")
            return {"error": f"搜索出错: {str(e)}", "query": query}

    def extract_web_content(
        self,
        url: str,
        use_javascript: bool = True,
        extract_images: bool = False,
        output_format: Optional[str] = None,
        wait_time: Optional[int] = None,
    ) -> dict[str, Any]:
        """
        提取网页内容

        Args:
            url: 网页URL
            use_javascript: 是否使用JavaScript渲染
            extract_images: 是否提取图片
            output_format: 输出格式
            wait_time: 等待渲染的时间（秒）

        Returns:
            Dict[str, Any]: 包含网页内容的字典
        """
        try:
            # 准备额外参数
            kwargs = {}
            if extract_images:
                kwargs["extract_images"] = extract_images
            if wait_time:
                kwargs["wait_time"] = wait_time

            # 提取内容
            result = self.collector.extract_web_content(
                url=url,
                use_javascript=use_javascript,
                output_format=output_format,
                **kwargs,
            )

            # 如果结果是文本，则包装为字典
            if isinstance(result, str):
                return {"content": result, "url": url, "use_javascript": use_javascript}
            return result

        except Exception as e:
            logger.error(f"提取网页内容出错: {e}")
            return {"error": f"提取网页内容出错: {str(e)}", "url": url}

    def process_pdf(
        self,
        pdf_path: str,
    ) -> dict[str, Any]:
        """
        处理PDF文件内容

        Args:
            pdf_path: PDF文件路径或URL

        Returns:
            Dict[str, str]:
                - markdown_file: 转换后的markdown文件路径
                - image_info_file: 图片信息列表
        """
        try:
            # 处理PDF
            return self.collector.process_pdf(pdf_path=pdf_path)
        except Exception as e:
            logger.error(f"处理PDF文件出错: {e}")
            return {"error": f"处理PDF文件出错: {str(e)}", "path": pdf_path}

    def process_text(
        self,
        text: str,
        title: Optional[str] = None,
        output_format: Optional[str] = None,
        from_file: bool = False,
    ) -> dict[str, Any]:
        """
        处理文本内容

        Args:
            text: 文本内容或文件路径（如果from_file为True）
            title: 文本标题
            output_format: 输出格式
            from_file: 是否从文件读取文本

        Returns:
            Dict[str, Any]: 包含处理后文本的字典
        """
        try:
            # 如果是从文件读取
            if from_file:
                if not os.path.exists(text):
                    return {"error": f"文本文件不存在: {text}", "path": text}

                with open(text, encoding="utf-8") as f:
                    content = f.read()

                # 如果没有指定标题，使用文件名
                if not title:
                    title = os.path.basename(text)
            else:
                content = text

            # 处理文本
            result = self.collector.process_text(text=content, title=title, output_format=output_format)

            # 如果结果是文本，则包装为字典
            if isinstance(result, str):
                return {"content": result, "title": title or "未命名文本"}
            return result

        except Exception as e:
            logger.error(f"处理文本内容出错: {e}")
            return {"error": f"处理文本内容出错: {str(e)}", "title": title or (text if from_file else "未命名文本")}

    def get_available_adapters(self) -> list[str]:
        """
        获取可用的适配器列表

        Returns:
            List[str]: 适配器名称列表
        """
        return self.collector.get_available_adapters()

    def get_available_formats(self) -> list[str]:
        """
        获取可用的输出格式列表

        Returns:
            List[str]: 格式名称列表
        """
        return self.collector.get_available_formats()

    def clear_cache(self, pattern: Optional[str] = None) -> dict[str, Any]:
        """
        清除缓存

        Args:
            pattern: 缓存键模式，如果为None则清除所有缓存

        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            self.collector.clear_cache(pattern)
            return {"success": True, "message": f"已清除缓存{' (模式: ' + pattern + ')' if pattern else ''}"}
        except Exception as e:
            logger.error(f"清除缓存出错: {e}")
            return {"success": False, "error": f"清除缓存出错: {str(e)}"}

    def get_tools(self) -> list[FunctionTool]:
        """
        返回工具包中的工具列表

        Returns:
            list[FunctionTool]: 工具列表
        """
        return [
            FunctionTool(self.search),
            FunctionTool(self.extract_web_content),
            FunctionTool(self.process_pdf),
            # FunctionTool(self.process_text),
            # FunctionTool(self.get_available_adapters),
            # FunctionTool(self.get_available_formats),
            # FunctionTool(self.clear_cache),
        ]


if __name__ == "__main__":
    # 简单示例：执行搜索
    toolkit = InfoCollectorToolkit()

    # 搜索信息
    search_result = toolkit.search("费曼物理学讲义介绍", max_results=5)
    # print("搜索结果:", search_result)

    # 提取网页内容
    web_result = toolkit.extract_web_content("https://en.wikipedia.org/wiki/Richard_Feynman")
    # print("网页内容:", web_result)
