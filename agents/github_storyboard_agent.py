import json
import os
import re
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Union

import yaml
from dotenv import load_dotenv
from camel.agents import ChatAgent
from camel.logger import set_log_level
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.types import ModelPlatformType
from loguru import logger

from utils.format import extract_json, save_json_content

load_dotenv()
set_log_level(level="WARNING")

# 必要的目录结构
os.makedirs("config", exist_ok=True)
os.makedirs("output", exist_ok=True)

# Storyboard JSON格式样例 - 与 storyboard.json 保持一致
STORYBOARD_FORMAT = """
[
    {{
        "分镜名": "分镜的简短标题",
        "内容要点": ["GitHub影响力数据", "价值", "数据对比", "悬念问题"],
        "分镜内容": "在这个开场中需要讲解的详细脚本内容",
        "素材名": "null",
        "视觉动效建议": [
            "1. 在 `main` 区域显示标题：@分镜名 (content_type: 'markdown', animation: 'fadeIn')。",
            "2. 在 `main` 区域使用图表展示数据 (chart_type: 'line', data: {{'2022': 100, '2023': 500, '2024': 3000}}, title: 'GitHub星数增长曲线', animation_style: 'fadeIn')。"
        ]
    }}
]
"""

# GitHub 项目视频讲解 Storyboard 提示词
GITHUB_STORYBOARD_PROMPT = """
你是一个专业的GitHub项目视频讲解脚本创作者，现在需要为一个GitHub项目创建简洁有力的视频分镜脚本。

请基于以下GitHub项目资料，创建一个引人入胜的视频脚本分镜：

项目介绍文档：
{project_doc}

项目例子文档：
{example_doc}

可使用的动画效果：
{manim_capabilities}

请创建以下4-6个分镜（只有核心功能例子可以1-3个分镜）：

1. **封面开篇分镜** - 讲解GitHub项目的影响力、价值与作用
   - 第一句话以震惊体切入重点，避免废话，抓住观众，包含如下方面：
     - 使用具有冲击力的量化数据对比（星数增长速度、星数绝对数量、贡献者增长速率等）
     - 包含数字、机构名和强烈反差，使用"震惊"、"颠覆"、"爆发式"等词汇增强冲击力
     - 项目里有知名机构的，重点突出提升影响力，比如openai、google、meta、微软等
   - 第二句提出悬念式问题，引出后面评测内容，并可以暗示后续评测将揭示其潜在缺点或挑战 （如"为何微软/谷歌/Meta等顶级科技公司都在争相使用此项目？但它真的完美无缺吗？我们将深入评测！"）
   - 增加一句营销宣传内容，吸引用户互动，比如"评论区获取完整代码示例等"

2. **项目核心能力介绍分镜** 
   - 介绍项目有哪些核心功能，让用户了解到这个项目具体能做什么、能解决什么问题，比如codex的代码生成、代码补全、代码解释、代码重构等
   - 可以先介绍项目核心功能，再引出会从六大维度评测：核心功能完善性、可用性与易用性、项目活跃度、代码质量、架构设计、文档完备性

3. **测试核心功能的例子详解分镜** - 为**每个不同的例子创建一个独立的分镜**
   - 每个例子一个分镜，直接切入核心
   - **基于测试目标和效果给出客观分析，必须同时指出优点和缺点**
   - 给出量化的性能/效率数据（如"准确率提升40%"、"速度降低15%"）
   - 关联到前面的评测维度，说明此例子在哪些维度表现出色/不足
   - 分析测试结果对项目整体评价的影响，给出深度见解
   - 使用对比性词汇（如"优/劣"、"强/弱"、"高效/低效"）做客观结论
   - 将评测结论抽取为关键词，添加到内容要点中
   - 每个内容要点不超过8个字，且不超过5个要点，有具体数字的要点必须包含具体数字
   - 内容要点必须包含正面和负面评价结论

4. **总结评测分镜** - 提供六大维度的评测说明，给出批判性结论
   - 对六个评测维度进行1-10分的明确打分（其中1分表示非常差，10分表示极其优秀）
   - 必须明确列出六个维度的具体得分和得分理由（如"易用性：8分-配置简单但文档不全"）
   - 基于评测维度和得分生成动态雷达图，直观展示项目优劣势
   - 提供明确的使用建议、适用场景和限制条件
   - 包含批判性结论，指出项目的局限性和潜在风险
   - 提出未来发展方向的预测和建议
   - 确保内容要点不超过8个字，且不超过5个要点，有具体数字的要点必须包含具体数字


对于每个分镜，请按照以下格式提供：
- 分镜名：简短的标题（使用震惊体，加入数字和反差性表达）
- 内容要点：以数组形式列出核心信息点（每点不超过8字，最多5点）
- 分镜内容：简洁的演讲稿（50-100字），直接切入重点，减少废话，多用量化数据和对比分析
- 素材名：如有相关素材路径，直接引用；否则使用null或暗示内容类型（如"雷达图"、"代码对比"、"架构图动画"）
- 视觉动效建议：一个字符串列表，按顺序描述分镜期望的视觉呈现步骤。每一条建议必须遵循以下规则：
  * a. 编号清晰：以"1. ", "2. ", ...开始，描述一个独立的视觉动作
  * b. 描述意图：清晰描述视觉动作的意图（如"播放视频"、"显示Markdown"、"使用计数器动画"等）
  * c. 指定区域：必须明确指定内容放置的目标区域，只能从以下列表选择：`main`, `full_screen`, `left_half`, `right_half`, `upper_half`, `bottom_half`
  * d. 指定内容来源：明确指出动作所需内容的来源，使用`@字段名`引用其他字段（如`@分镜名`、`@内容要点`、`@素材名`等）
  * e. 包含关键参数和数据：在括号中提供实现意图所必需的关键参数（如图表类型、数据值、动画效果等）

对于每个分镜的营销点的描述:
- 在开篇、总结两个分镜中自然地在内容中融入以下类似吸引用户互动行为，为商业转化提供基础，比如:
    - 迫不及待想要动手实践？**点击评论区获取独家完整代码示例**
    - 项目部署总是遇到那些难以解决的问题？**关注我们并私信，立即获取一键式部署方案**
    - 寻找进阶学习资料？**在评论区留言并转发，整理好的相关材料等你来拿！**

**总结与评测分镜的雷达图动效特别说明**：
- 在`main`区域使用`animate_chart`创建雷达图元素(chart_type: 'radar')，展示所有评测维度得分
- 使用`highlight_sequence`依次高亮强调雷达图上的每个维度及其得分
- 最后一个画面应当展示完整雷达图，突出项目整体优劣势
- 设计雷达图区域从无到有的动态变化效果，依次填充每个维度分数
- 建议使用色彩区分不同评分区间（如高分绿色、中分黄色、低分红色）

分镜视觉动效建议示例：
```json
"视觉动效建议": [
  "1. 在 `main` 区域显示标题：@分镜名 (content_type: 'markdown', animation: 'fadeIn')。",
  "2. 在 `main` 区域使用图表展示数据 (chart_type: 'line', data: {{'2022': 100, '2023': 500, '2024': 3000}}, title: 'GitHub星数增长曲线', animation_style: 'fadeIn')。",
  "3. 在 `bottom_half` 区域显示文本：@分镜内容 (animation: 'write')。",
  "4. 在 `right_half` 区域显示列表：@内容要点 (content_type: 'markdown', animation: 'sequential')。"
]
```

**示例 2 (文本与图表):**

```json
{{
  "分镜名": "展示核心指标",
  "内容要点": ["指标X: 80%", "指标Y: 65%", "指标Z: 90%"],
  "分镜内容": "接下来我们看一下几个核心指标的表现情况。如图所示，各项指标均达到预期。",
  "素材名": "核心指标条形图",
  "视觉动效建议": [
    "1. 在 `upper_half` 区域显示 Markdown 标题：@分镜名。",
    "2. 在 `bottom_half` 区域使用条形图 (chart_type: 'bar') 展示数据：{{'指标X': 80, '指标Y': 65, '指标Z': 90}}。设置 Y 轴范围 (options: {{'y_range': [0, 100, 20]}})。"
  ]
}}  
```

请严格按照以上JSON格式和要求输出结果，只返回JSON格式内容，格式必须与示例完全一致：
{storyboard_format}
"""

# Storyboard 素材验证提示词
STORYBOARD_VALIDATOR_PROMPT = """
你是一个专业的视频分镜脚本审核专家，需要审核以下分镜脚本的质量并验证其中引用的素材是否存在。

分镜脚本内容：{storyboard_content}

可使用的动画效果：
{manim_capabilities}

项目文档中提到的媒体素材：{mentioned_media}

请检查以下几个关键方面：

1. **素材引用验证**
   - 检查每个分镜中引用的"素材名"是否在项目文档中被提及
   - 如果素材没有在文档中提及，必须将其设置为null
   - 所有找不到的素材必须替换为null值，而非保留原始路径

2. **JSON格式正确性**
   - 确保输出符合标准JSON格式
   - 检查所有引号、括号是否闭合正确
   - 确保没有多余的逗号或错误的语法

3. **内容表述规范性**
   - 确保每个分镜的"分镜内容"简洁有力且信息准确
   - 检查"内容要点"是否清晰、简洁且包含关键信息
   - 确保每个内容要点不超过8个字，且每个分镜不超过5个要点
   - 确保内容长度适中（50-100字之间）
   - 检查总结与评测分镜是否自然融入了行动号召（获取代码/部署/材料）

4. **动效建议合理性与详细性**
   - 验证"视觉动效建议"是否为字符串列表格式
   - 确保每个动效建议都遵循规定的格式：编号、意图描述、区域指定、内容来源、关键参数
   - 检查每个动效建议是否指定了目标区域（必须从`main`, `full_screen`, `left_half`, `right_half`, `upper_half`, `bottom_half`中选择）
   - 确保动效描述清晰地指明了涉及的元素、动画效果和可能调用的函数
   - 确保动效描述具体、可执行且有助于增强内容表现力
   - **对于总结与评测分镜，特别检查雷达图动画是否包含以下要素：**
     - **是否指定使用animate_chart创建雷达图元素**
     - **是否使用highlight_sequence依次强调每个维度得分**
     - **是否描述了雷达图从无到有填充的动态变化效果**
     - **是否提出使用不同色彩区分评分区间**

5. **例子解说完整性**
   - 确保对项目例子文档中提到的每个例子都有单独的分镜
   - 验证例子分镜是否基于测试目标和效果给出客观评价，必须同时指出优点和缺点
   - 确保例子评价使用对比性词汇（优/劣、强/弱）做客观结论
   - 确保评价结论已被抽取为关键词，添加到内容要点中
   - 检查内容要点是否同时包含正面和负面评价结论
   - 确保评价结论简洁清晰且有说服力

6. **标题震撼性与内容质量**
   - 确保分镜名使用震惊体表达，包含数字、机构名和反差性表述
   - 标题的数字需要检查是否正确，特别是Star数和增长速度，比如400%增长，需要检查是否正确
   - 验证标题是否抓人眼球并包含量化数据

7. 项目核心能力介绍分镜
   - 是否介绍了项目的核心能力能做什么，吸引下用户
   - 是否抛出六大评测维度，提出问题吸引用户继续观看

8. **总的评测分镜**：
   - 检查总结分镜是否包含批判性结论和未来建议
   - **验证总结分镜是否为每个评测维度提供了1-10分的明确打分**
   - **确保每个维度评分都有得分理由**
   - **检查是否正确指定了使用雷达图动画来可视化各维度得分**
   - **打分和理由在输出的“分镜内容”中要体现**

   
请根据审核结果修改分镜脚本，确保所有引用的素材都在文档中被提及（如果未提及则必须设为null），JSON格式正确，内容表述简洁且动效建议合理详细。分镜名需体现震惊体风格，开篇需有悬念，总结需包含行动号召。

请只返回修改后的JSON格式分镜脚本，不要添加任何其他解释：
"""

# 定义GitHub项目视频讲解Agent类
class GitHubStoryboardAgent:
    def __init__(self, config_path="config/config.yaml"):
        # 创建默认配置文件（如果不存在）
        self._create_default_config_if_not_exists(config_path)

        # 加载配置
        self.load_config(config_path)

        # 初始化模型
        self.model = self._create_model()
        logging.warning("模型初始化完成")

        # 初始化代理
        self.agents = self._initialize_agents()
        logging.warning("代理初始化完成")
        
        # 加载Manim DSL v2功能说明
        self.manim_capabilities = self._load_manim_capabilities()
        logging.warning("Manim DSL v2功能加载完成")

    def _create_default_config_if_not_exists(self, config_path):
        """如果配置文件不存在，创建默认配置"""
        if not os.path.exists(config_path):
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            default_config = {
                "model": {
                    "type": "openai/gpt-4o-mini",
                    "api": {
                        "openai_compatibility_api_key": os.environ.get("OPENAI_API_KEY", ""),
                        "openai_compatibility_api_base_url": os.environ.get(
                            "OPENAI_API_BASE", "https://api.openai.com/v1"
                        ),
                    },
                },
                "agents": {
                    "storyboard_generator": {"enabled": True},
                    "storyboard_validator": {"enabled": True},
                },
                "files": {
                    "llm_interface_path": "docs/llm_interface.md"  # Manim DSL v2 功能文档路径
                },
                "github": {
                    "project_url": "",  # GitHub项目URL
                },
                "storyboard": {
                    "output_format": "json",
                    "required_sections": [
                        "封面开篇", 
                        "项目评测维度", 
                        "例子详解", 
                        "总结与评测"
                    ],
                    "media_validation": True
                }
            }
            with open(config_path, "w") as f:
                yaml.dump(default_config, f)

    def _load_manim_capabilities(self) -> str:
        """加载Manim DSL v2的功能说明"""
        try:
            # 从配置中获取路径
            interface_path = os.path.join(os.getcwd(), self.files_config.get("llm_interface_path", "docs/llm_interface.md"))
            if os.path.exists(interface_path):
                with open(interface_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 提取关键动画功能说明
                animation_sections = ["可用的动画效果包括："]
                
                # 使用正则表达式提取动画接口描述
                animation_pattern = r'#+\s*(.*?动画.*?|.*?效果.*?)\n+(.*?)(?=#+|$)'
                matches = re.findall(animation_pattern, content, re.DOTALL)
                
                if matches:
                    for i, (title, description) in enumerate(matches, 1):
                        # 提取第一行作为简要描述
                        first_line = description.strip().split('\n')[0].strip()
                        if first_line:
                            animation_sections.append(f"{i}. {title.strip()} - {first_line}")
                else:
                    # 如果没有找到匹配项，使用默认描述
                    default_animations = [
                        "display_formatted_content - 显示格式化内容（文本、代码、JSON、Markdown）",
                        "animate_chart - 创建并动画展示条形图、折线图、雷达图",
                        "animate_counter - 数字从起始值动态变化到目标值",
                        "highlight_sequence - 按顺序高亮显示代码或内容",
                        "side_by_side_comparison - 创建左右比较布局",
                        "timeline - 创建水平时间轴动画",
                        "animate_architecture_diagram - 创建架构图动画"
                    ]
                    for i, desc in enumerate(default_animations, 1):
                        animation_sections.append(f"{i}. {desc}")
                
                return "\n".join(animation_sections)
            else:
                logging.warning(f"未找到Manim DSL v2接口文档: {interface_path}")
                return "未能加载动画效果说明，将使用默认动画效果。"
        except Exception as e:
            logging.error(f"加载Manim功能说明出错: {e}")
            return "加载动画效果说明出错，将使用默认动画效果。"

    def load_config(self, config_path):
        """从YAML文件加载配置"""
        try:
            with open(config_path) as file:
                config = yaml.safe_load(file)
            # 设置配置属性
            self.model_config = config.get("model", {})
            self.files_config = config.get("files", {})
            self.agent_config = config.get("agents", {})
            self.storyboard_config = config.get("storyboard", {})
            self.github_config = config.get("github", {})
        except Exception as e:
            logging.error(f"加载配置出错: {str(e)}")
            raise

    def _create_model(self):
        """根据配置创建模型实例"""
        api_config = self.model_config.get("api", {})

        return ModelFactory.create(
            model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
            model_type=self.model_config.get("type", "openai/gpt-4o-mini"),
            api_key=api_config.get("openai_compatibility_api_key"),
            url=api_config.get("openai_compatibility_api_base_url"),
        )

    def _initialize_agents(self) -> dict:
        """初始化所有启用的代理及其系统提示和工具"""
        agents = {}

        # 分镜脚本生成代理
        agents["storyboard_generator"] = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="视频分镜脚本生成者",
                content="你负责创建简洁有力的GitHub项目视频分镜脚本，包含开场、评测维度、例子解说和总结四个部分。对于例子解说，需要为每个例子创建一个独立分镜，并根据测试目标和效果给出客观简洁的评价。",
            ),
            model=self.model,
        )

        # 分镜脚本验证代理
        agents["storyboard_validator"] = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="分镜脚本验证者",
                content="你负责验证分镜脚本的质量、JSON格式正确性、引用素材的存在性和动效建议的合理性。确保每个例子都有对应的独立分镜，并包含基于测试结果的客观简洁评价。",
            ),
            model=self.model,
        )

        return agents

    def process_message(self, agent_name: str, message: str):
        """处理消息并返回响应"""
        agent = self.agents[agent_name]
        # 发送消息并获取响应
        response = agent.step(message)
        logger.debug(response)

        # 获取响应内容
        content = response.msgs[0].content

        # 对于分镜生成和验证结果，尝试提取JSON部分
        if agent_name in ["storyboard_generator", "storyboard_validator"]:
            # 首先尝试直接查找JSON数组开始标记
            if "[" in content and "]" in content:
                start_idx = content.find("[")
                end_idx = content.rfind("]") + 1
                if start_idx < end_idx:
                    json_content = content[start_idx:end_idx]
                    try:
                        # 验证是否为有效JSON
                        json.loads(json_content)
                        return json_content
                    except json.JSONDecodeError:
                        logger.warning(f"提取的内容不是有效的JSON: {json_content[:100]}...")

            # 查找```json代码块
            json_block_pattern = r"```(?:json)?\s*([\s\S]*?)```"
            matches = re.findall(json_block_pattern, content)
            if matches:
                for match in matches:
                    try:
                        # 验证是否为有效JSON
                        json.loads(match)
                        return match
                    except json.JSONDecodeError:
                        continue

        return content

    def _get_project_name_from_url(self) -> str:
        """从GitHub URL中提取项目名称"""
        project_url = self.github_config.get("project_url", "")
        if not project_url:
            return ""
            
        # 提取项目名称（URL的最后一部分）
        url_parts = project_url.rstrip('/').split('/')
        if len(url_parts) > 0:
            return url_parts[-1]
        return ""
        
    def _get_doc_paths_from_project(self) -> Dict[str, str]:
        """根据GitHub项目名称获取文档路径"""
        project_name = self._get_project_name_from_url()
        if not project_name:
            return {
                "project_doc": "",
                "example_doc": ""
            }
            
        # 构建项目相关的文档路径
        output_dir = os.path.join("output", project_name)
        return {
            "project_doc": os.path.join(output_dir, "project_analysis.md"),
            "example_doc": os.path.join(output_dir, "example_evaluation.md")
        }

    def load_documents(self, project_doc_path: str, example_doc_path: str) -> Dict[str, str]:
        """加载项目介绍文档和例子介绍文档"""
        result = {}
        
        # 加载项目介绍文档
        try:
            if os.path.exists(project_doc_path):
                with open(project_doc_path, 'r', encoding='utf-8') as f:
                    result['project_doc'] = f.read()
                print(f"项目介绍文档加载成功: {project_doc_path}")
            else:
                logging.warning(f"项目介绍文档不存在: {project_doc_path}")
                result['project_doc'] = "项目介绍文档未找到"
        except Exception as e:
            logging.error(f"加载项目介绍文档出错: {e}")
            result['project_doc'] = f"加载项目介绍文档出错: {str(e)}"
        
        # 加载例子介绍文档
        try:
            if os.path.exists(example_doc_path):
                with open(example_doc_path, 'r', encoding='utf-8') as f:
                    result['example_doc'] = f.read()
                print(f"例子介绍文档加载成功: {example_doc_path}")
            else:
                logging.warning(f"例子介绍文档不存在: {example_doc_path}")
                result['example_doc'] = "例子介绍文档未找到"
        except Exception as e:
            logging.error(f"加载例子介绍文档出错: {e}")
            result['example_doc'] = f"加载例子介绍文档出错: {str(e)}"
        
        return result

    def extract_media_references(self, document_texts: Dict[str, str]) -> List[str]:
        """从文档中提取媒体引用"""
        media_refs = []
        
        # 匹配常见的媒体文件路径
        patterns = [
            r'(?:!\[.*?\]\()(.*?)(?:\))',  # Markdown 图片
            r'<img\s+[^>]*src=[\'"](.*?)[\'"]',  # HTML 图片
            r'(?:src|href)=[\'"](.*?\.(?:png|jpg|jpeg|gif|mp4|webm|svg))[\'"]]',  # 其他引用
            r'[\'"](.*?\.(?:png|jpg|jpeg|gif|mp4|webm|svg))[\'"]',  # 引号中的媒体路径
            r'(?:^|\s)([\w\.\/\-]+\.(?:png|jpg|jpeg|gif|mp4|webm|svg))(?:$|\s)',  # 裸路径
        ]
        
        for doc_content in document_texts.values():
            for pattern in patterns:
                matches = re.findall(pattern, doc_content, re.IGNORECASE)
                for match in matches:
                    if match and match not in media_refs:
                        media_refs.append(match)
        
        return media_refs

    def generate_storyboard(self, documents: Dict[str, str]) -> str:
        """生成视频分镜脚本"""
        print("正在生成视频分镜脚本...")
        
        # 构建提示词
        prompt = GITHUB_STORYBOARD_PROMPT.format(
            project_doc=documents.get('project_doc', '项目文档未找到'),
            example_doc=documents.get('example_doc', '例子文档未找到'),
            manim_capabilities=self.manim_capabilities,
            storyboard_format=STORYBOARD_FORMAT
        )
        
        # 使用分镜脚本生成代理生成初始脚本
        storyboard_json = self.process_message("storyboard_generator", prompt)
        
        print("初始分镜脚本生成完成")
        return storyboard_json

    def validate_storyboard(self, storyboard_content: str, media_references: List[str]) -> str:
        """验证分镜脚本的素材和格式"""
        print("正在验证分镜脚本...")
        
        # 构建验证提示词
        prompt = STORYBOARD_VALIDATOR_PROMPT.format(
            storyboard_content=storyboard_content,
            manim_capabilities=self.manim_capabilities,
            mentioned_media=", ".join(media_references)
        )
        
        # 使用分镜脚本验证代理验证脚本
        validated_storyboard = self.process_message("storyboard_validator", prompt)
        
        print("分镜脚本验证完成")
        return validated_storyboard

    def create_github_video_storyboard(self) -> str:
        """创建GitHub视频讲解分镜脚本的主流程"""
        try:
            # 从GitHub项目URL获取文档路径
            doc_paths = self._get_doc_paths_from_project()
            project_doc_path = doc_paths.get("project_doc")
            example_doc_path = doc_paths.get("example_doc")
                
            if not project_doc_path or not os.path.exists(project_doc_path):
                raise ValueError(f"项目介绍文档不存在: {project_doc_path}. 请确保config中的github.project_url配置正确，并且output目录下有对应的文件。")
                
            if not example_doc_path or not os.path.exists(example_doc_path):
                raise ValueError(f"项目例子文档不存在: {example_doc_path}. 请确保config中的github.project_url配置正确，并且output目录下有对应的文件。")
                
            # 步骤1：加载文档
            print(f"步骤1: 加载项目文档...")
            documents = self.load_documents(project_doc_path, example_doc_path)
            
            # 提取媒体引用
            media_references = self.extract_media_references(documents)
            print(f"从文档中提取了 {len(media_references)} 个媒体引用")
            
            # 步骤2：生成初始分镜脚本
            print("步骤2: 生成视频分镜脚本...")
            storyboard_json = self.generate_storyboard(documents)
            
            # 步骤3：验证分镜脚本
            print("步骤3: 验证分镜脚本内容和格式...")
            if self.storyboard_config.get("media_validation", True):
                validated_storyboard = self.validate_storyboard(storyboard_json, media_references)
                final_result = validated_storyboard
            else:
                final_result = storyboard_json
            
            return final_result
            
        except Exception as e:
            logging.error(f"创建分镜脚本出错: {str(e)}")
            import traceback
            traceback.print_exc()
            raise


def main():
    """主函数，运行整个流程"""
    # 初始化Agent
    agent = GitHubStoryboardAgent()
    
    # 检查GitHub URL是否已配置
    project_name = agent._get_project_name_from_url()
    if not project_name:
        print("错误: 配置文件中未设置GitHub项目URL，请在config/config.yaml中配置github.project_url")
        return
    
    # 检查输入文件是否存在
    doc_paths = agent._get_doc_paths_from_project()
    project_doc_path = doc_paths.get("project_doc")
    example_doc_path = doc_paths.get("example_doc")
    
    if not os.path.exists(project_doc_path):
        print(f"错误: 项目介绍文档不存在: {project_doc_path}")
        print(f"请确保项目 {project_name} 的输入文件已准备好在 output/{project_name}/ 目录下")
        return
        
    if not os.path.exists(example_doc_path):
        print(f"错误: 例子介绍文档不存在: {example_doc_path}")
        print(f"请确保项目 {project_name} 的输入文件已准备好在 output/{project_name}/ 目录下")
        return

    print(f"开始为项目 {project_name} 创建视频讲解分镜脚本...")
    results = agent.create_github_video_storyboard()

    # 打印原始结果，用于调试
    print("=== 分镜脚本生成结果 ===")
    print(results[:500] + "..." if len(results) > 500 else results)

    # 提取JSON内容并保存
    print("正在提取JSON内容...")
    try:
        extracted_json = extract_json(results)
        
        # 保存到项目对应的目录
        output_dir = os.path.join("output", project_name)
        os.makedirs(output_dir, exist_ok=True)
        output_file = os.path.join(output_dir, "storyboard.json")
            
        print(f"正在保存到: {output_file}")

        save_success = save_json_content(extracted_json, output_file)
        if save_success:
            print(f"分镜脚本创建完成，结果已保存到 {output_file}")
        else:
            print("保存JSON内容失败")
    except Exception as e:
        print(f"处理JSON时出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
