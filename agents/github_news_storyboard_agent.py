import json
import os
import re
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Union

import yaml
from dotenv import load_dotenv
from camel.agents import ChatAgent
from camel.logger import set_log_level
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.types import ModelPlatformType
from loguru import logger

from utils.format import extract_json, save_json_content

load_dotenv()
set_log_level(level="WARNING")

# 必要的目录结构
os.makedirs("config", exist_ok=True)
os.makedirs("output", exist_ok=True)

# Storyboard JSON格式样例 - 与 storyboard.json 保持一致
STORYBOARD_FORMAT = """
[
    {{
        "分镜名": "分镜的简短标题",
        "内容要点": ["GitHub影响力数据", "价值", "数据对比", "悬念问题"],(每点不超过8字，最多5点）) 
        "分镜内容": "在这个开场中需要讲解的详细脚本内容"（不超过200字）,
        "素材名": "output/项目名/project_screenshot.mp4",
        "视觉动效建议": [
            使用display_video在full_screen区域显示项目架构图'output/项目名/architecture.png'，采用zoomIn动画效果
        ]
    }}
]
"""

# GitHub 项目视频讲解 Storyboard 提示词
GITHUB_STORYBOARD_PROMPT = """
你是一个专业的GitHub项目视频讲解脚本创作者，现在需要为一个GitHub项目创建简洁有力的视频分镜脚本。

请基于以下GitHub项目资料，创建一个引人入胜的视频脚本分镜：

项目介绍文档：
{project_doc}

视觉动效接口参考：
{manim_capabilities}

项目名称：
{project_name}

素材路径前缀：
output/{project_name}/

**重要提示**：
- 所有核心数据（如星标数、增长率、下载量等）必须严格来自项目介绍文档，不得编造
- 所有核心模块和核心能力必须完全基于项目介绍文档中明确提到的内容，不得无中生有
- 必须使用通俗易懂的语言描述项目功能，避免过度专业术语
- 核心能力部分的描述要全面且准确，不漏掉项目介绍文档中提到的任何重要功能
- 开篇分镜使和架构分镜调用display_video函数，播放视频和图片
- **重要：开篇分镜和模块关系分镜的素材名必须包含前缀"output/{project_name}/"**

请创建以下5个分镜：

1. **开篇分镜**
   分镜内容：
   - 描述项目Star数的影响力，要量化，重点是Star总数、增长趋势、同类项目对比优势等,有知名机构的比如谷歌、微软、OpenAI等需要包含
   - 突出项目的差异性特点，比如只有100行代码的Agent框架
   - 描述文档中提到的项目核心能力和应用价值
   - 表述可以使用震惊体，吸引用户，但不要过度夸张，不要编造任何未在文档中提到的数据
   素材名：
   - 必须设置为: "output/{project_name}/project_screenshot.mp4"
   视觉动效建议：
   - 使用display_video在full_screen区域显示项目截图，素材名需要包含完整路径，采用zoomIn动画效果
   - 包含输入数据的详细描述

2. **项目核心能力介绍分镜** 
   分镜内容：
   - 描述项目的核心功能和价值，不能遗漏编造，使用通俗易懂的语言
   - 用Markdown列表格式展示所有核心功能
   视觉动效建议：
   - 使用display_formatted_content函数展示Markdown列表
   - 输入参数content,相比分镜内容要简练，这个是用来屏幕上展现的
   - Markdown列表格式示例：
     ```
     - **自然语言处理**：支持多语言文本理解和生成
     - **图像识别**：实现高精度目标检测和场景分类
     - **知识图谱**：构建和查询复杂领域知识关系
     ```
3. **项目模块关系分镜**
    分镜内容：
   - 详细解释各模块的主要功能，模块之间如何协同工作，数据和控制如何流转
   - 批判性给出模块关系图的不足之处，比如模块划分不合理、功能重复等
   素材名：
   - 必须设置为: "output/{project_name}/module_relationships.png"
   视觉动效建议：
   - 使用display_video在full_screen区域显示项目架构图，素材名需要包含完整路径，采用zoomIn动画效果
   - 包含输入数据的详细描述

4. **延展应用分镜** 
   分镜内容：
   - 展示3个极有价值的应用案例（比如影响金融、生活、科研、教育、医疗等领域），每个案例需明确说明使用了项目的哪些具体能力，能解决了什么实际问题，有何特别优势
   - 应用案例展示使用Markdown列表格式
   视觉动效建议：
   - 使用display_formatted_content函数展示Markdown列表
   - 输入参数content,相比分镜内容要简练，这个是用来屏幕上展现的
  
5. **总结评测分镜**
   分镜内容：
   - 列出六个维度得分（1-10分）和评价理由（如"易用性：8分-配置简单但文档不全"）
     六个维度：核心功能完善性、可用性与易用性、项目活跃度、代码质量、架构设计、文档完备性
   - 每个维度得分的理由要简洁，不要超过10个字
   视觉动效建议：
   - 使用animate_chart生成雷达图
   - 雷达图动效建议，确保使用animate_chart时输入的是Dict, Dict的key是维度名称，value是维度得分
   - 包含输入数据的详细描述

视觉动效建议部分要求：
1. 每个分镜最多使用2个动效函数
2. 将多个文本类内容（如标题、正文、列表等）合并成一个markdown文本，调用一次display_formatted_content函数
3. 优先多媒体素材（图片、视频等）的动效展示
4. 每条动效建议需包含：接口函数名称、目标区域和主要参数
5. 动效函数的输入数据是否包含在动效果建议中
6. 开篇分镜使用素材"output/{project_name}/project_screenshot.mp4"，架构分镜使用素材"output/{project_name}/module_relationships.png"，调用display_video函数
7. 所有视觉动效函数严格参考"视觉动效接口参考"部分的接口函数定义、传入参数格式。

请严格按照以上JSON格式和要求输出结果，只返回JSON格式内容：
{storyboard_format}
"""

# Storyboard 素材验证提示词
STORYBOARD_VALIDATOR_PROMPT = """
你是一个专业的视频分镜脚本审核专家，需要审核以下分镜脚本的质量并验证其中引用的素材是否存在。

分镜脚本内容：{storyboard_content}

视觉动效接口参考：
{manim_capabilities}

项目文档中提到的媒体素材：{mentioned_media}

项目名称：
{project_name}

素材路径前缀：
output/{project_name}/

请检查以下几个关键方面：

1. **素材引用验证**
   - 检查每个分镜中引用的"素材名"是否在项目文档中被提及
   - 开篇分镜素材名必须是"output/{project_name}/project_screenshot.mp4"
   - 项目模块关系分镜素材名必须是"output/{project_name}/module_relationships.png"
   - 如果素材没有在文档中提及，必须将其设置为null（除了开篇和模块关系分镜）

2. **JSON格式正确性**
   - 确保输出符合标准JSON格式
   - 检查所有引号、括号是否闭合正确

3. **内容表述规范性**
   - 确保每个内容要点不超过8个字，且每个分镜不超过5个要点
   - 每个分镜的动效分析描述的函数是否在"视觉动效接口参考"部分给的里面
   - 每个分镜的视觉动效建议传入参数格式是否和"视觉动效接口参考"部分给的格式一致

4. **分镜内容针对性**
   - 开篇分镜是否直接使用数据说话，避免铺垫，并对star数据进行正向解读
   - 项目核心能力介绍分镜是否使用了Markdown列表格式展示所有核心功能
   - 应用案例分镜是否使用Markdown列表格式展示3个案例，每个案例是否明确说明使用了项目的哪些能力、解决了什么问题
   - 评测分镜是否涵盖六个维度的明确评分（1-10分）和简要理由(必须要包含六个维度)

5. **动效建议合理性**
   - 确保动效建议严格参考"视觉动效接口参考"部分的接口函数定义、传入参数格式。
   - 确保每条动效建议以中文自然语言描述
   - **每个分镜最多只能有2个动效函数调用**
   - **将多个文本类内容（如标题、正文、列表等）必须合并在一个display_formatted_content函数中**
   - 开篇分镜使和架构分镜调用display_video函数，播放视频和图片
   - 确认每条动效建议是否包含：接口函数名称、目标区域和主要参数
   - 雷达图动效建议，确保使用animate_chart时输入的是Dict, Dict的key是维度名称，value是维度得分
   - **对于核心能力介绍分镜和应用案例分镜**，验证是否使用display_formatted_content函数，并且确保内容设置为使用
   Markdown列表格式
   - **对于模块关系分镜**，确保使用了合适的函数来可视化展示模块关系
   - **对于总结与评测分镜**，确保使用了animate_chart生成雷达图，并包含了所有评测维度的数据

6. **标题震撼性与内容质量**
   - 标题是否震撼数据表达，包含数字，特别是星标

   
请根据审核结果修改分镜脚本，确保所有引用的素材都在文档中被提及（对于module_relationships.png可以直接使用），JSON格式正确，内容表述简洁且动效建议合理详细。

动效建议示例："使用display_video在full_screen区域显示项目架构图'architecture.png'，采用zoomIn动画效果"

请只返回修改后的JSON格式分镜脚本：
"""

# 定义GitHub项目视频讲解Agent类
class GitHubStoryboardAgent:
    def __init__(self, config_path="config/config.yaml"):
        # 创建默认配置文件（如果不存在）
        self._create_default_config_if_not_exists(config_path)

        # 加载配置
        self.load_config(config_path)

        # 初始化模型
        self.model = self._create_model()
        logging.warning("模型初始化完成")

        # 初始化代理
        self.agents = self._initialize_agents()
        logging.warning("代理初始化完成")
        
        # 加载Manim DSL v2功能说明
        self.manim_capabilities = self._load_manim_capabilities()
        logging.warning("Manim DSL v2功能加载完成")

    def _create_default_config_if_not_exists(self, config_path):
        """如果配置文件不存在，创建默认配置"""
        if not os.path.exists(config_path):
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            default_config = {
                "model": {
                    "type": "openai/gpt-4o-mini",
                    "api": {
                        "openai_compatibility_api_key": os.environ.get("OPENAI_API_KEY", ""),
                        "openai_compatibility_api_base_url": os.environ.get(
                            "OPENAI_API_BASE", "https://api.openai.com/v1"
                        ),
                    },
                },
                "agents": {
                    "storyboard_generator": {"enabled": True},
                    "storyboard_validator": {"enabled": True},
                },
                "files": {
                    "llm_interface_path": "docs/llm_interface.md"  # Manim DSL v2 功能文档路径
                },
                "github": {
                    "project_url": "",  # GitHub项目URL
                },
                "storyboard": {
                    "output_format": "json",
                    "required_sections": [
                        "开篇", 
                        "核心能力介绍", 
                        "模块关系",
                        "应用案例", 
                        "总结评测"
                    ],
                    "media_validation": True
                }
            }
            with open(config_path, "w") as f:
                yaml.dump(default_config, f)

    def _load_manim_capabilities(self) -> str:
        """加载Manim DSL v2的功能说明"""
        try:
            # 从配置中获取路径
            interface_path = os.path.join(os.getcwd(), "docs/dsl_reference_for_llm.md")
            if os.path.exists(interface_path):
                with open(interface_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return content
            else:
                logging.warning(f"未找到Manim DSL v2参考文档: {interface_path}")
                return "未能加载动画效果说明，将使用默认动画效果。请确保docs/dsl_reference_for_llm.md文件存在。"
        except Exception as e:
            logging.error(f"加载Manim功能说明出错: {e}")
            return "加载动画效果说明出错，将使用默认动画效果。"

    def load_config(self, config_path):
        """从YAML文件加载配置"""
        try:
            with open(config_path) as file:
                config = yaml.safe_load(file)
            # 设置配置属性
            self.model_config = config.get("model", {})
            self.files_config = config.get("files", {})
            self.agent_config = config.get("agents", {})
            self.storyboard_config = config.get("storyboard", {})
            self.github_config = config.get("github", {})
        except Exception as e:
            logging.error(f"加载配置出错: {str(e)}")
            raise

    def _create_model(self):
        """根据配置创建模型实例"""
        api_config = self.model_config.get("api", {})

        return ModelFactory.create(
            model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
            model_type=self.model_config.get("type", "openai/gpt-4o-mini"),
            api_key=api_config.get("openai_compatibility_api_key"),
            url=api_config.get("openai_compatibility_api_base_url"),
        )

    def _initialize_agents(self) -> dict:
        """初始化所有启用的代理及其系统提示和工具"""
        agents = {}

        # 分镜脚本生成代理
        agents["storyboard_generator"] = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="视频分镜脚本生成者",
                content="你负责创建简洁有力的GitHub项目视频分镜脚本，分别包含开篇、核心能力展示、应用案例和总结评测四个部分。开篇直接用数据说话展示项目影响力，核心能力要展示框架流程，应用案例需提供具体示例让用户恍然大悟，最后给出六个维度的评测和雷达图。",
            ),
            model=self.model,
        )

        # 分镜脚本验证代理
        agents["storyboard_validator"] = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="分镜脚本验证者",
                content="你负责验证分镜脚本的质量、JSON格式正确性、引用素材的存在性和动效建议的合理性。确保开篇直接用数据说话，应用案例展示具体且有惊喜感，评测分镜包含完整的六维度评分和雷达图动画效果。",
            ),
            model=self.model,
        )

        return agents

    def process_message(self, agent_name: str, message: str):
        """处理消息并返回响应"""
        agent = self.agents[agent_name]
        # 发送消息并获取响应
        response = agent.step(message)
        logger.debug(response)

        # 获取响应内容
        content = response.msgs[0].content

        # 对于分镜生成和验证结果，尝试提取JSON部分
        if agent_name in ["storyboard_generator", "storyboard_validator"]:
            # 首先尝试直接查找JSON数组开始标记
            if "[" in content and "]" in content:
                start_idx = content.find("[")
                end_idx = content.rfind("]") + 1
                if start_idx < end_idx:
                    json_content = content[start_idx:end_idx]
                    try:
                        # 验证是否为有效JSON
                        json.loads(json_content)
                        return json_content
                    except json.JSONDecodeError:
                        logger.warning(f"提取的内容不是有效的JSON: {json_content[:100]}...")

            # 查找```json代码块
            json_block_pattern = r"```(?:json)?\s*([\s\S]*?)```"
            matches = re.findall(json_block_pattern, content)
            if matches:
                for match in matches:
                    try:
                        # 验证是否为有效JSON
                        json.loads(match)
                        return match
                    except json.JSONDecodeError:
                        continue

        return content

    def _get_project_name_from_url(self) -> str:
        """从GitHub URL中提取项目名称"""
        project_url = self.github_config.get("project_url", "")
        if not project_url:
            return ""
            
        # 提取项目名称（URL的最后一部分）
        url_parts = project_url.rstrip('/').split('/')
        if len(url_parts) > 0:
            return url_parts[-1]
        return ""
        
    def _get_doc_paths_from_project(self) -> Dict[str, str]:
        """根据GitHub项目名称获取文档路径"""
        project_name = self._get_project_name_from_url()
        if not project_name:
            return {
                "project_doc": "",
            }
            
        # 构建项目相关的文档路径
        output_dir = os.path.join("output", project_name)
        return {
            "project_doc": os.path.join(output_dir, "project_analysis.md"),
        }

    def load_documents(self, project_doc_path: str) -> Dict[str, str]:
        """加载项目介绍文档"""
        result = {}
        
        # 加载项目介绍文档
        try:
            if os.path.exists(project_doc_path):
                with open(project_doc_path, 'r', encoding='utf-8') as f:
                    result['project_doc'] = f.read()
                print(f"项目介绍文档加载成功: {project_doc_path}")
            else:
                logging.warning(f"项目介绍文档不存在: {project_doc_path}")
                result['project_doc'] = "项目介绍文档未找到"
        except Exception as e:
            logging.error(f"加载项目介绍文档出错: {e}")
            result['project_doc'] = f"加载项目介绍文档出错: {str(e)}"
        
        return result

    def extract_media_references(self, document_texts: Dict[str, str]) -> List[str]:
        """从文档中提取媒体引用"""
        media_refs = []
        
        # 匹配常见的媒体文件路径
        patterns = [
            r'(?:!\[.*?\]\()(.*?)(?:\))',  # Markdown 图片
            r'<img\s+[^>]*src=[\'"](.*?)[\'"]',  # HTML 图片
            r'(?:src|href)=[\'"](.*?\.(?:png|jpg|jpeg|gif|mp4|webm|svg))[\'"]]',  # 其他引用
            r'[\'"](.*?\.(?:png|jpg|jpeg|gif|mp4|webm|svg))[\'"]',  # 引号中的媒体路径
            r'(?:^|\s)([\w\.\/\-]+\.(?:png|jpg|jpeg|gif|mp4|webm|svg))(?:$|\s)',  # 裸路径
        ]
        
        for doc_content in document_texts.values():
            for pattern in patterns:
                matches = re.findall(pattern, doc_content, re.IGNORECASE)
                for match in matches:
                    if match and match not in media_refs:
                        media_refs.append(match)
        
        return media_refs

    def generate_storyboard(self, documents: Dict[str, str]) -> str:
        """生成视频分镜脚本"""
        print("正在生成视频分镜脚本...")
        
        # 获取项目名称
        project_name = self._get_project_name_from_url()
        
        # 构建提示词
        prompt = GITHUB_STORYBOARD_PROMPT.format(
            project_doc=documents.get('project_doc', '项目文档未找到'),
            manim_capabilities=self.manim_capabilities,
            storyboard_format=STORYBOARD_FORMAT,
            project_name=project_name
        )
        
        # 使用分镜脚本生成代理生成初始脚本
        storyboard_json = self.process_message("storyboard_generator", prompt)
        
        print("初始分镜脚本生成完成")
        return storyboard_json

    def validate_storyboard(self, storyboard_content: str, media_references: List[str]) -> str:
        """验证分镜脚本的素材和格式"""
        print("正在验证分镜脚本...")
        
        # 获取项目名称
        project_name = self._get_project_name_from_url()
        
        # 构建验证提示词
        prompt = STORYBOARD_VALIDATOR_PROMPT.format(
            storyboard_content=storyboard_content,
            manim_capabilities=self.manim_capabilities,
            mentioned_media=", ".join(media_references),
            project_name=project_name
        )
        
        # 使用分镜脚本验证代理验证脚本
        validated_storyboard = self.process_message("storyboard_validator", prompt)
        
        print("分镜脚本验证完成")
        return validated_storyboard

    def create_github_video_storyboard(self) -> str:
        """创建GitHub视频讲解分镜脚本的主流程"""
        try:
            # 从GitHub项目URL获取文档路径
            doc_paths = self._get_doc_paths_from_project()
            project_doc_path = doc_paths.get("project_doc")
                
            if not project_doc_path or not os.path.exists(project_doc_path):
                raise ValueError(f"项目介绍文档不存在: {project_doc_path}. 请确保config中的github.project_url配置正确，并且output目录下有对应的文件。")
                
            # 步骤1：加载文档
            print(f"步骤1: 加载项目文档...")
            documents = self.load_documents(project_doc_path)
            
            # 提取媒体引用
            media_references = self.extract_media_references(documents)
            print(f"从文档中提取了 {len(media_references)} 个媒体引用")
            
            # 步骤2：生成初始分镜脚本
            print("步骤2: 生成视频分镜脚本...")
            storyboard_json = self.generate_storyboard(documents)
            
            # 步骤3：验证分镜脚本
            print("步骤3: 验证分镜脚本内容和格式...")
            if self.storyboard_config.get("media_validation", True):
                validated_storyboard = self.validate_storyboard(storyboard_json, media_references)
                final_result = validated_storyboard
            else:
                final_result = storyboard_json
            
            return final_result
            
        except Exception as e:
            logging.error(f"创建分镜脚本出错: {str(e)}")
            import traceback
            traceback.print_exc()
            raise


def main():
    """主函数，运行整个流程"""
    # 初始化Agent
    agent = GitHubStoryboardAgent()
    
    # 检查GitHub URL是否已配置
    project_name = agent._get_project_name_from_url()
    if not project_name:
        print("错误: 配置文件中未设置GitHub项目URL，请在config/config.yaml中配置github.project_url")
        return
    
    # 检查输入文件是否存在
    doc_paths = agent._get_doc_paths_from_project()
    project_doc_path = doc_paths.get("project_doc")
    
    if not os.path.exists(project_doc_path):
        print(f"错误: 项目介绍文档不存在: {project_doc_path}")
        print(f"请确保项目 {project_name} 的输入文件已准备好在 output/{project_name}/ 目录下")
        return

    print(f"开始为项目 {project_name} 创建视频讲解分镜脚本...")
    results = agent.create_github_video_storyboard()

    # 打印原始结果，用于调试
    print("=== 分镜脚本生成结果 ===")
    print(results[:500] + "..." if len(results) > 500 else results)

    # 提取JSON内容并保存
    print("正在提取JSON内容...")
    try:
        extracted_json = extract_json(results)
        
        # 保存到项目对应的目录
        output_dir = os.path.join("output", project_name)
        os.makedirs(output_dir, exist_ok=True)
        output_file = os.path.join(output_dir, "storyboard.json")
            
        print(f"正在保存到: {output_file}")

        save_success = save_json_content(extracted_json, output_file)
        if save_success:
            print(f"分镜脚本创建完成，结果已保存到 {output_file}")
        else:
            print("保存JSON内容失败")
    except Exception as e:
        print(f"处理JSON时出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
