from dotenv import load_dotenv

load_dotenv()

import datetime
import json
import logging
import os
import subprocess
import sys
import tempfile
import time
from typing import Any, Dict, List, Optional, Tuple

import git
import yaml
from camel.agents import ChatAgent
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.types import ModelPlatformType

# 设置日志级别为INFO，并配置日志格式
log_file = f"output/deployment_agent_log_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
os.makedirs(os.path.dirname(log_file), exist_ok=True)

# 配置根日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(),  # 同时输出到控制台
    ],
)

# 获取logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 系统提示词
SYSTEM_PROMPT = """
你是一个专业的部署自动化代理，负责帮助用户自动化从GitHub克隆、分析、安装和部署开源项目。

你的职责包括：
1. 分析GitHub仓库地址，提取基本信息
2. 克隆仓库到本地
3. 分析项目的README和安装说明
4. 识别合适的安装方法（如pip、npm、yarn、cargo等）
5. 自动执行安装步骤
6. 录制整个部署过程
7. 提供详细的部署报告和可能遇到的问题

请根据提供的GitHub地址，规划并执行完整的部署流程，确保每个步骤都有详细记录。
"""


class DeploymentAgent:
    """
    部署代理，负责自动部署GitHub开源项目并录屏

    职责：
    1. 接收GitHub仓库地址
    2. 下载和分析代码
    3. 自动执行安装和部署指令
    4. 录制整个部署过程
    5. 生成部署报告
    """

    def __init__(self, config_path="config/config.yaml"):
        """初始化部署代理"""
        # 加载配置
        self.load_config(config_path)

        # 初始化模型
        self.model = self._create_model()

        # 初始化ChatAgent
        self.agent = self._create_agent()

        # 初始化工作目录
        self.work_dir = tempfile.mkdtemp(prefix="deployment_")
        
        logger.info(f"部署代理已初始化，工作目录: {self.work_dir}")

    def load_config(self, config_path):
        """从YAML文件加载配置"""
        try:
            with open(config_path) as file:
                config = yaml.safe_load(file)
            # 设置配置属性
            self.model_config = config.get("model", {})
            self.deployment_config = config.get("deployment_agent", {})
            logger.info(f"从 {config_path} 加载配置")
        except Exception as e:
            logger.error(f"加载配置出错: {str(e)}")
            # 设置默认值
            self.model_config = {"temperature": 0.1, "max_tokens": 4096}
            self.deployment_config = {"recording_dir": "recordings"}

    def _create_model(self):
        """创建模型实例"""
        # 从配置中获取API设置
        api_config = self.model_config.get("api", {})
        
        return ModelFactory.create(
            model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
            model_type=self.model_config.get("type", "openai/gpt-4o-mini"),
            api_key=api_config.get("openai_compatibility_api_key"),
            url=api_config.get("openai_compatibility_api_base_url"),
        )

    def _create_agent(self):
        """创建ChatAgent实例"""
        return ChatAgent(model=self.model, system_message=SYSTEM_PROMPT)

    def clone_repository(self, repo_url: str) -> Tuple[bool, str, str]:
        """
        克隆GitHub仓库
        
        参数:
        - repo_url: GitHub仓库URL
        
        返回:
        - 成功状态, 仓库路径, 错误信息
        """
        try:
            # 从URL中提取仓库名称
            repo_name = repo_url.split("/")[-1]
            if repo_name.endswith(".git"):
                repo_name = repo_name[:-4]
                
            # 构建本地路径
            repo_path = os.path.join(self.work_dir, repo_name)
            
            logger.info(f"开始克隆仓库 {repo_url} 到 {repo_path}")
            
            # 克隆仓库
            git.Repo.clone_from(repo_url, repo_path)
            
            logger.info(f"仓库 {repo_url} 克隆成功")
            return True, repo_path, ""
        except Exception as e:
            error_msg = f"克隆仓库失败: {str(e)}"
            logger.error(error_msg)
            return False, "", error_msg

    def analyze_installation(self, repo_path: str) -> Dict[str, Any]:
        """
        分析仓库的安装说明，通过调用大模型解析出部署步骤
        
        参数:
        - repo_path: 仓库本地路径
        
        返回:
        - 包含安装步骤的字典
        """
        try:
            # 查找项目文档文件
            doc_files = {
                "readme": ["README.md", "README", "README.txt", "Readme.md", "readme.md"],
                "install": ["INSTALL.md", "INSTALL", "INSTALL.txt", "INSTALLATION.md", "SETUP.md"],
                "docs": ["docs/README.md", "docs/INSTALL.md", "doc/README.md", "doc/INSTALL.md", 
                         "docs/installation.md", "docs/setup.md", "docs/getting-started.md"],
                "contributing": ["CONTRIBUTING.md", "CONTRIBUTING"],
                "config": [".github/workflows/ci.yml", ".github/workflows/build.yml"]
            }
            
            collected_docs = {}
            
            # 收集所有相关文档
            for doc_type, file_patterns in doc_files.items():
                for pattern in file_patterns:
                    file_path = os.path.join(repo_path, pattern)
                    if os.path.exists(file_path):
                        with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
                            collected_docs[pattern] = f.read()
            
            # 检查是否找到任何文档
            if not collected_docs:
                logger.warning("未找到任何项目文档，尝试查找配置文件")
                
                # 查找可能的安装相关配置文件
                config_files = [
                    "setup.py", "package.json", "Cargo.toml", "requirements.txt", 
                    "Makefile", "CMakeLists.txt", "build.gradle", "pom.xml", 
                    "Dockerfile"
                ]
                
                for file in config_files:
                    file_path = os.path.join(repo_path, file)
                    if os.path.exists(file_path):
                        with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
                            collected_docs[file] = f.read()
            
            if not collected_docs:
                return {
                    "success": False,
                    "error": "未找到任何项目文档或配置文件",
                    "steps": []
                }
            
            # 准备发送给大模型的文档内容
            doc_content = ""
            for filename, content in collected_docs.items():
                doc_content += f"\n\n=== 文件: {filename} ===\n\n{content}"
            
            # 特别强调处理特殊表述的提示词
            prompt = f"""
            请作为一名专业的DevOps工程师，仔细分析以下项目文档，提取出精确的安装和部署步骤。
            我需要你识别出各种可能的安装方法，并提供详细的、可直接执行的命令行指令。

            文档内容:
            {doc_content}

            特别注意识别以下特殊表述和格式:
            1. 条件安装说明（如"如果你使用X系统，运行Y命令"）
            2. 平台特定指令（Windows/Linux/macOS的不同命令）
            3. 环境变量设置（export/set命令）
            4. 前置条件和依赖项（"在安装前，确保你已安装..."）
            5. 可选配置参数（"你可以通过--option参数自定义..."）
            6. 多步骤命令（需要在特定目录下执行的命令链）
            7. 表格格式中的命令说明
            8. 代码块（```）中的命令序列
            9. 不同安装方法之间的比较（"推荐方法 vs 手动方法"）
            10. 故障排除说明（"如果遇到X错误，尝试Y"）

            请将分析结果组织为以下JSON格式:
            ```json
            {{
                "project_name": "项目名称",
                "description": "项目简短描述（最多100字）",
                "installation_overview": "总体安装流程概述（最多200字）",
                "platform_requirements": [
                    {{
                        "platform": "平台名称（如Linux, macOS, Windows等）",
                        "requirements": ["要求1", "要求2"]
                    }}
                ],
                "dependencies": [
                    {{
                        "name": "依赖项名称",
                        "optional": true/false,
                        "installation": "安装依赖的命令或说明"
                    }}
                ],
                "installation_methods": [
                    {{
                        "method_name": "安装方法名称（如：源码安装/包管理器安装/Docker等）",
                        "recommended": true/false,
                        "platforms": ["适用平台1", "适用平台2"],
                        "prerequisites": ["前提条件1", "前提条件2"],
                        "steps": [
                            {{
                                "step": 1,
                                "description": "详细步骤描述，说明这一步的目的",
                                "command": "完整执行命令，确保可以直接复制粘贴运行",
                                "context": "执行命令的上下文（如：在项目根目录、需要特定环境变量等）",
                                "expected_output": "预期输出或成功标志的关键信息"
                            }}
                        ]
                    }}
                ],
                "verification": {{
                    "description": "验证安装成功的方法说明",
                    "commands": ["验证命令1", "验证命令2"]
                }},
                "common_issues": [
                    {{
                        "issue": "常见问题描述",
                        "solution": "解决方案"
                    }}
                ]
            }}
            ```
            
            请确保提供完整、准确的信息，特别是命令行部分。如果文档中有多种安装方法，请全部提取并标明推荐方法。
            """
            
            user_message = BaseMessage.make_user_message(role_name="User", content=prompt)
            logger.info("正在调用大模型解析安装说明...")
            
            response = self.agent.step(user_message)
            logger.info("大模型解析完成，正在提取结果...")
            
            # 提取JSON结果
            result = self._extract_json(response.msg.content)
            
            # 确保重要字段存在
            if "installation_methods" not in result or not result["installation_methods"]:
                # 尝试进行补充提示，专注于安装步骤提取
                logger.warning("初次解析未提取到安装方法，进行补充提示...")
                
                follow_up_prompt = f"""
                之前的解析似乎没有提取到完整的安装步骤。请再次仔细分析文档，特别关注以下内容:

                1. 查找"Installation"、"Getting Started"、"Quick Start"、"Usage"等章节
                2. 识别所有的命令行指令，特别是以`$`、`>`开头或者在代码块```中的命令
                3. 寻找类似"Clone the repository"、"Build from source"、"Install via pip/npm/cargo"等表述
                4. 注意识别编号列表中的安装步骤（1. xxx  2. xxx）
                5. 提取测试或运行项目的命令

                请至少提供一种完整的安装方法，包含具体的命令行指令。即使文档不完整，也请尽力推断必要的步骤。
                """
                
                user_message = BaseMessage.make_user_message(role_name="User", content=follow_up_prompt)
                response = self.agent.step(user_message)
                
                # 再次尝试提取JSON
                supplementary_result = self._extract_json(response.msg.content)
                
                # 如果补充提示得到了安装方法，合并结果
                if "installation_methods" in supplementary_result and supplementary_result["installation_methods"]:
                    result["installation_methods"] = supplementary_result["installation_methods"]
                    if "verification" in supplementary_result:
                        result["verification"] = supplementary_result["verification"]
            
            # 处理结果，确保兼容原始接口
            result["success"] = "installation_methods" in result and len(result["installation_methods"]) > 0
            
            # 将第一个安装方法作为主要安装步骤（保持兼容）
            if result["success"]:
                for method in result["installation_methods"]:
                    if method.get("steps"):
                        result["installation_steps"] = method["steps"]
                        break
            
            return result
        
        except Exception as e:
            error_msg = f"分析安装说明失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "steps": []
            }

    def _extract_json(self, text: str) -> dict[str, Any]:
        """从文本中提取JSON内容"""
        try:
            # 寻找JSON块
            if "```json" in text and "```" in text[text.find("```json") + 7:]:
                json_start = text.find("```json") + 7
                json_end = text.find("```", json_start)
                json_str = text[json_start:json_end].strip()
                return json.loads(json_str)
            
            # 尝试找到{}括起来的JSON
            json_start = text.find("{")
            json_end = text.rfind("}") + 1
            
            if json_start >= 0 and json_end > json_start:
                json_str = text[json_start:json_end]
                return json.loads(json_str)
            
            raise ValueError("未在响应中找到有效的JSON")
        except Exception as e:
            logger.error(f"提取JSON错误: {str(e)}")
            # 返回默认结构
            return {
                "project_name": "未知项目",
                "description": "无法提取项目描述",
                "dependencies": [],
                "environment": "未知",
                "installation_steps": [],
                "verification": "未知",
                "potential_issues": []
            }

    def execute_installation(self, repo_path: str, installation_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行安装步骤
        
        参数:
        - repo_path: 仓库本地路径
        - installation_info: 安装信息字典
        
        返回:
        - 包含执行结果的字典
        """
        results = []
        success = True
        
        logger.info(f"开始执行 {installation_info.get('project_name', '未知项目')} 的安装步骤")
        
        # 切换到仓库目录
        original_dir = os.getcwd()
        os.chdir(repo_path)
        
        try:
            # 执行每个安装步骤
            for step in installation_info.get("installation_steps", []):
                step_num = step.get("step", 0)
                command = step.get("command", "").strip()
                
                if not command:
                    continue
                
                logger.info(f"执行步骤 {step_num}: {command}")
                
                try:
                    # 执行命令
                    process = subprocess.Popen(
                        command,
                        shell=True,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True
                    )
                    
                    stdout, stderr = process.communicate(timeout=300)  # 设置5分钟超时
                    exit_code = process.returncode
                    
                    # 记录结果
                    step_result = {
                        "step": step_num,
                        "command": command,
                        "success": exit_code == 0,
                        "exit_code": exit_code,
                        "stdout": stdout,
                        "stderr": stderr
                    }
                    
                    results.append(step_result)
                    
                    if exit_code != 0:
                        logger.warning(f"步骤 {step_num} 执行失败: {stderr}")
                        success = False
                    else:
                        logger.info(f"步骤 {step_num} 执行成功")
                
                except subprocess.TimeoutExpired:
                    logger.error(f"步骤 {step_num} 执行超时")
                    step_result = {
                        "step": step_num,
                        "command": command,
                        "success": False,
                        "exit_code": -1,
                        "stdout": "",
                        "stderr": "命令执行超时"
                    }
                    results.append(step_result)
                    success = False
                    
                    # 尝试终止进程
                    process.kill()
                
                except Exception as e:
                    logger.error(f"步骤 {step_num} 执行出错: {str(e)}")
                    step_result = {
                        "step": step_num,
                        "command": command,
                        "success": False,
                        "exit_code": -1,
                        "stdout": "",
                        "stderr": str(e)
                    }
                    results.append(step_result)
                    success = False
        
        finally:
            # 恢复原始目录
            os.chdir(original_dir)
        
        return {
            "success": success,
            "results": results
        }

    def start_recording(self, output_file: str) -> Tuple[bool, Optional[subprocess.Popen], str]:
        """
        开始录制屏幕
        
        参数:
        - output_file: 录制输出文件路径
        
        返回:
        - 成功状态, 进程对象, 错误消息
        """
        try:
            # 确保asciinema已安装
            try:
                subprocess.run(["asciinema", "--version"], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            except (subprocess.SubprocessError, FileNotFoundError):
                logger.warning("asciinema未安装，尝试安装...")
                subprocess.run(["pip", "install", "asciinema"], check=True)
            
            # 创建输出目录
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            
            logger.info(f"开始录制: {output_file}")
            
            # 启动asciinema录制
            process = subprocess.Popen(
                ["asciinema", "rec", output_file],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待录制启动
            time.sleep(1)
            
            return True, process, ""
        
        except Exception as e:
            error_msg = f"开始录制失败: {str(e)}"
            logger.error(error_msg)
            return False, None, error_msg

    def stop_recording(self, process: subprocess.Popen) -> bool:
        """
        停止录制
        
        参数:
        - process: 录制进程对象
        
        返回:
        - 成功状态
        """
        try:
            if process:
                logger.info("正在停止录制...")
                
                # 发送Ctrl+D结束录制
                try:
                    process.communicate(input="\x04", timeout=5)
                except subprocess.TimeoutExpired:
                    # 如果超时，尝试强制终止进程
                    process.terminate()
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        process.kill()
                
                # 等待进程结束
                time.sleep(3)
                
                logger.info("录制已停止")
                return True
            return False
        except Exception as e:
            logger.error(f"停止录制失败: {str(e)}")
            return False

    def deploy(self, repo_url: str, recording_process: Optional[subprocess.Popen] = None, recording_file: Optional[str] = None) -> Dict[str, Any]:
        """
        执行完整的部署流程
        
        参数:
        - repo_url: GitHub仓库URL
        - recording_process: 可选的asciinema录制进程
        - recording_file: 可选的录制文件路径
        
        返回:
        - 包含部署结果的字典
        """
        # 如果没有提供录制进程和文件，创建新的
        if recording_process is None or recording_file is None:
            # 创建录制输出目录
            recording_dir = self.deployment_config.get("recording_dir", "recordings")
            os.makedirs(recording_dir, exist_ok=True)
            
            # 生成唯一文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            repo_name = repo_url.split("/")[-1]
            if repo_name.endswith(".git"):
                repo_name = repo_name[:-4]
            
            recording_file = os.path.join(recording_dir, f"{repo_name}_{timestamp}.cast")
            
            # 开始录制
            recording_success, recording_process, recording_error = self.start_recording(recording_file)
            
            if not recording_success:
                return {
                    "success": False,
                    "error": f"无法启动录制: {recording_error}",
                    "steps_completed": []
                }
        
        # 初始化结果
        result = {
            "repo_url": repo_url,
            "timestamp": datetime.datetime.now().strftime("%Y%m%d_%H%M%S"),
            "recording_file": recording_file,
            "steps_completed": [],
            "success": False
        }
        
        try:
            # 显示欢迎信息
            print(f"\n\n{'='*50}")
            print(f"    开始部署: {repo_url}")
            print(f"{'='*50}\n")
            
            # 步骤1: 克隆仓库
            print(f"\n🔍 [步骤1] 克隆仓库...")
            clone_success, repo_path, clone_error = self.clone_repository(repo_url)
            
            if not clone_success:
                print(f"❌ 克隆失败: {clone_error}")
                result["error"] = f"克隆仓库失败: {clone_error}"
                return result
            
            print(f"✅ 克隆成功，仓库保存至: {repo_path}")
            result["steps_completed"].append("clone")
            result["repo_path"] = repo_path
            
            # 步骤2: 分析安装说明
            print(f"\n🔍 [步骤2] 分析安装说明（正在解析README和相关文档）...")
            installation_info = self.analyze_installation(repo_path)
            
            if not installation_info.get("success", False):
                print(f"❌ 分析安装说明失败: {installation_info.get('error', '未知错误')}")
                result["error"] = f"分析安装说明失败: {installation_info.get('error', '未知错误')}"
                return result
            
            # 打印解析出的安装信息摘要
            self.print_installation_summary(installation_info)
            
            result["steps_completed"].append("analyze")
            result["installation_info"] = installation_info
            
            # 要求用户确认是否执行安装步骤
            print(f"\n{'='*50}")
            choice = input("是否继续执行安装步骤？(y/n): ")
            
            if choice.lower() != 'y':
                print("用户选择终止安装过程")
                result["success"] = False
                result["error"] = "用户终止安装"
                return result
            
            # 选择安装方法
            installation_method = None
            
            if "installation_methods" in installation_info and len(installation_info["installation_methods"]) > 1:
                print("\n请选择安装方法:")
                for idx, method in enumerate(installation_info["installation_methods"], 1):
                    recommended = "（推荐）" if method.get("recommended", False) else ""
                    print(f"{idx}. {method.get('method_name', f'方法 {idx}')} {recommended}")
                
                try:
                    method_choice = int(input("\n请输入安装方法编号: "))
                    if 1 <= method_choice <= len(installation_info["installation_methods"]):
                        installation_method = installation_info["installation_methods"][method_choice-1]
                    else:
                        # 默认使用第一个方法
                        installation_method = installation_info["installation_methods"][0]
                        print(f"选择无效，使用默认方法: {installation_method.get('method_name', '默认方法')}")
                except ValueError:
                    # 默认使用第一个方法
                    installation_method = installation_info["installation_methods"][0]
                    print(f"输入无效，使用默认方法: {installation_method.get('method_name', '默认方法')}")
            elif "installation_methods" in installation_info and installation_info["installation_methods"]:
                installation_method = installation_info["installation_methods"][0]
            
            # 步骤3: 执行安装
            print(f"\n🔧 [步骤3] 执行安装...")
            
            if installation_method:
                print(f"使用安装方法: {installation_method.get('method_name', '默认方法')}")
                installation_steps = installation_method.get("steps", [])
            else:
                installation_steps = installation_info.get("installation_steps", [])
            
            # 构建安装信息
            execution_info = {
                "project_name": installation_info.get("project_name", "未知项目"),
                "installation_steps": installation_steps
            }
            
            # 执行安装步骤
            installation_result = self.execute_installation(repo_path, execution_info)
            
            result["steps_completed"].append("install")
            result["installation_result"] = installation_result
            
            if not installation_result.get("success", False):
                print(f"\n❌ 安装过程中出现错误")
                
                # 显示失败的步骤
                for step_result in installation_result.get("results", []):
                    if not step_result.get("success", False):
                        print(f"\n步骤 {step_result.get('step', '?')} 失败:")
                        print(f"命令: \033[1;31m{step_result.get('command', '未知')}\033[0m")
                        print(f"错误: {step_result.get('stderr', '无错误信息')}")
                
                # 提供常见问题解决方案
                if "common_issues" in installation_info and installation_info["common_issues"]:
                    print("\n可能的解决方案:")
                    for issue in installation_info["common_issues"]:
                        print(f"- 问题: {issue.get('issue', '')}")
                        print(f"  解决: {issue.get('solution', '')}\n")
                
                result["error"] = "安装过程中出现错误"
                result["success"] = False
            else:
                print(f"\n✅ 安装成功完成")
                result["success"] = True
            
            # 验证安装
            if result["success"] and "verification" in installation_info and installation_info["verification"]:
                verification = installation_info["verification"]
                print(f"\n🔍 [步骤4] 验证安装...")
                
                if "commands" in verification and verification["commands"]:
                    original_dir = os.getcwd()
                    os.chdir(repo_path)
                    
                    for cmd in verification["commands"]:
                        print(f"\n执行验证命令: \033[1;32m{cmd}\033[0m")
                        try:
                            process = subprocess.Popen(
                                cmd,
                                shell=True,
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE,
                                text=True
                            )
                            
                            stdout, stderr = process.communicate(timeout=60)
                            if process.returncode == 0:
                                print(f"验证成功: \n{stdout[:300]}")
                            else:
                                print(f"验证失败: \n{stderr[:300]}")
                        except Exception as e:
                            print(f"验证执行错误: {str(e)}")
                    
                    os.chdir(original_dir)
                else:
                    print(f"验证说明: {verification.get('description', '无详细说明')}")
            
            result["steps_completed"].append("verify")
            
            # 总结
            print(f"\n{'='*50}")
            print(f"    部署总结")
            print(f"{'='*50}")
            print(f"📦 仓库: {repo_url}")
            print(f"📂 本地路径: {repo_path}")
            print(f"🎬 录制文件: {recording_file}")
            print(f"✅ 状态: {'成功' if result['success'] else '失败'}")
            
            if not result["success"] and "error" in result:
                print(f"❌ 错误: {result['error']}")
            
            print(f"\n{'='*50}")
            print(f"    部署完成")
            print(f"{'='*50}")
            
            return result
            
        except Exception as e:
            logger.error(f"部署过程出错: {str(e)}")
            result["error"] = f"部署过程出错: {str(e)}"
            result["success"] = False
            return result
        
        finally:
            # 无论结果如何，都停止录制
            if recording_process:
                self.stop_recording(recording_process)

    def print_installation_summary(self, info: Dict[str, Any]) -> None:
        """
        打印安装信息的详细摘要
        
        参数:
        - info: 安装信息字典
        """
        try:
            print("\n" + "="*60)
            print(f"    {info.get('project_name', '未知项目')} 安装摘要")
            print("="*60)
            
            # 项目描述
            if "description" in info:
                print(f"\n📝 项目描述:")
                print(f"   {info['description']}")
            
            # 安装概述
            if "installation_overview" in info:
                print(f"\n📋 安装概述:")
                print(f"   {info['installation_overview']}")
            
            # 平台要求
            if "platform_requirements" in info and info["platform_requirements"]:
                print("\n💻 平台要求:")
                for platform in info["platform_requirements"]:
                    print(f"  • {platform.get('platform', '未知')}:")
                    for req in platform.get("requirements", []):
                        print(f"    - {req}")
            
            # 依赖项
            if "dependencies" in info and info["dependencies"]:
                print("\n🔗 依赖项:")
                for dep in info["dependencies"]:
                    optional = "（可选）" if dep.get("optional", False) else ""
                    print(f"  • {dep.get('name', '未知')} {optional}")
                    if "installation" in dep:
                        print(f"    安装命令: {dep['installation']}")
            
            # 安装方法
            if "installation_methods" in info and info["installation_methods"]:
                methods = info["installation_methods"]
                print(f"\n🔧 安装方法 ({len(methods)}):")
                
                for i, method in enumerate(methods, 1):
                    recommended = "（推荐）" if method.get("recommended", False) else ""
                    platforms = ", ".join(method.get("platforms", ["所有平台"]))
                    
                    print(f"\n  {i}. {method.get('method_name', f'方法 {i}')} {recommended}")
                    print(f"     适用平台: {platforms}")
                    
                    # 前提条件
                    if "prerequisites" in method and method["prerequisites"]:
                        print("     前提条件:")
                        for prereq in method["prerequisites"]:
                            print(f"      - {prereq}")
                    
                    # 步骤
                    steps = method.get("steps", [])
                    if steps:
                        print(f"\n     安装步骤 ({len(steps)}):")
                        for j, step in enumerate(steps, 1):
                            desc = step.get("description", "无描述")
                            cmd = step.get("command", "无命令")
                            context = step.get("context", "")
                            
                            print(f"\n      步骤 {j}: {desc}")
                            if context:
                                print(f"      上下文: {context}")
                            
                            # 打印命令（用不同颜色标记）
                            if cmd:
                                print(f"      命令: \033[1;33m{cmd}\033[0m")
            
            # 验证方法
            if "verification" in info and info["verification"]:
                print("\n✅ 验证方法:")
                verification = info["verification"]
                print(f"   {verification.get('description', '无描述')}")
                
                if "commands" in verification and verification["commands"]:
                    print("   验证命令:")
                    for i, cmd in enumerate(verification["commands"], 1):
                        print(f"    {i}. \033[1;32m{cmd}\033[0m")
            
            # 常见问题
            if "common_issues" in info and info["common_issues"]:
                print("\n⚠️ 常见问题:")
                for i, issue in enumerate(info["common_issues"], 1):
                    print(f"  {i}. 问题: {issue.get('issue', '未知问题')}")
                    print(f"     解决方案: {issue.get('solution', '无解决方案')}")
            
            print("\n" + "="*60)
        
        except Exception as e:
            logger.error(f"打印安装摘要时出错: {str(e)}")
            print(f"\n打印安装摘要时出错: {str(e)}")

def main():
    """主函数，演示部署代理的使用"""
    # 打印欢迎信息
    print("\n" + "="*60)
    print("    GitHub项目自动部署与录屏工具")
    print("="*60)
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        repo_url = sys.argv[1]
    else:
        # 提示输入
        repo_url = input("\n请输入GitHub仓库URL: ")
    
    # 检查URL格式
    if not repo_url.startswith(("https://github.com/", "**************:")):
        print("\n❌ 错误: 请提供有效的GitHub仓库URL")
        sys.exit(1)
    
    # 初始化部署代理
    agent = DeploymentAgent()
    
    # 创建录制输出目录
    recording_dir = agent.deployment_config.get("recording_dir", "recordings")
    os.makedirs(recording_dir, exist_ok=True)
    
    # 生成唯一文件名
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    repo_name = repo_url.split("/")[-1]
    if repo_name.endswith(".git"):
        repo_name = repo_name[:-4]
    
    recording_file = os.path.join(recording_dir, f"{repo_name}_{timestamp}.cast")
    
    # 在程序开始就启动录制
    recording_success, recording_process, recording_error = agent.start_recording(recording_file)
    
    if not recording_success:
        print(f"\n❌ 无法启动录制: {recording_error}")
        sys.exit(1)
    
    try:
        # 执行部署，传入已启动的录制进程和文件路径
        result = agent.deploy(repo_url, recording_process, recording_file)
        
        # 显示录制文件信息
        if result["success"]:
            print(f"\n✅ 部署成功!")
        else:
            print(f"\n❌ 部署失败: {result.get('error', '未知错误')}")
        
        print(f"\n🎬 录制文件: {result['recording_file']}")
        print(f"   可使用命令查看录制: asciinema play {result['recording_file']}")
        
        # 提供下一步建议
        if result["success"] and "installation_info" in result:
            info = result["installation_info"]
            if "verification" in info and info["verification"].get("commands"):
                print("\n🔍 建议执行以下验证命令:")
                for cmd in info["verification"]["commands"]:
                    print(f"   {cmd}")
            
            print("\n📚 项目使用说明可查阅:")
            if "repo_path" in result:
                readme_path = os.path.join(result["repo_path"], "README.md")
                if os.path.exists(readme_path):
                    print(f"   {readme_path}")
        
    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"\n❌ 发生错误: {str(e)}")
        logger.exception("部署过程中发生异常")
    finally:
        # 确保无论如何都正确停止录制
        if recording_process:
            print("\n正在结束录制...")
            agent.stop_recording(recording_process)
            print(f"录制已保存至: {recording_file}")

if __name__ == "__main__":
    main()