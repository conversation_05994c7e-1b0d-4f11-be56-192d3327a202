#!/usr/bin/env python3
"""
大纲代理 (Outline Agent)

负责设计后续分镜讲解的提纲，包括：
1. 从给定URL通过info_collector_toolkit获取和解析markdown文件
2. 分解文章的体系结构和核心要点
3. 根据用户意图生成适合用户的分镜内容大纲
4. 通过角色扮演迭代修改大纲，直到满足覆盖要求
"""

from dotenv import load_dotenv

load_dotenv()

import datetime
import json
import logging
import os
import sys
from typing import Any, Dict, List, Optional

# 添加父目录到导入路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import yaml
from camel.agents import ChatAgent
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.societies import RolePlaying
from camel.types import ModelPlatformType, TaskType

from agents.intention_agent import IntentionAgent
from agents.video_agent.tools.info_collector_toolkit import InfoCollectorToolkit
from utils.format import save_json_content

# 设置日志级别为INFO，并配置日志格式
log_file = f"output/outline_agent_log_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
os.makedirs(os.path.dirname(log_file), exist_ok=True)

# 配置根日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(),  # 同时输出到控制台
    ],
)

# 获取logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 大纲输出的JSON格式
OUTLINE_FORMAT = """
```json
{
    "title": "文章标题",
    "key_concepts": ["核心概念1", "核心概念2", "核心概念3"],
    "structure": [
        {
            "section": "章节名称",
            "summary": "章节摘要",
            "key_points": ["要点1", "要点2", "要点3"],
            "importance": "high/medium/low"
        }
    ],
    "innovation_points": ["创新点1", "创新点2", "创新点3"],
    "storyboard_suggestions": [
        {
            "scene_name": "分镜名称",
            "key_elements": ["关键元素1", "关键元素2"],
            "focus_points": "应该重点讲解的内容",
            "user_relevance": "与用户意图的相关性解释"
        }
    ]
}
```
"""

# 大纲创作者角色提示
OUTLINE_CREATOR_PROMPT = """
你是一位专业的学术视频分镜大纲创作者，擅长将复杂的学术内容转化为清晰、引人入胜的视频讲解大纲。

作为大纲创作者，你的核心技能是：
1. 识别文章的核心概念、创新点和主要贡献
2. 理解并突出用户感兴趣的关键内容
3. 设计合理的分镜结构，确保内容连贯且易于理解
4. 为每个分镜设计适合的视觉元素和表现形式
5. 调整内容深度以匹配目标受众的需求
6. 确保大纲既有学术价值又能吸引观众

你需要根据文章内容和用户意图，创建一个全面而精确的大纲，确保：
1. 完整覆盖文章的所有重要内容和贡献
2. 特别突出符合用户意图的内容部分
3. 设计合理的分镜结构，便于后续制作
4. 为每个分镜提供清晰的焦点和关键元素
5. 建议适当的视觉表现方式
6. 确保内容编排符合逻辑，引导观众循序渐进地理解

大纲的输出格式应为JSON，包含以下关键字段：
- title: 视频标题，简明扼要地概括内容
- key_concepts: 文章的核心概念列表
- innovation_points: 文章的创新点和贡献列表
- structure: 文章结构摘要，包含每个部分的重要性标记
- storyboard_suggestions: 分镜建议列表，每个分镜包含场景名称、关键元素、重点内容和与用户的相关性

请以专业、准确而有创意的方式创建大纲，确保它既能忠实反映原文的学术价值，又能满足用户的特定需求和偏好。
"""

# 大纲审阅者角色提示
OUTLINE_REVIEWER_PROMPT = """
你是一位资深的学术视频分镜大纲审阅专家，擅长评估和优化视频讲解大纲的质量和有效性。

作为大纲审阅者，你的核心职责是：
1. 全面评估大纲是否涵盖了原文的所有重要内容
2. 检查大纲是否准确反映了用户的意图和需求
3. 评估分镜安排的逻辑性和连贯性
4. 提出具体的改进建议，包括内容、结构和表现形式
5. 确保大纲在学术准确性和观众吸引力之间取得平衡
6. 指出大纲中的任何遗漏、误解或不足

在审阅大纲时，你应当特别注意：
1. 核心概念是否被准确识别和充分强调
2. 创新点是否被明确突出
3. 分镜结构是否合理且便于理解
4. 用户意图是否得到充分满足
5. 内容的专业性和通俗性之间是否平衡
6. 是否有遗漏的重要内容

你的反馈应当既有建设性又有具体的改进建议，指出大纲的优点的同时不回避其问题和不足。你的目标是帮助创作者完善大纲，使其更好地服务于用户需求和学术内容的传播。

请以专业、直接而有建设性的方式提供反馈，确保每一条建议都有助于提升大纲的质量和有效性。
"""

class OutlineAgent:
    """
    大纲代理，负责设计后续分镜讲解的提纲

    职责：
    1. 从给定URL通过info_collector_toolkit获取和解析markdown文件
    2. 分解文章的体系结构和核心要点
    3. 根据用户意图生成适合用户的分镜内容大纲
    4. 通过角色扮演迭代修改大纲，直到满足覆盖要求
    """

    def __init__(self, config_path="config/config.yaml"):
        """初始化大纲代理"""
        # 加载配置
        self.load_config(config_path)

        # 初始化模型
        self.model = self._create_model()

        # 初始化工具包
        self.info_collector = InfoCollectorToolkit(use_cache=True)
        
        # 初始化意图代理
        self.intention_agent = IntentionAgent(config_path=config_path)
        
        logger.info("大纲代理初始化完成")

    def load_config(self, config_path):
        """从YAML文件加载配置"""
        try:
            with open(config_path) as file:
                config = yaml.safe_load(file)
            # 设置配置属性
            self.model_config = config.get("model", {})
            self.file_config = config.get("files", {})
            self.pdf_config = config.get("pdf", {})
            self.agent_config = config.get("agents", {})
            
            # 加载大纲相关配置
            self.outline_config = config.get("outline", {
                "max_rounds": 3,
                "query_template": "我想了解这篇文章: {url}",
                "user_profile": {
                    "education_level": "研究生",
                    "field": "计算机科学",
                    "interests": ["人工智能", "机器学习", "自然语言处理"],
                    "expertise_level": "中级",
                    "content_preferences": {
                        "style": "通俗易懂",
                        "depth": "中等",
                        "format": "视频讲解",
                    },
                },
                "content_topics": ["论文解读", "学术分享", "技术讲解"]
            })
            
            logger.info("从 %s 加载配置", config_path)
        except Exception as e:
            logger.error(f"加载配置出错: {str(e)}")
            # 设置默认值
            self.model_config = {"type": "openai/gpt-4o-mini"}
            self.file_config = {
                "outline_file": "output/outline.json",
                "storyboard_file": "output/storyboard.json",
                "content_file": "output/paper_content.json",
            }
            self.pdf_config = {"url": "", "output_dir": "pdf_output/"}
            self.agent_config = {}
            self.outline_config = {
                "max_rounds": 3,
                "query_template": "我想了解这篇文章: {url}",
                "user_profile": {
                    "education_level": "研究生",
                    "field": "计算机科学",
                    "interests": ["人工智能", "机器学习", "自然语言处理"],
                    "expertise_level": "中级",
                    "content_preferences": {
                        "style": "通俗易懂",
                        "depth": "中等",
                        "format": "视频讲解",
                    },
                },
                "content_topics": ["论文解读", "学术分享", "技术讲解"]
            }

    def _create_model(self):
        """创建模型实例"""
        # 从配置中获取API设置
        api_config = self.model_config.get("api", {})
        
        return ModelFactory.create(
            model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
            model_type=self.model_config.get("type", "openai/gpt-4o-mini"),
            api_key=api_config.get("openai_compatibility_api_key"),
            url=api_config.get("openai_compatibility_api_base_url"),
        )

    def collect_article_content(self, url: str) -> str:
        """
        收集文章内容
        
        参数:
        - url: 文章URL
        
        返回:
        - str: 文章内容
        """
        logger.info(f"开始从URL收集文章内容: {url}")
        
        # 首先检查是否为PDF链接
        if url.lower().endswith(".pdf") or "pdf" in url.lower():
            logger.info("检测到PDF链接，使用PDF处理工具")
            try:
                # 获取PDF输出目录
                output_dir = self.pdf_config.get("output_dir", "pdf_output/")
                # 确保输出目录存在
                os.makedirs(output_dir, exist_ok=True)
                
                logger.info(f"PDF输出目录: {output_dir}")
                
                # 使用PDF工具包处理
                result = self.info_collector.process_pdf(pdf_path=url)
                
                if isinstance(result, dict) and "markdown_file" in result:
                    markdown_file = result["markdown_file"]
                    logger.info(f"PDF处理成功，生成Markdown文件: {markdown_file}")
                    
                    # 读取生成的Markdown文件内容
                    try:
                        with open(markdown_file, "r", encoding="utf-8") as f:
                            content = f.read()
                        
                        # 保存内容到标准位置
                        content_file = "output/paper_content.md"
                        os.makedirs(os.path.dirname(content_file), exist_ok=True)
                        with open(content_file, "w", encoding="utf-8") as f:
                            f.write(content)
                        logger.info(f"PDF内容已保存到 {content_file}")
                        
                        return content
                    except Exception as e:
                        logger.error(f"读取Markdown文件失败: {str(e)}")
                
                if isinstance(result, dict) and "error" in result:
                    logger.error(f"PDF处理失败: {result['error']}")
            except Exception as e:
                logger.error(f"PDF处理异常: {str(e)}")
        
        # 如果不是PDF或PDF处理失败，使用网页提取方法
        try:
            logger.info("使用网页内容提取方法")
            
            # 提取网页内容
            result = self.info_collector.extract_web_content(
                url=url,
                use_javascript=True,
                output_format="markdown"
            )
            
            if isinstance(result, dict) and "error" in result:
                logger.error(f"提取网页内容失败: {result['error']}")
                return ""
            
            if isinstance(result, dict) and "content" in result:
                content = result["content"]
                logger.info(f"成功获取文章内容，长度: {len(content)} 字符")
                
                # 保存文章内容到文件
                content_file = "output/paper_content.md"
                os.makedirs(os.path.dirname(content_file), exist_ok=True)
                with open(content_file, "w", encoding="utf-8") as f:
                    f.write(content)
                logger.info(f"文章内容已保存到 {content_file}")
                
                return content
            
            logger.error("未能从结果中提取文章内容")
            return ""
            
        except Exception as e:
            logger.error(f"收集文章内容出错: {str(e)}")
            return ""

    def process_pdf_from_config(self) -> str:
        """
        直接从配置文件获取PDF URL并处理
        
        返回:
        - str: 处理后的PDF内容
        """
        # 从配置中获取PDF URL
        pdf_url = self.pdf_config.get("url", "")
        if not pdf_url:
            logger.error("配置中未提供PDF URL")
            return ""
        
        logger.info(f"从配置中获取PDF URL: {pdf_url}")
        
        # 使用collect_article_content处理PDF
        return self.collect_article_content(pdf_url)

    def analyze_intention(self, query: str, user_id: Optional[str] = None, user_profile: Dict[str, Any] = None, content_topics: List[str] = None) -> Dict[str, Any]:
        """
        分析用户意图
        
        参数:
        - query: 用户查询
        - user_id: 用户ID
        - user_profile: 用户画像
        - content_topics: 内容主题
        
        返回:
        - Dict[str, Any]: 意图分析结果
        """
        logger.info("开始分析用户意图")
        try:
            # 使用意图代理分析用户意图
            intention_result = self.intention_agent.analyze_intent(
                query=query,
                user_id=user_id,
                user_profile=user_profile,
                content_topics=content_topics,
            )
            
            logger.info(f"意图分析完成: 主要意图 = {', '.join(intention_result.get('primary_intent', []))}")
            return intention_result
        except Exception as e:
            logger.error(f"分析用户意图出错: {str(e)}")
            # 返回默认意图分析结果
            return {
                "primary_intent": ["理解与学习"],
                "secondary_intent": ["洞察分析"],
                "motivation": "了解学术内容和前沿知识",
                "expected_response": "清晰解释复杂概念",
                "related_topics": [],
                "content_style": {
                    "tone": "专业中立",
                    "depth": "中等",
                    "format": "视频讲解",
                    "personalization_aspects": ["知识深度", "逻辑清晰", "易于理解"]
                },
                "probing_questions": [
                    "你希望深入了解哪些具体方面？",
                    "你计划如何应用这些知识？",
                    "你对这个主题的现有了解程度如何？",
                ],
                "reasoning_process": "用户可能希望学习并理解复杂的学术内容",
                "detailed_reasoning": {"summary": "用户查询表明学习意图", "steps": []},
                "rewritten_query": {"original": query, "rewritten": query, "explanation": ""},
            }

    def generate_outline(self, article_content: str, intention_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成初始大纲
        
        参数:
        - article_content: 文章内容
        - intention_analysis: 意图分析结果
        
        返回:
        - Dict[str, Any]: 大纲内容
        """
        logger.info("开始生成初始大纲")
        
        # 截断文章内容以避免超过模型的最大令牌限制
        max_content_length = 160000  # 设置一个最大长度
        if len(article_content) > max_content_length:
            logger.info(f"截断文章内容，从 {len(article_content)} 至 {max_content_length} 字符")
            article_content = article_content[:max_content_length] + "...[内容已截断]"
        
        try:
            # 提取用户意图相关信息
            primary_intent = ", ".join(intention_analysis.get("primary_intent", ["理解与学习"]))
            secondary_intent = ", ".join(intention_analysis.get("secondary_intent", ["洞察分析"]))
            motivation = intention_analysis.get("motivation", "了解学术内容")
            content_style = intention_analysis.get("content_style", {})
            tone = content_style.get("tone", "专业中立")
            depth = content_style.get("depth", "中等")
            
            # 构建用户意图描述
            user_intent_description = f"""
            用户主要意图: {primary_intent}
            次要意图: {secondary_intent}
            潜在动机: {motivation}
            内容风格偏好:
            - 语调: {tone}
            - 深度: {depth}
            - 个性化要素: {', '.join(content_style.get('personalization_aspects', []))}
            """
            
            # 创建ChatAgent
            system_prompt = f"""
            你是一个专业的学术内容分析和大纲创作专家。
            
            请分析提供的学术文章内容，并根据用户意图创建一个详细的分镜大纲。
            
            用户意图信息:
            {user_intent_description}
            
            你的任务是：
            1. 分析文章的整体结构和核心概念
            2. 识别文章的主要章节和关键要点
            3. 找出创新点和重要发现
            4. 设计适合视频讲解的分镜大纲
            5. 确保大纲符合用户的意图和风格偏好
            
            请确保大纲的完整性和逻辑性，不遗漏任何重要内容，同时突出重点，适合用户的理解水平。
            
            请以JSON格式输出大纲，格式如下：
            {OUTLINE_FORMAT}
            """
            
            # 创建Agent
            agent = ChatAgent(
                system_message=system_prompt,
                model=self.model
            )
            
            # 构建用户消息
            user_message = f"""
            请分析以下学术文章内容，生成详细的视频分镜大纲：
            
            {article_content}
            
            请特别注意以下方面：
            1. 确保大纲涵盖文章的所有重要部分
            2. 适当突出符合用户意图的内容
            3. 清晰标识创新点和核心概念
            4. 为每个分镜提供明确的重点
            5. 输出必须是有效的JSON格式
            """
            
            # 获取回复
            response = agent.step(user_message)
            
            # 从回复中提取JSON
            outline_json = self._extract_json(response.msg.content)
            
            logger.info("初始大纲生成完成")
            return outline_json
            
        except Exception as e:
            logger.error(f"生成大纲出错: {str(e)}")
            # 返回默认大纲
            return {
                "title": "未能解析文章标题",
                "key_concepts": ["未能提取核心概念"],
                "structure": [
                    {
                        "section": "文章主体",
                        "summary": "无法生成摘要",
                        "key_points": ["未能提取关键点"],
                        "importance": "high"
                    }
                ],
                "innovation_points": ["未能提取创新点"],
                "storyboard_suggestions": [
                    {
                        "scene_name": "概述",
                        "key_elements": ["文章概览"],
                        "focus_points": "介绍文章的主要内容",
                        "user_relevance": "提供基础了解"
                    }
                ]
            }

    def refine_outline(self, article_content: str, initial_outline: Dict[str, Any], intention_analysis: Dict[str, Any], max_rounds: int = 3) -> Dict[str, Any]:
        """
        通过角色扮演迭代优化大纲
        
        参数:
        - article_content: 文章内容
        - initial_outline: 初始大纲
        - intention_analysis: 意图分析结果
        - max_rounds: 最大迭代轮数
        
        返回:
        - Dict[str, Any]: 优化后的大纲
        """
        logger.info("开始通过角色扮演迭代优化大纲，最大轮数: %d", max_rounds)
        
        # 截断文章内容
        max_content_length = 100000  # 设置一个较小的最大长度
        if len(article_content) > max_content_length:
            logger.info(f"截断用于迭代优化的文章内容，从 {len(article_content)} 至 {max_content_length} 字符")
            article_content = article_content[:max_content_length] + "...[内容已截断]"
        
        # 将初始大纲转换为JSON字符串
        initial_outline_json = json.dumps(initial_outline, ensure_ascii=False, indent=2)
        
        # 提取用户意图相关信息
        primary_intent = ", ".join(intention_analysis.get("primary_intent", ["理解与学习"]))
        secondary_intent = ", ".join(intention_analysis.get("secondary_intent", ["洞察分析"]))
        motivation = intention_analysis.get("motivation", "了解学术内容")
        content_style = intention_analysis.get("content_style", {})
        
        # 构建用户意图描述
        user_intent_description = f"""
        用户主要意图: {primary_intent}
        次要意图: {secondary_intent}
        潜在动机: {motivation}
        内容风格偏好:
        - 语调: {content_style.get("tone", "专业中立")}
        - 深度: {content_style.get("depth", "中等")}
        - 格式: {content_style.get("format", "视频讲解")}
        - 个性化要素: {', '.join(content_style.get("personalization_aspects", []))}
        """
        
        # 准备任务内容，使用原始字符串模板避免格式化问题
        task_content = f"""
        请分析以下文章内容，并优化视频分镜大纲。
        
        文章内容摘要：
        {article_content[:5000]}...（文章内容已截断）
        
        初始大纲：
        {initial_outline_json}
        
        用户意图信息：
        {user_intent_description}
        
        请通过大纲创作者和大纲审阅者的对话，共同优化这个大纲，确保它：
        1. 完整覆盖文章的所有重要内容
        2. 合理突出符合用户意图的部分
        3. 准确识别核心概念和创新点
        4. 分镜划分合理，便于视频讲解
        5. 结构清晰，逻辑连贯
        
        最终输出应当是一个改进的JSON格式大纲，格式与初始大纲相同，包含以下关键字段：
        - title: 视频标题
        - key_concepts: 核心概念列表
        - structure: 文章结构
        - innovation_points: 创新点列表
        - storyboard_suggestions: 分镜建议列表
        """
        
        # 将角色提示合并到任务中
        complete_task_content = f"""
        {task_content}
        
        ## 角色定位
        
        大纲创作者(Outline Creator)：
        {OUTLINE_CREATOR_PROMPT}
        
        大纲审阅者(Outline Reviewer)：
        {OUTLINE_REVIEWER_PROMPT}
        
        ## 对话流程安排
        1. 大纲创作者首先基于初始大纲提出改进版本
        2. 大纲审阅者针对改进版本提出具体评价和建议
        3. 大纲创作者根据审阅者的反馈进一步优化大纲
        4. 重复以上步骤，直到达到满意的结果
        5. 最后，大纲创作者提供最终优化后的JSON格式大纲
        """
        
        try:
            # 设置角色扮演
            role_playing = RolePlaying(
                # 设置大纲创作者为助手角色
                assistant_role_name="Outline Creator",
                assistant_agent_kwargs={"model": self.model},
                # 设置大纲审阅者为用户角色
                user_role_name="Outline Reviewer",
                user_agent_kwargs={"model": self.model},
                # 任务参数
                task_prompt=complete_task_content,
                task_type=TaskType.AI_SOCIETY,
                with_task_specify=False,  # 禁用task_specify避免报错
                # 附加配置
                output_language="chinese",
            )
            
            # 开始对话
            logger.info("开始角色对话")
            messages = []
            
            # 初始化对话
            chat_history = role_playing.init_chat()
            messages.append(chat_history)
            
            # 跟踪当前大纲
            current_outline = initial_outline
            
            # 进行多轮对话
            for round_num in range(max_rounds * 2):  # 每轮包含两步对话
                logger.info(f"对话步骤 {round_num + 1}")
                try:
                    # 进行对话步骤
                    assistant_response, user_response = role_playing.step(chat_history)
                    
                    # 从响应中获取消息
                    assistant_message = assistant_response.msg
                    user_message = user_response.msg
                    
                    # 添加到历史记录
                    chat_history = assistant_message
                    messages.append(assistant_message)
                    messages.append(user_message)
                    
                    # 尝试从助手消息中提取JSON
                    try:
                        extracted_outline = self._extract_json(assistant_message.content)
                        if extracted_outline and isinstance(extracted_outline, dict) and "title" in extracted_outline:
                            current_outline = extracted_outline
                            logger.info(f"步骤 {round_num + 1} 从创作者消息中提取到有效大纲")
                    except Exception as e:
                        logger.warning(f"无法从步骤 {round_num + 1} 的创作者消息中提取JSON: {str(e)}")
                    
                    # 尝试从用户消息中提取JSON
                    try:
                        extracted_outline = self._extract_json(user_message.content)
                        if extracted_outline and isinstance(extracted_outline, dict) and "title" in extracted_outline:
                            current_outline = extracted_outline
                            logger.info(f"步骤 {round_num + 1} 从审阅者消息中提取到有效大纲")
                    except Exception as e:
                        logger.warning(f"无法从步骤 {round_num + 1} 的审阅者消息中提取JSON: {str(e)}")
                    
                    # 如果已经完成了足够的轮次，或者对话似乎已经收敛，则退出
                    if round_num >= max_rounds * 2 - 1 or (round_num >= 3 and "最终大纲" in assistant_message.content):
                        logger.info("对话已完成足够的轮次或已收敛")
                        break
                        
                except Exception as e:
                    logger.error(f"对话步骤出错: {str(e)}")
                    break
            
            logger.info("角色对话完成，迭代优化结束")
            
            # 确保最终大纲是有效的
            if current_outline != initial_outline:
                logger.info("成功优化大纲")
                return current_outline
            else:
                logger.warning("未能成功优化大纲，返回初始大纲")
                return initial_outline
            
        except Exception as e:
            logger.error(f"迭代优化大纲出错: {str(e)}")
            return initial_outline

    def _extract_json(self, text: str) -> Dict[str, Any]:
        """从文本中提取JSON内容"""
        try:
            # 查找JSON代码块
            json_pattern = r"```(?:json)?(.*?)```"
            import re
            matches = re.findall(json_pattern, text, re.DOTALL)
            
            if matches:
                for match in matches:
                    try:
                        # 尝试解析JSON
                        json_str = match.strip()
                        return json.loads(json_str)
                    except:
                        continue
            
            # 如果没有在代码块中找到，尝试在文本中查找JSON
            start_idx = text.find("{")
            end_idx = text.rfind("}") + 1
            
            if start_idx >= 0 and end_idx > start_idx:
                json_str = text[start_idx:end_idx]
                return json.loads(json_str)
                
            raise ValueError("未找到有效的JSON内容")
            
        except Exception as e:
            logger.error(f"提取JSON出错: {str(e)}")
            raise

    def run(self, url: str = None, query: str = None, user_id: str = None, user_profile: Dict[str, Any] = None, content_topics: List[str] = None, max_rounds: int = 3) -> Dict[str, Any]:
        """
        运行大纲代理的完整流程
        
        参数:
        - url: 文章URL，不提供则使用配置中的URL
        - query: 用户查询，不提供则构建默认查询
        - user_id: 用户ID
        - user_profile: 用户画像
        - content_topics: 内容主题
        - max_rounds: 最大迭代轮数
        
        返回:
        - Dict[str, Any]: 包含大纲内容的结果字典
        """
        result = {}
        
        # 1. 确定文章URL
        if not url:
            url = self.pdf_config.get("url", "")
            if not url:
                logger.error("未提供文章URL")
                return {"error": "未提供文章URL"}
        
        logger.info(f"开始处理文章URL: {url}")
        result["article_url"] = url
        
        # 2. 确定用户查询
        if not query:
            query = f"我想了解这篇文章: {url}"
        
        logger.info(f"用户查询: {query}")
        result["query"] = query
        
        # 3. 收集文章内容
        logger.info("开始收集文章内容...")
        article_content = self.collect_article_content(url)
        if not article_content:
            logger.error("获取文章内容失败")
            return {"error": "获取文章内容失败"}
        
        logger.info(f"文章内容收集完成，长度: {len(article_content)} 字符")
        result["article_content_length"] = len(article_content)
        
        # 4. 分析用户意图
        logger.info("开始分析用户意图...")
        intention_analysis = self.analyze_intention(query, user_id, user_profile, content_topics)
        
        logger.info(f"用户意图分析完成: 主要意图 = {', '.join(intention_analysis.get('primary_intent', []))}")
        result["intention_analysis"] = intention_analysis
        
        # 5. 生成初始大纲
        logger.info("开始生成初始大纲...")
        initial_outline = self.generate_outline(article_content, intention_analysis)
        
        if not initial_outline or not isinstance(initial_outline, dict) or "title" not in initial_outline:
            logger.error("生成初始大纲失败")
            return {**result, "error": "生成初始大纲失败"}
        
        logger.info("初始大纲生成完成")
        result["initial_outline"] = initial_outline
        
        # 保存初始大纲到临时文件
        initial_outline_file = "output/initial_outline.json"
        save_json_content(initial_outline, initial_outline_file)
        logger.info(f"初始大纲已保存到 {initial_outline_file}")
        
        # 6. 迭代优化大纲
        logger.info(f"开始迭代优化大纲，最大轮数: {max_rounds}...")
        final_outline = self.refine_outline(article_content, initial_outline, intention_analysis, max_rounds)
        
        logger.info("大纲迭代优化完成")
        result["final_outline"] = final_outline
        
        # 7. 保存最终大纲到文件
        outline_file = self.file_config.get("outline_file", "output/outline.json")
        save_json_content(final_outline, outline_file)
        logger.info(f"最终大纲已保存到 {outline_file}")
        result["outline_file"] = outline_file
        
        return result

def main():
    """主函数，运行大纲代理"""
    # 解析命令行参数
    import argparse
    parser = argparse.ArgumentParser(description="生成文章分镜大纲")
    parser.add_argument("--config", default="config/config.yaml", help="配置文件路径")
    parser.add_argument("--url", help="文章URL，不提供则使用配置文件中的URL")
    parser.add_argument("--query", help="用户查询，不提供则使用配置中的查询模板")
    parser.add_argument("--rounds", type=int, help="最大迭代轮数，不提供则使用配置中的值")
    parser.add_argument("--from-config", action="store_true", help="直接从配置文件获取并处理PDF")
    args = parser.parse_args()
    
    # 初始化大纲代理
    agent = OutlineAgent(config_path=args.config)
    
    # 确定最大迭代轮数（优先使用命令行参数，其次使用配置文件）
    max_rounds = args.rounds if args.rounds is not None else agent.outline_config.get("max_rounds", 3)
    
    # 如果指定了从配置文件处理PDF
    if args.from_config:
        print("\n===== 从配置文件获取并处理PDF =====")
        article_content = agent.process_pdf_from_config()
        if not article_content:
            print("错误: 从配置文件处理PDF失败。请检查配置文件中的PDF URL。")
            return
            
        # 获取PDF URL作为参考
        url = agent.pdf_config.get("url", "")
        print(f"处理PDF: {url}")
        
        # 使用默认查询模板或命令行参数
        query_template = agent.outline_config.get("query_template", "我想了解这篇文章: {url}")
        query = args.query or query_template.format(url=url)
    else:
        # 从配置文件或命令行参数获取URL
        url = args.url
        if not url:
            url = agent.pdf_config.get("url", "")
            if not url:
                print("错误: 未提供文章URL。请在配置文件中设置或通过--url参数指定。")
                return
        
        print(f"\n===== 开始为文章 {url} 生成分镜大纲 =====")
        
        # 使用默认查询模板或命令行参数
        query_template = agent.outline_config.get("query_template", "我想了解这篇文章: {url}")
        query = args.query or query_template.format(url=url)
    
    print(f"配置文件: {args.config}")
    print(f"最大迭代轮数: {max_rounds}")
    print(f"用户查询: {query}")
    
    # 从配置中获取用户画像
    user_profile = agent.outline_config.get("user_profile", {
        "education_level": "研究生",
        "field": "计算机科学",
        "interests": ["人工智能", "机器学习", "自然语言处理"],
        "expertise_level": "中级",
        "content_preferences": {
            "style": "通俗易懂",
            "depth": "中等",
            "format": "视频讲解",
        },
    })
    
    # 从配置中获取内容主题
    content_topics = agent.outline_config.get("content_topics", ["论文解读", "学术分享", "技术讲解"])
    
    # 运行大纲代理
    try:
        result = agent.run(
            url=url,
            query=query,
            user_id="user123",
            user_profile=user_profile,
            content_topics=content_topics,
            max_rounds=max_rounds
        )
        
        # 输出结果
        if "error" in result:
            print(f"\n错误: {result['error']}")
            return
        
        print(f"\n大纲生成成功，已保存到 {result['outline_file']}")
        
        # 获取大纲内容
        outline = result.get("final_outline", {})
        
        # 打印大纲摘要
        print(f"\n文章标题: {outline.get('title', '未提供')}")
        
        if "key_concepts" in outline and outline["key_concepts"]:
            print(f"\n核心概念:")
            for concept in outline["key_concepts"]:
                print(f"- {concept}")
        
        if "innovation_points" in outline and outline["innovation_points"]:
            print(f"\n创新点:")
            for point in outline["innovation_points"]:
                print(f"- {point}")
        
        if "structure" in outline and outline["structure"]:
            print(f"\n文章结构:")
            for i, section in enumerate(outline["structure"], 1):
                importance = section.get("importance", "medium")
                importance_mark = "*" if importance == "high" else ""
                print(f"{i}. {section.get('section', '未命名章节')}{importance_mark}")
        
        if "storyboard_suggestions" in outline and outline["storyboard_suggestions"]:
            print(f"\n分镜建议:")
            for i, scene in enumerate(outline["storyboard_suggestions"], 1):
                print(f"{i}. {scene.get('scene_name', '未命名分镜')}")
                print(f"   关键元素: {', '.join(scene.get('key_elements', []))}")
                print(f"   重点: {scene.get('focus_points', '未提供')}")
        
        # 打印意图分析摘要
        intention = result.get("intention_analysis", {})
        print(f"\n用户意图分析:")
        print(f"主要意图: {', '.join(intention.get('primary_intent', []))}")
        print(f"次要意图: {', '.join(intention.get('secondary_intent', []))}")
        
        content_style = intention.get("content_style", {})
        print(f"内容风格: 语调={content_style.get('tone', '中性')}, 深度={content_style.get('depth', '中等')}")
        
        print(f"\n详细结果已保存到: {result['outline_file']}")
    
    except Exception as e:
        import traceback
        print(f"\n运行出错: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    main() 