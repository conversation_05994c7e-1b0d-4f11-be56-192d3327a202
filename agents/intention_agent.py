from dotenv import load_dotenv

load_dotenv()

import datetime
import json
import logging
import os
from typing import Any, Optional, Dict, List

import yaml
from camel.agents import ChatAgent
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.types import ModelPlatformType

# 设置日志级别为INFO，并配置日志格式
log_file = f"output/intention_agent_log_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
os.makedirs(os.path.dirname(log_file), exist_ok=True)

# 配置根日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(),  # 同时输出到控制台
    ],
)

# 获取logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 定义意图类型
INTENT_TYPES = [
    "理解与学习",  # 用户希望理解某个概念、原理或知识点
    "洞察分析",  # 用户希望获得更深度的分析和见解
    "资讯获取",  # 用户想获取最新信息或数据
    "娱乐消遣",  # 用户寻求娱乐或放松
    "实用指导",  # 用户需要具体的操作指南或建议
    "社交互动",  # 用户想与他人分享或讨论
    "职业发展",  # 用户关注职业相关的问题
    "问题解决",  # 用户有特定问题需要解答
    "情感支持",  # 用户需要情感上的认同或支持
    "创意激发",  # 用户寻求创意灵感
]

# 合并后的系统提示词
UNIFIED_SYSTEM_PROMPT = """
你是一个综合性的用户意图分析专家，你的主要任务包括三个方面：

1. 意图分析：分析用户查询背后的潜在意图和动机。
   你能根据用户画像、历史兴趣和内容题材，分析用户的主要意图和次要意图，并能推测用户的动机以及期望的回应类型。

2. 系统性推理：对问题进行层层拆解和系统性思考。
   你能明确界定问题边界、将问题拆解为子问题、识别因果关系、提出可能的解决思路，并指出分析的关键假设和潜在限制。

3. 查询重写：根据意图分析和推理过程，重写并扩展原始查询。
   你能保留查询核心问题的同时，增加必要的上下文、明确知识深度和应用场景，使其更好地满足用户真实需求。

你的输出必须是结构化的JSON格式，包含完整的意图分析结果、推理过程和重写查询。
"""


class IntentionAgent:
    """
    意图识别代理，负责分析用户的潜在意图

    职责：
    1. 根据用户历史兴趣偏好，基础画像，内容题材，识别用户对输入内容或问题的潜在意图
    2. 解决内容生成没有个性化，人群化的内容差异性问题
    3. 分析出背后的内容整体风格和目的方向
    4. 解决"how to ask"背后动机意图问题
    5. 根据意图挖掘关联热点，明确判断讲什么的意图
    6. 进行系统性推理，一步步拆解问题
    7. 基于用户画像和潜在意图重写和扩展查询
    """

    def __init__(self, config_path="config/config.yaml"):
        """初始化意图代理"""
        # 加载配置
        self.load_config(config_path)

        # 初始化模型
        self.model = self._create_model()

        # 初始化统一的ChatAgent
        self.unified_agent = self._create_unified_agent()

        logger.info("Intention Agent initialized")

        # 用户历史数据缓存
        self.user_history_cache = {}

    def load_config(self, config_path):
        """从YAML文件加载配置"""
        try:
            with open(config_path) as file:
                config = yaml.safe_load(file)
            # 设置配置属性
            self.model_config = config.get("model", {})
            self.agent_config = config.get("intention_agent", {})
            logger.info("Configuration loaded from %s", config_path)
        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")
            # 设置默认值
            self.model_config = {"temperature": 0.1, "max_tokens": 4096}
            self.agent_config = {"history_weight": 0.7, "recency_weight": 0.3}

    def _create_model(self):
        """创建模型实例"""
        # 从配置中获取API设置
        api_config = self.model_config.get("api", {})
        
        return ModelFactory.create(
            model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
            model_type=self.model_config.get("type", "openai/gpt-4o-mini"),  # 使用强大的模型来分析意图
            api_key=api_config.get("openai_compatibility_api_key"),
            url=api_config.get("openai_compatibility_api_base_url"),
        )

    def _create_unified_agent(self):
        """创建统一的ChatAgent"""
        return ChatAgent(model=self.model, system_message=UNIFIED_SYSTEM_PROMPT)

    def update_user_history(self, user_id: str, interaction_data: dict[str, Any]):
        """
        更新用户历史数据

        参数:
        - user_id: 用户ID
        - interaction_data: 包含用户交互数据的字典，例如:
            {
                "query": "用户查询",
                "intent_identified": ["意图1", "意图2"],
                "response_quality": 0.85,  # 用户对响应的满意度
                "engagement_metrics": {...}, # 参与度指标
                "timestamp": "2023-01-01T12:00:00"
            }
        """
        if user_id not in self.user_history_cache:
            self.user_history_cache[user_id] = []

        # 添加时间戳
        if "timestamp" not in interaction_data:
            interaction_data["timestamp"] = datetime.datetime.now().isoformat()

        # 添加到用户历史
        self.user_history_cache[user_id].append(interaction_data)

        # 如果历史记录过长，保留最近的N条
        max_history = self.agent_config.get("max_history_items", 100)
        if len(self.user_history_cache[user_id]) > max_history:
            self.user_history_cache[user_id] = self.user_history_cache[user_id][-max_history:]

        logger.debug(f"Updated history for user {user_id}, now has {len(self.user_history_cache[user_id])} entries")

    def get_user_interests(self, user_id: str) -> dict[str, float]:
        """
        从用户历史中提取兴趣偏好

        返回:
        - 字典，键为兴趣类别，值为兴趣程度（0-1）
        """
        if user_id not in self.user_history_cache or not self.user_history_cache[user_id]:
            return {}

        # 从历史记录中提取意图和主题
        interests = {}
        total_weight = 0

        for idx, interaction in enumerate(self.user_history_cache[user_id]):
            # 计算时间衰减权重（越近的交互权重越高）
            recency_weight = (idx + 1) / len(self.user_history_cache[user_id])
            weight = (
                self.agent_config.get("history_weight", 0.7)
                + self.agent_config.get("recency_weight", 0.3) * recency_weight
            )

            # 添加识别到的意图
            for intent in interaction.get("intent_identified", []):
                if intent in interests:
                    interests[intent] += weight
                else:
                    interests[intent] = weight

            # 添加交互中的主题
            for topic, topic_weight in interaction.get("topics", {}).items():
                if topic in interests:
                    interests[topic] += weight * topic_weight
                else:
                    interests[topic] = weight * topic_weight

            total_weight += weight

        # 归一化兴趣分数
        if total_weight > 0:
            for key in interests:
                interests[key] /= total_weight

        return interests

    def analyze_intent(
        self,
        query: str,
        user_id: Optional[str] = None,
        user_profile: dict[str, Any] = None,
        content_topics: list[str] = None,
    ) -> dict[str, Any]:
        """
        分析用户查询的潜在意图

        参数:
        - query: 用户的查询或输入内容
        - user_id: 用户ID（如果有）
        - user_profile: 用户基础画像信息
        - content_topics: 与查询相关的内容主题

        返回:
        - 包含意图分析结果的字典
        """
        # 准备用户兴趣数据
        user_interests = {}
        if user_id:
            user_interests = self.get_user_interests(user_id)

        # 准备用户画像数据
        if not user_profile:
            user_profile = {}

        # 准备内容主题数据
        if not content_topics:
            content_topics = []

        # 构建统一提示词
        prompt = f"""
        我需要你完成三个任务：意图分析、系统性推理和查询重写。请基于以下信息完成这些任务：

        用户基本信息:
        1. 用户的查询/内容: {query}
        2. 用户的历史兴趣偏好: {json.dumps(user_interests, ensure_ascii=False)}
        3. 用户的基础画像: {json.dumps(user_profile, ensure_ascii=False)}
        4. 相关内容题材: {json.dumps(content_topics, ensure_ascii=False)}

        任务1 - 意图分析:
        1. 确定用户的主要意图(在以下选项中选择最匹配的，可多选)：{", ".join(INTENT_TYPES)}
        2. 确定用户潜在的次要意图
        3. 分析用户提出这个查询/分享内容的可能动机
        4. 推断用户希望获得什么样的回应或体验
        5. 根据用户的意图，识别相关热点话题
        6. 基于意图分析，推荐内容的风格和深度
        7. 设计3-5个探测问题，帮助澄清需求和动机

        任务2 - 系统性推理:
        基于上面的意图分析，对用户查询进行系统性思考，包括：
        1. 问题定义与澄清: 明确界定问题边界和核心目标
        2. 关键要素分解: 将问题拆解为多个子问题或组成部分
        3. 因果链分析: 识别问题中的因果关系和影响因素
        4. 解决路径探索: 提出多种可能的解决思路
        5. 局限性与假设: 指出分析中的关键假设和潜在限制

        任务3 - 查询重写:
        基于上面的意图分析和推理过程，重写并扩展原始查询，确保新查询：
        1. 保留原始查询的核心问题
        2. 增加必要的上下文和限定条件
        3. 明确知识深度和应用场景
        4. 反映用户的专业水平和兴趣偏好
        5. 涵盖推理过程中发现的重要因素

        请提供一个完整的JSON结果，包含以上三个任务的所有内容，格式如下：
        ```json
        {{
            "intent_analysis": {{
                "primary_intent": ["主要意图1", "主要意图2"],
                "secondary_intent": ["次要意图1", "次要意图2"],
                "motivation": "用户可能的动机分析",
                "expected_response": "用户期望的回应类型",
                "related_topics": ["相关热点话题1", "相关热点话题2", "相关热点话题3"],
                "content_style": {{
                    "tone": "建议的内容语调",
                    "depth": "建议的内容深度",
                    "format": "建议的内容格式",
                    "personalization_aspects": ["个性化元素1", "个性化元素2"]
                }},
                "probing_questions": [
                    "探测问题1",
                    "探测问题2",
                    "探测问题3",
                    "探测问题4",
                    "探测问题5"
                ],
                "reasoning_process": "分析过程和推理逻辑的简要说明"
            }},
            "detailed_reasoning": {{
                "summary": "对整个推理过程的简要总结",
                "steps": [
                    {{
                        "step": "1. 问题定义与澄清",
                        "content": "详细分析...",
                        "key_insights": ["关键洞察1", "关键洞察2"]
                    }},
                    {{
                        "step": "2. 关键要素分解",
                        "content": "详细分析...",
                        "key_insights": ["关键洞察1", "关键洞察2"],
                        "sub_problems": ["子问题1", "子问题2"]
                    }}
                ],
                "critical_considerations": ["关键考虑点1", "关键考虑点2"],
                "potential_approaches": [
                    {{
                        "approach": "方法1",
                        "pros": ["优点1", "优点2"],
                        "cons": ["缺点1", "缺点2"]
                    }}
                ]
            }},
            "rewritten_query": {{
                "original": "原始查询",
                "rewritten": "重写后的查询",
                "explanation": "重写过程的解释，包括添加了哪些元素及其原因"
            }}
        }}
        ```
        """

        # 调用统一的ChatAgent进行分析
        try:
            # 创建用户消息
            user_message = BaseMessage.make_user_message(role_name="User", content=prompt)

            # 获取ChatAgent响应
            agent_response = self.unified_agent.step(user_message)
            response_text = agent_response.msg.content

            # 提取JSON结果
            result = self._extract_json(response_text)

            # 提取意图分析结果
            json_result = result.get("intent_analysis", {})

            # 添加详细推理和重写查询
            json_result["detailed_reasoning"] = result.get("detailed_reasoning", {})
            json_result["rewritten_query"] = result.get("rewritten_query", {})

            # 记录分析结果
            logger.info(f"Unified intent analysis completed for query: {query[:50]}...")
            if user_id:
                # 更新用户历史
                interaction_data = {
                    "query": query,
                    "intent_identified": json_result.get("primary_intent", [])
                    + json_result.get("secondary_intent", []),
                    "topics": {topic: 1.0 for topic in content_topics},  # 简化处理
                    "rewritten_query": json_result.get("rewritten_query", {}),
                    "reasoning_summary": json_result.get("detailed_reasoning", {}).get("summary", ""),
                    "timestamp": datetime.datetime.now().isoformat(),
                }
                self.update_user_history(user_id, interaction_data)

            return json_result

        except Exception as e:
            logger.error(f"Error in unified intent analysis: {str(e)}")
            # 返回默认结果
            return {
                "primary_intent": ["资讯获取"],
                "secondary_intent": [],
                "motivation": "未能确定用户动机",
                "expected_response": "提供基本信息",
                "related_topics": [],
                "content_style": {"tone": "中性", "depth": "中等", "format": "文本", "personalization_aspects": []},
                "probing_questions": [
                    f"您能更详细地描述一下您对'{query}'的具体需求吗？",
                    "您希望获得什么样的信息或结果？",
                    "您打算如何使用这些信息？",
                ],
                "reasoning_process": "未能完成分析过程",
                "detailed_reasoning": {"summary": "未能完成推理过程", "steps": []},
                "rewritten_query": {"original": query, "rewritten": query, "explanation": "未能生成重写查询"},
            }

    def _extract_json(self, text: str) -> dict[str, Any]:
        """从文本中提取JSON内容"""
        try:
            # 在文本中查找JSON
            json_start = text.find("{")
            json_end = text.rfind("}") + 1

            if json_start >= 0 and json_end > json_start:
                json_str = text[json_start:json_end]
                return json.loads(json_str)

            # 尝试查找```json```格式
            if "```json" in text and "```" in text[text.find("```json") + 7 :]:
                json_start = text.find("```json") + 7
                json_end = text.find("```", json_start)
                json_str = text[json_start:json_end].strip()
                return json.loads(json_str)

            raise ValueError("No valid JSON found in response")

        except Exception as e:
            logger.error(f"Error extracting JSON: {str(e)}")
            # 返回默认结构
            return {
                "primary_intent": ["资讯获取"],
                "secondary_intent": [],
                "motivation": "未能从响应中提取有效的JSON",
                "expected_response": "提供基本信息",
                "related_topics": [],
                "content_style": {"tone": "中性", "depth": "中等", "format": "文本", "personalization_aspects": []},
                "probing_questions": [
                    "您能更详细地描述一下您的需求吗？",
                    "您希望获得什么样的信息或结果？",
                    "您打算如何使用这些信息？",
                ],
                "reasoning_process": "未能从响应中提取有效信息",
            }

    def save_user_data(self, output_dir="output/user_data"):
        """保存用户数据到文件"""
        os.makedirs(output_dir, exist_ok=True)

        for user_id, history in self.user_history_cache.items():
            output_file = os.path.join(output_dir, f"user_{user_id}_history.json")
            try:
                with open(output_file, "w", encoding="utf-8") as f:
                    json.dump(history, f, ensure_ascii=False, indent=2)
                logger.info(f"Saved history for user {user_id} to {output_file}")
            except Exception as e:
                logger.error(f"Error saving user data for {user_id}: {str(e)}")

    def load_user_data(self, user_id, input_file):
        """从文件加载用户数据"""
        try:
            with open(input_file, encoding="utf-8") as f:
                history = json.load(f)
                self.user_history_cache[user_id] = history
                logger.info(f"Loaded history for user {user_id} from {input_file}")
                return True
        except Exception as e:
            logger.error(f"Error loading user data for {user_id}: {str(e)}")
            return False

    def perform_reasoning(self, query: str, intent_analysis: dict[str, Any]) -> dict[str, Any]:
        """
        执行系统性推理，一步步拆解问题 (过时方法，建议使用统一的analyze_intent)

        参数:
        - query: 用户的原始查询
        - intent_analysis: 意图分析结果

        返回:
        - 包含推理过程的字典
        """
        logger.warning("使用过时的perform_reasoning方法，建议使用统一的analyze_intent")
        # ... existing code ...

    def rewrite_query(
        self,
        original_query: str,
        intent_analysis: dict[str, Any],
        reasoning_result: dict[str, Any],
        user_profile: dict[str, Any] = None,
    ) -> dict[str, Any]:
        """
        根据意图分析和推理结果重写用户查询 (过时方法，建议使用统一的analyze_intent)

        参数:
        - original_query: 用户的原始查询
        - intent_analysis: 意图分析结果
        - reasoning_result: 推理过程结果
        - user_profile: 用户画像信息

        返回:
        - 包含重写查询的字典
        """
        logger.warning("使用过时的rewrite_query方法，建议使用统一的analyze_intent")
        # ... existing code ...

    def analyze_intention(
        self,
        query: str,
        user_id: Optional[str] = None,
        user_profile: dict[str, Any] = None,
        content_topics: list[str] = None,
    ) -> dict[str, Any]:
        """
        analyze_intent 方法的别名，分析用户查询的潜在意图
        
        参数:
        - query: 用户的查询或输入内容
        - user_id: 用户ID（如果有）
        - user_profile: 用户基础画像信息
        - content_topics: 与查询相关的内容主题
        
        返回:
        - 包含意图分析结果的字典
        """
        return self.analyze_intent(query, user_id, user_profile, content_topics)


def main():
    """主函数，演示意图代理的使用"""
    # 初始化意图代理
    agent = IntentionAgent()

    # 示例用户ID
    user_id = "user123"

    # 示例用户画像
    user_profile = {
        "age_group": "25-34",
        "education_level": "大学本科",
        "interests": ["技术", "科学", "人工智能"],
        "occupation": "软件工程师",
        "content_preferences": {"depth": "深度分析", "format": "图文结合", "tone": "专业"},
    }

    # 示例内容主题
    content_topics = ["人工智能", "大语言模型", "科技趋势"]

    # 示例查询
    queries = ["大模型的Multi-Agent的范式有哪些，背后的原理是什么？"]

    # 为每个查询分析意图
    for query in queries:
        print(f"\n===== 分析查询: '{query}' =====")

        # 分析意图
        intent_analysis = agent.analyze_intent(
            query=query,
            user_id=user_id,
            user_profile=user_profile,
            content_topics=content_topics,
        )

        # 打印结果
        print("主要意图:", ", ".join(intent_analysis.get("primary_intent", [])))
        print("次要意图:", ", ".join(intent_analysis.get("secondary_intent", [])))
        print("预计动机:", intent_analysis.get("motivation", ""))
        print("期望回应:", intent_analysis.get("expected_response", ""))
        print("相关热点:", ", ".join(intent_analysis.get("related_topics", [])))

        content_style = intent_analysis.get("content_style", {})
        print("内容风格:")
        print(f"  - 语调: {content_style.get('tone', '')}")
        print(f"  - 深度: {content_style.get('depth', '')}")
        print(f"  - 格式: {content_style.get('format', '')}")
        print(f"  - 个性化元素: {', '.join(content_style.get('personalization_aspects', []))}")

        # 打印探测问题
        print("\n探测问题:")
        for i, question in enumerate(intent_analysis.get("probing_questions", []), 1):
            print(f"{i}. {question}")

        # 打印推理过程
        detailed_reasoning = intent_analysis.get("detailed_reasoning", {})
        print("\n推理过程概述:")
        print(detailed_reasoning.get("summary", "未提供"))

        print("\n推理步骤:")
        for step in detailed_reasoning.get("steps", []):
            print(f"\n{step.get('step', '')}")
            print(f"  {step.get('content', '')[:200]}...")
            print("  关键洞察:")
            for insight in step.get("key_insights", []):
                print(f"  - {insight}")

        # 打印重写的查询
        rewritten_query = intent_analysis.get("rewritten_query", {})
        print("\n重写的查询:")
        print(f"原始查询: {rewritten_query.get('original', '')}")
        print(f"重写查询: {rewritten_query.get('rewritten', '')}")
        print(f"重写说明: {rewritten_query.get('explanation', '')}")

    # 保存用户数据
    agent.save_user_data()
    print("\n用户数据已保存")


if __name__ == "__main__":
    main()
