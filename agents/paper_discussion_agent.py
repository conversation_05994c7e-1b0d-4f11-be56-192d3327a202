from dotenv import load_dotenv

load_dotenv()

import datetime
import logging
import os

import yaml
from camel.models import ModelFactory
from camel.societies import RolePlaying
from camel.types import ModelPlatformType, TaskType

from tools.pdf_toolkit import PDFToolkit
from utils.format import save_json_content

# 设置日志级别为INFO，并配置日志格式
log_file = f"output/paper_discussion_log_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
os.makedirs(os.path.dirname(log_file), exist_ok=True)

# 配置根日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(),  # 同时输出到控制台
    ],
)

# 获取logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 定义结构化输出格式
DISCUSSION_FORMAT = """
```json
{
    "问题链条": [
        {
            "初始问题": "问题内容",
            "追问链": [
                {
                    "问题": "追问1",
                    "回答": {
                        "综合回答": "完整回答内容",
                        "作者视角": "从作者角度的解释",
                        "论文依据": "论文中的相关数据和结论"
                    }
                },
                {
                    "问题": "追问2",
                    "回答": {
                        "综合回答": "完整回答内容",
                        "作者视角": "从作者角度的解释",
                        "论文依据": "论文中的相关数据和结论"
                    }
                }
            ],
            "问题评价": {
                "深刻度": 1-10,
                "启发性": 1-10,
                "犀利度": 1-10,
                "思维拓展性": 1-10,
                "总分": 计算总分,
                "点评": "对问题质量的简短点评"
            },
            "关键洞见": ["该问题线索产生的关键洞见1", "关键洞见2", ...],
            "研究启示": ["该问题链条产生的对未来研究的启示1", "启示2", ...]
        },
        ...更多问题链条...
    ],
    "总结": {
        "高分问题": [
            {
                "问题": "得分最高的问题",
                "回答摘要": "回答的简要总结",
                "得分": 9.5,
                "关键洞见": ["关键洞见1", "关键洞见2"]
            },
            ...更多高分问题...
        ],
        "关键见解": ["讨论中产生的最重要关键见解1", "关键见解2", ...],
        "研究启示": ["对未来研究的启示1", "研究启示2", ...]
    }
}
```
"""


class PaperDiscussionAgent:
    def __init__(self, config_path="../config/config.yaml"):
        # 加载配置
        self.load_config(config_path)

        # 初始化模型
        self.model = self._create_model()
        logger.info("模型初始化完成")

    def load_config(self, config_path):
        """从YAML文件加载配置"""
        # 确保config_path是绝对路径或相对于当前工作目录
        if not os.path.isabs(config_path):
            # 如果不是绝对路径，转换为相对于当前文件的路径
            script_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(script_dir, config_path)

        with open(config_path) as file:
            config = yaml.safe_load(file)
        # 设置配置属性
        self.model_config = config.get("model", {})
        self.file_config = config.get("files", {})
        logger.info("从 %s 加载配置", config_path)
        # 从配置文件中获取PDF URL、输出目录和讨论参数
        self.pdf_config = config.get("pdf", {})
        self.pdf_url = self.pdf_config.get("url")
        self.pdf_output_dir = self.pdf_config.get("output_dir", "pdf_output")
        self.max_chains = self.pdf_config.get("max_chains", 3)
        self.max_depth = self.pdf_config.get("max_depth", 4)

        logger.info(
            "PDF配置: URL=%s, 输出目录=%s, 问题链条数=%d, 深度=%d",
            self.pdf_url,
            self.pdf_output_dir,
            self.max_chains,
            self.max_depth,
        )

    def _create_model(self):
        """根据配置创建模型实例"""
        # 从配置中获取API设置
        api_config = self.model_config.get("api", {})
        
        return ModelFactory.create(
            model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
            model_type=self.model_config.get("type", "openai/gpt-4o-mini"),
            api_key=api_config.get("openai_compatibility_api_key"),
            url=api_config.get("openai_compatibility_api_base_url"),
        )

    def process_paper(self, paper_content, max_chains=3, max_depth=4):
        """通过角色间的对话处理论文内容，实现连续追问和问题评分"""
        logger.info("开始处理长度为 %d 字符的论文", len(paper_content))

        task_content = f"""
        请分析以下学术论文，通过提问者和回答者的对话，深入挖掘论文中的深刻见解和潜在的问题。

        论文内容：
        {paper_content}

        对话流程如下：
        1. 提问者提出一个关于论文的深刻问题（初始问题）
           a. 要求第一个问题从给定主题或者论文里，围绕核心的创新思路或者模块，提出问题讨论，
              包含这个工作为什么被提出来，为什么很重要，以及这个工作创新性在哪，本质原理、认知结论是什么，和其他工作的差异性优缺点。
        2. 回答者从两个角度回答问题：
           a. 从作者的角度（基于论文内容推测）解释这个问题
           b. 引用论文中的实验数据、结论或论点来支持解释
        3. 提问者针对回答提出"为什么"类型的追问，继续挖掘
        4. 回答者回答追问，继续结合作者视角和论文实际内容
        5. 重复步骤3-4，直到该问题线索无法进一步深入（通常3-5轮追问）
        6. 回答者评价初始问题的深刻度、启发性、犀利度和思维拓展性，给出总结
        7. 提问者提出下一个新的初始问题，重复上述过程
        8. 总共进行{max_chains}个问题链条的探讨

        提问者角色说明：
        提问者是一位具有穿透力的学术论文分析者，负责针对论文提出深刻而富有洞见的问题，并进行连续追问直到问题无法进一步深入。
        提问者的核心职责是：
        1. 精读论文内容，特别关注论文的假设、方法、结论和隐含观点
        2. 提出挑战性的初始问题，这些问题应当能够：
           - 揭示论文中可能存在的逻辑漏洞或欠考虑的因素
           - 能合理延展出论文中意外的发现，或者论文中没有提到的结论
           - 引导思考论文成果的长远影响及潜在的负面后果
           - 将论文的发现与更广泛的领域或其他学科进行跨学科连接
        3. 针对回答者的回应，连续提出"为什么"类型的追问，不断挖掘更深层次的原因和影响
        4. 持续追问直到该问题线索无法进一步深入为止（通常3-5轮追问）
        5. 然后回到初始问题，提出新的高质量初始问题，重复上述过程

        回答者角色说明：
        回答者是一位深思熟虑的学术回应者，负责回答关于论文的深刻问题及其连续追问，并在完成一系列追问后评价初始问题的质量。
        回答者的核心职责是：
        1. 针对提出的问题，提供深入、全面的回答，同时：
           - 从作者的角度（基于论文内容的合理推测）解释问题
           - 直接引用论文中的实验数据、方法和结论来支持解释
           - 明确区分哪些是论文已有内容，哪些是推测性的扩展
        2. 对每个连续追问提供更深层次的回答，揭示更本质的原因和影响
        3. 当一个问题线索通过连续追问已经充分挖掘后，明确指出"这个问题线索已经充分探讨"
        4. 在完成对一个初始问题的连续追问后，评价初始问题的质量，使用以下标准：
           - 深刻度：问题是否触及研究的核心或揭示隐藏的假设/影响（1-10分）
           - 启发性：问题是否能引发新的研究方向或视角（1-10分）
           - 犀利度：问题是否精准指出研究中的关键挑战或潜在弱点（1-10分）
           - 思维拓展性：问题是否促进跨学科思考或理论创新（1-10分）
        5. 提供一个总结性评论，概括整个问题线索探讨的价值和关键发现，同时提炼出对未来研究的启示

        最终输出应当是JSON格式的讨论记录，包含每个问题链条的初始问题、追问链、问题评价和关键洞见，以及对整个讨论的总结。

        格式要求：
        {DISCUSSION_FORMAT}
        """

        # 设置角色扮演，使用提问者和回答者两个角色
        logger.info("设置提问者和回答者的角色扮演")

        role_playing = RolePlaying(
            # 设置提问者为用户角色
            user_role_name="Questioner",
            user_agent_kwargs={"model": self.model},
            # 设置回答者为助手角色
            assistant_role_name="Responder",
            assistant_agent_kwargs={"model": self.model},
            # 任务参数
            task_prompt=task_content,
            task_type=TaskType.AI_SOCIETY,
            with_task_specify=False,  # 禁用task_specify避免报错
            # 附加配置
            output_language="chinese",
        )

        # 开始对话
        logger.info("开始角色对话")
        messages = []

        # 初始化对话
        chat_history = role_playing.init_chat()
        messages.append(chat_history)

        # 存储当前正在进行的问题链条
        current_chain = {
            "初始问题": None,
            "追问链": [],
            "问题评价": None,
            "关键洞见": [],
            "研究启示": [],
        }

        # 存储所有完成的问题链条
        all_chains = []

        # 状态跟踪
        current_state = "initial_question"  # 可能的状态: initial_question, follow_up, evaluation
        chain_count = 0
        depth_count = 0

        # 进行多轮对话
        max_steps = max_chains * (max_depth + 2)  # 每个链条包含一个初始问题、多个追问和一个评价
        for step_num in range(max_steps):
            logger.info(f"对话步骤 {step_num + 1}, 状态: {current_state}, 链条: {chain_count}, 深度: {depth_count}")
            try:
                # 进行对话步骤
                assistant_response, user_response = role_playing.step(chat_history)

                # 从响应中获取消息
                assistant_message = assistant_response.msg
                user_message = user_response.msg

                # 将消息添加到历史记录
                chat_history = assistant_message
                messages.append(assistant_message)
                messages.append(user_message)

                # 根据对话状态处理消息
                if current_state == "initial_question" and assistant_message.role_name == "Responder":
                    # 提问者提出了初始问题，回答者回答
                    current_chain["初始问题"] = messages[-3].content  # 初始问题是上上条消息
                    current_chain["追问链"] = []
                    current_state = "follow_up"
                    depth_count = 0

                elif current_state == "follow_up" and assistant_message.role_name == "Responder":
                    # 提问者提出了追问，回答者回答
                    follow_up_question = messages[-3].content  # 追问是上上条消息
                    follow_up_answer = assistant_message.content

                    # 检查回答中是否表明问题已经充分探讨
                    if "充分探讨" in follow_up_answer or "已经探讨" in follow_up_answer or depth_count >= max_depth - 1:
                        # 追问链结束，进入评价阶段
                        current_state = "evaluation"
                        # 引导回答者进行评价
                        special_message = f"这个问题链条已经充分探讨。请评价初始问题「{current_chain['初始问题']}」的质量，包括深刻度、启发性、犀利度和思维拓展性，并给出总结和对未来研究的启示。"
                        user_response.msg.content = special_message
                    else:
                        # 从回答中提取作者视角和论文依据（如果有的话）
                        import re

                        author_perspective = ""
                        paper_evidence = ""

                        # 尝试提取作者视角
                        author_match = re.search(r"作者视角[：:]([\s\S]+?)(?=论文依据|$)", follow_up_answer)
                        if author_match:
                            author_perspective = author_match.group(1).strip()

                        # 尝试提取论文依据
                        evidence_match = re.search(r"论文依据[：:]([\s\S]+?)(?=\n\n|$)", follow_up_answer)
                        if evidence_match:
                            paper_evidence = evidence_match.group(1).strip()

                        # 如果无法提取，则使用完整回答
                        answer_obj = {
                            "综合回答": follow_up_answer,
                            "作者视角": author_perspective if author_perspective else "未明确区分",
                            "论文依据": paper_evidence if paper_evidence else "未明确区分",
                        }

                        # 添加追问和回答到当前链条
                        current_chain["追问链"].append(
                            {
                                "问题": follow_up_question,
                                "回答": answer_obj,
                            },
                        )
                        depth_count += 1

                elif current_state == "evaluation" and assistant_message.role_name == "Responder":
                    # 回答者提供了评价
                    evaluation_content = assistant_message.content

                    # 解析评价内容
                    try:
                        # 提取评分
                        import re

                        deep_score = re.search(r"深刻度[：:]\s*(\d+)[，,。.\s]", evaluation_content)
                        inspirational_score = re.search(r"启发性[：:]\s*(\d+)[，,。.\s]", evaluation_content)
                        sharpness_score = re.search(r"犀利度[：:]\s*(\d+)[，,。.\s]", evaluation_content)
                        thinking_score = re.search(r"思维拓展性[：:]\s*(\d+)[，,。.\s]", evaluation_content)

                        evaluation = {
                            "深刻度": int(deep_score.group(1)) if deep_score else 5,
                            "启发性": int(inspirational_score.group(1)) if inspirational_score else 5,
                            "犀利度": int(sharpness_score.group(1)) if sharpness_score else 5,
                            "思维拓展性": int(thinking_score.group(1)) if thinking_score else 5,
                        }

                        # 计算总分
                        total_score = sum(evaluation.values()) / 4
                        evaluation["总分"] = round(total_score, 1)

                        # 提取点评
                        comment_match = re.search(
                            r"总结[：:]([\s\S]+?)(?=\n\n|关键洞见|研究启示|$)",
                            evaluation_content,
                        )
                        evaluation["点评"] = comment_match.group(1).strip() if comment_match else "无点评"

                        # 提取关键洞见
                        insights = []
                        insights_section = re.search(r"关键洞见[：:]([\s\S]+?)(?=\n\n|研究启示|$)", evaluation_content)
                        if insights_section:
                            insights_text = insights_section.group(1)
                            insights_items = re.findall(r"\d+\.\s*([^\n]+)", insights_text)
                            if insights_items:
                                insights = insights_items
                            else:
                                # 如果没有编号，尝试按段落分割
                                insights = [item.strip() for item in insights_text.split("\n") if item.strip()]

                        # 提取研究启示
                        implications = []
                        implications_section = re.search(r"研究启示[：:]([\s\S]+?)(?=\n\n|$)", evaluation_content)
                        if implications_section:
                            implications_text = implications_section.group(1)
                            implications_items = re.findall(r"\d+\.\s*([^\n]+)", implications_text)
                            if implications_items:
                                implications = implications_items
                            else:
                                # 如果没有编号，尝试按段落分割
                                implications = [item.strip() for item in implications_text.split("\n") if item.strip()]

                        current_chain["问题评价"] = evaluation
                        current_chain["关键洞见"] = insights
                        current_chain["研究启示"] = implications

                        # 完成当前链条，开始新的链条
                        all_chains.append(current_chain.copy())
                        chain_count += 1

                        # 重置当前链条和状态
                        current_chain = {
                            "初始问题": None,
                            "追问链": [],
                            "问题评价": None,
                            "关键洞见": [],
                            "研究启示": [],
                        }
                        current_state = "initial_question"
                        depth_count = 0

                        # 如果已完成所需链条数，结束对话
                        if chain_count >= max_chains:
                            logger.info("已完成所需问题链条数量")
                            break

                    except Exception as e:
                        logger.error(f"解析评价内容时出错: {str(e)}")
                        # 出错时也尝试进入下一个链条
                        current_state = "initial_question"
                        chain_count += 1
                        depth_count = 0

            except Exception as e:
                logger.error(f"对话步骤出错: {str(e)}")
                break

        # 准备最终结果
        # 找出得分最高的问题
        sorted_chains = sorted(all_chains, key=lambda x: x["问题评价"]["总分"] if x["问题评价"] else 0, reverse=True)
        top_chains = sorted_chains[: min(3, len(sorted_chains))]

        high_score_questions = []
        for chain in top_chains:
            if chain["问题评价"]:
                if chain["追问链"]:
                    try:
                        answer_summary = chain["追问链"][0]["回答"]["综合回答"]
                        # 如果综合回答太长，截取前200个字符
                        if len(answer_summary) > 200:
                            answer_summary = answer_summary[:200] + "..."
                    except (KeyError, TypeError):
                        # 兼容旧格式
                        answer_summary = str(chain["追问链"][0]["回答"])[:200]
                else:
                    answer_summary = "无回答"

                high_score_questions.append(
                    {
                        "问题": chain["初始问题"],
                        "回答摘要": answer_summary,
                        "得分": chain["问题评价"]["总分"],
                        "关键洞见": chain["关键洞见"],
                    },
                )

        # 收集所有关键洞见和研究启示
        all_insights = []
        all_implications = []
        for chain in all_chains:
            all_insights.extend(chain.get("关键洞见", []))
            all_implications.extend(chain.get("研究启示", []))

        # 最终的讨论结果
        discussion = {
            "问题链条": all_chains,
            "总结": {
                "高分问题": high_score_questions,
                "关键见解": list({insight for insight in all_insights if insight}),
                "研究启示": list({impl for impl in all_implications if impl}),
            },
        }

        # 保存讨论结果
        output_file = self.file_config.get("paper_discussion_file", "output/paper_discussion.json")
        # 确保输出路径是绝对路径或相对于当前工作目录
        if not os.path.isabs(output_file):
            script_dir = os.path.dirname(os.path.abspath(__file__))
            output_dir = os.path.join(script_dir, "..", "output")
            os.makedirs(output_dir, exist_ok=True)
            output_file = os.path.join(output_dir, os.path.basename(output_file))

        save_json_content(discussion, output_file)
        logger.info("讨论结果已保存到 %s", output_file)

        # 同时保存完整讨论记录作为参考
        discussion_dir = os.path.dirname(output_file)
        discussion_file = os.path.join(discussion_dir, "paper_discussion_full.txt")
        full_discussion = "\n\n--- 完整讨论记录 ---\n\n"
        for msg in messages:
            if hasattr(msg, "role_name") and hasattr(msg, "content"):
                full_discussion += f"{msg.role_name}: {msg.content}\n\n"
            elif hasattr(msg, "msg") and hasattr(msg.msg, "role_name"):
                full_discussion += f"{msg.msg.role_name}: {msg.msg.content}\n\n"
            else:
                full_discussion += f"未知角色: {str(msg)}\n\n"

        with open(discussion_file, "w", encoding="utf-8") as f:
            f.write(full_discussion)
        logger.info("完整讨论记录已保存到 %s", discussion_file)

        return {"discussion": discussion, "full_text": full_discussion}


def main():
    """主函数，运行整个工作流程"""
    # 初始化工作流
    workflow = PaperDiscussion()
    # 获取论文内容
    paper_content = None

    logger.info(f"开始从URL下载和解析PDF: {workflow.pdf_url}")
    pdf_toolkit = PDFToolkit()
    extract_result = pdf_toolkit.extract_pdf(workflow.pdf_url, workflow.pdf_output_dir)
    markdown_file = extract_result.get("markdown_file")
    with open(markdown_file, encoding="utf-8") as f:
        paper_content = f.read()
    logger.info(f"已加载PDF生成的Markdown，长度: {len(paper_content)} 字符")
    # 保存图片信息
    image_info = extract_result.get("images", [])
    if image_info:
        logger.info(f"PDF中提取了 {len(image_info)} 张图片")

    # 处理论文
    logger.info("开始论文深度讨论工作流")
    results = workflow.process_paper(paper_content, max_chains=workflow.max_chains, max_depth=workflow.max_depth)

    # 打印总结
    logger.info("论文深度讨论完成")
    print(f"问题链条数: {len(results['discussion']['问题链条'])}")
    print(f"高分问题数: {len(results['discussion']['总结']['高分问题'])}")
    print(f"完整讨论记录长度: {len(results['full_text'])} 字符")

    # 获取输出文件的实际路径
    output_json_path = workflow.file_config.get("paper_discussion_file", "output/paper_discussion.json")
    if not os.path.isabs(output_json_path):
        script_dir = os.path.dirname(os.path.abspath(__file__))
        output_dir = os.path.join(script_dir, "..", "output")
        output_json_path = os.path.join(output_dir, os.path.basename(output_json_path))

    discussion_dir = os.path.dirname(output_json_path)
    full_text_path = os.path.join(discussion_dir, "paper_discussion_full.txt")

    print(f"讨论结果已保存到: {output_json_path}")
    print(f"完整讨论记录已保存到: {full_text_path}")


if __name__ == "__main__":
    main()
