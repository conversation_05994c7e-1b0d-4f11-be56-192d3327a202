from dotenv import load_dotenv

load_dotenv()

import datetime
import json
import logging
import os
import re
import sys
from typing import Any, Dict, List, Optional

# 添加父目录到导入路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import yaml
from camel.agents import ChatAgent
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.societies import RolePlaying
from camel.types import ModelPlatformType, TaskType

# 设置日志级别为INFO，并配置日志格式
log_file = f"output/material_agent_log_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
os.makedirs(os.path.dirname(log_file), exist_ok=True)

# 配置根日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(),  # 同时输出到控制台
    ],
)

# 获取logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 定义素材规范化输出格式
MATERIAL_FORMAT = """
# 视频标题：[引人注目的标题]

## 视频信息
- **目标受众**：[目标受众详细描述]
- **视频时长**：[预计视频长度]
- **核心意图**：[视频主要目的简述]
- **表达风格**：[视频表达风格描述]

## 内容精华

### [第一部分标题]
[第一部分核心内容，包含该部分最重要的1-2个要点，语言精炼。在内容中直接引用原素材中已有的相关多模态元素（图片、视频、GIF、代码、表格、公式等），如：

![图片描述](原素材中实际存在的图片路径) - 简单说明图片与内容的关系

在内容中强调重要数据和结论，使用**加粗**、_斜体_或`代码块`等Markdown样式突出关键数字和核心发现。使用列表、表格等形式直观呈现多个要点或对比信息。]

### [第二部分标题]
[第二部分核心内容，在叙述中自然融入重要数据和实验结果，同时直接嵌入原素材中的关键多模态元素，如代码示例：

```python
# 原素材中实际存在的代码示例
def example_function():
    return "重要示例"
```

用适当的Markdown格式（如有序列表、表格对比等）呈现内容，使之更加直观。核心信息应突出显示，确保吸引观众注意。]

### [第三部分标题，如需要]
[第三部分核心内容，关注目标受众最需要了解的内容。继续使用合适的Markdown格式表达内容，如时间线形式展示发展历程，或通过对比格式展示不同方案的优缺点，或者系统流程展现核心模块关系。在需要时直接引用原素材中存在的多模态元素，如：

<原素材中实际存在的视频引用地址> - 简要说明视频内容与主题的关系

或数学公式：$E = mc^2$ - 简要说明公式意义]

## 关键结论
[基于受众需求和视频意图的总结性结论，提供价值洞见]
"""

# 修改素材编辑者角色提示
MATERIAL_EDITOR_PROMPT = """
作为视频素材编辑专家，你的职责是根据目标受众和视频长度要求，优化原始素材内容。
重点是保持所有事实的准确性，只调整表述方式和内容结构。

你需要：
1. 优先确保markdown文档各部分结构完整，包括标题、视频信息、内容各部分和结论
2. 根据视频长度参考内容密度范围，但不必严格限制字数
3. 根据风格要求调整表述方式，但不改变基础事实
4. 确保内容深度与受众知识水平匹配
5. 设计吸引人但不夸大的标题和结构
6. 在内容描述中自然融入重要数据和关键结论
7. 在正文中直接嵌入合适的多模态元素，给出地址和简单描述

对于不同长度的视频，内容密度仅作参考：
- 1-2分钟视频：3-4个核心要点，约450-600字（参考范围）
- 3-5分钟视频：5-7个重要要点，约900-1500字（参考范围）
- 5-10分钟视频：更全面的内容，约1500-3000字（参考范围）

最重要的是确保markdown文档结构完整，内容有价值，多模态元素直接嵌入在相关内容中。
"""

# 修改质量检查者角色提示
QUALITY_REVIEWER_PROMPT = """
作为质量检查专家，你需要首先验证markdown文档的完整性，然后审查编辑后的素材内容质量：

## 完整性检查（最高优先级）
1. 文档结构完整性：必须包含以下所有部分
   - 视频标题
   - 视频信息（目标受众、视频时长、核心意图、表达风格）
   - 内容主体（至少包含1-3个主要部分）
   - 关键结论

2. 内容完整性：所有部分是否有实质性内容，不存在仅有标题无内容的情况
   - 每个部分都必须有足够的实质内容
   - 多模态元素是否在正文中直接引用并附有简要说明

## 内容质量检查
1. 事实准确性：内容必须与原始素材中的事实保持一致，不允许有事实性错误
2. 内容密度：根据视频长度，内容量是否恰当，既不过于简略也不过于冗长
3. 受众适配：内容深度和专业程度是否符合目标受众的知识水平
4. 风格一致：表述方式是否符合要求，同时不扭曲基础事实
5. 结构清晰：布局是否合理，标题是否能有效吸引目标受众
6. 多模态元素：引用元素是否与所在段落内容语义匹配
7. 吸引力：内容是否既能吸引目标受众，又不夸大或误导

若发现任何结构不完整问题（如缺少某个完整部分），请首先指出这是一个严重的完整性问题，并要求优先修复。
内容质量问题则可以作为次要问题提出。

请提供具体、建设性的反馈，帮助改进素材质量，首先确保文档完整性，然后是事实准确性和内容表达的优化。
"""

class MaterialAgent:
    """
    视频素材规范化代理
    
    职责：
    1. 分析输入的素材markdown文件
    2. 解析用户目的描述，包括目标人群、主题内容、分析意图和风格偏好
    3. 根据用户意图和受众特点，重构规范化的视频素材markdown
    4. 通过角色扮演检查和优化生成内容的质量
    5. 输出最终的规范化视频素材markdown文件
    """
    
    class Config:
        """素材代理配置子模块"""
        
        def __init__(self, config_dict=None):
            """初始化配置"""
            if not config_dict:
                config_dict = {}
                
            # 模型配置
            self.model = {
                "type": config_dict.get("model", {}).get("type", "openai/gpt-4o-mini"),
                "temperature": config_dict.get("model", {}).get("temperature", 0.7),
                "api": config_dict.get("model", {}).get("api", {})
            }
            
            # 文件配置
            self.files = {
                "output_dir": config_dict.get("files", {}).get("output_dir", "output"),
                "material_file": config_dict.get("files", {}).get("material_file", "output/video_material.md")
            }
            
            # 素材代理特定配置
            material_config = config_dict.get("material", {})
            self.max_rounds = material_config.get("max_rounds", 3)
            self.output_dir = material_config.get("output_dir", "output")
            self.material_file = material_config.get("material_file", "output/video_material.md")
            self.analysis_techniques = material_config.get("analysis_techniques", [
                "audience_focus", 
                "content_refinement", 
                "style_adaptation", 
                "multimedia_handling"
            ])
            self.default_video_length = material_config.get("default_video_length", "2分钟")
            self.audience_presets = material_config.get("audience_presets", [
                "研究人员", "学生", "从业者", "技术爱好者", "管理人员"
            ])
            self.style_presets = material_config.get("style_presets", [
                "学术严谨", "通俗易懂", "批判性思考", "实用指导", "趣味性解读"
            ])
    
    def __init__(self, config_path="config/config.yaml"):
        """初始化素材规范化代理"""
        # 加载配置
        config_dict = self._load_yaml_config(config_path)
        
        # 初始化配置子模块
        self.config = self.Config(config_dict)
        
        # 初始化模型
        self.model = self._create_model()
        
        logger.info("素材规范化代理初始化完成")
    
    def _load_yaml_config(self, config_path):
        """从YAML文件加载配置"""
        try:
            with open(config_path) as file:
                config = yaml.safe_load(file)
            logger.info("从 %s 加载配置", config_path)
            return config
        except Exception as e:
            logger.error(f"加载配置出错: {str(e)}")
            return {}
    
    def _create_model(self):
        """创建模型实例"""
        # 从配置中获取API设置
        api_config = self.config.model["api"]
        
        return ModelFactory.create(
            model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
            model_type=self.config.model["type"],
            api_key=api_config.get("openai_compatibility_api_key"),
            url=api_config.get("openai_compatibility_api_base_url"),
        )
    
    def read_material(self, material_file_path: str) -> str:
        """
        读取原始素材文件
        
        参数:
        - material_file_path: 素材文件路径
        
        返回:
        - str: 素材内容
        """
        try:
            with open(material_file_path, "r", encoding="utf-8") as f:
                content = f.read()
            logger.info(f"成功读取素材文件: {material_file_path}, 长度: {len(content)} 字符")
            return content
        except Exception as e:
            logger.error(f"读取素材文件失败: {str(e)}")
            raise
    
    def extract_multimedia_elements(self, content: str) -> Dict[str, List[str]]:
        """
        从Markdown内容中提取多模态元素
        
        参数:
        - content: Markdown内容
        
        返回:
        - Dict: 包含多种多模态元素的字典
        """
        multimedia = {
            "images": [],
            "videos": [],
            "gifs": [],
            "audios": [],
            "code_blocks": [],
            "tables": [],
            "formulas": [],
            "lists": []
        }
        
        try:
            # 提取图片链接
            image_pattern = r"!\[(.*?)\]\((.*?)\)"
            images = re.findall(image_pattern, content)
            multimedia["images"] = [{"alt": img[0], "url": img[1]} for img in images]
            
            # 尝试区分GIF
            for img in multimedia["images"][:]:
                if img["url"].lower().endswith('.gif'):
                    multimedia["gifs"].append(img)
                    multimedia["images"].remove(img)
            
            # 提取视频链接（匹配常见的视频嵌入格式）
            video_patterns = [
                r"<video.*?src=[\"\'](.*?)[\"\'].*?>.*?</video>",
                r"\[video\]\((.*?)\)",
                r"!\[video\]\((.*?)\)"
            ]
            for pattern in video_patterns:
                videos = re.findall(pattern, content, re.DOTALL)
                for video in videos:
                    multimedia["videos"].append({"url": video})
            
            # 提取音频链接
            audio_patterns = [
                r"<audio.*?src=[\"\'](.*?)[\"\'].*?>.*?</audio>",
                r"\[audio\]\((.*?)\)",
                r"!\[audio\]\((.*?)\)"
            ]
            for pattern in audio_patterns:
                audios = re.findall(pattern, content, re.DOTALL)
                for audio in audios:
                    multimedia["audios"].append({"url": audio})
            
            # 提取代码块
            code_pattern = r"```(.*?)\n(.*?)```"
            code_blocks = re.findall(code_pattern, content, re.DOTALL)
            multimedia["code_blocks"] = [{"language": cb[0].strip(), "code": cb[1].strip()} for cb in code_blocks]
            
            # 提取表格
            table_pattern = r"(\|.*\|[\r\n]+\|[\s-]*\|[\s-]*\|.*[\r\n]+(\|.*\|[\r\n]+)*)"
            tables = re.findall(table_pattern, content)
            multimedia["tables"] = [{"content": table[0].strip()} for table in tables]
            
            # 提取数学公式
            formula_patterns = [
                r"\$\$(.*?)\$\$",  # 块级公式
                r"\$(.*?)\$"       # 行内公式
            ]
            formulas = []
            for pattern in formula_patterns:
                matches = re.findall(pattern, content, re.DOTALL)
                formulas.extend(matches)
            multimedia["formulas"] = [{"formula": formula.strip()} for formula in formulas]
            
            # 提取列表（有序和无序）
            list_patterns = [
                r"((?:^|\n)[\s]*[\-\*].*(?:\n[\s]*[\-\*].*)*)",  # 无序列表
                r"((?:^|\n)[\s]*\d+\..*(?:\n[\s]*\d+\..*)*)"     # 有序列表
            ]
            lists = []
            for pattern in list_patterns:
                matches = re.findall(pattern, content, re.DOTALL)
                lists.extend(matches)
            multimedia["lists"] = [{"content": lst.strip()} for lst in lists]
            
            logger.info(f"从素材中提取多模态元素: {len(multimedia['images'])}张图片, {len(multimedia['gifs'])}个GIF, "
                       f"{len(multimedia['videos'])}个视频, {len(multimedia['audios'])}个音频, "
                       f"{len(multimedia['code_blocks'])}个代码块, {len(multimedia['tables'])}个表格, "
                       f"{len(multimedia['formulas'])}个公式, {len(multimedia['lists'])}个列表")
            return multimedia
        
        except Exception as e:
            logger.error(f"提取多模态元素失败: {str(e)}")
            return multimedia
    
    def analyze_purpose(self, purpose_description: str) -> Dict[str, Any]:
        """
        分析用户目的描述
        
        参数:
        - purpose_description: 用户目的描述
        
        返回:
        - Dict: 包含目标人群、主题内容、意图、风格和视频长度的字典
        """
        logger.info("开始分析用户目的描述")
        
        try:
            # 创建ChatAgent进行分析
            system_prompt = """
            你是一个精准的内容目的分析专家。你的任务是从用户描述中提取以下关键信息：
            1. 目标人群 - 视频内容面向的具体受众群体
            2. 主题内容 - 视频将要讲述的具体主题或素材内容
            3. 意图分析 - 制作这个视频的目的，如教育、分析、批判等
            4. 风格偏好 - 视频内容应该采用的表达风格
            5. 视频长度 - 视频的时长要求，如1分钟、5分钟等
            
            请分析提供的描述，并以结构化JSON格式返回结果，包含以上五个关键字段。
            如果描述中未明确指定视频长度，则默认为2分钟。

            示例输入: "给技术爱好者介绍这些AI项目，主要目的是分析趋势和热点，侧重客观中立的表述，视频长度为3分钟"
            
            示例输出:
            ```json
            {
                "target_audience": "技术爱好者",
                "content_theme": "AI项目",
                "intention": "分析趋势和热点",
                "style_preference": "客观中立",
                "video_length": "3分钟"
            }
            ```
            
            务必仔细分析输入内容，确保提取出所有明确指定的信息。只有在信息真正缺失时才使用默认值。
            返回的JSON必须包含所有五个字段，确保格式正确无误。
            """
            
            agent = ChatAgent(
                system_message=system_prompt,
                model=self.model
            )
            
            # 构建用户消息
            user_message = f"""
            请分析以下视频制作目的描述，并提取关键信息：
            
            "{purpose_description}"
            
            请以JSON格式返回分析结果，必须包含目标人群、主题内容、意图分析、风格偏好和视频长度五个字段。
            """
            
            # 获取回复
            response = agent.step(user_message)
            
            # 从回复中提取JSON
            purpose_json = self._extract_json(response.msg.content)
            
            # 调试输出
            logger.info(f"提取到的目的JSON: {json.dumps(purpose_json, ensure_ascii=False)}")
            
            # 如果JSON提取失败或为空，尝试直接从文本中提取关键信息
            if not purpose_json or len(purpose_json) < 2:
                logger.warning("JSON提取失败，尝试从文本中直接提取关键信息")
                purpose_json = self._extract_purpose_directly(purpose_description)
            
            # 添加缺失的字段和默认值
            purpose_json = self._ensure_purpose_fields(purpose_json, purpose_description)
            
            logger.info(f"目的分析完成: 目标人群={purpose_json.get('target_audience', '未指定')}, 主题内容={purpose_json.get('content_theme', '未指定')}, 意图={purpose_json.get('intention', '未指定')}, 风格={purpose_json.get('style_preference', '未指定')}, 视频长度={purpose_json.get('video_length', '2分钟')}")
            return purpose_json
            
        except Exception as e:
            logger.error(f"分析用户目的描述失败: {str(e)}")
            # 返回默认分析结果
            return {
                "target_audience": "一般受众",
                "content_theme": "技术内容",
                "intention": "信息传递",
                "style_preference": "客观分析",
                "video_length": self.config.default_video_length
            }
    
    def _ensure_purpose_fields(self, purpose_json: Dict[str, Any], purpose_description: str) -> Dict[str, Any]:
        """确保目的分析结果包含所有必要字段，并填充默认值"""
        # 检查必要的字段
        required_fields = {
            "target_audience": "一般受众", 
            "content_theme": "未指定主题",
            "intention": "信息传递",
            "style_preference": "客观中立",
            "video_length": self.config.default_video_length
        }
        
        # 规范化键名
        key_mapping = {
            "目标人群": "target_audience", 
            "目标受众": "target_audience",
            "主题内容": "content_theme", 
            "内容主题": "content_theme",
            "意图分析": "intention", 
            "意图": "intention",
            "风格偏好": "style_preference", 
            "表达风格": "style_preference",
            "视频长度": "video_length", 
            "时长": "video_length"
        }
        
        # 规范化键名
        normalized_json = {}
        for k, v in purpose_json.items():
            if k in key_mapping:
                normalized_json[key_mapping[k]] = v
            else:
                normalized_json[k] = v
        
        # 填充缺失的字段
        for field, default_value in required_fields.items():
            if field not in normalized_json or not normalized_json[field]:
                # 尝试直接从描述中提取
                if field == "video_length" and "分钟" in purpose_description:
                    match = re.search(r'(\d+)\s*分钟', purpose_description)
                    if match:
                        normalized_json[field] = f"{match.group(1)}分钟"
                    else:
                        normalized_json[field] = default_value
                else:
                    normalized_json[field] = default_value
        
        return normalized_json
    
    def _extract_purpose_directly(self, purpose_description: str) -> Dict[str, Any]:
        """直接从目的描述中提取关键信息"""
        result = {}
        
        # 提取目标人群
        audience_patterns = [
            r'给(.*?)介绍', 
            r'面向(.*?)的', 
            r'(.*?)为受众',
            r'目标受众是(.*?)[,，。]',
            r'目标人群是(.*?)[,，。]'
        ]
        for pattern in audience_patterns:
            match = re.search(pattern, purpose_description)
            if match:
                result["target_audience"] = match.group(1).strip()
                break
        
        # 提取主题内容
        content_patterns = [
            r'介绍(.*?)，', 
            r'讲解(.*?)，', 
            r'关于(.*?)的',
            r'主题是(.*?)[,，。]'
        ]
        for pattern in content_patterns:
            match = re.search(pattern, purpose_description)
            if match:
                result["content_theme"] = match.group(1).strip()
                break
        
        # 提取意图
        intention_patterns = [
            r'目的是(.*?)[,，。]', 
            r'旨在(.*?)[,，。]', 
            r'为了(.*?)[,，。]',
            r'意图是(.*?)[,，。]'
        ]
        for pattern in intention_patterns:
            match = re.search(pattern, purpose_description)
            if match:
                result["intention"] = match.group(1).strip()
                break
        
        # 提取风格
        style_patterns = [
            r'侧重(.*?)的表述', 
            r'风格(.*?)[,，。]', 
            r'采用(.*?)的方式',
            r'以(.*?)的风格'
        ]
        for pattern in style_patterns:
            match = re.search(pattern, purpose_description)
            if match:
                result["style_preference"] = match.group(1).strip()
                break
        
        # 提取视频长度
        length_patterns = [
            r'(\d+)\s*分钟', 
            r'时长(\d+)', 
            r'长度为(\d+)',
            r'视频长度[为是](\d+)'
        ]
        for pattern in length_patterns:
            match = re.search(pattern, purpose_description)
            if match:
                result["video_length"] = f"{match.group(1)}分钟"
                break
        
        return result
    
    def _extract_json(self, text: str) -> Dict[str, Any]:
        """从文本中提取JSON内容"""
        try:
            # 查找JSON代码块
            json_pattern = r"```(?:json)?(.*?)```"
            import re
            matches = re.findall(json_pattern, text, re.DOTALL)
            
            if matches:
                for match in matches:
                    try:
                        # 尝试解析JSON
                        json_str = match.strip()
                        return json.loads(json_str)
                    except:
                        continue
            
            # 如果没有在代码块中找到，尝试在文本中查找JSON
            start_idx = text.find("{")
            end_idx = text.rfind("}") + 1
            
            if start_idx >= 0 and end_idx > start_idx:
                json_str = text[start_idx:end_idx]
                return json.loads(json_str)
                
            raise ValueError("未找到有效的JSON内容")
            
        except Exception as e:
            logger.error(f"提取JSON出错: {str(e)}")
            return {}
            
    def _extract_markdown(self, text: str) -> str:
        """从文本中提取Markdown内容"""
        # 首先检查是否有明确标记的Markdown段落
        markdown_markers = [
            "---MARKDOWN_BEGINS---", 
            "```markdown", 
            "以下是规范化的Markdown素材：",
            "以下是优化后的Markdown素材：",
            "# "  # 以标题标记开始的内容也可能是Markdown
        ]
        
        # 尝试通过明确的标记寻找Markdown起始位置
        start_index = -1
        for marker in markdown_markers:
            if marker in text:
                possible_start = text.find(marker)
                if start_index == -1 or possible_start < start_index:
                    start_index = possible_start
        
        # 如果找到了标记，从标记处开始提取
        if start_index != -1:
            # 从标记处开始，但跳过标记本身（除非是Markdown标题）
            if text[start_index:start_index+2] == "# ":
                extracted_text = text[start_index:]
            else:
                # 找到标记后的第一个换行
                next_line = text.find("\n", start_index)
                if next_line != -1:
                    extracted_text = text[next_line+1:]
                else:
                    extracted_text = text[start_index:]
            
            # 寻找终止标记
            end_markers = [
                "---MARKDOWN_ENDS---", 
                "```", 
                "以上是优化后的素材", 
                "以上是我优化的",
                "希望这个优化",
                "这样的修改"
            ]
            for marker in end_markers:
                if marker in extracted_text:
                    extracted_text = extracted_text.split(marker)[0]
            
            return extracted_text.strip()
        
        # 如果没有明确的标记，尝试通过Markdown代码块提取
        markdown_pattern = r"```(?:markdown)?\s*([\s\S]*?)```"
        matches = re.findall(markdown_pattern, text, re.DOTALL)
        
        if matches:
            # 选择最长的匹配结果，避免提取到不完整的片段
            return max(matches, key=len).strip()
        
        # 如果没有明确的Markdown代码块，但文本看起来已经是Markdown格式（包含标题）
        if re.search(r"^#\s+", text, re.MULTILINE):
            # 尝试找到第一个标题
            match = re.search(r"^#\s+", text, re.MULTILINE)
            if match:
                start = match.start()
                return text[start:].strip()
        
        # 无法提取有效的Markdown，返回原始文本
        logger.warning("无法提取Markdown代码块，使用完整响应文本")
        return text
        
    def _validate_material_completeness(self, content: str) -> bool:
        """
        验证生成的素材是否包含所有必要部分
        
        参数:
        - content: 生成的素材内容
        
        返回:
        - bool: 是否完整
        """
        # 检查必要的部分是否存在
        required_sections = [
            r"#\s+.*?标题|#\s+.*?题目|#\s+.*?[A-Za-z]", # 视频标题
            r"##\s+视频信息|##\s+内容信息", # 视频信息部分
            r"目标受众|受众群体|观众", # 目标受众信息
            r"视频时长|视频长度|时长", # 视频时长信息
            r"核心意图|主要目的|目的|意图", # 核心意图信息
            r"表达风格|风格|表述风格", # 表达风格信息
            r"##\s+内容|##\s+正文|##\s+内容精华", # 内容主体部分
            r"###\s+.*?", # 至少一个内容小节
            r"##\s+.*?结论|##\s+总结|##\s+关键结论" # 结论部分
        ]
        
        missing_sections = []
        for section_pattern in required_sections:
            if not re.search(section_pattern, content, re.IGNORECASE):
                missing_sections.append(section_pattern)
        
        if missing_sections:
            logger.warning(f"素材内容缺少以下部分: {missing_sections}")
            return False
        
        # 检查内容部分是否有实质内容（不仅仅是标题）
        content_sections = re.findall(r"###\s+.*?\n(.*?)(?=##|\Z)", content, re.DOTALL)
        if not content_sections or all(len(section.strip()) < 50 for section in content_sections):
            logger.warning("素材内容部分缺少实质性内容")
            return False
        
        return True

    def _validate_material_multimedia(self, content: str, original_multimedia: Dict[str, List[Any]]) -> bool:
        """
        验证生成的素材中引用的多媒体元素是否在原素材中存在
        
        参数:
        - content: 生成的素材内容
        - original_multimedia: 原素材中的多媒体元素
        
        返回:
        - bool: 是否所有引用的多媒体元素都在原素材中存在
        """
        invalid_references = []
        
        # 检查图片引用
        image_references = re.findall(r"!\[(.*?)\]\((.*?)\)", content)
        original_image_urls = [img.get("url") for img in original_multimedia.get("images", [])]
        original_image_urls += [gif.get("url") for gif in original_multimedia.get("gifs", [])]
        
        for _, img_url in image_references:
            if img_url and img_url not in original_image_urls and not any(url.endswith(img_url) for url in original_image_urls):
                invalid_references.append(f"图片: {img_url}")
        
        # 检查视频引用
        video_references = re.findall(r"<(https?://.*?)>", content)
        original_video_urls = [video.get("url") for video in original_multimedia.get("videos", [])]
        
        for video_url in video_references:
            if video_url and video_url not in original_video_urls and not any(url.endswith(video_url) for url in original_video_urls):
                invalid_references.append(f"视频: {video_url}")
        
        # 检查代码块（更宽松的检查，只确保语言和结构匹配）
        code_blocks = re.findall(r"```(.*?)\n(.*?)```", content, re.DOTALL)
        original_code_languages = set(cb.get("language", "").strip() for cb in original_multimedia.get("code_blocks", []))
        
        for lang, _ in code_blocks:
            lang = lang.strip()
            if lang and lang not in original_code_languages and lang not in ["", "markdown"]:
                invalid_references.append(f"代码块(语言): {lang}")
        
        if invalid_references:
            logger.warning(f"素材中包含以下可能不存在于原素材的多媒体引用: {invalid_references}")
            return False
        
        return True

    def generate_material(self, original_content: str, purpose_analysis: Dict[str, Any], multimedia_elements: Dict[str, List[Any]]) -> str:
        """
        生成规范化的视频素材
        
        参数:
        - original_content: 原始素材内容
        - purpose_analysis: 目的分析结果
        - multimedia_elements: 多模态元素
        
        返回:
        - str: 规范化的视频素材Markdown
        """
        logger.info("开始生成规范化的视频素材")
        
        try:
            # 检查原始内容长度
            original_length = len(original_content)
            logger.info(f"原始内容长度: {original_length} 字符")
            
            # 如果原始内容过长，可能需要截断处理
            max_content_length = 300000  # 设置最大处理长度
            truncated_content = original_content
            if original_length > max_content_length:
                logger.info(f"原始内容过长，将截断到前 {max_content_length} 字符进行处理")
                # 保留前面的内容并添加说明
                truncated_content = original_content[:max_content_length] + "\n\n... [内容过长已截断，完整内容请参考原文] ..."
            
            # 准备多模态元素信息
            images = multimedia_elements.get("images", [])
            gifs = multimedia_elements.get("gifs", [])
            videos = multimedia_elements.get("videos", [])
            audios = multimedia_elements.get("audios", [])
            code_blocks = multimedia_elements.get("code_blocks", [])
            tables = multimedia_elements.get("tables", [])
            formulas = multimedia_elements.get("formulas", [])
            lists = multimedia_elements.get("lists", [])
            
            # 构建多模态元素信息字符串
            multimedia_info = []
            
            if images:
                multimedia_info.append(f"- 图片: {len(images)}张，可选择最能说明核心概念的图片引用")
            
            if gifs:
                multimedia_info.append(f"- GIF: {len(gifs)}个，可选择最能展示动态过程的GIF")
            
            if videos:
                multimedia_info.append(f"- 视频: {len(videos)}个，可选择最有价值的视频引用")
            
            if audios:
                multimedia_info.append(f"- 音频: {len(audios)}个，可选择最关键的音频内容")
            
            if code_blocks:
                multimedia_info.append(f"- 代码块: {len(code_blocks)}个，选择最能说明问题的代码段")
            
            if tables:
                multimedia_info.append(f"- 表格: {len(tables)}个，选择包含核心数据的表格")
            
            if formulas:
                multimedia_info.append(f"- 数学公式: {len(formulas)}个，选择关键算法的公式")
            
            if lists:
                multimedia_info.append(f"- 列表内容: {len(lists)}个，可转化为适当的列表形式")
            
            multimedia_info_str = "\n".join(multimedia_info)
            
            # 确定视频长度对应的内容量要求
            video_length = purpose_analysis.get('video_length', '2分钟')
            video_minutes = 2  # 默认
            
            # 提取视频时长的数字部分
            if isinstance(video_length, str):
                match = re.search(r'(\d+)', video_length)
                if match:
                    video_minutes = int(match.group(1))
            
            # 设置期望内容长度范围
            min_content_length = video_minutes * 300  # 每分钟约300字的下限
            target_content_length = video_minutes * 450  # 每分钟约450字的目标
            max_content_length = video_minutes * 600  # 每分钟约600字的上限
            
            logger.info(f"视频长度 {video_minutes} 分钟，目标内容长度范围: {min_content_length}-{max_content_length} 字符")
            
            # 创建ChatAgent - 修改系统提示
            system_prompt = f"""
            你是一位专业的视频素材编辑专家，擅长将原始素材转化为规范化的视频讲解素材。
            
            请根据提供的原始素材内容，分析其结构，结合目标受众信息和多模态元素，生成规范化的视频素材Markdown文档。
            
            视频信息:
            - 目标人群: {purpose_analysis.get('target_audience', '一般受众')}
            - 主题内容: {purpose_analysis.get('content_theme', '未指定')}
            - 分析意图: {purpose_analysis.get('intention', '信息传递')}
            - 风格偏好: {purpose_analysis.get('style_preference', '客观分析')}
            - 视频长度: {video_length}
            
            素材中可用的多模态元素:
            {multimedia_info_str if multimedia_info_str else "- 未检测到特定多模态元素"}
            
            请分析素材的内容结构，针对"{video_length}"的视频长度，提炼适量的核心内容。
            
            请生成符合以下格式的规范化Markdown：
            {MATERIAL_FORMAT}
            
            重要说明:
            1. 最重要的是确保markdown各部分输出完整，包括标题、视频信息、内容各部分和结论
            2. 保持原始素材中所有事实的准确性，不要修改或编造事实，只调整表述方式
            3. 根据视频长度"{video_length}"的参考范围调整内容密度:
               - 1-2分钟视频: 提炼3-4个最核心的要点，约450-600字（参考范围，非严格限制）
               - 3-5分钟视频: 提炼5-7个重要要点，约900-1500字（参考范围，非严格限制）
               - 5-10分钟视频: 提供更全面的内容覆盖，约1500-3000字（参考范围，非严格限制）
            4. 根据"{purpose_analysis.get('style_preference', '客观分析')}"的风格要求调整表述方式，但不改变事实本身
            5. 确保内容的深度和专业程度与"{purpose_analysis.get('target_audience', '一般受众')}"的知识水平相匹配
            6. 在内容描述中自然融入原素材的重要数据和结论：
               - 使用加粗、斜体等Markdown格式突出关键数字和实验结果
               - 将比较数据组织成列表或表格形式，提高可读性
               - 将时间节点或发展历程用清晰的叙述结构表达
               - 确保核心论点和发现在内容中得到强调
            7. 多模态元素处理原则:
               - 只引用原素材中实际存在的多媒体素材，严禁创建不存在的素材引用
               - 在正文各部分直接引用多媒体素材，不要放在单独的部分
               - 每个引用都通过简单描述说明与周围内容的关系
               - 只选择直接支持核心论点或关键信息理解的元素
               - 确保每个引用都与周围文本内容高度相关
               - 不遗漏能有效增强内容理解的重要元素
               - 避免引入仅作装饰或次要补充的元素
               - 多模态元素质量优于数量，确保每个引用都有明确的信息传递目的
            8. 多模态元素引用格式:
               - 图片：![图片描述](原素材中图片的实际路径) - 简要说明
               - 视频：<原素材中视频的实际链接> - 简要说明
               - GIF：![GIF描述](原素材中GIF的实际路径) - 简要说明
               - 代码块：```[语言] [原素材中代码的实际内容] ``` - 简要说明
               - 表格：直接复制原素材中的表格 - 简要说明
               - 公式：$[原素材中公式的实际内容]$ - 简要说明
            9. 充分利用Markdown的表现力，使用适合内容特点的格式：
               - 对步骤性内容用有序列表
               - 对并列概念用无序列表或表格
               - 对层次关系用适当的标题结构
               - 对重点内容用加粗或其他强调形式
            10. 设计吸引人但不夸大的标题，帮助吸引目标受众
            11. 输出的内容必须是完整的Markdown格式，不要省略或截断内容
            
            请确保你的输出足够完整，内容结构清晰完整，markdown各部分都要输出。字数可以参考{video_minutes}分钟视频的理想{target_content_length}字符，但不必严格遵循。多模态元素必须是原素材中实际存在的，直接嵌入到相关内容中，既不遗漏关键元素，也不过度引入次要元素。
            """
            
            # 构建用户消息
            user_message = f"""
            请分析以下原始素材内容的结构，并转换为规范化的视频素材Markdown文档:
            
            ```
            {truncated_content}
            ```
            
            请确保:
            1. 保持所有事实的准确性，仅根据风格"{purpose_analysis.get('style_preference', '客观分析')}"调整表述方式
            2. 确保markdown各部分输出完整，包括标题、视频信息、内容各部分和结论
            3. 内容深度与"{purpose_analysis.get('target_audience', '一般受众')}"的知识水平相匹配
            4. 内容能够达成"{purpose_analysis.get('intention', '信息传递')}"的目的
            5. 设计清晰的内容结构和吸引人的标题，帮助传递核心信息
            6. 在内容描述中融入重要数据和结论，使用合适的Markdown格式使其突出
            7. 只引用原素材中实际存在的多媒体素材，不要创建不存在的素材引用
            8. 使用合适的Markdown表现形式（列表、表格、强调等）使内容更具可读性
            9. 突出与主题"{purpose_analysis.get('content_theme', '技术内容')}"相关的核心内容
            
            务必注意：只使用原素材中已有的多媒体元素（图片、视频、代码等），不要创建不存在的素材引用。输出内容要结构完整，包含所有必要的部分。
            """
            
            # 创建ChatAgent实例
            agent = ChatAgent(
                system_message=system_prompt,
                model=self.model
            )
            
            # 获取回复
            logger.info("正在生成规范化素材内容...")
            response = agent.step(user_message)
            logger.info(f"获取到响应，长度: {len(response.msg.content)} 字符")
            
            # 提取Markdown内容
            material_markdown = self._extract_markdown(response.msg.content)
            
            if not material_markdown:
                logger.warning("无法从响应中提取有效的Markdown，使用完整响应")
                material_markdown = response.msg.content
                
            # 检查生成内容的长度
            generated_length = len(material_markdown)
            logger.info(f"生成内容长度: {generated_length} 字符")
            
            # 验证内容完整性
            is_complete = self._validate_material_completeness(material_markdown)
            
            # 验证多媒体引用
            is_valid_multimedia = self._validate_material_multimedia(material_markdown, multimedia_elements)
            if not is_valid_multimedia:
                logger.warning("生成内容中包含可能不存在于原素材的多媒体引用，将进行修正")
            
            # 如果内容不完整或存在无效多媒体引用或生成的内容太短，尝试补充内容
            if not is_complete or not is_valid_multimedia or generated_length < min_content_length:
                reasons = []
                if not is_complete:
                    reasons.append("结构不完整")
                if not is_valid_multimedia:
                    reasons.append("包含无效多媒体引用")
                if generated_length < min_content_length:
                    reasons.append("长度不足")
                
                reason_str = "、".join(reasons)
                logger.warning(f"生成内容存在以下问题：{reason_str}，尝试补充完善内容")
                
                # 准备补充内容的提示
                supplement_prompt = f"""
                你生成的内容存在以下问题，需要修正：
                
                {'- 内容结构不完整，缺少必要的部分' if not is_complete else ''}
                {'- 包含可能不存在于原素材的多媒体引用，请仅使用原素材中已有的多媒体元素' if not is_valid_multimedia else ''}
                {'- 内容长度不足，仅为 ' + str(generated_length) + ' 字符' if generated_length < min_content_length else ''}
                
                请根据以下已生成的内容，完善所有必要的结构部分，确保markdown文档完整，并添加更多细节：
                
                ```markdown
                {material_markdown}
                ```
                
                必须确保输出包含完整的markdown文档结构：
                1. 视频标题
                2. 视频信息部分（包含目标受众、视频时长、核心意图和表达风格）
                3. 内容主体部分（至少1-3个子部分，以###开头）
                4. 关键结论部分（##关键结论）
                
                关于多媒体元素的重要说明：
                - 只引用原素材中实际存在的多媒体素材，不要创建不存在的素材引用
                - 如果不确定素材是否存在，则不要引用
                - 在正文中直接嵌入适当的多媒体元素，并附上简要说明
                
                原素材中可用的多媒体元素：
                {multimedia_info_str if multimedia_info_str else "- 未检测到特定多模态元素"}
                
                确保最终输出是一个结构完整、内容充实的Markdown文档。
                """
                
                # 再次生成内容
                logger.info("正在补充完善内容...")
                supplement_response = agent.step(supplement_prompt)
                logger.info(f"获取到补充响应，长度: {len(supplement_response.msg.content)} 字符")
                
                # 提取补充的Markdown内容
                supplemented_markdown = self._extract_markdown(supplement_response.msg.content)
                
                # 再次验证内容完整性
                if supplemented_markdown:
                    is_supplemented_complete = self._validate_material_completeness(supplemented_markdown)
                    
                    if is_supplemented_complete and len(supplemented_markdown) > len(material_markdown):
                        logger.info("补充内容完整性良好，使用补充后的内容")
                        material_markdown = supplemented_markdown
                    else:
                        # 如果补充内容仍不完整但比原内容长，尝试合并两者取长补短
                        if len(supplemented_markdown) > len(material_markdown):
                            logger.warning("补充内容仍不完整，但内容更丰富，尝试合并内容取长补短")
                            material_markdown = self._merge_markdown_contents(material_markdown, supplemented_markdown)
                else:
                    logger.warning("补充内容提取失败，将继续使用原始内容")
            
            logger.info("规范化视频素材生成完成")
            return material_markdown
            
        except Exception as e:
            logger.error(f"生成规范化视频素材失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            
            # 返回一个基础模板
            return f"""
            # 视频素材（生成失败）
            
            ## 视频信息
            - **目标受众**：{purpose_analysis.get('target_audience', '一般受众')}
            - **视频长度**：{purpose_analysis.get('video_length', '2分钟')}
            - **核心意图**：{purpose_analysis.get('intention', '信息传递')}
            
            ## 原始内容
            请参考原始素材内容。
            
            ## 错误信息
            生成视频素材时发生错误: {str(e)}
            """
    
    def _merge_markdown_contents(self, original: str, supplement: str) -> str:
        """
        合并两个Markdown内容，取长补短
        
        参数:
        - original: 原始Markdown内容
        - supplement: 补充的Markdown内容
        
        返回:
        - str: 合并后的Markdown内容
        """
        # 如果某一个为空，返回另一个
        if not original:
            return supplement
        if not supplement:
            return original
            
        merged = ""
        
        # 提取标题部分
        orig_title_match = re.search(r"(#\s+.*?)\n", original)
        supp_title_match = re.search(r"(#\s+.*?)\n", supplement)
        
        if supp_title_match:
            merged += supp_title_match.group(1) + "\n\n"
        elif orig_title_match:
            merged += orig_title_match.group(1) + "\n\n"
        
        # 提取视频信息部分
        orig_info_match = re.search(r"(##\s+视频信息.*?)(?=##|\Z)", original, re.DOTALL)
        supp_info_match = re.search(r"(##\s+视频信息.*?)(?=##|\Z)", supplement, re.DOTALL)
        
        if supp_info_match and len(supp_info_match.group(1)) > 50:
            merged += supp_info_match.group(1) + "\n\n"
        elif orig_info_match:
            merged += orig_info_match.group(1) + "\n\n"
            
        # 提取内容部分
        merged += "## 内容精华\n\n"
        
        orig_sections = re.findall(r"(###\s+.*?(?=###|\Z))", original, re.DOTALL)
        supp_sections = re.findall(r"(###\s+.*?(?=###|\Z))", supplement, re.DOTALL)
        
        # 取两者中小节数量较多的一个
        if len(supp_sections) >= len(orig_sections):
            for section in supp_sections:
                merged += section + "\n\n"
        else:
            for section in orig_sections:
                merged += section + "\n\n"
                
        # 提取结论部分
        orig_conclusion_match = re.search(r"(##\s+.*?结论.*?)(?=##|\Z)", original, re.DOTALL)
        supp_conclusion_match = re.search(r"(##\s+.*?结论.*?)(?=##|\Z)", supplement, re.DOTALL)
        
        if supp_conclusion_match:
            merged += supp_conclusion_match.group(1)
        elif orig_conclusion_match:
            merged += orig_conclusion_match.group(1)
        else:
            # 如果都没有结论部分，添加一个基本的结论部分
            merged += "## 关键结论\n\n本视频内容围绕核心主题进行了详细讲解，希望能为目标受众提供有价值的信息和见解。"
            
        return merged
    
    def refine_material(self, material_content: str, purpose_analysis: Dict[str, Any], multimedia_elements: Dict[str, List[Any]], max_rounds: int = None) -> str:
        """
        通过角色扮演迭代优化素材内容
        
        参数:
        - material_content: 初始素材内容
        - purpose_analysis: 目的分析结果
        - multimedia_elements: 多模态元素
        - max_rounds: 最大迭代轮数
        
        返回:
        - str: 优化后的素材内容
        """
        # 如果未提供max_rounds，则从配置中获取
        if max_rounds is None:
            max_rounds = self.config.max_rounds
            
        logger.info(f"开始通过角色扮演迭代优化素材内容，最大轮数: {max_rounds}")
        
        # 视频长度从目的分析中获取
        video_length = purpose_analysis.get('video_length', '2分钟')
        match = re.search(r'(\d+)', video_length)
        video_minutes = 2  # 默认值
        if match:
            video_minutes = int(match.group(1))
            
        # 设置期望内容长度范围
        min_content_length = video_minutes * 300  # 每分钟约300字的下限
        target_content_length = video_minutes * 450  # 每分钟约450字的目标
        
        # 准备任务内容
        task_content = f"""
        请优化以下视频素材内容，使其更好地适应目标受众、视频长度和意图，同时保持所有事实的准确性。
        
        视频素材内容：
        ```markdown
        {material_content}
        ```
        
        视频信息:
        - 目标人群: {purpose_analysis.get('target_audience', '一般受众')}
        - 主题内容: {purpose_analysis.get('content_theme', '未指定')}
        - 分析意图: {purpose_analysis.get('intention', '信息传递')}
        - 风格偏好: {purpose_analysis.get('style_preference', '客观分析')}
        - 视频长度: {video_length}（需要生成约{min_content_length}-{target_content_length}字的内容）
        
        素材中可用的多模态元素:
        {json.dumps(multimedia_elements, ensure_ascii=False, indent=2) if multimedia_elements else "未检测到特定多模态元素"}
        
        请通过素材编辑者和质量检查者的对话，共同优化这个素材，确保它：
        1. 完全保持原始素材中事实的准确性，仅调整表述方式
        2. 确保结构完整，包含标题、视频信息、内容各部分和结论
        3. 根据视频长度限制提供恰当的内容密度和深度
        4. 完美适配目标受众的知识水平和兴趣
        5. 表述风格符合要求但不扭曲事实
        6. 内容结构清晰，便于观众理解
        7. 只引用原素材中实际存在的多媒体元素，不创建不存在的引用
        8. 标题和内容能有效吸引目标受众，但不夸大或误导

        请记住，优化的目标是调整表述方式和结构，不是改变或编造事实。
        
        重要格式要求：
        当素材编辑者提出修改后的最终素材内容时，必须使用以下格式：
        
        ===开始：规范化Markdown素材===
        [完整的Markdown内容]
        ===结束：规范化Markdown素材===
        
        这将确保我们准确识别出最终素材内容。
        """
        
        # 将角色提示合并到任务中
        complete_task_content = f"""
        {task_content}
        
        ## 角色定位
        
        素材编辑者(Material Editor)：
        {MATERIAL_EDITOR_PROMPT}
        
        质量检查者(Quality Reviewer)：
        {QUALITY_REVIEWER_PROMPT}
        
        ## 对于多模态元素的重要说明
        只引用原素材中实际存在的多媒体素材，不要创建不存在的素材引用。以下是原素材中可用的多模态元素:
        {json.dumps(multimedia_elements, ensure_ascii=False, indent=2) if multimedia_elements else "未检测到特定多模态元素"}
        
        ## 对话流程安排
        1. 素材编辑者首先分析当前素材，并提出初步优化后的完整Markdown素材
        2. 质量检查者评估内容是否符合视频长度和目标受众要求，检查多媒体引用是否有效，并检查事实准确性
        3. 素材编辑者根据反馈修改优化内容，并再次提供完整的Markdown素材
        4. 最后一轮对话时，素材编辑者必须提供最终版本的完整Markdown素材，使用格式标记：
        
        ===开始：规范化Markdown素材===
        [完整的Markdown内容]
        ===结束：规范化Markdown素材===
        
        请严格遵循这个格式规范，确保最终输出是完整的Markdown格式素材，而不是评估或讨论内容。
        """
        
        try:
            # 设置角色扮演
            role_playing = RolePlaying(
                # 设置素材编辑者为助手角色
                assistant_role_name="Material Editor",
                assistant_agent_kwargs={"model": self.model},
                # 设置质量检查者为用户角色
                user_role_name="Quality Reviewer",
                user_agent_kwargs={"model": self.model},
                # 任务参数
                task_prompt=complete_task_content,
                task_type=TaskType.AI_SOCIETY,
                with_task_specify=False,  # 禁用task_specify避免报错
                # 附加配置
                output_language="chinese",
            )
            
            # 开始对话
            logger.info("开始角色对话")
            messages = []
            
            # 初始化对话
            chat_history = role_playing.init_chat()
            messages.append(chat_history)
            
            # 收集所有可能的素材版本，包括原始版本
            potential_materials = [material_content]
            
            # 最后一轮提前特别提醒
            is_final_reminder_sent = False
            
            # 进行多轮对话
            for round_num in range(max_rounds * 2):  # 每轮包含两步对话
                logger.info(f"对话步骤 {round_num + 1}")
                
                # 在最后一轮对话前，提醒编辑者使用正确的格式标记
                if round_num == max_rounds * 2 - 2 and not is_final_reminder_sent:
                    # 这是最后一轮对话的前一步
                    remind_message = """
                    这是最后一轮对话。请记住，您必须在回复中提供最终版本的完整Markdown素材，
                    并使用以下格式标记：
                    
                    ===开始：规范化Markdown素材===
                    [完整的Markdown内容]
                    ===结束：规范化Markdown素材===
                    
                    这样我才能准确识别最终素材内容。
                    """
                    chat_history.content += "\n\n" + remind_message
                    is_final_reminder_sent = True
                
                try:
                    # 进行对话步骤
                    assistant_response, user_response = role_playing.step(chat_history)
                    
                    # 从响应中获取消息
                    assistant_message = assistant_response.msg
                    user_message = user_response.msg
                    
                    # 添加到历史记录
                    chat_history = assistant_message
                    messages.append(assistant_message)
                    messages.append(user_message)
                    
                    # 处理助手消息中的内容
                    if hasattr(assistant_message, 'content') and assistant_message.role_name == "Material Editor":
                        content = assistant_message.content
                        
                        # 首先检查是否有明确标记的Markdown段落
                        if "===开始：规范化Markdown素材===" in content and "===结束：规范化Markdown素材===" in content:
                            parts = content.split("===开始：规范化Markdown素材===", 1)
                            if len(parts) > 1:
                                marked_content = parts[1].split("===结束：规范化Markdown素材===", 1)[0].strip()
                                logger.info(f"步骤 {round_num + 1} 从明确标记中提取到Markdown素材")
                                potential_materials.append(marked_content)
                                
                                # 如果是最后一轮并使用了正确的标记，可以直接结束对话
                                if round_num >= max_rounds * 2 - 2:
                                    logger.info("获取到最终标记的素材内容，结束对话")
                                    break
                        
                        # 尝试提取Markdown文本（作为备选）
                        extracted_markdown = self._extract_markdown(content)
                        if extracted_markdown and len(extracted_markdown) > 100:
                            logger.info(f"步骤 {round_num + 1} 尝试提取Markdown素材")
                            # 检查提取的内容是否为有效Markdown（有标题结构）
                            if "#" in extracted_markdown[:50] or re.search(r"^#\s+", extracted_markdown, re.MULTILINE):
                                logger.info(f"步骤 {round_num + 1} 提取到可能的Markdown素材")
                                potential_materials.append(extracted_markdown)
                                
                    # 如果已经完成了足够的轮次
                    if round_num >= max_rounds * 2 - 1:
                        logger.info("对话已完成足够的轮次")
                        break
                        
                except Exception as e:
                    logger.error(f"对话步骤出错: {str(e)}")
                    break
            
            logger.info("角色对话完成，迭代优化结束")
            
            # 筛选并选择最佳素材内容
            valid_materials = []
            
            # 按质量筛选素材（从最后一轮向前）
            for material in reversed(potential_materials):
                # 跳过太短的内容或明显非Markdown格式的内容
                if len(material) < 100 or (not material.startswith("#") and not re.search(r"^#\s+", material, re.MULTILINE)):
                    continue
                    
                # 清理并格式化Markdown
                material = material.strip()
                # 确保以标题开始
                if not material.startswith("#"):
                    title_match = re.search(r"^#\s+", material, re.MULTILINE)
                    if title_match:
                        start_idx = title_match.start()
                        material = material[start_idx:]
                
                valid_materials.append(material)
            
            # 如果没有找到任何有效素材，回退到原始素材
            if not valid_materials:
                logger.warning("未找到任何有效的优化素材，回退到原始素材")
                return material_content
            
            # 选择最长的有效素材（通常是最完整的）
            final_material = max(valid_materials, key=len)
            logger.info(f"选择了长度为 {len(final_material)} 字符的最终素材")
            
            # 验证最终内容的完整性
            is_final_complete = self._validate_material_completeness(final_material)
            is_valid_multimedia = self._validate_material_multimedia(final_material, multimedia_elements)
            
            if not is_final_complete or not is_valid_multimedia:
                logger.warning(f"最终优化的内容存在问题：{'结构不完整' if not is_final_complete else ''} {'有无效多媒体引用' if not is_valid_multimedia else ''}")
                
                # 创建ChatAgent实例，用于修复不完整的内容
                system_prompt = f"""
                你是一位专业的markdown文档修复专家。你需要检查并修复提供的视频素材markdown文档，确保其结构完整且所有多媒体引用有效。
                
                提供的markdown文档可能存在以下问题：
                1. 结构不完整，缺少某些必要部分
                2. 包含不存在于原素材的多媒体引用
                
                请确保修复后的文档：
                1. 包含以下所有必要部分：
                   - 视频标题（以单个#开头）
                   - 视频信息部分（##视频信息），包含目标受众、视频时长、核心意图和表达风格
                   - 内容主体部分（##内容精华），包含至少1-3个子部分（以###开头）
                   - 关键结论部分（##关键结论）
                   
                2. 只使用原素材中实际存在的多媒体素材，移除所有可能不存在的引用
                   
                原素材中可用的多模态元素：
                {json.dumps(multimedia_elements, ensure_ascii=False, indent=2) if multimedia_elements else "未检测到特定多模态元素"}
                
                请保留原文档中所有已有的有效内容，只添加缺失的部分或修正无效引用。如果某部分完全缺失，请根据文档上下文合理创建。
                对于视频信息部分，可参考以下信息：
                - 目标受众：{purpose_analysis.get('target_audience', '一般受众')}
                - 视频时长：{purpose_analysis.get('video_length', '2分钟')}
                - 核心意图：{purpose_analysis.get('intention', '信息传递')}
                - 表达风格：{purpose_analysis.get('style_preference', '客观分析')}
                
                请输出修复后的完整markdown文档，不要添加任何解释或说明。
                """
                
                repair_agent = ChatAgent(
                    system_message=system_prompt,
                    model=self.model
                )
                
                repair_message = f"""
                请修复以下markdown文档，确保其结构完整且所有多媒体引用有效：
                
                ```markdown
                {final_material}
                ```
                
                {'文档结构不完整，请确保包含所有必要部分。' if not is_final_complete else ''}
                {'文档中可能包含不存在于原素材的多媒体引用，请移除或替换这些引用。' if not is_valid_multimedia else ''}
                
                请只输出修复后的完整markdown文档，不要添加任何解释或额外内容。
                """
                
                # 获取修复响应
                logger.info("尝试修复内容结构...")
                repair_response = repair_agent.step(repair_message)
                
                # 提取修复后的Markdown
                repaired_markdown = self._extract_markdown(repair_response.msg.content)
                
                # 验证修复后内容的完整性
                if repaired_markdown and self._validate_material_completeness(repaired_markdown):
                    logger.info("成功修复内容结构，使用修复后的内容")
                    return repaired_markdown
                else:
                    logger.warning("修复尝试失败，使用原始优化内容")
            
            return final_material
            
        except Exception as e:
            logger.error(f"迭代优化素材内容失败: {str(e)}")
            logger.error(traceback.format_exc())
            # 出错时返回原始素材
            return material_content
    
    def save_material(self, content: str, output_file: str = None) -> str:
        """
        保存素材内容到文件
        
        参数:
        - content: 素材内容
        - output_file: 输出文件路径，如果为None则使用配置中的默认路径
        
        返回:
        - str: 保存的文件路径
        """
        if output_file is None:
            output_file = self.config.material_file
        
        # 确保输出目录存在
        output_dir = os.path.dirname(output_file)
        os.makedirs(output_dir, exist_ok=True)
        
        # 写入文件
        try:
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(content)
            logger.info(f"素材内容已保存到 {output_file}")
            return output_file
        except Exception as e:
            logger.error(f"保存素材内容失败: {str(e)}")
            # 尝试使用备用路径
            backup_file = f"output/material_backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            try:
                with open(backup_file, "w", encoding="utf-8") as f:
                    f.write(content)
                logger.info(f"素材内容已保存到备用文件 {backup_file}")
                return backup_file
            except Exception as e2:
                logger.error(f"保存到备用文件也失败: {str(e2)}")
                return ""
                
    def run(self, material_file_path: str, purpose_description: str, output_file: str = None, max_rounds: int = None) -> Dict[str, Any]:
        """
        运行素材规范化的完整流程
        
        参数:
        - material_file_path: 原始素材文件路径
        - purpose_description: 目的描述
        - output_file: 输出文件路径
        - max_rounds: 最大迭代轮数
        
        返回:
        - Dict: 包含处理结果的字典
        """
        result = {}
        
        try:
            # 1. 读取原始素材
            logger.info(f"开始处理素材文件: {material_file_path}")
            original_content = self.read_material(material_file_path)
            result["original_length"] = len(original_content)
            
            # 2. 提取多媒体元素
            logger.info("提取素材中的多媒体元素")
            multimedia_elements = self.extract_multimedia_elements(original_content)
            result["multimedia_elements"] = {
                "images_count": len(multimedia_elements.get("images", [])),
                "gifs_count": len(multimedia_elements.get("gifs", [])),
                "videos_count": len(multimedia_elements.get("videos", [])),
                "audios_count": len(multimedia_elements.get("audios", [])),
                "code_blocks_count": len(multimedia_elements.get("code_blocks", [])),
                "tables_count": len(multimedia_elements.get("tables", [])),
                "formulas_count": len(multimedia_elements.get("formulas", [])),
                "lists_count": len(multimedia_elements.get("lists", []))
            }
            
            # 3. 分析用户目的
            logger.info("分析用户目的描述")
            purpose_analysis = self.analyze_purpose(purpose_description)
            result["purpose_analysis"] = purpose_analysis
            
            # 4. 生成初始规范化素材
            logger.info("生成初始规范化素材")
            initial_material = self.generate_material(original_content, purpose_analysis, multimedia_elements)
            result["initial_material_length"] = len(initial_material)
            
            # 5. 通过角色对话优化素材
            logger.info("开始通过角色对话优化素材")
            final_material = self.refine_material(initial_material, purpose_analysis, multimedia_elements, max_rounds)
            result["final_material_length"] = len(final_material)
            
            # 6. 保存结果
            logger.info("保存优化后的素材")
            saved_file = self.save_material(final_material, output_file)
            result["saved_file"] = saved_file
            result["success"] = True
            
            logger.info("素材规范化处理完成")
            return result
            
        except Exception as e:
            logger.error(f"素材规范化处理出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            
            result["error"] = str(e)
            result["success"] = False
            return result


def main():
    """主函数，运行素材规范化处理"""
    import argparse
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="视频素材规范化处理")
    parser.add_argument("--config", default="config/config.yaml", help="配置文件路径")
    parser.add_argument("--material", required=True, help="原始素材文件路径")
    parser.add_argument("--purpose", required=True, help="目的描述")
    parser.add_argument("--output", help="输出文件路径")
    parser.add_argument("--rounds", type=int, help="优化迭代轮数")
    args = parser.parse_args()
    
    # 初始化素材代理
    agent = MaterialAgent(config_path=args.config)
    
    # 运行处理流程
    print(f"\n===== 开始处理素材: {args.material} =====")
    print(f"目的描述: {args.purpose}")
    
    if args.rounds:
        print(f"指定迭代轮数: {args.rounds}")
    
    try:
        result = agent.run(
            material_file_path=args.material,
            purpose_description=args.purpose,
            output_file=args.output,
            max_rounds=args.rounds
        )
        
        if result.get("success", False):
            print("\n处理成功!")
            print(f"多媒体元素: {result.get('multimedia_elements', {}).get('images_count', 0)}张图片, {result.get('multimedia_elements', {}).get('gifs_count', 0)}个GIF, "
                  f"{result.get('multimedia_elements', {}).get('videos_count', 0)}个视频, {result.get('multimedia_elements', {}).get('audios_count', 0)}个音频, "
                  f"{result.get('multimedia_elements', {}).get('code_blocks_count', 0)}个代码块, {result.get('multimedia_elements', {}).get('tables_count', 0)}个表格, "
                  f"{result.get('multimedia_elements', {}).get('formulas_count', 0)}个公式, {result.get('multimedia_elements', {}).get('lists_count', 0)}个列表")
            
            # 打印目的分析结果
            purpose = result.get("purpose_analysis", {})
            print("\n目的分析结果:")
            print(f"- 目标受众: {purpose.get('target_audience', '未指定')}")
            print(f"- 主题内容: {purpose.get('content_theme', '未指定')}")
            print(f"- 意图分析: {purpose.get('intention', '未指定')}")
            print(f"- 风格偏好: {purpose.get('style_preference', '未指定')}")
            
            # 打印处理统计
            print("\n处理统计:")
            original_length = result.get("original_length", 0)
            final_length = result.get("final_material_length", 0)
            print(f"- 原始素材: {original_length} 字符")
            print(f"- 最终素材: {final_length} 字符")
            
            if final_length and original_length:
                change_percent = (final_length - original_length) / original_length * 100
                change_type = "增加" if change_percent > 0 else "减少"
                print(f"- 内容变化: {change_type} {abs(change_percent):.1f}%")
            
            print(f"\n规范化素材已保存至: {result.get('saved_file')}")
        else:
            print(f"\n处理失败: {result.get('error', '未知错误')}")
    
    except Exception as e:
        import traceback
        print(f"\n处理过程中发生错误: {str(e)}")
        traceback.print_exc()


if __name__ == "__main__":
    main() 