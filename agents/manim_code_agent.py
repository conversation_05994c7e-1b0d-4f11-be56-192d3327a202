from dotenv import load_dotenv

load_dotenv()

import json
import os
import re
import yaml
from pathlib import Path
from typing import Optional

from camel.agents import ChatAgent
from camel.models import BaseModelBackend, ModelFactory
from camel.toolkits.base import BaseToolkit
from camel.toolkits.function_tool import FunctionTool
from camel.types import ModelPlatformType
from loguru import logger

PROMPT_TEMPLATE = """
参考以下说明和示例，通过 manim 库为当前分镜生成 manim 代码。

<function_reference>
<function>
<name>
render_media_display
</name>
<description>
生成用于显示媒体内容的Manim动画代码。

该函数接收一个图片文件路径列表，并生成相应的Manim动画。

Args:
    images(List[str]): 要在动画中显示的图片文件路径列表，如果输入的分镜没有图片，则传入空列表，不要自行编造不存在的图片地址，也不要传入占位符
Returns:
    list: 包含动画对象的列表。每个元素是一个字典，包含"obj_type"和"obj"两个键。使用时需要通过["obj"]访问实际的动画对象。
</description>
</function>

<function>
<name>
render_keywords
</name>
<description>
生成关键词垂直排列的动画效果。

为每个关键词创建带有背景的文本对象，并将它们垂直排列显示。
第一个关键词位于画面中心偏上的位置，后续关键词垂直向下排列。

Args:
    keywords: list[str]: 要显示的关键词列表

Returns:
    list: 包含动画对象的列表。使用时直接作为参数传递给play方法。
</description>
</function>

<function>
<name>
render_sort_animation
</name>
<description>
生成数组排序的动画效果。

创建一个可视化的数组排序动画，支持多种排序算法。
动画过程中会显示元素交换和比较的过程，直观展示排序算法的工作原理。

Args:
    data: list: 要排序的数据列表
    algorithm: str: 排序算法类型，支持 "bubble"(冒泡排序), "quick"(快速排序), "selection"(选择排序), "insertion"(插入排序)
    layout: str: 数组布局方式，支持 "horizontal"(水平), "vertical"(垂直)
    layout_area: str: 布局区域名称，决定动画在屏幕中的位置和大小，默认为"full_screen"

Returns:
    list: 包含动画对象的列表。每个元素是一个字典，包含"obj_type"和"obj"两个键。使用时需要通过["obj"]访问实际的动画对象。
</description>
</function>

<function>
<name>
render_code_snippet
</name>
<description>
生成代码片段的动画效果。

创建一个带有语法高亮和可选行高亮的代码片段。
代码会以打字机效果呈现，突出显示代码的结构和关键部分。

Args:
    code: str: 代码内容字符串
    language: str: 编程语言，默认为"python"
    highlight_lines: list: 要高亮显示的行号列表
    title: str: 代码块的标题，通常是文件名
    layout_area: str: 布局区域名称，决定代码块在屏幕中的位置和大小，默认为"full_screen"

Returns:
    list: 包含动画对象的列表。每个元素是一个字典，包含"obj_type"和"obj"两个键。使用时需要通过["obj"]访问实际的动画对象。
</description>
</function>

<function>
<name>
render_text_element
</name>
<description>
生成具有不同角色和展示效果的文本元素。

创建一个基于角色的文本元素，每种角色都有预设的默认样式和动画效果。
可以呈现不同类型的文本，如标题、正文、注释等，并智能应用合适的效果。

Args:
    content: str: 文本内容
    role: str: 文本角色 - "title"(标题), "heading"(小标题), "subheading"(子标题),
         "body"(正文), "caption"(说明文字), "quote"(引用), "note"(注释)
    position: str: 位置指示 - "top"(顶部), "center"(中央), "bottom"(底部),
             "left"(左侧), "right"(右侧), 也可以组合使用如"top_left"
    layout_area: str: 布局区域名称，决定文本在屏幕中的位置和大小，默认为"full_screen"
    **kwargs: 可选参数，用于覆盖默认设置

Returns:
    list: 包含动画对象的列表。每个元素是一个字典，包含"obj_type"和"obj"两个键。使用时需要通过["obj"]访问实际的动画对象。
</description>
</function>

<function>
<name>
display_slogo
</name>
<description>
显示标题和logo。

Args:
    cover_question(str): 标题文本
    step(int): 步骤，0表示带动画显示，1表示直接显示
</description>
</function>

<layouts>
LAYOUTS = {{
    # 上半部分
    "upper_half": 屏幕上半部分区域
    # 右上1/4
    "right_upper_quarter": 屏幕右上角四分之一区域
    # 右半部分
    "right_half": 屏幕右半部分区域
    # 全屏
    "full_screen": 整个屏幕区域
    # 左半部分
    "left_half": 屏幕左半部分区域
    # 中间区域
    "origin_x14y6": 屏幕中央较大区域，高度为屏幕高度的3/4
    # 上1/8
    "upper_one_eighth": 屏幕顶部八分之一区域，适合放置标题
    # 下1/8
    "lower_one_eighth": 屏幕底部八分之一区域，适合放置字幕
}}
</layouts>
</function_reference>

<example>
from manim import *

from manim_funcs.anim_funcs import *
from utils.edgetts_service import EdgeTTSService

config.pixel_height = 1920
config.pixel_width = 1090
config.frame_height = 16.0
config.frame_width = 9.0


class GeneratedAnimation(FeynmanScene):
    def construct(self):
        self.set_speech_service(
            EdgeTTSService(global_speed=1.2, voice="zh-CN-YunxiNeural"),
            create_subcaption=True,
        )

        with self.voiceover("<content>") as tracker:
            # tracker.duration
            self.display_slogo("<title>", step=<step>)
            pre_mobjects = self.mobjects

            # 获取关键词动画
            keyword_animations = self.render_keywords(["局限性", "特定任务", "动作集稀疏", "GAIA任务成本", "OpenAI模型"])
            
            # 获取媒体展示动画
            media_animations = self.render_media_display(
                [
                    "pdf_output/2411.01747v1_artifacts/page_3_image_0.png",
                    "pdf_output/2411.01747v1_artifacts/page_7_image_6.png",
                ]
            )

            # 确保动画列表非空
            keyword_anims = keyword_animations if keyword_animations else []
            media_anims = media_animations if media_animations else []

            # 获取动画数量
            keyword_len = len(keyword_anims)
            media_len = len(media_anims)
            max_len = max(keyword_len, media_len)

            # 逐一播放动画
            for i in range(max_len):
                # 获取当前索引的动画（如果存在）
                kw_anim = keyword_anims[i] if i < keyword_len else None
                media_item = media_anims[i] if i < media_len else None

                # 根据动画类型组合播放
                if kw_anim and media_item:
                    if media_item["obj_type"] == "Video":
                        # 对于视频，需要先添加再播放其他动画
                        self.add(media_item["obj"])
                        self.play(kw_anim)
                    else:
                        # 同时播放图片和关键词
                        self.play(media_item["obj"], kw_anim)
                elif kw_anim:
                    # 只播放关键词动画
                    self.play(kw_anim)
                elif media_item:
                    # 只播放媒体动画
                    if media_item["obj_type"] == "Video":
                        self.add(media_item["obj"])
                    else:
                        self.play(media_item["obj"])

            # 使用不同布局区域的文本元素动画示例
            heading_text = self.render_text_element(
                "这是一个标题文本",
                role="heading",
                position="center",
                layout_area="upper_one_eighth"
            )

            if heading_text:
                for text_anim in heading_text:
                    self.play(text_anim["obj"])

            # 在左半部分显示代码片段
            code_animations = self.render_code_snippet(
                "def hello_world():\n    print('Hello, World!')\n    return True\n\nresult = hello_world()",
                language="python",
                layout_area="left_half"
            )

            if code_animations:
                for code_anim in code_animations:
                    self.play(code_anim["obj"])

            # 在右半部分显示排序算法
            sort_data = [5, 3, 8, 1, 9, 4, 7, 2, 6]
            sort_animations = self.render_sort_animation(
                sort_data,
                algorithm="bubble",
                layout="horizontal",
                layout_area="right_half"
            )

            if sort_animations:
                for anim in sort_animations:
                    self.play(anim["obj"])

            # 在下半部分显示文本
            body_text = self.render_text_element(
                "这是一段较长的正文内容，将会自动换行以适应指定的布局区域。文字过长时会进行自动换行处理，确保内容不会超出指定区域的边界。",
                role="body",
                position="center",
                layout_area="lower_one_eighth"
            )

            if body_text:
                for text_anim in body_text:
                    self.play(text_anim["obj"])

            # 展示不同算法的排序效果 - 放在中央区域
            new_data = [12, 5, 18, 2, 9, 15, 7, 3]
            quick_sort_animations = self.render_sort_animation(
                new_data,
                algorithm="quick",
                layout="horizontal",
                layout_area="origin_x14y6"
            )

            if quick_sort_animations:
                for anim in quick_sort_animations:
                    self.play(anim["obj"])

        # 清理场景
        self.scene_clear_display(pre_mobjects)
</example>

<step>
{step}
</step>

<title>
{title}
</title>

<content>
{content}
</content>

<storyboard>
{storyboard}
</storyboard>

<media_assets>
{media_assets}
</media_assets>

注意：
1. 生成的代码中必须包括config相关的设置，以及display_slogo的调用
2. 根据视频描述，判断应该调用哪些工具函数，以及需要传入的参数
3. 从FeynmanScene继承，并重写construct方法
4. 合理安排动画顺序，确保视觉效果符合描述
5. 如果要使用多媒体素材，只能使用media_assets中的素材，不能无中生有
6. 只能使用以上有说明的函数生成的manim代码，不能使用其他函数，增加其他动效代码
"""


class ManimCodeToolkit(BaseToolkit):
    """
    Toolkit for generating manim code from storyboard descriptions.
    """

    def generate_manim_code(
        self, title: str, content: str, storyboard: str, media_assets: Optional[list[str]] = None, step: int = 1
    ) -> str:
        """根据给定的分镜描述提示生成manim代码

        Args:
            title (str): 视频的标题
            content (str): 视频的介绍文案
            storyboard (str): 要生成的内容的描述
            media_assets (Optional[List[str]]): 可用的媒体资源列表
            step (int): 当前分镜的序号，用于命名输出视频文件

        Returns:
            str: 生成的代码文件路径
        """
        # 获取项目根目录
        current_dir = Path(__file__).resolve().parent
        root_dir = current_dir.parent

        # 加载配置
        config_path = os.path.join(root_dir, "config/config.yaml")
        with open(config_path) as file:
            config = yaml.safe_load(file)

        # 从配置中获取API设置
        model_config = config.get("model", {})
        api_config = model_config.get("api", {})
        
        model: BaseModelBackend = ModelFactory.create(
            model_platform=ModelPlatformType.OPENROUTER,
            model_type="google/gemini-2.0-flash-lite-001",
            api_key=api_config.get("openrouter_api_key"),
            url=api_config.get("openrouter_api_base_url"),
        )
        agent = ChatAgent(model=model)

        # Prepare media assets string
        media_assets_str = json.dumps(media_assets if media_assets else [], ensure_ascii=False, indent=2)

        # Format the prompt
        prompt = PROMPT_TEMPLATE.format(
            step=step, title=title, content=content, storyboard=storyboard, media_assets=media_assets_str
        )

        # Get response from the agent
        response = agent.step(prompt)
        response_content = response.msgs[0].content

        # Extract Python code from response
        code = re.findall(r"```(?:python|py)(.*)```", response_content, re.DOTALL)
        if len(code) == 0:
            logger.error(f"No code found in the response: {response_content}")
            return None

        # Write code to file
        output_file = f"render_storyboard_{step}.py"
        with open(output_file, "w") as f:
            f.write(code[0])

        return output_file

    def generate_from_storyboard_json(
        self, storyboard_json_path: str, start_step: int = 1, end_step: Optional[int] = None
    ) -> list[str]:
        """从JSON格式的分镜描述生成多个manim代码文件

        Args:
            storyboard_json_path (str): 分镜JSON文件路径
            start_step (int): 起始分镜索引
            end_step (Optional[int]): 结束分镜索引

        Returns:
            List[str]: 生成的代码文件路径列表
        """
        try:
            with open(storyboard_json_path, encoding="utf-8") as f:
                storyboard_data = json.load(f)

            # Handle both list and dictionary format for storyboard
            if isinstance(storyboard_data, dict):
                storyboard = storyboard_data.get("storyboard", [])
            else:
                storyboard = storyboard_data  # Assume it's already a list

            if not storyboard:
                logger.error("Storyboard is empty or invalid")
                return []

            if end_step is None:
                end_step = len(storyboard)

            output_files = []

            for step in range(start_step, min(end_step + 1, len(storyboard) + 1)):
                frame = storyboard[step - 1]

                # Extract title
                title = frame.get("分镜名", "")

                # Get content from either "分镜内容" or "讲解文案"
                content = frame.get("分镜内容", frame.get("讲解文案", ""))

                # Create a summary of the storyboard frame
                frame_description = json.dumps(frame, ensure_ascii=False, indent=2)

                # Extract media assets
                media_assets = []
                primary_asset = frame.get("素材名", "")
                if primary_asset:
                    media_assets.append(primary_asset)

                # Generate the manim code
                output_file = self.generate_manim_code(
                    title=title, content=content, storyboard=frame_description, media_assets=media_assets, step=step
                )

                if output_file:
                    output_files.append(output_file)
                    logger.info(f"Generated manim code for step {step}: {output_file}")
                else:
                    logger.warning(f"Failed to generate manim code for step {step}")

            return output_files

        except Exception as e:
            logger.error(f"Error generating code from storyboard: {str(e)}")
            return []

    def get_tools(self) -> list[FunctionTool]:
        """Return toolkit functions"""
        return [
            FunctionTool(self.generate_manim_code),
            FunctionTool(self.generate_from_storyboard_json),
        ]


if __name__ == "__main__":
    toolkit = ManimCodeToolkit()
    # Example of generating from a storyboard json file
    # If storyboard JSON file exists, generate code for it
    storyboard_path = "output/paper_content.json"
    if os.path.exists(storyboard_path):
        logger.info("Generating code from storyboard JSON...")
        output_files = toolkit.generate_from_storyboard_json(storyboard_json_path=storyboard_path, start_step=1)

        if output_files:
            logger.info(f"Generated {len(output_files)} manim code files")
            for file in output_files:
                logger.info(f"- {file}")
