"""
Smolagents-based Universal Coding Agent

This module provides a smolagents-based implementation of a universal coding agent
that can handle various programming tasks across different languages and frameworks.
It incorporates best practices from leading AI coding assistants.
"""

import os
import subprocess
import sys
from pathlib import Path
from typing import Optional

from loguru import logger

# from openinference.instrumentation.smolagents import SmolagentsInstrumentor
# from phoenix.otel import register
# register()
# SmolagentsInstrumentor().instrument()
from smolagents import ActionStep, ChatMessage, CodeAgent, InferenceClientModel, OpenAIServerModel, Tool

# Add project root to path
cwd = os.getcwd()
sys.path.insert(0, cwd)

# Import smolagents-compatible tools
from tools.code_agent_tools.smolagents_adapters import (
    bash_execute,
    check_code_issues,
    file_create,
    file_view,
    get_library_docs,
    list_files,
    replace_in_file,
    resolve_library_id,
    search_files,
    sequential_thinking,
)
from utils.common import Config


class SmolagentsUniversalCodingAgent:
    """
    Smolagents-based universal coding agent with autonomous debugging capabilities.

    This agent can handle various programming tasks across different languages and frameworks,
    incorporating best practices from leading AI coding assistants like Augment, Cursor, and Cline.
    """

    def __init__(self, working_dir: Optional[str] = None):
        """
        Initialize the smolagents-based toolkit.

        Args:
            working_dir: Working directory for file operations (default: current directory)
        """
        self.working_dir = Path(working_dir or os.getcwd()).resolve()

        config = Config().config.get("workflow", {}).get("code_agent", {})
        self.enable_sequential_thinking = config.get("enable_sequential_thinking", False)
        self.enable_get_docs = config.get("enable_get_docs", False)
        self.enable_memory = config.get("enable_memory", True)  # Enable memory by default
        self.memory_file_path = config.get("memory_file_path", "universal_coding_agent_memory.md")
        self.model_type = config.get("model", "google/gemini-2.5-flash-preview-05-20")
        self.memory_model_type = config.get("memory_model", "google/gemini-2.5-flash-lite-preview-06-17")
        self.summary_model_type = config.get("summary_model", "google/gemini-2.5-flash-lite-preview-06-17")
        max_iteration_per_step = config.get("max_iteration_per_step", 20)

        # Initialize memory system
        if self.enable_memory:
            self._ensure_memory_directory()

        # Create smolagents-compatible model
        self.model = self._create_smolagents_model(self.model_type)

        # Create agent with tools
        self.agent = CodeAgent(
            tools=self._get_all_tools(),
            model=self.model,
            instructions=self._create_system_instructions(),
            max_steps=max_iteration_per_step,
            max_print_outputs_length=1000,
        )
        self.memory_agent = self._create_memory_agent()

        logger.info(
            f"Universal coding agent initialized with working directory: {self.working_dir},"
            f" max_steps: {max_iteration_per_step},"
            f" sequential_thinking: {self.enable_sequential_thinking}, get_docs: {self.enable_get_docs},"
            f" memory: {self.enable_memory}"
        )

    def _create_smolagents_model(self, model: str = None):
        """Create a smolagents-compatible model from the existing model configuration."""
        try:
            # Get model configuration from config file
            config = Config().config
            model_config = config.get("model", {})
            api_config = model_config.get("api", {})

            # Extract OpenRouter configuration
            model_type = model or model_config.get("type", "google/gemini-2.5-flash-preview-05-20")
            api_key = api_config.get("openrouter_api_key")
            api_base = api_config.get("openrouter_api_base_url", "https://openrouter.ai/api/v1")
            # temperature = model_config.get("temperature", 0.7)
            max_tokens = model_config.get("max_tokens", 32768)

            if not api_key:
                raise RuntimeError("No OpenRouter API key found in config, using default InferenceClientModel")

            # Create OpenAI-compatible model for OpenRouter
            model = OpenAIServerModel(
                model_id=model_type,
                api_base=api_base,
                api_key=api_key,
                temperature=0.0,
                max_tokens=max_tokens,
            )

            logger.info(f"Created smolagents OpenAIServerModel with model: {model_type}")
            return model

        except Exception as e:
            logger.warning(f"Failed to create smolagents model from config: {e}")
            # Fallback to default model
            return InferenceClientModel()

    def _get_all_tools(self) -> list:
        """
        Get all tools for smolagents framework integration.

        Returns:
            List of smolagents-compatible tools
        """
        tools: list[Tool] = []

        # Add sequential thinking tool conditionally
        if self.enable_sequential_thinking:
            tools.append(sequential_thinking)

        # Add core tools
        tools.extend(
            [
                file_view,
                file_create,
                list_files,
                replace_in_file,
                search_files,
                bash_execute,
                check_code_issues,
            ]
        )

        # Add documentation tools conditionally
        if self.enable_get_docs:
            tools.extend(
                [
                    resolve_library_id,
                    get_library_docs,
                ]
            )

        logger.info(f"Total tools registered for smolagents: {len(tools)}")
        for tool in tools:
            logger.info(tool.name)
        return tools

    def _create_system_instructions(self) -> str:
        """
        Create system instructions for the smolagents agent.
        This is equivalent to the system_message in camel but adapted for smolagents.
        """
        return """# Universal Programming Expert

## Role
Professional programming expert with autonomous debugging capabilities across multiple languages, frameworks, and technology stacks. Generate high-quality, maintainable, and scalable code following industry best practices.

## Core Development Workflow

### 1. Context & Planning
- **Assess task complexity**: For substantial tasks (>100 lines or unfamiliar frameworks), invest time in systematic planning
- **Break down complex problems**: Use available thinking tools to decompose into manageable components
- **Gather comprehensive context**: Research APIs, syntax, and patterns before implementation
- **Verify assumptions**: Check documentation and validate approach, especially for unfamiliar territory

### 2. Incremental Development
- **Start minimal**: Begin with working skeleton/framework rather than attempting complete solutions
- **Build incrementally**: Add features one at a time with validation between steps
- **Test each milestone**: Verify functionality before adding complexity
- **Adapt based on feedback**: Use validation results to guide next steps and course corrections

### 3. Quality Assurance
- **Systematic verification**: Run syntax checks, compilation, and framework-specific validation
- **Complete testing**: Execute verification steps mentioned in task requirements
- **Professional standards**: Ensure code runs successfully and handles edge cases
- **Validation-driven completion**: Task completion requires successful verification when specified

## Decision Guidelines

### Consider Advanced Planning When:
- Task involves multiple distinct steps or components
- Working with unfamiliar frameworks or APIs
- Complex logic or algorithm implementation required
- Multiple interconnected files or modules need coordination

### Research Documentation When:
- Using framework-specific features or APIs
- Uncertain about syntax or parameter requirements
- Need to verify best practices or patterns
- Working with version-specific implementations

### Execute Validation When:
- After incremental development milestones
- Before claiming task completion
- When verification commands are explicitly specified
- After significant code modifications

## Quality Standards

### Avoid These Approaches:
- Generating large codebases (>100 lines) without incremental testing
- Skipping verification when explicitly requested in requirements
- Proceeding without sufficient understanding of requirements
- Claiming completion without running specified validation steps

### Prioritize These Practices:
- Plan systematically for complex tasks before coding
- Research unfamiliar APIs and frameworks thoroughly
- Build and test incrementally with validation checkpoints
- Execute all verification steps mentioned in requirements
- Validate functionality at logical milestones

## Success Criteria
A task is considered complete when:
1. **Implementation works**: Code compiles and runs without errors
2. **Requirements fulfilled**: All specified functionality is implemented
3. **Validation passes**: Verification commands execute successfully when specified
4. **Quality maintained**: Code follows established patterns and handles edge cases
5. **Planning adequate**: Complex tasks show evidence of systematic approach

## Error Recovery Approach
- Analyze failures systematically to understand root causes
- Fix issues incrementally rather than wholesale rewrites
- Re-validate after each fix to ensure progress
- Learn from patterns to prevent similar issues
- Prioritize working solutions over perfect code

## Tool Usage Philosophy
- **Choose appropriate tools** based on task characteristics and complexity
- **Maximize efficiency** through parallel operations when gathering information
- **Use targeted edits** rather than full file rewrites when possible
- **Leverage documentation** to verify framework-specific implementations
- **Validate systematically** especially when verification steps are specified

Remember: **Effective solutions over rigid processes**. Adapt your approach based on task complexity and requirements."""

    def _to_absolute_path(self, path: str) -> str:
        """Convert relative path to absolute path"""
        path_obj = Path(path)
        if path_obj.is_absolute():
            return str(path_obj)
        return str(self.working_dir / path_obj)

    def _get_memory_file_path(self) -> Path:
        """Get the path to the memory file."""
        return self.working_dir / "memory" / self.memory_file_path

    def _ensure_memory_directory(self):
        """Ensure the memory directory exists."""
        memory_file = self._get_memory_file_path()
        memory_file.parent.mkdir(exist_ok=True)

    def _read_memory(self) -> str:
        """
        Read memory file content.

        Returns:
            Memory content as string, empty string if file doesn't exist or on error
        """
        if not self.enable_memory:
            return ""

        try:
            memory_file = self._get_memory_file_path()
            if memory_file.exists():
                return memory_file.read_text(encoding="utf-8")
            else:
                return ""
        except Exception as e:
            logger.warning(f"Failed to read memory file: {e}")
            return ""

    def _create_memory_agent(self):
        """
        Create a specialized agent for memory updates with file operation tools.

        Returns:
            CodeAgent configured for memory management
        """
        # Import file operation tools
        from tools.code_agent_tools.smolagents_adapters import file_create, file_view, replace_in_file

        # Create memory-specific model (use smaller model for cost efficiency)
        memory_model = self._create_smolagents_model(self.memory_model_type)

        # Create memory agent with file operation tools
        memory_agent = CodeAgent(
            tools=[file_view, file_create, replace_in_file],
            model=memory_model,
            instructions="""# Programming Experience Memory Management Expert

You are a specialized AI assistant responsible for managing programming development experience memory. Your task is to analyze conversation history from programming development processes, extract valuable experiences and lessons, and update memory files.

## 🎯 Core Responsibilities
1. **Analyze Conversation History** - Identify programming error patterns, successful strategies, technical solutions, and best practices
2. **Manage Memory Files** - Use file operation tools to read, create, and update memory files
3. **Structured Storage** - Categorize and store experiences by technology stack, problem types, and solutions in markdown format

## 📋 Recommended Memory File Structure
- **Language-Specific Experiences** - Special considerations for Python, JavaScript, Java, etc.
- **Framework and Library Experiences** - Best practices for React, Django, Spring, etc.
- **Common Error Patterns** - Solutions for syntax errors, logic errors, performance issues, etc.
- **Tool Usage Techniques** - Development tools, debugging techniques, testing strategies
- **Architecture Design Principles** - Code organization, module division, interface design
- **Problem-Solution Mappings** - Specific problems and their proven solutions
- **Performance Optimization** - Code optimization techniques and patterns

## 🔧 Workflow
1. Use `file_view` to read current memory file content
2. Analyze provided conversation history to extract valuable programming experiences
3. Use `replace_in_file` to update relevant sections, or use `file_create` to create new file if target file does not exist
4. Ensure only new, valuable information is added, avoid duplication
5. Maintain content in English for consistency

## ✅ Update Principles
- **Value-Focused**: Only record truly valuable programming experiences and lessons, focus on error patterns and successful strategies
- **Solution-Oriented**: Record effective solutions and best practices, avoid introducing misleading information
- **Accuracy and Utility**: Maintain information accuracy and practicality for future programming task reference
- **Categorized Organization**: Organize by technology stack and problem types for quick search and use
- **Concise Description**: Use clear and concise language, ideally one line per key point
- **Incremental Value**: Avoid duplicating existing information, focus on incremental value
- **Actionable Insights**: Ensure recorded experiences can be directly applied to future tasks

When starting work, I will provide conversation history and memory file path. Please autonomously decide how to update the memory.""",
            max_steps=10,
        )

        return memory_agent

    def _update_memory(self, conversation_history: str):
        """
        Update memory file using a specialized memory agent.

        Args:
            conversation_history: Complete conversation history from current session
        """
        if not self.enable_memory:
            return

        try:
            memory_file_path = str(self._get_memory_file_path())

            # Create task prompt for memory agent
            memory_task = f"""Please analyze the following programming development conversation history and update the memory file.

Conversation history:
```
{conversation_history}
```

Memory file path: `{memory_file_path}`

**CRITICAL CONSTRAINTS**:
- You may ONLY modify the memory file specified above
- **NEVER** modify any other files mentioned in the conversation history
- Focus on extracting actionable programming insights and lessons learned
- Organize information for maximum utility in future programming tasks
"""

            # Create and run memory agent
            self.memory_agent.memory.reset()
            self.memory_agent.run(memory_task)

            logger.info(f"Memory agent completed update for: {memory_file_path}")

        except Exception as e:
            logger.warning(f"Failed to update memory using agent: {e}")

    def _get_conversation_history(self, skip_initial_prompts: bool = True, include_full_details: bool = True) -> str:
        """
        Extract complete conversation history from agent memory.

        Args:
            skip_initial_prompts: Whether to skip system prompt and initial task
            include_full_details: Whether to include complete tool calling details

        Returns:
            Formatted conversation history string
        """
        if not hasattr(self.agent, "memory") or not self.agent.memory.steps:
            return "No previous conversation history."

        history_parts = []

        # Add system prompt (unless skipping)
        if not skip_initial_prompts and hasattr(self.agent.memory, "system_prompt"):
            history_parts.append(f"System Prompt: {self.agent.memory.system_prompt.system_prompt}")

        # Process conversation steps
        for i, step in enumerate(self.agent.memory.steps):
            if hasattr(step, "task"):
                # Task step - skip if requested
                if not skip_initial_prompts:
                    history_parts.append(f"Task {i+1}: {step.task}")
            elif isinstance(step, ActionStep):
                # Action step - get complete details
                step_info = f"Step {step.step_number}:"

                # Add model input messages (what was sent to LLM)
                if include_full_details and hasattr(step, "model_input_messages") and step.model_input_messages:
                    step_info += f"\n  Model Input Messages: {len(step.model_input_messages)} messages"
                    # Optionally include the last few messages for context
                    for msg in step.model_input_messages[-2:]:  # Last 2 messages
                        if isinstance(msg, dict):
                            role = msg.get("role", "unknown")
                            content = (
                                str(msg.get("content", ""))[:200] + "..."
                                if len(str(msg.get("content", ""))) > 200
                                else str(msg.get("content", ""))
                            )
                            step_info += f"\n    {role}: {content}"

                # Add model output (LLM's raw response with thoughts and actions)
                if hasattr(step, "model_output_message") and step.model_output_message:
                    output_content = str(step.model_output_message.content)
                    if include_full_details:
                        step_info += f"\n  Model Output: {output_content}"
                    else:
                        step_info += (
                            f"\n  Model Output: {output_content[:500]}..."
                            if len(output_content) > 500
                            else f"\n  Model Output: {output_content}"
                        )

                # Add tool calls (complete information)
                if hasattr(step, "tool_calls") and step.tool_calls:
                    step_info += f"\n  Tool Calls: {len(step.tool_calls)} calls"
                    for j, tool_call in enumerate(step.tool_calls):
                        if hasattr(tool_call, "name"):
                            tool_name = tool_call.name
                            tool_args = getattr(tool_call, "arguments", {})
                            if include_full_details:
                                step_info += f"\n    Call {j+1}: {tool_name}({tool_args})"
                            else:
                                # Truncate long arguments
                                args_str = str(tool_args)
                                if len(args_str) > 200:
                                    args_str = args_str[:200] + "..."
                                step_info += f"\n    Call {j+1}: {tool_name}({args_str})"

                # Add observations (tool results)
                if step.observations:
                    obs_str = str(step.observations)
                    if include_full_details:
                        step_info += f"\n  Observations: {obs_str}"
                    else:
                        step_info += (
                            f"\n  Observations: {obs_str[:300]}..."
                            if len(obs_str) > 300
                            else f"\n  Observations: {obs_str}"
                        )

                # Add errors
                if step.error:
                    step_info += f"\n  Error: {step.error}"

                # Add timing information if available
                if hasattr(step, "duration") and step.duration:
                    step_info += f"\n  Duration: {step.duration:.2f}s"

                history_parts.append(step_info)

        return "\n\n".join(history_parts)

    def get_complete_conversation_history(self) -> str:
        """
        Get complete conversation history including all tool calls and model outputs.
        This is the method you should use to get full records.

        Returns:
            Complete formatted conversation history
        """
        return self._get_conversation_history(skip_initial_prompts=False, include_full_details=True)

    def get_action_steps_only(self) -> str:
        """
        Get only the action steps (tool calls and results), skipping system prompts and tasks.
        Useful for analyzing the actual execution flow.

        Returns:
            Action steps only conversation history
        """
        return self._get_conversation_history(skip_initial_prompts=True, include_full_details=True)

    def get_compact_history(self) -> str:
        """
        Get a compact version of conversation history with truncated details.
        Useful for summaries or when dealing with long conversations.

        Returns:
            Compact conversation history
        """
        return self._get_conversation_history(skip_initial_prompts=True, include_full_details=False)

    def _summarize_previous_iteration(self, iteration_num: int, current_iteration_error: str = None) -> str:
        """
        Summarize the previous iteration using the model.

        Args:
            iteration_num: Current iteration number

        Returns:
            Summary of previous iteration
        """
        if iteration_num <= 1:
            return ""

        # Get complete conversation history, skipping initial prompts for cleaner summary
        history = self._get_conversation_history(skip_initial_prompts=True, include_full_details=False)

        # Create a summary prompt
        summary_prompt = f"""
Analyze the following programming development history and provide a concise summary focusing on:

## 📋 Key Analysis Areas:
1. **Technical Approaches Attempted** - What solutions and implementation steps were tried
2. **Errors and Issues Encountered** - Specific problems faced and their root causes
3. **Solutions and Fixes Applied** - What worked and what didn't work
4. **Successful Implementations** - Features and modules that were completed successfully
5. **Technology and Architecture Decisions** - Key technical choices and their outcomes
6. **Lessons Learned** - Critical insights for future iterations

## 📚 Development History:
```
{history}
```

## 🌐 Current Status:
{current_iteration_error or "No current errors."}

## 🎯 Required Output:
Provide a structured summary that will help the next iteration:
- **Avoid repeating failed approaches**
- **Build upon successful implementations**
- **Address specific issues identified**
- **Improve development efficiency and code quality**

Focus on actionable insights that directly inform the next development iteration.
"""

        # logger.debug(f"Summary prompt for iteration {iteration_num}:\n{summary_prompt}")
        try:
            summarize_model = self._create_smolagents_model(self.summary_model_type)
            summary = summarize_model.generate([ChatMessage(role="user", content=summary_prompt)]).content
            with open(self.working_dir / f"history_{iteration_num}.txt", "w", encoding="utf-8") as f:
                f.write(history)
            with open(self.working_dir / f"summary_{iteration_num}.txt", "w", encoding="utf-8") as f:
                f.write(summary)
            return summary
        except Exception as e:
            logger.warning(f"Failed to generate summary for iteration {iteration_num}: {e}")
            return f"Previous iteration encountered issues. Key points from history: {history[:500]}..."

    def _create_task_prompt(
        self, task_description: str, output_path: str, iteration_num: int = 1, previous_summary: str = ""
    ) -> str:
        """
        Create task-specific prompt for programming tasks.

        Args:
            task_description: Description of the programming task to complete
            output_path: Output file path (if applicable)
            iteration_num: Current iteration number
            previous_summary: Summary of previous iterations
        """
        # Read memory content
        memory_content = self._read_memory()

        # Start with core task description
        base_prompt = f"""# Task: {task_description}

根据视觉描述，生成对应的manim代码，并确保最终代码的正确性和完整性，能通过 `manim --dry_run --progress_bar none {output_path}` 的检查。

{f'**Output file**: `{output_path}`' if output_path else ''}"""

        # Add memory content if available
        if memory_content.strip():
            base_prompt += f"""

## 📚 Relevant Experience
```
{memory_content}
```"""

        # Add previous iteration summary if available
        if iteration_num > 1 and previous_summary:
            base_prompt += f"""

## 📝 Previous Iteration (#{iteration_num-1})
```
{previous_summary}
```

**Key Points**: Learn from above, avoid repeating failures, build on successes."""

        return base_prompt

    def execute_programming_task(
        self, task_description: str, output_file: str = None, max_iterations: int = 3
    ) -> Optional[str]:
        """
        Execute programming task using smolagents framework with iteration history.

        Args:
            task_description: Description of the programming task to complete
            output_file: Output file path for the generated code (optional)
            max_iterations: Maximum number of iterations for quality improvement

        Returns:
            Path to generated code file if applicable, or success message, or None on failure
        """
        output_path = self._to_absolute_path(output_file) if output_file else None

        try:
            logger.info("Starting smolagents-based programming task execution...")
            current_iteration_error = ""

            for iteration in range(max_iterations):
                logger.info(f"Iteration {iteration + 1}/{max_iterations}")

                # Generate summary of previous iteration if not the first iteration
                previous_summary = ""
                if iteration > 0:
                    logger.info(f"Generating summary for iteration {iteration + 1}...")
                    previous_summary = self._summarize_previous_iteration(iteration + 1, current_iteration_error)

                # Create task prompt with iteration context
                task_prompt = self._create_task_prompt(task_description, output_path, iteration + 1, previous_summary)

                self.agent.memory.reset()
                # Run the agent with the task prompt
                _ = self.agent.run(task_prompt)

                logger.info(f"Agent completed iteration {iteration + 1}")

                # Update memory with conversation history from this iteration
                if self.enable_memory:
                    try:
                        current_history = self.get_compact_history()
                        with open(self.working_dir / f"memory_{iteration + 1}.txt", "w", encoding="utf-8") as f:
                            f.write(current_history)
                        full_history = self.get_complete_conversation_history()
                        with open(self.working_dir / f"full_history_{iteration + 1}.txt", "w", encoding="utf-8") as f:
                            f.write(full_history)
                        self._update_memory(current_history)
                        logger.debug(f"Updated memory after iteration {iteration + 1}")
                    except Exception as e:
                        logger.warning(f"Failed to update memory after iteration {iteration + 1}: {e}")

                # Check if task was completed successfully
                task_completed = True
                if output_path and Path(output_path).exists():
                    # Check for code issues if there's an output file
                    issues_result = check_code_issues([output_path], "error")
                    logger.info(f"Iteration {iteration + 1}, Code issues: {issues_result}")

                    if "No issues found" in issues_result or "❌" not in issues_result:
                        manim_dryrun_result = bash_execute(f"manim --dry_run --progress_bar none {output_path}")
                        if manim_dryrun_result.startswith(
                            "Command executed successfully"
                        ) or manim_dryrun_result.startswith("Command execution successfully"):
                            logger.success(f"Programming task completed after {iteration + 1} iterations")
                            return output_path
                        else:
                            logger.error(f"Manim dry run failed: {manim_dryrun_result}")
                            task_completed = False
                            current_iteration_error = manim_dryrun_result
                    else:
                        task_completed = False
                        current_iteration_error = issues_result
                else:
                    # For tasks without specific output files, assume completion based on agent execution
                    logger.success(f"Programming task completed after {iteration + 1} iterations")
                    return "Task completed successfully"

                # If not successful, continue to next iteration
                if not task_completed and iteration < max_iterations - 1:
                    logger.info(f"Iteration {iteration + 1} not successful, continuing...")

            # Return result based on whether there was an output file
            if output_path:
                return output_path if Path(output_path).exists() else None
            else:
                return "Task execution completed"

        except Exception as e:
            logger.error(f"Error in programming task execution: {e}")
            return None

    def generate_manim_code_enhanced(
        self, scene_description: str, output_file: str, max_iterations: int = 3
    ) -> Optional[str]:
        """
        Backward compatibility method for Manim code generation.

        Args:
            scene_description: Description of the scene to generate
            output_file: Output file path for the generated code
            max_iterations: Maximum number of iterations for quality improvement

        Returns:
            Path to generated and debugged code file, or None on failure
        """
        return self.execute_programming_task(scene_description, output_file, max_iterations)

    def render_manim_code(self, code_file: str, quality: str = "l") -> Optional[str]:
        """
        Render Manim code to video.
        This method is identical to the camel implementation.
        """
        try:
            code_path = Path(code_file)
            if not code_path.exists():
                logger.error(f"Code file not found: {code_file}")
                return None

            # Prepare manim command
            cmd = [
                "manim",
                str(code_path),
                "--quality",
                quality,
            ]

            logger.info(f"Rendering Manim code: {' '.join(cmd)}")

            # Run manim command
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
            )

            if result.returncode == 0:
                resolution = {
                    "l": "480p15",
                    "m": "720p30",
                    "h": "1080p60",
                    "q": "1440p60",
                    "k": "2160p60",
                }[quality]
                output_path = Path("media") / "videos" / code_path.stem / resolution
                # Find the generated video file
                video_files = list(output_path.glob("*.mp4"))
                if video_files:
                    # Sort by modification time (newest first) and take the most recent one
                    video_file = max(video_files, key=lambda f: f.stat().st_mtime)
                    logger.success(f"Video rendered successfully: {video_file}")
                    return str(video_file)
                else:
                    logger.error(f"No video file found at {output_path} after rendering")
                    return None
            else:
                error_msg = result.stderr or result.stdout
                logger.error(f"Manim rendering failed: {error_msg}")
                raise subprocess.CalledProcessError(result.returncode, cmd, error_msg)

        except subprocess.TimeoutExpired:
            logger.error("Manim rendering timed out")
            return None
        except Exception as e:
            logger.error(f"Failed to render Manim code: {e}")
            raise


# Alias for backward compatibility
SmolagentsSceneCodeGenerationToolkit = SmolagentsUniversalCodingAgent
