"""
工具函数模块

提供各种通用功能函数
"""

import json
import logging
import os
import re
from typing import Any, Optional, Union

logger = logging.getLogger(__name__)


def create_output_directory(directory: str) -> str:
    """
    创建输出目录

    Args:
        directory: 目录路径

    Returns:
        创建的目录路径
    """
    os.makedirs(directory, exist_ok=True)
    logger.info(f"创建目录: {directory}")
    return directory


def save_to_file(content: str, filepath: str) -> bool:
    """
    保存内容到文件

    Args:
        content: 要保存的内容
        filepath: 文件路径

    Returns:
        是否保存成功
    """
    try:
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(content)
        logger.info(f"内容已保存到: {filepath}")
        return True
    except Exception as e:
        logger.error(f"保存内容到文件失败: {e}")
        return False


def save_json_to_file(data: Union[dict, list], filepath: str) -> bool:
    """
    保存JSON数据到文件

    Args:
        data: 要保存的JSON数据
        filepath: 文件路径

    Returns:
        是否保存成功
    """
    try:
        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info(f"JSON已保存到: {filepath}")
        return True
    except Exception as e:
        logger.error(f"保存JSON到文件失败: {e}")
        return False


def extract_json_from_text(text: str) -> Optional[Any]:
    """
    从文本中提取JSON内容

    Args:
        text: 包含JSON的文本

    Returns:
        提取的JSON对象，如果提取失败则返回None
    """
    # 尝试直接解析整个文本
    try:
        return json.loads(text)
    except json.JSONDecodeError:
        pass

    # 尝试查找JSON数组或对象
    json_pattern = r'(\[\s*\{.*\}\s*\]|\{\s*".*"\s*:.*\})'
    json_matches = re.search(json_pattern, text, re.DOTALL)
    if json_matches:
        try:
            return json.loads(json_matches.group(1))
        except json.JSONDecodeError:
            pass

    # 尝试从代码块中提取JSON
    code_block_pattern = r"```(?:json)?\s*([\s\S]*?)```"
    code_matches = re.findall(code_block_pattern, text)

    for match in code_matches:
        try:
            return json.loads(match)
        except json.JSONDecodeError:
            continue

    logger.warning(f"无法从文本中提取JSON: {text[:100]}...")
    return None


def sanitize_filename(filename: str) -> str:
    """
    清理文件名，移除非法字符

    Args:
        filename: 原始文件名

    Returns:
        清理后的文件名
    """
    # 替换非法字符
    sanitized = re.sub(r'[<>:"/\\|?*]', "_", filename)
    # 移除前导和尾随空格
    sanitized = sanitized.strip()
    # 如果文件名为空，使用默认名称
    if not sanitized:
        sanitized = "unnamed_file"

    return sanitized


def truncate_text(text: str, max_length: int = 100) -> str:
    """
    截断文本到指定长度

    Args:
        text: 原始文本
        max_length: 最大长度

    Returns:
        截断后的文本
    """
    if len(text) <= max_length:
        return text

    return text[: max_length - 3] + "..."


def format_markdown_table(headers: list[str], rows: list[list[str]]) -> str:
    """
    格式化Markdown表格

    Args:
        headers: 表格头部
        rows: 表格行

    Returns:
        Markdown表格字符串
    """
    if not headers or not rows:
        return ""

    # 创建表头
    table = "| " + " | ".join(headers) + " |\n"
    # 添加分隔行
    table += "| " + " | ".join(["---"] * len(headers)) + " |\n"

    # 添加数据行
    for row in rows:
        # 确保行的长度与表头一致
        while len(row) < len(headers):
            row.append("")
        table += "| " + " | ".join(row) + " |\n"

    return table
