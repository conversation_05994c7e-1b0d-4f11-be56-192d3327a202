---
description: 
globs: 
alwaysApply: false
---
# Scene2 BPE Manim Code Generation Rules

## 整体布局系统 (Layout System)

### 网格系统 (Grid System)
- **屏幕分辨率**: 16:9 比例 (1920x1080 或 1280x720)
- **网格划分**: 31列 × 17行 = 527个中心点
- **网格单元尺寸**: 
  - 横向: 屏幕宽度 / 31 = 单元宽度
  - 纵向: 屏幕高度 / 17 = 单元高度

### 优化的网格系统实现 (Optimized Grid Implementation)
```python
# 网格系统参数 - 全局定义，避免序列化问题
GRID_COLS = 31
GRID_ROWS = 17
GRID_WIDTH = 14
GRID_HEIGHT = 8

def grid_position(x, y):
    """将网格坐标转换为Manim坐标 - 全局函数避免引用问题"""
    manim_x = -GRID_WIDTH/2 + (x * GRID_WIDTH) / (GRID_COLS - 1)
    manim_y = GRID_HEIGHT/2 - (y * GRID_HEIGHT) / (GRID_ROWS - 1)
    return np.array([manim_x, manim_y, 0])
```

### 强制性布局约束 (Mandatory Layout Constraints)
1. **所有Mobject的中心点必须对齐到网格中心点**
2. **文本对象的baseline必须对齐到网格线**
3. **VGroup的整体中心必须在网格点上**
4. **动画路径的关键帧位置必须在网格点上**
5. **⚠️ 禁止在类中存储场景引用，避免深拷贝序列化问题**

## 视觉风格系统 (Visual Style System)

### 颜色方案 (Color Scheme)
```python
# 主色调配置
BACKGROUND_COLOR = BLACK  # 全黑背景
PRIMARY_TEXT_COLOR = WHITE
SECONDARY_TEXT_COLOR = "#CCCCCC"
ACCENT_COLOR = "#00D4FF"  # 蓝色强调
WARNING_COLOR = "#FF6B35"  # 橙色警告
SUCCESS_COLOR = "#4CAF50"  # 绿色成功
HIGHLIGHT_COLOR = "#FFD700"  # 金色高亮

# 字符颜色映射
CHAR_COLOR_MAP = {
    'l': "#3498DB",  # 蓝色
    'o': "#F39C12",  # 橙色
    'w': "#E74C3C",  # 红色
    'e': "#2ECC71",  # 绿色
    'n': "#9B59B6",  # 紫色
    's': "#E67E22",  # 深橙色
    't': "#1ABC9C",  # 青色
    'r': "#E91E63",  # 粉色
    'a': "#34495E",  # 深灰蓝
    'b': "#95A5A6",  # 灰色
    'c': "#F1C40F",  # 黄色
    'd': "#8E44AD",  # 深紫色
    'f': "#D35400",  # 深橙色
}
```

### 字体系统 (Typography System)
```python
# 字体配置
FONT_FAMILY = "PingFang SC"  # 等宽字体，确保对齐
TITLE_FONT_SIZE = 48
SUBTITLE_FONT_SIZE = 36
BODY_FONT_SIZE = 24
SMALL_FONT_SIZE = 18
TINY_FONT_SIZE = 14

# 字体权重
FONT_WEIGHT_NORMAL = NORMAL
FONT_WEIGHT_BOLD = BOLD
```

### 尺寸系统 (Size System)
```python
# 标准尺寸
LETTER_BLOCK_SIZE = 0.8  # 字母块基础尺寸
LETTER_BLOCK_PADDING = 0.1  # 字母块内边距
CONNECTION_LINE_WIDTH = 3  # 连接线宽度
HIGHLIGHT_STROKE_WIDTH = 4  # 高亮描边宽度
```

## 代码生成规则 (Code Generation Rules)

### 1. 场景初始化模板 (优化版)
```python
class Scene2BPEConcept(Scene):
    def construct(self):
        # 设置背景色
        self.camera.background_color = BACKGROUND_COLOR
        
        # 初始化网格系统
        self.setup_grid_system()
        
        # 显示调试网格（如果启用）
        if DEBUG_MODE:
            self.show_grid_debug()
        
        # 执行四个阶段
        self.stage1_corpus_initialization()
        self.wait(WAIT_TIME["normal"])
        
        self.stage2_frequency_statistics()
        self.wait(WAIT_TIME["normal"])
        
        self.stage3_optimal_merging()
        self.wait(WAIT_TIME["normal"])
        
        self.stage4_vocabulary_construction()
        self.wait(WAIT_TIME["long"])
    
    def setup_grid_system(self):
        """设置网格系统 - 简化版，避免存储不必要的引用"""
        # 存储网格对象
        self.grid_objects = {}
```

### 2. 布局分区规则 (Layout Zones)
```python
# 屏幕分区定义
ZONES = {
    "title_zone": {"x_range": (8, 22), "y_range": (1, 3)},      # 标题区域
    "text_example_zone": {"x_range": (2, 14), "y_range": (4, 12)},  # 文本示例区域
    "frequency_panel_zone": {"x_range": (16, 28), "y_range": (4, 12)}, # 频率面板区域
    "merge_animation_zone": {"x_range": (6, 24), "y_range": (6, 10)},  # 合并动画区域
    "file_display_zone": {"x_range": (18, 30), "y_range": (13, 16)},   # 文件显示区域
}
```

### 3. 对象创建规则 (优化版)

#### LetterBlock 创建规则 - 避免场景引用
```python
class LetterBlock(VGroup):
    """网格对齐的字母块 - 优化版"""
    def __init__(self, letter, grid_x, grid_y, **kwargs):
        super().__init__(**kwargs)
        
        self.letter = letter
        self.grid_position_coords = (grid_x, grid_y)  # 存储坐标而非引用
        
        # 创建背景方块
        self.background = RoundedRectangle(
            width=LETTER_BLOCK_SIZE,
            height=LETTER_BLOCK_SIZE,
            corner_radius=0.1,
            fill_color=CHAR_COLOR_MAP.get(letter, PRIMARY_TEXT_COLOR),
            fill_opacity=0.8,
            stroke_color=PRIMARY_TEXT_COLOR,
            stroke_width=2
        )
        
        # 创建字母文本
        self.text = Text(
            letter,
            font_size=BODY_FONT_SIZE,
            font=FONT_FAMILY,
            color=BLACK if letter in ['o', 'f', 'c'] else PRIMARY_TEXT_COLOR,
            weight=BOLD
        )
        
        # 组合并定位到网格
        self.add(self.background, self.text)
        self.move_to(grid_position(grid_x, grid_y))  # 使用全局函数
```

#### 频率计数器创建规则 - 避免场景引用
```python
class FrequencyCounter(VGroup):
    """网格对齐的频率计数器 - 优化版"""
    def __init__(self, pair_text, grid_x, grid_y, **kwargs):
        super().__init__(**kwargs)
        
        self.pair_text = pair_text
        self.grid_position_coords = (grid_x, grid_y)  # 存储坐标而非引用
        
        # 背景圆圈
        self.background = Circle(
            radius=0.4,
            fill_color=ACCENT_COLOR,
            fill_opacity=0.3,
            stroke_color=ACCENT_COLOR,
            stroke_width=2
        )
        
        # 计数文本
        self.count_text = Text(
            "0",
            font_size=SMALL_FONT_SIZE,
            font=FONT_FAMILY,
            color=PRIMARY_TEXT_COLOR,
            weight=BOLD
        )
        
        # 字符对标签
        self.pair_label = Text(
            pair_text,
            font_size=TINY_FONT_SIZE,
            font=FONT_FAMILY,
            color=SECONDARY_TEXT_COLOR
        )
        self.pair_label.next_to(self.background, DOWN, buff=0.1)
        
        self.add(self.background, self.count_text, self.pair_label)
        self.move_to(grid_position(grid_x, grid_y))  # 使用全局函数
    
    def update_count(self, new_count):
        """更新计数"""
        new_text = Text(
            str(new_count),
            font_size=SMALL_FONT_SIZE,
            font=FONT_FAMILY,
            color=PRIMARY_TEXT_COLOR,
            weight=BOLD
        )
        new_text.move_to(self.count_text.get_center())
        return Transform(self.count_text, new_text)
```

#### VocabularyPanel 创建规则 - 避免场景引用
```python
class VocabularyPanel(VGroup):
    """词汇表面板 - 优化版"""
    def __init__(self, title, grid_x, grid_y, **kwargs):
        super().__init__(**kwargs)
        
        # 标题
        self.title = Text(
            title,
            font_size=SUBTITLE_FONT_SIZE,
            font=FONT_FAMILY,
            color=ACCENT_COLOR,
            weight=BOLD
        )
        
        # 背景框
        self.background = RoundedRectangle(
            width=4.5,
            height=6,
            corner_radius=0.2,
            fill_color=BACKGROUND_COLOR,
            fill_opacity=0.3,
            stroke_color=ACCENT_COLOR,
            stroke_width=2
        )
        
        # 内容容器
        self.content_container = VGroup()
        
        self.add(self.background, self.title, self.content_container)
        self.title.next_to(self.background, UP, buff=0.2)
        
        # 定位到网格
        self.move_to(grid_position(grid_x, grid_y))  # 使用全局函数
```

### 4. 动画约束规则 (优化版)

#### 动画冲突避免规则
```python
# ⚠️ 重要：避免动画冲突
# 1. Write 和 Wiggle 动画不能同时作用在同一对象上
# 2. 需要分开执行，避免点数不匹配错误

# 错误示例：
# self.play(Write(text), Wiggle(text))  # 会导致 ValueError

# 正确示例：
self.play(Write(text), run_time=ANIMATION_DURATION["normal"])
self.play(Wiggle(text), run_time=ANIMATION_DURATION["fast"])
```

#### 颜色插值规则
```python
# ⚠️ 重要：颜色插值必须使用ManimColor对象
# 错误示例：
# interpolate_color("#FF0000", "#00FF00", 0.5)  # 会导致 AttributeError

# 正确示例：
color1 = ManimColor(CHAR_COLOR_MAP.get(char1, PRIMARY_TEXT_COLOR))
color2 = ManimColor(CHAR_COLOR_MAP.get(char2, PRIMARY_TEXT_COLOR))
interpolated_color = interpolate_color(color1, color2, 0.5)

# 在合并动画中的应用：
new_block = LetterBlock(merged_char, grid_x, grid_y)
color1 = ManimColor(CHAR_COLOR_MAP.get(chars[0], PRIMARY_TEXT_COLOR))
color2 = ManimColor(CHAR_COLOR_MAP.get(chars[1], PRIMARY_TEXT_COLOR))
new_block.background.set_fill(color=interpolate_color(color1, color2, 0.5))
```

#### 移动动画约束
```python
def create_grid_aligned_animation(mobject, target_grid_x, target_grid_y):
    """创建网格对齐的移动动画"""
    target_position = grid_position(target_grid_x, target_grid_y)
    return mobject.animate.move_to(target_position)
```

#### 时间约束
```python
# 标准动画时长
ANIMATION_DURATION = {
    "fast": 0.5,
    "normal": 1.0,
    "slow": 1.5,
    "very_slow": 2.0
}

# 等待时间
WAIT_TIME = {
    "short": 0.5,
    "normal": 1.0,
    "long": 1.5
}
```

### 5. 阶段实现规则

#### 阶段1: 语料初始化
- 文本示例位置: text_example_zone
- 单词排列: 垂直排列，每个单词占用3-4个网格单元
- 字母块间距: 1个网格单元
- 频率面板位置: frequency_panel_zone

#### 阶段2: 频率统计
- 连接线必须连接相邻字母块的中心点
- 频率计数器位置在连接线上方1个网格单元
- 扫描动画路径沿网格线移动

#### 阶段3: 最优合并
- 高亮效果使用HIGHLIGHT_COLOR
- 合并动画的中间帧位置在网格点上
- "Merge!"提示位置在merge_animation_zone中心
- ⚠️ 合并后的颜色使用ManimColor插值

#### 阶段4: 词汇表构建
- 文件图标位置在file_display_zone
- 更新后的文本重新对齐到网格
- 背景数字闪烁位置随机但在网格点上

### 6. 调试和验证规则 (优化版)

#### 网格可视化（调试用）
```python
def show_grid_debug(self):
    """显示网格线用于调试"""
    grid_lines = VGroup()
    
    # 垂直线
    for i in range(32):
        line = Line(
            start=grid_position(i, 0),
            end=grid_position(i, 16),
            color=GRAY,
            stroke_width=0.5,
            stroke_opacity=0.3
        )
        grid_lines.add(line)
    
    # 水平线
    for j in range(18):
        line = Line(
            start=grid_position(0, j),
            end=grid_position(30, j),
            color=GRAY,
            stroke_width=0.5,
            stroke_opacity=0.3
        )
        grid_lines.add(line)
    
    self.add(grid_lines)
```

#### 位置验证 (优化版)
```python
def validate_grid_alignment(self, mobject):
    """验证对象是否对齐到网格"""
    center = mobject.get_center()
    grid_width = GRID_WIDTH / (GRID_COLS - 1)
    grid_height = GRID_HEIGHT / (GRID_ROWS - 1)
    
    grid_x = round((center[0] + GRID_WIDTH/2) / grid_width)
    grid_y = round((GRID_HEIGHT/2 - center[1]) / grid_height)
    
    expected_pos = grid_position(grid_x, grid_y)
    actual_pos = center
    
    tolerance = 0.01
    if np.linalg.norm(expected_pos - actual_pos) > tolerance:
        print(f"Warning: Object not aligned to grid. Expected: {expected_pos}, Actual: {actual_pos}")
```

## 常见问题和解决方案 (Common Issues & Solutions)

### 1. 序列化问题 (Serialization Issues)
**问题**: `TypeError: cannot pickle '_thread.lock' object`
**原因**: 在类中存储了场景对象引用
**解决**: 使用全局函数和坐标存储，避免场景引用

### 2. 动画冲突问题 (Animation Conflicts)
**问题**: `ValueError: could not broadcast input array from shape (4,3) into shape (20,3)`
**原因**: Write和Wiggle动画同时作用在同一对象上
**解决**: 分开执行动画，避免点数不匹配

### 3. 颜色插值问题 (Color Interpolation Issues)
**问题**: `AttributeError: 'str' object has no attribute 'interpolate'`
**原因**: 直接使用字符串进行颜色插值
**解决**: 使用ManimColor对象进行插值

### 4. 网格对齐问题 (Grid Alignment Issues)
**问题**: 对象位置不精确对齐到网格
**原因**: 坐标计算精度问题
**解决**: 使用统一的grid_position函数和验证机制

## 使用指南 (Usage Guidelines)

1. **开始代码生成前，必须先设置网格系统**
2. **所有新创建的Mobject都必须指定网格坐标**
3. **动画路径必须经过网格点**
4. **使用预定义的颜色和字体常量**
5. **避免在类中存储场景引用**
6. **颜色插值必须使用ManimColor对象**
7. **分开执行可能冲突的动画**
8. **在调试模式下启用网格可视化**
9. **定期验证对象的网格对齐**

## 性能优化建议

1. **预计算网格位置**
2. **重用相同类型的Mobject**
3. **使用LaggedStart优化批量动画**
4. **避免过度复杂的Transform动画**
5. **使用全局函数避免重复计算**

这个优化版MDC文件确保生成的Manim代码严格遵循31×17网格系统，使用统一的黑色背景和专业的视觉风格，同时解决了常见的序列化、动画冲突和颜色插值问题，保持代码的可维护性和性能。
