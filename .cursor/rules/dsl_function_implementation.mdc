---
description:
globs:
alwaysApply: false
---
---
title: DSL 功能扩展流程
description: 为项目添加新的 DSL 功能的标准流程和最佳实践，包括设计、实现、测试的完整步骤
glob: "dsl/v2/**/*.{py,md}"
alwaysApply: true
docs:
  - name: Man<PERSON>
    url: https://docs.manim.community/en/stable
---

# DSL 功能扩展流程

## 简介

本规则文档定义了为 DSL v2 系统添加新功能的标准流程，确保一致性、可测试性和代码质量。遵循这个流程可以使新功能集成顺畅、保持架构一致性，并确保足够的测试覆盖率。

## 流程概述

扩展 DSL 功能通常遵循以下流程：

1. 设计阶段：明确需求和规格
2. 实现阶段：编写代码和函数
3. 测试阶段：单元测试和集成测试
4. 文档和优化阶段

## 1. 设计阶段

### 1.1 功能规格定义

首先在 `design/` 目录下创建或更新设计文档，明确：

```markdown
1. 功能目的/效果：新功能要解决什么问题，产生什么效果
2. 参数设计：包括必需参数和可选参数，每个参数的类型和默认值
3. 底层 Manim 概念：此功能将使用哪些 Manim 类和方法，一定要参考 Manim 文档选择最准确的 Manim 函数，比如想实现数字动态变化到某个值的效果，用ChangeDecimalToValue就比用Number.animate.set_value更合适。
4. AST 节点：需要创建的节点类型和结构
5. Visitor 实现：需要创建的 visitor 函数路径
```

示例：
```markdown
### 3.1 `AnimateCounter`

*   **目的/效果:** 创建一个动态变化的数字计数器，可以附带标签，并有强调效果。
*   **参数:**
    *   `target_value`: (必需) 最终计数值 (数字)。
    *   `label`: (可选) 显示在数字旁边的文本标签 (字符串)。
    *   `start_value`: (可选, 默认 0) 起始计数值 (数字)。
    *   `duration`: (可选, 默认 2) 动画持续时间 (秒)。
    *   `effect`: (可选, 默认 "zoom") 结尾强调效果 ('zoom', 'flash', 'none')。
    *   `unit`: (可选) 单位，如 "K+", "%" (字符串)。
*   **Manim 概念:** `ChangeDecimalToValue`, `Text`, `Transform`, `LaggedStart`.
*   **AST 节点:** `AnimateCounterNode`
*   **Visitor:** `visitors.animate_counter.visit_animate_counter`
```

## 2. 实现阶段

### 2.1 AST 节点定义

在 `dsl/v2/ast_nodes.py` 中添加新的节点类：

```python
class NewFeatureNode(Node):  # 或者 class NewFeatureNode(ActionNode)
    """
    为新功能创建 AST 节点类

    属性：
        必需参数: 类型描述
        可选参数: 类型描述 = 默认值
    """
    type: Literal["new_feature"] = "new_feature"  # 必须包含 type 字段作为判别器
    required_param: str
    optional_param: int = 10
    another_param: Optional[str] = None
```

### 2.2 AST 构建器更新

在 `dsl/v2/ast_builder.py` 中的 `_process_action` 方法中添加新的动作类型处理：

```python
def _process_action(self, action_data: Dict) -> BaseNode:
    """处理单个动作数据，创建相应的 AST 节点"""
    action_type = action_data.get("type", "")

    # ...现有代码...

    elif action_type == "new_feature":
        # 处理参数，创建节点
        required_param = action_data.get("required_param")
        if required_param is None:
            raise ValueError("'required_param' 必须提供")

        optional_param = action_data.get("optional_param", 10)
        another_param = action_data.get("another_param")

        return NewFeatureNode(
            required_param=required_param,
            optional_param=optional_param,
            another_param=another_param
        )
```

### 2.3 创建 Visitor 模块

在 `dsl/v2/visitors/` 目录下创建新的 visitor 模块，例如 `new_feature.py`：

```python
# dsl/v2/visitors/new_feature.py

from typing import TYPE_CHECKING, Any

from loguru import logger

if TYPE_CHECKING:
    from ..ast_nodes import NewFeatureNode
    from ..code_generator import CodeGenerator


def visit_new_feature(generator: "CodeGenerator", node: "NewFeatureNode", **kwargs: Any) -> None:
    """
    生成 NewFeatureNode 对应的 Manim 代码。

    参数:
        generator: 代码生成器实例
        node: NewFeatureNode 实例
        **kwargs: 额外的参数
    """
    logger.info(f"正在访问 NewFeatureNode")

    # 1. 创建唯一的变量名前缀
    prefix = f"feature_{abs(hash(node.required_param)) % 10000}"

    # 2. 生成代码，使用 generator._add_line 方法
    generator._add_line(f"# 创建新功能对象")
    generator._add_line(f"{prefix}_obj = SomeClass(")
    generator._add_line(f"    param={node.required_param},")
    generator._add_line(f"    optional={node.optional_param},")
    generator._add_line(f")")

    # 3. 添加到场景中
    generator._add_line(f"self.play(Create({prefix}_obj))")

    # 4. 从场景中淡出，确保没有遗留元素，不会干扰后续的动画
    generator._add_line(f"self.play(FadeOut({prefix}_obj), run_time=1.0)")

    logger.info(f"NewFeatureNode 访问完成")
```

## 3. 测试阶段

### 3.1 创建单元测试

在 `tests/dsl/v2/` 目录下创建针对新功能的测试文件：

```python
# tests/dsl/v2/test_new_feature.py
import sys

from loguru import logger

from dsl.v2.ast_nodes import NewFeatureNode, MetadataNode, SceneNode
from dsl.v2.code_generator import CodeGenerator

# 测试辅助函数
def create_test_scene(actions=[]) -> SceneNode:
    metadata = MetadataNode(title="TestScene", author="Tester", background_color="BLACK")
    return SceneNode(metadata=metadata, objects=[], actions=actions)

# 测试基本功能
def test_new_feature_basic():
    """测试基本的新功能节点生成"""
    node = NewFeatureNode(required_param="test")
    scene = create_test_scene(actions=[node])

    generator = CodeGenerator()
    code = generator.generate(scene)

    logger.debug(f"Generated code:\n{code}")

    # 验证生成的代码包含预期内容
    assert "SomeClass" in code
    assert "param=test" in code
    assert "self.play(Create(" in code
```

### 3.2 运行测试

使用 pytest 运行测试并验证：

```bash
uv run pytest tests/dsl/v2/test_new_feature.py -v
```

如果测试失败，修复问题并重新测试，直到通过。

### 3.3 运行覆盖率测试

确保测试覆盖率达标：

```bash
uv run pytest --cov=dsl.v2.visitors.new_feature tests/dsl/v2/test_new_feature.py
```

### 3.4 创建示例 DSL 文件

创建一个实际的 DSL 示例文件验证功能：

```json
{
  "schema_version": "2.0-mvp",
  "metadata": {
    "title": "NewFeatureDemo",
    "author": "测试者",
    "background_color": "BLACK"
  },
  "objects": [],
  "actions": [
    {
      "type": "new_feature",
      "required_param": "示例值",
      "optional_param": 20
    },
    {
      "type": "wait",
      "duration": 1
    }
  ]
}
```

### 3.5 运行集成测试

使用 `dsl_to_manim.py` 脚本生成并渲染动画：

```bash
python dsl/v2/dsl_to_manim.py examples/new_feature_example.json -r -q l
```

## 4. 性能优化和错误处理

### 4.1 代码优化

- 确保变量名唯一性，避免冲突
- 优化代码生成，减少冗余
- 确保适当的日志记录

### 4.2 错误处理

添加全面的错误处理逻辑：

```python
# 在 visitor 函数中
if node.required_param is None or node.required_param == "":
    logger.error("必需参数为空")
    return  # 错误时中止生成
```

### 4.3 高级功能考虑

- 适当的参数验证和转换
- 灵活的布局和定位控制
- 动画效果的多样化选择

## 5. 对象生命周期管理

在动画场景中，正确管理对象的生命周期对避免内容重叠和冲突至关重要。

### 5.1 单一元素管理

使用场景中特定变量跟踪元素，而不是依赖通用的 `self.mobjects`：

```python
# 不要这样做
if len(self.mobjects) > 0:
    self.play(FadeOut(*self.mobjects), run_time=0.5)
    self.clear()

# 推荐做法
generator._add_line(f"# 淡出之前的对比内容（如果存在）")
generator._add_line(f"if hasattr(self, 'previous_comparison_group') and self.previous_comparison_group is not None:")
generator._add_line(f"    self.play(FadeOut(self.previous_comparison_group), run_time=0.5)")
```


### 5.2 保存元素引用

每次生成新元素后，将其保存到特定变量中供后续使用：

```python
# 保存当前显示的内容组，供下次淡出使用
generator._add_line(f"self.previous_{prefix}_group = {prefix}_group")
```

## 6. Manim 对象参数兼容性

不同的 Manim 对象支持不同的参数集。在实现之前先了解对象的实际可用参数非常重要。

### 6.1 常见对象参数参考表

| 对象类型 | 支持的参数 | 不支持的参数 | 替代方案 |
|---------|-----------|------------|---------|
| Code    | code_string, language | font_size, line_spacing | 使用 scale() 方法 |
| Text    | font, font_size, color | width (直接设置) | 先创建再修改 width 属性 |
| Group/VGroup | 无构造参数 | 位置、大小参数 | 先创建后使用 arrange() |

### 6.2 处理不兼容参数的示例

```python
# 不要这样做
code_obj = Code(code_string="...", font_size=16)  # 错误!

# 而是这样做
code_obj = Code(code_string="...")
code_obj.scale(0.8)  # 使用 scale 代替 font_size

# 对于 Text 对象的宽度限制
text_obj = Text("长文本内容", font_size=24)
text_obj.width = min(text_obj.width, config.frame_width * 0.3)
```

## 7. 屏幕边界处理

为确保内容不超出屏幕边界，应当添加标准的大小检测和调整代码。

### 7.1 全局组对象缩放

```python
# 确保内容适合屏幕大小
generator._add_line(f"screen_width = config.frame_width * 0.9  # 留出10%的边距")
generator._add_line(f"if {prefix}_group.width > screen_width:")
generator._add_line(f"    scale_factor = screen_width / {prefix}_group.width")
generator._add_line(f"    {prefix}_group.scale(scale_factor)")
```

### 7.2 单个元素的宽度限制

```python
# 比如，在 SideBySideComparison 中，因为是左右双栏结构，确保文本宽度不超过屏幕的三分之一
generator._add_line(f"{prefix}_content.width = min({prefix}_content.width, config.frame_width * 0.3)")
```

### 7.3 导入需要用到的python函数或module

`CodeGenerator` 中的 `_add_import`函数可用于导入需要用到的python函数或module

```python
    def _add_import(self, module: str, class_name: str = None, as_name: str = None) -> None:
        """
        将类名添加到导入集合中，并添加导入语句到代码的顶部。

        参数:
            module: 模块名称，例如 "json"
            class_name: 需要导入的类名，例如 "linspace"，如果为 None，则只导入模块
            as_name: 导入的别名，例如将 "numpy" 导入为 "np"
        """
```

注意，manim中的元素无需导入，因为已经通过 `from manim import *` 全部导入了

## 8. 动画效果调优

### 8.1 标准动画位移值

不同动画效果推荐的参数值范围：

| 动画效果 | 参数 | 推荐值 | 说明 |
|---------|-----|-------|------|
| shift   | UP/DOWN/LEFT/RIGHT | 1-3 | 屏幕高度约为8个单位 |
| scale   | factor | 0.5-2.0 | <0.5过小, >2.0过大 |
| FadeIn/Out | run_time | 0.5-1.5 | 适中的过渡时间 |

### 8.2 滑动效果最佳实践

```python
# 从屏幕下方滑入效果
generator._add_line(f"# 先将组放置在屏幕下方")
generator._add_line(f"{prefix}_group.shift(DOWN * 3)")  # 位移适量
generator._add_line(f"self.add({prefix}_group)")
generator._add_line(f"# 从下方滑入屏幕中央")
generator._add_line(f"self.play({prefix}_group.animate.shift(UP * 3), run_time=1.0)")
```

## 9. 测试断言更新

### 9.1 灵活的测试断言

测试中应该验证功能核心，但不要过度耦合特定实现细节：

```python
# 不要这样写
assert "animate.shift(DOWN * 8)" in code  # 具体数值容易变化

# 更好的做法
assert "shift(DOWN" in code
assert "animate.shift(UP" in code
assert "从下方滑入屏幕中央" in code  # 验证注释存在
```

### 9.2 关注核心功能而非实现细节

```python
# 关注功能正确工作
assert "Code(code_string=\"\"\"" in code  # 确保代码元素被创建
assert "language='json'" in code  # 确保语言设置正确
```

## 10. 字体和样式标准化

### 10.1 项目标准字体

```python
# Text 对象使用标准字体
generator._add_line(f"Text(\"{title}\", font_size=36, color=BLUE, font='Maple Mono NF CN')")
```

### 10.2 标准字体大小

| 元素类型 | 推荐字体大小 | 说明 |
|---------|------------|------|
| 标题      | 32-36     | 醒目但不过大 |
| 正文文本   | 24       | 清晰可读 |
| 代码/数据  | 缩放 0.7   | 通过缩放控制 |

## 常见错误与解决方案

1. **参数类型错误**：确保在 AST 节点定义中使用正确的类型提示，并在 visitor 中进行适当的类型转换。

2. **变量名冲突**：使用基于哈希或递增计数器的唯一前缀，如 `prefix = f"feature_{abs(hash(node.required_param)) % 10000}"`。

3. **导入问题**：仔细检查循环导入问题，使用 TYPE_CHECKING 解决类型提示的循环导入。

4. **测试断言失败**：确保测试断言匹配实际生成的代码。例如，浮点数可能会显示为 `100.0` 而不是 `100`。

5. **视觉重叠问题**：当多个元素同时显示时，确保适当的位置控制和淡出处理。

6. **属性错误**：如果遇到 "对象没有属性 X" 错误，不要假设 Manim 对象支持某属性，应首先查阅文档或采用替代方法。

## 示例

请参考 AnimateCounter 功能的完整实现：

- [dsl_v2_extension_design.md](mdc:design/dsl_v2_extension_design.md) 设计文档
- [README.md](mdc:dsl/v2/README.md) 框架说明
- [animate_counter.py](mdc:dsl/v2/visitors/animate_counter.py) 代码实现
- [test_animate_counter.py](mdc:tests/dsl/v2/test_animate_counter.py) 单元测试
