---
description: 
globs: 
alwaysApply: false
---
# 新增素材扩充工具开发指南

本指南详细说明如何为素材扩充系统添加新的工具，以现有 [CompetitiveAnalysisTool](mdc:tools/enhance_tools/competitive_analysis_tool.py) 参考。

## 核心设计原则

### 🤖 智能适用性判断
- **使用Agent判断**：通过LLM Agent智能判断内容是否适合使用该工具
- **精准场景匹配**：详细定义适用和不适用场景，帮助Agent做出准确判断
- **动态检查机制**：在`can_apply`方法中集成Agent检查，而非仅依赖规则判断

### 📝 内容精简要求
- **输出必须精简**：所有工具输出都要求内容精简明了，去除冗余信息
- **字数限制**：对关键信息设置明确的字数限制
- **核心信息优先**：重点突出差异化和核心价值点
- **避免空洞描述**：使用具体数据和事实，避免空洞的形容词

## 开发步骤

### 1. 研究现有工具结构
首先查看现有工具的实现模式：
- 参考 [base_tool.py](mdc:tools/enhance_tools/base_tool.py) 了解基础架构
- 查看 [competitive_analysis_tool.py](mdc:tools/enhance_tools/competitive_analysis_tool.py) 学习Agent判断和精简输出
- 查看 [six_dimensions_evaluation_tool.py](mdc:tools/enhance_tools/six_dimensions_evaluation_tool.py) 等现有工具实现
- 了解工具分类、参数定义、适用场景的标准结构
- 注意错误处理和日志记录模式

### 2. 确定工具分类和定位
根据 `ToolCategory` 枚举选择合适的分类：

```python
class ToolCategory(Enum):
    CONTENT_ORGANIZATION = "A_内容结构化组织"     # timeline、表格、公式、框架图、思维导图等
    MULTIMODAL_PRESENTATION = "B_多模态呈现"     # 动态数据图、图片展示、emoji、插图等
    DEEP_INSIGHTS = "C_深度洞察"                # 对比、评估、insights洞察、深度提问和问答
    SMART_INTERACTION = "D_智能交互"            # 详细例子、模拟器、交互式媒体等
```

### 3. 创建新的工具文件
在 `tools/enhance_tools/` 目录下创建新文件，必须包含：

**文件头部和导入：**
```python
#!/usr/bin/env python3
"""
工具名称 - 工具分类
简洁描述工具的作用和效果
"""

import json
import os
import re
from typing import Any, Optional

from loguru import logger

from .base_tool import EnhancementTool, ToolCategory
```

**工具类定义要点：**
```python
class NewEnhanceTool(EnhancementTool):
    """工具名称 - 工具分类"""

    # 必需的类属性
    tool_name = "tool_identifier"  # 唯一标识符，用于配置和注册
    tool_description = "详细描述工具功能和价值"
    tool_category = ToolCategory.APPROPRIATE_CATEGORY

    # 适用场景描述 - 供LLM智能选择使用（参考competitive_analysis_tool.py）
    suitable_content_types = [
        "具体的内容类型1",
        "具体的内容类型2", 
        "具体的内容类型3",
        # 要具体明确，避免模糊描述
    ]
    suitable_purposes = [
        "明确的使用目标1",
        "明确的使用目标2",
        "明确的使用目标3",
        # 描述具体的应用场景
    ]
    required_conditions = [
        "内容涉及具体的XXX",
        "存在明确的YYY特征",
        "内容长度超过XXX字符以便提取有效信息",
        # 设置明确的必需条件
    ]

    # 不适用场景描述 - 帮助LLM做负面判断（参考competitive_analysis_tool.py）
    unsuitable_content_types = [
        "纯理论知识和概念解释",
        "历史事件和人物传记",
        "抽象哲学和思想讨论",
        # 明确不适合的内容类型
    ]
    unsuitable_purposes = [
        "纯知识传授和教育",
        "理论学习和概念理解",
        "娱乐和休闲内容",
        # 明确不适合的使用目的
    ]
    blocking_conditions = [
        "内容没有明确的XXX特征",
        "内容过于抽象无法进行具体分析",
        "缺乏可量化的分析维度",
        "内容长度不足以提取有效信息",
        # 明确的阻止条件
    ]

    def __init__(self, config=None):
        self.config = config
        self.agent = None
        if config:
            self._init_model()  # 必须初始化Agent用于适用性判断

    def _init_model(self):
        """初始化Camel模型 - 必需用于智能适用性判断"""
        try:
            from camel.agents import ChatAgent
            from camel.messages import BaseMessage
            from camel.models import ModelFactory
            from camel.types import ModelPlatformType

            model_config = self.config.get("model", {})
            self.model = ModelFactory.create(
                model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
                model_type=model_config.get("type", "openai/gpt-4o-mini"),
                api_key=model_config.get("api", {}).get("openai_compatibility_api_key"),
                url=model_config.get("api", {}).get("openai_compatibility_api_base_url"),
            )

            # 定制化系统提示词 - 强调精简输出
            system_prompt = """你是专业的内容分析专家，擅长判断内容适用性和提取精简的结构化信息。
你的任务是：
1. 准确判断内容是否适合使用特定工具
2. 提取最核心的关键信息，去除冗余内容
3. 确保输出精简明了，重点突出
4. 严格按照要求的JSON格式输出

请严格按照要求的JSON格式输出，确保分析的准确性和精简性。"""

            self.agent = ChatAgent(
                system_message=BaseMessage.make_assistant_message("", system_prompt),
                model=self.model,
            )
            logger.info(f"{self.tool_name}模型初始化成功")
        except Exception as e:
            logger.warning(f"{self.tool_name}模型初始化失败: {e}")

    def get_tool_info(self) -> dict[str, Any]:
        """获取工具信息 - 供智能选择器决策使用"""
        return {
            "name": self.tool_name,
            "description": self.tool_description,
            "category": self.tool_category.value,
            "suitable_content": self.suitable_content_types,
            "suitable_purposes": self.suitable_purposes,
            "required_conditions": self.required_conditions,
            "unsuitable_content": self.unsuitable_content_types,
            "unsuitable_purposes": self.unsuitable_purposes,
            "blocking_conditions": self.blocking_conditions,
            "output_type": "输出类型描述",
            "typical_output": "典型输出描述（精简版）",
            "use_case": "详细的使用场景说明"
        }

    def can_apply(self, content: str, purpose: str, context: dict[str, Any]) -> bool:
        """检查工具是否可用 - 集成Agent智能判断（参考competitive_analysis_tool.py）"""
        # 基本检查：配置存在、内容长度、配置开关
        basic_check = (
            self.config
            and len(content) >= 800  # 需要足够的内容来分析
            and self.config.get("material", {}).get("material_enhance", {}).get(self.tool_name, True)
            and self.agent is not None
        )

        if not basic_check:
            return False

        # 核心检查：使用Agent智能判断适用性
        return self._check_applicability_with_agent(content, purpose)

    def _check_applicability_with_agent(self, content: str, purpose: str) -> bool:
        """使用Agent智能判断工具适用性（参考competitive_analysis_tool.py）"""
        try:
            # 使用LLM快速判断是否适合使用该工具
            check_prompt = f"""请分析以下内容是否适合使用{self.tool_description}：

**内容**：
{content[:2000]}{'...(内容截断)' if len(content) > 2000 else ''}

**目的**：{purpose}

**判断标准**：
{chr(10).join([f"{i+1}. {condition}" for i, condition in enumerate(self.required_conditions)])}

**不适用条件**：
{chr(10).join([f"- {condition}" for condition in self.blocking_conditions])}

请简洁回答：YES或NO，并在同一行简要说明理由（限制在50字内）。

格式示例：YES - 内容涉及具体产品，存在明确的分析需求"""

            response = self.agent.step(check_prompt)
            response_content = response.msgs[0].content.strip()

            logger.info(f"{self.tool_name}适用性检查响应: {response_content[:200]}...")

            # 解析响应 - 支持多种格式
            response_upper = response_content.upper()

            # 检查是否包含YES
            if "YES" in response_upper or response_content.upper().startswith("YES"):
                logger.info(f"{self.tool_name}适用性检查通过")
                return True

            # 检查JSON格式（如果Agent返回JSON）
            try:
                if response_content.startswith("{"):
                    data = json.loads(response_content)
                    suitable_value = data.get("适合使用工具", "").upper()
                    if suitable_value == "YES":
                        logger.info(f"{self.tool_name}适用性检查通过: JSON格式")
                        return True
            except:
                pass

            logger.info(f"{self.tool_name}适用性检查未通过: {response_content[:100]}...")
            return False

        except Exception as e:
            logger.error(f"{self.tool_name}适用性检查失败: {e}")
            return False

    def apply_tool(self, content: str, output_dir: str, context: dict[str, Any]) -> Optional[dict[str, Any]]:
        """应用工具，执行具体的扩充操作"""
        logger.info(f"🔧 应用{self.tool_name}工具，准备处理内容")

        try:
            os.makedirs(output_dir, exist_ok=True)

            output_filename = f"{self.tool_name}_output.json"
            output_path = os.path.join(output_dir, output_filename)

            # 检查是否已存在文件
            if os.path.exists(output_path):
                logger.info(f"{self.tool_name}结果已存在: {output_path}")
                with open(output_path, encoding="utf-8") as f:
                    existing_data = json.load(f)
                return {
                    "tool_name": self.tool_name,
                    "type": self.tool_name,
                    "file_path": output_path,
                    "data": existing_data,
                    "status": "exists",
                }

            # 执行具体的工具逻辑
            result_data = self._process_content(content, context)

            if not result_data:
                return None

            # 保存结果数据
            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)

            logger.info(f"🔧 {self.tool_name}工具处理完成: {output_path}")

            return {
                "tool_name": self.tool_name,
                "type": self.tool_name,
                "file_path": output_path,
                "data": result_data,
                "status": "created",
            }

        except Exception as e:
            logger.error(f"{self.tool_name}工具处理失败: {e}")
            return None

    def _process_content(self, content: str, context: dict[str, Any]) -> Optional[dict[str, Any]]:
        """处理内容的核心逻辑 - 确保输出精简"""
        return self._process_with_agent(content, context)

    def _process_with_agent(self, content: str, context: dict[str, Any]) -> Optional[dict[str, Any]]:
        """使用Agent进行内容处理 - 强调精简输出"""
        purpose = context.get("purpose", "内容分析")

        # 构建精简化提示词
        prompt = f"""基于以下内容进行{self.tool_description}：

**内容材料**：
{content[:6000]}{'...(内容截断)' if len(content) > 6000 else ''}

**分析目标**：{purpose}

**重要要求**：
1. 内容必须精简明了，去除冗余信息
2. 每个分析点限制在1-2句话内
3. 重点突出核心价值和差异化特点
4. 避免空洞的形容词，使用具体的数据和事实
5. 所有文本描述都要有明确的字数限制

请严格按照以下JSON格式输出：

```json
{{
    "核心分析": {{
        "主题": "内容主题（限制20字内）",
        "重点": "分析重点（限制30字内）",
        "核心洞察": "关键洞察（限制50字内）"
    }},
    "详细结果": {{
        // 根据具体工具需求定义精简的数据结构
        // 每个字段都要有明确的字数限制
        "关键点1": "精简描述（限制XX字内）",
        "关键点2": "精简描述（限制XX字内）"
    }},
    "总结建议": {{
        "核心优势": "优势总结（限制30字内）",
        "改进建议": "建议总结（限制40字内）"
    }}
}}
```"""

        try:
            response = self.agent.step(prompt)
            response_content = response.msgs[0].content

            # 提取JSON
            json_match = re.search(r"```json\s*(\{.*?\})\s*```", response_content, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                json_str = response_content.strip()

            result_data = json.loads(json_str)
            logger.info(f"{self.tool_name}内容处理成功")
            return result_data

        except Exception as e:
            logger.error(f"{self.tool_name}内容处理失败: {e}")
            return None

    def generate_intro(self, tool_result: dict[str, Any]) -> str:
        """生成工具输出的介绍文本 - 精简版"""
        data = tool_result.get("data", {})

        if not data:
            return ""

        intro = f"## 🔧 {self.tool_description}\n\n"

        # 核心分析 - 精简版
        core_analysis = data.get("核心分析", {})
        if core_analysis:
            intro += f"**主题**: {core_analysis.get('主题', 'N/A')}\n"
            intro += f"**核心洞察**: {core_analysis.get('核心洞察', 'N/A')}\n\n"

        # 关键结果 - 只显示最重要的
        detailed_results = data.get("详细结果", {})
        if detailed_results:
            intro += "### 关键发现\n"
            for key, value in list(detailed_results.items())[:3]:  # 限制显示前3个
                intro += f"- **{key}**: {value}\n"
            intro += "\n"

        # 总结建议 - 精简版
        summary = data.get("总结建议", {})
        if summary:
            core_advantage = summary.get("核心优势")
            if core_advantage:
                intro += f"**核心优势**: {core_advantage}\n\n"

        # 精简结语
        intro += "提供精准分析洞察，助力优化决策。\n"

        return intro
```

### 4. Agent智能判断最佳实践（参考competitive_analysis_tool.py）

#### 4.1 何时使用Agent判断
**必须使用场景：**
- 需要理解内容语义和上下文
- 判断标准较为复杂或主观
- 需要分析内容特征和模式
- 工具适用性依赖内容质量

**Agent判断模式：**
```python
def _check_applicability_with_agent(self, content: str, purpose: str) -> bool:
    """使用Agent智能判断工具适用性的标准模式"""
    try:
        check_prompt = f"""请分析以下内容是否适合使用{self.tool_description}：

**内容**：
{content[:2000]}{'...(内容截断)' if len(content) > 2000 else ''}

**目的**：{purpose}

**判断标准**：
{chr(10).join([f"{i+1}. {condition}" for i, condition in enumerate(self.required_conditions)])}

请简洁回答：YES或NO，并简要说明理由（限制50字内）。"""

        response = self.agent.step(check_prompt)
        response_content = response.msgs[0].content.strip()

        # 支持多种响应格式的解析
        return self._parse_applicability_response(response_content)

    except Exception as e:
        logger.error(f"适用性检查失败: {e}")
        return False

def _parse_applicability_response(self, response_content: str) -> bool:
    """解析适用性检查响应的标准方法"""
    response_upper = response_content.upper()
    
    # 方法1: 检查YES关键词
    if "YES" in response_upper or response_content.upper().startswith("YES"):
        return True
    
    # 方法2: 检查JSON格式
    try:
        if response_content.startswith("{"):
            data = json.loads(response_content)
            suitable_value = data.get("适合使用工具", "").upper()
            return suitable_value == "YES"
    except:
        pass
    
    return False
```

#### 4.2 精简输出要求

**核心原则：**
- **字数限制**：每个输出字段都要有明确的字数限制
- **去除冗余**：避免重复和无意义的描述
- **突出重点**：优先显示最核心的信息
- **具体数据**：使用具体事实而非空洞形容词

**精简输出模式：**
```python
def _build_concise_prompt(self, content: str, context: dict[str, Any]) -> str:
    """构建精简化输出提示词"""
    return f"""**重要要求**：
1. 内容必须精简明了，去除冗余信息
2. 每个分析点限制在1-2句话内
3. 重点突出核心价值和差异化特点
4. 避免空洞的形容词，使用具体的数据和事实
5. 所有文本描述都要有明确的字数限制

请严格按照以下JSON格式输出：

```json
{{
    "核心信息": {{
        "主题": "主题描述（限制20字内）",
        "重点": "重点说明（限制30字内）"
    }},
    "关键结果": {{
        "结果1": "精简描述（限制25字内）",
        "结果2": "精简描述（限制25字内）"
    }}
}}
```"""
```

### 5. 更新模块导入
修改 [__init__.py](mdc:tools/enhance_tools/__init__.py)：
- 添加新工具的import语句
- 在`__all__`列表中添加工具类名

```python
from .new_enhance_tool import NewEnhanceTool

__all__ = [
    'EnhancementTool',
    'ToolCategory',
    'ScreenRecordingTool',
    'TimelineTool',
    'SixDimensionsEvaluationTool',
    'DeepInsightQATool',
    'CompetitiveAnalysisTool',
    'NewEnhanceTool'  # 添加新工具
]
```

### 6. 更新工具注册表
修改 [material_enhancement.py](mdc:agents/material_enhancement.py) 中的 `ToolRegistry`：

```python
class ToolRegistry:
    _tools = {
        "screen_recording": ScreenRecordingTool,
        "timeline_generation": TimelineTool,
        "six_dimensions_evaluation": SixDimensionsEvaluationTool,
        "deep_insight_qa": DeepInsightQATool,
        "competitive_analysis": CompetitiveAnalysisTool,
        "new_tool_identifier": NewEnhanceTool,  # 添加新工具
    }
```

### 7. 更新MaterialEnhancer配置
在 `MaterialEnhancer._initialize_tools()` 方法中添加新工具的初始化逻辑：

```python
def _initialize_tools(self) -> List[EnhancementTool]:
    tools = []

    # 现有工具...

    if self.config.get("new_tool_identifier", True):  # 默认启用
        tools.append(NewEnhanceTool(self.config_dict))
        logger.info("✅ 已启用新工具")

    return tools
```

### 8. 更新配置文件
在 `config/config.yaml` 的 `material_enhance` 配置部分添加新工具的开关：

```yaml
# 素材扩充配置
material_enhance:
  screen_record: true
  timeline_generation: true
  six_dimensions_evaluation: true
  deep_insight_qa: true  # 深度洞察问答工具
  competitive_analysis: true  # 竞品对比分析工具
  new_tool_identifier: true  # 新工具配置开关
```

**配置说明：**
- 配置项名称应与工具的 `tool_name` 保持一致
- 默认值建议设为 `true`，便于用户直接使用
- 添加注释说明工具的作用

### 9. 测试和验证
创建测试脚本验证新工具：

```python
#!/usr/bin/env python3
"""测试新工具"""

def test_new_tool():
    from tools.enhance_tools import NewEnhanceTool

    # 测试实例化
    tool = NewEnhanceTool()
    print(f"✅ {tool.tool_name} 实例化成功")

    # 测试工具信息
    info = tool.get_tool_info()
    print(f"工具信息: {info}")

    # 测试Agent适用性检查
    test_content = "这是一段测试内容，用于验证工具的适用性判断功能..."
    can_apply = tool.can_apply(test_content, "测试目标", {})
    print(f"适用性检查: {can_apply}")

    # 测试精简输出
    if can_apply:
        result = tool.apply_tool(test_content, "./test_output", {"purpose": "测试"})
        if result:
            print(f"✅ 工具执行成功，输出精简度验证通过")
            print(f"输出文件: {result['file_path']}")
        else:
            print("❌ 工具执行失败")

if __name__ == "__main__":
    test_new_tool()
```

## 重要注意事项

### 🎨 设计原则
- **功能聚焦**：每个工具应该有明确的单一职责
- **智能适配**：通过Agent智能判断内容适用性，而非简单规则匹配
- **精简输出**：所有输出都要求内容精简，设置明确字数限制
- **用户友好**：提供清晰的输出和错误提示
- **数据驱动**：优先使用Agent进行智能分析和提取

### 🔧 技术要点
- **Agent集成**：必须正确初始化和使用Agent进行适用性判断
- **精简要求**：在所有提示词中明确精简输出要求
- **配置管理**：支持通过config.yaml配置启用/禁用
- **错误处理**：优雅处理异常情况，提供有意义的错误信息
- **日志记录**：使用loguru记录关键操作和状态
- **文件管理**：正确处理输出目录和文件路径
- **数据格式**：统一使用JSON格式保存结构化数据

### 📝 实现规范
- **Agent判断**：在`can_apply`方法中集成Agent智能检查
- **精简输出**：所有JSON输出字段都要有字数限制
- **类型提示**：使用完整的类型注解
- **文档字符串**：为所有公共方法提供清晰的文档
- **命名规范**：使用描述性的变量和方法名
- **代码组织**：保持方法简洁，复杂逻辑拆分为私有方法
- **JSON处理**：使用标准的JSON提取和解析模式

### 🚨 常见问题
1. **Agent依赖**：确保Agent正确初始化，处理初始化失败的情况
2. **适用性判断**：Agent判断逻辑要健壮，支持多种响应格式
3. **精简输出**：严格控制输出内容长度，避免冗余信息
4. **配置检查**：在`can_apply`中进行必要的配置和依赖检查
5. **文件冲突**：正确处理已存在的输出文件
6. **内存管理**：对于大文件或复杂处理，注意内存使用
7. **网络异常**：处理Agent API调用的网络异常
8. **JSON解析**：使用健壮的JSON提取和解析方法

### ✅ 验证清单
- [ ] 工具类正确继承`EnhancementTool`基类
- [ ] 所有必需的类属性都已定义
- [ ] 适用场景和不适用场景描述清晰准确（参考competitive_analysis_tool.py）
- [ ] Agent正确初始化，系统提示词强调精简输出
- [ ] `can_apply`方法集成Agent智能判断
- [ ] `_check_applicability_with_agent`方法实现健壮的适用性检查
- [ ] 所有输出JSON字段都有明确的字数限制
- [ ] `_process_with_agent`方法强调精简输出要求
- [ ] `generate_intro`方法生成精简的介绍内容
- [ ] 工具在`__init__.py`中正确导出
- [ ] 工具在`ToolRegistry`中正确注册
- [ ] 工具在`MaterialEnhancer`中正确初始化
- [ ] 工具配置开关已添加到`config/config.yaml`
- [ ] Agent适用性判断逻辑健壮，支持多种响应格式
- [ ] 精简输出要求在所有提示词中明确体现
- [ ] 错误处理和日志记录完善
- [ ] 测试验证功能正常，包括Agent判断和精简输出

## 示例参考
参考现有工具的完整实现：
- **[CompetitiveAnalysisTool](mdc:tools/enhance_tools/competitive_analysis_tool.py)** - 展示Agent智能判断和精简输出的最佳实践
- [TimelineTool](mdc:tools/enhance_tools/timeline_tool.py) - 展示内容分析和结构化输出
- [DeepInsightQATool](mdc:tools/enhance_tools/deep_insight_qa_tool.py) - 展示Agent集成和复杂数据提取
- [SixDimensionsEvaluationTool](mdc:tools/enhance_tools/six_dimensions_evaluation_tool.py) - 展示评估类工具和雷达图生成

**重点学习CompetitiveAnalysisTool的以下特性：**
- `_has_comparison_potential`方法：使用Agent判断内容是否具有对比潜力
- 精简化的JSON输出格式：所有字段都有明确的字数限制
- 精简的`generate_intro`方法：只显示最核心的信息
- 健壮的Agent响应解析：支持多种响应格式

这些示例展示了：
- 完整的Agent智能判断机制
- 严格的精简输出要求
- 优雅的错误处理机制
- 标准的配置和初始化模式
- 有效的LLM智能选择支持
- Agent数据提取的最佳实践
