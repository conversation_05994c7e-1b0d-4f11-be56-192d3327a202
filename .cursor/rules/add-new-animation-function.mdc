---
description:
globs:
alwaysApply: false
---
# 添加新动画函数开发指南

本指南详细说明如何为DSL系统添加新的动画函数，以 [animate_image.py](mdc:dsl/v2/animation_functions/animate_image.py) 为例。

## 开发步骤

### 1. 研究现有函数结构
首先查看现有动画函数的实现模式：
- 参考 [animate_side_by_side_comparison.py](mdc:dsl/v2/animation_functions/animate_side_by_side_comparison.py) 等现有函数
- 了解YAML文档格式、参数定义、DSL示例的标准结构
- 注意函数签名和错误处理模式

### 2. 创建新的动画函数文件
在 `dsl/v2/animation_functions/` 目录下创建新文件，必须包含：

**YAML格式文档头部：**
```yaml
effect: |
    简洁描述函数的作用和效果

use_cases:
    - 具体使用场景1
    - 具体使用场景2

params:
  scene:
    type: FeynmanScene
    desc: Manim场景实例（由系统自动传入）
  param_name:
    type: 参数类型
    desc: 参数描述
    required: true/false
    default: 默认值

dsl_examples:
  - type: 函数名
    params:
      param1: value1
      param2: value2

notes:
  - 重要使用说明
  - 限制和注意事项
```

**函数实现要点：**
- 使用类型提示和可选参数
- 包含详细的日志记录
- 函数开头调用 `scene.clear_current_mobj()` 清理已有对象
- 设置 `scene.current_mobj` 保存当前对象
- 优雅的错误处理和异常恢复
- 使用with scene.voiceover(narration) as tracker添加语音旁白，将动画操作放在这个with语句中

### 3. 更新模块导入
修改 [__init__.py](mdc:dsl/v2/animation_functions/__init__.py)：
- 添加新函数的import语句
- 在`__all__`列表中添加函数名

### 4. 使用DSL工具生成文档和示例
```bash
# 生成文档和示例配置
python -m scripts.dsl_tool --module 新函数名

# 仅生成文档（跳过DSL配置）
python -m scripts.dsl_tool --module 新函数名 --no-dsl
```

### 5. 测试和验证
```bash
# 生成Manim代码并渲染视频
python -m scripts.dsl_tool --module 新函数名 -r
```

会先生成 DSL 配置，然后调用 dsl.v2.dsl_to_manim 将其转成 Manim 代码，并渲染成视频。

## 重要注意事项

### 🎨 设计原则
- **风格设计**：追求简洁优雅的视觉效果
- **智能缩放**：确保内容适应不同屏幕尺寸（只在超出范围时缩小，如果原本就小于屏幕尺寸，无需放大）
- **中文支持**：标题和文本支持中文显示

### 🔧 技术要点
- **颜色处理**：使用Manim标准颜色常量，避免未定义的颜色（如CYAN问题）
- **动画时序**：合理安排动画的时间和顺序
- **错误恢复**：当核心功能失败时提供友好的错误提示
- **代码正确**：严禁杜撰不存在的Manim函数或者属性


### 📝 文档规范
- **YAML格式**：严格遵循现有的文档格式
- **示例完整**：提供多个不同复杂度的使用示例
- **参数说明**：清晰描述每个参数的作用和限制（参数中的`narration`是必须的，用于生成语音旁白）
- **使用场景**：明确说明适用的场景和用途
- **文档语言**：使用中文

### 🚨 常见问题
1. **分片编辑**：如果代码量太大，导致编辑文件时出错，可以每次编辑一部分代码，通过多次编辑完成任务。
2. **颜色常量**：确保使用的颜色在Manim中已定义
3. **类型安全**：使用适当的类型检查和默认值处理
4. **性能优化**：避免在动画循环中进行重复的复杂计算

### ✅ 验证清单
- [ ] YAML文档格式正确且完整，包括必须的`narration`参数，且文档语言为中文
- [ ] 函数在 [__init__.py](mdc:dsl/v2/animation_functions/__init__.py) 中正确导入
- [ ] DSL工具能成功提取文档和示例
- [ ] 生成的Manim代码语法正确
- [ ] 错误情况下有友好的提示信息
- [ ] 支持所有声明的参数和功能

## 示例参考
参考 [animate_image.py](mdc:dsl/v2/animation_functions/animate_image.py) 的完整实现，它展示了：
- 完整的YAML文档结构
- 优雅的Manim图形创建
- 完善的错误处理机制
- 标准的动画函数接口
