from google import genai
import os
import logging
import json
from datetime import datetime

# Configure logging
log_directory = os.getenv("LOG_DIR", "logs")
os.makedirs(log_directory, exist_ok=True)
log_file = os.path.join(log_directory, f"llm_calls_{datetime.now().strftime('%Y%m%d')}.log")

# Set up logger
logger = logging.getLogger("llm_logger")
logger.setLevel(logging.INFO)
logger.propagate = False  # Prevent propagation to root logger
file_handler = logging.FileHandler(log_file)
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
logger.addHandler(file_handler)

# Simple cache configuration
cache_file = "llm_cache.json"

# By default, we Google Gemini 2.5 pro, as it shows great performance for code understanding
def call_llm1(prompt: str, use_cache: bool = True) -> str:
    # 计算提示内容的大小
    prompt_size_bytes = len(prompt.encode('utf-8'))
    prompt_size_kb = prompt_size_bytes / 1024
    prompt_size_tokens = len(prompt.split()) * 1.3  # 粗略估计token数量
    
    # Log the prompt
    logger.info(f"PROMPT: {prompt}")
    logger.info(f"PROMPT SIZE: {prompt_size_bytes} bytes ({prompt_size_kb:.2f} KB), ~{int(prompt_size_tokens)} tokens")
    
    print(f"## 💬 调用大模型 (Gemini)\n")
    print(f"* **请求大小**: `{prompt_size_bytes}` 字节 (`{prompt_size_kb:.2f}` KB)")
    print(f"* **估计Token数**: 约 `{int(prompt_size_tokens)}` tokens")
    print(f"* **模型**: `{os.getenv('GEMINI_MODEL', 'gemini-2.5-pro-exp-03-25')}`\n")
    
    # Check cache if enabled
    if use_cache:
        # Load cache from disk
        cache = {}
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r') as f:
                    cache = json.load(f)
            except:
                logger.warning(f"Failed to load cache, starting with empty cache")
        
        # Return from cache if exists
        if prompt in cache:
            logger.info(f"RESPONSE: {cache[prompt]}")
            print("* ⚡ **缓存命中**，直接返回缓存结果\n")
            return cache[prompt]
    
    # Call the LLM if not in cache or cache disabled
    client = genai.Client(
        vertexai=True, 
        # TODO: change to your own project id and location
        project=os.getenv("GEMINI_PROJECT_ID", "your-project-id"),
        location=os.getenv("GEMINI_LOCATION", "us-central1")
    )
    # You can comment the previous line and use the AI Studio key instead:
    # client = genai.Client(
    #     api_key=os.getenv("GEMINI_API_KEY", "your-api_key"),
    # )
    model = os.getenv("GEMINI_MODEL", "gemini-2.5-pro-exp-03-25")
    print(f"* 🔄 正在调用API...\n")
    response = client.models.generate_content(
        model=model,
        contents=[prompt]
    )
    response_text = response.text
    
    # Log the response
    logger.info(f"RESPONSE: {response_text}")
    
    # Update cache if enabled
    if use_cache:
        # Load cache again to avoid overwrites
        cache = {}
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r') as f:
                    cache = json.load(f)
            except:
                pass
        
        # Add to cache and save
        cache[prompt] = response_text
        try:
            with open(cache_file, 'w') as f:
                json.dump(cache, f)
            print("* 💾 响应已保存到缓存\n")
        except Exception as e:
            logger.error(f"Failed to save cache: {e}")
            print(f"* ❌ 缓存保存失败: `{e}`\n")
    
    print(f"* ✅ API调用完成，返回结果\n")
    return response_text

# # Use Anthropic Claude 3.7 Sonnet Extended Thinking
# def call_llm(prompt, use_cache: bool = True):
#     from anthropic import Anthropic
#     client = Anthropic(api_key=os.environ.get("ANTHROPIC_API_KEY", "your-api-key"))
#     response = client.messages.create(
#         model="claude-3-7-sonnet-20250219",
#         max_tokens=21000,
#         thinking={
#             "type": "enabled",
#             "budget_tokens": 20000
#         },
#         messages=[
#             {"role": "user", "content": prompt}
#         ]
#     )
#     return response.content[1].text

# Use OpenAI o1
def call_llm(prompt, use_cache: bool = True):    
    # 计算提示内容的大小
    prompt_size_bytes = len(prompt.encode('utf-8'))
    prompt_size_kb = prompt_size_bytes / 1024
    prompt_size_tokens = len(prompt.split()) * 1.3  # 粗略估计token数量
    
    print(f"## 💬 调用大模型 (OpenRouter)\n")
    print(f"* **请求大小**: `{prompt_size_bytes}` 字节 (`{prompt_size_kb:.2f}` KB)")
    print(f"* **估计Token数**: 约 `{int(prompt_size_tokens)}` tokens")
    print(f"* **模型**: `google/gemini-2.5-flash-preview`\n")
    
    # 记录日志
    logger.info(f"PROMPT: {prompt}")
    logger.info(f"PROMPT SIZE: {prompt_size_bytes} bytes ({prompt_size_kb:.2f} KB), ~{int(prompt_size_tokens)} tokens")
    
    # 检查缓存
    if use_cache:
        cache = {}
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r') as f:
                    cache = json.load(f)
                
                if prompt in cache:
                    logger.info(f"CACHE HIT: using cached response")
                    print("* ⚡ **缓存命中**，直接返回缓存结果\n")
                    return cache[prompt]
            except Exception as e:
                logger.warning(f"Failed to load cache: {e}")
    
    # 调用API
    from openai import OpenAI
    client = OpenAI(api_key=os.environ.get("OPENROUTER_API_KEY", "your-api-key"),
                    base_url=os.environ.get("OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1"))
    
    print(f"* 🔄 正在调用API...\n")
    try:
        r = client.chat.completions.create(
            #model="google/gemini-2.5-pro-preview-03-25",
            model="google/gemini-2.5-flash-preview",
            messages=[{"role": "user", "content": prompt}],
            response_format={
                "type": "text"
            },
            reasoning_effort="medium",
            store=False
        )
        
        response_text = r.choices[0].message.content
        
        # 记录响应
        logger.info(f"RESPONSE: {response_text}")
        
        # 更新缓存
        if use_cache:
            cache = {}
            if os.path.exists(cache_file):
                try:
                    with open(cache_file, 'r') as f:
                        cache = json.load(f)
                except:
                    pass
            
            cache[prompt] = response_text
            try:
                with open(cache_file, 'w') as f:
                    json.dump(cache, f)
                print("* 💾 响应已保存到缓存\n")
            except Exception as e:
                logger.error(f"Failed to save cache: {e}")
                print(f"* ❌ 缓存保存失败: `{e}`\n")
        
        print(f"* ✅ API调用完成，返回结果\n")
        return response_text
    
    except Exception as e:
        logger.error(f"API call failed: {e}")
        print(f"### ❌ API调用失败\n\n```\n{e}\n```\n")
        return f"API调用失败: {e}"

if __name__ == "__main__":
    test_prompt = "Hello, how are you?"
    
    # First call - should hit the API
    print("## 🧪 测试API调用\n")
    response1 = call_llm(test_prompt, use_cache=False)
    print(f"### 📝 响应结果\n\n```\n{response1}\n```\n")
    
