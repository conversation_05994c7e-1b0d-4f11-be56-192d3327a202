import os
import sys
import argparse
from pathlib import Path

from evaluate_main import get_project_files  # 复用已实现的文件收集函数
from select_examples_flow import create_select_examples_flow


def main():
    """命令行入口: 从代码库筛选示例代码"""

    parser = argparse.ArgumentParser(description="在代码库中筛选并保存示例代码")
    parser.add_argument("--local_dir", required=True, help="本地代码库目录路径", type=str)
    parser.add_argument("--output_dir", default="examples", help="示例输出目录", type=str)
    parser.add_argument("--include", help="包含匹配的文件模式，如 '*.py,*.md'", type=str)
    parser.add_argument("--exclude", help="排除匹配的文件模式，如 '*.pyc'", type=str)
    parser.add_argument("--max_file_size", type=int, default=1000, help="最大文件大小 KB")
    parser.add_argument("--project_name", help="项目名称 (可选)")
    parser.add_argument("--skip_llm", action="store_true", help="跳过调用大模型，仅测试示例搜索")
    parser.add_argument("--max_examples", type=int, default=0, help="最大示例数量，0表示不限制")
    args = parser.parse_args()

    include_patterns = args.include.split(',') if args.include else [
        "*.py", "*.js", "*.ts", "*.java", "*.go", "*.rs", "*.c", "*.cpp",
        "*.h", "*.hpp", "*.md", "*.rst", "*.txt"
    ]
    exclude_patterns = args.exclude.split(',') if args.exclude else [
        "*__pycache__*", "*.pyc", "*.egg-info", "*.git*", "node_modules/*",
        "*.ipynb_checkpoints*", "build/*", "dist/*", "*.o", "*.so", "*.dylib",
        "*.dll", "*.class", "*.env", ".env"
    ]

    if not os.path.isdir(args.local_dir):
        print(f"❌ 指定的目录 {args.local_dir} 无效")
        sys.exit(1)

    # 收集文件
    files_dict = get_project_files(
        directory=args.local_dir,
        include_patterns=include_patterns,
        exclude_patterns=exclude_patterns,
        max_file_size=args.max_file_size * 1024
    )

    shared = {
        "files": [(p, c) for p, c in files_dict.items()],
        "project_name": args.project_name or os.path.basename(os.path.abspath(args.local_dir)),
        "output_dir": args.output_dir,
        "local_dir": args.local_dir,
        "skip_llm": args.skip_llm,
        "max_examples": args.max_examples,
    }

    flow = create_select_examples_flow()
    flow.run(shared)

    md_path = shared.get("selected_examples_md")
    if md_path and os.path.exists(md_path):
        print(f"\n✅ 已成功生成示例说明: {md_path}")
    else:
        print("⚠️ 未生成示例说明文件")


if __name__ == "__main__":
    main() 