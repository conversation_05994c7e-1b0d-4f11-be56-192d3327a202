import os
import yaml
import re
import sys
import json
import hashlib
from pathlib import Path
from pocketflow import Node, BatchNode
from utils.call_llm import call_llm

# 缓存目录配置
CACHE_DIR = os.environ.get("CACHE_DIR", "cache")
os.makedirs(CACHE_DIR, exist_ok=True)

# 辅助函数，获取特定文件索引的内容
def get_content_for_indices(files_data, indices):
    content_map = {}
    for i in indices:
        if 0 <= i < len(files_data):
            path, content = files_data[i]
            content_map[f"{i} # {path}"] = content # 使用索引+路径作为上下文的键
    return content_map

# 定义用于生成示例代码的提示模板
GENERATE_EXAMPLES_PROMPT = """
对于项目 `{project_name}`，请创建3-5个能体现项目实际应用价值的 Python 代码示例。
示例应侧重于解决实际问题或展示真实世界的使用流程。

项目概述:
{project_summary}

核心功能:
{core_features_text}

API接口:
{api_interfaces_text}

核心组件:
{core_components_text}

相关代码片段 (用于上下文参考):
{simplified_content}

---

**代码库中找到的潜在示例 (供参考和评估):**
{found_examples_section}

**质量要求（对所有示例强制执行）:**

以下质量要求对**所有**最终提供的示例都必须严格执行，无论是从现有代码重构的还是新生成的：

- **通俗易懂**: 示例必须确保非技术/非开发人员也能理解其价值和用途
- **应用广泛**: 必须选择广泛适用的、有代表性的使用场景，避免过于专业或小众的应用
- **价值明确**: 必须清晰地展示项目如何解决实际问题或提高效率
- **注释充分**: 必须提供足够的注释和解释，帮助各类用户理解代码的工作原理
- **场景多样化**: 不同示例应涵盖不同难度和应用领域，从简单入门到高级应用

**新增技术要求:**

- **真实调用**: 所有示例必须包含真实的API调用或功能调用，不允许使用"模拟"的示例代码。每个示例应该能够在正确环境下实际运行并产生结果。
- **环境检查**: 每个示例必须包含环境就绪检查代码，用于验证所需的依赖项、配置文件、环境变量等是否已正确设置，并给出友好的错误提示。
- **错误处理**: 必须包含适当的错误处理代码，捕获可能的异常并提供清晰的错误信息。
- **输入验证**: 对用户提供的输入参数进行验证，确保非法输入被正确处理。
- **资源释放**: 确保正确释放资源（如文件句柄、网络连接等），避免资源泄漏。

**任务:**

1.  **评估与筛选:** 请严格评估上面提供的"潜在示例代码"。**只有当它们满足上述所有质量要求，且清晰地展示了项目的实际应用价值、解决了真实世界的问题时**，才考虑对其进行修改和格式化。请遵循 **"宁缺毋滥"** 的原则：如果找到的示例质量不高或不符合应用角度，请**直接忽略它们**，不要包含在最终输出中。

2.  **生成高质量新示例:** 无论是否筛选出可用的现有示例，请**务必生成全新的、高质量的应用示例**。这些新示例必须严格满足上述所有质量要求，不得有任何折扣。每个示例都应包含环境就绪检查和实际的API/功能调用代码。

3.  **场景多样化:** 确保最终的示例集合涵盖不同难度和应用领域，从简单入门到高级应用，确保不同背景和需求的用户都能找到适合的参考。

4.  **统一格式:** 所有最终输出的示例（无论是筛选重构的还是新生成的）都必须严格遵循以下 Markdown 格式，确保最终总共有 **3-5 个符合所有质量要求的、高质量的应用示例**。

**输出格式 (Markdown):**

```markdown
# {project_name} 应用示例

## 示例 1: <示例标题 - 用通俗语言清晰说明应用场景>

**描述:**
<详细描述该示例的目的和价值，以非技术语言解释它解决了什么实际问题。写得让非开发人员也能理解这个示例的价值...>

**代码:**
```python
# 完整的Python代码
# 包含环境就绪检查
import sys
import os

# 环境就绪检查函数
def check_environment():
    \"\"\"检查运行环境是否就绪\"\"\"
    # 检查必要的依赖项
    required_packages = ['package1', 'package2']
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"错误: 缺少必要的依赖项: {{', '.join(missing_packages)}}")
        print(f"请运行: pip install {{' '.join(missing_packages)}}")
        return False
    
    # 检查必要的环境变量
    required_env_vars = ['API_KEY', 'OTHER_CONFIG']
    missing_env_vars = []
    for var in required_env_vars:
        if not os.environ.get(var):
            missing_env_vars.append(var)
    
    if missing_env_vars:
        print(f"错误: 缺少必要的环境变量: {{', '.join(missing_env_vars)}}")
        print("请设置所需的环境变量后再运行")
        return False
    
    # 检查必要的配置文件
    if not os.path.exists("config.json"):
        print("错误: 缺少配置文件 config.json")
        print("请确保配置文件存在并包含正确的设置")
        return False
    
    return True

# 主函数
def main():
    # 首先检查环境
    if not check_environment():
        sys.exit(1)
    
    # 实际的API调用和功能实现
    # ...
    
    # 一定要包含真实的API调用/功能调用代码
    # ...
    
    # 合理的错误处理
    try:
        # 核心功能调用
        pass
    except Exception as e:
        print(f"执行过程中出错: {{e}}")
        sys.exit(1)

if __name__ == "__main__":
    main()
```

**运行方法:**
```bash
# 运行示例前的准备步骤
pip install -r requirements.txt
export API_KEY=your_api_key_here

# 运行示例
python example1.py
```

**预期输出或效果:**
<用通俗语言清晰说明运行代码后的预期结果或达成的效果>

**适用场景:**
<简述此示例适用的广泛场景或行业>

---

## 示例 2: <示例标题 - 用通俗语言清晰说明应用场景>

**描述:**
<...>

**代码:**
```python
# ...包含环境就绪检查和真实API调用...
```

**运行方法:**
```bash
# ...
```

**预期输出或效果:**
<...>

**适用场景:**
<...>

---
(根据需要生成更多示例，总数保持在 3-5 个)
```
"""

class AnalyzeCodebase(Node):
    """分析代码库结构和核心功能"""
    
    def prep(self, shared):
        """准备阶段：收集需要分析的代码文件"""
        files_data = shared["files"]
        project_name = shared["project_name"]
        print(f"## 🔍 分析项目：**{project_name}**\n")
        # 优先提取README和文档文件
        readme_files = []
        doc_files = []
        code_files = []
        
        # 文档文件的优先级排序
        doc_patterns = [
            r'README\.md$', r'README\.txt$', r'README$',  # 各类README文件最优先
            r'documentation/.*\.md$', r'docs/.*\.md$',     # 文档目录下的Markdown文件
            r'OVERVIEW\.md$', r'ARCHITECTURE\.md$',        # 项目概览和架构文档
            r'GUIDE\.md$', r'TUTORIAL\.md$',               # 使用指南和教程
            r'API\.md$', r'API_REFERENCE\.md$',            # API参考文档
            r'FEATURES\.md$', r'CAPABILITIES\.md$',        # 功能和能力文档
            r'\.rst$', r'\.txt$', r'\.md$'                 # 其他文档文件
        ]
        
        # 定义可能与核心功能相关的文件模式
        core_code_patterns = [
            r'core/.*\.(py|js|java|go|rb|rs|cpp|c|ts)$',    # core目录下的代码文件
            r'src/.*\.(py|js|java|go|rb|rs|cpp|c|ts)$',     # src目录下的代码文件
            r'lib/.*\.(py|js|java|go|rb|rs|cpp|c|ts)$',     # lib目录下的代码文件
            r'api/.*\.(py|js|java|go|rb|rs|cpp|c|ts)$',     # api目录下的代码文件
            r'models/.*\.(py|js|java|go|rb|rs|cpp|c|ts)$',  # models目录下的代码文件
            r'controllers/.*\.(py|js|java|go|rb|rs|cpp|c|ts)$', # controllers目录
            r'services/.*\.(py|js|java|go|rb|rs|cpp|c|ts)$', # services目录
            r'main\.(py|js|java|go|rb|rs|cpp|c|ts)$',       # main文件
            r'index\.(py|js|java|go|rb|rs|cpp|c|ts)$',      # index文件
            r'app\.(py|js|java|go|rb|rs|cpp|c|ts)$',        # app文件
            r'config\.(py|js|java|go|rb|rs|cpp|c|ts|yml|yaml|json)$', # 配置文件
            r'\./.*',                                    # 所有以"."开头的目录
        ]
        
        # 排除模式 - 这些文件通常不包含核心功能
        exclude_patterns = [
            r'test/.*', r'tests/.*', r'__tests__/.*',     # 测试文件
            r'\.git/.*', r'node_modules/.*', r'venv/.*',  # 外部依赖和环境
            r'.*\.log$', r'logs/.*',                      # 日志文件
            r'.*\.md$', r'.*\.txt$',                      # 文本文档（已在doc_patterns中处理）
            r'\.DS_Store$', r'Thumbs\.db$',              # 系统文件
            r'.*\.min\.(js|css)$',                       # 压缩版文件
            r'dist/.*', r'build/.*',                     # 构建输出目录
            r'__pycache__/.*', r'.*\.pyc$',              # Python缓存
            r'\./.*',                                    # 所有以"."开头的目录
        ]

        # 分类所有文件
        print("\n### 📁 筛选核心文件\n")
        print("> 注意：所有以'.'开头的文件夹内的文件将被忽略\n")
        core_code_selected = []
        dot_dir_files_skipped = 0
        
        for i, (path, content) in enumerate(files_data):
            path_lower = path.lower()
            
            # 检查文件是否在以"."开头的目录中
            if any(part.startswith('.') for part in path.split(os.sep) if part):
                dot_dir_files_skipped += 1
                continue
                
            # 检查是否是README文件
            if re.search(r'readme\.md$', path_lower) or re.search(r'readme\.txt$', path_lower) or path_lower == 'readme':
                readme_files.append((i, path, content))
                print(f"* 📘 **README文件**: `{path}`")
            # 检查是否是其他文档文件
            elif any(re.search(pattern, path, re.IGNORECASE) for pattern in doc_patterns):
                doc_files.append((i, path, content))
                print(f"* 📄 **文档文件**: `{path}`")
            # 筛选核心代码文件
            elif any(re.search(pattern, path, re.IGNORECASE) for pattern in core_code_patterns) and not any(re.search(pattern, path, re.IGNORECASE) for pattern in exclude_patterns):
                code_files.append((i, path, content))
                core_code_selected.append((i, path))
                print(f"* 💻 **核心代码文件**: `{path}`")
            else:
                # 不打印被排除的文件，减少输出
                pass
        
        print(f"\n### 📊 文件分类结果\n")
        print(f"* **README文件**: {len(readme_files)} 个")
        print(f"* **文档文件**: {len(doc_files)} 个") 
        print(f"* **核心代码文件**: {len(code_files)} 个")
        print(f"* **点目录跳过文件**: {dot_dir_files_skipped} 个\n")
        
        # 创建基于文件的上下文，优先放置README和文档文件
        def create_llm_context(readme_files, doc_files, code_files):
            context = "# 项目文档文件\n\n"
            file_info = []  # 存储(索引, 路径)元组
            
            # 首先添加README文件
            if readme_files:
                context += "## README文件\n\n"
                for idx, path, content in readme_files:
                    entry = f"--- 文件: {path} ---\n{content}\n\n"
                    context += entry
                    file_info.append((idx, path))
            
            # 然后添加其他文档文件
            if doc_files:
                context += "## 其他文档文件\n\n"
                for idx, path, content in doc_files:
                    entry = f"--- 文件: {path} ---\n{content}\n\n"
                    context += entry
                    file_info.append((idx, path))
            
            # 最后添加代码文件
            context += "# 项目代码文件\n\n"
            for idx, path, content in code_files:
                entry = f"--- 文件索引 {idx}: {path} ---\n{content}\n\n"
                context += entry
                file_info.append((idx, path))
            
            return context, file_info
        
        context, file_info = create_llm_context(readme_files, doc_files, [(i, path, content) for i, (path, content) in enumerate(files_data) if (i, path, content) not in readme_files and (i, path, content) not in doc_files])
        file_listing_for_prompt = "\n".join([f"- {idx} # {path}" for idx, path in file_info])
        
        # 输出日志，说明找到的关键文档
        if readme_files:
            print(f"### 📘 README文件\n")
            for _, path, _ in readme_files:
                print(f"* `{path}`")
        else:
            print("### ⚠️ 警告\n\n未找到README文件，这可能影响对项目的理解\n")
            
        if doc_files:
            print(f"\n### 📄 其他文档文件\n")
            for i, (_, path, _) in enumerate(doc_files[:5]):  # 只打印前5个，避免过多输出
                print(f"* `{path}`")
            if len(doc_files) > 5:
                print(f"\n> ...及其他 {len(doc_files)-5} 个文档文件\n")
        
        skip_llm = shared.get("skip_llm", False)

        return context, file_listing_for_prompt, len(files_data), project_name, skip_llm
    
    def exec(self, prep_res):
        """执行阶段：使用LLM分析代码库，支持 skip_llm 标志直接跳过"""
        context, file_listing_for_prompt, file_count, project_name, skip_llm = prep_res
        
        # 如果跳过 LLM，直接返回最简分析结果
        if skip_llm:
            print("* ℹ️ skip_llm=True，跳过 AnalyzeCodebase 的大模型调用")
            return {
                "project_summary": "",
                "core_features": [],
                "api_interfaces": [],
                "core_components": [],
                "potential_example_files": []
            }
        
        # 提取README内容作为缓存键的一部分
        readme_content = ""
        readme_match = re.search(r"--- 文件: (.*?README.*?) ---\n(.*?)\n\n", context, re.DOTALL | re.IGNORECASE)
        if readme_match:
            readme_path = readme_match.group(1)
            readme_content = readme_match.group(2)[:1000]  # 取前1000个字符作为哈希计算基础
            print(f"\n### 💾 缓存策略\n\n* 找到README文件 `{readme_path}` 并将其内容纳入缓存计算")
        
        # 生成缓存文件名
        # 使用项目名称、文件列表和README内容的哈希值作为缓存文件名
        cache_key = f"{project_name}_{file_listing_for_prompt}_{readme_content}"
        files_hash = hashlib.md5(cache_key.encode()).hexdigest()
        cache_filename = f"{project_name}_{files_hash}_analysis.json"
        cache_path = Path(CACHE_DIR) / cache_filename
        
        # 检查是否有缓存
        if cache_path.exists():
            print(f"\n### ⏱️ 缓存加载\n\n* 找到缓存的代码库分析结果，正在加载...\n")
            try:
                with open(cache_path, "r", encoding="utf-8") as f:
                    analysis_result = json.load(f)
                
                # 检查缓存的分析结果是否应该更新
                # 这里可以添加验证代码，确保缓存结果中的文件引用是有效的
                cache_is_valid = True
                
                # 验证潜在示例文件是否存在
                if "potential_example_files" in analysis_result:
                    file_paths = [match.split(" # ")[1] for match in file_listing_for_prompt.split("\n") if " # " in match]
                    file_path_set = set(file_paths)
                    
                    # 过滤掉不存在的示例文件
                    valid_examples = []
                    invalid_count = 0
                    for example in analysis_result["potential_example_files"]:
                        if "path" in example and example["path"] in file_path_set:
                            valid_examples.append(example)
                        else:
                            invalid_count += 1
                    
                    if invalid_count > 0:
                        print(f"* ⚠️ 缓存中有 {invalid_count} 个潜在示例文件在当前代码库中不存在，已忽略")
                        analysis_result["potential_example_files"] = valid_examples
                        
                        # 如果缓存中的所有示例都无效，则强制重新分析
                        if not valid_examples and analysis_result["potential_example_files"]:
                            print("* 🔄 缓存中的所有示例文件均无效，将重新分析代码库")
                            cache_is_valid = False
                
                if cache_is_valid:
                    print(f"* ✅ 代码库分析结果加载完成，识别出 {len(analysis_result.get('core_features', []))} 个核心功能\n")
                    return analysis_result
                else:
                    print("* ❌ 缓存结果无效，将重新分析代码库\n")
            except Exception as e:
                print(f"* ❌ 加载缓存分析结果失败: {e}，将重新分析代码库...\n")
        
        print(f"\n## 🧠 使用大模型分析代码库...\n")
        
        # 可以重用现有的ANALYZE_CODEBASE_PROMPT或创建新的简化版本
        prompt = """
对于项目 `{project_name}`：

代码库上下文已按以下顺序提供：
1. 首先是README文件和重要文档，包含项目概述、核心功能和使用说明
2. 然后是代码文件

请仔细阅读README和文档文件部分，这些是理解项目本质的关键。
然后结合代码文件进行综合分析，识别项目的核心功能和价值。

代码库上下文:
{context}

请深入分析这个代码库，重点关注README.md和文档文件中的内容，提炼出以下关键信息：

1. **项目概述**：根据README和文档，全面概述该项目的目的、解决的问题和核心价值，约200-300字。
   - 用通俗的语言解释项目是做什么的
   - 项目针对哪些用户群体或使用场景
   - 项目的主要价值主张是什么

2. **核心能力**：主要从README和文档提取，并结合代码验证，列出该项目提供的3-8个关键能力，按重要性排序。对每个能力，提供：
   - 能力名称（简洁明了）
   - 能力描述（100字左右，使用通俗易懂的语言）
   - 实现该能力的关键文件索引列表（如果可以从代码中识别）
   - 尽可能引用README或文档中对此能力的原始描述

3. **API接口**：识别项目的主要API接口或使用方式，特别关注文档中明确描述的接口。包括：
   - API名称/使用方式名称
   - 接口功能简短描述
   - 参数和返回值（如果文档中有提供）
   - 相关文件索引（如果可以从代码中识别）
   - 文档中对该API的相关说明或示例

4. **核心组件**：分析项目的核心组件/类/模块，特别关注文档中明确描述的架构。提供：
   - 组件名称
   - 组件职责（用通俗语言描述）
   - 组件之间的关系和依赖
   - 相关文件索引（如果可以从代码中识别）

5. **潜在示例文件**：识别代码库中可能包含独立、可运行的代码示例的文件。**特别注意查找以下位置**：
    - cookbook/ 或 cookbooks/ 文件夹中的文件（最优先）
    - recipes/ 文件夹中的文件
    - examples/ 文件夹中的文件
    - 文件名中含有 example, demo, tutorial, sample 等关键词的文件
    - tests/ 或 testing/ 目录下可能作为使用示例的测试文件
    - README或其他文档中提到的示例代码
    
    对于每个找到的潜在示例文件，提供：
    - 文件索引
    - 文件路径 (从索引列表中获取)
    - 简要说明为什么认为它是示例文件
    - 评估其质量（高/中/低）以及是否符合"应用角度的真实案例"标准

文件索引和路径列表:
{file_listing_for_prompt}

以严格的YAML格式输出结果，确保所有字符串值使用引号包裹，特别是多行文本使用 | 符号并正确缩进：

```yaml
project_summary: |
  "全面、准确的项目概述，清晰描述其目的、特点、应用场景和价值..."

core_features:
  - name: "核心能力名称"
    description: "这个能力的详细描述，说明其功能和价值..."
    doc_reference: "来自项目文档的相关引用或描述（如有）..."
    file_indices:
      - 0 # 相关文件路径
      - 3 # 相关文件路径

api_interfaces:
  - name: "API名称或接口名称"
    description: "API的功能描述"
    parameters: "输入参数详细说明"
    returns: "返回值详细说明"
    doc_reference: "来自文档的API说明引用（如有）..."
    file_indices:
      - 1 # 相关文件路径（如果可从代码中识别）

core_components:
  - name: "组件名称"
    responsibility: "组件主要职责和功能描述"
    relationships: "与其他组件的关系和交互方式"
    file_indices:
      - 2 # 相关文件路径（如果可从代码中识别）

potential_example_files:
  - file_index: 5 # 示例文件索引
    path: "examples/simple_usage.py" # 示例文件路径
    reason: "位于 examples/ 目录下，文件名暗示是简单用法示例。" # 识别原因
    quality: "高" # 质量评估：高/中/低
    real_world_application: true # 是否符合真实应用案例标准
```

在分析过程中，请特别注意以下几点：
1. 对README.md文件中的内容给予最高优先级
2. 其次重视docs/目录下的文档文件
3. 确保提取的核心能力、API接口和组件是项目真正核心的部分，而不是次要特性
4. 如果文档中明确描述了项目架构或组件关系，请优先使用这些信息
5. 对于难以从代码中识别的文件索引，可以留空或标记为无法确定
6. 确保输出的YAML格式严格正确，所有字符串都应该用引号包裹，特别是包含特殊字符的字符串
""".format(
    project_name=project_name,
    context=context,
    file_listing_for_prompt=file_listing_for_prompt
)
        
        response = call_llm(prompt)
        
        # 验证和解析响应
        try:
            yaml_match = re.search(r"```yaml(.*?)```", response, re.DOTALL)
            if not yaml_match:
                print("### ⚠️ 警告\n\n无法从LLM响应中提取YAML内容。尝试使用整个响应作为YAML...\n")
                yaml_str = response.strip()
            else:
                yaml_str = yaml_match.group(1).strip()
            
            # 尝试解析YAML
            try:
                analysis_result = yaml.safe_load(yaml_str)
            except Exception as yaml_error:
                print(f"### ❌ YAML解析错误\n\n```\n{str(yaml_error)}\n```\n")
                
                # 尝试修复常见的YAML问题
                print("### 🔧 尝试修复YAML格式...\n")
                
                # 1. 尝试修复多行字符串的缩进问题
                fixed_yaml = re.sub(r'(\s+)(\w+): \|\n(\s+)([^\n]+)', r'\1\2: |\n\1  \4', yaml_str)
                
                # 2. 确保所有冒号后面有空格
                fixed_yaml = re.sub(r'(\w+):([^\s|])', r'\1: \2', fixed_yaml)
                
                # 3. 尝试为没有引号的字符串添加引号，特别是包含特殊字符的字符串
                lines = fixed_yaml.split('\n')
                for i in range(len(lines)):
                    if ': ' in lines[i] and not (': |' in lines[i] or ': >' in lines[i] or ': [' in lines[i] or ': {' in lines[i]):
                        key, value = lines[i].split(': ', 1)
                        if value and not (value.startswith('"') or value.startswith("'")) and not value.isdigit() and value.lower() not in ['true', 'false', 'null']:
                            # 如果值包含特殊字符，添加双引号
                            if any(char in value for char in ':/,[]{}#&*!%|<>?`'):
                                lines[i] = f'{key}: "{value}"'
                
                fixed_yaml = '\n'.join(lines)
                
                try:
                    analysis_result = yaml.safe_load(fixed_yaml)
                    print("* ✅ YAML修复成功！\n")
                except Exception as fixed_yaml_error:
                    print(f"* ❌ YAML修复失败: {str(fixed_yaml_error)}\n")
                    # 作为最后的手段，创建一个基本的结构
                    print("* 🔄 创建基本分析结果结构...\n")
                    analysis_result = {
                        "project_summary": "无法解析LLM返回的YAML结果。这是一个自动生成的基本摘要。",
                        "core_features": [],
                        "api_interfaces": [],
                        "core_components": [],
                        "potential_example_files": []
                    }
        except Exception as e:
            print(f"### ❌ 解析LLM响应时出错: {e}")
            # 创建一个基本的结构
            analysis_result = {
                "project_summary": "处理LLM响应时出错。这是一个自动生成的基本摘要。",
                "core_features": [],
                "api_interfaces": [],
                "core_components": [],
                "potential_example_files": []
            }
        
        # 基本验证
        required_keys = ["core_features", "api_interfaces", "core_components", "project_summary", "potential_example_files"]
        for key in required_keys:
            if key not in analysis_result:
                if key == "potential_example_files":
                    print(f"* ⚠️ LLM输出缺少可选部分: `{key}`，将视为空列表。")
                    analysis_result[key] = []
                else:
                    print(f"* ⚠️ LLM输出缺少关键部分: `{key}`，将创建空值。")
                    if key == "project_summary":
                        analysis_result[key] = "未提供项目摘要。"
                    else:
                        analysis_result[key] = []
        
        # 验证 potential_example_files 结构 (可选，增加健壮性)
        if "potential_example_files" in analysis_result and isinstance(analysis_result["potential_example_files"], list):
            for item in analysis_result["potential_example_files"]:
                if not isinstance(item, dict) or not all(k in item for k in ["file_index", "path", "reason"]):
                    print(f"* ⚠️ potential_example_files 中存在格式不正确的项: {item}，将忽略此项。")
            # 清理掉格式不正确的项
            analysis_result["potential_example_files"] = [
                item for item in analysis_result["potential_example_files"]
                if isinstance(item, dict) and all(k in item for k in ["file_index", "path", "reason"])
            ]
        elif "potential_example_files" in analysis_result:
             print(f"* ⚠️ potential_example_files 格式不正确（应为列表），将视为空列表。")
             analysis_result["potential_example_files"] = []
        
        # 保存分析结果到缓存文件
        try:
            with open(cache_path, "w", encoding="utf-8") as f:
                json.dump(analysis_result, f, ensure_ascii=False, indent=2)
            print(f"* 💾 代码库分析结果已保存到缓存: `{cache_path}`")
        except Exception as e:
            print(f"* ❌ 保存分析结果到缓存失败: {e}")
        
        print(f"\n### 📊 分析完成\n\n* 识别出 **{len(analysis_result['core_features'])}** 个核心功能")
        return analysis_result
    
    def post(self, shared, prep_res, exec_res):
        """后处理阶段：保存分析结果到共享数据中"""
        # 验证分析结果中的文件引用
        print("\n## 🧪 验证分析结果\n")
        files_data = shared["files"]
        file_paths = [path for path, _ in files_data]
        
        # 确保潜在示例文件的路径存在
        if "potential_example_files" in exec_res and isinstance(exec_res["potential_example_files"], list):
            validated_examples = []
            for example in exec_res["potential_example_files"]:
                if "path" in example and example["path"] in file_paths:
                    validated_examples.append(example)
                elif "path" in example:
                    print(f"* ⚠️ 分析结果中的示例文件 '`{example['path']}`' 不存在于代码库中，将被忽略")
            
            # 更新分析结果
            exec_res["potential_example_files"] = validated_examples
            print(f"\n* ✅ 验证完成: 原有 {len(exec_res.get('potential_example_files', []))} 个潜在示例中，有效的示例文件数量为 {len(validated_examples)}\n")
        
        shared["codebase_analysis"] = exec_res
        
        # 如果项目名称为None，则从分析结果中推导
        if shared["project_name"] is None and "project_name" in exec_res:
            shared["project_name"] = exec_res["project_name"]
            print(f"* 🔍 已从分析结果推导项目名称: **{shared['project_name']}**")
        elif shared["project_name"] is None:
            # 如果分析结果中也没有项目名称，则设置为默认值
            shared["project_name"] = "unknown_project"
            print(f"* ⚠️ 无法推导项目名称，使用默认名称: **{shared['project_name']}**")

class GenerateExampleCode(Node):
    """生成项目核心功能的示例代码"""
    
    def _actively_search_cookbook_examples(self, files_data):
        """主动搜索cookbook文件夹中的示例"""
        cookbook_examples = []
        cookbook_prefixes = [
            "cookbook/", "cookbooks/", "recipes/", "examples/", 
            "samples/", "tutorials/", "demo/", "demos/"
        ]
        
        print("\n### 🔍 主动搜索示例文件\n")
        
        found_count = 0
        # 遍历所有文件，查找位于cookbook或其他示例目录中的文件
        for idx, (path, content) in enumerate(files_data):
            path_lower = path.lower()
            
            # 增强路径检查 - 考虑子目录情况和拼写变化
            # 检查是否在示例目录中
            is_in_example_dir = False
            # 1. 直接前缀检查
            if any(path_lower.startswith(prefix) for prefix in cookbook_prefixes):
                is_in_example_dir = True
            # 2. 路径部分匹配检查 - 处理如docs/examples/或src/demo/等情况
            if not is_in_example_dir and '/' in path_lower:
                path_parts = path_lower.split('/')
                for part in path_parts:
                    if part in ["cookbook", "cookbooks", "recipes", "examples", "samples", "tutorials", "demo", "demos"]:
                        is_in_example_dir = True
                        break
            
            # 检查是否是示例文件命名 - 扩展关键词集
            example_keywords = ["example", "sample", "demo", "tutorial", "howto", "quickstart", "usage", "getting_started", "get_started"]
            is_example_file = False
            
            # 1. 整个文件名包含关键词
            if any(keyword in path_lower for keyword in example_keywords):
                is_example_file = True
            # 2. 文件名部分匹配 (如example_file.py, using_api_demo.js等)
            if not is_example_file:
                file_name = os.path.basename(path_lower)
                if any(keyword in file_name for keyword in example_keywords):
                    is_example_file = True
            
            # 检查文件内容是否暗示这是示例文件
            is_example_content = False
            first_lines = content.split('\n')[:20]  # 只检查前20行
            example_content_indicators = [
                "example", "demo", "tutorial", "sample usage", "usage example", 
                "how to use", "quickstart", "getting started", "for demonstration"
            ]
            content_start = '\n'.join(first_lines).lower()
            if any(indicator in content_start for indicator in example_content_indicators):
                is_example_content = True
            
            if is_in_example_dir or is_example_file or is_example_content:
                # 创建一个示例信息对象，根据发现方式设置质量级别
                quality = "高"
                reason_parts = []
                
                if is_in_example_dir:
                    reason_parts.append("位于示例目录中")
                    # cookbook和recipes目录优先级最高
                    if "cookbook" in path_lower or "recipe" in path_lower:
                        quality = "高"
                    else:
                        quality = "中"
                
                if is_example_file:
                    reason_parts.append("文件名暗示是示例")
                
                if is_example_content:
                    reason_parts.append("文件内容表明是示例代码")
                    quality = "高" # 如果文件内容明确指出是示例，提高质量评级
                
                reason = f"主动发现：{', '.join(reason_parts)}"
                
                example_info = {
                    "file_index": idx,
                    "path": path,
                    "reason": reason,
                    "quality": quality,
                    "real_world_application": True  # 假设示例文件符合真实应用标准
                }
                cookbook_examples.append(example_info)
                found_count += 1
                print(f"* 发现示例文件: `{path}` ({reason})")
        
        print(f"\n* ✅ 主动搜索找到 **{found_count}** 个潜在示例文件\n")
        return cookbook_examples
    
    def prep(self, shared):
        """准备阶段：获取代码库分析结果和潜在示例文件"""
        codebase_analysis = shared["codebase_analysis"]
        project_name = shared["project_name"]
        files_data = shared["files"]
        local_dir = shared.get("local_dir", "")
        
        # 从分析结果中提取关键信息
        core_features = codebase_analysis.get("core_features", [])
        api_interfaces = codebase_analysis.get("api_interfaces", [])
        core_components = codebase_analysis.get("core_components", [])
        project_summary = codebase_analysis.get("project_summary", "")
        potential_example_files_info = codebase_analysis.get("potential_example_files", []) # 获取潜在示例文件信息
        
        print(f"\n## 🔍 准备生成示例代码\n")
        print(f"* **项目名称**: {project_name}")
        print(f"* **核心功能数**: {len(core_features)}")
        print(f"* **API接口数**: {len(api_interfaces)}")
        print(f"* **核心组件数**: {len(core_components)}\n")
        
        # 主动搜索cookbook等目录中的示例文件
        active_search_examples = self._actively_search_cookbook_examples(files_data)
        
        # 将主动搜索结果与LLM分析结果合并
        # 先构建路径集合，避免重复
        existing_paths = {ex.get("path") for ex in potential_example_files_info if "path" in ex}
        
        # 添加非重复的主动搜索结果
        for example in active_search_examples:
            if example["path"] not in existing_paths:
                potential_example_files_info.append(example)
                existing_paths.add(example["path"])
        
        # 格式化核心功能文本
        core_features_text = "\n".join([
            f"{i+1}. {feature['name']}: {feature['description']}"
            for i, feature in enumerate(core_features)
        ])
        
        # 格式化API接口文本
        api_interfaces_text = "\n".join([
            f"{i+1}. {api['name']}: {api['description']}\n   参数: {api['parameters']}\n   返回: {api['returns']}"
            for i, api in enumerate(api_interfaces)
        ])
        
        # 格式化核心组件文本
        core_components_text = "\n".join([
            f"{i+1}. {component['name']}: {component['responsibility']}\n   关系: {component['relationships']}"
            for i, component in enumerate(core_components)
        ])
        
        # 收集所有相关文件索引 (用于简化内容展示，不是示例文件)
        all_indices = set()
        for group in [core_features, api_interfaces, core_components]:
            for item in group:
                indices = item.get("file_indices", [])
                if indices is not None:  # 添加检查确保索引不是None
                     # 确保索引是整数
                    valid_indices = [idx for idx in indices if isinstance(idx, int)]
                    all_indices.update(valid_indices)
        
        # 获取相关文件内容 (用于简化内容展示)
        related_files_content_map = get_content_for_indices(files_data, sorted(list(all_indices)))
        
        # 创建简化版代码内容 (用于简化内容展示)
        simplified_content = "\n\n".join([
            f"--- {idx_path} ---\n{content[:1000]}...(截断)"  # 使用前1000个字符
            for idx_path, content in related_files_content_map.items()
        ])

        # 处理潜在的真实示例文件 - 收集原始内容
        found_example_sources = []
        if potential_example_files_info:
            print(f"### 📋 验证潜在示例文件\n")
            print(f"* 找到 **{len(potential_example_files_info)}** 个潜在的真实示例文件，进行验证...\n")
            
            # 创建已知文件路径集合，用于验证
            # 改进路径处理方式，更具容错性
            known_file_paths = set()
            for path, _ in files_data:
                # 将路径添加到集合中（原始路径）
                known_file_paths.add(path)
                # 添加小写版本用于不区分大小写的比较
                known_file_paths.add(path.lower())
                # 考虑相对路径情况
                if local_dir and not path.startswith('/'):
                    # 将相对路径转换为绝对路径
                    abs_path = os.path.join(local_dir, path)
                    known_file_paths.add(abs_path)
                    known_file_paths.add(abs_path.lower())
                # 如果是绝对路径，也添加相对于local_dir的路径形式
                elif local_dir and path.startswith('/'):
                    try:
                        rel_path = os.path.relpath(path, local_dir)
                        known_file_paths.add(rel_path)
                        known_file_paths.add(rel_path.lower())
                    except ValueError:
                        # 如果路径在不同驱动器上会抛出ValueError
                        pass

            verified_examples = []
            
            # 先验证文件是否真实存在
            for example_info in potential_example_files_info:
                path = example_info.get("path", "")
                path_lower = path.lower() if path else ""
                
                if path in known_file_paths or path_lower in known_file_paths:
                    verified_examples.append(example_info)
                # 检查绝对路径和相对路径形式
                elif local_dir and not path.startswith('/'):
                    abs_path = os.path.join(local_dir, path)
                    if abs_path in known_file_paths or abs_path.lower() in known_file_paths:
                        verified_examples.append(example_info)
                    else:
                        print(f"* ⚠️ 跳过不存在的示例文件: `{path}`")
                elif local_dir and path.startswith('/'):
                    try:
                        rel_path = os.path.relpath(path, local_dir)
                        if rel_path in known_file_paths or rel_path.lower() in known_file_paths:
                            verified_examples.append(example_info)
                        else:
                            print(f"* ⚠️ 跳过不存在的示例文件: `{path}`")
                    except ValueError:
                        print(f"* ⚠️ 跳过不存在的示例文件 (无法计算相对路径): `{path}`")
                else:
                    print(f"* ⚠️ 跳过不存在的示例文件: `{path}`")
            
            if not verified_examples:
                print("\n* ⚠️ 所有潜在示例文件均不存在于当前代码库中。将由大模型生成全新示例。\n")
                potential_example_files_info = []
            else:
                print(f"\n* ✅ 验证完成: 在 {len(potential_example_files_info)} 个潜在示例中，有 {len(verified_examples)} 个确实存在。\n")
                potential_example_files_info = verified_examples
            
            # 根据quality和real_world_application对示例进行排序
            # 优先选择高质量且符合真实应用场景的示例
            if potential_example_files_info:
                sorted_examples = sorted(
                    potential_example_files_info,
                    key=lambda x: (
                        # 优先使用cookbook目录下的文件
                        "cookbook" in str(x.get("path", "")).lower(),
                        # 然后是quality评级
                        x.get("quality", "").lower() == "高",
                        # 然后是是否符合真实应用场景
                        x.get("real_world_application", False) is True
                    ),
                    reverse=True  # 降序排列，最符合条件的排在前面
                )
                
                print(f"### 📊 示例文件排序\n")
                print(f"* 示例已按优先级排序，将处理前 **{min(10, len(sorted_examples))}** 个高质量示例\n")
                
                print("| 序号 | 示例文件 | 质量评级 | 真实应用 |")
                print("|------|----------|----------|----------|")
                
                for i, example_info in enumerate(sorted_examples[:10]):  # 最多处理前10个优质示例
                    idx = example_info.get("file_index")
                    path = example_info.get("path")
                    reason = example_info.get("reason")
                    quality = example_info.get("quality", "未知")
                    real_world = "✅" if example_info.get("real_world_application", False) else "❌"
                    
                    print(f"| {i+1} | `{path}` | {quality} | {real_world} |")
                    
                    if idx is not None and 0 <= idx < len(files_data):
                        example_content = files_data[idx][1]
                        found_example_sources.append({
                            "path": path,
                            "content": example_content,
                            "reason": reason,
                            "quality": quality,
                            "real_world_application": real_world
                        })
                    else:
                        print(f"\n* ⚠️ 跳过无效的潜在示例文件索引 {idx} 或路径 {path}")

        return {
            "project_name": project_name,
            "project_summary": project_summary,
            "core_features_text": core_features_text,
            "api_interfaces_text": api_interfaces_text,
            "core_components_text": core_components_text,
            "simplified_content": simplified_content,
            "found_example_sources": found_example_sources # 传递原始示例内容
        }
    
    def exec(self, prep_res):
        """执行阶段：生成示例代码"""
        project_name = prep_res["project_name"]
        project_summary = prep_res["project_summary"]
        core_features_text = prep_res["core_features_text"]
        api_interfaces_text = prep_res["api_interfaces_text"]
        core_components_text = prep_res["core_components_text"]
        simplified_content = prep_res["simplified_content"]
        found_example_sources = prep_res["found_example_sources"] # 获取找到的示例源

        # 初始化变量以确保其总是被定义
        found_examples_section = "在代码库中没有明确识别出潜在的示例文件。" # 默认值

        # 格式化找到的示例以包含在提示中
        if found_example_sources:
            print("\n## 📝 准备示例源代码\n")
            found_examples_section_parts = []
            try:
                for i, src in enumerate(found_example_sources):
                    path = src.get("path", "未知路径")
                    reason = src.get("reason", "无原因")
                    quality = src.get("quality", "未评估")
                    real_world = src.get("real_world_application", "未评估")
                    content_snippet = src.get("content", "")[:1000] # 限制长度
                    
                    print(f"* 处理示例 {i+1}: `{path}`")
                    
                    part = f"潜在示例 {i+1} (来自: {path}):\n" \
                           f"- 原因: {reason}\n" \
                           f"- 质量评估: {quality}\n" \
                           f"- 真实应用案例: {real_world}\n" \
                           f"```python\n{content_snippet}\n...\n```"
                    found_examples_section_parts.append(part)
                if found_examples_section_parts: # 仅当成功生成部分时才覆盖默认值
                     found_examples_section = "\n\n".join(found_examples_section_parts)
                     print(f"\n* ✅ 成功准备了 {len(found_examples_section_parts)} 个示例源代码段\n")
            except Exception as e:
                print(f"\n* ❌ 格式化找到的示例时出错: {e}\n")
                found_examples_section = f"警告: 格式化找到的示例时出错: {e}" # 更新为错误信息
        # else 分支不再需要，因为变量已经有了默认值

        print(f"\n## 🚀 生成项目功能示例\n")
        print(f"* **项目**: `{project_name}`")
        print(f"* **格式**: Markdown\n")
        
        # 增强对project_summary的处理，确保它不为空
        if not project_summary or project_summary.strip() == "":
            print("* ⚠️ 项目概述为空，将使用默认描述")
            project_summary = f"{project_name} 是一个值得探索的项目，以下是根据代码分析生成的示例。"
        else:
            print(f"* ℹ️ 使用项目概述 ({len(project_summary)} 字符) 作为示例生成的基础")
        
        # 构建提示模板
        prompt = GENERATE_EXAMPLES_PROMPT.format(
            project_name=project_name,
            project_summary=project_summary,
            core_features_text=core_features_text,
            api_interfaces_text=api_interfaces_text,
            core_components_text=core_components_text,
            simplified_content=simplified_content,
            found_examples_section=found_examples_section # 传入格式化后的潜在示例
        )
        
        # 调用LLM生成示例
        print("\n* 🧠 正在调用大模型生成示例...")
        response = call_llm(prompt)
        
        # 清理LLM响应中可能包含的包裹```markdown ... ```的部分
        print("\n* 🔍 处理模型响应...")
        markdown_content = response.strip()
        if markdown_content.startswith("```markdown"):
            markdown_content = markdown_content[len("```markdown"):].strip()
        if markdown_content.endswith("```"):
            markdown_content = markdown_content[:-len("```")].strip()

        print(f"\n* ✅ 示例代码 (Markdown) 生成完成\n")
        return markdown_content # 直接返回Markdown字符串
    
    def post(self, shared, prep_res, exec_res):
        """后处理阶段：将生成的示例 Markdown 保存到输出文件夹"""
        markdown_content = exec_res # 获取 Markdown 字符串
        if not markdown_content:
            print("\n## ❌ 错误\n\n未生成有效的示例 Markdown 内容")
            return None # 返回 None 表示成功并继续默认流程
        
        project_name = shared["project_name"]
        output_dir = shared.get("output_dir", "examples")
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 创建/写入示例 Markdown 文件
        examples_md_path = os.path.join(output_dir, "EXAMPLES.md")
        
        with open(examples_md_path, "w", encoding="utf-8") as f:
            f.write(markdown_content)
        
        print(f"\n## ✅ 生成完成\n")
        print(f"* **示例文件**: `{examples_md_path}`")
        print(f"* **项目名称**: `{project_name}`\n")
        print(f"> 注意：在生成过程中，系统已优先考虑 cookbook/、recipes/ 等目录中的高质量真实应用示例。")
        
        # 更新共享数据
        shared["example_files"] = [] # 不再有单独的示例文件
        shared["examples_readme"] = examples_md_path # 指向合并后的 Markdown 文件
        
        return None # 返回 None 表示成功并继续默认流程 