from pocketflow import Flow
# 导入所需的节点类
from evaluate_nodes import (
    AnalyzeCodebase,
    GenerateExampleCode
)

def create_evaluation_flow():
    """创建并返回代码库示例代码生成流程"""
    
    # 实例化节点
    analyze_codebase = AnalyzeCodebase()
    generate_examples = GenerateExampleCode()
    
    # 顺序连接节点
    analyze_codebase >> generate_examples
    
    # 创建流程，以AnalyzeCodebase作为起点
    examples_flow = Flow(start=analyze_codebase)
    
    return examples_flow 