import os
import sys
import argparse
import re
from pathlib import Path
from evaluate_flow import create_evaluation_flow
# from utils.crawl_local_files import crawl_local_files  # 导入文件爬取函数，现在不再需要
import importlib.util
import glob
import fnmatch

def get_project_files(directory, include_patterns, exclude_patterns, max_file_size):
    """递归获取项目文件列表，返回包含文件路径和内容的字典"""
    files_dict = {}
    
    # 确保.env文件被排除
    exclude_patterns = list(exclude_patterns) + ["*.env", ".env"]
    
    # 定义可能与核心功能相关的文件模式
    core_patterns = [
        "*/core/*", "*/src/*", "*/lib/*", "*/api/*", "*/models/*",
        "*/controllers/*", "*/services/*", "main.*", "index.*", "app.*",
        "config.*", "*/proto/*", "*/interfaces/*", "*/types/*",
        "*/constants/*", "*/utils/*", "package.json", "setup.py"
    ]
    
    print("\n## 🔍 项目文件筛选\n")
    print("**筛选策略**: 重点关注核心功能相关文件\n")
    print("> 注意：所有以'.'开头的文件夹（如.git, .vscode等）将被忽略\n")
    
    total_files = 0
    selected_files = 0
    skipped_dot_dirs = set()
    
    for root, dirs, filenames in os.walk(directory):
        # 移除不需要遍历的目录
        dirs[:] = [d for d in dirs if not any(fnmatch.fnmatch(os.path.join(root, d), pat) for pat in exclude_patterns)]
        
        # 移除所有以"."开头的目录
        dot_dirs = [d for d in dirs if d.startswith('.')]
        for dot_dir in dot_dirs:
            skipped_dot_dirs.add(dot_dir)
        dirs[:] = [d for d in dirs if not d.startswith('.')]
        
        for filename in filenames:
            total_files += 1
            file_path = os.path.join(root, filename)
            
            # 明确跳过.env文件
            if filename == ".env" or filename.endswith(".env"):
                print(f"* ⏩ 跳过环境配置文件: `{file_path}`")
                continue
                
            # 检查是否是软链接
            if os.path.islink(file_path):
                print(f"* ⏩ 跳过软链接: `{file_path}`")
                continue
                
            try:
                # 检查文件大小
                if os.path.exists(file_path) and os.path.getsize(file_path) > max_file_size:
                    continue
                    
                # 检查文件是否应该包含
                if any(fnmatch.fnmatch(file_path, pat) for pat in include_patterns) and not any(fnmatch.fnmatch(file_path, pat) for pat in exclude_patterns):
                    # 检查是否是核心相关文件
                    is_core_file = any(fnmatch.fnmatch(file_path, pat) for pat in core_patterns)
                    if is_core_file:
                        print(f"* ✅ **核心文件**: `{file_path}`")
                    else:
                        # 是README或文档文件
                        if filename.lower() == "readme.md" or filename.lower().endswith(".md") or filename.lower().endswith(".rst"):
                            print(f"* 📄 **文档文件**: `{file_path}`")
                        else:
                            # 其他普通文件，不打印以减少输出
                            pass
                            
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                            content = f.read()
                        files_dict[file_path] = content
                        selected_files += 1
                    except Exception as e:
                        print(f"* ❌ **读取错误**: `{file_path}` - {e}")
                        files_dict[file_path] = ""  # 文件读取失败时使用空字符串
            except (FileNotFoundError, OSError) as e:
                print(f"* ❌ **处理错误**: `{file_path}` - {e}")
                continue
    
    print(f"\n## 📊 文件筛选结果\n")
    print(f"* **总文件数**: {total_files}")
    print(f"* **选择文件数**: {selected_files}")
    print(f"* **核心功能文件**: {len([f for f in files_dict.keys() if any(fnmatch.fnmatch(f, pat) for pat in core_patterns)])}")
    print(f"* **文档相关文件**: {len([f for f in files_dict.keys() if f.lower().endswith(('.md', '.rst', '.txt'))])}\n")
    
    if skipped_dot_dirs:
        print(f"**已忽略的点目录**: `{', '.join(sorted(skipped_dot_dirs))}`\n")
    
    return files_dict

def main():
    """命令行主程序：从代码库生成示例代码"""
    
    parser = argparse.ArgumentParser(description="为代码库生成示例代码")
    parser.add_argument("--repo_url", help="GitHub仓库URL", type=str)
    parser.add_argument("--local_dir", help="本地代码库目录路径", type=str)
    parser.add_argument("--output_dir", help="示例输出目录", default="examples", type=str)
    parser.add_argument("--project_name", help="项目名称（如果不指定，将尝试自动推导）", type=str)
    parser.add_argument("--include", help="包含匹配的文件模式（例如 '*.py,*.md'），多个模式用逗号分隔", type=str)
    parser.add_argument("--exclude", help="排除匹配的文件模式（例如 '*.pyc,*__pycache__*'），多个模式用逗号分隔", type=str)
    parser.add_argument("--max_file_size", help="最大文件大小（KB）", type=int, default=1000)
    parser.add_argument("--verbose", help="输出详细日志", action="store_true")
    
    args = parser.parse_args()
    
    # 加载默认文件模式，优先使用脚本所在目录的main.py中定义的模式
    include_patterns = None
    exclude_patterns = None
    
    # 尝试加载 main.py 中定义的模式
    try:
        main_path = os.path.join(os.path.dirname(__file__), "..", "main.py")
        if os.path.exists(main_path):
            spec = importlib.util.spec_from_file_location("main_module", main_path)
            main_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(main_module)
            
            # 检查 main.py 中是否有所需的变量
            if hasattr(main_module, "INCLUDE_PATTERNS"):
                include_patterns = main_module.INCLUDE_PATTERNS
                print(f"* 📎 从 main.py 中加载包含模式: `{include_patterns}`")
            
            if hasattr(main_module, "EXCLUDE_PATTERNS"):
                exclude_patterns = main_module.EXCLUDE_PATTERNS
                print(f"* 🚫 从 main.py 中加载排除模式: `{exclude_patterns}`")
    except Exception as e:
        print(f"* ⚠️ 尝试加载main.py中的模式时出错: {e}")
        # 使用默认模式，不中断流程
    
    # 如果未能从 main.py 加载或命令行指定了模式，使用命令行参数或默认值
    if args.include is not None:
        include_patterns = args.include.split(',')
        print(f"* 📎 使用命令行指定的包含模式: `{include_patterns}`")
    
    if args.exclude is not None:
        exclude_patterns = args.exclude.split(',')
        print(f"* 🚫 使用命令行指定的排除模式: `{exclude_patterns}`")
    
    # 如果仍然没有设置模式，使用默认值
    if include_patterns is None:
        include_patterns = ["*.py", "*.js", "*.ts", "*.java", "*.go", "*.rs", "*.c", "*.cpp", "*.h", "*.hpp", "*.md", "*.rst", "*.txt"]
        print(f"* 📎 使用默认包含模式: `{include_patterns}`")
    
    if exclude_patterns is None:
        exclude_patterns = ["*__pycache__*", "*.pyc", "*.egg-info", "*.egg", "*.git*", "*.ipynb_checkpoints*", "build/*", "dist/*", "*.o", "*.so", "*.dylib", "*.dll", "*.class", "node_modules/*", "*.env", ".env"]
        print(f"* 🚫 使用默认排除模式: `{exclude_patterns}`")
    
    # 共享数据结构
    shared = {}
    
    # 验证参数
    if args.repo_url is None and args.local_dir is None:
        print("## ❌ 错误\n\n必须通过 `--repo_url` 或 `--local_dir` 指定代码库路径\n")
        sys.exit(1)
    
    # 优先使用本地目录，如果指定了的话
    if args.local_dir:
        if not os.path.isdir(args.local_dir):
            print(f"## ❌ 错误\n\n指定的本地目录 '{args.local_dir}' 不存在或不是有效的目录\n")
            sys.exit(1)
        
        print(f"## 📂 加载代码库\n\n* **本地目录**: `{args.local_dir}`\n")
        code_dir = args.local_dir
        
        # 获取项目文件和内容
        loaded_repo_files = get_project_files(
            directory=args.local_dir,
            include_patterns=include_patterns,
            exclude_patterns=exclude_patterns,
            max_file_size=args.max_file_size * 1024  # 转换为字节
        )
        
        # 将文件内容转换为评估流程所需的格式：(path, content) 的列表
        shared["files"] = [(path, content) for path, content in loaded_repo_files.items()]
        
        # 如果通过命令行明确指定了项目名称，则使用命令行参数
        if args.project_name:
            shared["project_name"] = args.project_name
            print(f"* 📌 使用命令行指定的项目名称: **{shared['project_name']}**\n")
        else:
            # 尝试从目录结构推导项目名称
            if os.path.basename(args.local_dir):
                shared["project_name"] = os.path.basename(args.local_dir)
                print(f"* 🔍 从目录名自动推导项目名称: **{shared['project_name']}**\n")
            else:
                # 如果无法从目录名推导，使用默认名称并在后续处理中尝试从代码库内容推导
                shared["project_name"] = None
                print("* ⚠️ 无法从目录名推导项目名称，将在分析代码库时尝试推导\n")
    # ... 以下代码保持不变 ...

    # 设置输出目录
    shared["output_dir"] = args.output_dir
    
    # 设置local_dir用于参考
    shared["local_dir"] = code_dir
    
    # 创建并运行评估流程
    examples_flow = create_evaluation_flow()
    examples_flow.run(shared)
    
    # 检查运行结果
    if "examples_readme" in shared and os.path.exists(shared["examples_readme"]):
        print(f"\n## ✅ 生成完成\n\n* **Markdown文档**: `{shared['examples_readme']}`")
    else:
        print("\n## ⚠️ 警告\n\n未找到生成的Markdown文档。请检查输出目录。")
    
    if "example_files" in shared and shared["example_files"]:
        print(f"* **示例文件数**: {len(shared['example_files'])} 个Python示例文件")

if __name__ == "__main__":
    main() 