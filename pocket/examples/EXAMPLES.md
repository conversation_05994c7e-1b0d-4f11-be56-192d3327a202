# markdownify-mcp 应用示例

## 示例 1: 将 PDF 文档转换为 Markdown 便于知识管理

**描述:**
很多重要信息被存储在 PDF 文件中，但 PDF 通常难以直接编辑和集成到基于文本的工作流中。这个示例展示了如何使用 `markdownify-mcp` 服务器的 MCP `call_tool` 功能，将一个本地 PDF 文件转换为 Markdown 格式。转换后的 Markdown 文件可以方便地进行编辑、搜索、版本控制，并集成到 Obsidian、VS Code 等支持 Markdown 的桌面应用中，用于个人知识管理、文档归档等场景。这个过程极大地简化了从非结构化 PDF 中提取和利用信息的工作。

**代码:**
```python
import sys
import os
import json
import subprocess
import tempfile

# 假设 MCP 客户端库已安装
# from mcp_client import MCPClient # 实际使用时需要根据具体客户端库导入

# 环境就绪检查函数
def check_environment():
    """检查运行环境是否就绪"""
    # 检查是否有 'node' 命令以启动 MCP 服务器
    try:
        subprocess.run(["node", "--version"], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("错误: 未找到 Node.js。请安装 Node.js 以运行 markdownify-mcp 服务器。")
        return False

    # 检查 MCP 服务器的 dist 目录是否存在
    # 注意：这里假设服务器代码已克隆并构建
    # 在实际应用中，桌面应用可能提供服务器启动路径或配置
    # 为简化示例，我们假设服务器在当前目录下或指定路径
    # 请根据实际 MCP 客户端的接入方式调整此部分
    server_dist_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'dist', 'index.js'))
    if not os.path.exists(server_dist_path):
         # 尝试查找 README.md 中的默认安装路径提示
        readme_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'README.md'))
        print(f"错误: 未找到 markdownify-mcp 服务器的可执行文件: {server_dist_path}")
        print(f"请按照 README.md ({readme_path}) 中的说明，克隆仓库并运行 'pnpm install' 和 'pnpm run build' 构建项目。")
        return False

    print("环境检查通过：Node.js 和服务器文件路径看起来正确。")
    return True

# 主函数
def main():
    # 首先检查环境
    if not check_environment():
        sys.exit(1)

    # 模拟 MCP 客户端通过标准输入/输出来与服务器通信
    # 在真实的桌面应用集成中，这部分由 MCP 客户端 SDK 处理
    # 为了示例目的，我们手动构建 MCP 请求并模拟进程间通信

    # 创建一个临时的 PDF 文件用于转换测试
    temp_pdf_path = None
    try:
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as temp_pdf:
            temp_pdf_path = temp_pdf.name
            # 写入一些模拟内容 (实际 PDF 内容)
            # 需要一个真正的 PDF 文件来测试，这里只是创建了一个空文件作为占位符
            # print(f"创建临时 PDF 文件: {temp_pdf_path}")
            pass # 实际测试时，替换为一个包含内容的 PDF 文件路径

        # 重要：替换为实际的 PDF 文件路径
        # 确保文件存在且 markdownify 可以访问
        pdf_to_convert = "/path/to/your/document.pdf" # <--- *** 请替换为您的实际 PDF 文件路径 ***
        if not os.path.exists(pdf_to_convert):
            print(f"错误: 待转换的 PDF 文件不存在: {pdf_to_convert}")
            print("请将此路径替换为您要转换的实际 PDF 文件路径。")
            sys.exit(1)

        print(f"\n准备将文件 '{pdf_to_convert}' 转换为 Markdown...")

        # 构建 MCP CallToolRequest
        call_request = {
            "jsonrpc": "2.0",
            "id": 1, # 请求 ID
            "method": "call_tool",
            "params": {
                "name": "pdf-to-markdown", # 调用 PDF 转换工具
                "arguments": {
                    "filepath": os.path.abspath(pdf_to_convert) # 提供文件的绝对路径
                    # 可以选择提供 projectRoot 和 uvPath 参数，如果需要覆盖默认值
                    # "projectRoot": "/path/to/markdownify-mcp",
                    # "uvPath": "~/.local/bin/uv"
                }
            }
        }

        # 启动 markdownify-mcp 服务器进程，并通过 stdin/stdout 通信
        # 注意：这是一个简化的模拟，真实的 MCP 客户端库会更健壮
        server_dist_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'dist', 'index.js'))
        process = subprocess.Popen(
            ["node", server_dist_path],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            text=True # 以文本模式处理 stdin/stdout
        )

        # 发送 CallToolRequest 给服务器
        print("发送 MCP 请求...")
        process.stdin.write(json.dumps(call_request) + '\n')
        process.stdin.flush()

        # 读取服务器的响应
        print("等待服务器响应...")
        response_line = process.stdout.readline()
        response = json.loads(response_line)

        # 打印响应
        print("\n--- MCP 服务器响应 ---")
        print(json.dumps(response, indent=2))
        print("----------------------")


        # 处理响应结果
        if "result" in response and "content" in response["result"]:
            markdown_content_lines = response["result"]["content"]
            # 将内容数组合并成一个字符串
            markdown_content = "".join(markdown_content_lines)

            # 将转换后的 Markdown 内容保存到文件
            output_markdown_path = os.path.splitext(pdf_to_convert)[0] + ".md"
            with open(output_markdown_path, "w", encoding="utf-8") as md_file:
                md_file.write(markdown_content)

            print(f"\nPDF 文件成功转换为 Markdown。")
            print(f"转换后的内容已保存到: {output_markdown_path}")

        elif "error" in response:
            print(f"\nMCP 服务器返回错误: {response['error'].get('message', '未知错误')}")
            print(f"错误详情: {response['error']}")

        else:
            print("\n未知 MCP 响应格式。")
            print(response)

    except Exception as e:
        print(f"\n执行过程中出错: {e}")
        sys.exit(1)
    finally:
        # 清理临时文件（如果创建了）
        # if temp_pdf_path and os.path.exists(temp_pdf_path):
        #     os.remove(temp_pdf_path)
        #     print(f"已清理临时文件: {temp_pdf_path}")

        # 确保服务器进程终止
        if 'process' in locals() and process.poll() is None:
             process.terminate()
             print("\n已终止 MCP 服务器进程。")


if __name__ == "__main__":
    main()
```

**运行方法:**
```bash
# 1. 克隆 markdownify-mcp 仓库并安装依赖
git clone <repository_url>
cd markdownify-mcp
pnpm install
pnpm run build

# 2. 将上面的 Python 代码保存为 example1.py (确保代码中的 PDF 文件路径是正确的)
#    将 example1.py 放在 markdownify-mcp 仓库的根目录下或其子目录中

# 3. 安装 Python 依赖 (如果示例中使用了特定的 Python 库，比如一个模拟的 mcp_client)
# pip install mcp_client # 假设有这样的库

# 4. 运行示例
python example1.py
```

**预期输出或效果:**
代码将启动 `markdownify-mcp` 服务器，通过标准输入发送一个 MCP 请求来调用 `pdf-to-markdown` 工具，然后读取服务器通过标准输出返回的响应。如果成功，你将看到 MCP 响应的 JSON 输出，并且在与原始 PDF 文件相同的目录下会生成一个同名的 `.md` 文件，其中包含从 PDF 转换而来的 Markdown 格式的文本内容。这将使你能够直接在喜欢的 Markdown 编辑器中处理 PDF 的内容。

**适用场景:**
个人或团队需要将大量 PDF 文档的内容提取到统一的 Markdown 系统中进行管理、检索和二次利用；研究人员需要从论文、报告等 PDF 中快速提取关键信息；需要将 PDF 内容导入到笔记应用或知识图谱工具。

---

## 示例 2: 抓取网页内容并转为 Markdown 用于离线阅读和存档

**描述:**
在浏览器中保存网页往往格式混乱且难以维护，而 `Markdownify` 提供了将任意网页转换为干净 Markdown 格式的功能。这个示例演示了如何通过 MCP 接口调用 `webpage-to-markdown` 工具，将一个指定的 URL 内容抓取并转换为 Markdown。这对于需要离线保存网页内容、建立个人文章库、对网页内容进行结构化处理或集成到博客发布工作流的用户非常有用，确保了内容的易读性和可移植性。

**代码:**
```python
import sys
import os
import json
import subprocess
import tempfile

# 环境就绪检查函数 (与示例 1 相同，确保 Node.js 和服务器文件存在)
def check_environment():
    """检查运行环境是否就绪"""
    try:
        subprocess.run(["node", "--version"], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("错误: 未找到 Node.js。请安装 Node.js 以运行 markdownify-mcp 服务器。")
        return False

    server_dist_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'dist', 'index.js'))
    if not os.path.exists(server_dist_path):
        readme_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'README.md'))
        print(f"错误: 未找到 markdownify-mcp 服务器的可执行文件: {server_dist_path}")
        print(f"请按照 README.md ({readme_path}) 中的说明，克隆仓库并运行 'pnpm install' 和 'pnpm run build' 构建项目。")
        return False

    print("环境检查通过：Node.js 和服务器文件路径看起来正确。")
    return True


# 主函数
def main():
    # 首先检查环境
    if not check_environment():
        sys.exit(1)

    # 待转换的网页 URL
    url_to_convert = "https://example.com/some-article" # <--- *** 请替换为您想转换的网页 URL ***
    output_filename = "converted_webpage.md" # 输出文件名

    print(f"\n准备将网页 '{url_to_convert}' 转换为 Markdown...")

    # 构建 MCP CallToolRequest
    call_request = {
        "jsonrpc": "2.0",
        "id": 2, # 请求 ID
        "method": "call_tool",
        "params": {
            "name": "webpage-to-markdown", # 调用网页转换工具
            "arguments": {
                "url": url_to_convert # 提供要转换的网页 URL
                # 可选参数同上
            }
        }
    }

    # 启动 markdownify-mcp 服务器进程，并通过 stdin/stdout 通信 (与示例 1 类似)
    try:
        server_dist_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'dist', 'index.js'))
        process = subprocess.Popen(
            ["node", server_dist_path],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            text=True
        )

        # 发送 CallToolRequest 给服务器
        print("发送 MCP 请求...")
        process.stdin.write(json.dumps(call_request) + '\n')
        process.stdin.flush()

        # 读取服务器的响应
        print("等待服务器响应...")
        response_line = process.stdout.readline()
        response = json.loads(response_line)

        # 打印响应
        print("\n--- MCP 服务器响应 ---")
        print(json.dumps(response, indent=2))
        print("----------------------")

        # 处理响应结果
        if "result" in response and "content" in response["result"]:
            markdown_content_lines = response["result"]["content"]
            markdown_content = "".join(markdown_content_lines)

            # 保存到文件
            output_markdown_path = os.path.abspath(output_filename)
            with open(output_markdown_path, "w", encoding="utf-8") as md_file:
                md_file.write(markdown_content)

            print(f"\n网页内容成功转换为 Markdown。")
            print(f"转换后的内容已保存到: {output_markdown_path}")

        elif "error" in response:
             print(f"\nMCP 服务器返回错误: {response['error'].get('message', '未知错误')}")
             print(f"错误详情: {response['error']}")

        else:
             print("\n未知 MCP 响应格式。")
             print(response)

    except Exception as e:
        print(f"\n执行过程中出错: {e}")
        sys.exit(1)
    finally:
         # 确保服务器进程终止
        if 'process' in locals() and process.poll() is None:
             process.terminate()
             print("\n已终止 MCP 服务器进程。")


if __name__ == "__main__":
    main()
```

**运行方法:**
```bash
# 1. 确保已按照示例 1 的步骤克隆并构建了 markdownify-mcp 仓库
# 2. 将上面的 Python 代码保存为 example2.py (确保代码中的 URL 是正确的)
# 3. 安装 Python 依赖 (如果示例中使用了特定的 Python 库)
# pip install ...

# 4. 运行示例
python example2.py
```

**预期输出或效果:**
代码将启动 `markdownify-mcp` 服务器，发送 MCP 请求调用 `webpage-to-markdown` 工具，并提供一个 URL。服务器将访问该 URL，提取主要内容，并将其转换为 Markdown 格式。响应中的 `content` 字段将包含转换后的 Markdown 文本。脚本会将此内容保存到当前目录下的 `converted_webpage.md` 文件中。这个文件将包含一个干净、易于阅读的网页内容 Markdown 版本。

**适用场景:**
为信息收集建立一个自动化的流程，用于保存感兴趣的文章或技术文档；为博客或内容管理系统抓取并格式化外部内容；需要对大量网页进行离线存档或全文搜索。

---

## 示例 3: 批量处理文件类型混杂的文档库

**描述:**
在一个项目、研究或个人文档库中，经常会遇到各种不同格式的文件，如 Word 文档 (.docx)、Excel 表格 (.xlsx)、PowerPoint 演示文稿 (.pptx)、PDF 文件等。将这些不同格式的文档内容整合到一个统一的 Markdown 工作流中进行检索和管理是一个挑战。这个示例展示了如何利用 `markdownify-mcp` 对指定目录下的常见办公文档进行批量转换。通过遍历目录并根据文件扩展名调用相应的 MCP 工具，可以将整个文档库的内容统一到 Markdown 格式，极大地方便了跨格式的信息检索和知识整合。

**代码:**
```python
import sys
import os
import json
import subprocess
import tempfile
import time

# 环境就绪检查函数 (与示例 1 相同)
def check_environment():
    """检查运行环境是否就绪"""
    try:
        subprocess.run(["node", "--version"], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("错误: 未找到 Node.js。请安装 Node.js 以运行 markdownify-mcp 服务器。")
        return False

    server_dist_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'dist', 'index.js'))
    if not os.path.exists(server_dist_path):
        readme_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'README.md'))
        print(f"错误: 未找到 markdownify-mcp 服务器的可执行文件: {server_dist_path}")
        print(f"请按照 README.md ({readme_path}) 中的说明，克隆仓库并运行 'pnpm install' 和 'pnpm run build' 构建项目。")
        return False

    print("环境检查通过：Node.js 和服务器文件路径看起来正确。")
    return True

# 函数：调用 MCP 工具转换文件
def call_markdownify_tool(file_path, tool_name, process):
    """向 MCP 服务器发送请求调用指定工具转换文件"""
    request_id = int(time.time() * 1000) # 简单的唯一请求ID
    call_request = {
        "jsonrpc": "2.0",
        "id": request_id,
        "method": "call_tool",
        "params": {
            "name": tool_name,
            "arguments": {
                "filepath": os.path.abspath(file_path)
            }
        }
    }

    try:
        print(f"发送 MCP 请求用于转换 '{file_path}' 使用工具 '{tool_name}'...")
        process.stdin.write(json.dumps(call_request) + '\n')
        process.stdin.flush()

        # 读取服务器的响应
        response_line = process.stdout.readline()
        response = json.loads(response_line)

        return response

    except Exception as e:
        print(f"与 MCP 服务器通信出错: {e}")
        return {"error": {"code": -32603, "message": f"通信错误: {e}"}} # 模拟 MCP 错误响应

# 主函数
def main():
    # 首先检查环境
    if not check_environment():
        sys.exit(1)

    # 待处理的目录
    source_directory = "./docs_to_convert" # <--- *** 请替换为您的实际文档目录路径 ***
    output_directory = "./converted_markdown" # 转换后 Markdown 文件的输出目录

    if not os.path.isdir(source_directory):
        print(f"错误: 源目录 '{source_directory}' 不存在或不是一个有效目录。")
        print("请将此路径替换为您包含要转换文档的目录。")
        sys.exit(1)

    # 创建输出目录（如果不存在）
    os.makedirs(output_directory, exist_ok=True)
    print(f"\n将从目录 '{source_directory}' 读取文件，并将 Markdown 输出到 '{output_directory}'")

    # 启动 markdownify-mcp 服务器进程 (一次启动，多次调用)
    process = None
    try:
        server_dist_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'dist', 'index.js'))
        process = subprocess.Popen(
            ["node", server_dist_path],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            text=True
        )
        # Give the server a moment to start
        time.sleep(1)
        print("MCP 服务器已启动。")

        # 遍历目录处理文件
        for root, _, files in os.walk(source_directory):
            for file in files:
                file_path = os.path.join(root, file)
                file_name, file_extension = os.path.splitext(file)
                file_extension = file_extension.lower()

                tool_name = None
                if file_extension == ".pdf":
                    tool_name = "pdf-to-markdown"
                elif file_extension in [".docx", ".xlsx", ".pptx"]:
                    # markitdown 通常对这些格式使用同一个通用工具或内部处理
                    # 根据实际工具名称调整，这里假设有一个通用的 office-to-markdown 或类似工具
                    # 或者 markitdown 内部根据扩展名处理
                    # 假设直接传递文件路径给一个通用的工具或者pdf-to-markdown工具也能处理这些
                    # 实际上 markitdown 的调用方式是 uv run markitdown <filepath>
                    # 所以我们只需要 call_tool 并提供 filepath，服务器会调用 markitdown
                    # 我们只需要确保服务器正确映射了这些文件类型到markitdown的调用
                    # MCP Tools 定义了具体的工具名称，我们需要与 src/tools.ts 中的定义一致
                    # 假设服务器内部根据文件扩展名选择了正确的 markitdown 处理逻辑
                    # 我们在这里直接调用一个适合处理文件路径的工具名称，例如 `pdf-to-markdown` 也可以接受其他文件类型
                    # 或者检查 list_tools 找到正确的工具名称
                    # 为简化示例，我们假设 'pdf-to-markdown' 可以处理这些格式（或有一个通用的 'file-to-markdown'）
                    # 更好的做法是先 list_tools 查找可用的工具
                    # 或者调用一个通用的 file-to-markdown 工具（如果存在）
                    # 这里我们模拟调用 pdf-to-markdown，因为它接受 filepath 参数
                    tool_name = "pdf-to-markdown" # <-- 模拟，请根据实际 MCP 工具名称调整
                    print(f"尝试使用 '{tool_name}' 工具处理文件 '{file_path}'...")
                elif file_extension in [".jpg", ".jpeg", ".png", ".gif"]:
                    # 图像文件
                    tool_name = "pdf-to-markdown" # <-- markitdown 也可以处理图片，同样用接受 filepath 的工具
                    print(f"尝试处理图片文件 '{file_path}'...")
                # 可以添加更多文件类型判断，如音频等

                if tool_name:
                    response = call_markdownify_tool(file_path, tool_name, process)

                    if "result" in response and "content" in response["result"]:
                        markdown_content_lines = response["result"]["content"]
                        markdown_content = "".join(markdown_content_lines)

                        # 构建输出文件路径，保留原始目录结构
                        relative_path = os.path.relpath(file_path, source_directory)
                        output_markdown_path = os.path.join(output_directory, os.path.splitext(relative_path)[0] + ".md")

                        # 创建输出子目录（如果需要）
                        os.makedirs(os.path.dirname(output_markdown_path), exist_ok=True)

                        with open(output_markdown_path, "w", encoding="utf-8") as md_file:
                            md_file.write(markdown_content)

                        print(f"成功转换 '{file}' -> '{output_markdown_path}'")
                    elif "error" in response:
                        print(f"转换 '{file}' 时出错: {response['error'].get('message', '未知错误')}")
                    else:
                         print(f"转换 '{file}' 时收到未知响应: {response}")
                else:
                    print(f"跳过未知文件类型: '{file_path}'")

    except Exception as e:
        print(f"\n执行过程中出错: {e}")
        sys.exit(1)
    finally:
        # 确保服务器进程终止
        if process and process.poll() is None:
             process.terminate()
             print("\n已终止 MCP 服务器进程。")


if __name__ == "__main__":
    main()
```

**运行方法:**
```bash
# 1. 确保已按照示例 1 的步骤克隆并构建了 markdownify-mcp 仓库
# 2. 在 markdownify-mcp 仓库根目录或方便的位置创建一个名为 'docs_to_convert' 的文件夹
# 3. 在 'docs_to_convert' 文件夹中放入一些不同类型的文件（.pdf, .docx, .xlsx, .pptx, .jpg 等）
# 4. 将上面的 Python 代码保存为 example3.py
# 5. 运行示例
python example3.py
```

**预期输出或效果:**
脚本将启动 `markdownify-mcp` 服务器，并遍历 `docs_to_convert` 目录下的所有文件。对于支持转换的文件类型 (PDF, DOCX, XLSX, PPTX, 图片等)，脚本将调用相应的 MCP 工具（在本例中模拟调用 `pdf-to-markdown`，因为它接受文件路径），并将服务器返回的 Markdown 内容保存到 `converted_markdown` 目录下，保留原始目录结构。最终，`converted_markdown` 目录将包含源文档库的 Markdown 版本，便于统一管理和检索。

**适用场景:**
将分散在不同格式文档中的项目资料、会议记录、报告和数据整合到一个统一的知识库或文档管理系统中；建立一个自动化的文档处理流程，使得用户提交任何常用格式的文档后都能自动获得其 Markdown 版本；进行大规模文档内容的检索和分析。

---

## 示例 4: 获取现有 Markdown 文件内容供其他应用使用

**描述:**
在一些工作流中，你可能已经有了许多 Markdown 文件，例如笔记、代码文档、项目计划等。其他应用程序（如图表生成工具、报告生成工具、AI 助手应用）可能需要读取这些 Markdown 文件的内容进行进一步处理。`markdownify-mcp` 不仅能创建 Markdown 文件，还提供了一个工具来直接获取现有 Markdown 文件的内容。这个示例展示了如何通过 MCP `call_tool` 功能调用 `get-markdown-file` 工具，读取一个指定路径的 Markdown 文件内容。这使得任何支持 MCP 协议的应用都能轻松集成 Markdown 文件读取能力。

**代码:**
```python
import sys
import os
import json
import subprocess
import tempfile

# 环境就绪检查函数 (与示例 1 相同)
def check_environment():
    """检查运行环境是否就绪"""
    try:
        subprocess.run(["node", "--version"], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("错误: 未找到 Node.js。请安装 Node.js 以运行 markdownify-mcp 服务器。")
        return False

    server_dist_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'dist', 'index.js'))
    if not os.path.exists(server_dist_path):
        readme_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'README.md'))
        print(f"错误: 未找到 markdownify-mcp 服务器的可执行文件: {server_dist_path}")
        print(f"请按照 README.md ({readme_path}) 中的说明，克隆仓库并运行 'pnpm install' 和 'pnpm run build' 构建项目。")
        return False

    print("环境检查通过：Node.js 和服务器文件路径看起来正确。")
    return True

# 主函数
def main():
    # 首先检查环境
    if not check_environment():
        sys.exit(1)

    # 创建一个临时的 Markdown 文件用于读取测试
    temp_md_path = None
    markdown_content_to_write = "# 测试 Markdown 文件\n\n这是一段测试内容。\n - 项目列表项 1\n - 项目列表项 2\n\n这是一个**加粗**的单词和*斜体*的单词。"
    try:
        with tempfile.NamedTemporaryFile(suffix=".md", mode="w", delete=False, encoding="utf-8") as temp_md:
            temp_md_path = temp_md.name
            temp_md.write(markdown_content_to_write)
        print(f"创建临时 Markdown 文件用于读取: {temp_md_path}")

        # 要读取的 Markdown 文件路径
        md_file_to_read = temp_md_path # <--- *** 可以替换为您想读取的实际 Markdown 文件路径 ***
        # 如果替换，请确保文件存在
        if not os.path.exists(md_file_to_read):
             print(f"错误: 待读取的 Markdown 文件不存在: {md_file_to_read}")
             print("请将此路径替换为您要读取的实际 Markdown 文件路径。")
             sys.exit(1)

        print(f"\n准备读取文件 '{md_file_to_read}' 的内容...")

        # 构建 MCP CallToolRequest
        call_request = {
            "jsonrpc": "2.0",
            "id": 4, # 请求 ID
            "method": "call_tool",
            "params": {
                "name": "get-markdown-file", # 调用读取 Markdown 文件工具
                "arguments": {
                    "filepath": os.path.abspath(md_file_to_read) # 提供文件的绝对路径
                }
            }
        }

        # 启动 markdownify-mcp 服务器进程，并通过 stdin/stdout 通信 (与示例 1 类似)
        process = None
        try:
            server_dist_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'dist', 'index.js'))
            process = subprocess.Popen(
                ["node", server_dist_path],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                text=True
            )
            # Give the server a moment to start
            time.sleep(1)
            print("MCP 服务器已启动。")

            # 发送 CallToolRequest 给服务器
            print("发送 MCP 请求...")
            process.stdin.write(json.dumps(call_request) + '\n')
            process.stdin.flush()

            # 读取服务器的响应
            print("等待服务器响应...")
            response_line = process.stdout.readline()
            response = json.loads(response_line)

            # 打印响应
            print("\n--- MCP 服务器响应 ---")
            print(json.dumps(response, indent=2))
            print("----------------------")

            # 处理响应结果
            if "result" in response and "content" in response["result"]:
                file_content_lines = response["result"]["content"]
                file_content = "".join(file_content_lines)

                print(f"\n成功读取 Markdown 文件 '{md_file_to_read}' 的内容:")
                print("--- 文件内容 ---")
                print(file_content)
                print("----------------")

            elif "error" in response:
                print(f"\nMCP 服务器返回错误: {response['error'].get('message', '未知错误')}")
                print(f"错误详情: {response['error']}")

            else:
                print("\n未知 MCP 响应格式。")
                print(response)

        except Exception as e:
            print(f"\n与 MCP 服务器通信或处理响应时出错: {e}")
            sys.exit(1)
        finally:
            # 确保服务器进程终止
            if process and process.poll() is None:
                process.terminate()
                print("\n已终止 MCP 服务器进程。")

    except Exception as e:
        print(f"\n创建临时文件时出错: {e}")
        sys.exit(1)
    finally:
         # 清理临时文件
        if temp_md_path and os.path.exists(temp_md_path):
            os.remove(temp_md_path)
            print(f"已清理临时文件: {temp_md_path}")


if __name__ == "__main__":
    main()
```

**运行方法:**
```bash
# 1. 确保已按照示例 1 的步骤克隆并构建了 markdownify-mcp 仓库
# 2. 将上面的 Python 代码保存为 example4.py (如果需要，替换代码中的 Markdown 文件路径)
# 3. 运行示例
python example4.py
```

**预期输出或效果:**
脚本将启动 `markdownify-mcp` 服务器，向其发送一个 MCP 请求调用 `get-markdown-file` 工具，并提供一个 Markdown 文件的路径。服务器将读取该文件的内容，并在 MCP 响应的 `content` 字段中返回。脚本将打印服务器返回的 MCP JSON 响应，以及提取出的 Markdown 文件内容。

**适用场景:**
桌面 AI 助手需要读取用户本地笔记来回答问题或生成摘要；报告生成工具需要整合多个 Markdown 源文件；内容管理系统需要通过 MCP 接口访问和展示存储在本地的文件内容。

---