import os
import re
import yaml
from pathlib import Path
from typing import List, Dict, Any
import shutil

from pocketflow import Node
from utils.call_llm import call_llm

# 缓存目录
CACHE_DIR = os.environ.get("CACHE_DIR", "cache")
os.makedirs(CACHE_DIR, exist_ok=True)


class SelectExamples(Node):
    """在项目中收集所有示例（文件或文件夹），并生成运行说明 Markdown"""

    # 用于主动搜索示例目录 / 文件
    _EXAMPLE_DIR_KEYWORDS = [
        "cookbook", "cookbooks", "recipes", "examples", "samples",
        "tutorials", "demo", "demos"
    ]
    _EXAMPLE_FILE_KEYWORDS = [
        "example", "sample", "demo", "tutorial", "howto",
        "quickstart", "usage", "getting_started", "get_started"
    ]

    def _actively_search_examples(self, files_data: List):
        """遍历项目文件，主动发现潜在示例文件或示例目录。

        返回列表，每项结构:
            {
              path: str,              # 相对路径（文件或目录）
              is_dir: bool,           # 是否目录
              reason: str,            # 发现原因
              file_index: Optional[int]  # 当 is_dir 为 False 时，可提供索引用于代码片段
            }
        """
        results: List[Dict[str, Any]] = []
        seen_paths = set()

        # 先检测文件级别示例
        for idx, (path, content) in enumerate(files_data):
            lower_path = path.lower()
            in_dir = any(f"/{kw}/" in lower_path or lower_path.startswith(f"{kw}/") for kw in self._EXAMPLE_DIR_KEYWORDS)
            file_kw = any(kw in os.path.basename(lower_path) for kw in self._EXAMPLE_FILE_KEYWORDS)
            first_lines = "\n".join(content.splitlines()[:15]).lower()
            content_kw = any(kw in first_lines for kw in ["example", "demo", "tutorial", "quickstart", "usage"])

            if in_dir or file_kw or content_kw:
                reason_parts = []
                if in_dir:
                    reason_parts.append("位于示例目录")
                if file_kw:
                    reason_parts.append("文件名包含示例关键词")
                if content_kw:
                    reason_parts.append("文件内容暗示示例代码")

                if path not in seen_paths:
                    results.append({
                        "file_index": idx,
                        "path": path,
                        "reason": ", ".join(reason_parts),
                        "is_dir": False,
                    })
                    seen_paths.add(path)

            # 再检测目录级别示例: 如果任一路径片段命中关键字
            parts = path.split(os.sep)
            for depth, part in enumerate(parts[:-1]):  # exclude filename
                if part.lower() in self._EXAMPLE_DIR_KEYWORDS:
                    dir_path = os.sep.join(parts[: depth + 1])
                    if dir_path not in seen_paths:
                        results.append({
                            "file_index": None,
                            "path": dir_path,
                            "reason": "目录名称包含示例关键词",
                            "is_dir": True,
                        })
                        seen_paths.add(dir_path)
        return results

    # ---------- Node 生命周期 ----------

    def prep(self, shared: Dict[str, Any]):
        """收集潜在示例文件及必要上下文"""
        files_data = shared["files"]
        project_name = shared["project_name"]

        # 来自 LLM 分析的潜在示例
        potential = []
        # 主动再搜索一遍，避免遗漏
        active_search = self._actively_search_examples(files_data)

        # 根据路径去重
        existing_paths = {item.get("path") for item in potential}
        for item in active_search:
            if item.get("path") not in existing_paths:
                potential.append(item)

        return {
            "project_name": project_name,
            "project_summary": "",
            "files_data": files_data,
            "candidates": potential,
            "skip_llm": shared.get("skip_llm", False),
            "max_examples": shared.get("max_examples", 0)
        }

    def exec(self, prep_res: Dict[str, Any]):
        """调用 LLM 评估候选示例并返回结构化结果"""
        project_name = prep_res["project_name"]
        project_summary = prep_res["project_summary"]
        files_data = prep_res["files_data"]
        candidates = prep_res["candidates"]
        skip_llm = prep_res.get("skip_llm", False)
        max_examples = prep_res.get("max_examples", 0)

        if not candidates:
            print("* ⚠️ 未发现任何候选示例文件，跳过筛选")
            return {
                "examples": []
            }

        # 如果跳过 LLM，直接返回基本信息
        if skip_llm:
            examples = []
            for c in candidates:
                examples.append({
                    "path": c.get("path"),
                    "title": "",
                    "description": c.get("reason", ""),
                    "usage": "",
                    "expected_input": "",
                    "expected_output": "",
                })
            if max_examples > 0:
                examples = examples[:max_examples]
            return {"examples": examples}

        # 为 prompt 准备示例列表，包含路径及可用的代码片段
        snippet_parts = []
        for c in candidates:
            path = c.get("path")
            reason = c.get("reason", "")
            is_dir = c.get("is_dir", False)

            if not is_dir and c.get("file_index") is not None:
                idx = c["file_index"]
                code_content = files_data[idx][1]
                snippet = "\n".join(code_content.splitlines()[:40])  # 取前 40 行即可
                snippet_section = f"```\n{snippet}\n```"
            else:
                snippet_section = "(目录，省略代码片段)"

            snippet_parts.append(f"### 路径: {path}\n类型: {'目录' if is_dir else '文件'}\n原因: {reason}\n{snippet_section}")

        candidate_section = "\n\n".join(snippet_parts)

        limit_instruction = f"最多 {max_examples} 个示例" if max_examples > 0 else "所有合适的示例"

        prompt = f"""
你是一位经验丰富的开发者，请根据以下项目描述和候选示例列表，为每个示例生成运行说明，并输出严格的 YAML。

项目名称: {project_name}
项目概述: {project_summary}

候选示例列表:
{candidate_section}

---
输出要求:
请严格从候选列表中挑选出{limit_instruction}（按实际应用价值和易上手程度排序）。
以 YAML 输出，顶级键为 examples，其值为列表，每个元素包含:
  path: 原始示例相对路径（必须与候选路径一致）
  title: 一句话说明示例场景
  description: 用通俗语言介绍示例解决了什么问题、价值在哪里
  usage: |
    详细的运行步骤/命令（bash，多行）
  expected_input: |
    运行示例时典型输入（如有）
  expected_output: |
    成功运行后的典型输出或效果描述

**仅输出 YAML，不要输出任何解释或 Markdown**
"""
        print("* 🧠 正在调用大模型生成运行说明...")
        llm_response = call_llm(prompt)
        yaml_match = re.search(r"```yaml(.*?)```", llm_response, re.DOTALL)
        yaml_text = yaml_match.group(1).strip() if yaml_match else llm_response.strip()

        try:
            parsed = yaml.safe_load(yaml_text)
            if not isinstance(parsed, dict) or "examples" not in parsed:
                raise ValueError("YAML 中缺少 examples 键")
        except Exception as e:
            print(f"* ❌ 无法解析 LLM 返回的 YAML: {e}")
            parsed = {"examples": []}

        # 若大模型输出超过 max_examples (且 max_examples > 0) 则截断
        if isinstance(parsed, dict) and "examples" in parsed and max_examples > 0:
            parsed["examples"] = parsed["examples"][:max_examples]

        return parsed

    def post(self, shared: Dict[str, Any], prep_res: Dict[str, Any], exec_res: Dict[str, Any]):
        """保存代码文件与 Markdown 说明"""
        skip_llm = prep_res.get("skip_llm", False)
        max_examples_param = prep_res.get("max_examples", 0)
        examples: List[Dict[str, Any]] = exec_res.get("examples", [])
        if not examples:
            print("* ⚠️ 未生成任何示例")
            return None

        # 默认保存目录: <output_dir>/<project_name>
        base_output = Path(shared.get("output_dir", "examples"))
        project_name = shared.get("project_name", "project")
        output_dir_path = base_output / project_name
        output_dir = str(output_dir_path)
        os.makedirs(output_dir_path, exist_ok=True)

        title_str = "示例汇总"
        if max_examples_param > 0:
            title_str = f"TOP{len(examples)} 示例汇总 (基于请求 {max_examples_param})"
        elif skip_llm and max_examples_param == 0:
             title_str = f"{len(examples)} 个扫描到的示例汇总"
        else:
            title_str = f"{len(examples)} 个示例汇总"
        
        md_lines = [f"# {shared.get('project_name', '项目')} {title_str}\n"]

        saved_example_paths = []
        collected_root = output_dir_path / "collected_examples"
        for idx, ex in enumerate(examples, 1):
            rel_path = ex.get("path")
            title = ex.get("title", "")
            description = ex.get("description", "")
            usage = ex.get("usage", "")
            expected_input = ex.get("expected_input", "")
            expected_output = ex.get("expected_output", "")

            if not rel_path:
                continue

            # 源绝对路径
            src_path = Path(rel_path)
            if not src_path.is_absolute():
                src_abs_path = (Path(shared.get("local_dir")) / src_path).resolve()
            else:
                src_abs_path = src_path.resolve()

            # 目标保存路径：位于输出目录下，保持原层级
            dest_path = (collected_root / rel_path).resolve()

            # 若目标不存在，则复制文件/目录
            if not dest_path.exists():
                try:
                    if src_abs_path.is_dir():
                        shutil.copytree(src_abs_path, dest_path, dirs_exist_ok=True)
                    else:
                        dest_path.parent.mkdir(parents=True, exist_ok=True)
                        shutil.copy2(src_abs_path, dest_path)
                except Exception as copy_err:
                    print(f"* ⚠️ 无法复制 {src_abs_path} -> {dest_path}: {copy_err}")
                    # 若复制失败，仍使用源绝对路径写入
                    dest_path = src_abs_path

            # 写入 Markdown 部分
            md_lines.append(f"## 示例 {idx}: {title}\n")
            md_lines.append(f"**路径:** `{dest_path}`  \n")
            md_lines.append(f"**发现原因:** {description}\n")
            if usage:
                md_lines.append("**运行方法:**\n")
                md_lines.append("```bash")
                md_lines.append(usage)
                md_lines.append("```")
            if expected_input:
                md_lines.append("**示例输入:**\n")
                md_lines.append("```bash")
                md_lines.append(expected_input)
                md_lines.append("```")
            if expected_output:
                md_lines.append("**预期输出/效果:**\n")
                md_lines.append("```bash")
                md_lines.append(expected_output)
                md_lines.append("```")
            md_lines.append("\n---\n")

            # update ex path to absolute dest once copied
            ex["path"] = str(dest_path)
            saved_example_paths.append(str(dest_path))

        md_content = "\n".join(md_lines)
        md_path = output_dir_path / "SELECTED_EXAMPLES.md"
        with open(md_path, "w", encoding="utf-8") as f_md:
            f_md.write(md_content)

        print(f"* ✅ 已收集 {len(saved_example_paths)} 个示例路径")
        if skip_llm:
            print("* ℹ️ 已跳过 LLM 调用，仅执行示例搜索与复制")
        print(f"* ✅ 已保存 Markdown 说明至 {md_path}")

        shared["selected_examples_md"] = str(md_path)
        shared["selected_example_files"] = saved_example_paths
        return None 