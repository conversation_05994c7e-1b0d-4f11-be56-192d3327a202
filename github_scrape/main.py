import argparse
import logging
import logging.config
import os
import sys
import glob
from config import LOG_LEVEL, LOG_FORMAT, LOG_FILE, BASE_DIR
import json
from datetime import datetime
import signal

# 导入新创建的模块
from scraper.file_storage import FileStorage
from scraper.github_api import GitHubScraper
from scraper.processor import DataProcessor

# 数据库客户端导入
try:
    from database.mongo_client import get_mongo_client
except ImportError:
    # 如果数据库模块不可用，提供一个空函数
    def get_mongo_client():
        raise ImportError("MongoDB客户端不可用")

# 创建必要的目录结构
os.makedirs(os.path.join(BASE_DIR, "logs"), exist_ok=True)

# 配置日志
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format=LOG_FORMAT,
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(os.path.join(BASE_DIR, "logs", LOG_FILE))
    ]
)

logger = logging.getLogger(__name__)

def run_api_server(host, port):
    """启动API服务器"""
    from api.server import app
    import uvicorn
    from config import API_HOST, API_PORT, API_DEBUG
    
    logger.info(f"启动API服务器 - 监听: {host}:{port}")
    uvicorn.run(
        app,
        host=host,
        port=port,
        reload=API_DEBUG
    )

def run_scraper(min_stars: int = 1000, direct_mode: bool = False):
    """运行GitHub AI项目抓取器"""
    logger.info(f"开始抓取星标数 >= {min_stars} 的AI项目")
    
    try:
        # 检查数据库连接
        db = get_mongo_client()
        
        # 从数据库获取已有仓库ID和名称（如果有）
        repo_ids = db.get_all_repository_ids()
        repo_names = db.get_all_repository_names()
        
        if repo_ids or repo_names:
            logger.info(f"已有 {len(repo_ids)} 个仓库ID和 {len(repo_names)} 个仓库名称在数据库中")
    except Exception as e:
        logger.warning(f"数据库连接失败: {str(e)}，将使用文件存储")
        
    logger.info("开始抓取仓库数据")
    
    # 初始化GitHub爬虫和数据处理器
    scraper = GitHubScraper()
    processor = DataProcessor()
    
    # 获取仓库数据
    if direct_mode:
        # 直接模式：跳过搜索，直接获取预定义的热门AI仓库
        logger.info("使用直接模式获取热门AI仓库")
        raw_repos = scraper.get_popular_ai_repos_directly()
    else:
        # 标准模式：使用搜索API获取仓库
        raw_repos = scraper.get_all_ai_repositories(min_stars)
    
    # 创建数据目录
    data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
    os.makedirs(data_dir, exist_ok=True)
    file_storage = FileStorage(output_dir=data_dir)
    
    # 加载已有的仓库ID，避免重复抓取
    existing_repo_ids = set()
    existing_repo_names = set()
    
    # 遍历data目录中的所有JSON文件
    for filename in glob.glob(os.path.join(data_dir, "*.json")):
        try:
            if os.path.basename(filename).startswith("github_repos_"):
                # 这是汇总文件，需要遍历其中的所有仓库
                with open(filename, 'r', encoding='utf-8') as f:
                    try:
                        repos = json.load(f)
                        if isinstance(repos, list):
                            for repo in repos:
                                if "repo_id" in repo:
                                    existing_repo_ids.add(repo["repo_id"])
                                if "full_name" in repo:
                                    existing_repo_names.add(repo["full_name"])
                    except json.JSONDecodeError:
                        logger.warning(f"无法解析JSON文件: {filename}")
            else:
                # 这是单个仓库文件
                with open(filename, 'r', encoding='utf-8') as f:
                    try:
                        repo = json.load(f)
                        if "repo_id" in repo:
                            existing_repo_ids.add(repo["repo_id"])
                        if "full_name" in repo:
                            existing_repo_names.add(repo["full_name"])
                    except json.JSONDecodeError:
                        logger.warning(f"无法解析JSON文件: {filename}")
        except Exception as e:
            logger.error(f"读取文件 {filename} 时出错: {str(e)}")
    
    logger.info(f"已有 {len(existing_repo_ids)} 个仓库ID和 {len(existing_repo_names)} 个仓库名称在数据库中")
    
    processed_repos = []
    
    try:
        # 分批抓取仓库数据
        logger.info("开始抓取仓库数据")
        raw_repos = scraper.get_all_ai_repositories(min_stars)
        logger.info(f"共抓取 {len(raw_repos)} 个原始仓库数据")
        
        # 创建原始数据的备份
        backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backup')
        os.makedirs(backup_dir, exist_ok=True)
        
        raw_backup_file = os.path.join(backup_dir, f'raw_repos_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        with open(raw_backup_file, 'w', encoding='utf-8') as f:
            json.dump(raw_repos, f, ensure_ascii=False, indent=2)
        logger.info(f"原始仓库数据已备份至: {raw_backup_file}")
        
        # 过滤出新的仓库
        new_raw_repos = []
        for repo in raw_repos:
            repo_id = repo.get("id")
            full_name = repo.get("full_name")
            
            if repo_id in existing_repo_ids or full_name in existing_repo_names:
                logger.info(f"跳过已存在的仓库: {full_name}")
                continue
                
            new_raw_repos.append(repo)
        
        logger.info(f"过滤后有 {len(new_raw_repos)} 个新仓库需要处理")
        
        # 处理仓库数据
        logger.info("开始处理仓库数据")
        processed_repos = processor.process_repositories(new_raw_repos)
        logger.info(f"共处理 {len(processed_repos)} 个仓库数据")
        
        # 创建处理后数据的备份
        processed_backup_file = os.path.join(backup_dir, f'processed_repos_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        with open(processed_backup_file, 'w', encoding='utf-8') as f:
            json.dump(processed_repos, f, ensure_ascii=False, indent=2)
        logger.info(f"处理后仓库数据已备份至: {processed_backup_file}")
        
        # 保存到文本文件，而不是数据库
        try:
            logger.info("开始保存数据到文本文件")
            
            if processed_repos:
                # 生成带时间戳的汇总文件
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                saved_file = file_storage.save_repositories(processed_repos)
                
                # 同时将每个仓库数据单独保存到一个文件
                for repo in processed_repos:
                    file_storage.save_repository(repo)
                    
                logger.info(f"共保存 {len(processed_repos)} 个仓库数据到文件目录: {data_dir}")
                logger.info(f"汇总文件已保存至: {saved_file}")
            else:
                logger.info("没有新的仓库数据需要保存")
        except Exception as e:
            logger.error(f"保存到文件失败: {str(e)}")
    except Exception as e:
        logger.error(f"抓取过程发生错误: {str(e)}")
        
        # 如果已经获取了处理后的数据，尝试保存到本地
        if processed_repos:
            try:
                backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backup')
                os.makedirs(backup_dir, exist_ok=True)
                
                emergency_backup_file = os.path.join(backup_dir, f'emergency_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
                with open(emergency_backup_file, 'w', encoding='utf-8') as f:
                    json.dump(processed_repos, f, ensure_ascii=False, indent=2)
                logger.info(f"发生错误，已创建应急备份: {emergency_backup_file}")
            except Exception as backup_err:
                logger.error(f"创建应急备份失败: {str(backup_err)}")

def run_update(days=None):
    """更新已有仓库数据"""
    from scheduler.tasks import update_outdated_repositories
    from config import UPDATE_INTERVAL_DAYS
    
    if days is None:
        days = UPDATE_INTERVAL_DAYS
    
    logger.info(f"开始更新 {days} 天前的仓库数据")
    result = update_outdated_repositories(days)
    logger.info(f"更新结果: {result}")

def run_worker():
    """启动Celery Worker"""
    from scheduler.tasks import app as celery_app
    
    logger.info("启动Celery Worker")
    
    argv = [
        'worker',
        '--loglevel=INFO',
        '--concurrency=4'
    ]
    
    celery_app.worker_main(argv)

def run_beat():
    """启动Celery Beat (定时器)"""
    from scheduler.tasks import app as celery_app
    
    logger.info("启动Celery Beat")
    
    argv = [
        'beat',
        '--loglevel=INFO'
    ]
    
    celery_app.worker_main(argv)

def signal_handler(sig, frame):
    """处理程序中断信号"""
    logger.info("程序被用户中断")
    sys.exit(0)

def main():
    """主函数"""
    signal.signal(signal.SIGINT, signal_handler)
    
    parser = argparse.ArgumentParser(description="GitHub AI 项目抓取系统")
    subparsers = parser.add_subparsers(dest="command", help="命令")
    
    # 添加API服务器子命令
    api_parser = subparsers.add_parser("api", help="启动API服务器")
    api_parser.add_argument("--host", default="0.0.0.0", help="监听主机")
    api_parser.add_argument("--port", type=int, default=8000, help="监听端口")
    
    # 添加爬虫子命令
    scrape_parser = subparsers.add_parser("scrape", help="运行爬虫抓取GitHub AI项目")
    scrape_parser.add_argument("--min-stars", type=int, default=1000, help="最低星标数")
    scrape_parser.add_argument("--direct", action="store_true", help="直接获取热门仓库模式，跳过搜索")
    
    # 更新命令
    update_parser = subparsers.add_parser("update", help="更新已有仓库数据")
    update_parser.add_argument("--days", type=int, help="更新几天前的数据")
    
    # Worker命令
    subparsers.add_parser("worker", help="启动Celery Worker")
    
    # Beat命令
    subparsers.add_parser("beat", help="启动Celery Beat (定时器)")
    
    args = parser.parse_args()
    
    if args.command == "api":
        run_api_server(args.host, args.port)
    elif args.command == "scrape":
        run_scraper(args.min_stars, args.direct)
    elif args.command == "update":
        run_update(args.days)
    elif args.command == "worker":
        run_worker()
    elif args.command == "beat":
        run_beat()
    else:
        parser.print_help()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.exception(f"程序异常: {str(e)}")
        sys.exit(1) 