import requests
import time
import logging
import json
import os
import concurrent.futures
from typing import List, Dict, Any, Optional
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
# 直接在代码中设置GitHub Token，不使用环境变量
GITHUB_TOKEN = "****************************************"
from config import RATE_LIMIT_WAIT
from scraper.processor import DataProcessor

logger = logging.getLogger(__name__)

class GitHubScraper:
    """GitHub API交互类，用于抓取AI项目信息"""
    
    BASE_URL = "https://api.github.com"
    
    def __init__(self, token: str = GITHUB_TOKEN, output_dir: str = "data", max_workers: int = 5):
        # 使用硬编码的token
        self.token = GITHUB_TOKEN
        logger.info(f"使用硬编码的GitHub Token: {self.token[:4]}...{self.token[-4:]}")
        
        self.headers = {
            "Authorization": f"token {self.token}",
            "Accept": "application/vnd.github.v3+json"
        }
        self.output_dir = output_dir
        self.max_workers = max_workers
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(os.path.join(output_dir, "repos"), exist_ok=True)
        
        # 记录已处理的仓库ID
        self.processed_repo_ids_file = os.path.join(output_dir, "processed_repo_ids.json")
        self.processed_repo_ids = self._load_processed_repo_ids()
        
        # 创建数据处理器
        self.processor = DataProcessor()
    
    def _load_processed_repo_ids(self) -> set:
        """加载已处理的仓库ID"""
        if os.path.exists(self.processed_repo_ids_file):
            try:
                with open(self.processed_repo_ids_file, 'r') as f:
                    return set(json.load(f))
            except Exception as e:
                logger.error(f"加载已处理仓库ID时出错: {str(e)}")
                return set()
        return set()
    
    def _save_processed_repo_ids(self) -> None:
        """保存已处理的仓库ID"""
        try:
            with open(self.processed_repo_ids_file, 'w') as f:
                json.dump(list(self.processed_repo_ids), f)
        except Exception as e:
            logger.error(f"保存已处理仓库ID时出错: {str(e)}")
    
    def _save_repo_data(self, repo_data: Dict) -> None:
        """保存单个仓库数据到文件，使用仓库名作为文件名并按processor格式处理"""
        try:
            # 记录原始数据基本信息以便调试
            repo_id = repo_data.get("id")
            repo_name = repo_data.get("name", "unknown")
            logger.info(f"开始处理仓库数据: ID={repo_id}, 名称={repo_name}")
            
            # 尝试使用processor处理数据，添加更多错误捕获
            try:
                processed_data = self.processor.process_repository(repo_data)
                logger.info(f"处理器成功处理数据: {repo_name}")
            except Exception as process_error:
                logger.error(f"处理器处理数据失败: {str(process_error)}")
                # 如果处理器失败，我们使用原始数据
                processed_data = repo_data
                logger.info("将使用原始数据保存文件")
            
            # 确保文件名合法（移除特殊字符）
            safe_repo_name = "".join(c for c in repo_name if c.isalnum() or c in ['-', '_'])
            logger.info(f"使用安全文件名: {safe_repo_name}")
            
            # 构建文件路径并检查目录
            repos_dir = os.path.join(self.output_dir, "repos")
            if not os.path.exists(repos_dir):
                logger.info(f"创建目录: {repos_dir}")
                os.makedirs(repos_dir, exist_ok=True)
                
            repo_file = os.path.join(repos_dir, f"{safe_repo_name}.json")
            logger.info(f"准备写入文件: {repo_file}")
            
            # 如果存在同名文件，附加ID以避免冲突
            if os.path.exists(repo_file):
                logger.info(f"文件已存在，将附加ID: {repo_file}")
                repo_file = os.path.join(repos_dir, f"{safe_repo_name}_{repo_id}.json")
                logger.info(f"新文件名: {repo_file}")
            
            # 写入文件
            with open(repo_file, 'w', encoding='utf-8') as f:
                logger.info(f"写入数据到文件: {repo_file}")
                json.dump(processed_data, f, indent=2, ensure_ascii=False)
                
            logger.info(f"成功保存仓库数据: {repo_file}")
            
            # 检查文件是否实际写入
            if os.path.exists(repo_file):
                file_size = os.path.getsize(repo_file)
                logger.info(f"确认文件已创建: {repo_file}, 大小: {file_size}字节")
            else:
                logger.error(f"文件似乎未创建: {repo_file}")
                
        except Exception as e:
            logger.error(f"保存仓库数据时出错 (ID: {repo_data.get('id')}, 名称: {repo_data.get('name')}): {str(e)}")
            # 打印详细错误信息和堆栈跟踪
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
    
    def _make_request(self, url: str, params: Optional[Dict] = None) -> Dict:
        """发送请求到GitHub API并处理速率限制"""
        if params is None:
            params = {}
            
        max_retries = 5
        retry_count = 0
        
        # 添加更多日志信息以便于调试
        logger.info(f"准备请求: {url}")
        logger.info(f"使用token: {self.token[:4]}...{self.token[-4:]}")
        logger.info(f"请求头: {self.headers}")
        
        while retry_count < max_retries:
            try:
                response = requests.get(url, headers=self.headers, params=params, timeout=30)
                
                # 记录响应详情
                logger.info(f"响应状态码: {response.status_code}")
                if 'x-ratelimit-remaining' in response.headers:
                    logger.info(f"API剩余请求数: {response.headers['x-ratelimit-remaining']}")
                
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 403 and 'x-ratelimit-remaining' in response.headers and int(response.headers['x-ratelimit-remaining']) == 0:
                    reset_time = int(response.headers['x-ratelimit-reset'])
                    sleep_time = reset_time - time.time() + 1
                    logger.warning(f"Rate limit exceeded. Waiting for {sleep_time} seconds")
                    time.sleep(max(sleep_time, RATE_LIMIT_WAIT))
                elif response.status_code == 404:
                    logger.warning(f"资源不存在: {url}")
                    return {}
                else:
                    logger.error(f"请求错误: {response.status_code} - {response.text}")
                    retry_count += 1
                    sleep_time = 2 ** retry_count  # 指数退避策略
                    logger.info(f"等待 {sleep_time} 秒后重试 ({retry_count}/{max_retries})")
                    time.sleep(sleep_time)
            except requests.exceptions.RequestException as e:
                logger.error(f"请求异常: {str(e)}")
                retry_count += 1
                sleep_time = 2 ** retry_count
                logger.info(f"等待 {sleep_time} 秒后重试 ({retry_count}/{max_retries})")
                time.sleep(sleep_time)
        
        logger.error(f"请求失败，已达到最大重试次数: {url}")
        return {}
    
    def search_ai_repositories(self, min_stars: int = 500, page: int = 1, per_page: int = 100) -> Dict:
        """搜索AI相关的GitHub仓库，将长查询拆分为多个简单独立的查询"""
        # 结果合并
        all_results = {"items": [], "total_count": 0}
        
        # 修改搜索顺序，先使用关键词搜索
        # 1. 通过单独关键词搜索
        self._search_by_single_keywords(all_results, min_stars, page, per_page)
        
        # 2. 通过单独的框架/库搜索
        self._search_by_frameworks(all_results, min_stars, page, per_page)
        
        # 3. 通过热门AI主题搜索（每次只使用1-2个主题）
        self._search_by_popular_topics(all_results, min_stars, page, per_page)
        
        return all_results
    
    def _search_by_popular_topics(self, results: Dict, min_stars: int, page: int, per_page: int) -> None:
        """仅通过热门AI主题搜索，每次搜索只用1-2个主题"""
        # 热门AI主题列表
        popular_topics = [
            "ai",
            "machine-learning",
            "deep-learning", 
            "artificial-intelligence",
            "nlp",
            "computer-vision",
            "reinforcement-learning",
            "generative-ai",
            "llm",
            "large-language-model",
            "transformer",
            "diffusion-model",
            "neural-network",
            "ai-tools",
            "chatgpt",
            "openai",
            "huggingface",
            "ai-agents",
            "vector-database",
            "knowledge-base"
        ]
        
        # 每个主题单独搜索，避免使用过多的布尔操作符
        for topic in popular_topics:
            logger.info(f"执行单一主题搜索: topic:{topic}")
            query = f"stars:>={min_stars} topic:{topic}"
            
            try:
                topic_results = self._execute_search(query, page, per_page)
                
                # 检查是否达到了星标下限，如果达到了就跳过当前主题，继续下一个
                if topic_results.get("reached_star_limit", False):
                    logger.info(f"主题 '{topic}' 搜索结果中存在低于最低星标({min_stars})的仓库，跳过此主题")
                    continue
                
                self._merge_results(results, topic_results, min_stars)
                logger.info(f"主题 '{topic}' 搜索结果: {len(topic_results.get('items', []))} 个仓库")
                
                # 立即处理搜索结果
                repos = topic_results.get("items", [])
                if repos:
                    logger.info(f"立即处理主题 '{topic}' 搜索到的 {len(repos)} 个仓库")
                    self._process_repos_batch(repos)
                
                time.sleep(1)  # 短暂延迟避免触发速率限制
            except Exception as e:
                logger.error(f"主题搜索失败 - {topic}: {str(e)}")
    
    def _search_by_frameworks(self, results: Dict, min_stars: int, page: int, per_page: int) -> None:
        """搜索主要AI框架和库"""
        frameworks = [
            "tensorflow",
            "pytorch", 
            "huggingface",
            "openai",
            "langchain",
            "scikit-learn",
            "keras",
            "fastai",
            "jax",
            "llamaindex",
            "autogen",
            "diffusers",
            "transformers",
            "haystack",
            "llama-cpp",
            "whisper",
            "anthropic",
            "milvus",
            "chroma",
            "faiss"
        ]
        
        for framework in frameworks:
            logger.info(f"执行框架搜索: {framework}")
            
            # 框架名称搜索 - 简单查询无布尔操作符
            query = f"stars:>={min_stars} {framework} in:name,description,readme"
            
            try:
                framework_results = self._execute_search(query, page, per_page)
                
                # 检查是否达到了星标下限，如果达到了就跳过当前框架，继续下一个
                if framework_results.get("reached_star_limit", False):
                    logger.info(f"框架 '{framework}' 搜索结果中存在低于最低星标({min_stars})的仓库，跳过此框架")
                    continue
                
                self._merge_results(results, framework_results, min_stars)
                logger.info(f"框架 '{framework}' 搜索结果: {len(framework_results.get('items', []))} 个仓库")
                
                # 立即处理搜索结果
                repos = framework_results.get("items", [])
                if repos:
                    logger.info(f"立即处理框架 '{framework}' 搜索到的 {len(repos)} 个仓库")
                    self._process_repos_batch(repos)
                
                time.sleep(1)
            except Exception as e:
                logger.error(f"框架搜索失败 - {framework}: {str(e)}")
    
    def _search_by_single_keywords(self, results: Dict, min_stars: int, page: int, per_page: int) -> None:
        """使用单独关键词搜索，无复杂布尔逻辑"""
        keywords = [
            # 核心AI关键词
            "artificial intelligence",
            "machine learning",
            "deep learning",
            "neural network",
            "language model",
            
            # 流行模型与框架
            "gpt",
            "llama",
            "mistral",
            "anthropic",
            "claude",
            "gemini",
            "chatgpt",
            "copilot",
            
            # 模型能力
            "diffusion",
            "transformer",
            "fine-tuning",
            "rag",
            "chatbot",
            "vector database",
            "embeddings",
            "multimodal",
            
            # 具体技术与应用
            "text-to-image",
            "speech recognition",
            "voice assistant",
            "nlp",
            "computer vision",
            "reinforcement learning",
            "stable diffusion",
            "generative ai",
            "agent",
            "autonomous agent",
            
            # AI工具和平台
            "ai assistant",
            "ai tool",
            "llm framework",
            "ai platform",
            "ai studio",
            "prompt engineering",
            "ai workflow",
            "ai automation",
            "ai api",
            "ai orchestration",
            "document ai",
            "ai pipeline",
            "ai ui",
            "chatgpt plugin",
            "ai integration",
            "ai monitoring",
            "ai explainer",
            "ai evaluation",
            "ai visualization",
            "knowledge graph",
            
            # 开发工具
            "ai coding assistant",
            "code generator",
            "developer tools",
            "code explainer",
            "ai debugging",
            "semantic search",
            "ai testing",
            "model deployment",
            
            # 从processor.py提取的更多细分关键词
            # 模型开发与研究
            "model repository", "model zoo", "model hub", "pretrained models", "foundation models",
            "parameter efficient fine-tuning", "peft", "lora", "qlora", "adapter", "distributed training",
            "gradient accumulation", "mixed precision", "deepspeed", "accelerate", 
            "inference optimization", "quantization", "pruning", "distillation", "compression",
            "int8", "int4", "fp16", "bfloat16", "KV cache", "inference acceleration", "model compression",
            "hardware acceleration",
            "dataset engineering", "data augmentation", "data preprocessing", "data annotation", 
            "synthetic data", "data generation", "data cleaning", "data labeling",
            "vision-language", "vision transformer", "clip", "imagen", "3d generation", "audio processing",
            "speech synthesis", "multilingual", "cross-lingual", "language understanding", 
            "language detection", "instruction tuning", "alignment", 
            "code model", "code completion", "code understanding", "codegen", "code llm", 
            "programming assistant", "code interpreter", "code synthesis", "code translation",
            
            # AI工程化工具
            "langchain", "autogen", "crewai", "babyagi", "agentverse", "llamaindex", 
            "tool use", "function calling", "planning", "reasoning", "agent memory", "tool augmented",
            "ai interface", "chatbot interface", "ai interaction", "chat ui", "api wrapper",
            "chain of thought", "few-shot", "in-context learning", "prompt tuning", "zero-shot",
            "prompt optimization", "system prompt", "instruction engineering",
            "benchmark", "leaderboard", "performance testing", "precision", "recall", "f1 score",
            "human evaluation", "retrieval augmented generation", "document retrieval", "knowledge base",
            "document indexing", "context augmentation",
            
            # 实用应用
            "ide plugin", "vscode extension", "code review", "lint", "static analysis", "code documentation",
            "code search", "code suggestion", "code transformation",
            "information retrieval", "knowledge retrieval", "information extraction", "question answering",
            "document qa", "information synthesis", "summarization",
            "workflow automation", "task automation", "no-code", "low-code", "business process",
            "workflow engine", "automation platform", "robotic process automation",
            "conversational ai", "dialogue system", "customer service bot", "chat interface",
            "chat application", "chat agent",
            "midjourney", "dall-e", "gan", "diffusion model", "image editing", "image inpainting",
            "style transfer", "image synthesis", "generative art",
            "video synthesis", "motion generation", "animation", "video diffusion", "video transformation",
            "frame prediction", "content creation", "blog writer", "story generation", "storytelling",
            "copywriting", "article writer", "essay writing", "creative writing", "script generation",
            "data visualization", "data analysis", "data dashboard", "data exploration",
            
            # 基础设施与支持
            "embedding store", "vector store", "pinecone", "qdrant", "vector index", "semantic cache",
            "similarity search", "approximate nearest neighbors", "ann",
            "model serving", "inference server", "inference api", "serverless inference", "triton",
            "torchserve", "model hosting", "prediction service", "model endpoint", "service mesh",
            "compute management", "gpu management", "cluster management", "kubernetes", "docker",
            "containerization", "orchestration", "resource allocation", "cloud deployment",
            "infrastructure as code", "observability", "logging", "tracing", "prometheus", "grafana",
            "model monitoring", "performance tracking", "data drift", "concept drift", "dashboard",
            "ci/cd", "version control", "debugging", "profiling", "code quality", "testing framework",
            "development kit", "sdk", "api documentation",
            "data versioning", "data lineage", "data catalog", "dvc", "mlflow", "metadata management",
            "feature store", "data registry", "data governance", "data quality",
            
            # 教育与参考资源
            "tutorial", "guide", "course", "learning resource", "documentation", "handbook",
            "getting started", "best practices", "cookbook", "example notebooks", "workbook",
            "awesome list", "awesome repo", "curated list", "resource list", "toolkit",
            "reference implementation", "starter kit", "template collection", "boilerplate",
            "example", "demo", "sample code", "showcase", "demonstration", "case study",
            "proof of concept", "example project", "playground", "interactive demo", "sample application",
            
            # 技术细分领域
            "natural language understanding", "natural language generation", "sentiment analysis",
            "word embedding", "language representation", "text analysis",
            "object detection", "image classification", "segmentation", "face recognition", "pose estimation",
            "depth estimation", "cnn", "rnn", "lstm", "attention mechanism", "backpropagation",
            "gradient descent", "activation function", "classification", "regression", "clustering",
            "decision tree", "svm", "feature engineering", "model selection", "hyperparameter tuning",
            "ensemble methods", "q-learning", "dqn", "policy gradient", "reward function",
            "environment", "action space", "state space", "exploration"
        ]
        
        for keyword in keywords:
            logger.info(f"执行单一关键词搜索: '{keyword}'")
            
            # 单一关键词搜索 - 最简单的查询
            query = f"stars:>={min_stars} \"{keyword}\" in:description,readme"
            
            try:
                keyword_results = self._execute_search(query, page, per_page)
                
                # 检查是否达到了星标下限，如果达到了就跳过当前关键词，继续下一个
                if keyword_results.get("reached_star_limit", False):
                    logger.info(f"关键词 '{keyword}' 搜索结果中存在低于最低星标({min_stars})的仓库，跳过此关键词")
                    continue
                
                self._merge_results(results, keyword_results, min_stars)
                logger.info(f"关键词 '{keyword}' 搜索结果: {len(keyword_results.get('items', []))} 个仓库")
                
                # 立即处理搜索结果
                repos = keyword_results.get("items", [])
                if repos:
                    logger.info(f"立即处理关键词 '{keyword}' 搜索到的 {len(repos)} 个仓库")
                    self._process_repos_batch(repos)
                
                time.sleep(1)  # 短暂延迟避免触发速率限制
            except Exception as e:
                logger.error(f"关键词搜索失败 - {keyword}: {str(e)}")
    
    def _execute_search(self, query: str, page: int, per_page: int) -> Dict:
        """执行单个搜索查询"""
        url = f"{self.BASE_URL}/search/repositories"
        params = {
            "q": query,
            "sort": "stars",
            "order": "desc",
            "page": page,
            "per_page": per_page
        }
        
        result = self._make_request(url, params)
        
        # 检查是否有低于最低星标的仓库（因为是逆序搜索，如果有一个低于，那么后面都会低于）
        if result and "items" in result and result["items"]:
            # 提取query中的min_stars值
            min_stars_str = ""
            for part in query.split():
                if part.startswith("stars:>="):
                    min_stars_str = part[8:]
                    break
            
            if min_stars_str:
                try:
                    min_stars = int(min_stars_str)
                    # 检查结果中的最后一个仓库（星标最少的）
                    last_repo = result["items"][-1]
                    if last_repo["stargazers_count"] < min_stars:
                        # 添加标记，表示已经达到了星标的下限
                        result["reached_star_limit"] = True
                except (ValueError, KeyError) as e:
                    logger.warning(f"解析星标或检查结果时出错: {str(e)}")
        
        return result
    
    def _merge_results(self, target: Dict, source: Dict, min_stars: int = 0) -> None:
        """合并搜索结果，去重，并过滤掉星标数低于指定值的仓库"""
        if not source or "items" not in source:
            return
            
        existing_ids = {item["id"] for item in target["items"]}
        
        # 遍历源结果，只合并星标数达到要求的仓库
        for item in source.get("items", []):
            if item["id"] not in existing_ids:
                # 只有星标数大于等于min_stars的仓库才会被合并
                if min_stars <= 0 or item.get("stargazers_count", 0) >= min_stars:
                    target["items"].append(item)
                    existing_ids.add(item["id"])
        
        # 更新总数
        target["total_count"] = len(target["items"])
    
    def get_repository_details(self, repo_full_name: str) -> Dict:
        """获取仓库的详细信息"""
        url = f"{self.BASE_URL}/repos/{repo_full_name}"
        return self._make_request(url)
    
    def get_repository_topics(self, repo_full_name: str) -> List[str]:
        """获取仓库的主题标签"""
        url = f"{self.BASE_URL}/repos/{repo_full_name}/topics"
        response = self._make_request(url)
        return response.get("names", [])
    
    def process_repository(self, repo: Dict) -> Dict:
        """处理单个仓库获取详细信息"""
        repo_id = repo.get("id")
        repo_full_name = repo.get("full_name")
        
        if repo_id in self.processed_repo_ids:
            logger.info(f"仓库已处理过，跳过: {repo_full_name}")
            return None
            
        try:
            logger.info(f"获取仓库详情: {repo_full_name}")
            details = self.get_repository_details(repo_full_name)
            if not details:
                logger.warning(f"无法获取仓库详情: {repo_full_name}")
                return None
                
            topics = self.get_repository_topics(repo_full_name)
            details["topics"] = topics
            
            # 记录已处理的仓库ID
            self.processed_repo_ids.add(repo_id)
            self._save_processed_repo_ids()
            
            # 保存仓库数据
            self._save_repo_data(details)
            
            logger.info(f"仓库处理完成: {repo_full_name}")
            return details
        except Exception as e:
            logger.error(f"处理仓库时出错 {repo_full_name}: {str(e)}")
            return None
    
    def get_popular_ai_repos_directly(self) -> List[Dict]:
        """直接获取知名AI仓库，不依赖搜索功能"""
        popular_repos = [
            "huggingface/transformers",
            "openai/openai-cookbook",
            "microsoft/DeepSpeed",
            "tensorflow/tensorflow",
            "pytorch/pytorch", 
            "langchain-ai/langchain",
            "microsoft/onnxruntime",
            "google/jax",
            "meta-llama/llama",
            "AUTOMATIC1111/stable-diffusion-webui",
            "Lightning-AI/lightning",
            "scikit-learn/scikit-learn",
            "keras-team/keras",
            "fastai/fastai",
            "ray-project/ray"
        ]
        
        logger.info(f"尝试直接获取{len(popular_repos)}个流行的AI仓库")
        detailed_repos = []
        
        # 使用线程池处理仓库
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 准备仓库信息
            repo_info_list = []
            for repo_full_name in popular_repos:
                repo_info = {"full_name": repo_full_name}
                # 尝试从名称中提取ID（这里只是占位，实际处理时会获取正确的ID）
                repo_info["id"] = repo_full_name.replace("/", "_")
                repo_info_list.append(repo_info)
            
            # 并行处理仓库
            future_to_repo = {executor.submit(self.process_repository, repo): repo for repo in repo_info_list}
            for future in concurrent.futures.as_completed(future_to_repo):
                repo = future_to_repo[future]
                try:
                    details = future.result()
                    if details:
                        detailed_repos.append(details)
                except Exception as e:
                    logger.error(f"处理仓库时出错 {repo.get('full_name')}: {str(e)}")
        
        return detailed_repos
    
    def get_all_ai_repositories(self, min_stars: int = 500, max_pages: int = 50) -> List[Dict]:
        """批量抓取所有符合条件的AI仓库，支持断点续传和并行处理"""
        # 保存已处理仓库的详细信息
        detailed_repos = []
        
        # 尝试直接获取知名仓库
        popular_repos = self.get_popular_ai_repos_directly()
        detailed_repos.extend(popular_repos)
        
        # 使用搜索API批量获取更多仓库
        all_repos = []
        
        # 从多个页面获取搜索结果
        for page in range(1, max_pages + 1):
            try:
                logger.info(f"正在获取第{page}页搜索结果（每页100个）")
                response = self.search_ai_repositories(min_stars, page, 100)
                repos = response.get("items", [])
                
                if not repos:
                    logger.info("没有更多搜索结果")
                    break
                
                # 检查是否有低于最低星标的仓库
                star_limit_reached = False
                if repos:
                    # 检查最后一个仓库的星标数（按照stars:desc排序，最后一个应该是星数最少的）
                    last_repo = repos[-1]
                    if last_repo.get("stargazers_count", 0) < min_stars:
                        logger.info(f"搜索结果中包含星标数低于{min_stars}的仓库，停止获取下一页")
                        star_limit_reached = True
                    
                # 添加新的结果到总列表
                all_repos.extend(repos)
                
                logger.info(f"已获取 {len(all_repos)} 个仓库")
                
                # 立即处理每页的结果，不要等待积累
                logger.info(f"立即处理获取到的 {len(repos)} 个仓库")
                self._process_repos_batch(repos)
                
                # 检查是否需要停止获取下一页
                if star_limit_reached or len(repos) < 100:
                    if len(repos) < 100:
                        logger.info("已到达搜索结果最后一页")
                    break
                
                # 翻页并增加延迟以避免触发GitHub API速率限制
                logger.info(f"延迟3秒后获取下一页")
                time.sleep(3)
            except Exception as e:
                logger.error(f"搜索仓库时出错: {str(e)}")
                # 如果遇到错误，等待一段时间后继续
                logger.info("30秒后重试...")
                time.sleep(30)
                continue
        
        # 处理剩余仓库
        if all_repos:
            self._process_repos_batch(all_repos)
        
        logger.info(f"批量抓取完成，所有数据已保存到 {self.output_dir} 目录")
        
        # 返回已处理的仓库ID列表
        return list(self.processed_repo_ids)
    
    def _process_repos_batch(self, repos: List[Dict]) -> None:
        """并行处理一批仓库"""
        logger.info(f"开始并行处理 {len(repos)} 个仓库")
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_repo = {executor.submit(self.process_repository, repo): repo for repo in repos}
            completed = 0
            
            for future in concurrent.futures.as_completed(future_to_repo):
                repo = future_to_repo[future]
                completed += 1
                
                try:
                    future.result()  # 获取结果，但我们不需要存储它，因为已经在process_repository中保存了
                except Exception as e:
                    logger.error(f"处理仓库时出错 {repo.get('full_name')}: {str(e)}")
                
                # 每处理10个仓库输出一次进度
                if completed % 10 == 0:
                    logger.info(f"进度: {completed}/{len(repos)}")
        
        logger.info(f"批处理完成，共处理 {len(repos)} 个仓库")
    
    def batch_scrape(self, min_stars: int = 100, max_pages: int = 100) -> None:
        """执行批量大规模抓取，降低最低星标要求，增加最大页数"""
        logger.info(f"开始批量抓取GitHub AI项目 (最低星标: {min_stars}, 最大页数: {max_pages})")
        self.get_all_ai_repositories(min_stars, max_pages) 