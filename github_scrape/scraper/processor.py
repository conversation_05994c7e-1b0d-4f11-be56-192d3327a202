import logging
from typing import Dict, List, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class DataProcessor:
    """处理从GitHub API获取的原始数据"""
    
    def __init__(self):
        # 可用于AI项目分类的关键词
        self.ai_categories = {
            # 1. 模型开发与研究
            "model_repository": ["model repository", "模型库", "模型仓库", "model zoo", "model hub", 
                                "huggingface", "model collection", "预训练模型", "pretrained models",
                                "weights", "checkpoints", "foundation models", "基础模型"],
            "model_training": ["training", "fine-tuning", "微调", "模型训练", "pretrain", "finetune", 
                              "parameter efficient fine-tuning", "peft", "lora", "qlora", "adapter",
                              "training framework", "训练框架", "distributed training", "分布式训练",
                              "gradient accumulation", "梯度累积", "混合精度训练", "mixed precision", 
                              "deepspeed", "accelerate", "optimizers", "优化器"],
            "inference_optimization": ["inference", "推理优化", "quantization", "量化", "pruning", "剪枝", 
                                      "distillation", "蒸馏", "compression", "onnx", "tensorrt", "vllm",
                                      "int8", "int4", "fp16", "bfloat16", "KV cache", "推理加速", 
                                      "inference acceleration", "模型压缩", "model compression", 
                                      "hardware acceleration", "硬件加速"],
            "dataset_engineering": ["dataset", "数据集", "data engineering", "数据工程", "data augmentation", 
                                   "数据增强", "data preprocessing", "数据预处理", "data annotation", "标注",
                                   "synthdatic data", "合成数据", "data generation", "数据生成", "data cleaning",
                                   "数据清洗", "data collection", "数据收集", "data labeling", "数据标记"],
            "multimodal_models": ["multimodal", "多模态", "vision-language", "视觉语言", "text-to-image", 
                                 "文本到图像", "text-to-video", "文本到视频", "audio-visual", "音视频",
                                 "vision transformer", "视觉transformer", "clip", "imagen", "video generation",
                                 "视频生成", "3d generation", "三维生成", "音频处理", "audio processing",
                                 "speech recognition", "语音识别", "speech synthesis", "语音合成"],
            "multilingual_models": ["multilingual", "多语言", "cross-lingual", "跨语言", "translation", 
                                   "翻译", "language understanding", "语言理解", "language detection",
                                   "语言检测", "language model", "语言模型", "llm", "gpt", "chat model",
                                   "对话模型", "instruction tuning", "指令微调", "alignment", "对齐"],
            "code_models": ["code model", "代码模型", "code generation", "代码生成", "code assistant", 
                           "code completion", "代码补全", "code understanding", "代码理解", "codegen",
                           "code llm", "programming assistant", "编程助手", "code interpreter", "代码解释器",
                           "code synthesis", "代码合成", "code translation", "代码翻译", "copilot"],
            
            # 2. AI工程化工具
            "ai_framework": ["ai framework", "ai开发框架", "deep learning framework", "machine learning framework",
                           "tensorflow", "pytorch", "jax", "keras", "fastai", "paddle", "mxnet",
                           "lightning", "ignite", "flux", "oneflow", "mindspore", "深度学习框架", 
                           "机器学习框架", "神经网络框架"],
            "agent_systems": ["agent", "智能代理", "autonomous agent", "agent framework", "multi-agent", 
                             "langchain", "autogen", "crewai", "babyagi", "agentverse", "llamaindex",
                             "tool use", "工具使用", "function calling", "函数调用", "planning", "规划", 
                             "react", "reasoning", "推理", "action", "行动", "agent memory", "代理记忆",
                             "tool augmented", "工具增强", "agentic", "自主性"],
            "ai_interface": ["ai interface", "ai交互接口", "ai ui", "chatbot interface", "ai interaction",
                            "ui framework for ai", "ai前端", "ai应用界面", "chat ui", "聊天界面",
                            "ai app", "ai应用", "web ui", "网页界面", "api wrapper", "api封装", 
                            "ai integration", "ai集成", "chatbot ui", "对话机器人界面"],
            "prompt_engineering": ["prompt engineering", "提示工程", "prompt", "提示词", "prompt template", 
                                  "chain of thought", "few-shot", "in-context learning", "prompt tuning",
                                  "zero-shot", "prompt optimization", "提示优化", "system prompt", "系统提示词",
                                  "instruction", "指令", "instruction engineering", "指令工程"],
            "evaluation_tools": ["evaluation", "评估", "benchmark", "基准测试", "metric", "指标", 
                                "测试工具", "model evaluation", "模型评估", "leaderboard", "排行榜", 
                                "performance testing", "性能测试", "accuracy", "准确率", "precision", "精确度",
                                "recall", "召回率", "f1 score", "human evaluation", "人工评估"],
            "rag_systems": ["rag", "retrieval augmented generation", "检索增强生成", "vector search", 
                           "向量检索", "document retrieval", "文档检索", "knowledge base", "知识库", 
                           "embedding", "嵌入", "semantic search", "语义搜索", "faiss", "chromadb", 
                           "pinecone", "milvus", "weaviate", "向量数据库", "vector database",
                           "document indexing", "文档索引", "context augmentation", "上下文增强"],
            
            # 3. 实用应用
            "coding_assistance": ["coding assistant", "编程辅助", "code generation", "代码生成", "ide plugin", 
                                "vscode extension", "coding tool", "code review", "代码审查", "lint", "静态分析",
                                "code explanation", "代码解释", "code documentation", "代码文档", "code search",
                                "代码搜索", "code suggestion", "代码建议", "code transformation", "代码转换"],
            "information_retrieval": ["information retrieval", "信息检索", "search", "搜索", "knowledge retrieval", 
                                     "知识检索", "information extraction", "信息提取", "semantic search", 
                                     "语义搜索", "question answering", "问答系统", "document qa", "文档问答",
                                     "information synthesis", "信息合成", "summarization", "摘要"],
            "workflow_automation": ["workflow", "工作流", "automation", "自动化", "pipeline", "流程", 
                                   "task automation", "任务自动化", "no-code", "low-code", "无代码", "低代码",
                                   "business process", "业务流程", "workflow engine", "工作流引擎", 
                                   "automation platform", "自动化平台", "robotic process automation", "rpa"],
            "conversational_bots": ["chatbot", "聊天机器人", "conversational ai", "对话系统", "virtual assistant", 
                                   "虚拟助手", "dialogue system", "对话模型", "customer service bot", "客服机器人",
                                   "voice assistant", "语音助手", "chat interface", "聊天界面", "chat application",
                                   "聊天应用", "conversation", "会话", "chat agent", "聊天代理"],
            "image_creation": ["image generation", "图像生成", "text-to-image", "文本到图像", "stable diffusion", 
                             "midjourney", "dall-e", "gan", "diffusion model", "图像创作", "image editing",
                             "图像编辑", "image inpainting", "图像修复", "style transfer", "风格迁移",
                             "image synthesis", "图像合成", "generative art", "生成艺术"],
            "video_creation": ["video generation", "视频生成", "text-to-video", "文本到视频", "video synthesis", 
                              "video editing", "视频编辑", "motion generation", "动作生成", "animation",
                              "动画", "video diffusion", "视频扩散", "video transformation", "视频变换",
                              "video processing", "视频处理", "frame prediction", "帧预测"],
            "text_creation": ["text generation", "文本生成", "content creation", "内容创作", "blog writer", 
                             "story generation", "storytelling", "copywriting", "文案", "article writer",
                             "文章生成", "essay writing", "论文写作", "creative writing", "创意写作",
                             "script generation", "脚本生成", "text completion", "文本补全"],
            "data_organization": ["data organization", "数据组织", "data management", "数据管理", 
                                 "data visualization", "数据可视化", "data analysis", "数据分析",
                                 "data dashboard", "数据仪表盘", "data exploration", "数据探索",
                                 "data processing", "数据处理", "data integration", "数据集成",
                                 "data platform", "数据平台", "data tools", "数据工具"],
            
            # 4. 基础设施与支持
            "vector_database": ["vector database", "向量数据库", "embedding store", "vector store", 
                               "pinecone", "faiss", "milvus", "weaviate", "chroma", "qdrant",
                               "vector index", "向量索引", "semantic cache", "语义缓存", "vector search",
                               "similarity search", "相似度搜索", "approximate nearest neighbors", "ann"],
            "model_serving": ["model serving", "模型部署", "model deployment", "inference server", 
                             "inference api", "model api", "serverless inference", "triton", "torchserve",
                             "model hosting", "模型托管", "api service", "api服务", "prediction service",
                             "预测服务", "model endpoint", "模型端点", "service mesh", "服务网格"],
            "compute_management": ["compute management", "计算资源管理", "gpu management", "gpu调度", 
                                  "cluster management", "集群管理", "kubernetes", "docker", "容器化",
                                  "containerization", "orchestration", "编排", "resource allocation",
                                  "资源分配", "cloud deployment", "云部署", "infrastructure as code", "iac"],
            "monitoring_observability": ["monitoring", "监控", "observability", "可观测性", "logging", "日志", 
                                        "tracing", "追踪", "prometheus", "grafana", "ai监控", "model monitoring",
                                        "模型监控", "performance tracking", "性能跟踪", "data drift", "数据漂移",
                                        "concept drift", "概念漂移", "alert", "警报", "dashboard", "仪表板"],
            "development_tools": ["development tools", "开发工具", "ide", "开发环境", "ci/cd", "持续集成", 
                                 "version control", "版本控制", "debugging", "调试工具", "profiling", "性能分析",
                                 "code quality", "代码质量", "testing framework", "测试框架", "development kit",
                                 "开发套件", "sdk", "api documentation", "api文档"],
            "data_management": ["data management platform", "数据管理平台", "data versioning", "数据版本控制", 
                               "data lineage", "数据血缘", "data catalog", "数据目录", "dvc", "mlflow",
                               "metadata management", "元数据管理", "feature store", "特征存储", "data registry",
                               "数据注册表", "data governance", "数据治理", "data quality", "数据质量"],
            
            # 5. 教育与参考资源
            "tutorials_guides": ["tutorial", "教程", "guide", "指南", "course", "课程", "learning resource", 
                                "学习资源", "documentation", "文档", "教学", "handbook", "手册",
                                "getting started", "入门指南", "best practices", "最佳实践", "cookbook",
                                "example notebooks", "示例笔记本", "workbook", "工作簿"],
            "resource_collections": ["resource collection", "资源集合", "awesome list", "awesome repo", 
                                    "curated list", "精选列表", "resource list", "资源列表", "toolkit",
                                    "工具包", "reference implementation", "参考实现", "starter kit",
                                    "入门套件", "template collection", "模板集合", "boilerplate", "样板"],
            "examples_demos": ["example", "示例", "demo", "演示", "sample code", "示例代码", "showcase", 
                              "展示", "demonstration", "case study", "案例研究", "proof of concept",
                              "概念验证", "example project", "示例项目", "playground", "游乐场",
                              "interactive demo", "交互式演示", "sample application", "示例应用"],
            
            # 保留原有的一些通用分类
            "nlp": ["nlp", "natural language processing", "language model", "llm", "gpt", 
                    "transformer", "bert", "translation", "text generation", "chatbot",
                    "natural language understanding", "自然语言理解", "natural language generation",
                    "自然语言生成", "sentiment analysis", "情感分析", "word embedding", "词嵌入",
                    "language representation", "语言表示", "text analysis", "文本分析", "llama", "mistral"],
            "computer_vision": ["computer vision", "image processing", "object detection", 
                               "image classification", "segmentation", "face recognition", 
                               "opencv", "diffusion", "stable-diffusion", "gan", "计算机视觉",
                               "图像处理", "目标检测", "图像分类", "分割", "人脸识别", "pose estimation",
                               "姿态估计", "depth estimation", "深度估计", "生成对抗网络"],
            "deep_learning": ["deep learning", "neural network", "tensorflow", "pytorch", 
                             "keras", "deep neural network", "dnn", "cnn", "rnn", "lstm",
                             "深度学习", "神经网络", "卷积神经网络", "循环神经网络", "长短期记忆网络",
                             "transformer", "attention", "注意力机制", "backpropagation", "反向传播",
                             "gradient descent", "梯度下降", "activation function", "激活函数"],
            "machine_learning": ["machine learning", "ml", "classification", "regression", 
                               "clustering", "scikit-learn", "sklearn", "xgboost", "random forest",
                               "机器学习", "分类", "回归", "聚类", "决策树", "支持向量机", "svm",
                               "feature engineering", "特征工程", "model selection", "模型选择",
                               "hyperparameter tuning", "超参数调优", "ensemble methods", "集成方法"],
            "reinforcement_learning": ["reinforcement learning", "rl", "deep reinforcement learning", 
                                      "drl", "q-learning", "dqn", "policy gradient", "强化学习",
                                      "深度强化学习", "q学习", "策略梯度", "reward function", "奖励函数",
                                      "agent", "environment", "环境", "action space", "动作空间",
                                      "state space", "状态空间", "exploration", "探索"]
        }
    
    def process_repository(self, repo_data: Dict) -> Dict:
        """处理单个仓库数据，提取并格式化所需的元信息"""
        try:
            # 获取一级和二级分类
            categories = self._categorize_repository(repo_data)
            
            processed_data = {
                "repo_id": repo_data.get("id"),
                "name": repo_data.get("name"),
                "full_name": repo_data.get("full_name"),
                "html_url": repo_data.get("html_url"),
                "api_url": repo_data.get("url"),
                "description": repo_data.get("description"),
                "homepage": repo_data.get("homepage"),
                "language": repo_data.get("language"),
                "languages_url": repo_data.get("languages_url"),
                "stars": repo_data.get("stargazers_count"),
                "forks": repo_data.get("forks_count"),
                "watchers": repo_data.get("watchers_count"),
                "open_issues": repo_data.get("open_issues_count"),
                "default_branch": repo_data.get("default_branch"),
                "created_at": repo_data.get("created_at"),
                "updated_at": repo_data.get("updated_at"),
                "pushed_at": repo_data.get("pushed_at"),
                "topics": repo_data.get("topics", []),
                "license": self._extract_license(repo_data),
                "owner": self._extract_owner(repo_data),
                "primary_categories": categories["primary_categories"],  # 一级分类
                "secondary_categories": categories["secondary_categories"],  # 二级分类
                "is_ai_related": len(categories["secondary_categories"]) > 0,  # 是否AI相关
                "last_scraped": datetime.utcnow().isoformat(),
            }
            return processed_data
        except Exception as e:
            logger.error(f"Error processing repository data: {str(e)}")
            raise
    
    def _extract_license(self, repo_data: Dict) -> Dict:
        """提取仓库许可证信息"""
        license_data = repo_data.get("license")
        if license_data:
            return {
                "key": license_data.get("key"),
                "name": license_data.get("name"),
                "spdx_id": license_data.get("spdx_id"),
                "url": license_data.get("url")
            }
        return None
    
    def _extract_owner(self, repo_data: Dict) -> Dict:
        """提取仓库所有者信息"""
        owner_data = repo_data.get("owner")
        if owner_data:
            return {
                "id": owner_data.get("id"),
                "login": owner_data.get("login"),
                "type": owner_data.get("type"),
                "html_url": owner_data.get("html_url"),
                "avatar_url": owner_data.get("avatar_url")
            }
        return None
    
    def _categorize_repository(self, repo_data: Dict) -> Dict[str, List[str]]:
        """根据描述、主题和名称对仓库进行AI类别分类"""
        categories = set()
        
        # 获取仓库的文本信息
        description = (repo_data.get("description") or "").lower()
        name = (repo_data.get("name") or "").lower()
        full_name = (repo_data.get("full_name") or "").lower()
        topics = [t.lower() for t in repo_data.get("topics") or []]
        
        combined_text = f"{description} {name} {full_name} {' '.join(topics)}"
        
        # 根据关键词进行分类
        for category, keywords in self.ai_categories.items():
            for keyword in keywords:
                if keyword.lower() in combined_text:
                    categories.add(category)
                    break
        
        # 映射二级分类到一级分类
        primary_categories = {
            # 1. 模型开发与研究
            "model_repository": "model_development_research",
            "model_training": "model_development_research",
            "inference_optimization": "model_development_research",
            "dataset_engineering": "model_development_research",
            "multimodal_models": "model_development_research",
            "multilingual_models": "model_development_research",
            "code_models": "model_development_research",
            
            # 2. AI工程化工具
            "ai_framework": "ai_engineering_tools",
            "agent_systems": "ai_engineering_tools",
            "ai_interface": "ai_engineering_tools",
            "prompt_engineering": "ai_engineering_tools",
            "evaluation_tools": "ai_engineering_tools",
            "rag_systems": "ai_engineering_tools",
            
            # 3. 实用应用
            "coding_assistance": "practical_applications",
            "information_retrieval": "practical_applications",
            "workflow_automation": "practical_applications",
            "conversational_bots": "practical_applications",
            "image_creation": "practical_applications",
            "video_creation": "practical_applications",
            "text_creation": "practical_applications",
            "data_organization": "practical_applications",
            
            # 4. 基础设施与支持
            "vector_database": "infrastructure_support",
            "model_serving": "infrastructure_support",
            "compute_management": "infrastructure_support",
            "monitoring_observability": "infrastructure_support",
            "development_tools": "infrastructure_support",
            "data_management": "infrastructure_support",
            
            # 5. 教育与参考资源
            "tutorials_guides": "education_resources",
            "resource_collections": "education_resources",
            "examples_demos": "education_resources",
            
            # 原有分类映射
            "nlp": "model_development_research",
            "computer_vision": "model_development_research",
            "deep_learning": "model_development_research",
            "machine_learning": "model_development_research",
            "reinforcement_learning": "model_development_research"
        }
        
        # 创建包含一级和二级分类的结果
        result = {
            "primary_categories": set(),  # 一级分类
            "secondary_categories": categories  # 二级分类
        }
        
        # 添加一级分类
        for category in categories:
            if category in primary_categories:
                result["primary_categories"].add(primary_categories[category])
        
        # 转换集合为列表
        result["primary_categories"] = list(result["primary_categories"])
        result["secondary_categories"] = list(result["secondary_categories"])
        
        return result
    
    def process_repositories(self, repos_data: List[Dict]) -> List[Dict]:
        """处理多个仓库数据"""
        processed_repos = []
        
        for repo_data in repos_data:
            try:
                processed_repo = self.process_repository(repo_data)
                processed_repos.append(processed_repo)
            except Exception as e:
                logger.error(f"Error processing repository {repo_data.get('full_name', 'unknown')}: {str(e)}")
                continue
        
        return processed_repos 