matplotlib==3.8.0
nltk==3.8.1
numpy==1.24.3
opencv-python==********
pillow==10.1.0
scikit-learn==1.3.2
selenium==4.14.0
webdriver-manager==4.0.1
gitpython>=3.1.32
beautifulsoup4>=4.12.2
requests>=2.28.1
pyyaml>=6.0.1
python-dotenv>=0.21.0
pymongo>=4.3.2
fastapi>=0.89.1
uvicorn>=0.20.0
celery>=5.2.7
redis>=4.4.2
pydantic>=1.10.4
typing-extensions>=4.4.0
python-multipart>=0.0.5
# 用于处理后台任务
flower>=1.2.0
# 用于日志记录
python-json-logger>=2.0.4
# 用于Celery的SQLite后备方案
sqlalchemy>=2.0.0
# 用于检测Redis可用性
redis-py-cluster>=2.1.3
