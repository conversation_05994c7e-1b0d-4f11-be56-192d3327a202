#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
通用功能模块
提供日志设置、工具函数等
"""

import os
import sys
import json
import logging
import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Union

# 根目录设置
ROOT_DIR = Path(__file__).parent.parent.parent
PROJECT_DIR = Path(__file__).parent.parent

# 日志设置
def setup_logger(name: str, log_file: Optional[str] = None, level: int = logging.INFO) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 记录器名称
        log_file: 日志文件路径（可选）
        level: 日志级别
        
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 创建日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器（如果提供了日志文件）
    if log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
            
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
    return logger

# 默认日志记录器
logger = setup_logger("github_scrape")

# JSON 工具函数
def load_json(file_path: Union[str, Path]) -> Dict:
    """
    加载JSON文件
    
    Args:
        file_path: JSON文件路径
        
    Returns:
        Dict: 加载的JSON数据
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载JSON文件 {file_path} 失败: {e}")
        return {}
        
def save_json(data: Any, file_path: Union[str, Path], indent: int = 2) -> bool:
    """
    保存数据到JSON文件
    
    Args:
        data: 要保存的数据
        file_path: 目标文件路径
        indent: JSON缩进空格数
        
    Returns:
        bool: 是否成功保存
    """
    try:
        # 确保目录存在
        dir_path = os.path.dirname(file_path)
        if dir_path and not os.path.exists(dir_path):
            os.makedirs(dir_path)
            
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=indent)
        return True
    except Exception as e:
        logger.error(f"保存JSON文件 {file_path} 失败: {e}")
        return False

# 日期时间工具函数
def get_current_time_str(format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    获取当前时间的格式化字符串
    
    Args:
        format_str: 日期时间格式
        
    Returns:
        str: 格式化的时间字符串
    """
    return datetime.datetime.now().strftime(format_str)

def get_current_date_str(format_str: str = "%Y-%m-%d") -> str:
    """
    获取当前日期的格式化字符串
    
    Args:
        format_str: 日期格式
        
    Returns:
        str: 格式化的日期字符串
    """
    return datetime.datetime.now().strftime(format_str)

# 其他通用工具函数
def ensure_dir(directory: Union[str, Path]) -> bool:
    """
    确保目录存在，不存在则创建
    
    Args:
        directory: 目录路径
        
    Returns:
        bool: 是否成功确保目录存在
    """
    try:
        if not os.path.exists(directory):
            os.makedirs(directory)
        return True
    except Exception as e:
        logger.error(f"创建目录 {directory} 失败: {e}")
        return False
