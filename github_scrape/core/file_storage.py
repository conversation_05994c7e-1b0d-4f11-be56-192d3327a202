import json
import os
import logging
from datetime import datetime
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

class FileStorage:
    """将抓取的GitHub仓库数据存储到文本文件中"""
    
    def __init__(self, output_dir="data"):
        """
        初始化文件存储
        
        参数:
            output_dir: 输出目录的路径
        """
        self.output_dir = output_dir
        self._ensure_directory_exists()
    
    def _ensure_directory_exists(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            logger.info(f"创建输出目录: {self.output_dir}")
    
    def save_repositories(self, repositories: List[Dict[str, Any]]):
        """
        将仓库数据保存到文本文件
        
        参数:
            repositories: 处理后的仓库数据列表
        """
        # 使用当前时间创建唯一的文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"github_repos_{timestamp}.json"
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(repositories, f, ensure_ascii=False, indent=2)
            
            logger.info(f"成功保存 {len(repositories)} 个仓库数据到文件: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"保存数据到文件时出错: {str(e)}")
            raise
    
    def save_repository(self, repository: Dict[str, Any]):
        """
        将单个仓库数据保存到单独的文本文件
        
        参数:
            repository: 处理后的单个仓库数据
        """
        # 使用仓库名称和ID创建文件名
        repo_name = repository.get("full_name", "unknown").replace("/", "_")
        repo_id = repository.get("repo_id", "unknown")
        filename = f"{repo_name}_{repo_id}.json"
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(repository, f, ensure_ascii=False, indent=2)
            
            logger.info(f"成功保存仓库数据到文件: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"保存数据到文件时出错: {str(e)}")
            raise 