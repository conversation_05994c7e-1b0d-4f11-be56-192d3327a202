# GitHub AI项目批量抓取工具

这个工具用于批量抓取GitHub上与AI相关的项目信息，支持多线程并行处理、断点续传和数据持久化。

## 功能特点

- 多线程并行处理，提高抓取效率
- 断点续传，支持中断后继续抓取
- 数据持久化，保存到JSON文件
- 智能重试和错误处理
- 自动处理GitHub API速率限制
- 通过多种搜索策略获取AI项目（主题、关键词、框架）

## 使用方法

### 1. 设置GitHub API Token

首先，设置GitHub API Token环境变量：

```bash
export GITHUB_API_TOKEN=your_github_personal_access_token
```

### 2. 安装依赖

```bash
pip install requests
```

### 3. 运行抓取脚本

```bash
python run_batch_scraper.py
```

### 4. 命令行参数

可以通过命令行参数自定义抓取行为：

- `--min-stars`: 仓库最低星标数（默认: 100）
- `--max-pages`: 最大抓取页数（默认: 100）
- `--output-dir`: 数据输出目录（默认: data）
- `--workers`: 并行工作线程数（默认: 5）

示例：

```bash
python run_batch_scraper.py --min-stars 500 --max-pages 200 --output-dir ai_data --workers 10
```


```bash
python tools/github_runner.py quick --days 21 --min-stars 300 --output "new_14day_repos.md" --top 50 --sort-by stars
```

## 数据格式

抓取的数据存储在`<output_dir>/repos/`目录下，每个仓库一个JSON文件，使用仓库ID作为文件名。

已处理的仓库ID列表保存在`<output_dir>/processed_repo_ids.json`文件中，用于断点续传。

## 注意事项

1. GitHub API有速率限制，请确保有足够的配额
2. 大规模抓取可能需要较长时间，建议在服务器或后台运行
3. 抓取过程中可以随时中断（Ctrl+C），下次运行时会自动继续

## 开发者信息

如有问题或建议，请联系项目维护者。
