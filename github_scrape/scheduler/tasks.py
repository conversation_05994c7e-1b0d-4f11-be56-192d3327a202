import logging
import time
import os
import json
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Optional
from celery import Celery
from config import (
    CELERY_BROKER_URL, 
    CELERY_RESULT_BACKEND,
    MIN_STARS,
    UPDATE_INTERVAL_DAYS
)
from scraper.github_api import GitHub<PERSON><PERSON>raper
from scraper.processor import DataProcessor
from scraper.file_storage import FileStorage

logger = logging.getLogger(__name__)

# 创建Celery应用
app = Celery('github_ai_scraper',
             broker=CELERY_BROKER_URL,
             backend=CELERY_RESULT_BACKEND)

# 检查是否使用SQLite后备存储
if 'sqlite' in CELERY_BROKER_URL:
    # SQLAlchemy后备存储需要的额外配置
    app.conf.update(
        broker_transport_options={'visibility_timeout': 3600},  # 1 hour
        broker_connection_retry=True,
        broker_connection_retry_on_startup=True,
        broker_connection_max_retries=10,
        # SQLite特定配置
        broker_pool_limit=None,  # 不限制连接池大小
        broker_heartbeat=None,  # 禁用心跳
        worker_prefetch_multiplier=1  # 减少预取量
    )

# Celery配置
app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    worker_concurrency=4,
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    task_time_limit=3600,  # 1小时超时
)

# 配置定期任务
app.conf.beat_schedule = {
    'scrape-all-ai-repos': {
        'task': 'scheduler.tasks.scrape_all_ai_repositories',
        'schedule': 60 * 60 * 24 * 7,  # 每周执行一次
        'args': (MIN_STARS,)
    },
    'update-outdated-repos': {
        'task': 'scheduler.tasks.update_outdated_repositories',
        'schedule': 60 * 60 * 24,  # 每天执行一次
        'args': (UPDATE_INTERVAL_DAYS,)
    },
}

@app.task
def scrape_all_ai_repositories(min_stars: int = 500) -> Dict:
    """
    抓取所有符合条件的AI项目仓库
    """
    logger.info(f"开始抓取所有星标数大于{min_stars}的AI仓库")
    
    start_time = time.time()
    results = {
        "total_scraped": 0,
        "total_saved": 0,
        "errors": 0,
        "status": "success"
    }
    
    try:
        # 创建所需的组件实例
        scraper = GitHubScraper()
        processor = DataProcessor()
        
        # 抓取仓库数据
        raw_repos = scraper.get_all_ai_repositories(min_stars)
        results["total_scraped"] = len(raw_repos)
        
        # 处理仓库数据
        processed_repos = processor.process_repositories(raw_repos)
        
        # 保存到文件
        data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data')
        os.makedirs(data_dir, exist_ok=True)
        file_storage = FileStorage(output_dir=data_dir)
        saved_count = file_storage.save_repositories(processed_repos)
        results["total_saved"] = saved_count
        
        logger.info(f"完成抓取，共处理{len(raw_repos)}个仓库，保存{saved_count}个仓库")
    except Exception as e:
        logger.error(f"抓取AI仓库时出错: {str(e)}")
        results["status"] = "error"
        results["error_message"] = str(e)
        results["errors"] += 1
    
    elapsed_time = time.time() - start_time
    results["elapsed_time"] = elapsed_time
    logger.info(f"抓取任务完成，耗时{elapsed_time:.2f}秒")
    
    return results

@app.task
def update_outdated_repositories(days: int = 7) -> Dict:
    """
    更新超过指定天数未更新的仓库
    """
    logger.info(f"开始更新{days}天未更新的仓库")
    
    start_time = time.time()
    results = {
        "total_found": 0,
        "total_updated": 0,
        "errors": 0,
        "status": "success"
    }
    
    try:
        # 创建所需的组件实例
        scraper = GitHubScraper()
        processor = DataProcessor()
        
        # 创建文件存储实例
        data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data')
        os.makedirs(data_dir, exist_ok=True)
        file_storage = FileStorage(output_dir=data_dir)
        
        # 获取过期的仓库列表
        # 由于我们使用文件存储而不是数据库，我们需要加载所有文件并筛选过期仓库
        outdated_repos = []
        cutoff_date = (datetime.utcnow() - timedelta(days=days)).isoformat()
        
        # 遍历data目录中的所有JSON文件
        for filename in os.listdir(data_dir):
            if filename.endswith('.json'):
                try:
                    with open(os.path.join(data_dir, filename), 'r', encoding='utf-8') as file:
                        repo_data = json.loads(file.read())
                        # 检查是单个仓库数据还是仓库列表
                        if isinstance(repo_data, dict):
                            # 单个仓库
                            last_scraped = repo_data.get('last_scraped')
                            if last_scraped and last_scraped < cutoff_date:
                                outdated_repos.append(repo_data)
                        elif isinstance(repo_data, list):
                            # 仓库列表
                            for repo in repo_data:
                                last_scraped = repo.get('last_scraped')
                                if last_scraped and last_scraped < cutoff_date:
                                    outdated_repos.append(repo)
                except Exception as e:
                    logger.error(f"加载文件 {filename} 时出错: {str(e)}")
        
        results["total_found"] = len(outdated_repos)
        
        updated_count = 0
        
        for repo in outdated_repos:
            try:
                repo_full_name = repo.get("full_name")
                
                if not repo_full_name:
                    logger.warning(f"仓库缺少full_name，跳过: {repo.get('repo_id', 'unknown')}")
                    continue
                
                # 重新抓取仓库信息
                raw_repo_data = scraper.get_repository_details(repo_full_name)
                topics = scraper.get_repository_topics(repo_full_name)
                raw_repo_data["topics"] = topics
                
                # 处理仓库数据
                processed_repo = processor.process_repository(raw_repo_data)
                
                # A. 保存到文件
                file_storage.save_repository(processed_repo)
                updated_count += 1
                
                # 每10个仓库记录一次日志
                if updated_count % 10 == 0:
                    logger.info(f"已更新{updated_count}/{len(outdated_repos)}个仓库")
                
                # 避免触发GitHub API速率限制
                time.sleep(1)
            except Exception as e:
                logger.error(f"更新仓库{repo.get('full_name', 'unknown')}时出错: {str(e)}")
                results["errors"] += 1
        
        results["total_updated"] = updated_count
        logger.info(f"完成更新，共找到{len(outdated_repos)}个过期仓库，更新{updated_count}个仓库")
    except Exception as e:
        logger.error(f"更新过期仓库时出错: {str(e)}")
        results["status"] = "error"
        results["error_message"] = str(e)
    
    elapsed_time = time.time() - start_time
    results["elapsed_time"] = elapsed_time
    logger.info(f"更新任务完成，耗时{elapsed_time:.2f}秒")
    
    return results

@app.task
def update_single_repository(repo_full_name: str) -> Dict:
    """
    更新单个仓库的信息
    """
    logger.info(f"开始更新仓库: {repo_full_name}")
    
    results = {
        "repo_full_name": repo_full_name,
        "updated": False,
        "status": "success"
    }
    
    try:
        # 创建所需的组件实例
        scraper = GitHubScraper()
        processor = DataProcessor()
        
        # 创建文件存储实例
        data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data')
        os.makedirs(data_dir, exist_ok=True)
        file_storage = FileStorage(output_dir=data_dir)
        
        # 抓取仓库信息
        raw_repo_data = scraper.get_repository_details(repo_full_name)
        topics = scraper.get_repository_topics(repo_full_name)
        raw_repo_data["topics"] = topics
        
        # 处理仓库数据
        processed_repo = processor.process_repository(raw_repo_data)
        
        # 保存到文件
        file_storage.save_repository(processed_repo)
        results["updated"] = True
        
        logger.info(f"成功更新仓库: {repo_full_name}")
    except Exception as e:
        logger.error(f"更新仓库{repo_full_name}时出错: {str(e)}")
        results["status"] = "error"
        results["error_message"] = str(e)
    
    return results 