# GitHub API配置
# 如何获取GitHub令牌:
# 1. 访问 https://github.com/settings/tokens
# 2. 点击 "Generate new token" > "Generate new token (classic)"
# 3. 勾选 "public_repo" 权限
# 4. 生成并复制令牌到这里
GITHUB_TOKEN=your_github_personal_access_token
# API速率限制等待时间(秒)，建议设置较大的值以避免触发GitHub API限制
RATE_LIMIT_WAIT=120

# MongoDB配置
MONGODB_URI=mongodb://localhost:27017/
MONGODB_DB_NAME=github_ai_projects

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# 爬虫配置
# 设置抓取的GitHub仓库的最低星标数，数值越大获取的仓库质量越高但数量越少
MIN_STARS=1000
# 仓库更新间隔(天)，超过这个天数的仓库将被重新抓取
UPDATE_INTERVAL_DAYS=7

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=github_ai_scraper.log

# API服务配置
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=False 