import logging
from typing import Dict, List, Optional, Any
from fastapi import FastAPI, HTTPException, Query, Path, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from database.mongo_client import MongoDBClient
from scheduler.tasks import update_single_repository

logger = logging.getLogger(__name__)

# FastAPI 应用实例
app = FastAPI(
    title="GitHub AI 项目抓取系统 API",
    description="提供GitHub AI项目的查询和管理功能",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据库依赖
def get_db():
    db = MongoDBClient()
    try:
        yield db
    finally:
        db.close()

# 模型定义
class RepositorySummary(BaseModel):
    repo_id: int
    name: str
    full_name: str
    html_url: str
    description: Optional[str] = None
    language: Optional[str] = None
    stars: int
    forks: int
    topics: List[str] = []
    categories: List[str] = []

class PaginatedResponse(BaseModel):
    total: int
    page: int
    per_page: int
    items: List[Any]

# API 路由
@app.get("/")
async def root():
    return {"message": "GitHub AI 项目抓取系统 API"}

@app.get("/repositories", response_model=PaginatedResponse)
async def get_repositories(
    page: int = Query(1, ge=1, description="页码"),
    per_page: int = Query(20, ge=1, le=100, description="每页数量"),
    category: Optional[str] = Query(None, description="AI类别筛选"),
    language: Optional[str] = Query(None, description="编程语言筛选"),
    min_stars: Optional[int] = Query(None, ge=0, description="最低星标数"),
    sort_by: str = Query("stars", description="排序字段"),
    sort_order: int = Query(-1, description="排序方向 (1: 升序, -1: 降序)"),
    db: MongoDBClient = Depends(get_db)
):
    """获取AI仓库列表，支持分页和筛选"""
    # 构建查询条件
    query = {}
    
    if category:
        query["categories"] = category
        
    if language:
        query["language"] = language
        
    if min_stars is not None:
        query["stars"] = {"$gte": min_stars}
    
    # 计算跳过的记录数
    skip = (page - 1) * per_page
    
    # 获取符合条件的仓库总数
    total = db.count_repositories(query)
    
    # 获取当前页的仓库列表
    repositories = db.get_repositories(
        query=query,
        sort_by=sort_by,
        sort_order=sort_order,
        skip=skip,
        limit=per_page
    )
    
    # 返回分页响应
    return {
        "total": total,
        "page": page,
        "per_page": per_page,
        "items": repositories
    }

@app.get("/repositories/{repo_id}", response_model=Dict)
async def get_repository(
    repo_id: int = Path(..., description="仓库ID"),
    db: MongoDBClient = Depends(get_db)
):
    """获取单个AI仓库的详细信息"""
    repository = db.get_repository(repo_id)
    
    if not repository:
        raise HTTPException(status_code=404, detail="仓库不存在")
        
    return repository

@app.get("/repositories/name/{full_name}", response_model=Dict)
async def get_repository_by_name(
    full_name: str = Path(..., description="仓库全名 (owner/name)"),
    db: MongoDBClient = Depends(get_db)
):
    """通过全名获取AI仓库信息"""
    repository = db.get_repository_by_name(full_name)
    
    if not repository:
        raise HTTPException(status_code=404, detail="仓库不存在")
        
    return repository

@app.post("/repositories/{repo_id}/update")
async def trigger_repository_update(
    repo_id: int = Path(..., description="仓库ID"),
    db: MongoDBClient = Depends(get_db)
):
    """触发单个仓库的更新"""
    repository = db.get_repository(repo_id)
    
    if not repository:
        raise HTTPException(status_code=404, detail="仓库不存在")
    
    full_name = repository["full_name"]
    # 异步触发更新任务
    update_single_repository.delay(full_name)
    
    return {"message": f"已触发仓库 {full_name} 的更新任务"}

@app.get("/categories")
async def get_categories(db: MongoDBClient = Depends(get_db)):
    """获取所有AI类别及其仓库数量"""
    pipeline = [
        {"$unwind": "$categories"},
        {"$group": {"_id": "$categories", "count": {"$sum": 1}}},
        {"$sort": {"count": -1}}
    ]
    
    try:
        categories = list(db.repos_collection.aggregate(pipeline))
        return [{"category": item["_id"], "count": item["count"]} for item in categories]
    except Exception as e:
        logger.error(f"获取类别统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail="无法获取类别统计")

@app.get("/languages")
async def get_languages(db: MongoDBClient = Depends(get_db)):
    """获取所有编程语言及其仓库数量"""
    pipeline = [
        {"$match": {"language": {"$ne": None}}},
        {"$group": {"_id": "$language", "count": {"$sum": 1}}},
        {"$sort": {"count": -1}}
    ]
    
    try:
        languages = list(db.repos_collection.aggregate(pipeline))
        return [{"language": item["_id"], "count": item["count"]} for item in languages]
    except Exception as e:
        logger.error(f"获取语言统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail="无法获取语言统计")

@app.get("/stats")
async def get_stats(db: MongoDBClient = Depends(get_db)):
    """获取系统统计信息"""
    try:
        total_repos = db.count_repositories()
        
        # 按星标数分组统计
        star_ranges = [
            {"$match": {"stars": {"$gte": 10000}}},
            {"$match": {"stars": {"$gte": 5000, "$lt": 10000}}},
            {"$match": {"stars": {"$gte": 1000, "$lt": 5000}}},
            {"$match": {"stars": {"$gte": 500, "$lt": 1000}}}
        ]
        star_counts = []
        
        for i, star_range in enumerate(star_ranges):
            count = db.count_repositories(star_range["$match"])
            
            if i == 0:
                label = "10000+"
            elif i == 1:
                label = "5000-9999"
            elif i == 2:
                label = "1000-4999"
            else:
                label = "500-999"
                
            star_counts.append({"range": label, "count": count})
        
        # 获取最近更新的仓库
        recent_repos = db.get_repositories(
            sort_by="last_scraped",
            sort_order=-1,
            limit=5
        )
        
        # 计算平均星标数
        pipeline = [{"$group": {"_id": None, "avg_stars": {"$avg": "$stars"}}}]
        avg_stars_result = list(db.repos_collection.aggregate(pipeline))
        avg_stars = round(avg_stars_result[0]["avg_stars"], 2) if avg_stars_result else 0
        
        return {
            "total_repositories": total_repos,
            "star_distribution": star_counts,
            "average_stars": avg_stars,
            "recent_updates": [
                {
                    "repo_id": repo["repo_id"],
                    "full_name": repo["full_name"],
                    "stars": repo["stars"],
                    "last_scraped": repo["last_scraped"]
                } for repo in recent_repos
            ]
        }
    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail="无法获取统计信息")

# 启动服务器
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("api.server:app", host="0.0.0.0", port=8000, reload=True) 