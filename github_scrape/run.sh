#!/bin/bash

# GitHub AI 项目抓取系统启动脚本

# 设置正确的Python解释器路径
# 检查是否存在虚拟环境
if [ -d ".venv" ]; then
    PYTHON_PATH=".venv/bin/python"
elif [ -d "../.venv" ]; then
    PYTHON_PATH="../.venv/bin/python"
elif [ -d "venv" ]; then
    PYTHON_PATH="venv/bin/python"
elif [ -d "../venv" ]; then
    PYTHON_PATH="../venv/bin/python"
else
    # 如果没有找到虚拟环境，尝试使用系统Python
    PYTHON_PATH=$(which python3 || which python)
fi

echo "使用Python解释器: $PYTHON_PATH"

# 检查是否已有 .env 文件，若没有则创建
if [ ! -f .env ]; then
    echo "创建 .env 文件..."
    cp .env.example .env
    echo "请修改 .env 文件，填写必要的配置信息后再运行此脚本。"
    exit 1
fi

# 检查是否已安装依赖
$PYTHON_PATH -m pip --version > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "错误: 无法运行pip，请确保Python环境正确安装"
    exit 1
fi

pip_check=$($PYTHON_PATH -m pip freeze | grep -E "fastapi|pymongo|celery|python-dotenv" | wc -l)
if [ $pip_check -lt 4 ]; then
    echo "安装依赖..."
    $PYTHON_PATH -m pip install -r requirements.txt
fi

# 定义颜色代码
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 恢复默认颜色

# 显示帮助信息
show_help() {
    echo -e "${BLUE}GitHub AI 项目抓取系统${NC}"
    echo ""
    echo -e "用法: $0 <命令> [参数]"
    echo ""
    echo -e "可用命令:"
    echo -e "  ${GREEN}api${NC}           启动 API 服务器"
    echo -e "  ${GREEN}worker${NC}        启动 Celery Worker 处理任务"
    echo -e "  ${GREEN}beat${NC}          启动 Celery Beat 定时任务"
    echo -e "  ${GREEN}scrape${NC}        立即运行爬虫抓取 AI 项目"
    echo -e "  ${GREEN}update${NC}        立即更新已有项目数据"
    echo -e "  ${GREEN}quick${NC}         快速抓取模式（精简版，推荐）"
    echo -e "  ${GREEN}all${NC}           启动所有服务 (API, Worker, Beat)"
    echo -e "  ${GREEN}help${NC}          显示此帮助信息"
    echo ""
    echo -e "示例:"
    echo -e "  $0 api              # 启动 API 服务器"
    echo -e "  $0 scrape           # 立即抓取项目"
    echo -e "  $0 quick --days 14  # 快速抓取最近14天的项目"
    echo -e "  $0 all              # 启动所有服务"
    echo ""
}

# 启动API服务
start_api() {
    echo -e "${BLUE}启动 API 服务器...${NC}"
    $PYTHON_PATH main.py api &
    echo $! > .api.pid
    echo -e "${GREEN}API 服务已启动，PID: $(cat .api.pid)${NC}"
}

# 启动Worker
start_worker() {
    echo -e "${BLUE}启动 Celery Worker...${NC}"
    $PYTHON_PATH main.py worker &
    echo $! > .worker.pid
    echo -e "${GREEN}Worker 已启动，PID: $(cat .worker.pid)${NC}"
}

# 启动Beat
start_beat() {
    echo -e "${BLUE}启动 Celery Beat...${NC}"
    $PYTHON_PATH main.py beat &
    echo $! > .beat.pid
    echo -e "${GREEN}Beat 已启动，PID: $(cat .beat.pid)${NC}"
}

# 抓取项目
run_scrape() {
    echo -e "${BLUE}开始抓取 AI 项目...${NC}"
    $PYTHON_PATH main.py scrape
}

# 更新项目
run_update() {
    echo -e "${BLUE}开始更新项目数据...${NC}"
    $PYTHON_PATH main.py update $1
}

# 快速抓取项目
run_quick() {
    echo -e "${BLUE}开始快速抓取 AI 项目...${NC}"
    $PYTHON_PATH tools/quick_scraper.py $@
}

# 停止所有服务
stop_all() {
    echo -e "${YELLOW}停止所有服务...${NC}"
    
    if [ -f .api.pid ]; then
        kill $(cat .api.pid) 2>/dev/null
        rm .api.pid
        echo -e "${GREEN}API 服务已停止${NC}"
    fi
    
    if [ -f .worker.pid ]; then
        kill $(cat .worker.pid) 2>/dev/null
        rm .worker.pid
        echo -e "${GREEN}Worker 已停止${NC}"
    fi
    
    if [ -f .beat.pid ]; then
        kill $(cat .beat.pid) 2>/dev/null
        rm .beat.pid
        echo -e "${GREEN}Beat 已停止${NC}"
    fi
}

# 处理命令行参数
case "$1" in
    api)
        start_api
        ;;
    worker)
        start_worker
        ;;
    beat)
        start_beat
        ;;
    scrape)
        run_scrape
        ;;
    update)
        run_update $2
        ;;
    quick)
        run_quick $2
        ;;
    all)
        start_api
        start_worker
        start_beat
        echo -e "${GREEN}所有服务已启动${NC}"
        ;;
    stop)
        stop_all
        ;;
    help|*)
        show_help
        ;;
esac

exit 0 