#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
仓库数据更新脚本，将已有的仓库数据统一更新为新格式，
包括添加star_history、history和growth_stats

Usage:
    python -m github_scrape.tools.update_repos
"""

import os
import sys
import json
import logging
from datetime import datetime
from typing import Dict, List, Any
from concurrent.futures import ThreadPoolExecutor
import argparse

# 添加项目根目录到系统路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from github_scrape.core.processor import DataProcessor
from github_scrape.config import REPOS_DIR, GITHUB_TOKEN

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("update_repos")

def get_repo_files() -> List[str]:
    """获取所有仓库数据文件路径"""
    if not os.path.exists(REPOS_DIR):
        logger.error(f"仓库目录不存在: {REPOS_DIR}")
        return []
    
    files = [os.path.join(REPOS_DIR, f) for f in os.listdir(REPOS_DIR) 
              if f.endswith('.json') and os.path.isfile(os.path.join(REPOS_DIR, f))]
    
    logger.info(f"找到 {len(files)} 个仓库数据文件")
    return files

def load_repo_data(file_path: str) -> Dict:
    """加载仓库数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载仓库数据失败: {file_path}, 错误: {str(e)}")
        return {}

def save_repo_data(file_path: str, data: Dict) -> bool:
    """保存仓库数据"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logger.error(f"保存仓库数据失败: {file_path}, 错误: {str(e)}")
        return False

def update_repo_file(file_path: str, star_days: int = None) -> bool:
    """更新单个仓库文件的数据格式"""
    logger.info(f"开始更新仓库: {os.path.basename(file_path)}")
    
    # 加载仓库数据
    repo_data = load_repo_data(file_path)
    if not repo_data:
        return False
    
    # 创建数据处理器
    processor = DataProcessor()
    
    # 处理仓库数据
    try:
        # 确保repo_id存在
        if "repo_id" not in repo_data and "id" in repo_data:
            repo_data["repo_id"] = repo_data["id"]
        
        # 每次都走完整处理流程，确保星标历史增量更新
        updated_data = processor.process_repository(repo_data, force_days=star_days)
        
        # 保存更新后的数据
        if save_repo_data(file_path, updated_data):
            logger.info(f"成功更新仓库数据: {os.path.basename(file_path)}")
            return True
        else:
            logger.error(f"保存更新后的数据失败: {os.path.basename(file_path)}")
            return False
    
    except Exception as e:
        logger.error(f"更新仓库数据出错: {os.path.basename(file_path)}, 错误: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def update_all_repos(star_days: int = None) -> None:
    """更新所有仓库数据"""
    # 获取所有仓库文件
    repo_files = get_repo_files()
    if not repo_files:
        logger.warning("没有找到仓库数据文件")
        return
    
    # 并行更新仓库数据
    from functools import partial
    with ThreadPoolExecutor(max_workers=10) as executor:
        results = list(executor.map(partial(update_repo_file, star_days=star_days), repo_files))
    
    # 统计结果
    success_count = sum(1 for result in results if result)
    logger.info(f"更新完成: 成功 {success_count}/{len(repo_files)}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="批量更新仓库数据")
    parser.add_argument("--star-days", type=int, default=None, help="手动指定抓取最近N天的star历史（如90表示抓取最近90天）")
    args = parser.parse_args()
    logger.info("开始更新仓库数据")
    update_all_repos(star_days=args.star_days)
    logger.info("仓库数据更新完成") 