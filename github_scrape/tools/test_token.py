#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试GitHub令牌有效性的简单脚本
"""

import requests
import os
import json
import time

# 直接设置令牌
TOKEN = "****************************************"

def test_token(token):
    """测试GitHub API令牌是否有效"""
    headers = {
        "Authorization": f"token {token}",
        "Accept": "application/vnd.github.v3+json"
    }
    
    # 测试端点
    endpoints = [
        "https://api.github.com/user",  # 用户信息
        "https://api.github.com/rate_limit"  # 速率限制信息
    ]
    
    print(f"测试令牌: {token[:4]}...{token[-4:]}")
    
    for endpoint in endpoints:
        try:
            response = requests.get(endpoint, headers=headers, timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {endpoint} - 状态码: {response.status_code}")
                if endpoint == "https://api.github.com/user":
                    print(f"   用户名: {data.get('login')}")
                    print(f"   类型: {data.get('type')}")
                elif endpoint == "https://api.github.com/rate_limit":
                    rate = data.get('rate', {})
                    print(f"   API速率限制: {rate.get('limit')}")
                    print(f"   剩余请求数: {rate.get('remaining')}")
            else:
                print(f"❌ {endpoint} - 状态码: {response.status_code}")
                print(f"   错误: {response.text}")
                
        except Exception as e:
            print(f"❌ {endpoint} - 发生错误: {str(e)}")

def test_scraper_request(token):
    """模拟GitHubScraper的请求方式"""
    print("\n模拟GitHubScraper的请求方式...")
    headers = {
        "Authorization": f"token {token}",
        "Accept": "application/vnd.github.v3+json"
    }
    
    # 测试仓库详情请求
    repo_names = [
        "huggingface/transformers", 
        "openai/openai-cookbook"
    ]
    
    for repo_name in repo_names:
        url = f"https://api.github.com/repos/{repo_name}"
        try:
            print(f"\n测试仓库: {repo_name}")
            print(f"请求URL: {url}")
            print(f"使用令牌: {token[:4]}...{token[-4:]}")
            print(f"请求头: {headers}")
            
            response = requests.get(url, headers=headers, timeout=30)
            
            print(f"响应状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 仓库信息获取成功")
                print(f"   名称: {data.get('name')}")
                print(f"   星标数: {data.get('stargazers_count')}")
                print(f"   描述: {data.get('description')[:50]}...")
            else:
                print(f"❌ 仓库信息获取失败")
                print(f"   错误: {response.text}")
                
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")

def test_with_direct_import():
    """直接从config导入并测试"""
    try:
        from config import GITHUB_TOKEN
        print(f"\n从config导入的令牌: {GITHUB_TOKEN[:4] if GITHUB_TOKEN else 'None'}...{GITHUB_TOKEN[-4:] if GITHUB_TOKEN else ''}")
        if GITHUB_TOKEN:
            test_token(GITHUB_TOKEN)
        else:
            print("config.py中的GITHUB_TOKEN为空")
    except Exception as e:
        print(f"导入config.GITHUB_TOKEN时出错: {str(e)}")

if __name__ == "__main__":
    # 先测试直接设置的令牌
    test_token(TOKEN)
    
    # 测试环境变量中的令牌
    env_token = os.environ.get("GITHUB_TOKEN")
    if env_token and env_token != TOKEN:
        print("\n测试环境变量中的令牌:")
        test_token(env_token)
    else:
        print("\n环境变量中没有设置令牌或与直接设置的令牌相同。")
    
    # 测试从config导入的令牌
    test_with_direct_import()
    
    # 模拟GitHubScraper的请求方式
    test_scraper_request(TOKEN) 