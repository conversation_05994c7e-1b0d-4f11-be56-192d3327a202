#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
快速GitHub AI项目抓取脚本 - 精简版
专门用于快速抓取最近期间的高星AI项目，减少步骤和时间
"""

import logging
import argparse
import sys
import os
import time
from datetime import datetime

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 修复导入路径
try:
    # 先尝试相对导入
    from core.github_api import GitHubScraper
except ImportError:
    # 如果失败，尝试绝对导入
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    from github_scrape.core.github_api import GitHubScraper

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
        ]
    )
    return logging.getLogger("quick_scraper")

def main():
    """主函数"""
    logger = setup_logging()
    
    parser = argparse.ArgumentParser(description="快速抓取GitHub AI项目")
    parser.add_argument('--days', type=int, default=14, help='抓取最近多少天的项目 (默认: 14)')
    parser.add_argument('--min-stars', type=int, default=100, help='最低星标数 (默认: 100)')
    parser.add_argument('--output-dir', type=str, default='data', help='输出目录 (默认: data)')
    parser.add_argument('--max-workers', type=int, default=10, help='最大并行工作线程数 (默认: 10)')
    
    args = parser.parse_args()
    
    logger.info(f"=== 快速抓取开始 ===")
    logger.info(f"参数: 天数={args.days}, 最低星标={args.min_stars}, 输出目录={args.output_dir}")
    
    start_time = datetime.now()
    
    try:
        # 创建抓取器
        scraper = GitHubScraper(
            output_dir=args.output_dir,
            max_workers=args.max_workers
        )
        
        # 执行快速抓取
        repos = scraper.get_new_high_starred_repos(
            min_stars=args.min_stars,
            days=args.days,
            max_workers=args.max_workers
        )
        
        # 计算耗时
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        logger.info(f"=== 抓取完成 ===")
        logger.info(f"找到项目: {len(repos)} 个")
        logger.info(f"总耗时: {duration:.2f} 秒")
        logger.info(f"平均: {duration/max(1, len(repos)):.2f} 秒/项目")
        
        # 显示最近找到的几个项目
        if repos:
            logger.info("最新发现的项目:")
            for i, repo in enumerate(repos[:5], 1):
                name = repo.get("full_name", "未知")
                stars = repo.get("stars", 0)
                created = repo.get("created_at", "")
                logger.info(f"  {i}. {name} (★{stars}) - {created}")
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("用户中断抓取")
        return 1
    except Exception as e:
        logger.error(f"抓取失败: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 