#!/usr/bin/env python3
import csv
import argparse
from operator import itemgetter

def load_data(file_path):
    """加载CSV数据"""
    data = []
    with open(file_path, 'r', encoding='utf-8', errors='replace') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            # 将星数和增长速度转换为数值类型
            try:
                row['star数'] = int(row['star数'])
            except ValueError:
                row['star数'] = 0
                
            try:
                row['增长速度'] = float(row['增长速度'])
            except ValueError:
                row['增长速度'] = 0.0
                
            data.append(row)
    return data

def filter_by_category(data, category):
    """按类别过滤"""
    if not category:
        return data
    return [row for row in data if category in row['一级分类'] or category in row['二级分类']]

def filter_by_min_stars(data, min_stars):
    """按最小星数过滤"""
    return [row for row in data if row['star数'] >= min_stars]

def filter_by_min_growth_rate(data, min_growth_rate):
    """按最小增长率过滤"""
    return [row for row in data if row['增长速度'] >= min_growth_rate]

def sort_data(data, sort_by='star数', reverse=True):
    """排序数据"""
    return sorted(data, key=itemgetter(sort_by), reverse=reverse)

def save_results(data, output_file, limit=None):
    """保存结果到文件"""
    if limit:
        data = data[:limit]
        
    if data:
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=data[0].keys())
            writer.writeheader()
            writer.writerows(data)
        print(f"结果已保存到: {output_file}")
    else:
        print("没有找到符合条件的仓库")

def main():
    parser = argparse.ArgumentParser(description='对GitHub仓库数据进行排序和过滤')
    parser.add_argument('--input', default='data/repo_analysis.csv', help='输入CSV文件路径')
    parser.add_argument('--output', default='data/sorted_repos.csv', help='输出CSV文件路径')
    parser.add_argument('--sort', default='star数', choices=['star数', '增长速度'], help='排序字段')
    parser.add_argument('--category', help='按类别过滤')
    parser.add_argument('--min-stars', type=int, default=0, help='最小星数')
    parser.add_argument('--min-growth', type=float, default=0.0, help='最小增长速度')
    parser.add_argument('--limit', type=int, help='结果数量限制')
    
    args = parser.parse_args()
    
    print("加载数据...")
    data = load_data(args.input)
    print(f"加载了 {len(data)} 个仓库的数据")
    
    # 应用过滤器
    if args.category:
        print(f"按类别过滤: {args.category}")
        data = filter_by_category(data, args.category)
        print(f"过滤后剩余: {len(data)} 个仓库")
    
    if args.min_stars > 0:
        print(f"按最小星数过滤: {args.min_stars}")
        data = filter_by_min_stars(data, args.min_stars)
        print(f"过滤后剩余: {len(data)} 个仓库")
    
    if args.min_growth > 0:
        print(f"按最小增长速度过滤: {args.min_growth}")
        data = filter_by_min_growth_rate(data, args.min_growth)
        print(f"过滤后剩余: {len(data)} 个仓库")
    
    # 排序
    print(f"按 {args.sort} 排序")
    data = sort_data(data, args.sort)
    
    # 保存结果
    save_results(data, args.output, args.limit)
    
    if data:
        # 显示前10个结果
        print("\n前10个结果:")
        for i, row in enumerate(data[:10], 1):
            print(f"{i}. {row['项目名称']} - 星数: {row['star数']}, 增长速度: {row['增长速度']}")

if __name__ == "__main__":
    main() 