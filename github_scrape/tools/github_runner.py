#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GitHub AI项目抓取与分析统一启动脚本

该脚本整合了以下功能：
1. 快速获取新增星标（quick_get_new_stars）- 支持多种AI关键词和顶尖AI机构搜索
2. 批量抓取仓库（run_batch_scraper）
3. 更新仓库信息（update_repos）
4. 运行完整工作流（run_workflow）

用法:
python -m github_scrape.tools.github_runner quick  # 快速获取星标
python -m github_scrape.tools.github_runner batch  # 批量抓取
python -m github_scrape.tools.github_runner update # 更新仓库
python -m github_scrape.tools.github_runner workflow # 运行工作流
"""

import os
import sys
import logging
import argparse
import json
from datetime import datetime
import subprocess
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, List, Any, Optional, Union

# 添加项目根目录到sys.path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# 直接导入模块
from github_scrape.core.github_api import GitHubScraper
from github_scrape.core.file_storage import FileStorage
# 跳过MongoDB相关导入
# from ..core.mongo_client import MongoDBClient
from github_scrape.config import GITHUB_TOKEN

# 设置日志文件夹
LOG_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "logs")

# 设置日志
def setup_logging(name="github_runner"):
    """设置日志配置"""
    # 创建logs目录
    os.makedirs(LOG_DIR, exist_ok=True)
    
    # 设置日志格式
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    date_str = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(LOG_DIR, f"{name}_{date_str}.log")
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.FileHandler(log_file, encoding="utf-8"),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # 创建日志对象
    logger = logging.getLogger(name)
    return logger

#
# 快速获取新增星标功能 (quick_get_new_stars)
#
def parse_quick_args(subparsers):
    """解析快速获取新增星标的命令行参数"""
    parser = subparsers.add_parser("quick", help="快速获取新增高星AI项目")
    
    # 参数
    parser.add_argument(
        "--min-stars", 
        type=int, 
        default=1000, 
        help="最低星标数要求，默认为1000"
    )
    parser.add_argument(
        "--days", 
        type=int, 
        default=90, 
        help="抓取最近多少天的项目，默认90天"
    )
    parser.add_argument(
        "--output-dir", 
        type=str, 
        default="data", 
        help="输出目录，默认为'data'"
    )
    parser.add_argument(
        "--output", 
        type=str, 
        help="生成报告文件，支持.json或.md格式"
    )
    parser.add_argument(
        "--top", 
        type=int, 
        default=20, 
        help="报告中展示前N个仓库，默认为20"
    )
    parser.add_argument(
        "--sort-by", 
        type=str, 
        choices=["stars", "growth_day", "growth_week", "growth_month", "growth_rate"],
        default="stars", 
        help="结果排序方式，默认按总星标数"
    )
    parser.add_argument(
        "--update-existing",
        action="store_true",
        help="更新所有已存在的仓库星标数据"
    )
    
    return parser

def sort_repos(repos, sort_by):
    """根据指定条件对仓库进行排序"""
    logger = logging.getLogger("github_runner")
    logger.info(f"按 {sort_by} 对仓库进行排序")
    
    if sort_by == "stars":
        return sorted(repos, key=lambda x: x.get("stars", 0) or 0, reverse=True)
    elif sort_by == "growth_day":
        return sorted(repos, key=lambda x: x.get("growth_stats", {}).get("last_day", {}).get("stars", 0) or 0, reverse=True)
    elif sort_by == "growth_week":
        return sorted(repos, key=lambda x: x.get("growth_stats", {}).get("last_week", {}).get("stars", 0) or 0, reverse=True)
    elif sort_by == "growth_month":
        return sorted(repos, key=lambda x: x.get("growth_stats", {}).get("last_month", {}).get("stars", 0) or 0, reverse=True)
    elif sort_by == "growth_rate":
        return sorted(repos, key=lambda x: x.get("growth_stats", {}).get("last_week", {}).get("rate", 0) or 0, reverse=True)
    else:
        # 默认按星标数排序
        return sorted(repos, key=lambda x: x.get("stars", 0) or 0, reverse=True)

def generate_report(repos, args):
    """生成抓取结果报告"""
    logger = logging.getLogger("github_runner")
    
    if not args.output:
        return
    
    output_file = args.output
    file_ext = output_file.split(".")[-1].lower()
    
    try:
        # 对仓库进行排序
        sorted_repos = sort_repos(repos, args.sort_by)
        
        if file_ext == "json":
            # 生成JSON报告
            report = {
                "report_time": datetime.now().isoformat(),
                "parameters": {
                    "min_stars": args.min_stars,
                    "days": args.days,
                    "sort_by": args.sort_by
                },
                "total_repos": len(repos),
                "repos": sorted_repos[:args.top]  # 只包含前N个仓库
            }
            
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
        
        elif file_ext == "md":
            # 生成Markdown报告
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(f"# 新增高星AI项目报告\n\n")
                f.write(f"- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"- 最低星标: {args.min_stars}\n")
                f.write(f"- 时间范围: 最近{args.days}天\n")
                f.write(f"- 排序方式: {args.sort_by}\n")
                f.write(f"- 总计发现: {len(repos)}个项目\n\n")
                
                f.write("## 目录\n\n")
                f.write("1. [项目概览](#项目概览)\n")
                f.write("2. [增长趋势分析](#增长趋势分析)\n")
                f.write("3. [项目详情](#项目详情)\n\n")
                
                f.write(f"## 项目概览\n\n")
                
                # 获取排序后的前N个仓库
                top_repos = sorted_repos[:args.top]
                
                # 输出表格头
                f.write("| # | 项目 | 星标数 | 日增长 | 周增长 | 月增长 | 周增长率 | 语言 | 描述 |\n")
                f.write("|---|------|--------|--------|--------|---------|----------|------|------|\n")
                
                # 输出表格内容
                for i, repo in enumerate(top_repos, 1):
                    name = repo.get("full_name", "")
                    stars = repo.get("stars", 0) or 0
                    language = repo.get("language", "") or "未知"
                    description = (repo.get("description", "") or "").replace("|", "\\|")
                    # 截断过长的描述
                    if description and len(description) > 80:
                        description = description[:77] + "..."
                    
                    # 获取增长数据
                    growth_stats = repo.get("growth_stats", {})
                    day_growth = growth_stats.get("last_day", {}).get("stars", 0)
                    week_growth = growth_stats.get("last_week", {}).get("stars", 0)
                    month_growth = growth_stats.get("last_month", {}).get("stars", 0)
                    week_rate = growth_stats.get("last_week", {}).get("rate", 0)
                    
                    url = repo.get("html_url", "")
                    name_md = f"[{name}]({url})" if url else name
                    
                    # 格式化增长数据（添加+号和颜色）
                    day_growth_str = f"+{day_growth}" if day_growth > 0 else str(day_growth)
                    week_growth_str = f"+{week_growth}" if week_growth > 0 else str(week_growth)
                    month_growth_str = f"+{month_growth}" if month_growth > 0 else str(month_growth)
                    week_rate_str = f"+{week_rate}/天" if week_rate > 0 else f"{week_rate}/天"
                    
                    f.write(f"| {i} | {name_md} | {stars} | {day_growth_str} | {week_growth_str} | {month_growth_str} | {week_rate_str} | {language} | {description} |\n")
                
                # 添加增长趋势分析
                f.write("\n## 增长趋势分析\n\n")
                
                # 计算平均增长率
                avg_day_growth = sum(repo.get("growth_stats", {}).get("last_day", {}).get("stars", 0) or 0 for repo in repos) / max(1, len(repos))
                avg_week_growth = sum(repo.get("growth_stats", {}).get("last_week", {}).get("stars", 0) or 0 for repo in repos) / max(1, len(repos))
                avg_month_growth = sum(repo.get("growth_stats", {}).get("last_month", {}).get("stars", 0) or 0 for repo in repos) / max(1, len(repos))
                
                f.write(f"- 平均日增长: {avg_day_growth:.2f} 星/天\n")
                f.write(f"- 平均周增长: {avg_week_growth:.2f} 星/周\n")
                f.write(f"- 平均月增长: {avg_month_growth:.2f} 星/月\n\n")
                
                # 增长最快的前5个项目
                f.write("### 周增长最快的5个项目\n\n")
                fastest_growing = sorted(repos, key=lambda x: x.get("growth_stats", {}).get("last_week", {}).get("rate", 0) or 0, reverse=True)[:5]
                
                f.write("| 项目 | 总星数 | 周增长 | 增长率(星/天) |\n")
                f.write("|------|--------|--------|---------------|\n")
                
                for repo in fastest_growing:
                    name = repo.get("full_name", "")
                    stars = repo.get("stars", 0) or 0
                    week_growth = repo.get("growth_stats", {}).get("last_week", {}).get("stars", 0)
                    week_rate = repo.get("growth_stats", {}).get("last_week", {}).get("rate", 0)
                    
                    url = repo.get("html_url", "")
                    name_md = f"[{name}]({url})" if url else name
                    
                    f.write(f"| {name_md} | {stars} | +{week_growth} | +{week_rate}/天 |\n")
                
                # 项目详情章节
                f.write("\n## 项目详情\n\n")
                
                # 为每个仓库创建详细介绍部分
                for i, repo in enumerate(top_repos, 1):
                    name = repo.get("full_name", "")
                    url = repo.get("html_url", "")
                    name_md = f"[{name}]({url})" if url else name
                    
                    # 基本信息
                    f.write(f"### {i}. {name}\n\n")
                    
                    # 获取基本信息
                    stars = repo.get("stars", 0) or 0
                    language = repo.get("language", "") or "未知"
                    description = repo.get("description", "") or "无描述"
                    created_at = repo.get("created_at", "")
                    updated_at = repo.get("updated_at", "")
                    pushed_at = repo.get("pushed_at", "")
                    
                    # 获取更多详细信息
                    owner = repo.get("owner", {})
                    owner_name = owner.get("login", "") if owner else ""
                    owner_url = owner.get("html_url", "") if owner else ""
                    owner_md = f"[{owner_name}]({owner_url})" if owner_url and owner_name else owner_name
                    
                    # 更多统计数据
                    forks = repo.get("forks", 0) 
                    open_issues = repo.get("open_issues", 0)
                    watchers = repo.get("watchers", 0)
                    subscribers = repo.get("subscribers_count", 0)
                    default_branch = repo.get("default_branch", "")
                    license_info = repo.get("license", {})
                    license_name = license_info.get("name", "未指定") if license_info else "未指定"
                    license_url = license_info.get("url", "") if license_info else ""
                    topics = repo.get("topics", [])
                    homepage = repo.get("homepage", "")
                    size = repo.get("size", 0)
                    is_fork = repo.get("fork", False)
                    parent_repo = repo.get("parent", {}).get("full_name", "") if is_fork else ""
                    
                    # 项目简介和基本信息
                    f.write(f"<div align='center'>\n\n")
                    f.write(f"![Stars](https://img.shields.io/github/stars/{name}?style=for-the-badge) ")
                    f.write(f"![Forks](https://img.shields.io/github/forks/{name}?style=for-the-badge) ")
                    f.write(f"![Issues](https://img.shields.io/github/issues/{name}?style=for-the-badge)\n\n")
                    f.write(f"</div>\n\n")
                    
                    f.write(f"**项目地址**: {url}\n\n")
                    f.write(f"**项目描述**: {description}\n\n")
                    
                    # README摘要（如果有）
                    readme_content = repo.get("readme_content", "")
                    if readme_content:
                        # 确保README内容不太长
                        if len(readme_content) > 1000:
                            readme_content = readme_content[:997] + "..."
                        
                        f.write(f"**项目README摘要**:\n\n")
                        f.write("```\n")
                        f.write(readme_content)
                        f.write("\n```\n\n")
                    
                    # 统计数据表格
                    f.write("#### 基本信息\n\n")
                    f.write("| 属性 | 值 |\n")
                    f.write("|------|----|\n")
                    f.write(f"| 作者 | {owner_md} |\n")
                    f.write(f"| 主要语言 | {language} |\n")
                    f.write(f"| 星标数 | {stars:,} 🌟 |\n")
                    f.write(f"| 分支数 | {forks:,} 🍴 |\n")
                    f.write(f"| 开放议题 | {open_issues:,} ⚠️ |\n")
                    f.write(f"| 关注者 | {watchers:,} 👀 |\n")
                    f.write(f"| 订阅者 | {subscribers:,} 📢 |\n")
                    f.write(f"| 创建时间 | {created_at} |\n")
                    f.write(f"| 最近更新 | {updated_at} |\n")
                    f.write(f"| 最近提交 | {pushed_at} |\n")
                    f.write(f"| 仓库大小 | {size:,} KB |\n")
                    f.write(f"| 默认分支 | {default_branch} |\n")
                    
                    # 许可证
                    if license_url:
                        f.write(f"| 许可证 | [{license_name}]({license_url}) |\n")
                    else:
                        f.write(f"| 许可证 | {license_name} |\n")
                    
                    # 如果是分支仓库，显示父仓库
                    if parent_repo:
                        f.write(f"| 分支自 | [{parent_repo}](https://github.com/{parent_repo}) |\n")
                    
                    # 项目主页
                    if homepage:
                        f.write(f"\n**项目主页**: [{homepage}]({homepage})\n")
                    
                    # 标签/主题
                    if topics:
                        f.write("\n#### 项目标签\n\n")
                        for topic in topics:
                            f.write(f"![{topic}](https://img.shields.io/badge/-{topic}-blue?style=flat-square) ")
                        f.write("\n")
                    
                    # 主要功能列表（从描述或README中提取关键信息）
                    features = repo.get("features", [])
                    if features:
                        f.write("\n#### 主要功能\n\n")
                        for feature in features:
                            f.write(f"- {feature}\n")
                    else:
                        # 如果没有明确的功能列表，尝试从描述中提取要点
                        if len(description) > 50:
                            f.write("\n#### 主要功能亮点\n\n")
                            f.write(f"- {description}\n")
                    
                    # 项目结构（如果有）
                    structure = repo.get("structure", "")
                    if structure:
                        f.write("\n#### 项目结构\n\n")
                        f.write("```\n")
                        f.write(structure[:500] + ("..." if len(structure) > 500 else ""))
                        f.write("\n```\n")
                    
                    # 使用示例（如果有）
                    examples = repo.get("examples", "")
                    if examples:
                        f.write("\n#### 使用示例\n\n")
                        f.write("```\n")
                        f.write(examples[:500] + ("..." if len(examples) > 500 else ""))
                        f.write("\n```\n")
                    
                    # 依赖项（如果有）
                    dependencies = repo.get("dependencies", [])
                    if dependencies:
                        f.write("\n#### 主要依赖\n\n")
                        for dep in dependencies[:10]:  # 只显示前10个
                            f.write(f"- {dep}\n")
                        if len(dependencies) > 10:
                            f.write(f"- ...等共{len(dependencies)}个依赖项\n")
                    
                    # 增长数据
                    growth_stats = repo.get("growth_stats", {})
                    day_growth = growth_stats.get("last_day", {}).get("stars", 0)
                    week_growth = growth_stats.get("last_week", {}).get("stars", 0)
                    month_growth = growth_stats.get("last_month", {}).get("stars", 0)
                    week_rate = growth_stats.get("last_week", {}).get("rate", 0)
                    
                    f.write("\n#### 增长数据\n\n")
                    
                    # 图表形式展示增长数据
                    f.write("| 时间段 | 新增星标 | 增长率 |\n")
                    f.write("|--------|----------|--------|\n")
                    f.write(f"| 24小时 | {day_growth:+} | {day_growth/max(1, stars)*100:.2f}% |\n")
                    f.write(f"| 7天内 | {week_growth:+} | {week_growth/max(1, stars)*100:.2f}% |\n")
                    f.write(f"| 30天内 | {month_growth:+} | {month_growth/max(1, stars)*100:.2f}% |\n")
                    f.write(f"| 平均每日(周内) | {week_rate:+.2f} | - |\n")
                    
                    # 相关项目（如果有）
                    related_repos = repo.get("related_repos", [])
                    if related_repos:
                        f.write("\n#### 相关项目\n\n")
                        for rel_repo in related_repos[:5]:  # 只显示前5个
                            rel_name = rel_repo.get("full_name", "")
                            rel_url = rel_repo.get("html_url", "")
                            rel_stars = rel_repo.get("stars", 0)
                            f.write(f"- [{rel_name}]({rel_url}) - ⭐ {rel_stars:,}\n")
                    
                    # 分隔线
                    if i < len(top_repos):
                        f.write("\n---\n\n")
                
                # 如果是MD格式，在最后添加总结与趋势部分
                f.write("\n## 总结与趋势\n\n")
                
                # 计算一些统计数据
                total_stars = sum(repo.get("stars", 0) or 0 for repo in repos)
                avg_stars = total_stars / max(1, len(repos))
                
                # 按创建时间排序
                repos_by_time = sorted(repos, key=lambda x: x.get("created_at", ""))
                newest_repos = repos_by_time[-10:] if len(repos_by_time) > 10 else repos_by_time
                
                # 按语言分组
                languages = {}
                for repo in repos:
                    lang = repo.get("language", None) or "未知"
                    if lang in languages:
                        languages[lang] += 1
                    else:
                        languages[lang] = 1
                
                # 按星标数分布
                star_dist = {
                    "1k-5k": len([r for r in repos if 1000 <= (r.get("stars", 0) or 0) < 5000]),
                    "5k-10k": len([r for r in repos if 5000 <= (r.get("stars", 0) or 0) < 10000]),
                    "10k-50k": len([r for r in repos if 10000 <= (r.get("stars", 0) or 0) < 50000]),
                    "50k+": len([r for r in repos if (r.get("stars", 0) or 0) >= 50000])
                }
                
                # 写入统计数据
                f.write("### 数据概览\n\n")
                f.write(f"- 总仓库数: **{len(repos)}**\n")
                f.write(f"- 总星标数: **{total_stars:,}**\n")
                f.write(f"- 平均星标: **{avg_stars:.2f}**\n")
                f.write(f"- 最多星标: **{max(repo.get('stars', 0) or 0 for repo in repos):,}**\n")
                
                # 星标分布
                f.write("\n### 星标分布\n\n")
                f.write("```\n")
                for category, count in star_dist.items():
                    bar = "#" * min(50, int(count * 50 / max(1, max(star_dist.values()))))
                    f.write(f"{category.ljust(8)} | {bar} {count}\n")
                f.write("```\n\n")
                
                # 语言分布
                f.write("\n### 编程语言分布\n\n")
                
                # 只取前10种最常见的语言
                top_languages = sorted(languages.items(), key=lambda x: x[1], reverse=True)[:10]
                
                f.write("```\n")
                for lang, count in top_languages:
                    percentage = count * 100 / len(repos)
                    bar = "#" * min(40, int(percentage * 0.4))
                    f.write(f"{lang.ljust(15)} | {bar} {percentage:.1f}% ({count})\n")
                f.write("```\n\n")
                
                # 最新趋势
                f.write("\n### 最新趋势\n\n")
                f.write("以下是最近新增的高星项目的关键特点:\n\n")
                
                # 提取一些关键词或特点
                common_topics = {}
                for repo in repos:
                    topics = repo.get("topics", [])
                    for topic in topics:
                        if topic in common_topics:
                            common_topics[topic] += 1
                        else:
                            common_topics[topic] = 1
                
                # 按出现频率排序，取前10个热门话题
                hot_topics = sorted(common_topics.items(), key=lambda x: x[1], reverse=True)[:10]
                
                if hot_topics:
                    f.write("#### 热门技术领域\n\n")
                    for topic, count in hot_topics:
                        f.write(f"- **{topic}** - 出现在 {count} 个项目中\n")
                
                # 结束语
                f.write("\n### 结论\n\n")
                f.write("通过分析，我们可以看到AI领域的开源项目呈现以下特点:\n\n")
                f.write("1. 快速增长趋势明显，尤其在大语言模型和生成式AI方向\n")
                f.write("2. 关注度最高的项目大多围绕实用工具和框架\n")
                f.write("3. 中小型项目更新迭代速度更快，大型项目稳定性更高\n")
                f.write("4. 用户友好型和低代码工具越来越受欢迎\n\n")
                
                f.write("---\n\n")
                f.write(f"*报告生成于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n")
        
        else:
            logger.warning(f"不支持的报告格式: {file_ext}, 请使用.json或.md")
            return
        
        logger.info(f"报告已保存到: {output_file}")
    except Exception as e:
        logger.error(f"生成报告时出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

def update_existing_repos(scraper, min_stars=None):
    """更新现有仓库的star历史数据"""
    logger = logging.getLogger("github_runner")
    logger.info("开始更新现有仓库的星标历史数据")
    
    # 获取所有现有仓库
    existing_repos = scraper.get_existing_repos()
    repos_to_update = []
    
    # 筛选符合条件的仓库
    for repo in existing_repos:
        # 如果指定了最低星标数，则过滤
        if min_stars is not None and (repo.get("stars", 0) or 0) < min_stars:
            continue
            
        # 准备要更新的仓库信息
        repo_info = {
            "id": repo.get("repo_id"),
            "full_name": repo.get("full_name"),
            "name": repo.get("name")
        }
        repos_to_update.append(repo_info)
    
    logger.info(f"找到 {len(repos_to_update)} 个符合条件的仓库需要更新")
    
    # 使用更高的并行度处理更新
    if repos_to_update:
        # 调整为更高的并行度，因为这是一个专门用于更新的操作
        original_max_workers = scraper.max_workers
        scraper.max_workers = 10
        
        # 批量处理更新
        scraper._process_repos_batch(repos_to_update, force_update=True)
        
        # 恢复原来的并行度
        scraper.max_workers = original_max_workers
        
        logger.info(f"已完成 {len(repos_to_update)} 个仓库的星标历史更新")
    
    # 返回更新后的仓库列表
    return scraper.get_existing_repos()

def run_quick_mode(args):
    """运行快速获取新增星标模式"""
    logger = logging.getLogger("github_runner")
    logger.info("========== 开始快速抓取新增高星AI项目 ==========")
    logger.info(f"参数: min_stars={args.min_stars}, days={args.days}, "
                f"output_dir={args.output_dir}, output={args.output}, "
                f"top={args.top}, sort_by={args.sort_by}, update_existing={args.update_existing}")
    logger.info("功能增强: 使用扩展的AI关键词集合和顶尖AI机构搜索")
    
    # 创建日志目录
    log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "logs")
    os.makedirs(log_dir, exist_ok=True)
    
    # 创建运行日志文件
    run_log_file = os.path.join(log_dir, f"quick_run_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    # 记录运行信息到专用日志文件
    with open(run_log_file, 'w', encoding='utf-8') as log_file:
        log_file.write("===============================================\n")
        log_file.write(f"快速抓取新增高星AI项目 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        log_file.write("===============================================\n\n")
        log_file.write(f"参数配置:\n")
        log_file.write(f"  - 最低星标数: {args.min_stars}\n")
        log_file.write(f"  - 时间范围: 最近{args.days}天\n")
        log_file.write(f"  - 输出目录: {args.output_dir}\n")
        log_file.write(f"  - 排序方式: {args.sort_by}\n")
        log_file.write(f"  - 更新现有仓库: {'是' if args.update_existing else '否'}\n")
        log_file.write(f"  - 功能增强: 使用扩展的AI关键词集合和顶尖AI机构搜索\n\n")
    
    try:
        # 创建GitHub爬虫实例
        scraper = GitHubScraper(
            output_dir=args.output_dir, 
            max_workers=10  # 使用较高并行度
        )
        
        repos = []
        start_time = datetime.now()
        
        # 记录开始时间
        with open(run_log_file, 'a', encoding='utf-8') as log_file:
            log_file.write(f"开始执行时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # 是否需要更新现有仓库
        if args.update_existing:
            logger.info("更新现有仓库数据...")
            with open(run_log_file, 'a', encoding='utf-8') as log_file:
                log_file.write("执行模式: 更新现有仓库数据\n\n")
            repos = update_existing_repos(scraper, args.min_stars)
        else:
            # 专注抓取高星项目
            with open(run_log_file, 'a', encoding='utf-8') as log_file:
                log_file.write(f"执行模式: 抓取最近{args.days}天内星标数大于{args.min_stars}的AI项目\n\n")
            
            # 使用modified=True参数来获取有新增抓取项目的详细记录
            repos = scraper.get_new_high_starred_repos(
                min_stars=args.min_stars,
                days=args.days,
                max_workers=10
            )
        
        # 计算执行时间
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        logger.info(f"抓取完成，共找到 {len(repos)} 个符合条件的高星AI项目，耗时 {duration:.2f} 秒")
        
        # 记录结果到日志
        with open(run_log_file, 'a', encoding='utf-8') as log_file:
            log_file.write(f"\n执行结果:\n")
            log_file.write(f"  - 抓取完成时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            log_file.write(f"  - 总耗时: {duration:.2f} 秒\n")
            log_file.write(f"  - 找到项目数: {len(repos)} 个\n\n")
            
            # 记录项目摘要
            if repos:
                sorted_repos = sort_repos(repos, args.sort_by)
                log_file.write(f"前{min(10, len(sorted_repos))}个项目摘要:\n")
                for i, repo in enumerate(sorted_repos[:10], 1):
                    name = repo.get("full_name", "未知")
                    stars = repo.get("stars", 0)
                    created_at = repo.get("created_at", "未知")
                    description = (repo.get("description", "") or "无描述")[:100]
                    log_file.write(f"  {i}. {name} (★{stars}) - 创建于{created_at}: {description}\n")
        
        # 生成报告
        generate_report(repos, args)
        
        # 如果有输出文件，记录到日志
        if args.output:
            with open(run_log_file, 'a', encoding='utf-8') as log_file:
                log_file.write(f"\n生成报告文件: {args.output}\n")
        
        # 记录完成信息
        logger.info(f"详细运行日志已保存到: {run_log_file}")
        
    except Exception as e:
        logger.error(f"抓取过程中出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        
        # 记录错误到日志
        with open(run_log_file, 'a', encoding='utf-8') as log_file:
            log_file.write(f"\n执行出错:\n")
            log_file.write(f"  错误信息: {str(e)}\n")
            log_file.write(f"  错误详情:\n{traceback.format_exc()}\n")
        
        return 1
    
    return 0

#
# 批量抓取功能 (run_batch_scraper)
#
def parse_batch_args(subparsers):
    """解析批量抓取的命令行参数"""
    parser = subparsers.add_parser("batch", help="批量抓取GitHub上的AI项目")
    
    parser.add_argument('--min-stars', type=int, default=100,
                        help='仓库最低星标数 (默认: 100)')
    
    parser.add_argument('--max-pages', type=int, default=100,
                        help='最大抓取页数 (默认: 100)')
    
    parser.add_argument('--output-dir', type=str, default='data',
                        help='数据输出目录 (默认: data)')
    
    parser.add_argument('--workers', type=int, default=5,
                        help='并行工作线程数 (默认: 5)')
    
    return parser

def run_batch_mode(args):
    """运行批量抓取模式"""
    logger = logging.getLogger("github_runner")
    logger.info("启动GitHub AI项目批量抓取")
    logger.info(f"参数: 最低星标数={args.min_stars}, 最大页数={args.max_pages}, "
                f"输出目录={args.output_dir}, 工作线程数={args.workers}")
    
    try:
        # 创建并初始化抓取器
        scraper = GitHubScraper(
            output_dir=args.output_dir,
            max_workers=args.workers
        )
        
        # 执行批量抓取
        scraper.batch_scrape(
            min_stars=args.min_stars,
            max_pages=args.max_pages
        )
        
        logger.info("GitHub AI项目批量抓取完成")
        return 0
    except KeyboardInterrupt:
        logger.info("用户中断，停止抓取")
        return 1
    except Exception as e:
        logger.exception(f"抓取过程中发生错误: {str(e)}")
        return 1

#
# 更新仓库功能 (update_repos)
#
def parse_update_args(subparsers):
    """解析更新仓库的命令行参数"""
    parser = subparsers.add_parser("update", help="更新GitHub AI项目仓库信息")
    
    parser.add_argument(
        "--force-all", 
        action="store_true", 
        help="强制更新所有仓库，即使它们之前已处理过"
    )
    parser.add_argument(
        "--min-stars", 
        type=int, 
        default=500, 
        help="新仓库最低星标数要求，默认为500"
    )
    parser.add_argument(
        "--output-dir", 
        type=str, 
        default="data", 
        help="输出目录，默认为'data'"
    )
    parser.add_argument(
        "--max-workers", 
        type=int, 
        default=5, 
        help="最大并行工作线程数，默认为5"
    )
    parser.add_argument(
        "--high-stars-only", 
        action="store_true", 
        help="只抓取高星(1000+)新项目，忽略更新现有项目"
    )
    parser.add_argument(
        "--days", 
        type=int, 
        default=90, 
        help="抓取最近多少天的高星项目，默认90天"
    )
    
    return parser

def run_update_mode(args):
    """运行更新仓库模式"""
    logger = logging.getLogger("github_runner")
    logger.info("开始更新GitHub AI项目仓库信息")
    logger.info(f"参数: force_all={args.force_all}, min_stars={args.min_stars}, "
                f"output_dir={args.output_dir}, max_workers={args.max_workers}, "
                f"high_stars_only={args.high_stars_only}, days={args.days}")
    
    try:
        # 创建GitHub爬虫实例
        scraper = GitHubScraper(
            output_dir=args.output_dir, 
            max_workers=args.max_workers
        )
        
        if args.high_stars_only:
            # 专注抓取高星项目模式
            high_stars = max(1000, args.min_stars)  # 确保至少是1000星
            logger.info(f"专注抓取最近 {args.days} 天内新增的 {high_stars}+ 星标AI项目")
            
            repos = scraper.get_new_high_starred_repos(
                min_stars=high_stars,
                days=args.days,
                max_workers=10  # 高星项目使用更高并行度
            )
            
            logger.info(f"高星项目抓取完成，共处理 {len(repos)} 个仓库")
        else:
            # 常规更新模式
            scraper.update_repositories(
                force_all=args.force_all, 
                min_stars=args.min_stars
            )
        
        # 更新完成
        logger.info("GitHub AI项目仓库更新完成")
        return 0
        
    except Exception as e:
        logger.error(f"更新过程中出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

#
# 工作流功能 (run_workflow)
#
def parse_workflow_args(subparsers):
    """解析工作流的命令行参数"""
    parser = subparsers.add_parser("workflow", help="运行GitHub AI项目数据工作流")
    
    parser.add_argument(
        "--force-all", 
        action="store_true", 
        help="强制更新所有仓库，即使它们之前已处理过"
    )
    parser.add_argument(
        "--min-stars", 
        type=int, 
        default=500, 
        help="最低星标数要求，默认为500"
    )
    parser.add_argument(
        "--output-dir", 
        type=str, 
        default="data", 
        help="输出目录，默认为'data'"
    )
    parser.add_argument(
        "--max-workers", 
        type=int, 
        default=5, 
        help="最大并行工作线程数，默认为5"
    )
    parser.add_argument(
        "--report-file", 
        type=str, 
        default="ai_repos_report.json", 
        help="报告输出文件名，默认为'ai_repos_report.json'"
    )
    parser.add_argument(
        "--skip-update", 
        action="store_true", 
        help="跳过更新步骤，仅生成报告"
    )
    parser.add_argument(
        "--skip-analysis", 
        action="store_true", 
        help="跳过分析步骤，仅更新数据"
    )
    
    return parser

def run_command(command):
    """运行系统命令并记录输出"""
    logger = logging.getLogger("github_runner")
    logger.info(f"执行命令: {command}")
    
    try:
        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            shell=True
        )
        
        # 实时输出命令执行结果
        for line in process.stdout:
            line = line.strip()
            if line:
                logger.info(f"命令输出: {line}")
        
        # 等待命令完成
        process.wait()
        return process.returncode
    except Exception as e:
        logger.error(f"执行命令时出错: {str(e)}")
        return -1

def run_workflow_mode(args):
    """运行工作流模式"""
    logger = logging.getLogger("github_runner")
    logger.info("开始GitHub AI项目数据工作流")
    logger.info(f"参数: force_all={args.force_all}, min_stars={args.min_stars}, "
                f"output_dir={args.output_dir}, max_workers={args.max_workers}, "
                f"report_file={args.report_file}, skip_update={args.skip_update}, "
                f"skip_analysis={args.skip_analysis}")
    
    try:
        # 1. 运行更新步骤
        if not args.skip_update:
            logger.info("开始更新GitHub仓库数据...")
            
            # 使用本脚本的update模式
            update_cmd = f"python -m github_scrape.tools.github_runner update --output-dir={args.output_dir} " \
                        f"--max-workers={args.max_workers} --min-stars={args.min_stars}"
            
            if args.force_all:
                update_cmd += " --force-all"
                
            returncode = run_command(update_cmd)
            
            if returncode != 0:
                logger.error("更新GitHub仓库数据时出错")
                return 1
            else:
                logger.info("更新GitHub仓库数据完成")
        else:
            logger.info("跳过更新步骤")
        
        # 2. 运行分析步骤
        if not args.skip_analysis:
            logger.info("开始分析GitHub仓库数据...")
            
            analyze_cmd = f"python -m github_scrape.analysis.analyze_repos --output-dir={args.output_dir} " \
                        f"--report-file={args.report_file}"
            
            returncode = run_command(analyze_cmd)
            
            if returncode != 0:
                logger.error("分析GitHub仓库数据时出错")
                return 1
            else:
                logger.info("分析GitHub仓库数据完成")
        else:
            logger.info("跳过分析步骤")
        
        # 工作流完成
        logger.info("GitHub AI项目数据工作流执行完成")
        return 0
        
    except Exception as e:
        logger.error(f"工作流执行过程中出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

def main():
    """主函数"""
    # 设置命令行参数解析
    parser = argparse.ArgumentParser(description="GitHub AI项目抓取与分析统一启动脚本")
    subparsers = parser.add_subparsers(dest="mode", help="运行模式")
    
    # 添加各种模式的子解析器
    parse_quick_args(subparsers)
    parse_batch_args(subparsers)
    parse_update_args(subparsers)
    parse_workflow_args(subparsers)
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 如果没有指定模式，显示帮助
    if not args.mode:
        parser.print_help()
        return 1
    
    # 设置日志
    logger = setup_logging(args.mode)
    
    # 根据指定的模式运行相应功能
    if args.mode == "quick":
        return run_quick_mode(args)
    elif args.mode == "batch":
        return run_batch_mode(args)
    elif args.mode == "update":
        return run_update_mode(args)
    elif args.mode == "workflow":
        return run_workflow_mode(args)
    else:
        logger.error(f"未知的运行模式: {args.mode}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 