#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GitHub AI项目批量抓取脚本
执行大规模抓取GitHub上与AI相关的项目
"""

import logging
import argparse
import sys
import os

# 修复导入方式，使用直接导入
from scraper.github_api import GitHubScraper

# 配置日志
def setup_logging(log_dir="logs"):
    """设置日志配置"""
    os.makedirs(log_dir, exist_ok=True)
    
    # 创建日志格式
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO)
    
    # 文件处理器
    file_handler = logging.FileHandler(os.path.join(log_dir, 'github_scrape.log'), encoding='utf-8')
    file_handler.setFormatter(formatter)
    file_handler.setLevel(logging.DEBUG)
    
    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    
    return root_logger

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='批量抓取GitHub上的AI项目')
    
    parser.add_argument('--min-stars', type=int, default=100,
                        help='仓库最低星标数 (默认: 1000)')
    
    parser.add_argument('--max-pages', type=int, default=100,
                        help='最大抓取页数 (默认: 100)')
    
    parser.add_argument('--output-dir', type=str, default='data',
                        help='数据输出目录 (默认: data)')
    
    parser.add_argument('--workers', type=int, default=5,
                        help='并行工作线程数 (默认: 5)')
    
    return parser.parse_args()

def main():
    """主函数"""
    # 设置日志
    logger = setup_logging()
    logger.info("启动GitHub AI项目批量抓取")
    
    # 解析参数
    args = parse_arguments()
    logger.info(f"参数: 最低星标数={args.min_stars}, 最大页数={args.max_pages}, "
                f"输出目录={args.output_dir}, 工作线程数={args.workers}")
    
    try:
        # 创建并初始化抓取器
        scraper = GitHubScraper(
            output_dir=args.output_dir,
            max_workers=args.workers
        )
        
        # 执行批量抓取
        scraper.batch_scrape(
            min_stars=args.min_stars,
            max_pages=args.max_pages
        )
        
        logger.info("GitHub AI项目批量抓取完成")
    except KeyboardInterrupt:
        logger.info("用户中断，停止抓取")
    except Exception as e:
        logger.exception(f"抓取过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main() 