#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GitHub AI项目仓库分析脚本

该脚本用于分析已收集的GitHub AI项目数据并生成报告。
主要功能：
1. 生成基本统计信息（总数、平均星标数等）
2. 按照分类统计项目分布
3. 生成增长趋势报告（基于历史数据）
4. 识别快速增长的项目

用法:
python analyze_repos.py  # 分析默认data目录下的数据
python analyze_repos.py --output-dir custom_data  # 指定自定义数据目录
python analyze_repos.py --report-file report.json  # 指定报告输出文件
"""

import os
import sys
import json
import logging
import argparse
from datetime import datetime
from typing import Dict, List, Any, Tuple
from collections import Counter, defaultdict

# 添加项目根目录到sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
def setup_logging():
    """设置日志配置"""
    # 创建logs目录
    os.makedirs("logs", exist_ok=True)
    
    # 设置日志格式
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.FileHandler(f"logs/analyze_repos_{datetime.now().strftime('%Y%m%d')}.log", encoding="utf-8"),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # 创建日志对象
    logger = logging.getLogger("analyze_repos")
    return logger

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="分析GitHub AI项目仓库数据")
    parser.add_argument(
        "--output-dir", 
        type=str, 
        default="data", 
        help="包含仓库数据的目录，默认为'data'"
    )
    parser.add_argument(
        "--report-file", 
        type=str, 
        default="ai_repos_report.json", 
        help="报告输出文件名，默认为'ai_repos_report.json'"
    )
    parser.add_argument(
        "--top-n", 
        type=int, 
        default=20, 
        help="列出前N个排名的仓库，默认为20"
    )
    parser.add_argument(
        "--growth-days", 
        type=int, 
        default=30, 
        help="分析最近N天的增长趋势，默认为30天"
    )
    
    return parser.parse_args()

def load_repo_data(data_dir: str) -> List[Dict]:
    """从数据目录加载所有仓库数据"""
    logger = logging.getLogger("analyze_repos")
    repos_dir = os.path.join(data_dir, "repos")
    repos = []
    
    if not os.path.exists(repos_dir):
        logger.error(f"仓库目录不存在: {repos_dir}")
        return repos
        
    for filename in os.listdir(repos_dir):
        if filename.endswith('.json'):
            file_path = os.path.join(repos_dir, filename)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    repo_data = json.load(f)
                    repos.append(repo_data)
            except Exception as e:
                logger.error(f"读取仓库文件时出错 {file_path}: {str(e)}")
    
    logger.info(f"成功加载 {len(repos)} 个仓库数据")
    return repos

def generate_basic_stats(repos: List[Dict]) -> Dict:
    """生成基本统计信息"""
    if not repos:
        return {
            "total_repos": 0,
            "avg_stars": 0,
            "avg_forks": 0,
            "avg_watchers": 0,
            "total_stars": 0,
            "total_forks": 0,
            "languages": {}
        }
    
    # 计算基本统计数据
    total_repos = len(repos)
    total_stars = sum(repo.get("stars", 0) for repo in repos)
    total_forks = sum(repo.get("forks", 0) for repo in repos)
    total_watchers = sum(repo.get("watchers", 0) for repo in repos)
    
    # 统计语言分布
    languages = Counter(repo.get("language", "未知") for repo in repos)
    languages = dict(languages.most_common())
    
    # 计算平均值
    avg_stars = total_stars / total_repos if total_repos > 0 else 0
    avg_forks = total_forks / total_repos if total_repos > 0 else 0
    avg_watchers = total_watchers / total_repos if total_repos > 0 else 0
    
    # 统计更新时间分布
    update_time_stats = analyze_update_times(repos)
    
    return {
        "total_repos": total_repos,
        "avg_stars": round(avg_stars, 2),
        "avg_forks": round(avg_forks, 2),
        "avg_watchers": round(avg_watchers, 2),
        "total_stars": total_stars,
        "total_forks": total_forks,
        "total_watchers": total_watchers,
        "languages": languages,
        "update_stats": update_time_stats
    }

def analyze_update_times(repos: List[Dict]) -> Dict:
    """分析仓库更新时间分布"""
    now = datetime.utcnow()
    update_stats = {
        "last_day": 0,
        "last_week": 0,
        "last_month": 0,
        "last_quarter": 0,
        "last_year": 0,
        "older": 0
    }
    
    for repo in repos:
        try:
            last_scraped = repo.get("last_scraped", "")
            if not last_scraped:
                update_stats["older"] += 1
                continue
                
            update_time = datetime.fromisoformat(last_scraped)
            days_diff = (now - update_time).days
            
            if days_diff <= 1:
                update_stats["last_day"] += 1
            elif days_diff <= 7:
                update_stats["last_week"] += 1
            elif days_diff <= 30:
                update_stats["last_month"] += 1
            elif days_diff <= 90:
                update_stats["last_quarter"] += 1
            elif days_diff <= 365:
                update_stats["last_year"] += 1
            else:
                update_stats["older"] += 1
        except Exception:
            update_stats["older"] += 1
    
    return update_stats

def analyze_categories(repos: List[Dict]) -> Dict:
    """分析仓库分类信息"""
    # 统计一级分类
    primary_categories = Counter()
    for repo in repos:
        categories = repo.get("primary_categories", [])
        for category in categories:
            primary_categories[category] += 1
    
    # 统计二级分类
    secondary_categories = Counter()
    for repo in repos:
        categories = repo.get("secondary_categories", [])
        for category in categories:
            secondary_categories[category] += 1
    
    return {
        "primary_categories": dict(primary_categories.most_common()),
        "secondary_categories": dict(secondary_categories.most_common())
    }

def analyze_growth_trends(repos: List[Dict], days: int = 30) -> Dict:
    """分析仓库的增长趋势"""
    growth_data = {}
    
    for repo in repos:
        history = repo.get("history", [])
        if not history or len(history) < 2:
            continue
            
        # 尝试获取最早和最近的历史记录
        try:
            latest = history[-1]
            # 查找大约days天前的记录
            earlier = None
            for entry in reversed(history):
                try:
                    entry_time = datetime.fromisoformat(entry.get("date", ""))
                    latest_time = datetime.fromisoformat(latest.get("date", ""))
                    if (latest_time - entry_time).days >= days:
                        earlier = entry
                        break
                except (ValueError, TypeError):
                    continue
            
            # 如果没有找到足够早的记录，使用第一条
            if not earlier and len(history) > 1:
                earlier = history[0]
                
            if earlier and latest:
                # 计算增长率
                repo_name = repo.get("full_name", "unknown")
                stars_growth = (latest.get("stars", 0) or 0) - (earlier.get("stars", 0) or 0)
                forks_growth = (latest.get("forks", 0) or 0) - (earlier.get("forks", 0) or 0)
                
                growth_data[repo_name] = {
                    "name": repo.get("name", "unknown"),
                    "full_name": repo_name,
                    "url": repo.get("html_url", ""),
                    "stars_growth": stars_growth,
                    "forks_growth": forks_growth,
                    "current_stars": latest.get("stars", 0) or 0,
                    "current_forks": latest.get("forks", 0) or 0,
                    "stars_growth_percent": round((stars_growth / max(1, earlier.get("stars", 1))) * 100, 2) if earlier.get("stars", 0) else 0,
                    "days_period": days
                }
        except Exception as e:
            logging.getLogger("analyze_repos").error(f"分析仓库 {repo.get('full_name', 'unknown')} 增长趋势时出错: {str(e)}")
    
    return growth_data

def identify_top_repos(repos: List[Dict], top_n: int = 20) -> Dict:
    """识别各种指标下的顶级仓库"""
    # 按星标数排序
    top_by_stars = sorted(repos, key=lambda x: x.get("stars", 0) or 0, reverse=True)[:top_n]
    top_by_stars = [{
        "name": repo.get("name", "unknown"),
        "full_name": repo.get("full_name", "unknown"),
        "url": repo.get("html_url", ""),
        "stars": repo.get("stars", 0) or 0,
        "description": repo.get("description", "")
    } for repo in top_by_stars]
    
    # 按分叉数排序
    top_by_forks = sorted(repos, key=lambda x: x.get("forks", 0) or 0, reverse=True)[:top_n]
    top_by_forks = [{
        "name": repo.get("name", "unknown"),
        "full_name": repo.get("full_name", "unknown"),
        "url": repo.get("html_url", ""),
        "forks": repo.get("forks", 0) or 0,
        "stars": repo.get("stars", 0) or 0
    } for repo in top_by_forks]
    
    # 按最近更新时间排序
    try:
        recently_updated = sorted(repos, 
            key=lambda x: datetime.fromisoformat(x.get("updated_at", "1970-01-01T00:00:00")) 
            if x.get("updated_at") else datetime.min, 
            reverse=True
        )[:top_n]
    except (ValueError, TypeError):
        recently_updated = []
    
    recently_updated = [{
        "name": repo.get("name", "unknown"),
        "full_name": repo.get("full_name", "unknown"),
        "url": repo.get("html_url", ""),
        "updated_at": repo.get("updated_at", ""),
        "stars": repo.get("stars", 0) or 0
    } for repo in recently_updated]
    
    # 按最近创建时间排序
    try:
        recently_created = sorted(repos, 
            key=lambda x: datetime.fromisoformat(x.get("created_at", "1970-01-01T00:00:00")) 
            if x.get("created_at") else datetime.min, 
            reverse=True
        )[:top_n]
    except (ValueError, TypeError):
        recently_created = []
    
    recently_created = [{
        "name": repo.get("name", "unknown"),
        "full_name": repo.get("full_name", "unknown"),
        "url": repo.get("html_url", ""),
        "created_at": repo.get("created_at", ""),
        "stars": repo.get("stars", 0) or 0
    } for repo in recently_created]
    
    return {
        "top_by_stars": top_by_stars,
        "top_by_forks": top_by_forks,
        "recently_updated": recently_updated,
        "recently_created": recently_created
    }

def identify_fast_growing(growth_data: Dict, top_n: int = 20) -> Dict:
    """识别快速增长的仓库"""
    # 按星标增长排序
    by_stars_growth = sorted(growth_data.values(), key=lambda x: x.get("stars_growth", 0), reverse=True)[:top_n]
    
    # 按星标增长百分比排序
    by_stars_percent = sorted(growth_data.values(), key=lambda x: x.get("stars_growth_percent", 0), reverse=True)[:top_n]
    
    # 按分叉增长排序
    by_forks_growth = sorted(growth_data.values(), key=lambda x: x.get("forks_growth", 0), reverse=True)[:top_n]
    
    return {
        "by_stars_growth": by_stars_growth,
        "by_stars_percent": by_stars_percent,
        "by_forks_growth": by_forks_growth
    }

def generate_report(repos: List[Dict], args: argparse.Namespace) -> Dict:
    """生成综合报告"""
    # 生成基本统计信息
    basic_stats = generate_basic_stats(repos)
    
    # 分析分类信息
    category_stats = analyze_categories(repos)
    
    # 分析增长趋势
    growth_trends = analyze_growth_trends(repos, args.growth_days)
    
    # 识别顶级仓库
    top_repos = identify_top_repos(repos, args.top_n)
    
    # 识别快速增长的仓库
    fast_growing = identify_fast_growing(growth_trends, args.top_n)
    
    # 组合报告
    report = {
        "report_time": datetime.now().isoformat(),
        "basic_stats": basic_stats,
        "category_stats": category_stats,
        "top_repos": top_repos,
        "fast_growing": fast_growing,
        "report_params": {
            "top_n": args.top_n,
            "growth_days": args.growth_days
        }
    }
    
    return report

def save_report(report: Dict, output_file: str) -> None:
    """保存报告到文件"""
    logger = logging.getLogger("analyze_repos")
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        logger.info(f"报告已保存到 {output_file}")
    except Exception as e:
        logger.error(f"保存报告时出错: {str(e)}")

def main():
    """主函数"""
    # 设置日志
    logger = setup_logging()
    logger.info("开始分析GitHub AI项目仓库数据")
    
    # 解析命令行参数
    args = parse_args()
    logger.info(f"参数: output_dir={args.output_dir}, report_file={args.report_file}, "
                f"top_n={args.top_n}, growth_days={args.growth_days}")
    
    try:
        # 加载仓库数据
        repos = load_repo_data(args.output_dir)
        
        if not repos:
            logger.error("没有找到任何仓库数据")
            return 1
        
        # 生成报告
        report = generate_report(repos, args)
        
        # 保存报告
        save_report(report, args.report_file)
        
        # 输出基本统计信息
        basic_stats = report["basic_stats"]
        logger.info(f"分析完成. 共 {basic_stats['total_repos']} 个仓库, "
                   f"{basic_stats['total_stars']} 颗星, "
                   f"平均 {basic_stats['avg_stars']} 颗星/仓库")
        
    except Exception as e:
        logger.error(f"分析过程中出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 