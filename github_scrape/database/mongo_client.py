import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import time
from pymongo import MongoClient, UpdateOne
from pymongo.collection import Collection
from pymongo.errors import PyMongoError, ConnectionFailure, ServerSelectionTimeoutError
from config import MONGODB_URI, MONGODB_DB_NAME

logger = logging.getLogger(__name__)

class MongoDBClient:
    """MongoDB数据库操作类"""
    
    def __init__(self, uri: str = MONGODB_URI, db_name: str = MONGODB_DB_NAME, max_retries: int = 3, retry_delay: int = 5):
        self.uri = uri
        self.db_name = db_name
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.client = None
        self.db = None
        self.repos_collection = None
        self._connect()
        
    def _connect(self) -> bool:
        """连接到MongoDB，带重试机制"""
        retry_count = 0
        
        while retry_count < self.max_retries:
            try:
                logger.info(f"尝试连接MongoDB (尝试 {retry_count + 1}/{self.max_retries})...")
                self.client = MongoClient(self.uri, serverSelectionTimeoutMS=10000)
                
                # 测试连接
                self.client.admin.command('ping')
                
                self.db = self.client[self.db_name]
                self.repos_collection = self.db["repositories"]
                
                logger.info("MongoDB连接成功")
                self._setup_indexes()
                return True
                
            except (ConnectionFailure, ServerSelectionTimeoutError) as e:
                retry_count += 1
                logger.warning(f"MongoDB连接失败: {str(e)}. 重试 ({retry_count}/{self.max_retries})...")
                
                if retry_count >= self.max_retries:
                    logger.error(f"MongoDB连接失败，已达最大重试次数: {str(e)}")
                    self._handle_db_unavailable()
                    return False
                
                time.sleep(self.retry_delay)
                
        return False
        
    def _handle_db_unavailable(self):
        """处理数据库不可用情况"""
        logger.warning("MongoDB不可用，将使用文件存储代替")
        # 未来可以在这里实现备用存储机制
        
    def _setup_indexes(self):
        """设置必要的索引"""
        try:
            # 主键索引
            self.repos_collection.create_index("repo_id", unique=True)
            # 常用查询索引
            self.repos_collection.create_index("stars")
            self.repos_collection.create_index("primary_categories")
            self.repos_collection.create_index("secondary_categories")
            self.repos_collection.create_index("topics")
            self.repos_collection.create_index("language")
            self.repos_collection.create_index("full_name")
            logger.info("MongoDB索引设置完成")
        except PyMongoError as e:
            logger.error(f"设置MongoDB索引失败: {str(e)}")
    
    def save_repositories(self, repositories: List[Dict]) -> int:
        """批量保存或更新仓库数据"""
        if not repositories:
            return 0
        
        # 如果MongoDB不可用，保存到本地文件
        if self.client is None:
            return self._save_to_file(repositories)
            
        try:
            operations = []
            
            for repo in repositories:
                # 使用repo_id作为唯一标识
                repo_id = repo.get("repo_id")
                if not repo_id:
                    logger.warning(f"仓库缺少repo_id，跳过: {repo.get('full_name', 'unknown')}")
                    continue
                
                # 更新现有记录或插入新记录
                operations.append(
                    UpdateOne(
                        {"repo_id": repo_id},
                        {"$set": repo},
                        upsert=True
                    )
                )
            
            if operations:
                result = self.repos_collection.bulk_write(operations)
                return result.upserted_count + result.modified_count
            return 0
            
        except PyMongoError as e:
            logger.error(f"保存仓库数据到MongoDB失败: {str(e)}")
            # 尝试重新连接
            if self._connect():
                logger.info("重新连接MongoDB成功，重试保存操作")
                return self.save_repositories(repositories)
            else:
                # 如果仍然失败，保存到本地文件
                return self._save_to_file(repositories)
    
    def _save_to_file(self, repositories: List[Dict]) -> int:
        """将数据保存到本地文件（作为MongoDB不可用时的备用方案）"""
        import json
        import os
        from datetime import datetime
        
        try:
            # 创建备份目录
            backup_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'backup')
            os.makedirs(backup_dir, exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = os.path.join(backup_dir, f'repos_backup_{timestamp}.json')
            
            # 保存为JSON文件
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(repositories, f, ensure_ascii=False, indent=2)
            
            logger.info(f"已将{len(repositories)}个仓库数据保存到备份文件: {filename}")
            return len(repositories)
            
        except Exception as e:
            logger.error(f"保存到备份文件失败: {str(e)}")
            return 0
    
    def get_repository(self, repo_id: int) -> Optional[Dict]:
        """通过repo_id获取单个仓库信息"""
        if self.client is None:
            logger.warning("MongoDB不可用，无法获取仓库信息")
            return None
            
        try:
            return self.repos_collection.find_one({"repo_id": repo_id})
        except PyMongoError as e:
            logger.error(f"从MongoDB获取仓库信息失败: {str(e)}")
            if self._connect():
                return self.get_repository(repo_id)
            return None
    
    def get_repository_by_name(self, full_name: str) -> Optional[Dict]:
        """通过full_name获取单个仓库信息"""
        if self.client is None:
            logger.warning("MongoDB不可用，无法通过名称获取仓库信息")
            return None
            
        try:
            return self.repos_collection.find_one({"full_name": full_name})
        except PyMongoError as e:
            logger.error(f"从MongoDB通过名称获取仓库信息失败: {str(e)}")
            if self._connect():
                return self.get_repository_by_name(full_name)
            return None
    
    def get_repositories(self, 
                        query: Dict = None, 
                        sort_by: str = "stars", 
                        sort_order: int = -1, 
                        skip: int = 0, 
                        limit: int = 100) -> List[Dict]:
        """获取符合条件的仓库列表"""
        if self.client is None:
            logger.warning("MongoDB不可用，无法获取仓库列表")
            return []
            
        if query is None:
            query = {}
            
        try:
            cursor = self.repos_collection.find(query)
            cursor = cursor.sort(sort_by, sort_order).skip(skip).limit(limit)
            return list(cursor)
        except PyMongoError as e:
            logger.error(f"从MongoDB获取仓库列表失败: {str(e)}")
            if self._connect():
                return self.get_repositories(query, sort_by, sort_order, skip, limit)
            return []
    
    def count_repositories(self, query: Dict = None) -> int:
        """计算符合条件的仓库数量"""
        if self.client is None:
            logger.warning("MongoDB不可用，无法计算仓库数量")
            return 0
            
        if query is None:
            query = {}
            
        try:
            return self.repos_collection.count_documents(query)
        except PyMongoError as e:
            logger.error(f"计算MongoDB仓库数量失败: {str(e)}")
            if self._connect():
                return self.count_repositories(query)
            return 0
    
    def update_repository(self, repo_id: int, update_data: Dict) -> bool:
        """更新单个仓库信息"""
        if self.client is None:
            logger.warning("MongoDB不可用，无法更新仓库信息")
            return False
            
        try:
            result = self.repos_collection.update_one(
                {"repo_id": repo_id},
                {"$set": update_data}
            )
            return result.modified_count > 0
        except PyMongoError as e:
            logger.error(f"更新MongoDB仓库信息失败: {str(e)}")
            if self._connect():
                return self.update_repository(repo_id, update_data)
            return False
    
    def delete_repository(self, repo_id: int) -> bool:
        """删除单个仓库"""
        if self.client is None:
            logger.warning("MongoDB不可用，无法删除仓库")
            return False
            
        try:
            result = self.repos_collection.delete_one({"repo_id": repo_id})
            return result.deleted_count > 0
        except PyMongoError as e:
            logger.error(f"从MongoDB删除仓库失败: {str(e)}")
            if self._connect():
                return self.delete_repository(repo_id)
            return False
    
    def get_outdated_repositories(self, days: int = 7) -> List[Dict]:
        """获取超过指定天数未更新的仓库列表"""
        if self.client is None:
            logger.warning("MongoDB不可用，无法获取过期仓库列表")
            return []
            
        from datetime import datetime, timedelta
        
        cutoff_date = (datetime.utcnow() - timedelta(days=days)).isoformat()
        
        try:
            query = {
                "$or": [
                    {"last_scraped": {"$lt": cutoff_date}},
                    {"last_scraped": {"$exists": False}}
                ]
            }
            return list(self.repos_collection.find(query))
        except PyMongoError as e:
            logger.error(f"获取过期仓库列表失败: {str(e)}")
            if self._connect():
                return self.get_outdated_repositories(days)
            return []
    
    def close(self):
        """关闭MongoDB连接"""
        if self.client:
            self.client.close()
            self.client = None 