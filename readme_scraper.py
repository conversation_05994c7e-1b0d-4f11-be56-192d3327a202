"""
GitHub仓库README爬虫模块

负责抓取GitHub仓库的README文件内容和相关媒体资源
"""

import logging
import os
import time
from typing import Any, Optional
from urllib.parse import urljoin, urlparse

import requests
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

logger = logging.getLogger(__name__)


class GitHubScraper:
    """GitHub仓库README爬虫"""

    def __init__(self, headless: bool = True, wait_time: int = 5, media_dir: str = "github_media"):
        """
        初始化爬虫

        Args:
            headless: 是否使用无头模式
            wait_time: 页面加载等待时间
            media_dir: 媒体文件保存目录
        """
        self.headless = headless
        self.wait_time = wait_time
        self.media_dir = media_dir

        # 创建媒体保存目录
        os.makedirs(self.media_dir, exist_ok=True)

        logger.info("GitHub爬虫初始化完成")

    def _setup_driver(self) -> webdriver.Chrome:
        """
        设置并返回Chrome浏览器驱动

        Returns:
            Chrome浏览器驱动实例
        """
        # 设置Chrome选项
        options = Options()
        if self.headless:
            options.add_argument("--headless")

        options.add_argument("--disable-gpu")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")

        # 创建Chrome驱动
        driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)

        return driver

    def scrape_github_readme(self, repo_url: str) -> tuple[str, list[dict[str, Any]]]:
        """
        抓取GitHub仓库README内容和相关媒体资源

        Args:
            repo_url: GitHub仓库URL

        Returns:
            README内容和媒体元素列表
        """
        driver = None
        try:
            # 设置浏览器驱动
            driver = self._setup_driver()

            # 加载仓库页面
            logger.info(f"正在加载仓库页面: {repo_url}")
            driver.get(repo_url)

            # 等待页面加载
            time.sleep(self.wait_time)

            # 解析页面
            soup = BeautifulSoup(driver.page_source, "html.parser")

            # 提取README部分
            readme_section = self._extract_readme_section(soup)
            if not readme_section:
                logger.warning("未找到README部分")
                return "", []

            # 提取README内容
            readme_content = readme_section.get_text(strip=True)

            # 提取媒体元素
            media_elements = self._extract_media_elements(readme_section, repo_url)

            logger.info(f"抓取完成，发现 {len(media_elements)} 个媒体元素")

            return readme_content, media_elements

        except Exception as e:
            logger.error(f"抓取过程出错: {e}")
            return "", []

        finally:
            # 关闭浏览器驱动
            if driver:
                driver.quit()

    def _extract_readme_section(self, soup: BeautifulSoup) -> Optional[BeautifulSoup]:
        """
        从GitHub页面中提取README部分

        Args:
            soup: BeautifulSoup对象

        Returns:
            README部分的BeautifulSoup对象
        """
        # GitHub上README通常位于下列选择器标识的元素中
        readme_selectors = [
            "article.markdown-body",
            "#readme article",
            ".Box-body.readme.blob.js-code-block-container",
            ".Box-body.p-4",
            "#readme",
        ]

        for selector in readme_selectors:
            readme_section = soup.select_one(selector)
            if readme_section:
                return readme_section

        return None

    def _extract_media_elements(self, readme_section: BeautifulSoup, base_url: str) -> list[dict[str, Any]]:
        """
        从README部分提取媒体元素

        Args:
            readme_section: README部分的BeautifulSoup对象
            base_url: 基础URL

        Returns:
            媒体元素字典列表
        """
        media_elements = []

        # 提取图片
        img_counter = 0
        for img in readme_section.find_all("img"):
            img_counter += 1
            src = img.get("src")
            if src:
                # 构建完整URL
                full_url = urljoin(base_url, src)

                # 判断媒体类型
                media_type = "image"
                if ".gif" in src.lower():
                    media_type = "gif"
                elif any(term in src.lower() for term in ["diagram", "chart", "graph"]):
                    media_type = "diagram"

                # 下载媒体
                local_path = self._download_media(full_url, media_type, img_counter)
                if local_path:
                    # 查找最近的文本
                    nearest_text = self._find_nearest_text(img)

                    # 添加到媒体元素列表
                    media_elements.append(
                        {"type": media_type, "url": full_url, "local_path": local_path, "nearest_text": nearest_text}
                    )

        # 提取视频
        video_counter = 0
        for video in readme_section.find_all(["video", "source"]):
            video_counter += 1
            src = video.get("src")
            if src:
                full_url = urljoin(base_url, src)
                local_path = self._download_media(full_url, "video", video_counter)
                if local_path:
                    nearest_text = self._find_nearest_text(video)
                    media_elements.append(
                        {"type": "video", "url": full_url, "local_path": local_path, "nearest_text": nearest_text}
                    )

        # 提取链接中的媒体
        link_counter = 0
        for a in readme_section.find_all("a"):
            href = a.get("href")
            media_type = None

            # 判断链接是否指向媒体文件
            if href:
                if any(ext in href.lower() for ext in [".gif"]):
                    media_type = "gif"
                    link_counter += 1
                elif any(ext in href.lower() for ext in [".mp4", ".webm", ".mov"]):
                    media_type = "video"
                    link_counter += 1
                elif any(ext in href.lower() for ext in [".jpg", ".jpeg", ".png", ".svg"]):
                    media_type = "image"
                    link_counter += 1

            if media_type:
                full_url = urljoin(base_url, href)
                local_path = self._download_media(full_url, media_type, link_counter)
                if local_path:
                    nearest_text = self._find_nearest_text(a)
                    media_elements.append(
                        {"type": media_type, "url": full_url, "local_path": local_path, "nearest_text": nearest_text}
                    )

        return media_elements

    def _download_media(self, url: str, media_type: str, counter: int) -> Optional[str]:
        """
        下载媒体文件

        Args:
            url: 媒体URL
            media_type: 媒体类型
            counter: 计数器

        Returns:
            本地文件路径，如果下载失败则返回None
        """
        # 验证URL有效性
        if not self._is_valid_url(url):
            logger.warning(f"无效的媒体URL: {url}")
            return None

        try:
            # 下载文件
            response = requests.get(url, stream=True, timeout=30)
            if response.status_code != 200:
                logger.warning(f"下载失败，状态码: {response.status_code}, URL: {url}")
                return None

            # 确定文件扩展名
            content_type = response.headers.get("content-type", "")
            ext = self._get_extension_from_content_type(content_type)

            # 如果无法从content-type确定扩展名，从URL中提取
            if not ext:
                path = urlparse(url).path
                ext = path.split(".")[-1] if "." in path else "jpg"
                if len(ext) > 5:  # 如果扩展名看起来无效
                    ext = "jpg"

            # 构建文件路径
            filename = f"{media_type}_{counter}.{ext}"
            filepath = os.path.join(self.media_dir, filename)

            # 保存文件
            with open(filepath, "wb") as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            logger.info(f"媒体已下载: {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"下载媒体时出错: {e}, URL: {url}")
            return None

    def _is_valid_url(self, url: str) -> bool:
        """
        检查URL是否有效

        Args:
            url: URL字符串

        Returns:
            URL是否有效
        """
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except:
            return False

    def _get_extension_from_content_type(self, content_type: str) -> str:
        """
        从content-type中获取文件扩展名

        Args:
            content_type: Content-Type头部值

        Returns:
            文件扩展名
        """
        if "image/jpeg" in content_type or "image/jpg" in content_type:
            return "jpg"
        elif "image/png" in content_type:
            return "png"
        elif "image/gif" in content_type:
            return "gif"
        elif "image/svg+xml" in content_type:
            return "svg"
        elif "video" in content_type:
            return "mp4"
        else:
            return ""

    def _find_nearest_text(self, element) -> str:
        """
        查找元素最近的文本内容

        Args:
            element: BeautifulSoup元素

        Returns:
            最近的文本内容
        """
        # 首先检查元素自身是否有文本
        element_text = element.get_text(strip=True)
        if element_text:
            return element_text

        # 检查父元素
        parent = element.parent
        depth = 0
        max_depth = 3  # 限制向上查找的深度

        while parent and depth < max_depth:
            parent_text = parent.get_text(strip=True)
            if parent_text and parent.name not in ["body", "html"]:
                return parent_text
            parent = parent.parent
            depth += 1

        # 检查前一个兄弟元素
        prev_sibling = element.find_previous_sibling()
        if prev_sibling:
            prev_text = prev_sibling.get_text(strip=True)
            if prev_text:
                return prev_text

        # 检查后一个兄弟元素
        next_sibling = element.find_next_sibling()
        if next_sibling:
            next_text = next_sibling.get_text(strip=True)
            if next_text:
                return next_text

        # 如果没有找到相关文本
        return "无相关文本"
