# Agentic Feynman - 智能视频生成工具

基于AI Agent的智能视频生成系统，从多种材料源自动生成高质量讲解视频。

## 🚀 快速使用

### 基本命令

```bash
python feynman_workflow_refactor.py
```
### 配置设置

在 `config/config.yaml` 中设置材料源：

```yaml
material:
  sources:
    github:
      enabled: true
      url: "https://github.com/用户名/仓库名"
    pdf:
      enabled: false
      url: "https://arxiv.org/abs/论文ID"
    chat:
      enabled: false
      purpose: "您想了解的主题"
```

## 🎬 工作流程

1. **材料解析** - 提取内容
2. **素材生成** - 生成讲解素材
3. **分镜设计** - 创建动画脚本
4. **视频渲染** - 输出最终视频

## 📁 输出文件

生成的视频保存在 `output/项目名称/videos/` 目录下。

---

# 智能网页内容分析和录制工具

这是一个基于Selenium的智能Agent，可以自动抓取网页全部元素，通过内容分析算法识别核心区域，并使用录屏技术自动对核心内容施加动态聚焦(focus)和放大(zoom in)效果。

## 功能特点

- **智能内容分析**：自动识别网页中的重要内容区域
- **动态聚焦效果**：对关键内容区域进行平滑放大和聚焦
- **自然的浏览体验**：模拟人类阅读习惯的页面浏览
- **高度可定制**：可调整视频时长、分辨率和其他参数

## 安装

1. 确保已安装 Python 3.7 或更高版本
2. 安装依赖包：

```bash
pip install -r requirements.txt
```

## 使用方法

基本用法：

```bash
python web_content_analyzer.py https://example.com
```

### 参数说明

- `url`：要分析和录制的网页URL（必需）
- `--output, -o`：输出视频文件路径（默认：output.mp4）
- `--duration, -d`：视频时长，单位秒（默认：20）
- `--width, -w`：视频宽度，单位像素（默认：1280）
- `--height, -h`：视频高度，单位像素（默认：720）
- `--fps`：视频的每秒帧数（默认：15）
- `--max-elements`：最多聚焦的关键元素数量（默认：5）
- `--headless`：使用无头模式，不显示浏览器窗口

### 示例

录制GitHub仓库页面的30秒视频：

```bash
python web_content_analyzer.py https://github.com/tensorflow/tensorflow --output tensorflow-demo.mp4 --duration 30
```

以高分辨率录制网站首页：

```bash
python web_content_analyzer.py https://www.example.com --output example-hd.mp4 --width 1920 --height 1080
```

## 工作原理

1. **页面分析**：程序首先加载目标网页，然后分析页面结构和内容
2. **内容评分**：基于位置、大小、文本相关性等因素对页面元素进行重要性评分
3. **关键内容识别**：识别得分最高的元素作为关键内容区域
4. **智能录制**：按照优化的顺序依次聚焦关键区域，并平滑过渡
5. **视频生成**：将捕获的帧合成为高质量MP4视频

## 技术细节

- 使用Selenium WebDriver进行浏览器控制
- 通过CV2(OpenCV)进行视频处理
- 使用NLTK和scikit-learn进行内容分析（如可用）
- 应用缓动函数创建平滑动画效果

## 系统要求

- Python 3.7+
- Chrome浏览器
- 足够的系统内存（推荐4GB以上）

# 如何在Storyboard框架中增加新的体裁类型

本项目采用基于体裁的内容分析和处理系统，支持针对不同内容类型（如学术论文、GitHub项目、博客文章等）进行专门优化的分镜生成。以下是添加新体裁类型的完整步骤：

## 1. 定义新体裁类型

在 `agents/generate_manim_dsl_agent_refactored.py` 文件中的 `ContentGenre` 枚举类中添加新的体裁类型：

```python
class ContentGenre(Enum):
    """内容体裁类型"""
    PAPER = "paper"           # 学术论文（通用）
    REVIEW_PAPER = "review_paper"  # 综述论文
    TECH_REPORT = "tech_report"    # 技术报告论文
    GITHUB = "github"         # GitHub项目
    BLOG = "blog"             # 网页博客
    CASE_STUDY = "case_study" # 综合案例
    BOOK = "book"             # 本地书籍
    GENERAL = "general"       # 通用内容
    # 添加新的体裁类型
    TUTORIAL = "tutorial"     # 教程类内容
    NEWS = "news"             # 新闻资讯
    DOCUMENTATION = "documentation"  # 技术文档
```

## 2. 更新体裁检测逻辑

在 `GenreDetector.detect_genre()` 方法中添加新体裁的关键词检测逻辑：

```python
@staticmethod
def detect_genre(content: str, purpose: str = "") -> ContentGenre:
    """根据用户目的检测体裁类型"""
    purpose_lower = purpose.lower()
    logger.info(f"开始体裁检测 - 用户目的: '{purpose}'")
    
    # 现有检测逻辑...
    
    # 添加新体裁的检测逻辑
    elif any(keyword in purpose_lower for keyword in ['教程', 'tutorial', '指南', '入门', '学习']):
        logger.info("✅ 检测到关键词匹配 -> 教程体裁")
        return ContentGenre.TUTORIAL
    elif any(keyword in purpose_lower for keyword in ['新闻', 'news', '资讯', '报道', '动态']):
        logger.info("✅ 检测到关键词匹配 -> 新闻体裁")
        return ContentGenre.NEWS
    elif any(keyword in purpose_lower for keyword in ['文档', 'documentation', 'docs', 'api', '手册']):
        logger.info("✅ 检测到关键词匹配 -> 技术文档体裁")
        return ContentGenre.DOCUMENTATION
    
    # 默认为通用类型
    logger.info("⚠️ 未检测到特定关键词，使用通用体裁")
    return ContentGenre.GENERAL
```

## 3. 添加体裁模板文件映射

在 `PromptLoader._load_templates()` 方法中的 `genre_files` 字典中添加新体裁的模板文件映射：

```python
# 加载各体裁专用模板
genre_files = {
    ContentGenre.PAPER: "paper_prompt.md",
    ContentGenre.REVIEW_PAPER: "review_paper_prompt.md",
    ContentGenre.TECH_REPORT: "tech_report_prompt.md",
    ContentGenre.GITHUB: "github_prompt.md", 
    ContentGenre.BLOG: "blog_prompt.md",
    ContentGenre.CASE_STUDY: "case_study_prompt.md",
    ContentGenre.BOOK: "book_prompt.md",
    # 添加新体裁的模板文件映射
    ContentGenre.TUTORIAL: "tutorial_prompt.md",
    ContentGenre.NEWS: "news_prompt.md",
    ContentGenre.DOCUMENTATION: "documentation_prompt.md"
}
```

## 4. 创建体裁专用Prompt模板

在 `prompts/storyboard_prompts/` 目录下创建新的体裁专用prompt模板文件。以教程体裁为例，创建 `tutorial_prompt.md`：

```markdown
# 教程体裁专用Prompt

## 教程内容特色要求

### 内容理解重点
- **学习目标**: 明确教程要教会用户什么技能或知识
- **难度层次**: 识别内容的难度级别，适配目标受众
- **实践性**: 突出动手操作和实际应用的部分

### 分镜设计原则
- **循序渐进**: 按照学习的逻辑顺序组织内容
- **重点突出**: 对关键概念和操作步骤进行重点展示
- **实例驱动**: 优先使用具体的代码示例和操作演示

### 特殊处理要点
- **必须** 在每个关键步骤前添加"第X步"或"接下来"等引导词
- **必须** 对代码块和操作截图给予特别关注
- **必须** 使用渐进式的信息展示，避免一次性展示过多内容
- **必须** 在适当位置添加学习提示和注意事项
```

## 5. 体裁模板设计最佳实践

设计新体裁模板时，请遵循以下原则：

#### 模板结构
- **内容理解重点**: 定义该体裁内容的核心特征
- **分镜设计原则**: 指导如何组织和展示该体裁的内容
- **特殊处理要点**: 该体裁特有的处理要求和注意事项

#### 内容要素
- **必须项**: 使用"必须"标记不可忽略的要求
- **优先项**: 使用"优先"标记重要但可选的建议
- **禁止项**: 明确该体裁应避免的处理方式

#### 示例参考
可以参考现有的体裁模板文件：
- `github_prompt.md`: 项目展示类内容
- `paper_prompt.md`: 学术严谨类内容  
- `blog_prompt.md`: 轻松易读类内容
- `tutorial_prompt.md`: 教学指导类内容

---

# 如何添加新的Enhancement Tools

本项目采用智能工具选择架构，支持基于内容分析和用户目标的自动工具选择。系统将Enhancement Tools分为四大类别，并使用LLM进行智能选择。以下是添加新工具的完整步骤：

## 🏗️ 工具分类架构

Enhancement Tools分为四大类别：

```python
class ToolCategory(Enum):
    CONTENT_ORGANIZATION = "A_内容结构化组织"     # 时间轴、表格、公式、框架图等
    MULTIMODAL_PRESENTATION = "B_多模态呈现"     # 动态图表、图片、emoji、录屏等  
    DEEP_INSIGHTS = "C_深度洞察"               # 对比、评估、洞察、深度问答等
    SMART_INTERACTION = "D_智能交互"           # 案例、模拟器、交互式媒体等
```

## 📝 添加新工具的步骤

### 1. 继承EnhancementTool基类

在 `agents/material_enhancement.py` 中创建新的工具类：

```python
class YourNewTool(EnhancementTool):
    """新工具类 - 选择合适的类别"""
    
    # 工具基本信息
    tool_name = "your_new_tool"
    tool_description = "详细描述工具的功能和作用"
    tool_category = ToolCategory.CONTENT_ORGANIZATION  # 选择合适的类别
    
    # 适用场景描述 - 供LLM理解和决策
    suitable_content_types = ["适合的内容类型1", "适合的内容类型2"]
    suitable_purposes = ["适合的用户目标1", "适合的用户目标2"] 
    required_conditions = ["必需的条件1", "必需的条件2"]
    
    # 不适用场景描述 - 帮助LLM做负面判断
    unsuitable_content_types = ["不适合的内容类型1", "不适合的内容类型2"]
    unsuitable_purposes = ["不适合的用户目标1", "不适合的用户目标2"]
    blocking_conditions = ["阻止使用的条件1", "阻止使用的条件2"]
```

### 2. 实现必需的抽象方法

```python
def get_tool_info(self) -> Dict[str, Any]:
    """获取工具信息供LLM决策使用"""
    return {
        "name": self.tool_name,
        "description": self.tool_description,
        "category": self.tool_category.value,
        "suitable_content": self.suitable_content_types,
        "suitable_purposes": self.suitable_purposes,
        "required_conditions": self.required_conditions,
        "unsuitable_content": self.unsuitable_content_types,
        "unsuitable_purposes": self.unsuitable_purposes,
        "blocking_conditions": self.blocking_conditions,
        "output_type": "工具输出的内容类型",
        "typical_duration": "典型执行时间",
        "use_case": "工具使用场景的详细描述"
    }

def can_apply(self, content: str, purpose: str, context: Dict[str, Any]) -> bool:
    """简化的可用性检查 - 只检查基本配置条件"""
    # 检查配置、依赖等基本条件
    # 复杂的适用性判断由LLM处理
    if not self.config:
        return False
    
    # 检查工具特定的配置项
    tool_config = self.config.get("material", {}).get("material_enhance", {})
    if not tool_config.get("your_tool_config_key", False):
        return False
    
    return True

def apply_tool(self, content: str, output_dir: str, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """执行工具的核心逻辑"""
    if not self.can_apply(content, context.get("purpose", ""), context):
        return None
    
    try:
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 执行工具的具体逻辑
        result = self._execute_tool_logic(content, output_dir, context)
        
        if result:
            return {
                "tool_name": self.tool_name,
                "type": "your_tool_type",
                "output_path": result.get("output_path"),
                "status": "success",
                "metadata": result.get("metadata", {})
            }
    except Exception as e:
        logger.error(f"工具执行失败: {e}")
        return None

def generate_intro(self, tool_result: Dict[str, Any]) -> str:
    """生成工具输出的介绍文本"""
    if tool_result.get("status") == "success":
        return f"✅ {self.tool_description}已生成，输出路径: {tool_result.get('output_path')}"
    else:
        return f"❌ {self.tool_name}执行失败"
```

### 3. 实现工具特定逻辑

```python
def _execute_tool_logic(self, content: str, output_dir: str, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """实现工具的具体功能"""
    # 这里实现你的工具逻辑
    # 例如：生成图表、处理数据、创建文件等
    
    output_file = os.path.join(output_dir, "tool_output.ext")
    
    # 执行具体操作...
    
    return {
        "output_path": output_file,
        "metadata": {
            "processed_content_length": len(content),
            "creation_time": "timestamp"
        }
    }
```

### 4. 注册工具到系统

在 `ToolRegistry` 中注册新工具：

```python
class ToolRegistry:
    _tools = {
        "screen_recording": ScreenRecordingTool,
        "timeline_generation": TimelineTool,
        "your_new_tool": YourNewTool,  # 添加新工具
    }
```

### 5. 配置工具开关

在配置文件中添加工具开关：

```yaml
# config/config.yaml
material:
  material_enhance:
    screen_record: true
    timeline_generation: true
    your_new_tool: true  # 添加新工具配置
```

## 💡 工具设计最佳实践

### 描述信息设计原则

1. **具体明确**: 描述应该具体到LLM能够准确理解适用场景
2. **正负并重**: 既要描述适用场景，也要明确不适用场景
3. **语义丰富**: 使用多样化的词汇帮助LLM理解

### 示例：好的描述 vs 差的描述

❌ **差的描述**:
```python
suitable_content_types = ["文档", "内容"]
unsuitable_content_types = ["其他"]
```

✅ **好的描述**:
```python
suitable_content_types = [
    "包含时间序列的历史发展文档", 
    "有明确演进过程的项目介绍",
    "技术发展历程和里程碑事件"
]
unsuitable_content_types = [
    "静态API文档和技术规范", 
    "无时间维度的功能列表",
    "单纯的安装配置指南"
]
```

### LLM友好的工具信息

工具信息应该帮助LLM理解：
- **何时使用**: 在什么内容和目标下使用
- **何时不用**: 避免误用的条件
- **输出特点**: 工具产生什么类型的结果
- **使用场景**: 典型的应用示例

## 🧪 测试新工具

创建测试文件验证工具功能：

```python
# test_your_new_tool.py
from agents.material_enhancement import MaterialEnhancer, YourNewTool

def test_new_tool():
    # 配置测试环境
    config = {
        "material": {
            "material_enhance": {
                "your_new_tool": True
            }
        }
    }
    
    # 创建增强器
    enhancer = MaterialEnhancer(
        enhancement_config={"your_new_tool": True},
        config_dict=config
    )
    
    # 测试内容和目标
    test_content = "测试内容..."
    test_purpose = "测试目标..."
    
    # 执行测试
    result = enhancer.enhance_material(test_content, test_purpose)
    
    print("测试结果:", result)

if __name__ == "__main__":
    test_new_tool()
```

## 🔄 工具集成到工作流

新工具会自动集成到智能选择系统中：

1. **LLM分析**: 系统会自动将新工具信息提供给LLM分析
2. **智能选择**: 基于内容和目标，LLM会考虑是否选择新工具
3. **自动执行**: 被选中的工具会自动执行并整合结果

新工具添加后，无需修改其他代码即可参与智能选择流程。

## 📚 现有工具参考

### 已实现的工具示例

1. **ScreenRecordingTool** (多模态呈现类)
   - 为GitHub项目或学术论文生成录屏视频
   - 适用于需要视觉展示的内容

2. **TimelineTool** (内容结构化组织类)  
   - 基于内容生成时间轴
   - 适用于有时间序列的历史发展内容

### 未来可扩展的工具类型

#### A_内容结构化组织类
- 表格生成工具：将数据转换为结构化表格
- 公式展示工具：美化数学公式和科学公式
- Mermaid图表工具：生成流程图、架构图等
- 思维导图工具：将内容转换为思维导图

#### B_多模态呈现类
- 动态图表工具：生成交互式数据可视化
- 图片生成工具：AI生成配图和插图
- Emoji插图工具：添加表情符号增强表达
- 代码高亮工具：美化代码展示

#### C_深度洞察类
- 对比分析工具：生成对比表格和分析
- 评估洞察工具：提供深度观点和见解
- 深度提问工具：生成引发思考的问题
- 批判性思考工具：多角度分析内容

#### D_智能交互类
- 案例生成工具：创建相关实例和案例
- 模拟器工具：生成交互式演示
- 问答对话工具：生成Q&A格式内容
- 交互式媒体工具：创建可交互的媒体内容

这种模块化设计让系统具有很强的扩展性，可以根据需要持续添加新的工具类型。

