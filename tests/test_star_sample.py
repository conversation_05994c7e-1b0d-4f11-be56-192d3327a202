import requests
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
from dotenv import load_dotenv
import matplotlib.pyplot as plt
import re
import pytz
from dateutil.relativedelta import relativedelta
import matplotlib as mpl

# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans', 'Bitstream Vera Sans', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 12
plt.style.use('ggplot')

# 加载环境变量
load_dotenv()

# GitHub API 认证
github_token = os.getenv("GITHUB_TOKEN")
headers = {
    "Accept": "application/vnd.github.v3+json",
    "Authorization": f"token {github_token}"
}

def parse_github_url(github_url):
    """
    解析GitHub URL，提取所有者和仓库名
    
    参数:
    github_url (str): GitHub仓库URL
    
    返回:
    tuple: (owner, repo)
    """
    pattern = r"github\.com/([^/]+)/([^/]+)"
    match = re.search(pattern, github_url)
    
    if match:
        owner = match.group(1)
        repo = match.group(2)
        # 移除可能的.git后缀
        repo = repo.replace(".git", "")
        return owner, repo
    else:
        raise ValueError("无效的GitHub URL")

def estimate_stars_history(owner, repo, days=365, max_samples=50):
    """
    通过抽样估算仓库在指定时间范围内的 Star 历史
    
    参数:
    owner (str): 仓库所有者
    repo (str): 仓库名称
    days (int): 要查询的天数，默认为365（一年）
    max_samples (int): 最大采样点数，用于控制API请求数量
    
    返回:
    pd.DataFrame: 估算的 Star 历史
    """
    # 首先获取仓库信息
    url = f"https://api.github.com/repos/{owner}/{repo}"
    response = requests.get(url, headers=headers)
    response.raise_for_status()
    
    repo_data = response.json()
    total_stars = repo_data['stargazers_count']
    # 确保时间为UTC时区的aware datetime
    created_at = pd.to_datetime(repo_data['created_at']).tz_convert('UTC')
    
    print(f"仓库 {owner}/{repo} 有 {total_stars:,} 颗星")
    print(f"创建于 {created_at.strftime('%Y-%m-%d')}")
    
    if total_stars == 0:
        print("该仓库没有星标")
        return None
    
    # 确定时间范围，使用UTC时区
    end_date = datetime.now(pytz.UTC)
    start_date = end_date - timedelta(days=days)
    
    # 如果项目创建时间晚于开始时间，则以创建时间为准
    if created_at > start_date:
        start_date = created_at
        print(f"项目创建时间晚于指定的开始时间，将使用项目创建时间 {start_date.strftime('%Y-%m-%d')} 作为开始时间")
    else:
        print(f"将查询从 {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')} 的Star历史")
    
    # 计算时间跨度
    time_span = (end_date - start_date).days
    
    # 根据时间跨度自动确定采样频率
    # 如果时间跨度大于max_samples天，则减少采样频率
    if time_span > max_samples:
        # 计算适当的采样频率
        freq = max(int(time_span / max_samples), 1)
        if freq >= 7:
            # 如果频率超过7天，改用周粒度
            sample_freq = 'W'
            print(f"由于时间跨度较大 ({time_span} 天)，将使用周粒度采样")
        else:
            # 否则使用天粒度但减少采样点
            sample_freq = f"{freq}D"
            print(f"由于时间跨度较大 ({time_span} 天)，将每 {freq} 天采样一次")
    else:
        # 时间跨度小，使用天粒度
        sample_freq = 'D'
        print("使用天粒度采样")
    
    # 生成采样日期
    date_range = pd.date_range(start=start_date, end=end_date, freq=sample_freq, tz='UTC')
    print(f"将采样 {len(date_range)} 个时间点")
    
    # 获取指定时间范围内的星标数据
    stars_data = []
    headers_with_star = {
        "Accept": "application/vnd.github.v3.star+json",
        "Authorization": f"token {github_token}"
    }
    
    # 使用二分法估算每个时间点的星标数
    # 首先获取总星标数以及直接信息
    all_stars_info = get_repo_star_info(owner, repo, headers_with_star)
    if not all_stars_info:
        print("无法获取仓库星标信息")
        return None
    
    # 使用更高效的方法预估每个采样点的星标数
    for sample_date in date_range:
        try:
            star_count = estimate_stars_at_date(sample_date, all_stars_info)
            stars_data.append({
                'date': sample_date,
                'stars': star_count
            })
            print(f"{sample_date.strftime('%Y-%m-%d')}: 估计有 {star_count} 颗星")
        except Exception as e:
            print(f"处理日期 {sample_date.strftime('%Y-%m-%d')} 时出错: {str(e)}")
    
    if not stars_data:
        print("未能获取到有效数据")
        return None
    
    # 将数据转换为 DataFrame
    stars_df = pd.DataFrame(stars_data)
    stars_df = stars_df.sort_values('date')
    
    return stars_df

def get_repo_star_info(owner, repo, headers):
    """
    获取仓库的星标信息，包括总页数和关键采样点
    
    返回:
    dict: 包含星标信息的字典
    """
    try:
        # 获取仓库总星标数
        repo_url = f"https://api.github.com/repos/{owner}/{repo}"
        response = requests.get(repo_url, headers=headers)
        response.raise_for_status()
        repo_data = response.json()
        total_stars = repo_data['stargazers_count']
        
        # 获取分页信息
        url = f"https://api.github.com/repos/{owner}/{repo}/stargazers?per_page=100"
        response = requests.get(url, headers=headers)
        
        if response.status_code == 403 and 'API rate limit exceeded' in response.text:
            reset_time = int(response.headers.get('X-RateLimit-Reset', 0))
            current_time = int(time.time())
            sleep_time = max(reset_time - current_time + 1, 0)
            print(f"已达到 API 速率限制，等待 {sleep_time} 秒后继续...")
            time.sleep(sleep_time)
            response = requests.get(url, headers=headers)
        
        response.raise_for_status()
        
        # 获取总页数
        last_page = 1
        if 'Link' in response.headers:
            links = response.headers['Link']
            last_match = re.search(r'page=(\d+)>; rel="last"', links)
            if last_match:
                last_page = int(last_match.group(1))
        
        # 采样关键页面以获取时间分布
        key_pages = []
        if last_page > 1:
            # 获取第一页数据
            first_page_data = response.json()
            if first_page_data:
                first_star = pd.to_datetime(first_page_data[0]['starred_at']).tz_convert('UTC')
                key_pages.append({
                    'page': 1,
                    'date': first_star,
                    'stars': 1
                })
            
            # 获取最后一页数据
            last_url = f"https://api.github.com/repos/{owner}/{repo}/stargazers?per_page=100&page={last_page}"
            last_response = requests.get(last_url, headers=headers)
            
            if last_response.status_code == 200:
                last_page_data = last_response.json()
                if last_page_data:
                    last_star = pd.to_datetime(last_page_data[-1]['starred_at']).tz_convert('UTC')
                    key_pages.append({
                        'page': last_page,
                        'date': last_star,
                        'stars': total_stars
                    })
            
            # 如果页数多，采样中间页面
            if last_page > 5:
                middle_pages = [int(last_page * p / 4) for p in range(1, 4)]
                for page in middle_pages:
                    if page > 1 and page < last_page:
                        mid_url = f"https://api.github.com/repos/{owner}/{repo}/stargazers?per_page=100&page={page}"
                        mid_response = requests.get(mid_url, headers=headers)
                        
                        if mid_response.status_code == 200:
                            mid_page_data = mid_response.json()
                            if mid_page_data:
                                mid_star = pd.to_datetime(mid_page_data[-1]['starred_at']).tz_convert('UTC')
                                key_pages.append({
                                    'page': page,
                                    'date': mid_star,
                                    'stars': page * 100
                                })
                        
                        # 避免触发API限制
                        time.sleep(1)
        
        # 确保关键页面按星标数排序
        key_pages.sort(key=lambda x: x['stars'])
        
        return {
            'total_stars': total_stars,
            'last_page': last_page,
            'key_points': key_pages
        }
    
    except Exception as e:
        print(f"获取仓库星标信息失败: {str(e)}")
        return None

def estimate_stars_at_date(target_date, star_info):
    """
    基于关键采样点估算特定日期的星标数
    
    参数:
    target_date: 目标日期
    star_info: 仓库星标信息
    
    返回:
    int: 估计的星标数
    """
    key_points = star_info['key_points']
    
    # 如果目标日期在所有关键点之后，返回总星标数
    if len(key_points) > 0 and target_date >= key_points[-1]['date']:
        return star_info['total_stars']
    
    # 如果目标日期在所有关键点之前，返回0
    if len(key_points) > 0 and target_date < key_points[0]['date']:
        return 0
    
    # 在关键点之间进行线性插值
    for i in range(len(key_points) - 1):
        point1 = key_points[i]
        point2 = key_points[i + 1]
        
        if point1['date'] <= target_date <= point2['date']:
            # 计算日期差
            date_diff = (point2['date'] - point1['date']).total_seconds()
            target_diff = (target_date - point1['date']).total_seconds()
            
            # 计算插值比例
            ratio = target_diff / date_diff if date_diff > 0 else 0
            
            # 线性插值计算星标数
            estimated_stars = int(point1['stars'] + ratio * (point2['stars'] - point1['stars']))
            return estimated_stars
    
    # 如果没有找到合适的区间，返回最接近的关键点的星标数
    closest_point = min(key_points, key=lambda x: abs((target_date - x['date']).total_seconds()))
    return closest_point['stars']

def create_beautiful_chart(owner, repo, df, freq_type, output_path):
    """
    创建美观的星标历史图表
    
    参数:
    owner (str): 仓库所有者
    repo (str): 仓库名称
    df (DataFrame): 包含日期和星标数的数据
    freq_type (str): 频率类型（天或周）
    output_path (str): 输出文件路径
    """
    # 创建漂亮的图表
    plt.figure(figsize=(12, 6), dpi=100)
    
    # 设置背景色和网格样式
    ax = plt.gca()
    ax.set_facecolor('#f5f5f5')
    
    # 绘制主曲线
    plt.plot(df['date'], df['stars'], 
             marker='', 
             linewidth=2.5, 
             color='#2196F3',
             alpha=0.9)
    
    # 添加均值辅助线
    avg_stars = df['stars'].mean()
    plt.axhline(y=avg_stars, color='#FF5722', linestyle='--', alpha=0.5, 
                label=f'平均: {int(avg_stars):,} 星')
    
    # 添加起点和终点标记
    plt.scatter(df['date'].iloc[0], df['stars'].iloc[0], color='#4CAF50', s=100, zorder=5)
    plt.scatter(df['date'].iloc[-1], df['stars'].iloc[-1], color='#F44336', s=100, zorder=5)
    
    # 设置网格
    plt.grid(True, linestyle='--', alpha=0.7, color='#cccccc')
    
    # 设置标题和标签
    # 避免乱码，使用简单的ASCII字符
    repo_full_name = f"{owner}/{repo}"
    freq_suffix = "Weekly" if freq_type == "周" else "Daily"
    plt.title(f"GitHub Stars History: {repo_full_name} ({freq_suffix})", 
              fontsize=18, fontweight='bold', pad=20)
    
    plt.xlabel('Date', fontsize=14, labelpad=10)
    plt.ylabel('Stars Count', fontsize=14, labelpad=10)
    
    # 格式化 y 轴，使用千分位分隔符
    plt.gca().get_yaxis().set_major_formatter(
        plt.FuncFormatter(lambda x, loc: f"{int(x):,}")
    )
    
    # 优化x轴日期标签
    date_range = (df['date'].max() - df['date'].min()).days
    if date_range > 365 * 2:
        # 大于2年显示年份
        plt.gca().xaxis.set_major_locator(mpl.dates.YearLocator())
        plt.gca().xaxis.set_major_formatter(mpl.dates.DateFormatter('%Y'))
    elif date_range > 180:
        # 大于6个月显示月份
        plt.gca().xaxis.set_major_locator(mpl.dates.MonthLocator(interval=2))
        plt.gca().xaxis.set_major_formatter(mpl.dates.DateFormatter('%Y-%m'))
    else:
        # 小于6个月显示具体日期
        plt.gca().xaxis.set_major_locator(mpl.dates.MonthLocator())
        plt.gca().xaxis.set_major_formatter(mpl.dates.DateFormatter('%Y-%m-%d'))
    
    plt.xticks(rotation=45)
    
    # 添加数据点标注（只标注部分关键点）
    num_points = len(df)
    if num_points > 20:
        # 如果点太多，只标注一部分
        step = max(1, num_points // 10)
        for i in range(0, num_points, step):
            date = df['date'].iloc[i]
            stars = df['stars'].iloc[i]
            if i > 0 and i < num_points - 1:  # 跳过第一个和最后一个点
                plt.annotate(f"{stars:,}",
                            xy=(date, stars),
                            xytext=(0, 10),
                            textcoords='offset points',
                            ha='center', va='bottom',
                            fontsize=9, alpha=0.8)
    else:
        # 点较少，全部标注
        for i in range(num_points):
            date = df['date'].iloc[i]
            stars = df['stars'].iloc[i]
            plt.annotate(f"{stars:,}",
                        xy=(date, stars),
                        xytext=(0, 10),
                        textcoords='offset points',
                        ha='center', va='bottom',
                        fontsize=9)
    
    # 添加最终星标数注释
    final_date = df['date'].iloc[-1]
    final_stars = df['stars'].iloc[-1]
    plt.annotate(f"Latest: {final_stars:,} stars",
                xy=(final_date, final_stars),
                xytext=(10, 0),
                textcoords='offset points',
                ha='left', va='center',
                fontsize=11, fontweight='bold',
                bbox=dict(boxstyle="round,pad=0.3", fc="#f0f0f0", ec="gray", alpha=0.8))
    
    # 添加图例
    plt.legend(loc='upper left', frameon=True, framealpha=0.9)
    
    # 添加数据来源注释
    plt.figtext(0.99, 0.01, f"Generated at {datetime.now().strftime('%Y-%m-%d')}",
               ha='right', va='bottom', fontsize=8, alpha=0.7)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"美化图表已保存到 {output_path}")

# 主程序
if __name__ == "__main__":
    # 获取用户输入
    github_url = input("请输入 GitHub 仓库链接: ").strip()
    
    try:
        # 解析GitHub URL
        owner, repo = parse_github_url(github_url)
        
        # 估算 Star 历史
        stars_history = estimate_stars_history(owner, repo)
        
        if stars_history is not None:
            # 保存数据
            output_dir = 'output'  # 改为 'output' 而不是 'outputs'
            os.makedirs(output_dir, exist_ok=True)
            
            # 在保存前将时区信息移除，以便CSV格式更清晰
            stars_history_local = stars_history.copy()
            stars_history_local['date'] = stars_history_local['date'].dt.tz_localize(None)
            
            # 使用repo名作为文件名基础，不包含所有者
            file_base = f"{repo}_stars"
            
            # 保存CSV数据
            csv_path = f'{output_dir}/{file_base}.csv'
            stars_history_local.to_csv(csv_path, index=False)
            print(f"估算数据已保存到 {csv_path}")
            
            # 确定频率类型
            freq_type = "周" if ('W' in stars_history.index.freq.name if hasattr(stars_history.index, 'freq') else False) else "天"
            
            # 创建美观的图表
            png_path = f'{output_dir}/{file_base}.png'
            create_beautiful_chart(owner, repo, stars_history_local, freq_type, png_path)
            
    except Exception as e:
        print(f"发生错误: {str(e)}")
