"""Tests for the dsl_to_manim module."""

import os
import tempfile
from pathlib import Path

from dsl.ast_builder import ASTBuilder
from dsl.manim_code import ManimCodeGenerator
from dsl.parser import <PERSON><PERSON><PERSON>LPars<PERSON>


def test_dsl_parsing():
    """Test that we can parse a DSL file without errors."""
    dsl_file_path = Path("tests/test_dsl.txt")
    dsl = ManimDSLParser.parse_from_file(dsl_file_path)

    # Basic checks on the parsed DSL
    assert dsl is not None
    assert hasattr(dsl, "metadata")
    assert hasattr(dsl, "objects")
    assert hasattr(dsl, "actions")

    # Check metadata properties
    assert dsl.metadata.title == "SimpleTestScene"

    # Check objects
    assert len(dsl.objects) == 2
    assert dsl.objects[0].id == "circle1"
    assert dsl.objects[1].id == "text1"

    # Check actions
    assert len(dsl.actions) == 8


def test_ast_building():
    """Test that we can build an AST from the parsed DSL."""
    dsl_file_path = Path("tests/test_dsl.txt")
    dsl = ManimDSLParser.parse_from_file(dsl_file_path)

    # Build AST
    ast = ASTBuilder.build_ast(dsl)

    # Basic checks on the AST
    assert ast is not None
    assert ast.metadata.title == "SimpleTestScene"
    assert len(ast.object_definitions) == 2
    assert len(ast.action_sequence) == 8


def test_code_generation():
    """Test that we can generate Manim code from the AST."""
    dsl_file_path = Path("tests/test_dsl.txt")
    dsl = ManimDSLParser.parse_from_file(dsl_file_path)

    # Build AST
    ast = ASTBuilder.build_ast(dsl)

    # Generate code
    generator = ManimCodeGenerator()
    code = generator.visit_scene(ast)

    # Basic checks on the generated code
    assert code is not None
    assert "from manim import *" in code
    assert "class SimpleTestScene(Scene):" in code
    assert "mobj_circle1" in code
    assert "mobj_text1" in code

    # Check for specific animations
    assert "FadeIn" in code
    assert "Write" in code
    assert "animate.move_to" in code
    assert "animate.set_color" in code


def test_end_to_end():
    """Test the entire pipeline from DSL to Manim code file."""
    dsl_file_path = Path("tests/test_dsl.txt")

    # Create a temporary directory for output
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        output_file_path = temp_path / "output.py"

        # Parse DSL file
        dsl = ManimDSLParser.parse_from_file(dsl_file_path)

        # Build AST
        ast = ASTBuilder.build_ast(dsl)

        # Generate code
        generator = ManimCodeGenerator()
        code = generator.visit_scene(ast)

        # Write to file
        with open(output_file_path, "w") as f:
            f.write(code)

        # Check that file exists and has content
        assert os.path.exists(output_file_path)
        assert os.path.getsize(output_file_path) > 0

        # Read the file and do some basic checks
        with open(output_file_path) as f:
            content = f.read()
            assert "class SimpleTestScene(Scene):" in content
            assert "def construct(self):" in content
