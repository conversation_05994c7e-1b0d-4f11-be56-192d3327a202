from manim import *
from manim_voiceover import VoiceoverScene

from manim_funcs.anim_funcs import FeynmanScene, LAYOUTS

# 创建一个用于测试代码片段渲染的场景类
class TestCodeSnippetScene(FeynmanScene):
    def construct(self):
        # 定义两种代码：标准代码和长代码（测试换行）
        standard_code = """def hello_world():
    print("Hello, World!")
    return True

# 调用函数
result = hello_world()
print(f"函数返回值: {result}")"""

        long_code = """def fibonacci(n):
    '''计算斐波那契数列的第n项'''
    if n <= 0:
        return 0
    elif n == 1:
        return 1
    else:
        a, b = 0, 1
        for _ in range(2, n + 1):
            a, b = b, a + b
        return b

def factorial(n):
    '''计算n的阶乘'''
    if n <= 1:
        return 1
    else:
        result = 1
        for i in range(2, n + 1):
            result *= i
        return result

def is_prime(n):
    '''判断一个数是否为质数'''
    if n <= 1:
        return False
    if n <= 3:
        return True
    if n % 2 == 0 or n % 3 == 0:
        return False
    i = 5
    while i * i <= n:
        if n % i == 0 or n % (i + 2) == 0:
            return False
        i += 6
    return True

# 测试函数
print(f"斐波那契数列第10项: {fibonacci(10)}")
print(f"5的阶乘: {factorial(5)}")
print(f"17是否为质数: {is_prime(17)}")"""

        # 定义8个不同布局区域的测试
        layout_areas = [
            "full_screen",
            "upper_half",
            "right_upper_quarter",
            "right_half",
            "left_half",
            "origin_x14y6", 
            "upper_one_eighth",
            "lower_one_eighth"
        ]

        # 为每个布局区域渲染代码片段
        for i, layout_area in enumerate(layout_areas):
            # 清除之前的所有对象
            self.clear()
            
            # 显示布局区域名称
            area_title = Text(f"布局区域: {layout_area}", font_size=36, color=YELLOW)
            area_title.to_edge(UP)
            self.add(area_title)
            
            # 可视化布局区域的边界
            layout_rect = LAYOUTS[layout_area].copy()
            layout_rect.set_stroke(color=RED, width=4)
            layout_rect.set_fill(color=BLUE, opacity=0.1)
            self.add(layout_rect)
            
            # 根据布局区域选择合适的代码
            # 对于较小的区域使用标准代码，对于较大的区域使用长代码
            code = standard_code
            if layout_area in ["full_screen", "right_half", "left_half", "origin_x14y6"]:
                code = long_code
            
            # 设置高亮行
            highlight_lines = [3] if code == standard_code else [2, 11, 20]
            
            # 渲染代码片段
            code_animations = self.render_code_snippet(
                code=code,
                language="python",
                highlight_lines=highlight_lines,
                title=f"测试代码 - {layout_area}",
                layout_area=layout_area
            )
            
            # 播放动画
            for anim in code_animations:
                self.play(anim["obj"])
            
            # 暂停一段时间以便观察
            self.wait(2)
            
            # 添加一个表示测试编号的标签
            test_num = Text(f"测试 {i+1}/{len(layout_areas)}", font_size=20)
            test_num.to_corner(DR)
            self.add(test_num)
            
            self.wait(1)  # 等待一秒，然后进入下一个测试

if __name__ == "__main__":
    # 运行场景
    scene = TestCodeSnippetScene()
    scene.render() 