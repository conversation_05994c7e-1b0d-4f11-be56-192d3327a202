#!/usr/bin/env python
# -*- coding: utf-8 -*-

from manim import *
import numpy as np
import random

class CodeShowcase(Scene):
    def construct(self):
        # 设置高级渐变背景
        background = Rectangle(
            width=config.frame_width,
            height=config.frame_height,
            fill_opacity=1,
            stroke_width=0
        )
        background.set_color_by_gradient([
            "#0A0A2A",  # 深蓝色底色
            "#141450",  # 稍微亮一点的蓝色
            "#181860"   # 靠上部的颜色
        ])
        
        # 添加纹理元素
        texture_dots = VGroup()
        for _ in range(100):  # 增加点的数量
            dot = Dot(
                radius=random.uniform(0.01, 0.03), 
                color=WHITE,
                fill_opacity=random.uniform(0.1, 0.4)
            )
            dot.move_to([
                random.uniform(-config.frame_width/2, config.frame_width/2),
                random.uniform(-config.frame_height/2, config.frame_height/2),
                0
            ])
            texture_dots.add(dot)
        
        # 添加装饰线
        decoration_top = Line(
            start=[-config.frame_width/2, config.frame_height/2 - 1.5, 0],
            end=[config.frame_width/2, config.frame_height/2 - 1.5, 0],
            color=BLUE_B,
            stroke_width=1.5,
            stroke_opacity=0.4
        )
        
        decoration_bottom = Line(
            start=[-config.frame_width/2, -config.frame_height/2 + 1, 0],
            end=[config.frame_width/2, -config.frame_height/2 + 1, 0],
            color=BLUE_B,
            stroke_width=1.5,
            stroke_opacity=0.4
        )
        
        # 标题区域背景
        title_bg = Rectangle(
            width=config.frame_width,
            height=1.5,
            fill_opacity=0.5,
            fill_color="#1A237E",
            stroke_width=0
        ).to_edge(UP, buff=0)
        
        # 标题文本
        title = Text("多LLM代理示例", font_size=72, color=WHITE)
        title.to_edge(UP, buff=0.4)
        
        # 创建代码展示区域
        code_box = RoundedRectangle(
            width=config.frame_width - 1.5,  # 恢复为之前较大的宽度
            height=config.frame_height - 3.5,  # 恢复为之前较大的高度
            corner_radius=0.5,
            fill_color="#1A1A40",
            fill_opacity=0.8,
            stroke_color=BLUE_A,
            stroke_width=2
        )
        code_box.move_to([0, 0, 0])  # 居中放置
        
        # 创建代码文本
        # 第一部分代码
        code_part1 = """import os

from smolagents import CodeAgent, LiteLLMRouterModel, WebSearchTool


# Make sure to setup the necessary environment variables!"""
        
        # 第二部分代码
        code_part2 = """llm_loadbalancer_model_list = [
    {
        "model_name": "model-group-1",
        "litellm_params": {
            "model": "gpt-4o-mini",
            "api_key": os.getenv("OPENAI_API_KEY"),
        },
    },
    {
        "model_name": "model-group-1",
        "litellm_params": {
            "model": "bedrock/anthropic.claude-3-sonnet-********-v1:0",
            "aws_access_key_id": os.getenv("AWS_ACCESS_KEY_ID"),
            "aws_secret_access_key": os.getenv("AWS_SECRET_ACCESS_KEY"),
            "aws_region_name": os.getenv("AWS_REGION"),
        },
    },
]"""
        
        # 第三部分代码
        code_part3 = """model = LiteLLMRouterModel(
    model_id="model-group-1",
    model_list=llm_loadbalancer_model_list,
    client_kwargs={"routing_strategy": "simple-shuffle"},
)
agent = CodeAgent(tools=[WebSearchTool()], model=model, stream_outputs=True)

query = "一只豹以全速穿过艺术桥需要多少秒？"
agent.run(query)"""
        
        # 创建代码文本对象
        code_text1 = Code(code_string=code_part1, language="python")
        code_text1.font_size = 18  # 减小字体
        code_text1.line_spacing = 0.7  # 减小行间距
        
        code_text2 = Code(code_string=code_part2, language="python")
        code_text2.font_size = 18  # 减小字体
        code_text2.line_spacing = 0.7  # 减小行间距
        
        code_text3 = Code(code_string=code_part3, language="python")
        code_text3.font_size = 18  # 减小字体
        code_text3.line_spacing = 0.7  # 减小行间距
        
        # 设置代码位置
        code_text1.scale(0.85).move_to(code_box.get_center())  # 减小缩放比例
        code_text2.scale(0.85).move_to(code_box.get_center())  # 减小缩放比例
        code_text3.scale(0.85).move_to(code_box.get_center())  # 减小缩放比例
        
        # 关键词标签 - 定位到代码对应位置
        keywords1 = VGroup()
        kw1_1 = Text("导入模块", font_size=24, color="#FF7F50")  # 珊瑚色
        kw1_2 = Text("初始化列表", font_size=24, color="#FF7F50")
        kw1_3 = Text("工具准备", font_size=24, color="#FF7F50")
        keywords1.add(kw1_1, kw1_2, kw1_3)
        
        # 使用绝对位置来定位关键词
        kw1_1.move_to(code_text1.get_center() + UP * 1.2 + LEFT * 2)
        kw1_2.move_to(code_text1.get_center() + DOWN * 0.5 + RIGHT * 2)
        kw1_3.move_to(code_text1.get_center() + UP * 0.2 + RIGHT * 2.5)
        
        # 添加描述词1 - 移到代码上方
        desc1 = Text("模块导入与初始化", font_size=20, color=LIGHT_GRAY)
        desc1.next_to(code_box, UP, buff=0.5)
        
        keywords2 = VGroup()
        kw2_1 = Text("OpenAI模型", font_size=24, color="#7FFF00")  # 查特酸绿
        kw2_2 = Text("Claude模型", font_size=24, color="#7FFF00")
        kw2_3 = Text("负载均衡", font_size=24, color="#7FFF00")
        kw2_4 = Text("API配置", font_size=24, color="#7FFF00")
        keywords2.add(kw2_1, kw2_2, kw2_3, kw2_4)
        
        # 使用绝对位置来定位关键词
        kw2_1.move_to(code_text2.get_center() + UP * 0.7 + RIGHT * 2.5)
        kw2_2.move_to(code_text2.get_center() + DOWN * 0.7 + RIGHT * 2.5)
        kw2_3.move_to(code_text2.get_center() + UP * 1.5 + LEFT * 2.5)
        kw2_4.move_to(code_text2.get_center() + UP * 0.2 + LEFT * 2.5)
        
        # 添加描述词2 - 移到代码上方
        desc2 = Text("多模型配置与环境变量", font_size=20, color=LIGHT_GRAY)
        desc2.next_to(code_box, UP, buff=0.5)
        
        keywords3 = VGroup()
        kw3_1 = Text("Router初始化", font_size=24, color="#00BFFF")  # 深天蓝
        kw3_2 = Text("Agent创建", font_size=24, color="#00BFFF")
        kw3_3 = Text("执行查询", font_size=24, color="#00BFFF")
        kw3_4 = Text("WebSearch工具", font_size=24, color="#00BFFF")
        keywords3.add(kw3_1, kw3_2, kw3_3, kw3_4)
        
        # 使用绝对位置来定位关键词
        kw3_1.move_to(code_text3.get_center() + UP * 1.2 + LEFT * 2.5)
        kw3_2.move_to(code_text3.get_center() + DOWN * 0.2 + LEFT * 2.5)
        kw3_3.move_to(code_text3.get_center() + DOWN * 1.0 + RIGHT * 2.5)
        kw3_4.move_to(code_text3.get_center() + DOWN * 0.2 + RIGHT * 2.5)
        
        # 添加描述词3 - 移到代码上方
        desc3 = Text("Router与Agent运行", font_size=20, color=LIGHT_GRAY)
        desc3.next_to(code_box, UP, buff=0.5)
        
        # 高亮矩形 - 恢复为之前的蓝色
        highlight1 = SurroundingRectangle(code_text1, color="#4169E1", buff=0.2, stroke_width=2.5)  # 皇家蓝
        highlight2 = SurroundingRectangle(code_text2, color="#4169E1", buff=0.2, stroke_width=2.5)
        highlight3 = SurroundingRectangle(code_text3, color="#4169E1", buff=0.2, stroke_width=2.5)

        # 动画展示
        # 先添加背景元素
        self.add(background)
        self.play(
            FadeIn(texture_dots, scale=1.2),
            Create(decoration_top),
            Create(decoration_bottom),
            FadeIn(title_bg),
            run_time=0.8
        )
        
        # 添加标题
        self.play(Write(title), run_time=0.8)
        
        # 添加代码框
        self.play(Create(code_box), run_time=0.8)
        
        # 第一部分代码展示
        self.play(FadeIn(code_text1), run_time=0.8)
        self.play(Write(desc1), run_time=0.6)  # 先显示描述
        self.play(Create(highlight1), run_time=0.5)
        self.play(FadeIn(keywords1), run_time=0.6)
        self.wait(1.2)  # 减少等待时间
        
        # 淡出第一部分，进入第二部分
        self.play(
            FadeOut(code_text1),
            FadeOut(highlight1),
            FadeOut(keywords1),
            FadeOut(desc1),
            run_time=0.8
        )
        
        # 第二部分代码展示
        self.play(FadeIn(code_text2), run_time=0.8)
        self.play(Write(desc2), run_time=0.6)  # 先显示描述
        self.play(Create(highlight2), run_time=0.5)
        self.play(FadeIn(keywords2), run_time=0.6)
        self.wait(1.5)  # 减少等待时间
        
        # 淡出第二部分，进入第三部分
        self.play(
            FadeOut(code_text2),
            FadeOut(highlight2),
            FadeOut(keywords2),
            FadeOut(desc2),
            run_time=0.8
        )
        
        # 第三部分代码展示
        self.play(FadeIn(code_text3), run_time=0.8)
        self.play(Write(desc3), run_time=0.6)  # 先显示描述
        self.play(Create(highlight3), run_time=0.5)
        self.play(FadeIn(keywords3), run_time=0.6)
        self.wait(1.5)  # 减少等待时间
        
        # 淡出所有内容，准备结束
        self.play(
            FadeOut(code_text3),
            FadeOut(highlight3),
            FadeOut(keywords3),
            FadeOut(desc3),
            run_time=0.8
        )
        
        # 显示简短总结关键词
        final_keywords = VGroup()
        kw_final1 = Text("多LLM协同", font_size=40, color="#FF7F50")  # 恢复为珊瑚色
        kw_final2 = Text("负载均衡策略", font_size=40, color="#7FFF00")  # 查特酸绿
        kw_final3 = Text("WebSearchTool辅助", font_size=40, color="#00BFFF")  # 深天蓝
        kw_final4 = Text("高效Agent实现", font_size=40, color="#BA55D3")  # 紫色
        
        final_keywords.arrange(DOWN, buff=0.5)
        final_keywords.move_to(code_box.get_center())
        
        self.play(FadeIn(final_keywords, scale=1.1), run_time=1)
        
        # 结束
        self.play(
            FadeOut(final_keywords),
            FadeOut(code_box),
            FadeOut(title),
            FadeOut(title_bg),
            FadeOut(decoration_top),
            FadeOut(decoration_bottom),
            FadeOut(texture_dots),
            FadeOut(background),
            run_time=1.2
        )


if __name__ == "__main__":
    # 命令行参数示例: python -m manim code_showcase.py CodeShowcase -pql
    pass 