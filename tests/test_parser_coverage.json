{"metadata": {"title": "ParserCoverageTest", "author": "Coverage Test", "resolution": "1440p", "estimated_duration": 30.5, "background_color": "BLUE"}, "objects": [{"id": "circle1", "type": "circle", "properties": {"radius": 1.0, "color": "RED", "fill_opacity": 0.5, "stroke_width": 2, "stroke_color": "WHITE", "z_index": 1}}, {"id": "rect1", "type": "rectangle", "properties": {"width": 3.0, "height": 2.0, "color": "GREEN", "fill_opacity": 0.3, "z_index": 0}}, {"id": "line1", "type": "line", "properties": {"start": [-2, 0], "end": [2, 0], "color": "YELLOW", "stroke_width": 5}}, {"id": "dot1", "type": "dot", "properties": {"color": "PURPLE", "radius": 0.2, "fill_opacity": 1.0}}, {"id": "star1", "type": "star", "properties": {"color": "GOLD", "fill_opacity": 0.8, "n_points": 7}}, {"id": "text1", "type": "text", "properties": {"content": "Hello World", "font_size": 36, "color": "WHITE", "alignment": "center"}}, {"id": "tex1", "type": "tex", "properties": {"content": "plain tex", "font_size": 42, "color": "WHITE"}}, {"id": "math1", "type": "math_tex", "properties": {"content": "\\sum_{i=1}^{n} i = \\frac{n(n+1)}{2}", "font_size": 48, "color": "PINK"}}, {"id": "image1", "type": "image", "properties": {"file_path": "assets/example.png", "height": 3.0}}, {"id": "graph1", "type": "graph", "properties": {"x_range": [-5, 5, 1], "y_range": [-3, 3, 0.5], "x_length": 5, "y_length": 3, "x_label": "x", "y_label": "y", "tips": true, "color": "WHITE"}}, {"id": "coord1", "type": "coordinate_system", "properties": {"x_range": [-10, 10, 1], "y_range": [-5, 5, 1], "x_length": 6, "y_length": 4, "x_label": "\\omega", "y_label": "f(\\omega)", "include_numbers": true, "tips": true, "color": "WHITE", "axis_config": {"color": "GRAY"}}}, {"id": "group1", "type": "group", "properties": {"members": ["circle1", "rect1"], "layout": {"type": "horizontal", "buffer": 0.5, "alignment": "CENTER"}}}, {"id": "group2", "type": "group", "properties": {"members": ["dot1", "star1"], "layout": {"type": "vertical", "buffer": 0.5, "alignment": "LEFT"}}}], "actions": [{"type": "create", "target": "circle1", "animation": "fade_in", "position": [-4, 2, 0], "duration": 0.8}, {"type": "create", "target": "rect1", "animation": "grow", "position": [4, 2, 0], "duration": 1.0}, {"type": "create", "target": "line1", "animation": "draw", "position": [0, 1, 0], "duration": 0.8}, {"type": "create", "target": "dot1", "animation": "fade_in", "position": [-2, -2, 0], "duration": 0.5}, {"type": "create", "target": "star1", "animation": "grow", "position": [2, -2, 0], "duration": 0.5}, {"type": "create", "target": "group1", "animation": "draw", "duration": 1.2}, {"type": "wait", "duration": 0.5}, {"type": "move_to", "target": "circle1", "position": [-2, 0, 0], "path": "arc", "duration": 1.0}, {"type": "transform", "target": "rect1", "properties": {"color": "BLUE", "scale": 0.8, "rotation": 30, "fill_opacity": 0.7}, "easing": "elastic", "duration": 1.5}, {"type": "arrange", "target": "circle1", "relative_to": "rect1", "direction": "LEFT", "buffer": 0.3, "align_edge": "UP", "duration": 0.8}, {"type": "create", "target": "group2", "animation": "none", "duration": 0.0}, {"type": "align", "targets": ["dot1", "star1"], "reference_target": "SCREEN_BOTTOM", "edge": "BOTTOM", "duration": 1.0}, {"type": "distribute", "targets": ["circle1", "dot1", "star1"], "direction": "HORIZONTAL", "spacing": 2.0, "reference_frame": {"start_edge_of": "SCREEN_LEFT", "end_edge_of": "SCREEN_RIGHT", "mode": "edges"}, "duration": 1.2}, {"type": "wait", "duration": 0.5}, {"type": "highlight", "target": "star1", "color": "WHITE", "duration": 0.7}, {"type": "focus", "target": "circle1", "scale_factor": 1.3, "opacity_factor": 0.4, "duration": 1.0}, {"type": "indicate", "target": "rect1", "scale_factor": 1.2, "color": "YELLOW", "duration": 0.8}, {"type": "create", "target": "text1", "animation": "write", "position": [0, 3, 0], "duration": 1.0}, {"type": "camera", "action": "zoom", "scale": 1.5, "duration": 1.0}, {"type": "wait", "duration": 0.3}, {"type": "camera", "action": "pan", "position": [2, 1, 0], "duration": 1.0}, {"type": "wait", "duration": 0.3}, {"type": "camera", "action": "reset", "duration": 1.0}, {"type": "create", "target": "tex1", "animation": "write", "position": [0, 1, 0], "duration": 1.0}, {"type": "wait", "duration": 0.5}, {"type": "replace", "source": "circle1", "target": "tex1", "animation": "transform", "position_match": true, "duration": 1.5}, {"type": "create", "target": "math1", "animation": "write", "position": [0, -3, 0], "duration": 1.0}, {"type": "wait", "duration": 0.5}, {"type": "animate_group", "actions": [{"type": "move_to", "target": "tex1", "position": [0, 1, 0], "duration": 1.0}, {"type": "transform", "target": "rect1", "properties": {"color": "ORANGE", "scale": 1.2}, "duration": 1.0}, {"type": "move_to", "target": "star1", "position": [4, -2, 0], "duration": 1.0}], "duration": 1.2}, {"type": "create", "target": "graph1", "animation": "draw", "position": [-4, 0, 0], "duration": 1.0}, {"type": "plot_function", "target": "graph1", "function": "np.sin(x)", "color": "RED", "x_range": [-5, 5], "animation": "draw", "duration": 1.5}, {"type": "add_point", "target": "graph1", "position": [3, 0], "color": "GREEN", "label": "x_0", "animation": "grow", "duration": 0.8}, {"type": "create", "target": "coord1", "animation": "draw", "position": [4, 0, 0], "duration": 1.0}, {"type": "plot_function", "target": "coord1", "function": "x**2", "color": "BLUE", "animation": "draw", "duration": 1.5}, {"type": "add_point", "target": "coord1", "position": [2, 4], "color": "YELLOW", "label": "P", "animation": "fade_in", "duration": 0.8}, {"type": "remove", "target": "dot1", "animation": "shrink", "duration": 0.5}, {"type": "remove", "target": "tex1", "animation": "unwrite", "duration": 0.8}, {"type": "remove", "target": "star1", "animation": "none", "duration": 0.0}, {"type": "wait", "duration": 1.0}]}