#!/usr/bin/env python
"""Run coverage tests on the manim_code module."""

import os
import sys
import tempfile
from pathlib import Path

import coverage

# Add project root to path to ensure imports work
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import necessary modules
from dsl.ast_builder import ASTBuilder
from dsl.manim_code import ManimCodeGenerator
from dsl.parser import ManimDSLPars<PERSON>


def run_single_test(test_file):
    """Process a single DSL test file and generate Manim code."""
    print(f"\nProcessing test file: {test_file}")

    try:
        # Parse DSL file
        dsl = ManimDSLParser.parse_from_file(test_file)

        # Build AST
        ast = ASTBuilder.build_ast(dsl)

        # Generate code
        generator = ManimCodeGenerator()
        code = generator.visit_scene(ast)

        # Write to a temporary file
        with tempfile.NamedTemporaryFile(suffix=".py", delete=False, mode="w") as f:
            f.write(code)
            output_file = f.name

        print(f"  Generated Manim code: {output_file}")

        # Basic validation
        success = "from manim import *" in code and f"class {ast.metadata.title}(Scene):" in code

        # Clean up
        try:
            os.remove(output_file)
        except Exception:
            pass

        return success

    except Exception as e:
        print(f"  Error processing file: {e}")
        return False


def run_coverage_tests():
    """Run coverage tests on all test DSL files."""
    test_dir = Path(__file__).parent

    # Initialize coverage
    cov = coverage.Coverage(source=["dsl"])
    cov.start()

    # Find all test files
    test_files = list(test_dir.glob("test_*.json")) + list(test_dir.glob("test_*.txt"))

    print(f"Found {len(test_files)} test files.")

    # Process each test file
    results = []
    for test_file in test_files:
        success = run_single_test(test_file)
        results.append((test_file.name, success))

    # Stop coverage collection
    cov.stop()

    # Print summary
    print("\n--- Test Results ---")
    failures = 0
    for file_name, success in results:
        status = "PASS" if success else "FAIL"
        if not success:
            failures += 1
        print(f"{file_name}: {status}")

    print(f"\nPassed: {len(results) - failures}/{len(results)} tests")

    # Generate coverage report
    print("\n--- Coverage Report for DSL Test Files ---")
    cov.report()
    cov.html_report(directory="coverage_dsl_files")
    print("\nCoverage report generated in 'coverage_dsl_files' directory")

    return failures


if __name__ == "__main__":
    # Run tests
    failures = run_coverage_tests()
