#!/bin/bash

# 确保output目录存在
mkdir -p ./output

# 获取当前时间作为文件名
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
OUTPUT_FILE="./output/terminal_demo_${TIMESTAMP}.cast"
HTML_OUTPUT_FILE="${OUTPUT_FILE%.cast}.html"

# 默认终端尺寸设置
COLS=80
ROWS=24
# 默认空闲时间限制（秒），设为0表示不限制
IDLE_TIME_LIMIT=2.5
# 默认录制标题
RECORDING_TITLE="终端命令演示"
# 默认打字速度（秒/字符）
TYPING_SPEED=0.05
# 默认不生成HTML
GENERATE_HTML=false

# 检查asciinema是否已安装
if ! command -v asciinema &> /dev/null; then
    echo "错误: 未找到asciinema命令"
    echo "请通过以下命令安装asciinema:"
    echo "  brew install asciinema    # macOS"
    echo "  pip install asciinema     # 使用pip安装"
    exit 1
fi

# 检查bc是否已安装（用于浮点数比较）
if ! command -v bc &> /dev/null; then
    echo "警告: 未找到bc命令，将使用默认空闲时间限制"
    echo "请通过以下命令安装bc:"
    echo "  brew install bc    # macOS"
    echo "  apt install bc     # Debian/Ubuntu"
    # 设置一个标志，避免使用bc
    BC_AVAILABLE=false
else
    BC_AVAILABLE=true
fi

echo "===== 终端演示录制工具 ====="
echo "本工具将自动执行预定义命令并录制演示"
echo "录制文件将保存至: $OUTPUT_FILE"
echo ""

# 询问是否需要设置自定义标题
echo "是否设置自定义录制标题? (y/n) [默认: n]"
read -r customize_title
if [[ "$customize_title" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    echo "请输入录制标题 [默认: $RECORDING_TITLE]:"
    read -r input_title
    if [[ -n "$input_title" ]]; then
        RECORDING_TITLE="$input_title"
    fi
    
    echo "录制标题设置为: $RECORDING_TITLE"
fi

# 询问是否需要自定义终端尺寸
echo "是否需要自定义终端显示尺寸? (y/n) [默认: n]"
read -r customize_size
if [[ "$customize_size" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    # 询问列数
    echo "请输入终端列数 (columns) [默认: 80]:"
    read -r input_cols
    if [[ -n "$input_cols" ]] && [[ "$input_cols" =~ ^[0-9]+$ ]]; then
        COLS=$input_cols
    fi
    
    # 询问行数
    echo "请输入终端行数 (rows) [默认: 24]:"
    read -r input_rows
    if [[ -n "$input_rows" ]] && [[ "$input_rows" =~ ^[0-9]+$ ]]; then
        ROWS=$input_rows
    fi
    
    echo "终端尺寸设置为: ${COLS}x${ROWS}"
fi

# 询问是否需要自定义空闲时间限制
echo "是否需要设置空闲时间限制? (y/n) [默认: n]"
echo "说明: 设置后，录制时连续空闲超过指定秒数的部分会被压缩"
read -r customize_idle
if [[ "$customize_idle" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    # 询问空闲时间限制
    echo "请输入空闲时间限制(秒) [默认: 2.5, 0表示不限制]:"
    read -r input_idle
    if [[ -n "$input_idle" ]] && [[ "$input_idle" =~ ^[0-9]+(\.[0-9]+)?$ ]]; then
        IDLE_TIME_LIMIT=$input_idle
    fi
    
    echo "空闲时间限制设置为: ${IDLE_TIME_LIMIT}秒"
fi

# 询问是否需要自定义打字速度
echo "是否需要自定义打字速度? (y/n) [默认: n]"
echo "说明: 值越小，打字速度越快"
read -r customize_typing
if [[ "$customize_typing" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    # 询问打字速度
    echo "请输入打字速度(秒/字符) [默认: 0.05]:"
    read -r input_typing
    if [[ -n "$input_typing" ]] && [[ "$input_typing" =~ ^[0-9]+(\.[0-9]+)?$ ]]; then
        TYPING_SPEED=$input_typing
    fi
    
    echo "打字速度设置为: ${TYPING_SPEED}秒/字符"
fi

# 询问是否生成HTML版本
echo "是否需要生成HTML版本用于网页播放? (y/n) [默认: n]"
read -r generate_html
if [[ "$generate_html" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    GENERATE_HTML=true
    echo "将为录制生成HTML网页版: $HTML_OUTPUT_FILE"
fi

echo "将在3秒后开始录制..."

# 倒计时
echo "3..."
sleep 1
echo "2..."
sleep 1
echo "1..."
sleep 1

# 创建临时脚本，包含要执行的命令序列
TMP_SCRIPT="/tmp/demo_script_$$.sh"

# 使用变量替换创建脚本，而不是使用纯引用
cat > "$TMP_SCRIPT" << EOF
#!/bin/bash

# 模拟打字效果函数
type_text() {
  text="\$1"
  for (( i=0; i<\${#text}; i++ )); do
    echo -n "\${text:\$i:1}"
    sleep ${TYPING_SPEED}
  done
  echo ""
}

# 清空屏幕并展示标题
clear
echo "======================================"
echo "       ${RECORDING_TITLE}"
echo "======================================"
echo ""
sleep 1

# 展示当前目录
type_text "# 让我们看看当前目录有哪些文件"
sleep 0.5
ls -la
sleep 2

# 展示系统信息
type_text "# 查看系统信息"
sleep 0.5
uname -a
sleep 2

# 创建一个示例目录
type_text "# 创建一个新目录"
sleep 0.5
mkdir -p demo_folder
sleep 1
type_text "# 进入新创建的目录"
cd demo_folder
sleep 1
pwd
sleep 2

# 创建一个示例文件
type_text "# 创建一个简单的Python脚本"
sleep 0.5
cat > hello.py << 'PYFILE'
#!/usr/bin/env python3

def main():
    print("Hello, world!")
    print("这是一个asciinema终端录制演示")
    print("感谢观看!")

if __name__ == "__main__":
    main()
PYFILE
sleep 1

# 展示文件内容
type_text "# 查看我们创建的文件"
sleep 0.5
cat hello.py
sleep 2

# 执行Python脚本
type_text "# 运行Python脚本"
sleep 0.5
python3 hello.py
sleep 2

# 返回上层目录
type_text "# 返回上层目录"
sleep 0.5
cd ..
sleep 1
pwd
sleep 2

# 清理
type_text "# 清理演示文件"
sleep 0.5
rm -rf demo_folder
sleep 1
type_text "# 确认文件已删除"
ls -la
sleep 2

# 结束语
echo ""
echo "======================================"
echo "     演示结束，感谢观看!"
echo "======================================"
sleep 3

exit
EOF

# 给临时脚本添加执行权限
chmod +x "$TMP_SCRIPT"

# 构建asciinema命令
ASCIINEMA_CMD="asciinema rec --command=\"$TMP_SCRIPT\" --cols=\"$COLS\" --rows=\"$ROWS\" --title=\"$RECORDING_TITLE\""

# 如果设置了空闲时间限制且不为0，则添加对应选项
if $BC_AVAILABLE; then
    if (( $(echo "$IDLE_TIME_LIMIT > 0" | bc -l) )); then
        ASCIINEMA_CMD="$ASCIINEMA_CMD --idle-time-limit=\"$IDLE_TIME_LIMIT\""
    fi
else
    # 如果bc不可用，简单比较整数部分
    if [ "$IDLE_TIME_LIMIT" != "0" ]; then
        ASCIINEMA_CMD="$ASCIINEMA_CMD --idle-time-limit=\"$IDLE_TIME_LIMIT\""
    fi
fi

# 添加输出文件路径
ASCIINEMA_CMD="$ASCIINEMA_CMD \"$OUTPUT_FILE\""

# 使用asciinema录制命令执行过程
echo "开始录制演示..."
eval "$ASCIINEMA_CMD"

# 删除临时脚本
rm -f "$TMP_SCRIPT"

# 检查录制是否成功
if [ -f "$OUTPUT_FILE" ]; then
    echo "录制完成!"
    echo "录制文件已保存至: $OUTPUT_FILE"
    echo "文件信息:"
    ls -lh "$OUTPUT_FILE"

    # 如果需要生成HTML版本
    if $GENERATE_HTML; then
        echo "生成HTML版本中..."
        asciinema play --html "$OUTPUT_FILE" > "$HTML_OUTPUT_FILE"
        
        if [ -f "$HTML_OUTPUT_FILE" ]; then
            echo "HTML版本已生成: $HTML_OUTPUT_FILE"
            echo "文件信息:"
            ls -lh "$HTML_OUTPUT_FILE"
        else
            echo "HTML版本生成失败"
        fi
    fi

    echo ""
    echo "您可以通过以下命令回放录制内容:"
    echo "  asciinema play $OUTPUT_FILE"
    if ! $GENERATE_HTML; then
        echo "  或生成HTML网页版: asciinema play --html $OUTPUT_FILE > ${OUTPUT_FILE%.cast}.html"
    fi

    # 询问是否立即回放
    echo ""
    echo "是否立即回放录制内容? (y/n)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        asciinema play "$OUTPUT_FILE"
    fi

else
    echo "录制失败或保存文件出错"
fi

echo ""
echo "脚本执行完毕"
