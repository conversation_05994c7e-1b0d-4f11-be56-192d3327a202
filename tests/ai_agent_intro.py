#!/usr/bin/env python
# -*- coding: utf-8 -*-

from manim import *
import numpy as np
import random

class AIAgentIntro(Scene):
    def construct(self):
        # 设置高级渐变背景
        background = Rectangle(
            width=config.frame_width,
            height=config.frame_height,
            fill_opacity=1,
            stroke_width=0
        )
        background.set_color_by_gradient([
            "#0A0A2A",  # 深蓝色底色
            "#141450",  # 稍微亮一点的蓝色
            "#181860"   # 靠上部的颜色
        ])
        
        # 添加纹理元素
        texture_dots = VGroup()
        for _ in range(80):
            dot = Dot(
                radius=random.uniform(0.01, 0.03), 
                color=WHITE,
                fill_opacity=random.uniform(0.1, 0.4)
            )
            dot.move_to([
                random.uniform(-config.frame_width/2, config.frame_width/2),
                random.uniform(-config.frame_height/2, config.frame_height/2),
                0
            ])
            texture_dots.add(dot)
        
        # 添加装饰线
        decoration_top = Line(
            start=[-config.frame_width/2, config.frame_height/2 - 1.7, 0],
            end=[config.frame_width/2, config.frame_height/2 - 1.7, 0],
            color=BLUE_B,
            stroke_width=1.5,
            stroke_opacity=0.4
        )
        
        decoration_bottom = Line(
            start=[-config.frame_width/2, -config.frame_height/2 + 1, 0],
            end=[config.frame_width/2, -config.frame_height/2 + 1, 0],
            color=BLUE_B,
            stroke_width=1.5,
            stroke_opacity=0.4
        )
        
        # 标题区域背景
        title_bg = Rectangle(
            width=config.frame_width,
            height=1.7,
            fill_opacity=0.4,
            fill_color="#1A237E",
            stroke_width=0
        ).to_edge(UP, buff=0)
        
        # 标题文本
        title = Text("AI Agent", font_size=76, color=WHITE)
        title.to_edge(UP, buff=0.4)
        
        # 创建分区域 - 使用RoundedRectangle替代
        agent_section = RoundedRectangle(
            width=5,
            height=5,
            corner_radius=0.5,
            fill_color="#1A237E",
            fill_opacity=0.15,
            stroke_color=BLUE_B,
            stroke_width=2,
            stroke_opacity=0.5
        ).shift(LEFT*3.5)
        
        user_section = RoundedRectangle(
            width=5,
            height=5,
            corner_radius=0.5,
            fill_color="#4A148C",
            fill_opacity=0.15,
            stroke_color=PURPLE_B,
            stroke_width=2,
            stroke_opacity=0.5
        ).shift(RIGHT*3.5)
        
        # 创建更精致的Agent图标
        agent_outer_circle = Circle(radius=1.3, color=BLUE_A, fill_opacity=0.2, stroke_width=2)
        agent_inner_circle = Circle(radius=1.1, color=BLUE_C, fill_opacity=0.3, stroke_width=1)
        agent_core = Circle(radius=0.2, color=BLUE, fill_opacity=1, stroke_width=0).move_to([0, 0.2, 0])
        
        # 创建Agent "电路"线条
        circuit_lines = VGroup()
        for i in range(8):
            angle = i * PI/4
            start_point = agent_core.get_center() + np.array([0.2 * np.cos(angle), 0.2 * np.sin(angle), 0])
            end_point = start_point + np.array([0.7 * np.cos(angle), 0.7 * np.sin(angle), 0])
            line = Line(start_point, end_point, color=BLUE_B, stroke_width=1.5)
            circuit_lines.add(line)
            
            # 添加一些点作为节点
            if i % 2 == 0:
                node = Dot(radius=0.04, color=BLUE_A).move_to(end_point)
                circuit_lines.add(node)
        
        # 组合Agent图标
        agent_icon = VGroup(agent_outer_circle, agent_inner_circle, agent_core, circuit_lines)
        agent_icon.move_to(agent_section.get_center())
        
        # 创建更精致的用户图标
        user_circle = Circle(radius=1.2, color=PURPLE_B, fill_opacity=0.2, stroke_width=2)
        
        # 创建用户图形
        user_head = Circle(radius=0.4, color=PURPLE_A, fill_opacity=0.8, stroke_width=0)
        user_head.move_to([0, 0.3, 0])
        
        user_body = Triangle(color=PURPLE_A, fill_opacity=0.8, stroke_width=0)
        user_body.scale(0.8)
        user_body.rotate(PI)  # 翻转三角形
        user_body.next_to(user_head, DOWN, buff=0.05)
        
        # 组合用户图标
        user_icon = VGroup(user_circle, user_head, user_body)
        user_icon.move_to(user_section.get_center())
        
        # Agent标签
        agent_label = Text("AI Agent", font_size=28, color=BLUE_A)
        agent_label.next_to(agent_section, UP, buff=0.3)
        
        # 用户标签
        user_label = Text("用户", font_size=28, color=PURPLE_A)
        user_label.next_to(user_section, UP, buff=0.3)
        
        # 创建Agent能力标签 - 使用更精致的圆形布局
        sense_circle = Circle(radius=0.6, fill_color="#388E3C", fill_opacity=0.7, stroke_color=GREEN, stroke_width=2)
        think_circle = Circle(radius=0.6, fill_color="#FBC02D", fill_opacity=0.7, stroke_color=YELLOW, stroke_width=2)
        act_circle = Circle(radius=0.6, fill_color="#D32F2F", fill_opacity=0.7, stroke_color=RED, stroke_width=2)
        
        sense_text = Text("感知", font_size=22, color=WHITE)
        think_text = Text("思考", font_size=22, color=BLACK)
        act_text = Text("行动", font_size=22, color=WHITE)
        
        sense_text.move_to(sense_circle.get_center())
        think_text.move_to(think_circle.get_center())
        act_text.move_to(act_circle.get_center())
        
        sense = VGroup(sense_circle, sense_text)
        think = VGroup(think_circle, think_text)
        act = VGroup(act_circle, act_text)
        
        # 放置能力按钮位置
        sense.move_to(agent_section.get_center() + DOWN*2.5 + LEFT*1.5)
        think.move_to(agent_section.get_center() + DOWN*2.5)
        act.move_to(agent_section.get_center() + DOWN*2.5 + RIGHT*1.5)
        
        abilities = VGroup(sense, think, act)
        
        # 添加必要的箭头 - 从能力到Agent的连接
        sense_arrow = Arrow(
            sense.get_top(), 
            agent_icon.get_bottom() + LEFT*0.5, 
            color=GREEN_A, 
            buff=0.2,
            stroke_width=2,
            max_tip_length_to_length_ratio=0.15
        )
        
        think_arrow = Arrow(
            think.get_top(), 
            agent_icon.get_bottom(), 
            color=YELLOW_A, 
            buff=0.2,
            stroke_width=2,
            max_tip_length_to_length_ratio=0.15
        )
        
        act_arrow = Arrow(
            act.get_top(), 
            agent_icon.get_bottom() + RIGHT*0.5, 
            color=RED_A, 
            buff=0.2,
            stroke_width=2,
            max_tip_length_to_length_ratio=0.15
        )
        
        # 创建Agent与用户之间的交互箭头
        query_arrow = Arrow(
            user_icon.get_left() + LEFT*0.1, 
            agent_icon.get_right() + RIGHT*0.1, 
            color=PURPLE_A, 
            buff=0.3,
            stroke_width=3,
            max_tip_length_to_length_ratio=0.1
        )
        
        response_arrow = Arrow(
            agent_icon.get_right() + RIGHT*0.1, 
            user_icon.get_left() + LEFT*0.1, 
            color=BLUE_A, 
            buff=0.3,
            stroke_width=3,
            max_tip_length_to_length_ratio=0.1
        )
        
        # 交互文本标签
        query_text = Text("指令", font_size=24, color=PURPLE_A)
        query_text.next_to(query_arrow, UP, buff=0.2)
        
        response_text = Text("响应", font_size=24, color=BLUE_A)
        response_text.next_to(response_arrow, DOWN, buff=0.2)
        
        # 动画展示
        # 先添加背景元素
        self.add(background)
        self.play(
            FadeIn(texture_dots, scale=1.2),
            Create(decoration_top),
            Create(decoration_bottom),
            FadeIn(title_bg),
            run_time=0.8
        )
        
        # 添加标题
        self.play(Write(title), run_time=0.8)
        
        # 添加两个区域
        self.play(
            Create(agent_section),
            Create(user_section),
            run_time=0.8
        )
        
        # 添加标签
        self.play(
            Write(agent_label),
            Write(user_label),
            run_time=0.6
        )
        
        # 添加Agent和用户图标
        self.play(
            FadeIn(agent_icon, scale=1.1), 
            FadeIn(user_icon, scale=1.1),
            run_time=1
        )
        
        # 展示Agent的核心能力和箭头
        self.play(
            FadeIn(abilities, scale=1.1),
            run_time=0.8
        )
        
        self.play(
            Create(sense_arrow),
            Create(think_arrow),
            Create(act_arrow),
            run_time=0.8
        )
        
        # 展示交互箭头
        self.play(
            Create(query_arrow),
            Write(query_text),
            run_time=0.8
        )
        
        self.play(
            Create(response_arrow),
            Write(response_text),
            run_time=0.8
        )
        
        # 最终效果 - 图标的脉动效果
        self.play(
            agent_icon.animate.scale(1.1),
            rate_func=there_and_back,
            run_time=1.2
        )
        
        # 保持最终画面
        self.wait(1)


if __name__ == "__main__":
    # 命令行参数示例: python -m manim ai_agent_intro.py AIAgentIntro -pql
    pass