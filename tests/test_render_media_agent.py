# 导入必要的库

from tools.render_media_toolkit import RenderMediaToolkit

# 初始化工具
render_toolkit = RenderMediaToolkit()

# 生成关键词动画代码
images = [
    "pdf_output/2411.01747v1_artifacts/page_3_image_0.png",
    "pdf_output/2411.01747v1_artifacts/page_7_image_6.png",
]
topic = "DynaSaur框架如何通过动态动作创建，让LLM代理更智能，解决现实世界难题？"
step = 1
animation_code = render_toolkit.generate_media_display_code(images, step)
code_lines = [
    "from manim import *\n",
    "import os\n",
    "config.pixel_height = 1920\n",
    "config.pixel_width = 1090\n",
    "config.frame_height = 16.0\n",
    "config.frame_width = 9.0\nfrom manim_funcs.anim_funcs import *\n",
    "class GeneratedAnimation(FeynmanScene):",
    "    def construct(self):",
    "        def print_current_time():",
    "            current_time = self.renderer.time",
    "            return current_time",
    "        begin_time = print_current_time()",
    "        end_time = print_current_time()",
]
code_lines.append(f'        self.display_slogo("{topic}", {step})')
code_lines.append("        anim = self.render_media_tool()")
code_lines.append("        if len(anim) > 0:")
code_lines.append("            for anim_key in anim:")
code_lines.append('                if anim_key["obj_type"] == "Video":')
code_lines.append('                    self.add(anim_key["obj"])')
code_lines.append('                elif anim_key["obj_type"] == "Image":')
code_lines.append('                    self.play(anim_key["obj"])')
code_lines.extend(animation_code)
with open("render_media.py", "w", encoding="utf-8") as f:
    f.write("\n".join(code_lines))
