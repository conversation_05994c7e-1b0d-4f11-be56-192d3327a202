from manim import *
import numpy as np
import random

class CamelStockCompareDemo(Scene):
    def construct(self):
        # 统一配色方案 - 简化到3个主色
        # 主色1：DARK_BLUE（深蓝）- 稳重专业
        # 主色2：PURPLE（紫色）- 科技感AI
        # 主色3：ORANGE（橙色）- 活力数据
        # 主文字：WHITE（白色）
        # 强调文字：YELLOW（黄色）
        # 数据：GREEN（绿色）、RED（红色）
        
        # 初始背景
        self.current_background = Rectangle(
            width=config.frame_width,
            height=config.frame_height,
            fill_color=DARK_BLUE,
            fill_opacity=1,
            stroke_width=0
        )
        self.add(self.current_background)
        
        # 场景1：标题介绍
        self.scene_1_title()
        
        # 场景2：AI Agent 介绍
        self.scene_2_ai_agent_intro()
        
        # 场景3：股票数据对比
        self.scene_3_stock_comparison()
        
        # 场景4：财务指标分析
        self.scene_4_financial_analysis()
        
        # 场景5：结论与未来展望
        self.scene_5_conclusion()
    
    def fade_background_transition(self, new_color):
        """淡入淡出背景切换"""
        new_bg = Rectangle(
            width=config.frame_width,
            height=config.frame_height,
            fill_color=new_color,
            fill_opacity=1,
            stroke_width=0
        )
        return Transform(self.current_background, new_bg)
    
    def push_background_transition(self, new_color, direction=RIGHT):
        """推镜背景切换 - 修复版本"""
        new_bg = Rectangle(
            width=config.frame_width,
            height=config.frame_height,
            fill_color=new_color,
            fill_opacity=1,
            stroke_width=0
        )
        
        # 简化推镜效果，使用fade_background_transition
        animation = Transform(self.current_background, new_bg)
        self.current_background = new_bg
        return [animation]
    
    def mask_background_transition(self, new_color):
        """遮罩动画背景切换"""
        # 创建圆形遮罩
        mask = Circle(radius=0.1, fill_color=new_color, fill_opacity=1, stroke_width=0)
        mask.move_to(ORIGIN)
        
        new_bg = Rectangle(
            width=config.frame_width,
            height=config.frame_height,
            fill_color=new_color,
            fill_opacity=1,
            stroke_width=0
        )
        
        # 遮罩扩展效果
        expanded_mask = Circle(radius=10, fill_color=new_color, fill_opacity=1, stroke_width=0)
        expanded_mask.move_to(ORIGIN)
        
        animation = Transform(self.current_background, new_bg)
        self.current_background = new_bg
        return [animation]
    
    def blur_background_transition(self, new_color):
        """模糊过渡背景切换"""
        new_bg = Rectangle(
            width=config.frame_width,
            height=config.frame_height,
            fill_color=new_color,
            fill_opacity=1,
            stroke_width=0
        )
        animation = Transform(self.current_background, new_bg)
        self.current_background = new_bg
        return animation
    
    def slide_background_transition(self, new_color, direction=UP):
        """滑动背景切换 - 修复版本"""
        new_bg = Rectangle(
            width=config.frame_width,
            height=config.frame_height,
            fill_color=new_color,
            fill_opacity=1,
            stroke_width=0
        )
        
        # 简化滑动效果，使用fade_background_transition
        animation = Transform(self.current_background, new_bg)
        self.current_background = new_bg
        return [animation]
    
    def zoom_background_transition(self, new_color):
        """缩放背景切换"""
        new_bg = Rectangle(
            width=config.frame_width,
            height=config.frame_height,
            fill_color=new_color,
            fill_opacity=1,
            stroke_width=0
        )
        
        animation = Transform(self.current_background, new_bg)
        self.current_background = new_bg
        return animation
    
    def scene_1_title(self):
        """场景1：标题介绍 - 多阶段Transform + 背景渐进切换"""
        
        # ===== 阶段1：标题出现 (DARK_BLUE -> PURPLE) =====
        # 主标题 - 保持合理边界
        title = Text("AI帮你选股？", font_size=76, color=YELLOW)
        title.move_to([0, 1.2, 0])  # 调整位置避免贴边
        
        title_group_1 = VGroup(title)
        empty_group_1 = VGroup()
        
        # 背景切换到紫色
        self.play(
            self.fade_background_transition(PURPLE),
            Transform(empty_group_1, title_group_1),
            run_time=0.6
        )
        self.wait(0.4)
        self.remove(empty_group_1)
        
        # ===== 阶段2：添加副标题 (PURPLE -> PURPLE) =====
        subtitle = Text("实测 CAMEL Agent 对比 AAPL vs NVDA", font_size=40, color=WHITE)
        subtitle.move_to([0, 0.4, 0])
        
        title_group_2 = VGroup(title.copy(), subtitle)
        
        # 保持紫色背景 + 副标题Transform出现
        self.play(
            Transform(title_group_1, title_group_2),
            run_time=0.6
        )
        self.wait(0.4)
        
        # ===== 阶段3：添加装饰元素 (PURPLE -> PURPLE) =====
        # AI 图标 - 合理大小和位置
        ai_icon = Circle(radius=0.4, fill_color=YELLOW, fill_opacity=0.3, stroke_color=YELLOW, stroke_width=3)
        ai_icon.move_to([0, -0.4, 0])
        ai_text = Text("AI", font_size=32, color=YELLOW)
        ai_text.move_to([0, -0.4, 0])
        
        # 装饰线条 - 避免超出边界
        decorative_line1 = Line([-3, -1.2, 0], [3, -1.2, 0], stroke_width=3, color=YELLOW)
        decorative_line2 = Line([-2.5, -1.6, 0], [2.5, -1.6, 0], stroke_width=2, color=WHITE)
        
        title_group_3 = VGroup(title.copy(), subtitle.copy(), ai_icon, ai_text, decorative_line1, decorative_line2)
        
        # 保持紫色背景 + 装饰元素Transform出现
        self.play(
            Transform(title_group_1, title_group_3),
            run_time=0.6
        )
        self.wait(0.8)
        
        # ===== 阶段4：退出动画 =====
        exit_empty = VGroup()
        self.play(
            Transform(title_group_1, exit_empty),
            run_time=0.5
        )
        self.remove(title_group_1)
    
    def scene_2_ai_agent_intro(self):
        """场景2：AI Agent 介绍 - 多阶段Transform + 背景渐进切换"""
        features = ["自动化数据获取", "实时股价分析", "财务指标对比", "智能投资建议"]
        
        # ===== 阶段1：标题出现 (当前背景 -> ORANGE) =====
        agent_title = Text("CAMEL AI Agent", font_size=64, color=YELLOW)
        agent_title.move_to([0, 2.8, 0])  # 保持合理边界
        
        title_stage = VGroup(agent_title)
        empty_start_1 = VGroup()
        
        # 推进切换到橙色背景
        push_animations = self.push_background_transition(ORANGE, LEFT)
        self.play(
            *push_animations,
            Transform(empty_start_1, title_stage),
            run_time=0.6
        )
        self.wait(0.3)
        self.remove(empty_start_1)
        
        # ===== 阶段2：四角分布特性 (ORANGE -> ORANGE_D) =====
        # 合理的四角位置，避免贴边
        text1 = Text(features[0], font_size=36, color=WHITE)
        text1.move_to([-3.5, 1.4, 0])
        text2 = Text(features[1], font_size=36, color=WHITE)
        text2.move_to([3.5, 1.4, 0])
        text3 = Text(features[2], font_size=36, color=WHITE)
        text3.move_to([-3.5, -0.8, 0])
        text4 = Text(features[3], font_size=36, color=WHITE)
        text4.move_to([3.5, -0.8, 0])
        
        scattered_stage = VGroup(agent_title.copy(), text1, text2, text3, text4)
        
        # 背景深化 + 四角特性Transform出现
        self.play(
            self.blur_background_transition(ORANGE),
            Transform(title_stage, scattered_stage),
            run_time=0.6
        )
        self.wait(0.4)
        
        # ===== 阶段3：水平一行 (ORANGE -> PURPLE) =====
        h_text1 = Text(features[0][:4], font_size=44, color=YELLOW)
        h_text1.move_to([-3.8, 0.3, 0])
        h_text2 = Text(features[1][:4], font_size=44, color=YELLOW)
        h_text2.move_to([-1.3, 0.3, 0])
        h_text3 = Text(features[2][:4], font_size=44, color=YELLOW)
        h_text3.move_to([1.3, 0.3, 0])
        h_text4 = Text(features[3][:4], font_size=44, color=YELLOW)
        h_text4.move_to([3.8, 0.3, 0])
        
        horizontal_stage = VGroup(agent_title.copy(), h_text1, h_text2, h_text3, h_text4)
        
        # 背景切换到紫色 + 水平Transform
        self.play(
            self.fade_background_transition(PURPLE),
            Transform(title_stage, horizontal_stage),
            run_time=0.6
        )
        self.wait(0.4)
        
        # ===== 阶段4：垂直列表 (PURPLE -> DARK_BLUE) =====
        vertical_positions = [[0, 1.6, 0], [0, 0.6, 0], [0, -0.4, 0], [0, -1.4, 0]]
        vertical_features = []
        
        for i, (feature, pos) in enumerate(zip(features, vertical_positions)):
            # 图标 - 合理位置
            icon = Circle(radius=0.2, fill_color=YELLOW, fill_opacity=1, stroke_width=0)
            icon.move_to([pos[0] - 3, pos[1], 0])
            
            feature_text = Text(feature, font_size=32, color=WHITE)
            feature_text.move_to([pos[0] + 0.3, pos[1], 0])
            
            feature_item = VGroup(icon, feature_text)
            vertical_features.append(feature_item)
        
        vertical_stage = VGroup(agent_title.copy().move_to([0, 2.8, 0]), *vertical_features)
        
        # 背景切换到深蓝色 + 垂直Transform
        self.play(
            self.slide_background_transition(DARK_BLUE, DOWN),
            Transform(title_stage, vertical_stage),
            run_time=0.6
        )
        self.wait(0.5)
        
        # ===== 阶段5：最终合并 (DARK_BLUE -> DARK_BLUE) =====
        final_line1 = Text("AI智能", font_size=68, color=YELLOW)
        final_line1.move_to([0, 0.6, 0])
        final_line2 = Text("投资分析", font_size=68, color=YELLOW)
        final_line2.move_to([0, -0.6, 0])
        
        final_stage = VGroup(final_line1, final_line2)
        
        # 保持深蓝色背景 + 合并Transform
        self.play(
            Transform(title_stage, final_stage),
            run_time=0.6
        )
        self.wait(0.8)
        
        # ===== 退出动画 =====
        exit_empty = VGroup()
        self.play(
            Transform(title_stage, exit_empty),
            run_time=0.5
        )
        self.remove(title_stage)
    
    def scene_3_stock_comparison(self):
        """场景3：股票数据对比 - 多阶段Transform + 背景渐进切换"""
        stock_data = {
            "AAPL": {"name": "苹果公司", "price": "210.195", "high": "260.1", "low": "169.21"},
            "NVDA": {"name": "英伟达公司", "price": "134.98", "high": "153.13", "low": "86.62"}
        }
        
        # ===== 阶段1：标题出现 (当前背景 -> ORANGE) =====
        comparison_title = Text("股票价格对比", font_size=64, color=YELLOW)
        comparison_title.move_to([0, 2.8, 0])
        
        title_stage = VGroup(comparison_title)
        empty_init_1 = VGroup()
        
        # 滑动切换到橙色背景
        slide_animations = self.slide_background_transition(ORANGE, DOWN)
        self.play(
            *slide_animations,
            Transform(empty_init_1, title_stage),
            run_time=0.6
        )
        self.wait(0.4)
        self.remove(empty_init_1)
        
        # ===== 阶段2：股票代码出现 (ORANGE -> ORANGE) =====
        aapl_symbol = Text("AAPL", font_size=56, color=YELLOW)
        aapl_symbol.move_to([-3.2, 0.5, 0])
        nvda_symbol = Text("NVDA", font_size=56, color=YELLOW)
        nvda_symbol.move_to([3.2, 0.5, 0])
        vs_text = Text("VS", font_size=48, color=RED)
        vs_text.move_to([0, 0.5, 0])
        
        symbols_stage = VGroup(comparison_title.copy(), aapl_symbol, nvda_symbol, vs_text)
        
        # 保持橙色背景 + 股票代码Transform出现
        self.play(
            Transform(title_stage, symbols_stage),
            run_time=0.6
        )
        self.wait(0.4)
        
        # ===== 阶段3：完整卡片 (ORANGE -> ORANGE) =====
        def create_stock_card(symbol, data, position):
            # 卡片背景 - 合理尺寸，避免超出边界
            card_bg = RoundedRectangle(
                width=5.8, height=3.2, 
                corner_radius=0.3,
                fill_color=WHITE,
                fill_opacity=0.12,
                stroke_color=WHITE,
                stroke_width=2
            )
            card_bg.move_to(position)
            
            # 股票代码
            symbol_text = Text(symbol, font_size=52, color=YELLOW)
            symbol_text.move_to([position[0], position[1] + 1.1, 0])
            
            # 公司名称
            name_text = Text(data["name"], font_size=28, color=WHITE)
            name_text.move_to([position[0], position[1] + 0.6, 0])
            
            # 当前价格
            price_text = Text(f"${data['price']}", font_size=36, color=GREEN)
            price_text.move_to([position[0], position[1] + 0.1, 0])
            
            # 价格区间
            range_text = Text(f"区间: ${data['low']} - ${data['high']}", font_size=20, color=WHITE)
            range_text.move_to([position[0], position[1] - 0.6, 0])
            
            return VGroup(card_bg, symbol_text, name_text, price_text, range_text)
        
        # 创建完整卡片 - 合理间距
        aapl_card = create_stock_card("AAPL", stock_data["AAPL"], [-3.2, 0, 0])
        nvda_card = create_stock_card("NVDA", stock_data["NVDA"], [3.2, 0, 0])
        
        # 添加装饰箭头
        arrow_left = Arrow(start=[-1.6, 0, 0], end=[-0.6, 0, 0], color=RED, stroke_width=3)
        arrow_right = Arrow(start=[0.6, 0, 0], end=[1.6, 0, 0], color=RED, stroke_width=3)
        
        cards_stage = VGroup(comparison_title.copy(), aapl_card, nvda_card, vs_text.copy(), arrow_left, arrow_right)
        
        # 保持橙色背景 + 完整卡片Transform
        self.play(
            Transform(title_stage, cards_stage),
            run_time=0.8
        )
        self.wait(1.2)
        
        # ===== 退出动画 =====
        exit_empty = VGroup()
        self.play(
            Transform(title_stage, exit_empty),
            run_time=0.6
        )
        self.remove(title_stage)
    
    def scene_4_financial_analysis(self):
        """场景4：财务指标分析 - 多阶段Transform + 背景渐进切换"""
        financial_metrics = [
            {"metric": "市值", "aapl": "3.5T", "nvda": "2.9T", "winner": "AAPL"},
            {"metric": "市盈率", "aapl": "37.29", "nvda": "39.90", "winner": "AAPL"},
            {"metric": "营收/每股", "aapl": "25.48", "nvda": "5.31", "winner": "AAPL"},
            {"metric": "股本回报率", "aapl": "1.65", "nvda": "0.92", "winner": "AAPL"},
        ]
        
        # ===== 阶段1：标题出现 (当前背景 -> PURPLE) =====
        analysis_title = Text("财务指标分析", font_size=64, color=YELLOW)
        analysis_title.move_to([0, 2.8, 0])
        
        title_stage = VGroup(analysis_title)
        empty_start_1 = VGroup()
        
        # 缩放切换到紫色背景
        zoom_animation = self.zoom_background_transition(PURPLE)
        self.play(
            zoom_animation,
            Transform(empty_start_1, title_stage),
            run_time=0.6
        )
        self.wait(0.4)
        self.remove(empty_start_1)
        
        # ===== 阶段2：表头出现 (PURPLE -> PURPLE) =====
        table_header = VGroup(
            Text("指标", font_size=38, color=WHITE).move_to([-3.5, 1.8, 0]),
            Text("AAPL", font_size=38, color=GREEN).move_to([0, 1.8, 0]),
            Text("NVDA", font_size=38, color=BLUE).move_to([3.5, 1.8, 0])
        )
        
        header_stage = VGroup(analysis_title.copy(), table_header)
        
        # 保持紫色背景 + 表头Transform出现
        self.play(
            Transform(title_stage, header_stage),
            run_time=0.6
        )
        self.wait(0.4)
        
        # ===== 阶段3：逐行添加数据 (PURPLE -> DARK_BLUE) =====
        table_rows = VGroup()
        y_positions = [1.1, 0.4, -0.3, -1.0]
        
        for i, (metric_data, y_pos) in enumerate(zip(financial_metrics, y_positions)):
            # 指标名称 - 合理字体大小和位置
            metric_name = Text(metric_data["metric"], font_size=30, color=WHITE).move_to([-3.5, y_pos, 0])
            
            # AAPL数据
            aapl_value = Text(metric_data["aapl"], font_size=30, 
                            color=YELLOW if metric_data["winner"] == "AAPL" else WHITE)
            aapl_value.move_to([0, y_pos, 0])
            
            # NVDA数据
            nvda_value = Text(metric_data["nvda"], font_size=30,
                            color=YELLOW if metric_data["winner"] == "NVDA" else WHITE)
            nvda_value.move_to([3.5, y_pos, 0])
            
            row = VGroup(metric_name, aapl_value, nvda_value)
            table_rows.add(row)
        
        # 分割线 - 合理长度
        separator_lines = VGroup()
        for i in range(len(financial_metrics) + 1):
            y = 1.4 - i * 0.7
            line = Line([-5, y, 0], [5, y, 0], stroke_width=1.5, color=GRAY)
            separator_lines.add(line)
        
        # 垂直分割线
        vertical_lines = VGroup(
            Line([-1.75, 1.4, 0], [-1.75, -1.3, 0], stroke_width=1.5, color=GRAY),
            Line([1.75, 1.4, 0], [1.75, -1.3, 0], stroke_width=1.5, color=GRAY)
        )
        
        table_stage = VGroup(analysis_title.copy(), table_header.copy(), table_rows, separator_lines, vertical_lines)
        
        # 背景切换到深蓝色 + 表格数据Transform出现
        self.play(
            self.fade_background_transition(DARK_BLUE),
            Transform(title_stage, table_stage),
            run_time=0.8
        )
        self.wait(1.5)
        
        # ===== 退出动画 =====
        exit_empty = VGroup()
        self.play(
            Transform(title_stage, exit_empty),
            run_time=0.6
        )
        self.remove(title_stage)
    
    def scene_5_conclusion(self):
        """场景5：结论与未来展望 - 多阶段Transform + 背景渐进切换"""
        value_points = [
            "自动化数据收集与整理",
            "快速生成对比分析", 
            "提供基于数据的洞察",
            "赋能投资决策过程"
        ]
        
        # ===== 阶段1：标题出现 (当前背景 -> PURPLE) =====
        conclusion_title = Text("AI投资分析的价值", font_size=64, color=YELLOW)
        conclusion_title.move_to([0, 2.8, 0])
        
        title_stage = VGroup(conclusion_title)
        empty_final_1 = VGroup()
        
        # 推进切换到紫色背景
        push_animations = self.push_background_transition(PURPLE, UP)
        self.play(
            *push_animations,
            Transform(empty_final_1, title_stage),
            run_time=0.6
        )
        self.wait(0.4)
        self.remove(empty_final_1)
        
        # ===== 阶段2：逐个添加价值点 (PURPLE -> PURPLE) =====
        value_list = VGroup()
        y_positions = [1.6, 0.6, -0.4, -1.4]
        
        for point, y_pos in zip(value_points, y_positions):
            # 图标 - 合理大小
            check_icon = Text("✓", font_size=32, color=GREEN)
            check_icon.move_to([-4.2, y_pos, 0])
            
            # 文本 - 合理字体大小
            point_text = Text(point, font_size=32, color=WHITE)
            point_text.move_to([0.2, y_pos, 0])
            
            value_item = VGroup(check_icon, point_text)
            value_list.add(value_item)
        
        value_stage = VGroup(conclusion_title.copy(), value_list)
        
        # 保持紫色背景 + 价值点Transform出现
        self.play(
            Transform(title_stage, value_stage),
            run_time=0.8
        )
        self.wait(0.6)
        
        # ===== 阶段3：添加未来展望 (PURPLE -> PURPLE) =====
        future_text = Text("AI将在金融投资领域扮演越来越重要的角色", 
                          font_size=28, color=YELLOW)
        future_text.move_to([0, -2.4, 0])
        
        future_stage = VGroup(conclusion_title.copy(), value_list.copy(), future_text)
        
        # 保持紫色背景 + 未来展望Transform出现
        self.play(
            Transform(title_stage, future_stage),
            run_time=0.6
        )
        self.wait(1)
        
        # ===== 阶段4：感谢页面 (PURPLE -> DARK_BLUE) =====
        thanks_title = Text("感谢观看", font_size=76, color=YELLOW)
        thanks_title.move_to([0, 0.8, 0])
        
        thanks_subtitle = Text("AI Agent 金融分析演示", font_size=44, color=WHITE)
        thanks_subtitle.move_to([0, -0.2, 0])
        
        # 星星装饰 - 合理分布，避免贴边
        stars = VGroup()
        star_positions = [[-4.2, 2.2, 0], [4.2, 2.2, 0], [-4.2, -1.8, 0], [4.2, -1.8, 0], 
                         [0, -1.8, 0], [-2.1, 2.2, 0], [2.1, 2.2, 0]]
        
        for pos in star_positions:
            star = Star(n=5, outer_radius=0.2, color=YELLOW, fill_opacity=1)
            star.move_to(pos)
            stars.add(star)
        
        thanks_stage = VGroup(thanks_title, thanks_subtitle, stars)
        
        # 背景切换到深蓝色 + 感谢页面Transform
        self.play(
            self.fade_background_transition(DARK_BLUE),
            Transform(title_stage, thanks_stage),
            run_time=0.8
        )
        
        # ===== 阶段5：星星闪烁效果 =====
        for _ in range(3):
            dim_stars = VGroup(*[star.copy().set_opacity(0.3) for star in stars])
            dim_group = VGroup(thanks_title.copy(), thanks_subtitle.copy(), dim_stars)
            
            self.play(Transform(title_stage, dim_group), run_time=0.25)
            
            bright_stars = VGroup(*[star.copy().set_opacity(1) for star in stars])
            bright_group = VGroup(thanks_title.copy(), thanks_subtitle.copy(), bright_stars)
            
            self.play(Transform(title_stage, bright_group), run_time=0.25)
        
        self.wait(1.2)
        
        # ===== 最终退出 (DARK_BLUE -> BLACK) =====
        final_empty = VGroup()
        self.play(
            self.fade_background_transition(BLACK),
            Transform(title_stage, final_empty),
            run_time=1
        )
        self.remove(title_stage)
        
        self.wait(0.8)

# 测试代码
if __name__ == "__main__":
    pass 