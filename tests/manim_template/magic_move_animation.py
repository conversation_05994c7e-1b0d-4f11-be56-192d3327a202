from manim import *
import numpy as np
import random

class MagicMoveAnimation(Scene):
    def construct(self):
        # 创建初始背景
        self.current_background = self.create_background([BLUE_E, BLUE_A, WHITE])
        self.add(self.current_background)
        
        # 场景1：标题介绍
        self.scene_1_title()
        
        # 场景2：文字神奇移动
        self.scene_2_text_magic_move()
        
        # 场景3：综合展示
        self.scene_3_combined_magic_move()
        
        # 结束场景
        self.scene_4_conclusion()
    
    def create_background(self, colors):
        """创建渐变背景"""
        gradient = Rectangle(
            width=config.frame_width,
            height=config.frame_height,
            fill_opacity=1,
            stroke_width=0
        ).set_color_by_gradient(colors)
        return gradient
    
    def change_background(self, new_colors, run_time=1.5):
        """改变背景颜色"""
        new_background = self.create_background(new_colors)
        return Transform(self.current_background, new_background)
    
    def move_in_from_direction(self, mobject, direction="LEFT", distance=10, run_time=1):
        """从指定方向移动进入"""
        if direction == "LEFT":
            start_pos = mobject.get_center() + LEFT * distance
        elif direction == "RIGHT":
            start_pos = mobject.get_center() + RIGHT * distance
        elif direction == "UP":
            start_pos = mobject.get_center() + UP * distance
        elif direction == "DOWN":
            start_pos = mobject.get_center() + DOWN * distance
        
        final_pos = mobject.get_center()
        mobject.move_to(start_pos)
        
        return mobject.animate.move_to(final_pos)
    
    def move_out_to_direction(self, mobject, direction="RIGHT", distance=10, run_time=1):
        """向指定方向移动退出"""
        if direction == "LEFT":
            end_pos = mobject.get_center() + LEFT * distance
        elif direction == "RIGHT":
            end_pos = mobject.get_center() + RIGHT * distance
        elif direction == "UP":
            end_pos = mobject.get_center() + UP * distance
        elif direction == "DOWN":
            end_pos = mobject.get_center() + DOWN * distance
        
        return mobject.animate.move_to(end_pos)
    
    def scene_1_title(self):
        """场景1：标题介绍"""
        # 标题
        title = Text("神奇移动", font_size=72, color=YELLOW, weight=BOLD)
        title.set_stroke(width=2, color=WHITE, opacity=0.8)
        title.move_to([0, 0.8, 0])
        
        subtitle = Text("Magic Move Animation", font_size=40, color=GOLD, weight=BOLD)
        subtitle.move_to([0, -0.5, 0])
        
        # 装饰线条
        left_line = Line([-3, 0, 0], [-1.5, 0, 0], stroke_width=5, color=YELLOW)
        right_line = Line([1.5, 0, 0], [3, 0, 0], stroke_width=5, color=YELLOW)
        center_dot = Dot(ORIGIN, radius=0.15, color=YELLOW)
        center_dot.move_to([0, -1.3, 0])
        
        # 背景变化和所有内容同时移入
        self.play(
            self.change_background([DARK_BLUE, PURPLE, PINK]),
            self.move_in_from_direction(title, "UP", 8, 0.8),
            self.move_in_from_direction(subtitle, "DOWN", 8, 0.8),
            self.move_in_from_direction(left_line, "LEFT", 10, 0.8),
            self.move_in_from_direction(right_line, "RIGHT", 10, 0.8),
            self.move_in_from_direction(center_dot, "UP", 6, 0.8),
            rate_func=smooth,
            run_time=1.8
        )
        self.wait(1.2)
        
        # 所有内容同时快速移出
        self.play(
            self.move_out_to_direction(title, "LEFT", 10, 0.6),
            self.move_out_to_direction(subtitle, "RIGHT", 10, 0.6),
            self.move_out_to_direction(left_line, "DOWN", 8, 0.6),
            self.move_out_to_direction(right_line, "UP", 8, 0.6),
            self.move_out_to_direction(center_dot, "RIGHT", 12, 0.6),
            rate_func=smooth,
            run_time=1.0
        )
    
    def scene_2_text_magic_move(self):
        """场景2：文字神奇移动效果"""
        # 初始状态：分散的文字
        text1 = Text("人工", font_size=52, color=RED, weight=BOLD)
        text2 = Text("智能", font_size=52, color=GREEN, weight=BOLD)
        text3 = Text("改变", font_size=52, color=BLUE, weight=BOLD)
        text4 = Text("世界", font_size=52, color=PURPLE, weight=BOLD)
        
        # 目标位置：四个角落，确保不超出边界
        text1.move_to([-3, 1.5, 0])
        text2.move_to([3, 1.5, 0])
        text3.move_to([-3, -1.5, 0])
        text4.move_to([3, -1.5, 0])
        
        # 背景变化和所有文字同时从屏幕外快速移入
        self.play(
            self.change_background([GREEN_E, TEAL, BLUE_C]),
            self.move_in_from_direction(text1, "LEFT", 12, 0.8),
            self.move_in_from_direction(text2, "RIGHT", 12, 0.8),
            self.move_in_from_direction(text3, "DOWN", 10, 0.8),
            self.move_in_from_direction(text4, "UP", 10, 0.8),
            rate_func=smooth,
            run_time=1.5
        )
        self.wait(0.5)
        
        # 神奇移动：文字重新排列成一行
        target_positions = [
            [-2.5, 0, 0],  # 人工
            [-0.8, 0, 0],  # 智能
            [0.8, 0, 0],   # 改变
            [2.5, 0, 0]    # 世界
        ]
        
        self.play(
            text1.animate.move_to(target_positions[0]).scale(1.2).set_color(YELLOW),
            text2.animate.move_to(target_positions[1]).scale(1.2).set_color(YELLOW),
            text3.animate.move_to(target_positions[2]).scale(1.2).set_color(YELLOW),
            text4.animate.move_to(target_positions[3]).scale(1.2).set_color(YELLOW),
            run_time=2.0,
            rate_func=smooth
        )
        self.wait(0.8)
        
        # 再次神奇移动：变成垂直排列
        vertical_positions = [
            [0, 1.8, 0],   # 人工
            [0, 0.6, 0],   # 智能
            [0, -0.6, 0],  # 改变
            [0, -1.8, 0]   # 世界
        ]
        
        self.play(
            text1.animate.move_to(vertical_positions[0]).set_color(ORANGE),
            text2.animate.move_to(vertical_positions[1]).set_color(ORANGE),
            text3.animate.move_to(vertical_positions[2]).set_color(ORANGE),
            text4.animate.move_to(vertical_positions[3]).set_color(ORANGE),
            run_time=2.0,
            rate_func=smooth
        )
        self.wait(0.8)
        
        # 最终变换：合并成一个完整句子
        final_text = Text("人工智能改变世界", font_size=56, color=GOLD, weight=BOLD)
        final_text.set_stroke(width=2, color=WHITE, opacity=0.6)
        
        # 创建一个新的组合对象来接收Transform结果
        text_group = VGroup(text1, text2, text3, text4)
        self.play(
            Transform(text_group, final_text),
            run_time=2,
            rate_func=smooth
        )
        self.wait(1.5)
        
        # 快速移出变换后的组合对象
        self.play(
            self.move_out_to_direction(text_group, "UP", 12, 0.8),
            rate_func=smooth
        )
        # 确保完全移除
        self.remove(text1, text2, text3, text4, text_group)
    
    def scene_3_combined_magic_move(self):
        """场景3：综合展示"""
        # 创建标题
        title = Text("2024年科技趋势", font_size=60, color=YELLOW, weight=BOLD)
        title.set_stroke(width=2, color=WHITE, opacity=0.7)
        title.move_to([0, 3.0, 0])
        
        # 背景变化和标题同时移入
        self.play(
            self.change_background([PURPLE_E, PURPLE_C, PINK]),
            self.move_in_from_direction(title, "UP", 12, 0.8),
            rate_func=smooth,
            run_time=1.5
        )
        self.wait(0.5)
        
        # 创建要点列表
        points = [
            "AI技术快速发展",
            "量子计算突破", 
            "绿色能源革命",
            "元宇宙兴起"
        ]
        
        # 存储已显示的要点
        displayed_points = []
        
        # 逐点从上往下进入，并把之前的要点往下挤
        for i, point in enumerate(points):
            # 创建新要点
            new_point = Text(f"• {point}", font_size=48, color=GREEN, weight=BOLD)
            new_point.move_to([0, 2.0, 0])  # 从顶部进入
            
            animations = []
            
            # 移动已有的点往下
            for j, existing_point in enumerate(displayed_points):
                target_y = 2.0 - (j + 1) * 1.0  # 增大间距
                animations.append(
                    existing_point.animate.move_to([0, target_y, 0])
                )
            
            # 新点从上方快速移入
            self.play(
                self.move_in_from_direction(new_point, "UP", 8, 0.6),
                *animations,
                rate_func=smooth,
                run_time=0.8
            )
            
            displayed_points.append(new_point)
            self.wait(0.3)
        
        self.wait(0.5)
        
        # 最终整体展示：所有要点以最终样式显示
        final_points = VGroup()
        for i, point in enumerate(points):
            point_text = Text(f"• {point}", font_size=42, color=WHITE, weight=BOLD)
            point_text.move_to([0, 2.0 - i * 1.0, 0])
            final_points.add(point_text)
        
        # Transform到最终样式
        displayed_group = VGroup(*displayed_points)
        self.play(
            Transform(displayed_group, final_points),
            title.animate.set_color(GOLD).scale(0.9),
            run_time=1.5,
            rate_func=smooth
        )
        self.wait(1.5)
        
        # 所有内容同时移出
        self.play(
            self.move_out_to_direction(title, "UP", 10, 0.6),
            self.move_out_to_direction(displayed_group, "DOWN", 12, 0.8),
            rate_func=smooth,
            run_time=0.8
        )
        # 确保完全移除
        self.remove(title, displayed_group)
    
    def scene_4_conclusion(self):
        """结束场景"""
        # 创建结束文字
        conclusion = Text("神奇移动效果演示完成", font_size=52, color=GOLD, weight=BOLD)
        conclusion.set_stroke(width=2, color=WHITE, opacity=0.8)
        conclusion.move_to([0, 0.4, 0])
        
        subtitle = Text("Magic Move Animation Demo", font_size=36, color=WHITE, weight=BOLD)
        subtitle.move_to([0, -0.6, 0])
        
        # 创建装饰效果 - 更多星星，确保不超出边界
        stars = VGroup()
        for i in range(25):
            star = Star(n=5, outer_radius=np.random.uniform(0.06, 0.12), 
                       color=random.choice([YELLOW, GOLD, WHITE, PINK]), 
                       fill_opacity=0.9)
            star.move_to([
                np.random.uniform(-5.5, 5.5),
                np.random.uniform(-2.8, 2.8),
                0
            ])
            stars.add(star)
        
        # 背景变化和所有内容同时从不同方向快速移入
        star_animations = []
        directions = ["UP", "DOWN", "LEFT", "RIGHT"]
        for i, star in enumerate(stars):
            direction = directions[i % 4]
            star_animations.append(
                self.move_in_from_direction(star, direction, np.random.uniform(8, 12), 0.8)
            )
        
        # 所有元素同时移入
        all_animations = [
            self.change_background([RED, ORANGE, YELLOW, GREEN, BLUE, PURPLE]),
            self.move_in_from_direction(conclusion, "LEFT", 15, 1),
            self.move_in_from_direction(subtitle, "RIGHT", 15, 1)
        ] + star_animations
        
        self.play(
            *all_animations,
            rate_func=smooth,
            run_time=1.8
        )
        
        # 星星闪烁效果
        for _ in range(3):
            self.play(
                stars.animate.set_opacity(0.3),
                run_time=0.3
            )
            self.play(
                stars.animate.set_opacity(1),
                run_time=0.3
            )
        
        self.wait(1.5)
        
        # 最终全部快速移出，同时背景变黑
        self.play(
            self.change_background([BLACK, BLACK, BLACK]),
            self.move_out_to_direction(conclusion, "UP", 12, 0.8),
            self.move_out_to_direction(subtitle, "DOWN", 12, 0.8),
            self.move_out_to_direction(stars, "RIGHT", 15, 1),
            rate_func=smooth,
            run_time=1.5
        )
        
        # 确保完全移除所有元素
        self.remove(conclusion, subtitle, stars)

# 如果直接运行此文件
if __name__ == "__main__":
    # 可以在这里添加测试代码
    pass