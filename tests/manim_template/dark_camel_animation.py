from manim import *
import numpy as np
import random

class DarkCamelStockAnalysis(Scene):
    def construct(self):
        # Set dark background
        self.camera.background_color = "#0a0a0a"
        
        # Create dynamic background with particle system
        particles, flow_lines = self.create_dynamic_background()
        self.add(particles, flow_lines)
        
        # Start continuous animations
        self.start_background_animations(particles, flow_lines)
        
        # Main content sequence with cinematic transitions
        self.cinematic_intro()
        self.agent_showcase()
        self.data_visualization()
        self.conclusion_sequence()
        
        # Clean up animations
        self.stop_background_animations(particles, flow_lines)
    
    def create_dynamic_background(self):
        # Create particle system - reduced number
        particles = VGroup()
        num_particles = 60  # Reduced from 80
        
        for i in range(num_particles):
            # Random positions across the screen, but avoid center area
            while True:
                x = random.uniform(-8, 8)
                y = random.uniform(-5, 5)
                # Keep particles away from center content area
                if abs(x) > 3 or abs(y) > 2.5:
                    break
            
            # Create glowing particles with different sizes
            size = random.uniform(0.02, 0.06)  # Slightly smaller
            particle = Circle(
                radius=size,
                fill_opacity=random.uniform(0.3, 0.6),  # Reduced opacity
                stroke_width=0,
                fill_color=random.choice(["#00ffff", "#ff6b6b", "#4ecdc4", "#45b7d1", "#96ceb4"])
            )
            particle.move_to([x, y, 0])
            
            # Add glow effect - reduced intensity
            glow = Circle(
                radius=size * 2.5,  # Reduced from 3
                fill_opacity=0.08,  # Reduced from 0.1
                stroke_width=0,
                fill_color=particle.fill_color
            ).move_to(particle.get_center())
            
            particle_group = VGroup(glow, particle)
            particles.add(particle_group)
        
        # Create flowing energy lines - reduced number
        flow_lines = VGroup()
        num_lines = 8  # Reduced from 12
        
        for i in range(num_lines):
            # Create curved paths across the screen
            start_angle = i * TAU / num_lines
            
            # Create flowing bezier curves
            start_point = 6 * np.array([np.cos(start_angle), np.sin(start_angle), 0])
            end_point = 6 * np.array([np.cos(start_angle + PI), np.sin(start_angle + PI), 0])
            
            # Control points for smooth curves
            control1 = np.array([random.uniform(-3, 3), random.uniform(-3, 3), 0])
            control2 = np.array([random.uniform(-3, 3), random.uniform(-3, 3), 0])
            
            curve = CubicBezier(
                start_point,
                control1,
                control2,
                end_point,
                stroke_width=1.2,  # Reduced from 1.5
                stroke_opacity=0.3,  # Reduced from 0.4
                stroke_color=random.choice(["#00ffff", "#ff6b6b", "#4ecdc4"])
            )
            
            # Add gradient effect
            curve.set_stroke(opacity=[0, 0.4, 0.4, 0])  # Reduced opacity
            flow_lines.add(curve)
        
        return particles, flow_lines
    
    def start_background_animations(self, particles, flow_lines):
        # Particle floating animation
        def float_particles(mob, dt):
            for particle_group in mob:
                particle = particle_group[1]  # The actual particle (not the glow)
                glow = particle_group[0]      # The glow effect
                
                # Floating motion
                current_pos = particle.get_center()
                new_x = current_pos[0] + np.sin(self.renderer.time * 0.5 + current_pos[1]) * dt * 0.3
                new_y = current_pos[1] + np.cos(self.renderer.time * 0.3 + current_pos[0]) * dt * 0.2
                
                # Wrap around screen edges
                if new_x > 8: new_x = -8
                if new_x < -8: new_x = 8
                if new_y > 5: new_y = -5
                if new_y < -5: new_y = 5
                
                new_pos = [new_x, new_y, 0]
                particle.move_to(new_pos)
                glow.move_to(new_pos)
                
                # Pulsing opacity
                base_opacity = 0.5
                pulse = 0.3 * np.sin(self.renderer.time * 2 + current_pos[0] + current_pos[1])
                particle.set_fill(opacity=base_opacity + pulse)
        
        # Flow lines animation
        def animate_flow_lines(mob, dt):
            for line in mob:
                # Shift stroke opacity to create flowing effect
                current_time = self.renderer.time
                phase = current_time * 2
                
                # Create traveling wave effect
                opacity_values = []
                for t in np.linspace(0, 1, 20):
                    wave = 0.5 * (1 + np.sin(phase + t * TAU * 2))
                    opacity_values.append(wave * 0.6)
                
                line.set_stroke(opacity=opacity_values)
        
        particles.add_updater(float_particles)
        flow_lines.add_updater(animate_flow_lines)
    
    def stop_background_animations(self, particles, flow_lines):
        particles.clear_updaters()
        flow_lines.clear_updaters()
    
    def cinematic_intro(self):
        # Create holographic title effect
        title_text = "AI股票分析实验"
        subtitle_text = "CAMEL Agent 深度测试"
        
        # Main title with glitch effect - increased font size
        title = Text(title_text, font_size=84, color="#00ffff")
        title.set_stroke(width=2, color="#ffffff", opacity=0.8)
        
        # Create glitch copies
        glitch1 = title.copy().set_color("#ff6b6b").shift(0.05 * LEFT + 0.02 * UP)
        glitch2 = title.copy().set_color("#4ecdc4").shift(0.05 * RIGHT + 0.02 * DOWN)
        
        title_group = VGroup(glitch2, glitch1, title)
        
        # Subtitle with typewriter effect - increased font size
        subtitle = Text(subtitle_text, font_size=42, color="#96ceb4")
        subtitle.next_to(title, DOWN, buff=1.0)
        
        # Ensure content stays within boundaries
        content_group = VGroup(title_group, subtitle)
        if content_group.width > config.frame_width - 1:
            content_group.scale((config.frame_width - 1) / content_group.width)
        if content_group.height > config.frame_height - 1:
            content_group.scale((config.frame_height - 1) / content_group.height)
        
        # Holographic frame
        frame = Rectangle(
            width=content_group.width + 2,
            height=content_group.height + 2,
            stroke_width=2,
            stroke_color="#00ffff",
            stroke_opacity=0.6,
            fill_opacity=0
        )
        frame.surround(content_group, buff=0.8)
        
        # Corner decorations
        corners = VGroup()
        corner_size = 0.5
        for corner in [frame.get_corner(UL), frame.get_corner(UR), 
                      frame.get_corner(DL), frame.get_corner(DR)]:
            corner_lines = VGroup(
                Line(corner, corner + corner_size * (RIGHT if corner[0] > 0 else LEFT), stroke_width=3, color="#00ffff"),
                Line(corner, corner + corner_size * (UP if corner[1] > 0 else DOWN), stroke_width=3, color="#00ffff")
            )
            corners.add(corner_lines)
        
        # Entrance animation
        self.play(
            DrawBorderThenFill(frame),
            Create(corners),
            run_time=1.5
        )
        
        # Glitch entrance for title
        self.play(
            FadeIn(glitch1, shift=0.1*LEFT),
            FadeIn(glitch2, shift=0.1*RIGHT),
            run_time=0.3
        )
        self.play(
            Write(title),
            run_time=1.5
        )
        
        # Typewriter effect for subtitle - fix disappearing issue
        subtitle_chars = []
        for i in range(len(subtitle_text)):
            partial_text = Text(subtitle_text[:i+1], font_size=42, color="#96ceb4")
            partial_text.next_to(title, DOWN, buff=1.0)
            subtitle_chars.append(partial_text)
            
            if i == 0:
                self.add(partial_text)
            else:
                self.remove(subtitle_chars[i-1])
                self.add(partial_text)
            self.wait(0.08)
        
        # Keep the final subtitle for proper fadeout
        final_subtitle = subtitle_chars[-1]
        
        self.wait(1)
        
        # Exit with dissolve effect - include final subtitle
        self.play(
            FadeOut(title_group, shift=UP),
            FadeOut(final_subtitle, shift=UP),
            FadeOut(frame),
            FadeOut(corners),
            run_time=1.5
        )
    
    def agent_showcase(self):
        # Create AI agent visualization
        center = ORIGIN
        
        # Central AI core
        core = Circle(
            radius=0.8,
            fill_opacity=0.3,
            stroke_width=3,
            stroke_color="#00ffff",
            fill_color="#001122"
        )
        
        # Pulsing inner core
        inner_core = Circle(
            radius=0.4,
            fill_opacity=0.6,
            stroke_width=0,
            fill_color="#00ffff"
        )
        
        # Data streams - reduced number to avoid clutter
        streams = VGroup()
        num_streams = 6
        
        for i in range(num_streams):
            angle = i * TAU / num_streams
            
            # Create data points flowing toward center
            stream_points = VGroup()
            for j in range(4):  # Reduced from 5 to 4
                distance = 2.5 + j * 0.6  # Reduced distance
                point_pos = distance * np.array([np.cos(angle), np.sin(angle), 0])
                
                point = Circle(
                    radius=0.08,
                    fill_opacity=0.8,
                    stroke_width=1,
                    stroke_color="#4ecdc4",
                    fill_color="#4ecdc4"
                ).move_to(point_pos)
                
                stream_points.add(point)
            
            streams.add(stream_points)
        
        # Labels for different data types - reduced and repositioned
        labels = [
            "股价数据", "财务指标", "市场趋势", 
            "技术分析", "基本面", "风险评估"
        ]
        
        label_objects = VGroup()
        for i, label in enumerate(labels):
            angle = i * TAU / len(labels)
            # Reduced radius to keep within boundaries
            label_pos = 3.5 * np.array([np.cos(angle), np.sin(angle), 0])
            
            # Increased font size
            label_obj = Text(label, font_size=32, color="#96ceb4")
            label_obj.move_to(label_pos)
            
            # Ensure labels stay within screen boundaries
            if abs(label_obj.get_x()) > config.frame_width/2 - 1:
                label_obj.set_x(np.sign(label_obj.get_x()) * (config.frame_width/2 - 1))
            if abs(label_obj.get_y()) > config.frame_height/2 - 1:
                label_obj.set_y(np.sign(label_obj.get_y()) * (config.frame_height/2 - 1))
            
            label_objects.add(label_obj)
        
        # Animation sequence
        self.play(
            DrawBorderThenFill(core),
            FadeIn(inner_core),
            run_time=1
        )
        
        # Add pulsing animation to core - reduced intensity
        def pulse_core(mob, dt):
            scale_factor = 1 + 0.1 * np.sin(self.renderer.time * 3)  # Reduced from 0.2 to 0.1
            mob.scale(scale_factor / getattr(mob, 'scale_factor', 1))
            mob.scale_factor = scale_factor
        
        inner_core.add_updater(pulse_core)
        
        # Show data streams with better spacing
        for i, (stream, label) in enumerate(zip(streams, label_objects)):
            self.play(
                FadeIn(label),
                *[FadeIn(point, shift=0.2*LEFT) for point in stream],  # Reduced shift
                run_time=0.4  # Faster animation
            )
        
        # Animate data flowing to center - slower movement
        def flow_to_center(mob, dt):
            for stream in mob:
                for point in stream:
                    current_pos = point.get_center()
                    direction_to_center = -current_pos / np.linalg.norm(current_pos) if np.linalg.norm(current_pos) > 0.1 else np.array([0, 0, 0])
                    new_pos = current_pos + direction_to_center * dt * 1.5  # Reduced speed
                    
                    # Reset if too close to center
                    if np.linalg.norm(new_pos) < 1:
                        angle = np.arctan2(current_pos[1], current_pos[0])
                        new_pos = 4 * np.array([np.cos(angle), np.sin(angle), 0])  # Reduced reset distance
                    
                    point.move_to(new_pos)
        
        streams.add_updater(flow_to_center)
        
        self.wait(3)
        
        # Clean up
        inner_core.clear_updaters()
        streams.clear_updaters()
        
        self.play(
            FadeOut(core),
            FadeOut(inner_core),
            FadeOut(streams),
            FadeOut(label_objects),
            run_time=1.5
        )
    
    def data_visualization(self):
        # Create dynamic data comparison
        title = Text("实时数据对比", font_size=56, color="#00ffff")  # Increased font size
        title.to_edge(UP, buff=1.2)  # More buffer from top
        
        self.play(Write(title), run_time=1)
        
        # Stock symbols - increased font sizes
        aapl_symbol = Text("AAPL", font_size=72, color="#ff6b6b")
        nvda_symbol = Text("NVDA", font_size=72, color="#4ecdc4")
        
        vs_text = Text("VS", font_size=48, color="#ffffff")
        
        symbols_group = VGroup(aapl_symbol, vs_text, nvda_symbol)
        symbols_group.arrange(RIGHT, buff=1.8)  # More spacing
        symbols_group.move_to(UP * 0.8)  # Adjusted position to avoid overlap
        
        # Ensure symbols stay within boundaries
        if symbols_group.width > config.frame_width - 1:
            symbols_group.scale((config.frame_width - 1) / symbols_group.width)
        
        self.play(
            FadeIn(aapl_symbol, shift=LEFT),
            Write(vs_text),
            FadeIn(nvda_symbol, shift=RIGHT),
            run_time=1.5
        )
        
        # Animated data bars - reduced number of metrics to prevent overlap
        metrics = ["股价", "市值", "P/E"]  # Reduced from 4 to 3 metrics
        aapl_values = [210.20, 3.5, 37.29]
        nvda_values = [134.98, 2.91, 39.90]
        
        # Normalize values for visualization
        max_vals = [max(a, n) for a, n in zip(aapl_values, nvda_values)]
        aapl_normalized = [a/m for a, m in zip(aapl_values, max_vals)]
        nvda_normalized = [n/m for n, m in zip(nvda_values, max_vals)]
        
        bars_group = VGroup()
        
        for i, (metric, aapl_norm, nvda_norm, aapl_val, nvda_val) in enumerate(
            zip(metrics, aapl_normalized, nvda_normalized, aapl_values, nvda_values)
        ):
            # Metric label - increased font size
            label = Text(metric, font_size=36, color="#96ceb4")
            label.move_to(LEFT * 3.5 + DOWN * (i * 1.8 - 1.5))  # Better spacing
            
            # AAPL bar - adjusted size
            aapl_bar = Rectangle(
                width=aapl_norm * 2.5,  # Reduced width to fit better
                height=0.4,  # Increased height
                fill_opacity=0.8,
                stroke_width=2,
                stroke_color="#ff6b6b",
                fill_color="#ff6b6b"
            )
            aapl_bar.next_to(label, RIGHT, buff=0.6, aligned_edge=LEFT)
            
            # NVDA bar - adjusted size
            nvda_bar = Rectangle(
                width=nvda_norm * 2.5,  # Reduced width to fit better
                height=0.4,  # Increased height
                fill_opacity=0.8,
                stroke_width=2,
                stroke_color="#4ecdc4",
                fill_color="#4ecdc4"
            )
            nvda_bar.next_to(aapl_bar, DOWN, buff=0.2, aligned_edge=LEFT)
            
            # Value labels - increased font size
            aapl_value_label = Text(f"{aapl_val}", font_size=28, color="#ffffff")
            aapl_value_label.next_to(aapl_bar, RIGHT, buff=0.3)
            
            nvda_value_label = Text(f"{nvda_val}", font_size=28, color="#ffffff")
            nvda_value_label.next_to(nvda_bar, RIGHT, buff=0.3)
            
            metric_group = VGroup(label, aapl_bar, nvda_bar, aapl_value_label, nvda_value_label)
            
            # Ensure the entire group stays within boundaries
            if metric_group.get_right()[0] > config.frame_width/2 - 0.5:
                scale_factor = (config.frame_width/2 - 0.5 - metric_group.get_left()[0]) / metric_group.width
                metric_group.scale(scale_factor)
            
            bars_group.add(metric_group)
        
        # Animate bars growing
        for metric_group in bars_group:
            label, aapl_bar, nvda_bar, aapl_val, nvda_val = metric_group
            
            # Start with zero width
            original_aapl_width = aapl_bar.width
            original_nvda_width = nvda_bar.width
            
            aapl_bar.stretch(0.01, 0, about_edge=LEFT)
            nvda_bar.stretch(0.01, 0, about_edge=LEFT)
            
            self.play(
                Write(label),
                run_time=0.6
            )
            
            self.play(
                aapl_bar.animate.stretch(original_aapl_width/0.01, 0, about_edge=LEFT),
                nvda_bar.animate.stretch(original_nvda_width/0.01, 0, about_edge=LEFT),
                FadeIn(aapl_val),
                FadeIn(nvda_val),
                run_time=1.2
            )
        
        self.wait(2)
        
        # Exit animation
        self.play(
            FadeOut(title),
            FadeOut(symbols_group),
            FadeOut(bars_group),
            run_time=1.5
        )
    
    def conclusion_sequence(self):
        # Final holographic conclusion
        conclusion_text = "AI驱动的投资未来"
        
        # Create 3D-like text effect - increased font size
        main_text = Text(conclusion_text, font_size=64, color="#00ffff")
        shadow_text = main_text.copy().set_color("#001122").shift(0.1*DOWN + 0.1*RIGHT)
        
        text_group = VGroup(shadow_text, main_text)
        
        # Ensure text stays within boundaries
        if text_group.width > config.frame_width - 1:
            text_group.scale((config.frame_width - 1) / text_group.width)
        
        # Surrounding energy field - reduced size to prevent overlap
        energy_rings = VGroup()
        for i in range(3):
            ring = Circle(
                radius=1.5 + i * 0.6,  # Reduced radius
                stroke_width=2,
                stroke_opacity=0.4,
                stroke_color=["#00ffff", "#4ecdc4", "#96ceb4"][i],
                fill_opacity=0
            )
            energy_rings.add(ring)
        
        # Key points orbiting around - reduced number and repositioned
        key_points = [
            "自动化分析", "实时数据", "智能决策"  # Reduced from 4 to 3
        ]
        
        orbiting_points = VGroup()
        for i, point in enumerate(key_points):
            angle = i * TAU / len(key_points)
            # Increased font size
            point_obj = Text(point, font_size=32, color="#96ceb4")
            
            # Position on orbit - reduced radius to stay within boundaries
            radius = 3.2
            point_obj.move_to(radius * np.array([np.cos(angle), np.sin(angle), 0]))
            
            # Ensure orbiting points stay within boundaries
            if abs(point_obj.get_x()) > config.frame_width/2 - 0.8:
                point_obj.set_x(np.sign(point_obj.get_x()) * (config.frame_width/2 - 0.8))
            if abs(point_obj.get_y()) > config.frame_height/2 - 0.8:
                point_obj.set_y(np.sign(point_obj.get_y()) * (config.frame_height/2 - 0.8))
            
            orbiting_points.add(point_obj)
        
        # Animation
        self.play(
            Write(text_group),
            *[Create(ring) for ring in energy_rings],
            run_time=2
        )
        
        # Add orbiting animation with boundary constraints
        def orbit_points(mob, dt):
            for i, point in enumerate(mob):
                current_angle = np.arctan2(point.get_y(), point.get_x())
                new_angle = current_angle + dt * 0.3  # Reduced speed
                radius = 3.2
                new_pos = radius * np.array([np.cos(new_angle), np.sin(new_angle), 0])
                
                # Constrain to boundaries
                if abs(new_pos[0]) > config.frame_width/2 - 0.8:
                    new_pos[0] = np.sign(new_pos[0]) * (config.frame_width/2 - 0.8)
                if abs(new_pos[1]) > config.frame_height/2 - 0.8:
                    new_pos[1] = np.sign(new_pos[1]) * (config.frame_height/2 - 0.8)
                
                point.move_to(new_pos)
        
        orbiting_points.add_updater(orbit_points)
        
        self.play(
            *[FadeIn(point, shift=0.3*UP) for point in orbiting_points],
            run_time=1.5
        )
        
        # Pulsing energy rings - reduced intensity
        def pulse_rings(mob, dt):
            for i, ring in enumerate(mob):
                scale_factor = 1 + 0.05 * np.sin(self.renderer.time * 2 + i * PI/3)  # Reduced from 0.1
                ring.scale(scale_factor / getattr(ring, 'last_scale', 1))
                ring.last_scale = scale_factor
        
        energy_rings.add_updater(pulse_rings)
        
        self.wait(4)
        
        # Final fade out
        orbiting_points.clear_updaters()
        energy_rings.clear_updaters()
        
        self.play(
            FadeOut(text_group),
            FadeOut(energy_rings),
            FadeOut(orbiting_points),
            run_time=2
        )

if __name__ == "__main__":
    # Command to run: manim -pql dark_camel_animation.py DarkCamelStockAnalysis
    pass 