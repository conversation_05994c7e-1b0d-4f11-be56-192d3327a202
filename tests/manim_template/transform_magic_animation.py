from manim import *
import numpy as np
import random

class TransformMagicAnimation(Scene):
    def construct(self):
        # 统一配色方案
        # 背景色：深色系（BLUE_E, PURPLE_E, GREEN_E）
        # 主文字：WHITE（白色）
        # 强调文字：YELLOW（黄色）
        
        # 初始背景
        self.current_background = Rectangle(
            width=config.frame_width,
            height=config.frame_height,
            fill_color=BLUE_E,
            fill_opacity=1,
            stroke_width=0
        )
        self.add(self.current_background)
        
        # 场景1：标题介绍
        self.scene_1_title()
        
        # 场景2：文字Transform
        self.scene_2_text_transform()
        
        # 场景3：卡片展示
        self.scene_3_card_display()
        
        # 结束场景
        self.scene_4_conclusion()
    
    def fade_background_transition(self, new_color):
        """淡入淡出背景切换"""
        new_bg = Rectangle(
            width=config.frame_width,
            height=config.frame_height,
            fill_color=new_color,
            fill_opacity=1,
            stroke_width=0
        )
        return Transform(self.current_background, new_bg)
    
    def push_background_transition(self, new_color, direction=RIGHT):
        """推镜背景切换"""
        new_bg = Rectangle(
            width=config.frame_width,
            height=config.frame_height,
            fill_color=new_color,
            fill_opacity=1,
            stroke_width=0
        )
        new_bg.shift(direction * config.frame_width)
        
        # 创建推进效果
        old_bg_target = self.current_background.copy().shift(-direction * config.frame_width)
        new_bg_start = new_bg.copy()
        new_bg.shift(-direction * config.frame_width)
        
        return [
            Transform(self.current_background, old_bg_target),
            Transform(new_bg_start, new_bg)
        ]
    
    def blur_background_transition(self, new_color):
        """模糊过渡背景切换"""
        new_bg = Rectangle(
            width=config.frame_width,
            height=config.frame_height,
            fill_color=new_color,
            fill_opacity=1,
            stroke_width=0
        )
        return Transform(self.current_background, new_bg)
    
    def mask_background_transition(self, new_color):
        """遮罩动画背景切换"""
        # 创建圆形遮罩
        mask = Circle(radius=0.1, fill_color=new_color, fill_opacity=1, stroke_width=0)
        mask.move_to(ORIGIN)
        
        new_bg = Rectangle(
            width=config.frame_width,
            height=config.frame_height,
            fill_color=new_color,
            fill_opacity=1,
            stroke_width=0
        )
        
        # 遮罩扩展效果
        expanded_mask = Circle(radius=10, fill_color=new_color, fill_opacity=1, stroke_width=0)
        expanded_mask.move_to(ORIGIN)
        
        return [
            Transform(mask, expanded_mask, run_time=0.4),
            Transform(self.current_background, new_bg, run_time=0.1)
        ]
    
    def scene_1_title(self):
        """场景1：标题介绍"""
        # 主标题 - 居中合理大小
        title = Text("Transform动画", font_size=72, color=YELLOW, weight=BOLD)
        title.move_to([0, 0.5, 0])
        
        # 副标题
        subtitle = Text("Manim变换演示", font_size=48, color=WHITE, weight=BOLD)
        subtitle.move_to([0, -0.8, 0])
        
        # 简单装饰线
        decoration = Line([-3, 0, 0], [3, 0, 0], stroke_width=4, color=YELLOW)
        
        title_group = VGroup(title, subtitle, decoration)
        empty_group = VGroup()
        
        # 背景切换到紫色 + 标题Transform出现
        self.play(
            self.fade_background_transition(PURPLE_E),
            Transform(empty_group, title_group),
            run_time=0.5
        )
        self.wait(0.8)
        # 在wait之后，下一个play之前remove
        self.remove(empty_group)
        
        # Transform到空组退出
        next_scene_init = VGroup()
        self.play(
            Transform(title_group, next_scene_init),
            run_time=0.5
        )
        # 立即remove标题组
        self.remove(title_group)
    
    def scene_2_text_transform(self):
        """场景2：文字Transform效果"""
        # 四个关键词 - 合理分布
        keywords = ["创新", "技术", "未来", "发展"]
        
        # 初始四角分布
        text1 = Text(keywords[0], font_size=56, color=WHITE, weight=BOLD)
        text1.move_to([-2.5, 1.5, 0])
        text2 = Text(keywords[1], font_size=56, color=WHITE, weight=BOLD)
        text2.move_to([2.5, 1.5, 0])
        text3 = Text(keywords[2], font_size=56, color=WHITE, weight=BOLD)
        text3.move_to([-2.5, -1.5, 0])
        text4 = Text(keywords[3], font_size=56, color=WHITE, weight=BOLD)
        text4.move_to([2.5, -1.5, 0])
        
        scattered_group = VGroup(text1, text2, text3, text4)
        empty_start = VGroup()
        
        # 推进切换到绿色背景 + 文字Transform出现
        push_animations = self.push_background_transition(GREEN_E, LEFT)
        self.play(
            *push_animations,
            Transform(empty_start, scattered_group),
            run_time=0.5
        )
        self.wait(0.3)
        # 在wait之后，下一个play之前remove
        self.remove(empty_start)
        
        # Transform到水平一行
        h_text1 = Text(keywords[0], font_size=64, color=YELLOW, weight=BOLD)
        h_text1.move_to([-3, 0, 0])
        h_text2 = Text(keywords[1], font_size=64, color=YELLOW, weight=BOLD)
        h_text2.move_to([-1, 0, 0])
        h_text3 = Text(keywords[2], font_size=64, color=YELLOW, weight=BOLD)
        h_text3.move_to([1, 0, 0])
        h_text4 = Text(keywords[3], font_size=64, color=YELLOW, weight=BOLD)
        h_text4.move_to([3, 0, 0])
        
        horizontal_group = VGroup(h_text1, h_text2, h_text3, h_text4)
        
        self.play(
            Transform(scattered_group, horizontal_group),
            run_time=0.5
        )
        self.wait(0.3)
        
        # Transform到垂直列  
        v_text1 = Text(keywords[0], font_size=60, color=WHITE, weight=BOLD)
        v_text1.move_to([0, 1.8, 0])
        v_text2 = Text(keywords[1], font_size=60, color=WHITE, weight=BOLD)
        v_text2.move_to([0, 0.6, 0])
        v_text3 = Text(keywords[2], font_size=60, color=WHITE, weight=BOLD)
        v_text3.move_to([0, -0.6, 0])
        v_text4 = Text(keywords[3], font_size=60, color=WHITE, weight=BOLD)
        v_text4.move_to([0, -1.8, 0])
        
        vertical_group = VGroup(v_text1, v_text2, v_text3, v_text4)
        
        # 模糊过渡到蓝色背景
        blur_animation = self.blur_background_transition(BLUE_E)
        self.play(
            blur_animation,
            Transform(scattered_group, vertical_group),
            run_time=0.5
        )
        self.wait(0.3)
        
        # 最终合并成短语 - 分两行显示
        final_line1 = Text("创新技术", font_size=64, color=YELLOW, weight=BOLD)
        final_line1.move_to([0, 0.4, 0])
        final_line2 = Text("引领未来", font_size=64, color=YELLOW, weight=BOLD)
        final_line2.move_to([0, -0.4, 0])
        
        final_phrase = VGroup(final_line1, final_line2)
        
        self.play(
            Transform(scattered_group, final_phrase),
            run_time=0.5
        )
        self.wait(0.5)
        
        # Transform到空组退出
        next_prep = VGroup()
        self.play(
            Transform(scattered_group, next_prep),
            run_time=0.5
        )
        # 立即remove最终组
        self.remove(scattered_group)
    
    def scene_3_card_display(self):
        """场景3：卡片式展示 - 重新优化版本"""
        # 科技领域数据
        tech_areas = ["人工智能", "物联网", "区块链", "5G网络"]
        
        # ===== 第一阶段：标题出现 =====
        main_title = Text("科技领域", font_size=72, color=YELLOW, weight=BOLD)
        main_title.move_to([0, 2.8, 0])
        
        empty_init = VGroup()
        self.play(
            self.mask_background_transition(PURPLE_E),
            Transform(empty_init, VGroup(main_title)),
            run_time=0.5
        )
        self.wait(0.3)
        self.remove(empty_init)
        
        # ===== 第二阶段：卡片网格一起出现 =====
        # 2x2网格位置
        grid_positions = [
            [-3, 1, 0],   # 左上
            [3, 1, 0],    # 右上  
            [-3, -1, 0],  # 左下
            [3, -1, 0]    # 右下
        ]
        
        # 创建完整的网格布局 - 所有卡片一起出现
        grid_layout = VGroup()
        grid_layout.add(main_title.copy())  # 标题
        
        # 创建所有四张卡片
        for i, (area, pos) in enumerate(zip(tech_areas, grid_positions)):
            # 创建卡片
            card_bg = RoundedRectangle(
                width=4, height=1.4, 
                corner_radius=0.25,
                fill_color=WHITE,
                fill_opacity=0.08,
                stroke_color=WHITE,
                stroke_width=3
            )
            card_bg.move_to(pos)
            
            # 卡片图标（用小圆圈表示）
            icon = Circle(radius=0.15, fill_color=YELLOW, fill_opacity=1, stroke_width=0)
            icon.move_to([pos[0] - 1.2, pos[1], 0])
            
            # 卡片文字
            card_text = Text(area, font_size=38, color=WHITE, weight=BOLD)
            card_text.move_to([pos[0] + 0.2, pos[1], 0])
            
            card = VGroup(card_bg, icon, card_text)
            grid_layout.add(card)
        
        # 四张卡片一起Transform出现
        current_display = VGroup(main_title.copy())
        self.play(
            self.fade_background_transition(GREEN_E),
            Transform(current_display, grid_layout),
            run_time=0.5
        )
        self.wait(0.6)
        
        # ===== 第三阶段：标题变换 =====
        # 先变换标题
        evolution_title = Text("技术融合", font_size=68, color=YELLOW, weight=BOLD)
        evolution_title.move_to([0, 2.5, 0])
        
        title_change_layout = VGroup()
        title_change_layout.add(evolution_title)
        
        # 保持原有卡片
        for j in range(4):
            existing_card_bg = RoundedRectangle(
                width=4, height=1.4, 
                corner_radius=0.25,
                fill_color=WHITE,
                fill_opacity=0.08,
                stroke_color=WHITE,
                stroke_width=3
            )
            existing_card_bg.move_to(grid_positions[j])
            
            existing_icon = Circle(radius=0.15, fill_color=YELLOW, fill_opacity=1, stroke_width=0)
            existing_icon.move_to([grid_positions[j][0] - 1.2, grid_positions[j][1], 0])
            
            existing_text = Text(tech_areas[j], font_size=38, color=WHITE, weight=BOLD)
            existing_text.move_to([grid_positions[j][0] + 0.2, grid_positions[j][1], 0])
            
            existing_card = VGroup(existing_card_bg, existing_icon, existing_text)
            title_change_layout.add(existing_card)
        
        self.play(
            Transform(current_display, title_change_layout),
            run_time=0.5
        )
        self.wait(0.4)
        
        # ===== 第四阶段：卡片重排为水平布局 =====
        horizontal_positions = [[-4.8, 0.2, 0], [-1.6, 0.2, 0], [1.6, 0.2, 0], [4.8, 0.2, 0]]
        
        horizontal_layout = VGroup()
        horizontal_layout.add(evolution_title.copy())
        
        for i, (area, pos) in enumerate(zip(tech_areas, horizontal_positions)):
            # 水平卡片 - 更紧凑的设计
            h_card_bg = RoundedRectangle(
                width=2.8, height=1.2, 
                corner_radius=0.2,
                fill_color=YELLOW,
                fill_opacity=0.15,
                stroke_color=YELLOW,
                stroke_width=2
            )
            h_card_bg.move_to(pos)
            
            # 更小的图标
            h_icon = Circle(radius=0.1, fill_color=WHITE, fill_opacity=1, stroke_width=0)
            h_icon.move_to([pos[0], pos[1] + 0.35, 0])
            
            # 水平文字
            h_text = Text(area, font_size=28, color=WHITE, weight=BOLD)
            h_text.move_to([pos[0], pos[1] - 0.2, 0])
            
            h_card = VGroup(h_card_bg, h_icon, h_text)
            horizontal_layout.add(h_card)
        
        self.play(
            self.blur_background_transition(GREEN_E),
            Transform(current_display, horizontal_layout),
            run_time=0.5
        )
        self.wait(0.8)
        
        # ===== 第五阶段：最终聚合效果 =====
        # 卡片聚合成一个大卡片
        final_card_bg = RoundedRectangle(
            width=8, height=2.5, 
            corner_radius=0.3,
            fill_color=YELLOW,
            fill_opacity=0.2,
            stroke_color=YELLOW,
            stroke_width=3
        )
        final_card_bg.move_to([0, 0, 0])
        
        final_title = Text("智能科技生态", font_size=48, color=WHITE, weight=BOLD)
        final_title.move_to([0, 0.5, 0])
        
        # 四个小标签
        tags = VGroup()
        tag_texts = ["AI", "IoT", "区块链", "5G"]
        tag_positions = [[-2.5, -0.5, 0], [-0.8, -0.5, 0], [0.8, -0.5, 0], [2.5, -0.5, 0]]
        
        for tag_text, tag_pos in zip(tag_texts, tag_positions):
            tag_bg = RoundedRectangle(
                width=1.2, height=0.4,
                corner_radius=0.1,
                fill_color=WHITE,
                fill_opacity=0.3,
                stroke_width=0
            )
            tag_bg.move_to(tag_pos)
            
            tag_label = Text(tag_text, font_size=20, color=YELLOW, weight=BOLD)
            tag_label.move_to(tag_pos)
            
            tag = VGroup(tag_bg, tag_label)
            tags.add(tag)
        
        final_layout = VGroup(
            evolution_title.copy().move_to([0, 2.5, 0]),
            final_card_bg,
            final_title,
            tags
        )
        
        self.play(
            Transform(current_display, final_layout),
            run_time=0.5
        )
        self.wait(1)
        
        # ===== 退出动画 =====
        exit_empty = VGroup()
        self.play(
            self.fade_background_transition(BLACK),
            Transform(current_display, exit_empty),
            run_time=0.5
        )
        self.remove(current_display)
    
    def scene_4_conclusion(self):
        """结束场景"""
        # 结束文字 - 简洁明了
        conclusion = Text("演示完成", font_size=72, color=YELLOW, weight=BOLD)
        conclusion.move_to([0, 0.5, 0])
        
        # 英文副标题
        subtitle = Text("Transform Demo", font_size=48, color=WHITE, weight=BOLD)
        subtitle.move_to([0, -0.8, 0])
        
        # 简化装饰 - 只用几个星星
        stars = VGroup()
        star_positions = [[-3, 1.5, 0], [3, 1.5, 0], [-3, -1.5, 0], [3, -1.5, 0]]
        
        for pos in star_positions:
            star = Star(n=5, outer_radius=0.12, color=YELLOW, fill_opacity=1)
            star.move_to(pos)
            stars.add(star)
        
        conclusion_group = VGroup(conclusion, subtitle, stars)
        empty_final = VGroup()
        
        # 推进切换到绿色背景 + Transform出现
        final_push = self.push_background_transition(GREEN_E, UP)
        self.play(
            *final_push,
            Transform(empty_final, conclusion_group),
            run_time=0.5
        )
        # 闪烁之前remove空组
        self.remove(empty_final)
        
        # 星星闪烁效果 - 用Transform
        dim_stars_group = VGroup(
            conclusion.copy(),
            subtitle.copy(),
            VGroup(*[star.copy().set_opacity(0.3) for star in stars])
        )
        
        for _ in range(2):
            self.play(
                Transform(conclusion_group, dim_stars_group),
                run_time=0.25
            )
            bright_stars_group = VGroup(
                conclusion.copy(),
                subtitle.copy(),
                VGroup(*[star.copy().set_opacity(1) for star in stars])
            )
            self.play(
                Transform(conclusion_group, bright_stars_group),
                run_time=0.25
            )
        
        self.wait(1)
        
        # 最终Transform到空组 + 背景变黑
        final_empty = VGroup()
        self.play(
            self.fade_background_transition(BLACK),
            Transform(conclusion_group, final_empty),
            run_time=0.5
        )
        # 立即remove结论组
        self.remove(conclusion_group)
        
        self.wait(0.5)

# 测试代码
if __name__ == "__main__":
    pass 