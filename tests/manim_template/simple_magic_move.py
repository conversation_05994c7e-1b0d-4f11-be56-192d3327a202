from manim import *
import numpy as np

class SimpleMagicMove(Scene):
    def construct(self):
        # 设置背景
        self.camera.background_color = "#f8f9fa"
        
        # 演示1：文字神奇移动
        self.demo_text_magic_move()
        
        # 演示2：表格神奇移动
        self.demo_table_magic_move()
        
        # 演示3：布局重组
        self.demo_layout_reorganization()
    
    def demo_text_magic_move(self):
        """演示文字的神奇移动效果"""
        # 标题
        title = Text("文字神奇移动", font_size=40, color=BLUE_D)
        title.to_edge(UP, buff=1)
        self.play(Write(title))
        
        # 初始状态：分散的单词
        words = [
            Text("机器", font_size=36, color=RED),
            Text("学习", font_size=36, color=GREEN), 
            Text("改变", font_size=36, color=BLUE),
            Text("未来", font_size=36, color=PURPLE)
        ]
        
        # 随机分布
        positions = [
            [-4, 1, 0], [4, 1, 0], [-4, -1, 0], [4, -1, 0]
        ]
        
        for word, pos in zip(words, positions):
            word.move_to(pos)
        
        # 显示分散的文字
        self.play(*[FadeIn(word) for word in words])
        self.wait(1)
        
        # 神奇移动：聚合成一行
        line_positions = [[-2.5, 0, 0], [-0.8, 0, 0], [0.8, 0, 0], [2.5, 0, 0]]
        
        self.play(
            *[word.animate.move_to(pos).scale(1.2) for word, pos in zip(words, line_positions)],
            run_time=2,
            rate_func=smooth
        )
        self.wait(1)
        
        # 变成完整句子
        sentence = Text("机器学习改变未来", font_size=42, color=GOLD)
        sentence.move_to([0, 0, 0])
        
        self.play(
            Transform(VGroup(*words), sentence),
            run_time=2,
            rate_func=smooth
        )
        self.wait(1)
        
        # 清除
        self.play(FadeOut(VGroup(title, *words)))
    
    def demo_table_magic_move(self):
        """演示表格的神奇移动效果"""
        # 标题
        title = Text("表格神奇移动", font_size=40, color=BLUE_D)
        title.to_edge(UP, buff=1)
        self.play(Write(title))
        
        # 原始表格：简单的3x3表格
        original_data = [
            ["产品", "价格", "库存"],
            ["手机", "5000", "100"],
            ["电脑", "8000", "50"]
        ]
        
        original_table = self.create_simple_table(original_data)
        original_table.move_to([-2, 0, 0])
        
        self.play(Create(original_table))
        self.wait(1)
        
        # 目标表格：重新排列和格式化
        new_data = [
            ["库存报告", "", ""],
            ["产品", "库存", "价格"],
            ["手机", "100台", "¥5000"],
            ["电脑", "50台", "¥8000"],
            ["总计", "150台", "¥13000"]
        ]
        
        new_table = self.create_simple_table(new_data, highlight_header=True)
        new_table.move_to([2, 0, 0])
        
        # 神奇移动变换
        self.play(
            Transform(original_table, new_table),
            run_time=3,
            rate_func=smooth
        )
        self.wait(2)
        
        # 清除
        self.play(FadeOut(VGroup(title, original_table)))
    
    def demo_layout_reorganization(self):
        """演示整体布局的重组"""
        # 标题
        title = Text("布局重组演示", font_size=40, color=BLUE_D)
        title.move_to([0, 3, 0])
        
        # 左侧：要点列表
        bullet_points = VGroup(
            Text("• 人工智能", font_size=28, color=GREEN),
            Text("• 大数据分析", font_size=28, color=GREEN),
            Text("• 云计算服务", font_size=28, color=GREEN)
        )
        bullet_points.arrange(DOWN, aligned_edge=LEFT, buff=0.3)
        bullet_points.move_to([-3, 0, 0])
        
        # 右侧：数据表格
        data_table = self.create_simple_table([
            ["技术", "成熟度"],
            ["AI", "85%"],
            ["大数据", "90%"],
            ["云计算", "95%"]
        ])
        data_table.move_to([3, 0, 0])
        
        # 底部：总结
        summary = Text("技术发展趋势良好", font_size=32, color=PURPLE)
        summary.move_to([0, -2.5, 0])
        
        # 显示初始布局
        self.play(
            Write(title),
            Create(bullet_points),
            Create(data_table),
            Write(summary)
        )
        self.wait(2)
        
        # 神奇移动：重新组织布局
        # 标题移到左上角
        new_title = title.copy().scale(0.7).move_to([-4, 2.5, 0])
        
        # 要点移到中央上方
        new_bullets = bullet_points.copy().move_to([0, 1, 0])
        
        # 表格移到中央下方
        new_table = data_table.copy().scale(0.8).move_to([0, -1, 0])
        
        # 总结移到右下角
        new_summary = summary.copy().scale(0.8).move_to([3, -2.5, 0])
        
        # 执行神奇移动
        self.play(
            Transform(title, new_title),
            Transform(bullet_points, new_bullets),
            Transform(data_table, new_table),
            Transform(summary, new_summary),
            run_time=3,
            rate_func=smooth
        )
        self.wait(2)
        
        # 最终效果：添加连接线
        connecting_lines = VGroup(
            Line(new_bullets.get_bottom(), new_table.get_top(), stroke_width=2, color=YELLOW),
            Line(new_table.get_right(), new_summary.get_left(), stroke_width=2, color=YELLOW)
        )
        
        self.play(Create(connecting_lines))
        self.wait(2)
        
        # 结束
        end_text = Text("神奇移动演示完成！", font_size=36, color=GOLD)
        end_text.move_to([0, 0, 0])
        
        self.play(
            FadeOut(VGroup(title, bullet_points, data_table, summary, connecting_lines)),
            FadeIn(end_text)
        )
        self.wait(2)
        self.play(FadeOut(end_text))
    
    def create_simple_table(self, data, highlight_header=False):
        """创建简单表格"""
        table = VGroup()
        
        for i, row in enumerate(data):
            row_group = VGroup()
            for j, cell_text in enumerate(row):
                # 文字颜色
                text_color = BLUE_D if (highlight_header and i == 0) else BLACK
                if highlight_header and i == len(data) - 1:  # 最后一行
                    text_color = RED_D
                
                # 创建单元格
                cell = Rectangle(
                    width=1.8,
                    height=0.5,
                    stroke_width=1,
                    stroke_color=GRAY,
                    fill_opacity=0.1 if i == 0 else 0,
                    fill_color=BLUE_A if i == 0 else WHITE
                )
                
                text = Text(cell_text, font_size=20, color=text_color)
                
                cell_group = VGroup(cell, text)
                cell_group.move_to([j * 1.8, 0, 0])
                row_group.add(cell_group)
            
            row_group.move_to([0, -i * 0.5, 0])
            table.add(row_group)
        
        return table

# 运行场景的辅助函数
if __name__ == "__main__":
    # 可以用于测试
    pass 