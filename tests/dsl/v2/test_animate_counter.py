# tests/dsl/v2/test_animate_counter.py
import sys

from loguru import logger

from dsl.v2.ast_nodes import AnimateCounterNode, MetadataNode, SceneNode
from dsl.v2.code_generator import CodeGenerator

# 确保 loguru 配置
if not logger._core.handlers:
    logger.add(sys.stderr, level="DEBUG", format="{time} - {level} - {message}")


# Helper to create a simple SceneNode for testing
def create_test_scene(title="TestScene", author="Tester", bg_color="BLACK", actions=[]) -> SceneNode:
    metadata = MetadataNode(title=title, author=author, background_color=bg_color)
    return SceneNode(metadata=metadata, objects=[], actions=actions)


# --- Test AnimateCounter ---


def test_animate_counter_basic():
    """测试基本的 AnimateCounter 节点生成，带有最小参数集"""
    # 仅提供必需的 target_value 参数
    counter_action = AnimateCounterNode(target_value=100)
    scene = create_test_scene(actions=[counter_action])

    generator = CodeGenerator()
    code = generator.generate(scene)

    logger.debug(f"Generated code for basic AnimateCounter:\n{code}")

    # 修改验证导入，因为 scene.py 中已经导入了 from manim import *
    assert "from manim import *" in code

    # 验证数字对象创建
    assert "counter_" in code  # 检查变量命名前缀
    assert "DecimalNumber(" in code
    assert "number=0.0" in code  # 默认 start_value
    assert "group_with_commas=True" in code  # 新增的参数

    # 验证组装和显示
    assert "VGroup(" in code
    assert "self.play(Create(" in code
    assert "move_to(ORIGIN)" in code  # 新增的居中定位

    # 验证数字更新 - 现在使用 ChangeDecimalToValue 而不是 animate.set_value
    assert "ChangeDecimalToValue(" in code
    assert "run_time=2.0" in code  # 默认 duration

    # 验证缩放效果 (默认)
    assert "Indicate(" in code

    # 验证淡出效果
    assert "FadeOut(" in code


def test_animate_counter_with_full_parameters():
    """测试带有所有可选参数的 AnimateCounter 节点生成"""
    counter_action = AnimateCounterNode(
        target_value=75.5, label="分数", start_value=25.0, duration=3.5, effect="flash", unit="%"
    )
    scene = create_test_scene(actions=[counter_action])

    generator = CodeGenerator()
    code = generator.generate(scene)

    logger.debug(f"Generated code for AnimateCounter with full parameters:\n{code}")

    # 修改验证导入，scene.py 中已经导入了 from manim import *
    assert "from manim import *" in code

    # 验证数字对象创建
    assert "number=25.0" in code  # 自定义 start_value

    # 验证标签创建
    assert 'Text("分数", font_size=24)' in code
    assert "next_to(" in code  # 验证使用next_to而不是arrange
    assert "LARGE_BUFF" in code  # 验证使用了更大的缓冲区

    # 验证单位创建
    assert 'Text("%", font_size=24)' in code

    # 验证数字更新 - 使用ChangeDecimalToValue而不是animate.set_value
    assert "ChangeDecimalToValue(" in code
    assert "run_time=3.5" in code  # 自定义 duration

    # 验证闪光效果
    assert "Flash(" in code
    assert "color=WHITE" in code


def test_animate_counter_with_none_effect():
    """测试指定 effect='none' 的 AnimateCounter 节点生成"""
    counter_action = AnimateCounterNode(target_value=1000, label="Total", effect="none", unit="K+")
    scene = create_test_scene(actions=[counter_action])

    generator = CodeGenerator()
    code = generator.generate(scene)

    logger.debug(f"Generated code for AnimateCounter with no effect:\n{code}")

    # 修改验证导入语句和断言
    assert "from manim import *" in code

    # 验证标签和单位
    assert 'Text("Total", font_size=24)' in code
    assert 'Text("K+", font_size=24)' in code

    # 验证没有添加结尾效果
    assert "Indicate(" not in code
    assert "Flash(" not in code

    # 但还是应该有数字动画，使用ChangeDecimalToValue
    assert "ChangeDecimalToValue(" in code
    assert "move_to(ORIGIN)" in code  # 验证居中


def test_multiple_animate_counters():
    """测试在一个场景中使用多个 AnimateCounter 节点"""
    counter1 = AnimateCounterNode(target_value=50, label="上半年")
    counter2 = AnimateCounterNode(target_value=100, label="全年", unit="M")

    scene = create_test_scene(actions=[counter1, counter2])

    generator = CodeGenerator()
    code = generator.generate(scene)

    logger.debug(f"Generated code for multiple AnimateCounters:\n{code}")

    # 验证两组变量名不会冲突
    assert code.count("DecimalNumber(") == 2
    assert code.count('Text("上半年"') == 1
    assert code.count('Text("全年"') == 1
    assert code.count('Text("M"') == 1
    assert code.count("move_to(ORIGIN)") == 2  # 每个计数器都居中

    # 验证显示、动画更新和效果是独立的
    assert code.count("self.play(Create(") == 2
    assert code.count("ChangeDecimalToValue(") == 2  # 使用新的动画方法
    assert code.count("Indicate(") == 2
    assert code.count("FadeOut(") == 2  # 验证淡出效果
