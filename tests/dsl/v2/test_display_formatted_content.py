# tests/dsl/v2/test_display_formatted_content.py
import sys

from loguru import logger

from dsl.v2.ast_nodes import DisplayFormattedContentNode, MetadataNode, SceneNode
from dsl.v2.code_generator import CodeGenerator

# 确保 loguru 配置
if not logger._core.handlers:
    logger.add(sys.stderr, level="DEBUG", format="{time} - {level} - {message}")


# Helper to create a simple SceneNode for testing
def create_test_scene(title="TestScene", author="Tester", bg_color="BLACK", actions=[]) -> SceneNode:
    metadata = MetadataNode(title=title, author=author, background_color=bg_color)
    return SceneNode(metadata=metadata, objects=[], actions=actions)


# --- Test DisplayFormattedContent ---


def test_display_formatted_content_text():
    """测试基本的 DisplayFormattedContent 节点生成，内容类型为 text"""
    content_action = DisplayFormattedContentNode(content="这是一段测试文本", content_type="text", animation="write")
    scene = create_test_scene(actions=[content_action])

    generator = CodeGenerator()
    code = generator.generate(scene)

    logger.debug(f"Generated code for DisplayFormattedContent with text:\n{code}")

    # 验证文本对象创建
    assert "content_" in code  # 检查变量命名前缀
    assert "Text(" in code
    assert "这是一段测试文本" in code
    assert 'font="Maple Mono NF CN"' in code
    assert "font_size=24" in code

    # 验证位置和显示
    assert "move_to(ORIGIN)" in code
    assert "screen_width = config.frame_width * 0.9" in code

    # 验证动画方式
    assert "self.play(AddTextLetterByLetter(" in code
    assert "run_time=2" in code

    # 验证内容记录
    assert "current_displayed_content" in code


def test_display_formatted_content_code():
    """测试 DisplayFormattedContent 节点生成，内容类型为 code"""
    code_content = """def hello_world():
    print("Hello, World!")
    return True"""

    content_action = DisplayFormattedContentNode(
        content=code_content, content_type="code", language="python", style="monokai", animation="fadeIn"
    )
    scene = create_test_scene(actions=[content_action])

    generator = CodeGenerator()
    code = generator.generate(scene)

    logger.debug(f"Generated code for DisplayFormattedContent with code:\n{code}")

    # 验证代码对象创建
    assert "content_" in code  # 检查变量命名前缀
    assert "Code(" in code
    assert "code_string=" in code
    assert "def hello_world():" in code
    assert "language='python'" in code
    assert "formatter_style='monokai'" in code
    assert "background='window'" in code

    # 验证动画方式
    assert "self.play(FadeIn(" in code
    assert "run_time=1" in code


def test_display_formatted_content_json():
    """测试 DisplayFormattedContent 节点生成，内容类型为 json"""
    json_content = """{
    "name": "示例",
    "data": [1, 2, 3],
    "enabled": true
}"""

    content_action = DisplayFormattedContentNode(content=json_content, content_type="json", animation="none")
    scene = create_test_scene(actions=[content_action])

    generator = CodeGenerator()
    code = generator.generate(scene)

    logger.debug(f"Generated code for DisplayFormattedContent with json:\n{code}")

    # 验证JSON对象创建
    assert "content_" in code  # 检查变量命名前缀
    assert "Code(" in code
    assert "code_string=" in code
    assert '"name": "示例"' in code
    assert "language='json'" in code

    # 验证动画方式
    assert "self.add(" in code
    assert "self.wait(0.5)" in code


def test_display_formatted_content_with_id():
    """测试 DisplayFormattedContent 节点生成，带有id参数"""
    content_action = DisplayFormattedContentNode(
        content="带有ID的内容块", content_type="text", id="important_text", animation="write"
    )
    scene = create_test_scene(actions=[content_action])

    generator = CodeGenerator()
    code = generator.generate(scene)

    logger.debug(f"Generated code for DisplayFormattedContent with id:\n{code}")

    # 验证ID引用保存
    assert "self.important_text_content =" in code
    assert "保存对象引用，以便后续访问" in code


def test_multiple_display_formatted_contents():
    """测试在一个场景中使用多个 DisplayFormattedContent 节点"""
    content1 = DisplayFormattedContentNode(content="第一段内容", content_type="text")
    content2 = DisplayFormattedContentNode(content="第二段内容", content_type="markdown", animation="fadeIn")

    scene = create_test_scene(actions=[content1, content2])

    generator = CodeGenerator()
    code = generator.generate(scene)

    logger.debug(f"Generated code for multiple DisplayFormattedContents:\n{code}")

    # 验证内容对象创建
    assert "第一段内容" in code
    assert "第二段内容" in code
    assert "MarkupText(" in code
    assert code.count("move_to(ORIGIN)") == 2  # 每个内容块都居中

    # 验证不同的动画方法
    assert "self.play(AddTextLetterByLetter(" in code
    assert "self.play(FadeIn(" in code
