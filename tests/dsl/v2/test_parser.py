# tests/dsl/v2/test_parser.py
import json
import sys

import pytest
from loguru import logger

from dsl.v2.parser import parse_dsl_file

# 确保 loguru 配置，测试时能看到日志
# (如果 pytest 配置了捕获日志，可能需要调整)
logger.remove()
logger.add(sys.stderr, level="DEBUG", format="{time} - {level} - {message}")


# Fixture for creating temporary files in a dedicated test directory
@pytest.fixture(scope="module")  # Use module scope for efficiency if files don't change per test
def test_files_dir(tmp_path_factory):
    # tmp_path_factory provides a session-scoped temporary directory
    # Use a subdirectory within it for this module's tests
    dir_path = tmp_path_factory.mktemp("parser_tests")
    logger.info(f"Created temporary directory for parser tests: {dir_path}")
    return dir_path


@pytest.fixture
def valid_dsl_file(test_files_dir):
    file_path = test_files_dir / "valid.json"
    data = {
        "metadata": {"title": "Valid Test"},
        "objects": [{"id": "t1", "type": "text", "properties": {}}],
        "actions": [{"type": "wait", "duration": 1}],
    }
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(data, f)
    logger.debug(f"Created valid test file: {file_path}")
    return file_path


@pytest.fixture
def invalid_json_file(test_files_dir):
    file_path = test_files_dir / "invalid.json"
    with open(file_path, "w", encoding="utf-8") as f:
        f.write("{ invalid json")
    logger.debug(f"Created invalid JSON test file: {file_path}")
    return file_path


@pytest.fixture
def missing_keys_file(test_files_dir):
    file_path = test_files_dir / "missing_keys.json"
    data = {"metadata": {"title": "Missing Keys"}}  # Missing objects and actions
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(data, f)
    logger.debug(f"Created missing keys test file: {file_path}")
    return file_path


def test_parse_valid_file(valid_dsl_file):
    """测试解析有效的 DSL 文件"""
    logger.info(f"Testing valid file: {valid_dsl_file}")
    result = parse_dsl_file(str(valid_dsl_file))
    assert isinstance(result, dict)
    assert "metadata" in result
    assert "objects" in result
    assert "actions" in result
    assert result["metadata"]["title"] == "Valid Test"
    logger.success("Valid file parsed successfully.")


def test_parse_invalid_json(invalid_json_file):
    """测试解析无效的 JSON 文件"""
    logger.info(f"Testing invalid JSON file: {invalid_json_file}")
    with pytest.raises(json.JSONDecodeError):
        parse_dsl_file(str(invalid_json_file))
    logger.success("Successfully caught JSONDecodeError for invalid JSON.")


def test_parse_missing_keys(missing_keys_file):
    """测试解析缺少顶级键的文件"""
    logger.info(f"Testing missing keys file: {missing_keys_file}")
    with pytest.raises(ValueError, match="缺少必需的顶级键: objects, actions"):
        parse_dsl_file(str(missing_keys_file))
    logger.success("Successfully caught ValueError for missing keys.")


def test_parse_file_not_found(test_files_dir):
    """测试解析不存在的文件"""
    non_existent_file = test_files_dir / "non_existent.json"
    logger.info(f"Testing non-existent file: {non_existent_file}")
    with pytest.raises(FileNotFoundError):
        parse_dsl_file(str(non_existent_file))
    logger.success("Successfully caught FileNotFoundError.")


# 可以添加更多测试，例如测试类型警告等
def test_parse_wrong_list_types(test_files_dir, caplog):  # Use caplog to check logs
    """测试 objects 或 actions 不是列表的情况 (应有警告)"""
    file_path = test_files_dir / "wrong_types.json"
    data = {
        "metadata": {"title": "Wrong Types"},
        "objects": {"id": "t1", "type": "text"},  # Should be a list
        "actions": {"type": "wait", "duration": 1},  # Should be a list
    }
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(data, f)

    logger.info(f"Testing file with wrong list types: {file_path}")
    # 捕获 loguru 的警告
    # with caplog.at_level(logging.WARNING):
    #     result = parse_dsl_file(str(file_path))
    #     assert isinstance(result, dict)
    #     assert "'objects' 字段类型不是列表" in caplog.text
    #     assert "'actions' 字段类型不是列表" in caplog.text
    # logger.success("Parser ran and warnings were logged for wrong list types.")

    # 或者，如果不依赖捕获日志，仅检查它不抛异常：
    try:
        result = parse_dsl_file(str(file_path))
        assert isinstance(result, dict)
        logger.success("Parser ran without error for wrong list types (warnings expected).")
    except Exception as e:
        pytest.fail(f"Parsing file with wrong list types raised unexpected exception: {e}")
