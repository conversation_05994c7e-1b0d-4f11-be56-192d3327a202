# tests/dsl/v2/test_dsl_to_manim.py
import json
import os
import subprocess
import sys
import tempfile
from pathlib import Path
from unittest import mock

import pytest

# 确保可以导入待测试的模块
try:
    from dsl.v2.ast_nodes import MetadataNode, SceneNode
    from dsl.v2.dsl_to_manim import generate_manim_code, main, run_manim_render
except ImportError:
    # 添加项目根目录到 sys.path
    project_root = Path(__file__).parent.parent.parent.parent
    sys.path.insert(0, str(project_root))
    from dsl.v2.ast_nodes import MetadataNode, SceneNode
    from dsl.v2.dsl_to_manim import generate_manim_code, main, run_manim_render

# 测试用的 DSL 数据
VALID_DSL_DATA = {
    "schema_version": "2.0-mvp",
    "metadata": {"title": "TestScene", "author": "Tester", "background_color": "BLACK"},
    "objects": [],
    "actions": [{"type": "wait", "duration": 1.0}],
}


# 设置测试环境
@pytest.fixture
def valid_dsl_file():
    """创建一个有效的 DSL 文件用于测试"""
    with tempfile.NamedTemporaryFile(suffix=".json", delete=False, mode="w") as tmp:
        json.dump(VALID_DSL_DATA, tmp)
        tmp_path = tmp.name

    yield tmp_path

    # 清理测试文件
    try:
        os.unlink(tmp_path)
    except Exception:
        pass


@pytest.fixture
def invalid_dsl_file():
    """创建一个无效的 DSL 文件用于测试"""
    with tempfile.NamedTemporaryFile(suffix=".json", delete=False, mode="w") as tmp:
        tmp.write("{invalid json")
        tmp_path = tmp.name

    yield tmp_path

    # 清理测试文件
    try:
        os.unlink(tmp_path)
    except Exception:
        pass


@pytest.fixture
def non_existent_file():
    """返回一个不存在的文件路径"""
    return "/tmp/non_existent_file_for_testing_dsl.json"


# 测试 generate_manim_code 函数
def test_generate_manim_code_success(valid_dsl_file):
    """测试在正常情况下从 DSL 文件生成 Manim 代码"""
    output_file = tempfile.mktemp(suffix=".py")

    try:
        # 执行测试
        result = generate_manim_code(valid_dsl_file, output_file)

        # 验证结果
        assert result is not None
        assert isinstance(result, SceneNode)
        assert result.metadata.title == "TestScene"
        assert Path(output_file).exists()

        # 验证生成的文件内容
        with open(output_file) as f:
            content = f.read()
            assert "from manim import *" in content
            assert "class TestScene(Scene)" in content
            assert "self.wait(1.0)" in content
    finally:
        # 清理
        try:
            os.unlink(output_file)
        except Exception:
            pass


def test_generate_manim_code_file_not_found(non_existent_file):
    """测试当 DSL 文件不存在时的处理"""
    output_file = tempfile.mktemp(suffix=".py")

    # 执行测试
    result = generate_manim_code(non_existent_file, output_file)

    # 验证结果
    assert result is None
    assert not Path(output_file).exists()


def test_generate_manim_code_parse_error(invalid_dsl_file):
    """测试当 DSL 文件格式无效时的处理"""
    output_file = tempfile.mktemp(suffix=".py")

    # 执行测试
    result = generate_manim_code(invalid_dsl_file, output_file)

    # 验证结果
    assert result is None
    assert not Path(output_file).exists()


# 测试 run_manim_render 函数
@mock.patch("dsl.v2.dsl_to_manim.subprocess.run")
@mock.patch("dsl.v2.dsl_to_manim.shutil.which")
def test_run_manim_render_with_uv(mock_which, mock_run):
    """测试在有 uv 命令的情况下渲染"""
    # 配置模拟对象
    mock_which.return_value = "/usr/bin/uv"  # 模拟 uv 命令存在
    mock_run.return_value = mock.MagicMock(stdout="Rendering success", stderr="")

    # 执行测试
    run_manim_render("test.py", "TestScene", "l")

    # 验证结果
    mock_which.assert_called_once_with("uv")
    mock_run.assert_called_once()
    args, kwargs = mock_run.call_args
    assert args[0] == ["uv", "run", "manim", "render", "test.py", "TestScene", "-ql"]


@mock.patch("dsl.v2.dsl_to_manim.subprocess.run")
@mock.patch("dsl.v2.dsl_to_manim.shutil.which")
def test_run_manim_render_without_uv(mock_which, mock_run):
    """测试在没有 uv 命令的情况下渲染"""
    # 配置模拟对象
    mock_which.return_value = None  # 模拟 uv 命令不存在
    mock_run.return_value = mock.MagicMock(stdout="Rendering success", stderr="")

    # 执行测试
    run_manim_render("test.py", "TestScene", "m")

    # 验证结果
    mock_which.assert_called_once_with("uv")
    mock_run.assert_called_once()
    args, kwargs = mock_run.call_args
    assert args[0] == ["manim", "render", "test.py", "TestScene", "-qm"]


@mock.patch("dsl.v2.dsl_to_manim.subprocess.run")
def test_run_manim_render_command_not_found(mock_run):
    """测试当渲染命令不存在时的处理"""
    # 配置模拟对象
    mock_run.side_effect = FileNotFoundError("Command not found")

    # 执行测试 - 不应抛出异常
    run_manim_render("test.py", "TestScene")

    # 验证
    mock_run.assert_called_once()


@mock.patch("dsl.v2.dsl_to_manim.subprocess.run")
def test_run_manim_render_process_error(mock_run):
    """测试当渲染进程出错时的处理"""
    # 配置模拟对象
    error = subprocess.CalledProcessError(1, "manim", stderr="Error message")
    mock_run.side_effect = error

    # 执行测试 - 不应抛出异常
    run_manim_render("test.py", "TestScene")

    # 验证
    mock_run.assert_called_once()


# 测试 main 函数
@mock.patch("dsl.v2.dsl_to_manim.run_manim_render")
@mock.patch("dsl.v2.dsl_to_manim.generate_manim_code")
@mock.patch("sys.argv")
def test_main_generate_only(mock_argv, mock_generate, mock_render, valid_dsl_file):
    """测试仅生成代码（不渲染）的主函数"""
    # 配置模拟对象
    mock_argv.__getitem__.side_effect = lambda i: ["dsl_to_manim.py", valid_dsl_file, "-o", "output.py"][i]
    mock_generate.return_value = None

    # 执行测试
    with mock.patch("sys.exit") as mock_exit:
        main()

    # 验证结果
    mock_generate.assert_called_once()
    mock_render.assert_not_called()
    mock_exit.assert_not_called()


@mock.patch("dsl.v2.dsl_to_manim.run_manim_render")
@mock.patch("dsl.v2.dsl_to_manim.generate_manim_code")
@mock.patch("sys.argv")
def test_main_generate_and_render(mock_argv, mock_generate, mock_render, valid_dsl_file):
    """测试生成代码并渲染的主函数"""
    # 创建模拟的 SceneNode 对象
    mock_scene = SceneNode(
        metadata=MetadataNode(title="TestScene", author="Tester", background_color="BLACK"), objects=[], actions=[]
    )

    # 配置模拟对象
    mock_argv.__getitem__.side_effect = lambda i: ["dsl_to_manim.py", valid_dsl_file, "-r", "-q", "m"][i]
    mock_generate.return_value = mock_scene

    # 执行测试
    with mock.patch("sys.exit") as mock_exit:
        main()

    # 验证结果
    mock_generate.assert_called_once()
    mock_render.assert_called_once()
    mock_exit.assert_not_called()


@mock.patch("dsl.v2.dsl_to_manim.run_manim_render")
@mock.patch("dsl.v2.dsl_to_manim.generate_manim_code")
@mock.patch("sys.argv")
def test_main_generate_fail_and_render(mock_argv, mock_generate, mock_render, valid_dsl_file):
    """测试生成代码失败但请求渲染的主函数"""
    # 配置模拟对象
    mock_argv.__getitem__.side_effect = lambda i: ["dsl_to_manim.py", valid_dsl_file, "-r"][i]
    mock_generate.return_value = None  # 生成失败

    # 执行测试
    with mock.patch("sys.exit") as mock_exit:
        main()

    # 验证结果
    mock_generate.assert_called_once()
    mock_render.assert_not_called()  # 不应调用渲染
    mock_exit.assert_not_called()


@mock.patch("sys.argv")
def test_main_file_not_found(mock_argv, non_existent_file):
    """测试当 DSL 文件不存在时的主函数处理"""
    # 配置模拟对象
    mock_argv.__getitem__.side_effect = lambda i: ["dsl_to_manim.py", non_existent_file][i]

    # 执行测试
    with mock.patch("sys.exit") as mock_exit:
        main()

    # 验证结果
    mock_exit.assert_called_once_with(1)


if __name__ == "__main__":
    pytest.main(["-v", __file__])
