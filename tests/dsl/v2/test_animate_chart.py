# tests/dsl/v2/test_animate_chart.py

from loguru import logger

from dsl.v2.ast_nodes import AnimateChartNode, MetadataNode, SceneNode
from dsl.v2.code_generator import CodeGenerator


# 测试辅助函数
def create_test_scene(actions=None) -> SceneNode:
    """创建测试场景"""
    if actions is None:
        actions = []
    metadata = MetadataNode(title="测试场景", author="测试者", background_color="BLACK")
    return SceneNode(metadata=metadata, objects=[], actions=actions)


# 测试基本功能
def test_bar_chart_basic():
    """测试基本的条形图生成"""
    # 创建测试数据
    test_data = {"类别A": 10, "类别B": 20, "类别C": 15}

    # 创建图表节点
    node = AnimateChartNode(chart_type="bar", data=test_data, title="测试条形图", animation_style="grow")

    # 创建测试场景
    scene = create_test_scene(actions=[node])

    # 生成代码
    generator = CodeGenerator()
    code = generator.generate(scene)

    logger.debug(f"生成的代码:\n{code}")

    # 验证生成的代码包含预期内容
    assert "BarChart" in code
    assert "测试条形图" in code
    assert "['类别A', '类别B', '类别C']" in code
    assert "[10, 20, 15]" in code
    assert "Create(" in code
    assert "FadeOut(" in code


def test_line_chart_basic():
    """测试基本的折线图生成"""
    # 创建测试数据
    test_data = {"2020": 10, "2021": 25, "2022": 18, "2023": 30}

    # 创建图表节点
    node = AnimateChartNode(chart_type="line", data=test_data, title="年度趋势", animation_style="fadeIn")

    # 创建测试场景
    scene = create_test_scene(actions=[node])

    # 生成代码
    generator = CodeGenerator()
    code = generator.generate(scene)

    logger.debug(f"生成的代码:\n{code}")

    # 验证生成的代码包含预期内容
    assert "Axes" in code
    assert "plot_line_graph" in code
    assert "年度趋势" in code
    assert "['2020', '2021', '2022', '2023']" in code
    assert "[10, 25, 18, 30]" in code
    assert "FadeIn(" in code


def test_radar_chart_basic():
    """测试基本的雷达图生成"""
    # 创建测试数据
    test_data = {"性能": 8, "易用性": 7, "安全性": 9, "可靠性": 6, "成本": 5}

    # 创建图表节点
    node = AnimateChartNode(chart_type="radar", data=test_data, title="产品评估", animation_style="draw")

    # 创建测试场景
    scene = create_test_scene(actions=[node])

    # 生成代码
    generator = CodeGenerator()
    code = generator.generate(scene)

    logger.debug(f"生成的代码:\n{code}")

    # 验证生成的代码包含预期内容
    assert "Polygon" in code
    assert "np.linspace" in code
    assert "产品评估" in code
    assert "['性能', '易用性', '安全性', '可靠性', '成本']" in code
    assert "[8, 7, 9, 6, 5]" in code
    assert "Create(" in code


def test_chart_with_options():
    """测试带有自定义选项的图表生成"""
    # 创建测试数据
    test_data = {"A": 10, "B": 20, "C": 15}

    # 创建图表节点，包含自定义选项
    node = AnimateChartNode(
        chart_type="bar",
        data=test_data,
        title="带选项的图表",
        animation_style="update",
        options={"y_range": "[0, 30]", "bar_colors": "[RED, GREEN, BLUE]", "width": 8, "height": 5},
    )

    # 创建测试场景
    scene = create_test_scene(actions=[node])

    # 生成代码
    generator = CodeGenerator()
    code = generator.generate(scene)

    logger.debug(f"生成的代码:\n{code}")

    # 验证生成的代码包含预期内容
    assert "y_range=[0, 30]" in code
    assert "bar_colors=[chart_" in code  # 检查是否有bar_colors，但不限制具体值
    assert "x_length=8" in code
    assert "y_length=5" in code
    assert "初始为空，然后逐个更新条形" in code  # update 动画特有


def test_multi_dataset_chart():
    """测试带有多个数据集的图表生成"""
    # 创建多组测试数据
    test_data = [{"A": 10, "B": 20, "C": 15}, {"A": 15, "B": 10, "C": 25}]

    # 创建图表节点
    node = AnimateChartNode(
        chart_type="bar",
        data=test_data,
        title="多数据集对比图",
        animation_style="grow",
        options={"dataset_names": ["数据集A", "数据集B"]},
    )

    # 创建测试场景
    scene = create_test_scene(actions=[node])

    # 生成代码
    generator = CodeGenerator()
    code = generator.generate(scene)

    logger.debug(f"生成的代码:\n{code}")

    # 验证生成的代码包含预期内容
    assert "多数据集对比图" in code
    assert "dataset_names = ['数据集A', '数据集B']" in code
    assert "创建第2组条形" in code  # 替换为更具体的检查
    assert "移动第2组条形" in code  # 检查是否移动了第二组条形
    assert "VGroup" in code
