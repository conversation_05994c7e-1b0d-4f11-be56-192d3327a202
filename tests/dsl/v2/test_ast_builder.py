# tests/dsl/v2/test_ast_builder.py
import json
import sys

import pytest
from loguru import logger
from pydantic import ValidationError

from dsl.v2.ast_builder import ASTBuilder, build_ast_from_file
from dsl.v2.ast_nodes import ImageObjectNode, SceneNode, SideBySideComparisonNode, TextObjectNode, WaitNode

# 确保 loguru 配置
if not logger._core.handlers:
    logger.add(sys.stderr, level="DEBUG", format="{time} - {level} - {message}")


# Fixture for temporary test files directory (similar to test_parser)
@pytest.fixture(scope="module")
def test_files_dir(tmp_path_factory):
    dir_path = tmp_path_factory.mktemp("ast_builder_tests")
    logger.info(f"Created temporary directory for AST builder tests: {dir_path}")
    return dir_path


# Fixtures to create test JSON files
@pytest.fixture
def valid_dsl_data():
    return {
        "schema_version": "2.0-mvp-builder-test",
        "metadata": {"title": "有效构建测试", "author": "Builder"},
        "objects": [
            {"id": "text1", "type": "text", "properties": {"content": "左"}},
            {"id": "img1", "type": "image", "properties": {"file_path": "right.png"}},
        ],
        "actions": [
            {"type": "side_by_side_comparison", "left_content": "text1", "right_content": "img1"},
            {"type": "wait", "duration": 1.5},
        ],
    }


@pytest.fixture
def invalid_structure_dsl_data():  # Pydantic validation should fail
    return {
        "metadata": {"title": "无效结构"},
        "objects": [{"id": "obj1", "type": "text"}],
        "actions": [{"type": "wait", "duration": -1}],  # duration < 0
    }


@pytest.fixture
def valid_dsl_file_for_builder(test_files_dir, valid_dsl_data):
    file_path = test_files_dir / "valid_builder.json"
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(valid_dsl_data, f)
    return file_path


@pytest.fixture
def invalid_structure_dsl_file(test_files_dir, invalid_structure_dsl_data):
    file_path = test_files_dir / "invalid_structure_builder.json"
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(invalid_structure_dsl_data, f)
    return file_path


# --- Tests for ASTBuilder class directly ---


def test_ast_builder_valid(valid_dsl_data):
    """测试使用有效数据直接调用 ASTBuilder.build"""
    logger.info("Testing ASTBuilder.build with valid data")
    builder = ASTBuilder()
    ast = builder.build(valid_dsl_data)
    assert isinstance(ast, SceneNode)
    assert ast.metadata.title == "有效构建测试"
    assert len(ast.objects) == 2
    assert isinstance(ast.objects[0], TextObjectNode)
    assert isinstance(ast.objects[1], ImageObjectNode)
    assert len(ast.actions) == 2
    assert isinstance(ast.actions[0], SideBySideComparisonNode)
    assert isinstance(ast.actions[1], WaitNode)
    logger.success("ASTBuilder.build valid test passed.")


def test_ast_builder_invalid_structure(invalid_structure_dsl_data):
    """测试 ASTBuilder.build 传播 Pydantic 验证错误"""
    logger.info("Testing ASTBuilder.build with invalid structure data")
    builder = ASTBuilder()
    with pytest.raises(ValidationError, match="duration"):
        builder.build(invalid_structure_dsl_data)
    logger.success("ASTBuilder.build invalid structure (ValidationError) test passed.")


# --- Tests for the helper function build_ast_from_file ---


def test_build_ast_from_valid_file(valid_dsl_file_for_builder):
    """测试从有效文件构建 AST (端到端)"""
    logger.info(f"Testing build_ast_from_file with valid file: {valid_dsl_file_for_builder}")
    ast = build_ast_from_file(str(valid_dsl_file_for_builder))
    assert isinstance(ast, SceneNode)
    assert ast.metadata.title == "有效构建测试"
    assert len(ast.objects) == 2
    assert len(ast.actions) == 2
    logger.success("build_ast_from_file valid test passed.")


def test_build_ast_from_invalid_structure_file(invalid_structure_dsl_file):
    """测试从包含无效结构的文件构建 AST"""
    logger.info(f"Testing build_ast_from_file with invalid structure file: {invalid_structure_dsl_file}")
    with pytest.raises(ValidationError, match="duration"):
        build_ast_from_file(str(invalid_structure_dsl_file))
    logger.success("build_ast_from_file invalid structure (ValidationError) test passed.")


def test_build_ast_from_file_not_found(test_files_dir):
    """测试 build_ast_from_file 处理文件不存在 (来自 parser)"""
    non_existent_file = test_files_dir / "non_existent_builder.json"
    logger.info(f"Testing build_ast_from_file with non-existent file: {non_existent_file}")
    with pytest.raises(FileNotFoundError):
        build_ast_from_file(str(non_existent_file))
    logger.success("build_ast_from_file FileNotFoundError test passed.")
