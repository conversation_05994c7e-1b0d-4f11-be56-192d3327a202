"""
测试 SideBySideComparison 功能的单元测试

测试各种内容类型和布局选项的正确性。
"""

import sys

from loguru import logger

from dsl.v2.ast_nodes import MetadataNode, SceneNode, SideBySideComparisonNode
from dsl.v2.code_generator import CodeGenerator


# 测试辅助函数
def create_test_scene(actions=None):
    """创建测试场景节点"""
    if actions is None:
        actions = []
    metadata = MetadataNode(title="TestScene", author="Tester", background_color="BLACK")
    return SceneNode(metadata=metadata, objects=[], actions=actions)


def test_basic_side_by_side_comparison():
    """测试基本的左右分屏对比功能"""
    # 创建一个简单的左右对比节点
    node = SideBySideComparisonNode(
        left_content="这是左侧内容", right_content="这是右侧内容", left_title="左侧标题", right_title="右侧标题"
    )

    # 创建测试场景
    scene = create_test_scene(actions=[node])

    # 生成代码
    generator = CodeGenerator()
    code = generator.generate(scene)

    # 验证生成的代码包含预期内容
    assert 'Text("""这是左侧内容"""' in code
    assert 'Text("""这是右侧内容"""' in code
    assert 'Text("左侧标题"' in code
    assert 'Text("右侧标题"' in code
    assert "Group" in code
    assert "arrange(RIGHT" in code
    assert "VS" in code
    assert "FadeIn" in code


def test_different_content_types():
    """测试不同的内容类型"""
    # 创建代码和图像内容类型的对比节点
    node = SideBySideComparisonNode(
        left_content="print('Hello World')",
        right_content="path/to/image.png",
        left_type="code",
        right_type="image",
        vs_symbol=False,
    )

    # 创建测试场景
    scene = create_test_scene(actions=[node])

    # 生成代码
    generator = CodeGenerator()
    code = generator.generate(scene)

    # 验证生成的代码包含预期内容
    assert 'Code(code_string="""print(\'Hello World\')"""' in code
    assert 'ImageMobject("path/to/image.png")' in code
    assert "VS" not in code


def test_different_transitions():
    """测试不同的过渡动画"""
    # 创建使用slideUp过渡动画的对比节点
    node = SideBySideComparisonNode(
        left_content="左侧JSON内容", right_content="右侧内容", left_type="json", transition="slideUp"
    )

    # 创建测试场景
    scene = create_test_scene(actions=[node])

    # 生成代码
    generator = CodeGenerator()
    code = generator.generate(scene)

    # 验证生成的代码包含预期内容
    assert 'Code(code_string="""左侧JSON内容"""' in code
    assert "language='json'" in code
    assert "self.add(" in code
    assert "shift(DOWN * 3)" in code
    assert "animate.shift(UP * 3)" in code
    assert "从下方滑入屏幕中央" in code
    assert "run_time=1.0" in code


if __name__ == "__main__":
    logger.remove()
    logger.add(sys.stderr, level="INFO")

    test_basic_side_by_side_comparison()
    test_different_content_types()
    test_different_transitions()

    logger.info("所有测试通过!")
