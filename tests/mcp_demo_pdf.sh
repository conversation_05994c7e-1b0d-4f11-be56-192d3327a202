#!/bin/bash

# 清屏并显示演示标题
clear
echo "======================================"
echo "       PDF转Markdown演示"
echo "======================================"
echo ""
sleep 1

# 步骤1：展示将要执行的命令
echo "1. 运行 mcp-cli chat --server markdownify 将PDF转换为Markdown"
sleep 1

# 步骤2：准备并执行转换命令
echo "2. 输入转换指令..."
echo "把wenzi.pdf转换成markdown格式，并展示结果" > /tmp/input_$$.txt
# 执行转换命令
(cat /tmp/input_$$.txt; sleep 5) | mcp-cli chat --server markdownify
# 清理临时输入文件
rm -f /tmp/input_$$.txt

# 获取屏幕宽度用于窗口位置设置
SCREEN_WIDTH=$(osascript -e 'tell application "Finder" to get bounds of window of desktop' | sed 's/, /\n/g' | sed -n '3p')
WINDOW_WIDTH=$((SCREEN_WIDTH / 2 - 50))

# 步骤3：在屏幕左侧打开源PPT文件
sleep 1
echo "3. 打开源PDF文件..."
# 先打开文件
open wenzi.pdf
sleep 2
# 设置窗口位置到屏幕左侧
osascript -e "
tell application \"System Events\"
    set frontApp to first application process whose frontmost is true
    tell frontApp
        set position of first window to {0, 50}
        set size of first window to {$WINDOW_WIDTH, 800}
    end tell
end tell
"
sleep 1

# 向下滚动演示PPT内容
osascript -e '
tell application "System Events"
    set frontApp to first application process whose frontmost is true
    tell frontApp
        repeat 8 times
            key code 125 -- 向下箭头键
            delay 0.2 -- 滚动间隔
        end repeat
    end tell
end tell
'
sleep 1

# 关闭PPT展示文件
osascript -e 'tell application "Preview" to close (every window whose name contains "wenzi.pdf")' || true
sleep 1

# 步骤4：在屏幕右侧打开转换后的Markdown文件
echo "4. 打开转换后的Markdown文件..."
LATEST_FILE=$(ls -t /tmp | head -n 1)
# 打开最新生成的文件
open "/tmp/${LATEST_FILE}"
sleep 2
# 设置窗口位置到屏幕右侧
RIGHT_X=$((SCREEN_WIDTH / 2))
osascript -e "
tell application \"System Events\"
    set frontApp to first application process whose frontmost is true
    tell frontApp
        set position of first window to {$RIGHT_X, 50}
        set size of first window to {$WINDOW_WIDTH, 800}
    end tell
end tell
"
sleep 1

# 滚动展示转换后的Markdown内容
osascript -e '
tell application "System Events"
    set frontApp to first application process whose frontmost is true
    tell frontApp
        repeat 50 times
            key code 125 -- 向下箭头键
            delay 0.01 -- 快速滚动
        end repeat
    end tell
end tell
'

# 关闭所有预览窗口
osascript -e 'tell application "Preview" to close every window' || 
osascript -e 'tell application "TextEdit" to close every window' || true

# 完成演示
echo "5. 演示完成!"
sleep 1
exit 0