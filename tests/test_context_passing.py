#!/usr/bin/env python
"""
Test script to verify that context passing between agents works as expected.
"""

from loguru import logger
from omegaconf import OmegaConf
from smolagents import LiteLLMModel

from src.video_agent import VideoAgent, get_api_key, get_config


def main():
    """Run a test of context passing between agents in the VideoAgent."""
    # Get configuration
    config = get_config()
    logger.debug(f"Config:\n{OmegaConf.to_yaml(config, resolve=True)}")

    # Initialize the model
    model = LiteLLMModel(
        config["model"]["openrouter"]["default_model"],
        api_key=get_api_key("openrouter"),
    )

    # Create the VideoAgent
    agent = VideoAgent(model=model, config=config)

    # Define a topic to explain
    topic = "Explain how quantum computing works in a way that a high school student could understand."

    print(f"\nGenerating Feynman-style explanation for: {topic}\n")
    print("-" * 80)

    # Generate the explanation
    explanation = agent.explain(topic)

    print("\nFinal Explanation:\n")
    print(explanation)
    print("\n" + "-" * 80)


if __name__ == "__main__":
    main()
