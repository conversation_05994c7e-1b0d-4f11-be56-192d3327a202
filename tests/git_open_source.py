import json
import re

from dotenv import load_dotenv

load_dotenv()
import logging
import os

import yaml
from camel.agents import ChatAgent
from camel.logger import set_log_level
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.types import ModelPlatformType
from loguru import logger

from agents.video_agent.tools.info_collector_toolkit import InfoCollectorToolkit
from utils.format import extract_json, save_json_content

set_log_level(level="WARNING")

# 必要的目录结构
os.makedirs("config", exist_ok=True)
os.makedirs("output", exist_ok=True)

REPO_ANALYSIS_FORMAT_PROMPT = """
[
    {
        "分镜名": "标题或章节名，如开场吸引/核心价值与功能/实际应用场景一/实际应用场景二/实际应用场景三/优缺点与适用条件/总结与行动建议等",
        "分镜内容": "以讲解的口吻介绍该分镜内容",
        "素材名": "分镜内容对应输入markdown中材料的视频、图片、表格、视频等媒体素材名称, 比如pdf_output/2411.01747v1_artifacts/page_3_image_0.png",
        "视觉动效建议": "如何可视化展示该部分内容，比如流程图、架构图或代码高亮建议"
    }
]
"""

REPO_INFO_COLLECTOR_PROMPT = """
仓库信息如下：{repo}

分析该Git仓库，收集以下信息：
1. 项目名称和简介
2. 主要功能和特性
3. 技术栈和依赖库
4. 项目结构和重要模块
5. README内容和项目文档
6. 项目测试案例

结果应包含仓库的核心信息，帮助理解项目的目的和架构。
"""

REPO_ANALYSIS_PROMPT = """分析以上Git仓库信息，生成一份全面清晰的项目概述，结构如下：

1. **分镜名**：分镜的子标题描述，包括开场吸引/核心价值与功能/实际应用场景/优缺点与适用条件/总结与行动建议

2. **分镜内容**：精炼且深入的讲解文案，严格控制在规定字数内：

   - **开场吸引类**：【10-20字】展示技术能解决的核心问题或带来的惊人效果，吸引人的策略包括：
        - 颠覆认知的数据对比，如"这个项目让开发时间缩短了78%，但99%的开发者还不知道它"，"为什么这个只有2年历史的项目击败了存在10年的行业标准？"，"没有编程经验的普通用户用它完成了专业开发者需要1周的工作"
        - 挑战常规的效果展示，如"先展示使用传统方法的复杂过程，然后秒切到项目解决方案的简洁流程"，"使用 大家都以为 vs 实际上的对比框架"，"展示一个令人惊讶的用例，然后揭示是由该项目实现的"
        - 悬念式问题抛出，如"如果告诉你可以用3行代码替代300行实现，你会相信吗？"，"为什么顶尖科技公司的工程师都在悄悄转向这个开源项目？"，"这个被低估的工具如何改变了整个行业的工作方式？"

   - **核心价值与功能类**：【30-50字】简明介绍技术的关键价值主张和主要功能点，使用"三点法则"提炼3个最有价值的功能

   - **实际应用场景类**：【30-50字】列出2-3个项目的输入输出示例，优先展示最有价值或者评价更高的示例，以解决实际问题的角度展示全流程

   - **优缺点与适用条件类**：【30-50字】客观分析优势、局限性和最佳适用场景，使用星级评分等视觉化方式展示不同维度的表现

   - **总结与行动建议类**：【20-30字】总结核心价值和适用人群，提供明确下一步建议，使用简洁有力的号召性用语

3. **素材名**：每个分镜必须引用至少一个适当的素材：
    - 代码：项目中的关键代码
    - 图表：展现项目性能或者效果指标
    - 流程图：项目的逻辑流程图
    - 应用案例：项目在实际场景中的应用案例

4. **视觉动效建议**：针对素材的动效建议，详细描述动效类型和实现方式：
   - 对于代码：提供代码行高亮、逐行解释、关键变量追踪等动效建议
   - 对于图表：提供逐步展开、高亮重点部分、使用箭头指示流程等动效建议
   - 对于流程图：提供逐步展开、高亮重点部分、使用箭头指示流程等动效建议
   - 对于应用案例：提供公式组件逐步展示、符号解释动画等动效建议


仓库信息如下：{repo_info}

你必须使用以下严格的JSON数组格式输出结果，不要添加任何解释文字，只返回JSON：
"""

REPO_REFLECTION_PROMPT = """检查生成的项目概述内容，评估其质量和完整性。

格式检查要点：
1. 内容完整性：是否涵盖了项目概述、核心特性、技术架构等所有必要部分
2. JSON格式：检查JSON格式是否正确，特殊字符是否正确转义
3. 各部分内容长度：检查是否符合建议的句子/段落长度

内容检查要点：
1. 开场吸引是否简洁明了且足够吸引人
2. 核心价值与功能是否清晰表达了项目目的和价值
3. 实际应用场景是否全面且有代表性
4. 优缺点与适用条件是否客观且具有明确数据支持
5. 总结与行动建议是否简洁有力且具有明确行动指引


请检查内容并改进，使用以下严格的JSON数组格式输出结果，不要添加任何解释文字，只返回JSON：
"""


# 定义Git仓库分析工作流类
class GitRepoAnalyzer:
    def __init__(self, config_path="config/config.yaml"):
        # 创建默认配置文件（如果不存在）
        self._create_default_config_if_not_exists(config_path)

        # 加载配置
        self.load_config(config_path)

        # 初始化模型
        self.model = self._create_model()
        logging.warning("模型初始化完成")

        # 初始化代理
        self.agents = self._initialize_agents()
        logging.warning("代理初始化完成")

    def _create_default_config_if_not_exists(self, config_path):
        """如果配置文件不存在，创建默认配置"""
        if not os.path.exists(config_path):
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            default_config = {
                "model": {
                    "type": "openai/gpt-4o-mini",
                    "api": {
                        "openai_compatibility_api_key": os.environ.get("OPENAI_API_KEY", ""),
                        "openai_compatibility_api_base_url": os.environ.get(
                            "OPENAI_API_BASE", "https://api.openai.com/v1"
                        ),
                    },
                },
                "agents": {
                    "repo_info_collector": {"enabled": True},
                    "repo_analyzer": {"enabled": True},
                    "repo_reflector": {"enabled": True},
                },
                "repo": {
                    "default_branch": "main",
                    "max_file_size": 1024 * 1024,  # 1MB
                },
            }
            with open(config_path, "w") as f:
                yaml.dump(default_config, f)

    def load_config(self, config_path):
        """从YAML文件加载配置"""
        try:
            with open(config_path) as file:
                config = yaml.safe_load(file)
            # 设置配置属性
            self.model_config = config.get("model", {})
            self.repo_config = config.get("repo", {})
            self.agent_config = config.get("agents", {})
        except Exception as e:
            logging.error(f"加载配置出错: {str(e)}")
            raise

    def _create_model(self):
        """根据配置创建模型实例"""
        api_config = self.model_config.get("api", {})

        return ModelFactory.create(
            model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
            model_type=self.model_config.get("type", "openai/gpt-4o-mini"),
            api_key=api_config.get("openai_compatibility_api_key"),
            url=api_config.get("openai_compatibility_api_base_url"),
        )

    def _initialize_agents(self) -> dict:
        """初始化所有启用的代理及其系统提示和工具"""
        agents = {}

        # 仓库信息收集代理
        agents["repo_info_collector"] = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="仓库信息收集者",
                content="你负责收集和组织Git仓库的信息，包括项目结构、功能特性和技术栈。",
            ),
            model=self.model,
            tools=[*InfoCollectorToolkit().get_tools()],
        )

        # 仓库分析代理
        agents["repo_analyzer"] = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="仓库分析者",
                content="你负责分析Git仓库信息并生成结构化的项目概述和文档。",
            ),
            model=self.model,
        )

        # 内容反思代理
        agents["repo_reflector"] = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="内容反思者",
                content="你负责检查和完善生成的仓库分析内容，确保其完整性、准确性和格式正确性。",
            ),
            model=self.model,
        )

        return agents

    def process_message(self, agent_name: str, message: str):
        """处理消息并返回响应"""
        agent = self.agents[agent_name]
        # 发送消息并获取响应
        response = agent.step(message)
        logger.debug(response)

        # 获取响应内容
        content = response.msgs[0].content

        # 对于分析和反思结果，尝试提取JSON部分
        if agent_name in ["repo_analyzer", "repo_reflector"]:
            # 首先尝试直接查找JSON数组开始标记
            if "[" in content and "]" in content:
                start_idx = content.find("[")
                end_idx = content.rfind("]") + 1
                if start_idx < end_idx:
                    json_content = content[start_idx:end_idx]
                    try:
                        # 验证是否为有效JSON
                        json.loads(json_content)
                        return json_content
                    except json.JSONDecodeError:
                        logger.warning(f"提取的内容不是有效的JSON: {json_content[:100]}...")

            # 查找```json代码块
            json_block_pattern = r"```(?:json)?\s*([\s\S]*?)```"
            matches = re.findall(json_block_pattern, content)
            if matches:
                for match in matches:
                    try:
                        # 验证是否为有效JSON
                        json.loads(match)
                        return match
                    except json.JSONDecodeError:
                        continue

        return content

    def collect_repo_info(self, repo_url):
        """收集远程仓库信息"""
        print(f"正在收集远程仓库信息: {repo_url}...")

        try:
            # 检查URL格式，判断是否为GitHub或其他Git仓库
            if not repo_url.startswith("http"):
                raise ValueError(f"仓库URL格式不正确: {repo_url}")

            # 从URL提取仓库名称
            repo_name = repo_url.split("/")[-1].replace(".git", "")

            # 使用仓库信息收集代理处理URL
            prompt = REPO_INFO_COLLECTOR_PROMPT.format(repo=repo_url)
            repo_info = self.process_message("repo_info_collector", prompt)

            print(f"成功获取仓库 {repo_name} 的信息")
            return repo_info

        except Exception as e:
            logging.error(f"收集仓库信息出错: {str(e)}")
            raise

    def analyze_repo(self, repo_url):
        """分析仓库，生成结构化文档"""
        # 步骤1：收集仓库信息
        print("步骤1：收集仓库信息...")
        repo_info = self.collect_repo_info(repo_url)

        # 正确处理输出结果的长度限制
        repo_info_str = str(repo_info)
        if len(repo_info_str) > 200:
            print("步骤1结果：" + repo_info_str[:200] + "...")
        else:
            print("步骤1结果：" + repo_info_str)

        # 步骤2：分析仓库
        print("步骤2：分析仓库...")
        # 使用正确的方式格式化提示词
        analysis_prompt = REPO_ANALYSIS_PROMPT.format(repo_info=str(repo_info)) + REPO_ANALYSIS_FORMAT_PROMPT
        analysis_result = self.process_message("repo_analyzer", analysis_prompt)

        # 步骤3：反思和完善
        print("步骤3：反思和完善内容...")
        # 使用正确的方式格式化提示词
        reflection_prompt = (
            f"""以下是生成的内容:
            {analysis_result}
            """
            + REPO_REFLECTION_PROMPT
            + REPO_ANALYSIS_FORMAT_PROMPT
        )
        final_result = self.process_message("repo_reflector", reflection_prompt)

        return final_result


def main():
    """主函数，运行工作流"""
    # 初始化工作流
    analyzer = GitRepoAnalyzer()

    # 分析仓库
    repo_url = "https://github.com/stefanoamorelli/nasdaq-data-link-mcp"

    print(f"开始分析仓库: {repo_url}")
    results = analyzer.analyze_repo(repo_url)

    # 打印原始结果，用于调试
    print("=== 分析结果原始内容 ===")
    print(results[:500] + "..." if len(results) > 500 else results)  # 只打印前500个字符

    # 提取JSON内容并保存
    print("正在提取JSON内容...")
    try:
        extracted_json = extract_json(results)
        print(f"提取的JSON类型: {type(extracted_json)}")
        print(
            f"提取的JSON内容: {extracted_json[:200] if isinstance(extracted_json, str) else str(extracted_json)[:200]}..."
        )

        output_file = f"output/{repo_url.split('/')[-1].replace('.git', '')}_analysis.json"
        print(f"正在保存到: {output_file}")

        save_success = save_json_content(extracted_json, output_file)
        if save_success:
            print(f"分析完成，结果已保存到 {output_file}")
        else:
            print("保存JSON内容失败")
    except Exception as e:
        print(f"处理JSON时出错: {str(e)}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
