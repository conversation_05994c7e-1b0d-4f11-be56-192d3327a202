#!/usr/bin/env python
"""
测试内容管理工具包
"""

from dotenv import load_dotenv

load_dotenv()

from src.video_agent.tools.content_manager_toolkit import ContentManagerToolkit


def main():
    # 初始化工具包
    toolkit = ContentManagerToolkit()

    # 获取工具列表
    tools = toolkit.get_tools()

    print(f"工具包初始化成功，包含 {len(tools)} 个工具")
    print("可用工具:")
    for tool in tools:
        print(f"- {tool.func.__name__}: {tool.func.__doc__}")

    # 测试文本处理
    print("\n测试文本处理:")
    text_result = toolkit.process_text(
        text="# 测试文档\n\n这是一个简单的测试文档，用于演示内容管理工具包的功能。",
        title="测试文档",
        is_markdown=True,
    )
    print(f"文本处理完成，标题: {text_result.get('metadata', {}).get('title')}")
    print(f"缓存路径: {text_result.get('cached_path')}")

    # 测试网页处理（使用较小的页面以便快速测试）
    print("\n测试网页处理:")
    web_result = toolkit.extract_web_content(
        url="https://en.wikipedia.org/wiki/Richard_Feynman",
        extract_images=True,
    )
    print(f"网页处理完成，标题: {web_result.get('metadata', {}).get('title')}")
    print(f"图片资源数量: {web_result.get('resources_count', 0)}")
    print(f"缓存路径: {web_result.get('cached_path')}")

    # 测试搜索功能（使用DuckDuckGo避免API密钥需求）
    print("\n测试搜索功能:")
    search_result = toolkit.search(
        query="Python programming language",
        search_type="duckduckgo",
        max_results=5,
    )
    print(f"搜索完成，结果数量: {search_result.get('statistics', {}).get('results_count', 0)}")
    print(f"缓存路径: {search_result.get('cached_path')}")

    # 测试pdf功能
    print("\n测试pdf功能:")
    pdf_result = toolkit.process_pdf(
        source_id="https://arxiv.org/pdf/2501.12948",
        extract_images=True,
    )
    print(f"pdf处理完成，标题: {pdf_result.get('metadata', {}).get('title')}")
    print(f"缓存路径: {pdf_result.get('cached_path')}")

    # 测试缓存统计
    print("\n测试缓存统计:")
    stats = toolkit.get_cache_stats()
    print(f"缓存内容数量: {stats.get('content_count', 0)}")
    print(f"缓存资源数量: {stats.get('resource_count', 0)}")
    print(f"总大小: {stats.get('total_size_bytes', 0) / (1024*1024):.2f} MB")


if __name__ == "__main__":
    main()
