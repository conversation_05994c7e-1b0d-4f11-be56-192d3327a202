{"metadata": {"title": "ObjectGeneratorsCoverageTest", "author": "Coverage Test", "resolution": "2160p", "background_color": "DARK_BLUE"}, "objects": [{"id": "circle_default", "type": "circle", "properties": {}}, {"id": "circle_custom", "type": "circle", "properties": {"radius": 2.5, "color": "BLUE", "fill_opacity": 0.7, "stroke_width": 3, "stroke_color": "WHITE"}}, {"id": "square_default", "type": "square", "properties": {}}, {"id": "square_custom", "type": "square", "properties": {"side_length": 3.0, "color": "GREEN", "fill_opacity": 0.6, "stroke_width": 2, "stroke_color": "RED"}}, {"id": "rectangle_default", "type": "rectangle", "properties": {}}, {"id": "rectangle_custom", "type": "rectangle", "properties": {"width": 4.0, "height": 2.0, "color": "YELLOW", "fill_opacity": 0.5, "stroke_width": 5, "stroke_color": "ORANGE"}}, {"id": "triangle_default", "type": "triangle", "properties": {}}, {"id": "triangle_custom", "type": "triangle", "properties": {"color": "PURPLE", "fill_opacity": 0.8, "stroke_width": 1, "stroke_color": "BLUE"}}, {"id": "line_default", "type": "line", "properties": {}}, {"id": "line_2d", "type": "line", "properties": {"start": [-3, 2], "end": [3, 2], "color": "RED", "stroke_width": 3}}, {"id": "line_3d", "type": "line", "properties": {"start": [-3, 0, 1], "end": [3, 0, 1], "color": "PINK", "stroke_width": 4}}, {"id": "arrow_default", "type": "arrow", "properties": {}}, {"id": "arrow_2d", "type": "arrow", "properties": {"start": [-3, -2], "end": [3, -2], "color": "TEAL", "stroke_width": 2}}, {"id": "arrow_3d", "type": "arrow", "properties": {"start": [-3, -3, 1], "end": [3, -3, 1], "color": "MAROON", "stroke_width": 3}}, {"id": "dot_default", "type": "dot", "properties": {}}, {"id": "dot_custom", "type": "dot", "properties": {"radius": 0.3, "color": "GOLD", "fill_opacity": 0.9}}, {"id": "star_default", "type": "star", "properties": {}}, {"id": "star_custom", "type": "star", "properties": {"radius": 1.8, "color": "PURPLE", "fill_opacity": 0.7, "n_points": 8}}, {"id": "text_default", "type": "text", "properties": {"content": "Default Text"}}, {"id": "text_custom", "type": "text", "properties": {"content": "Custom Text", "font_size": 52, "color": "YELLOW"}}, {"id": "tex_default", "type": "tex", "properties": {"content": "\\text{Default Te<PERSON>}"}}, {"id": "tex_custom", "type": "tex", "properties": {"content": "\\text{Custom TeX}", "font_size": 60, "color": "GREEN"}}, {"id": "math_default", "type": "math_tex", "properties": {"content": "1 + 1 = 2"}}, {"id": "math_custom", "type": "math_tex", "properties": {"content": "\\int_{a}^{b} f(x) dx", "font_size": 72, "color": "GREEN"}}, {"id": "image_default", "type": "image", "properties": {"file_path": "assets/placeholder.png"}}, {"id": "image_custom", "type": "image", "properties": {"file_path": "assets/custom.jpg", "height": 4.5}}, {"id": "graph_minimal", "type": "graph", "properties": {"x_range": [-5, 5, 1], "y_range": [-5, 5, 1]}}, {"id": "graph_full", "type": "graph", "properties": {"x_range": [-10, 10, 2], "y_range": [-10, 10, 2], "x_length": 7, "y_length": 5, "x_label": "Time (s)", "y_label": "Amplitude", "tips": true, "color": "LIGHT_GRAY"}}, {"id": "coord_minimal", "type": "coordinate_system", "properties": {"x_range": [-5, 5, 1], "y_range": [-5, 5, 1]}}, {"id": "coord_full", "type": "coordinate_system", "properties": {"x_range": [-8, 8, 1], "y_range": [-4, 4, 1], "x_length": 8, "y_length": 4, "x_label": "x", "y_label": "f(x)", "include_numbers": true, "tips": true, "color": "WHITE", "axis_config": {"color": "BLUE", "include_tip": true, "include_numbers": true}}}, {"id": "simple_group", "type": "group", "properties": {"members": ["circle_default", "square_default"]}}, {"id": "horizontal_group", "type": "group", "properties": {"members": ["circle_custom", "square_custom", "triangle_custom"], "layout": {"type": "horizontal", "buffer": 0.5}}}, {"id": "vertical_group", "type": "group", "properties": {"members": ["dot_custom", "star_custom", "text_custom"], "layout": {"type": "vertical", "buffer": 1.0, "alignment": "RIGHT"}}}, {"id": "complex_group", "type": "group", "properties": {"members": ["tex_custom", "math_custom", "line_3d", "arrow_3d"], "layout": {"type": "horizontal", "buffer": 0.75, "alignment": "DOWN"}}}], "actions": [{"type": "create", "target": "circle_default", "position": [-5, 3, 0], "animation": "fade_in", "duration": 0.3}, {"type": "create", "target": "circle_custom", "position": [-3, 3, 0], "animation": "grow", "duration": 0.3}, {"type": "create", "target": "square_default", "position": [-1, 3, 0], "animation": "fade_in", "duration": 0.3}, {"type": "create", "target": "square_custom", "position": [1, 3, 0], "animation": "grow", "duration": 0.3}, {"type": "create", "target": "rectangle_default", "position": [3, 3, 0], "animation": "fade_in", "duration": 0.3}, {"type": "create", "target": "rectangle_custom", "position": [5, 3, 0], "animation": "grow", "duration": 0.3}, {"type": "create", "target": "triangle_default", "position": [-5, 1, 0], "animation": "fade_in", "duration": 0.3}, {"type": "create", "target": "triangle_custom", "position": [-3, 1, 0], "animation": "grow", "duration": 0.3}, {"type": "create", "target": "line_default", "position": [-1, 1, 0], "animation": "draw", "duration": 0.3}, {"type": "create", "target": "line_2d", "animation": "draw", "duration": 0.3}, {"type": "create", "target": "line_3d", "animation": "draw", "duration": 0.3}, {"type": "create", "target": "arrow_default", "position": [3, 1, 0], "animation": "draw", "duration": 0.3}, {"type": "create", "target": "arrow_2d", "animation": "draw", "duration": 0.3}, {"type": "create", "target": "arrow_3d", "animation": "draw", "duration": 0.3}, {"type": "create", "target": "dot_default", "position": [-5, -1, 0], "animation": "fade_in", "duration": 0.3}, {"type": "create", "target": "dot_custom", "position": [-3, -1, 0], "animation": "grow", "duration": 0.3}, {"type": "create", "target": "star_default", "position": [-1, -1, 0], "animation": "fade_in", "duration": 0.3}, {"type": "create", "target": "star_custom", "position": [1, -1, 0], "animation": "grow", "duration": 0.3}, {"type": "create", "target": "text_default", "position": [3, -1, 0], "animation": "write", "duration": 0.3}, {"type": "create", "target": "text_custom", "position": [5, -1, 0], "animation": "write", "duration": 0.3}, {"type": "wait", "duration": 0.5}, {"type": "create", "target": "tex_default", "position": [-5, -3, 0], "animation": "write", "duration": 0.3}, {"type": "create", "target": "tex_custom", "position": [-3, -3, 0], "animation": "write", "duration": 0.3}, {"type": "create", "target": "math_default", "position": [-1, -3, 0], "animation": "write", "duration": 0.3}, {"type": "create", "target": "math_custom", "position": [1, -3, 0], "animation": "write", "duration": 0.3}, {"type": "create", "target": "image_default", "position": [3, -3, 0], "animation": "fade_in", "duration": 0.3}, {"type": "create", "target": "image_custom", "position": [5, -3, 0], "animation": "fade_in", "duration": 0.3}, {"type": "wait", "duration": 0.5}, {"type": "remove", "target": "circle_default", "animation": "fade_out", "duration": 0.1}, {"type": "remove", "target": "circle_custom", "animation": "fade_out", "duration": 0.1}, {"type": "remove", "target": "square_default", "animation": "fade_out", "duration": 0.1}, {"type": "remove", "target": "square_custom", "animation": "fade_out", "duration": 0.1}, {"type": "remove", "target": "rectangle_default", "animation": "fade_out", "duration": 0.1}, {"type": "remove", "target": "rectangle_custom", "animation": "fade_out", "duration": 0.1}, {"type": "remove", "target": "triangle_default", "animation": "fade_out", "duration": 0.1}, {"type": "remove", "target": "triangle_custom", "animation": "fade_out", "duration": 0.1}, {"type": "remove", "target": "line_default", "animation": "fade_out", "duration": 0.1}, {"type": "remove", "target": "line_2d", "animation": "fade_out", "duration": 0.1}, {"type": "remove", "target": "line_3d", "animation": "fade_out", "duration": 0.1}, {"type": "remove", "target": "arrow_default", "animation": "fade_out", "duration": 0.1}, {"type": "remove", "target": "arrow_2d", "animation": "fade_out", "duration": 0.1}, {"type": "remove", "target": "arrow_3d", "animation": "fade_out", "duration": 0.1}, {"type": "remove", "target": "dot_default", "animation": "fade_out", "duration": 0.1}, {"type": "remove", "target": "dot_custom", "animation": "fade_out", "duration": 0.1}, {"type": "remove", "target": "star_default", "animation": "fade_out", "duration": 0.1}, {"type": "remove", "target": "star_custom", "animation": "fade_out", "duration": 0.1}, {"type": "remove", "target": "text_default", "animation": "fade_out", "duration": 0.1}, {"type": "remove", "target": "text_custom", "animation": "fade_out", "duration": 0.1}, {"type": "remove", "target": "tex_default", "animation": "fade_out", "duration": 0.1}, {"type": "remove", "target": "tex_custom", "animation": "fade_out", "duration": 0.1}, {"type": "remove", "target": "math_default", "animation": "fade_out", "duration": 0.1}, {"type": "remove", "target": "math_custom", "animation": "fade_out", "duration": 0.1}, {"type": "remove", "target": "image_default", "animation": "fade_out", "duration": 0.1}, {"type": "remove", "target": "image_custom", "animation": "fade_out", "duration": 0.1}, {"type": "wait", "duration": 0.5}, {"type": "create", "target": "graph_minimal", "position": [-3, 0, 0], "animation": "draw", "duration": 0.5}, {"type": "create", "target": "graph_full", "position": [3, 0, 0], "animation": "draw", "duration": 0.5}, {"type": "plot_function", "target": "graph_minimal", "function": "np.sin(x)", "color": "RED", "duration": 0.5}, {"type": "plot_function", "target": "graph_full", "function": "x**2", "color": "BLUE", "x_range": [-5, 5], "duration": 0.5}, {"type": "wait", "duration": 0.5}, {"type": "remove", "target": "graph_minimal", "animation": "fade_out", "duration": 0.3}, {"type": "remove", "target": "graph_full", "animation": "fade_out", "duration": 0.3}, {"type": "wait", "duration": 0.3}, {"type": "create", "target": "coord_minimal", "position": [-3, 0, 0], "animation": "draw", "duration": 0.5}, {"type": "create", "target": "coord_full", "position": [3, 0, 0], "animation": "draw", "duration": 0.5}, {"type": "plot_function", "target": "coord_minimal", "function": "np.cos(x)", "color": "GREEN", "duration": 0.5}, {"type": "plot_function", "target": "coord_full", "function": "np.exp(-x**2/2)", "color": "ORANGE", "x_range": [-4, 4], "duration": 0.5}, {"type": "wait", "duration": 0.5}, {"type": "remove", "target": "coord_minimal", "animation": "fade_out", "duration": 0.3}, {"type": "remove", "target": "coord_full", "animation": "fade_out", "duration": 0.3}, {"type": "wait", "duration": 0.3}, {"type": "create", "target": "circle_custom", "position": [-2, 2, 0], "animation": "fade_in", "duration": 0.3}, {"type": "create", "target": "square_custom", "position": [0, 2, 0], "animation": "fade_in", "duration": 0.3}, {"type": "create", "target": "triangle_custom", "position": [2, 2, 0], "animation": "fade_in", "duration": 0.3}, {"type": "create", "target": "dot_custom", "position": [-2, -2, 0], "animation": "fade_in", "duration": 0.3}, {"type": "create", "target": "star_custom", "position": [0, -2, 0], "animation": "fade_in", "duration": 0.3}, {"type": "create", "target": "text_custom", "position": [2, -2, 0], "animation": "fade_in", "duration": 0.3}, {"type": "create", "target": "horizontal_group", "animation": "none", "duration": 0.1}, {"type": "create", "target": "vertical_group", "animation": "none", "duration": 0.1}, {"type": "wait", "duration": 0.5}, {"type": "remove", "target": "horizontal_group", "animation": "fade_out", "duration": 0.3}, {"type": "remove", "target": "vertical_group", "animation": "fade_out", "duration": 0.3}, {"type": "wait", "duration": 0.3}, {"type": "create", "target": "tex_custom", "position": [-3, 0, 0], "animation": "write", "duration": 0.3}, {"type": "create", "target": "math_custom", "position": [-1, 0, 0], "animation": "write", "duration": 0.3}, {"type": "create", "target": "line_3d", "position": [1, 0, 0], "animation": "draw", "duration": 0.3}, {"type": "create", "target": "arrow_3d", "position": [3, 0, 0], "animation": "draw", "duration": 0.3}, {"type": "create", "target": "complex_group", "animation": "none", "duration": 0.1}, {"type": "wait", "duration": 0.5}, {"type": "remove", "target": "complex_group", "animation": "fade_out", "duration": 0.3}, {"type": "wait", "duration": 0.3}, {"type": "create", "target": "simple_group", "animation": "none", "duration": 0.1}, {"type": "wait", "duration": 0.5}, {"type": "wait", "duration": 0.5}]}