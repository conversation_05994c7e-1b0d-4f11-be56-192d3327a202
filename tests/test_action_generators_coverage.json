{"metadata": {"title": "ActionGeneratorsCoverageTest", "author": "Coverage Test", "resolution": "1080p", "background_color": "DARK_GRAY"}, "objects": [{"id": "circle1", "type": "circle", "properties": {"radius": 1.0, "color": "BLUE", "fill_opacity": 0.5}}, {"id": "circle2", "type": "circle", "properties": {"radius": 1.0, "color": "RED", "fill_opacity": 0.5}}, {"id": "circle3", "type": "circle", "properties": {"radius": 1.0, "color": "GREEN", "fill_opacity": 0.5}}, {"id": "square1", "type": "square", "properties": {"side_length": 2.0, "color": "PURPLE", "fill_opacity": 0.5}}, {"id": "square2", "type": "square", "properties": {"side_length": 2.0, "color": "ORANGE", "fill_opacity": 0.5}}, {"id": "text1", "type": "text", "properties": {"content": "Action Generators Coverage Test", "color": "WHITE", "font_size": 36}}, {"id": "dot1", "type": "dot", "properties": {"color": "YELLOW"}}, {"id": "dot2", "type": "dot", "properties": {"color": "PINK"}}, {"id": "dot3", "type": "dot", "properties": {"color": "WHITE"}}, {"id": "line1", "type": "line", "properties": {"start": [-2, 0], "end": [2, 0], "color": "BLUE"}}, {"id": "graph1", "type": "graph", "properties": {"x_range": [-5, 5, 1], "y_range": [-3, 3, 1], "x_length": 6, "y_length": 4, "x_label": "x", "y_label": "f(x)", "tips": true}}, {"id": "graph2", "type": "graph", "properties": {"x_range": [-5, 5, 1], "y_range": [-3, 3, 1], "x_length": 6, "y_length": 4}}, {"id": "group1", "type": "group", "properties": {"members": ["dot1", "dot2", "dot3"]}}], "actions": [{"type": "create", "target": "text1", "position": [0, 3, 0], "animation": "write", "duration": 1.0}, {"type": "wait", "duration": 0.5}, {"type": "create", "target": "circle1", "position": [-3, 1, 0], "animation": "fade_in", "duration": 0.5}, {"type": "create", "target": "circle2", "position": [0, 1, 0], "animation": "grow", "duration": 0.5}, {"type": "create", "target": "circle3", "position": [3, 1, 0], "animation": "draw", "duration": 0.5}, {"type": "create", "target": "square1", "position": [-2, -1, 0], "animation": "write", "duration": 0.5}, {"type": "create", "target": "square2", "position": [2, -1, 0], "animation": "none", "duration": 0.0}, {"type": "create", "target": "line1", "position": [0, 2, 0], "animation": "draw", "duration": 0.5}, {"type": "wait", "duration": 0.5}, {"type": "move_to", "target": "circle1", "position": [-3, 0, 0], "path": "line", "duration": 0.5}, {"type": "move_to", "target": "circle2", "position": [0, 0, 0], "path": "arc", "duration": 0.5}, {"type": "move_to", "target": "circle3", "position": [3, 0, 0], "duration": 0.5}, {"type": "transform", "target": "square1", "properties": {"color": "YELLOW", "fill_opacity": 0.8, "scale": 1.2}, "easing": "smooth", "duration": 0.8}, {"type": "transform", "target": "square2", "properties": {"color": "RED", "rotation": 45, "scale": [0.8, 1.2, 1.0]}, "easing": "linear", "duration": 0.8}, {"type": "transform", "target": "circle1", "properties": {"position": [-3, -1, 0]}, "duration": 0.5}, {"type": "indicate", "target": "circle2", "color": "YELLOW", "scale_factor": 1.3, "duration": 0.5}, {"type": "indicate", "target": "circle3", "duration": 0.5}, {"type": "wait", "duration": 0.3}, {"type": "remove", "target": "square1", "animation": "fade_out", "duration": 0.5}, {"type": "remove", "target": "square2", "animation": "shrink", "duration": 0.5}, {"type": "remove", "target": "circle3", "animation": "unwrite", "duration": 0.5}, {"type": "remove", "target": "text1", "animation": "none", "duration": 0.0}, {"type": "wait", "duration": 0.5}, {"type": "create", "target": "dot1", "position": [-2, 0, 0], "animation": "fade_in", "duration": 0.3}, {"type": "create", "target": "dot2", "position": [0, 0, 0], "animation": "fade_in", "duration": 0.3}, {"type": "create", "target": "dot3", "position": [2, 0, 0], "animation": "fade_in", "duration": 0.3}, {"type": "create", "target": "group1", "animation": "none", "duration": 0.0}, {"type": "animate_group", "actions": [{"type": "move_to", "target": "dot1", "position": [-2, 1, 0], "duration": 0.5}, {"type": "move_to", "target": "dot2", "position": [0, 1.5, 0], "duration": 0.5}, {"type": "move_to", "target": "dot3", "position": [2, 1, 0], "duration": 0.5}, {"type": "transform", "target": "circle1", "properties": {"scale": 1.2}, "duration": 0.5}, {"type": "transform", "target": "circle2", "properties": {"scale": 0.8}, "duration": 0.5}], "duration": 0.8}, {"type": "wait", "duration": 0.3}, {"type": "arrange", "target": "dot1", "relative_to": "circle1", "direction": "LEFT", "buffer": 0.2, "duration": 0.5}, {"type": "arrange", "target": "dot3", "relative_to": "SCREEN_RIGHT", "direction": "LEFT", "buffer": 0.5, "align_edge": "UP", "duration": 0.5}, {"type": "wait", "duration": 0.3}, {"type": "align", "targets": ["dot1", "dot2", "dot3"], "reference_target": "circle1", "edge": "BOTTOM", "duration": 0.5}, {"type": "align", "targets": ["dot1", "dot2", "dot3"], "edge": "CENTER_Y", "duration": 0.5}, {"type": "align", "targets": ["dot1", "dot2"], "reference_target": "SCREEN_TOP", "edge": "TOP", "duration": 0.5}, {"type": "wait", "duration": 0.3}, {"type": "distribute", "targets": ["dot1", "dot2", "dot3"], "direction": "HORIZONTAL", "spacing": 2.0, "duration": 0.5}, {"type": "distribute", "targets": ["circle1", "circle2", "dot3"], "direction": "VERTICAL", "reference_frame": {"start_edge_of": "SCREEN_TOP", "end_edge_of": "SCREEN_BOTTOM", "mode": "centers"}, "duration": 0.5}, {"type": "distribute", "targets": ["dot1", "dot2", "dot3"], "direction": "HORIZONTAL", "reference_frame": {"start_edge_of": "SCREEN_LEFT", "end_edge_of": "SCREEN_RIGHT", "mode": "edges"}, "duration": 0.5}, {"type": "wait", "duration": 0.3}, {"type": "highlight", "target": "dot2", "color": "WHITE", "duration": 0.5}, {"type": "focus", "target": "dot1", "scale_factor": 1.5, "opacity_factor": 0.3, "duration": 0.8}, {"type": "wait", "duration": 0.3}, {"type": "camera", "action": "zoom", "scale": 1.5, "duration": 0.8}, {"type": "camera", "action": "pan", "position": [2, 1, 0], "duration": 0.8}, {"type": "camera", "action": "move_to", "position": [-2, 0, 0], "duration": 0.8}, {"type": "camera", "action": "reset", "duration": 0.8}, {"type": "wait", "duration": 0.3}, {"type": "replace", "source": "dot1", "target": "circle3", "animation": "transform", "position_match": true, "duration": 0.8}, {"type": "wait", "duration": 0.5}, {"type": "create", "target": "graph1", "position": [-3, -2, 0], "animation": "draw", "duration": 0.8}, {"type": "plot_function", "target": "graph1", "function": "np.sin(x)", "color": "RED", "x_range": [-5, 5], "animation": "draw", "duration": 0.8}, {"type": "add_point", "target": "graph1", "position": [2, 0.9], "color": "GREEN", "label": "A", "animation": "grow", "duration": 0.5}, {"type": "wait", "duration": 0.3}, {"type": "create", "target": "graph2", "position": [3, -2, 0], "animation": "draw", "duration": 0.8}, {"type": "plot_function", "target": "graph2", "function": "x**2", "color": "BLUE", "animation": "none", "duration": 0.0}, {"type": "add_point", "target": "graph2", "position": [1, 1], "color": "YELLOW", "animation": "fade_in", "duration": 0.5}, {"type": "add_point", "target": "graph2", "position": [-1, 1], "animation": "none", "duration": 0.0}, {"type": "wait", "duration": 0.5}]}