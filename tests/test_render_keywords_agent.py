# 导入必要的库

from tools.render_keywords_toolkit import RenderKeywordsToolkit

# 初始化工具
render_toolkit = RenderKeywordsToolkit()

# 生成关键词动画代码
keywords = ["局限性", "特定任务", "动作集稀疏", "GAIA任务成本", "OpenAI模型"]
topic = "DynaSaur框架如何通过动态动作创建，让LLM代理更智能，解决现实世界难题？"
step = 1
animation_code = render_toolkit.generate_keywords_animation_code(keywords, step)
code_lines = [
    "from manim import *",
    "import os",
    "config.pixel_height = 1920",
    "config.pixel_width = 1090",
    "config.frame_height = 16.0",
    "config.frame_width = 9.0",
    "from manim_funcs.anim_funcs import *",
    "class GeneratedAnimation(FeynmanScene):",
    "    def construct(self):",
    "        def print_current_time():",
    "            current_time = self.renderer.time",
    "            return current_time",
    "        begin_time = print_current_time()",
    "        end_time = print_current_time()",
]
code_lines.append(f'        self.display_slogo("{topic}", {step})')
code_lines.append("        anim = self.render_keywords_tool()")
code_lines.append("        self.play(anim[0])")
code_lines.extend(animation_code)
with open("render_keywords.py", "w", encoding="utf-8") as f:
    f.write("\n".join(code_lines))
