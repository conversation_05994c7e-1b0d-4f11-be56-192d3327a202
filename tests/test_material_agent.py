#!/usr/bin/env python3
"""
测试视频素材规范化代理的功能

此脚本使用MaterialAgent进行视频素材规范化处理，支持CLI参数。
"""

import os
import sys
import argparse
import json
from pathlib import Path

# 添加项目根目录到Python路径
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
ROOT_DIR = os.path.dirname(SCRIPT_DIR)
sys.path.append(ROOT_DIR)

# 导入MaterialAgent
from agents.material_agent import MaterialAgent, logger

def main():
    """主函数：处理命令行参数并运行素材规范化流程"""
    
    # 定义命令行参数
    parser = argparse.ArgumentParser(description="视频素材规范化处理工具")
    parser.add_argument("--config", default="config/config.yaml", help="配置文件路径")
    parser.add_argument("--material", default="ai_trends.md", help="原始素材文件路径")
    parser.add_argument("--purpose", 
                       default="给技术爱好者介绍这些AI项目，主要目的是分析趋势和热点，侧重客观中立的表述，视频长度为3分钟", 
                       help="目的描述")
    parser.add_argument("--output", default="output/test_material_output.md", help="输出文件路径")
    parser.add_argument("--rounds", type=int, default=1, help="优化轮数")
    parser.add_argument("--verbose", action="store_true", help="显示详细输出")
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        import logging
        logger.setLevel(logging.DEBUG)
    
    print("\n====== 视频素材规范化测试 ======")
    print(f"配置文件: {args.config}")
    print(f"素材文件: {args.material}")
    print(f"目的描述: {args.purpose}")
    print(f"输出文件: {args.output}")
    print(f"优化轮数: {args.rounds}")
    print("================================\n")
    
    # 创建MaterialAgent实例
    agent = MaterialAgent(config_path=args.config)
    
    # 检查素材文件是否存在
    material_path = args.material
    if not os.path.exists(material_path):
        print(f"错误: 素材文件 {material_path} 不存在")
        return 1
    
    # 确保输出目录存在
    output_dir = os.path.dirname(args.output)
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    # 运行处理流程
    try:
        print("开始处理素材...")
        result = agent.run(
            material_file_path=material_path,
            purpose_description=args.purpose,
            output_file=args.output,
            max_rounds=args.rounds
        )
        
        # 处理结果
        if result["success"]:
            print("\n✅ 处理成功!")
            
            # 显示多媒体元素统计
            mm_elements = result.get("multimedia_elements", {})
            print(f"\n📊 多媒体元素统计:")
            print(f"  - 图片: {mm_elements.get('images_count', 0)} 个")
            print(f"  - 代码块: {mm_elements.get('code_blocks_count', 0)} 个")
            
            # 显示目的分析结果
            purpose = result.get("purpose_analysis", {})
            print(f"\n🎯 目的分析结果:")
            print(f"  - 目标受众: {purpose.get('target_audience', '未指定')}")
            print(f"  - 内容主题: {purpose.get('content_theme', '未指定')}")
            print(f"  - 意图分析: {purpose.get('intention', '未指定')}")
            print(f"  - 风格偏好: {purpose.get('style_preference', '未指定')}")
            print(f"  - 视频长度: {purpose.get('video_length', '未指定')}")
            
            # 显示处理统计
            orig_len = result.get("original_length", 0)
            init_len = result.get("initial_material_length", 0)
            final_len = result.get("final_material_length", 0)
            
            print(f"\n📈 内容变化统计:")
            print(f"  - 原始素材长度: {orig_len} 字符")
            print(f"  - 初始规范化长度: {init_len} 字符 ({(init_len/orig_len*100):.1f}%)")
            print(f"  - 最终优化长度: {final_len} 字符 ({(final_len/orig_len*100):.1f}%)")
            
            # 显示保存信息
            saved_file = result.get("saved_file", "")
            if saved_file and os.path.exists(saved_file):
                file_size = os.path.getsize(saved_file)
                print(f"\n💾 规范化素材已保存:")
                print(f"  - 文件路径: {saved_file}")
                print(f"  - 文件大小: {file_size/1024:.1f} KB")
                
                # 显示部分内容预览
                try:
                    with open(saved_file, "r", encoding="utf-8") as f:
                        content = f.read()
                    
                    # 提取前几行作为预览
                    preview_lines = content.split("\n")[:15]
                    preview = "\n".join(preview_lines)
                    if len(preview_lines) < len(content.split("\n")):
                        preview += "\n...(更多内容省略)..."
                    
                    print(f"\n👁️ 规范化素材预览:\n")
                    print(f"{preview}\n")
                except Exception as e:
                    print(f"\n无法显示预览: {e}")
            else:
                print(f"\n⚠️ 注意: 未找到保存的文件 {saved_file}")
        else:
            # 处理失败
            print(f"\n❌ 处理失败: {result.get('error', '未知错误')}")
            return 1
            
    except Exception as e:
        import traceback
        print(f"\n❌ 发生错误: {e}")
        print(traceback.format_exc())
        return 1
        
    return 0

if __name__ == "__main__":
    sys.exit(main()) 