#!/usr/bin/env python3
"""
测试使用真实example_gen_prompt.py的DSPy优化

验证原始prompt模板的优化效果
"""

import sys
from pathlib import Path

import dspy

# 添加项目路径
sys.path.append(str(Path(__file__).parent))


from tools.dspy_prompt_optimizer import DSPyPromptOptimizer


def test_prompt_loading():
    """测试prompt模板加载和转换"""
    print("📝 测试prompt模板加载和转换")

    try:
        optimizer = DSPyPromptOptimizer()

        # 加载原始prompt
        original_prompt = optimizer.load_base_prompt_template()

        print(f"✅ 成功加载prompt，长度: {len(original_prompt)} 字符")

        # 检查变量转换
        if "{topic}" in original_prompt and "{purpose}" in original_prompt and "{audience}" in original_prompt:
            print("✅ 变量格式转换成功")
        else:
            print("❌ 变量格式转换失败")

        # 显示prompt预览
        print("\n📄 转换后的prompt预览:")
        print("-" * 60)
        print(original_prompt[:500] + "..." if len(original_prompt) > 500 else original_prompt)
        print("-" * 60)

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_optimization_with_real_prompt():
    """使用真实prompt进行优化测试"""
    print("\n🔧 使用真实prompt进行优化测试")

    try:
        optimizer = DSPyPromptOptimizer()

        # 加载真实prompt
        real_prompt = optimizer.load_base_prompt_template()

        print("开始优化真实prompt...")
        print("⏳ 这可能需要几分钟时间...")

        # 执行优化
        optimized_prompt = optimizer.optimize_prompt(
            base_prompt=real_prompt,
            max_few_shots=1,  # 减少few-shot数量以加快速度
            optimizer_type="MIPROv2",
        )

        print("\n✅ 优化完成！")
        print(f"原始prompt长度: {len(real_prompt)} 字符")
        print(f"优化后长度: {len(optimized_prompt)} 字符")

        # 保存优化结果
        output_dir = Path("output/real_prompt_optimization")
        output_dir.mkdir(parents=True, exist_ok=True)

        with open(output_dir / "original_prompt.txt", "w", encoding="utf-8") as f:
            f.write(real_prompt)

        with open(output_dir / "optimized_prompt.txt", "w", encoding="utf-8") as f:
            f.write(optimized_prompt)

        print(f"💾 结果已保存到: {output_dir}")

        return optimized_prompt

    except Exception as e:
        print(f"❌ 优化失败: {e}")
        return None


def test_optimized_generation():
    """测试优化后prompt的生成效果"""
    print("\n🧪 测试优化后prompt的生成效果")

    try:
        optimizer = DSPyPromptOptimizer()

        # 先进行优化
        optimized_prompt = test_optimization_with_real_prompt()
        if not optimized_prompt:
            print("❌ 无法获取优化后的prompt")
            return False

        # 测试用例
        test_cases = [
            {
                "topic": "深度学习中的注意力机制",
                "purpose": "帮助AI研究者理解Transformer的核心创新",
                "audience": "机器学习研究生",
            },
            {
                "topic": "量子计算的量子比特",
                "purpose": "让物理爱好者理解量子信息的基本单位",
                "audience": "对量子物理感兴趣的大学生",
            },
        ]

        # 创建测试程序
        class TestSignature(dspy.Signature):
            f"""{optimized_prompt}"""
            prompt = dspy.InputField()
            generation = dspy.OutputField()

        from tools.dspy_prompt_optimizer import CustomPredict

        test_program = CustomPredict(TestSignature)

        results = []
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🎯 测试案例 {i}: {test_case['topic']}")

            # 构建测试输入
            test_input = f"请为主题'{test_case['topic']}'生成一个教学例子，目的是{test_case['purpose']}，面向{test_case['audience']}。"

            # 生成结果
            result = test_program(prompt=test_input)
            generated = result.generation

            print(f"✅ 生成完成，长度: {len(generated)} 字符")

            # 检查生成质量
            quality_indicators = {
                "包含分镜结构": "<SCENE_OUTLINE>" in generated or "分镜" in generated,
                "包含具体数据": len([c for c in generated if c.isdigit()]) >= 10,
                "包含视觉描述": "视觉" in generated or "动画" in generated or "变换" in generated,
                "包含核心洞察": "洞察" in generated or "Aha" in generated or "顿悟" in generated,
            }

            print("📊 质量指标:")
            for indicator, passed in quality_indicators.items():
                status = "✅" if passed else "❌"
                print(f"  {status} {indicator}")

            # 评估质量分数
            quality_result = optimizer.quality_judge(
                topic=test_case["topic"],
                purpose=test_case["purpose"],
                target_audience=test_case["audience"],
                example_content=generated,
            )

            print(f"🎯 质量评分: {quality_result.score}/100")

            results.append(
                {
                    "test_case": test_case,
                    "generated": generated,
                    "quality_score": quality_result.score,
                    "quality_indicators": quality_indicators,
                }
            )

        # 计算平均分数
        avg_score = sum(r["quality_score"] for r in results) / len(results)
        print(f"\n📈 平均质量分数: {avg_score:.2f}/100")

        # 保存测试结果
        output_dir = Path("output/real_prompt_optimization")
        import json

        with open(output_dir / "test_results.json", "w", encoding="utf-8") as f:
            # 简化结果以便保存
            simplified_results = []
            for r in results:
                simplified_results.append(
                    {
                        "topic": r["test_case"]["topic"],
                        "quality_score": r["quality_score"],
                        "quality_indicators": r["quality_indicators"],
                        "generated_length": len(r["generated"]),
                    }
                )
            json.dump(simplified_results, f, ensure_ascii=False, indent=2)

        print("💾 测试结果已保存")

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 测试真实prompt模板的DSPy优化")
    print("=" * 60)

    success_count = 0
    total_tests = 3

    # 测试1: prompt加载
    print("1️⃣ 测试prompt模板加载...")
    if test_prompt_loading():
        success_count += 1

    # 测试2: 优化过程
    print("\n2️⃣ 测试优化过程...")
    if test_optimization_with_real_prompt():
        success_count += 1

    # 测试3: 生成效果
    print("\n3️⃣ 测试生成效果...")
    if test_optimized_generation():
        success_count += 1

    print(f"\n📊 测试结果: {success_count}/{total_tests} 通过")

    if success_count >= 2:  # 至少2个测试通过就算成功
        print("🎉 真实prompt优化测试基本成功！")
        print("\n💡 下一步建议:")
        print("- 查看 output/real_prompt_optimization/ 目录的结果")
        print("- 比较优化前后的prompt差异")
        print("- 分析生成例子的质量改进")
        return True
    else:
        print("⚠️ 测试未完全通过，需要进一步调试")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
