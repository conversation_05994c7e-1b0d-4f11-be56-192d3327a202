#!/bin/bash
# 合并 media/videos/render_storyboard_* 下的视频和字幕文件，并最终生成一个完整视频

# 设置变量
VIDEO_DIR="media/videos"
STORYBOARD_PATTERN="render_storyboard_*"
OUTPUT_DIR="media/merged"
FINAL_OUTPUT="final_output.mp4"
TEMP_FILE="temp_file_list.txt"

# 检查 ffmpeg 是否安装
if ! command -v ffmpeg &> /dev/null; then
    echo "错误: 未找到 ffmpeg。请先安装 ffmpeg。"
    exit 1
fi

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 清空临时文件列表
> "$TEMP_FILE"

echo "开始处理视频文件..."

# 直接列出所有 render_storyboard_* 目录
storyboard_dirs=($VIDEO_DIR/$STORYBOARD_PATTERN)

# 检查是否找到了任何目录
if [ ${#storyboard_dirs[@]} -eq 0 ] || [ ! -d "${storyboard_dirs[0]}" ]; then
    echo "错误: 没有找到任何 $STORYBOARD_PATTERN 目录"
    exit 1
fi

echo "找到 ${#storyboard_dirs[@]} 个渲染目录:"
for dir in "${storyboard_dirs[@]}"; do
    echo "- $(basename "$dir")"
done

# 处理每个目录
for dir in "${storyboard_dirs[@]}"; do
    dir_name=$(basename "$dir")
    echo -e "\n处理目录: $dir_name"

    # 查找目录中的视频文件，排除 partial_movie_files 目录中的文件
    video_files=($(find "$dir" -name "*video.mp4" -o -name "*.mp4" | grep -v "partial_movie_files" | sort))

    # 检查是否找到视频文件
    if [ ${#video_files[@]} -eq 0 ]; then
        echo "  警告: 在 $dir 中没有找到视频文件，跳过。"
        continue
    fi

    echo "  找到 ${#video_files[@]} 个视频文件"

    for video_file in "${video_files[@]}"; do
        # 再次确认不处理 partial_movie_files 目录中的文件
        if [[ "$video_file" == *"partial_movie_files"* ]]; then
            echo "  跳过 partial_movie_files 中的文件: $(basename "$video_file")"
            continue
        fi

        video_base=$(basename "$video_file" .mp4)
        video_dir=$(dirname "$video_file")
        output_file="$OUTPUT_DIR/${dir_name}_${video_base}_merged.mp4"

        # 查找对应的字幕文件
        srt_file=$(find "$video_dir" -name "${video_base}.srt" 2>/dev/null)

        echo "  处理视频: $(basename "$video_file")"

        if [ -n "$srt_file" ] && [ -f "$srt_file" ]; then
            echo "  找到字幕: $(basename "$srt_file")"

            # 使用更可靠的方式合并视频和硬编码字幕，同时保留原始音频
            ffmpeg -i "$video_file" -vf "subtitles=$srt_file:force_style='Alignment=2,MarginV=60,fontcolor=white,Fontsize=9,fontweight=bold,FontName=微软雅黑'" \
                  -c:a copy -max_muxing_queue_size 9999 -y "$output_file"
            
            echo "  已合并视频和字幕: $output_file"
        else
            echo "  未找到对应的字幕文件，仅复制视频。"
            # 如果没有字幕文件，则只复制视频，确保保留所有流
            ffmpeg -i "$video_file" -c copy -y "$output_file"
        fi

        # 验证处理后的视频文件是否存在
        if [ ! -f "$output_file" ]; then
            echo "  错误: 无法创建输出文件: $output_file"
            continue
        fi

        # 检查视频的时长是否与原视频相同
        original_duration=$(ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "$video_file")
        output_duration=$(ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "$output_file")
        
        echo "  原始视频时长: $original_duration 秒"
        echo "  处理后视频时长: $output_duration 秒"
        
        # 将合并后的视频添加到列表中
        echo "file '$output_file'" >> "$TEMP_FILE"
    done
done

# 检查是否有视频文件需要合并
if [ ! -s "$TEMP_FILE" ]; then
    echo "错误: 没有找到任何视频文件可以合并。"
    rm -f "$TEMP_FILE"
    exit 1
fi

echo -e "\n所有视频文件处理完成。开始合并最终视频..."

# 使用改进的方法合并所有视频，确保保留原视频的长度、字幕及声音
# 首先检查是否所有输入文件都有效
echo "检查所有输入文件是否有效..."
all_files_valid=true
while IFS= read -r line; do
    if [[ "$line" =~ ^file\ \'(.+)\'$ ]]; then
        file_path="${BASH_REMATCH[1]}"
        if [ ! -f "$file_path" ]; then
            echo "错误: 文件不存在: $file_path"
            all_files_valid=false
        fi
    fi
done < "$TEMP_FILE"

if [ "$all_files_valid" = false ]; then
    echo "错误: 有一些输入文件不存在，无法合并视频。"
    exit 1
fi

# 使用concat demuxer合并视频，这种方式对于相同格式的视频效果最好
echo "开始合并视频..."
ffmpeg -f concat -safe 0 -i "$TEMP_FILE" -c copy "$FINAL_OUTPUT" -y

# 检查最终合并是否成功
if [ $? -eq 0 ]; then
    echo "成功: 所有视频已合并为 $FINAL_OUTPUT"
    echo "文件大小: $(du -h "$FINAL_OUTPUT" | cut -f1)"
    final_duration=$(ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "$FINAL_OUTPUT")
    echo "视频时长: $final_duration 秒"
    
    # 检查最终视频是否有音频流
    audio_streams=$(ffprobe -v error -select_streams a -show_entries stream=codec_type -of csv=p=0 "$FINAL_OUTPUT" | wc -l)
    echo "音频流数量: $audio_streams"
    
    # 尝试检测字幕流或硬编码字幕
    subtitle_streams=$(ffprobe -v error -select_streams s -show_entries stream=codec_type -of csv=p=0 "$FINAL_OUTPUT" | wc -l)
    echo "字幕流数量: $subtitle_streams (如果为0，字幕可能是硬编码的)"
    
    echo "视频合并完成！请检查 $FINAL_OUTPUT 文件的质量。"
else
    echo "错误: 视频合并失败。"
    # 如果合并失败，尝试使用替代方法
    echo "尝试使用替代方法合并视频..."
    ffmpeg -f concat -safe 0 -i "$TEMP_FILE" -c:v copy -c:a copy -c:s copy "$FINAL_OUTPUT" -y
    
    if [ $? -eq 0 ]; then
        echo "成功: 使用替代方法合并视频。"
        echo "文件大小: $(du -h "$FINAL_OUTPUT" | cut -f1)"
        echo "视频时长: $(ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "$FINAL_OUTPUT") 秒"
    else
        echo "错误: 两种方法都无法合并视频。"
        exit 1
    fi
fi

# 清理临时文件
rm -f "$TEMP_FILE"

echo "处理完成!"
