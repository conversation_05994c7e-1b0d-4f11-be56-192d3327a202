#!/usr/bin/env python3
"""
Test script to verify chart scaling modifications
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from manim import *
from dsl.v2.core.scene import FeynmanScene
from dsl.v2.animation_functions.animate_chart import animate_chart

class TestChartScale(FeynmanScene):
    def construct(self):
        # Test data for bar chart
        bar_data = {
            "苹果": 75,
            "香蕉": 120,
            "橙子": 90,
            "葡萄": 65,
            "菠萝": 85
        }
        
        # Test bar chart with smaller scale
        animate_chart(
            scene=self,
            chart_type="bar",
            data=bar_data,
            title="水果销量对比 (调整后尺寸)",
            narration="这是调整尺寸后的条形图测试",
            x_label="水果种类",
            y_label="销量 (千克)",
            animation_style="fadeIn"
        )
        
        self.wait(2)
        
        # Test line chart
        line_data = [
            {"第一季度": 50, "第二季度": 65, "第三季度": 80, "第四季度": 70},
            {"第一季度": 40, "第二季度": 50, "第三季度": 60, "第四季度": 90}
        ]
        
        self.clear()
        
        animate_chart(
            scene=self,
            chart_type="line",
            data=line_data,
            title="季度销售对比 (调整后尺寸)",
            dataset_names=["产品A", "产品B"],
            narration="这是调整尺寸后的折线图测试",
            x_label="季度",
            y_label="销售额 (万元)",
            animation_style="grow"
        )
        
        self.wait(2)

if __name__ == "__main__":
    print("Testing chart scale modifications...")
    print("Available test modes:")
    print("1. Preview mode: manim test_chart_scale.py TestChartScale -pql")
    print("2. Low quality: manim test_chart_scale.py TestChartScale -ql") 
    print("3. High quality: manim test_chart_scale.py TestChartScale -qh") 