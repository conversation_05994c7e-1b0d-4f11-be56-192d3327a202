常见Manim代码错误和注意事项：

1. MathTex中不能使用中文字符，需要使用Text或MarkupText来显示中文
2. 导入语句要完整，确保from manim import *在文件开头
3. Scene类必须继承自Scene，构造函数中调用super().__init__()
4. 颜色使用时要用正确的格式，如RED, BLUE, GREEN或"#FF0000"。<PERSON><PERSON>中不存在 ORANGE、
5. 位置设置使用UP, DOWN, LEFT, RIGHT或具体坐标如np.array([1, 2, 0])。Man<PERSON>中不存在 CENTER，
6. 文本对象创建后要设置合适的字体大小，避免过大或过小
7. 动画播放使用self.play()，等待使用self.wait()
8. 数学公式在MathTex中使用LaTeX语法，注意转义字符
9. 图形对象的属性设置要在创建时或使用set_*方法
10. VGroup用于组合多个对象，便于统一操作
11. Transform动画需要源对象和目标对象
12. 不要使用ShowCreation，都直接改成新版的Create
13. 避免在同一帧中对同一对象进行多个冲突的动画
14. 文件路径使用相对路径，确保资源文件存在
15. 场景渲染质量参数：-ql(低质量), -qm(中等), -qh(高质量)
16. ReplacementTransform 需要源和目标对象子元素数量相同，不同则用 FadeOut+FadeIn，比如old_group = VGroup(text1, text2) # 2个元素 new_group = VGroup(rect1, rect2, rect3)  # 3个元素 self.play(ReplacementTransform(old_group, new_group)) # 报错！
17.确保传给 VGroup 的参数都是 VMobject 对象，字符串需要先转换为 Text 或 MathTex 对象
18. 注意调用函数的参数数量是否匹配，比如create_left_auxiliary_content() 方法只接受1个参数，但代码传递了2个参数（title, items），需要合并成一个VGroup
19. 禁止使用LIGHT_BLUE、LIGHT_GREEN，DARK_RED，DARK_GREEN，BROWN等这些没有的颜色，改成对应的BLUE、GREEN，RED、GREEN
20. 在访问self.create_main_region_content['main'][0][1]子元素时注意加上[0]，因为存的是VGroup