import json
import os
import re
from math import ceil

import cv2
import wcwidth
from loguru import logger
from manim import *
from manim_voiceover import VoiceoverScene

from manim_funcs.video_mobject import VideoMobject
from utils.util import *
from visual_system.elements.custom_array import ArrayElement
from visual_system.elements.custom_code import CodeElement
from visual_system.elements.text import TextElement

DEFAULT_FONT = "LXGW WenKai Mono"
DEFAULT_FONT_SIZE = 24
INOUT_FACTOR = 1e-6

LAYOUTS = {
    # 上半部分
    "upper_half": Rectangle(height=config.frame_height / 2, width=config.frame_width).to_edge(UP),
    # 右上1/4
    "right_upper_quarter": Rectangle(height=config.frame_height / 2, width=config.frame_width / 2).to_corner(UR),
    # 右半部分
    "right_half": Rectangle(height=config.frame_height, width=config.frame_width / 2).to_edge(RIGHT),
    # 全屏
    "full_screen": Rectangle(height=config.frame_height, width=config.frame_width),
    # 左半部分
    "left_half": Rectangle(height=config.frame_height, width=config.frame_width / 2).to_edge(LEFT),
    # 中间: x~[-7,7], y~[-3,3]
    "origin_x14y6": Rectangle(height=config.frame_height * 3 / 4, width=config.frame_width).move_to(ORIGIN),
    # 上1/8 [title]
    "upper_one_eighth": Rectangle(height=config.frame_height / 8, width=config.frame_width).to_edge(UP),
    # 下1/8 [subtitle]
    "lower_one_eighth": Rectangle(height=config.frame_height / 8, width=config.frame_width).to_edge(DOWN),
}


def add_wrapped_subcaption(
    self,
    subcaption: str,
    duration: float,
    subcaption_buff: float = 0.1,
    max_subcaption_len: int = 70,
) -> None:
    """
    使用 wcwidth 结合正则表达式对文本进行分词，确保不会在单词内部或标点前拆分，
    同时根据各段显示宽度按比例分配语音时长，从而保证语音与字幕对齐。
    """
    # 清理多余空格
    subcaption = " ".join(subcaption.split())
    total_width = wcwidth.wcswidth(subcaption)
    n_chunks = ceil(total_width / max_subcaption_len)
    target_width = total_width / n_chunks

    # 正则将文本拆分为：CJK单字符、英文单词、标点符号和空白（保留空格）
    token_pattern = r"[\u4e00-\u9fff]|[A-Za-z0-9]+|[^\w\s\u4e00-\u9fff]+|\s+"
    tokens = re.findall(token_pattern, subcaption)

    chunks_ = []
    current_chunk = ""
    current_width = 0

    for token in tokens:
        token_width = wcwidth.wcswidth(token)
        # 当累计宽度超出目标宽度，并且当前已有内容时分割
        if current_chunk and (current_width + token_width > target_width):
            # 避免在标点前拆分：若 token 非空且首字符非字母数字（例如标点）
            if token.strip() and not token[0].isalnum():
                current_chunk += token
                current_width += token_width
                chunks_.append(current_chunk)
                current_chunk = ""
                current_width = 0
            else:
                chunks_.append(current_chunk)
                current_chunk = token
                current_width = token_width
        else:
            current_chunk += token
            current_width += token_width

    if current_chunk:
        chunks_.append(current_chunk)

    # 如果最后一段宽度太小，则与前一段合并（允许超过max_subcaption_len）
    if len(chunks_) > 1 and wcwidth.wcswidth(chunks_[-1]) < (target_width / 4):
        chunks_[-2] += chunks_[-1]
        chunks_.pop()

    # 根据每一段的实际宽度分配时长比例
    actual_total = sum(wcwidth.wcswidth(chunk) for chunk in chunks_)
    subcaption_weights = [wcwidth.wcswidth(chunk) / actual_total for chunk in chunks_]

    current_offset = 0
    for idx, caption in enumerate(chunks_):
        chunk_duration = duration * subcaption_weights[idx]
        self.add_subcaption(
            caption,
            duration=max(chunk_duration - subcaption_buff, 0),
            offset=current_offset,
        )
        current_offset += chunk_duration


VoiceoverScene.add_wrapped_subcaption = add_wrapped_subcaption


class CustomEncoder(json.JSONEncoder):
    def encode(self, obj):
        if isinstance(obj, list):
            return (
                "[\n"
                + ",\n".join(
                    ["  " + ", ".join(json.dumps(item) for item in obj[i : i + 2]) for i in range(0, len(obj), 2)],
                )
                + "\n]"
            )
        return json.JSONEncoder.encode(self, obj)


class FeynmanScene(VoiceoverScene):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def render_keywords(self, keywords: list[str]) -> tuple[list[Animation], list[Animation]]:
        """生成关键词垂直排列的动画效果。

        为每个关键词创建带有背景的文本对象，并将它们垂直排列显示。
        第一个关键词位于画面中心偏上的位置，后续关键词垂直向下排列。

        Args:
            keywords: 要显示的关键词列表

        Returns:
            包含两个动画列表的元组:
            - 第一个列表包含背景矩形的动画
            - 第二个列表包含文本对象的动画
        """
        if not keywords:
            logger.warning("没有提供关键词，返回空动画列表")
            return []

        # 创建文本对象
        text_objects = self._create_text_objects(keywords)

        # 创建动画列表
        background_animations, text_animations = [], []  # 背景矩形动画, 文本动画

        # 应用布局和创建动画
        self._create_vertical_layout(text_objects, background_animations, text_animations)
        anim_obj = []
        for idx, obj in enumerate(background_animations):
            # anim_obj.append(Succession(*[obj, text_animations[idx]]))
            pair = Succession(obj, text_animations[idx])
            anim_obj.append(pair)

        return anim_obj

        # return [background_animations, text_animations]

    def render_sort_animation(
        self, data: list, algorithm: str = "bubble", layout="horizontal", layout_area="full_screen"
    ) -> list:
        """生成数组排序的动画效果。

        创建一个可视化的数组排序动画，支持多种排序算法。
        动画过程中会显示元素交换和比较的过程，直观展示排序算法的工作原理。

        Args:
            data: 要排序的数据列表
            algorithm: 排序算法类型，支持 "bubble"(冒泡排序), "quick"(快速排序), "selection"(选择排序), "insertion"(插入排序)
            layout: 数组布局方式，支持 "horizontal"(水平), "vertical"(垂直)
            layout_area: 布局区域名称，决定动画在屏幕中的位置和大小，默认为"full_screen"

        Returns:
            包含排序动画的列表
        """
        if not data:
            logger.warning("没有提供数据，返回空动画列表")
            return []

        # 确定适合的数据类型和显示风格
        is_numeric = all(isinstance(item, (int, float)) for item in data)
        display_style = "bar" if is_numeric else "block"

        # 创建数组元素对象
        array_element = ArrayElement(
            data.copy(),  # 使用数据的副本，避免修改原始数据
            {
                "type": "numeric" if is_numeric else "mixed",
                "style": display_style,
                "layout": layout,
                "indexed": True,
                "operations": ["sort"],
                "highlight_changes": True,
            },
        )

        # 创建自定义的排序动画类来捕获可视化过程
        class SortAnimationCapture(Scene):
            def __init__(self, array_element, algorithm, **kwargs):
                super().__init__(**kwargs)
                self.array_element = array_element
                self.algorithm = algorithm
                self.captured_animations = []

            def play(self, *args, **kwargs):
                # 捕获动画而不是播放它们
                for anim in args:
                    self.captured_animations.append(anim)

            def construct(self):
                # 运行可视化排序过程
                self.array_element.visualize_sort(self, algorithm=self.algorithm)

            def get_animations(self):
                return self.captured_animations

        # 创建并运行排序动画捕获场景
        sort_scene = SortAnimationCapture(array_element, algorithm)
        sort_scene.construct()
        animations = sort_scene.get_animations()

        # 创建排序描述
        algorithm_names = {"bubble": "冒泡排序", "quick": "快速排序", "selection": "选择排序", "insertion": "插入排序"}

        algorithm_name = algorithm_names.get(algorithm, algorithm)
        sort_title = TextElement(f"{algorithm_name} 演示", {"role": "heading", "size": "large", "color": "yellow"})
        title_obj = sort_title.create_manim_object()

        # 应用布局区域并缩放
        target_layout = LAYOUTS.get(layout_area, LAYOUTS["full_screen"])

        # 使用Group而不是VGroup，因为动画对象可能不是VMobject类型
        title_group = Group(title_obj)
        sort_animation_group = Group()

        # 收集排序动画的对象
        for anim in animations:
            if hasattr(anim, "mobject"):
                sort_animation_group.add(anim.mobject)

        # 缩放标题以适应布局
        if len(sort_animation_group) > 0:
            # 根据布局区域缩放动画对象
            if hasattr(sort_animation_group, "get_width") and hasattr(sort_animation_group, "get_height"):
                # 只有在sort_animation_group具有尺寸方法时才进行缩放
                self.scale_to_fit_layout(sort_animation_group, target_layout, shrink_ratio=0.8)

            # 将标题放在排序动画上方
            if hasattr(sort_animation_group, "get_top"):
                title_obj.next_to(sort_animation_group, UP, buff=0.5)
        else:
            # 如果没有排序动画对象，只缩放标题
            self.scale_to_fit_layout(title_group, target_layout, shrink_ratio=0.8)

        # 创建标题动画
        title_animation = sort_title.create_animation(title_obj)

        # 封装所有动画对象
        anim_obj = []
        anim_obj.append({"obj_type": "SortTitle", "obj": title_animation})

        # 添加数组动画
        for i, anim in enumerate(animations):
            anim_obj.append({"obj_type": f"SortStep_{i}", "obj": anim})

        return anim_obj

    def render_code_snippet(
        self,
        code: str,
        language: str = "python",
        highlight_lines: list = [],
        title: str = None,
        layout_area="full_screen",
    ) -> list:
        """生成代码片段的动画效果。

        创建一个带有语法高亮和可选行高亮的代码片段。
        代码会以打字机效果呈现，突出显示代码的结构和关键部分。

        Args:
            code: 代码内容字符串
            language: 编程语言，默认为"python"
            highlight_lines: 要高亮显示的行号列表
            title: 代码块的标题，通常是文件名
            layout_area: 布局区域名称，决定代码块在屏幕中的位置和大小，默认为"full_screen"

        Returns:
            包含代码片段动画的列表
        """
        if not code.strip():
            logger.warning("没有提供代码内容，返回空动画列表")
            return []

        # 创建代码元素对象，直接利用CodeElement的智能默认值功能
        code_element = CodeElement(
            code,
            {
                "language": language,
                "line_numbers": True,
                "highlight_lines": highlight_lines,
                "title": title,
                "theme": "dark",
            },
        )

        # 创建Manim对象
        code_obj = code_element.create_manim_object()

        # 应用布局区域
        target_layout = LAYOUTS.get(layout_area, LAYOUTS["full_screen"])

        # 确保代码对象不会超出布局区域 - 先进行缩放
        # 创建一个临时组以便于处理代码对象
        code_group = Group(code_obj)

        # 检查对象是否具有所需的方法
        if hasattr(code_obj, "get_width") and hasattr(code_obj, "get_height"):
            # 先进行初始缩放，使其适合布局区域
            self.scale_to_fit_layout(code_group, target_layout, shrink_ratio=0.9)

            # 检查代码是否仍超出布局区域宽度 - 需要更激进的缩放或分行
            if code_obj.width > target_layout.width * 0.85:
                # 先尝试进一步缩小
                scale_factor = target_layout.width * 0.85 / code_obj.width
                code_obj.scale(scale_factor)

                # 如果缩放后高度超出布局区域，再次进行缩放以适应高度
                if code_obj.height > target_layout.height * 0.85:
                    height_scale = target_layout.height * 0.85 / code_obj.height
                    code_obj.scale(height_scale)

        # 将代码对象移动到目标布局区域的中心位置
        if hasattr(target_layout, "get_center") and hasattr(code_obj, "move_to"):
            code_obj.move_to(target_layout.get_center())

        # 创建动画 - 使用CodeElement自带的动画创建功能
        code_animation = code_element.create_animation(code_obj)

        # 组合动画
        anim_obj = []
        anim_obj.append({"obj_type": "CodeSnippet", "obj": code_animation})

        return anim_obj

    def render_text_element(
        self, content: str, role: str = "body", position: str = None, layout_area="full_screen", **kwargs
    ) -> list:
        """生成具有不同角色和展示效果的文本元素。

        创建一个基于角色的文本元素，每种角色都有预设的默认样式和动画效果。
        可以呈现不同类型的文本，如标题、正文、注释等，并智能应用合适的效果。

        Args:
            content: 文本内容
            role: 文本角色 - "title"(标题), "heading"(小标题), "subheading"(子标题),
                 "body"(正文), "caption"(说明文字), "quote"(引用), "note"(注释)
            position: 位置指示 - "top"(顶部), "center"(中央), "bottom"(底部),
                     "left"(左侧), "right"(右侧), 也可以组合使用如"top_left"
            layout_area: 布局区域名称，决定文本在屏幕中的位置和大小，默认为"full_screen"
            **kwargs: 可选参数，用于覆盖默认设置：
                      style: 文本样式 - "normal", "bold", "italic", "highlight" 等
                      size: 文本大小 - "small", "medium", "large", "xlarge"
                      align: 对齐方式 - "left", "center", "right", "justify"
                      color: 文本颜色
                      animation_type: 动画类型 - "typewriter", "fade", "slide" 等

        Returns:
            包含文本元素动画的列表
        """
        if not content.strip():
            logger.warning("没有提供文本内容，返回空动画列表")
            return []

        # 创建属性字典，以role为基础
        properties = {"role": role}

        # 从kwargs获取可选属性
        # 只使用有效的属性键
        valid_props = ["style", "size", "align", "color"]
        for key in valid_props:
            if key in kwargs:
                properties[key] = kwargs[key]

        # 获取动画类型或使用基于role的默认动画
        animation_type = kwargs.get("animation_type")

        # 创建文本元素，利用TextElement的智能默认值功能
        text_element = TextElement(content, properties, {"type": animation_type} if animation_type else None)

        # 创建Manim对象
        text_obj = text_element.create_manim_object()

        # 应用布局区域
        target_layout = LAYOUTS.get(layout_area, LAYOUTS["full_screen"])

        # 处理文本过长的情况 - 使用Group而不是VGroup
        text_group = Group(text_obj)

        # 检查和处理文本是否超出布局区域
        if hasattr(text_obj, "width") and hasattr(target_layout, "width"):
            # 如果文本宽度超过布局区域宽度的85%，需要进行自动换行或缩放
            if text_obj.width > target_layout.width * 0.85:
                # 优先尝试自动换行处理
                if isinstance(text_obj, Text) and len(content) > 20:
                    # 根据布局区域宽度和文本宽度比例计算合适的每行字符数
                    layout_width_ratio = target_layout.width * 0.85 / text_obj.width
                    chars_per_line = max(10, int(len(content) * layout_width_ratio))

                    # 根据字符数分行
                    lines = []
                    for i in range(0, len(content), chars_per_line):
                        # 确保不会在单词中间断开（对于非中文文本）
                        end_idx = min(i + chars_per_line, len(content))
                        if (
                            end_idx < len(content)
                            and not content[end_idx].isspace()
                            and not any(c in ",.;:!?)]}" for c in content[end_idx])
                        ):
                            # 向后寻找空格或标点符号作为换行点
                            for j in range(end_idx, max(i, end_idx - 10), -1):
                                if content[j].isspace() or any(c in ",.;:!?)]}" for c in content[j]):
                                    end_idx = j + 1
                                    break

                        lines.append(content[i:end_idx])

                    # 创建新的换行文本
                    new_content = "\n".join(lines)

                    # 重新创建文本元素
                    properties["align"] = "center"
                    text_element = TextElement(
                        new_content, properties, {"type": animation_type} if animation_type else None
                    )
                    text_obj = text_element.create_manim_object()

                # 如果文本仍超出布局区域，应用缩放
                text_group = Group(text_obj)
                if hasattr(text_group, "get_width") and hasattr(text_group, "get_height"):
                    self.scale_to_fit_layout(text_group, target_layout, shrink_ratio=0.85)

        # 根据布局区域设置文本位置
        if hasattr(target_layout, "get_center") and hasattr(text_obj, "move_to"):
            # 基于布局区域设置位置
            if position:
                if position == "center" and hasattr(target_layout, "get_center"):
                    text_obj.move_to(target_layout.get_center())
                elif position == "top" and hasattr(target_layout, "get_top"):
                    text_obj.move_to(target_layout.get_top() + DOWN * 0.5)
                elif position == "bottom" and hasattr(target_layout, "get_bottom"):
                    text_obj.move_to(target_layout.get_bottom() + UP * 0.5)
                elif position == "left" and hasattr(target_layout, "get_left"):
                    text_obj.move_to(target_layout.get_left() + RIGHT * 0.5)
                elif position == "right" and hasattr(target_layout, "get_right"):
                    text_obj.move_to(target_layout.get_right() + LEFT * 0.5)
                elif position == "top_left" and hasattr(target_layout, "get_corner"):
                    text_obj.move_to(target_layout.get_corner(UL) + DOWN * 0.5 + RIGHT * 0.5)
                elif position == "top_right" and hasattr(target_layout, "get_corner"):
                    text_obj.move_to(target_layout.get_corner(UR) + DOWN * 0.5 + LEFT * 0.5)
                elif position == "bottom_left" and hasattr(target_layout, "get_corner"):
                    text_obj.move_to(target_layout.get_corner(DL) + UP * 0.5 + RIGHT * 0.5)
                elif position == "bottom_right" and hasattr(target_layout, "get_corner"):
                    text_obj.move_to(target_layout.get_corner(DR) + UP * 0.5 + LEFT * 0.5)
                else:
                    # 默认回退到居中
                    text_obj.move_to(target_layout.get_center())
            else:
                # 默认居中
                text_obj.move_to(target_layout.get_center())

        # 创建动画，使用TextElement自带的动画创建功能
        text_animation = text_element.create_animation(text_obj)

        # 组合动画
        anim_obj = []
        anim_obj.append({"obj_type": "TextElement", "obj": text_animation})

        return anim_obj

    def _create_text_objects(self, keywords: list[str]) -> list[Text]:
        """为关键词列表创建文本对象。

        Args:
            keywords: 关键词列表

        Returns:
            文本对象列表
        """
        return [Text(keyword, font=DEFAULT_FONT, color=YELLOW, weight=BOLD, font_size=28) for keyword in keywords]
        # ).set_z_index(2)

    def _create_vertical_layout(
        self,
        text_objects: list[Text],
        background_animations: list[Animation],
        text_animations: list[Animation],
    ) -> None:
        """创建垂直布局的关键词列表和相应的动画。

        将文本对象垂直排列，并为每个文本创建背景矩形。
        同时创建背景和文本的动画效果。

        Args:
            text_objects: 文本对象列表
            background_animations: 用于存储背景动画的列表（将被修改）
            text_animations: 用于存储文本动画的列表（将被修改）
        """
        previous_text = None

        for i, text_obj in enumerate(text_objects):
            # 创建背景矩形
            background = self._create_background_for_text(text_obj)

            # 设置文本位置和动画
            if i == 0:
                # 第一个文本放在中心上方
                text_animations.append(text_obj.animate.move_to(ORIGIN).shift(UP * 1.5))
                text_obj.move_to(ORIGIN).shift(UP * 1.5)  # 垂直排列
            else:
                # 后续文本在前一个文本下方
                text_animations.append(text_obj.animate.next_to(previous_text, DOWN, buff=0.35))
                text_obj.next_to(previous_text, DOWN, buff=0.35)

            # 设置背景动画
            background_animations.append(background.animate.move_to(text_obj.get_center()))
            background.move_to(text_obj.get_center())

            # 更新前一个文本对象，用于下一次迭代
            previous_text = text_obj

    def _create_background_for_text(self, text_obj: Text) -> Rectangle:
        """为文本对象创建背景矩形。

        Args:
            text_obj: 文本对象

        Returns:
            设置了z_index的背景矩形
        """
        return Rectangle(
            height=text_obj.height + 0.15,  # 矩形高度
            width=text_obj.width + 0.15,  # 矩形宽度
            fill_color=BLACK,  # 背景色
            fill_opacity=0.5,  # 透明度
            stroke_width=0,  # 去掉边框
        )
        # ).set_z_index(1)

    def scale_to_fit_layout(self, group, layout, shrink_ratio=0.8, enlarge=True):
        # 确保对象具有所需方法
        if not (
            hasattr(group, "get_width")
            and hasattr(group, "get_height")
            and hasattr(layout, "get_width")
            and hasattr(layout, "get_height")
        ):
            # 如果缺少需要的方法，记录警告并返回
            logger.warning("无法缩放对象，缺少所需的get_width或get_height方法")
            return

        # 太大了，缩小
        try:
            x_scale = min(layout.get_width() / group.get_width() * shrink_ratio, 1.0) if group.get_width() > 0 else 1.0
            y_scale = (
                min(layout.get_height() / group.get_height() * shrink_ratio, 1.0) if group.get_height() > 0 else 1.0
            )
            scale_factor = min(x_scale, y_scale)

            # 只有当对象支持缩放且需要缩小时才应用缩放
            if scale_factor < 1.0 and hasattr(group, "scale"):
                group.scale(scale_factor)
        except Exception as e:
            logger.warning(f"缩放过程中出错: {str(e)}")

    def media_display(
        self,
        anim_object: list,
        image_path: str,
        i_type: int,
        pos: float = 1.0,
        layout=LAYOUTS["full_screen"],
        side="left",
    ) -> VMobject:
        """显示第一个媒体文件，作为主要内容。

        Args:
            image_path: 媒体文件路径
            i_type: 媒体类型 (1=图片, 2=视频, 3=GIF)
            pos: 垂直位置因子
            layout: 布局对象

        Returns:
            创建的媒体对象
        """
        image = None
        # inout_factor = INOUT_FACTOR

        if i_type == 1:  # 图片
            image = ImageMobject(image_path)
            # 缩放以适应布局
            if layout:
                self.scale_to_fit_layout(image, layout)

            global_height = config.frame_height * 0.45

            animation_sequence = Succession(
                Transform(image, image.copy().move_to(ORIGIN).scale_to_fit_width(config.frame_width * 0.9), run_time=1),
                # ApplyMethod(
                #    image.scale,
                #    inout_factor,
                #    run_time=1  # 可以根据需要调整时间
                # ),
                Transform(
                    image,
                    image.copy()
                    .scale(global_height / image.height)
                    .shift(RIGHT * image.width / 2 * (global_height / image.height - 1)),
                    run_time=2,
                ),
                # image.animate(run_time=3).shift(LEFT * (scale_factor - 1) * image_width),
                ApplyMethod(image.shift, LEFT * (global_height / image.height - 1) * image.width, run_time=5),
                Transform(
                    image,
                    image.copy().scale_to_fit_width(config.frame_width * 0.9).move_to(ORIGIN).shift(2 * UP * pos),
                    run_time=2,
                ),
            )
            obj_dict = {"obj_type": "Image", "obj": animation_sequence}
        elif i_type == 2:  # 视频
            obj_dict = self.get_video_animate(image_path)

        anim_object.append(obj_dict)

        return image

    def get_media_type(self, image_path: str) -> int:
        """确定媒体文件的类型。

        Args:
            image_path: 媒体文件路径

        Returns:
            媒体类型代码:
            0 - 未知类型
            1 - 图片 (png, jpg, jpeg, bmp, webp)
            2 - 视频 (mp3, mp4, mov)
        """
        if not image_path:
            return 0

        image_path = image_path.lower()  # 确保大小写不敏感

        if image_path.endswith((".png", ".jpg", ".jpeg", ".bmp", ".webp")):
            return 1
        elif image_path.endswith((".mp3", ".mp4", ".mov")):
            return 2

        return 0

    def get_media_resources(self, images: list[str]) -> tuple[list[int], list[str]]:
        """获取有效的媒体资源列表。

        Args:
            images: 媒体文件路径列表

        Returns:
            媒体类型列表和有效路径列表的元组
        """
        image_types, image_paths = [], []

        for image in images:
            image_type = self.get_media_type(image)
            if image_type == 0:
                continue

            if self.check_image_exists(image):
                image_paths.append(image)
                image_types.append(image_type)

        return image_types, image_paths

    def render_media_display(self, images: list[str]) -> None:
        """显示一组媒体文件。

        Args:
            images: 媒体文件路径列表
        """
        # 获取有效的媒体资源
        image_types, image_paths = self.get_media_resources(images)
        anim_object = []
        if len(image_paths) == 0:
            logger.warning("没有找到有效的媒体文件，无法显示")
            return anim_object

        # 显示每个媒体文件
        image_cnt = 0
        for itype in image_types:
            if itype == 1:
                image_cnt += 1
        start_image_cnt = 0
        for idx, image_path in enumerate(image_paths):
            position = (image_cnt - 1) / 2 - start_image_cnt
            if image_types[idx] == 1:
                start_image_cnt += 1

            self.media_display(anim_object, image_path=image_path, i_type=image_types[idx], pos=position)
            # 每个媒体文件之间添加等待时间
        return anim_object

    def check_image_exists(self, image_path: str) -> bool:
        """检查指定的图片路径是否存在。

        参数:
            image_path: 图片的完整路径或相对路径

        返回:
            如果图片存在返回True，否则返回False
        """
        if not image_path or len(image_path.strip()) == 0:
            logger.warning("提供的图片路径为空")
            return False

        # 规范化路径
        normalized_path = os.path.normpath(image_path)

        # 检查文件是否存在
        exists = os.path.exists(normalized_path)
        if not exists:
            logger.warning(f"媒体文件路径不存在: {normalized_path}")
            return False
        else:
            logger.info(f"媒体文件验证成功: {normalized_path}")
            return True

    def pic_scale_from_side_horizontal(self, anim_object, image, side="left", center=ORIGIN, run_time=1):
        image_width = image.width
        image_height = image.height
        global_height = config.frame_height
        scale_factor = global_height / image_height
        dis_obj = None
        if side == "left":
            self.play(image.animate.scale(scale_factor).shift(RIGHT * image_width / 2 * (scale_factor - 1)), run_time=1)
            # anim_object.append(image.animate.scale(scale_factor).shift(RIGHT * image_width / 2 * (scale_factor - 1)))
            # self.play(anim_object[-1])
            dis_obj = image.animate.shift(LEFT * (scale_factor - 1) * image_width)
        elif side == "right":
            self.play(image.animate.scale(scale_factor).shift(LEFT * image_width / 2 * (scale_factor - 1)), run_time=1)
            # anim_object.append(image.animate.scale(scale_factor).shift(LEFT * image_width / 2 * (scale_factor - 1)))
            # self.play(anim_object[-1])
            dis_obj = image.animate.shift(RIGHT * (scale_factor - 1) * image_width)
        return 1.0 / scale_factor, dis_obj

    def pic_scale_from_side_vertical(self, anim_object, image, side="top", center=ORIGIN, run_time=1):
        image_width = image.width
        image_height = image.height
        global_width = config.frame_width
        dis_obj = None
        scale_factor = global_width / image_width
        if side == "top":
            self.play(image.animate.scale(scale_factor).shift(DOWN * image_height / 2 * (scale_factor - 1)), run_time=1)
            # anim_object.append(image.animate.scale(scale_factor).shift(DOWN * image_height / 2 * (scale_factor - 1)))
            # self.play(anim_object[-1])
            dis_obj = image.animate.shift(UP * (scale_factor - 1) * image_height)
            self.play(image.animate.shift(UP * (scale_factor - 1) * image_height), run_time=run_time)
        elif side == "bottom":
            self.play(image.animate.scale(scale_factor).shift(UP * image_height / 2 * (scale_factor - 1)), run_time=1)
            # anim_object.append(image.animate.scale(scale_factor).shift(UP * image_height / 2 * (scale_factor - 1)))
            # self.play(anim_object[-1])
            dis_obj = image.animate.shift(DOWN * (scale_factor - 1) * image_height)
            self.play(image.animate.shift(DOWN * (scale_factor - 1) * image_height), run_time=run_time)
        return 1.0 / scale_factor, dis_obj

    def get_gif_animate(self, file_name, wait_time=0.07, repetitions=2, modify=lambda img: img):
        for i in range(0, repetitions):
            cap = cv2.VideoCapture(file_name)
            flag = True

            while flag:
                flag, frame = cap.read()
                if flag:
                    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    frame_img = ImageMobject(frame)
                    modify(frame_img)
                    self.add(frame_img)
                    self.wait(0.07)
                    self.remove(frame_img)
            cap.release()

    def get_video_animate(self, video_path):
        image_video = VideoMobject(filename=video_path, speed=1.0, loop=True)
        if image_video.height > config.frame_height * 0.45:
            image_video.scale_to_fit_height(config.frame_height * 0.40)
        else:
            image_video.scale_to_fit_width(config.frame_width * 0.87)
        return {"obj_type": "Video", "obj": image_video}

    def scene_clear_display(self, pre_mobjects, wait_time=1.0):
        clear_vec = [v for v in self.mobjects if v not in pre_mobjects]
        self.wait(wait_time)
        if len(clear_vec) > 0:
            # self.wait(max(duration - end_time + begin_time, wait_time))
            image_mobs = [v for v in self.mobjects if v not in pre_mobjects and isinstance(v, ImageMobject)]
            other_mobs = [v for v in self.mobjects if v not in pre_mobjects and v not in image_mobs]
            self.play(*[mob.animate.scale(INOUT_FACTOR) for mob in image_mobs], *[FadeOut(mob) for mob in other_mobs])

    def display_slogo(self, cover_question, step=1):
        title_content = wrap_text_fill_lines_smart_v2(cover_question, width=30)
        topic_title = Text(
            title_content,
            font_size=24,
            font="Microsoft YaHei",
            weight=BOLD,
            color=WHITE,
            line_spacing=1.0,
        )
        topic_title.move_to(ORIGIN).shift(4.5 * UP + 0.5 * RIGHT)
        self.title = topic_title
        # Create an orange background box
        background_box = Rectangle(
            width=16,
            height=topic_title.height + 0.8,
            fill_color=GRAY,
            fill_opacity=0.5,
            stroke_width=0,
        )
        # Position the box behind the text
        background_box.move_to(topic_title)
        background_box.z_index = 0
        topic_title.z_index = 1

        # Group text and background together
        topic = VGroup(background_box, topic_title)

        slogon = (
            Text("AI费曼", color=WHITE, font_size=20, font=DEFAULT_FONT, line_spacing=1.0)
            .to_edge(LEFT)
            .shift(4.5 * UP + 0.55 * RIGHT)
        )
        image1 = ImageMobject("cover/logo.jpg").scale(0.1).next_to(slogon, LEFT, buff=0.01)
        slogon.z_index = 1
        image1.z_index = 1
        logo_group = Group(slogon, image1)
        slogon1 = Text(
            "让知识易懂",
            font_size=16,
            font=DEFAULT_FONT,
            color=YELLOW,
            weight=BOLD,
            line_spacing=1.0,
        ).next_to(logo_group, DOWN, buff=0.03)
        slogon1.z_index = 1

        background_box = Rectangle(
            width=16,  # Specify width
            height=16,  # Specify height
            fill_color=BLACK,
            fill_opacity=0.5,
            stroke_width=0,
        ).move_to(ORIGIN)
        background_box.z_index = 0
        if step == 0:
            self.play(FadeIn(logo_group), FadeIn(slogon1), FadeIn(topic), FadeIn(background_box))
        else:
            self.add(background_box, logo_group, slogon1, topic)
