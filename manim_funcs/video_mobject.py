from dataclasses import dataclass

import cv2
from manim import *
from PIL import Image


@dataclass
class VideoStatus:
    time: float = 0
    videoObject: cv2.VideoCapture = None

    def __deepcopy__(self, memo):
        return self


class VideoMobject(ImageMobject):
    """
    Following a discussion on Discord about animated GIF images.
    Modified for videos

    Parameters
    ----------
    filename
        the filename of the video file

    imageops
        (optional) possibility to include a PIL.ImageOps operation, e.g.
        PIL.ImageOps.mirror

    speed
        (optional) speed-up/slow-down the playback

    loop
        (optional) replay the video from the start in an endless loop

    https://discord.com/channels/581738731934056449/1126245755607339250/1126245755607339250
    2023-07-06 <PERSON><PERSON> & Abulafia
    2024-03-09 <PERSON><PERSON>
    """

    def __init__(self, scale=0, filename=None, imageops=None, speed=1.0, loop=False, **kwargs):
        self.filename = filename
        self.imageops = imageops
        self.speed = speed
        self.loop = loop
        self._id = id(self)
        self.status = VideoStatus()
        self.status.videoObject = cv2.VideoCapture(filename)

        self.status.videoObject.set(cv2.CAP_PROP_POS_FRAMES, 1)
        ret, frame = self.status.videoObject.read()
        if ret:
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            if scale == 1:
                h, w = frame.shape[:2]
                # Calculate aspect ratio scaling factors
                width_ratio = 640 / float(w)
                height_ratio = 480 / float(h)
                # Use the smaller ratio to maintain aspect ratio
                scale_ratio = min(width_ratio, height_ratio)
                # Calculate new dimensions
                new_w = int(w * scale_ratio)
                new_h = int(h * scale_ratio)
                frame = cv2.resize(frame, (new_w, new_h))
            img = Image.fromarray(frame)

            if imageops is not None:
                img = imageops(img)
        else:
            img = Image.fromarray(np.uint8([[63, 0, 0, 0], [0, 127, 0, 0], [0, 0, 191, 0], [0, 0, 0, 255]]))
        super().__init__(img, **kwargs)
        if ret:
            self.add_updater(self.videoUpdater)

    def videoUpdater(self, mobj, dt):
        if dt == 0:
            return
        status = self.status
        status.time += 1000 * dt * mobj.speed
        self.status.videoObject.set(cv2.CAP_PROP_POS_MSEC, status.time)
        ret, frame = self.status.videoObject.read()
        if (ret is False) and self.loop:
            status.time = 0
            self.status.videoObject.set(cv2.CAP_PROP_POS_MSEC, status.time)
            ret, frame = self.status.videoObject.read()
        if ret:
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)  # needed here?
            img = Image.fromarray(frame)

            if mobj.imageops is not None:
                img = mobj.imageops(img)
            mobj.pixel_array = change_to_rgba_array(np.asarray(img), mobj.pixel_array_dtype)
