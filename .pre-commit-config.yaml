repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
    -   id: check-merge-conflict
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
    -   id: check-yaml
    -   id: debug-statements
    -   id: requirements-txt-fixer
-   repo: https://github.com/asottile/pyupgrade
    rev: v3.19.1
    hooks:
    -   id: pyupgrade
        args: [--py39-plus]
-   repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.2.1
    hooks:
    -   id: ruff
        args:
        - --fix
    -   id: ruff-format
