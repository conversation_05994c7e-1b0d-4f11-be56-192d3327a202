"""
重构后的分镜处理主控制器
"""
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Optional

from loguru import logger

from feynman_workflow.core.performance_monitor import performance_monitor, timing_decorator

from .config import StoryboardConfig
from .managers import PathManager
from .models import EntryResult, ProcessingStage, ProcessResult, TransitionResult
from .processors import (
    DSLGenerator,
    ManimCodeGenerator,
    StoryboardLoader,
    SubtitleProcessor,
    TransitionGenerator,
    VideoComposer,
    VideoRenderer,
)


class RetryableProcessor:
    """可重试的处理器基类"""

    def __init__(self, max_retries: int = 2):
        self.max_retries = max_retries

    def process_with_retry(self, process_func, error_context_func=None, *args, **kwargs):
        """
        带重试机制的处理方法

        Args:
            process_func: 处理函数
            error_context_func: 错误上下文提取函数
            *args, **kwargs: 传递给处理函数的参数
        """
        last_error = None
        error_context = None

        for attempt in range(self.max_retries + 1):
            try:
                # 将attempt传递给处理函数
                return process_func(*args, attempt=attempt + 1, error_context=error_context, **kwargs)
            except Exception as e:
                last_error = e
                if error_context_func:
                    error_context = error_context_func(e)

                if attempt < self.max_retries:
                    logger.warning(f"处理失败，重试 {attempt + 1}/{self.max_retries}: {e}")
                    continue
                else:
                    raise last_error


class StoryboardProcessor(RetryableProcessor):
    """重构后的分镜处理器 - 集成性能监控和模块化处理"""

    def __init__(self, config: StoryboardConfig):
        super().__init__(config.max_retries)

        # 验证配置
        config.validate()
        self.config = config

        # 初始化路径管理器
        self.path_manager = PathManager(config)

        # 初始化各个处理器
        self.loader = StoryboardLoader(config)
        self.dsl_generator = DSLGenerator(config, self.path_manager)
        self.code_generator = ManimCodeGenerator(config, self.path_manager)
        self.renderer = VideoRenderer(config, self.path_manager)
        self.subtitle_processor = SubtitleProcessor(config, self.path_manager)
        self.transition_generator = TransitionGenerator(config, self.path_manager)
        self.video_composer = VideoComposer(config, self.path_manager)

        # 启动性能监控
        performance_monitor.start_workflow()

        logger.info(f"分镜处理器初始化完成 - Max Workers: {config.max_workers}, Quality: {config.quality}")
        logger.info(f"处理阶段: {', '.join(config.stages)}")

    @timing_decorator("完整处理流程")
    def process_all(self, start_idx: int = 0, end_idx: Optional[int] = None) -> ProcessResult:
        """
        处理所有分镜条目

        Args:
            start_idx: 起始索引
            end_idx: 结束索引

        Returns:
            处理结果
        """
        start_time = time.time()

        # 加载分镜数据
        storyboard = self.loader.load_storyboard()
        validated_storyboard = self.loader.validate_storyboard_entries(storyboard)

        if not validated_storyboard:
            logger.error("没有有效的分镜条目")
            return ProcessResult(total_entries=0)

        # 应用索引限制
        if end_idx is None:
            end_idx = len(validated_storyboard)

        start_idx = max(0, start_idx)
        end_idx = min(end_idx, len(validated_storyboard))

        if start_idx >= end_idx:
            logger.error(f"无效的索引范围: [{start_idx}, {end_idx})")
            return ProcessResult(total_entries=0)

        # 过滤分镜条目
        entries_to_process = [(idx, validated_storyboard[idx]) for idx in range(start_idx, end_idx)]

        logger.info(f"开始处理分镜条目 [{start_idx}, {end_idx}), 共 {len(entries_to_process)} 个条目")

        # 创建处理结果
        result = ProcessResult(total_entries=len(entries_to_process))

        # 并行处理分镜条目
        entry_results = self._process_entries_parallel(entries_to_process)

        # 分类处理结果
        for entry_result in entry_results:
            if entry_result.success:
                result.add_successful_entry(entry_result)
            else:
                result.add_failed_entry(entry_result)

        # 处理转场视频（如果需要）
        if ProcessingStage.CONCAT in [ProcessingStage(s) for s in self.config.stages]:
            transition_results = self._handle_transitions(result.successful_entries, start_idx)
            for tr in transition_results:
                result.add_transition_result(tr)

            # 视频拼接
            final_video = self._handle_concatenation(result)
            result.final_video_path = final_video

        # 记录处理时间和完成的阶段
        result.processing_time = time.time() - start_time
        result.stages_completed = [ProcessingStage(s) for s in self.config.stages]

        # 输出处理摘要
        self._log_processing_summary(result)

        return result

    @timing_decorator("并行处理分镜条目")
    def _process_entries_parallel(self, entries_to_process: list[tuple[int, dict]]) -> list[EntryResult]:
        """
        并行处理分镜条目

        Args:
            entries_to_process: 要处理的条目列表 [(idx, entry), ...]

        Returns:
            处理结果列表
        """
        # 过滤出非concat阶段
        entry_stages = [s for s in self.config.stages if s != "concat"]

        results = []

        with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
            # 提交所有处理任务
            future_to_idx = {
                executor.submit(self._process_single_entry_with_retry, idx, entry, entry_stages): idx
                for idx, entry in entries_to_process
            }

            # 收集结果
            for future in as_completed(future_to_idx):
                idx = future_to_idx[future]
                try:
                    entry_result = future.result()
                    results.append(entry_result)

                    if entry_result.success:
                        logger.success(f"✅ Entry {idx + 1} 处理成功")
                    else:
                        logger.error(f"❌ Entry {idx + 1} 处理失败: {entry_result.error_message}")

                except Exception as e:
                    logger.error(f"❌ Entry {idx + 1} 任务异常: {e}")
                    results.append(EntryResult(seq_num=idx + 1, success=False, error_message=f"任务执行异常: {e}"))

        # 按序号排序结果
        results.sort(key=lambda x: x.seq_num)
        return results

    def _process_single_entry_with_retry(self, idx: int, entry: dict, stages: list[str]) -> EntryResult:
        """
        带重试机制的单个分镜条目处理

        Args:
            idx: 条目索引
            entry: 条目数据
            stages: 要执行的阶段列表

        Returns:
            处理结果
        """
        seq_num = idx + 1

        def process_func(attempt: int, error_context: str = None) -> EntryResult:
            return self._process_single_entry_stages(idx, entry, stages, attempt, error_context)

        def error_context_func(error: Exception) -> str:
            return f"Previous attempt failed: {str(error)}"

        try:
            return self.process_with_retry(process_func, error_context_func)
        except Exception as e:
            logger.error(f"Entry {seq_num} 在所有重试后仍然失败: {e}")
            return EntryResult(
                seq_num=seq_num,
                success=False,
                entry_data=entry,
                error_message=str(e),
                attempt_count=self.max_retries + 1,
            )

    @timing_decorator("处理单个分镜条目的各个阶段")
    def _process_single_entry_stages(
        self, idx: int, entry: dict, stages: list[str], attempt: int, error_context: Optional[str] = None
    ) -> EntryResult:
        """
        处理单个分镜条目的各个阶段

        Args:
            idx: 条目索引
            entry: 条目数据
            stages: 要执行的阶段列表
            attempt: 尝试次数
            error_context: 错误上下文

        Returns:
            处理结果
        """
        seq_num = idx + 1
        result = EntryResult(seq_num=seq_num, success=True, entry_data=entry, attempt_count=attempt)

        logger.info(f"[Entry {seq_num}, Attempt {attempt}] 开始处理阶段: {', '.join(stages)}")

        try:
            # 阶段1: DSL生成
            if "dsl" in stages:
                result.dsl_file = self.dsl_generator.generate_dsl(entry, seq_num, attempt, error_context)
                logger.debug(f"[Entry {seq_num}] DSL文件: {result.dsl_file}")

            # 如果后续阶段需要DSL文件但DSL阶段未执行，尝试查找现有文件
            if not result.dsl_file and any(s in stages for s in ["code", "render", "subtitles"]):
                dsl_path = self.path_manager.get_dsl_path(seq_num, attempt)
                if dsl_path.exists():
                    result.dsl_file = str(dsl_path.resolve())
                else:
                    raise FileNotFoundError(f"DSL文件不存在且未请求生成: {dsl_path}")

            # 提取场景信息
            if result.dsl_file:
                scene_info = self.dsl_generator.extract_scene_info(result.dsl_file, seq_num)
                result.scene_name = scene_info["scene_name"]

            # 阶段2: 代码生成
            if "code" in stages:
                result.py_file = self.code_generator.generate_code(result.dsl_file, seq_num, attempt)
                logger.debug(f"[Entry {seq_num}] Python文件: {result.py_file}")

            # 如果后续阶段需要Python文件但代码阶段未执行，尝试查找现有文件
            if not result.py_file and any(s in stages for s in ["render", "subtitles"]):
                py_path = self.path_manager.get_py_path(seq_num, attempt)
                if py_path.exists():
                    result.py_file = str(py_path.resolve())
                else:
                    raise FileNotFoundError(f"Python文件不存在且未请求生成: {py_path}")

            # 阶段3: 视频渲染
            if "render" in stages:
                result.rendered_video_file = self.renderer.render_video(
                    result.py_file, result.scene_name, seq_num, attempt
                )
                logger.debug(f"[Entry {seq_num}] 渲染视频: {result.rendered_video_file}")

            # 如果字幕阶段需要视频文件但渲染阶段未执行，尝试查找现有文件
            if not result.rendered_video_file and "subtitles" in stages:
                existing_video = self.renderer.find_existing_video(
                    seq_num, result.scene_name or f"Storyboard_{seq_num}", attempt
                )
                if existing_video:
                    result.rendered_video_file = existing_video
                else:
                    raise FileNotFoundError("渲染视频不存在且未请求渲染")

            # 阶段4: 字幕添加
            if "subtitles" in stages and result.rendered_video_file:
                result.subtitled_video_file = self.subtitle_processor.add_subtitles(
                    result.rendered_video_file, result.scene_name or f"Storyboard_{seq_num}", seq_num, attempt
                )
                logger.debug(f"[Entry {seq_num}] 字幕视频: {result.subtitled_video_file}")

            logger.success(f"[Entry {seq_num}, Attempt {attempt}] 所有阶段处理完成")
            return result

        except Exception as e:
            # 确定失败的阶段
            if "dsl" in stages and not result.dsl_file:
                result.failed_stage = ProcessingStage.DSL
            elif "code" in stages and not result.py_file:
                result.failed_stage = ProcessingStage.CODE
            elif "render" in stages and not result.rendered_video_file:
                result.failed_stage = ProcessingStage.RENDER
            elif "subtitles" in stages and not result.subtitled_video_file:
                result.failed_stage = ProcessingStage.SUBTITLES

            result.success = False
            result.error_message = str(e)

            logger.error(f"[Entry {seq_num}, Attempt {attempt}] 阶段处理失败: {e}")
            raise

    @timing_decorator("处理转场视频")
    def _handle_transitions(self, successful_entries: list[EntryResult], start_idx: int) -> list[TransitionResult]:
        """
        处理转场视频生成

        Args:
            successful_entries: 成功处理的条目列表
            start_idx: 起始索引

        Returns:
            转场处理结果列表
        """
        if not self.config.transition_enabled or len(successful_entries) < 2:
            return []

        # 创建场景对
        scene_pairs = self.transition_generator.create_scene_pairs_from_entries(successful_entries, start_idx)

        # 并行生成转场视频
        return self.transition_generator.generate_transitions_parallel(scene_pairs)

    @timing_decorator("Compose:Concatenate")
    def _handle_concatenation(self, result: ProcessResult) -> Optional[str]:
        """
        处理视频拼接

        Args:
            result: 处理结果

        Returns:
            最终视频路径
        """
        video_files = result.video_files_for_concat
        if not video_files:
            logger.warning("没有可用于拼接的视频文件")
            return None

        # 获取成功的转场视频
        transition_videos = result.successful_transition_videos

        # 创建包含转场的视频序列
        final_sequence = self.video_composer.create_video_sequence_with_transitions(video_files, transition_videos)

        # 拼接视频
        concatenated_video = self.video_composer.concatenate_videos(final_sequence)

        if not concatenated_video:
            return None

        # 添加背景音乐
        final_video = self.video_composer.add_background_music(concatenated_video)

        return final_video

    def _log_processing_summary(self, result: ProcessResult) -> None:
        """记录处理摘要"""
        summary = result.get_summary()

        logger.info("=" * 60)
        logger.info("📊 分镜处理完成摘要")
        logger.info("=" * 60)

        for key, value in summary.items():
            logger.info(f"{key}: {value}")

        logger.info("=" * 60)

        # 输出性能统计
        performance_monitor.log_summary()

    def cleanup(self) -> None:
        """清理临时文件和资源"""
        try:
            self.path_manager.cleanup_temp_files()
            logger.info("临时文件清理完成")
        except Exception as e:
            logger.warning(f"清理临时文件时出错: {e}")
