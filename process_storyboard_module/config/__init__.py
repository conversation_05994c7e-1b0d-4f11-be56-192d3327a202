"""
配置管理模块
"""
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Optional


@dataclass
class StoryboardConfig:
    """分镜处理配置类"""

    # 基础配置
    storyboard_file: str
    output_dir: str = "output/videos"
    dsl_schema_file: str = "docs/animation_functions.md"
    project_name: Optional[str] = None

    # 并发配置
    max_workers: int = 4

    # 渲染配置
    quality: str = "l"  # l, m, h, k

    # 处理阶段配置
    stages: list[str] = field(default_factory=lambda: ["dsl", "code", "render", "subtitles", "concat"])

    # 重试配置
    max_retries: int = 2

    # 转场配置
    transition_enabled: bool = False
    transition_run_time: float = 1.0
    transition_type: str = "wipe_left"
    state_dir: str = "output/scene_states"

    # UV配置
    use_uv: Optional[bool] = None  # None表示自动检测

    @classmethod
    def from_dict(cls, config_dict: dict[str, Any]) -> "StoryboardConfig":
        """从字典创建配置对象"""
        return cls(**config_dict)

    @classmethod
    def from_yaml_file(cls, yaml_path: str) -> "StoryboardConfig":
        """从YAML文件加载配置"""
        try:
            import yaml

            with open(yaml_path, encoding="utf-8") as f:
                config_data = yaml.safe_load(f)
            return cls.from_dict(config_data)
        except Exception as e:
            raise ValueError(f"无法加载配置文件 {yaml_path}: {e}")

    def validate(self) -> None:
        """验证配置的有效性"""
        if not Path(self.storyboard_file).exists():
            raise FileNotFoundError(f"分镜脚本文件不存在: {self.storyboard_file}")

        if not Path(self.dsl_schema_file).exists():
            raise FileNotFoundError(f"DSL模式文件不存在: {self.dsl_schema_file}")

        if self.quality not in ["l", "m", "h", "k"]:
            raise ValueError(f"无效的渲染质量: {self.quality}")

        if self.max_workers < 1:
            raise ValueError(f"max_workers必须大于0: {self.max_workers}")

        valid_stages = {"dsl", "code", "render", "subtitles", "concat"}
        invalid_stages = set(self.stages) - valid_stages
        if invalid_stages:
            raise ValueError(f"无效的处理阶段: {invalid_stages}")

    @property
    def quality_folder_mapping(self) -> dict[str, str]:
        """质量参数到文件夹名称的映射"""
        return {"l": "480p15", "m": "720p30", "h": "1080p60", "p": "1440p60", "k": "2160p60"}

    @property
    def expected_quality_folder(self) -> str:
        """获取期望的质量文件夹名称"""
        return self.quality_folder_mapping.get(self.quality, "480p15")
