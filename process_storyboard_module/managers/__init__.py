"""
路径管理器模块
"""
import shutil
from pathlib import Path
from typing import Optional

from ..config import StoryboardConfig


class PathManager:
    """统一管理所有文件路径的类"""

    def __init__(self, config: StoryboardConfig):
        self.config = config
        self.output_dir = Path(config.output_dir)
        self.project_root = Path(__file__).resolve().parent.parent.parent
        self.setup_directories()

    def setup_directories(self) -> None:
        """创建必要的目录结构"""
        directories = [
            self.output_dir,
            self.output_dir / "generated_manim_code",
            self.output_dir / "dsl_files",
            self.output_dir / "transitions",
            Path(self.config.state_dir),
            Path("logs"),
        ]

        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)

        # 清理状态目录
        if Path(self.config.state_dir).exists():
            shutil.rmtree(self.config.state_dir, ignore_errors=True)
            Path(self.config.state_dir).mkdir(parents=True, exist_ok=True)

    def get_dsl_path(self, seq_num: int, attempt: int = 1) -> Path:
        """获取DSL文件路径"""
        attempt_suffix = f"_attempt_{attempt}" if attempt > 1 else ""
        filename = f"storyboard_{seq_num}{attempt_suffix}_dsl.json"
        return self.output_dir / "dsl_files" / filename

    def get_py_path(self, seq_num: int, attempt: int = 1) -> Path:
        """获取Python代码文件路径"""
        attempt_suffix = f"_attempt_{attempt}" if attempt > 1 else ""
        filename = f"storyboard_{seq_num}{attempt_suffix}_dsl.py"
        return self.output_dir / "generated_manim_code" / filename

    def get_manim_video_path(self, py_file_path: Path, scene_name: str) -> Path:
        """获取Manim输出的视频文件路径"""
        py_filename_stem = py_file_path.stem
        expected_quality_folder = self.config.expected_quality_folder

        video_base_dir = self.project_root / "media" / "videos" / py_filename_stem
        video_dir = video_base_dir / expected_quality_folder
        return video_dir / f"{scene_name}.mp4"

    def get_srt_path(self, py_file_path: Path, scene_name: str) -> Path:
        """获取字幕文件路径"""
        py_filename_stem = py_file_path.stem
        expected_quality_folder = self.config.expected_quality_folder

        video_base_dir = self.project_root / "media" / "videos" / py_filename_stem
        video_dir = video_base_dir / expected_quality_folder
        return video_dir / f"{scene_name}.srt"

    def get_subtitled_video_path(self, original_video_path: Path) -> Path:
        """获取添加字幕后的视频文件路径"""
        return original_video_path.parent / f"{original_video_path.stem}_subtitle.mp4"

    def get_transition_video_path(self, from_scene: str, to_scene: str) -> Path:
        """获取转场视频文件路径"""
        filename = f"transition_{from_scene}_to_{to_scene}.mp4"
        return self.output_dir / "transitions" / filename

    def get_final_video_path(self) -> Path:
        """获取最终视频文件路径"""
        if self.config.project_name:
            filename = f"{self.config.project_name}.mp4"
        else:
            filename = "final_video.mp4"
        return self.output_dir / filename

    def get_video_with_music_path(self, original_video_path: Path) -> Path:
        """获取添加背景音乐后的视频文件路径"""
        return original_video_path.parent / f"{original_video_path.stem}_with_music.mp4"

    def get_fixed_sync_video_path(self, original_video_path: Path, index: int) -> Path:
        """获取音频同步修复后的视频文件路径"""
        filename = f"fixed_sync_{index}_{Path(original_video_path).name}"
        return self.output_dir / filename

    def get_temp_list_file_path(self) -> Path:
        """获取ffmpeg拼接用的临时列表文件路径"""
        return self.output_dir / "mylist.txt"

    def find_rendered_video(self, seq_num: int, scene_name: str, attempt: int = 1) -> Optional[Path]:
        """查找已渲染的视频文件"""
        py_file_path = self.get_py_path(seq_num, attempt)
        video_path = self.get_manim_video_path(py_file_path, scene_name)

        if video_path.exists():
            return video_path

        # 如果找不到，尝试在所有质量文件夹中搜索
        py_filename_stem = py_file_path.stem
        video_base_dir = self.project_root / "media" / "videos" / py_filename_stem

        if video_base_dir.exists():
            for quality_folder in self.config.quality_folder_mapping.values():
                video_path = video_base_dir / quality_folder / f"{scene_name}.mp4"
                if video_path.exists():
                    return video_path

        return None

    def cleanup_temp_files(self) -> None:
        """清理临时文件"""
        temp_files = [
            self.get_temp_list_file_path(),
        ]

        for temp_file in temp_files:
            if temp_file.exists():
                try:
                    temp_file.unlink()
                except OSError:
                    pass  # 忽略删除失败
