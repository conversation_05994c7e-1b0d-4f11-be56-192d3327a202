"""
处理结果数据模型
"""
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Optional


class ProcessingStage(Enum):
    """处理阶段枚举"""

    DSL = "dsl"
    CODE = "code"
    RENDER = "render"
    SUBTITLES = "subtitles"
    CONCAT = "concat"


@dataclass
class EntryResult:
    """单个分镜条目的处理结果"""

    seq_num: int
    success: bool
    entry_data: dict[str, Any] = field(default_factory=dict)

    # 文件路径
    dsl_file: Optional[str] = None
    py_file: Optional[str] = None
    scene_name: Optional[str] = None
    rendered_video_file: Optional[str] = None
    subtitled_video_file: Optional[str] = None

    # 错误信息
    error_message: Optional[str] = None
    failed_stage: Optional[ProcessingStage] = None
    attempt_count: int = 1

    @property
    def final_video_file(self) -> Optional[str]:
        """获取最终的视频文件路径"""
        return self.subtitled_video_file or self.rendered_video_file

    @property
    def has_video_output(self) -> bool:
        """是否有视频输出"""
        return self.final_video_file is not None


@dataclass
class TransitionResult:
    """转场视频处理结果"""

    from_scene: str
    to_scene: str
    success: bool
    video_file: Optional[str] = None
    error_message: Optional[str] = None


@dataclass
class ProcessResult:
    """整体处理结果"""

    total_entries: int
    successful_entries: list[EntryResult] = field(default_factory=list)
    failed_entries: list[EntryResult] = field(default_factory=list)

    # 转场结果
    transition_results: list[TransitionResult] = field(default_factory=list)

    # 最终输出
    final_video_path: Optional[str] = None

    # 统计信息
    processing_time: float = 0.0
    stages_completed: list[ProcessingStage] = field(default_factory=list)

    @property
    def success_count(self) -> int:
        """成功处理的条目数量"""
        return len(self.successful_entries)

    @property
    def failure_count(self) -> int:
        """失败的条目数量"""
        return len(self.failed_entries)

    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_entries == 0:
            return 0.0
        return self.success_count / self.total_entries

    @property
    def video_files_for_concat(self) -> list[str]:
        """用于拼接的视频文件列表"""
        videos = []
        for entry in self.successful_entries:
            if entry.final_video_file:
                videos.append(entry.final_video_file)
        return videos

    @property
    def successful_transition_videos(self) -> list[str]:
        """成功生成的转场视频列表"""
        videos = []
        for result in self.transition_results:
            if result.success and result.video_file:
                videos.append(result.video_file)
            else:
                videos.append("")  # placeholder
        return videos

    def add_successful_entry(self, entry: EntryResult) -> None:
        """添加成功的条目"""
        entry.success = True
        self.successful_entries.append(entry)

    def add_failed_entry(self, entry: EntryResult) -> None:
        """添加失败的条目"""
        entry.success = False
        self.failed_entries.append(entry)

    def add_transition_result(self, result: TransitionResult) -> None:
        """添加转场结果"""
        self.transition_results.append(result)

    def get_summary(self) -> dict[str, Any]:
        """获取处理结果摘要"""
        return {
            "总条目数": self.total_entries,
            "成功条目数": self.success_count,
            "失败条目数": self.failure_count,
            "成功率": f"{self.success_rate:.1%}",
            "处理时间": f"{self.processing_time:.2f}秒",
            "完成阶段": [stage.value for stage in self.stages_completed],
            "转场视频数": len(self.successful_transition_videos),
            "最终视频": self.final_video_path or "未生成",
        }
