"""
字幕处理器
"""
import subprocess
from pathlib import Path

from loguru import logger

from feynman_workflow.core.performance_monitor import timing_decorator

from ..config import StoryboardConfig
from ..managers import PathManager


class SubtitleProcessor:
    """字幕处理器"""

    def __init__(self, config: StoryboardConfig, path_manager: PathManager):
        self.config = config
        self.path_manager = path_manager

    @timing_decorator("添加字幕")
    def add_subtitles(self, video_file_path: str, scene_name: str, seq_num: int, attempt: int = 1) -> str:
        """
        为视频添加字幕

        Args:
            video_file_path: 输入视频文件路径
            scene_name: 场景名称
            seq_num: 序号
            attempt: 尝试次数

        Returns:
            添加字幕后的视频文件路径
        """
        input_video_path = Path(video_file_path)
        if not input_video_path.exists():
            raise FileNotFoundError(f"输入视频文件不存在: {video_file_path}")

        logger.info(f"[Entry {seq_num}, Attempt {attempt}] 添加字幕到视频: {scene_name}")

        # 查找对应的SRT字幕文件
        py_file_path = self.path_manager.get_py_path(seq_num, attempt)
        srt_path = self.path_manager.get_srt_path(py_file_path, scene_name)

        if not srt_path.exists():
            logger.warning(f"[Entry {seq_num}] SRT字幕文件不存在: {srt_path}")
            logger.warning("跳过字幕添加步骤，返回原视频文件")
            return str(input_video_path.resolve())

        # 生成输出文件路径
        output_video_path = self.path_manager.get_subtitled_video_path(input_video_path)

        try:
            # 构建ffmpeg命令
            srt_filter_path = str(srt_path.resolve()).replace("\\", "/")

            ffmpeg_cmd = [
                "ffmpeg",
                "-i",
                str(input_video_path.resolve()),
                "-lavfi",
                f"subtitles='{srt_filter_path}':force_style='Alignment=2,MarginV=6,fontcolor=white,Fontsize=16,fontweight=bold,FontName=微软雅黑'",
                "-y",  # 覆盖输出文件
                str(output_video_path.resolve()),
            ]

            logger.debug(f"运行ffmpeg字幕命令: {' '.join(ffmpeg_cmd)}")

            # 执行ffmpeg命令
            subprocess.run(ffmpeg_cmd, check=True, capture_output=True, text=True, encoding="utf-8")

            result_path = str(output_video_path.resolve())
            logger.success(f"[Entry {seq_num}] 字幕添加成功: {result_path}")

            return result_path

        except subprocess.CalledProcessError as e:
            logger.error(f"[Entry {seq_num}, Attempt {attempt}] ffmpeg字幕添加失败: {e.stderr}")
            raise
        except Exception as e:
            logger.error(f"[Entry {seq_num}, Attempt {attempt}] 字幕处理异常: {e}")
            raise

    def has_subtitle_file(self, seq_num: int, scene_name: str, attempt: int = 1) -> bool:
        """
        检查是否存在字幕文件

        Args:
            seq_num: 序号
            scene_name: 场景名称
            attempt: 尝试次数

        Returns:
            是否存在字幕文件
        """
        py_file_path = self.path_manager.get_py_path(seq_num, attempt)
        srt_path = self.path_manager.get_srt_path(py_file_path, scene_name)
        return srt_path.exists()
