"""
视频渲染器
"""
import shutil
import subprocess
from pathlib import Path
from typing import Optional

from loguru import logger

from feynman_workflow.core.performance_monitor import timing_decorator

from ..config import StoryboardConfig
from ..managers import PathManager


class VideoRenderer:
    """视频渲染处理器"""

    def __init__(self, config: StoryboardConfig, path_manager: PathManager):
        self.config = config
        self.path_manager = path_manager

        # 检查是否可用uv
        self.use_uv = config.use_uv
        if self.use_uv is None:
            self.use_uv = shutil.which("uv") is not None

    @timing_decorator("渲染视频")
    def render_video(self, py_file_path: str, scene_name: str, seq_num: int, attempt: int = 1) -> str:
        """
        使用Manim渲染视频

        Args:
            py_file_path: Python文件路径
            scene_name: 场景名称
            seq_num: 序号
            attempt: 尝试次数

        Returns:
            渲染的视频文件路径
        """
        py_path = Path(py_file_path)
        if not py_path.exists():
            raise FileNotFoundError(f"Python文件不存在: {py_file_path}")

        logger.info(f"[Entry {seq_num}, Attempt {attempt}] 渲染视频: {scene_name}")

        try:
            # 构建Manim渲染命令
            manim_cmd = []
            if self.use_uv:
                manim_cmd.extend(["uv", "run"])

            manim_cmd.extend(
                [
                    "manim",
                    "render",
                    str(py_path.resolve()),
                    scene_name,
                    f"-q{self.config.quality}",
                    "--progress_bar",
                    "none",  # 禁用进度条避免日志干扰
                ]
            )

            logger.debug(f"运行Manim渲染命令: {' '.join(manim_cmd)}")

            # 执行渲染
            _ = subprocess.run(
                manim_cmd,
                check=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                encoding="utf-8",
            )

            logger.info(f"Manim渲染完成 - Entry {seq_num}, Scene: {scene_name}")

            # 查找渲染输出的视频文件
            rendered_video_path = self.path_manager.find_rendered_video(seq_num, scene_name, attempt)

            if not rendered_video_path:
                logger.error("Manim执行成功但找不到渲染的视频文件")
                logger.error(f"搜索场景名: '{scene_name}', 尝试次数: {attempt}")
                raise FileNotFoundError(f"渲染后找不到视频文件: {scene_name}")

            result_path = str(rendered_video_path.resolve())
            logger.success(f"[Entry {seq_num}] 视频渲染成功: {result_path}")

            return result_path

        except subprocess.CalledProcessError as e:
            logger.error(f"[Entry {seq_num}, Attempt {attempt}] Manim渲染失败: {e.stdout}")
            raise
        except Exception as e:
            logger.error(f"[Entry {seq_num}, Attempt {attempt}] 渲染异常: {e}")
            raise

    def find_existing_video(self, seq_num: int, scene_name: str, attempt: int = 1) -> Optional[str]:
        """
        查找已存在的视频文件

        Args:
            seq_num: 序号
            scene_name: 场景名称
            attempt: 尝试次数

        Returns:
            视频文件路径，如果不存在则返回None
        """
        video_path = self.path_manager.find_rendered_video(seq_num, scene_name, attempt)

        if video_path:
            logger.info(f"[Entry {seq_num}] 找到已存在的视频: {video_path}")
            return str(video_path.resolve())

        logger.debug(f"[Entry {seq_num}] 未找到已存在的视频: {scene_name}")
        return None
