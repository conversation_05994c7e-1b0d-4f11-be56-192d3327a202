"""
DSL生成器
"""
import json
from pathlib import Path
from typing import Any, Optional

from loguru import logger

from agents.dsl_generation_agent import DSLGenerationToolkit
from feynman_workflow.core.performance_monitor import timing_decorator

from ..config import StoryboardConfig
from ..managers import PathManager


class DSLGenerator:
    """DSL生成处理器"""

    def __init__(self, config: StoryboardConfig, path_manager: PathManager):
        self.config = config
        self.path_manager = path_manager

        # 加载DSL文档
        self.dsl_schema = self._load_dsl_documentation()

        # 初始化DSL生成工具包
        self.dsl_toolkit = DSLGenerationToolkit()

    def _load_dsl_documentation(self) -> str:
        """加载DSL文档"""
        try:
            with open(self.config.dsl_schema_file, encoding="utf-8") as f:
                schema = f.read()
            logger.debug(f"加载DSL文档: {self.config.dsl_schema_file}")
            return schema
        except Exception as e:
            logger.error(f"无法加载DSL文档 {self.config.dsl_schema_file}: {e}")
            raise

    @timing_decorator("生成DSL")
    def generate_dsl(
        self, entry: dict[str, Any], seq_num: int, attempt: int = 1, error_context: Optional[str] = None
    ) -> str:
        """
        为单个分镜条目生成DSL JSON文件

        Args:
            entry: 分镜条目数据
            seq_num: 序号
            attempt: 尝试次数
            error_context: 错误上下文（用于重试）

        Returns:
            生成的DSL文件路径
        """
        dsl_file_path = self.path_manager.get_dsl_path(seq_num, attempt)

        logger.info(f"[Entry {seq_num}, Attempt {attempt}] 生成DSL: {entry.get('分镜名', '')}")

        if error_context:
            logger.warning(f"[Entry {seq_num}] 重试DSL生成，错误上下文: {error_context}")

        try:
            # 将条目数据转换为JSON字符串
            entry_str = json.dumps(entry, ensure_ascii=False, indent=2)

            # 调用DSL生成工具包
            generated_dsl_file = self.dsl_toolkit.generate_dsl_json(
                storyboard_content=entry_str,
                output_file=str(dsl_file_path),
                dsl_schema=self.dsl_schema,
                seq_num=seq_num,
                error_context=error_context,
            )

            if not generated_dsl_file or not Path(generated_dsl_file).exists():
                raise ValueError(f"DSL生成失败或返回了无效的文件路径: {generated_dsl_file}")

            # 确保返回绝对路径
            result_path = str(Path(generated_dsl_file).resolve())
            logger.success(f"[Entry {seq_num}] DSL文件生成成功: {result_path}")

            return result_path

        except Exception as e:
            logger.error(f"[Entry {seq_num}, Attempt {attempt}] DSL生成失败: {e}")
            raise

    @timing_decorator("解析DSL元数据")
    def extract_scene_info(self, dsl_file_path: str, seq_num: int) -> dict[str, str]:
        """
        从DSL文件中提取场景信息

        Args:
            dsl_file_path: DSL文件路径
            seq_num: 序号

        Returns:
            包含场景名称等信息的字典
        """
        try:
            dsl_path = Path(dsl_file_path)
            if not dsl_path.exists():
                raise FileNotFoundError(f"DSL文件不存在: {dsl_file_path}")

            with open(dsl_path, encoding="utf-8") as f:
                dsl_content = json.load(f)

            # 从DSL元数据中提取场景名称
            metadata = dsl_content.get("metadata", {})
            title = metadata.get("title", f"Storyboard_{seq_num}")

            # 清理和规范化场景名称
            scene_name = self._sanitize_scene_name(title)

            # 如果场景名称不符合Python类名规范，添加前缀
            if not scene_name or (not scene_name[0].isalpha() and scene_name[0] != "_"):
                scene_name = f"ManimScene_{scene_name}"

            logger.debug(f"[Entry {seq_num}] 提取场景信息: {scene_name}")

            return {"scene_name": scene_name, "base_scene_name_for_files": scene_name, "original_title": title}

        except Exception as e:
            logger.warning(f"[Entry {seq_num}] 无法提取DSL场景信息: {e}, 使用默认名称")
            fallback_name = f"Storyboard_{seq_num}"
            return {
                "scene_name": fallback_name,
                "base_scene_name_for_files": fallback_name,
                "original_title": fallback_name,
            }

    def _sanitize_scene_name(self, name: str) -> str:
        """清理场景名称，使其符合Python类名规范"""
        # 替换空格和连字符为下划线
        sanitized = name.replace(" ", "_").replace("-", "_")

        # 只保留字母、数字和下划线
        sanitized = "".join(c if c.isalnum() or c == "_" else "_" for c in sanitized)

        # 移除连续的下划线
        while "__" in sanitized:
            sanitized = sanitized.replace("__", "_")

        # 移除开头和结尾的下划线
        sanitized = sanitized.strip("_")

        return sanitized
