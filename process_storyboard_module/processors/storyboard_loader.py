"""
分镜脚本加载器
"""
import json
from pathlib import Path
from typing import Any

from loguru import logger

from feynman_workflow.core.performance_monitor import timing_decorator

from ..config import StoryboardConfig


class StoryboardLoader:
    """分镜脚本加载器"""

    def __init__(self, config: StoryboardConfig):
        self.config = config

    @timing_decorator("加载分镜脚本")
    def load_storyboard(self) -> list[dict[str, Any]]:
        """
        加载并解析分镜脚本JSON文件

        Returns:
            分镜条目列表
        """
        try:
            storyboard_path = Path(self.config.storyboard_file)

            if not storyboard_path.exists():
                raise FileNotFoundError(f"分镜脚本文件不存在: {self.config.storyboard_file}")

            with open(storyboard_path, encoding="utf-8") as f:
                data = json.load(f)

            # 处理不同的JSON结构
            if isinstance(data, dict):
                # 如果根对象是字典，查找'storyboard'键
                storyboard = data.get("storyboard", [])
            else:
                # 假设已经是分镜条目列表
                storyboard = data

            if not isinstance(storyboard, list):
                raise ValueError("分镜脚本格式错误：应该包含分镜条目列表")

            logger.info(f"成功加载分镜脚本，共 {len(storyboard)} 个条目")
            return storyboard

        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            raise ValueError(f"分镜脚本JSON格式错误: {e}")
        except Exception as e:
            logger.error(f"加载分镜脚本失败: {e}")
            raise

    @timing_decorator("验证分镜数据")
    def validate_storyboard_entries(self, storyboard: list[dict[str, Any]]) -> list[dict[str, Any]]:
        """
        验证分镜条目的数据完整性

        Args:
            storyboard: 分镜条目列表

        Returns:
            验证后的分镜条目列表
        """
        validated_entries = []

        for i, entry in enumerate(storyboard):
            if not isinstance(entry, dict):
                logger.warning(f"跳过非字典类型的分镜条目 {i+1}")
                continue

            # 检查必要字段
            if not entry.get("分镜名"):
                logger.warning(f"分镜条目 {i+1} 缺少'分镜名'字段，使用默认名称")
                entry["分镜名"] = f"分镜_{i+1}"

            validated_entries.append(entry)

        logger.info(f"验证完成，有效分镜条目: {len(validated_entries)}")
        return validated_entries
