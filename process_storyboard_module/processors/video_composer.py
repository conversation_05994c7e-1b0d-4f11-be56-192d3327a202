"""
视频合成器 - 负责视频拼接和后处理
"""
import json
import subprocess
from pathlib import Path
from typing import Optional

from loguru import logger

from feynman_workflow.core.performance_monitor import timing_decorator
from utils.common import Config

from ..config import StoryboardConfig
from ..managers import PathManager


class VideoComposer:
    """视频合成器"""

    def __init__(self, config: StoryboardConfig, path_manager: PathManager):
        self.config = config
        self.path_manager = path_manager

        # 加载背景音乐配置
        self.bg_config = Config().config.get("background_music", {})

    @timing_decorator("音频视频同步修复")
    def ensure_audio_video_sync(self, video_paths: list[str]) -> list[str]:
        """
        确保每个视频的音频时长与视频时长匹配

        Args:
            video_paths: 原始视频文件路径列表

        Returns:
            处理后的视频文件路径列表
        """
        if not video_paths:
            return []

        logger.info(f"检查并修复 {len(video_paths)} 个视频的音频同步问题")
        processed_paths = []

        for i, video_path in enumerate(video_paths):
            try:
                # 获取视频和音频时长
                probe_cmd = [
                    "ffprobe",
                    "-v",
                    "quiet",
                    "-print_format",
                    "json",
                    "-show_streams",
                    str(Path(video_path).resolve()),
                ]

                result = subprocess.run(probe_cmd, capture_output=True, text=True, encoding="utf-8", check=True)
                probe_data = json.loads(result.stdout)

                video_duration = None
                audio_duration = None

                for stream in probe_data.get("streams", []):
                    if stream.get("codec_type") == "video" and "duration" in stream:
                        video_duration = float(stream["duration"])
                    elif stream.get("codec_type") == "audio" and "duration" in stream:
                        audio_duration = float(stream["duration"])

                # 检查是否需要音频填充
                if video_duration and audio_duration and audio_duration < video_duration - 0.1:
                    logger.info(
                        f"视频 {i+1}: 音频时长 {audio_duration:.2f}s < 视频时长 {video_duration:.2f}s，添加静音填充"
                    )

                    # 生成修复后的视频路径
                    fixed_video_path = self.path_manager.get_fixed_sync_video_path(Path(video_path), i + 1)

                    # 填充静音
                    fix_cmd = [
                        "ffmpeg",
                        "-y",
                        "-i",
                        str(Path(video_path).resolve()),
                        "-filter_complex",
                        f"[0:a]apad=pad_dur={video_duration - audio_duration}[a]",
                        "-map",
                        "0:v",
                        "-map",
                        "[a]",
                        "-c:v",
                        "copy",
                        "-c:a",
                        "aac",
                        str(fixed_video_path.resolve()),
                    ]

                    subprocess.run(fix_cmd, check=True, capture_output=True, text=True, encoding="utf-8")
                    processed_paths.append(str(fixed_video_path))
                    logger.success(f"音频同步修复完成: {fixed_video_path.name}")
                else:
                    # 音频时长正常，直接使用原视频
                    processed_paths.append(video_path)

            except Exception as e:
                logger.warning(f"处理视频 {Path(video_path).name} 时出错: {e}，使用原视频")
                processed_paths.append(video_path)

        return processed_paths

    @timing_decorator("创建包含转场的视频序列")
    def create_video_sequence_with_transitions(self, video_paths: list[str], transition_videos: list[str]) -> list[str]:
        """
        创建包含转场视频的完整视频序列

        Args:
            video_paths: 原始分镜视频路径列表
            transition_videos: 转场视频路径列表

        Returns:
            包含转场视频的完整视频序列
        """
        if not self.config.transition_enabled or len(video_paths) < 2:
            logger.info(
                f"转场功能已禁用或视频数量不足，跳过转场视频集成: {self.config.transition_enabled} {len(video_paths)} 个视频"
            )
            return video_paths

        logger.info(f"构建包含转场的视频序列: {len(video_paths)} 个分镜 + {len(transition_videos)} 个转场")

        final_sequence = []

        # 构建最终序列：分镜1 -> 转场1 -> 分镜2 -> 转场2 -> ... -> 分镜N
        for i, video_path in enumerate(video_paths):
            final_sequence.append(video_path)

            # 如果不是最后一个视频，尝试添加转场视频
            if i < len(video_paths) - 1:
                if i < len(transition_videos):
                    if transition_videos[i]:
                        final_sequence.append(transition_videos[i])
                        logger.debug(f"添加转场视频: {Path(transition_videos[i]).name}")
                    else:
                        logger.warning(f"跳过失败的转场视频: 分镜{i+1} -> 分镜{i+2}")
                else:
                    logger.warning(f"跳过超出范围的转场视频: 分镜{i+1} -> 分镜{i+2}")

        logger.info(f"视频序列创建完成，共 {len(final_sequence)} 个文件")
        return final_sequence

    @timing_decorator("视频拼接")
    def concatenate_videos(self, video_paths: list[str]) -> Optional[str]:
        """
        拼接视频文件

        Args:
            video_paths: 要拼接的视频文件路径列表

        Returns:
            拼接后的视频文件路径，失败返回None
        """
        if not video_paths:
            logger.warning("没有视频文件需要拼接")
            return None

        logger.info(f"开始拼接 {len(video_paths)} 个视频文件")

        # 首先确保音频视频同步
        synced_videos = self.ensure_audio_video_sync(video_paths)

        # 获取最终视频路径
        final_video_path = self.path_manager.get_final_video_path()
        list_file_path = self.path_manager.get_temp_list_file_path()

        try:
            # 创建ffmpeg文件列表
            with open(list_file_path, "w", encoding="utf-8") as f:
                for video_path in synced_videos:
                    abs_path = Path(video_path).resolve()
                    # 简化路径处理，避免过度转义
                    f.write(f"file '{abs_path}'\n")

            logger.debug(f"创建ffmpeg文件列表: {list_file_path}")

            # 验证文件列表内容
            if logger.level("DEBUG").no <= logger._core.min_level:
                with open(list_file_path, encoding="utf-8") as f:
                    logger.debug(f"文件列表内容:\n{f.read()}")

            # 执行拼接
            ffmpeg_cmd = [
                "ffmpeg",
                "-y",
                "-f",
                "concat",
                "-safe",
                "0",
                "-i",
                str(list_file_path.resolve()),
                "-c",
                "copy",
                "-avoid_negative_ts",
                "make_zero",
                "-async",
                "1",
                str(final_video_path.resolve()),
            ]

            logger.debug(f"运行ffmpeg拼接命令: {' '.join(ffmpeg_cmd)}")

            subprocess.run(ffmpeg_cmd, check=True, capture_output=True, text=True, encoding="utf-8")

            result_path = str(final_video_path.resolve())
            logger.success(f"视频拼接成功: {final_video_path.name}")

            return result_path

        except subprocess.CalledProcessError as e:
            logger.error(f"视频拼接失败: {e.stderr}")
            return None
        except Exception as e:
            logger.error(f"视频拼接异常: {e}")
            return None

    @timing_decorator("添加背景音乐")
    def add_background_music(self, video_path: str) -> Optional[str]:
        """
        为视频添加背景音乐

        Args:
            video_path: 输入视频文件路径

        Returns:
            添加背景音乐后的视频路径，失败返回原路径
        """
        if not self.bg_config.get("enabled", False):
            logger.info("背景音乐功能已禁用")
            return video_path

        audio_file = self.bg_config.get("audio_file", "")
        if not audio_file:
            logger.warning("未指定背景音乐文件")
            return video_path

        # 解析音频文件路径
        audio_path = Path(audio_file)
        if not audio_path.is_absolute():
            project_root = self.path_manager.project_root
            audio_path = project_root / audio_file

        if not audio_path.exists():
            logger.error(f"背景音乐文件不存在: {audio_path}")
            return video_path

        input_video_path = Path(video_path)
        output_video_path = self.path_manager.get_video_with_music_path(input_video_path)

        # 获取配置参数
        volume = max(0.0, min(1.0, self.bg_config.get("volume", 0.1)))
        fade_in = max(0.0, self.bg_config.get("fade_in", 2.0))
        fade_out = max(0.0, self.bg_config.get("fade_out", 3.0))
        loop_audio = self.bg_config.get("loop", True)
        start_offset = max(0.0, self.bg_config.get("start_offset", 0.0))

        logger.info(f"添加背景音乐: {audio_path.name}")
        logger.info(f"音乐设置 - 音量: {volume}, 淡入: {fade_in}s, 淡出: {fade_out}s, 循环: {loop_audio}")

        try:
            # 获取视频时长
            duration_cmd = [
                "ffprobe",
                "-v",
                "quiet",
                "-show_entries",
                "format=duration",
                "-of",
                "csv=p=0",
                str(input_video_path.resolve()),
            ]

            duration_result = subprocess.run(duration_cmd, capture_output=True, text=True, encoding="utf-8")
            if duration_result.returncode != 0:
                logger.error(f"获取视频时长失败: {duration_result.stderr}")
                return video_path

            try:
                video_duration = float(duration_result.stdout.strip())
            except ValueError:
                logger.error(f"无效的视频时长: {duration_result.stdout.strip()}")
                return video_path

            # 构建音频滤镜
            audio_filters = []

            if start_offset > 0:
                audio_filters.append(f"adelay={int(start_offset * 1000)}|{int(start_offset * 1000)}")

            if volume != 1.0:
                audio_filters.append(f"volume={volume}")

            if fade_in > 0 or fade_out > 0:
                fade_filter = "afade=t=in:st=0"
                if fade_in > 0:
                    fade_filter += f":d={fade_in}"
                if fade_out > 0:
                    fade_filter += f",afade=t=out:st={max(0, video_duration - fade_out)}:d={fade_out}"
                audio_filters.append(fade_filter)

            # 构建完整的ffmpeg命令
            ffmpeg_cmd = ["ffmpeg", "-y"]

            # 输入视频
            ffmpeg_cmd.extend(["-i", str(input_video_path.resolve())])

            # 输入音频
            if loop_audio:
                ffmpeg_cmd.extend(["-stream_loop", "-1", "-i", str(audio_path.resolve())])
                ffmpeg_cmd.extend(["-t", str(video_duration)])
            else:
                ffmpeg_cmd.extend(["-i", str(audio_path.resolve())])

            # 应用音频滤镜
            if audio_filters:
                audio_filter_string = ",".join(audio_filters)
                ffmpeg_cmd.extend(
                    ["-filter_complex", f"[1:a]{audio_filter_string}[bg];[0:a][bg]amix=inputs=2:duration=shortest[out]"]
                )
                ffmpeg_cmd.extend(["-map", "0:v", "-map", "[out]"])
            else:
                ffmpeg_cmd.extend(["-filter_complex", "[0:a][1:a]amix=inputs=2:duration=shortest[out]"])
                ffmpeg_cmd.extend(["-map", "0:v", "-map", "[out]"])

            # 输出设置
            ffmpeg_cmd.extend(["-c:v", "copy", "-c:a", "aac", str(output_video_path.resolve())])

            logger.debug(f"运行ffmpeg背景音乐命令: {' '.join(ffmpeg_cmd)}")

            subprocess.run(ffmpeg_cmd, check=True, capture_output=True, text=True, encoding="utf-8")

            result_path = str(output_video_path.resolve())
            logger.success(f"背景音乐添加成功: {output_video_path.name}")

            return result_path

        except subprocess.CalledProcessError as e:
            logger.error(f"背景音乐添加失败: {e.stderr}")
            return video_path
        except Exception as e:
            logger.error(f"背景音乐处理异常: {e}")
            return video_path
