"""
Manim代码生成器
"""
import shutil
import subprocess
from pathlib import Path

from loguru import logger

from feynman_workflow.core.performance_monitor import timing_decorator

from ..config import StoryboardConfig
from ..managers import PathManager


class ManimCodeGenerator:
    """Man<PERSON>代码生成处理器"""

    def __init__(self, config: StoryboardConfig, path_manager: PathManager):
        self.config = config
        self.path_manager = path_manager

        # 检查是否可用uv
        self.use_uv = config.use_uv
        if self.use_uv is None:
            self.use_uv = shutil.which("uv") is not None

        if self.use_uv:
            logger.info("检测到 uv，将使用 'uv run' 来执行命令")
        else:
            logger.info("未检测到 uv，将直接执行命令")

    @timing_decorator("生成Manim代码")
    def generate_code(self, dsl_file_path: str, seq_num: int, attempt: int = 1) -> str:
        """
        从DSL文件生成Manim Python代码

        Args:
            dsl_file_path: DSL文件路径
            seq_num: 序号
            attempt: 尝试次数

        Returns:
            生成的Python文件路径
        """
        dsl_path = Path(dsl_file_path)
        if not dsl_path.exists():
            raise FileNotFoundError(f"DSL文件不存在: {dsl_file_path}")

        py_file_path = self.path_manager.get_py_path(seq_num, attempt)

        logger.info(f"[Entry {seq_num}, Attempt {attempt}] 生成Manim代码: {dsl_path.name}")

        # 构建dsl_to_manim命令的后缀参数
        scene_suffix = f"A{attempt}" if attempt > 1 else ""

        try:
            # 构建命令
            cmd = []
            if self.use_uv:
                cmd.extend(["uv", "run"])

            cmd.extend(
                [
                    "python",
                    "-m",
                    "dsl.v2.dsl_to_manim",
                    str(dsl_path.resolve()),
                    "--output",
                    str(py_file_path.resolve()),
                    "--suffix",
                    scene_suffix,
                ]
            )

            logger.debug(f"运行Manim代码生成命令: {' '.join(cmd)}")

            # 执行命令
            _ = subprocess.run(cmd, check=True, capture_output=True, text=True, encoding="utf-8")

            # 验证输出文件
            if not py_file_path.exists():
                raise FileNotFoundError(f"代码生成失败，输出文件不存在: {py_file_path}")

            result_path = str(py_file_path.resolve())
            logger.success(f"[Entry {seq_num}] Manim代码生成成功: {result_path}")

            return result_path

        except subprocess.CalledProcessError as e:
            logger.error(f"[Entry {seq_num}, Attempt {attempt}] Manim代码生成失败: {e.stderr}")
            raise
        except Exception as e:
            logger.error(f"[Entry {seq_num}, Attempt {attempt}] 代码生成异常: {e}")
            raise
