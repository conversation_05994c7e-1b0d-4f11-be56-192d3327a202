"""
转场生成器 - 支持并行处理
"""
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from pathlib import Path
from typing import Optional

from loguru import logger

from dsl.v2.core.inter_scene_transition import generate_inter_scene_transition
from feynman_workflow.core.performance_monitor import timing_decorator

from ..config import StoryboardConfig
from ..managers import PathManager
from ..models import TransitionResult


class TransitionGenerator:
    """转场视频生成器 - 支持并行处理"""

    def __init__(self, config: StoryboardConfig, path_manager: PathManager):
        self.config = config
        self.path_manager = path_manager

    @timing_decorator("生成单个转场视频")
    def generate_single_transition(
        self, from_scene: str, to_scene: str, transition_type: Optional[str] = None
    ) -> TransitionResult:
        """
        生成单个转场视频

        Args:
            from_scene: 源场景ID
            to_scene: 目标场景ID
            transition_type: 转场类型，None表示使用配置中的默认类型

        Returns:
            转场处理结果
        """
        if not self.config.transition_enabled:
            logger.debug(f"转场功能已禁用，跳过: {from_scene} -> {to_scene}")
            return TransitionResult(
                from_scene=from_scene, to_scene=to_scene, success=False, error_message="转场功能已禁用"
            )

        logger.info(f"生成转场视频: {from_scene} -> {to_scene}")

        # 使用配置中的转场类型或传入的类型
        actual_transition_type = transition_type or self.config.transition_type

        try:
            # 调用现有的转场生成系统
            transition_video_path = generate_inter_scene_transition(
                from_scene_id=from_scene,
                to_scene_id=to_scene,
                transition_type=actual_transition_type,
                transition_run_time=self.config.transition_run_time,
                quality=self.config.quality,
            )

            if transition_video_path and Path(transition_video_path).exists():
                logger.success(f"转场视频生成成功: {from_scene} -> {to_scene}: {transition_video_path}")
                return TransitionResult(
                    from_scene=from_scene, to_scene=to_scene, success=True, video_file=transition_video_path
                )
            else:
                error_msg = f"转场视频生成失败或文件不存在: {transition_video_path}"
                logger.warning(error_msg)
                return TransitionResult(
                    from_scene=from_scene, to_scene=to_scene, success=False, error_message=error_msg
                )

        except Exception as e:
            error_msg = f"生成转场视频时发生异常: {e}"
            logger.error(error_msg)
            import traceback

            traceback.print_exc()

            return TransitionResult(from_scene=from_scene, to_scene=to_scene, success=False, error_message=error_msg)

    @timing_decorator("并行生成转场视频")
    def generate_transitions_parallel(
        self, scene_pairs: list[tuple[str, str]], transition_type: Optional[str] = None
    ) -> list[TransitionResult]:
        """
        并行生成多个转场视频

        Args:
            scene_pairs: 场景对列表 [(from_scene, to_scene), ...]
            transition_type: 转场类型，None表示使用配置中的默认类型

        Returns:
            转场处理结果列表
        """
        if not self.config.transition_enabled or not scene_pairs:
            logger.info("转场功能已禁用或无场景对，跳过转场视频生成")
            return []

        logger.info(f"开始并行生成 {len(scene_pairs)} 个转场视频，使用 {self.config.max_workers} 个工作线程")

        results = []

        # 使用线程池并行处理转场生成
        with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
            # 提交所有转场生成任务
            future_to_pair = {
                executor.submit(self.generate_single_transition, from_scene, to_scene, transition_type): (
                    from_scene,
                    to_scene,
                )
                for from_scene, to_scene in scene_pairs
            }

            # 收集完成的结果
            for future in as_completed(future_to_pair):
                from_scene, to_scene = future_to_pair[future]
                try:
                    result = future.result()
                    results.append(result)

                    if result.success:
                        logger.info(f"✅ 转场完成: {from_scene} -> {to_scene}")
                    else:
                        logger.warning(f"❌ 转场失败: {from_scene} -> {to_scene}: {result.error_message}")

                except Exception as e:
                    logger.error(f"❌ 转场任务异常: {from_scene} -> {to_scene}: {e}")
                    results.append(
                        TransitionResult(
                            from_scene=from_scene, to_scene=to_scene, success=False, error_message=f"任务执行异常: {e}"
                        )
                    )

        # 统计结果
        successful_count = sum(1 for r in results if r.success)
        failed_count = len(results) - successful_count

        logger.info(f"转场视频生成完成: 成功 {successful_count}, 失败 {failed_count}, 总计 {len(results)}")

        results = list(sorted(results, key=lambda x: x.from_scene))

        return results

    @timing_decorator("创建场景对序列")
    def create_scene_pairs_from_entries(
        self, successful_entries: list[any], start_idx: int = 0
    ) -> list[tuple[str, str]]:
        """
        从成功处理的分镜条目创建场景对序列

        Args:
            successful_entries: 成功处理的分镜条目列表
            start_idx: 起始索引

        Returns:
            场景对列表
        """
        if len(successful_entries) < 2:
            logger.info("分镜条目数量不足，无需生成转场")
            return []

        scene_pairs = []

        for i in range(len(successful_entries) - 1):
            # 基于索引生成场景ID（与原代码保持一致的命名）
            from_scene_id = f"Storyboard_{start_idx + i + 1}"
            to_scene_id = f"Storyboard_{start_idx + i + 2}"

            scene_pairs.append((from_scene_id, to_scene_id))

        logger.info(f"创建了 {len(scene_pairs)} 个场景对用于转场生成")
        return scene_pairs

    def get_successful_transition_videos(self, results: list[TransitionResult]) -> list[str]:
        """
        获取成功生成的转场视频文件列表

        Args:
            results: 转场处理结果列表

        Returns:
            成功的转场视频文件路径列表
        """
        videos = []
        for result in results:
            if result.success and result.video_file:
                videos.append(result.video_file)

        logger.info(f"获取到 {len(videos)} 个成功的转场视频")
        return videos
