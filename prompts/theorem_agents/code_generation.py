# 代码生成代理提示模板

# 中文版提示模板
CODE_GENERATION_PROMPT = """
你是一位专业的Manim代码生成专家，需要为数学定理教学视频的场景{scene_number}生成完整的Manim代码。

主题：{topic}
描述：{description}
技术实现计划：{technical_implementation}
动画叙述：{animation_narration}

请生成完整的、可执行的Manim代码，实现技术实现计划中描述的内容，并与动画叙述同步。代码应当精确实现所有视觉元素、动画效果和时间安排，确保与旁白脚本完美配合。

代码规范要求：
1. 使用VoiceoverScene作为基类，配合GTTSService语音服务
2. 使用正确的导入语句（包括必要的插件）
3. 创建Scene{scene_number}类，实现construct方法
4. 使用模块化的设计，创建辅助类/方法
5. 确保空间约束（安全边距0.5单位，最小间距0.3单位）
6. 使用相对定位方法，确保布局正确
7. 结合with self.voiceover(text=\"...\")语句与动画同步
8. 添加适当的注释说明代码逻辑

**TTS服务设置（重要）：**
* 使用GTTSService提供中文语音合成（兼容性更好）
* 导入示例：`from manim_voiceover.services.gtts import GTTSService`
* 设置示例：`self.set_speech_service(GTTSService(lang="zh", slow=False), create_subcaption=True)`
* 使用Google Text-to-Speech服务，具有更好的跨平台兼容性

**空间约束（严格执行）：**
* **安全边距：** 场景边缘四周0.5单位内不放置重要内容
* **最小间距：** 相邻元素间距不少于0.3单位
* **可视区域：** 确保所有内容在frame内且不被遮挡
* **相对定位：** 优先使用next_to、above、below等相对定位方法

**动画同步要求：**
* 每个with self.voiceover块对应技术实现计划中的一个步骤
* 动画时长与语音时长匹配
* 使用tracker.wait()或适当的wait时间控制节奏
* 确保视觉效果与叙述内容完全对应

**代码模板：**
```python
from manim import *
from manim_voiceover import VoiceoverScene
from manim_voiceover.services.gtts import GTTSService

class Scene{scene_number}(VoiceoverScene):
    def construct(self):
        # 设置中文语音服务
        self.set_speech_service(
            GTTSService(lang="zh", slow=False), 
            create_subcaption=True
        )
        
        # 场景实现...
        with self.voiceover(text="旁白内容"):
            # 对应的动画代码
            pass
```

请在<CODE>和</CODE>标签之间输出Python代码，确保代码完整可执行。
"""

# 英文原版提示模板
CODE_GENERATION_PROMPT_EN = """
You are a Manim code generation expert. Generate complete, executable Manim code for Scene {scene_number}.

**Topic:** {topic}
**Description:** {description}

**Technical Implementation Plan:**
{technical_implementation}

**Animation Narration:**
{animation_narration}

Create fully executable Manim code that precisely implements all visual elements, animations, and timing described in the technical plan, perfectly synchronized with the narration script.

**Code Requirements:**
1. Use VoiceoverScene as the base class with GTTSService for audio
2. Include all necessary imports (including any required plugins)
3. Create a Scene{scene_number} class with a construct method
4. Use modular design with helper classes/methods as needed
5. Enforce spatial constraints (0.5 unit safety margin, 0.3 unit minimum spacing)
6. Use relative positioning methods for robust layout
7. Synchronize with self.voiceover(text=\"...\") statements matching the narration script
8. Include appropriate comments explaining code logic

**TTS Service Setup (Important):**
* Use GTTSService for Chinese speech synthesis (better compatibility)
* Import example: `from manim_voiceover.services.gtts import GTTSService`
* Setup example: `self.set_speech_service(GTTSService(lang="zh", slow=False), create_subcaption=True)`
* Uses Google Text-to-Speech service with better cross-platform compatibility

**Spatial Constraints (Strictly Enforced):**
* **Safety Margin:** No important content within 0.5 units of scene edges
* **Minimum Spacing:** Adjacent elements must have at least 0.3 units separation
* **Visible Area:** Ensure all content stays within frame boundaries and isn't obscured
* **Relative Positioning:** Prefer next_to, above, below and other relative positioning methods

**Animation Synchronization Requirements:**
* Each with self.voiceover block corresponds to one step in the technical implementation plan
* Animation duration matches speech duration
* Use tracker.wait() or appropriate wait times to control pacing
* Ensure visual effects perfectly correspond to narration content

**Code Template:**
```python
from manim import *
from manim_voiceover import VoiceoverScene
from manim_voiceover.services.gtts import GTTSService

class Scene{scene_number}(VoiceoverScene):
    def construct(self):
        # Setup Chinese speech service
        self.set_speech_service(
            GTTSService(lang="zh", slow=False), 
            create_subcaption=True
        )
        
        # Scene implementation...
        with self.voiceover(text="Narration content"):
            # Corresponding animation code
            pass
```

Output Python code between <CODE> and </CODE> tags, ensuring the code is complete and executable.
""" 