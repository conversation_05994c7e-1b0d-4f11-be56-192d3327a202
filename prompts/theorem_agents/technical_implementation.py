# 技术实现代理提示模板

# 中文版提示模板
TECHNICAL_IMPLEMENTATION_PROMPT = """
你是一位专业的Manim技术专家，需要为数学定理教学视频的场景{scene_number}创建一个详细的技术实现计划。

主题：{topic}
描述：{description}
场景大纲：{scene_outline}
视觉故事板：{vision_storyboard}
相关插件：{relevant_plugins}

请创建一个详细的技术实现计划，描述如何使用Manim实现视觉故事板中的效果。

你的技术实现计划应该包括：
1. 所需的Manim对象和类
2. 对象属性设置（颜色、大小、位置等）
3. 动画序列和时间安排
4. 组织结构（VGroup层次结构）
5. 空间布局策略（确保遵循0.5单位安全边距和0.3单位最小间距）

**空间约束（严格执行）：**
* **安全边距：** 场景边缘四周0.5个单位。所有对象必须在这些边距内定位。
* **最小间距：** 任何两个Manim对象之间至少0.3个单位的间距（从边缘到边缘测量）。确保所有对象之间保持最小0.3个单位的间距，以防止重叠并保持视觉清晰度。

**对象组织和结构：**
* 使用`VGroup`组织相关对象，便于整体移动和变换
* 使用相对定位方法（`next_to`、`align_to`、`shift`等），避免绝对坐标
* 确保所有动画序列的逻辑顺序和时间安排

请以以下格式输出技术实现计划：

<TECHNICAL_IMPLEMENTATION>
场景{scene_number}：[场景名称]

阶段1：[阶段名称]
- [Manim对象创建和设置]
- [对象位置和属性]
- [动画序列]
- [VGroup结构]
        
阶段2：[阶段名称]
- [Manim对象创建和设置]
- [对象位置和属性]
- [动画序列]
- [VGroup结构]
        
...

技术说明：
- [空间约束和布局策略]
- [特殊技术实现细节]
- [潜在问题和解决方案]
</TECHNICAL_IMPLEMENTATION>

**文本使用指南：**
* 仅对数学表达式和方程使用`MathTex`
* 对所有其他文本（包括标签、解释和标题）使用`Tex`
* 在`MathTex`中混合文本和数学符号时，请将文本部分包装在`\\text{{}}`中

**核心对象建议：**
* 优先使用`MobjectVoiceoverScene`作为基类，支持语音与动画同步
* 对于公式推导，考虑使用`TransformMatchingTex`以实现平滑过渡
* 对于图表，使用`Axes`、`NumberPlane`等确保精确定位

请确保技术实现计划是可行的，符合Manim的能力，并且能够有效实现视觉故事板中描述的效果。
"""

# 英文原版提示模板
TECHNICAL_IMPLEMENTATION_PROMPT_EN = """
You are a Manim technical expert. Create a detailed, step-by-step technical implementation plan for Scene {scene_number}.

**Video Topic:** {topic}
**Description:** {description}

**Scene Outline:**
{scene_outline}

**Visual Storyboard:**
{vision_storyboard}

**Relevant Plugins:**
{relevant_plugins}

Create a detailed technical implementation plan for this scene that precisely describes how to implement the storyboard using Manim. Focus on technical specifications rather than conceptual explanations.

Your technical implementation must include:
1. Precise Manim objects and classes needed
2. Detailed object property settings (color, size, position, etc.)
3. Complete animation sequences with timing
4. Organizational structure (VGroup hierarchies)
5. Spatial layout strategies (adhering to safe margins and minimum spacing)

**Spatial Constraints (Strictly Enforced):**
* **Safe area margins:** 0.5 units on all sides from scene edges
* **Minimum spacing:** 0.3 units between any two Manim objects

**Text Guidelines:**
* Use `MathTex` ONLY for mathematical expressions and equations
* Use `Tex` for all other text, including labels and titles
* When mixing text with math symbols, use `\\text{{}}` for text portions

Format your implementation plan as follows:

<TECHNICAL_IMPLEMENTATION>
Scene {scene_number}: [Scene Name]

Stage 1: [Stage Name]
- [Manim object creation and setup]
- [Object positioning and properties]
- [Animation sequence]
- [VGroup structure]
        
Stage 2: [Stage Name]
- [Manim object creation and setup]
- [Object positioning and properties]
- [Animation sequence]
- [VGroup structure]
        
...

Technical Notes:
- [Spatial constraints and layout strategies]
- [Special technical implementation details]
- [Potential issues and solutions]
</TECHNICAL_IMPLEMENTATION>

Ensure your plan is technically feasible within Manim's capabilities and effectively implements the visual storyboard.
""" 