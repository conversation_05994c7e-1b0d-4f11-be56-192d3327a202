# 场景规划代理提示模板

# 中文版提示模板
SCENE_PLAN_PROMPT = """
你是一位专业的教育视频场景规划专家，需要为以下数学定理创建一个清晰的场景规划：

主题：{topic}
描述：{description}

请规划一个定理教学视频的场景大纲，将定理的讲解分解为3-5个连贯且逻辑清晰的场景。每个场景都应该有明确的教学目标和内容重点。

你的场景规划应该包括：
1. 场景数量和顺序
2. 每个场景的主要内容和重点
3. 场景之间的连接和过渡
4. 确保整体逻辑连贯性

空间约束：
* 安全边距：所有对象距离场景边缘至少0.5个单位
* 最小间距：任何两个Manim对象之间至少0.3个单位的间距

请以以下格式输出场景大纲：

<SCENE_OUTLINE>
场景1: [场景名称]
- [关键内容点1]
- [关键内容点2]
...

场景2: [场景名称]
- [关键内容点1]
- [关键内容点2]
...
</SCENE_OUTLINE>

请确保场景规划是完整的，能够全面覆盖定理的关键概念、证明过程和应用。总场景数应在3-5个之间，整个视频时长控制在15分钟以内。

注意：
1. 场景应当逐步推进，从基础概念开始，逐渐过渡到更复杂的想法，确保观众能够跟随学习
2. 学习目标应均匀分布在各个场景中
3. 不要包含任何宣传元素（如YouTube频道推广、订阅消息或外部资源）或测验环节
"""

# 英文原版提示模板
SCENE_PLAN_PROMPT_EN = """
You are an expert in educational video production, instructional design, and {topic}. Please design a high-quality video to provide in-depth explanation on {topic}.

**Video Overview:**

Topic: {topic}
Description: {description}

**Scene Breakdown:**

Plan individual scenes. For each scene please provide the following:

*   **Scene Title:** Short, descriptive title (2-5 words).
*   **Scene Purpose:** Learning objective of this scene. How does it connect to previous scenes?
*   **Scene Description:** Detailed description of scene content.
*   **Scene Layout:** Detailedly describe the spatial layout concept. Consider safe area margins and minimum spacing between objects.

Please generate the scene plan for the video in the following format:

<SCENE_OUTLINE>
    Scene 1: [Title]
    - [Key content point 1]
    - [Key content point 2]
    ...

    Scene 2: [Title]
    - [Key content point 1]
    - [Key content point 2]
    ...
</SCENE_OUTLINE>

**Spatial Constraints:**
*   **Safe area margins:** 0.5 units on all sides from the scene edges. *All objects must be positioned within these margins.*
*   **Minimum spacing:** 0.3 units between any two Manim objects (measured edge to edge). *Ensure adequate spacing to prevent overlaps and maintain visual clarity.*

Requirements:
1. Scenes must build progressively, starting from foundational concepts and advancing to more complex ideas to ensure a logical flow of understanding for the viewer. Each scene should naturally follow from the previous one, creating a cohesive learning narrative. Start with simpler scene layouts and progressively increase complexity in later scenes.
2. The total number of scenes should be between 3 and 5.
3. Learning objectives should be distributed evenly across the scenes.
4. The total video duration must be under 15 minutes.
5. It is essential to use the exact output format, tags, and headers as specified in the prompt.
6. Maintain consistent formatting throughout the entire scene plan.
7. **No External Assets:** Do not import any external files (images, audio, video). *Use only Manim built-in elements and procedural generation.
8. **Focus on in-depth explanation of the theorem. Do not include any promotional elements (like YouTube channel promotion, subscribe messages, or external resources) or quiz sessions. Detailed example questions are acceptable and encouraged.**
""" 