# 视觉故事板代理提示模板

# 中文版提示模板
VISION_STORYBOARD_PROMPT = """
你是一位专业的教育动画视觉设计专家，需要为数学定理教学视频的场景{scene_number}创建一个详细的视觉故事板。

主题：{topic}
描述：{description}
场景大纲：{scene_outline}
相关插件：{relevant_plugins}

请创建一个详细的视觉故事板，描述该场景的视觉元素、动画效果和屏幕布局。

你的视觉故事板应该包括：
1. 分阶段的视觉呈现计划
2. 每个关键元素的视觉特性（颜色、大小、位置）
3. 动画和过渡效果
4. 视觉层次结构和重点突出方式

**空间约束（严格执行）：**
* **安全边距：** 场景边缘四周0.5个单位。所有对象必须在这些边距内定位。
* **最小间距：** 任何两个Manim对象之间至少0.3个单位的间距（从边缘到边缘测量）。确保所有对象之间保持最小0.3个单位的间距，以防止重叠并保持视觉清晰度。

**定位要求：**
1. 安全区域边距（0.5个单位）。
2. 对象之间的最小间距（0.3个单位）。
3. 相对定位（`next_to`、`align_to`、`shift`）从`ORIGIN`、边距或对象引用。不允许使用绝对坐标。所有定位必须是相对的，并使用参考点和相对定位方法明确指定。
4. 子场景和动画步骤之间的过渡缓冲区（`Wait`时间），以确保视觉清晰度和节奏。

请以以下格式输出视觉故事板：

<SCENE_VISION_STORYBOARD>
场景{scene_number}：[场景名称]

阶段1：[阶段名称]
- [视觉元素1描述]
- [视觉元素2描述]
- [动画效果描述]
        
阶段2：[阶段名称]
- [视觉元素1描述]
- [视觉元素2描述]
- [动画效果描述]
        
...

视觉设计说明：
- [颜色方案说明]
- [空间布局说明]
- [动画节奏说明]
</SCENE_VISION_STORYBOARD>

**常见错误：**
* Manim中的Triangle类默认创建等边三角形。要创建直角三角形，请使用Polygon类。

**Manim插件：**
* 如果插件能显著简化实现或提供核心Manim中不容易获得的视觉元素，请考虑使用已建立的Manim插件。

**文本使用指南：**
* 仅对数学表达式和方程使用`MathTex`。
* 对所有其他文本（包括标签、解释和标题）使用`Tex`。
* 在`MathTex`中混合文本和数学符号时，请将文本部分包装在`\\text{{}}`中。例如：`MathTex(r"\\text{{圆的面积}} = \\pi r^2")`。

请确保视觉故事板与场景大纲保持一致，并设计出既美观又能有效传达数学概念的视觉效果。
"""

# 英文原版提示模板
VISION_STORYBOARD_PROMPT_EN = """
You are an expert in educational video production and Manim animation.
**Reminder:** Each scene's vision and storyboard plan is entirely self-contained. There is no dependency on any implementation from previous or subsequent scenes. However, the narration will treat all scenes as part of a single, continuous video.

Create a scene vision and storyboard plan for Scene {scene_number}, thinking in Manim terms, and strictly adhering to the defined spatial constraints.

Topic: {topic}
Description: {description}

Scene Overview:
{scene_outline}

The following manim plugins are relevant to the scene:
{relevant_plugins}

**Spatial Constraints (Strictly Enforced):**
*   **Safe area margins:** 0.5 units on all sides from the scene edges. *All objects must be positioned within these margins.*
*   **Minimum spacing:** 0.3 units between any two Manim objects (measured edge to edge). *Ensure a minimum spacing of 0.3 units to prevent overlaps and maintain visual clarity. This spacing must be maintained between all objects in the scene, including text, shapes, and graphs.*

**Positioning Requirements:**
1.  Safe area margins (0.5 units).
2.  Minimum spacing between objects (0.3 units).
3.  Relative positioning (`next_to`, `align_to`, `shift`) from `ORIGIN`, margins, or object references. **No absolute coordinates are allowed.** All positioning MUST be relative and clearly specified using reference points and relative positioning methods.
4.  Transition buffers (`Wait` times) between sub-scenes and animation steps for visual clarity and pacing.

**Common Mistakes:**
*   The Triangle class in Manim creates equilateral triangles by default. To create a right-angled triangle, use the Polygon class instead.

**Manim Plugins:**
*   Consider using established Manim plugins if they significantly simplify the implementation or offer visual elements not readily available in core Manim.  If a plugin is used, clearly indicate this in the storyboard with a note like "**Plugin Suggestion:** Consider using the `manim-plugin-name` plugin for [brief explanation of benefit]."

Please generate the scene vision and storyboard plan for the scene in the following format:

<SCENE_VISION_STORYBOARD>
Scene {scene_number}: [Scene Name]

Stage 1: [Stage Name]
- [Visual Element 1 Description]
- [Visual Element 2 Description]
- [Animation Effect Description]
        
Stage 2: [Stage Name]
- [Visual Element 1 Description]
- [Visual Element 2 Description]
- [Animation Effect Description]
        
...

Visual Design Notes:
- [Color Scheme Description]
- [Spatial Layout Description]
- [Animation Rhythm Description]
</SCENE_VISION_STORYBOARD>

**Text Usage Guidelines:**
- **Use `MathTex` *only* for mathematical expressions and equations.**
- **Use `Tex` for all other text, including labels, explanations, and titles.**
- **When mixing text with mathematical symbols in `MathTex`, wrap the text portions in `\\text{{}}`. Example: `MathTex(r"\\text{{Area of circle}} = \\pi r^2")`.**
""" 