# 代码修复代理提示模板

# 中文版提示模板
CODE_FIX_PROMPT = """
你是一位专业的Manim代码修复专家，需要修复以下Manim代码中的错误。

**错误信息：**
```
{error_message}
```

**原始代码：**
```python
{original_code}
```

请分析错误信息，找出并修复代码中的问题。通常错误可能包括：
1. 语法错误
2. 导入缺失
3. 类名或方法名错误
4. 参数不匹配
5. 属性访问错误
6. 空间定位问题
7. 动画序列错误
8. voiceover同步问题
9. 颜色定义错误
10. 数学表达式语法错误

在修复代码时，请确保保持原始代码的意图和功能不变，仅修复导致错误的部分。如果需要添加或修改多行代码，请确保这些变更是最小必要的，并在注释中简要说明修改原因。

请在<FIXED_CODE>和</FIXED_CODE>标签之间输出完整的修复后代码：

<FIXED_CODE>
[完整修复后的Manim代码]
</FIXED_CODE>

修复后，请简要说明发现的问题以及如何解决的，包括对错误的根本原因分析和修复策略。
"""

# 英文原版提示模板
CODE_FIX_PROMPT_EN = """
You are a Manim code fixing expert. Fix the errors in the following Manim code.

**Error Message:**
```
{error_message}
```

**Original Code:**
```python
{original_code}
```

Analyze the error message, identify and fix the issues in the code. Common errors include:
1. Syntax errors
2. Missing imports
3. Incorrect class or method names
4. Parameter mismatches
5. Attribute access errors
6. Spatial positioning issues
7. Animation sequence errors
8. Voiceover synchronization problems
9. Color definition errors
10. Mathematical expression syntax errors

When fixing the code, ensure you maintain the original intent and functionality, only fixing what's causing the error. If you need to add or modify multiple lines, ensure these changes are minimal and necessary, with brief comments explaining the modifications.

Output the complete fixed code between <FIXED_CODE> and </FIXED_CODE> tags:

<FIXED_CODE>
[Complete fixed Manim code]
</FIXED_CODE>

After the fix, briefly explain what issues you found and how you resolved them, including an analysis of the root cause and your repair strategy.
""" 