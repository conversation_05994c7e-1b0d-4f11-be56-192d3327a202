请根据用户提供的原始信息（如脚本、大纲或想法），生成一系列分镜描述，并以 JSON 列表的格式输出。每个 JSON 对象代表一个分镜，必须包含以下字段：

1.  `分镜名` (string): 简洁明了的分镜标题。
2.  `内容要点` (list[string]): 提炼出的本分镜核心信息点或关键词列表。
3.  `分镜内容` (string): 对本分镜场景的详细文字描述，通常作为生成旁白的基础。
4.  `素材名` (string): 指向关联的媒体资源（如视频文件路径 `output/some_video.mp4`、图片路径 `images/logo.png`）或暗示内容类型（如 `雷达图`, `代码对比`, `架构图动画`）。
5.  `视觉动效建议` (list[string]): **[核心规则]** 这是一个字符串列表，按顺序描述本分镜期望的视觉呈现步骤。每一条建议必须遵循以下规则：
    *   a. **编号清晰**: 以 "1. ", "2. ", ... 开始，描述一个独立的视觉动作。
    *   b. **描述意图**: 清晰描述视觉动作的 *意图* (例如: "播放视频", "显示 Markdown 列表", "使用计数器动画", "用雷达图展示数据", "并排比较内容", "时间轴展示事件", "动画展示架构图", "高亮代码第 5 行")。尽量使用接近 Manim DSL 功能的术语。
    *   c. **指定区域**: **必须** 明确指定内容要放置的目标区域 `target_region_id`，且 **只能** 从以下预定义列表中选择：`main`, `full_screen`, `left_half`, `right_half`, `upper_half`, `bottom_half`。例如: "在 `main` 区域 ...", "放置于 `left_half` ...". (注意：某些全屏覆盖动作如 `side_by_side_comparison`, `animate_chart`, `timeline`, `animate_architecture_diagram` 会忽略此设置，但建议仍按意图指定，如 `main`)。 **这是强制要求！**
    *   d. **指定内容来源**: **必须** 明确指出动作所需内容的来源。使用 `@字段名` 引用本文档其他字段 (如 `@分镜名`, `@内容要点`, `@素材名`, `@分镜内容`)，或者直接提供文本或数据。对于需要复杂描述的（如架构图动画），可引用 `@分镜内容` 或单独字段。
    *   e. **包含关键参数和数据**:
        *   **必须** 在括号中提供实现核心意图所必需的关键参数和数据 (例如: 计数器的 `target_value`；图表的 `chart_type` 和 `data`；高亮的 `lines` 或 `elements`；并排比较的 `left_type`/`right_type`；时间轴的 `events` 列表；架构图动画的 `content_description` 或引用；内容显示的 `content_type` 和 `content` 或引用)。
        *   **如果原始需求提到**，也应在括号中包含常见的可选参数或细节信息，使用以下推荐标签作为提示，以更好地反映 DSL 能力：
            *   **显示内容 (文本/代码/Markdown/图像)**: 可包含 `id` (用于后续引用), `animation` (出现动画如 'write', 'fadeIn', 'zoomAndPan'等), `language` (代码语言), `style` (代码风格)。
            *   **计数器动画**: 可包含 `start_value` (起始值), `duration` (动画时长), `label` (标签), `unit` (单位), `effect` (结尾效果如 'flash', 'zoom')。
            *   **图表动画 (条形/折线/雷达图)**: 可包含 `title` (标题), `animation_style` (动画风格如 'grow', 'draw', 'fadeIn'), `options` (如图例 `dataset_names`, 坐标轴范围/标签 `y_range`/`x_range`/`y_label`/`x_label`, 图表宽高 `width`/`height` 等)。
            *   **并排比较**: 可包含 `left_title`/`right_title` (两侧标题), `vs_symbol` (是否显示VS), `transition` (入场动画如 'fadeIn', 'slideUp')。
            *   **时间轴**: 可包含 `duration` (每事件停留时间), `focus_effect` (聚焦效果如 'flash', 'zoom', 'color'), `event_style` (节点形状/颜色), `timeline_color`。
            *   **架构图动画**: (主要通过 `content_description` 控制，无太多独立可选参数)。
            *   **高亮已有元素**: 可包含 `highlight_type` (高亮类型如 'box', 'flash', 'underline'), `color` (高亮颜色), `duration_per_item` (每项持续时间)。
            *   **播放视频**: 可包含 `id` (用于后续引用), `overlay_text` (叠加文字), `overlay_animation_style` (叠加动画如 'sequential', 'simultaneous'), `overlay_animation_delay`。
        *   **示例**: `(target_value: 100, unit: '%', effect: 'flash')`, `(chart_type: 'bar', data: {...}, title: '销售额', options: {'y_label': '万元', 'y_range': [0, 100, 10]})`, `(content_type: 'code', content: '@some_code', id: 'my_code', language: 'python', animation: 'write')`, `(left_type:'code', right_type:'text', left_title:'Code', vs_symbol: true, transition: 'slideUp')`, `(events: [...], duration: 1.5, focus_effect: 'flash')`。
    *   f. **可选建议**: 对于规则 e 中未提及的其他非常见可选参数或纯粹风格化的建议（如特定颜色值、字体名称），可以简单文字描述，而非强制要求结构化标签。

**示例 1 (视频与计数器):**

```json
{
  "分镜名": "介绍视频和数据增长",
  "内容要点": ["关键特性 A", "关键特性 B"],
  "分镜内容": "这是产品的介绍视频，展示了主要功能。同时，用户数量也在快速增长。",
  "素材名": "media/intro.mp4",
  "视觉动效建议": [
    "1. 在 `main` 区域播放视频：@素材名。",
    "2. 将 @内容要点 作为 `overlay_text` 叠加在视频中心，建议使用逐行出现动画。",
    "3. 在 `main` 区域使用曲线图显示用户数，从 0 增长到 5000 (target_value: 5000, label: '用户数')。"
  ]
}
```

**示例 2 (文本与图表):**

```json
{
  "分镜名": "展示核心指标",
  "内容要点": ["指标X: 80%", "指标Y: 65%", "指标Z: 90%"],
  "分镜内容": "接下来我们看一下几个核心指标的表现情况。如图所示，各项指标均达到预期。",
  "素材名": "核心指标条形图",
  "视觉动效建议": [
    "1. 在 `upper_half` 区域显示 Markdown 标题：@分镜名。",
    "2. 在 `bottom_half` 区域使用条形图 (chart_type: 'bar') 展示数据：{'指标X': 80, '指标Y': 65, '指标Z': 90}。设置 Y 轴范围 (options: {'y_range': [0, 100, 20]})。"
  ]
}
```
**示例 3 (代码对比):**
```json
{
  "分镜名": "代码优化前后对比",
  "内容要点": ["优化前性能: 50ms", "优化后性能: 10ms"],
  "分镜内容": "我们来看一下优化前后的代码差异以及性能提升。",
  "素材名": "代码对比",
  "视觉动效建议": [
    "1. 在 `main` 区域并排比较内容 (left_type: 'code', right_type: 'code')。",
    "2. 左侧显示优化前代码 (left_content: '@优化前代码变量', left_title: '优化前 (50ms)')。",
    "3. 右侧显示优化后代码 (right_content: '@优化后代码变量', right_title: '优化后 (10ms)')。",
    "4. 中间显示 VS 符号 (vs_symbol: true)。",
    "5. 建议使用滑入动画 (transition: 'slideUp')。"
  ]
}
```
请严格按照上述规则和 JSON 格式生成分镜描述。输出应仅包含最终的 JSON 列表。
