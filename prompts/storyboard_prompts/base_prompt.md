# 通用基础Prompt模板

根据以下Markdown内容生成Manim DSL分镜脚本。

**Markdown内容**:
{markdown_content}

**重要多媒体素材**：
{important_media_info}

**用户目的**: {purpose}

**内容体裁**: {content_genre}

## 🎯 体裁专用要求（最高优先级，必须严格遵循）
{genre_specific_requirements}

**通用生成要求**:

1. **自适应分镜设计**：
   - 根据内容类型、用户目的、视频时长来自动决定分镜数量
   - 优先使用重要多媒体素材作为分镜核心
   - 减少纯文字分镜，优先多媒体视觉化展示
   - 整体的分镜逻辑要完整，适配purpose，不要跳跃性，有始有终，不要遗漏要点
   - 禁止设计视频背景介绍的分镜，比如介绍目标受众、视频时长，视频目的等内容
   - ⚠️ 注意：体裁专用要求优先级更高，如有冲突以体裁要求为准

2. **重要素材优先原则**：
   - 开篇优先使用最重要的录屏、Demo或架构图
   - 核心技术点使用流程图、架构图等重要图表
   - 每个重要素材只能使用一次
   - 为多媒体分镜提供关键词叠加文字
  
3. **内容描述严格要求**：
   - 必须保留核心认知、关键结论、重要发现
   - 分镜内容要精炼但不能遗漏重点，核心内容要详细介绍清楚
   - narration文案字数限制在200字以内
   - markdown_content文案字数限制在50字以内
   - 开篇分镜的narration提炼整篇的内容，突出核心价值，简练而吸引观众
   - 所有文案描述，必须不要出现"我、我们"等主观的词，要客观
   - 所有文案描述，不要浪费一个字，要言之有物，不用虚的话术，核心内容反而要详细介绍清楚，不能一笔带过
   - **必须**分镜narration表述要逻辑完整，合适的衔接专场话术，不能跳跃性，不能有头无尾，不能遗漏重点

4. **DSL格式要求**：
   - 多媒体分镜使用对应的animate函数
   - 其他分镜可使用animate_markdown、animate_chart等
   - 所有素材路径保持原有格式不修改

5 **扩充素材要求**：
   - 充分使用扩充素材，选择合适的作为分镜，让讲解更丰富，易懂易记住
   - 根据扩充素材内容，选择合适的函数来渲染，比如时间线内容选择animate_timeline,思维导图选择animate_mindmap

6. **素材类型技术要求**：

   **视频素材（.mp4, .avi, .mov, .wmv, .flv, .webm）**：
   - 必须使用animate_video函数
   - 必须提供overlay_text关键词数组，突出视频核心内容
   - overlay_text关键词要与视频内容强相关，是关键信息，数量限制在3-5个
   - narration要与视频节奏同步，描述视频中的关键动作
   - 如果是演示视频，重点突出操作步骤和效果

   **图片素材（.png, .jpg, .jpeg, .svg, .webp）**：
   - 使用animate_image函数
   - 必须提供annotation, 以markdown格式列举关键信息，3-5条，每条不超过12个字，语句通顺、直击本质，每列增加一个emoji
   - 如果是架构图，要逐步解释各个组件和连接关系
   - 如果是流程图，要按照流程顺序进行讲解
   - 如果是对比图，要突出关键差异点
   - narration要引导观众关注图片的重点区域

   **GIF动图（.gif）**：
   - 使用animate_video函数（GIF作为视频处理）
   - 重点突出GIF展示的动态过程
   - overlay_text要捕捉动画的关键帧信息
   - narration要描述动态变化过程
   - 适合展示操作步骤、变化过程、动画效果

**通用严格禁止要求**:
   -**严禁**markdown_content包含章节信息描述，比如2.2，1.1等
   -**严禁**markdown_content文案字数超过50个
   -**严禁**narration文案包含图1、图2等原素材特有的位置描述


**输出格式**：
只返回纯JSON数组，格式如下：
[{{"视觉动效建议": [{{"type": "animate_video", "video_path": "路径", "overlay_text": ["关键词1", "关键词2"], "narration": "讲解内容"}}]}}]

请直接输出JSON，不要包含任何解释说明或代码块标记。 