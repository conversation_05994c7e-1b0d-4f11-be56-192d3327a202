# 通用基础Prompt模板

根据以下Markdown内容生成Manim DSL分镜脚本。

**Markdown内容**:
{markdown_content}

**重要多媒体素材**：
{important_media_info}

**用户目的**: {purpose}

**内容体裁**: {content_genre}

## 🎯 体裁专用要求（最高优先级，必须严格遵循）
{genre_specific_requirements}

## 📋 动画函数标准与参考（选择动画函数时必须严格遵循）
{animation_functions_guide}

**通用生成要求**:

1. **自适应分镜设计**：
   - 优先使用重要*多媒体素材*作为分镜核心，尽量不使用纯文字分镜，禁止连续使用纯文字分镜（如果内容连续则进行合并）
   - 禁止设计视频背景介绍的分镜，比如介绍目标受众、视频时长，视频目的等内容
   - 保持每个分镜的【视觉动效建议】只有1-2个元素

2. **重要素材优先原则**：
   - 开篇优先使用最重要的录屏、Demo或架构图
   - 优先使用核心技术点的流程图、架构图等重要图表
   - 每个重要素材只能使用一次
   - 为多媒体分镜提供关键词叠加文字
  
3. **内容描述严格要求**：
   - animate_markdown里的content文案总字数限制在30个字以内，如果无法简化的内容（如完整例子）则可以保留
   - animate_markdown里的content要提取关键词信息展示，**不要使用长句长段**，**多用核心的短词短句**
   
4. **分镜讲解生成要求**：
   - narration文案字数**必须严格遵守限制在100字以内**
   - narration文案，必须不能出现"我、我们"等主观的词，要客观，要言之有物，不用虚的话术
   - narration文案表述要逻辑完整，合适的衔接专场话术，不能跳跃性，不能有头无尾，不能遗漏重点
   
5. **DSL格式要求**：
   - 多媒体分镜使用对应的animate函数
   - 其他分镜可使用animate_text_only、animate_chart等
   - 所有素材路径保持原有格式不修改

6. **扩充素材要求**：
   - 选择合适的扩充素材作为分镜，让讲解更丰富，易懂易记住，内容不要重复使用
   - 根据扩充素材内容，选择合适的函数来渲染，比如时间线内容选择animate_timeline,思维导图选择animate_mindmap,信息对比选择competitive_analysis

7. **分镜主旨要求**
   - title是整个分镜核心表达的内容，**必须严格遵守不超过8个字**
   - 保证所有分镜的title前后逻辑顺畅，信息表达清晰

8. **素材类型技术要求**：

   **视频素材（.mp4, .avi, .mov, .wmv, .flv, .webm）**：
   - 必须使用animate_video函数
   - 必须提供overlay_text关键词数组，overlay_text关键词要与视频内容强相关，是关键信息，数量限制在3-5个

   **图片素材（.png, .jpg, .jpeg, .svg, .webp）**：
   - 使用animate_image函数
   - 必须提供annotation关键词数组，和内容描述强相关，3-5条，每条不超过6个字，语句通顺、直击本质

   **GIF动图（.gif）**：
   - 使用animate_video函数（GIF作为视频处理）
   - overlay_text要捕捉动画的关键帧信息

   **文本素材（无多媒体素材）**：
   - 无多媒体素材的内容使用animate_text_only函数
   - tags要总结提炼出关键词

   **代码或者伪代码素材**：
   - 使用animate_markdown函数

   **公式素材**:
   - 使用animate_markdown函数

**通用严格禁止要求**:
   -**严禁**animate_markdown里content包含章节信息描述，比如2.2，1.1等
   -**严禁**animate_markdown里content文案字数超过30个，如果无法简化的例子则完整保留
   -**严禁**narration文案包含图1、图2等原素材特有的位置描述，可以改为图中展示了xxx
   -**严禁**丢失分镜，narration讲了第一，后面分镜没了第二，逻辑一定要完整，不能丢失分镜

**通用内容生成流程**:
   - 根据要求先生成所有分镜内容（除了narration文案），保证框架完整，逻辑清晰，满足用户目的。
   - 根据现有的分镜内容再生成连续的narration文案，**必须**保证narration口语化，文案满足以下要求：
      - 所有分镜多用转场词和逻辑词（比如首先，接下来, 第一、第二等）
      - 条理清晰、深入浅出、内容详实，分析到位
      - 让人一看就明白，理解成本低，吸收效率高
   - 最后将连续的narration文案分拆替换各分镜的narrtion构建最终的结果，分拆替换时再次优化文案内容，**必须**保证narration文案满足以下要求
      - 在文案中对分镜多媒体素材内容介绍时，增加“如图/表/架构图中展示了xxx”
      - **一定要**文案口语化、逻辑完整、逻辑连贯性！！所有分镜narration之间要增加衔接话术，包括多媒体分镜和纯文本分镜之间，比如“下面分析下实验效果怎样？如图所示...”

**输出格式**：
只返回纯JSON数组，格式如下：
[{{"视觉动效建议": [{{"type": "animate_video", "video_path": "路径", "overlay_text": ["关键词1", "关键词2"], "narration": "讲解内容","title":"核心内容"}}]}}]

**必须**请直接输出JSON，保证格式满足要求，视觉动效建议层必须保留，不要包含任何解释说明或代码块标记。 