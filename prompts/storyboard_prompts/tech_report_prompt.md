# 技术报告论文体裁专用Prompt

## 🚨 强制执行要求（必须严格遵循）

### 分镜设计强制要求
- **问题导向**: 开篇分镜**必须**明确提出要解决的具体技术问题，narration应突出问题的实际意义和解决的紧迫性，例如"现有系统面临性能瓶颈急需新的解决方案"
- **方案概述**: 第二个分镜**必须**概述技术方案的整体架构和核心思想，给观众建立全局认知
- **实现细节**: **必须**重点设计技术实现过程的分镜，详细展示关键步骤、算法流程或系统架构，确保技术细节清晰可懂
- **经验总结**: 最后分镜**必须**总结实施过程中的关键经验、遇到的问题及解决方法
- **分镜规模**: 分镜注重实用性和可操作性，**强制控制在6-10个之间**

### 内容组织强制要求
- **实用导向**: **必须**突出技术方案的实际应用价值和可操作性
- **详实具体**: **必须**提供充分的技术细节和实现参数，便于他人复现和应用
- **数据支撑**: **必须**用详细的实验数据和性能指标证明方案的有效性
- **问题解决**: **必须**明确展示如何解决具体的技术问题或业务需求

### 语言风格强制要求
- **技术精准**: **必须**使用准确的技术术语和规范的描述方式
- **实操性强**: **必须**注重可执行性，提供具体的实施步骤和参数设置
- **数据详实**: **必须**大量使用量化数据、图表和实验结果支撑论述
- **经验分享**: **必须**分享实际实施过程中的经验教训和最佳实践

### 特色表现形式强制要求
- **流程图示**: **必须**多使用流程图、架构图展示技术实现过程
- **数据可视化**: **必须**通过图表、对比表格展示实验结果和性能数据
- **代码片段**: **必须**适当展示关键算法或实现代码的核心逻辑
- **配置说明**: **必须**详细说明系统配置、参数设置等技术细节

### 🚫 严格禁止内容
- **禁止**过分理论化而缺乏实际应用指导
- **禁止**遗漏关键的技术实现细节和参数配置
- **禁止**缺少充分的实验验证和数据支撑
- **禁止**忽视实际部署中可能遇到的问题和解决方案
- **禁止**使用过于抽象的描述而缺乏具体指导意义
- **禁止**使用无意义的表述，比如“快速了解论文的标题、摘要和基本信息”
- **禁止**分镜数量超过10个或少于6个 