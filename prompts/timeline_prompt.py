from loguru import logger

TIMELINE_PROMPT = """
你是一个AI助手，负责分析输入文本，判断其是否适合用时间轴（timeline）的形式进行可视化展示。如果适合，你将提取相关信息并以JSON格式输出。

<task_definition>
1.  **分析适用性:**
    仔细评估输入文本是否适合制作成引人入胜的时间轴。理想的文本应满足以下所有条件：
    *   **统一核心主题 (Unified Core Theme):** 所有事件节点共同围绕一个清晰、单一的主题或叙事目标（例如，一个产品的演变、一个项目的阶段、一个历史事件的发展）。避免主题分散或不相关的内容。
    *   **线性进展与动态变化 (Linear Progression & Dynamic Change):** 事件或阶段以清晰的时间顺序或逻辑步骤顺序展开，并且能够展现出事物从一个状态到另一个状态的转变、发展或演进过程。不仅仅是静态信息的罗列。
    *   **必要的细节信息 (Necessary Detail Information):** 每个时间点或阶段都包含有意义的关联信息，而不仅仅是一个日期或名称。这些细节应有助于理解该节点的意义和其在整个时间轴中的作用。
    *   **足够的时间节点 (Sufficient Time Nodes):** 至少包含三个或以上不同的时间点或阶段，以构成一个有意义的序列。
    *   **区分于普通列表 (Distinction from Simple Lists):** 确保内容具有内在的发展逻辑或时间演进关系，而非静态的、并列式的信息列表（如按字母排序的清单、无明确演进关系的特性罗列等）。

2.  **提取信息 (如果适用):**
    如果文本适用，为每个事件或阶段提取以下字段：
    *   `year`: (字符串) 事件发生的具体年份、日期、阶段标识或顺序指示 (例如："1950", "第一周", "步骤1", "早期").
    *   `title`: (字符串) 简洁且吸引人的事件标题 (建议5-7个词).
    *   `description`: (字符串) 事件的简短摘要 (建议1-2个短句).
    *   `emoji`: (字符串, 可选) 一个相关的表情符号。若无则省略或用 `null`.
    *   `narration`: (字符串) 对事件的全面解释，用作旁白素材，需信息丰富且适合口述。如果有全局的content_narration，那么事件的narration将被忽略。
</task_definition>

<examples>
**示例 1: 历史事件 (适用)**

输入文本:
\"\"\"
唐朝（618年－907年）是中国历史上一个重要的朝代，被广泛认为是中华文明的黄金时代之一。

**建立与初期（618年 - 712年）**

*   **隋末唐初：** 隋朝末年，天下大乱，民不聊生。太原留守李渊（唐高祖）在儿子李世民（唐太宗）的劝说下，于617年起兵，次年称帝，建立唐朝，定都长安（今西安）。
*   **贞观之治：** 李世民即位后，励精图治，广纳贤才，轻徭薄赋，使得国家迅速从隋末的战乱中恢复过来，社会安定，经济繁荣，史称“贞观之治”。这一时期，唐朝国力强盛，民族关系融洽，疆域辽阔。
*   **武周时期：** 唐高宗李治后期，武则天逐渐掌握实权。690年，武则天称帝，改国号为周，成为中国历史上唯一的女皇帝。她在位期间，继续推行贞观时期的一些政策，打击门阀士族，提拔寒门子弟，社会经济文化得到持续发展。
*   **神龙政变与中宗、睿宗时期：** 705年，张柬之等人发动神龙政变，武则天被迫退位，唐中宗李显复位，恢复唐朝国号。之后，唐朝政局一度动荡，经历了韦后乱政和太平公主干政等事件，直至唐玄宗李隆基即位。

**盛世与转折（712年 - 763年）**

*   **开元盛世：** 唐玄宗李隆基（唐明皇）即位初期，任用贤相姚崇、宋璟等人，改革吏治，发展生产，使得唐朝进入全盛时期，史称“开元盛世”。此时的唐朝，政治清明，经济繁荣，文化昌盛，万邦来朝，长安成为国际性大都市。
*   **安史之乱：** 开元盛世后期，唐玄宗逐渐怠于政事，宠信杨贵妃和奸相李林甫、杨国忠，导致政治腐败，边将权力过大。755年，身兼三镇节度使的安禄山与部将史思明发动叛乱，史称“安史之乱”。这场长达八年的叛乱，使唐朝元气大伤，由盛转衰。

**中晚唐时期（763年 - 907年）**

*   **藩镇割据：** 安史之乱后，为了平定叛乱，唐朝不得不依赖各地节度使。叛乱平定后，这些节度使拥兵自重，形成藩镇割据的局面，中央权力被大大削弱。
*   **宦官专权：** 中晚唐时期，宦官势力恶性膨胀，操纵朝政，甚至可以废立皇帝，与朝臣之间的党争（如牛李党争）也愈演愈烈，进一步加剧了政治的黑暗。
*   **农民起义：** 政治腐败、藩镇割据、赋役繁重，导致民不聊生，农民起义此起彼伏。其中，黄巢起义（875年-884年）规模最大，席卷全国，沉重打击了唐朝的统治。
*   **灭亡：** 黄巢起义后，唐朝名存实亡。907年，梁王朱温（原为黄巢部将，后降唐）废黜唐哀帝，自立为帝，建立后梁，唐朝正式灭亡，中国历史进入五代十国时期。

**唐朝的特点与成就**

*   **政治制度：** 完善了三省六部制，推行科举制度，为后世沿用。
*   **经济繁荣：** 农业、手工业和商业都得到长足发展，出现了许多繁华的商业城市。均田制和租庸调制保证了国家财政收入和农业生产。
*   **文化昌盛：**
    *   **诗歌：** 唐诗是中国文学史上的瑰宝，名家辈出，如李白、杜甫、白居易等，作品流传千古。
    *   **艺术：** 绘画（如阎立本、吴道子）、书法（如颜真卿、柳公权）、音乐、舞蹈等都取得了很高的成就。敦煌莫高窟是唐代艺术的宝库。
    *   **科技：** 雕版印刷术在唐朝得到广泛应用，火药开始应用于军事。
*   **对外开放与民族融合：** 唐朝实行开放的对外政策，与亚洲乃至欧洲、非洲的许多国家都有经济文化往来，长安是当时世界上最大的国际都市之一。通过丝绸之路，中外文化交流频繁。唐朝对周边各民族也采取了较为开明的政策，促进了民族融合。
*   **宗教发展：** 佛教在唐朝达到鼎盛，同时也存在道教和景教、摩尼教等外来宗教。

总的来说，唐朝是中国历史上一个辉煌灿烂的时代，其强大的国力、开放包容的文化、繁荣的经济以及对后世深远的影响，都使其在中国乃至世界历史上占据着重要的地位。
\"\"\"

输出JSON:
```json
{{
  "suitable": true,
  "reason": "文本详细描述了唐朝从建立到灭亡的三个主要发展阶段，每个阶段内包含多个具有明确时间顺序和显著特征的事件，且信息量充足，非常适合生成时间轴以展现朝代兴衰。由于事件较多，适用整体的content_narration来介绍唐朝的背景信息，事件的narration将被忽略。",
  "events": [
    {{
      "year": "618年",
      "title": "唐朝建立",
      "description": "李渊称帝，定都长安，唐朝开国。",
      "emoji": "🇨🇳"
    }},
    {{
      "year": "626年起",
      "title": "贞观之治",
      "description": "唐太宗励精图治，天下安定繁荣。",
      "emoji": "👑"
    }},
    {{
      "year": "690年",
      "title": "武则天称帝",
      "description": "武周代唐，中国唯一女皇统治。",
      "emoji": "👸"
    }},
    {{
      "year": "705年",
      "title": "神龙政变",
      "description": "武则天退位，唐朝国号复立。",
      "emoji": "⚡"
    }},
    {{
      "year": "712年起",
      "title": "开元盛世",
      "description": "唐玄宗执政初期，唐朝达鼎盛。",
      "emoji": " prosper"
    }},
    {{
      "year": "755年",
      "title": "安史之乱爆发",
      "description": "唐朝由盛转衰的转折点。",
      "emoji": "⚔️"
    }},
    {{
      "year": "763年起",
      "title": "藩镇割据与宦官专权",
      "description": "中晚唐弊病，中央权力衰弱。",
      "emoji": "📉"
    }},
    {{
      "year": "875年起",
      "title": "黄巢起义",
      "description": "规模最大的农民起义，重创唐朝。",
      "emoji": "🔥"
    }},
    {{
      "year": "907年",
      "title": "唐朝灭亡",
      "description": "朱温废唐哀帝，建立后梁。",
      "emoji": "🔚"
    }}
  ],
  "intro_narration": "以下将通过几个关键事件，为您呈现唐朝的兴盛与变迁。",
  "content_narration": "唐朝的历史是一部波澜壮阔的史诗，从初期的建立与贞观之治的繁盛，到开元盛世的顶峰，再到安史之乱后的衰落与最终的灭亡。尽管历经坎坷，唐朝仍以其开放包容的文化、辉煌灿烂的文明成就，在中国乃至世界历史上留下了浓墨重彩的一笔，影响深远。"
}}
```

**示例 2: 项目计划 (适用)**

输入文本:
\"\"\"
我们的新产品开发分为三个主要阶段。第一阶段是市场调研与需求分析，预计耗时一个月，从下周一开始。第二阶段是核心功能开发与原型测试，计划投入两个月时间。最后是产品打磨、公测及正式上线，预计在第四个月完成。
\"\"\"

输出JSON:
```json
{{
  "suitable": true,
  "reason": "文本详细描述了新产品开发的三个阶段，具有清晰的逻辑顺序和阶段性目标，适合生成时间轴。",
  "events": [
    {{
      "year": "阶段一 (首月)",
      "title": "市场调研与需求分析",
      "description": "明确产品定位与用户需求。",
      "emoji": "🔍",
      "narration": "项目的第一阶段将聚焦于深入的市场调研和精确的用户需求分析。我们将通过问卷、访谈等多种方式收集信息，确保产品方向与市场趋势和用户期望高度一致。此阶段预计从下周一开始，持续一个月。"
    }},
    {{
      "year": "阶段二 (接下来两个月)",
      "title": "核心开发与原型测试",
      "description": "完成产品核心功能并进行初步验证。",
      "emoji": "💻",
      "narration": "在第二阶段，开发团队将全力投入核心功能的研发，并制作出可交互的产品原型。我们将进行内部测试和部分用户测试，收集反馈，快速迭代，确保产品核心体验的流畅与稳定。此阶段计划为期两个月。"
    }},
    {{
      "year": "阶段三 (第四个月)",
      "title": "产品打磨与上线",
      "description": "完善产品细节，公测并正式发布。",
      "emoji": "🎉",
      "narration": "最后一个阶段是产品的精细打磨、更大范围的公开测试以及最终的正式上线。我们将修复已知问题，优化用户体验，并制定详细的上线计划，确保产品顺利推向市场。此阶段预计在项目的第四个月完成。"
    }}
  ],
  "intro_narration": "从市场调研到最终上线，我们的产品开发流程分为三个清晰阶段，确保高质量交付。",
  "outro_narration": "通过这三个精心设计的阶段，我们将把创意转化为满足用户需求的成熟产品。"
}}
```

**示例 3: 技术演进 (适用)**

输入文本:
\"\"\"
人工智能的发展经历了几个关键浪潮。早期是符号主义的探索，大约在20世纪50-70年代。接着是连接主义的兴起，特别是神经网络在80-90年代的复苏。进入21世纪后，深度学习带来了革命性突破，至今仍在快速发展。
\"\"\"

输出JSON:
```json
{{
  "suitable": true,
  "reason": "文本清晰地勾勒了人工智能发展的三个关键浪潮，具有明确的时间分期和技术特点，适合生成时间轴。",
  "events": [
    {{
      "year": "20世纪50-70年代",
      "title": "早期探索：符号主义",
      "description": "AI的起源，基于逻辑和符号推理。",
      "emoji": "🧠",
      "narration": "人工智能的早期发展，大约在20世纪50至70年代，主要由符号主义主导。这一时期的研究者们相信，智能行为可以通过操作符号和应用逻辑规则来实现，代表性成果有逻辑理论家和通用问题求解器等。"
    }},
    {{
      "year": "20世纪80-90年代",
      "title": "连接主义复兴",
      "description": "神经网络研究复苏，关注学习和模式识别。",
      "emoji": "🔗",
      "narration": "到了20世纪80至90年代，连接主义，特别是人工神经网络的研究得到复兴。研究重点转向了从数据中学习和模式识别，反向传播算法等关键技术的出现推动了这一领域的发展，为后来的深度学习奠定了基础。"
    }},
    {{
      "year": "21世纪至今",
      "title": "深度学习革命",
      "description": "大数据和计算力驱动的AI突破性进展。",
      "emoji": "💡",
      "narration": "进入21世纪，随着大数据时代的到来和计算能力的显著提升，深度学习技术取得了革命性的突破。多层神经网络在图像识别、自然语言处理等多个领域展现出超越传统方法的性能，引领了当前人工智能发展的新浪潮。"
    }}
  ],
  "intro_narration": "人工智能技术从萌芽到繁荣，经历了几个关键的技术浪潮与突破。",
  "outro_narration": "从符号主义到深度学习，人工智能技术的发展见证了计算能力和算法理论的共同进步，未来将继续推动智能化时代的演进。"
}}
```

**示例 4: 不适用 (细节不足)**

输入文本:
\"\"\"
昨天天气很好。
\"\"\"

输出JSON:
```json
{{
  "suitable": false,
  "reason": "文本仅描述单一简单情景，缺乏多个时间节点或阶段性进展，不构成时间轴。"
}}
```

**示例 5: 不适用 (非线性/无关联事件)**

输入文本:
\"\"\"
我喜欢苹果。他喜欢香蕉。AI可以写代码。
\"\"\"

输出JSON:
```json
{{
  "suitable": false,
  "reason": "文本包含多个独立陈述，它们之间没有明显的时间顺序或逻辑关联，不适合用时间轴展示。"
}}
```
</examples>

<output_format_specification>
**输出格式要求:**

*   无论输入文本是否适合制作时间轴，都必须返回一个 **JSON对象**。
*   该JSON对象必须包含一个布尔类型的 `suitable` 字段。
*   该JSON对象必须包含一个字符串类型的 `reason` 字段，用中文解释判断为适合或不适合的原因。
    *   如果 `suitable` 为 `true`，`reason` 应简要说明为何文本适合（例如：“文本内容清晰地描述了多个具有时间先后顺序的事件，且细节充分，适合生成时间轴。”）。
    *   如果 `suitable` 为 `false`，`reason` 应清晰解释不适用的具体原因。
*   如果 `suitable` 为 `true`，JSON对象还必须包含以下字段：
    *   一个 `events` 字段。`events` 的值是一个 **JSON数组**，数组中的每个对象代表一个时间轴事件，并包含 `year`, `title`, `description`, `emoji` (可选), 和 `narration` 字段，其中`narration`字段为可选，尽量简洁。如果设置了全局的`content_narration`，则事件的`narration`字段将被忽略。`events` 的数量不能太多，尽量不超过10个。
    *   一个 `intro_narration` 字段。`intro_narration` 的值是一个 **字符串**，用于时间轴开始时的信息介绍，如果无需介绍则为空。需要尽量简洁，因为此时屏幕上只有标题，时间太长会导致用户流失。不超过30字。
    *   一个 `outro_narration` 字段。`outro_narration` 的值是一个 **字符串**，用于时间轴结束时的信息介绍，如果无需介绍则为空。
    *   一个 `content_narration` 字段。`content_narration` 的值是一个 **字符串**，用于时间轴事件的旁白介绍。适用场景：
        1. 事件较多（一般超过5个就算比较多），每个事件如果单独配有`narration`会使视频变得冗长。
        2. 每个事件的`narration`内容相似，可以合并为一个`content_narration`。
        3. 每个事件的内容太简单，不足以单独配有`narration`。
*   如果 `suitable` 为 `false`，JSON对象不应包含 `events`、`intro_narration`、`outro_narration` 和 `content_narration` 字段。
*   确保整体JSON格式严格正确。

**适用情况下的输出示例:**
```json
{{
  "suitable": true,
  "reason": "文本清晰地描述了[某事物]的多个发展阶段，具有明确的时间顺序和关键事件，适合生成时间轴。",
  "events": [
    {{
      "year": "时间点1",
      "title": "事件标题1",
      "description": "事件描述1。",
      "emoji": "👍",
      "narration": "关于事件1的详细介绍，用于旁白。"
    }},
    {{
      "year": "时间点2",
      "title": "事件标题2",
      "description": "事件描述2。",
      "emoji": "🎉",
      "narration": "关于事件2的详细介绍，用于旁白。"
    }}
    // ... 更多事件
  ]
}}
```

**不适用情况下的输出示例:**
```json
{{
  "suitable": false,
  "reason": "文本[具体原因，例如：缺乏清晰的时间顺序/细节不足/少于三个时间点/主题不连贯等]。"
}}
```
</output_format_specification>

请根据以上定义、示例和输出格式要求，分析以下用户提供的文本，并以focus为指导，生成时间轴。

<input_text_to_analyze>
{input_text_to_analyze}
</input_text_to_analyze>

<focus>
{focus}
</focus>

输出:
"""


def generate_timeline(input_text_to_analyze: str, focus: str = ""):
    from camel.agents import ChatAgent
    from camel.messages import BaseMessage

    from utils.create_llm_model import create_model
    from utils.format import extract_json

    model = create_model()
    prompt = TIMELINE_PROMPT.format(input_text_to_analyze=input_text_to_analyze, focus=focus)
    logger.info(f"生成时间轴，prompt长度: {len(prompt)}, words: {len(prompt.split())}")
    system_message = BaseMessage.make_assistant_message(
        role_name="Timeline Generator",
        content="You are an expert in timeline analysis and generation. You are given a text and you need to analyze it and generate a timeline.",
    )
    agent = ChatAgent(system_message=system_message, model=model)
    response = agent.step(prompt)

    # Ensure response object exists
    if not response or not hasattr(response, "msgs") or not response.msgs:
        return None

    response_content = response.msgs[0].content

    # Try to parse JSON
    json_data = extract_json(response_content)
    if not json_data:
        return None
    return json_data


if __name__ == "__main__":
    import json
    import sys

    json_data = generate_timeline(open(sys.argv[1]).read())
    print(json.dumps(json_data, indent=2, ensure_ascii=False))
