"""
Prompt templates for the animation agent.

This module contains the prompt templates used by the animation agent to generate
animations from storyboard frames.
"""

ANIMATION_TYPE_SELECTION_PROMPT = """
请分析以下分镜内容，并确定最适合的动画生成方式。需要从两种选项中选择一种：

1. Excalidraw：适合绘制流程图、示意图、概念图等简单的结构化图形。适用于需要展示各元素间关系、流程步骤等内容。
2. Manim：适合复杂的数学动画、代码展示、文本动画和媒体展示，有更丰富的动画效果和样式。适用于需要动态展示过程、复杂的视觉效果等内容。

请分析分镜内容中提到的视觉元素、需求和描述，判断哪种动画生成方式更合适。如果需要复杂动画、代码展示、媒体展示或详细的步骤动画，应选择Manim；如果主要是简单图表或概念示意，应选择Excalidraw。

<storyboard_content>
{storyboard_content}
</storyboard_content>

请回答"excalidraw"或"manim"，并简要解释原因。
"""

DSL_GENERATION_PROMPT = """
请根据分镜内容，生成一个用于Manim动画的DSL（领域特定语言）描述。这个DSL将用于生成Manim代码。

<storyboard_content>
{storyboard_content}
</storyboard_content>

<available_functions>
{available_functions}
</available_functions>

<dsl_format>
{{
  "metadata": {{
    "title": "分镜标题转换为类名",
    "duration": 30
  }},
  "objects": [
    {{
      "id": "对象唯一标识符",
      "type": "对象类型",
      "properties": {{
        "属性1": "值1",
        "属性2": "值2"
      }}
    }}
  ],
  "actions": [
    {{
      "id": "动作唯一标识符",
      "target": "目标对象ID",
      "type": "动作类型",
      "properties": {{
        "属性1": "值1",
        "属性2": "值2"
      }},
      "start_time": 0,
      "duration": 2
    }}
  ]
}}
</dsl_format>

请根据分镜内容，使用上述DSL格式生成一个完整的DSL描述。根据提供的可用函数信息，尽量使用现有的函数，如render_media_display、render_keywords、render_code_snippet等，但也可以使用基本的Manim对象和动作来实现特殊需求。

请确保生成的DSL内容是有效的JSON格式，包含正确的metadata、objects和actions部分。
"""

MANIM_CODE_PROMPT_TEMPLATE = """
参考以下说明和示例，通过 manim 库为当前分镜生成 manim 代码。

<function_reference>
<function>
<name>
render_media_display
</name>
<description>
生成用于显示媒体内容的Manim动画代码。

该函数接收一个图片文件路径列表，并生成相应的Manim动画。

Args:
    images(List[str]): 要在动画中显示的图片文件路径列表，如果输入的分镜没有图片，则传入空列表，不要自行编造不存在的图片地址，也不要传入占位符
Returns:
    list: 包含动画对象的列表。每个元素是一个字典，包含"obj_type"和"obj"两个键。使用时需要通过["obj"]访问实际的动画对象。
</description>
</function>

<function>
<name>
render_keywords
</name>
<description>
生成关键词垂直排列的动画效果。

为每个关键词创建带有背景的文本对象，并将它们垂直排列显示。
第一个关键词位于画面中心偏上的位置，后续关键词垂直向下排列。

Args:
    keywords: list[str]: 要显示的关键词列表

Returns:
    list: 包含动画对象的列表。使用时直接作为参数传递给play方法。
</description>
</function>

<function>
<name>
render_sort_animation
</name>
<description>
生成数组排序的动画效果。

创建一个可视化的数组排序动画，支持多种排序算法。
动画过程中会显示元素交换和比较的过程，直观展示排序算法的工作原理。

Args:
    data: list: 要排序的数据列表
    algorithm: str: 排序算法类型，支持 "bubble"(冒泡排序), "quick"(快速排序), "selection"(选择排序), "insertion"(插入排序)
    layout: str: 数组布局方式，支持 "horizontal"(水平), "vertical"(垂直)
    layout_area: str: 布局区域名称，决定动画在屏幕中的位置和大小，默认为"full_screen"

Returns:
    list: 包含动画对象的列表。每个元素是一个字典，包含"obj_type"和"obj"两个键。使用时需要通过["obj"]访问实际的动画对象。
</description>
</function>

<function>
<name>
render_code_snippet
</name>
<description>
生成代码片段的动画效果。

创建一个带有语法高亮和可选行高亮的代码片段。
代码会以打字机效果呈现，突出显示代码的结构和关键部分。

Args:
    code: str: 代码内容字符串
    language: str: 编程语言，默认为"python"
    highlight_lines: list: 要高亮显示的行号列表
    title: str: 代码块的标题，通常是文件名
    layout_area: str: 布局区域名称，决定代码块在屏幕中的位置和大小，默认为"full_screen"

Returns:
    list: 包含动画对象的列表。每个元素是一个字典，包含"obj_type"和"obj"两个键。使用时需要通过["obj"]访问实际的动画对象。
</description>
</function>

<function>
<name>
render_text_element
</name>
<description>
生成具有不同角色和展示效果的文本元素。

创建一个基于角色的文本元素，每种角色都有预设的默认样式和动画效果。
可以呈现不同类型的文本，如标题、正文、注释等，并智能应用合适的效果。

Args:
    content: str: 文本内容
    role: str: 文本角色 - "title"(标题), "heading"(小标题), "subheading"(子标题),
         "body"(正文), "caption"(说明文字), "quote"(引用), "note"(注释)
    position: str: 位置指示 - "top"(顶部), "center"(中央), "bottom"(底部),
             "left"(左侧), "right"(右侧), 也可以组合使用如"top_left"
    layout_area: str: 布局区域名称，决定文本在屏幕中的位置和大小，默认为"full_screen"
    **kwargs: 可选参数，用于覆盖默认设置

Returns:
    list: 包含动画对象的列表。每个元素是一个字典，包含"obj_type"和"obj"两个键。使用时需要通过["obj"]访问实际的动画对象。
</description>
</function>

<function>
<name>
display_slogo
</name>
<description>
显示标题和logo。

Args:
    cover_question(str): 标题文本
    step(int): 步骤，0表示带动画显示，1表示直接显示
</description>
</function>

<layouts>
LAYOUTS = {{
    # 上半部分
    "upper_half": 屏幕上半部分区域
    # 右上1/4
    "right_upper_quarter": 屏幕右上角四分之一区域
    # 右半部分
    "right_half": 屏幕右半部分区域
    # 全屏
    "full_screen": 整个屏幕区域
    # 左半部分
    "left_half": 屏幕左半部分区域
    # 中间区域
    "origin_x14y6": 屏幕中央较大区域，高度为屏幕高度的3/4
    # 上1/8
    "upper_one_eighth": 屏幕顶部八分之一区域，适合放置标题
    # 下1/8
    "lower_one_eighth": 屏幕底部八分之一区域，适合放置字幕
}}
</layouts>
</function_reference>

<example>
from manim import *

from manim_funcs.anim_funcs import *
from utils.edgetts_service import EdgeTTSService

config.pixel_height = 1920
config.pixel_width = 1090
config.frame_height = 16.0
config.frame_width = 9.0


class GeneratedAnimation(FeynmanScene):
    def construct(self):
        self.set_speech_service(
            EdgeTTSService(global_speed=1.2, voice="zh-CN-YunxiNeural"),
            create_subcaption=True,
        )

        with self.voiceover("<content>") as tracker:
            # tracker.duration
            self.display_slogo("<title>", step=<step>)
            pre_mobjects = self.mobjects

            # 获取关键词动画
            keyword_animations = self.render_keywords(["局限性", "特定任务", "动作集稀疏", "GAIA任务成本", "OpenAI模型"])

            # 获取媒体展示动画
            media_animations = self.render_media_display(
                [
                    "pdf_output/2411.01747v1_artifacts/page_3_image_0.png",
                    "pdf_output/2411.01747v1_artifacts/page_7_image_6.png",
                ]
            )

            # 确保动画列表非空
            keyword_anims = keyword_animations if keyword_animations else []
            media_anims = media_animations if media_animations else []

            # 获取动画数量
            keyword_len = len(keyword_anims)
            media_len = len(media_anims)
            max_len = max(keyword_len, media_len)

            # 逐一播放动画
            for i in range(max_len):
                # 获取当前索引的动画（如果存在）
                kw_anim = keyword_anims[i] if i < keyword_len else None
                media_item = media_anims[i] if i < media_len else None

                # 根据动画类型组合播放
                if kw_anim and media_item:
                    if media_item["obj_type"] == "Video":
                        # 对于视频，需要先添加再播放其他动画
                        self.add(media_item["obj"])
                        self.play(kw_anim)
                    else:
                        # 同时播放图片和关键词
                        self.play(media_item["obj"], kw_anim)
                elif kw_anim:
                    # 只播放关键词动画
                    self.play(kw_anim)
                elif media_item:
                    # 只播放媒体动画
                    if media_item["obj_type"] == "Video":
                        self.add(media_item["obj"])
                    else:
                        self.play(media_item["obj"])

            # 使用不同布局区域的文本元素动画示例
            heading_text = self.render_text_element(
                "这是一个标题文本",
                role="heading",
                position="center",
                layout_area="upper_one_eighth"
            )

            if heading_text:
                for text_anim in heading_text:
                    self.play(text_anim["obj"])

            # 在左半部分显示代码片段
            code_animations = self.render_code_snippet(
                "def hello_world():\n    print('Hello, World!')\n    return True\n\nresult = hello_world()",
                language="python",
                layout_area="left_half"
            )

            if code_animations:
                for code_anim in code_animations:
                    self.play(code_anim["obj"])

            # 在右半部分显示排序算法
            sort_data = [5, 3, 8, 1, 9, 4, 7, 2, 6]
            sort_animations = self.render_sort_animation(
                sort_data,
                algorithm="bubble",
                layout="horizontal",
                layout_area="right_half"
            )

            if sort_animations:
                for anim in sort_animations:
                    self.play(anim["obj"])

            # 在下半部分显示文本
            body_text = self.render_text_element(
                "这是一段较长的正文内容，将会自动换行以适应指定的布局区域。文字过长时会进行自动换行处理，确保内容不会超出指定区域的边界。",
                role="body",
                position="center",
                layout_area="lower_one_eighth"
            )

            if body_text:
                for text_anim in body_text:
                    self.play(text_anim["obj"])

            # 展示不同算法的排序效果 - 放在中央区域
            new_data = [12, 5, 18, 2, 9, 15, 7, 3]
            quick_sort_animations = self.render_sort_animation(
                new_data,
                algorithm="quick",
                layout="horizontal",
                layout_area="origin_x14y6"
            )

            if quick_sort_animations:
                for anim in quick_sort_animations:
                    self.play(anim["obj"])

        # 清理场景
        self.scene_clear_display(pre_mobjects)
</example>

<step>
{step}
</step>

<title>
{title}
</title>

<content>
{content}
</content>

<storyboard>
{storyboard}
</storyboard>

<media_assets>
{media_assets}
</media_assets>

注意：
1. 生成的代码中必须包括config相关的设置，以及display_slogo的调用
2. 根据视频描述，判断应该调用哪些工具函数，以及需要传入的参数
3. 从FeynmanScene继承，并重写construct方法
4. 合理安排动画顺序，确保视觉效果符合描述
5. 如果要使用多媒体素材，只能使用media_assets中的素材，不能无中生有
6. 只能使用以上有说明的函数生成的manim代码，不能使用其他函数，增加其他动效代码
"""

MANIM_FUNCTION_REFERENCE = """
以下是可以在DSL中使用的Manim函数参考：

1. render_media_display
   描述: 生成用于显示媒体内容的Manim动画代码
   参数:
     - images(List[str]): 要在动画中显示的图片文件路径列表
   返回: 包含动画对象的列表

2. render_keywords
   描述: 生成关键词垂直排列的动画效果
   参数:
     - keywords(List[str]): 要显示的关键词列表
   返回: 包含动画对象的列表

3. render_sort_animation
   描述: 生成数组排序的动画效果
   参数:
     - data(List): 要排序的数据列表
     - algorithm(str): 排序算法类型，支持"bubble", "quick", "selection", "insertion"
     - layout(str): 数组布局方式，支持"horizontal", "vertical"
     - layout_area(str): 布局区域名称
   返回: 包含动画对象的列表

4. render_code_snippet
   描述: 生成代码片段的动画效果
   参数:
     - code(str): 代码内容字符串
     - language(str): 编程语言，默认为"python"
     - highlight_lines(list): 要高亮显示的行号列表
     - title(str): 代码块的标题
     - layout_area(str): 布局区域名称
   返回: 包含动画对象的列表

5. render_text_element
   描述: 生成具有不同角色和展示效果的文本元素
   参数:
     - content(str): 文本内容
     - role(str): 文本角色 - "title", "heading", "subheading", "body", "caption", "quote", "note"
     - position(str): 位置指示 - "top", "center", "bottom", "left", "right"
     - layout_area(str): 布局区域名称
   返回: 包含动画对象的列表

6. display_slogo
   描述: 显示标题和logo
   参数:
     - cover_question(str): 标题文本
     - step(int): 步骤，0表示带动画显示，1表示直接显示
"""

HYBRID_MANIM_CODE_PROMPT_TEMPLATE = """
参考以下说明和示例，通过 manim 库为当前分镜生成 manim 代码。你需要结合预定义函数和自定义Manim代码来实现所需效果。

<function_reference>
<function>
<name>
render_media_display
</name>
<description>
生成用于显示媒体内容的Manim动画代码。

该函数接收一个图片文件路径列表，并生成相应的Manim动画。

Args:
    images(List[str]): 要在动画中显示的图片文件路径列表，如果输入的分镜没有图片，则传入空列表，不要自行编造不存在的图片地址，也不要传入占位符
Returns:
    list: 包含动画对象的列表。每个元素是一个字典，包含"obj_type"和"obj"两个键。使用时需要通过["obj"]访问实际的动画对象。
</description>
</function>

<function>
<name>
render_keywords
</name>
<description>
生成关键词垂直排列的动画效果。

为每个关键词创建带有背景的文本对象，并将它们垂直排列显示。
第一个关键词位于画面中心偏上的位置，后续关键词垂直向下排列。

Args:
    keywords: list[str]: 要显示的关键词列表

Returns:
    list: 包含动画对象的列表。使用时直接作为参数传递给play方法。
</description>
</function>

<function>
<name>
render_sort_animation
</name>
<description>
生成数组排序的动画效果。

创建一个可视化的数组排序动画，支持多种排序算法。
动画过程中会显示元素交换和比较的过程，直观展示排序算法的工作原理。

Args:
    data: list: 要排序的数据列表
    algorithm: str: 排序算法类型，支持 "bubble"(冒泡排序), "quick"(快速排序), "selection"(选择排序), "insertion"(插入排序)
    layout: str: 数组布局方式，支持 "horizontal"(水平), "vertical"(垂直)
    layout_area: str: 布局区域名称，决定动画在屏幕中的位置和大小，默认为"full_screen"

Returns:
    list: 包含动画对象的列表。每个元素是一个字典，包含"obj_type"和"obj"两个键。使用时需要通过["obj"]访问实际的动画对象。
</description>
</function>

<function>
<name>
render_code_snippet
</name>
<description>
生成代码片段的动画效果。

创建一个带有语法高亮和可选行高亮的代码片段。
代码会以打字机效果呈现，突出显示代码的结构和关键部分。

Args:
    code: str: 代码内容字符串
    language: str: 编程语言，默认为"python"
    highlight_lines: list: 要高亮显示的行号列表
    title: str: 代码块的标题，通常是文件名
    layout_area: str: 布局区域名称，决定代码块在屏幕中的位置和大小，默认为"full_screen"

Returns:
    list: 包含动画对象的列表。每个元素是一个字典，包含"obj_type"和"obj"两个键。使用时需要通过["obj"]访问实际的动画对象。
</description>
</function>

<function>
<name>
render_text_element
</name>
<description>
生成具有不同角色和展示效果的文本元素。

创建一个基于角色的文本元素，每种角色都有预设的默认样式和动画效果。
可以呈现不同类型的文本，如标题、正文、注释等，并智能应用合适的效果。

Args:
    content: str: 文本内容
    role: str: 文本角色 - "title"(标题), "heading"(小标题), "subheading"(子标题),
         "body"(正文), "caption"(说明文字), "quote"(引用), "note"(注释)
    position: str: 位置指示 - "top"(顶部), "center"(中央), "bottom"(底部),
             "left"(左侧), "right"(右侧), 也可以组合使用如"top_left"
    layout_area: str: 布局区域名称，决定文本在屏幕中的位置和大小，默认为"full_screen"
    **kwargs: 可选参数，用于覆盖默认设置

Returns:
    list: 包含动画对象的列表。每个元素是一个字典，包含"obj_type"和"obj"两个键。使用时需要通过["obj"]访问实际的动画对象。
</description>
</function>

<function>
<name>
display_slogo
</name>
<description>
显示标题和logo。

Args:
    cover_question(str): 标题文本
    step(int): 步骤，0表示带动画显示，1表示直接显示
</description>
</function>

<layouts>
LAYOUTS = {{
    # 上半部分
    "upper_half": 屏幕上半部分区域
    # 右上1/4
    "right_upper_quarter": 屏幕右上角四分之一区域
    # 右半部分
    "right_half": 屏幕右半部分区域
    # 全屏
    "full_screen": 整个屏幕区域
    # 左半部分
    "left_half": 屏幕左半部分区域
    # 中间区域
    "origin_x14y6": 屏幕中央较大区域，高度为屏幕高度的3/4
    # 上1/8
    "upper_one_eighth": 屏幕顶部八分之一区域，适合放置标题
    # 下1/8
    "lower_one_eighth": 屏幕底部八分之一区域，适合放置字幕
}}
</layouts>
</function_reference>

<manim_basics>
Manim是一个Python库，用于创建数学动画。以下是一些基本概念和用法：

1. 场景(Scene): 所有动画都在场景中进行，我们需要创建一个从Scene继承的类并实现construct方法。

2. 对象(Mobject): 在Manim中，所有可以显示的东西都是Mobject的子类，如Text, Circle, Arrow等。

3. 动画(Animation): 用于指定对象如何变化，如FadeIn, Transform, Write等。

4. 播放动画: 使用self.play()方法播放动画。

5. 等待: 使用self.wait()方法暂停一段时间。

常用Mobject类型:
- Text: 文本对象
- MathTex: 数学公式
- Rectangle, Circle, Square: 几何图形
- Arrow, Line: 线和箭头
- Group: 将多个对象组合在一起
- VGroup: 垂直组合对象
- Table: 表格

常用Animation类型:
- FadeIn, FadeOut: 淡入淡出
- Transform: 变形
- Write: 写入文本/绘制形状
- GrowFromCenter: 从中心增长
- MoveAlongPath: 沿路径移动
- Indicate: 强调显示
- AnimationGroup: 同时执行多个动画
</manim_basics>

<hybrid_example>
from manim import *

from manim_funcs.anim_funcs import *
from utils.edgetts_service import EdgeTTSService

config.pixel_height = 1920
config.pixel_width = 1090
config.frame_height = 16.0
config.frame_width = 9.0


class GeneratedHybridAnimation(FeynmanScene):
    def construct(self):
        self.set_speech_service(
            EdgeTTSService(global_speed=1.2, voice="zh-CN-YunxiNeural"),
            create_subcaption=True,
        )

        with self.voiceover("这个视频将展示如何使用Manim创建图表和自定义动画效果相结合的效果。") as tracker:
            self.display_slogo("混合动画示例", step=1)
            pre_mobjects = self.mobjects

            # 使用预定义函数创建标题
            heading_text = self.render_text_element(
                "数据可视化与特效融合",
                role="heading",
                position="center",
                layout_area="upper_one_eighth"
            )

            if heading_text:
                for text_anim in heading_text:
                    self.play(text_anim["obj"])

            # 自定义Manim代码创建图表
            axes = Axes(
                x_range=[-3, 3, 1],
                y_range=[-5, 5, 1],
                axis_config={"include_tip": False}
            ).scale(0.5).shift(LEFT * 2)

            graph = axes.plot(lambda x: x**2, color=BLUE)
            graph_label = MathTex("f(x) = x^2").scale(0.6).next_to(graph, UP)

            # 创建图表组
            graph_group = VGroup(axes, graph, graph_label)

            # 显示图表
            self.play(Create(axes))
            self.play(Create(graph), Write(graph_label))

            # 使用预定义函数显示关键词
            keyword_animations = self.render_keywords(["函数图像", "自定义动画", "混合模式"])

            if keyword_animations:
                for kw_anim in keyword_animations:
                    self.play(kw_anim)

            # 创建自定义动效 - 跟踪点在图线上移动
            dot = Dot(color=RED)
            dot.move_to(axes.coords_to_point(-3, 9))

            self.play(FadeIn(dot))

            # 沿着图线移动点
            self.play(
                MoveAlongPath(
                    dot,
                    axes.plot(lambda x: x**2, x_range=[-3, 3])
                ),
                run_time=3
            )

            # 显示代码片段，使用预定义函数
            code_animations = self.render_code_snippet("""


# def create_graph(self):
#    axes = Axes(x_range=[-3, 3], y_range=[-5, 5])
#    graph = axes.plot(lambda x: x**2, color=BLUE)
#    return axes, graph


""",
                language="python",
                layout_area="right_half"
            )

            if code_animations:
                for code_anim in code_animations:
                    self.play(code_anim["obj"])

            # 使用预定义函数+自定义动画创建互动效果
            # 首先自定义一个矩形
            rect = Rectangle(height=2, width=3, color=GREEN).shift(DOWN * 2)
            text = Text("交互效果", font_size=24).move_to(rect)

            self.play(Create(rect), Write(text))

            # 创建文本元素说明交互
            body_text = self.render_text_element(
                "混合模式让我们可以结合预定义函数与自定义Manim代码，创造更加灵活的动画效果。",
                role="body",
                position="center",
                layout_area="lower_one_eighth"
            )

            if body_text:
                for text_anim in body_text:
                    self.play(text_anim["obj"])

            # 自定义交互效果
            self.play(
                rect.animate.scale(1.2),
                text.animate.set_color(YELLOW),
                Flash(rect, color=ORANGE, flash_radius=0.3),
                run_time=2
            )

            # 最后收尾动画
            self.play(
                FadeOut(graph_group),
                FadeOut(dot),
                FadeOut(rect),
                FadeOut(text),
                run_time=1
            )

        # 清理场景
        self.scene_clear_display(pre_mobjects)
</hybrid_example>

<step>
{step}
</step>

<title>
{title}
</title>

<content>
{content}
</content>

<storyboard>
{storyboard}
</storyboard>

<media_assets>
{media_assets}
</media_assets>

请根据分镜内容，判断需要混合哪些已有函数和自定义Manim代码来实现所需效果。首先分析分镜内容，确定哪些部分可以用现有函数实现，哪些部分需要自定义代码。然后生成一个完整的Manim脚本，其中包含：

1. 必要的config设置和display_slogo调用
2. 对预定义函数的调用，用于实现标准效果
3. 自定义Manim代码，用于实现预定义函数无法实现的特殊效果
4. 合理安排动画顺序，确保视觉效果符合分镜描述

注意事项：
1. 生成的代码中必须包括config相关的设置，以及display_slogo的调用
2. 从FeynmanScene继承，并重写construct方法
3. 如果要使用多媒体素材，只能使用media_assets中的素材
4. 尽可能使用预定义函数（如render_media_display、render_keywords等）
5. 只在必要时使用自定义Manim代码，并确保与预定义函数调用良好集成
6. 确保生成的代码可以运行，不会有语法错误
"""
