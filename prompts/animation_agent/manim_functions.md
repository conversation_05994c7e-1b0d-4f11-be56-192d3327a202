# Manim Functions Reference

This document provides a reference for the available Manim functions that can be used by the animation agent.

## Core Animation Functions

### render_media_display
**Description**: 生成用于显示媒体内容的Manim动画代码

**Parameters**:
- `images` (List[str]): 要在动画中显示的图片文件路径列表，如果输入的分镜没有图片，则传入空列表，不要自行编造不存在的图片地址，也不要传入占位符

**Returns**:
- `list`: 包含动画对象的列表。每个元素是一个字典，包含"obj_type"和"obj"两个键。使用时需要通过["obj"]访问实际的动画对象。

### render_keywords
**Description**: 生成关键词垂直排列的动画效果

**Parameters**:
- `keywords` (List[str]): 要显示的关键词列表

**Returns**:
- `list`: 包含动画对象的列表。使用时直接作为参数传递给play方法。

### render_sort_animation
**Description**: 生成数组排序的动画效果

**Parameters**:
- `data` (List): 要排序的数据列表
- `algorithm` (str): 排序算法类型，支持 "bubble"(冒泡排序), "quick"(快速排序), "selection"(选择排序), "insertion"(插入排序)
- `layout` (str): 数组布局方式，支持 "horizontal"(水平), "vertical"(垂直)
- `layout_area` (str): 布局区域名称，决定动画在屏幕中的位置和大小，默认为"full_screen"

**Returns**:
- `list`: 包含动画对象的列表。每个元素是一个字典，包含"obj_type"和"obj"两个键。使用时需要通过["obj"]访问实际的动画对象。

### render_code_snippet
**Description**: 生成代码片段的动画效果

**Parameters**:
- `code` (str): 代码内容字符串
- `language` (str): 编程语言，默认为"python"
- `highlight_lines` (List): 要高亮显示的行号列表
- `title` (str): 代码块的标题，通常是文件名
- `layout_area` (str): 布局区域名称，决定代码块在屏幕中的位置和大小，默认为"full_screen"

**Returns**:
- `list`: 包含动画对象的列表。每个元素是一个字典，包含"obj_type"和"obj"两个键。使用时需要通过["obj"]访问实际的动画对象。

### render_text_element
**Description**: 生成具有不同角色和展示效果的文本元素

**Parameters**:
- `content` (str): 文本内容
- `role` (str): 文本角色 - "title"(标题), "heading"(小标题), "subheading"(子标题), "body"(正文), "caption"(说明文字), "quote"(引用), "note"(注释)
- `position` (str): 位置指示 - "top"(顶部), "center"(中央), "bottom"(底部), "left"(左侧), "right"(右侧), 也可以组合使用如"top_left"
- `layout_area` (str): 布局区域名称，决定文本在屏幕中的位置和大小，默认为"full_screen"
- `**kwargs`: 可选参数，用于覆盖默认设置

**Returns**:
- `list`: 包含动画对象的列表。每个元素是一个字典，包含"obj_type"和"obj"两个键。使用时需要通过["obj"]访问实际的动画对象。

### display_slogo
**Description**: 显示标题和logo

**Parameters**:
- `cover_question` (str): 标题文本
- `step` (int): 步骤，0表示带动画显示，1表示直接显示

## Layout Areas

以下是可用的布局区域：

```python
LAYOUTS = {
    # 上半部分
    "upper_half": "屏幕上半部分区域",
    # 右上1/4
    "right_upper_quarter": "屏幕右上角四分之一区域",
    # 右半部分
    "right_half": "屏幕右半部分区域",
    # 全屏
    "full_screen": "整个屏幕区域",
    # 左半部分
    "left_half": "屏幕左半部分区域",
    # 中间区域
    "origin_x14y6": "屏幕中央较大区域，高度为屏幕高度的3/4",
    # 上1/8
    "upper_one_eighth": "屏幕顶部八分之一区域，适合放置标题",
    # 下1/8
    "lower_one_eighth": "屏幕底部八分之一区域，适合放置字幕"
}
```

## DSL Format

DSL (Domain Specific Language) 是一种为特定领域设计的语言，用于简化在该领域中的问题描述和解决方案表达。在动画生成中，DSL 用于描述场景、对象和动作。

基本结构如下：

```json
{
  "metadata": {
    "title": "场景标题",
    "duration": 30
  },
  "objects": [
    {
      "id": "对象唯一标识符",
      "type": "对象类型",
      "properties": {
        "属性1": "值1",
        "属性2": "值2"
      }
    }
  ],
  "actions": [
    {
      "id": "动作唯一标识符",
      "target": "目标对象ID",
      "type": "动作类型",
      "properties": {
        "属性1": "值1",
        "属性2": "值2"
      },
      "start_time": 0,
      "duration": 2
    }
  ]
}
