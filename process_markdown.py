# process_markdown.py
import argparse
import os
import re
import subprocess
import sys
from pathlib import Path

from loguru import logger

# Ensure logger is configured (e.g., add a default handler if not already configured elsewhere)
# if not logger._core.handlers: # Basic check, might need more robust check depending on setup
#     logger.add(sys.stderr, level="INFO")


def run_mmdc(mermaid_code, output_png_path, temp_mmd_path):
    """Runs the mmdc command to convert mermaid code to PNG."""
    try:
        # Write mermaid code to a temporary file
        with open(temp_mmd_path, "w", encoding="utf-8") as f:
            f.write(mermaid_code)

        # Ensure output directory exists
        output_png_path.parent.mkdir(parents=True, exist_ok=True)

        # Construct the mmdc command
        # Added --backgroundColor transparent for better embedding
        command = ["mmdc", "-i", str(temp_mmd_path), "-o", str(output_png_path), "-w", "2160", "-s", "4"]
        logger.info(f"Running command: {' '.join(command)}")

        # Run the command
        result = subprocess.run(command, capture_output=True, text=True, check=True, encoding="utf-8")
        logger.info(f"Successfully generated {output_png_path}")
        if result.stderr:
            logger.warning(f"mmdc stderr: {result.stderr}")
        return True
    except FileNotFoundError:
        logger.error("Error: 'mmdc' command not found. Please ensure mermaid-cli is installed and in your PATH.")
        logger.error("You can typically install it using: npm install -g @mermaid-js/mermaid-cli")
        return False
    except subprocess.CalledProcessError as e:
        logger.error(f"Error running mmdc for {output_png_path}:")
        logger.error(f"Command: {' '.join(e.cmd)}")
        logger.error(f"Return code: {e.returncode}")
        logger.error(f"stdout: {e.stdout}")
        logger.error(f"stderr: {e.stderr}")
        # Try to read the temp file content for debugging
        try:
            with open(temp_mmd_path, encoding="utf-8") as f_err:
                logger.error(f"Input Mermaid Code:\n{f_err.read()}")
        except Exception as read_err:
            logger.error(f"Could not read temp mmd file {temp_mmd_path}: {read_err}")
        return False
    except Exception as e:
        logger.error(f"An unexpected error occurred while processing {output_png_path}: {e}")
        return False
    finally:
        # Clean up the temporary file
        if temp_mmd_path.exists():
            try:
                os.remove(temp_mmd_path)
            except Exception as e:
                logger.warning(f"Could not remove temporary file {temp_mmd_path}: {e}")


def _process_and_write_markdown_content(
    full_content: str,
    output_md_path: Path,
    image_subdir_name: str = "mermaid_images",
    temp_dir_base_name: str = ".mermaid_temp",
):
    """
    Processes the given markdown content to convert Mermaid blocks to images,
    embeds them, and writes the result to the output file.
    Manages image and temporary directories relative to the output_md_path.
    Returns True if all mmdc operations were successful or no blocks needed mmdc,
    False if any mmdc operation failed for a diagram.
    """
    logger.info(f"Starting processing of markdown content for output: {output_md_path}")

    # --- 1. Prepare output directories (relative to output_md_path) ---
    output_md_parent = output_md_path.parent
    image_output_dir = output_md_parent / image_subdir_name
    temp_dir = output_md_parent / temp_dir_base_name

    try:
        output_md_parent.mkdir(parents=True, exist_ok=True)
        image_output_dir.mkdir(parents=True, exist_ok=True)
        temp_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"Ensured output directory exists: {output_md_parent}")
        logger.info(f"Ensured image directory exists: {image_output_dir}")
        logger.info(f"Ensured temp directory exists: {temp_dir}")
    except OSError as e:
        logger.error(f"Error creating output directories relative to {output_md_path}: {e}")
        sys.exit(1)  # Critical error, exit

    # --- 2. Find, extract, process Mermaid blocks, and prepare for replacement ---
    mermaid_block_capture_regex = re.compile(r"```mermaid\s*?\n(.*?)\n\s*?```", re.DOTALL | re.IGNORECASE)

    matches_iterator = list(mermaid_block_capture_regex.finditer(full_content))
    logger.info(f"Found {len(matches_iterator)} potential mermaid blocks using finditer.")

    processed_content_segments = []
    last_end = 0
    any_mmdc_failed = False
    actual_blocks_replaced_count = 0

    for i, match in enumerate(matches_iterator):
        original_block_text = match.group(0)
        mermaid_code = match.group(1).strip()
        start_index, end_index = match.span(0)

        processed_content_segments.append(full_content[last_end:start_index])  # Content before match

        replacement_for_this_block = original_block_text  # Default to original

        if not mermaid_code:
            logger.warning(
                f"Skipping empty mermaid block {i+1} (original: '{original_block_text[:60]}...'). No image generated."
            )
        else:
            image_filename = f"mermaid_diagram_{i+1}.png"
            output_png_path = image_output_dir / image_filename
            temp_mmd_path = temp_dir / f"temp_mermaid_{i+1}.mmd"
            # Path for the markdown link, relative to the MD file's location
            relative_image_path_in_md = Path(image_subdir_name) / image_filename

            logger.info(f"Processing Mermaid Block {i+1} -> {relative_image_path_in_md}")

            if run_mmdc(mermaid_code, output_png_path, temp_mmd_path):
                # Use forward slashes for Markdown links, even on Windows
                image_link = f"![Mermaid Diagram {i+1}]({relative_image_path_in_md.as_posix()})"
                replacement_for_this_block = image_link
                actual_blocks_replaced_count += 1
                logger.info(f"Image generated for block {i+1}. Will use image link.")
            else:
                logger.error(f"Failed to generate image for block {i+1}. Keeping original code block.")
                any_mmdc_failed = True
                # replacement_for_this_block remains original_block_text

        processed_content_segments.append(replacement_for_this_block)
        last_end = end_index

    processed_content_segments.append(full_content[last_end:])  # Remaining content after last match
    final_content = "".join(processed_content_segments)

    logger.info(f"Performed {actual_blocks_replaced_count} successful image replacements for mermaid blocks.")

    # --- 3. Write the final output ---
    try:
        with open(output_md_path, "w", encoding="utf-8") as f:
            f.write(final_content)
        logger.info(f"Successfully wrote processed markdown to: {output_md_path}")
    except OSError as e:
        logger.error(f"Error writing final output to {output_md_path}: {e}")
        sys.exit(1)  # Critical error

    # --- 4. Clean up temp directory ---
    try:
        logger.info(f"Attempting to clean up temporary directory: {temp_dir}")
        if temp_dir.exists() and temp_dir.is_dir():  # Ensure it exists and is a directory
            for temp_file in temp_dir.iterdir():  # Use iterdir for direct children
                if temp_file.is_file():
                    try:
                        os.remove(temp_file)
                        logger.debug(f"Removed temp file: {temp_file}")
                    except Exception as e_rm:
                        logger.warning(f"Could not remove temp file {temp_file}: {e_rm}")
            try:
                os.rmdir(temp_dir)  # Remove the directory itself if empty
                logger.info(f"Successfully cleaned up temporary directory: {temp_dir}")
            except OSError as e_rmdir:  # Catch if not empty or other issues
                logger.warning(
                    f"Could not remove temporary directory {temp_dir} (it might not be empty or access denied): {e_rmdir}"
                )
        else:
            logger.info(f"Temporary directory {temp_dir} does not exist or is not a directory, no cleanup needed.")
    except Exception as e_cleanup:
        logger.warning(f"An unexpected error occurred during temp directory cleanup {temp_dir}: {e_cleanup}")

    if any_mmdc_failed:
        logger.warning("One or more mermaid diagrams failed to generate. Please check the logs.")
        return False  # Indicate partial success or failure

    return True  # All operations successful or no mmdc errors


def process_markdown_files(input_dir_path: Path, output_md_path: Path):
    """
    Finds markdown files in a directory, concatenates them (index.md first,
    then numerically prefixed, then others alphabetically), then processes
    the combined content for Mermaid diagrams.
    """
    if not input_dir_path.is_dir():
        logger.error(f"Input directory '{input_dir_path}' not found or is not a directory.")
        sys.exit(1)

    all_md_files = list(input_dir_path.glob("*.md"))
    index_file = None
    numeric_files = []
    other_alpha_files = []

    for md_file in all_md_files:
        if md_file.name.lower() == "index.md":
            index_file = md_file
        else:
            # Check for numeric prefix like "01_..."
            parts = md_file.name.split("_", 1)
            if len(parts) > 1 and parts[0].isdigit():
                try:
                    # Store the numeric part for sorting
                    numeric_prefix = int(parts[0])
                    numeric_files.append((numeric_prefix, md_file))
                except ValueError:
                    # Not a simple integer prefix
                    other_alpha_files.append(md_file)
            else:
                other_alpha_files.append(md_file)

    # Sort numeric files: primary key is the integer part, secondary is full name
    numeric_files.sort(key=lambda item: (item[0], item[1].name))
    # Sort other files alphabetically by name
    other_alpha_files.sort(key=lambda f: f.name)

    ordered_md_files = []
    if index_file:
        ordered_md_files.append(index_file)
        logger.info(f"Found '{index_file.name}', placing it first.")
    else:
        logger.warning(f"'index.md' not found in {input_dir_path}.")

    # Add sorted numeric files (only the Path object)
    ordered_md_files.extend([item[1] for item in numeric_files])
    if numeric_files:
        logger.info(f"Added {len(numeric_files)} numerically prefixed files in order.")

    ordered_md_files.extend(other_alpha_files)
    if other_alpha_files:
        logger.info(f"Added {len(other_alpha_files)} other markdown files alphabetically.")

    if not ordered_md_files:
        logger.warning(f"No markdown files found in '{input_dir_path}'. Writing an empty output file.")
        try:
            output_md_path.parent.mkdir(parents=True, exist_ok=True)
            with open(output_md_path, "w", encoding="utf-8") as f:
                f.write("")
            logger.info(f"Created empty output file: {output_md_path}")
        except OSError as e:
            logger.error(f"Error creating empty output file {output_md_path}: {e}")
        # No sys.exit(0) here, let main decide exit codes.
        # An empty output file is a valid outcome.
        return True  # Indicate success (empty output created)

    logger.info(f"Processing {len(ordered_md_files)} markdown files in the following order:")
    for md_file in ordered_md_files:
        logger.info(f"  - {md_file.name}")

    full_content_parts = []
    for md_file in ordered_md_files:
        try:
            with open(md_file, encoding="utf-8") as f:
                full_content_parts.append(f.read())
            logger.info(f"Read and appended content from {md_file.name}")
        except Exception as e:
            logger.error(f"Error reading file {md_file}: {e}. Skipping this file.")
            continue

    full_content = "\n\n".join(full_content_parts)

    # Call the core processing and writing function
    return _process_and_write_markdown_content(full_content, output_md_path)


def main():
    parser = argparse.ArgumentParser(
        description="Processes a single Markdown file or all Markdown files in a directory, "
        "converts Mermaid diagrams to PNGs, and embeds them."
    )
    parser.add_argument(
        "input_path", type=str, help="Path to the input Markdown file or directory containing Markdown files."
    )
    parser.add_argument("output_file", type=str, help="Path to the output processed Markdown file.")

    # Check if any arguments were provided. If not, use defaults.
    if len(sys.argv) == 1:
        default_input_value = Path("tutorials/docs/SmolaAgents/").resolve()  # Default is a directory
        default_output_value = default_input_value / "combined_smola_agents_docs.md"

        logger.warning("No command line arguments provided. Using default values:")
        logger.warning(f"Input Path (default): {default_input_value}")
        logger.warning(f"Output File (default): {default_output_value}")

        input_path_to_use = default_input_value
        output_md_path_to_use = default_output_value

        # Since default input is a directory, ensure it exists before calling process_markdown_files
        if not input_path_to_use.exists() or not input_path_to_use.is_dir():
            logger.error(
                f"Default input directory '{input_path_to_use}' does not exist or is not a directory. Exiting."
            )
            sys.exit(1)
        overall_success = process_markdown_files(input_path_to_use, output_md_path_to_use)

    else:
        args = parser.parse_args()
        input_path_to_use = Path(args.input_path).resolve()
        output_md_path_to_use = Path(args.output_file).resolve()

        if not input_path_to_use.exists():
            logger.error(f"Input path '{input_path_to_use}' does not exist.")
            sys.exit(1)

        if input_path_to_use.is_file():
            if input_path_to_use.suffix.lower() not in [".md", ".markdown"]:
                logger.error(
                    f"Input file '{input_path_to_use}' does not appear to be a Markdown file "
                    "(.md or .markdown extension expected)."
                )
                sys.exit(1)

            logger.info(f"Processing single Markdown file: {input_path_to_use}")
            try:
                with open(input_path_to_use, encoding="utf-8") as f:
                    markdown_content = f.read()
                logger.info(f"Successfully read content from {input_path_to_use}.")
                overall_success = _process_and_write_markdown_content(markdown_content, output_md_path_to_use)
            except Exception as e:
                logger.error(f"Error reading or processing file {input_path_to_use}: {e}")
                sys.exit(1)

        elif input_path_to_use.is_dir():
            logger.info(f"Processing Markdown files in directory: {input_path_to_use}")
            overall_success = process_markdown_files(input_path_to_use, output_md_path_to_use)
        else:
            logger.error(f"Input path '{input_path_to_use}' is not a valid file or directory.")
            sys.exit(1)

    if overall_success:
        logger.info("Script execution completed successfully.")
        sys.exit(0)
    else:
        logger.warning("Script execution completed with some errors (e.g., mmdc failures). Please check logs.")
        sys.exit(2)  # Indicate partial failure or mmdc issues


if __name__ == "__main__":
    main()
