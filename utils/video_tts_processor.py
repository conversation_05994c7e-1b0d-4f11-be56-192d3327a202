#!/usr/bin/env python3
"""
视频TTS处理工具
根据log文件中的动画时间戳将视频分段，并为每段添加对应的TTS讲解文案
支持在动画开始时间叠加音效
"""

import argparse
import asyncio
import os
import re
from pathlib import Path
from typing import List, Tuple

import edge_tts
from moviepy import VideoFileClip, AudioFileClip, concatenate_videoclips, CompositeAudioClip


class VideoTTSProcessor:
    def __init__(self, voice="zh-CN-YunxiNeural"):
        """
        初始化视频TTS处理器
        
        Args:
            voice: EdgeTTS语音模型
        """
        self.voice = voice
        self.temp_dir = None
    
    def parse_log_timestamps(self, log_file: str) -> List[Tuple[str, float]]:
        """解析log文件中的动画时间戳和阶段信息"""
        timestamps = []
        
        with open(log_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                
                # 匹配时间戳格式：[动画时间: 3.5s] 阶段记录：红黑树规则
                match = re.match(r'\[动画时间:\s*([\d.]+)s\]\s*阶段记录：(.+)', line)
                if match:
                    animation_time = float(match.group(1))
                    stage_name = match.group(2).strip()
                    timestamps.append((stage_name, animation_time))
                    print(f"解析时间戳: {stage_name} @ {animation_time:.2f}s")
        
        return timestamps
    
    def parse_markdown_scripts(self, md_file: str) -> dict:
        """从markdown文件中提取每个阶段的讲解文案"""
        scripts = {}
        
        with open(md_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 按阶段分割内容
        stages = re.split(r'### 阶段(\d+)：', content)[1:]
        
        for i in range(0, len(stages), 2):
            if i + 1 < len(stages):
                stage_num = int(stages[i])
                stage_content = stages[i + 1]
                
                # 提取讲解文案
                script_match = re.search(r'\*\*讲解文案\*\*：(.+?)(?=\n\n|\n###|\Z)', stage_content, re.DOTALL)
                if script_match:
                    script_text = script_match.group(1).strip()
                    scripts[stage_num] = script_text
                    print(f"提取到阶段{stage_num}的讲解文案：{script_text[:50]}...")
        
        return scripts
    
    def calculate_video_segments(self, timestamps: List[Tuple[str, float]], video_duration: float) -> List[Tuple[float, float, str]]:
        """根据动画时间戳计算视频分段"""
        if not timestamps:
            return []
        
        segments = []
        
        for i, (stage_name, animation_time) in enumerate(timestamps):
            start_time = animation_time
            
            # 计算结束时间
            if i + 1 < len(timestamps):
                end_time = timestamps[i + 1][1]
            else:
                end_time = video_duration
            
            # 确保时间在有效范围内
            start_time = max(0, min(start_time, video_duration))
            end_time = max(start_time, min(end_time, video_duration))
            
            if end_time > start_time:
                segments.append((start_time, end_time, stage_name))
                print(f"分段: {stage_name} [{start_time:.2f}s - {end_time:.2f}s]")
        
        return segments
    
    async def generate_tts_audio(self, text: str, output_path: str) -> str:
        """为文本生成TTS音频"""
        communicate = edge_tts.Communicate(text, self.voice)
        await communicate.save(output_path)
        print(f"✅ TTS音频生成: {output_path}")
        return output_path
    
    def add_audio_to_video_segment(self, video_clip, audio_path: str):
        """为视频片段添加音频"""
        if not Path(audio_path).exists():
            print(f"警告：音频文件不存在: {audio_path}")
            return video_clip
        
        # 验证音频文件
        file_size = Path(audio_path).stat().st_size
        if file_size < 1000:  # 小于1KB
            print(f"警告：音频文件太小: {audio_path}")
            return video_clip
        
        # 尝试加载音频
        try:
            audio_clip = AudioFileClip(audio_path)
            # 测试音频是否可读
            test_frame = audio_clip.get_frame(0)
            if test_frame is None:
                audio_clip.close()
                print(f"警告：音频文件无法读取: {audio_path}")
                return video_clip
        except Exception as e:
            print(f"警告：音频文件损坏: {audio_path}, {e}")
            return video_clip
        
        # 调整视频时长匹配音频
        if audio_clip.duration > video_clip.duration:
            # 音频比视频长，延长视频（静止最后一帧）
            extend_duration = audio_clip.duration - video_clip.duration
            last_frame = video_clip.get_frame(video_clip.duration - 1.0 / video_clip.fps)
            
            from moviepy import ImageClip
            frozen_clip = ImageClip(last_frame).with_duration(extend_duration)
            video_clip = concatenate_videoclips([video_clip, frozen_clip])
            print(f"视频延长 {extend_duration:.2f}s 匹配音频")
        
        elif audio_clip.duration < video_clip.duration:
            # 音频比视频短，裁剪视频
            video_clip = video_clip.subclipped(0, audio_clip.duration)
            print(f"视频裁剪到 {audio_clip.duration:.2f}s 匹配音频")
        
        # 替换音频
        video_clip = video_clip.with_audio(audio_clip)
        audio_clip.close()
        
        return video_clip
    
    def merge_audio_with_ffmpeg(self, video_path: str, audio_files: list, output_path: str) -> bool:
        """使用FFmpeg合成音视频，支持混合TTS和音效"""
        import subprocess
        
        try:
            # 分离TTS文件和音效文件
            tts_files = [f for f in audio_files if 'tts_segment_' in f]
            sound_effect_files = [f for f in audio_files if 'sound_effects' in f]
            
            combined_audio = str(self.temp_dir / "combined_audio.mp3")
            
            if len(tts_files) == 0 and len(sound_effect_files) == 1:
                # 只有音效，直接使用
                combined_audio = sound_effect_files[0]
            elif len(tts_files) == 1 and len(sound_effect_files) == 0:
                # 只有TTS，直接使用
                combined_audio = tts_files[0]
            elif len(tts_files) > 0 and len(sound_effect_files) == 1:
                # 有TTS和音效，需要混合
                # 1. 先拼接TTS文件
                tts_combined = str(self.temp_dir / "tts_combined.mp3")
                if len(tts_files) == 1:
                    tts_combined = tts_files[0]
                else:
                    file_list = str(self.temp_dir / "tts_list.txt")
                    with open(file_list, 'w', encoding='utf-8') as f:
                        for tts_file in tts_files:
                            # 使用绝对路径避免路径问题
                            abs_path = os.path.abspath(tts_file)
                            f.write(f"file '{abs_path}'\n")
                    
                    subprocess.run([
                        'ffmpeg', '-y', '-f', 'concat', '-safe', '0',
                        '-i', file_list, '-c:a', 'mp3', tts_combined
                    ], check=True, capture_output=True)
                
                # 2. 混合TTS和音效
                subprocess.run([
                    'ffmpeg', '-y',
                    '-i', tts_combined,
                    '-i', sound_effect_files[0],
                    '-filter_complex', '[0:a][1:a]amix=inputs=2:duration=longest:dropout_transition=3',
                    '-c:a', 'mp3',
                    combined_audio
                ], check=True, capture_output=True)
            elif len(tts_files) > 1:
                # 多个TTS文件，拼接
                file_list = str(self.temp_dir / "tts_list.txt")
                with open(file_list, 'w', encoding='utf-8') as f:
                    for tts_file in tts_files:
                        # 使用绝对路径避免路径问题
                        abs_path = os.path.abspath(tts_file)
                        f.write(f"file '{abs_path}'\n")
                
                subprocess.run([
                    'ffmpeg', '-y', '-f', 'concat', '-safe', '0',
                    '-i', file_list, '-c:a', 'mp3', combined_audio
                ], check=True, capture_output=True)
            else:
                print("❌ 没有找到有效的音频文件")
                return False
            
            # 3. 合成音视频
            subprocess.run([
                'ffmpeg', '-y', 
                '-i', video_path,
                '-i', combined_audio,
                '-c:v', 'copy',
                '-c:a', 'aac',
                '-shortest',
                output_path
            ], check=True, capture_output=True)
            
            print(f"✅ FFmpeg合成成功")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ FFmpeg命令执行失败: {e}")
            return False
        except FileNotFoundError:
            print(f"❌ 未找到FFmpeg，请确保已安装FFmpeg")
            return False
        except Exception as e:
            print(f"❌ FFmpeg合成出错: {e}")
            return False

    def parse_animation_timestamps(self, log_file: str) -> List[float]:
        """解析log文件中的动画开始时间戳"""
        animation_times = []
        
        with open(log_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                
                # 匹配动画时间戳格式：#动画: 开始: 7.00s, 结束: 8.00s
                match = re.match(r'#动画:\s*开始:\s*([\d.]+)s,\s*结束:\s*([\d.]+)s', line)
                if match:
                    start_time = float(match.group(1))
                    animation_times.append(start_time)
                    print(f"解析动画时间戳: {start_time:.2f}s")
        
        # 去重并排序
        animation_times = sorted(list(set(animation_times)))
        return animation_times
    
    def recalculate_animation_timestamps(self, animation_times: List[float], segments: List[Tuple[float, float, str]], segment_durations: List[float]) -> List[float]:
        """重新计算动画时间戳，映射到处理后的视频时间轴"""
        new_animation_times = []
        
        # 计算各段在新视频中的起始时间
        new_segment_starts = [0]
        for duration in segment_durations[:-1]:
            new_segment_starts.append(new_segment_starts[-1] + duration)
        
        print(f"📊 分段时间映射:")
        for i, (segment, new_start, new_duration) in enumerate(zip(segments, new_segment_starts, segment_durations)):
            orig_start, orig_end, name = segment
            print(f"  分段{i+1} {name}: [{orig_start:.2f}s-{orig_end:.2f}s] -> [{new_start:.2f}s-{new_start+new_duration:.2f}s]")
        
        # 重新映射每个动画时间戳
        for animation_time in animation_times:
            # 找到该时间戳属于哪个分段
            target_segment_idx = None
            for i, (start_time, end_time, _) in enumerate(segments):
                if start_time <= animation_time < end_time:
                    target_segment_idx = i
                    break
            
            if target_segment_idx is not None:
                # 计算在原分段中的相对位置
                orig_start, orig_end, _ = segments[target_segment_idx]
                relative_position = (animation_time - orig_start) / (orig_end - orig_start)
                
                # 映射到新分段中的位置
                new_segment_start = new_segment_starts[target_segment_idx]
                new_segment_duration = segment_durations[target_segment_idx]
                new_animation_time = new_segment_start + relative_position * new_segment_duration
                
                new_animation_times.append(new_animation_time)
                print(f"🎯 动画时间戳映射: {animation_time:.2f}s -> {new_animation_time:.2f}s (分段{target_segment_idx+1})")
            else:
                print(f"⚠️  动画时间戳 {animation_time:.2f}s 未找到对应分段，跳过")
        
        return new_animation_times
 
    def create_sound_effects_file(self, sound_effect_path: str, animation_times: List[float], video_duration: float):
        """创建音效文件，在指定时间点叠加音效"""
        if not animation_times:
            return None
            
        if not Path(sound_effect_path).exists():
            print(f"警告：音效文件不存在: {sound_effect_path}")
            return None
        
        try:
            # 加载音效文件
            sound_effect = AudioFileClip(sound_effect_path)
            
            # 确保音效不会太长，避免覆盖
            if sound_effect.duration > 2.0:
                sound_effect = sound_effect.subclipped(0, 2.0)
            
            sound_clips = []
            
            for animation_time in animation_times:
                if animation_time < video_duration and animation_time + sound_effect.duration <= video_duration:
                    # 在指定时间点添加音效
                    sound_clip = sound_effect.with_start(animation_time)
                    sound_clips.append(sound_clip)
                    print(f"添加音效于 {animation_time:.2f}s")
            
            if sound_clips:
                # 合成所有音效，设置为合理的时长
                composite_sound = CompositeAudioClip(sound_clips)
                # 确保音效轨道不超过视频长度
                if composite_sound.duration > video_duration:
                    composite_sound = composite_sound.subclipped(0, video_duration)
                
                # 保存音效文件
                sound_effects_output = self.temp_dir / "sound_effects.mp3"
                composite_sound.write_audiofile(str(sound_effects_output), logger=None)
                composite_sound.close()
                sound_effect.close()
                
                return str(sound_effects_output)
            else:
                return None
                
        except Exception as e:
            print(f"创建音效文件失败: {e}")
            return None
    
    async def process_video(self, video_path: str, log_file: str, md_file: str, output_path: str, sound_effect_path: str = None):
        """处理视频：根据log时间戳分段，添加对应的TTS音频和音效"""
        # 创建临时目录
        self.temp_dir = Path(output_path).parent / "temp_tts"
        self.temp_dir.mkdir(exist_ok=True)
        
        # 解析时间戳和讲解文案
        timestamps = self.parse_log_timestamps(log_file)
        scripts = self.parse_markdown_scripts(md_file)
        
        # 解析动画时间戳（用于音效）
        animation_times = self.parse_animation_timestamps(log_file)
        
        print(f"找到 {len(timestamps)} 个时间戳，{len(scripts)} 个讲解文案，{len(animation_times)} 个动画时间戳")
        
        # 加载视频
        video = VideoFileClip(video_path)
        video_duration = video.duration
        print(f"视频时长: {video_duration:.2f} 秒")
        
        # 计算视频分段
        segments = self.calculate_video_segments(timestamps, video_duration)
        processed_clips = []
        segment_durations = []  # 记录每段的实际长度
        
        # 处理每个分段
        for i, (start_time, end_time, stage_name) in enumerate(segments):
            print(f"\n处理分段 {i+1}/{len(segments)}: {stage_name}")
            
            # 提取视频片段
            video_segment = video.subclipped(start_time, end_time)
            original_duration = video_segment.duration
            
            # 查找对应的讲解文案
            script_text = None
            for stage_num, script in scripts.items():
                if stage_num == i + 1:  # 按序号匹配
                    script_text = script
                    break
            
            if script_text:
                print(f"找到讲解文案: {script_text[:50]}...")
                
                # 生成TTS音频
                audio_path = self.temp_dir / f"tts_segment_{i}.mp3"
                await self.generate_tts_audio(script_text, str(audio_path))
                
                # 为视频段添加音频
                video_segment = self.add_audio_to_video_segment(video_segment, str(audio_path))
            else:
                print("未找到对应的讲解文案")
            
            processed_clips.append(video_segment)
            segment_durations.append(video_segment.duration)
            print(f"分段 {i+1} 长度变化: {original_duration:.2f}s -> {video_segment.duration:.2f}s")
        
        # 采用无音频策略，避免音频reader问题
        print(f"\n🎬 移除音频并拼接 {len(processed_clips)} 个视频片段...")
        video_clips_no_audio = []
        
        # 移除所有音频
        for i, clip in enumerate(processed_clips):
            video_only = clip.without_audio()
            video_clips_no_audio.append(video_only)
        
        # 拼接无音频视频
        final_video = concatenate_videoclips(video_clips_no_audio, method="compose")
        final_video_duration = final_video.duration
        
        # 重新计算音效时间戳（映射到处理后的视频时间轴）
        if sound_effect_path and animation_times:
            print(f"\n🎯 重新计算音效时间戳...")
            recalculated_times = self.recalculate_animation_timestamps(animation_times, segments, segment_durations)
            print(f"重新计算完成: {len(animation_times)} -> {len(recalculated_times)} 个有效时间戳")
        else:
            recalculated_times = []
        
        # 先保存无音频版本
        print(f"💾 保存无音频视频...")
        final_video.write_videofile(
            output_path,
            codec="libx264",
            fps=30,
            preset="medium",
            audio=False,
            remove_temp=True,
        )
        
        # 单独创建音效轨道（使用重新计算的时间戳）
        sound_effects_file = None
        if sound_effect_path and recalculated_times:
            sound_effects_file = self.create_sound_effects_file(sound_effect_path, recalculated_times, final_video_duration)
        
        print(f"✅ 无音频视频生成完成: {output_path}")
        if sound_effects_file:
            print(f"🎵 音效文件生成完成: {sound_effects_file}")
        
        # 收集生成的音频文件
        audio_files = []
        print(f"\n🎵 生成的TTS音频文件:")
        for i in range(len(processed_clips)):
            audio_file = self.temp_dir / f"tts_segment_{i}.mp3"
            if audio_file.exists():
                audio_files.append(str(audio_file))
                print(f"  - 分段{i+1}: {audio_file}")
        
        # 收集所有音频文件（包括音效）
        all_audio_files = audio_files.copy()
        if sound_effects_file and Path(sound_effects_file).exists():
            all_audio_files.append(sound_effects_file)
            print(f"🎵 添加音效文件到合成列表: {sound_effects_file}")
        
        # 清理资源
        video.close()
        for clip in processed_clips:
            clip.close()
        for clip in video_clips_no_audio:
            clip.close()
        final_video.close()
        
        print(f"✅ 视频生成完成: {output_path}")
        
        # 尝试使用FFmpeg合成音视频
        if all_audio_files:
            print(f"\n🔧 尝试使用FFmpeg合成音视频（包含 {len(all_audio_files)} 个音频文件）...")
            final_with_audio = output_path.replace('.mp4', '_with_audio.mp4')
            success = self.merge_audio_with_ffmpeg(output_path, all_audio_files, final_with_audio)
            
            if success:
                print(f"🎉 音视频合成成功！最终文件: {final_with_audio}")
            else:
                print(f"⚠️  FFmpeg合成失败，请手动合成音频")
                print(f"📁 TTS音频文件保留在: {self.temp_dir}")
        else:
            print(f"⚠️  未找到有效的音频文件")
        
        # 不清理临时文件，保留TTS音频供用户使用
        # if self.temp_dir and self.temp_dir.exists():
        #     import shutil
        #     shutil.rmtree(self.temp_dir)


async def main():
    parser = argparse.ArgumentParser(description="视频TTS处理工具")
    parser.add_argument("video_path", help="输入视频文件路径")
    parser.add_argument("log_file", help="时间戳log文件路径")
    parser.add_argument("md_file", help="markdown讲解文案文件路径")
    parser.add_argument("output_path", help="输出视频文件路径")
    parser.add_argument("--voice", default="zh-CN-YunxiNeural", help="EdgeTTS语音模型")
    parser.add_argument("--sound-effect", help="音效文件路径（如assets/slide.wav）")
    
    args = parser.parse_args()
    
    # 检查输入文件是否存在
    for file_path in [args.video_path, args.log_file, args.md_file]:
        if not os.path.exists(file_path):
            print(f"错误：文件不存在 {file_path}")
            return
    
    # 检查音效文件（如果指定了）
    if args.sound_effect and not os.path.exists(args.sound_effect):
        print(f"错误：音效文件不存在 {args.sound_effect}")
        return
    
    # 创建处理器并处理视频
    processor = VideoTTSProcessor(voice=args.voice)
    await processor.process_video(
        args.video_path,
        args.log_file, 
        args.md_file,
        args.output_path,
        args.sound_effect
    )


if __name__ == "__main__":
    asyncio.run(main())