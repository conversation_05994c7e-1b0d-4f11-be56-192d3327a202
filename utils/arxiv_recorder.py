#!/usr/bin/env python

import argparse
import os
import re
import time

import cv2
import numpy as np
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from webdriver_manager.chrome import ChromeDriverManager


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="录制ArXiv页面的滚动视频")
    parser.add_argument("url", type=str, help="要录制的ArXiv URL")
    parser.add_argument("--output-path", type=str, default="output.mp4", help="输出文件路径")
    parser.add_argument("--duration", type=int, default=8, help="录制时长（秒）")
    parser.add_argument("--width", type=int, default=1920, help="视频宽度（像素）")
    parser.add_argument("--height", type=int, default=1080, help="视频高度（像素）")
    parser.add_argument("--fps", type=int, default=5, help="每秒帧数")
    parser.add_argument("--smooth-factor", type=float, default=0.2, help="滚动平滑度（值越小越平滑）")
    parser.add_argument("--title-focus", type=int, default=4, help="聚焦标题的时间（秒）")
    parser.add_argument("--zoom-factor", type=float, default=2, help="缩放倍数（1.0表示不缩放）")
    parser.add_argument("--abstract-pause", type=float, default=0.0, help="在摘要部分暂停的时间（秒）")
    return parser.parse_args()


def setup_driver(width, height):
    """设置Selenium WebDriver及其选项"""
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument(f"--window-size={width},{height}")
    chrome_options.add_argument("--disable-infobars")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--log-level=3")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-browser-side-navigation")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")

    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option("useAutomationExtension", False)

    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        return driver
    except Exception as e:
        print(f"设置Chrome驱动时出错: {e}")
        raise


def get_scroll_height(driver):
    """获取页面可滚动的总高度"""
    return driver.execute_script(
        "return Math.max("
        "document.body.scrollHeight, "
        "document.documentElement.scrollHeight, "
        "document.body.offsetHeight, "
        "document.documentElement.offsetHeight, "
        "document.body.clientHeight, "
        "document.documentElement.clientHeight);"
    )


def center_element_in_viewport(driver, element, position="center"):
    """
    将元素居中显示在视口中
    position可以是: 'center', 'left', 'right'
    """
    # 获取元素位置
    rect = driver.execute_script(
        """
        var rect = arguments[0].getBoundingClientRect();
        return {
            top: rect.top,
            left: rect.left,
            width: rect.width,
            height: rect.height
        };
    """,
        element,
    )

    # 获取视口尺寸和滚动位置
    viewport = driver.execute_script(
        """
        return {
            width: window.innerWidth,
            height: window.innerHeight,
            scrollX: window.scrollX,
            scrollY: window.scrollY
        };
    """
    )

    # 计算元素中心坐标相对于视口
    element_center_x = rect["left"] + rect["width"] / 2
    element_center_y = rect["top"] + rect["height"] / 2

    # 计算视口中心
    viewport_center_x = viewport["width"] / 2
    viewport_center_y = viewport["height"] / 2

    # 根据请求的位置计算偏移量
    if position == "left":
        # 将元素放在屏幕左侧
        target_x = viewport["width"] * 0.15
        offset_x = element_center_x - target_x
    elif position == "right":
        # 将元素放在屏幕右侧
        target_x = viewport["width"] * 0.7
        offset_x = element_center_x - target_x
    else:  # center
        offset_x = element_center_x - viewport_center_x

    # 垂直方向始终居中
    offset_y = element_center_y - viewport_center_y

    # 计算目标滚动位置
    target_scroll_x = viewport["scrollX"] + offset_x
    target_scroll_y = viewport["scrollY"] + offset_y

    # 应用精确滚动
    print(f"将元素定位到 '{position}' (偏移量 X: {offset_x:.1f}px, Y: {offset_y:.1f}px)...")
    driver.execute_script(
        f"""
    window.scrollTo({{
        top: {target_scroll_y},
        left: {target_scroll_x},
        behavior: 'smooth'
    }});
    """
    )

    # 等待滚动完成
    time.sleep(0.2)

    # 返回视口中心以供变换原点使用
    return {
        "center_x": viewport_center_x,
        "center_y": viewport_center_y,
        "element_center_x": element_center_x,
        "element_center_y": element_center_y,
    }


def apply_highlight(driver, element):
    """对元素应用高亮效果，增强可见性"""
    driver.execute_script(
        """
    let el = arguments[0];
    // 使用红色高亮，增加可见度
    el.style.outline = '3px solid rgba(255, 0, 0, 0.9)';
    el.style.outlineOffset = '3px';
    el.style.transition = 'outline 0.5s ease-in-out, box-shadow 0.5s ease-in-out, transform 0.3s ease';

    // 添加脉冲动画效果
    function pulseOutline() {
        // 在深红色和亮红色之间切换
        el.style.outline = '3px solid rgba(255, 0, 0, 0.95)';
        setTimeout(() => {
            el.style.outline = '3px solid rgba(255, 30, 30, 0.8)';
            setTimeout(pulseOutline, 600);
        }, 600);
    }
    pulseOutline();

    // 添加更明显的阴影效果
    if (!el._originalBoxShadow) {
        el._originalBoxShadow = getComputedStyle(el).boxShadow;
    }
    el.style.boxShadow = '0 0 15px rgba(255, 0, 0, 0.7)';

    // 添加微小缩放效果增强突出显示
    if (!el._originalTransform) {
        el._originalTransform = getComputedStyle(el).transform;
    }
    el.style.transform = 'scale(1.02)';
    """,
        element,
    )


def remove_highlight(driver, element):
    """移除元素的高亮效果"""
    driver.execute_script(
        """
    let el = arguments[0];
    el.style.outline = '0px solid rgba(255, 0, 0, 0)';
    el.style.boxShadow = el._originalBoxShadow || 'none';
    el.style.transform = el._originalTransform || '';
    """,
        element,
    )


def ease_in_out_cubic(t):
    """三次方缓动函数，使动画更平滑"""
    if t < 0.5:
        return 4 * t * t * t
    else:
        p = 2 * t - 2
        return 0.5 * p * p * p + 1


def capture_zoom_in(driver, element, duration, fps, width, height, zoom_factor, position="center"):
    """捕获元素缩放过程的帧"""
    frames = []
    transition_frames = int(duration * fps)

    try:
        # 使用一致的缩放系数
        safe_zoom_factor = base_zoom = max(1.0, min(zoom_factor, 2.0))

        # 根据请求的位置将元素放置在视口中
        viewport_data = center_element_in_viewport(driver, element, position)

        # 获取更精确的定位以进行变换
        precise_center_x = viewport_data["center_x"]
        precise_center_y = viewport_data["center_y"]

        # 获取原始变换以便后续恢复
        original_transform = driver.execute_script("return document.body.style.transform || '';")

        # 设置缩放过渡效果，使用三次贝塞尔曲线实现平滑
        driver.execute_script(
            f"""
        // 在视口中心应用变换原点
        document.body.style.transformOrigin = '{precise_center_x}px {precise_center_y}px';
        document.body.style.transition = 'transform {duration}s cubic-bezier(0.34, 1.56, 0.64, 1)';
        """
        )

        print(f"捕获放大动画: {transition_frames} 帧，持续 {duration:.1f}秒")

        # 从缩放比例1开始
        driver.execute_script("document.body.style.transform = 'scale(1)';")

        # 再次获取元素的缩放前位置，用于精确计算
        element_rect = driver.execute_script(
            """
            var rect = arguments[0].getBoundingClientRect();
            return {
                top: rect.top,
                left: rect.left,
                width: rect.width,
                height: rect.height
            };
        """,
            element,
        )

        # 获取视口宽度，用于计算1/3移动距离
        viewport_width = driver.execute_script("return window.innerWidth;")
        right_shift = viewport_width / 3  # 向右移动1/3视口宽度

        # 计算缩放后元素的位置
        element_center_x = element_rect["left"] + element_rect["width"] / 2
        element_center_y = element_rect["top"] + element_rect["height"] / 2

        # 计算保持元素居中所需的偏移量，并添加向右的移动
        dx = element_center_x - precise_center_x
        dy = element_center_y - precise_center_y

        # 将中心点向右移动1/3
        dx += right_shift / safe_zoom_factor  # 除以缩放系数以获得正确的移动距离
        print(f"将放大中心点向右移动 {right_shift:.0f}px (视口宽度的1/3)")

        # 记录开始时间
        start_time = time.time()

        # 计算每帧的理想时间间隔
        frame_interval = duration / transition_frames

        # 在放大过程中捕获帧，使用缓动时间
        for i in range(transition_frames):
            # 计算当前帧应该在的时间点
            target_time = start_time + i * frame_interval

            # 计算进度，应用缓动效果
            progress = i / (transition_frames - 1) if transition_frames > 1 else 1.0  # 0到1
            eased_progress = ease_in_out_cubic(progress)
            current_scale = 1 + (safe_zoom_factor - 1) * eased_progress

            # 计算平移量以保持元素居中（加入向右移动）
            translate_x = -dx * (current_scale - 1)
            translate_y = -dy * (current_scale - 1)

            # 应用当前缩放和平移
            driver.execute_script(
                f"""
            document.body.style.transform = 'scale({current_scale}) translate({translate_x}px, {translate_y}px)';
            """
            )

            # 捕获当前帧
            screenshot = driver.get_screenshot_as_png()
            nparr = np.frombuffer(screenshot, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            img = cv2.resize(img, (width, height))
            frames.append(img)

            # 计算需要等待的时间，确保按照精确间隔捕获帧
            current_time = time.time()
            wait_time = max(0.001, target_time - current_time)
            if wait_time > 0:
                time.sleep(wait_time)

        # 确保我们以精确的目标缩放结束，完美居中（加入向右移动）
        translate_x = -dx * (safe_zoom_factor - 1)
        translate_y = -dy * (safe_zoom_factor - 1)
        driver.execute_script(
            f"""
        document.body.style.transform = 'scale({safe_zoom_factor}) translate({translate_x}px, {translate_y}px)';
        """
        )

        # 返回实际使用的缩放系数，以便其他函数保持一致性
        return frames, safe_zoom_factor

    except Exception as e:
        print(f"放大过程中出错: {e}")
        import traceback
        traceback.print_exc()

    return frames, zoom_factor


def capture_steady_zoom(driver, element, duration, fps, width, height, zoom_factor):
    """捕获稳定缩放状态下的元素帧"""
    frames = []
    total_frames = int(duration * fps)

    try:
        # 获取当前变换以确定居中平移
        current_transform = driver.execute_script("return document.body.style.transform || '';")

        # 提取当前平移（如果有）
        translate_match = re.search(r"translate\(([-\d.]+)px, ([-\d.]+)px\)", current_transform)
        base_translate_x = float(translate_match.group(1)) if translate_match else 0
        base_translate_y = float(translate_match.group(2)) if translate_match else 0

        print(f"捕获稳定缩放: {total_frames} 帧，持续 {duration:.1f}秒")

        # 记录开始时间
        start_time = time.time()

        # 计算每帧的理想时间间隔
        frame_interval = duration / total_frames

        # 捕获完全缩放元素的帧，位置固定（无动画）
        for i in range(total_frames):
            # 计算当前帧应该在的时间点
            target_time = start_time + i * frame_interval

            # 保持位置固定，消除任何会导致抖动的"浮动"效果
            driver.execute_script(
                f"""
            document.body.style.transform = 'scale({zoom_factor}) translate({base_translate_x}px, {base_translate_y}px)';
            """
            )

            screenshot = driver.get_screenshot_as_png()
            nparr = np.frombuffer(screenshot, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            img = cv2.resize(img, (width, height))
            frames.append(img)

            # 计算需要等待的时间，确保按照精确间隔捕获帧
            current_time = time.time()
            wait_time = max(0.001, target_time - current_time)
            if wait_time > 0:
                time.sleep(wait_time)

    except Exception as e:
        print(f"稳定缩放期间出错: {e}")

    return frames


def capture_zoom_out(driver, element, duration, fps, width, height, zoom_factor):
    """捕获元素缩小过程的帧"""
    frames = []
    transition_frames = int(duration * fps)

    try:
        # 获取当前变换以确定居中平移
        current_transform = driver.execute_script("return document.body.style.transform || '';")

        # 提取当前平移（如果有）
        translate_match = re.search(r"translate\(([-\d.]+)px, ([-\d.]+)px\)", current_transform)
        initial_translate_x = float(translate_match.group(1)) if translate_match else 0
        initial_translate_y = float(translate_match.group(2)) if translate_match else 0

        # 开始缩小过渡，改进的缓动效果
        driver.execute_script(
            f"""
        document.body.style.transition = 'transform {duration}s cubic-bezier(0.34, 1.56, 0.64, 1)';
        """
        )

        print(f"捕获缩小动画: {transition_frames} 帧，持续 {duration:.1f}秒")

        # 从全缩放和当前平移开始
        driver.execute_script(
            f"""
        document.body.style.transform = 'scale({zoom_factor}) translate({initial_translate_x}px, {initial_translate_y}px)';
        """
        )

        # 记录开始时间
        start_time = time.time()

        # 计算每帧的理想时间间隔
        frame_interval = duration / transition_frames

        # 在缩小过程中捕获帧，使用手动缓动
        for i in range(transition_frames):
            # 计算当前帧应该在的时间点
            target_time = start_time + i * frame_interval

            progress = i / (transition_frames - 1) if transition_frames > 1 else 1.0  # 0到1

            # 应用缓动以实现更平滑的动画捕获
            eased_progress = ease_in_out_cubic(progress)

            # 计算当前缩放
            current_scale = zoom_factor - (zoom_factor - 1) * eased_progress

            # 计算当前平移（逐渐减少到0）
            current_translate_x = initial_translate_x * (1 - eased_progress)
            current_translate_y = initial_translate_y * (1 - eased_progress)

            # 应用当前缩放和平移
            driver.execute_script(
                f"""
            document.body.style.transform = 'scale({current_scale}) translate({current_translate_x}px, {current_translate_y}px)';
            """
            )

            # 捕获当前帧
            screenshot = driver.get_screenshot_as_png()
            nparr = np.frombuffer(screenshot, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            img = cv2.resize(img, (width, height))
            frames.append(img)

            # 计算需要等待的时间，确保按照精确间隔捕获帧
            current_time = time.time()
            wait_time = max(0.001, target_time - current_time)
            if wait_time > 0:
                time.sleep(wait_time)

        # 确保我们以精确的缩放比例1结束，没有平移
        driver.execute_script("document.body.style.transform = 'scale(1) translate(0px, 0px)';")

        # 清理样式
        driver.execute_script(
            """
        document.body.style.transformOrigin = '';
        document.body.style.transition = '';
        """
        )

        # 留出一点时间进行清理
        time.sleep(0.1)

    except Exception as e:
        print(f"缩小过程中出错: {e}")

    return frames 


def capture_scrolling(driver, duration, fps, width, height, smooth_factor, abstract_pause=1.0):
    """捕获页面平滑滚动过程中的帧"""
    # 计算要捕获的总帧数
    total_frames = int(duration * fps)
    frames = []

    # 获取可滚动的总高度
    scroll_height = get_scroll_height(driver)
    
    # 调整滚动速度 - 降低滚动距离和速度
    adjusted_scroll_height = scroll_height * 0.9  # 滚动页面的90%
    
    # 增加平滑系数，使滚动更加缓慢
    adjusted_smooth_factor = smooth_factor * 0.3  # 降低平滑系数，使滚动更慢

    # 计算每帧的滚动步长
    step = adjusted_scroll_height / total_frames

    # 当前滚动位置和速度
    position = 0
    velocity = 0

    print(f"滚动 {adjusted_scroll_height:.0f}像素，持续 {duration}秒，帧率 {fps}fps")

    # 计算每帧的实际捕获间隔，确保总时长符合预期
    frame_interval = duration / total_frames

    # 记录开始时间
    start_time = time.time()

    # 查找摘要元素
    abstract_element = None
    try:
        abstract_element = driver.find_element(By.CSS_SELECTOR, ".abstract")
    except:
        try:
            abstract_element = driver.find_element(By.CSS_SELECTOR, ".ltx_abstract")
        except:
            print("无法找到摘要元素")

    # 记录是否已经暂停过
    has_paused = False
    # 记录暂停后的新起始位置
    new_start_position = None
    # 记录暂停结束后的帧计数
    frames_after_pause = 0
    # 定义重启加速时间（帧数）
    restart_acceleration_frames = int(fps * 1.5)  # 1.5秒的加速时间

    for i in range(total_frames):
        # 计算当前帧应该在的时间点
        target_time = start_time + i * frame_interval

        # 如果已经暂停过，使用新的起始位置和渐进式加速
        if has_paused and new_start_position is not None:
            frames_after_pause = i - pause_frame
            
            # 计算渐进式加速系数，从0逐渐增加到1
            acceleration_factor = min(1.0, frames_after_pause / restart_acceleration_frames)
            
            # 使用渐进式速度计算下一个位置
            target = new_start_position + frames_after_pause * step * acceleration_factor
            
            # 使用更平滑的加速
            velocity += (target - position) * adjusted_smooth_factor * 0.3 * (0.2 + 0.8 * acceleration_factor)
            position += velocity
        else:
            # 使用原始速度计算
            target = i * step
            # 降低速度变化率，使滚动更加平滑
            velocity += (target - position) * adjusted_smooth_factor * 0.5
            position += velocity

        # 滚动到位置
        driver.execute_script(f"window.scrollTo(0, {position});")

        # 检查是否需要暂停在摘要处
        if abstract_element and not has_paused:
            try:
                # 获取摘要元素的位置
                abstract_rect = driver.execute_script(
                    """
                    var rect = arguments[0].getBoundingClientRect();
                    return {
                        top: rect.top,
                        bottom: rect.bottom
                    };
                    """,
                    abstract_element
                )
                
                # 如果摘要在视口顶部附近，则暂停
                if abstract_rect['top'] < 100 and abstract_rect['top'] > -100:  # 100px的容差
                    print(f"在摘要处暂停 {abstract_pause} 秒...")
                    # 记录当前帧号
                    pause_frame = i
                    # 记录暂停时的位置
                    pause_position = position
                    
                    # 捕获暂停期间的帧
                    pause_frames = int(abstract_pause * fps)
                    for _ in range(pause_frames):
                        screenshot = driver.get_screenshot_as_png()
                        nparr = np.frombuffer(screenshot, np.uint8)
                        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                        img = cv2.resize(img, (width, height))
                        frames.append(img)
                    
                    has_paused = True
                    # 更新开始时间以补偿暂停时间
                    start_time += abstract_pause
                    # 设置新的起始位置为当前暂停位置
                    new_start_position = pause_position
                    # 重置速度
                    velocity = 0
                    # 重置暂停后帧计数
                    frames_after_pause = 0
                    print("恢复滚动，开始缓慢加速...")
            except Exception as e:
                print(f"检查摘要位置时出错: {e}")

        # 捕获截图
        screenshot = driver.get_screenshot_as_png()
        nparr = np.frombuffer(screenshot, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        img = cv2.resize(img, (width, height))
        frames.append(img)

        # 计算需要等待的时间，确保按照精确间隔捕获帧
        current_time = time.time()
        wait_time = max(0.001, target_time - current_time)
        if wait_time > 0:
            time.sleep(wait_time)

        # 打印进度
        if i % fps == 0 or i == total_frames - 1:
            elapsed = time.time() - start_time
            print(f"已捕获 {i+1}/{total_frames} 帧 ({(i+1)/total_frames*100:.1f}%)，已用时间: {elapsed:.1f}秒")

    # 显示实际捕获时长
    total_time = time.time() - start_time
    print(f"总捕获时间: {total_time:.2f}秒，请求的持续时间: {duration:.2f}秒")

    return frames


def capture_arxiv_paper(driver, duration, fps, width, height, smooth_factor, title_focus, zoom_factor, abstract_pause=1.0):
    """捕获ArXiv论文页面，聚焦标题并滚动查看内容"""
    frames = []

    # 计算每个阶段的持续时间
    zoom_in_duration = min(2.0, duration * 0.1)  # 放大时间，最多2秒
    wait_duration = 1.0  # 等待时间
    
    # 剩余时间平均分配给3个高亮阶段
    remaining_duration = duration - zoom_in_duration - wait_duration
    highlight_duration = max(1.0, remaining_duration / 3)  # 每个元素的高亮时间
    
    # 打印时间分配信息
    total_calculated_time = zoom_in_duration + wait_duration + highlight_duration * 3
    print(f"时间分配: zoom_in={zoom_in_duration:.1f}s, 等待={wait_duration:.1f}s, 每个高亮={highlight_duration:.1f}s, 总计={total_calculated_time:.1f}s (请求={duration}s)")

    try:
        # 查找所有需要高亮的元素
        title_element = None
        authors_element = None
        abstract_element = None
        first_author_element = None

        # 查找标题元素
        try:
            print("查找标题元素...")
            title_element = driver.find_element(By.CSS_SELECTOR, "h1.title")
        except:
            try:
                title_element = driver.find_element(By.CSS_SELECTOR, ".title")
            except:
                try:
                    title_element = driver.find_element(By.XPATH, "//h1[contains(@class, 'title')] | //div[contains(@class, 'title')]")
                except Exception as e:
                    print(f"无法找到标题元素: {e}")

        # 查找第一个作者元素（用于zoom in的中心）
        try:
            print("查找第一个作者元素...")
            # 尝试不同的选择器来找到第一个作者元素
            try:
                first_author_element = driver.find_element(By.CSS_SELECTOR, ".authors a")
            except:
                try:
                    authors_container = driver.find_element(By.CSS_SELECTOR, ".authors")
                    author_links = authors_container.find_elements(By.TAG_NAME, "a")
                    if author_links and len(author_links) > 0:
                        first_author_element = author_links[0]
                    else:
                        first_author_element = authors_container
                except Exception as e2:
                    print(f"尝试备用作者选择器时出错: {e2}")
                    try:
                        first_author_element = driver.find_element(By.XPATH, "//div[contains(@class, 'authors')]//a[1] | //div[contains(@class, 'authors')]")
                    except Exception as e3:
                        print(f"尝试XPath作者选择器时出错: {e3}")
        except Exception as e:
            print(f"查找第一个作者元素时出错: {e}")

        # 查找作者容器元素
        try:
            print("查找作者容器元素...")
            authors_element = driver.find_element(By.CSS_SELECTOR, ".authors")
        except:
            try:
                authors_element = driver.find_element(By.CSS_SELECTOR, ".authors-list")
            except Exception as e:
                print(f"无法找到作者容器元素: {e}")

        # 查找摘要元素
        try:
            print("查找摘要元素...")
            abstract_element = driver.find_element(By.CSS_SELECTOR, ".abstract")
        except:
            try:
                abstract_element = driver.find_element(By.CSS_SELECTOR, ".ltx_abstract")
            except Exception as e:
                print(f"无法找到摘要元素: {e}")

        # 如果找不到任何元素，回退到普通滚动
        if not title_element and not authors_element and not abstract_element:
            print("找不到任何焦点元素，回退到普通滚动")
            scroll_frames = capture_scrolling(driver, duration, fps, width, height, smooth_factor, abstract_pause)
            frames.extend(scroll_frames)
            return frames

        # 跟踪实际使用的缩放系数
        actual_zoom_factor = zoom_factor

        # 使用第一个作者作为zoom in的中心，如果找不到则使用标题
        zoom_center_element = first_author_element if first_author_element else title_element

        # 1. 首先定位第一个作者并放大
        if zoom_center_element:
            print(f"聚焦第一个作者并放大 ({zoom_in_duration:.1f}秒)...")
            try:
                zoom_in_frames, actual_zoom_factor = capture_zoom_in(
                    driver, zoom_center_element, zoom_in_duration, fps, width, height, zoom_factor, "center"
                )
                frames.extend(zoom_in_frames)
            except Exception as e:
                print(f"放大失败: {e}")

        # 等待1秒后再开始高亮
        wait_duration = 1.0
        print(f"等待 {wait_duration:.1f} 秒后开始高亮...")
        wait_frames = capture_steady_zoom(
            driver, zoom_center_element, wait_duration, fps, width, height, actual_zoom_factor
        )
        frames.extend(wait_frames)

        # 2. 高亮标题
        if title_element:
            print(f"高亮标题 ({highlight_duration:.1f}秒)...")
            try:
                # 添加动效高亮，保持高亮状态
                driver.execute_script("""
                    var element = arguments[0];
                    element.style.transition = 'all 1.5s cubic-bezier(0.4, 0, 0.2, 1)';
                    element.style.backgroundColor = 'rgba(255, 255, 0, 0.7)';
                    element.style.transform = 'scale(1.05)';
                    element.style.boxShadow = '0 0 20px rgba(255, 255, 0, 0.5)';
                    element.style.borderRadius = '8px';
                    element.style.padding = '10px';
                    
                    // 添加晃动动画，只晃动2次，更慢
                    var keyframes = [
                        { backgroundColor: 'rgba(255, 255, 0, 0.7)', transform: 'scale(1.05)' },
                        { backgroundColor: 'rgba(255, 255, 0, 0.9)', transform: 'scale(1.08)' },
                        { backgroundColor: 'rgba(255, 255, 0, 0.7)', transform: 'scale(1.05)' }
                    ];
                    element.animate(keyframes, {
                        duration: 1800,
                        iterations: 2,
                        easing: 'ease-in-out'
                    });
                """, title_element)
                
                title_frames = capture_steady_zoom(
                    driver, title_element, highlight_duration, fps, width, height, actual_zoom_factor
                )
                frames.extend(title_frames)
                
                # 标题高亮保持不移除
            except Exception as e:
                print(f"标题处理失败: {e}")

        # 3. 在当前视野中高亮作者（不移动画面）
        if authors_element:
            print(f"高亮作者 ({highlight_duration:.1f}秒)...")
            try:
                # 添加2次动画效果的高亮
                driver.execute_script("""
                    var element = arguments[0];
                    element.style.transition = 'all 0.8s ease-in-out';
                    element.style.backgroundColor = 'rgba(0, 255, 0, 0.2)';
                    element.style.borderRadius = '3px';
                    element.style.padding = '3px';
                    
                    // 2次闪烁动画
                    var keyframes = [
                        { backgroundColor: 'rgba(0, 255, 0, 0.2)' },
                        { backgroundColor: 'rgba(0, 255, 0, 0.4)' },
                        { backgroundColor: 'rgba(0, 255, 0, 0.2)' }
                    ];
                    var animation = element.animate(keyframes, {
                        duration: 1000,
                        iterations: 2,
                        easing: 'ease-in-out'
                    });
                    
                    // 动画结束后消失
                    animation.onfinish = function() {
                        element.style.transition = 'all 1.2s ease-out';
                        element.style.backgroundColor = '';
                        element.style.borderRadius = '';
                        element.style.padding = '';
                    };
                """, authors_element)
                
                author_frames = capture_steady_zoom(
                    driver, zoom_center_element, highlight_duration, fps, width, height, actual_zoom_factor
                )
                frames.extend(author_frames)
            except Exception as e:
                print(f"作者处理失败: {e}")

        # 4. 摘要不需要高亮，只是停留一下
        if abstract_element:
            print(f"在摘要处停留 ({highlight_duration:.1f}秒)...")
            try:
                # 不添加任何高亮效果，只是停留
                abstract_frames = capture_steady_zoom(
                    driver, zoom_center_element, highlight_duration, fps, width, height, actual_zoom_factor
                )
                frames.extend(abstract_frames)
            except Exception as e:
                print(f"摘要处理失败: {e}")

        # 清理样式
        driver.execute_script("""
            document.body.style.transformOrigin = '';
            document.body.style.transition = '';
        """)

    except Exception as e:
        print(f"捕获帧过程中出错: {e}")
        import traceback
        traceback.print_exc()

    return frames


def create_video(frames, output_path, fps, width, height):
    """从捕获的帧创建视频文件"""
    # 确保目录存在
    output_dir = os.path.dirname(output_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 使用与捕获相同的帧率以保持原始时间
    output_fps = fps
    print(f"创建视频，帧率: {output_fps}")

    # 创建视频编码器
    fourcc = cv2.VideoWriter_fourcc(*'avc1')  # 使用H.264编码器
    out = cv2.VideoWriter(output_path, fourcc, output_fps, (width, height))

    # 将帧写入视频
    for frame in frames:
        out.write(frame)

    # 释放资源
    out.release()
    print(f"视频已保存到 {output_path} (时长: {len(frames)/output_fps:.1f}秒)")


def main():
    """主函数，运行脚本"""
    args = parse_arguments()

    # 设置WebDriver
    print("设置WebDriver...")
    driver = None
    try:
        driver = setup_driver(args.width, args.height)

        # 导航到URL
        print(f"加载 {args.url}...")
        driver.get(args.url)

        # 等待页面加载
        print("等待页面加载完成...")
        time.sleep(3)

        # 捕获帧，聚焦标题并滚动页面
        print(f"开始捕获，总时长 {args.duration} 秒，帧率 {args.fps} FPS...")
        frames = capture_arxiv_paper(
            driver,
            args.duration,
            args.fps,
            args.width,
            args.height,
            args.smooth_factor,
            args.title_focus,
            args.zoom_factor,
            args.abstract_pause
        )

        # 如果捕获到帧，创建视频
        if frames:
            print(f"创建视频（共 {len(frames)} 帧）...")
            create_video(frames, args.output_path, args.fps, args.width, args.height)
            print(f"视频已保存到 {args.output_path}")
        else:
            print("未能捕获任何帧，无法创建视频")

    except Exception as e:
        print(f"执行过程中出错: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 清理资源
        if driver:
            try:
                driver.quit()
                print("WebDriver已关闭")
            except:
                print("关闭WebDriver时出错")
        print("完成。")


if __name__ == "__main__":
    main() 