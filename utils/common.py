#!/usr/bin/env python3
"""
共用工具类和方法
包含MediaClassifier、Config、模型创建等共同功能
"""

import logging
import os
import re

import yaml
from camel.agents import ChatAgent
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.toolkits import MCPToolkit
from camel.types import ModelPlatformType

# 配置日志
from loguru import logger


# 1. 文件扩展名常量
class FileExtensions:
    """文件扩展名常量定义"""

    CODE = [".py", ".js", ".jsx", ".ts", ".tsx", ".java", ".go", ".rs", ".rb", ".php", ".c", ".cpp", ".h", ".hpp"]
    CONFIG = [".json", ".yaml", ".yml", ".toml", ".ini", ".xml"]
    DOC = [".md", ".txt", ".rst"]
    IMAGE = [".jpg", ".jpeg", ".png", ".gif", ".svg", ".webp"]
    VIDEO = [".mp4", ".mov", ".avi", ".webm", ".flv", ".wmv"]
    BINARY = [".zip", ".jar", ".exe", ".bin", ".pdf"] + IMAGE + VIDEO


# 2. 多媒体分类器 - 两个文件共用
class MediaClassifier:
    """多媒体分类器 - 简化为重要/不重要两级分类"""

    # 定义重要素材的关键词分类
    CRITICAL_KEYWORDS = {
        "architecture": [
            "架构",
            "architecture",
            "framework",
            "structure",
            "system",
            "design",
            "模块",
            "module",
            "组件",
            "component",
        ],
        "demo": ["demo", "演示", "example", "sample", "showcase", "tutorial", "使用", "usage", "操作", "operation"],
        "performance": [
            "测评",
            "evaluation",
            "performance",
            "benchmark",
            "result",
            "测试",
            "test",
            "效果",
            "effect",
            "对比",
            "comparison",
        ],
        "feature": ["功能", "feature", "capability", "特性", "亮点", "highlight", "核心", "core"],
        "workflow": ["流程", "workflow", "process", "步骤", "step", "pipeline", "工作流"],
        "ui": ["界面", "ui", "interface", "gui", "页面", "page", "显示", "display", "截图", "screenshot"],
    }

    # 需要跳过的关键词（徽章、标识等）
    SKIP_KEYWORDS = ["license", "logo", "badge", "icon", "star", "fork", "build", "coverage", "version"]

    @staticmethod
    def classify_importance(element_text: str, element_type: str = "image", context: str = "") -> tuple:
        """
        分类媒体重要性：重要(True)/不重要(False)

        Args:
            element_text: 元素的描述文本（filename、alt、url、内容等）
            element_type: 元素类型（image、video、code、table等）
            context: 上下文信息（可选）

        Returns:
            tuple: (is_important: bool, category: str, enhanced_description: str)
        """
        combined_text = f"{element_text} {context}".lower()

        # 优先处理视频内容 - 视频通常都很重要
        if element_type == "video" or any(element_text.lower().endswith(ext) for ext in FileExtensions.VIDEO):
            # 只跳过明显的徽章视频
            if any(keyword in combined_text for keyword in ["badge", "icon", "logo"]):
                return False, "skip", "跳过的徽章或标识"
            enhanced_desc = f"【演示视频】{element_text or 'video'}"
            return True, "demo", enhanced_desc

        # 跳过不重要的内容（但对视频更宽松）
        skip_matches = [keyword for keyword in MediaClassifier.SKIP_KEYWORDS if keyword in combined_text]
        if skip_matches:
            # 如果是star history这样的图表，但包含重要信息，仍然保留
            if "star" in combined_text and "history" in combined_text:
                enhanced_desc = f"【项目统计】{element_text or 'image'}"
                return True, "performance", enhanced_desc
            return False, "skip", "跳过的徽章或标识"

        # 检查是否包含重要关键词
        enhanced_desc = element_text or f"{element_type}素材"
        category = "other"

        for cat, keywords in MediaClassifier.CRITICAL_KEYWORDS.items():
            for keyword in keywords:
                if keyword in combined_text:
                    # 根据类别添加描述前缀
                    if cat == "architecture":
                        enhanced_desc = f"【架构图】{enhanced_desc}"
                        category = "architecture"
                    elif cat == "demo":
                        enhanced_desc = f"【演示Demo】{enhanced_desc}"
                        category = "demo"
                    elif cat == "performance":
                        enhanced_desc = f"【性能测评】{enhanced_desc}"
                        category = "performance"
                    elif cat == "workflow":
                        enhanced_desc = f"【工作流程】{enhanced_desc}"
                        category = "workflow"
                    elif cat == "feature":
                        enhanced_desc = f"【功能特性】{enhanced_desc}"
                        category = "feature"
                    elif cat == "ui":
                        enhanced_desc = f"【界面展示】{enhanced_desc}"
                        category = "ui"

                    return True, category, enhanced_desc

        # 特殊文件名模式检测
        filename_patterns = {
            r"(arch|framework|structure|design)": ("architecture", "【架构设计】"),
            r"(demo|example|tutorial|showcase)": ("demo", "【演示示例】"),
            r"(flow|workflow|process|pipeline)": ("workflow", "【流程图】"),
            r"(performance|benchmark|test|result)": ("performance", "【测试结果】"),
            r"(feature|function|capability)": ("feature", "【功能说明】"),
            r"(ui|interface|gui|screen)": ("ui", "【界面截图】"),
            r"(roadmap|plan|timeline)": ("workflow", "【发展路线图】"),
        }

        for pattern, (cat, prefix) in filename_patterns.items():
            if re.search(pattern, combined_text, re.IGNORECASE):
                enhanced_desc = f"{prefix}{enhanced_desc}"
                return True, cat, enhanced_desc

        # 视频和代码默认给予较高评级
        if element_type in ["video", "code"]:
            enhanced_desc = f"【{element_type}】{enhanced_desc}"
            return True, element_type, enhanced_desc

        # 对于图片，如果包含assets路径且不是logo，通常是重要的
        if "assets/" in element_text and not any(skip in element_text.lower() for skip in ["logo", "icon", "badge"]):
            # 尝试从文件名推断内容
            filename = element_text.lower()
            if "roadmap" in filename:
                enhanced_desc = f"【发展路线图】{enhanced_desc}"
                return True, "workflow", enhanced_desc
            elif "result" in filename:
                enhanced_desc = f"【测试结果】{enhanced_desc}"
                return True, "performance", enhanced_desc
            elif "architecture" in filename or "arch" in filename:
                enhanced_desc = f"【架构图】{enhanced_desc}"
                return True, "architecture", enhanced_desc
            else:
                # 默认assets下的图片都比较重要
                enhanced_desc = f"【项目素材】{enhanced_desc}"
                return True, "feature", enhanced_desc

        # 默认为不重要
        return False, "other", enhanced_desc


# 3. 配置管理 - 通用版本
class Config:
    """简化的配置管理 - 适用于多种代理"""

    def __init__(self, config_path: str = "config/config.yaml"):
        self.config = self._load_config(config_path)

    def _load_config(self, config_path: str) -> dict:
        """简化配置加载 - 失败时使用默认值"""
        try:
            with open(config_path, encoding="utf-8") as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            logger.warning(f"配置加载失败，使用默认值: {e}")
            return {}

    def get_model_config(self) -> dict:
        """获取模型配置"""
        return self.config.get("model", {})

    def get_github_config(self) -> dict:
        """获取GitHub配置"""
        github_config = self.config.get("material", {}).get("sources", {}).get("github", {})
        return {
            "url": github_config.get("url", "https://github.com/openai/codex"),
            "purpose": github_config.get("purpose", "分析GitHub项目"),
            "deep_analysis": github_config.get("analysis_config", {}).get("deep_analysis", True),
            "download_media": github_config.get("analysis_config", {}).get("download_media", True),
        }


# 4. 通用的模型和Agent创建工具
class AgentFactory:
    """AI代理工厂类"""

    @staticmethod
    def create_model(config: Config):
        """创建模型实例"""
        model_config = config.get_model_config()
        api_config = model_config.get("api", {})

        return ModelFactory.create(
            model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
            model_type=model_config.get("type", "openai/gpt-4o-mini"),
            api_key=api_config.get("openai_compatibility_api_key"),
            url=api_config.get("openai_compatibility_api_base_url"),
        )

    @staticmethod
    def create_analyzer_agent(
        model, role_name: str = "分析专家", content: str = "你负责分析和处理内容", mcp_toolkit: MCPToolkit = None
    ):
        """创建分析代理"""
        return ChatAgent(
            system_message=BaseMessage.make_assistant_message(role_name=role_name, content=content),
            model=model,
            tools=[*mcp_toolkit.get_tools()] if mcp_toolkit else [],
        )

    @staticmethod
    def create_reviewer_agent(
        model, role_name: str = "审查专家", content: str = "你负责审查和完善内容", mcp_toolkit: MCPToolkit = None
    ):
        """创建审查代理"""
        return ChatAgent(
            system_message=BaseMessage.make_assistant_message(role_name=role_name, content=content),
            model=model,
            tools=[*mcp_toolkit.get_tools()] if mcp_toolkit else [],
        )


# 5. 通用工具函数
class CommonUtils:
    """通用工具函数集合"""

    @staticmethod
    def setup_logging(level=logging.INFO):
        return logger

    @staticmethod
    def read_file(file_path: str) -> str:
        """安全读取文件内容"""
        try:
            with open(file_path, encoding="utf-8") as f:
                return f.read()
        except Exception as e:
            logger.error(f"读取文件失败 {file_path}: {e}")
            return ""

    @staticmethod
    def save_file(content: str, file_path: str) -> bool:
        """安全保存文件内容"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            logger.info(f"文件已保存至: {file_path}")
            return True
        except Exception as e:
            logger.error(f"保存文件失败 {file_path}: {e}")
            return False

    @staticmethod
    def detect_content_type(content: str) -> str:
        """检测内容类型"""
        github_indicators = ["github.com", "star", "fork", "repository", "开源项目"]
        if any(indicator in content.lower() for indicator in github_indicators):
            return "github_project"

        paper_indicators = ["abstract", "论文", "研究", "实验", "arxiv", "paper"]
        if any(indicator in content.lower() for indicator in paper_indicators):
            return "academic_paper"

        return "general"

    @staticmethod
    def extract_github_url_parts(url: str) -> tuple:
        """解析GitHub URL，返回(owner, repo)"""
        pattern = r"github\.com/([^/]+)/([^/]+)"
        match = re.search(pattern, url)
        if match:
            return match.group(1), match.group(2).replace(".git", "")
        raise ValueError(f"无效的GitHub URL: {url}")
