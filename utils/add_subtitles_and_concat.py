#!/usr/bin/env python3

import argparse
import logging
import os
import platform
import re
import subprocess
from pathlib import Path
from typing import Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


def add_subtitles_to_video(
    input_video_path: Path,
    srt_path: Path,
    output_dir: Path,
) -> Optional[Path]:
    """Add subtitles to a video file using ffmpeg.

    Args:
        input_video_path: Path to the input video file.
        srt_path: Path to the SRT subtitle file.
        output_dir: Directory to save the subtitled video.

    Returns:
        Path to the subtitled video file, or None if subtitles could not be added.
    """
    if not srt_path.exists():
        logger.warning(f"SRT file not found at {srt_path}. Skipping subtitle step for {input_video_path.name}.")
        return None  # Indicate SRT was missing

    output_subtitled_video_path = output_dir / f"{input_video_path.stem}_subtitled.mp4"

    # ffmpeg requires the srt path to be escaped for Windows
    srt_filter_path = str(srt_path.resolve()).replace("\\", "/")
    if platform.system() == "Windows":
        srt_filter_path = srt_filter_path.replace(":", "\\:")

    # Subtitle styling from process_storyboard.py
    # Alignment=2 is bottom center
    # MarginV=10 # Adjust vertical margin as needed
    subtitle_style = "Alignment=2,MarginV=10,Fontsize=16,FontColor=&HFFFFFF,FontName=微软雅黑"

    ffmpeg_command = [
        "ffmpeg",
        "-i",
        str(input_video_path.resolve()),
        "-vf",
        # f"subtitles='{srt_filter_path}':force_style='Alignment=2,MarginV=6,fontcolor=white,Fontsize=16,fontweight=bold,FontName=微软雅黑'",
        f"subtitles='{srt_filter_path}':force_style='{subtitle_style}'",
        "-c:a",
        "copy",  # Copy audio stream without re-encoding
        "-c:v",
        "libx264",  # Re-encode video
        "-crf",
        "23",  # Constant Rate Factor (lower means better quality, larger file size)
        "-preset",
        "medium",  # Encoding speed vs compression ratio
        str(output_subtitled_video_path.resolve()),
        "-y",  # Overwrite output file if it exists
    ]

    logger.info(f"Adding subtitles to {input_video_path.name}...")
    try:
        _ = subprocess.run(
            ffmpeg_command,
            check=True,
            capture_output=True,
            text=True,
        )
        logger.info(f"Subtitles added successfully to {output_subtitled_video_path.name}")
        return output_subtitled_video_path
    except subprocess.CalledProcessError as sub_cpe:
        logger.error(f"Failed to add subtitles to {input_video_path.name}. Error: {sub_cpe.stderr}")
        return None
    except FileNotFoundError:
        logger.error("ffmpeg command not found. Please ensure ffmpeg is installed and in your PATH.")
        return None


def concatenate_videos(
    video_paths: list[Path],
    output_path: Path,
) -> Optional[Path]:
    """Concatenate multiple video files into one using ffmpeg concat demuxer.

    Args:
        video_paths: A list of paths to the video files to concatenate, in order.
        output_path: The path for the final concatenated video file.

    Returns:
        The path to the final concatenated video file, or None if concatenation fails.
    """
    if not video_paths:
        logger.warning("No video paths provided for concatenation.")
        return None

    # Create a temporary file list for ffmpeg's concat demuxer
    list_file_path = output_path.parent / "mylist.txt"
    try:
        with open(list_file_path, "w", encoding="utf-8") as f:
            for video_path in video_paths:
                # Need to escape special characters for the file list
                safe_path = str(video_path.resolve()).replace("'", "'\\''")
                f.write(f"file '{safe_path}'\n")

        ffmpeg_concat_command = [
            "ffmpeg",
            "-f",
            "concat",
            "-safe",
            "0",  # Allow unsafe file paths (needed for absolute paths)
            "-i",
            str(list_file_path.resolve()),
            "-c",
            "copy",  # Copy streams without re-encoding
            str(output_path.resolve()),
            "-y",  # Overwrite output file if it exists
        ]

        logger.info(f"Concatenating {len(video_paths)} videos into {output_path.name}...")
        _ = subprocess.run(
            ffmpeg_concat_command,
            check=True,
            capture_output=True,
            text=True,
        )
        logger.success(f"Successfully concatenated videos into: {output_path}")  # type: ignore
        return output_path

    except subprocess.CalledProcessError as concat_cpe:
        logger.error(f"Failed to concatenate videos. Error: {concat_cpe.stderr}")
        return None
    except FileNotFoundError:
        logger.error("ffmpeg command not found. Please ensure ffmpeg is installed and in your PATH.")
        return None
    except Exception as e:
        logger.error(f"An unexpected error occurred during concatenation: {e}")
        return None
    finally:
        # Clean up the temporary list file
        if list_file_path.exists():
            try:
                os.remove(list_file_path)
                logger.info(f"Cleaned up temporary file: {list_file_path}")
            except OSError as e:
                logger.warning(f"Could not remove temporary file {list_file_path}: {e}")


def extract_storyboard_number(path: Path) -> int:
    """Extracts the storyboard number from a path.

    Assumes a pattern like '.../storyboard_X/...' or '.../Storyboard_X.mp4'.
    Returns -1 if no number is found.
    """
    # Match '/storyboard_NUMBER/' or 'Storyboard_NUMBER.mp4' (case-insensitive)
    match = re.search(r"[Ss]toryboard_(\d+)(?:/|\.mp4)", str(path))
    if match:
        return int(match.group(1))
    logger.warning(f"Could not extract storyboard number from path: {path}")
    return -1  # Or raise an error, or handle as needed


def main():
    parser = argparse.ArgumentParser(description="Add subtitles to videos and concatenate them.")
    parser.add_argument(
        "-i",
        "--input-dir",
        type=str,
        default="media/videos",
        help="Directory containing input video files (.mp4) and subtitle files (.srt).",
    )
    parser.add_argument(
        "-o",
        "--output-file",
        type=str,
        default="output/final_video.mp4",
        help="Path for the final concatenated output video file.",
    )
    parser.add_argument(
        "--temp-dir",
        type=str,
        default="output/temp_subtitled",
        help="Directory to store intermediate subtitled videos.",
    )

    args = parser.parse_args()

    input_dir = Path(args.input_dir).resolve()
    output_file = Path(args.output_file).resolve()
    temp_dir = Path(args.temp_dir).resolve()

    if not input_dir.is_dir():
        logger.error(f"Input directory not found: {input_dir}")
        return

    # Ensure output directories exist
    output_file.parent.mkdir(parents=True, exist_ok=True)
    temp_dir.mkdir(parents=True, exist_ok=True)

    # Use rglob for recursive search and then filter
    all_video_files = list(input_dir.rglob("*.mp4"))
    filtered_video_files = []
    for video_path in all_video_files:
        # Exclude files in 'partial_movie_files' directories
        if "partial_movie_files" in video_path.parts:
            continue
        # Exclude files ending with '_subtitle.mp4'
        if video_path.name.endswith("_subtitle.mp4"):
            continue
        filtered_video_files.append(video_path)

    if not filtered_video_files:
        logger.warning(f"No suitable .mp4 files found recursively in {input_dir} after filtering.")
        return

    # Sort files based on extracted storyboard number
    video_files = sorted(filtered_video_files, key=extract_storyboard_number)

    subtitled_video_paths: list[Path] = []
    failed_subtitles: list[Path] = []

    logger.info(f"Found {len(video_files)} suitable video files after filtering and sorting.")

    for video_path in video_files:
        # Check if the number extraction worked (optional, depends on handling in extract_storyboard_number)
        if extract_storyboard_number(video_path) == -1:
            logger.warning(f"Skipping {video_path.name} as storyboard number couldn't be determined for sorting.")
            failed_subtitles.append(video_path)
            continue

        srt_path = video_path.with_suffix(".srt")
        subtitled_path = add_subtitles_to_video(video_path, srt_path, temp_dir)

        if subtitled_path:
            subtitled_video_paths.append(subtitled_path)
        else:
            # Decide how to handle videos where subtitling failed or SRT was missing
            # Option 1: Skip the video entirely
            logger.warning(f"Skipping {video_path.name} due to missing SRT or subtitle error.")
            failed_subtitles.append(video_path)
            # Option 2: Include the original video (uncomment below)
            # logger.warning(f"Including original video {video_path.name} as subtitles failed or SRT was missing.")
            # subtitled_video_paths.append(video_path)

    if not subtitled_video_paths:
        logger.error("No videos were successfully subtitled. Cannot concatenate.")
        return

    final_video = concatenate_videos(subtitled_video_paths, output_file)

    if final_video:
        logger.info(f"Final video created successfully at: {final_video}")
        # Optional: Clean up intermediate files
        # logger.info(f"Cleaning up intermediate files in {temp_dir}...")
        # for temp_file in temp_dir.glob("*.mp4"):
        #     try:
        #         os.remove(temp_file)
        #     except OSError as e:
        #         logger.warning(f"Could not remove temp file {temp_file}: {e}")
        # try:
        #     os.rmdir(temp_dir) # Remove dir only if empty
        # except OSError as e:
        #     logger.warning(f"Could not remove temp directory {temp_dir}: {e}")
    else:
        logger.error("Failed to create the final concatenated video.")

    if failed_subtitles:
        logger.warning("The following videos were skipped due to subtitle issues:")
        for p in failed_subtitles:
            logger.warning(f" - {p.name}")


if __name__ == "__main__":
    # Add success level to logging
    logging.SUCCESS = 25  # type: ignore
    logging.addLevelName(logging.SUCCESS, "SUCCESS")  # type: ignore
    setattr(logger, "success", lambda message, *args: logger._log(logging.SUCCESS, message, args))  # type: ignore
    main()
