from camel.models import BaseModelBackend, ModelFactory
from camel.types import ModelPlatformType
from loguru import logger


def create_model(
    config_file="config/config.yaml", model_type: str = None, url: str = None, api: str = None
) -> BaseModelBackend:
    """根据配置创建模型实例"""
    import yaml

    with open(config_file) as f:
        config = yaml.safe_load(f)["model"]
    platform = config.get("platform", "openrouter")
    model_type_in_config = config.get("type", "google/gemini-2.5-flash-preview")

    api_config = config.get("api", {})

    use_cheap_model = api_config.get("use_cheap_model", False)
    if use_cheap_model:
        api_key = api_config.get("cheap_api_key")
        api_url = api_config.get("cheap_api_base_url")
    else:
        api_key = api_config.get("openrouter_api_key")
        api_url = api_config.get("openrouter_api_base_url")
    if model_type is None:
        model_type = model_type_in_config
    if api is not None:
        api_key = api
    if url is not None:
        api_url = url

    logger.info(f"Creating model {model_type} on {api_url}")
    return ModelFactory.create(
        model_platform=ModelPlatformType.from_name(platform),
        model_type=model_type,
        api_key=api_key,
        url=api_url,
    )


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="创建LLM模型实例")
    parser.add_argument("--config", default="config/config.yaml", help="配置文件路径")
    parser.add_argument("--model", help="模型类型")
    parser.add_argument("--url", help="API URL")
    parser.add_argument("--api", help="API密钥")
    parser.add_argument("--message", default="Hello world", help="测试消息")
    args = parser.parse_args()
    model = create_model(args.config, args.model, args.url, args.api)
    response = model.run([{"role": "user", "content": args.message}])
    print(response.choices[0].message.content)
