import re

import ffmpeg


def get_video_duration(file_path):
    probe = ffmpeg.probe(file_path)
    video_info = next(s for s in probe["streams"] if s["codec_type"] == "video")
    duration = float(video_info["duration"])
    return duration


def wrap_text_fill_lines_smart_v2(text, width=None, canvas_width=None, font_size=None):
    """智能文本换行函数

    Args:
        text: 要换行的文本
        width: 每行字符数限制(如不指定则由canvas_width计算)
        canvas_width: 画布宽度(像素)
        font_size: 字体大小(像素)
    """
    # 如果提供canvas参数,计算每行可容纳的字符数
    if canvas_width and font_size:
        # 假设英文字符宽度为font_size/2,中文字符为font_size
        width = int(canvas_width / (font_size / 2))

    if not width:
        width = 40  # 默认宽度

    # 定义字符类型
    def char_type(c):
        if "\u4e00" <= c <= "\u9fff":  # 中文字符
            return "cn", 2  # 宽度权重为2
        elif c.isspace():
            return "space", 1
        elif c.isalnum() or c in ",.!?":
            return "en", 1  # 英文和数字宽度权重为1
        else:
            return "other", 1

    # 将文本分割成字符列表,并计算每个字符的实际宽度
    chars = [(c, *char_type(c)) for c in text]

    lines = []
    current_line = []
    current_width = 0
    word_buffer = []  # 用于存储未完成的英文单词

    def flush_word_buffer():
        nonlocal current_line, current_width, word_buffer
        if not word_buffer:
            return
        # 如果当前行放不下完整单词
        if current_width + sum(c[2] for c in word_buffer) > width:
            if current_line:  # 当前行有内容,将单词移到下一行
                lines.append("".join(c[0] for c in current_line))
                current_line = []
                current_width = 0
            # 如果单词本身就超过一行
            if sum(c[2] for c in word_buffer) > width:
                # 在适当位置添加连字符分割
                while word_buffer:
                    line_chars = []
                    line_width = 0
                    while word_buffer and line_width + word_buffer[0][2] <= width - 1:  # 预留连字符位置
                        char = word_buffer.pop(0)
                        line_chars.append(char[0])
                        line_width += char[2]
                    if word_buffer:  # 需要断词
                        line_chars.append("-")
                    lines.append("".join(line_chars))
            else:
                current_line.extend(word_buffer)
                current_width = sum(c[2] for c in word_buffer)
        else:  # 当前行可以容纳完整单词
            current_line.extend(word_buffer)
            current_width += sum(c[2] for c in word_buffer)
        word_buffer.clear()

    for char, char_tp, width_weight in chars:
        if char_tp == "en":  # 英文字符,累积到word_buffer
            word_buffer.append((char, char_tp, width_weight))
        else:
            # 先处理之前累积的英文单词
            flush_word_buffer()

            # 处理当前字符
            if current_width + width_weight > width:
                if current_line:
                    lines.append("".join(c[0] for c in current_line))
                current_line = [(char, char_tp, width_weight)]
                current_width = width_weight
            else:
                current_line.append((char, char_tp, width_weight))
                current_width += width_weight

    # 处理剩余内容
    flush_word_buffer()
    if current_line:
        lines.append("".join(c[0] for c in current_line))

    return "\n".join(lines)


def _markdown_to_pango(md_text: str) -> str:
    """
    Converts a limited subset of Markdown to Pango markup for Manim's MarkupText.

    Supports:
    - Headers (#, ##, ###) -> <big>, <big><big>, <big><big><big> and bold
    - Bold (**text**) -> <b>text</b>
    - Italics (*text*) -> <i>text</i>
    - Bold + Italics (***text***) -> <b><i>text</i></b>
    - Unordered lists (- item) -> • item (bullet point) - processed line by line
    - Basic escaping of Pango special chars (&, <, >)

    Args:
        md_text: The input Markdown string.

    Returns:
        A string formatted with Pango markup tags.
    """
    # 1. Escape Pango special characters first
    pango_text = md_text.replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;")

    # Process line by line for block elements like headers and lists
    lines = pango_text.split("\n")
    processed_lines = []
    for line in lines:
        stripped_line = line.strip()

        # 2. Headers
        if stripped_line.startswith("###"):
            content = stripped_line[3:].strip()
            processed_lines.append(f"<big><big><big><b>{content}</b></big></big></big>")  # L/XL size + Bold
        elif stripped_line.startswith("##"):
            content = stripped_line[2:].strip()
            processed_lines.append(f"<big><big><b>{content}</b></big></big>")  # L/XL size + Bold
        elif stripped_line.startswith("#"):
            content = stripped_line[1:].strip()
            processed_lines.append(f"<big><b>{content}</b></big>")  # Large size + Bold
        # 3. Unordered lists
        elif stripped_line.startswith("- "):
            content = stripped_line[2:].strip()
            processed_lines.append(f"  • {content}")  # Indent + bullet
        else:
            processed_lines.append(line)  # Keep original line if no block match

    pango_text = "\n".join(processed_lines)

    # 4. Inline styles (process after blocks)
    # Bold + Italics (***text***) - Process first
    pango_text = re.sub(r"\*\*\*([^*\n]+?)\*\*\*", r"<b><i>\1</i></b>", pango_text)
    # Bold (**text**)
    pango_text = re.sub(r"\*\*([^*\n]+?)\*\*", r"<b>\1</b>", pango_text)
    # Italics (*text*)
    pango_text = re.sub(r"\*([^*\n]+?)\*", r"<i>\1</i>", pango_text)

    return pango_text
