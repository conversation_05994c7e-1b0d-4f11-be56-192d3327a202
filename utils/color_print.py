"""
彩色打印工具模块，提供多种颜色和样式的打印函数
"""

import sys
import time


# ANSI 颜色代码
class Colors:
    """颜色代码类"""

    # 前景色
    BLACK = "\033[30m"
    RED = "\033[31m"
    GREEN = "\033[32m"
    YELLOW = "\033[33m"
    BLUE = "\033[34m"
    MAGENTA = "\033[35m"
    CYAN = "\033[36m"
    WHITE = "\033[37m"

    # 背景色
    BG_BLACK = "\033[40m"
    BG_RED = "\033[41m"
    BG_GREEN = "\033[42m"
    BG_YELLOW = "\033[43m"
    BG_BLUE = "\033[44m"
    BG_MAGENTA = "\033[45m"
    BG_CYAN = "\033[46m"
    BG_WHITE = "\033[47m"

    # 样式
    BOLD = "\033[1m"
    UNDERLINE = "\033[4m"
    BLINK = "\033[5m"
    REVERSE = "\033[7m"

    # 重置
    RESET = "\033[0m"


def cprint(
    text: str, color: str = Colors.WHITE, style: str = "", end: str = "\n", file=None, typewriter_delay: float = 0
) -> None:
    """
    带颜色的打印函数，带有打字机效果

    Args:
        text: 要打印的文本
        color: 颜色代码
        style: 样式代码
        end: 结尾字符
        file: 输出流对象，默认为 sys.stdout
        typewriter_delay: 打字机效果的延迟时间（秒），设为0则禁用打字机效果
    """
    output_stream = file if file is not None else sys.stdout

    if typewriter_delay <= 0:
        # 直接打印，没有打字机效果
        print(f"{style}{color}{text}{Colors.RESET}", end=end, file=output_stream)
    else:
        # 打字机效果
        for char in text:
            print(f"{style}{color}{char}{Colors.RESET}", end="", flush=True, file=output_stream)
            time.sleep(typewriter_delay)
        print(end=end, file=output_stream)


def print_section(text: str, color: str = Colors.CYAN) -> None:
    """
    打印带分隔线的章节标题

    Args:
        text: 章节标题
        color: 颜色代码
    """
    width = 60
    print()
    cprint("=" * width, color)
    cprint(f"{text:^{width}}", color)
    cprint("=" * width, color)
    print()


def print_step(text: str, color: str = Colors.GREEN) -> None:
    """
    打印步骤信息

    Args:
        text: 步骤信息
        color: 颜色代码
    """
    cprint(f"→ {text}", color)


def print_info(text: str) -> None:
    """
    打印普通信息

    Args:
        text: 信息文本
    """
    cprint(f"ℹ {text}", Colors.BLUE)


def print_success(text: str) -> None:
    """
    打印成功信息

    Args:
        text: 成功信息
    """
    cprint(f"✓ {text}", Colors.GREEN)


def print_warning(text: str) -> None:
    """
    打印警告信息

    Args:
        text: 警告信息
    """
    cprint(f"⚠ {text}", Colors.YELLOW)


def print_error(text: str) -> None:
    """
    打印错误信息

    Args:
        text: 错误信息
    """
    cprint(f"✗ {text}", Colors.RED)


def print_debug(text: str) -> None:
    """
    打印调试信息

    Args:
        text: 调试信息
    """
    cprint(f"🔍 {text}", Colors.MAGENTA)


def print_title(text: str) -> None:
    """
    打印带样式的标题

    Args:
        text: 标题文本
    """
    cprint(f"\n{text}", Colors.BOLD + Colors.CYAN)
    cprint("─" * len(text), Colors.CYAN)
    print()
