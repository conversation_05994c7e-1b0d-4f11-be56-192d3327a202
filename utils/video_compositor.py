import argparse
import asyncio
import json
import os

import edge_tts
from moviepy import (
    AudioFileClip,
    CompositeAudioClip,
    CompositeVideoClip,
    ImageClip,
    TextClip,
    VideoFileClip,
    concatenate_videoclips,
)
from moviepy.video.fx.FadeIn import FadeIn
from moviepy.video.fx.FadeOut import FadeOut


async def text_to_speech(text, output_path, voice="zh-CN-YunxiNeural"):
    """
    将文本转换为语音并保存

    参数:
        text: 需要转换的文本
        output_path: 输出的音频文件路径
        voice: 使用的Edge TTS语音
    """
    communicate = edge_tts.Communicate(text, voice)
    await communicate.save(output_path)


def add_audio_to_video(video_path, audio_path, output_path):
    """
    将音频添加到视频中

    参数:
        video_path: 视频文件路径
        audio_path: 音频文件路径
        output_path: 输出视频文件路径
    """
    video_clip = VideoFileClip(video_path)
    audio_clip = AudioFileClip(audio_path)

    # 如果音频长度大于视频长度，则让视频停留在最后一帧以匹配音频长度
    if audio_clip.duration > video_clip.duration:
        # 获取需要延长的时间
        extend_duration = audio_clip.duration - video_clip.duration
        # 获取最后一帧（不是 duration，而是 duration - 1/fps）
        last_frame_time = video_clip.duration - 1.0 / video_clip.fps
        last_frame = video_clip.get_frame(last_frame_time)
        frozen_clip = ImageClip(last_frame).with_duration(extend_duration)
        # 将原视频和静止帧剪辑拼接
        video_clip = concatenate_videoclips([video_clip, frozen_clip])

    # 将音频添加到视频
    video_clip = video_clip.with_audio(audio_clip)

    # 保存视频
    video_clip.write_videofile(
        output_path,
        codec="libx264",
        fps=20,
        bitrate="5000k",
        audio_codec="aac",
        preset="ultrafast",
        temp_audiofile="temp-audio.m4a",
        threads=6,
        ffmpeg_params=[
            "-movflags",
            "+faststart",  # 流媒体优化
            "-crf",
            "23",  # 质量平衡值(18-28)
            "-pix_fmt",
            "yuv420p",  # 广泛兼容的像素格式
        ],
        logger=None,
        remove_temp=True,
    )

    return output_path


def create_transition(clip, transition_duration=0.2):
    """
    为视频片段添加淡入淡出效果

    参数:
        clip: 视频片段
        transition_duration: 转场效果持续时间
    """
    clip_out = FadeOut(transition_duration).apply(FadeIn(transition_duration).apply(clip))
    return clip_out

    # return clip.fx(FadeIn, transition_duration).fx(FadeOut, transition_duration)


def add_subtitle_to_video(
    video_clip,
    text,
    audio_path,
    font_size=24,
    font_color="black",
    stroke_color="white",
    stroke_width=1.5,
    y_position=50,
):
    """
    为视频添加字幕

    参数:
        video_clip: 视频片段
        text: 字幕文本
        audio_path: 音频文件路径
        font_size: 字体大小
        font_color: 字体颜色
        stroke_color: 描边颜色
        stroke_width: 描边宽度
        y_position: 字幕距离底部的距离（像素）
    """
    # 加载音频文件获取时长
    audio_clip = AudioFileClip(audio_path)
    audio_duration = audio_clip.duration
    audio_clip.close()

    # 根据标点符号分割文本，但不在字幕中显示标点
    sentences = []
    current_sentence = ""
    for char in text:
        if char not in "。，；：？！.,:;?!":
            current_sentence += char
        if char in "。，；：？！.,:;?!":
            if current_sentence:
                sentences.append(current_sentence)
                current_sentence = ""
    if current_sentence:
        sentences.append(current_sentence)

    # 计算每个句子的显示时间
    sentence_duration = audio_duration / len(sentences)

    # 创建字幕片段列表
    subtitle_clips = []
    current_time = 0

    for sentence in sentences:
        # 字幕分行处理，每行最多25个字符
        max_chars_per_line = 30
        lines = []
        current_line = ""

        for char in sentence:
            current_line += char
            if len(current_line) >= max_chars_per_line:
                lines.append(current_line)
                current_line = ""
        if current_line:
            lines.append(current_line)

        words = "\n".join(lines)

        # 创建字幕文本，使用更大的宽度
        txt_clip = TextClip(
            "Songti",
            text=words,
            font_size=int(font_size),
            color=font_color,
            stroke_color=stroke_color,
            size=(3800, None),  # 设置固定宽度，高度自动调整
            stroke_width=int(stroke_width),
            method="label",
        )

        # 设置字幕位置（底部居中）和持续时间
        video_width, video_height = video_clip.size

        def subtitle_position(t):
            return ("center", video_height - y_position)

        txt_clip = txt_clip.with_position(subtitle_position).with_duration(sentence_duration)

        # 设置字幕出现时间
        txt_clip = txt_clip.with_start(current_time)
        current_time += sentence_duration

        subtitle_clips.append(txt_clip)

    # 合成视频和字幕
    return CompositeVideoClip([video_clip] + subtitle_clips)


async def process_storyboard(storyboard_path, subtitle_options=None):
    """
    处理分镜文件，生成最终视频

    参数:
        storyboard_path: 分镜JSON文件路径
        subtitle_options: 字幕选项，格式为字典 {
            'font_size': 24,
            'font_color': 'black',
            'stroke_color': 'white',
            'stroke_width': 1.5,
            'y_position': 50
        }
    """
    # 设置默认字幕选项
    if subtitle_options is None:
        subtitle_options = {
            "font_size": 24,
            "font_color": "black",
            "stroke_color": "white",
            "stroke_width": 1.5,
            "y_position": 50,
        }

    # 读取分镜JSON文件
    with open(storyboard_path, encoding="utf-8") as f:
        storyboard = json.load(f)

    # 创建临时目录用于存储中间文件
    output_dir = os.path.dirname(storyboard_path)
    temp_dir = os.path.join(output_dir, "temp")
    os.makedirs(temp_dir, exist_ok=True)

    processed_clips = []

    # 处理每个分镜
    for i, scene in enumerate(storyboard):
        scene_name = scene["分镜名"]
        scene_content = scene["分镜内容"]
        scene_material = scene["素材路径"]

        print(f"处理分镜 {i+1}/{len(storyboard)}: {scene_name}")

        # 生成语音文件
        audio_path = os.path.join(temp_dir, f"audio_{i}.mp3")
        await text_to_speech(scene_content, audio_path)

        # 将语音添加到视频中
        processed_video_path = os.path.join(temp_dir, f"processed_{i}.mp4")
        add_audio_to_video(scene_material, audio_path, processed_video_path)

        # 加载处理后的视频片段
        video_clip = VideoFileClip(processed_video_path)

        # 统一视频分辨率
        target_size = (1920, 1080)  # 设置目标分辨率为1080p
        if video_clip.size != target_size:
            # 计算缩放比例，保持宽高比
            w, h = video_clip.size
            scale = min(target_size[0] / w, target_size[1] / h)
            new_size = (int(w * scale), int(h * scale))

            # 缩放视频
            video_clip = video_clip.resized(new_size)

            # 如果缩放后的视频小于目标尺寸，添加黑色背景
            # if new_size != target_size:
            #     # 创建黑色背景
            #     bg_clip = ColorClip(size=target_size, color=(0, 0, 0))
            #     # 将视频居中放置
            #     video_clip = video_clip.with_position('center')
            #     # 合成视频和背景
            #     video_clip = CompositeVideoClip([bg_clip, video_clip])

        # 添加字幕
        video_clip = add_subtitle_to_video(
            video_clip,
            scene_content,
            audio_path,
            font_size=subtitle_options["font_size"],
            font_color=subtitle_options["font_color"],
            stroke_color=subtitle_options["stroke_color"],
            stroke_width=subtitle_options["stroke_width"],
            y_position=subtitle_options["y_position"],
        )

        processed_clips.append(video_clip)

    print("clipe")
    for clipe in processed_clips:
        print(clipe)
    print("clipe")

    # 拼接所有视频片段
    final_clip = concatenate_videoclips(processed_clips, method="compose")

    # 添加背景音乐
    bgm_path = os.path.join(output_dir, "bgm.mp3")
    print("bgm_path:" + bgm_path)
    if os.path.exists(bgm_path):
        # 加载背景音乐
        bgm_clip = AudioFileClip(bgm_path)
        # 设置音量为0.5
        bgm_clip = bgm_clip.with_volume_scaled(0.1)

        # 如果背景音乐长度大于视频长度，则截取
        if bgm_clip.duration > final_clip.duration:
            bgm_clip = bgm_clip.subclipped(0, final_clip.duration)
        # 如果背景音乐长度小于视频长度，则循环播放
        else:
            bgm_clip = bgm_clip.loop(duration=final_clip.duration)

        # 将背景音乐添加到视频
        final_clip = final_clip.with_audio(CompositeAudioClip([final_clip.audio, bgm_clip]))

    # 输出最终视频
    output_filename = os.path.join(output_dir, "final_output.mp4")

    print("clipe-out")
    final_clip.write_videofile(
        output_filename,
        codec="libx264",
        fps=20,
        bitrate="5000k",
        audio_codec="aac",
        preset="ultrafast",
        temp_audiofile="temp-audio.m4a",
        threads=6,
        ffmpeg_params=[
            "-movflags",
            "+faststart",  # 流媒体优化
            "-crf",
            "23",  # 质量平衡值(18-28)
            "-pix_fmt",
            "yuv420p",  # 广泛兼容的像素格式
        ],
        remove_temp=True,
    )

    print("clipe-out 2")
    # 清理临时文件
    for clip in processed_clips:
        clip.close()
    final_clip.close()

    print(f"视频合成完成！输出文件: {output_filename}")
    return output_filename


async def main(storyboard_path, subtitle_options=None):
    """
    主函数

    参数:
        storyboard_path: 分镜JSON文件路径
        subtitle_options: 字幕选项，可选
    """
    return await process_storyboard(storyboard_path, subtitle_options)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="视频合成工具")
    parser.add_argument("storyboard_path", help="分镜文件路径")
    parser.add_argument("--font-size", type=int, default=50, help="字幕字体大小")
    parser.add_argument("--font-color", default="black", help="字幕颜色")
    parser.add_argument("--stroke-color", default="white", help="字幕描边颜色")
    parser.add_argument("--stroke-width", type=float, default=5, help="字幕描边宽度")
    parser.add_argument("--y-position", type=int, default=100, help="字幕距离底部的距离（像素）")

    args = parser.parse_args()

    subtitle_options = {
        "font_size": args.font_size,
        "font_color": args.font_color,
        "stroke_color": args.stroke_color,
        "stroke_width": args.stroke_width,
        "y_position": args.y_position,
    }

    asyncio.run(main(args.storyboard_path, subtitle_options))
