"""
彩色日志模块，提供便捷的日志记录功能
"""

import datetime
from typing import Optional, TextIO, Any
import sys
import os
from utils.color_print import (
    Colors, cprint, print_info, print_success, 
    print_warning, print_error, print_debug, 
    print_section, print_title
)

# 日志级别常量
DEBUG = 10
INFO = 20
SUCCESS = 25  # 自定义级别
WARNING = 30
ERROR = 40
CRITICAL = 50

# 日志级别名称映射
LEVEL_NAMES = {
    DEBUG: "DEBUG",
    INFO: "INFO",
    SUCCESS: "SUCCESS",
    WARNING: "WARNING",
    ERROR: "ERROR",
    CRITICAL: "CRITICAL"
}

# 日志级别颜色映射
LEVEL_COLORS = {
    DEBUG: Colors.MAGENTA,
    INFO: Colors.BLUE,
    SUCCESS: Colors.GREEN,
    WARNING: Colors.YELLOW,
    ERROR: Colors.RED,
    CRITICAL: Colors.RED + Colors.BOLD
}

class ColoredLogger:
    """
    彩色日志记录器
    """
    
    def __init__(
        self, 
        name: str = "",
        level: int = INFO,
        output: Optional[TextIO] = sys.stdout,
        file_path: Optional[str] = None,
        show_time: bool = True
    ):
        """
        初始化日志记录器
        
        Args:
            name: 日志记录器名称
            level: 日志级别
            output: 输出流，默认为标准输出
            file_path: 日志文件路径，如果提供则同时输出到文件
            show_time: 是否显示时间戳
        """
        self.name = name
        self.level = level
        self.output = output
        self.file = None
        self.show_time = show_time
        
        # 如果提供了文件路径，创建或打开日志文件
        if file_path:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            self.file = open(file_path, "a", encoding="utf-8")
    
    def _log(self, level: int, msg: str, *args, **kwargs) -> None:
        """
        记录日志的内部方法
        
        Args:
            level: 日志级别
            msg: 日志消息
            *args: 格式化参数
            **kwargs: 关键字参数
        """
        if level < self.level:
            return
            
        # 格式化消息
        if args or kwargs:
            formatted_msg = msg.format(*args, **kwargs)
        else:
            formatted_msg = msg
            
        # 创建日志前缀
        prefix = ""
        if self.name:
            prefix += f"[{self.name}] "
            
        if self.show_time:
            now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            prefix += f"[{now}] "
            
        level_name = LEVEL_NAMES.get(level, "UNKNOWN")
        prefix += f"[{level_name}] "
        
        # 完整日志消息
        full_msg = f"{prefix}{formatted_msg}"
        
        # 输出到控制台，使用颜色
        if self.output:
            color = LEVEL_COLORS.get(level, Colors.WHITE)
            style = Colors.BOLD if level >= CRITICAL else ""
            cprint(full_msg, color, style, file=self.output)
            
        # 输出到文件（无颜色）
        if self.file:
            self.file.write(full_msg + "\n")
            self.file.flush()
    
    def debug(self, msg: str, *args, **kwargs) -> None:
        """记录调试信息"""
        self._log(DEBUG, msg, *args, **kwargs)
    
    def info(self, msg: str, *args, **kwargs) -> None:
        """记录一般信息"""
        self._log(INFO, msg, *args, **kwargs)
    
    def success(self, msg: str, *args, **kwargs) -> None:
        """记录成功信息"""
        self._log(SUCCESS, msg, *args, **kwargs)
    
    def warning(self, msg: str, *args, **kwargs) -> None:
        """记录警告信息"""
        self._log(WARNING, msg, *args, **kwargs)
    
    def error(self, msg: str, *args, **kwargs) -> None:
        """记录错误信息"""
        self._log(ERROR, msg, *args, **kwargs)
    
    def critical(self, msg: str, *args, **kwargs) -> None:
        """记录严重错误信息"""
        self._log(CRITICAL, msg, *args, **kwargs)
    
    def section(self, title: str) -> None:
        """打印日志章节标题"""
        if INFO < self.level:
            return
        print_section(title)
        
        if self.file:
            width = 60
            separator = "=" * width
            centered_title = f"{title:^{width}}"
            self.file.write(f"\n{separator}\n{centered_title}\n{separator}\n\n")
            self.file.flush()
    
    def title(self, title: str) -> None:
        """打印日志子标题"""
        if INFO < self.level:
            return
        print_title(title)
        
        if self.file:
            self.file.write(f"\n{title}\n{'─' * len(title)}\n\n")
            self.file.flush()
    
    def close(self) -> None:
        """关闭日志文件"""
        if self.file:
            self.file.close()
            self.file = None
    
    def __del__(self) -> None:
        """析构函数，确保关闭文件"""
        self.close()


# 创建默认日志记录器
default_logger = ColoredLogger(name="App")

# 导出默认日志记录器的方法
debug = default_logger.debug
info = default_logger.info
success = default_logger.success
warning = default_logger.warning
error = default_logger.error
critical = default_logger.critical
section = default_logger.section
title = default_logger.title


def get_logger(name: str, **kwargs: Any) -> ColoredLogger:
    """
    获取指定名称的日志记录器
    
    Args:
        name: 日志记录器名称
        **kwargs: 其他参数，将传递给 ColoredLogger 初始化
        
    Returns:
        ColoredLogger 实例
    """
    return ColoredLogger(name=name, **kwargs) 