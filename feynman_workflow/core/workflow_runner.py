"""
工作流运行器
"""

import os
import time

from loguru import logger

from ..core.performance_monitor import timing_context
from ..utils.command_runner import CommandRunner


class WorkflowRunner:
    """工作流运行器"""

    def __init__(self):
        self.command_runner = CommandRunner()

    def run_chat_mode(self, args):
        """运行chat模式：用户输入主题，直接通过material_agent_refactored生成素材，然后生成视频"""
        logger.info("===== Chat模式启动 =====")

        from ..core.config_manager import ConfigManager

        config_manager = ConfigManager()

        # 加载配置以获取chat设置
        config = config_manager.load_config(args.config)
        material_config = config.get("material", {})
        sources_config = material_config.get("sources", {})
        chat_config = sources_config.get("chat", {})

        # 获取用户输入的主题
        purpose = self._get_chat_purpose(args, chat_config)
        if not purpose:
            return

        logger.info(f"开始处理主题: {purpose}")

        # 生成项目名称（使用时间戳）
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        project_name = f"chat_{timestamp}"

        logger.info(f"生成项目名称: {project_name}")

        # 创建输出目录
        from ..utils.file_utils import FileUtils

        output_dir = FileUtils.ensure_output_directory(project_name)

        # 构建素材扩充配置
        enhancement_config = config_manager.build_enhancement_config(args)

        # 显示启用的扩充功能
        self._show_enabled_features(config, enhancement_config)

        # 执行Chat模式工作流
        success = self._run_chat_workflow(project_name, purpose, output_dir, enhancement_config, args)

        if success:
            self._show_completion_message(project_name, output_dir)
        else:
            logger.error("Chat模式工作流执行失败")

    def run_common_workflow(self, project_name, analysis_file, purpose, output_dir, enhancement_config, args=None):
        """执行通用工作流步骤（生成素材、故事板、视频）- 使用重构后的代理"""

        # 主线步骤二: 结合输入材料生成项目讲解素材，使用重构后的material_agent
        with timing_context("步骤2-生成项目介绍素材"):
            intro_md_path = f"{output_dir}/{project_name}_intro.md"
            if os.path.exists(intro_md_path):
                logger.info(f"项目介绍文件已存在: {intro_md_path}，跳过步骤2")
            else:
                if not self._generate_intro_material(analysis_file, purpose, intro_md_path, enhancement_config):
                    return False

        # 主线步骤三: 生成动画分镜故事板storyboard - 使用重构后的DSL代理
        with timing_context("步骤3-生成动画脚本故事板"):
            storyboard_path = f"{output_dir}/{project_name}_intro_storyboard.json"
            if os.path.exists(storyboard_path):
                logger.info(f"故事板文件已存在: {storyboard_path}，跳过步骤3")
            else:
                if not self._generate_storyboard(intro_md_path, purpose, storyboard_path):
                    return False

        # 主线步骤四: 用manim渲染最终视频
        with timing_context("步骤4-渲染最终视频"):
            video_output_dir = f"output/{project_name}/videos"
            if os.path.exists(video_output_dir) and len(os.listdir(video_output_dir)) > 0:
                logger.info(f"视频文件目录已存在且不为空: {video_output_dir}，跳过步骤4")
            else:
                if not self._render_video(storyboard_path, project_name, args):
                    return False

        logger.info("工作流成功完成！")
        logger.info(f"输出视频应该位于: {video_output_dir}")
        return True

    def _get_chat_purpose(self, args, chat_config):
        """获取Chat模式的主题"""
        if args.purpose:
            purpose = args.purpose
            logger.info(f"使用命令行参数中的主题: {purpose}")
        else:
            # 尝试从配置文件获取主题
            config_purpose = chat_config.get("purpose", "")
            if config_purpose:
                purpose = config_purpose
                logger.info(f"使用配置文件中的主题: {purpose}")
            else:
                # 交互式获取主题
                print("\n欢迎使用Chat模式！")
                print("请输入您想了解的主题，我将为您生成详细的解释视频。")
                print("例如：深度学习、区块链技术、量子计算等")
                purpose = input("\n请输入主题: ").strip()

                if not purpose:
                    logger.error("未输入有效主题，退出chat模式")
                    return None
        return purpose

    def _show_enabled_features(self, config, enhancement_config):
        """显示启用的扩充功能"""
        config_material = config.get("material", {})
        material_enhance = config_material.get("material_enhance", {})

        enabled_features = []
        # 优先显示命令行覆盖的配置，其次显示config配置
        if enhancement_config:
            if enhancement_config.get("video_recording"):
                enabled_features.append("录屏视频")
            if enhancement_config.get("image_generation"):
                enabled_features.append("图片生成")
            if enhancement_config.get("audio_synthesis"):
                enabled_features.append("音频合成")
        else:
            if material_enhance.get("screen_record"):
                enabled_features.append("录屏视频")
            if material_enhance.get("image_generation"):
                enabled_features.append("图片生成")
            if material_enhance.get("audio_synthesis"):
                enabled_features.append("音频合成")

        if enabled_features:
            logger.info(f"🎬 已启用扩充: {', '.join(enabled_features)}")
        else:
            logger.info("🎬 未启用任何扩充功能")

    def _run_chat_workflow(self, project_name, purpose, output_dir, enhancement_config, args):
        """执行Chat模式工作流"""
        # 步骤1: 直接使用material_agent_refactored生成项目介绍素材（Chat模式）
        with timing_context("步骤1-Chat模式生成项目介绍素材"):
            intro_md_path = f"{output_dir}/{project_name}_intro.md"
            if os.path.exists(intro_md_path):
                logger.info(f"项目介绍文件已存在: {intro_md_path}，跳过步骤1")
            else:
                if not self._generate_chat_intro_material(purpose, intro_md_path, enhancement_config, args):
                    return False

        logger.info(f"素材内容已生成: {intro_md_path}")

        # 步骤2: 生成动画脚本故事板
        with timing_context("步骤2-生成动画脚本故事板"):
            storyboard_path = f"{output_dir}/{project_name}_intro_storyboard.json"
            if os.path.exists(storyboard_path):
                logger.info(f"故事板文件已存在: {storyboard_path}，跳过步骤2")
            else:
                if not self._generate_storyboard(intro_md_path, purpose, storyboard_path):
                    return False

        # 步骤3: 渲染最终视频
        with timing_context("步骤3-渲染最终视频"):
            video_output_dir = f"output/{project_name}/videos"
            if os.path.exists(video_output_dir) and len(os.listdir(video_output_dir)) > 0:
                logger.info(f"视频文件目录已存在且不为空: {video_output_dir}，跳过步骤3")
            else:
                if not self._render_video(storyboard_path, project_name, args):
                    return False

        return True

    def _generate_intro_material(self, analysis_file, purpose, intro_md_path, enhancement_config):
        """生成项目介绍素材"""
        # 构建扩充选项（仅在有覆盖配置时添加）
        enhancement_str = ""
        if enhancement_config:
            enhancement_options = []
            if enhancement_config.get("video_recording"):
                enhancement_options.append("--enable-video")
            if enhancement_config.get("image_generation"):
                enhancement_options.append("--enable-image")
            if enhancement_config.get("audio_synthesis"):
                enhancement_options.append("--enable-audio")
            if not enhancement_config.get("video_recording"):
                enhancement_options.append("--disable-video")
            enhancement_str = " ".join(enhancement_options)

        step2_cmd = (
            f"python -m agents.material_agent_refactored "
            f"--material {analysis_file} "
            f'--purpose "{purpose}" '
            f"--output {intro_md_path} "
            f"{enhancement_str}"
        ).strip()

        if not self.command_runner.run_command(step2_cmd, "使用重构代理生成项目介绍素材"):
            logger.error("步骤2失败，终止工作流")
            return False

        if not os.path.exists(intro_md_path):
            logger.error(f"项目介绍文件未生成: {intro_md_path}")
            return False

        return True

    def _generate_chat_intro_material(self, purpose, intro_md_path, enhancement_config, args):
        """生成Chat模式的项目介绍素材"""
        # 构建扩充选项 - 安全访问enhancement_config
        enhancement_options = []
        if enhancement_config and enhancement_config.get("video_recording"):
            enhancement_options.append("--enable-video")
        if enhancement_config and enhancement_config.get("image_generation"):
            enhancement_options.append("--enable-image")
        if enhancement_config and enhancement_config.get("audio_synthesis"):
            enhancement_options.append("--enable-audio")
        if args.disable_video:
            enhancement_options.append("--disable-video")

        enhancement_str = " ".join(enhancement_options)

        # Chat模式：不提供材料文件，让agent知道这是chat模式
        step1_cmd = (
            f"python -m agents.material_agent_refactored "
            f'--purpose "{purpose}" '
            f"--output {intro_md_path} "
            f"{enhancement_str}"
        )

        if not self.command_runner.run_command(step1_cmd, "Chat模式生成项目介绍素材"):
            logger.error("步骤1失败，终止chat模式工作流")
            return False

        if not os.path.exists(intro_md_path):
            logger.error(f"项目介绍文件未生成: {intro_md_path}")
            return False

        return True

    def _generate_storyboard(self, intro_md_path, purpose, storyboard_path):
        """生成动画脚本故事板"""
        step3_cmd = (
            f"python -m agents.generate_manim_dsl_agent_refactored "
            f"--markdown {intro_md_path} "
            f'--purpose "{purpose}" '
            f"--output {storyboard_path}"
        )

        if not self.command_runner.run_command(step3_cmd, "生成动画脚本故事板", stage_name="Storyboard:Generation"):
            logger.error("故事板生成失败，终止工作流")
            return False

        if not os.path.exists(storyboard_path):
            logger.error(f"故事板文件未生成: {storyboard_path}")
            return False

        return True

    def _render_video(self, storyboard_path, project_name, args=None):
        """渲染最终视频"""
        try:
            # 导入重构后的处理器
            from process_storyboard_module.config import StoryboardConfig
            from process_storyboard_module.main_processor import StoryboardProcessor

            # 从命令行参数获取配置，如果没有则使用默认值
            quality = getattr(args, "quality", "h") if args else "h"
            max_workers = getattr(args, "max_workers", 6) if args else 6
            stages = (
                getattr(args, "stages", ["dsl", "code", "render", "subtitles", "concat"])
                if args
                else ["dsl", "code", "render", "subtitles", "concat"]
            )
            enable_transition = getattr(args, "enable_transition", False) if args else False

            # 构建配置
            storyboard_config = StoryboardConfig(
                storyboard_file=storyboard_path,
                output_dir=f"output/{project_name}",
                project_name=project_name,
                quality=quality,
                max_workers=max_workers,
                stages=stages,
                transition_enabled=enable_transition,
                transition_run_time=1.0,
                transition_type="transformation",
            )

            # 创建处理器并执行
            processor = StoryboardProcessor(storyboard_config)
            result = processor.process_all()

            # 清理资源
            processor.cleanup()

            # 检查处理结果
            if result.failure_count == result.total_entries or result.success_count == 0:
                logger.error(f"视频渲染失败: 成功{result.success_count}/{result.total_entries}条目")
                return False

            logger.info(f"视频渲染成功: {result.final_video_path}")
            logger.info(
                f"处理统计: 成功{result.success_count}/{result.total_entries}条目, 成功率{result.success_rate:.1%}"
            )
            return True

        except Exception as e:
            logger.error(f"视频渲染失败: {e}")
            return False

    def _show_completion_message(self, project_name, output_dir):
        """显示完成消息"""
        logger.info("Chat模式工作流成功完成！")
        print("\n🎉 视频生成完成！")
        print(f"📁 输出目录: {output_dir}")
        print(f"📄 素材内容: {output_dir}/{project_name}_intro.md")
        video_output_dir = f"output/{project_name}/videos"
        if os.path.exists(video_output_dir):
            print(f"🎬 视频文件: {video_output_dir}")
        else:
            print("❌ 视频生成失败，请检查日志文件")
