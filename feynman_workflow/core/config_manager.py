"""
配置管理模块
"""

import sys

import yaml
from loguru import logger

from ..core.performance_monitor import timing_decorator


class ConfigManager:
    """配置管理器"""

    @timing_decorator("配置文件加载")
    def load_config(self, config_path):
        """加载配置文件"""
        logger.info(f"正在加载配置文件: {config_path}")
        try:
            with open(config_path, encoding="utf-8") as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
            sys.exit(1)

    def get_active_material_source(self, config, specified_source=None):
        """获取活跃的材料源配置"""
        material_config = config.get("material", {})
        sources = material_config.get("sources", {})

        if specified_source:
            # 如果指定了材料源，检查该源是否存在配置
            if specified_source not in sources:
                logger.error(f"配置文件中未找到指定的材料源: {specified_source}")
                sys.exit(1)
            source_config = sources[specified_source]
            source_config["type"] = specified_source
            return source_config

        # 自动检测启用的材料源
        active_sources = []
        for source_type, source_config in sources.items():
            if source_config.get("enabled", False):
                source_config["type"] = source_type
                active_sources.append(source_config)

        if len(active_sources) == 0:
            logger.error("配置文件中没有启用任何材料源")
            sys.exit(1)
        elif len(active_sources) > 1:
            logger.warning("配置文件中启用了多个材料源，只使用第一个")
            enabled_types = [s["type"] for s in active_sources]
            logger.warning(f"启用的材料源: {enabled_types}")

        return active_sources[0]

    def get_purpose_from_config(self, config, source_config, args_purpose=None):
        """从配置文件或命令行参数获取视频目的"""
        if args_purpose:
            return args_purpose

        # 优先使用材料源特定的purpose
        if source_config.get("purpose"):
            return source_config["purpose"]

        # 回退到全局默认purpose
        material_config = config.get("material", {})
        return material_config.get("default_purpose", "提供简单易懂的内容分析，面向一般受众，以客观中立的风格呈现")

    def build_enhancement_config(self, args):
        """构建素材扩充配置 - 仅作为命令行覆盖用"""
        # 只有明确指定命令行参数时才返回覆盖配置
        if args.enable_video or args.disable_video or args.enable_image or args.enable_audio:
            return {
                "video_recording": (args.enable_video or not args.disable_video) and not args.disable_video,
                "image_generation": args.enable_image,
                "audio_synthesis": args.enable_audio,
            }
        return None  # 使用config默认配置
