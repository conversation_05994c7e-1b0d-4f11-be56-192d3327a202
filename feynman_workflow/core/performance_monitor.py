"""
性能监控模块 - 重构版
支持按类别分组和生成结构化报告
"""

import functools
import time
from collections import defaultdict
from contextlib import contextmanager

from loguru import logger

from ..constants import PERFORMANCE_LOG_FORMAT, PERFORMANCE_SUMMARY_SEPARATOR


class PerformanceMonitor:
    """性能监控器"""

    def __init__(self):
        self.stats = defaultdict(list)
        self.start_time = None
        self.stage_history = []  # 记录执行历史

    def start_workflow(self):
        """开始工作流计时"""
        self.start_time = time.time()
        self.stats.clear()
        self.stage_history.clear()
        logger.info("🚀 工作流性能监控已启动")

    def record_stage(self, stage_name, duration):
        """
        记录阶段耗时
        stage_name: 阶段名称，建议使用 'Category:Action' 格式
        """
        self.stats[stage_name].append(duration)
        self.stage_history.append({"stage": stage_name, "duration": duration, "timestamp": time.time()})
        logger.info(PERFORMANCE_LOG_FORMAT.format(stage=stage_name, duration=duration))

    def get_total_time(self):
        """获取总耗时"""
        if self.start_time is None:
            return 0
        return time.time() - self.start_time

    def get_stage_stats(self, stage_name):
        """获取特定阶段的统计信息"""
        times = self.stats.get(stage_name, [])
        if not times:
            return None

        total = sum(times)
        count = len(times)
        average = total / count
        return {
            "count": count,
            "total": total,
            "average": average,
            "min": min(times),
            "max": max(times),
            "std": (sum((t - average) ** 2 for t in times) / count) ** 0.5 if count > 1 else 0,
        }

    def export_stats_csv(self, filename):
        """导出统计数据为CSV，增加Category列"""
        try:
            import csv

            with open(filename, "w", newline="", encoding="utf-8") as f:
                writer = csv.writer(f)
                writer.writerow(
                    ["类别", "操作", "执行次数", "总耗时(秒)", "平均耗时(秒)", "最小耗时(秒)", "最大耗时(秒)"]
                )

                for stage_name, times in self.stats.items():
                    parts = stage_name.split(":", 1)
                    category = parts[0] if len(parts) > 1 else "General"
                    action = parts[1] if len(parts) > 1 else stage_name

                    stats = self.get_stage_stats(stage_name)
                    if stats:
                        writer.writerow(
                            [
                                category,
                                action,
                                stats["count"],
                                f"{stats['total']:.2f}",
                                f"{stats['average']:.2f}",
                                f"{stats['min']:.2f}",
                                f"{stats['max']:.2f}",
                            ]
                        )
            logger.info(f"📊 性能统计已导出到: {filename}")
        except Exception as e:
            logger.error(f"导出统计数据失败: {e}")

    def log_summary(self):
        """输出结构化的性能统计摘要"""
        if not self.stats:
            logger.info("📊 暂无性能统计数据")
            return

        logger.info(PERFORMANCE_SUMMARY_SEPARATOR)
        logger.info("📊 性能统计摘要")
        logger.info(PERFORMANCE_SUMMARY_SEPARATOR)

        # 按类别分组
        categorized_stats = defaultdict(lambda: {"total_time": 0.0, "stages": defaultdict(list)})
        overall_total_time = sum(sum(times) for times in self.stats.values())

        for stage_name, times in self.stats.items():
            parts = stage_name.split(":", 1)
            category = parts[0] if len(parts) > 1 else "General"
            action = parts[1] if len(parts) > 1 else stage_name
            stage_total = sum(times)
            categorized_stats[category]["stages"][action].extend(times)
            categorized_stats[category]["total_time"] += stage_total

        # 按大类总耗时排序
        sorted_categories = sorted(categorized_stats.items(), key=lambda item: item[1]["total_time"], reverse=True)

        # 逐个打印大类
        for category, data in sorted_categories:
            category_total_time = data["total_time"]
            category_percentage = (category_total_time / overall_total_time * 100) if overall_total_time > 0 else 0
            logger.info(f"📁 {category} (总耗时: {category_total_time:.2f}s, 占比: {category_percentage:.1f}%)")

            # 按子项耗时排序
            sorted_stages = sorted(data["stages"].items(), key=lambda item: sum(item[1]), reverse=True)

            for action, times in sorted_stages:
                stats = self.get_stage_stats(f"{category}:{action}" if category != "General" else action)
                stage_percentage = (stats["total"] / category_total_time * 100) if category_total_time > 0 else 0

                log_msg = (
                    f"  - {action:<40} "
                    f"| 总耗时: {stats['total']:>7.2f}s ({stage_percentage:>4.1f}%) "
                    f"| 次数: {stats['count']:>4} "
                    f"| 平均: {stats['average']:>6.2f}s"
                )
                logger.info(log_msg)
            logger.info("")

        # 总结信息
        workflow_total = self.get_total_time()
        logger.info(PERFORMANCE_SUMMARY_SEPARATOR)
        logger.info(f"⏱️  记录的操作总耗时: {overall_total_time:.2f}秒")
        logger.info(f"🕐 工作流总耗时: {workflow_total:.2f}秒")

        if workflow_total > 0:
            efficiency = (overall_total_time / workflow_total) * 100
            logger.info(f"📈 监控覆盖率: {efficiency:.1f}%")
            if efficiency < 80:
                logger.warning("💡 提示: 监控覆盖率较低，可能存在未被追踪的耗时操作。")

        # 性能瓶颈建议
        if sorted_categories:
            top_category_name, top_category_data = sorted_categories[0]
            top_category_total = top_category_data["total_time"]
            bottleneck_percentage = (top_category_total / overall_total_time * 100) if overall_total_time > 0 else 0

            if bottleneck_percentage > 40:
                logger.info(
                    f"🚨 性能瓶颈: '{top_category_name}' 分类占用 {bottleneck_percentage:.1f}% 的时间，建议优先优化。"
                )

        logger.info(PERFORMANCE_SUMMARY_SEPARATOR)


# 全局性能监控实例
performance_monitor = PerformanceMonitor()


def timing_decorator(stage_name):
    """装饰器：监控函数执行时间。stage_name建议使用 'Category:Action' 格式"""

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            # logger.info(f"⏱️  开始: {stage_name}")
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                performance_monitor.record_stage(stage_name, duration)
                # logger.info(f"✅ 完成: {stage_name} ({duration:.2f}s)")

        return wrapper

    return decorator


@contextmanager
def timing_context(stage_name):
    """上下文管理器：监控代码块执行时间。stage_name建议使用 'Category:Action' 格式"""
    start_time = time.time()
    # logger.info(f"⏱️  开始: {stage_name}")
    try:
        yield
    finally:
        duration = time.time() - start_time
        performance_monitor.record_stage(stage_name, duration)
        # logger.info(f"✅ 完成: {stage_name} ({duration:.2f}s)")
