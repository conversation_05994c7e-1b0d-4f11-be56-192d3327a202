#!/usr/bin/env python3
"""
重构后的自动工作流脚本 - 主入口

该脚本执行以下步骤：
1. 从各种材料源开始解析内容（GitHub、PDF、网页、本地文件等）
2. 生成项目/论文介绍素材（支持录屏等扩充功能）
3. 生成动画脚本故事板
4. 渲染最终视频

用法：
python feynman_workflow/main.py [选项]
"""

import os
import sys

import click
from loguru import logger

# 添加当前目录的父目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from feynman_workflow.constants import CHAT_MODE_DIRECT, SUPPORTED_SOURCE_TYPES
from feynman_workflow.core import ConfigManager, WorkflowRunner, performance_monitor, timing_context
from feynman_workflow.material_sources import MaterialSourceFactory
from feynman_workflow.utils import FileUtils


def validate_source_config(source_config):
    """验证材料源配置"""
    source_type = source_config["type"]

    if source_type == "github":
        if not source_config.get("url"):
            logger.error("GitHub源配置缺少url字段")
            return False
    elif source_type == "pdf":
        if not source_config.get("url"):
            logger.error("PDF源配置缺少url字段")
            return False
    elif source_type == "webpage":
        if not source_config.get("url"):
            logger.error("网页源配置缺少url字段")
            return False
    elif source_type == "local_file":
        if not source_config.get("path"):
            logger.error("本地文件源配置缺少path字段")
            return False
        import os

        if not os.path.exists(source_config["path"]):
            logger.error(f"本地文件不存在: {source_config['path']}")
            return False
    elif source_type == "chat":
        if not source_config.get("purpose"):
            logger.error("Chat源配置缺少purpose字段")
            return False
    else:
        logger.error(f"不支持的材料源类型: {source_type}")
        return False

    return True


@click.command()
@click.option("--config", default="config/config.yaml", help="配置文件路径")
@click.option(
    "--source",
    type=click.Choice(SUPPORTED_SOURCE_TYPES),
    help="指定材料源类型 (如果不指定，将根据配置文件自动判断)",
)
@click.option("--purpose", help="视频目的描述 (覆盖配置文件中的设置)")
@click.option("--chat", is_flag=True, help="启用chat模式，通过用户输入的purpose生成视频")
# 素材扩充选项
@click.option("--enable-video", is_flag=True, help="强制启用录屏扩充")
@click.option("--disable-video", is_flag=True, help="禁用录屏扩充")
@click.option("--enable-image", is_flag=True, help="启用图片生成扩充")
@click.option("--enable-audio", is_flag=True, help="启用音频合成扩充")
# 视频渲染相关参数
@click.option(
    "--quality", type=click.Choice(["l", "m", "h", "k"]), default="h", help="Manim渲染质量: l(低), m(中), h(高), k(4K)"
)
@click.option("--max-workers", type=int, default=6, help="最大并发worker数量")
@click.option("--stages", default="dsl,code,render,subtitles,concat", help="要执行的处理阶段，用逗号分隔")
@click.option("--enable-transition", default=True, is_flag=True, help="启用转场视频生成")
def main(
    config,
    source,
    purpose,
    chat,
    enable_video,
    disable_video,
    enable_image,
    enable_audio,
    quality,
    max_workers,
    stages,
    enable_transition,
):
    """主函数，执行完整工作流"""
    # 开始性能监控
    performance_monitor.start_workflow()

    try:
        with timing_context("Init:Workflow"):
            # 创建模拟args对象以兼容现有代码
            class Args:
                def __init__(self):
                    self.config = config
                    self.source = source
                    self.purpose = purpose
                    self.chat = chat
                    self.enable_video = enable_video
                    self.disable_video = disable_video
                    self.enable_image = enable_image
                    self.enable_audio = enable_audio
                    # 新增的渲染参数
                    self.quality = quality
                    self.max_workers = max_workers
                    self.stages = stages.split(",") if stages else []
                    self.enable_transition = enable_transition

            args = Args()
            config_manager = ConfigManager()
            workflow_runner = WorkflowRunner()

            # 检查是否为chat模式
            if args.chat or args.source == "chat":
                logger.info("启动chat模式")
                workflow_runner.run_chat_mode(args)
                # 在这里结束前记录性能
                performance_monitor.log_summary()
                return

            # 加载配置
            config_data = config_manager.load_config(args.config)

            # 获取活跃的材料源
            source_config = config_manager.get_active_material_source(config_data, args.source)
            logger.info(f"使用材料源: {source_config['type']}")

            # 验证材料源配置
            if not validate_source_config(source_config):
                logger.error("材料源配置验证失败")
                sys.exit(1)

            # 提取项目名
            project_name = FileUtils.extract_project_name(source_config)
            logger.info(f"项目名称: {project_name}")

            # 创建输出目录
            output_dir = FileUtils.ensure_output_directory(project_name)

            # 获取视频目的
            purpose = config_manager.get_purpose_from_config(config_data, source_config, args.purpose)
            logger.info(f"视频目的: {purpose}")

            # 构建素材扩充配置
            enhancement_config = config_manager.build_enhancement_config(args)

            # 显示启用的扩充功能
            workflow_runner._show_enabled_features(config_data, enhancement_config)

        # 主线步骤一：处理材料源生成md
        source_processor = MaterialSourceFactory.create_source(source_config, project_name, output_dir)
        if not source_processor:
            logger.error("无法创建材料源处理器，终止工作流")
            sys.exit(1)

        analysis_file = source_processor.run()
        if not analysis_file:
            logger.error("材料源处理失败，终止工作流")
            sys.exit(1)

        # 检查是否为chat源的特殊返回值
        if analysis_file == CHAT_MODE_DIRECT:
            logger.info("检测到Chat源，切换到Chat模式流程")
            # 获取chat配置中的purpose
            chat_purpose = source_config.get("purpose", "")
            if not chat_purpose:
                logger.error("Chat源配置缺少purpose字段")
                sys.exit(1)

            # 构建完整的视频purpose描述
            video_purpose = f"{chat_purpose}"

            # 构建扩充选项
            enhancement_options = []
            if enhancement_config and enhancement_config.get("video_recording"):
                enhancement_options.append("--enable-video")
            if enhancement_config and enhancement_config.get("image_generation"):
                enhancement_options.append("--enable-image")
            if enhancement_config and enhancement_config.get("audio_synthesis"):
                enhancement_options.append("--enable-audio")
            if args.disable_video:
                enhancement_options.append("--disable-video")

            enhancement_str = " ".join(enhancement_options)

            # 步骤1: 直接使用material_agent_refactored生成项目介绍素材（Chat模式）
            intro_md_path = f"{output_dir}/{project_name}_intro.md"
            import os

            if os.path.exists(intro_md_path):
                logger.info(f"项目介绍文件已存在: {intro_md_path}，跳过步骤1")
            else:
                step1_cmd = (
                    f"python -m agents.material_agent_refactored "
                    f'--purpose "{video_purpose}" '
                    f"--output {intro_md_path} "
                    f"{enhancement_str}"
                )

                if not workflow_runner.command_runner.run_command(
                    step1_cmd, "生成项目介绍素材(Chat模式)", "MaterialGeneration:Chat"
                ):
                    logger.error("步骤1失败，终止工作流")
                    sys.exit(1)

                if not os.path.exists(intro_md_path):
                    logger.error(f"项目介绍文件未生成: {intro_md_path}")
                    sys.exit(1)

            # 将intro_md_path设为analysis_file，这样就可以继续使用通用工作流
            analysis_file = intro_md_path
            purpose = video_purpose

        # 执行通用工作流步骤
        success = workflow_runner.run_common_workflow(
            project_name, analysis_file, purpose, output_dir, enhancement_config, args
        )

        if success:
            stats_file = f"output/{project_name}/performance_stats.csv"
            performance_monitor.export_stats_csv(stats_file)
            logger.info(f"📊 性能报告已生成: {stats_file}")
        else:
            logger.error("工作流执行失败")

    except Exception as e:
        logger.exception(f"工作流执行过程中发生严重错误: {str(e)}")
        sys.exit(1)
    finally:
        # 确保在任何情况下都输出性能摘要
        performance_monitor.log_summary()


if __name__ == "__main__":
    main()
