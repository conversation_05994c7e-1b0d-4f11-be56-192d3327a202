"""
文件操作工具
"""

import os
import re
import time
from urllib.parse import urlparse


class FileUtils:
    """文件操作工具类"""

    @staticmethod
    def ensure_output_directory(project_name):
        """确保输出目录存在"""
        output_dir = f"output/{project_name}"
        os.makedirs(output_dir, exist_ok=True)
        return output_dir

    @staticmethod
    def extract_project_name(source_config):
        """从源配置中提取项目名称"""
        source_type = source_config.get("type", "")

        if source_type == "github":
            url = source_config.get("url", "")
            if url:
                # 从GitHub URL提取项目名
                match = re.search(r"github\.com/([\w\-]+)/([\w\-]+)", url)
                if match:
                    return match.group(2)
            return "github_project"

        elif source_type == "pdf":
            url = source_config.get("url", "")
            if url and "arxiv.org" in url:
                # 提取arxiv论文ID作为项目名
                match = re.search(r"(\d{4}\.\d{5})", url)
                if match:
                    return match.group(1)
            return "pdf_content"

        elif source_type == "webpage":
            url = source_config.get("url", "")
            if url:
                # 从网页URL提取项目名
                parsed_url = urlparse(url)
                hostname = parsed_url.hostname or "webpage"
                path = parsed_url.path.strip("/").replace("/", "_") or "content"
                return f"{hostname}_{path}"[:50]  # 限制长度
            return "webpage_content"

        elif source_type == "local_file":
            path = source_config.get("path", "")
            if path:
                # 从本地文件路径提取项目名
                filename = os.path.basename(path)
                return os.path.splitext(filename)[0] or "local_content"
            return "local_content"

        elif source_type == "chat":
            # Chat模式使用时间戳作为项目名
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            return f"chat_{timestamp}"

        else:
            return "unknown_project"

    @staticmethod
    def get_file_extension(file_path):
        """获取文件扩展名"""
        return os.path.splitext(file_path)[1].lower()

    @staticmethod
    def is_file_type(file_path, extensions):
        """检查文件是否为指定类型"""
        ext = FileUtils.get_file_extension(file_path)
        return ext in extensions
