"""
命令执行工具
"""

import subprocess
import threading
import time

from loguru import logger

from ..core.performance_monitor import performance_monitor


class CommandRunner:
    """命令执行器"""

    def run_command(self, cmd, desc, stage_name=None, verbose=True):
        """运行命令并实时显示日志输出，添加耗时监控"""
        start_time = time.time()
        logger.info(f"🚀 开始执行: {desc}")
        logger.info("=" * 60)
        if verbose:
            logger.info(f"执行命令: {cmd}")

        try:
            process = subprocess.Popen(
                cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, bufsize=1
            )

            def handle_output(stream, is_error=False):
                for line in stream:
                    line = line.rstrip()
                    if not line or "DEBUG" in line:
                        continue

                    # 统一使用info级别，避免日志级别混乱
                    logger.info(line)

            stdout_thread = threading.Thread(target=handle_output, args=(process.stdout, False))
            stderr_thread = threading.Thread(target=handle_output, args=(process.stderr, True))

            stdout_thread.start()
            stderr_thread.start()

            stdout_thread.join()
            stderr_thread.join()

            return_code = process.wait()
            duration = time.time() - start_time

            # 记录性能统计
            if stage_name:
                performance_monitor.record_stage(stage_name, duration)
            else:
                # 提供一个默认的分类
                performance_monitor.record_stage(f"Command:{desc}", duration)

            logger.info("=" * 60)
            logger.info(f"✅ 执行完成: {desc}")

            if return_code == 0:
                return True
            else:
                logger.error(f"命令执行失败，返回码: {return_code}")
                return False
        except Exception as e:
            duration = time.time() - start_time
            # 异常情况也记录性能
            if stage_name:
                performance_monitor.record_stage(f"{stage_name}(异常)", duration)
            else:
                performance_monitor.record_stage(f"Command:{desc}(异常)", duration)
            logger.error(f"命令执行异常: {str(e)}")
            logger.info("=" * 60)
            return False
