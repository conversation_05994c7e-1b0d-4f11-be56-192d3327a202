"""
本地文件材料源处理
"""

import os

from loguru import logger

from ..core.performance_monitor import timing_decorator
from .base_source import BaseMaterialSource


class LocalFileSource(BaseMaterialSource):
    """本地文件材料源处理器"""

    def validate_config(self):
        """验证本地文件源配置"""
        file_path = self.source_config.get("path")
        if not file_path:
            logger.error("本地文件源配置缺少path字段")
            return False

        if not os.path.exists(file_path):
            logger.error(f"指定的文件不存在: {file_path}")
            return False

        return True

    def get_analysis_file_path(self):
        """获取分析文件路径"""
        return f"{self.output_dir}/{self.project_name}.md"

    @timing_decorator("SourceProcessing:LocalFile")
    def process_source(self):
        """处理本地文件源"""
        file_path = self.source_config["path"]
        analysis_file = self.get_analysis_file_path()

        try:
            # 获取文件扩展名
            file_ext = os.path.splitext(file_path)[1].lower()

            if file_ext == ".md":
                # 如果是markdown文件，直接复制内容
                content = self._process_markdown_file(file_path)
            elif file_ext in [".txt", ".py", ".js", ".json", ".yaml", ".yml"]:
                # 处理文本文件
                content = self._process_text_file(file_path)
            else:
                logger.warning(f"不支持的文件类型: {file_ext}，尝试作为文本文件处理")
                content = self._process_text_file(file_path)

            # 写入分析文件
            with open(analysis_file, "w", encoding="utf-8") as f:
                f.write(content)

            logger.info(f"本地文件源处理完成: {analysis_file}")
            return analysis_file

        except Exception as e:
            logger.error(f"本地文件源处理失败: {e}")
            return None

    def _process_markdown_file(self, file_path):
        """处理markdown文件"""
        with open(file_path, encoding="utf-8") as f:
            content = f.read()

        # 添加原始文件路径信息
        header = f"**原始文件路径**: {file_path}\n\n"
        return header + content

    def _process_text_file(self, file_path):
        """处理文本文件"""
        with open(file_path, encoding="utf-8") as f:
            content = f.read()

        # 获取文件名和扩展名
        file_name = os.path.basename(file_path)
        file_ext = os.path.splitext(file_path)[1].lower()

        # 生成markdown格式
        markdown_content = f"""**原始文件路径**: {file_path}

# {file_name}

```{file_ext[1:] if file_ext else 'text'}
{content}
```
"""
        return markdown_content
