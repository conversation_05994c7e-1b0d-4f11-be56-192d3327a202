"""
Chat模式材料源处理
"""

from loguru import logger

from ..constants import CHAT_MODE_DIRECT
from .base_source import BaseMaterialSource


class ChatSource(BaseMaterialSource):
    """Chat模式材料源处理器"""

    def validate_config(self):
        """验证Chat源配置"""
        if not self.source_config.get("purpose"):
            logger.error("Chat源配置缺少purpose字段")
            return False
        return True

    def get_analysis_file_path(self):
        """Chat模式不需要分析文件"""
        return None

    def process_source(self):
        """处理chat源 - 不生成中间文件，返回特殊标识让后续流程知道这是chat模式"""
        purpose = self.source_config.get("purpose", "")
        if not purpose:
            logger.error("Chat源配置缺少purpose字段")
            return None

        logger.info(f"Chat源检测到主题: {purpose}")
        logger.info("Chat模式将跳过中间文件生成，直接进入material_agent_refactored流程")

        # 返回特殊标识，告诉调用者这是chat模式
        return CHAT_MODE_DIRECT
