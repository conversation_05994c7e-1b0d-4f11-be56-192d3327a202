"""
GitHub材料源处理
"""

import os

from loguru import logger

from ..utils.command_runner import CommandRunner
from .base_source import BaseMaterialSource


class GitHubSource(BaseMaterialSource):
    """GitHub材料源处理器"""

    def __init__(self, source_config, project_name, output_dir):
        super().__init__(source_config, project_name, output_dir)
        self.command_runner = CommandRunner()

    def validate_config(self):
        """验证GitHub源配置"""
        if not self.source_config.get("url"):
            logger.error("GitHub源配置缺少url字段")
            return False
        return True

    def get_analysis_file_path(self):
        """获取分析文件路径"""
        return f"{self.output_dir}/project_analysis.md"

    def process_source(self):
        """处理GitHub源 - 使用重构后的代理"""
        analysis_file = self.get_analysis_file_path()

        # 使用重构后的GitHub代理
        github_url = self.source_config["url"]
        step1_cmd = f"python -m agents.github_source_agent_refactored --repo {github_url}"

        if not self.command_runner.run_command(step1_cmd, "分析GitHub项目", stage_name="SourceProcessing:GitHub"):
            logger.error("GitHub源处理失败")
            return None

        if not os.path.exists(analysis_file):
            logger.error(f"GitHub分析文件未生成: {analysis_file}")
            return None

        return analysis_file
