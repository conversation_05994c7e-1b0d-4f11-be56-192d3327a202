"""
材料源处理基类
"""

import os
from abc import ABC, abstractmethod

from loguru import logger


class BaseMaterialSource(ABC):
    """材料源处理基类"""

    def __init__(self, source_config, project_name, output_dir):
        self.source_config = source_config
        self.project_name = project_name
        self.output_dir = output_dir
        self.source_type = source_config.get("type", "")

    @abstractmethod
    def validate_config(self):
        """验证配置"""
        pass

    @abstractmethod
    def get_analysis_file_path(self):
        """获取分析文件路径"""
        pass

    @abstractmethod
    def process_source(self):
        """处理材料源"""
        pass

    def is_processed(self):
        """检查是否已经处理过"""
        analysis_file = self.get_analysis_file_path()
        if os.path.exists(analysis_file):
            logger.info(f"分析文件已存在: {analysis_file}，跳过处理")
            return True
        return False

    def run(self):
        """运行材料源处理"""
        # 验证配置
        if not self.validate_config():
            return None

        # 检查是否已处理
        if self.is_processed():
            return self.get_analysis_file_path()

        # 处理材料源
        return self.process_source()
