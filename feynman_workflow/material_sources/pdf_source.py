"""
PDF材料源处理
"""

import os

from loguru import logger

from ..utils.command_runner import CommandRunner
from .base_source import BaseMaterialSource


class PDFSource(BaseMaterialSource):
    """PDF材料源处理器"""

    def __init__(self, source_config, project_name, output_dir):
        super().__init__(source_config, project_name, output_dir)
        self.command_runner = CommandRunner()

    def validate_config(self):
        """验证PDF源配置"""
        if not self.source_config.get("url"):
            logger.error("PDF源配置缺少url字段")
            return False
        return True

    def get_analysis_file_path(self):
        """获取分析文件路径"""
        return f"{self.output_dir}/{self.project_name}.md"

    def process_source(self):
        """处理PDF源"""
        pdf_url = self.source_config["url"]
        analysis_file = self.get_analysis_file_path()

        # 检查文件是否存在并添加URL信息
        if os.path.exists(analysis_file):
            self._add_url_to_existing_file(analysis_file, pdf_url)
            return analysis_file

        # 使用PDF工具包处理
        step1_cmd = f"python -c \"from tools.pdf_toolkit import PDFToolkit; result = PDFToolkit().extract_pdf('{pdf_url}', 'output'); print(f'PDF处理完成: {{result.get(\"markdown_file\", \"未知\")}}')\""

        if not self.command_runner.run_command(step1_cmd, "下载并解析PDF文件", stage_name="SourceProcessing:PDF"):
            logger.error("PDF源处理失败")
            return None

        if not os.path.exists(analysis_file):
            logger.error(f"PDF分析文件未生成: {analysis_file}")
            return None

        # 在生成的markdown文件中添加原始PDF URL
        self._add_url_to_file(analysis_file, pdf_url)
        return analysis_file

    def _add_url_to_existing_file(self, analysis_file, pdf_url):
        """为已存在的文件添加URL信息"""
        try:
            with open(analysis_file, encoding="utf-8") as f:
                content = f.read()
            if pdf_url not in content:
                logger.info("为已存在的文件添加原始PDF URL信息")
                url_header = f"**原始PDF链接**: {pdf_url}\n\n"
                with open(analysis_file, "w", encoding="utf-8") as f:
                    f.write(url_header + content)
        except Exception as e:
            logger.warning(f"无法更新PDF文件URL信息: {e}")

    def _add_url_to_file(self, analysis_file, pdf_url):
        """在文件中添加URL信息"""
        try:
            with open(analysis_file, encoding="utf-8") as f:
                content = f.read()

            # 在文件开头添加URL信息，确保录屏功能可以检测到
            url_header = f"**原始PDF链接**: {pdf_url}\n\n"

            with open(analysis_file, "w", encoding="utf-8") as f:
                f.write(url_header + content)

            logger.info(f"已在PDF分析文件中添加原始URL: {pdf_url}")

        except Exception as e:
            logger.error(f"无法在PDF文件中添加URL信息: {e}")
