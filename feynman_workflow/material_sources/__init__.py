"""
材料源工厂类
"""

from loguru import logger

from .chat_source import ChatSource
from .github_source import GitHubSource
from .local_file_source import LocalFileSource
from .pdf_source import PDFSource
from .webpage_source import WebpageSource


class MaterialSourceFactory:
    """材料源工厂"""

    # 材料源映射
    SOURCE_MAPPING = {
        "github": GitHubSource,
        "pdf": PDFSource,
        "chat": ChatSource,
        "webpage": WebpageSource,
        "local_file": LocalFileSource,
    }

    @classmethod
    def create_source(cls, source_config, project_name, output_dir):
        """创建材料源处理器"""
        source_type = source_config.get("type", "")

        if source_type not in cls.SOURCE_MAPPING:
            logger.error(f"不支持的材料源类型: {source_type}")
            logger.info(f"支持的类型: {list(cls.SOURCE_MAPPING.keys())}")
            return None

        source_class = cls.SOURCE_MAPPING[source_type]
        return source_class(source_config, project_name, output_dir)

    @classmethod
    def get_supported_types(cls):
        """获取支持的材料源类型"""
        return list(cls.SOURCE_MAPPING.keys())
