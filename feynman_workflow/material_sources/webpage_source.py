"""
网页材料源处理
"""

import os
import urllib.parse
from pathlib import Path

import requests
from bs4 import BeautifulSoup
from loguru import logger
from markdownify import markdownify

from ..core.performance_monitor import timing_decorator
from .base_source import BaseMaterialSource


class WebpageSource(BaseMaterialSource):
    """网页材料源处理器"""

    def __init__(self, source_config, project_name, output_dir):
        super().__init__(source_config, project_name, output_dir)
        self.user_agent = (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
            "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )

    def validate_config(self):
        """验证网页源配置"""
        if not self.source_config.get("url"):
            logger.error("网页源配置缺少url字段")
            return False
        return True

    def get_analysis_file_path(self):
        """获取分析文件路径"""
        return f"{self.output_dir}/{self.project_name}.md"

    @timing_decorator("SourceProcessing:Webpage")
    def process_source(self):
        """处理网页源"""
        webpage_url = self.source_config["url"]
        analysis_file = self.get_analysis_file_path()

        try:
            # 获取网页内容
            headers = {"User-Agent": self.user_agent}
            response = requests.get(webpage_url, headers=headers, timeout=30)
            response.raise_for_status()
            response.encoding = response.apparent_encoding or "utf-8"

            # 解析HTML
            soup = BeautifulSoup(response.text, "html.parser")

            # 获取标题
            title = soup.find("title")
            title_text = title.get_text().strip() if title else "未知标题"

            # 创建媒体目录
            media_dir = os.path.join(self.output_dir, "media")
            Path(media_dir).mkdir(parents=True, exist_ok=True)

            # 下载图片并替换链接
            downloaded_images = self._download_images(soup, webpage_url, media_dir)

            # 转换HTML为Markdown
            markdown_content = self._convert_to_markdown(soup, webpage_url, title_text, downloaded_images)

            # 写入文件
            with open(analysis_file, "w", encoding="utf-8") as f:
                f.write(markdown_content)

            logger.info(f"网页源处理完成: {analysis_file}")
            return analysis_file

        except Exception as e:
            logger.error(f"网页源处理失败: {e}")
            return None

    def _download_images(self, soup, base_url, media_dir):
        """下载图片到本地并返回URL映射"""
        downloaded_images = {}
        img_tags = soup.find_all("img")

        logger.info(f"找到 {len(img_tags)} 个图片")

        for i, img in enumerate(img_tags):
            src = img.get("src") or img.get("data-src", "")
            if not src:
                continue

            # 转换为绝对URL
            if not src.startswith(("http://", "https://")):
                src = urllib.parse.urljoin(base_url, src)

            try:
                # 下载图片
                headers = {"User-Agent": self.user_agent}
                img_response = requests.get(src, headers=headers, timeout=15, stream=True)
                img_response.raise_for_status()

                # 生成文件名
                parsed_url = urllib.parse.urlparse(src)
                filename = os.path.basename(parsed_url.path)
                if not filename or "." not in filename:
                    ext = self._detect_image_extension(img_response)
                    filename = f"image_{i+1}{ext}"

                # 清理文件名
                filename = self._sanitize_filename(filename)

                # 保存图片
                filepath = os.path.join(media_dir, filename)
                with open(filepath, "wb") as f:
                    for chunk in img_response.iter_content(chunk_size=8192):
                        f.write(chunk)

                # 记录映射
                downloaded_images[src] = f"media/{filename}"
                logger.info(f"下载图片: {filename}")

            except Exception as e:
                logger.warning(f"下载图片失败 {src}: {e}")
                continue

        return downloaded_images

    def _detect_image_extension(self, response):
        """检测图片扩展名"""
        content_type = response.headers.get("Content-Type", "").lower()
        if "jpeg" in content_type or "jpg" in content_type:
            return ".jpg"
        elif "png" in content_type:
            return ".png"
        elif "gif" in content_type:
            return ".gif"
        elif "webp" in content_type:
            return ".webp"
        elif "svg" in content_type:
            return ".svg"
        else:
            return ".jpg"

    def _sanitize_filename(self, filename):
        """清理文件名"""
        import re

        filename = re.sub(r'[<>:"/\\|?*]', "_", filename)
        filename = re.sub(r"[^\w\-_.]", "_", filename)
        filename = re.sub(r"_+", "_", filename)
        return filename.strip("_.")

    def _convert_to_markdown(self, soup, url, title, image_mapping):
        """转换HTML为Markdown"""
        # 移除脚本和样式
        for tag in soup(["script", "style", "meta", "link", "noscript"]):
            tag.decompose()

        # 替换图片链接
        for img in soup.find_all("img"):
            src = img.get("src") or img.get("data-src", "")
            if src:
                # 转换为绝对URL
                if not src.startswith(("http://", "https://")):
                    src = urllib.parse.urljoin(url, src)

                # 替换为本地路径
                if src in image_mapping:
                    img["src"] = image_mapping[src]

        # 转换为Markdown
        html_content = str(soup)
        markdown_content = markdownify(
            html_content, heading_style="ATX", bullets="-", strip=["script", "style", "meta", "link"]
        )

        # 清理格式
        import re

        markdown_content = re.sub(r"\n{3,}", "\n\n", markdown_content)
        markdown_content = markdown_content.replace("\xa0", " ")
        markdown_content = markdown_content.strip()

        # 生成最终内容
        final_content = f"""**原始网页链接**: {url}

# {title}

{markdown_content}
"""
        return final_content
