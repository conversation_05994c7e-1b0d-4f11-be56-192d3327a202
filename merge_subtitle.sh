#!/bin/bash

# 检查参数数量
if [ "$#" -ne 3 ]; then
    echo "用法: $0 <起始序号> <结束序号> <分辨率>"
    echo "例如: $0 1 10 1080p60"
    exit 1
fi

# 获取参数
start_index=$1
end_index=$2
resolution=$3
base_dir="media/videos"
output_dir="$base_dir" # 输出目录定在 media/videos/
final_output_file="${output_dir}/Storyboard_${start_index}-${end_index}_${resolution}_final.mp4"
list_file="mylist.txt"
processed_files=()

# 检查起始序号是否小于等于结束序号
if [ "$start_index" -gt "$end_index" ]; then
    echo "错误: 起始序号 ($start_index) 不能大于结束序号 ($end_index)."
    exit 1
fi

# 清理可能存在的旧列表文件
rm -f "$list_file"

echo "开始处理 Storyboards 从 ${start_index} 到 ${end_index}，分辨率 ${resolution}..."

# 遍历 storyboard 序号
for i in $(seq "$start_index" "$end_index"); do
    storyboard_dir="${base_dir}/storyboard_${i}_attempt_1_dsl/${resolution}"
    input_video="${storyboard_dir}/Storyboard_${i}.mp4"
    input_srt="${storyboard_dir}/Storyboard_${i}.srt"
    output_subtitle_video="${storyboard_dir}/Storyboard_${i}_subtitle.mp4"

    echo "正在处理 Storyboard $i..."

    # 检查视频和字幕文件是否存在
    if [ ! -f "$input_video" ]; then
        echo "  警告: 视频文件 '$input_video' 不存在，跳过。"
        continue
    fi
    if [ ! -f "$input_srt" ]; then
        echo "  警告: 字幕文件 '$input_srt' 不存在，跳过。"
        continue
    fi

    echo "  找到视频和字幕，开始合并..."
    # 运行 ffmpeg 合并字幕
    ffmpeg -i "$input_video" -lavfi "subtitles='$input_srt':force_style='Alignment=2,MarginV=6,fontcolor=white,Fontsize=16,fontweight=bold,FontName=微软雅黑'" -y "$output_subtitle_video" > /dev/null 2>&1

    if [ $? -eq 0 ]; then
        echo "  字幕合并成功: $output_subtitle_video"
        # 将处理好的文件路径添加到列表文件，注意需要转义特殊字符给ffmpeg
        printf "file '%s'
" "$output_subtitle_video" >> "$list_file"
        processed_files+=("$output_subtitle_video") # 记录用于后续清理
    else
        echo "  错误: ffmpeg 合并字幕失败 '$input_video'."
    fi
done

# 检查是否有文件被处理
if [ ! -f "$list_file" ] || [ ! -s "$list_file" ]; then
    echo "没有成功处理任何视频文件，无法进行拼接。"
    exit 1
fi

echo "所有 Storyboard 处理完毕，开始拼接视频..."

# 使用 ffmpeg concat demuxer 拼接视频
ffmpeg -f concat -safe 0 -i "$list_file" -c copy -y "$final_output_file"

if [ $? -eq 0 ]; then
    echo "视频拼接成功: $final_output_file"
else
    echo "错误: ffmpeg 拼接视频失败。"
    # 清理
    echo "清理临时文件..."
    rm -f "$list_file"
    for file in "${processed_files[@]}"; do
        rm -f "$file"
    done
    exit 1
fi

# 清理临时文件
echo "清理临时文件..."
rm -f "$list_file"
for file in "${processed_files[@]}"; do
    rm -f "$file"
done

echo "处理完成！"
exit 0
