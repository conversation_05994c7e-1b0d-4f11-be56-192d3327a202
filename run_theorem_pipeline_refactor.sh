#!/bin/bash

# 完整流水线执行脚本 (Shell版本)
# 自动从配置文件读取主题并依次执行三个命令

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 从YAML配置文件中提取topic
get_topic_from_config() {
    local config_file="config/config.yaml"
    
    if [ ! -f "$config_file" ]; then
        log_error "配置文件不存在: $config_file"
        exit 1
    fi
    
    # 使用python来解析YAML文件（更可靠）
    topic=$(python3 -c "
import yaml
try:
    with open('$config_file', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    print(config['example_explain']['topic'])
except Exception as e:
    print('ERROR: ' + str(e))
    exit(1)
")
    
    if [[ $topic == ERROR:* ]]; then
        log_error "解析配置文件失败: $topic"
        exit 1
    fi
    
    echo "$topic"
}

# 执行命令的函数
run_command() {
    local cmd="$1"
    local description="$2"
    
    log_step "$description"
    log_info "执行命令: $cmd"
    
    if eval "$cmd"; then
        log_info "✅ $description 执行成功"
        return 0
    else
        log_error "❌ $description 执行失败"
        return 1
    fi
}

# 主函数
main() {
    echo "=================================================="
    log_info "🎬 开始执行完整流水线"
    echo "=================================================="
    
    # 检查必要的依赖
    if ! command -v python3 &> /dev/null; then
        log_error "需要安装 Python3"
        exit 1
    fi
    
    if ! python3 -c "import yaml" 2>/dev/null; then
        log_error "需要安装 PyYAML: pip install PyYAML"
        exit 1
    fi
    
    # 获取主题
    log_info "从配置文件读取主题..."
    topic=$(get_topic_from_config)
    log_info "📋 主题: $topic"
    
    # 构建路径
    topic_dir="output/${topic}"
    example_explain_file="${topic_dir}/example_explain.md"
    vision_storyboard_dir="${topic_dir}/vision_storyboard"
    
    log_info "📁 主题目录: $topic_dir"
    log_info "📝 例子解释文件: $example_explain_file"
    log_info "📊 视觉故事板目录: $vision_storyboard_dir"
    
    # 确保输出目录存在
    mkdir -p "$topic_dir"
    
    # 执行三个命令
    local success_count=0
    local total_commands=3
    
    echo ""
    log_info "📋 执行进度: 1/$total_commands"
    if run_command "uv run python agents/example_explain_agent_refactor.py" "步骤1: 生成例子解释"; then
        ((success_count++))
    else
        log_error "💥 流水线在步骤 1 中断"
        exit 1
    fi
    
    echo ""
    log_info "📋 执行进度: 2/$total_commands"
    if run_command "uv run python agents/theorem_agents/professional_template_agent.py '$example_explain_file' '$vision_storyboard_dir'" "步骤2: 生成视觉故事板"; then
        ((success_count++))
    else
        log_error "💥 流水线在步骤 2 中断"
        exit 1
    fi
    
    echo ""
    log_info "📋 执行进度: 3/$total_commands"
    if run_command "uv run python agents/scene_code_generation_agent_refactor.py '$vision_storyboard_dir'" "步骤3: 生成场景代码"; then
        ((success_count++))
    else
        log_error "💥 流水线在步骤 3 中断"
        exit 1
    fi
    
    # 输出结果
    echo ""
    echo "=================================================="
    if [ $success_count -eq $total_commands ]; then
        log_info "🎉 完整流水线执行成功!"
        log_info "✨ 主题: $topic"
        log_info "📂 输出目录: $topic_dir"
        log_info "✅ 成功执行 $success_count/$total_commands 个步骤"
    else
        log_error "⚠️  流水线部分完成: $success_count/$total_commands 个步骤成功"
        exit 1
    fi
    echo "=================================================="
}

# 检查脚本是否被直接执行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 