"""
Git仓库README内容抓取和分析代理

负责抓取GitHub仓库的README文件，分析重点内容，并收集相关素材
输出分镜内容和对应素材名
"""

import json
import logging
import os
from typing import Any

import yaml

from readme_analyzer import ReadmeAnalyzer

# 导入自定义模块
from readme_scraper import GitHubScraper

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

# 默认配置
DEFAULT_CONFIG = {
    "model": {
        "type": "gpt-3.5-turbo",
        "api": {
            "openai_api_key": os.environ.get("OPENAI_API_KEY", ""),
            "openai_api_base": os.environ.get("OPENAI_API_BASE", "https://api.openai.com/v1"),
        },
    },
    "github": {
        "max_retries": 3,
        "retry_delay": 2,
        "timeout": 30,
    },
    "scraper": {
        "headless": True,
        "wait_time": 5,
        "media_dir": "github_media",
        "image_extensions": [".png", ".jpg", ".jpeg", ".gif", ".svg"],
        "video_extensions": [".mp4", ".webm", ".mov"],
        "max_media_size": 10 * 1024 * 1024,  # 10MB
    },
    "output": {
        "dir": "output",
        "format": ["md", "json"],
    },
}


class GitReadmeAgent:
    """Git仓库README内容抓取和分析代理"""

    def __init__(self, config_path: str = "config/config.yaml"):
        """
        初始化代理

        Args:
            config_path: 配置文件路径
        """
        # 加载配置
        self.config = self._load_config(config_path)

        # 创建输出目录
        self.output_dir = self.config["output"]["dir"]
        os.makedirs(self.output_dir, exist_ok=True)

        # 初始化爬虫
        self.scraper = GitHubScraper(
            headless=self.config["scraper"]["headless"],
            wait_time=self.config["scraper"]["wait_time"],
            media_dir=self.config["scraper"]["media_dir"],
        )

        # 初始化分析器
        self.analyzer = ReadmeAnalyzer(
            model_type=self.config["model"]["type"],
            api_key=self.config["model"]["api"]["openai_api_key"],
            api_base=self.config["model"]["api"]["openai_api_base"],
        )

        logger.info("GitReadmeAgent初始化完成")

    def _load_config(self, config_path: str) -> dict[str, Any]:
        """
        加载配置文件，如果不存在则创建默认配置

        Args:
            config_path: 配置文件路径

        Returns:
            配置字典
        """
        # 如果配置文件不存在，创建默认配置
        if not os.path.exists(config_path):
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            with open(config_path, "w") as f:
                yaml.dump(DEFAULT_CONFIG, f)
            logger.info(f"创建默认配置文件: {config_path}")
            return DEFAULT_CONFIG

        # 加载配置文件
        try:
            with open(config_path) as f:
                config = yaml.safe_load(f)
            logger.info(f"从 {config_path} 加载配置")
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            logger.info("使用默认配置")
            return DEFAULT_CONFIG

    def process_repository(self, repo_url: str) -> dict[str, Any]:
        """
        处理GitHub仓库

        Args:
            repo_url: GitHub仓库URL

        Returns:
            处理结果，包含分镜内容和素材
        """
        logger.info(f"开始处理仓库: {repo_url}")

        result = {
            "repo_url": repo_url,
            "success": False,
            "readme_content": None,
            "media_elements": [],
            "storyboard": [],
            "error": None,
        }

        try:
            # 1. 抓取README内容和相关素材
            logger.info("正在抓取README内容和相关素材...")
            readme_content, media_elements = self.scraper.scrape_github_readme(repo_url)

            if not readme_content:
                result["error"] = "无法获取README内容"
                return result

            result["readme_content"] = readme_content
            result["media_elements"] = media_elements

            # 2. 分析README内容
            logger.info("正在分析README内容...")
            storyboard = self.analyzer.analyze_readme(readme_content, repo_url, media_elements)

            result["storyboard"] = storyboard
            result["success"] = True

            # 3. 保存结果
            self._save_results(result, repo_url)

            logger.info(f"仓库处理完成: {repo_url}")
            return result

        except Exception as e:
            logger.error(f"处理仓库时出错: {e}")
            result["error"] = str(e)
            return result

    def _save_results(self, result: dict[str, Any], repo_url: str) -> None:
        """
        保存处理结果

        Args:
            result: 处理结果
            repo_url: 仓库URL
        """
        # 从URL中提取仓库名
        repo_name = repo_url.split("/")[-1].replace(".git", "")

        # 保存为JSON
        if "json" in self.config["output"]["format"]:
            json_path = os.path.join(self.output_dir, f"{repo_name}_readme_analysis.json")
            with open(json_path, "w", encoding="utf-8") as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            logger.info(f"分析结果保存为JSON: {json_path}")

        # 保存为Markdown
        if "md" in self.config["output"]["format"]:
            md_path = os.path.join(self.output_dir, f"{repo_name}_readme_analysis.md")
            with open(md_path, "w", encoding="utf-8") as f:
                f.write(f"# {repo_name} README分析\n\n")
                f.write(f"仓库URL: {repo_url}\n\n")

                # 添加分镜内容
                f.write("## 分镜内容\n\n")
                for i, scene in enumerate(result["storyboard"]):
                    f.write(f"### {i+1}. {scene['分镜名']}\n\n")
                    f.write(f"{scene['分镜内容']}\n\n")

                    if "素材名" in scene and scene["素材名"]:
                        f.write(f"**素材**: {scene['素材名']}\n\n")

                    if "视觉动效建议" in scene:
                        f.write(f"**视觉动效建议**: {scene['视觉动效建议']}\n\n")

                # 添加媒体元素信息
                if result["media_elements"]:
                    f.write("## 媒体元素\n\n")
                    for i, media in enumerate(result["media_elements"]):
                        f.write(f"### 媒体 {i+1}\n\n")
                        f.write(f"**类型**: {media['type']}\n\n")
                        f.write(f"**路径**: {media['local_path']}\n\n")
                        f.write(f"**相关文本**: {media['nearest_text']}\n\n")

                        # 对于图片类型，直接在Markdown中显示
                        if media["type"] in ["image", "gif", "diagram"]:
                            f.write(f"![{media['type']}]({media['local_path']})\n\n")

            logger.info(f"分析结果保存为Markdown: {md_path}")


def main():
    """主函数"""
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description="Git仓库README内容抓取和分析工具")
    parser.add_argument("repo_url", help="GitHub仓库URL")
    parser.add_argument("--config", default="config/config.yaml", help="配置文件路径")

    args = parser.parse_args()

    # 检查URL格式
    if not args.repo_url.startswith("https://github.com/"):
        print("错误: 请提供有效的GitHub仓库URL")
        return

    # 初始化代理
    agent = GitReadmeAgent(config_path=args.config)

    # 处理仓库
    result = agent.process_repository(args.repo_url)

    # 输出结果
    if result["success"]:
        print("\n✅ 处理成功!")
        print(f"- 共发现 {len(result['media_elements'])} 个媒体元素")
        print(f"- 生成了 {len(result['storyboard'])} 个分镜")

        # 输出分镜列表
        print("\n📋 分镜列表:")
        for i, scene in enumerate(result["storyboard"]):
            print(f"{i+1}. {scene['分镜名']}")
    else:
        print(f"\n❌ 处理失败: {result['error']}")


if __name__ == "__main__":
    main()
