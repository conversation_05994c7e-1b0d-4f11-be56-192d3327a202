#!/usr/bin/env python3
"""
测试脚本：用实际文件测试 material_enhancement.py 中的
analyze_and_inject_tool_markers 和 apply_tools_and_merge 函数
"""

import json
import sys
from pathlib import Path

from loguru import logger

from agents.material_enhancement import MaterialEnhancer
from utils.common import Config
from utils.create_llm_model import create_model

purposes = {
    "paper": "为技术人员介绍这篇技术报告论文，目的是揭露核心能力和技术细节等，风格具备应用性，吸引用户学习，视频5分钟",
    "survey": "为研究人员介绍这篇综述论文，目的是介绍这个领域的研究体系，核心模块，各方向研究进展，代表性工作，未来展望等，风格具备严谨全面系统，视频15分钟",
    "project": "给技术爱好者介绍这个AI项目，目的介绍项目价值、具体核心能力, 项目架构等，风格是吸引用户试用，视频4分钟",
}


def main():
    """主函数：测试素材扩充的两个核心函数"""
    # 设置输出目录
    output_dir = Path("output/test_enhancement")
    output_dir.mkdir(parents=True, exist_ok=True)

    # 设置日志输出
    logger.add(output_dir / "test_enhancement.log", level="INFO")
    logger.info("开始测试素材扩充函数")

    # 1. 设置测试内容
    config = Config()
    model = create_model()
    enhancer = MaterialEnhancer(config_dict=config.config, model=model)
    logger.info(f"已创建 MaterialEnhancer 实例，可用工具数量: {len(enhancer.tools)}")

    original_content = open(sys.argv[1]).read()
    material_content = open(sys.argv[2]).read()
    purpose = purposes[sys.argv[3]]

    # 3. 测试 analyze_and_inject_tool_markers 函数（可选，因为没有模型可能无法正常工作）
    # 如果没有传入模型，这个函数可能会返回原始内容或报错
    logger.info("尝试调用 analyze_and_inject_tool_markers 函数...")
    marked_content = enhancer.analyze_and_inject_tool_markers(original_content, material_content, purpose)

    # 保存标记内容到文件
    with open(output_dir / "marked_content.md", "w", encoding="utf-8") as f:
        f.write(marked_content)
    logger.info(f"已保存标记内容到 {output_dir / 'marked_content.md'}")

    marked_content = open(output_dir / "marked_content.md").read()

    enhanced_content, enhancements = enhancer.apply_tools_and_merge(marked_content, str(output_dir))

    # 保存增强后的内容到文件
    with open(output_dir / "enhanced_content.md", "w", encoding="utf-8") as f:
        f.write(enhanced_content)
    logger.info(f"已保存增强后的内容到 {output_dir / 'enhanced_content.md'}")

    # 保存工具调用结果到JSON文件
    with open(output_dir / "enhancements.json", "w", encoding="utf-8") as f:
        json.dump(enhancements, f, ensure_ascii=False, indent=2)
    logger.info(f"已保存工具调用结果到 {output_dir / 'enhancements.json'}")

    # 输出工具调用统计
    logger.info(f"共调用了 {len(enhancements)} 个工具")
    for i, enhancement in enumerate(enhancements, 1):
        logger.info(f"工具 {i}: {enhancement['tool_name']}")


if __name__ == "__main__":
    main()
