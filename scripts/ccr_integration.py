#!/usr/bin/env python3
"""
Claude Code Router集成模块
提供与process_scene_file_enhanced兼容的接口
"""

import os
import sys
from pathlib import Path
from typing import Optional

# 添加当前目录到路径以导入run_claude_code_router
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

from run_claude_code_router import process_scene_with_ccr


def process_scene_file_enhanced_ccr(
    scene_file: str = "",
    scene_description: str = "",
    output_dir: str = "output/generated_code",
    max_iterations: int = 3,
    quality: str = "l",
    scene_num: int = 0,
    topic: str = "",
) -> Optional[dict[str, Optional[str]]]:
    """
    使用claude-code-router的process_scene_file_enhanced替代函数

    Args:
        scene_file: Path to the scene description file
        scene_description: Scene description content
        output_dir: Directory to save generated files
        max_iterations: Maximum number of iterations for code improvement (ignored by CCR)
        quality: Rendering quality (l, m, h, q, k) - used for video file detection
        scene_num: Scene number for naming
        topic: Topic for naming

    Returns:
        Dictionary containing paths to generated files, or None on failure
    """
    try:
        # 如果提供了scene_description但没有scene_file，创建临时文件
        if scene_description and not scene_file:
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)

            # 生成临时场景文件名
            if topic:
                temp_scene_file = os.path.join(output_dir, f"{topic}_{scene_num}_scene.txt")
            else:
                temp_scene_file = os.path.join(output_dir, f"scene_{scene_num}_description.txt")

            # 写入场景描述到临时文件
            with open(temp_scene_file, "w", encoding="utf-8") as f:
                f.write(scene_description)

            scene_file = temp_scene_file

        # 检查场景文件是否存在
        if not scene_file or not os.path.exists(scene_file):
            print(f"❌ 场景文件不存在: {scene_file}")
            return None

        # 生成输出文件名
        if topic:
            output_file = f"{topic}_{scene_num}"
        else:
            scene_path = Path(scene_file)
            output_file = f"{scene_path.stem}_{scene_num}"

        # 使用claude-code-router处理
        result = process_scene_with_ccr(scene_file, output_file, quality)

        # 如果成功，尝试移动生成的文件到指定的output_dir
        if result["success"] and result["final_code_path"]:
            try:
                # 确保输出目录存在
                os.makedirs(output_dir, exist_ok=True)

                # 移动代码文件
                code_file = Path(result["final_code_path"])
                if code_file.exists():
                    new_code_path = Path(output_dir) / code_file.name
                    code_file.rename(new_code_path)
                    result["final_code_path"] = str(new_code_path)

                # 移动视频文件
                if result["final_video_path"]:
                    video_file = Path(result["final_video_path"])
                    if video_file.exists():
                        new_video_path = Path(output_dir) / video_file.name
                        video_file.rename(new_video_path)
                        result["final_video_path"] = str(new_video_path)

            except Exception as e:
                print(f"⚠️ 移动文件时出错: {e}")
                # 即使移动失败，也返回原始路径

        return result

    except Exception as e:
        print(f"❌ CCR处理失败: {e}")
        return None


# 为了向后兼容，提供原始函数名的别名
process_scene_file_enhanced = process_scene_file_enhanced_ccr


def test_ccr_integration():
    """测试CCR集成功能"""
    print("🧪 测试CCR集成功能")
    print("=" * 50)

    # 创建测试场景描述
    test_description = """创建一个简单的数学动画：
1. 显示标题"数学之美"
2. 显示公式 a² + b² = c²
3. 让公式从小变大
4. 最后添加一些装饰效果"""

    # 测试函数
    result = process_scene_file_enhanced_ccr(
        scene_description=test_description, output_dir="output/test_ccr", scene_num=1, topic="math_beauty"
    )

    if result:
        print("✅ 测试成功")
        print(f"代码文件: {result.get('final_code_path')}")
        print(f"视频文件: {result.get('final_video_path')}")
        print(f"成功状态: {result.get('success')}")
    else:
        print("❌ 测试失败")

    return result is not None


if __name__ == "__main__":
    test_ccr_integration()
