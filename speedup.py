import os
import shutil
import subprocess
import time
from pathlib import Path

import cv2
from edgetts_service import EdgeTTSService
from manim import *
from manim_voiceover import VoiceoverScene


class StaticOptimizedScene(VoiceoverScene):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.static_segments = []
        self.temp_dir = Path("temp_static_optimization")
        self.temp_dir.mkdir(exist_ok=True)
        self.segment_counter = 0
        self.timeline = []

    def play(self, *args, **kwargs):
        """记录动态片段"""
        self.timeline.append({"type": "dynamic"})
        super().play(*args, **kwargs)
        self.renderer.update_frame(self)  # HACK

    def wait(self, *args, **kwargs):
        """将普通等待视为动态片段"""
        self.timeline.append({"type": "dynamic"})
        return super().wait(*args, **kwargs)

    def static_wait(self, duration):
        # """静态等待 - 只渲染一帧然后记录"""
        # if duration < 0.5:  # 太短的等待不优化
        #     self.wait(duration)
        #     return

        print(f"记录静态片段: {duration}秒")

        # 渲染当前帧并保存
        frame_path = self.temp_dir / f"static_frame_{self.segment_counter}.png"
        # self.renderer.update_frame(self)
        frame = self.renderer.get_frame()

        # 保存帧
        frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
        cv2.imwrite(str(frame_path), frame_bgr)

        # 记录静态片段信息
        segment_info = {
            "type": "static",
            "frame_path": str(frame_path),
            "duration": duration,
            "start_time": self.renderer.time,
            "segment_id": self.segment_counter,
        }
        self.static_segments.append(segment_info)
        self.timeline.append(segment_info)

        # 跳过时间但不渲染
        self.renderer.time += duration
        self.segment_counter += 1

    def render(self, **kwargs):
        """重写渲染方法"""
        start = time.time()
        print("开始优化渲染...")

        # 第一步：正常渲染（但跳过静态片段）
        # construct() 在 super().render() 内部被调用,
        # 会填充 self.timeline 和 self.static_segments,
        # 并生成动态片段的视频文件。
        print("第1步：渲染动态内容...")
        super().render(**kwargs)

        # 第二步：处理静态片段
        if self.static_segments:
            print("第2步：处理静态片段...")
            self.process_static_segments()

            # 第三步：拼接最终视频
            print("第3步：拼接最终视频...")
            self.create_final_video()
        else:
            # 如果没有静态片段, Manim已经生成了正确的视频
            print("没有静态片段，无需优化。")

        print("优化渲染完成！")
        print(time.time() - start)

    def process_static_segments(self):
        """处理所有静态片段"""
        for segment in self.static_segments:
            self.create_static_video(segment)

    def create_static_video(self, segment):
        """从单帧创建静态视频"""
        start = time.time()
        frame_path = segment["frame_path"]
        duration = segment["duration"]
        segment_id = segment["segment_id"]

        output_path = self.temp_dir / f"static_video_{segment_id}.mp4"

        # 获取场景配置
        fps = config.frame_rate
        width = config.pixel_width
        height = config.pixel_height

        cmd = [
            "ffmpeg",
            "-y",
            "-loop",
            "1",
            "-i",
            str(frame_path),
            "-t",
            str(duration),
            "-r",
            str(fps),
            "-s",
            f"{width}x{height}",
            "-pix_fmt",
            "yuv420p",
            "-c:v",
            "libx264",
            "-preset",
            "ultrafast",
            str(output_path),
        ]

        try:
            _ = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print(f"  静态视频片段 {segment_id} 创建成功 ({duration}s), {time.time() - start:.4f}s")
            segment["video_path"] = str(output_path)
        except subprocess.CalledProcessError as e:
            print(f"创建静态视频失败: {e}")
            print(f"错误输出: {e.stderr}")

    def create_final_video(self):
        """根据时间线拼接最终视频"""
        final_output_path = self.renderer.file_writer.movie_file_path
        if not final_output_path:
            print("错误：无法确定最终输出文件路径。")
            return

        dynamic_paths_iter = iter(self.renderer.file_writer.partial_movie_files)
        static_videos_by_id = {s["segment_id"]: s.get("video_path") for s in self.static_segments}

        concat_list_path = self.temp_dir / "concat_list.txt"

        with open(concat_list_path, "w") as f:
            for segment in self.timeline:
                path = None
                if segment["type"] == "dynamic":
                    try:
                        path = next(dynamic_paths_iter)
                    except StopIteration:
                        print("警告：时间线上的动态片段多于Manim生成的视频文件。")
                        continue
                elif segment["type"] == "static":
                    segment_id = segment["segment_id"]
                    path = static_videos_by_id.get(segment_id)
                    if not path:
                        print(f"警告：未找到静态片段 {segment_id} 的视频文件，跳过此片段。")
                        continue

                if path:
                    # 在拼接列表中使用绝对路径以增加稳健性
                    abs_path = os.path.abspath(path)
                    f.write(f"file '{abs_path}'\n")

        if not concat_list_path.exists() or concat_list_path.stat().st_size == 0:
            print("拼接列表为空，不生成最终视频。")
            return

        temp_final_output = Path(final_output_path).parent / f"temp_optimized_{Path(final_output_path).name}"

        cmd = [
            "ffmpeg",
            "-y",
            "-f",
            "concat",
            "-safe",
            "0",
            "-i",
            str(concat_list_path),
            "-c",
            "copy",
            str(temp_final_output),
        ]

        try:
            subprocess.run(cmd, capture_output=True, text=True, check=True)
            print(f"最终视频创建成功: {temp_final_output}")
            # 将拼接好的视频移动到Manim期望的最终位置
            temp_final_output_with_audio = (
                Path(final_output_path).parent / f"temp_optimized_with_audio_{Path(final_output_path).name}"
            )
            command = [
                "ffmpeg",
                "-i",
                temp_final_output,
                "-i",
                Path(final_output_path).with_suffix(".wav"),
                "-c:v",
                "copy",  # 复制视频流
                "-c:a",
                "aac",  # 将音频编码为 aac
                "-strict",
                "-2",  # aac 是实验性编码器，需要此参数
                "-shortest",  # 以最短的输入文件时长为准
                temp_final_output_with_audio,
            ]
            subprocess.run(command, capture_output=True, text=True, check=True)
            print(f"最终拼接音频成功: {temp_final_output_with_audio}")
            shutil.move(str(temp_final_output_with_audio), final_output_path)
            print(f"已将优化视频移动到: {final_output_path}")
        except subprocess.CalledProcessError as e:
            print(f"视频拼接失败: {e}")
            print(f"FFmpeg stderr: {e.stderr}")

        # 拼接 wav

    def cleanup(self):
        """清理临时文件"""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
            print("临时文件已清理")


class TestVoiceover(StaticOptimizedScene):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.set_speech_service(
            EdgeTTSService(global_speed=1.2, voice="zh-CN-YunxiNeural"),
            create_subcaption=True,
        )
        self.wait = self.static_wait

    def construct(self):
        with self.voiceover(
            "非常棒！这一版引入了模型名称 SMILE (Sample-Matching for Individual Lift Estimation)，这是一个巨大的提升。一个朗朗上口且与方法相关的缩写能让您的工作更容易被记住和引用，这在学术推广上非常有价值。"
        ):
            circle = Circle(color=RED)
            sqr = Square(color=YELLOW).next_to(circle, RIGHT)
            self.play(Create(circle), Create(sqr))

        self.wait(10)
        with self.voiceover("我们再来看一下，还有哪些功能可以通过静态帧复制的方法来节省耗时"):
            self.play(circle.animate.shift(RIGHT))
