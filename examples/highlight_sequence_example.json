{"schema_version": "2.0-mvp", "metadata": {"title": "HighlightSequenceDemo", "author": "用户", "background_color": "BLACK"}, "actions": [{"type": "animate_markdown", "params": {"content": "# 代码高亮示例\n\n这个演示展示如何高亮代码中的特定行", "id": "title", "narration": "这个演示展示如何高亮代码中的特定行"}}, {"type": "highlight_content", "params": {"elements": ["title"], "highlight_type": "box", "duration_per_item": 1.5, "color": "BLUE", "narration": "以蓝色框高亮 title 中的所有行"}}, {"type": "animate_markdown", "params": {"content": "## 代码高亮示例\n\n```json\n{\n  \"user\": {\n    \"name\": \"张三\",\n    \"age\": 28,\n    \"skills\": [\"Python\", \"JavaScript\", \"Machine Learning\"],\n    \"is_active\": true\n  },\n  \"project\": {\n    \"name\": \"数据可视化\",\n    \"start_date\": \"2023-01-15\",\n    \"completion\": 75\n  }\n}\n```", "animation": "fadeIn", "id": "json_data", "narration": "这个例子展示了如何高亮代码中的特定行"}}, {"type": "highlight_content", "params": {"elements": ["json_data"], "highlight_type": "box", "duration_per_item": 1.5, "color": "BLUE", "narration": "以蓝色框高亮 json_data 中的所有行"}}, {"type": "highlight_content", "params": {"elements": ["json_data"], "highlight_type": "color", "duration_per_item": 1.5, "color": "YELLOW", "lines": "1-2, 7-8", "narration": "以黄色高亮 json_data 中的第1-2行和第7-8行"}}, {"type": "highlight_content", "params": {"elements": ["json_data"], "highlight_type": "color", "duration_per_item": 1.5, "color": "RED", "lines": "3", "narration": "以红色高亮 json_data 中的第3行"}}, {"type": "highlight_content", "params": {"elements": ["json_data"], "highlight_type": "underline", "duration_per_item": 1.5, "color": "GREEN", "narration": "以绿色下划线高亮 json_data"}}]}