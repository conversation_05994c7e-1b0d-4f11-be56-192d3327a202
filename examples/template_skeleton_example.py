from manim import *
import numpy as np
from prompts.professional_science_template import ProfessionalScienceTemplate


class TemplateSkeletonExample(ProfessionalScienceTemplate):
    """
    🎯 模板使用骨架示例 - 详细说明容易犯错的地方
    
    ⚠️ 常见错误提醒：
    1. 标题超过8个字 → 导致显示不全或字体过小
    2. 步骤描述超过12个字 → 影响可读性
    3. 辅助区域内容过多 → 字体变得非常小
    4. 结果描述过长 → 超出区域边界
    5. 忘记调用setup_background() → 可能影响显示效果
    6. 主内容过于复杂 → 自动缩放后看不清细节
    
    📏 严格字数限制（超出会影响显示效果）：
    - 标题区域：≤8个字（如"函数原理"、"排序算法"）
    - 步骤区域：≤12个字（如"第一步：初始化数据"）
    - 辅助标题：≤6个字（如"要点"、"公式"、"特点"）
    - 辅助项目：≤5项，每项≤15个字
    - 结果区域：≤40个字
    """
    
    def construct(self):
        """
        🔧 标准构建流程 - 按此顺序执行，避免常见错误
        """
        
        # === 第0步：必须先设置背景 ===
        # ⚠️ 常见错误：忘记调用此方法
        self.setup_background()
        
        # === 第1步：创建标题（8字以内） ===
        # ✅ 正确示例：
        title_good = self.create_title_region_content("数学原理")  # 4字，完美
        # ❌ 错误示例：
        # title_bad = self.create_title_region_content("高等数学中的复杂函数原理详解")  # 16字，太长！
        
        # === 第2步：创建步骤描述（12字以内） ===
        # ✅ 正确示例：
        step_good = self.create_step_region_content("第一步：函数定义")  # 8字，合适
        # ❌ 错误示例：
        # step_bad = self.create_step_region_content("第一步：建立复杂的数学函数模型并进行详细分析")  # 22字，太长！
        
        # === 第3步：创建主内容（核心展示区域） ===
        # 💡 提示：主内容会自动缩放，但过于复杂的内容缩放后可能看不清
        main_content = self.create_simple_main_content()  # 创建适中复杂度的内容
        main_group = self.create_main_region_content(main_content)
        
        # === 第4步：创建辅助区域（可选，根据需要选择布局） ===
        
        # 🎨 布局选择1：完整布局（左右辅助区域都有）
        # ✅ 左侧辅助 - 正确示例（标题6字以内，项目5个以内，每项15字以内）
        left_aux_good = self.create_left_auxiliary_content(
            "要点:",  # 3字标题，✅ 合适
            [
                "• 开口向上",        # 5字，✅ 合适
                "• 顶点在原点",      # 6字，✅ 合适
                "• 关于y轴对称",     # 7字，✅ 合适
                "• 最小值为0"        # 6字，✅ 合适
            ]  # 4个项目，✅ 合适
        )
        
        # ✅ 右侧辅助 - 正确示例
        right_aux_good = self.create_right_auxiliary_content(
            "公式:",  # 3字标题，✅ 合适
            [
                MathTex(r"f(x) = x^2"),      # 数学公式，✅ 推荐
                MathTex(r"f'(x) = 2x"),      # 导数公式，✅ 推荐
                "定义域: ℝ",                  # 7字，✅ 合适
                "值域: [0,+∞)"               # 9字，✅ 合适
            ]  # 4个项目，✅ 合适
        )
        
        # === 第5步：创建结果区域（40字以内） ===
        # ✅ 正确示例：
        result_good = self.create_result_region_content(
            "结论：二次函数y=x²开口向上，顶点(0,0)，关于y轴对称"  # 28字，✅ 合适
        )
        
        # === 第6步：按正确顺序播放动画 ===
        # 💡 推荐顺序：标题→步骤→主内容→辅助区域→结果
        
        # 6.1 首先显示标题和步骤
        self.play(Write(title_good), Write(step_good))
        self.wait(0.5)
        
        # 6.2 然后显示主内容
        self.play(FadeIn(main_group))
        self.wait(0.5)
        
        # 6.3 接着显示辅助区域（如果有）
        self.play(FadeIn(left_aux_good), FadeIn(right_aux_good))
        self.wait(0.5)
        
        # 6.4 最后显示结果
        self.play(Write(result_good))
        self.wait(2)
        
        # === 第7步：动态更新示例（可选） ===
        # 💡 可以动态更新步骤和结果，但保持字数限制
        
        # 更新步骤
        step2 = self.create_step_region_content("第二步：性质分析")  # 8字，✅ 合适
        self.play(Transform(step_good, step2))
        
        # 更新结果
        result2 = self.create_result_region_content(
            "分析完成：函数性质清晰，图形美观，便于理解"  # 22字，✅ 合适
        )
        self.play(Transform(result_good, result2))
        self.wait(2)
        
        # === 第8步：清理和总结 ===
        # 💡 可以选择性地清除某些内容，为下一阶段做准备
        self.demonstrate_best_practices()
    
    def create_simple_main_content(self):
        """
        创建适中复杂度的主内容示例
        
        💡 最佳实践：
        - 避免过于复杂的图形（缩放后看不清）
        - 使用清晰的颜色对比
        - 保持合理的元素数量
        - 确保关键信息突出
        """
        # 创建简洁的坐标系
        axes = Axes(
            x_range=[-3, 3, 1],
            y_range=[0, 9, 1],
            x_length=4,
            y_length=2.5,
            axis_config={"color": self.colors['text_secondary']}
        )
        
        # 创建函数曲线
        parabola = axes.plot(
            lambda x: x**2,
            color=self.colors['primary'],  # 使用黄色突出
            stroke_width=4
        )
        
        # 添加关键点
        vertex = Dot(axes.coords_to_point(0, 0), color=self.colors['accent'], radius=0.08)
        vertex_label = Text("顶点", font_size=14, color=WHITE).next_to(vertex, DOWN)
        
        # 添加函数标签
        func_label = MathTex(r"f(x) = x^2", font_size=24, color=self.colors['primary']).next_to(axes, UP)
        
        return VGroup(axes, parabola, vertex, vertex_label, func_label)
    
    def demonstrate_best_practices(self):
        """
        演示最佳实践和常见错误对比
        
        📋 检查清单：
        ✅ 标题 ≤ 8字
        ✅ 步骤 ≤ 12字  
        ✅ 辅助标题 ≤ 6字
        ✅ 辅助项目 ≤ 5项×15字
        ✅ 结果 ≤ 40字
        ✅ 调用setup_background()
        ✅ 合理的动画顺序
        ✅ 适中的内容复杂度
        """
        
        # 显示最佳实践总结
        best_practices_title = self.create_title_region_content("最佳实践")
        best_practices_step = self.create_step_region_content("总结要点")
        
        # 创建最佳实践列表
        practices_content = self.create_left_auxiliary_content(
            "要点:",
            [
                "• 严控字数限制",
                "• 保持内容简洁", 
                "• 合理动画顺序",
                "• 测试显示效果"
            ]
        )
        
        final_result = self.create_result_region_content(
            "遵循模板规范，确保专业效果和最佳观看体验"  # 24字，✅ 合适
        )
        
        # 清除之前内容，显示总结
        self.clear()
        self.play(Write(best_practices_title), Write(best_practices_step))
        self.play(FadeIn(practices_content))
        self.play(Write(final_result))
        self.wait(3) 