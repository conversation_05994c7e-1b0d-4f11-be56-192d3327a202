# 导入必要的库
import sys
import json
from tools.gen_manim_video_toolkit import GenManimVideoToolkit

# 初始化工具
genManimCode_toolkit = GenManimVideoToolkit()

# 读取JSON文件的函数
def read_json_file(file_path):
    """
    读取JSON文件并返回JSON数据

    参数:
        file_path (str): JSON文件的路径

    返回:
        dict: 解析后的JSON数据
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        return data
    except FileNotFoundError:
        print(f"错误: 文件 '{file_path}' 不存在")
        return None
    except json.JSONDecodeError:
        print(f"错误: 文件 '{file_path}' 不是有效的JSON格式")
        return None
    except Exception as e:
        print(f"读取文件时发生错误: {str(e)}")
        return None

# JSON数据转换为字符串的函数
def json_to_string(json_data, indent=4, ensure_ascii=False):
    """
    将JSON数据转换为字符串

    参数:
        json_data (dict): JSON数据
        indent (int): 缩进空格数，默认为4
        ensure_ascii (bool): 是否确保输出ASCII编码，默认为False，允许输出Unicode字符

    返回:
        str: JSON格式的字符串
    """
    try:
        json_str = json.dumps(json_data, indent=indent, ensure_ascii=ensure_ascii)
        return json_str
    except Exception as e:
        print(f"转换JSON数据为字符串时发生错误: {str(e)}")
        return None

if __name__ == "__main__":
# 使用示例
    json_data = read_json_file(sys.argv[1])

    if json_data:
        # 将JSON数据转换为字符串（如果需要）
        # json_str = json_to_string(json_data)
        # print(json_str)
        num_args = len(sys.argv) - 1  # 减去
        if num_args == 1:
            for idx, content in enumerate(json_data):
                # 如果需要将content转换为字符串
                # content_str = json_to_string(content)

                genManimCode_toolkit.generate_manim_code(
                    json_data[0]["分镜名"],
                    content["分镜内容"], json_to_string(content), step=idx)
        elif num_args == 2:
            idx = int(sys.argv[2])
            genManimCode_toolkit.generate_manim_code(
                    json_data[0]["分镜名"],
                    json_data[idx]["分镜内容"], json_to_string(json_data[idx]), step=idx)
