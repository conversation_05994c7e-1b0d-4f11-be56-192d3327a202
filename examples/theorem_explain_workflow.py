from dotenv import load_dotenv

load_dotenv()

import asyncio
import datetime
import json
import logging
import os
import re
import sys
from typing import Any, Optional

import yaml
from camel.agents import ChatAgent as CamelChatAgent
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.types import ModelPlatformType
from loguru import logger

# 添加项目根目录到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

# 导入定理解释相关代理
from agents.scene_code_generation_agent import process_scene_file_enhanced
from agents.theorem_agents.animation_narration_agent import AnimationNarrationAgent
from agents.theorem_agents.code_generation_agent import CodeGenerationAgent
from agents.theorem_agents.scene_plan_agent import ScenePlanAgent
from agents.theorem_agents.technical_implementation_agent import TechnicalImplementationAgent
from agents.theorem_agents.vision_storyboard_agent import VisionStoryboardAgent

# 导入提示模板
# 导入工具包
from tools.manim_toolkit import ManimToolkit
from tools.rag_toolkit import RAGToolkit
from utils.format import extract_xml

# 设置日志
log_file = f"output/theorem_workflow_log_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
os.makedirs(os.path.dirname(log_file), exist_ok=True)

# 配置根日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(),  # 同时输出到控制台
    ],
)

# Scene计划输出格式提示
SCENE_PLAN_FORMAT_PROMPT = """
<SCENE_OUTLINE>
场景1: [场景名称]
- [关键内容点1]
- [关键内容点2]
...

场景2: [场景名称]
- [关键内容点1]
- [关键内容点2]
...
</SCENE_OUTLINE>
"""


class TheoremExplainWorkflow:
    """
    定理解释视频生成工作流类

    实现完整的定理解释视频生成流程，包括：
    1. 场景规划代理生成整体场景大纲
    2. 视觉故事板代理生成具体视觉表现
    3. 技术实现代理生成技术实现计划
    4. 动画叙述代理生成旁白脚本
    5. 代码生成代理生成Manim代码
    6. 视频渲染执行和错误修复
    7. 视频评估和质量控制
    """

    def __init__(self, config_path="config/config.yaml"):
        """初始化工作流"""
        # 保存配置路径
        self.config_path = config_path

        # 加载配置
        self.load_config(config_path)

        # 初始化模型
        self.model = self._create_model()
        logger.info("模型初始化完成")

        # 初始化工具包
        self.toolkits = self._initialize_toolkits()
        logger.info("工具包初始化完成")

        # 初始化代理
        self.agents = self._initialize_agents()
        logger.info("代理初始化完成")

        # 会话ID - 每次执行生成一个新的会话ID
        self.session_id = str(datetime.datetime.now().strftime("%Y%m%d_%H%M%S"))

    def load_config(self, config_path):
        """从YAML文件加载配置"""
        try:
            with open(config_path) as file:
                config = yaml.safe_load(file)

            # 设置配置属性
            self.model_config = config.get("model", {})
            self.file_config = config.get("files", {})
            self.agent_config = config.get("agents", {})
            self.manim_config = config.get("manim", {})

            # 加载工作流控制配置
            self.workflow_config = config.get("workflow", {})
            # 默认从头开始执行
            self.start_stage = self.workflow_config.get("start_stage", None)
            # 加载是否启用RAG和上下文学习
            self.enable_rag = self.workflow_config.get("enable_rag", True)
            self.enable_context_learning = self.workflow_config.get("enable_context_learning", True)
            self.enable_visual_fix = self.workflow_config.get("enable_visual_fix", True)
            
            # 加载是否启用优化的场景实现模式
            self.use_optimized_implementation = self.workflow_config.get("use_optimized_implementation", False)
            
            # 加载各个关键步骤的开关配置
            self.step_switches = self.workflow_config.get("step_switches", {})
            self.enable_scene_plan = self.step_switches.get("enable_scene_plan", True)
            self.enable_vision_storyboard = self.step_switches.get("enable_vision_storyboard", True)
            self.enable_technical_implementation = self.step_switches.get("enable_technical_implementation", True)
            self.enable_animation_narration = self.step_switches.get("enable_animation_narration", True)
            self.enable_code_generation = self.step_switches.get("enable_code_generation", True)
            self.enable_rendering = self.step_switches.get("enable_rendering", True)
            self.enable_evaluation = self.step_switches.get("enable_evaluation", True)
            self.enable_video_combine = self.step_switches.get("enable_video_combine", True)

            # 设置各阶段的输出目录和文件
            self.output_dir = self.file_config.get("output_dir", "output")

            # 设置各阶段的输出文件路径模板
            self.stage_file_templates = {
                "scene_plan": self.workflow_config.get("scene_plan_file", "output/{topic}/scene_outline.txt"),
                "vision_storyboard": self.workflow_config.get(
                    "vision_storyboard_dir", "output/{topic}/vision_storyboard"
                ),
                "technical_implementation": self.workflow_config.get(
                    "technical_implementation_dir", "output/{topic}/technical_implementation"
                ),
                "animation_narration": self.workflow_config.get(
                    "animation_narration_dir", "output/{topic}/animation_narration"
                ),
                "code": self.workflow_config.get("code_dir", "output/{topic}/code"),
                "rendered_video": self.workflow_config.get("video_dir", "output/{topic}/videos"),
                "evaluation": self.workflow_config.get("evaluation_file", "output/{topic}/evaluation.json"),
            }

            logger.info("从 %s 加载配置", config_path)
        except Exception as e:
            logger.error(f"加载配置错误: {str(e)}")
            # 设置默认值
            self.model_config = {"type": "openai/gpt-4o-mini"}
            self.file_config = {"output_dir": "output"}
            self.agent_config = {}
            self.manim_config = {}
            self.workflow_config = {}
            self.start_stage = None
            self.stage_file_templates = {}
            self.enable_rag = True
            self.enable_context_learning = True
            self.enable_visual_fix = True
            
            # 设置默认的步骤开关
            self.step_switches = {}
            self.enable_scene_plan = True
            self.enable_vision_storyboard = True
            self.enable_technical_implementation = True
            self.enable_animation_narration = True
            self.enable_code_generation = True
            self.enable_rendering = True
            self.enable_evaluation = True
            self.enable_video_combine = True
            
            self.output_dir = "output"

    def _create_model(self):
        """创建模型实例"""
        # 从配置中获取API设置
        api_config = self.model_config.get("api", {})

        return ModelFactory.create(
            model_platform=ModelPlatformType.OPENROUTER,
            model_type=self.model_config.get("type", "openai/gpt-4o-mini"),
            api_key=api_config.get("openrouter_api_key"),
            url=api_config.get("openrouter_api_base_url"),
        )

    def _initialize_toolkits(self):
        """初始化各种工具包"""
        return {
            "manim": ManimToolkit(),
            "rag": RAGToolkit(
                chroma_db_path=self.workflow_config.get("chroma_db_path", "data/rag/chroma_db"),
                manim_docs_path=self.workflow_config.get("manim_docs_path", "data/rag/manim_docs"),
                embedding_model=self.workflow_config.get("embedding_model", "text-embedding-3-large"),
            )
            if self.enable_rag
            else None,
        }

    def _initialize_agents(self):
        """初始化所有需要的代理"""
        agents = {}

        # 场景规划代理
        agents["scene_plan"] = ScenePlanAgent(model=self.model, config_path=self.config_path)

        # 视觉故事板代理
        agents["vision_storyboard"] = VisionStoryboardAgent(model=self.model, config_path=self.config_path)

        # 技术实现代理
        agents["technical_implementation"] = TechnicalImplementationAgent(
            model=self.model, config_path=self.config_path
        )

        # 动画叙述代理
        agents["animation_narration"] = AnimationNarrationAgent(model=self.model, config_path=self.config_path)

        # 代码生成代理
        agents["code_generator"] = CodeGenerationAgent(model=self.model, config_path=self.config_path)

        # 代码修复代理
        agents["code_fixer"] = CamelChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="Code Fixer",
                content="你是一位专业的Manim代码修复专家，善于识别和修复Manim代码中的错误，特别是布局、动画时序和空间约束问题。",
            ),
            model=self.model,
        )

        # 视觉反馈代理
        agents["visual_feedback"] = CamelChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="Visual Feedback Agent",
                content="你是一位专业的视觉反馈专家，善于分析视频渲染结果，检测视觉问题并提供改进建议，特别是对元素位置、大小、层次结构和动画时序的优化。",
            ),
            model=self.model,
        )

        # 评估代理
        agents["evaluator"] = CamelChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="Evaluation Agent",
                content="你是一位专业的教育视频评估专家，善于从教学效果、视觉表现、内容准确性和观众体验等角度评估定理讲解视频的质量。",
            ),
            model=self.model,
        )

        return agents

    async def generate_scene_planning(self, topic, description, file_path: Optional[str] = None) -> dict[str, Any]:
        """生成场景规划"""
        try:
            # 获取场景规划代理
            scene_plan_agent = self.agents.get("scene_plan")
            if not scene_plan_agent:
                logger.error("Scene plan agent not found")
                return {"status": "error", "message": "Scene plan agent not found"}

            # 准备RAG上下文
            rag_context = None
            if self.workflow_config.get("enable_rag", False):
                query = f"场景规划：{topic}"
                logger.info(f"[RAG-场景规划] 查询: {query}")
                rag_context = self.toolkits["rag"].search_relevant_context(query=query, limit=5)
                logger.info(f"[RAG-场景规划] 检索结果数量: {len(rag_context) if rag_context else 0}")
                if rag_context:
                    logger.info(f"[RAG-场景规划] 检索结果预览: {str(rag_context)[:200]}...")

            # 生成场景规划
            scene_plan_result = scene_plan_agent.generate(
                topic=topic, description=description, rag_context=rag_context, session_id=self.session_id, file_path=file_path
            )

            # 保存场景规划结果
            if scene_plan_result.get("status") == "success":
                scene_plan = scene_plan_result.get("scene_outline", "")
                output_path_template = self.workflow_config.get(
                    "scene_plan_file", f"output/{topic.replace(' ', '_')}/scene_outline.txt"
                )
                # 格式化路径模板，替换 {topic} 占位符
                output_path = output_path_template.format(topic=topic.replace(" ", "_"))

                # 确保输出目录存在
                os.makedirs(os.path.dirname(output_path), exist_ok=True)

                # 保存场景规划到文件
                with open(output_path, "w", encoding="utf-8") as f:
                    f.write(scene_plan)

                logger.info(f"Scene plan saved to {output_path}")

                return {"status": "success", "scene_plan": scene_plan, "file_path": output_path}
            else:
                return scene_plan_result
        except Exception as e:
            logger.error(f"Error generating scene planning: {str(e)}")
            return {"status": "error", "message": str(e)}

    async def generate_vision_storyboard(self, topic, description, scene_outline) -> dict[str, Any]:
        """生成视觉故事板"""
        try:
            # 获取视觉故事板代理
            vision_agent = self.agents.get("vision_storyboard")
            if not vision_agent:
                logger.error("Vision storyboard agent not found")
                return {"status": "error", "message": "Vision storyboard agent not found"}

            # 解析场景大纲，提取场景数量
            scenes = self._parse_scene_outline(scene_outline)
            if not scenes:
                logger.error("Failed to parse scene outline")
                return {"status": "error", "message": "Failed to parse scene outline"}

            results = []
            for scene_number, scene_info in enumerate(scenes, 1):
                logger.info(f"Generating vision storyboard for scene {scene_number}")

                # 检测相关插件
                logger.info(f"[RAG-视觉故事板-插件检测-场景{scene_number}] 正在检测相关插件...")
                relevant_plugins = (
                    self.toolkits["rag"].detect_relevant_plugins(topic=topic, description=f"{description} {scene_info}")
                    if self.workflow_config.get("enable_rag", False)
                    else ""
                )
                logger.info(f"[RAG-视觉故事板-插件检测-场景{scene_number}] 检测到相关插件: {relevant_plugins}")

                # 准备RAG上下文
                rag_context = None
                if self.workflow_config.get("enable_rag", False):
                    rag_context = self.toolkits["rag"].search_relevant_context(  # noqa: F841
                        query=f"视觉故事板：{topic} {scene_info}", limit=5
                    )

                # 生成视觉故事板
                vision_storyboard = await self._generate_vision_storyboard(
                    scene_number,
                    topic,
                    description,
                    scene_info,
                    relevant_plugins,
                    f"{topic.replace(' ', '_')}_{scene_number}",
                )

                # 保存视觉故事板结果
                if vision_storyboard.get("status") == "success":
                    storyboard = vision_storyboard.get("vision_storyboard", "")
                    output_dir = self.workflow_config.get("vision_storyboard_dir", "output/{topic}/vision_storyboard")
                    output_dir = output_dir.format(topic=topic.replace(" ", "_"))

                    # 确保输出目录存在
                    os.makedirs(output_dir, exist_ok=True)

                    # 保存视觉故事板到文件
                    output_path = os.path.join(output_dir, f"scene_{scene_number}_storyboard.txt")
                    with open(output_path, "w", encoding="utf-8") as f:
                        f.write(storyboard)

                    logger.info(f"Vision storyboard for scene {scene_number} saved to {output_path}")

                    results.append(
                        {"scene_number": scene_number, "vision_storyboard": storyboard, "file_path": output_path}
                    )
                else:
                    logger.error(
                        f"Failed to generate vision storyboard for scene {scene_number}: {vision_storyboard.get('message', 'Unknown error')}"
                    )
                    results.append(
                        {
                            "scene_number": scene_number,
                            "status": "error",
                            "message": vision_storyboard.get("message", "Unknown error"),
                        }
                    )

            return {"status": "success", "results": results}
        except Exception as e:
            logger.error(f"Error generating vision storyboard: {str(e)}")
            return {"status": "error", "message": str(e)}

    async def generate_technical_implementation(
        self, topic, description, scene_outline, vision_storyboards
    ) -> dict[str, Any]:
        """生成技术实现计划"""
        try:
            # 获取技术实现代理
            tech_agent = self.agents.get("technical_implementation")
            if not tech_agent:
                logger.error("Technical implementation agent not found")
                return {"status": "error", "message": "Technical implementation agent not found"}

            results = []
            for storyboard_info in vision_storyboards:
                scene_number = storyboard_info.get("scene_number")
                vision_storyboard = storyboard_info.get("vision_storyboard")

                if not vision_storyboard:
                    logger.error(f"Vision storyboard for scene {scene_number} not found")
                    continue

                logger.info(f"Generating technical implementation for scene {scene_number}")

                # 提取当前场景的大纲
                scene_info = self._get_scene_by_number(scene_outline, scene_number)
                if not scene_info:
                    logger.error(f"Scene outline for scene {scene_number} not found")
                    continue

                # 检测相关插件
                logger.info(f"[RAG-技术实现-插件检测-场景{scene_number}] 正在检测相关插件...")
                relevant_plugins = (
                    self.toolkits["rag"].detect_relevant_plugins(
                        topic=topic, description=f"{description} {scene_info} {vision_storyboard}"
                    )
                    if self.workflow_config.get("enable_rag", False)
                    else ""
                )
                logger.info(f"[RAG-技术实现-插件检测-场景{scene_number}] 检测到相关插件: {relevant_plugins}")

                # 准备RAG上下文
                rag_context = None
                if self.workflow_config.get("enable_rag", False):
                    query = f"技术实现：{topic} {scene_info} {vision_storyboard[:200]}"
                    logger.info(f"[RAG-技术实现-查询-场景{scene_number}] 查询: {query}")
                    rag_context = self.toolkits["rag"].search_relevant_context(query=query, limit=5)
                    logger.info(
                        f"[RAG-技术实现-查询-场景{scene_number}] 检索结果数量: {len(rag_context) if rag_context else 0}"
                    )
                    if rag_context:
                        logger.info(f"[RAG-技术实现-查询-场景{scene_number}] 检索结果预览: {str(rag_context)[:200]}...")

                # 生成技术实现计划
                technical_implementation = await tech_agent.generate(
                    topic=topic,
                    description=description,
                    scene_number=scene_number,
                    scene_outline=scene_info,
                    vision_storyboard=vision_storyboard,
                    relevant_plugins=relevant_plugins,
                    session_id=self.session_id,
                )

                # 保存技术实现计划结果
                if technical_implementation.get("status") == "success":
                    implementation = technical_implementation.get("technical_implementation", "")
                    output_dir = self.workflow_config.get(
                        "technical_implementation_dir", "output/{topic}/technical_implementation"
                    )
                    output_dir = output_dir.format(topic=topic.replace(" ", "_"))

                    # 确保输出目录存在
                    os.makedirs(output_dir, exist_ok=True)

                    # 保存技术实现计划到文件
                    output_path = os.path.join(output_dir, f"scene_{scene_number}_implementation.txt")
                    with open(output_path, "w", encoding="utf-8") as f:
                        f.write(implementation)

                    logger.info(f"Technical implementation for scene {scene_number} saved to {output_path}")

                    results.append(
                        {
                            "scene_number": scene_number,
                            "technical_implementation": implementation,
                            "file_path": output_path,
                        }
                    )
                else:
                    logger.error(
                        f"Failed to generate technical implementation for scene {scene_number}: {technical_implementation.get('message', 'Unknown error')}"
                    )
                    results.append(
                        {
                            "scene_number": scene_number,
                            "status": "error",
                            "message": technical_implementation.get("message", "Unknown error"),
                        }
                    )

            return {"status": "success", "results": results}
        except Exception as e:
            logger.error(f"Error generating technical implementation: {str(e)}")
            return {"status": "error", "message": str(e)}

    async def generate_animation_narration(
        self, topic, description, scene_outline, vision_storyboards, technical_implementations
    ) -> dict[str, Any]:
        """生成动画叙述"""
        try:
            # 获取动画叙述代理
            narration_agent = self.agents.get("animation_narration")
            if not narration_agent:
                logger.error("Animation narration agent not found")
                return {"status": "error", "message": "Animation narration agent not found"}

            results = []
            for tech_info in technical_implementations:
                scene_number = tech_info.get("scene_number")
                technical_implementation = tech_info.get("technical_implementation")

                if not technical_implementation:
                    logger.error(f"Technical implementation for scene {scene_number} not found")
                    continue

                # 获取对应的视觉故事板
                vision_storyboard = self._get_storyboard_by_scene_number(vision_storyboards, scene_number)
                if not vision_storyboard:
                    logger.error(f"Vision storyboard for scene {scene_number} not found")
                    continue

                logger.info(f"Generating animation narration for scene {scene_number}")

                # 提取当前场景的大纲
                scene_info = self._get_scene_by_number(scene_outline, scene_number)
                if not scene_info:
                    logger.error(f"Scene outline for scene {scene_number} not found")
                    continue

                # 检测相关插件
                logger.info(f"[RAG-动画叙述-插件检测-场景{scene_number}] 正在检测相关插件...")
                relevant_plugins = (
                    self.toolkits["rag"].detect_relevant_plugins(
                        topic=topic, description=f"{description} {scene_info} {vision_storyboard}"
                    )
                    if self.workflow_config.get("enable_rag", False)
                    else ""
                )
                logger.info(f"[RAG-动画叙述-插件检测-场景{scene_number}] 检测到相关插件: {relevant_plugins}")

                # 准备RAG上下文
                rag_context = None
                if self.workflow_config.get("enable_rag", False):
                    query = f"动画叙述：{topic} {scene_info} {vision_storyboard[:100]} {technical_implementation[:100]}"
                    logger.info(f"[RAG-动画叙述-查询-场景{scene_number}] 查询: {query}")
                    rag_context = self.toolkits["rag"].search_relevant_context(query=query, limit=5)
                    logger.info(
                        f"[RAG-动画叙述-查询-场景{scene_number}] 检索结果数量: {len(rag_context) if rag_context else 0}"
                    )
                    if rag_context:
                        logger.info(f"[RAG-动画叙述-查询-场景{scene_number}] 检索结果预览: {str(rag_context)[:200]}...")

                # 生成动画叙述
                animation_narration = await narration_agent.generate(
                    topic=topic,
                    description=description,
                    scene_number=scene_number,
                    scene_outline=scene_info,
                    vision_storyboard=vision_storyboard,
                    technical_implementation=technical_implementation,
                    relevant_plugins=relevant_plugins,
                    session_id=self.session_id,
                )

                # 保存动画叙述结果
                if animation_narration.get("status") == "success":
                    narration = animation_narration.get("animation_narration", "")
                    output_dir = self.workflow_config.get(
                        "animation_narration_dir", "output/{topic}/animation_narration"
                    )
                    output_dir = output_dir.format(topic=topic.replace(" ", "_"))

                    # 确保输出目录存在
                    os.makedirs(output_dir, exist_ok=True)

                    # 保存动画叙述到文件
                    output_path = os.path.join(output_dir, f"scene_{scene_number}_narration.txt")
                    with open(output_path, "w", encoding="utf-8") as f:
                        f.write(narration)

                    logger.info(f"Animation narration for scene {scene_number} saved to {output_path}")

                    results.append(
                        {"scene_number": scene_number, "animation_narration": narration, "file_path": output_path}
                    )
                else:
                    logger.error(
                        f"Failed to generate animation narration for scene {scene_number}: {animation_narration.get('message', 'Unknown error')}"
                    )
                    results.append(
                        {
                            "scene_number": scene_number,
                            "status": "error",
                            "message": animation_narration.get("message", "Unknown error"),
                        }
                    )

            return {"status": "success", "results": results}
        except Exception as e:
            logger.error(f"Error generating animation narration: {str(e)}")
            return {"status": "error", "message": str(e)}

    async def generate_scene_implementation(
        self, topic: str, description: str, scene_plan: str
    ) -> dict[int, dict[str, str]]:
        """生成场景实现计划，包括视觉故事板、技术实现和动画叙述"""
        logger.info(f"开始生成场景实现计划: {topic}")

        scene_implementations = {}

        # 从scene_plan字符串中提取场景
        scenes = self._parse_scene_outline(scene_plan)

        # 检测需要使用的插件
        relevant_plugins = []
        if self.enable_rag and self.toolkits["rag"]:
            logger.info(f"[RAG-插件检测] 正在检测相关插件... 主题: {topic}")
            relevant_plugins = self.toolkits["rag"].detect_relevant_plugins(topic=topic, description=description)
            logger.info(f"[RAG-插件检测] 检测到相关插件: {relevant_plugins}")

        # 为每个场景并行生成实现计划
        implementation_tasks = []

        for scene_number, scene_outline_i in enumerate(scenes, 1):
            implementation_tasks.append(
                self._generate_scene_implementation_single(
                    topic=topic,
                    description=description,
                    scene_outline_i=scene_outline_i,
                    scene_number=scene_number,
                    file_prefix=topic.replace(" ", "_"),
                    relevant_plugins=relevant_plugins,
                )
            )

        # 并行执行所有场景的实现计划生成
        implementations = await asyncio.gather(*implementation_tasks)

        # 整理结果
        for scene_number, implementation in enumerate(implementations, 1):
            scene_implementations[scene_number] = implementation

        return scene_implementations

    async def _generate_scene_implementation_single(
        self,
        topic: str,
        description: str,
        scene_outline_i: str,
        scene_number: int,
        file_prefix: str,
        relevant_plugins: list[str],
    ) -> dict[str, str]:
        """
        为单个场景生成实现计划

        Args:
            topic: 定理主题
            description: 定理描述
            scene_outline_i: 当前场景大纲
            scene_number: 场景编号
            file_prefix: 文件前缀
            relevant_plugins: 相关插件列表

        Returns:
            Dict: 包含视觉故事板、技术实现和动画叙述的字典
        """
        # 创建场景目录
        scene_dir = os.path.join(self.output_dir, file_prefix, f"scene{scene_number}")
        os.makedirs(scene_dir, exist_ok=True)

        # 定义文件路径
        vision_path = os.path.join(scene_dir, f"scene{scene_number}_vision_storyboard.txt")
        technical_path = os.path.join(scene_dir, f"scene{scene_number}_technical_implementation.txt")
        narration_path = os.path.join(scene_dir, f"scene{scene_number}_animation_narration.txt")

        # 检查是否加载现有结果
        if self.start_stage and self.start_stage not in ["scene_plan", "vision_storyboard"]:
            if os.path.exists(vision_path) and os.path.exists(technical_path) and os.path.exists(narration_path):
                try:
                    with open(vision_path) as f:
                        vision_storyboard = f.read()
                    with open(technical_path) as f:
                        technical_implementation = f.read()
                    with open(narration_path) as f:
                        animation_narration = f.read()
                    logger.info(f"已加载场景{scene_number}的实现计划")
                    return {
                        "vision_storyboard": vision_storyboard,
                        "technical_implementation": technical_implementation,
                        "animation_narration": animation_narration,
                    }
                except Exception as e:
                    logger.error(f"加载场景{scene_number}实现计划失败: {str(e)}")

        # 1. 生成视觉故事板
        vision_storyboard = ""
        if self.enable_vision_storyboard:
            logger.info(f"生成场景{scene_number}的视觉故事板")
            vision_storyboard = await self._generate_vision_storyboard(
                scene_number, topic, description, scene_outline_i, relevant_plugins, file_prefix
            )
            # 保存视觉故事板
            with open(vision_path, "w") as f:
                f.write(vision_storyboard)
            logger.info(f"视觉故事板已保存到: {vision_path}")
        else:
            logger.info(f"视觉故事板步骤已禁用，场景{scene_number}跳过视觉故事板生成")
            # 尝试从文件加载已有的视觉故事板
            try:
                if os.path.exists(vision_path):
                    with open(vision_path, "r") as f:
                        vision_storyboard = f.read()
                    logger.info(f"已从文件加载视觉故事板: {vision_path}")
                else:
                    vision_storyboard = f"场景{scene_number}视觉故事板（已跳过生成）"
            except Exception as e:
                logger.error(f"加载视觉故事板失败: {str(e)}")
                vision_storyboard = f"场景{scene_number}视觉故事板（已跳过生成）"

        # 2. 生成技术实现计划
        technical_implementation = ""
        if self.enable_technical_implementation:
            logger.info(f"生成场景{scene_number}的技术实现计划")
            technical_implementation = await self._generate_technical_implementation(
                scene_number, topic, description, scene_outline_i, vision_storyboard, relevant_plugins, file_prefix
            )
            # 保存技术实现计划
            with open(technical_path, "w") as f:
                f.write(technical_implementation)
            logger.info(f"技术实现计划已保存到: {technical_path}")
        else:
            logger.info(f"技术实现步骤已禁用，场景{scene_number}跳过技术实现计划生成")
            # 尝试从文件加载已有的技术实现计划
            try:
                if os.path.exists(technical_path):
                    with open(technical_path, "r") as f:
                        technical_implementation = f.read()
                    logger.info(f"已从文件加载技术实现计划: {technical_path}")
                else:
                    technical_implementation = f"场景{scene_number}技术实现计划（已跳过生成）"
            except Exception as e:
                logger.error(f"加载技术实现计划失败: {str(e)}")
                technical_implementation = f"场景{scene_number}技术实现计划（已跳过生成）"

        # 3. 生成动画叙述
        animation_narration = ""
        if self.enable_animation_narration:
            logger.info(f"生成场景{scene_number}的动画叙述")
            animation_narration = await self._generate_animation_narration(
                scene_number,
                topic,
                description,
                scene_outline_i,
                vision_storyboard,
                technical_implementation,
                relevant_plugins,
                file_prefix,
            )
            # 保存动画叙述
            with open(narration_path, "w") as f:
                f.write(animation_narration)
            logger.info(f"动画叙述已保存到: {narration_path}")
        else:
            logger.info(f"动画叙述步骤已禁用，场景{scene_number}跳过动画叙述生成")
            # 尝试从文件加载已有的动画叙述
            try:
                if os.path.exists(narration_path):
                    with open(narration_path, "r") as f:
                        animation_narration = f.read()
                    logger.info(f"已从文件加载动画叙述: {narration_path}")
                else:
                    animation_narration = f"场景{scene_number}动画叙述（已跳过生成）"
            except Exception as e:
                logger.error(f"加载动画叙述失败: {str(e)}")
                animation_narration = f"场景{scene_number}动画叙述（已跳过生成）"

        return {
            "vision_storyboard": vision_storyboard,
            "technical_implementation": technical_implementation,
            "animation_narration": animation_narration,
        }

    async def _generate_vision_storyboard(
        self,
        scene_number: int,
        topic: str,
        description: str,
        scene_outline: str,
        relevant_plugins: list[str],
        file_prefix: str,
    ) -> str:
        """
        生成视觉故事板

        Args:
            scene_number: 场景编号
            topic: 定理主题
            description: 定理描述
            scene_outline: 场景大纲
            relevant_plugins: 相关插件列表
            file_prefix: 文件前缀

        Returns:
            str: 生成的视觉故事板
        """
        logger.info(f"生成场景{scene_number}的视觉故事板")

        # 获取视觉故事板代理
        vision_agent = self.agents["vision_storyboard"]

        # 获取RAG上下文（如果启用）
        rag_context = None
        if self.enable_rag and self.toolkits["rag"]:
            # 为视觉故事板生成查询
            queries = self.toolkits["rag"].generate_queries_for_vision_storyboard(
                scene_outline, relevant_plugins, topic, scene_number, self.session_id
            )
            logger.info(f"[RAG-视觉故事板-场景{scene_number}] 生成的查询数量: {len(queries) if queries else 0}")
            if queries:
                for i, q in enumerate(queries):
                    logger.info(f"[RAG-视觉故事板-场景{scene_number}] 查询{i+1}: {q}")
            # 使用生成的查询获取上下文
            rag_context = self.toolkits["rag"].search_with_queries(queries, limit_per_query=3)
            logger.info(f"[RAG-视觉故事板-场景{scene_number}] 检索结果长度: {len(rag_context) if rag_context else 0}")
            if rag_context:
                logger.info(f"[RAG-视觉故事板-场景{scene_number}] 检索结果预览: {str(rag_context)[:300]}...")

        # 生成视觉故事板
        vision_result = await vision_agent.generate(
            topic=topic,
            description=description,
            scene_number=scene_number,
            scene_outline=scene_outline,
            relevant_plugins=relevant_plugins,
            session_id=self.session_id,
        )

        # 检查生成结果是否为字典类型
        if isinstance(vision_result, dict):
            vision_storyboard = vision_result.get("vision_storyboard", "")
            # 如果vision_storyboard为空但有status为success，提取其他可能有用的字段
            if not vision_storyboard and vision_result.get("status") == "success":
                # 尝试将字典转换为可读字符串
                import json

                vision_storyboard = json.dumps(vision_result, ensure_ascii=False, indent=2)
            elif vision_result.get("status") == "error":
                # 如果是错误状态，记录错误并返回错误消息
                error_msg = vision_result.get("message", "生成视觉故事板出错")
                logger.error(f"生成视觉故事板失败: {error_msg}")
                return f"<SCENE_VISION_STORYBOARD>\n生成失败: {error_msg}\n</SCENE_VISION_STORYBOARD>"
        else:
            vision_storyboard = vision_result

        # 提取标签中的内容
        vision_match = extract_xml(vision_storyboard, "SCENE_VISION_STORYBOARD")
        return vision_match if vision_match else vision_storyboard

    async def _generate_technical_implementation(
        self,
        scene_number: int,
        topic: str,
        description: str,
        scene_outline: str,
        vision_storyboard: str,
        relevant_plugins: list[str],
        file_prefix: str,
    ) -> str:
        """
        生成技术实现计划

        Args:
            scene_number: 场景编号
            topic: 定理主题
            description: 定理描述
            scene_outline: 场景大纲
            vision_storyboard: 视觉故事板
            relevant_plugins: 相关插件列表
            file_prefix: 文件前缀

        Returns:
            str: 生成的技术实现计划
        """
        logger.info(f"生成场景{scene_number}的技术实现计划")

        # 获取技术实现代理
        tech_agent = self.agents["technical_implementation"]

        # 获取RAG上下文（如果启用）
        rag_context = None
        if self.enable_rag and self.toolkits["rag"]:
            # 为技术实现生成查询
            queries = self.toolkits["rag"].generate_queries_for_technical(
                vision_storyboard, relevant_plugins, topic, scene_number, self.session_id
            )
            logger.info(f"[RAG-技术实现-场景{scene_number}] 生成的查询数量: {len(queries) if queries else 0}")
            if queries:
                for i, q in enumerate(queries):
                    logger.info(f"[RAG-技术实现-场景{scene_number}] 查询{i+1}: {q}")
            # 使用生成的查询获取上下文
            rag_context = self.toolkits["rag"].search_with_queries(queries, limit_per_query=3)
            logger.info(f"[RAG-技术实现-场景{scene_number}] 检索结果长度: {len(rag_context) if rag_context else 0}")
            if rag_context:
                logger.info(f"[RAG-技术实现-场景{scene_number}] 检索结果预览: {str(rag_context)[:300]}...")

        # 生成技术实现计划
        tech_result = await tech_agent.generate(
            topic=topic,
            description=description,
            scene_number=scene_number,
            scene_outline=scene_outline,
            vision_storyboard=vision_storyboard,
            relevant_plugins=relevant_plugins,
            session_id=self.session_id,
        )

        # 检查生成结果是否为字典类型
        if isinstance(tech_result, dict):
            technical_implementation = tech_result.get("technical_implementation", "")
            # 如果technical_implementation为空但有status为success，提取其他可能有用的字段
            if not technical_implementation and tech_result.get("status") == "success":
                # 尝试将字典转换为可读字符串
                import json

                technical_implementation = json.dumps(tech_result, ensure_ascii=False, indent=2)
            elif tech_result.get("status") == "error":
                # 如果是错误状态，记录错误并返回错误消息
                error_msg = tech_result.get("message", "生成技术实现计划出错")
                logger.error(f"生成技术实现计划失败: {error_msg}")
                return f"<TECHNICAL_IMPLEMENTATION>\n生成失败: {error_msg}\n</TECHNICAL_IMPLEMENTATION>"
        else:
            technical_implementation = tech_result

        # 提取标签中的内容
        tech_match = extract_xml(technical_implementation, "TECHNICAL_IMPLEMENTATION")
        return tech_match if tech_match else technical_implementation

    async def _generate_animation_narration(
        self,
        scene_number: int,
        topic: str,
        description: str,
        scene_outline: str,
        vision_storyboard: str,
        technical_implementation: str,
        relevant_plugins: list[str],
        file_prefix: str,
    ) -> str:
        """
        生成动画旁白

        Args:
            scene_number: 场景编号
            topic: 定理主题
            description: 定理描述
            scene_outline: 场景大纲
            vision_storyboard: 视觉故事板
            technical_implementation: 技术实现计划
            relevant_plugins: 相关插件列表
            file_prefix: 文件前缀

        Returns:
            str: 生成的动画旁白
        """
        logger.info(f"生成场景{scene_number}的动画旁白")

        # 获取动画叙述代理
        narration_agent = self.agents["animation_narration"]

        # 获取RAG上下文（如果启用）
        rag_context = None
        if self.enable_rag and self.toolkits["rag"]:
            # 为动画叙述生成查询
            queries = self.toolkits["rag"].generate_queries_for_narration(
                vision_storyboard, relevant_plugins, topic, scene_number, self.session_id
            )
            logger.info(f"[RAG-动画叙述-场景{scene_number}] 生成的查询数量: {len(queries) if queries else 0}")
            if queries:
                for i, q in enumerate(queries):
                    logger.info(f"[RAG-动画叙述-场景{scene_number}] 查询{i+1}: {q}")
            # 使用生成的查询获取上下文
            rag_context = self.toolkits["rag"].search_with_queries(queries, limit_per_query=3)
            logger.info(f"[RAG-动画叙述-场景{scene_number}] 检索结果长度: {len(rag_context) if rag_context else 0}")
            if rag_context:
                logger.info(f"[RAG-动画叙述-场景{scene_number}] 检索结果预览: {str(rag_context)[:300]}...")

        # 生成动画叙述
        narration_result = await narration_agent.generate(
            topic=topic,
            description=description,
            scene_number=scene_number,
            scene_outline=scene_outline,
            vision_storyboard=vision_storyboard,
            technical_implementation=technical_implementation,
            relevant_plugins=relevant_plugins,
            session_id=self.session_id,
        )

        # 检查生成结果是否为字典类型
        if isinstance(narration_result, dict):
            animation_narration = narration_result.get("animation_narration", "")
            # 如果animation_narration为空但有status为success，提取其他可能有用的字段
            if not animation_narration and narration_result.get("status") == "success":
                # 尝试将字典转换为可读字符串
                import json

                animation_narration = json.dumps(narration_result, ensure_ascii=False, indent=2)
            elif narration_result.get("status") == "error":
                # 如果是错误状态，记录错误并返回错误消息
                error_msg = narration_result.get("message", "生成动画叙述出错")
                logger.error(f"生成动画叙述失败: {error_msg}")
                return f"<ANIMATION_NARRATION>\n生成失败: {error_msg}\n</ANIMATION_NARRATION>"
        else:
            animation_narration = narration_result

        # 提取标签中的内容
        narration_match = extract_xml(animation_narration, "ANIMATION_NARRATION")
        return narration_match if narration_match else animation_narration

    async def generate_code_and_render(
        self, topic: str, description: str, scene_implementations: dict[int, dict[str, str]]
    ) -> dict[int, dict[str, Any]]:
        """
        生成Manim代码并渲染视频

        Args:
            topic: 定理主题
            description: 定理描述
            scene_implementations: 场景实现计划

        Returns:
            Dict: 场景编号到渲染结果的映射
        """
        logger.info(f"开始生成代码并渲染视频: {topic}")

        file_prefix = topic.lower().replace(" ", "_").replace("-", "_")
        scene_results = {}

        # 检测需要使用的插件
        relevant_plugins = []
        if self.enable_rag and self.toolkits["rag"]:
            logger.info(f"[RAG-代码渲染-插件检测] 正在检测相关插件... 主题: {topic}")
            relevant_plugins = self.toolkits["rag"].detect_relevant_plugins(topic, description)
            logger.info(f"[RAG-代码渲染-插件检测] 检测到相关插件: {relevant_plugins}")

        # 为每个场景生成代码和渲染视频
        for scene_number, implementation in scene_implementations.items():
            if self.enable_code_generation or self.enable_rendering:
                logger.info(f"开始处理场景{scene_number}")
                
                # 确定使用哪个描述来生成代码
                scene_description = ""
                if self.enable_technical_implementation:
                    scene_description = implementation.get("technical_implementation", "")
                    if not scene_description:
                        logger.warning(f"场景{scene_number}的技术实现为空")
                        scene_description = implementation.get("vision_storyboard", "")
                        logger.info(f"使用视觉故事板作为备选描述")
                else:
                    # 当技术实现被禁用时，直接使用视觉故事板
                    scene_description = implementation.get("vision_storyboard", "")
                    logger.info(f"技术实现已禁用，直接使用视觉故事板生成代码")

                if not scene_description:
                    logger.error(f"场景{scene_number}没有可用的描述来生成代码")
                    scene_results[scene_number] = {
                        "success": False,
                        "message": "没有可用的场景描述",
                        "skipped": True
                    }
                    continue

                # 根据开关控制是否执行代码生成和渲染
                if self.enable_code_generation and self.enable_rendering:
                    scene_result = process_scene_file_enhanced(
                        scene_description=scene_description,
                        output_dir=os.path.join(self.output_dir, file_prefix, f"scene{scene_number}", "code"),
                        max_iterations=3,
                        quality="l",
                        scene_num=scene_number,
                        topic=topic,
                    )
                elif self.enable_code_generation:
                    # 只生成代码，不渲染
                    logger.info(f"场景{scene_number}：只生成代码，不渲染")
                    scene_result = process_scene_file_enhanced(
                        scene_description=scene_description,
                        output_dir=os.path.join(self.output_dir, file_prefix, f"scene{scene_number}", "code"),
                        max_iterations=3,
                        quality="l",
                        scene_num=scene_number,
                        topic=topic,
                    )
                    scene_result["rendering_skipped"] = True
                elif self.enable_rendering:
                    # 只渲染，不生成代码（假设代码已存在）
                    logger.info(f"场景{scene_number}：只渲染，不生成代码")
                    scene_result = process_scene_file_enhanced(
                        scene_description=scene_description,
                        output_dir=os.path.join(self.output_dir, file_prefix, f"scene{scene_number}", "code"),
                        max_iterations=3,
                        quality="l",
                        scene_num=scene_number,
                        topic=topic,
                    )
                    scene_result["code_generation_skipped"] = True
                else:
                    # 两者都禁用
                    scene_result = {
                        "success": False,
                        "message": "代码生成和渲染步骤均已禁用",
                        "skipped": True
                    }
                logger.info(f"scene[{scene_number}]: {scene_result}")
                scene_results[scene_number] = scene_result
            else:
                logger.info(f"代码生成和渲染步骤已禁用，场景{scene_number}跳过处理")
                scene_results[scene_number] = {
                    "success": False,
                    "message": "代码生成和渲染步骤已禁用",
                    "skipped": True
                }

        return scene_results

    async def evaluate_video(
        self, topic: str, description: str, scene_results: dict[int, dict[str, Any]]
    ) -> dict[str, Any]:
        """
        评估生成的视频质量

        Args:
            topic: 定理主题
            description: 定理描述
            scene_results: 场景渲染结果

        Returns:
            Dict: 评估结果
        """
        logger.info(f"评估视频质量: {topic}")

        file_prefix = topic.lower().replace(" ", "_").replace("-", "_")

        # 检查所有场景是否成功渲染
        all_scenes_success = all(result.get("success", False) for result in scene_results.values())
        if not all_scenes_success:
            logger.warning("部分场景渲染失败，这可能影响评估结果")

        # 收集所有视频路径进行评估
        video_paths = []
        for scene_number, result in scene_results.items():
            video_path = result.get("final_video_path")
            if video_path and os.path.exists(video_path):
                video_paths.append(video_path)

        if not video_paths:
            logger.error("没有找到可评估的视频文件")
            return {"overall_score": 0, "error": "没有找到可评估的视频文件"}

        # 构建评估文本
        transcript = self._extract_transcripts(scene_results)

        # 获取评估代理
        evaluator = self.agents["evaluator"]

        # 构建提示
        prompt = f"""
你是一位专业的教育视频评估专家，需要评估以下数学定理讲解视频的质量。

视频信息:
- 主题: {topic}
- 描述: {description}
- 视频文件: {', '.join(video_paths)}
- 讲解文本: {transcript}

请从以下维度评估视频质量:

1. 教学效果 (30分)
   - 概念解释清晰度
   - 逻辑结构和连贯性
   - 内容深度与适当的复杂度

2. 视觉呈现 (30分)
   - 动画质量和流畅度
   - 空间布局和视觉平衡
   - 颜色使用和视觉层次结构

3. 内容准确性 (20分)
   - 数学定理和证明的准确性
   - 术语使用的精确性
   - 无概念性错误

4. 观众体验 (20分)
   - 节奏和时长适当性
   - 叙述风格和表达清晰度
   - 视听体验的整体协调性

请为每个维度提供详细评分和具体评语，指出优点和不足。
最后给出总体评分(满分100分)和整体评价。
"""

        # 获取评估结果
        user_message = BaseMessage.make_user_message(role_name="User", content=prompt)
        evaluation_response = evaluator.step(user_message)
        evaluation_text = evaluation_response.msg.content

        # 提取总分
        score_pattern = r"总体评分[:：]\s*(\d+(?:\.\d+)?)"
        score_match = re.search(score_pattern, evaluation_text, re.IGNORECASE)
        overall_score = float(score_match.group(1)) if score_match else 0

        # 保存评估结果
        evaluation_file = os.path.join(self.output_dir, file_prefix, "evaluation.json")
        evaluation_result = {
            "overall_score": overall_score,
            "evaluation_text": evaluation_text,
            "scene_results": {
                k: {key: value for key, value in v.items() if key != "error_logs"} for k, v in scene_results.items()
            },
        }

        with open(evaluation_file, "w") as f:
            json.dump(evaluation_result, f, indent=2, ensure_ascii=False)
        logger.info(f"评估结果已保存到: {evaluation_file}")

        return evaluation_result

    def _extract_transcripts(self, scene_results: dict[int, dict[str, Any]]) -> str:
        """
        从场景结果中提取讲解文本

        Args:
            scene_results: 场景渲染结果

        Returns:
            str: 合并的讲解文本
        """
        transcripts = []

        for scene_number, result in sorted(scene_results.items()):
            code_path = result.get("final_code_path")
            if code_path and os.path.exists(code_path):
                try:
                    with open(code_path) as f:
                        code = f.read()

                    # 提取voiceover文本
                    voiceover_pattern = r'self\.voiceover\(text="([^"]+)"\)'
                    voiceovers = re.findall(voiceover_pattern, code)

                    if voiceovers:
                        scene_transcript = f"场景{scene_number}讲解内容:\n"
                        for i, vo in enumerate(voiceovers, 1):
                            scene_transcript += f"{i}. {vo}\n"
                        transcripts.append(scene_transcript)
                except Exception as e:
                    logger.error(f"提取场景{scene_number}讲解文本失败: {str(e)}")

        return "\n".join(transcripts)

    async def combine_videos(self, topic: str, scene_results: dict[int, dict[str, Any]]) -> Optional[str]:
        """
        合并所有场景视频

        Args:
            topic: 定理主题
            scene_results: 场景渲染结果

        Returns:
            Optional[str]: 合并后的视频路径
        """
        logger.info(f"合并视频: {topic}")

        file_prefix = topic.lower().replace(" ", "_").replace("-", "_")

        # 收集所有视频路径
        video_paths = []
        for scene_number, result in sorted(scene_results.items()):
            video_path = result.get("final_video_path")
            if video_path and os.path.exists(video_path):
                video_paths.append(video_path)

        if not video_paths:
            logger.error("没有找到可合并的视频文件")
            return None

        # 使用Manim工具包合并视频
        try:
            combined_video_path = os.path.join(self.output_dir, file_prefix, f"{file_prefix}_combined.mp4")
            self.toolkits["manim"].combine_videos(video_paths, combined_video_path)
            logger.info(f"合并视频已保存到: {combined_video_path}")
            return combined_video_path
        except Exception as e:
            logger.error(f"合并视频失败: {str(e)}")
            return None

    async def run_workflow(self, topic: str, description: Optional[str] = None, file_path: Optional[str] = None) -> dict[str, Any]:
        """运行完整的工作流程

        Args:
            topic: 定理主题
            description: 定理描述（可选，如果不提供将使用配置中的purpose）
            file_path: 可选的文件路径，用于场景规划分镜设计

        Returns:
            Dict[str, Any]: 工作流结果
        """
        # 如果没有提供description，从配置中读取purpose作为描述
        if not description:
            logger.info("description未提供，从配置文件中读取purpose作为描述")
            
            # 从配置中读取purpose
            try:
                with open(self.config_path, 'r') as f:
                    config = yaml.safe_load(f)
                purpose = config.get("example_explain", {}).get("purpose", "")
                if purpose:
                    description = purpose
                    logger.info(f"使用配置中的purpose作为描述: {purpose}")
                else:
                    logger.warning("配置中的purpose为空，使用默认描述")
                    description = f"详细解释{topic}的核心原理和应用"
            except Exception as e:
                logger.error(f"读取配置文件失败: {str(e)}")
                description = f"详细解释{topic}的核心原理和应用"
        
        # 检查是否需要使用配置中的example_explain信息
        if not topic:
            logger.info("输入的topic为空，使用配置中的example_explain信息")
            
            # 从配置中读取example_explain信息
            example_explain_config = self.agent_config.get("example_explain", {})
            if not example_explain_config:
                # 如果没有example_explain配置，从根配置中读取
                try:
                    with open(self.config_path, 'r') as f:
                        config = yaml.safe_load(f)
                    example_explain_config = config.get("example_explain", {})
                except Exception as e:
                    logger.error(f"读取配置文件失败: {str(e)}")
                    return {"status": "error", "message": "配置文件中缺少example_explain配置"}
            
            # 使用配置中的topic和purpose
            topic = example_explain_config.get("topic", "")
            purpose = example_explain_config.get("purpose", "")
            
            if not topic:
                logger.error("配置中的example_explain.topic为空")
                return {"status": "error", "message": "配置中的example_explain.topic为空"}
            
            logger.info(f"使用配置中的主题: {topic}")
            logger.info(f"使用配置中的目的: {purpose}")
            
            # 检查example_explain.md文件是否已存在
            example_file_path = f"output/{topic}/example_explain.md"
            if os.path.exists(example_file_path):
                # 文件存在，直接读取
                try:
                    with open(example_file_path, 'r', encoding='utf-8') as f:
                        description = f.read()
                    logger.info(f"已从现有文件读取例子解释作为description: {example_file_path}，长度: {len(description)}")
                except Exception as e:
                    logger.error(f"读取现有例子解释文件失败: {str(e)}")
                    description = f"基于{purpose}的{topic}例子解释"
            else:
                # 文件不存在，调用example_explain_agent生成例子解释文件
                try:
                    from agents.example_explain_agent import ExampleExplainAgent
                    
                    logger.info("例子解释文件不存在，开始调用example_explain_agent生成例子解释")
                    example_agent = ExampleExplainAgent(config_path=self.config_path)
                    
                    # 生成例子解释文件
                    example_result = example_agent.run()
                    
                    if example_result.get("success"):
                        generated_file_path = example_result.get("saved_file", "")
                        logger.info(f"例子解释文件已生成: {generated_file_path}")
                        
                        # 读取生成的例子解释文件作为description
                        if generated_file_path and os.path.exists(generated_file_path):
                            try:
                                with open(generated_file_path, 'r', encoding='utf-8') as f:
                                    description = f.read()
                                logger.info(f"已读取生成的例子解释文件作为description，长度: {len(description)}")
                            except Exception as e:
                                logger.error(f"读取生成的例子解释文件失败: {str(e)}")
                                description = f"基于{purpose}的{topic}例子解释"
                        else:
                            description = f"基于{purpose}的{topic}例子解释"
                    else:
                        logger.error(f"生成例子解释失败: {example_result.get('error', '未知错误')}")
                        description = f"基于{purpose}的{topic}例子解释"
                        
                except Exception as e:
                    logger.error(f"调用example_explain_agent失败: {str(e)}")
                    description = f"基于{purpose}的{topic}例子解释"
        
        # 在确定topic之后，如果没有提供file_path，尝试自动查找example_explain.md文件
        if not file_path and topic:
            auto_file_path = f"output/{topic}/example_explain.md"
            if os.path.exists(auto_file_path):
                file_path = auto_file_path
                logger.info(f"自动找到输入文件: {file_path}")
            else:
                logger.info(f"未找到自动文件路径: {auto_file_path}")
        
        logger.info(f"开始运行工作流，处理定理: {topic}")
        logger.info(f"工作流起始阶段: {self.start_stage}")
        logger.info(f"RAG增强: {'启用' if self.enable_rag else '禁用'}")
        logger.info(f"上下文学习: {'启用' if self.enable_context_learning else '禁用'}")
        logger.info(f"优化场景实现模式: {'启用' if self.use_optimized_implementation else '禁用'}")
        
        # 输出各步骤开关状态
        logger.info("=== 工作流步骤开关状态 ===")
        logger.info(f"场景规划: {'启用' if self.enable_scene_plan else '禁用'}")
        logger.info(f"视觉故事板: {'启用' if self.enable_vision_storyboard else '禁用'}")
        logger.info(f"技术实现: {'启用' if self.enable_technical_implementation else '禁用'}")
        logger.info(f"动画叙述: {'启用' if self.enable_animation_narration else '禁用'}")
        logger.info(f"代码生成: {'启用' if self.enable_code_generation else '禁用'}")
        logger.info(f"视频渲染: {'启用' if self.enable_rendering else '禁用'}")
        logger.info(f"视频评估: {'启用' if self.enable_evaluation else '禁用'}")
        logger.info(f"视频合并: {'启用' if self.enable_video_combine else '禁用'}")
        logger.info("========================")

        result = {}

        # 如果没有指定起始阶段，从场景规划开始
        if self.start_stage is None:
            self.start_stage = "scene_plan"

        # 执行场景规划阶段
        if self.enable_scene_plan:
            if self.start_stage == "scene_plan":
                # 生成场景规划
                logger.info("开始执行场景规划步骤")
                scene_plan_result = await self.generate_scene_planning(topic, description, file_path)
                if scene_plan_result.get("status") != "success":
                    return scene_plan_result

                scene_plan = scene_plan_result.get("scene_plan", "")
                result["scene_plan"] = scene_plan
                logger.info("场景规划阶段完成")
            else:
                # 如果起始阶段不是场景规划，尝试从文件加载已有的场景规划
                output_path_template = self.workflow_config.get(
                    "scene_plan_file", f"output/{topic.replace(' ', '_')}/scene_outline.txt"
                )
                # 格式化路径模板，替换 {topic} 占位符
                output_path = output_path_template.format(topic=topic.replace(" ", "_"))
                try:
                    with open(output_path, encoding="utf-8") as f:
                        scene_plan = f.read()
                    result["scene_plan"] = scene_plan
                    logger.info(f"已从文件加载场景规划: {output_path}")
                except Exception as e:
                    logger.error(f"加载场景规划失败: {str(e)}")
                    logger.info("因为无法加载已有场景规划，将重新生成场景规划")
                    # 重新生成场景规划
                    scene_plan_result = await self.generate_scene_planning(topic, description, file_path)
                    if scene_plan_result.get("status") != "success":
                        return scene_plan_result

                    scene_plan = scene_plan_result.get("scene_plan", "")
                    result["scene_plan"] = scene_plan
                    logger.info("重新生成场景规划完成")
        else:
            logger.info("场景规划步骤已禁用，尝试从文件加载已有场景规划")
            output_path_template = self.workflow_config.get(
                "scene_plan_file", f"output/{topic.replace(' ', '_')}/scene_outline.txt"
            )
            # 格式化路径模板，替换 {topic} 占位符
            output_path = output_path_template.format(topic=topic.replace(" ", "_"))
            try:
                with open(output_path, encoding="utf-8") as f:
                    scene_plan = f.read()
                result["scene_plan"] = scene_plan
                logger.info(f"已从文件加载场景规划: {output_path}")
            except Exception as e:
                logger.error(f"场景规划步骤已禁用但无法加载已有场景规划: {str(e)}")
                return {"status": "error", "message": "场景规划步骤已禁用但无法加载已有场景规划"}

        # 获取场景规划内容
        scene_plan = result.get("scene_plan", "")
        if not scene_plan:
            logger.error("场景规划为空，无法继续执行工作流")
            return {"status": "error", "message": "场景规划为空"}

        # 场景实现计划生成（包括视觉故事板、技术实现、动画叙述）
        scene_implementations = {}
        if self.enable_vision_storyboard or self.enable_technical_implementation or self.enable_animation_narration:
            # 检查是否启用优化模式
            use_optimized_implementation = self.workflow_config.get("use_optimized_implementation", False)
            
            if use_optimized_implementation:
                logger.info("开始优化的场景实现计划生成（批量模式）")
                scene_implementations = await self.generate_scene_implementation_optimized(topic, description, scene_plan)
            else:
                logger.info("开始生成场景实现计划（传统模式）")
                scene_implementations = await self.generate_scene_implementation(topic, description, scene_plan)
            
            result["scene_implementations"] = scene_implementations
        else:
            logger.info("所有场景实现步骤已禁用，跳过场景实现计划生成")

        # 代码生成和渲染
        scene_results = {}
        if self.enable_code_generation or self.enable_rendering:
            if scene_implementations:
                logger.info("开始生成代码并渲染视频")
                scene_results = await self.generate_code_and_render(topic, description, scene_implementations)
                result["scene_results"] = scene_results
            else:
                logger.warning("场景实现计划为空，跳过代码生成和渲染")
        else:
            logger.info("代码生成和渲染步骤已禁用，跳过代码生成和渲染")

        # 评估视频
        evaluation_result = {}
        if self.enable_evaluation:
            if scene_results:
                logger.info("开始评估视频质量")
                evaluation_result = await self.evaluate_video(topic, description, scene_results)
                result["evaluation"] = evaluation_result
            else:
                logger.warning("场景渲染结果为空，跳过视频评估")
        else:
            logger.info("视频评估步骤已禁用，跳过视频评估")

        # 合并视频
        combined_video = None
        if self.enable_video_combine:
            if scene_results:
                logger.info("开始合并所有场景视频")
                combined_video = await self.combine_videos(topic, scene_results)
                result["combined_video"] = combined_video
            else:
                logger.warning("场景渲染结果为空，跳过视频合并")
        else:
            logger.info("视频合并步骤已禁用，跳过视频合并")

        # 保存最终结果
        file_prefix = topic.lower().replace(" ", "_").replace("-", "_")
        final_result_path = os.path.join(self.output_dir, file_prefix, "workflow_result.json")

        with open(final_result_path, "w") as f:
            # 过滤掉太大的字段
            filtered_result = {
                "topic": topic,
                "description": description,
                "scene_plan": scene_plan,
                "scene_counts": len(scene_implementations) if scene_implementations else 0,
                "evaluation": evaluation_result if evaluation_result else {},
                "combined_video": combined_video,
                "completion_time": datetime.datetime.now().isoformat(),
                "step_switches": {
                    "enable_scene_plan": self.enable_scene_plan,
                    "enable_vision_storyboard": self.enable_vision_storyboard,
                    "enable_technical_implementation": self.enable_technical_implementation,
                    "enable_animation_narration": self.enable_animation_narration,
                    "enable_code_generation": self.enable_code_generation,
                    "enable_rendering": self.enable_rendering,
                    "enable_evaluation": self.enable_evaluation,
                    "enable_video_combine": self.enable_video_combine,
                }
            }
            json.dump(filtered_result, f, indent=2, ensure_ascii=False)

        logger.info(f"工作流完成，结果已保存到: {final_result_path}")
        return result

    # 添加辅助方法
    def _parse_scene_outline(self, scene_outline: str) -> list[str]:
        """
        解析场景大纲，提取各个场景的内容
        
        Args:
            scene_outline: 场景大纲文本
            
        Returns:
            list[str]: 场景内容列表
        """
        if not scene_outline:
            return []

        # 移除XML标签前的内容
        if "<SCENE_OUTLINE>" in scene_outline:
            scene_outline = scene_outline[scene_outline.find("<SCENE_OUTLINE>"):]

        # 移除XML标签
        scene_outline = scene_outline.replace("<SCENE_OUTLINE>", "").replace("</SCENE_OUTLINE>", "").strip()

        # 使用正则表达式匹配场景块
        import re
        scene_pattern = r'场景\d+:\s*[^\n]+(?:\n(?:(?!场景\d+:).)*)*'
        scenes = re.findall(scene_pattern, scene_outline, re.DOTALL)

        # 处理和验证每个场景块
        validated_scenes = []
        for scene in scenes:
            scene = scene.strip()
            if not scene:
                continue

            # 验证场景格式
            if not re.match(r'场景\d+:', scene):
                logger.warning(f"跳过格式不正确的场景块: {scene[:50]}...")
                continue

            # 验证场景结构（应包含解释目标和关键内容点）
            if "**解释目标:**" not in scene or "**关键内容点:**" not in scene:
                logger.warning(f"场景缺少必要的结构元素: {scene[:50]}...")
                # 仍然添加到结果中，但记录警告
            
            validated_scenes.append(scene)

        logger.info(f"解析到{len(validated_scenes)}个有效场景")
        for i, scene in enumerate(validated_scenes, 1):
            # 提取场景标题
            title_match = re.match(r'场景\d+:\s*([^\n]+)', scene)
            title = title_match.group(1) if title_match else "未知标题"
            logger.info(f"场景{i}: {title}")
            logger.debug(f"场景{i}的内容长度: {len(scene)}")

        return validated_scenes

    def _get_scene_by_number(self, scene_outline: str, scene_number: int) -> Optional[str]:
        """获取指定编号的场景"""
        scenes = self._parse_scene_outline(scene_outline)
        if scene_number <= 0 or scene_number > len(scenes):
            return None
        return scenes[scene_number - 1]

    def _get_storyboard_by_scene_number(self, storyboards: list[dict], scene_number: int) -> Optional[str]:
        """获取指定场景编号的视觉故事板"""
        for sb in storyboards:
            if sb.get("scene_number") == scene_number:
                return sb.get("vision_storyboard")
        return None

    def _get_implementation_by_scene_number(self, implementations: list[dict], scene_number: int) -> Optional[str]:
        """获取指定场景编号的技术实现计划"""
        for impl in implementations:
            if impl.get("scene_number") == scene_number:
                return impl.get("technical_implementation")
        return None

    async def generate_scene_implementation_optimized(
        self, topic: str, description: str, scene_plan: str
    ) -> dict[int, dict[str, str]]:
        """
        优化的场景实现计划生成方法
        
        优化策略：
        1. 先批量生成所有场景的视觉故事板
        2. 然后批量生成所有场景的技术实现文档
        3. 最后批量生成所有场景的动画叙述
        4. 确保全局一致性和更好的场景间协调
        
        Args:
            topic: 定理主题
            description: 定理描述
            scene_plan: 场景规划
            
        Returns:
            Dict: 场景编号到实现计划的映射
        """
        logger.info(f"开始优化的场景实现计划生成: {topic}")
        
        # 从scene_plan字符串中提取场景
        scenes = self._parse_scene_outline(scene_plan)
        file_prefix = topic.replace(" ", "_")
        
        # 检测需要使用的插件
        relevant_plugins = []
        if self.enable_rag and self.toolkits["rag"]:
            logger.info(f"[RAG-插件检测] 正在检测相关插件... 主题: {topic}")
            relevant_plugins = self.toolkits["rag"].detect_relevant_plugins(topic=topic, description=description)
            logger.info(f"[RAG-插件检测] 检测到相关插件: {relevant_plugins}")
        
        # 创建场景目录
        scene_implementations = {}
        for scene_number in range(1, len(scenes) + 1):
            scene_dir = os.path.join(self.output_dir, file_prefix, f"scene{scene_number}")
            os.makedirs(scene_dir, exist_ok=True)
            scene_implementations[scene_number] = {}
        
        # 阶段1：批量生成所有场景的视觉故事板
        logger.info("=== 阶段1：批量生成所有场景的视觉故事板 ===")
        if self.enable_vision_storyboard:
            vision_tasks = []
            for scene_number, scene_outline_i in enumerate(scenes, 1):
                vision_tasks.append(
                    self._generate_vision_storyboard_batch(
                        scene_number, topic, description, scene_outline_i, 
                        relevant_plugins, file_prefix
                    )
                )
            
            # 并行生成所有视觉故事板
            vision_results = await asyncio.gather(*vision_tasks)
            
            # 保存视觉故事板结果
            for scene_number, vision_storyboard in enumerate(vision_results, 1):
                scene_dir = os.path.join(self.output_dir, file_prefix, f"scene{scene_number}")
                vision_path = os.path.join(scene_dir, f"scene{scene_number}_vision_storyboard.txt")
                
                with open(vision_path, "w") as f:
                    f.write(vision_storyboard)
                
                scene_implementations[scene_number]["vision_storyboard"] = vision_storyboard
                logger.info(f"场景{scene_number}视觉故事板已保存: {vision_path}")
        
        # 阶段2：批量生成所有场景的技术实现文档
        logger.info("=== 阶段2：批量生成所有场景的技术实现文档 ===")
        if self.enable_technical_implementation:
            tech_tasks = []
            for scene_number, scene_outline_i in enumerate(scenes, 1):
                vision_storyboard = scene_implementations[scene_number].get("vision_storyboard", "")
                tech_tasks.append(
                    self._generate_technical_implementation_batch(
                        scene_number, topic, description, scene_outline_i, 
                        vision_storyboard, relevant_plugins, file_prefix,
                        all_vision_storyboards=scene_implementations  # 传递所有视觉故事板以增强一致性
                    )
                )
            
            # 并行生成所有技术实现文档
            tech_results = await asyncio.gather(*tech_tasks)
            
            # 保存技术实现结果
            for scene_number, technical_implementation in enumerate(tech_results, 1):
                scene_dir = os.path.join(self.output_dir, file_prefix, f"scene{scene_number}")
                tech_path = os.path.join(scene_dir, f"scene{scene_number}_technical_implementation.txt")
                
                with open(tech_path, "w") as f:
                    f.write(technical_implementation)
                
                scene_implementations[scene_number]["technical_implementation"] = technical_implementation
                logger.info(f"场景{scene_number}技术实现文档已保存: {tech_path}")
        
        # 阶段3：批量生成所有场景的动画叙述
        logger.info("=== 阶段3：批量生成所有场景的动画叙述 ===")
        if self.enable_animation_narration:
            narration_tasks = []
            for scene_number, scene_outline_i in enumerate(scenes, 1):
                vision_storyboard = scene_implementations[scene_number].get("vision_storyboard", "")
                technical_implementation = scene_implementations[scene_number].get("technical_implementation", "")
                narration_tasks.append(
                    self._generate_animation_narration_batch(
                        scene_number, topic, description, scene_outline_i,
                        vision_storyboard, technical_implementation, relevant_plugins, file_prefix,
                        all_implementations=scene_implementations  # 传递所有实现以增强一致性
                    )
                )
            
            # 并行生成所有动画叙述
            narration_results = await asyncio.gather(*narration_tasks)
            
            # 保存动画叙述结果
            for scene_number, animation_narration in enumerate(narration_results, 1):
                scene_dir = os.path.join(self.output_dir, file_prefix, f"scene{scene_number}")
                narration_path = os.path.join(scene_dir, f"scene{scene_number}_animation_narration.txt")
                
                with open(narration_path, "w") as f:
                    f.write(animation_narration)
                
                scene_implementations[scene_number]["animation_narration"] = animation_narration
                logger.info(f"场景{scene_number}动画叙述已保存: {narration_path}")
        
        logger.info("=== 优化的场景实现计划生成完成 ===")
        return scene_implementations
    
    async def _generate_vision_storyboard_batch(
        self, scene_number: int, topic: str, description: str, scene_outline: str,
        relevant_plugins: list[str], file_prefix: str
    ) -> str:
        """批量生成视觉故事板的单个场景处理"""
        logger.info(f"批量生成场景{scene_number}的视觉故事板")
        
        # 使用现有的视觉故事板生成逻辑
        return await self._generate_vision_storyboard(
            scene_number, topic, description, scene_outline, relevant_plugins, file_prefix
        )
    
    async def _generate_technical_implementation_batch(
        self, scene_number: int, topic: str, description: str, scene_outline: str,
        vision_storyboard: str, relevant_plugins: list[str], file_prefix: str,
        all_vision_storyboards: dict = None
    ) -> str:
        """批量生成技术实现文档的单个场景处理"""
        logger.info(f"批量生成场景{scene_number}的技术实现文档")
        
        # 获取技术实现代理
        tech_agent = self.agents["technical_implementation"]
        
        # 构建全局上下文以增强一致性
        global_context = ""
        if all_vision_storyboards:
            global_context = "其他场景的视觉故事板概要：\n"
            for scene_num, impl in all_vision_storyboards.items():
                if scene_num != scene_number and impl.get("vision_storyboard"):
                    global_context += f"场景{scene_num}: {impl['vision_storyboard'][:100]}...\n"
        
        # 获取RAG上下文（如果启用）
        rag_context = None
        if self.enable_rag and self.toolkits["rag"]:
            queries = self.toolkits["rag"].generate_queries_for_technical(
                vision_storyboard, relevant_plugins, topic, scene_number, self.session_id
            )
            rag_context = self.toolkits["rag"].search_with_queries(queries, limit_per_query=3)
        
        # 生成技术实现计划（增强全局一致性）
        tech_result = await tech_agent.generate(
            topic=topic,
            description=description,
            scene_number=scene_number,
            scene_outline=scene_outline,
            vision_storyboard=vision_storyboard,
            relevant_plugins=relevant_plugins,
            session_id=self.session_id,
            global_context=global_context  # 传递全局上下文
        )
        
        # 处理返回结果
        if isinstance(tech_result, dict):
            technical_implementation = tech_result.get("technical_implementation", "")
            if not technical_implementation and tech_result.get("status") == "success":
                import json
                technical_implementation = json.dumps(tech_result, ensure_ascii=False, indent=2)
            elif tech_result.get("status") == "error":
                error_msg = tech_result.get("message", "生成技术实现计划出错")
                logger.error(f"生成技术实现计划失败: {error_msg}")
                return f"<TECHNICAL_IMPLEMENTATION>\n生成失败: {error_msg}\n</TECHNICAL_IMPLEMENTATION>"
        else:
            technical_implementation = tech_result
        
        # 提取标签中的内容
        from utils.format import extract_xml
        tech_match = extract_xml(technical_implementation, "TECHNICAL_IMPLEMENTATION")
        return tech_match if tech_match else technical_implementation
    
    async def _generate_animation_narration_batch(
        self, scene_number: int, topic: str, description: str, scene_outline: str,
        vision_storyboard: str, technical_implementation: str, relevant_plugins: list[str], 
        file_prefix: str, all_implementations: dict = None
    ) -> str:
        """批量生成动画叙述的单个场景处理"""
        logger.info(f"批量生成场景{scene_number}的动画叙述")
        
        # 获取动画叙述代理
        narration_agent = self.agents["animation_narration"]
        
        # 构建全局上下文以增强一致性
        global_context = ""
        if all_implementations:
            global_context = "其他场景的实现概要：\n"
            for scene_num, impl in all_implementations.items():
                if scene_num != scene_number:
                    if impl.get("vision_storyboard"):
                        global_context += f"场景{scene_num}视觉: {impl['vision_storyboard'][:50]}...\n"
                    if impl.get("technical_implementation"):
                        global_context += f"场景{scene_num}技术: {impl['technical_implementation'][:50]}...\n"
        
        # 获取RAG上下文（如果启用）
        rag_context = None
        if self.enable_rag and self.toolkits["rag"]:
            queries = self.toolkits["rag"].generate_queries_for_narration(
                vision_storyboard, relevant_plugins, topic, scene_number, self.session_id
            )
            rag_context = self.toolkits["rag"].search_with_queries(queries, limit_per_query=3)
        
        # 生成动画叙述（增强全局一致性）
        narration_result = await narration_agent.generate(
            topic=topic,
            description=description,
            scene_number=scene_number,
            scene_outline=scene_outline,
            vision_storyboard=vision_storyboard,
            technical_implementation=technical_implementation,
            relevant_plugins=relevant_plugins,
            session_id=self.session_id,
            global_context=global_context  # 传递全局上下文
        )
        
        # 处理返回结果
        if isinstance(narration_result, dict):
            animation_narration = narration_result.get("animation_narration", "")
            if not animation_narration and narration_result.get("status") == "success":
                import json
                animation_narration = json.dumps(narration_result, ensure_ascii=False, indent=2)
            elif narration_result.get("status") == "error":
                error_msg = narration_result.get("message", "生成动画叙述出错")
                logger.error(f"生成动画叙述失败: {error_msg}")
                return f"<ANIMATION_NARRATION>\n生成失败: {error_msg}\n</ANIMATION_NARRATION>"
        else:
            animation_narration = narration_result
        
        # 提取标签中的内容
        from utils.format import extract_xml
        narration_match = extract_xml(animation_narration, "ANIMATION_NARRATION")
        return narration_match if narration_match else animation_narration


async def main():
    # 解析命令行参数
    import argparse

    parser = argparse.ArgumentParser(description="运行定理解释视频生成工作流")
    parser.add_argument("--config", default="config/config.yaml", help="配置文件路径")
    parser.add_argument("--topic", help="定理主题（如果不提供，将使用配置文件中的example_explain.topic）")
    parser.add_argument("--description", help="定理描述（可选，如果不提供将使用配置文件中的purpose）")
    parser.add_argument("--file-path", help="可选的文件路径，用于场景规划分镜设计")
    parser.add_argument(
        "--start-stage",
        choices=[
            "scene_plan",
            "vision_storyboard",
            "technical_implementation",
            "animation_narration",
            "code_generation",
            "rendering",
            "evaluation",
        ],
        help="起始阶段",
    )
    
    # 添加步骤开关参数
    parser.add_argument("--disable-scene-plan", action="store_true", help="禁用场景规划步骤")
    parser.add_argument("--disable-vision-storyboard", action="store_true", help="禁用视觉故事板步骤")
    parser.add_argument("--disable-technical-implementation", action="store_true", help="禁用技术实现步骤")
    parser.add_argument("--disable-animation-narration", action="store_true", help="禁用动画叙述步骤")
    parser.add_argument("--disable-code-generation", action="store_true", help="禁用代码生成步骤")
    parser.add_argument("--disable-rendering", action="store_true", help="禁用视频渲染步骤")
    parser.add_argument("--disable-evaluation", action="store_true", help="禁用视频评估步骤")
    parser.add_argument("--disable-video-combine", action="store_true", help="禁用视频合并步骤")
    parser.add_argument("--enable-optimized-implementation", action="store_true", help="启用优化的场景实现模式（批量生成）")
    args = parser.parse_args()

    # 初始化工作流
    workflow = TheoremExplainWorkflow(config_path=args.config)

    # 设置起始阶段（命令行参数优先于配置文件）
    if args.start_stage:
        workflow.start_stage = args.start_stage

    # 设置步骤开关（命令行参数优先于配置文件）
    # 只有当命令行参数为True时才覆盖配置文件的设置
    if args.disable_scene_plan:
        workflow.enable_scene_plan = False
    if args.disable_vision_storyboard:
        workflow.enable_vision_storyboard = False
    if args.disable_technical_implementation:
        workflow.enable_technical_implementation = False
    if args.disable_animation_narration:
        workflow.enable_animation_narration = False
    if args.disable_code_generation:
        workflow.enable_code_generation = False
    if args.disable_rendering:
        workflow.enable_rendering = False
    if args.disable_evaluation:
        workflow.enable_evaluation = False
    if args.disable_video_combine:
        workflow.enable_video_combine = False
    if args.enable_optimized_implementation:
        workflow.use_optimized_implementation = True

    # 显示当前工作流步骤状态
    logger.info("=== 工作流步骤状态 ===")
    logger.info(f"场景规划: {'启用' if workflow.enable_scene_plan else '禁用'}")
    logger.info(f"视觉故事板: {'启用' if workflow.enable_vision_storyboard else '禁用'}")
    logger.info(f"技术实现: {'启用' if workflow.enable_technical_implementation else '禁用'}")
    logger.info(f"动画叙述: {'启用' if workflow.enable_animation_narration else '禁用'}")
    logger.info(f"代码生成: {'启用' if workflow.enable_code_generation else '禁用'}")
    logger.info(f"视频渲染: {'启用' if workflow.enable_rendering else '禁用'}")
    logger.info(f"视频评估: {'启用' if workflow.enable_evaluation else '禁用'}")
    logger.info(f"视频合并: {'启用' if workflow.enable_video_combine else '禁用'}")
    logger.info(f"优化场景实现模式: {'启用' if workflow.use_optimized_implementation else '禁用'}")
    logger.info("=" * 25)

    # 运行工作流
    topic = args.topic or ""
    description = args.description  # 可以为None，让run_workflow自动处理
    file_path = getattr(args, 'file_path', None)
    
    result = await workflow.run_workflow(topic, description, file_path)

    print("工作流完成。")
    if result.get('evaluation', {}).get('overall_score'):
        print(f"最终评分: {result['evaluation']['overall_score']}")
    else:
        print("评估步骤已禁用或失败，无评分")
    
    if result.get("combined_video"):
        print(f"合并视频路径: {result['combined_video']}")
    else:
        print("视频合并步骤已禁用或失败，无合并视频")


if __name__ == "__main__":
    asyncio.run(main())
