from manim import *
import numpy as np
from prompts.professional_science_template import ProfessionalScienceTemplate


class ComputerScienceExample(ProfessionalScienceTemplate):
    """计算机科学算法讲解示例 - 快速排序演示"""
    
    def construct(self):
        # 设置背景
        self.setup_background()
        
        # 演示快速排序算法
        self.demonstrate_quicksort_algorithm()
    
    def demonstrate_quicksort_algorithm(self):
        """演示快速排序算法的动态工作原理"""
        
        title_group = self.create_title_region_content("快速排序")
        step_group = self.create_step_region_content("算法初始化")
        
        # 创建初始数组
        self.numbers = [64, 34, 25, 12, 22, 11, 90]
        self.recursion_depth = 0
        
        # 主内容：排序过程动画
        main_content = self.create_dynamic_sorting_animation()
        main_group = self.create_main_region_content(main_content)
        
        # 演示无辅助区域布局 - 主内容享有全部空间
        result_group = self.create_result_region_content(
            "快速排序演示：分治算法，平均时间复杂度O(nlogn)"
        )
        
        # 初始展示 - 注意：这里没有辅助区域，展示纯净布局
        self.play(Write(title_group), Write(step_group))
        self.play(FadeIn(main_group))
        self.play(Write(result_group))
        self.wait(1)
        
        # 执行动态排序过程
        self.perform_quicksort_animation(
            step_group, main_group, result_group
        )
    
    def create_dynamic_sorting_animation(self):
        """创建动态排序动画框架（使用圆圈显示数字）"""
        # 创建数组可视化容器
        self.circles_group = VGroup()
        self.labels_group = VGroup()
        
        # 圆圈配置 - 相同大小的圆圈
        circle_radius = 0.35
        circle_spacing = 0.9  # 圆圈间距
        
        for i, num in enumerate(self.numbers):
            # 创建相同大小的圆圈
            circle = Circle(
                radius=circle_radius,
                fill_color=self.colors['accent'],
                fill_opacity=0.8,
                stroke_color=self.colors['primary'],
                stroke_width=3
            )
            
            # 数字标签（在圆圈中心）
            number_label = Text(
                str(num), 
                font_size=20, 
                color=WHITE, 
                weight=BOLD
            ).move_to(circle.get_center())
            
            # 位置索引（在圆圈下方）
            index_label = Text(
                str(i), 
                font_size=14, 
                color=self.colors['text_secondary']
            )
            
            # 计算水平位置
            x_offset = (i - len(self.numbers)/2 + 0.5) * circle_spacing
            
            # 设置位置
            circle.shift(RIGHT * x_offset)
            number_label.move_to(circle.get_center())
            index_label.move_to(circle.get_bottom() + DOWN * 0.3)
            
            # 创建圆圈组（圆圈+数字）
            circle_with_number = VGroup(circle, number_label)
            
            self.circles_group.add(circle_with_number)
            self.labels_group.add(index_label)
        
        # 标题
        array_label = Text(
            "快速排序可视化", 
            font_size=20, 
            color=self.colors['text_primary'], 
            weight=BOLD
        ).next_to(self.circles_group, UP, buff=0.6)
        
        # 操作指示器组
        self.partition_indicators = VGroup()
        self.comparison_indicators = VGroup()
        self.pivot_indicator = None
        
        # 跟踪基准元素和已排序元素的索引
        self.pivot_indices = set()  # 当前基准元素的索引
        self.sorted_indices = set()  # 已排序完成的元素索引
        
        # 添加操作说明区域
        self.operation_label = Text(
            "准备开始排序", 
            font_size=16, 
            color=self.colors['highlight']
        ).next_to(self.circles_group, DOWN, buff=0.8)
        
        return VGroup(self.circles_group, self.labels_group, array_label, self.operation_label)
    
    def perform_quicksort_animation(self, step_group, main_group, result_group):
        """执行完整的快速排序动画"""
        # 开始快速排序
        self.quicksort_visual(0, len(self.numbers) - 1, step_group, result_group)
        
        # 最终结果
        final_step = self.create_step_region_content("排序完成")
        final_result = self.create_result_region_content(
            f"排序完成！结果：{self.numbers} - 快速排序高效完成"
        )
        
        self.play(Transform(step_group, final_step))
        self.play(Transform(result_group, final_result))
        self.wait(3)
    
    def quicksort_visual(self, low, high, step_group, result_group):
        """可视化快速排序递归过程"""
        if low < high:
            self.recursion_depth += 1
            
            # 显示当前分区范围
            partition_step = self.create_step_region_content(f"分区[{low}:{high}]")
            self.play(Transform(step_group, partition_step))
            
            # 高亮当前分区
            self.highlight_partition(low, high)
            
            # 执行分区操作
            pivot_index = self.partition_visual(low, high, step_group, result_group)
            
            # 递归处理左右子数组
            if pivot_index - low > 1:  # 左侧有多个元素
                self.quicksort_visual(low, pivot_index - 1, step_group, result_group)
            
            if high - pivot_index > 1:  # 右侧有多个元素
                self.quicksort_visual(pivot_index + 1, high, step_group, result_group)
            
            self.recursion_depth -= 1
    
    def partition_visual(self, low, high, step_group, result_group):
        """可视化分区过程"""
        # 选择最后一个元素作为基准
        pivot = self.numbers[high]
        
        # 显示基准选择
        pivot_step = self.create_step_region_content(f"基准: {pivot}")
        self.play(Transform(step_group, pivot_step))
        
        # 高亮基准元素
        self.highlight_pivot(high)
        
        i = low - 1  # 小于基准元素的区域边界
        
        for j in range(low, high):
            # 比较当前元素与基准
            compare_step = self.create_step_region_content(f"比较: {self.numbers[j]} vs {pivot}")
            self.play(Transform(step_group, compare_step))
            
            # 高亮比较元素
            self.highlight_comparison(j, high)
            
            if self.numbers[j] <= pivot:
                i += 1
                if i != j:
                    # 交换元素
                    swap_result = self.create_result_region_content(
                        f"交换: {self.numbers[i]} ↔ {self.numbers[j]}"
                    )
                    self.play(Transform(result_group, swap_result))
                    
                    self.animate_swap(i, j)
                    self.numbers[i], self.numbers[j] = self.numbers[j], self.numbers[i]
            
            # 移除高亮
            self.remove_highlights()
            self.wait(0.3)
        
        # 将基准放到正确位置
        final_swap_step = self.create_step_region_content("基准归位")
        self.play(Transform(step_group, final_swap_step))
        
        if i + 1 != high:
            self.animate_swap(i + 1, high)
            self.numbers[i + 1], self.numbers[high] = self.numbers[high], self.numbers[i + 1]
        
        # 标记基准位置
        self.mark_pivot_position(i + 1)
        
        return i + 1
    
    def highlight_partition(self, low, high):
        """高亮显示当前分区（圆圈版本）"""
        # 更新操作说明
        partition_text = f"分区范围: [{low}] 到 [{high}]"
        new_operation_label = Text(
            partition_text, 
            font_size=16, 
            color=self.colors['continuity']
        ).move_to(self.operation_label.get_center())
        
        self.play(Transform(self.operation_label, new_operation_label), run_time=0.3)
        
        # 创建分区边界指示器（圆弧形状更适合圆圈）
        if low < len(self.circles_group) and high < len(self.circles_group):
            left_bound = self.circles_group[low].get_left()
            right_bound = self.circles_group[high].get_right()
            
            # 创建分区边界线
            partition_line = Line(
                start=left_bound + DOWN * 0.8,
                end=right_bound + DOWN * 0.8,
                stroke_color=self.colors['continuity'],
                stroke_width=4
            )
            
            # 添加分区标记
            partition_label = Text(
                f"分区[{low}:{high}]",
                font_size=12,
                color=self.colors['continuity']
            ).next_to(partition_line, DOWN, buff=0.1)
            
            partition_group = VGroup(partition_line, partition_label)
            self.partition_indicators.add(partition_group)
            self.play(Create(partition_group), run_time=0.5)
    
    def highlight_pivot(self, pivot_index):
        """高亮显示基准元素（圆圈版本）"""
        # 更新操作说明
        pivot_value = self.numbers[pivot_index]
        pivot_text = f"选择基准元素: {pivot_value} (位置{pivot_index})"
        new_operation_label = Text(
            pivot_text, 
            font_size=16, 
            color=self.colors['persistent']
        ).move_to(self.operation_label.get_center())
        
        self.play(Transform(self.operation_label, new_operation_label), run_time=0.3)
        
        # 添加到基准元素索引集合
        self.pivot_indices.add(pivot_index)
        
        if pivot_index < len(self.circles_group):
            pivot_circle = self.circles_group[pivot_index]
            
            # 改变基准元素颜色并添加特殊效果
            self.play(
                pivot_circle[0].animate.set_fill(color=self.colors['persistent'], opacity=0.9),
                pivot_circle[0].animate.set_stroke(color=self.colors['persistent'], width=5),
                run_time=0.5
            )
            
            # 添加基准标记（王冠图标或特殊标记）
            pivot_marker = Text(
                "PIVOT", 
                font_size=10, 
                color=self.colors['persistent'],
                weight=BOLD
            ).next_to(pivot_circle, UP, buff=0.1)
            
            self.pivot_indicator = pivot_marker
            self.play(FadeIn(pivot_marker), run_time=0.3)
    
    def highlight_comparison(self, i, j):
        """高亮显示比较的两个元素（圆圈版本）"""
        # 更新操作说明
        compare_text = f"比较: {self.numbers[i]} vs {self.numbers[j]}"
        if self.numbers[i] <= self.numbers[j]:
            compare_text += f" → {self.numbers[i]} ≤ {self.numbers[j]}"
        else:
            compare_text += f" → {self.numbers[i]} > {self.numbers[j]}"
            
        new_operation_label = Text(
            compare_text, 
            font_size=16, 
            color=self.colors['highlight']
        ).move_to(self.operation_label.get_center())
        
        self.play(Transform(self.operation_label, new_operation_label), run_time=0.3)
        
        if i < len(self.circles_group) and j < len(self.circles_group):
            circle_i = self.circles_group[i]
            circle_j = self.circles_group[j]
            
            # 清除之前的比较指示器
            if len(self.comparison_indicators) > 0:
                self.play(FadeOut(self.comparison_indicators), run_time=0.2)
                self.comparison_indicators = VGroup()
            
            # 临时改变边框颜色并增加脉动效果
            self.play(
                circle_i[0].animate.set_stroke(color=self.colors['highlight'], width=6),
                circle_j[0].animate.set_stroke(color=self.colors['highlight'], width=6),
                run_time=0.3
            )
            
            # 添加比较箭头
            if i != j:  # 避免自己和自己比较
                arrow = Arrow(
                    start=circle_i.get_center() + UP * 0.5,
                    end=circle_j.get_center() + UP * 0.5,
                    stroke_color=self.colors['highlight'],
                    stroke_width=3,
                    max_tip_length_to_length_ratio=0.15
                )
                self.comparison_indicators.add(arrow)
                self.play(Create(arrow), run_time=0.3)
    
    def remove_highlights(self):
        """移除所有高亮效果（圆圈版本）"""
        # 恢复所有圆圈的原始颜色（除了基准元素和已排序元素）
        for i, circle_group in enumerate(self.circles_group):
            circle = circle_group[0]  # 获取圆圈对象
            # 如果不是基准元素且不是已排序元素，恢复原始颜色
            if i not in self.pivot_indices and i not in self.sorted_indices:
                circle.set_fill(color=self.colors['accent'], opacity=0.8)
                circle.set_stroke(color=self.colors['primary'], width=3)
        
        # 清除比较指示器
        if len(self.comparison_indicators) > 0:
            self.play(FadeOut(self.comparison_indicators), run_time=0.2)
            self.comparison_indicators = VGroup()
    
    def animate_swap(self, i, j):
        """动画显示元素交换（圆圈版本，增强视觉效果）"""
        # 更新操作说明
        swap_text = f"交换: {self.numbers[i]} ↔ {self.numbers[j]}"
        new_operation_label = Text(
            swap_text, 
            font_size=16, 
            color=self.colors['transition']
        ).move_to(self.operation_label.get_center())
        
        self.play(Transform(self.operation_label, new_operation_label), run_time=0.3)
        
        if i < len(self.circles_group) and j < len(self.circles_group):
            circle_i, circle_j = self.circles_group[i], self.circles_group[j]
            index_label_i, index_label_j = self.labels_group[i], self.labels_group[j]
            
            # 获取当前位置
            pos_i, pos_j = circle_i.get_center(), circle_j.get_center()
            index_pos_i, index_pos_j = index_label_i.get_center(), index_label_j.get_center()
            
            # 添加交换特效 - 先高亮
            self.play(
                circle_i[0].animate.set_stroke(color=self.colors['transition'], width=8),
                circle_j[0].animate.set_stroke(color=self.colors['transition'], width=8),
                run_time=0.3
            )
            
            # 创建弧形路径交换（更有趣的动画）
            # 圆圈i向上弧形移动到j的位置
            # 圆圈j向下弧形移动到i的位置
            arc_height = 0.8
            
            # 中间点（弧形顶点）
            mid_point_i = pos_i + (pos_j - pos_i) * 0.5 + UP * arc_height
            mid_point_j = pos_j + (pos_i - pos_j) * 0.5 + DOWN * arc_height
            
            # 分两步执行弧形动画
            # 第一步：移动到弧形中点
            self.play(
                circle_i.animate.move_to(mid_point_i),
                circle_j.animate.move_to(mid_point_j),
                run_time=0.4
            )
            
            # 第二步：移动到最终位置
            self.play(
                circle_i.animate.move_to(pos_j),
                circle_j.animate.move_to(pos_i),
                index_label_i.animate.move_to(index_pos_j),
                index_label_j.animate.move_to(index_pos_i),
                run_time=0.4
            )
            
            # 恢复边框颜色
            self.play(
                circle_i[0].animate.set_stroke(color=self.colors['primary'], width=3),
                circle_j[0].animate.set_stroke(color=self.colors['primary'], width=3),
                run_time=0.2
            )
            
            # 更新VGroup中的顺序
            self.circles_group.submobjects[i], self.circles_group.submobjects[j] = circle_j, circle_i
            self.labels_group.submobjects[i], self.labels_group.submobjects[j] = index_label_j, index_label_i
    
    def mark_pivot_position(self, index):
        """标记基准元素的最终位置（圆圈版本）"""
        # 更新操作说明
        final_text = f"基准元素 {self.numbers[index]} 已就位于位置 {index}"
        new_operation_label = Text(
            final_text, 
            font_size=16, 
            color=self.colors['success']
        ).move_to(self.operation_label.get_center())
        
        self.play(Transform(self.operation_label, new_operation_label), run_time=0.3)
        
        # 从基准元素集合移除，添加到已排序集合
        self.pivot_indices.discard(index)
        self.sorted_indices.add(index)
        
        if index < len(self.circles_group):
            pivot_circle = self.circles_group[index]
            
            # 移除旧的基准标记
            if self.pivot_indicator:
                self.play(FadeOut(self.pivot_indicator), run_time=0.2)
                self.pivot_indicator = None
            
            # 改变基准元素为成功色并添加庆祝效果
            self.play(
                pivot_circle[0].animate.set_fill(color=self.colors['success'], opacity=0.9),
                pivot_circle[0].animate.set_stroke(color=self.colors['success'], width=5),
                run_time=0.5
            )
            
            # 添加完成标记 - 使用更醒目的标记
            check_mark = Text("✓", font_size=20, color=self.colors['success'], weight=BOLD)
            check_mark.next_to(pivot_circle, UP, buff=0.1)
            
            # 添加"已排序"标记
            sorted_label = Text(
                "已排序", 
                font_size=10, 
                color=self.colors['success'],
                weight=BOLD
            ).next_to(pivot_circle, DOWN, buff=0.5)
            
            # 同时显示两个标记
            completion_marks = VGroup(check_mark, sorted_label)
            self.play(FadeIn(completion_marks), run_time=0.5)
            
            # 短暂的庆祝动画 - 轻微的缩放效果
            self.play(
                pivot_circle.animate.scale(1.2),
                run_time=0.2
            )
            self.play(
                pivot_circle.animate.scale(1/1.2),
                run_time=0.2
            ) 