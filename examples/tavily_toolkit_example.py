#!/usr/bin/env python
"""
Tavily工具包使用示例。

本脚本展示如何在camel框架中使用TavilyToolkit。
"""

from dotenv import load_dotenv
import yaml

load_dotenv()

import logging
import os
import sys

from loguru import logger

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.insert(0, project_root)

# 导入TavilyToolkit
from camel.agents import ChatAgent
from camel.models import ModelFactory
from camel.types import ModelPlatformType
from camel.types.agents import ToolCallingRecord

from agents.video_agent.tools.tavily_toolkit import TavilyToolkit

# 设置日志级别
logger.remove()
logger.add(sys.stderr, level=logging.INFO)


def use_toolkit_with_agent():
    """展示如何在Agent中使用Tavily工具包"""

    # 初始化工具包
    toolkit = TavilyToolkit()

    # 获取工具列表
    tools = toolkit.get_tools()

    # 显示可用工具信息
    logger.info(f"\n=== 可用工具(共{len(tools)}个) ===")
    for i, tool in enumerate(tools, 1):
        logger.info(f"{i}. {tool.get_function_name()}: {tool.get_function_description()}")

    # 加载配置
    config_path = "config/config.yaml"
    try:
        with open(config_path) as file:
            config = yaml.safe_load(file)
        # 设置配置属性
        model_config = config.get("model", {})
        api_config = model_config.get("api", {})
    except Exception as e:
        logger.error(f"加载配置出错: {str(e)}")
        model_config = {"type": "openai/gpt-4o-mini"}
        api_config = {}

    # 创建聊天Agent配置
    model = ModelFactory.create(
        model_platform=ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
        model_type=model_config.get("type", "openai/gpt-4o-mini"),
        api_key=api_config.get("openai_compatibility_api_key"),
        url=api_config.get("openai_compatibility_api_base_url"),
    )

    # 创建聊天Agent
    agent = ChatAgent(
        system_message="""你是一个擅长使用搜索工具获取信息的助手。
当用户提出问题时，你应该使用提供的搜索工具来查找相关信息，并提供有根据的回答。
尽可能使用最新的信息，并引用信息的来源。""",
        model=model,
        tools=tools,
    )

    # 用户问题示例
    questions = [
        "理查德·费曼是谁？他的主要科学贡献是什么？",
        "2023年发布的主要大语言模型有哪些？",
        "量子计算的基本原理是什么？",
    ]

    # 模拟用户对话并获取回答
    for question in questions:
        logger.info(f"\n\n=== 问题: {question} ===\n")

        # 获取助手回答
        logger.info("正在使用Tavily搜索信息...")
        assistant_response = agent.step(question)
        if "tool_calls" in assistant_response.info:
            tool_calls: list[ToolCallingRecord] = assistant_response.info["tool_calls"]
            for tool_call in tool_calls:
                tool_name = tool_call.tool_name
                args = tool_call.args
                credit_usage = tool_call.result.get("metadata", {}).get("api_credits_used", 0)
                logger.info(f"tool_name: {tool_name}, args: {args}, credit_usage: {credit_usage}")
        logger.info(assistant_response.info)

        # 显示回答
        logger.info(f"\n回答: {assistant_response.msg.content}")
        logger.info("\n", "=" * 50)


def use_toolkit_directly():
    """直接使用Tavily工具包的功能"""
    logger.info("\n=== 直接使用Tavily工具包 ===\n")

    # 初始化工具包
    toolkit = TavilyToolkit()

    # 示例1: 基础搜索
    logger.info("\n1. 基础搜索示例\n")
    search_result = toolkit.search(
        query="费曼物理学讲义的主要内容",
        search_depth="basic",
        max_results=2,
        include_answer=True,
    )

    # 打印搜索结果摘要
    logger.info(f"搜索查询: {search_result.get('metadata', {}).get('query')}")
    logger.info(f"API点数使用: {search_result.get('metadata', {}).get('api_credits_used')}")

    if "answer" in search_result and search_result["answer"]:
        logger.info(f"回答: {search_result['answer']}")

    if "results" in search_result:
        logger.info(f"找到 {len(search_result['results'])} 个结果:")
        for i, result in enumerate(search_result["results"], 1):
            logger.info(f"  {i}. {result.get('title')} - {result.get('url')}")

    # 示例2: 获取搜索上下文
    logger.info("\n2. 获取搜索上下文\n")
    context_result = toolkit.get_search_context(query="量子纠缠的工作原理", max_tokens=500)

    logger.info(f"搜索查询: {context_result.get('query')}")
    context_preview = (
        context_result.get("context", "")[:200] + "..."
        if len(context_result.get("context", "")) > 200
        else context_result.get("context", "")
    )
    logger.info(f"上下文预览: {context_preview}")

    # 示例3: 内容提取
    logger.info("\n3. 内容提取示例\n")
    extract_result = toolkit.extract(urls=["https://en.wikipedia.org/wiki/Richard_Feynman"], include_images=True)

    if "results" in extract_result and extract_result["results"]:
        result = extract_result["results"][0]
        content_preview = (
            result.get("raw_content", "")[:200] + "..."
            if len(result.get("raw_content", "")) > 200
            else result.get("raw_content", "")
        )
        logger.info(f"URL: {result.get('url')}")
        logger.info(f"内容预览: {content_preview}")
        logger.info(f"图片数量: {len(result.get('images', []))}")

    logger.info(f"成功提取: {extract_result.get('metadata', {}).get('success_count')}")
    logger.info(f"失败提取: {extract_result.get('metadata', {}).get('failed_count')}")
    logger.info(f"API点数使用: {extract_result.get('metadata', {}).get('api_credits_used')}")


if __name__ == "__main__":
    # 加载配置
    config_path = "config/config.yaml"
    try:
        with open(config_path) as file:
            config = yaml.safe_load(file)
        # 获取Tavily API配置
        tavily_config = config.get("tavily", {})
        tavily_api_key = tavily_config.get("api_key")
        
        # 如果配置中没有API密钥，尝试使用环境变量
        if not tavily_api_key:
            tavily_api_key = os.environ.get("TAVILY_API_KEY")
    except Exception as e:
        logger.error(f"加载配置出错: {str(e)}")
        tavily_api_key = os.environ.get("TAVILY_API_KEY")
    
    # 检查是否设置了Tavily API密钥
    if not tavily_api_key:
        logger.warning("未找到Tavily API密钥。请在config/config.yaml文件中设置tavily.api_key或设置TAVILY_API_KEY环境变量。")
        logger.info("可以通过运行以下命令设置环境变量：")
        logger.info("export TAVILY_API_KEY='your_api_key'")

        # 如果未设置API密钥，询问是否继续
        continue_anyway = input("API密钥未设置，示例可能无法正常运行。是否继续？(y/n): ")
        if continue_anyway.lower() != "y":
            sys.exit(0)
    else:
        # 如果有API密钥，设置到环境变量以供Tavily工具包使用
        os.environ["TAVILY_API_KEY"] = tavily_api_key

    # 执行示例
    try:
        # 直接使用工具包功能
        use_toolkit_directly()

        # 在Agent中使用工具包
        use_toolkit_with_agent()
    except Exception as e:
        logger.error(f"示例运行出错: {e}")

    logger.info("\n==== 示例结束 ====\n")
