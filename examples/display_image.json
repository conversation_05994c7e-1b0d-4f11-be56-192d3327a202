{"schema_version": "2.0-mvp", "metadata": {"title": "aa", "author": "用户", "background_color": "BLACK"}, "objects": [], "actions": [{"type": "animate_image", "params": {"image_path": "assets/manim_logo.png", "id": "image", "annotation": "# 图像示例\n\n- 项目1\n- 项目2\n- 项目3", "narration": "这是一张manim logo图片"}}, {"type": "animate_image", "params": {"image_path": "output/2506.15556/medias/page_2_image_0.png", "annotation": ["🧠 迭代推测", "👂 接收分块输入", "💬 生成候选回复", "✅ 验证与更新", "🔊 最终输出"], "narration": "PredGen的核心魔力在于利用用户输入期间GPU的闲置资源进行推测性生成。其整体流程如图所示，首先，当用户说话时，系统会接收到一系列局部提示。其次，PredGen基于这些不完整的提示，立即猜测用户意图，生成候选回复，并预先启动文本转语音系统。最后，随着用户后续输入，PredGen会不断迭代更新猜测，并通过验证机制判断之前生成部分的正确性，从而实现核心能力。"}}]}