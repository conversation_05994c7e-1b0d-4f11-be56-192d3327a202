#!/usr/bin/env python3
"""
InfoCollectorToolkit 使用示例

本示例展示如何在 Camel 框架中使用 InfoCollectorToolkit 工具包，
用于从多种来源收集和处理信息。
"""

import argparse
import os
import sys
import yaml

from rich.console import Console
from rich.markdown import Markdown

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from camel.agents import ChatAgent
from camel.models import ModelFactory
from camel.types import ModelPlatformType
from camel.types.agents import ToolCallingRecord

from agents.video_agent.tools.info_collector_toolkit import InfoCollectorToolkit


def demo_standalone_toolkit():
    """
    演示独立使用工具包的功能
    """
    console = Console()
    console.print("[bold green]演示 InfoCollectorToolkit 独立使用[/bold green]")

    # 初始化工具包
    toolkit = InfoCollectorToolkit(use_cache=True)

    # 获取可用的适配器和格式
    adapters = toolkit.get_available_adapters()
    formats = toolkit.get_available_formats()
    console.print(f"可用适配器: [cyan]{', '.join(adapters)}[/cyan]")
    console.print(f"可用输出格式: [cyan]{', '.join(formats)}[/cyan]")

    # 演示搜索功能
    query = "费曼物理学讲义的主要内容"
    console.print(f"\n[bold yellow]搜索: [/bold yellow]{query}")
    result = toolkit.search(query, max_results=3, time_range="w")

    if isinstance(result, dict) and "content" in result:
        console.print(Markdown(result["content"]))
    else:
        console.print(result)

    # 演示网页内容提取
    url = "https://en.wikipedia.org/wiki/Richard_Feynman"
    console.print(f"\n[bold yellow]提取网页内容: [/bold yellow]{url}")
    result = toolkit.extract_web_content(url, output_format="markdown")

    if isinstance(result, dict) and "content" in result:
        # 只显示部分内容
        content = result["content"]
        if len(content) > 1000:
            content = content[:1000] + "...(内容已截断)"
        console.print(Markdown(content))
    else:
        console.print(result)


def demo_agent_usage(model_name: str):
    """
    演示在 Camel Agent 中使用 InfoCollectorToolkit
    
    参数:
        model_name: 模型名称
    """
    console = Console()
    console.print("[bold green]演示在 Camel Agent 中使用 InfoCollectorToolkit[/bold green]")

    # 加载配置
    config_path = "config/config.yaml"
    try:
        with open(config_path) as file:
            config = yaml.safe_load(file)
        # 设置配置属性
        model_config = config.get("model", {})
        api_config = model_config.get("api", {})
    except Exception as e:
        console.print(f"[bold red]加载配置出错: {str(e)}[/bold red]")
        model_config = {"type": model_name}
        api_config = {}

    # 初始化工具包
    toolkit = InfoCollectorToolkit(use_cache=True)
    tools = toolkit.get_tools()

    # 创建模型
    model = ModelFactory.create(
        model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
        model_type=model_config.get("type", model_name),
        api_key=api_config.get("openai_compatibility_api_key"),
        url=api_config.get("openai_compatibility_api_base_url"),
    )

    # 创建代理
    system_message = """你是一个研究助手，擅长收集和整理各种信息。
你可以搜索网络、提取网页内容、处理PDF文件和文本文档。
每次请求都要提供详细、准确的信息，并注明信息来源。"""

    agent = ChatAgent(
        system_message=system_message,
        model=model,
        tools=tools,
    )

    # 发送任务
    user_message = """帮我查询一下关于量子计算最新进展的信息，
特别是在量子算法和量子纠错方面的突破。
整理成一个简洁的摘要，不超过500字。"""

    console.print(f"\n[bold cyan]用户: [/bold cyan]{user_message}")

    # 获取代理回复
    ai_message = agent.step(user_message)

    # 显示工具调用过程
    tool_calls: list[ToolCallingRecord] = ai_message.tool_calls or []

    if tool_calls:
        console.print("[bold yellow]工具调用过程:[/bold yellow]")
        for i, tool_call in enumerate(tool_calls):
            console.print(f"[bold]调用 {i + 1}: [/bold]{tool_call.name}")
            # 简化显示参数
            args_str = str(tool_call.parameters)
            if len(args_str) > 100:
                args_str = args_str[:100] + "...(参数已截断)"
            console.print(f"参数: {args_str}")

            # 简化显示结果
            result_str = str(tool_call.result)
            if len(result_str) > 200:
                result_str = result_str[:200] + "...(结果已截断)"
            console.print(f"结果: {result_str}\n")

    # 显示最终回复
    console.print(f"[bold green]AI: [/bold green]{ai_message.content}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="InfoCollectorToolkit 示例")
    parser.add_argument(
        "--model-name",
        type=str,
        default="gpt-4o-mini",
        help="模型名称",
    )
    parser.add_argument(
        "--standalone-only",
        action="store_true",
        help="仅演示独立使用工具包，不使用 Agent",
    )

    args = parser.parse_args()

    # 演示独立使用工具包
    demo_standalone_toolkit()

    if not args.standalone_only:
        # 演示 Agent 使用工具包
        demo_agent_usage(args.model_name)


if __name__ == "__main__":
    main()
