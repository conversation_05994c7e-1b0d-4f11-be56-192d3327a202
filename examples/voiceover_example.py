from manim import *

from utils.edgetts_service import EdgeTTSService

config.pixel_height = 1920

config.pixel_width = 1090

config.frame_height = 16.0

config.frame_width = 9.0
from manim_funcs.anim_funcs import *


class GeneratedAnimation(FeynmanScene):
    def construct(self):
        self.set_speech_service(
            EdgeTTSService(global_speed=1.2, voice="zh-CN-YunxiNeural"),
            create_subcaption=True,
        )

        with self.voiceover("DynaSaur框架如何通过动态动作创建，让LLM代理更智能，解决现实世界难题？"):
            self.display_slogo("DynaSaur框架如何通过动态动作创建，让LLM代理更智能，解决现实世界难题？", 1)
