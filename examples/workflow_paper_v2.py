from dotenv import load_dotenv

load_dotenv()

import datetime
import json
import logging
import os
import sys

import yaml
from camel.agents import ChatAgent as CamelChatAgent
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.types import ModelPlatformType
from loguru import logger

# 添加项目根目录到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

# 导入模块，使用相对导入
from agents.feynman_agent import FeynmanAgent
from agents.intention_agent import IntentionAgent
from agents.outline_agent import OutlineAgent
from agents.paper_discussion_agent import PaperDiscussionAgent
from agents.video_agent.tools.info_collector_toolkit import InfoCollectorToolkit
from tools.pdf_toolkit import PDFToolkit
from utils.format import extract_json, save_json_content

# 设置日志级别为INFO，并配置日志格式
log_file = f"output/workflow_paper_log_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
os.makedirs(os.path.dirname(log_file), exist_ok=True)

# 配置根日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(),  # 同时输出到控制台
    ],
)

# 最终生成内容的格式
PAPER_GENERATION_FORMAT_PROMPT = """
```json
[
    {
        "分镜名": "分镜的子标题描述,比如开篇标题/背景动机/核心概念/方法框架/实验分析/评价讨论"，
        "内容要点": "提取关键词列表,关键词长度不超过10,关键词的个数不超过5，比如机构、提升效果、之前存在的问题、本文提出解决方法、研究方向"，
        "分镜内容": "开篇标题/背景动机/核心概念/方法框架/实验分析/应用方法/评价讨论等的文案内容"，
        "素材名": "分镜内容对应输入markdown中材料的图片、表格、视频等媒体素材名称, 比如pdf_output/2411.01747v1_artifacts/page_3_image_0.png"，
        "补充素材": "包含流程图、思维导图、代码片段、例子、数据等非多媒体的描述性内容，可以是文本形式的流程描述、伪代码、算法步骤、类比说明等，用于丰富分镜内容，不要求在markdown文件中存在"，
        "视觉动效建议": "针对用到的素材，描述需要添加什么动效，以达到最佳的效果，能吸引观众注意力，并使讲解重点突出而且易于理解"
    },
    ...
]
```
"""

# 内容生成器提示词
CONTENT_GENERATOR_PROMPT = """
你是一位专业的论文讲解内容生成器，需要基于以下输入生成一个论文讲解的分镜内容：

1. 用户意图分析结果：{intention_analysis}
2. 论文原始内容：{paper_content}
3. 论文讨论结果：{paper_discussion}
4. 费曼解释结果：{feynman_result}

根据用户意图分析，用户主要关注的是：
- 主要意图：{primary_intent}
- 期望的表述方式：{expected_expression}
- 关注的内容深度：{content_depth}

请生成一个完整的论文分镜讲解，每个分镜内容精炼且有深度，严格控制字数，并确保提供读者意外的认知收获。每个分镜包含以下内容：

1. **分镜名**：分镜的子标题描述
   - 根据论文类型和大纲建议，选择合适的分镜名称和结构
   - 适合不同类型论文的分镜组合：综述型论文（研究现状、方法分类、趋势展望）、实验型论文（实验设计、对照组结果、消融实验）、理论型论文（理论基础、定理证明、应用场景）、方法型论文（算法描述、计算复杂度、性能优化）、技术报告（实际部署、系统架构、应用案例）

2. **内容要点**：提取关键词列表（关键词长度不超过10，个数不超过5）
   - 关键词应优先从意图分析和论文讨论中提取，确保覆盖用户最关心的内容点

3. **分镜内容**：精炼且深入的讲解文案，严格控制在规定字数内：

   - **开篇标题类**：生成一句震撼吸引人的标题，【必须严格控制在15字以内】，必须包含知名机构/作者和量化的效果提升数字（如速度提升了45%、精度提高了8.7%等）。必须使用具体数字而非模糊表述。

   - **核心概念类**：【100-150字】解释研究问题的创新性和相关核心概念，包括概念的物理含义和通俗类比，引入论文中的关键公式或算法。必须使用费曼解释中的类比和简化解释，使复杂概念更易理解。

   - **背景动机类**：【100-180字】说明研究问题的重要性、相关工作的不足之处、本文的主要发现和创新点，给读者带来新视角。从论文讨论中提取深度见解作为论述基础。必须使用量化表述。

   - **方法框架类**：【150-200字】解释提出的方法/框架，按模块/步骤/环节一步步说明，解释每部分的作用和原理，必须引用论文中的关键算法、流程图或核心代码片段。采用费曼解释中的通俗表达方式解释技术细节。

   - **实验分析类**：【100-150字】简述实验方法和核心数据结论，必须包含精确的量化数据，引用论文中最具说服力的实验图表。应从论文讨论中提取对实验结果的深入分析和洞见。

   - **评价讨论类**：【150-200字】分析论文的创新点、不足之处和未来研究方向，给出具体的改进建议，融入论文讨论中提出的深度问题和洞见，为读者提供超越论文本身的思考。

   - **综述类**：【150-200字】概括研究领域的发展历程、主要方法分类、研究热点和挑战，必须引用关键时间节点和里程碑式的工作。从论文讨论中提取对不同方法的比较分析。

   - **趋势展望类**：【100-150字】分析该领域未来可能的研究方向、潜在突破点和应用前景，必须结合最新研究进展和产业需求，提供前瞻性观点。使用量化预测。

   - **应用案例类**：【100-180字】介绍该技术在实际场景中的应用示例、部署方式和实际效果，必须包含具体的应用环境、面临的挑战和解决方案。必须使用量化效果描述。

   - **理论基础类**：【100-150字】解释研究的理论依据、数学基础和推导过程，引用关键定理、公式或证明，但必须用通俗的类比使其易于理解。从费曼解释中借鉴简化复杂理论的表达方式。

   - **系统架构类**：【150-200字】说明系统的组成部分、各模块功能和数据流转过程，必须引用系统架构图，解释组件间的交互关系。采用清晰的层次描述。

   - **用户研究类**：【100-150字】描述用户调研方法、反馈结果和改进措施，必须包含用户群体特征、关键指标和实际提升效果。使用费曼解释中的场景描述使内容更加生动。

4. **素材名**：每个分镜必须引用至少一个适当的素材，且素材**必须来自论文原始内容**：
   - 图表：论文中的图形、图表、流程图等
   - 代码：论文中的关键算法的代码片段
   - 公式：论文中的核心公式和数学表达式
   - 表格：论文中展示实验结果或对比数据的表格

   **重要说明**：素材名必须对应论文中实际存在的内容，不得使用不存在的素材名称。

5. **补充素材**：每个分镜可以包含丰富的补充描述性内容，这些内容不要求在论文中存在，可以包括：
   - 文本形式的流程图描述：详细描述系统或方法的工作流程
   - 思维导图结构：概念之间的层次关系和连接
   - 伪代码和算法步骤：用简化形式展示算法逻辑
   - 具体应用例子：通过实际场景说明技术的应用方式
   - 数据分析说明：对关键数据的解读和趋势分析
   - 类比和比喻：将复杂概念通过生活中的例子进行解释

   **补充素材的来源和使用**：
   - 优先从费曼解释中提取通俗易懂的描述和类比
   - 从论文讨论中提取深度分析创建补充说明
   - 针对复杂概念，构建由简到难的渐进式解释

6. **视觉动效建议**：针对素材的动效建议，详细描述动效类型和实现方式：
   - 对于图表：提供逐步展开、高亮重点部分、使用箭头指示流程等动效建议
   - 对于代码：提供代码行高亮、逐行解释、关键变量追踪等动效建议
   - 对于公式：提供公式组件逐步展示、符号解释动画等动效建议
   - 对于表格：提供数据比较高亮、趋势线添加等动效建议

## 内容长度和格式特别要求
1. 开篇标题【必须严格控制在15字以内】，并且必须包含量化数字
2. 各类型分镜内容必须严格控制在规定字数内：
   - 核心概念类：100-150字
   - 方法框架类：150-200字
   - 实验分析类：100-150字
   - 评价讨论类：150-200字
   - 其他类型：100-180字
3. 确保内容精炼，直接表达核心观点，避免冗余表述
4. 必须使用量化数据和精确表述，如"提升45%"而非"显著提升"

## 素材和内容整合特别要求
1. 所有素材必须从论文原始内容中选取，不要使用不存在的素材名称
2. 每个分镜都必须整合费曼解释的内容：
   - 使用费曼解释中的类比来简化复杂概念
   - 借鉴费曼解释中的逐步推导方式
   - 引用费曼解释中的具体例子和场景
3. 每个分镜都必须整合论文讨论的见解：
   - 在评价讨论类分镜中融入论文讨论中的批评和改进建议
   - 在方法框架类分镜中引入讨论中关于技术原理的深度解析
   - 在实验分析类分镜中结合讨论中对实验设计和结果的评价

请使用以下JSON格式输出：
{output_format}
"""

# 内容评价器提示词
CONTENT_EVALUATOR_PROMPT = """
你是一位专业的内容评价专家，需要对以下生成的论文讲解内容进行全面评价：

{generated_content}

评价标准如下：
1. **格式规范性**：是否完全符合要求的JSON格式
2. **表述准确性**：内容是否准确表达了论文的核心思想和方法
   - 开篇标题是否足够简洁震撼（严格控制在15字以内，使用知名机构/提升数字等吸引人元素）
   - 背景动机是否包含相关工作及不足的介绍
   - 方法框架是否详细讲解每个模块/步骤的操作和物理含义
   - 实验数据和提升效果是否量化（具体到百分比、倍数等精确数字）
3. **内容长度控制**：是否符合各类型分镜的长度要求
   - 核心概念类：100-150字
   - 方法框架类：150-200字
   - 实验分析类：100-150字
   - 评价讨论类：150-200字
   - 其他类型：100-180字
4. **内容丰富度**：内容是否深入丰富，提供了足够的信息和洞见
   - 是否包含论文中的核心公式、关键代码或重要算法
   - 是否引入了深度的领域知识和见解
   - 是否挖掘了论文中隐含的重要信息
   - 是否为读者提供了超出论文表面的认知收获
5. **费曼解释整合**：是否有效整合了费曼解释的内容
   - 是否使用了费曼解释中的类比来简化复杂概念
   - 是否借鉴了费曼解释中的逐步推导方式
   - 是否引用了费曼解释中的具体例子和场景
6. **论文讨论整合**：是否整合了论文讨论的见解
   - 是否在评价讨论类分镜中融入了论文讨论中的批评和改进建议
   - 是否在方法框架类分镜中引入了讨论中关于技术原理的深度解析
   - 是否在实验分析类分镜中结合讨论中对实验设计和结果的评价
7. **素材来源合规性**：素材是否来自论文原始内容
   - 素材名称是否对应真实存在的论文内容
   - 是否使用了论文中的图表、表格、公式、算法等
   - 方法框架分镜是否使用了合适的方法图示或代码片段
8. **语言流畅性**：语言是否简洁、流畅、易于理解
   - 是否使用了通俗的类比解释专业概念
   - 表达是否避免过多术语堆砌
9. **量化表述**：是否使用了量化数据和精确表述
   - 标题中是否使用了量化数字
   - 效果描述是否使用了具体数字（如"提升45%"）而非模糊表述（如"显著提升"）
   - 实验结果是否有精确的数据支持

请详细评价每一个标准，指出优点和不足之处。评分标准为1-10分。
如果有不足之处，请提供具体的修改建议。特别注意评价每个分镜的内容长度控制、量化表述和费曼解释整合情况。

最后，给出总体评分（满分100分）和是否需要修改的结论。若总分低于80分则需要修改。
"""

# 内容修改器提示词
CONTENT_REVISER_PROMPT = """
你是一位专业的内容修改专家，需要基于以下评价结果修改论文讲解内容：

原始内容：
{original_content}

评价结果：
{evaluation_result}

请根据评价结果中指出的不足之处，对原始内容进行修改。修改时请特别注意以下几点：

1. 格式要求：
   - 保持原有的JSON格式结构不变
   - 确保所有字段名称和格式符合要求

2. 内容长度控制：
   - 开篇标题必须严格控制在15字以内，使用量化数字和知名机构名称
   - 各类型分镜内容必须严格控制在规定字数内：
     * 核心概念类：100-150字
     * 方法框架类：150-200字
     * 实验分析类：100-150字
     * 评价讨论类：150-200字
     * 其他类型：100-180字
   - 确保内容精炼，直接表达核心观点，避免冗余表述

3. 量化表述：
   - 标题中必须使用量化数字（如"提升45%"、"快10倍"等）
   - 效果描述必须使用具体数字而非模糊表述
   - 实验结果必须包含精确的数据支持

4. 费曼解释整合：
   - 在核心概念类分镜中使用费曼解释中的类比和简化解释
   - 在方法框架类分镜中借鉴费曼解释中的逐步推导方式
   - 引用费曼解释中的具体例子和场景来增强内容可理解性
   - 通过费曼解释中的内容来简化复杂概念的表达

5. 论文讨论整合：
   - 在评价讨论类分镜中融入论文讨论中的批评和改进建议
   - 在方法框架类分镜中引入讨论中关于技术原理的深度解析
   - 在实验分析类分镜中结合讨论中对实验设计和结果的评价
   - 使用论文讨论中的见解来增强内容深度

6. 素材来源合规性：
   - 所有素材必须来自论文原始内容，不允许使用不存在的素材
   - 方法框架分镜应使用展示模型架构或流程的图片，以及关键算法的代码片段
   - 实验分析分镜应使用包含实验结果的图表和数据比较
   - 核心概念分镜应引用关键公式和算法
   - 确保素材名称对应于论文中实际存在的内容

7. 内容精炼与深度平衡：
   - 在保持字数限制的同时，确保内容深入且有见解
   - 通过精炼语言表达核心观点，避免冗余和无关内容
   - 确保内容符合用户期望的专业深度：{user_intent}
   - 提供超出预期的认知价值，让目标读者获得新的视角

针对评价中提到的每个问题，请有针对性地进行修改，使修改后的内容更加准确、精炼、具有深度，并且符合内容长度限制和素材要求。

请直接输出修改后的完整内容，使用以下JSON格式：
{output_format}
"""


class WorkflowPaper:
    """
    论文分镜讲解工作流类

    实现完整的论文分镜讲解生成流程，包括：
    1. 使用意图代理分析用户的潜在意图
    2. 使用PDF工具和信息收集工具获取论文内容和相关信息
    3. 使用大纲代理生成内容大纲
    4. 使用论文讨论代理进行深入讨论
    5. 使用费曼代理生成通俗解释
    6. 使用内容生成代理生成分镜讲解内容
    7. 使用内容评价代理评价生成内容，并根据需要进行修改
    """

    def __init__(self, config_path="config/config.yaml"):
        """初始化工作流"""
        # 保存配置路径
        self.config_path = config_path

        # 加载配置
        self.load_config(config_path)

        # 初始化模型
        self.model = self._create_model()
        logger.info("模型初始化完成")

        # 初始化工具包
        self.toolkits = self._initialize_toolkits()
        logger.info("工具包初始化完成")

        # 初始化代理
        self.agents = self._initialize_agents()
        logger.info("代理初始化完成")

    def load_config(self, config_path):
        """从YAML文件加载配置"""
        try:
            with open(config_path) as file:
                config = yaml.safe_load(file)
            # 设置配置属性
            self.model_config = config.get("model", {})
            self.file_config = config.get("files", {})
            self.agent_config = config.get("agents", {})
            self.pdf_config = config.get("pdf", {})
            self.outline_config = config.get("outline", {})
            self.feynman_config = config.get("feynman", {})

            # 加载工作流控制配置
            self.workflow_config = config.get("workflow", {})
            # 默认从头开始执行
            self.start_stage = self.workflow_config.get("start_stage", None)
            # 加载是否启用大纲和费曼模块
            self.enable_outline = self.workflow_config.get("enable_outline", True)
            self.enable_feynman = self.workflow_config.get("enable_feynman", True)

            # 设置各阶段的输出文件路径
            self.stage_files = {
                "intention": self.workflow_config.get("intention_file", "output/intention_result.json"),
                "paper_info": self.workflow_config.get("paper_info_file", "output/paper_info.json"),
                "paper_content": self.workflow_config.get("paper_content_file", "output/paper_content.md"),
                "outline": self.workflow_config.get("outline_file", "output/outline_result.json"),
                "discussion": self.workflow_config.get("discussion_file", "output/discussion_result.json"),
                "feynman": self.workflow_config.get("feynman_file", "output/feynman_result.json"),
                "generation": self.workflow_config.get("generation_file", "output/generated_content.json"),
                "media_fix": self.workflow_config.get("media_fix_file", "output/fixed_content.json"),
                "evaluation": self.workflow_config.get("evaluation_file", "output/evaluation_result.json"),
                "revision": self.workflow_config.get("revision_file", "output/revised_content.json"),
                "final": self.file_config.get("content_file", "output/paper_content.json"),
            }

            logger.info("从 %s 加载配置", config_path)
        except Exception as e:
            logger.error(f"加载配置错误: {str(e)}")
            # 设置默认值
            self.model_config = {"type": "openai/gpt-4o-mini"}
            self.file_config = {"content_file": "output/paper_content.json"}
            self.agent_config = {}
            self.pdf_config = {"url": "", "output_dir": "pdf_output/"}
            self.workflow_config = {}
            self.start_stage = None
            self.stage_files = {}
            self.enable_outline = True
            self.enable_feynman = True

    def _create_model(self):
        """创建模型实例"""
        # 从配置中获取API设置
        api_config = self.model_config.get("api", {})
        
        return ModelFactory.create(
            model_platform=ModelPlatformType.OPENROUTER,
            model_type=self.model_config.get("type", "openai/gpt-4o-mini"),
            api_key=api_config.get("openrouter_api_key"),
            url=api_config.get("openrouter_api_base_url"),
        )

    def _initialize_toolkits(self):
        """初始化各种工具包"""
        return {
            "pdf": PDFToolkit(),
            "info_collector": InfoCollectorToolkit(use_cache=True),
        }

    def _initialize_agents(self):
        """初始化所有需要的代理"""
        agents = {}

        # 意图分析代理
        agents["intention"] = IntentionAgent()

        # 论文讨论代理
        agents["paper_discussion"] = PaperDiscussionAgent()

        # 大纲生成代理
        if self.enable_outline:
            agents["outline"] = OutlineAgent(config_path=self.config_path)

        # 费曼解释代理
        if self.enable_feynman:
            agents["feynman"] = FeynmanAgent(config_path=self.config_path)

        # 内容生成代理
        agents["content_generator"] = CamelChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="Content Generator",
                content="你是一位专业的论文讲解内容生成器，擅长将学术论文转化为通俗易懂、结构清晰的讲解内容。",
            ),
            model=self.model,
        )

        # 内容评价代理
        agents["content_evaluator"] = CamelChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="Content Evaluator",
                content="你是一位专业的内容评价专家，擅长从格式、表述、语言、逻辑、信息完整性、吸引力等角度评价内容质量。",
            ),
            model=self.model,
        )

        # 内容修改代理
        agents["content_reviser"] = CamelChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="Content Reviser",
                content="你是一位专业的内容修改专家，擅长根据评价意见对内容进行精准修改和优化。",
            ),
            model=self.model,
        )

        return agents

    def analyze_intention(self, user_id=None, user_profile=None, content_topics=None):
        """
        分析用户意图

        Args:
            user_id: 用户ID
            user_profile: 用户画像
            content_topics: 内容主题

        Returns:
            dict: 意图分析结果
        """
        logger.info("开始分析用户意图")
        intention_agent = self.agents["intention"]

        # 从配置中获取论文URL
        paper_url = self.pdf_config.get("url", "")

        # 构建查询内容
        query = f"我想了解这篇论文: {paper_url}"

        # 进行意图分析
        intention_result = intention_agent.analyze_intent(
            query=query,
            user_id=user_id,
            user_profile=user_profile,
            content_topics=content_topics,
        )

        logger.info("用户意图分析完成")
        return intention_result

    def collect_paper_info(self, paper_url):
        """
        收集论文信息

        Args:
            paper_url: 论文URL或路径

        Returns:
            dict: 收集到的论文信息
        """
        logger.info(f"开始收集论文信息: {paper_url}")

        output_dir = self.pdf_config.get("output_dir", "pdf_output/")

        # 使用PDF工具包提取论文内容
        pdf_toolkit = self.toolkits["pdf"]
        pdf_result = pdf_toolkit.extract_pdf(paper_url, output_dir)

        if "error" in pdf_result:
            logger.error(f"PDF提取错误: {pdf_result['error']}")
            return {"error": pdf_result["error"]}

        # 读取转换后的Markdown内容
        markdown_file = pdf_result.get("markdown_file")
        with open(markdown_file, encoding="utf-8") as f:
            paper_content = f.read()

        logger.info(f"论文信息收集完成，Markdown文件: {markdown_file}")

        # 使用信息收集工具补充相关信息
        info_collector = self.toolkits["info_collector"]

        # 提取论文标题，首先查找markdown中的第一个#开头的标题
        import re

        title_match = re.search(r"^#\s+(.+)$", paper_content, re.MULTILINE)

        if title_match:
            paper_title = title_match.group(1).strip()
            logger.info(f"从Markdown中提取的论文标题: {paper_title}")
        else:
            # 如果找不到标题，尝试提取前几行中可能的标题（通常加粗或大字体）
            first_lines = paper_content.split("\n")[:10]
            for line in first_lines:
                if "**" in line or "#" in line:
                    potential_title = line.replace("**", "").replace("#", "").strip()
                    if len(potential_title) > 10:  # 标题通常有一定长度
                        paper_title = potential_title
                        logger.info(f"从Markdown前几行提取的可能标题: {paper_title}")
                        break
            else:
                # 如果仍然无法找到标题，回退到使用文件名
                paper_title = os.path.basename(paper_url).replace(".pdf", "")
                logger.warning(f"无法从Markdown中提取标题，使用文件名作为搜索关键词: {paper_title}")

        try:
            search_result = info_collector.search(
                query=f"论文解读 {paper_title}",
                max_results=3,
                time_range="y",  # 最近一年的结果
            )
            logger.info("补充信息搜索完成")
        except Exception as e:
            logger.error(f"搜索补充信息时出错: {e}")
            search_result = {"error": str(e)}

        return {
            "paper_content": paper_content,
            "pdf_result": pdf_result,
            "search_result": search_result,
            "paper_title": paper_title,
        }

    def discuss_paper(self, paper_content):
        """
        进行论文深入讨论

        Args:
            paper_content: 论文内容

        Returns:
            dict: 讨论结果
        """
        logger.info("开始论文深入讨论")

        discussion_agent = self.agents["paper_discussion"]

        # 获取讨论参数
        max_chains = self.pdf_config.get("max_chains", 3)
        max_depth = self.pdf_config.get("max_depth", 4)

        # 进行讨论
        discussion_result = discussion_agent.process_paper(paper_content, max_chains, max_depth)

        logger.info("论文讨论完成")
        return discussion_result

    def generate_outline(self, article_content, intention_analysis):
        """
        生成内容大纲

        Args:
            article_content: 文章内容
            intention_analysis: 意图分析结果

        Returns:
            dict: 大纲生成结果
        """
        if not self.enable_outline:
            logger.info("大纲生成模块已禁用，跳过生成")
            return {"disabled": True}

        logger.info("开始生成内容大纲")

        outline_agent = self.agents.get("outline")
        if not outline_agent:
            logger.error("大纲代理未初始化")
            return {"error": "大纲代理未初始化"}

        # 获取大纲生成参数
        max_rounds = self.outline_config.get("max_rounds", 3)

        # 准备用户画像和内容主题
        # user_profile = self.outline_config.get("user_profile", {})
        # content_topics = self.outline_config.get("content_topics", [])

        try:
            # 使用意图分析结果调用大纲生成
            outline_result = outline_agent.generate_outline(article_content, intention_analysis)

            # 如果需要迭代优化
            if max_rounds > 1:
                outline_result = outline_agent.refine_outline(
                    article_content,
                    outline_result,
                    intention_analysis,
                    max_rounds,
                )

            logger.info("内容大纲生成完成")
            return {"outline": outline_result, "success": True}
        except Exception as e:
            logger.error(f"生成大纲出错: {str(e)}")
            return {"error": f"生成大纲错误: {str(e)}"}

    def generate_feynman_explanation(self, article_content, outline_result=None):
        """
        生成费曼解释

        Args:
            article_content: 文章内容
            outline_result: 大纲结果（可选）

        Returns:
            dict: 费曼解释结果
        """
        if not self.enable_feynman:
            logger.info("费曼解释模块已禁用，跳过生成")
            return {"disabled": True}

        logger.info("开始生成费曼解释")

        feynman_agent = self.agents.get("feynman")
        if not feynman_agent:
            logger.error("费曼代理未初始化")
            return {"error": "费曼代理未初始化"}

        # 获取费曼解释参数
        max_rounds = self.feynman_config.get("max_rounds", 3)

        # 准备大纲内容，如果有的话
        storyboard_content = None
        if outline_result and isinstance(outline_result, dict) and "outline" in outline_result:
            storyboard_content = json.dumps(outline_result["outline"], ensure_ascii=False, indent=2)

        # 提取论文URL
        paper_url = self.pdf_config.get("url", "")

        try:
            # 调用费曼代理生成解释
            feynman_result = feynman_agent.generate_feynman_explanation(
                article_content,
                storyboard_content,
                max_rounds,
                paper_url,
            )

            logger.info("费曼解释生成完成")
            return feynman_result
        except Exception as e:
            logger.error(f"生成费曼解释出错: {str(e)}")
            return {"error": f"生成费曼解释错误: {str(e)}"}

    def generate_content(
        self,
        intention_analysis,
        paper_info,
        discussion_result,
        outline_result=None,
        feynman_result=None,
    ):
        """
        生成论文讲解内容

        Args:
            intention_analysis: 意图分析结果
            paper_info: 论文信息
            discussion_result: 讨论结果
            outline_result: 大纲结果（可选）
            feynman_result: 费曼解释结果（可选）

        Returns:
            dict: 生成的内容
        """
        logger.info("开始生成论文讲解内容")

        content_generator = self.agents["content_generator"]

        # 提取用户意图的关键信息
        primary_intent = ", ".join(intention_analysis.get("primary_intent", ["理解与学习"]))

        content_style = intention_analysis.get("content_style", {})
        expected_expression = content_style.get("tone", "中性") + ", " + content_style.get("format", "文本")
        content_depth = content_style.get("depth", "中等")

        # 准备大纲内容，提取关键结构和概念
        outline_content = ""
        outline_key_concepts = []
        outline_structure = []

        if outline_result and isinstance(outline_result, dict):
            # 尝试从outline_result中提取outline
            outline_data = None
            if "outline" in outline_result:
                outline_data = outline_result["outline"]
            elif not outline_result.get("disabled", False) and not outline_result.get("error"):
                outline_data = outline_result

            if outline_data:
                try:
                    # 提取关键概念
                    if "key_concepts" in outline_data:
                        outline_key_concepts = outline_data["key_concepts"]

                    # 提取建议的分镜结构
                    if "storyboard_suggestions" in outline_data:
                        outline_structure = outline_data["storyboard_suggestions"]

                    # 完整大纲内容
                    outline_content = json.dumps(outline_data, ensure_ascii=False, indent=2)
                except Exception as e:
                    logger.warning(f"处理大纲内容时出错: {str(e)}")

        # 准备费曼解释内容，确保包含关键类比和简化解释
        feynman_content = ""
        feynman_analogies = []
        feynman_key_explanations = []
        feynman_json_data = {}

        if feynman_result and isinstance(feynman_result, dict):
            # 保存完整的费曼结果JSON数据
            feynman_json_data = feynman_result.copy()

            # 从feynman_result中提取各部分内容
            if "explanation" in feynman_result:
                feynman_content = feynman_result["explanation"] or ""

                # 尝试从解释中提取关键类比和解释
                if feynman_content:
                    # 简单的方式提取可能的类比段落（含有"就像"、"类似于"、"可以比作"等词语的句子）
                    import re

                    analogies = re.findall(
                        r"[^。！？.!?]*?(?:就像|类似于|可以比作|比如说|相当于)[^。！？.!?]*[。！？.!?]",
                        feynman_content,
                    )
                    if analogies:
                        feynman_analogies = analogies[:5]  # 限制数量

                    # 提取包含关键概念解释的段落
                    if outline_key_concepts:
                        for concept in outline_key_concepts:
                            concept_pattern = f"[^。！？.!?]*?{concept}[^。！？.!?]*[。！？.!?]"
                            explanations = re.findall(concept_pattern, feynman_content)
                            if explanations:
                                feynman_key_explanations.extend(explanations[:2])  # 每个概念最多取2个解释

            # 如果有简化版解释，也提取出来
            if "simplified_explanation" in feynman_result:
                simplified = feynman_result.get("simplified_explanation", "")
                if simplified and len(simplified) > 0:
                    if not feynman_content:
                        feynman_content = simplified
                    elif len(feynman_content) > 5000:  # 如果原解释太长，使用简化版
                        feynman_content = simplified

        # 提取论文讨论中的深度问题和洞见
        discussion_insights = []
        discussion_questions = []
        discussion_criticisms = []

        if discussion_result and isinstance(discussion_result, dict):
            discussion_data = discussion_result.get("discussion", {})

            # 提取对话中的深度洞见
            for chain in discussion_data.get("chains", []):
                for message in chain.get("messages", []):
                    content = message.get("content", "")
                    # 提取看起来是洞见的内容
                    if "值得注意的是" in content or "有趣的是" in content or "重要的发现" in content:
                        insights = re.findall(
                            r"[^。！？.!?]*?(?:值得注意的是|有趣的是|重要的发现)[^。！？.!?]*[。！？.!?]",
                            content,
                        )
                        if insights:
                            discussion_insights.extend(insights)

                    # 提取问题
                    if "?" in content or "？" in content:
                        questions = re.findall(r"[^。！？.!?]*?[?？]", content)
                        if questions:
                            discussion_questions.extend(questions)

                    # 提取批评或局限性分析
                    if "局限性" in content or "不足" in content or "改进" in content or "问题" in content:
                        criticisms = re.findall(
                            r"[^。！？.!?]*?(?:局限性|不足|改进|问题)[^。！？.!?]*[。！？.!?]",
                            content,
                        )
                        if criticisms:
                            discussion_criticisms.extend(criticisms)

        # 提取论文原始内容中的关键素材
        paper_materials = {}
        if paper_info and isinstance(paper_info, dict):
            # 提取图表、表格、公式和代码
            paper_content = paper_info.get("paper_content", "")

            # 提取图表引用
            import re

            figure_refs = re.findall(r"(?:图|Fig\.?|Figure|图表)\s*\d+", paper_content)
            table_refs = re.findall(r"(?:表|Tab\.?|Table)\s*\d+", paper_content)
            equation_refs = re.findall(r"(?:公式|Eq\.?|Equation)\s*\d+", paper_content)
            algorithm_refs = re.findall(r"(?:算法|Alg\.?|Algorithm)\s*\d+", paper_content)

            paper_materials = {
                "figures": figure_refs[:10],  # 限制数量
                "tables": table_refs[:5],
                "equations": equation_refs[:5],
                "algorithms": algorithm_refs[:5],
            }

        # 构建内容生成提示词
        prompt = CONTENT_GENERATOR_PROMPT.format(
            intention_analysis=json.dumps(intention_analysis, ensure_ascii=False),
            paper_content=paper_info.get("paper_content", "")[:5000],  # 限制长度
            paper_discussion=json.dumps(discussion_result.get("discussion", {}), ensure_ascii=False),
            primary_intent=primary_intent,
            expected_expression=expected_expression,
            content_depth=content_depth,
            feynman_result=json.dumps(feynman_json_data, ensure_ascii=False, indent=2),
            output_format=PAPER_GENERATION_FORMAT_PROMPT,
        )

        # 添加大纲内容
        if outline_content:
            prompt += f"\n\n## 内容大纲\n以下是为本文生成的内容大纲，请参考这个结构设计分镜:\n{outline_content}\n"

            # 添加关键概念和建议分镜
            if outline_key_concepts:
                prompt += f"\n### 大纲中的关键概念\n{json.dumps(outline_key_concepts, ensure_ascii=False, indent=2)}\n"

            if outline_structure:
                prompt += f"\n### 大纲建议的分镜结构\n{json.dumps(outline_structure, ensure_ascii=False, indent=2)}\n"

        # 添加费曼解释内容
        if feynman_content:
            # 截取费曼解释的前部分，避免提示词过长
            feynman_excerpt = feynman_content[:3000] + ("..." if len(feynman_content) > 3000 else "")
            prompt += f"\n\n## 费曼解释\n以下是使用费曼技术为本文生成的通俗解释，请借鉴其中的类比和解释方式:\n{feynman_excerpt}\n"

            # 添加费曼JSON数据
            if feynman_json_data:
                prompt += f"\n### 费曼解释JSON数据\n{json.dumps(feynman_json_data, ensure_ascii=False, indent=2)}\n"

            # 添加关键类比和解释
            if feynman_analogies:
                prompt += f"\n### 费曼解释中的关键类比\n{json.dumps(feynman_analogies, ensure_ascii=False, indent=2)}\n"

            if feynman_key_explanations:
                prompt += f"\n### 费曼解释中的核心概念解释\n{json.dumps(feynman_key_explanations, ensure_ascii=False, indent=2)}\n"

        # 添加论文讨论中的深度洞见
        if discussion_insights or discussion_questions or discussion_criticisms:
            prompt += "\n\n## 论文讨论中的关键洞见和问题\n"

            if discussion_insights:
                prompt += f"\n### 深度洞见\n{json.dumps(discussion_insights[:5], ensure_ascii=False, indent=2)}\n"

            if discussion_questions:
                prompt += f"\n### 关键问题\n{json.dumps(discussion_questions[:5], ensure_ascii=False, indent=2)}\n"

            if discussion_criticisms:
                prompt += (
                    f"\n### 批评和局限性分析\n{json.dumps(discussion_criticisms[:5], ensure_ascii=False, indent=2)}\n"
                )

        # 添加论文原始素材
        if paper_materials:
            prompt += "\n\n## 论文原始素材\n"
            prompt += f"{json.dumps(paper_materials, ensure_ascii=False, indent=2)}\n"

        # 添加整合指导
        prompt += """

## 内容整合指导
请将上述所有信息融合到分镜内容中：
1. 分镜结构应遵循大纲建议的框架
2. 使用费曼解释中的类比和简化解释来讲解复杂概念
3. 将论文讨论中的深度洞见、批评和问题融入评价讨论部分
4. 确保内容符合用户意图分析中的期望专业度和表述方式
5. 每个分镜都要提供超出论文表面的见解，给读者带来意外收获
6. 所有素材都必须从论文原始内容中选取，不要使用不存在的素材名称
7. 充分利用"补充素材"字段，为每个分镜提供丰富的描述性内容：
   - 方法框架类分镜应配有文本形式的流程图描述或伪代码
   - 核心概念类分镜应配有思维导图结构或概念之间的关系说明
   - 实验分析类分镜应配有数据分析说明和结果解读
   - 理论基础类分镜应配有通俗的类比和比喻
   - 应用案例类分镜应配有具体的使用场景描述

## 内容长度限制
1. 开篇标题必须严格控制在15字以内，并尽量使用量化数字
2. 每个分镜内容请控制在以下范围：
   - 核心概念类：100-150字
   - 方法框架类：150-200字
   - 实验分析类：100-150字
   - 评价讨论类：150-200字
   - 其他类型：100-180字
3. 确保内容精炼，直接表达核心观点，避免冗余表述
4. 优先使用量化数据和精确表述，如"提升45%"而非"显著提升"

## 费曼解释和论文讨论整合
1. 对于每个分镜，必须融入费曼解释中的相关内容：
   - 使用费曼解释中的类比来简化复杂概念
   - 借鉴费曼解释中的逐步推导方式
   - 引用费曼解释中的具体例子和场景
2. 同时整合论文讨论的深度见解：
   - 在评价讨论类分镜中融入论文讨论中的批评和改进建议
   - 在方法框架类分镜中引入讨论中关于技术原理的深度解析
   - 在实验分析类分镜中结合讨论中对实验设计和结果的评价
3. 使用费曼解释及论文讨论来增强每个分镜的深度和广度，但仍需保持内容精炼
"""

        # 生成内容
        user_message = BaseMessage.make_user_message(role_name="User", content=prompt)
        response = content_generator.step(user_message)

        try:
            # 提取JSON内容
            content_text = response.msg.content
            content_json = extract_json(content_text)
            logger.info("内容生成完成")
            return content_json
        except Exception as e:
            logger.error(f"解析生成内容错误: {e}")
            return {"error": f"内容生成错误: {str(e)}", "raw_content": response.msg.content}

    def evaluate_content(self, generated_content, intention_analysis):
        """
        评价生成的内容

        Args:
            generated_content: 生成的内容
            intention_analysis: 意图分析结果

        Returns:
            dict: 评价结果
        """
        logger.info("开始评价生成内容")

        content_evaluator = self.agents["content_evaluator"]

        # 准备评价提示词
        prompt = CONTENT_EVALUATOR_PROMPT.format(
            generated_content=json.dumps(generated_content, ensure_ascii=False, indent=2),
        )

        # 进行评价
        user_message = BaseMessage.make_user_message(role_name="User", content=prompt)
        response = content_evaluator.step(user_message)

        evaluation_result = response.msg.content
        logger.info("内容评价完成")

        return {
            "evaluation": evaluation_result,
            "is_satisfactory": "不需要修改" in evaluation_result or "无需修改" in evaluation_result,
        }

    def revise_content(self, original_content, evaluation_result, intention_analysis):
        """
        修改生成的内容

        Args:
            original_content: 原始内容
            evaluation_result: 评价结果
            intention_analysis: 意图分析结果

        Returns:
            dict: 修改后的内容，如果出错则返回包含error键的字典
        """
        logger.info("开始修改生成内容")

        if not original_content:
            logger.error("无法修改内容：原始内容为空")
            return {"error": "原始内容为空"}

        if not evaluation_result:
            logger.error("无法修改内容：评价结果为空")
            return {"error": "评价结果为空"}

        if not intention_analysis:
            logger.error("无法修改内容：意图分析结果为空")
            return {"error": "意图分析结果为空"}

        content_reviser = self.agents.get("content_reviser")
        if not content_reviser:
            logger.error("内容修改代理未初始化")
            return {"error": "内容修改代理未初始化"}

        # 提取用户意图的关键信息
        try:
            primary_intent = ", ".join(intention_analysis.get("primary_intent", ["理解与学习"]))
            content_style = intention_analysis.get("content_style", {})
            user_intent = f"意图: {primary_intent}, 风格: {content_style.get('tone', '中性')}, 深度: {content_style.get('depth', '中等')}"
        except Exception as e:
            logger.error(f"处理意图分析数据出错: {e}")
            user_intent = "意图: 理解与学习, 风格: 中性, 深度: 中等"

        # 准备修改提示词
        try:
            prompt = CONTENT_REVISER_PROMPT.format(
                original_content=json.dumps(original_content, ensure_ascii=False, indent=2),
                evaluation_result=evaluation_result,
                user_intent=user_intent,
                output_format=PAPER_GENERATION_FORMAT_PROMPT,
            )
        except Exception as e:
            logger.error(f"准备修改提示词出错: {e}")
            return {"error": f"准备修改提示词出错: {str(e)}"}

        # 进行修改
        try:
            user_message = BaseMessage.make_user_message(role_name="User", content=prompt)
            response = content_reviser.step(user_message)
        except Exception as e:
            logger.error(f"内容修改过程出错: {e}")
            return {"error": f"内容修改过程出错: {str(e)}"}

        # 如果响应为空，返回错误
        if not response or not hasattr(response, "msg") or not response.msg:
            logger.error("内容修改代理返回空响应")
            return {"error": "内容修改代理返回空响应"}

        try:
            # 提取JSON内容
            revised_text = response.msg.content
            if not revised_text:
                logger.error("内容修改代理返回空内容")
                return {"error": "内容修改代理返回空内容"}

            revised_json = extract_json(revised_text)
            if not revised_json:
                logger.error("无法从修改内容中提取JSON")
                return {"error": "无法从修改内容中提取JSON", "raw_content": revised_text}

            logger.info("内容修改完成")
            return revised_json
        except Exception as e:
            logger.error(f"解析修改内容错误: {e}")
            return {
                "error": f"内容修改错误: {str(e)}",
                "raw_content": response.msg.content if hasattr(response, "msg") else "无内容",
            }

    def check_and_fix_media_resources(self, content, paper_info):
        """
        检查和修复分镜讲解中的多媒体素材引用

        Args:
            content: 生成的分镜内容
            paper_info: 论文信息，包含PDF提取结果

        Returns:
            dict: 修复后的分镜内容
        """
        logger.info("开始检查和修复多媒体素材引用")

        # 验证参数
        if not content:
            logger.error("无法检查和修复媒体资源：内容为空")
            return content

        if not isinstance(content, list):
            logger.error(f"无法检查和修复媒体资源：内容格式不是列表 (实际类型: {type(content)})")
            return content

        if not paper_info:
            logger.error("无法检查和修复媒体资源：论文信息为空")
            return content

        # 获取PDF提取的图片信息
        pdf_result = paper_info.get("pdf_result", {})
        images = pdf_result.get("images", [])
        image_paths = [img.get("path", "") for img in images]

        # 获取PDF文件所在目录，用于构建相对路径
        output_dir = self.pdf_config.get("output_dir", "pdf_output/")
        if not output_dir.endswith("/"):
            output_dir += "/"

        # 尝试识别论文ID以查找对应的images.json文件
        paper_id = None
        if "url" in paper_info:
            import re

            id_match = re.search(r"(\d+\.\d+)", paper_info["url"])
            if id_match:
                paper_id = id_match.group(1)
                logger.info(f"从URL识别到论文ID: {paper_id}")

        # 查找对应的images.json文件
        import json
        import os

        images_json_data = []
        images_json_path = None

        # 检查可能的images.json文件位置
        possible_paths = []
        if paper_id:
            possible_paths.extend(
                [
                    f"pdf_output/{paper_id}_images.json",
                    f"pdf_output/{paper_id}v1_images.json",  # 尝试添加版本号
                    f"pdf_output/{paper_id}v2_images.json",  # 尝试添加版本号
                ],
            )

        # 检查输出目录中的所有images.json文件
        if os.path.exists(output_dir):
            for filename in os.listdir(output_dir):
                if filename.endswith("_images.json"):
                    possible_paths.append(os.path.join(output_dir, filename))

        # 尝试打开并读取每个可能的images.json文件
        for path in possible_paths:
            if os.path.exists(path):
                try:
                    with open(path, encoding="utf-8") as f:
                        images_json_data = json.load(f)
                    images_json_path = path
                    logger.info(f"找到并加载images.json文件: {path}")
                    break
                except Exception as e:
                    logger.warning(f"尝试读取{path}时出错: {str(e)}")

        # 如果找到images.json文件，添加到image_paths
        if images_json_data:
            for img_item in images_json_data:
                if "path" in img_item and img_item["path"] not in image_paths:
                    image_paths.append(img_item["path"])
                    # 添加到images列表，便于后续处理
                    if img_item not in images:
                        images.append(img_item)
            logger.info(f"从{images_json_path}添加了额外的图片信息")

        # 从markdown内容中提取可能的图片引用
        markdown_content = paper_info.get("paper_content", "")
        import re

        markdown_images = re.findall(r"!\[.*?\]\((.*?)\)", markdown_content)

        # 提取表格信息 (通常表格在markdown中以 | --- | 形式出现)
        table_positions = []
        lines = markdown_content.split("\n")
        for i, line in enumerate(lines):
            if "|" in line and "---" in line:
                # 找到表格头部后，往前找表格标题，往后找表格内容
                start = max(0, i - 5)  # 往前最多看5行
                end = min(len(lines), i + 15)  # 往后最多看15行
                table_positions.append((start, end))

        # 对每个分镜检查素材名称
        fixed_content = []
        relevance_records = []  # 用于记录相关性分数的变化

        for scene in content:
            scene_name = scene.get("分镜名", "")
            scene_content = scene.get("分镜内容", "")
            scene_material = scene.get("素材名", "")

            # 检查素材名是否存在
            material_exists = scene_material and (
                os.path.exists(scene_material) or any(scene_material in path for path in image_paths)
            )

            # 初始化记录
            record = {
                "分镜名": scene_name,
                "原素材": scene_material,
                "原相关性分数": None,
                "新素材": None,
                "新相关性分数": None,
                "修改原因": "",
            }

            # 如果素材不存在或为空，尝试匹配合适的素材
            if not material_exists:
                logger.warning(f"分镜'{scene_name}'的素材'{scene_material}'不存在，尝试匹配替代素材")
                record["修改原因"] = "素材不存在或为空"

                # 根据分镜内容关键词匹配可能的素材
                matched_material = self._match_content_with_material(
                    scene_name,
                    scene_content,
                    image_paths,
                    markdown_images,
                    table_positions,
                    lines,
                )

                if matched_material:
                    logger.info(f"为分镜'{scene_name}'找到替代素材: {matched_material}")
                    scene["素材名"] = matched_material
                    record["新素材"] = matched_material

                    # 计算新素材的相关性分数
                    try:
                        new_relevance = self._check_material_relevance(
                            scene_name,
                            scene_content,
                            matched_material,
                            paper_info,
                        )
                        record["新相关性分数"] = round(new_relevance, 2)
                        logger.info(f"新素材相关性分数: {round(new_relevance, 2)}")
                    except Exception as e:
                        logger.error(f"计算相关性分数出错: {str(e)}")

                    # 更新视觉动效建议，根据新素材类型
                    if matched_material.endswith((".png", ".jpg", ".jpeg")):
                        if "图" in scene_name or "实验" in scene_name or "方法" in scene_name:
                            scene[
                                "视觉动效建议"
                            ] = f"对于这张{scene_name}相关的图片，建议使用逐步呈现动画效果，突出关键组件，并在解释每个部分时使用放大聚焦效果。可配合箭头指示流程方向，增强理解。"
                        else:
                            scene[
                                "视觉动效建议"
                            ] = "对于这张图片，建议使用淡入效果展示，并在讲解相关内容时给图片添加柔和的高亮效果，突出重点区域。"
                    elif "表格" in matched_material.lower():
                        scene[
                            "视觉动效建议"
                        ] = "对于这个表格，建议使用行列逐步显示的动画效果，在讲解每组数据时高亮相关行列，并使用颜色区分不同类型的数据，突出关键的数值对比。"

            # 检查素材内容是否与分镜内容匹配
            if material_exists and scene_material:
                # 计算原素材的相关性分数
                original_relevance = self._check_material_relevance(
                    scene_name,
                    scene_content,
                    scene_material,
                    paper_info,
                )
                record["原相关性分数"] = round(original_relevance, 2)
                logger.info(f"分镜'{scene_name}'的素材'{scene_material}'相关性分数: {round(original_relevance, 2)}")

                if original_relevance < 0.5:  # 如果相关性较低
                    logger.warning(f"分镜'{scene_name}'的素材'{scene_material}'与内容相关性较低，尝试匹配更合适的素材")
                    record["修改原因"] = f"原素材相关性较低 ({round(original_relevance, 2)})"

                    # 尝试匹配更相关的素材
                    better_material = self._match_content_with_material(
                        scene_name,
                        scene_content,
                        image_paths,
                        markdown_images,
                        table_positions,
                        lines,
                    )

                    if better_material and better_material != scene_material:
                        logger.info(
                            f"为分镜'{scene_name}'找到更合适的素材: {better_material}，替换原素材: {scene_material}",
                        )
                        scene["素材名"] = better_material
                        record["新素材"] = better_material

                        # 计算新素材的相关性分数
                        new_relevance = self._check_material_relevance(
                            scene_name,
                            scene_content,
                            better_material,
                            paper_info,
                        )
                        record["新相关性分数"] = round(new_relevance, 2)
                        logger.info(f"新素材相关性分数: {round(new_relevance, 2)}")

                        # 更新视觉动效建议
                        if "方法" in scene_name or "框架" in scene_name:
                            scene[
                                "视觉动效建议"
                            ] = "对于这张方法框架图，建议使用分步骤动画展示，逐个模块高亮并配合箭头指示流程，在讲解每个环节时放大相关部分。"
                        elif "实验" in scene_name or "分析" in scene_name:
                            scene[
                                "视觉动效建议"
                            ] = "对于这张实验结果图表，建议使用数据渐进显示动画，并在提到关键性能指标时添加闪烁高亮效果，使用色彩对比突出本文方法的优势。"

            # 添加相关性记录
            if record["原相关性分数"] is not None or record["新相关性分数"] is not None:
                relevance_records.append(record)

            fixed_content.append(scene)

        # 输出相关性分数变化记录
        if relevance_records:
            logger.info("===== 素材相关性分数变化记录 =====")
            for record in relevance_records:
                change_desc = ""
                if record["原相关性分数"] is not None and record["新相关性分数"] is not None:
                    diff = record["新相关性分数"] - record["原相关性分数"]
                    change_desc = f"变化: {'+' if diff >= 0 else ''}{round(diff, 2)}"

                logger.info(f"分镜: {record['分镜名']}")
                logger.info(f"  原因: {record['修改原因']}")
                logger.info(f"  原素材: {record['原素材'] or '无'}, 原分数: {record['原相关性分数'] or '无'}")
                logger.info(f"  新素材: {record['新素材'] or '未修改'}, 新分数: {record['新相关性分数'] or '无'}")
                if change_desc:
                    logger.info(f"  {change_desc}")
                logger.info("---")

        logger.info("多媒体素材引用检查和修复完成")
        return fixed_content

    def _match_content_with_material(
        self,
        scene_name,
        scene_content,
        image_paths,
        markdown_images,
        table_positions,
        markdown_lines,
    ):
        """
        根据分镜内容匹配最合适的素材

        Args:
            scene_name: 分镜名称
            scene_content: 分镜内容
            image_paths: 可用的图片路径列表
            markdown_images: markdown中的图片引用
            table_positions: markdown中的表格位置列表(开始行, 结束行)
            markdown_lines: markdown内容按行分割的列表

        Returns:
            str: 匹配到的素材路径，如果没有匹配到则返回空字符串
        """
        # 提取分镜内容和名称中的关键词
        import re
        from collections import Counter

        # 清理文本，提取关键词
        def extract_keywords(text):
            # 移除常见的停用词和标点符号
            text = re.sub(r"[^\w\s]", " ", text.lower())
            words = text.split()
            # 根据长度和频率筛选关键词
            return [w for w in words if len(w) > 1]

        scene_keywords = extract_keywords(scene_name + " " + scene_content)
        scene_keyword_counts = Counter(scene_keywords)

        # 根据分镜类型确定要匹配的素材类型
        is_method_scene = any(kw in scene_name.lower() for kw in ["方法", "框架", "模型", "架构"])
        is_experiment_scene = any(kw in scene_name.lower() for kw in ["实验", "结果", "分析", "评估"])
        is_concept_scene = any(kw in scene_name.lower() for kw in ["概念", "背景", "核心"])

        best_match = ""
        highest_score = 0

        # 检查是否应该寻找表格
        should_find_table = "表" in scene_content or "对比" in scene_content or "数据" in scene_content

        # 如果是寻找表格，且表格位置列表非空
        if should_find_table and table_positions:
            for start, end in table_positions:
                table_content = "\n".join(markdown_lines[start:end])
                table_keywords = extract_keywords(table_content)

                # 计算关键词匹配分数
                match_score = sum(scene_keyword_counts.get(word, 0) for word in table_keywords)

                if match_score > highest_score:
                    highest_score = match_score
                    # 为表格创建一个描述性名称
                    table_desc = f"表格（位于行{start}-{end}）"
                    best_match = table_desc

        # 遍历所有图片路径，匹配最合适的图片
        for path in image_paths:
            # 获取图片的caption（如果有）
            caption = ""
            # 首先尝试从path对象中获取caption（针对images_json_data中的项目）
            if isinstance(path, dict) and "caption" in path:
                caption = path.get("caption", "")
                path = path.get("path", "")  # 更新path为实际路径

            # 提取图片名称和caption的关键词
            img_name = os.path.basename(path)
            img_keywords = extract_keywords(img_name)

            # 如果有caption，添加caption关键词
            if caption:
                caption_keywords = extract_keywords(caption)
                img_keywords.extend(caption_keywords)

            # 根据图片路径中的页码和图片类型评估相关性
            page_match = re.search(r"page_(\d+)", path)
            page_num = int(page_match.group(1)) if page_match else 0

            # 计算基础匹配分数
            match_score = sum(scene_keyword_counts.get(word, 0) for word in img_keywords)

            # 如果有caption，增加匹配权重
            if caption:
                # 直接检查caption中是否包含场景关键词
                for keyword in scene_keywords:
                    if keyword in caption.lower():
                        match_score += 3  # 每个匹配的关键词增加额外权重

            # 对于图形类型的关键词，增加特定权重
            if is_method_scene and any(
                kw in caption.lower()
                for kw in [
                    "framework",
                    "model",
                    "architecture",
                    "method",
                    "approach",
                    "system",
                    "structure",
                    "框架",
                    "方法",
                    "系统",
                    "架构",
                ]
            ):
                match_score += 10

            if is_experiment_scene and any(
                kw in caption.lower()
                for kw in [
                    "result",
                    "experiment",
                    "evaluation",
                    "performance",
                    "comparison",
                    "实验",
                    "结果",
                    "评估",
                    "性能",
                    "比较",
                ]
            ):
                match_score += 10

            if "figure" in caption.lower() or "图" in caption:
                figure_number = re.search(r"figure\s*(\d+)", caption.lower())
                if figure_number:
                    match_score += 5  # 图片编号会更准确

            # 基于分镜类型的额外加权
            if is_method_scene and "page_" in path and page_num > 1:
                match_score += 5  # 方法图通常不在第一页
            if is_experiment_scene and "page_" in path and page_num > 3:
                match_score += 5  # 实验结果通常在论文后部
            if is_concept_scene and "page_" in path and page_num < 3:
                match_score += 5  # 概念图通常在论文前部

            if match_score > highest_score:
                highest_score = match_score
                best_match = path

        # 如果没有找到足够好的匹配，使用任何可用的图片
        if not best_match and image_paths:
            if is_method_scene:
                # 尝试找中间页的图片作为方法图
                method_imgs = [
                    p
                    for p in image_paths
                    if isinstance(p, str) and "page_" in p and int(re.search(r"page_(\d+)", p).group(1)) in range(2, 5)
                ]
                if method_imgs:
                    return method_imgs[0]
            elif is_experiment_scene:
                # 尝试找后面页的图片作为实验图
                exp_imgs = [
                    p
                    for p in image_paths
                    if isinstance(p, str) and "page_" in p and int(re.search(r"page_(\d+)", p).group(1)) > 5
                ]
                if exp_imgs:
                    return exp_imgs[0]

            # 如果仍然没有找到，返回第一张图片
            if isinstance(image_paths[0], dict) and "path" in image_paths[0]:
                return image_paths[0]["path"]
            else:
                return image_paths[0]

        return best_match

    def _check_material_relevance(self, scene_name, scene_content, material_path, paper_info):
        """
        检查素材与分镜内容的相关性

        Args:
            scene_name: 分镜名称
            scene_content: 分镜内容
            material_path: 素材路径
            paper_info: 论文信息

        Returns:
            float: 相关性得分(0-1)
        """
        import json
        import os
        import re

        # 基础相关性得分
        base_relevance = 0.5

        # 如果素材是图片，检查图片相关性
        if material_path.endswith((".png", ".jpg", ".jpeg")):
            # 从路径中提取页码信息
            page_match = re.search(r"page_(\d+)", material_path)
            if not page_match:
                return base_relevance  # 无法确定页码的默认中等相关

            page_num = int(page_match.group(1))

            # 基于位置的相关性
            position_relevance = 0.4  # 默认相关性
            # 根据分镜类型和页码判断相关性
            if "方法" in scene_name or "框架" in scene_name:
                # 方法框架通常在中间页（第2-4页）
                position_relevance = 0.8 if 2 <= page_num <= 4 else 0.4
            elif "实验" in scene_name or "结果" in scene_name or "分析" in scene_name:
                # 实验结果通常在后面几页
                position_relevance = 0.8 if page_num >= 5 else 0.4
            elif "概念" in scene_name or "背景" in scene_name:
                # 概念和背景通常在前面几页
                position_relevance = 0.8 if page_num <= 2 else 0.4

            # 查找并使用图片的caption信息
            content_relevance = 0.5  # 默认内容相关性
            caption_found = False

            # 首先检查PDF结果中的图像caption
            if "pdf_result" in paper_info and "images" in paper_info["pdf_result"]:
                for image in paper_info["pdf_result"]["images"]:
                    if image["path"] == material_path and image.get("caption"):
                        caption = image["caption"]
                        caption_found = True

                        # 计算caption与分镜内容的相似度
                        scene_text = scene_name + " " + scene_content
                        similarity = self._calculate_text_similarity(caption, scene_text)
                        content_relevance = similarity
                        break

            # 如果在PDF结果中未找到caption，尝试从专用的images.json文件中查找
            if not caption_found:
                # 从URL或文件路径中提取论文ID
                paper_id = None
                if "url" in paper_info:
                    # 从URL中提取ID，例如 https://arxiv.org/abs/2411.01747
                    id_match = re.search(r"(\d+\.\d+)", paper_info["url"])
                    if id_match:
                        paper_id = id_match.group(1)

                # 如果无法从URL获取，尝试从material_path路径提取
                if not paper_id and material_path:
                    id_match = re.search(r"(\d+\.\d+v\d+)_artifacts", material_path)
                    if id_match:
                        paper_id = id_match.group(1)

                # 构建可能的images.json文件路径
                possible_paths = []
                if paper_id:
                    possible_paths.append(f"pdf_output/{paper_id}_images.json")
                    # 移除版本号尝试
                    base_id = re.sub(r"v\d+$", "", paper_id)
                    possible_paths.append(f"pdf_output/{base_id}_images.json")

                # 添加当前正在处理的素材所在目录的images.json
                if material_path:
                    material_dir = os.path.dirname(material_path)
                    if material_dir:
                        # 查找该目录中的所有*_images.json文件
                        if os.path.exists(material_dir):
                            for filename in os.listdir(material_dir):
                                if filename.endswith("_images.json"):
                                    possible_paths.append(os.path.join(material_dir, filename))

                # 添加默认路径
                possible_paths.append("pdf_output/images.json")

                # 遍历所有可能的路径查找caption
                for caption_file in possible_paths:
                    if os.path.exists(caption_file):
                        try:
                            with open(caption_file, encoding="utf-8") as f:
                                caption_data = json.load(f)

                            # 提取图片文件名，用于匹配
                            image_filename = os.path.basename(material_path)

                            for item in caption_data:
                                # 检查path字段
                                if "path" in item and item["path"] == material_path:
                                    caption = item.get("caption", "")
                                    caption_found = True
                                    break

                                # 检查file字段
                                if "file" in item and item["file"] == image_filename:
                                    caption = item.get("caption", "")
                                    caption_found = True
                                    break

                            if caption_found:
                                # 计算caption与分镜内容的相似度
                                scene_text = scene_name + " " + scene_content
                                similarity = self._calculate_text_similarity(caption, scene_text)
                                content_relevance = similarity
                                logger.info(f"从{caption_file}找到图片{image_filename}的caption")
                                break
                        except Exception as e:
                            logger.warning(f"读取caption文件{caption_file}出错: {str(e)}")
                            continue

            # 综合位置相关性和内容相关性
            final_relevance = 0.4 * position_relevance + 0.6 * content_relevance
            return final_relevance

        # 如果是表格或其他类型素材
        elif "表格" in material_path:
            # 表格与数据相关的分镜更相关
            if any(kw in scene_name or kw in scene_content for kw in ["数据", "对比", "评估", "结果", "表格"]):
                return 0.8
            return 0.5

        # 默认返回中等相关性
        return base_relevance

    def _calculate_text_similarity(self, text1, text2):
        """
        计算两段文本的相似度

        Args:
            text1: 第一段文本
            text2: 第二段文本

        Returns:
            float: 相似度得分(0-1)
        """
        from difflib import SequenceMatcher

        # 如果文本很长，可以考虑提取关键词或使用更复杂的NLP方法
        # 这里使用简单的序列匹配算法

        # 简化文本，移除无关字符
        def simplify_text(text):
            if not text:
                return ""
            # 转小写
            text = text.lower()
            # 移除标点和多余空格
            import re

            text = re.sub(r"[^\w\s]", " ", text)
            text = re.sub(r"\s+", " ", text).strip()
            return text

        # 提取关键词（简化处理，实际应用可使用更复杂的NLP方法）
        def extract_keywords(text):
            simplified = simplify_text(text)
            # 排除常见停用词（简化版）
            stopwords = {
                "the",
                "a",
                "an",
                "and",
                "or",
                "but",
                "is",
                "are",
                "in",
                "on",
                "at",
                "to",
                "for",
                "with",
                "的",
                "了",
                "和",
                "与",
                "或",
                "但",
                "是",
                "在",
                "对",
                "由",
                "和",
            }
            words = [w for w in simplified.split() if w not in stopwords and len(w) > 1]
            return " ".join(words)

        # 提取关键词
        keywords1 = extract_keywords(text1)
        keywords2 = extract_keywords(text2)

        # 如果关键词为空，返回低相关性
        if not keywords1 or not keywords2:
            return 0.3

        # 计算相似度
        matcher = SequenceMatcher(None, keywords1, keywords2)
        similarity = matcher.ratio()

        # 归一化相似度（可选）
        return min(1.0, max(0.2, similarity))

    def run_workflow(self, paper_url=None, user_id=None, user_profile=None, content_topics=None):
        """
        运行完整工作流

        Args:
            paper_url: 论文URL
            user_id: 用户ID
            user_profile: 用户画像
            content_topics: 内容主题

        Returns:
            dict: 工作流结果
        """
        result = {}

        # 如果未提供paper_url，使用配置中的
        if not paper_url:
            paper_url = self.pdf_config.get("url", "")
            if not paper_url:
                logger.error("未提供论文URL")
                return {"error": "未提供论文URL"}

        # 记录工作流参数
        logger.info(f"开始运行工作流，处理论文: {paper_url}")
        logger.info(f"工作流起始阶段: {self.start_stage or '从头开始'}")
        logger.info(f"大纲生成模块: {'启用' if self.enable_outline else '禁用'}")
        logger.info(f"费曼解释模块: {'启用' if self.enable_feynman else '禁用'}")

        # 1. 分析用户意图
        if self.start_stage is None or self.start_stage == "intention":
            intention_result = self.analyze_intention(user_id, user_profile, content_topics)
            self._save_stage_result("intention", intention_result)
        else:
            # 尝试从之前的结果加载
            intention_result = self._load_stage_result("intention")
            if not intention_result:
                logger.error("无法加载意图分析结果，无法继续")
                return {"error": "无法加载意图分析结果"}

        result["intention_analysis"] = intention_result
        logger.info(f"意图分析完成: 主要意图 = {', '.join(intention_result.get('primary_intent', []))}")
        logger.info(f"意图分析完成: 次要意图 = {intention_result.get('secondary_intent', [])}")

        # 2. 收集论文信息
        if self.start_stage is None or self.start_stage in ["intention", "paper_info"]:
            paper_info = self.collect_paper_info(paper_url)
            if "error" in paper_info:
                return {"error": paper_info["error"], "intention_analysis": intention_result}

            # 保存除了大型markdown内容之外的结果
            paper_info_save = {k: v for k, v in paper_info.items() if k != "paper_content"}
            self._save_stage_result("paper_info", paper_info_save)

            # 单独保存论文内容
            with open(self.stage_files["paper_content"], "w", encoding="utf-8") as f:
                f.write(paper_info["paper_content"])
            logger.info(f"已保存论文内容到: {self.stage_files['paper_content']}")
        else:
            # 尝试从之前的结果加载
            paper_info_save = self._load_stage_result("paper_info")
            if not paper_info_save:
                logger.error("无法加载论文信息，无法继续")
                return {"error": "无法加载论文信息", "intention_analysis": intention_result}

            # 恢复paper_content
            try:
                with open(self.stage_files["paper_content"], encoding="utf-8") as f:
                    paper_content = f.read()
                paper_info = {**paper_info_save, "paper_content": paper_content}
            except Exception as e:
                logger.error(f"无法加载论文内容: {str(e)}")
                return {"error": "无法加载论文内容", "intention_analysis": intention_result}

        result["paper_info"] = {
            "pdf_result": paper_info["pdf_result"],
            "search_result": paper_info["search_result"],
        }

        # 3. 生成内容大纲
        outline_result = None
        if self.enable_outline and (
            self.start_stage is None or self.start_stage in ["intention", "paper_info", "outline"]
        ):
            logger.info("执行大纲生成阶段")
            outline_result = self.generate_outline(paper_info["paper_content"], intention_result)
            self._save_stage_result("outline", outline_result)
        elif self.enable_outline:
            # 尝试从之前的结果加载
            outline_result = self._load_stage_result("outline")
            if not outline_result:
                logger.warning("未找到已保存的大纲结果，但大纲模块已启用")
            else:
                logger.info("已加载之前的大纲结果")
        else:
            logger.info("大纲生成模块已禁用，跳过生成")

        result["outline_result"] = outline_result

        # 4. 进行论文讨论
        if self.start_stage is None or self.start_stage in ["intention", "paper_info", "outline", "discussion"]:
            discussion_result = self.discuss_paper(paper_info["paper_content"])
            self._save_stage_result("discussion", discussion_result)
        else:
            # 尝试从之前的结果加载
            discussion_result = self._load_stage_result("discussion")
            if not discussion_result:
                logger.error("无法加载论文讨论结果，无法继续")
                return {"error": "无法加载论文讨论结果", **result}

        result["discussion_result"] = discussion_result

        # 5. 生成费曼解释
        feynman_result = None
        if self.enable_feynman and (
            self.start_stage is None
            or self.start_stage in ["intention", "paper_info", "outline", "discussion", "feynman"]
        ):
            logger.info("执行费曼解释阶段")
            feynman_result = self.generate_feynman_explanation(paper_info["paper_content"], outline_result)
            self._save_stage_result("feynman", feynman_result)
        elif self.enable_feynman:
            # 尝试从之前的结果加载
            feynman_result = self._load_stage_result("feynman")
            if not feynman_result:
                logger.warning("未找到已保存的费曼解释结果，但费曼模块已启用")
            else:
                logger.info("已加载之前的费曼解释结果")
        else:
            logger.info("费曼解释模块已禁用，跳过生成")

        result["feynman_result"] = feynman_result

        # 6. 生成内容
        if self.start_stage is None or self.start_stage in [
            "intention",
            "paper_info",
            "outline",
            "discussion",
            "feynman",
            "generation",
        ]:
            generated_content = self.generate_content(
                intention_result,
                paper_info,
                discussion_result,
                outline_result,
                feynman_result,
            )
            if "error" in generated_content:
                return {**result, "error": generated_content["error"]}
            self._save_stage_result("generation", generated_content)
        else:
            # 尝试从之前的结果加载
            generated_content = self._load_stage_result("generation")
            if not generated_content:
                logger.error("无法加载生成内容，无法继续")
                return {"error": "无法加载生成内容", **result}

        # 7. 检查和修复多媒体素材引用
        if self.start_stage is None or self.start_stage in [
            "intention",
            "paper_info",
            "outline",
            "discussion",
            "feynman",
            "generation",
            "media_fix",
        ]:
            fixed_content = self.check_and_fix_media_resources(generated_content, paper_info)
            self._save_stage_result("media_fix", fixed_content)
        else:
            # 尝试从之前的结果加载
            fixed_content = self._load_stage_result("media_fix")
            if not fixed_content:
                logger.error("无法加载多媒体素材修复结果，无法继续")
                return {"error": "无法加载多媒体素材修复结果", **result}

        # 8. 评价和修改内容
        current_content = fixed_content
        is_satisfactory = False
        revision_attempt = 0
        max_revision_attempts = self.workflow_config.get("max_revision_attempts", 3)

        # 如果直接从revision阶段开始，尝试加载之前的评价结果
        if self.start_stage == "revision":
            evaluation_result = self._load_stage_result("evaluation")
            is_satisfactory = evaluation_result and evaluation_result.get("is_satisfactory", False)
            if not is_satisfactory:
                revision_attempt = 1  # 从第1次修改开始
                # 尝试加载已有的修改结果
                revised_content = self._load_stage_result("revision")
                if revised_content:
                    current_content = revised_content

        while not is_satisfactory and revision_attempt < max_revision_attempts:
            # 评价内容
            if self.start_stage != "revision" or revision_attempt > 0:
                evaluation_result = self.evaluate_content(current_content, intention_result)
                self._save_stage_result("evaluation", evaluation_result)

            result[f"evaluation_{revision_attempt}"] = evaluation_result

            is_satisfactory = evaluation_result["is_satisfactory"]
            if is_satisfactory:
                logger.info("内容评价满意，无需修改")
                break

            # 修改内容
            logger.info(f"内容评价不满意，进行第 {revision_attempt + 1} 次修改")
            revised_content = self.revise_content(
                current_content,
                evaluation_result["evaluation"],
                intention_result,
            )

            if revised_content is None:
                logger.warning("修改内容返回为None，使用当前内容")
                break

            if "error" in revised_content:
                logger.warning(f"修改内容错误: {revised_content['error']}")
                # 如果修改失败但已有内容，使用当前内容
                break

            self._save_stage_result("revision", revised_content)

            # 每次修改后再次检查和修复多媒体素材引用
            try:
                # 确保revised_content是有效的对象
                if isinstance(revised_content, dict) and revised_content:
                    current_content = self.check_and_fix_media_resources(revised_content, paper_info)
                else:
                    logger.warning("跳过媒体资源检查：修改后的内容格式无效")
            except Exception as e:
                logger.error(f"检查和修复媒体资源时出错: {str(e)}")
                # 如果媒体检查失败，保持当前内容不变

            revision_attempt += 1

        # 保存最终内容
        result["final_content"] = current_content
        output_file = self.file_config.get("content_file", "output/paper_content.json")
        save_json_content(current_content, output_file)
        logger.info(f"最终内容已保存到 {output_file}")

        return result

    def _save_stage_result(self, stage, data):
        """
        保存阶段结果到文件

        Args:
            stage: 阶段名称
            data: 要保存的数据

        Returns:
            str: 保存的文件路径
        """
        if stage not in self.stage_files:
            logger.warning(f"未知阶段: {stage}")
            return None

        filename = self.stage_files[stage]

        # 确保输出目录存在
        os.makedirs(os.path.dirname(filename), exist_ok=True)

        # 处理数据中可能包含的Path对象
        processed_data = self._process_data_for_json(data)

        # 保存数据
        save_json_content(processed_data, filename)
        logger.info(f"已保存{stage}阶段结果到: {filename}")

        return filename

    def _process_data_for_json(self, data):
        """
        处理数据以便JSON序列化，主要处理Path对象

        Args:
            data: 要处理的数据

        Returns:
            处理后的数据
        """
        import pathlib

        if data is None:
            return None

        # 处理字典
        if isinstance(data, dict):
            return {k: self._process_data_for_json(v) for k, v in data.items()}

        # 处理列表或元组
        if isinstance(data, (list, tuple)):
            return [self._process_data_for_json(item) for item in data]

        # 处理Path对象
        if isinstance(data, (pathlib.Path, pathlib.PosixPath, pathlib.WindowsPath)):
            return str(data)

        return data

    def _load_stage_result(self, stage):
        """
        从文件加载阶段结果

        Args:
            stage: 阶段名称

        Returns:
            加载的数据，如果加载失败则返回None
        """
        if stage not in self.stage_files:
            logger.warning(f"未知阶段: {stage}")
            return None

        filename = self.stage_files[stage]

        try:
            if filename and os.path.exists(filename):
                with open(filename, encoding="utf-8") as f:
                    file_content = f.read()

                # 检查文件内容是否被截断或格式不正确
                try:
                    import json

                    data = json.loads(file_content)
                except json.JSONDecodeError as je:
                    logger.error(f"JSON解析错误: {je}")
                    logger.warning(f"文件内容可能被截断或格式错误: {filename}")
                    logger.info("尝试修复文件内容...")

                    # 尝试修复JSON格式
                    try:
                        # 如果是常见的被截断格式，尝试修复
                        if file_content.strip().endswith("%"):
                            fixed_content = file_content.replace("%", '""}')
                            data = json.loads(fixed_content)
                            logger.info("成功修复被截断的JSON格式")
                        elif file_content.strip().endswith(","):
                            fixed_content = file_content + ' "dummy": null}'
                            data = json.loads(fixed_content)
                            logger.info("成功修复缺少结束括号的JSON格式")
                        else:
                            logger.error("无法自动修复JSON格式")
                            return None
                    except Exception:
                        logger.error("修复JSON格式失败")
                        return None

                logger.info(f"已加载{stage}阶段数据: {filename}")
                return data

            logger.warning(f"未找到{stage}阶段数据文件: {filename}")
            return None
        except Exception as e:
            logger.error(f"加载{stage}阶段数据失败: {str(e)}")
            return None


def main():
    """主函数，运行完整工作流"""
    # 初始化工作流
    workflow = WorkflowPaper()

    # 从配置文件获取论文URL
    paper_url = workflow.pdf_config.get("url", "")

    # 用户画像示例
    user_profile = {
        "education_level": "研究生",
        "field": "计算机科学",
        "interests": ["人工智能", "机器学习", "自然语言处理"],
        "expertise_level": "中级",
        "content_preferences": {
            "style": "通俗易懂",
            "depth": "中等",
            "format": "视频讲解",
        },
    }

    # 内容主题
    content_topics = ["论文解读", "学术分享", "技术讲解"]

    # 运行工作流
    logger.info(f"开始运行工作流，处理论文: {paper_url}")
    result = workflow.run_workflow(
        paper_url=paper_url,
        user_id="user123",
        user_profile=user_profile,
        content_topics=content_topics,
    )

    # 检查结果
    if "error" in result:
        logger.error(f"工作流执行错误: {result['error']}")
    else:
        output_file = workflow.file_config.get("content_file", "output/paper_content.json")
        logger.info(f"工作流执行成功，结果已保存到 {output_file}")

        # 打印一些关键信息
        print("\n===== 工作流执行完成 =====")
        print(f"论文URL: {paper_url}")

        intention = result.get("intention_analysis", {})
        print(f"用户主要意图: {', '.join(intention.get('primary_intent', []))}")

        content_style = intention.get("content_style", {})
        print(f"内容风格: 语调={content_style.get('tone', '中性')}, 深度={content_style.get('depth', '中等')}")

        # 打印大纲和费曼功能的状态
        print(f"\n大纲生成模块: {'启用' if workflow.enable_outline else '禁用'}")
        outline_result = result.get("outline_result", {})
        if workflow.enable_outline and outline_result and not outline_result.get("error"):
            outline_file = workflow.stage_files.get("outline", "output/outline_result.json")
            print(f"大纲已保存到: {outline_file}")
            if "outline" in outline_result and isinstance(outline_result["outline"], dict):
                # 尝试打印大纲的部分内容
                outline = outline_result["outline"]
                if "key_concepts" in outline:
                    print(f"核心概念: {', '.join(outline['key_concepts'][:5])}")
                if "storyboard_suggestions" in outline:
                    print(f"分镜建议数量: {len(outline['storyboard_suggestions'])}")
        elif workflow.enable_outline and outline_result and outline_result.get("error"):
            print(f"大纲生成错误: {outline_result.get('error')}")

        print(f"\n费曼解释模块: {'启用' if workflow.enable_feynman else '禁用'}")
        feynman_result = result.get("feynman_result", {})
        if workflow.enable_feynman and feynman_result and not feynman_result.get("error"):
            if "explanation_file" in feynman_result:
                print(f"费曼解释已保存到: {feynman_result['explanation_file']}")
            if "full_text_file" in feynman_result:
                print(f"完整对话记录已保存到: {feynman_result['full_text_file']}")
        elif workflow.enable_feynman and feynman_result and feynman_result.get("error"):
            print(f"费曼解释错误: {feynman_result.get('error')}")

        final_content = result.get("final_content", [])
        print(f"\n生成分镜数量: {len(final_content)}")

        # 打印分镜名称列表
        if final_content:
            print("\n分镜概览:")
            for i, scene in enumerate(final_content, 1):
                print(f"{i}. {scene.get('分镜名', '未命名分镜')}")

        print(f"\n详细内容已保存到: {output_file}")


if __name__ == "__main__":
    main()
