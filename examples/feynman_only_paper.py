from dotenv import load_dotenv

load_dotenv()
import logging
import os

import yaml
from camel.agents import ChatAgent
from camel.logger import set_log_level
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.types import ModelPlatformType
from loguru import logger

import tools.pdf_toolkit
from agents.video_agent.tools.info_collector_toolkit import InfoCollectorToolkit
from utils.format import extract_json, save_json_content

set_log_level(level="WARNING")

PAPER_GENERATION_FORMAT_PROMPT = """
```json
[
    {{
        "分镜名": "分镜的子标题描述,比如开篇标题/背景动机/核心概念/方法框架/实验分析/评价讨论"，
        "内容要点": "提取关键词列表,关键词长度不超过10,关键词的个数不超过5，比如机构、提升效果、之前存在的问题、本文提出解决方法、研究方向"，
        "分镜内容": "开篇标题/背景动机/核心概念/方法框架/实验分析/应用方法/评价讨论等的文案内容"
        "素材名": "分镜内容对应输入markdown中材料的图片、表格、视频等媒体素材名称, 比如pdf_output/2411.01747v1_artifacts/page_3_image_0.png"
        "视觉动效建议": "针对用到的素材，描述需要添加什么动效，以达到最佳的效果，能吸引观众注意力，并使讲解重点突出而且易于理解"
    }},
    ...
]
```
"""

INFO_COLLECTOR_PROMPT = """
论文内容如下：{paper}

调用合适的工具，收集论文的相关信息。
工具选择逻辑如下：
1. 如果给出的是pdf文件路径或者URL，则调用process_pdf工具
2. 如果给出的是普通的网页url，则调用extract_web_content工具
3. 如果给出的是搜索关键词，则调用search工具，

将工具返回的结果在返回结果中直接输出，不要做任何修改。
"""

PAPER_GENERATION_PROMPT = f"""根据以上分析，请生成简洁、准确的内容，结构如下：
1. 开篇标题：生成一句震惊体吸引人,知名作者/机构，解决什么热点问题，可量化效果怎样, 比如谷歌提出全新Transformer速度快了100倍等(1句15字以内）
2. 核心概念：要理解研究问题的创新性，最相关的核心概念或者问题是什么？这个概念为什么重要，它的物理含义是什么，辅助例子讲解（2-3句话）
3. 背景动机：研究问题的重要意义，相关工作有哪些，有哪些不足之处，文章的主要发现认知是什么，围绕这些发现文章核心创新点和效果，用简练易懂的话概述，并类比生活中例子解释, 如果有相关论文名称可引用(5-8句)
4. 方法框架：具体提出了什么方法、框架, 按照模块/步骤/环节一步步简述操作和原理,每一模块/步骤/环节介绍物理含义上起的作用(8-10句)
5. 实验分析：简述实验方法，最核心的数据结论是什么(3-5句)
6. 评价讨论：网友围绕文章观点、使用体验等评论, 批判性分析下文章创新点或者实验待改善地方,未来这个方向有哪些值得进一步研究的具体方向,给后来人一些启发（5-8句）
论文内容如下：{{paper}}

把如上内容，使用以下JSON结构格式输出：
### JSON格式要求
{PAPER_GENERATION_FORMAT_PROMPT}
"""


PAPER_REFLECTION_PROMPT = f"""对照之前的分析内容和格式要求，检查生成的内容有没有遗漏或错误，格式是否符合要求。
格式检查要点：
1. 内容完整性：检查是否遗漏了任何关键内容，比如吸引人的问题、核心概念、关键创新点、类比描述、重要性、实验方法和结论等。
2. 格式要求：检查是否符合给定的JSON格式要求,markdown格式输出,如果有特殊字符的一定加上转译字符，比如双引号、单引号,是否有Invalid escape
3. 解释检查：是否包含背景动机、方法框架、实验分析三部分
4. 字数检查: 检查字数是否符合要求，比如开篇标题1句15字以内
5. 图/表格的格式: pdf_output/2411.01747v1_artifacts/page_3_image_0.png,name字段的名称必须都是markdown文件里出现的图片名或者表格名
6. 文字表达：检查文字表达是否连贯、通顺。

内容检查要点：
1. 开篇标题是否足够简洁震惊
2. 背景动机是否包含相关工作及不足的介绍
3. 背景动机是否包含了文章的核心发现和认知
4. 背景动机围绕发现的问题，提出了核心创新点和效果是否有
5. 方法框架里是否缺少模块，缺少步骤，缺少环节没介绍
6. 方法框架里每个模块/步骤/环节作用，操作，物理含义是否详细介绍清楚了
7. 实验分析里实验方法是否包含了
8. 实验分析里实验核心数据结论是否包含
9. 工作亮点与未来研究方向中是否包含从资深领域专家角度对当前工作的创新点总结，以及批判性分析下待改善地方，或者这个方向具体值得深入研究的点或者方向,能给后来人启发的，要明确不能太泛
10. 文案内到数字是否正确、具体，比如实验提升效果，是否具体到百分比

请给出详细的分析，并修改内容后，使用以下JSON结构格式输出：
### JSON格式要求
{PAPER_GENERATION_FORMAT_PROMPT}
"""


# Define a custom workflow class
class FeynmanWorkflow:
    def __init__(self, config_path="config/config.yaml"):
        # Load configuration
        self.load_config(config_path)

        # Initialize model and toolkits
        self.model = self._create_model()
        logging.warning("model initialized")

        self.toolkits = self._initialize_toolkits()
        logging.warning("toolkits initialized")
        # Initialize agents
        self.agents = self.initialize_agents()
        logging.warning("agents initialized")

    def load_config(self, config_path):
        """Load configuration from YAML file"""
        try:
            with open(config_path) as file:
                config = yaml.safe_load(file)
            # Set configuration properties
            self.model_config = config.get("model", {})
            self.file_config = config.get("files", {})
            self.agent_config = config.get("agents", {})
        except Exception as e:
            logging.error(f"Error loading configuration: {str(e)}")

    def _create_model(self):
        """Create model instance based on configuration"""
        # 从配置中获取API设置
        api_config = self.model_config.get("api", {})
        
        return ModelFactory.create(
            model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
            model_type=self.model_config.get("type", "openai/gpt-4o-mini"),
            api_key=api_config.get("openai_compatibility_api_key"),
            url=api_config.get("openai_compatibility_api_base_url"),
        )

    def _initialize_toolkits(self):
        """Initialize various toolkits"""
        return {"pdf": tools.pdf_toolkit.PDFToolkit().get_tools()}

    def initialize_agents(self) -> dict:
        """Initialize all enabled agents with their respective system prompts and tools"""
        agents = {}
        agents["info_collector"] = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="Info collector",
                content="You collect information about academic papers.",
            ),
            model=self.model,
            tools=[*InfoCollectorToolkit().get_tools()],
        )
        agents["paper_generator"] = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="Paper content generator",
                content="You generate structured content about academic papers in a specific JSON format.",
            ),
            model=self.model,
        )

        agents["paper_reflector"] = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="Paper content reflector",
                content="You review and analyze generated content about academic papers, checking for completeness and adherence to format requirements.",
            ),
            model=self.model,
        )
        return agents

    def process_message(self, agent_name: str, message: str, format_class=None):
        """Process message and return response"""
        agent = self.agents[agent_name]
        # Send message and get response
        response = agent.step(message)
        logger.debug(response)
        if agent_name == "info_collector":
            if "tool_calls" in response.info:
                return response.info["tool_calls"][0].result
        return response.msgs[0].content

    def process_paper(self, paper_url):
        """Process a paper through the four-step workflow: analysis, generation, reflection, refinement"""
        result = {}
        # Step 1: Info collector
        print("Collecting info...")
        info_collector_prompt = INFO_COLLECTOR_PROMPT.format(paper=paper_url)
        info_collector_result = self.process_message("info_collector", info_collector_prompt)
        paper_content = open(info_collector_result.get("markdown_file", ""), encoding="utf-8").read()
        # Step 2: Content Generation
        print("Generating content...")
        generation_prompt = PAPER_GENERATION_PROMPT.format(paper=paper_content)
        generation_result = self.process_message("paper_generator", generation_prompt)
        # Step 2: Content Reflection
        print("Reflecting on content...")
        reflection_prompt = f"""Here is the generated content:
            {generation_result}
            {PAPER_REFLECTION_PROMPT}"""
        result = self.process_message("paper_reflector", reflection_prompt)
        return result


def main():
    """Main function to run the workflow"""
    # Initialize workflow
    workflow = FeynmanWorkflow()
    # paper_content = open("pdf_output/2503.10628v1.md", encoding="utf-8").read()
    paper_url = "要讲解的论文pdf url是 https://arxiv.org/pdf/2503.10628"
    results = workflow.process_paper(paper_url)
    extracted_json = extract_json(results)
    save_json_content(extracted_json, "output/2503.10628v1.json")


if __name__ == "__main__":
    main()
