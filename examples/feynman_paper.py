from dotenv import load_dotenv

load_dotenv()
import logging
import os

import yaml
from camel.agents import ChatAgent
from camel.logger import set_log_level
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.types import ModelPlatformType

import prompts.system_prompts
import tools.pdf_toolkit

# Import models and helper functions from utils.format module
from utils.format import (
    ContentResponseFormat,
    OutlineResponseFormat,
    StoryboardResponseFormat,
    extract_json,
    format_prompt_for_json,
    get_format_description,
)

set_log_level(level="WARNING")


# Define a custom workflow class
class FeynmanWorkflow:
    def __init__(self, config_path="config/config.yaml"):
        # Load configuration
        self.load_config(config_path)

        # Initialize model and toolkits
        self.model = self._create_model()
        logging.warning("model initialized")

        self.toolkits = self._initialize_toolkits()
        logging.warning("toolkits initialized")
        # Initialize agents
        self.agents = self.initialize_agents()
        logging.warning("agents initialized")

    def load_config(self, config_path):
        """Load configuration from YAML file"""
        try:
            with open(config_path) as file:
                config = yaml.safe_load(file)
            # Set configuration properties
            self.model_config = config.get("model", {})
            self.file_config = config.get("files", {})
            self.agent_config = config.get("agents", {})
        except Exception as e:
            logging.error(f"Error loading configuration: {str(e)}")

    def _create_model(self):
        """Create model instance based on configuration"""
        # 从配置中获取API设置
        api_config = self.model_config.get("api", {})
        
        return ModelFactory.create(
            model_platform=ModelPlatformType[self.model_config.get("platform", "OPENAI_COMPATIBLE_MODEL")],
            model_type=self.model_config.get("type", "google/gemini-2.0-flash-lite-preview-02-05:free"),
            api_key=api_config.get("openai_compatibility_api_key"),
            url=api_config.get("openai_compatibility_api_base_url"),
        )

    def _initialize_toolkits(self):
        """Initialize various toolkits"""
        return {}

    def initialize_agents(self) -> dict:
        """Initialize all enabled agents with their respective system prompts and tools"""
        agents = {}

        # Initialize only enabled agents
        if self.agent_config.get("source", False):
            agents["source"] = ChatAgent(
                system_message=BaseMessage.make_assistant_message(
                    role_name="PDF analyzing agent",
                    content="You can analyze PDF documents and extract content from them",
                ),
                model=self.model,
                tools=[*tools.pdf_toolkit.PDFToolkit().get_tools()],
            )

        if self.agent_config.get("outline_generator", True):
            agents["outline_generator"] = ChatAgent(
                system_message=prompts.system_prompts.OUTLINE_GENERATOR_PROMPT_POSTFIX,
                model=self.model,
            )

        if self.agent_config.get("outline_reflector", True):
            agents["outline_reflector"] = ChatAgent(
                system_message=prompts.system_prompts.OUTLINE_REFLECTOR_PROMPT_POSTFIX,
                model=self.model,
            )

        if self.agent_config.get("storyboard_generator", True):
            agents["storyboard_generator"] = ChatAgent(
                system_message=prompts.system_prompts.STORYBOARD_GENERATOR_PROMPT_POSTFIX,
                model=self.model,
            )

        if self.agent_config.get("storyboard_reflector", True):
            agents["storyboard_reflector"] = ChatAgent(
                system_message=prompts.system_prompts.STORYBOARD_REFLECTOR_PROMPT_POSTFIX,
                model=self.model,
            )

        if self.agent_config.get("content_generator", True):
            agents["content_generator"] = ChatAgent(
                system_message=prompts.system_prompts.CONTENT_GENERATOR_PROMPT_POSTFIX,
                model=self.model,
            )

        return agents

    def process_message(self, agent_name: str, message: str, format_class=None):
        """Process message and return response"""
        # Check if agent exists
        if agent_name not in self.agents:
            logging.warning(f"Agent {agent_name} not found")
            return None

        agent = self.agents[agent_name]

        # Send message and get response
        response = agent.step(message)

        # Ensure response object exists
        if not response or not hasattr(response, "msgs") or not response.msgs:
            logging.error("Invalid response object from agent")
            return None

        response_content = response.msgs[0].content

        # If no format class is specified, return raw content
        if format_class is None:
            return response_content

        # Try to parse JSON
        json_data = extract_json(response_content)
        if not json_data:
            return self._request_json_fix(agent, response_content, format_class)

        # Try to validate with Pydantic
        try:
            result = format_class.model_validate(json_data)
            return result
        except Exception as e:
            logging.error(f"Validation error: {str(e)}")
            return self._request_json_fix(agent, response_content, format_class)

    def _request_json_fix(self, agent, response_content, format_class):
        """Request model to fix JSON format"""
        # Build fix prompt
        fix_prompt = (
            f"Your previous response could not be parsed as valid JSON. Please provide a valid JSON response that conforms to the following format:\n"
            f"{get_format_description(format_class)}\n\n"
            f"Your previous response was:\n{response_content[:500]}...\n\n"
            f"Please return only the JSON format without any other explanation."
        )

        # Try up to 3 fixes
        for attempt in range(3):
            response = agent.step(fix_prompt)
            fixed_content = response.msgs[0].content

            # Try to parse fixed response
            json_data = extract_json(fixed_content)
            if json_data:
                try:
                    result = format_class.model_validate(json_data)
                    return result
                except Exception:
                    pass

        # If all fix attempts fail, return None
        logging.warning("All JSON fix attempts failed")
        return None

    def generate_topic_introduction(self, topic: str, reference_contents=None):
        """Generate a topic introduction using multiple agents"""
        results = {}
        # 1. Collect resources if source agent is enabled
        if self.agent_config.get("source", False):
            print("Collecting resources...")
            source_prompt = (
                "Parse the content from https://arxiv.org/pdf/2411.01747 and save it to the pdf_output directory."
            )
            source_materials = self.process_message("source", source_prompt)
            reference_contents = source_materials
            results["source_materials"] = source_materials

        # 2. Generate outline
        if self.agent_config.get("outline_generator", True):
            print("Generating outline...")
            # Ensure reference contents exist
            if os.path.exists("pdf_output/2503.07536v2.md"):
                reference_contents = open("pdf_output/2503.07536v2.md", encoding="utf-8").read()
                logging.info(f"Reference material loaded: {len(reference_contents)} characters")
            else:
                reference_contents = ""

            base_outline_prompt = (
                f"Create a detailed content outline for the topic '{topic}'.\n\n"
                f"Reference material: {reference_contents}\n\n"
                f"Generate a clear structure with main chapters and subchapters, covering all key aspects of the topic."
            )

            outline_prompt = format_prompt_for_json(base_outline_prompt, OutlineResponseFormat)
            outline_result = self.process_message("outline_generator", outline_prompt, OutlineResponseFormat)

            # Save result
            if outline_result:
                with open(self.file_config["outline_file"], "w", encoding="utf-8") as f:
                    f.write(outline_result.model_dump_json(indent=4))
                results["outline"] = outline_result

        # 3. Reflect on outline
        if self.agent_config.get("outline_reflector", True) and self.agent_config.get("outline_generator", True):
            print("Evaluating outline...")

            try:
                with open(self.file_config["outline_file"], encoding="utf-8") as f:
                    outline_content = f.read()

                outline_reflection_prompt = (
                    f"Evaluate the following content outline for the topic '{topic}':\n\n"
                    f"{outline_content}\n\n"
                    f"Check if the outline is complete, well-structured, and covers all key concepts. "
                    f"Provide improvement suggestions if needed."
                )
                outline_reflection = self.process_message("outline_reflector", outline_reflection_prompt)
                results["outline_reflection"] = outline_reflection
            except Exception as e:
                logging.error(f"Error reflecting on outline: {str(e)}")
                results["outline_reflection_error"] = str(e)

        # 4. Generate storyboard
        if self.agent_config.get("storyboard_generator", True) and self.agent_config.get("outline_generator", True):
            print("Generating storyboard...")

            try:
                with open(self.file_config["outline_file"], encoding="utf-8") as f:
                    outline_content = f.read()

                base_storyboard_prompt = (
                    f"Create a detailed storyboard for the topic '{topic}' based on the following outline:\n\n"
                    f"{outline_content}\n\n"
                    f"Reference material: {reference_contents}\n\n"
                    f"For each section, create specific content including narration text and visual effect suggestions."
                )

                storyboard_prompt = format_prompt_for_json(base_storyboard_prompt, StoryboardResponseFormat)
                storyboard_result = self.process_message(
                    "storyboard_generator",
                    storyboard_prompt,
                    StoryboardResponseFormat,
                )

                # Save result
                if storyboard_result:
                    with open(self.file_config["storyboard_file"], "w", encoding="utf-8") as f:
                        f.write(storyboard_result.model_dump_json(indent=4))
                    results["storyboard"] = storyboard_result
            except Exception as e:
                logging.error(f"Error generating storyboard: {str(e)}")
                results["storyboard_error"] = str(e)

        # 5. Reflect on storyboard
        if self.agent_config.get("storyboard_reflector", True) and self.agent_config.get(
            "storyboard_generator",
            True,
        ):
            print("Evaluating storyboard...")

            try:
                with open(self.file_config["storyboard_file"], encoding="utf-8") as f:
                    storyboard_content = f.read()

                storyboard_reflection_prompt = (
                    f"Evaluate the following storyboard for the topic '{topic}':\n\n"
                    f"{storyboard_content}\n\n"
                    f"Check if the storyboard is clear, coherent, and engaging. Provide improvement suggestions if needed."
                )
                storyboard_reflection = self.process_message("storyboard_reflector", storyboard_reflection_prompt)
                results["storyboard_reflection"] = storyboard_reflection
            except Exception as e:
                logging.error(f"Error reflecting on storyboard: {str(e)}")
                results["storyboard_reflection_error"] = str(e)

        # 6. Generate content
        if self.agent_config.get("content_generator", True):
            print("Generating final content...")

            try:
                with open(self.file_config["storyboard_file"], encoding="utf-8") as f:
                    storyboard_content = f.read()

                base_generation_prompt = (
                    f"Generate content for the topic '{topic}' based on the following storyboard:\n\n"
                    f"{storyboard_content}\n\n"
                    f"Create professional yet accessible content following the Feynman learning method principles."
                )

                generation_prompt = format_prompt_for_json(base_generation_prompt, ContentResponseFormat)
                content_result = self.process_message("content_generator", generation_prompt, ContentResponseFormat)

                # Save result
                if content_result:
                    with open(self.file_config["content_file"], "w", encoding="utf-8") as f:
                        f.write(content_result.model_dump_json(indent=4))
                    results["final_content"] = content_result
            except Exception as e:
                logging.error(f"Error generating content: {str(e)}")
                results["content_error"] = str(e)

        logging.info("Topic introduction generation completed")

        return results


# Example usage
if __name__ == "__main__":
    # Set topic
    query = ""
    print(f"Generating topic introduction for: {query}")

    # Create workflow instance
    workflow = FeynmanWorkflow()
    print("workflow initialized")
    # Generate topic introduction
    result = workflow.generate_topic_introduction(query)

    print("Generation completed")
