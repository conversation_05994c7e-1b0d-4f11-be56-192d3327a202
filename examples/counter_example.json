{"schema_version": "2.0-mvp", "metadata": {"title": "AnimateCounterDemo", "author": "DSL v2 测试者", "background_color": "DARK_BLUE"}, "objects": [], "actions": [{"type": "animate_counter", "params": {"target_value": 100, "label": "基础示例", "effect": "zoom", "narration": "基础示例，目标值为100", "counter_type": "counter"}}, {"type": "wait", "duration": 1}, {"type": "animate_counter", "params": {"target_value": 75.5, "label": "销售额", "start_value": 25.0, "duration": 3.0, "effect": "flash", "unit": "万元", "narration": "销售额从25万增长到75.5万", "counter_type": "counter"}}, {"type": "wait", "duration": 1}, {"type": "animate_counter", "params": {"target_value": 98.6, "label": "完成率", "start_value": 0, "duration": 2.5, "effect": "zoom", "unit": "%", "narration": "完成率从0%增长到98.6%", "counter_type": "counter"}}, {"type": "wait", "duration": 1}, {"type": "animate_counter", "params": {"target_value": 1000, "label": "用户数", "effect": "none", "unit": "K+", "narration": "用户数从0增长到1000K+", "counter_type": "curve"}}, {"type": "wait", "duration": 2}]}