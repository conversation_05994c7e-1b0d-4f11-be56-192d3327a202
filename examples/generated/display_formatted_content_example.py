# Generated by Feynman DSL v2 Code Generator
# Author: 用户
# -*- coding: utf-8 -*-

import os
import sys

root_dir = os.getcwd()
sys.path.append(root_dir)

from manim import *

from dsl.v2.core.scene import FeynmanScene


class aa(FeynmanScene):
    def construct(self):
        # Action 1: DisplayImageNode
        self.display_image(
            content="mcp-github-setup.svg",
            narration="这是一个图片",
            annotation="# 这是svg格式的图片",
            target_region_id="full_screen",
        )
        # --- Final wait to hold the last frame ---
        self.wait(1)
