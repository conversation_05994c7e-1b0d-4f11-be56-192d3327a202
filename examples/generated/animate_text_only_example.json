{"schema_version": "2.0-mvp", "metadata": {"title": "animate_text_only 示例", "author": "自动生成", "background_color": "BLACK"}, "actions": [{"type": "animate_text_only", "params": {"title": "✨ 夏日出游 Checklist", "items": [{"icon": "☀️", "text": "智能防晒霜", "tags": "#物理防晒 #SPF50+", "color": "#fbbf24"}, {"icon": "😎", "text": "偏光墨镜", "tags": "#UV400 #驾驶友好", "color": "#60a5fa"}, {"icon": "💨", "text": "便携风扇", "tags": "#静音 #长续航", "color": "#a78bfa"}, {"icon": "🧊", "text": "降温冰袖", "tags": "#凉感面料 #防晒", "color": "#34d399"}], "narration": "这是一个完美的夏日出游清单，包含了所有必需的防暑用品。"}}, {"type": "animate_text_only", "params": {"title": "🎯 学习计划表", "items": [{"icon": "📚", "text": "阅读专业书籍", "tags": "#每日1小时 #系统学习", "color": "#f59e0b"}, {"icon": "💻", "text": "编程练习", "tags": "#算法题 #项目实战", "color": "#3b82f6"}, {"icon": "🏃", "text": "体育锻炼", "tags": "#跑步 #健身", "color": "#10b981"}], "narration": "制定一个合理的学习计划，平衡学习和健康。"}}, {"type": "animate_text_only", "params": {"title": "🔧 核心能力与关键技术剖析", "items": [{"icon": "💎", "text": "高质量、大规模数据", "tags": "#数据质量 #规模化", "color": "#8b5cf6"}, {"icon": "🔍", "text": "多阶段、多视角策展", "tags": "#策展 #多维度", "color": "#06b6d4"}, {"icon": "🎯", "text": "精准视频标注系统", "tags": "#标注 #精确性", "color": "#f59e0b"}, {"icon": "⚡", "text": "动态与静态特征结合", "tags": "#特征融合 #优化", "color": "#ef4444"}], "narration": "这些核心技术构成了 Seedance 1.0 的技术优势，确保了模型的高性能表现。"}}]}