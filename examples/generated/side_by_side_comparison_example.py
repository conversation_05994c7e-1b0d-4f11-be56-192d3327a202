# Generated by <PERSON>ynman DSL v2 Code Generator
# Author: AI助手
# -*- coding: utf-8 -*-

import os
import sys

root_dir = os.getcwd()
sys.path.append(root_dir)

from manim import *

from dsl.v2.animation_functions import *  # IMPORTANT: Ensures all animations are registered
from dsl.v2.core.scene import FeynmanScene


class SideBySideComparisonDemo(FeynmanScene):
    config.background_color = "#FFFFFF"

    def construct(self):
        self.add_background()

        # Action 1: side_by_side_comparison
        animate_side_by_side_comparison(
            scene=self,
            left_content="# Python 优点\n\n- 简洁易读\n- 大量第三方库\n- 跨平台兼容\n- 适合快速开发",
            right_content="# Java 优点\n\n- 强类型系统\n- JVM 跨平台\n- 企业级应用成熟\n- 高性能",
            left_title="Python",
            right_title="Java",
            left_type="markdown",
            right_type="markdown",
            transition="fadeIn",
            narration="Python 和 Java 的优点对比",
        )

        # Action 2: wait

        # Action 3: side_by_side_comparison
        animate_side_by_side_comparison(
            scene=self,
            left_content="def hello_world():\n    print('Hello, World!')\n\nhello_world()",
            right_content='public class HelloWorld {\n    public static void main(String[] args) {\n        System.out.println("Hello, World!");\n    }\n}',
            left_title="Python 代码",
            right_title="Java 代码",
            left_type="code",
            right_type="code",
            transition="slideUp",
            narration="Python 和 Java 的代码对比",
        )

        # Action 4: wait

        # Action 5: side_by_side_comparison
        animate_side_by_side_comparison(
            scene=self,
            left_content='{\n  "name": "Python",\n  "year": 1991,\n  "creator": "Guido van Rossum",\n  "paradigms": ["面向对象", "命令式", "函数式"]\n}',
            right_content='{\n  "name": "Java",\n  "year": 1995,\n  "creator": "James Gosling",\n  "paradigms": ["面向对象", "命令式"]\n}',
            left_title="Python 信息",
            right_title="Java 信息",
            left_type="json",
            right_type="json",
            transition="fadeIn",
            narration="Python 和 Java 的信息对比",
        )
        # --- Final wait to hold the last frame ---
        self.wait(1)
