# Generated by Feynman DSL v2 Code Generator
# Author: DSL v2 测试者
# -*- coding: utf-8 -*-

import os
import sys

root_dir = os.getcwd()
sys.path.append(root_dir)

from manim import *

from dsl.v2.animation_functions import *  # IMPORTANT: Ensures all animations are registered
from dsl.v2.core.scene import FeynmanScene


class AnimateCounterDemo(FeynmanScene):
    config.background_color = "DARK_BLUE"

    def construct(self):
        # Action 1: animate_counter
        animate_counter(
            scene=self,
            target_value=100,
            label="基础示例",
            effect="zoom",
            narration="基础示例，目标值为100",
            counter_type="counter",
        )

        # Action 2: wait

        # Action 3: animate_counter
        animate_counter(
            scene=self,
            target_value=75.5,
            label="销售额",
            start_value=25.0,
            duration=3.0,
            effect="flash",
            unit="万元",
            narration="销售额从25万增长到75.5万",
            counter_type="counter",
        )

        # Action 4: wait

        # Action 5: animate_counter
        animate_counter(
            scene=self,
            target_value=98.6,
            label="完成率",
            start_value=0,
            duration=2.5,
            effect="zoom",
            unit="%",
            narration="完成率从0%增长到98.6%",
            counter_type="counter",
        )

        # Action 6: wait

        # Action 7: animate_counter
        animate_counter(
            scene=self,
            target_value=1000,
            label="用户数",
            effect="none",
            unit="K+",
            narration="用户数从0增长到1000K+",
            counter_type="curve",
        )

        # Action 8: wait
        # --- Final wait to hold the last frame ---
        self.wait(1)
