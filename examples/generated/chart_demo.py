# Generated by Feynman DSL v2 Code Generator
# Author: 开发者
# -*- coding: utf-8 -*-

import os
import sys

root_dir = os.getcwd()
sys.path.append(root_dir)

from manim import *

from dsl.v2.core.scene import FeynmanScene


class Scene_(FeynmanScene):
    def construct(self):
        # Action 1: ActionNode
        self.animate_chart(
            chart_type="bar",
            data={"核心业务": 65, "创新项目": 42, "研发投入": 78, "市场营销": 53},
            title="2023年度投入分析",
            animation_style="grow",
            narration="2023年度投入分析，核心业务投入65%，创新项目投入42%，研发投入78%，市场营销投入53%",
            y_label="投入比例",
        )

        # Action 2: ActionNode
        # self.wait()

        # Action 3: ActionNode
        self.animate_chart(
            chart_type="line",
            data={"2019": 25, "2020": 42, "2021": 38, "2022": 51, "2023": 68},
            title="五年销售趋势",
            animation_style="grow",
            narration="五年销售趋势，2019年25%，2020年42%，2021年38%，2022年51%，2023年68%",
            y_label="销售比例",
        )

        # Action 4: ActionNode
        # self.wait()

        # Action 5: ActionNode
        self.animate_chart(
            chart_type="radar",
            data={"用户体验": 85, "性能": 75, "安全性": 92, "可扩展性": 65, "成本效益": 70},
            title="产品评估雷达图",
            animation_style="grow",
            narration="产品评估雷达图，用户体验85%，性能75%，安全性92%，可扩展性65%，成本效益70%",
        )

        # Action 6: ActionNode
        # self.wait()
        # --- Final wait to hold the last frame ---
        self.wait(1)
