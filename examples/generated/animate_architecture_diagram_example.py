# Generated by <PERSON>ynman DSL v2 Code Generator
# Author: 开发者
# -*- coding: utf-8 -*-

import os
import sys

root_dir = os.getcwd()
sys.path.append(root_dir)

from manim import *

from dsl.v2.animation_functions import *  # IMPORTANT: Ensures all animations are registered
from dsl.v2.core.scene import FeynmanScene


class architecture_diagram(FeynmanScene):
    def construct(self):
        # Action 1: animate_architecture_diagram
        animate_architecture_diagram(
            scene=self,
            content_description="一个云原生微服务架构，包含前端、API网关、服务发现、四个微服务和一个数据库。数据从前端通过API网关流向微服务，最终存储到数据库中。",
            narration="一个云原生微服务架构，包含前端、API网关、服务发现、四个微服务和一个数据库。数据从前端通过API网关流向微服务，最终存储到数据库中。",
        )
        # --- Final wait to hold the last frame ---
        self.wait(1)
