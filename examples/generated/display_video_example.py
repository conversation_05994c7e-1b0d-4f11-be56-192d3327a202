# Generated by <PERSON>ynman DSL v2 Code Generator
# Author: 开发者
# -*- coding: utf-8 -*-

import os
import sys

root_dir = os.getcwd()
sys.path.append(root_dir)

from manim import *

from dsl.v2.animation_functions import *  # IMPORTANT: Ensures all animations are registered
from dsl.v2.core.scene import FeynmanScene


class Scene_(FeynmanScene):
    def construct(self):
        # Action 1: animate_video
        animate_video(
            scene=self,
            video_path="assets/demo.mp4",
            overlay_text="步骤 1: xxx\n步骤 2: yyy\n作者: AI助手",
            overlay_animation_delay=1.0,
            narration="这个例子展示了如何显示视频",
        )
        # --- Final wait to hold the last frame ---
        self.wait(1)
