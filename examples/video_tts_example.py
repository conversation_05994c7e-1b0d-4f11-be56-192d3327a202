#!/usr/bin/env python3
"""
视频TTS处理示例
演示如何使用video_tts_processor添加TTS讲解和音效
"""

import asyncio
import sys
from pathlib import Path

# 添加上级目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from utils.video_tts_processor import VideoTTSProcessor


async def main():
    """演示视频TTS处理"""
    
    # 示例文件路径
    video_path = "output/concept_快速排序/快速排序_animation.mp4"  # 假设的视频文件
    log_file = "output/concept_快速排序/animation_timestamp_20250803_145734.log"
    md_file = "output/concept_快速排序/example_explain.md"  # 假设的markdown文件
    output_path = "output/concept_快速排序/quicksort_with_tts_and_effects.mp4"
    sound_effect_path = "assets/slide.mp3"
    
    # 检查必要文件是否存在
    required_files = [log_file, sound_effect_path]
    missing_files = [f for f in required_files if not Path(f).exists()]
    
    if missing_files:
        print(f"缺少必要文件：")
        for file in missing_files:
            print(f"  - {file}")
        return
    
    # 创建处理器
    processor = VideoTTSProcessor(voice="zh-CN-YunxiNeural")
    
    # 先演示解析动画时间戳
    print("🔍 解析动画时间戳...")
    animation_times = processor.parse_animation_timestamps(log_file)
    print(f"找到 {len(animation_times)} 个动画时间戳：")
    for i, time in enumerate(animation_times[:10]):  # 显示前10个
        print(f"  {i+1}. {time:.2f}s")
    if len(animation_times) > 10:
        print(f"  ... 还有 {len(animation_times) - 10} 个")
    
    # 如果有视频文件，则进行完整处理
    if Path(video_path).exists():
        print(f"\n🎬 开始处理视频：{video_path}")
        await processor.process_video(
            video_path=video_path,
            log_file=log_file,
            md_file=md_file,
            output_path=output_path,
            sound_effect_path=sound_effect_path
        )
        print(f"✅ 处理完成！输出文件：{output_path}")
    else:
        print(f"\n⚠️  视频文件不存在：{video_path}")
        print("只演示时间戳解析功能")


if __name__ == "__main__":
    asyncio.run(main()) 