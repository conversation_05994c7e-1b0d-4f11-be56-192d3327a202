#!/usr/bin/env python3
"""
Example script demonstrating how to use the animation agent to process a storyboard
and generate animations using either Excalidraw or Manim based on content analysis.
"""

import argparse
import os

from dotenv import load_dotenv
from loguru import logger

load_dotenv()

# Add parent directory to path to allow importing from the project
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.animation_agent import AnimationAgent
from agents.manim_dsl_agent import ManimCodeToolkit
from tools.excalidraw_toolkit import ExcalidrawToolkit


def process_storyboard(storyboard_path, start_index=0, end_index=None, use_manim=True, use_excalidraw=True):
    """
    Process a storyboard JSON file and generate animations.

    Args:
        storyboard_path (str): Path to the storyboard JSON file
        start_index (int): Index of the first frame to process (0-based)
        end_index (int): Index of the last frame to process (0-based)
        use_manim (bool): Whether to use the Manim toolkit
        use_excalidraw (bool): Whether to use the Excalidraw toolkit
    """
    # Initialize the agent
    logger.info("Initializing animation agent...")
    agent = AnimationAgent()

    # Initialize toolkits if needed
    manim_toolkit = ManimCodeToolkit() if use_manim else None
    excalidraw_toolkit = ExcalidrawToolkit() if use_excalidraw else None

    # Check if the storyboard file exists
    if not os.path.exists(storyboard_path):
        logger.error(f"Storyboard file not found: {storyboard_path}")
        return

    logger.info(f"Processing storyboard: {storyboard_path}")
    logger.info(f"Using Manim: {use_manim}, Using Excalidraw: {use_excalidraw}")
    logger.info(f"Processing frames from {start_index} to {end_index if end_index is not None else 'end'}")

    # Process the storyboard
    output_files = agent.process_storyboard(
        storyboard_json_path=storyboard_path,
        manim_toolkit=manim_toolkit,
        excalidraw_toolkit=excalidraw_toolkit,
        start_index=start_index,
        end_index=end_index,
    )

    # Report results
    if output_files:
        logger.info(f"Successfully generated {len(output_files)} animation files:")
        for i, file in enumerate(output_files):
            logger.info(f"{i+1}. {file}")
    else:
        logger.warning("No animation files were generated.")


def main():
    """Main function to parse arguments and run the storyboard processing."""
    parser = argparse.ArgumentParser(description="Process a storyboard and generate animations.")
    parser.add_argument("storyboard", help="Path to the storyboard JSON file")
    parser.add_argument("--start", type=int, default=0, help="Index of the first frame to process (0-based)")
    parser.add_argument("--end", type=int, default=None, help="Index of the last frame to process (0-based)")
    parser.add_argument("--no-manim", action="store_true", help="Do not use the Manim toolkit")
    parser.add_argument("--no-excalidraw", action="store_true", help="Do not use the Excalidraw toolkit")
    args = parser.parse_args()

    process_storyboard(
        storyboard_path=args.storyboard,
        start_index=args.start,
        end_index=args.end,
        use_manim=not args.no_manim,
        use_excalidraw=not args.no_excalidraw,
    )


if __name__ == "__main__":
    main()
