#!/usr/bin/env python3
"""
重构后的分镜处理脚本 - 集成性能监控和模块化架构
"""
import argparse
import os
import sys
from pathlib import Path

# 添加项目根路径
project_root = Path(__file__).resolve().parent
sys.path.append(str(project_root))

from loguru import logger

from process_storyboard.config import StoryboardConfig
from process_storyboard.main_processor import StoryboardProcessor


def setup_logger():
    """配置日志系统"""
    logger.remove()  # 移除默认处理器

    # 控制台处理器
    logger.add(
        sys.stderr,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO",
    )

    # 文件处理器
    os.makedirs("logs", exist_ok=True)
    logger.add(
        "logs/storyboard_processing.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level="DEBUG",
        rotation="500 MB",
    )


def create_config_from_args(args) -> StoryboardConfig:
    """从命令行参数创建配置对象"""

    # 加载现有配置文件（如果存在）
    config_path = Path(project_root) / "config" / "config.yaml"
    background_music = {}
    transition_config = {}

    if config_path.exists():
        try:
            import yaml

            with open(config_path, encoding="utf-8") as f:
                yaml_config = yaml.safe_load(f)
                background_music = yaml_config.get("background_music", {})
                transition_config = yaml_config.get("transition", {})
                logger.info(f"加载配置文件: {config_path}")
        except Exception as e:
            logger.warning(f"无法加载配置文件 {config_path}: {e}")

    # 创建配置对象
    config = StoryboardConfig(
        storyboard_file=args.storyboard_file,
        output_dir=args.output_dir,
        dsl_schema_file=args.dsl_schema,
        project_name=args.project_name,
        max_workers=args.max_workers,
        quality=args.quality,
        stages=args.stages.split(",") if isinstance(args.stages, str) else args.stages,
        max_retries=2,  # 默认重试次数
        transition_enabled=transition_config.get("enable", False),
        transition_run_time=transition_config.get("run_time", 1.0),
        transition_type=transition_config.get("type", "wipe_left"),
        state_dir=transition_config.get("state_dir", "output/scene_states"),
        background_music=background_music,
        use_uv=None,  # 自动检测
    )

    return config


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="重构后的分镜处理器 - 支持性能监控和并行转场生成",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python process_storyboard_refactored.py storyboard.json
  python process_storyboard_refactored.py storyboard.json --max-workers 6 --quality h
  python process_storyboard_refactored.py storyboard.json --stages dsl,code,render --start-idx 0 --end-idx 5
        """,
    )

    # 基础参数
    parser.add_argument(
        "storyboard_file", nargs="?", default="output/storyboard_mcp_nasdq.json", help="分镜脚本JSON文件路径"
    )
    parser.add_argument("--output-dir", "-o", default="output/videos", help="输出文件存储目录 (默认: output/videos)")
    parser.add_argument(
        "--dsl-schema",
        "-s",
        default="docs/animation_functions.md",
        help="DSL模式文件路径 (默认: docs/animation_functions.md)",
    )
    parser.add_argument("--project-name", type=str, default=None, help="项目名称，用于最终视频文件名")

    # 处理参数
    parser.add_argument("--max-workers", "-w", type=int, default=4, help="最大并发工作线程数 (默认: 4)")
    parser.add_argument(
        "--quality",
        "-q",
        type=str,
        default="l",
        choices=["l", "m", "h", "k"],
        help="Manim渲染质量 (l=480p, m=720p, h=1080p, k=2160p, 默认: l)",
    )

    # 处理范围
    parser.add_argument("--start-idx", "-i", type=int, default=0, help="起始处理索引 (0-based, 默认: 0)")
    parser.add_argument("--end-idx", "-e", type=int, default=None, help="结束处理索引 (0-based, exclusive, 默认: 全部)")

    # 处理阶段
    parser.add_argument(
        "--stages",
        type=str,
        default="dsl,code,render,subtitles,concat",
        help="要执行的处理阶段，逗号分隔 (默认: dsl,code,render,subtitles,concat)",
    )

    args = parser.parse_args()

    # 设置日志
    setup_logger()

    # 打印参数信息
    logger.info("=" * 60)
    logger.info("🚀 启动重构后的分镜处理器")
    logger.info("=" * 60)

    for arg_name, arg_value in vars(args).items():
        logger.info(f"{arg_name:<20} = {arg_value}")

    logger.info("=" * 60)

    try:
        # 创建配置
        config = create_config_from_args(args)

        # 验证文件存在性
        if not Path(config.storyboard_file).exists():
            logger.error(f"分镜脚本文件不存在: {config.storyboard_file}")
            return 1

        if not Path(config.dsl_schema_file).exists():
            logger.error(f"DSL模式文件不存在: {config.dsl_schema_file}")
            return 1

        # 创建处理器并执行
        processor = StoryboardProcessor(config)

        try:
            # 执行处理
            result = processor.process_all(start_idx=args.start_idx, end_idx=args.end_idx)

            # 输出最终结果
            logger.info("🎉 处理完成!")

            if result.final_video_path:
                logger.success(f"📽️  最终视频: {result.final_video_path}")

            if result.success_rate > 0:
                logger.success(f"✅ 成功率: {result.success_rate:.1%} ({result.success_count}/{result.total_entries})")

            if result.failure_count > 0:
                logger.warning(f"❌ 失败数量: {result.failure_count}")

            return 0 if result.success_count > 0 else 1

        finally:
            # 清理资源
            processor.cleanup()

    except KeyboardInterrupt:
        logger.warning("❌ 用户中断处理")
        return 130
    except Exception as e:
        logger.error(f"❌ 处理异常: {e}")
        import traceback

        logger.debug(traceback.format_exc())
        return 1


if __name__ == "__main__":
    sys.exit(main())
