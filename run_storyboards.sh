#!/bin/bash

# This script provides a simple interface to run the storyboard to video processor

# Default values
STORYBOARD_FILE="output/storyboard_mcp_nasdq.json"
OUTPUT_DIR="output"
MAX_WORKERS=6
START_IDX=0
END_IDX=""
QUALITY="l" # Default quality
STAGES="dsl,code,render,subtitles,concat" # Default stages
PROJECT_NAME="" # Default project name

# Show help
function show_help {
    echo "Usage: $0 [options]"
    echo
    echo "Options:"
    echo "  -f, --file FILE        Storyboard JSON file (default: $STORYBOARD_FILE)"
    echo "  -o, --output DIR       Output directory (default: $OUTPUT_DIR)"
    echo "  -w, --workers NUM      Maximum number of concurrent workers (default: $MAX_WORKERS)"
    echo "  -s, --start NUM        Start index, 0-based (default: $START_IDX)"
    echo "  -e, --end NUM          End index, 0-based, exclusive (default: all entries)"
    echo "  -q, --quality LEVEL    Manim render quality (l, m, h, k) (default: $QUALITY)"
    echo "  -S, --stages STAGES    Comma-separated list of stages to run (default: $STAGES)"
    echo "                         Stages: dsl,code,render,subtitles,concat"
    echo "  -p, --project-name NAME Project name for final video filename"
    echo "  -h, --help             Show this help"
    echo
    echo "Examples:"
    echo "  $0 -s 1 -e 4                # Process entries 2-4"
    echo "  $0 -f custom.json -o custom/videos # Process a custom storyboard file"
    echo "  $0 -q h                     # Process with high quality"
    echo "  $0 -S \"dsl,code\"            # Process only dsl and code stages"
    echo
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--file)
            STORYBOARD_FILE="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -w|--workers)
            MAX_WORKERS="$2"
            shift 2
            ;;
        -s|--start)
            START_IDX="$2"
            shift 2
            ;;
        -e|--end)
            END_IDX="$2"
            shift 2
            ;;
        -q|--quality)
            QUALITY="$2"
            # Basic validation (optional, can be enhanced)
            if [[ ! "$QUALITY" =~ ^(l|m|h|k)$ ]]; then
                echo "Error: Invalid quality value '$QUALITY'. Must be one of: l, m, h, k"
                exit 1
            fi
            shift 2
            ;;
        -S|--stages)
            STAGES="$2"
            shift 2
            ;;
        -p|--project-name)
            PROJECT_NAME="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate storyboard file
if [[ ! -f "$STORYBOARD_FILE" ]]; then
    echo "Error: Storyboard file not found: $STORYBOARD_FILE"
    exit 1
fi

CMD="./process_storyboard.py"

# Build command with options
CMD="$CMD $STORYBOARD_FILE --output-dir $OUTPUT_DIR/${PROJECT_NAME} --max-workers $MAX_WORKERS --start-idx $START_IDX --quality $QUALITY --stages \"$STAGES\"" # Add quality and stages args

# Add end index if provided
if [[ -n "$END_IDX" ]]; then
    CMD="$CMD --end-idx $END_IDX"
fi

# Add project name if provided
if [[ -n "$PROJECT_NAME" ]]; then
    CMD="$CMD --project-name \"$PROJECT_NAME\""
fi

# Print and execute the command
echo "Running: $CMD"
eval "$CMD"
