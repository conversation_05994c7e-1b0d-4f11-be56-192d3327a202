#!/usr/bin/env python3
"""
End-to-End PDF to Video Pipeline.
Takes a PDF URL/path and a purpose, generates a storyboard, and renders a video.
"""
import argparse
import json
import os
import subprocess
import sys
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from pathlib import Path

from loguru import logger

# Add project root to sys.path
project_root = Path(__file__).resolve().parent
sys.path.append(str(project_root))

from agents.unified_storyboard_agent import UnifiedStoryboardAgent
from dsl.v2.dsl_to_manim import generate_manim_code
from tools.pdf_toolkit import PDFToolkit
from tools.arxiv_recorder_toolkit import ArxivRecorderToolkit
from utils.common import CommonUtils


def run_command(command, description):
    """Runs a shell command and logs its status."""
    logger.info(f"🚀 Running: {description}")
    display_command = " ".join(command)
    logger.debug(f"   Command: {display_command}")
    try:
        result = subprocess.run(
            command,
            check=True,
            capture_output=True,
            text=True,
            encoding="utf-8",
            errors="ignore",
        )
        logger.debug(f"   STDOUT: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ FAILED: {description}")
        logger.error(f"   Return Code: {e.returncode}")
        logger.error(f"   STDOUT: {e.stdout.strip()}")
        logger.error(f"   STDERR: {e.stderr.strip()}")
        return False
    except Exception as e:
        logger.error(f"❌ FAILED: {description} with an unexpected error: {e}")
        return False


def run_pdf_to_storyboard(pdf_path: str, purpose: str, output_dir: Path) -> Path | None:
    """
    Step 1 & 2: PDF to Markdown, then Markdown to Storyboard JSON.
    Returns the path to the storyboard file, or None if failed.
    """
    # HACK: Guess project_name from pdf_path to check for existing files.
    # This might not be robust for all URLs.
    if pdf_path.startswith("http"):
        from urllib.parse import urlparse

        project_name = Path(urlparse(pdf_path).path).name.replace(".pdf", "")
    else:
        project_name = Path(pdf_path).name.replace(".pdf", "")

    storyboard_file = output_dir / project_name / f"{project_name}_storyboard.json"
    logger.info(f"Storyboard file: {storyboard_file}")
    if storyboard_file.exists():
        logger.info(f"✅ Storyboard already exists, skipping generation: {storyboard_file}")
        return storyboard_file

    # Guess markdown file path to skip PDF processing if it exists.
    # This assumes a fixed output structure from the PDF toolkit.
    markdown_file = output_dir / project_name / f"{project_name}.md"
    if not markdown_file.exists():
        logger.info("--- Step 1: Processing PDF to Markdown ---")
        pdf_toolkit = PDFToolkit()
        pdf_result = pdf_toolkit.extract_pdf(pdf_path, output_base_dir=str(output_dir))

        if "error" in pdf_result or not pdf_result.get("markdown_file"):
            logger.error(f"Failed to process PDF: {pdf_result.get('error', 'Unknown error')}")
            return None

        markdown_file = Path(pdf_result["markdown_file"])
        # Update project_name and storyboard_file with the definitive ones from the tool
        project_name = pdf_result["project_name"]
        storyboard_file = output_dir / project_name / f"{project_name}_storyboard.json"
        # Check again for the storyboard with the correct name
        if storyboard_file.exists():
            logger.info(f"✅ Storyboard already exists, skipping generation: {storyboard_file}")
            return storyboard_file
        logger.success(f"✅ PDF processed successfully. Markdown at: {markdown_file}")
    else:
        logger.info(f"✅ Markdown file already exists, skipping PDF processing: {markdown_file}")

    logger.info("--- Step 2: Generating Storyboard from Markdown ---")
    analysis_content = CommonUtils.read_file(str(markdown_file))
    storyboard_agent = UnifiedStoryboardAgent()

    storyboard_result = storyboard_agent.run(analysis_content, purpose, str(storyboard_file))

    if storyboard_result["status"] != "success":
        logger.error(f"Failed to generate storyboard: {storyboard_result.get('error', 'Unknown error')}")
        return None

    logger.success(f"✅ Storyboard generated successfully: {storyboard_file}")
    return storyboard_file


def get_video_duration(video_path):
    """Gets the duration of a video file using ffprobe."""
    command = [
        "ffprobe",
        "-v",
        "error",
        "-show_entries",
        "format=duration",
        "-of",
        "default=noprint_wrappers=1:nokey=1",
        str(video_path),
    ]
    try:
        duration_str = subprocess.check_output(command, text=True, encoding="utf-8").strip()
        return float(duration_str)
    except (subprocess.CalledProcessError, FileNotFoundError):
        logger.warning(f"Could not get duration for {video_path}. Defaulting to 5s.")
        return 5.0


def process_scene(task):
    """Processes a single scene: generates code, renders video, and adds subtitles."""
    scene_num, scene_dsl, output_dir, quality = task.values()
    final_clip_path = output_dir / f"scene_{scene_num}_final.mp4"

    logger.info(f"--- Processing Scene {scene_num} ---")
    py_file = output_dir / f"scene_{scene_num}.py"

    try:
        wrapped_dsl = {"metadata": {"title": f"Storyboard_{scene_num}"}, "actions": [scene_dsl]}
        _, scene_class_name = generate_manim_code(wrapped_dsl, str(py_file))
        if not scene_class_name:
            raise ValueError("generate_manim_code did not return a scene class name.")
        logger.success(f"Step 1/3: Generated Python code for Scene {scene_num}: {py_file.name}")
    except Exception as e:
        logger.error(f"FAILED to generate Python code for Scene {scene_num}: {e}")
        return None

    quality_map = {"l": "480p15", "m": "720p30", "h": "1080p60", "k": "2160p60"}
    media_dir = Path("media") / "videos" / py_file.stem / quality_map[quality]

    # Check if the raw rendered video already exists
    rendered_video = media_dir / f"{scene_class_name}.mp4"

    manim_cmd = ["manim", str(py_file), scene_class_name, f"--quality={quality}", "--progress_bar", "none"]
    if not run_command(manim_cmd, f"Rendering Scene {scene_num}"):
        return None
    logger.success(f"Step 2/3: Rendered video for Scene {scene_num}")

    if not rendered_video.exists():
        logger.error(f"Manim output video not found at {rendered_video}")
        return None

    narration = scene_dsl.get("params", {}).get("narration")
    if not narration:
        logger.info(f"No narration for Scene {scene_num}, skipping subtitles.")
        rendered_video.rename(final_clip_path)
        return str(final_clip_path)

    srt_file = media_dir / f"{scene_class_name}.srt"
    # Here we assume the srt file is generated alongside the video by Manim if narration exists.
    # If the srt file doesn't exist, we can't add subtitles.
    if not srt_file.exists():
        logger.warning(f"SRT file not found for Scene {scene_num} at {srt_file}. Skipping subtitles.")
        rendered_video.rename(final_clip_path)
        return str(final_clip_path)

    subtitle_style = "Alignment=2,MarginV=6,fontcolor=white,Fontsize=16,fontweight=bold,FontName=微软雅黑,BorderStyle=3,Outline=1,Shadow=1"
    ffmpeg_cmd = [
        "ffmpeg",
        "-loglevel",
        "warning",
        "-y",
        "-i",
        str(rendered_video),
        "-vf",
        f"subtitles='{srt_file}':force_style='{subtitle_style}'",
        str(final_clip_path),
    ]
    if not run_command(ffmpeg_cmd, f"Adding subtitles to Scene {scene_num}"):
        return None
    logger.success(f"Step 3/3: Added subtitles for Scene {scene_num}")
    return str(final_clip_path)


def main():
    """Main function to drive the PDF-to-video generation process."""

    parser = argparse.ArgumentParser(description="Generates a video from a PDF and a purpose.")
    parser.add_argument("pdf_path", help="Path or URL to the PDF file.")
    parser.add_argument("purpose", help="The purpose or main theme of the video.")
    parser.add_argument("-o", "--output_dir", default=None, help="Base output directory. Defaults to './output'.")
    parser.add_argument(
        "-w",
        "--max_workers",
        type=int,
        default=os.cpu_count(),
        help=f"Max parallel rendering jobs (default: {os.cpu_count()}).",
    )
    parser.add_argument(
        "-q",
        "--quality",
        type=str,
        default="h",
        choices=["l", "m", "h", "k"],
        help="Manim render quality (default: h).",
    )
    parser.add_argument(
        "--bg_music", type=str, default="assets/bgm_v2.m4a", help="Path to BGM. Defaults to 'assets/bgm_v2.m4a'."
    )
    args = parser.parse_args()

    base_output_dir = Path(args.output_dir) if args.output_dir else Path("output")

    # --- Steps 1 & 2: PDF -> Markdown -> Storyboard ---
    storyboard_path = run_pdf_to_storyboard(args.pdf_path, args.purpose, base_output_dir)
    if not storyboard_path:
        logger.error("🔥 Pipeline failed during storyboard generation. Exiting.")
        sys.exit(1)

    # --- Step 3: Storyboard -> Video ---
    logger.info("--- Step 3: Processing Storyboard to Video ---")
    project_name = storyboard_path.stem.replace("_storyboard", "")
    # The output directory is now the project-specific one created by the PDF toolkit
    output_dir = storyboard_path.parent / "generated_files"

    if not storyboard_path.exists():
        logger.error(f"Storyboard file not found: {storyboard_path}")
        sys.exit(1)

    logger.info("🎬 Starting video generation process...")
    logger.info(f"   - Output Directory: {output_dir}")
    logger.info(f"   - Max Workers: {args.max_workers}")
    logger.info(f"   - Quality: {args.quality}")

    with open(storyboard_path, encoding="utf-8") as f:
        scenes = json.load(f).get("actions", [])
    if not scenes:
        logger.warning("No scenes found in the storyboard file.")
        sys.exit(0)

    tasks = [
        {"scene_num": i + 1, "scene_dsl": s, "output_dir": output_dir, "quality": args.quality}
        for i, s in enumerate(scenes)
    ]
    logger.info(f"Found {len(scenes)} scenes. Starting parallel processing...")

    video_files = [None] * len(scenes)
    with ThreadPoolExecutor(max_workers=args.max_workers) as executor:
        future_to_num = {executor.submit(process_scene, task): task["scene_num"] for task in tasks}
        for future in as_completed(future_to_num):
            scene_num = future_to_num[future]
            try:
                if result_path := future.result():
                    video_files[scene_num - 1] = result_path
                    logger.success(f"✅ Finished processing Scene {scene_num}.")
                else:
                    logger.error(f"❌ Failed to process Scene {scene_num}.")
            except Exception as e:
                logger.error(f"Scene {scene_num} generated an exception: {e}", exc_info=True)

    successful_videos = [f for f in video_files if f]
    if not successful_videos:
        logger.error("🔥 No scenes were successfully rendered. Exiting.")
        sys.exit(1)

    logger.info("🎬 Concatenating video clips...")
    concat_list_path = output_dir / "concat_list.txt"
    # Use relative paths for the concat list
    concat_list_path.write_text("\n".join(f"file '{Path(p).name}'" for p in successful_videos))

    final_video_no_audio = output_dir / f"{project_name}_final_no_bgm.mp4"
    ffmpeg_concat_cmd = [
        "ffmpeg",
        "-loglevel",
        "warning",
        "-y",
        "-f",
        "concat",
        "-safe",
        "0",
        "-i",
        str(concat_list_path),
        "-c",
        "copy",
        str(final_video_no_audio),
    ]
    if not run_command(ffmpeg_concat_cmd, "Concatenating videos"):
        sys.exit(1)

    bg_music_path = Path(args.bg_music)
    final_video = final_video_no_audio
    if bg_music_path.exists():
        logger.info("🎵 Adding background music...")
        final_video_with_audio = output_dir / f"{project_name}_final.mp4"
        duration = get_video_duration(final_video_no_audio)
        ffmpeg_audio_cmd = [
            "ffmpeg",
            "-loglevel",
            "warning",
            "-y",
            "-i",
            str(final_video_no_audio),
            "-t",
            str(duration),
            "-stream_loop",
            "-1",
            "-i",
            str(bg_music_path),
            "-c:v",
            "copy",
            "-filter_complex",
            "[1:a]volume=0.05[bgm];[0:a][bgm]amix=inputs=2:duration=first",
            str(final_video_with_audio),
        ]
        if run_command(ffmpeg_audio_cmd, "Adding background music"):
            final_video = final_video_with_audio
            logger.success("Successfully added background music.")
        else:
            logger.warning("Failed to add background music. The video without music is available.")
    elif args.bg_music:
        logger.warning(f"Background music file not found at '{args.bg_music}'. Skipping audio.")

    logger.success(f"🎉🎉🎉 Processing complete! Final video is ready at: {final_video.resolve()}")


if __name__ == "__main__":
    main()
