#!/bin/bash
# 查找并执行当前目录下所有以render_storyboard_开头的Python文件

# 设置默认值
QUIET_MODE=false
target=$1
step=$3

# 依次执行每个脚本
file=$2
MAX_RETRIES=5  # 最大重试次数
attempt=0
SUCCESS_COUNT=0
FAIL_COUNT=0
while [ $attempt -lt $MAX_RETRIES ]; do
    if [ "$QUIET_MODE" = false ]; then
        manim -ql "$file"
        RETURN_CODE=$?
    else
        # 在静默模式下将输出重定向到/dev/null
        manim -pql "$file" > /dev/null 2>&1
        RETURN_CODE=$?
    fi
    ((attempt++))
    # 检查返回值
    if [ $RETURN_CODE -eq 0 ]; then
        if [ "$QUIET_MODE" = false ]; then
            echo "✅ 成功执行 $file"
        else
            echo "成功"
        fi
        ((SUCCESS_COUNT++))
        break  # 成功后退出循环
    else

        if [ "$QUIET_MODE" = false ]; then
            echo "❌ 执行 $file 失败，返回码: $RETURN_CODE (尝试 $attempt/$MAX_RETRIES)"
        else
            echo "失败 (返回码: $RETURN_CODE)"
        fi
        ((FAIL_COUNT++))
        rm -fr media/videos/render_storyboard_$step
        rm -fr media/voiceovers
    fi
    python3 -m examples.feynman_manim_code ${target} ${step}
done


# 打印执行汇总
echo -e "\n===================="$step"=============================="
echo "执行汇总:"
echo "成功执行: $SUCCESS_COUNT"
echo "执行失败: $FAIL_COUNT"
echo "=================================================="
exit $((SUCCESS_COUNT+FAIL_COUNT))
