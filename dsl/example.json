{"metadata": {"title": "BubbleSortDemo", "author": "AI Assistant", "resolution": "1080p", "background_color": "BLACK", "estimated_duration": 25}, "objects": [{"id": "num_5", "type": "text", "properties": {"content": "5", "font_size": 60, "color": "#ADD8E6"}}, {"id": "num_2", "type": "text", "properties": {"content": "2", "font_size": 60, "color": "#90EE90"}}, {"id": "num_8", "type": "text", "properties": {"content": "8", "font_size": 60, "color": "#FFB6C1"}}, {"id": "num_1", "type": "text", "properties": {"content": "1", "font_size": 60, "color": "#FFFFE0"}}, {"id": "num_9", "type": "text", "properties": {"content": "9", "font_size": 60, "color": "#FFA07A"}}, {"id": "num_4", "type": "text", "properties": {"content": "4", "font_size": 60, "color": "#D8BFD8"}}, {"id": "sort_button", "type": "text", "properties": {"content": "Sort", "font_size": 48, "color": "WHITE"}}, {"id": "star_1", "type": "star", "properties": {"color": "YELLOW", "radius": 0.2, "fill_opacity": 0}}, {"id": "star_2", "type": "star", "properties": {"color": "GOLD", "radius": 0.25, "fill_opacity": 0}}, {"id": "star_3", "type": "star", "properties": {"color": "YELLOW", "radius": 0.2, "fill_opacity": 0}}], "actions": [{"type": "create", "target": "num_5", "position": [-2.5, -5, 0], "animation": "none"}, {"type": "create", "target": "num_2", "position": [-1.5, -5, 0], "animation": "none"}, {"type": "create", "target": "num_8", "position": [-0.5, -5, 0], "animation": "none"}, {"type": "create", "target": "num_1", "position": [0.5, -5, 0], "animation": "none"}, {"type": "create", "target": "num_9", "position": [1.5, -5, 0], "animation": "none"}, {"type": "create", "target": "num_4", "position": [2.5, -5, 0], "animation": "none"}, {"type": "create", "target": "star_1", "position": [-2, 2, 0], "animation": "none"}, {"type": "create", "target": "star_2", "position": [0, 2.5, 0], "animation": "none"}, {"type": "create", "target": "star_3", "position": [2, 2, 0], "animation": "none"}, {"type": "animate_group", "actions": [{"type": "move_to", "target": "num_5", "position": [-2.5, 1, 0], "duration": 1.5}, {"type": "move_to", "target": "num_2", "position": [-1.5, 1, 0], "duration": 1.5}, {"type": "move_to", "target": "num_8", "position": [-0.5, 1, 0], "duration": 1.5}, {"type": "move_to", "target": "num_1", "position": [0.5, 1, 0], "duration": 1.5}, {"type": "move_to", "target": "num_9", "position": [1.5, 1, 0], "duration": 1.5}, {"type": "move_to", "target": "num_4", "position": [2.5, 1, 0], "duration": 1.5}]}, {"type": "create", "target": "sort_button", "position": [0, -2, 0], "animation": "fade_in", "duration": 0.5}, {"type": "wait", "duration": 1}, {"type": "indicate", "target": "num_5", "color": "YELLOW", "duration": 0.5}, {"type": "indicate", "target": "num_2", "color": "YELLOW", "duration": 0.5}, {"type": "wait", "duration": 0.5}, {"type": "animate_group", "actions": [{"type": "move_to", "target": "num_5", "position": [-1.5, 1, 0], "duration": 0.7}, {"type": "move_to", "target": "num_2", "position": [-2.5, 1, 0], "duration": 0.7}]}, {"type": "wait", "duration": 0.3}, {"type": "indicate", "target": "num_5", "color": "YELLOW", "duration": 0.5}, {"type": "indicate", "target": "num_8", "color": "YELLOW", "duration": 0.5}, {"type": "wait", "duration": 0.5}, {"type": "wait", "duration": 0.3}, {"type": "indicate", "target": "num_8", "color": "YELLOW", "duration": 0.5}, {"type": "indicate", "target": "num_1", "color": "YELLOW", "duration": 0.5}, {"type": "wait", "duration": 0.5}, {"type": "animate_group", "actions": [{"type": "move_to", "target": "num_8", "position": [0.5, 1, 0], "duration": 0.7}, {"type": "move_to", "target": "num_1", "position": [-0.5, 1, 0], "duration": 0.7}]}, {"type": "wait", "duration": 0.3}, {"type": "indicate", "target": "num_8", "color": "YELLOW", "duration": 0.5}, {"type": "indicate", "target": "num_9", "color": "YELLOW", "duration": 0.5}, {"type": "wait", "duration": 0.5}, {"type": "wait", "duration": 0.3}, {"type": "indicate", "target": "num_9", "color": "YELLOW", "duration": 0.5}, {"type": "indicate", "target": "num_4", "color": "YELLOW", "duration": 0.5}, {"type": "wait", "duration": 0.5}, {"type": "animate_group", "actions": [{"type": "move_to", "target": "num_9", "position": [2.5, 1, 0], "duration": 0.7}, {"type": "move_to", "target": "num_4", "position": [1.5, 1, 0], "duration": 0.7}]}, {"type": "wait", "duration": 0.3}, {"type": "indicate", "target": "num_2", "color": "GREEN_A", "duration": 0.5}, {"type": "indicate", "target": "num_5", "color": "GREEN_A", "duration": 0.5}, {"type": "wait", "duration": 0.5}, {"type": "wait", "duration": 0.3}, {"type": "indicate", "target": "num_5", "color": "GREEN_A", "duration": 0.5}, {"type": "indicate", "target": "num_1", "color": "GREEN_A", "duration": 0.5}, {"type": "wait", "duration": 0.5}, {"type": "animate_group", "actions": [{"type": "move_to", "target": "num_5", "position": [-0.5, 1, 0], "duration": 0.7}, {"type": "move_to", "target": "num_1", "position": [-1.5, 1, 0], "duration": 0.7}]}, {"type": "wait", "duration": 0.3}, {"type": "indicate", "target": "num_5", "color": "GREEN_A", "duration": 0.5}, {"type": "indicate", "target": "num_8", "color": "GREEN_A", "duration": 0.5}, {"type": "wait", "duration": 0.5}, {"type": "wait", "duration": 0.3}, {"type": "indicate", "target": "num_8", "color": "GREEN_A", "duration": 0.5}, {"type": "indicate", "target": "num_4", "color": "GREEN_A", "duration": 0.5}, {"type": "wait", "duration": 0.5}, {"type": "animate_group", "actions": [{"type": "move_to", "target": "num_8", "position": [1.5, 1, 0], "duration": 0.7}, {"type": "move_to", "target": "num_4", "position": [0.5, 1, 0], "duration": 0.7}]}, {"type": "wait", "duration": 0.3}, {"type": "indicate", "target": "num_8", "color": "GREEN_A", "duration": 0.5}, {"type": "indicate", "target": "num_9", "color": "GREEN_A", "duration": 0.5}, {"type": "wait", "duration": 0.5}, {"type": "wait", "duration": 0.3}, {"type": "indicate", "target": "num_2", "color": "BLUE_A", "duration": 0.5}, {"type": "indicate", "target": "num_1", "color": "BLUE_A", "duration": 0.5}, {"type": "wait", "duration": 0.5}, {"type": "animate_group", "actions": [{"type": "move_to", "target": "num_2", "position": [-1.5, 1, 0], "duration": 0.7}, {"type": "move_to", "target": "num_1", "position": [-2.5, 1, 0], "duration": 0.7}]}, {"type": "wait", "duration": 0.3}, {"type": "indicate", "target": "num_2", "color": "BLUE_A", "duration": 0.5}, {"type": "indicate", "target": "num_5", "color": "BLUE_A", "duration": 0.5}, {"type": "wait", "duration": 0.5}, {"type": "wait", "duration": 0.3}, {"type": "indicate", "target": "num_5", "color": "BLUE_A", "duration": 0.5}, {"type": "indicate", "target": "num_4", "color": "BLUE_A", "duration": 0.5}, {"type": "wait", "duration": 0.5}, {"type": "animate_group", "actions": [{"type": "move_to", "target": "num_5", "position": [0.5, 1, 0], "duration": 0.7}, {"type": "move_to", "target": "num_4", "position": [-0.5, 1, 0], "duration": 0.7}]}, {"type": "wait", "duration": 0.3}, {"type": "indicate", "target": "num_5", "color": "BLUE_A", "duration": 0.5}, {"type": "indicate", "target": "num_8", "color": "BLUE_A", "duration": 0.5}, {"type": "wait", "duration": 0.5}, {"type": "wait", "duration": 0.3}, {"type": "indicate", "target": "num_8", "color": "BLUE_A", "duration": 0.5}, {"type": "indicate", "target": "num_9", "color": "BLUE_A", "duration": 0.5}, {"type": "wait", "duration": 0.5}, {"type": "wait", "duration": 0.3}, {"type": "indicate", "target": "num_1", "color": "GRAY", "duration": 0.4}, {"type": "indicate", "target": "num_2", "color": "GRAY", "duration": 0.4}, {"type": "wait", "duration": 0.4}, {"type": "indicate", "target": "num_2", "color": "GRAY", "duration": 0.4}, {"type": "indicate", "target": "num_4", "color": "GRAY", "duration": 0.4}, {"type": "wait", "duration": 0.4}, {"type": "indicate", "target": "num_4", "color": "GRAY", "duration": 0.4}, {"type": "indicate", "target": "num_5", "color": "GRAY", "duration": 0.4}, {"type": "wait", "duration": 0.4}, {"type": "indicate", "target": "num_5", "color": "GRAY", "duration": 0.4}, {"type": "indicate", "target": "num_8", "color": "GRAY", "duration": 0.4}, {"type": "wait", "duration": 0.4}, {"type": "indicate", "target": "num_8", "color": "GRAY", "duration": 0.4}, {"type": "indicate", "target": "num_9", "color": "GRAY", "duration": 0.4}, {"type": "wait", "duration": 0.4}, {"type": "wait", "duration": 1}, {"type": "animate_group", "actions": [{"type": "transform", "target": "star_1", "properties": {"fill_opacity": 1}, "duration": 0.5}, {"type": "transform", "target": "star_2", "properties": {"fill_opacity": 1}, "duration": 0.5}, {"type": "transform", "target": "star_3", "properties": {"fill_opacity": 1}, "duration": 0.5}]}, {"type": "wait", "duration": 2}, {"type": "animate_group", "actions": [{"type": "transform", "target": "star_1", "properties": {"fill_opacity": 0}, "duration": 0.5}, {"type": "transform", "target": "star_2", "properties": {"fill_opacity": 0}, "duration": 0.5}, {"type": "transform", "target": "star_3", "properties": {"fill_opacity": 0}, "duration": 0.5}]}]}