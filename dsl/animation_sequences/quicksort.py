"""Generates the full JSON structure for a Quicksort animation."""

from typing import Any

# Basic colors for elements, can be expanded or customized
ELEMENT_COLORS = [
    "#F38BA8",  # Mauve
    "#A6E3A1",  # Green
    "#F9E2AF",  # Yellow
    "#FAB387",  # Peach
    "#CBA6F7",  # Lavender
    "#89B4FA",  # Blue
    "#74C7EC",  # Sky
    "#F5C2E7",  # Pink
    "#94E2D5",  # Teal
    "#F2CDCD",  # Flamingo
]


def _get_element_color(index: int) -> str:
    """Assigns a color based on index, cycling through the list."""
    return ELEMENT_COLORS[index % len(ELEMENT_COLORS)]


def generate_quicksort_json(arr_input: list[int], title: str = "QuickSortVisualization") -> dict[str, Any]:
    """
    Generates the complete JSON object for visualizing the Quicksort algorithm.

    Args:
        arr_input: The list of integers to sort and animate.
        title: The title for the animation (used as Python class name).

    Returns:
        A dictionary representing the complete JSON structure for the animation DSL.
    """
    n = len(arr_input)
    objects = []
    actions = []

    # --- Metadata ---
    metadata = {
        "title": title,
        "author": "Quicksort Generator",
        "resolution": "1080p",
        "background_color": "#1E1E2E",  # Catppuccin Mocha Base
        "estimated_duration": n * 5,  # Rough estimate
    }

    # --- Object Definitions ---

    # Base object properties (optional, for potential reuse or inheritance if supported)
    # Not strictly necessary per schema, but can help define common styles
    base_rect_props = {
        "width": 1.0,
        "height": 1.0,
        "stroke_color": "#89B4FA",  # Blue stroke
        "stroke_width": 3,
        "fill_opacity": 0.1,
    }
    base_text_props = {
        "font_size": 40,
        "color": "#CDD6F4",  # Text color
    }

    # Helper Data Structures
    element_ids = {f"element_{i}": arr_input[i] for i in range(n)}
    id_to_val = {f"element_{i}": arr_input[i] for i in range(n)}
    object_component_ids = {
        el_id: {"rect": f"rect_{el_id}", "text": f"text_{el_id}", "group": f"group_{el_id}"} for el_id in element_ids
    }

    # Calculate initial positions
    element_width = 1.0
    buffer = 1.0  # Increased buffer for clarity
    total_width = n * element_width + (n - 1) * buffer
    start_x = -total_width / 2 + element_width / 2
    positions = {
        object_component_ids[f"element_{i}"]["group"]: [
            start_x + i * (element_width + buffer),
            1.5,
            0,
        ]  # Raised Y position
        for i in range(n)
    }

    # Generate object definitions for each element
    for i in range(n):
        el_id = f"element_{i}"
        val = element_ids[el_id]
        components = object_component_ids[el_id]
        rect_id = components["rect"]
        text_id = components["text"]
        group_id = components["group"]
        color = _get_element_color(i)  # Assign color based on initial index

        # Rectangle definition
        objects.append(
            {
                "id": rect_id,
                "type": "rectangle",
                "properties": {**base_rect_props, "color": color},  # Combine base and specific color
            }
        )
        # Text definition
        objects.append(
            {
                "id": text_id,
                "type": "text",
                "properties": {**base_text_props, "content": str(val)},  # Set content here
            }
        )
        # Group definition
        objects.append({"id": group_id, "type": "group", "properties": {"members": [rect_id, text_id]}})

    # Define Pointer and Final Text objects
    # Change Arrow start/end to be relative offsets for the new generator logic.
    # The \'position\' in create/move_to will now be the anchor (center of the target element).
    objects.extend(
        [
            {
                "id": "pivot_indicator",
                "type": "arrow",
                "properties": {
                    "start": [0, 1.0, 0],  # Offset: Start slightly above the element center
                    "end": [0, 0.5, 0],  # Offset: End just above the element center (pointing down)
                    "color": "#F5C2E7",
                    "stroke_width": 6,  # Pink
                },
            },
            {
                "id": "left_ptr",
                "type": "arrow",
                "properties": {
                    "start": [0, -1.0, 0],  # Offset: Start below the element center
                    "end": [0, -0.5, 0],  # Offset: End just below the element center (pointing up)
                    "color": "#A6E3A1",
                    "stroke_width": 6,  # Green
                },
            },
            {
                "id": "right_ptr",
                "type": "arrow",
                "properties": {
                    "start": [0, -1.0, 0],  # Offset: Start below the element center
                    "end": [0, -0.5, 0],  # Offset: End just below the element center (pointing up)
                    "color": "#F38BA8",
                    "stroke_width": 6,  # Red/Mauve
                },
            },
            {
                "id": "sorted_text",
                "type": "text",
                "properties": {"content": "Array Sorted!", "font_size": 60, "color": "#A6E3A1"},  # Green
            },
        ]
    )

    # --- Action Generation ---

    # 1. Initial Creation Actions
    # Step 1: Create individual members (rects and texts) SEQUENTIALLY
    # We cannot use animate_group here due to the strict validator requiring
    # member create actions to appear *before* group create action in the top-level list.
    for i in range(n):
        el_id = f"element_{i}"
        components = object_component_ids[el_id]
        rect_id = components["rect"]
        text_id = components["text"]
        pos = positions[components["group"]]  # Use group position for members

        # Create Rectangle
        actions.append(
            {
                "type": "create",
                "target": rect_id,
                "position": pos,
                "animation": "grow",
                "duration": 0.3,
            }  # Faster individual creation
        )
        # Create Text
        actions.append(
            {
                "type": "create",
                "target": text_id,
                "position": pos,
                "animation": "write",
                "duration": 0.3,
            }  # Faster individual creation
        )

    # Step 2: Create the groups instantly AFTER their members have been created.
    for i in range(n):
        el_id = f"element_{i}"
        group_id = object_component_ids[el_id]["group"]
        # Note: Group position is implicitly the center of its members upon creation if not specified.
        # We rely on the members already being in the correct place.
        actions.append({"type": "create", "target": group_id, "animation": "none", "duration": 0})

    actions.append({"type": "wait", "duration": 0.5})  # Shorter wait after sequential creation

    # Helper for explanations - generates unique objects
    explanation_counter = 0
    last_explanation_id = None
    explanation_x_pos = 0
    explanation_y_pos = 1.5 + 2.0  # Y position derived from array_y

    def _define_and_show_explanation(text: str, duration: float = 0.5, wait_after: float = 1.0):
        nonlocal explanation_counter, last_explanation_id

        # Generate unique ID
        explanation_counter += 1
        new_id = f"explanation_{explanation_counter}"
        explanation_pos = [explanation_x_pos, explanation_y_pos, 0]

        # Define the new text object in the main objects list
        objects.append(
            {
                "id": new_id,
                "type": "text",
                "properties": {
                    "content": text,
                    "font_size": 36,
                    "color": "#CBA6F7",  # Lavender
                },
            }
        )

        # Add the create action
        actions.append(
            {
                "type": "create",
                "target": new_id,
                "position": explanation_pos,
                "animation": "write",
                "duration": duration,
            }
        )

        if wait_after > 0:
            actions.append({"type": "wait", "duration": wait_after})

        # Keep track of the last shown ID for removal
        last_explanation_id = new_id

    def _remove_last_explanation(duration: float = 0.3, wait_after: float = 0.1):
        nonlocal last_explanation_id
        if last_explanation_id:
            actions.append(
                {"type": "remove", "target": last_explanation_id, "animation": "fade_out", "duration": duration}
            )
            if wait_after > 0:
                actions.append({"type": "wait", "duration": wait_after})
            last_explanation_id = None  # Reset after removal action is added

    # --- Add Initial Explanation ---
    _define_and_show_explanation("Starting Quicksort!", wait_after=1.5)
    _remove_last_explanation()
    # --- End Initial Explanation ---

    # Helper to get current position
    def get_pos(element_group_id):
        return positions[element_group_id]

    # Helper for swap animation
    def swap_positions(group_id1, group_id2):
        pos1 = positions[group_id1]
        pos2 = positions[group_id2]
        positions[group_id1] = pos2
        positions[group_id2] = pos1
        actions.append(
            {
                "type": "animate_group",
                "actions": [
                    {"type": "move_to", "target": group_id1, "position": pos2, "duration": 1},
                    {"type": "move_to", "target": group_id2, "position": pos1, "duration": 1},
                ],
                # Duration is implicit in animate_group based on longest inner action
            }
        )
        actions.append({"type": "wait", "duration": 0.5})

    # 2. Quicksort Recursive Logic
    current_ids_in_array = [f"element_{i}" for i in range(n)]  # Tracks which element ID is at which index

    def _quicksort_recursive(low, high):
        if low >= high:
            # Optional: Add indication for sorted sub-array if desired
            _remove_last_explanation()  # Remove previous before showing base case
            if low >= 0 and low < n:  # Check bounds for single element case
                el_id = current_ids_in_array[low]
                val = id_to_val[el_id]
                _define_and_show_explanation(f"Element {val} at index {low} is sorted.", wait_after=1.0)
            else:
                # This case might not be reachable with proper bounds check, but good to have
                _define_and_show_explanation(
                    f"Sub-array from index {low} to {high} is sorted (Base Case).", wait_after=1.0
                )
            return

        _remove_last_explanation()  # Remove previous
        _define_and_show_explanation(f"Partitioning sub-array from index {low} to {high}.", wait_after=1.0)

        pivot_index_in_tracking_array = low
        pivot_id = current_ids_in_array[pivot_index_in_tracking_array]
        pivot_val = id_to_val[pivot_id]  # Use original value mapping
        pivot_group_id = object_component_ids[pivot_id]["group"]
        pivot_pos = get_pos(pivot_group_id)

        _remove_last_explanation()  # Remove previous
        _define_and_show_explanation(
            f"Selected pivot: {pivot_val} (at original index {pivot_index_in_tracking_array}).", wait_after=1.5
        )

        # Determine initial pointer positions based on element positions below/above
        # Pointer positions relative to the center of the element's group
        # REMOVED: ptr_y_offset = base_rect_props["height"] / 2 + 0.7 # Position pointers below/above boxes

        # Find the group ID for the element at low+1, if it exists
        left_ptr_target_group_id = None
        left_ptr_target_pos = None  # Position to anchor the left pointer
        if low + 1 <= high:
            left_ptr_target_id = current_ids_in_array[low + 1]
            left_ptr_target_group_id = object_component_ids[left_ptr_target_id]["group"]
            left_ptr_target_pos = get_pos(left_ptr_target_group_id)
        else:
            # If no element at low+1, place pointer conceptually next to pivot
            left_ptr_target_pos = [pivot_pos[0] + (element_width + buffer), pivot_pos[1], 0]

        # Find the group ID and position for the element at high
        right_ptr_target_id = current_ids_in_array[high]
        right_ptr_target_group_id = object_component_ids[right_ptr_target_id]["group"]
        right_ptr_target_pos = get_pos(right_ptr_target_group_id)

        _remove_last_explanation()  # Remove previous
        _define_and_show_explanation(
            f"Initializing pointers: Left (i) starts after pivot, Right (j) at end ({high}).", wait_after=1.5
        )

        # Calculate initial anchor positions for create action
        # Anchor position is the center of the element the arrow points to.
        pivot_indicator_initial_pos = pivot_pos
        left_ptr_initial_pos = left_ptr_target_pos
        right_ptr_initial_pos = right_ptr_target_pos

        # Create pointers - Use create/move pattern like test_qs.json
        # Create at initial calculated positions
        actions.append(
            {
                "type": "create",
                "target": "pivot_indicator",
                "position": pivot_indicator_initial_pos,
                "animation": "fade_in",
                "duration": 0.3,
            }
        )
        actions.append(
            {
                "type": "create",
                "target": "left_ptr",
                "position": left_ptr_initial_pos,
                "animation": "fade_in",
                "duration": 0.3,
            }
        )
        actions.append(
            {
                "type": "create",
                "target": "right_ptr",
                "position": right_ptr_initial_pos,
                "animation": "fade_in",
                "duration": 0.3,
            }
        )
        actions.append({"type": "wait", "duration": 0.8})  # Slightly longer wait

        i = low + 1
        j = high

        while True:
            # Move left pointer (i)
            moved_left = False
            while i <= high:  # Ensure 'i' doesn't go out of bounds for current partition
                i_id = current_ids_in_array[i]
                i_val = id_to_val[i_id]
                _remove_last_explanation()  # Remove previous
                _define_and_show_explanation(
                    f"Left pointer (i={i}): Check if {i_val} > pivot ({pivot_val}).", wait_after=0.5
                )
                if i_val > pivot_val:  # Stop condition
                    _remove_last_explanation()  # Remove previous
                    _define_and_show_explanation(f"Stop left pointer: {i_val} > {pivot_val}.", wait_after=0.5)
                    break
                _remove_last_explanation()  # Remove previous
                _define_and_show_explanation(
                    f"Move left pointer: {i_val} <= {pivot_val}.", wait_after=0.2
                )  # Shorter wait during move
                # Move pointer first, before incrementing i
                i_group_id = object_component_ids[i_id]["group"]
                i_pos = get_pos(i_group_id)
                # The 'position' for move_to is the anchor (center of element i)
                actions.append({"type": "move_to", "target": "left_ptr", "position": i_pos, "duration": 0.4})
                moved_left = True
                i += 1  # Now increment i
            if moved_left:
                actions.append({"type": "wait", "duration": 0.3})

            # Move right pointer (j)
            moved_right = False
            while j > low:  # Ensure 'j' doesn't go past the pivot's position
                j_id = current_ids_in_array[j]
                j_val = id_to_val[j_id]
                _remove_last_explanation()  # Remove previous
                _define_and_show_explanation(
                    f"Right pointer (j={j}): Check if {j_val} < pivot ({pivot_val}).", wait_after=0.5
                )
                if j_val < pivot_val:  # Stop condition
                    _remove_last_explanation()  # Remove previous
                    _define_and_show_explanation(f"Stop right pointer: {j_val} < {pivot_val}.", wait_after=0.5)
                    break
                _remove_last_explanation()  # Remove previous
                _define_and_show_explanation(
                    f"Move right pointer: {j_val} >= {pivot_val}.", wait_after=0.2
                )  # Shorter wait during move
                # Move pointer first, before decrementing j
                j_group_id = object_component_ids[j_id]["group"]
                j_pos = get_pos(j_group_id)
                # The 'position' for move_to is the anchor (center of element j)
                actions.append({"type": "move_to", "target": "right_ptr", "position": j_pos, "duration": 0.4})
                moved_right = True
                j -= 1  # Now decrement j
            if moved_right:
                actions.append({"type": "wait", "duration": 0.3})

            if i >= j:  # Pointers crossed or met
                break

            # --- Swap elements at indices i and j ---
            # Get IDs and group IDs from the tracking array *before* swapping it
            id_i = current_ids_in_array[i]
            id_j = current_ids_in_array[j]
            val_i = id_to_val[id_i]
            val_j = id_to_val[id_j]
            _remove_last_explanation()  # Remove previous
            _define_and_show_explanation(
                f"Pointers haven't crossed. Swap elements {val_i} (at i={i}) and {val_j} (at j={j}).", wait_after=1.5
            )

            group_id_i = object_component_ids[id_i]["group"]
            group_id_j = object_component_ids[id_j]["group"]

            # Swap IDs in our tracking array first
            current_ids_in_array[i], current_ids_in_array[j] = current_ids_in_array[j], current_ids_in_array[i]

            # Perform visual swap animation using the group IDs
            swap_positions(group_id_i, group_id_j)  # Includes wait

        # --- Swap pivot element (at index low) with element at index j ---
        # Get IDs and group IDs before swapping tracking array
        id_low = current_ids_in_array[low]  # ID currently at low index in tracking array
        id_j = current_ids_in_array[j]  # ID currently at j index in tracking array
        val_low = id_to_val[id_low]
        val_j = id_to_val[id_j]
        _remove_last_explanation()  # Remove previous
        _define_and_show_explanation(
            f"Pointers crossed or met. Swap pivot ({val_low}) with element at j ({val_j}).", wait_after=1.5
        )

        group_id_low = object_component_ids[id_low]["group"]
        group_id_j = object_component_ids[id_j]["group"]

        # Swap IDs in tracking array
        current_ids_in_array[low], current_ids_in_array[j] = current_ids_in_array[j], current_ids_in_array[low]

        # Perform visual swap
        swap_positions(group_id_low, group_id_j)  # Includes wait

        # Partition index is now j
        _remove_last_explanation()  # Remove previous
        _define_and_show_explanation(
            f"Partition complete. Pivot {pivot_val} is now at sorted position {j}.", wait_after=1.0
        )

        # Remove pointers
        actions.append({"type": "remove", "target": "pivot_indicator", "animation": "fade_out", "duration": 0.2})
        actions.append({"type": "remove", "target": "left_ptr", "animation": "fade_out", "duration": 0.2})
        actions.append({"type": "remove", "target": "right_ptr", "animation": "fade_out", "duration": 0.2})
        actions.append({"type": "wait", "duration": 1.0})  # Wait after partition is complete

        # Recursive calls
        _remove_last_explanation()  # Remove previous before recursive call
        _define_and_show_explanation(f"Recursively sort left sub-array ({low} to {j - 1}).", wait_after=1.0)
        _quicksort_recursive(low, j - 1)
        _remove_last_explanation()  # Remove previous before recursive call
        _define_and_show_explanation(f"Recursively sort right sub-array ({j + 1} to {high}).", wait_after=1.0)
        _quicksort_recursive(j + 1, high)

    # Start the recursion
    _quicksort_recursive(0, n - 1)

    # 3. Finalization Actions
    # Calculate center position below the array
    # Use the final positions dictionary which reflects the sorted state visually
    final_positions = [positions[object_component_ids[el_id]["group"]] for el_id in current_ids_in_array]
    if final_positions:
        center_x = sum(p[0] for p in final_positions) / len(final_positions)
        array_y = final_positions[0][1]  # Get Y coord of array elements
    else:
        center_x = 0
        array_y = 1.5  # Default if array was empty
    final_text_pos = [center_x, array_y - 2.5, 0]  # Position it further below the array

    # --- Remove final explanation ---
    _remove_last_explanation(wait_after=0.5)  # Remove before showing final text
    # --- End Remove final explanation ---

    actions.append(
        {"type": "create", "target": "sorted_text", "position": final_text_pos, "animation": "write", "duration": 1.5}
    )
    actions.append({"type": "wait", "duration": 3})

    # Combine everything into the final JSON structure
    full_json = {"metadata": metadata, "objects": objects, "actions": actions}

    return full_json


# Example usage (for testing):
if __name__ == "__main__":
    test_array = [8, 7, 5, 1, 9, 3]
    # test_array = [3, 1, 4, 1, 5, 9, 2, 6] # Test with duplicates and more elements
    # test_array = [1,2,3,4,5] # Already sorted
    # test_array = [5,4,3,2,1] # Reverse sorted
    # test_array = [] # Empty
    # test_array = [5] # Single element
    generated_json = generate_quicksort_json(test_array, title="QuicksortExample")
    import json
    # print(json.dumps(generated_json, indent=2))

    # Save to qs.json
    with open("qs.json", "w") as f:
        json.dump(generated_json, f, indent=2)
    print("Generated qs.json")
