"""Animation sequence for displaying images with cinematic effects."""

import json
import os
from typing import Any

from PIL import Image


def display_image(image_path: str) -> dict[str, Any]:
    """
    Generate a DSL configuration for displaying an image with a cinematic effect.

    The animation will:
    1. Start with an image that's very small (almost invisible)
    2. Grow it to full screen size
    3. Move it slowly from right to left to show the entire content
    4. Finally, scale it down to 2/3 of the screen size

    Args:
        image_path: Path to the image file

    Returns:
        Dictionary containing the DSL configuration
    """
    # Create a unique but readable ID based on the image path
    image_filename = os.path.basename(image_path)
    image_id = f"display_image_{os.path.splitext(image_filename)[0].replace(' ', '_').lower()}"

    # Initial tiny scale factor
    initial_scale = 0.01

    # Correct Manim dimensions
    screen_width_px = 1920
    screen_height_px = 1080
    manim_frame_width = 14.2222  # Correct Manim's coordinate system width
    manim_frame_height = 8.0  # Correct <PERSON><PERSON>'s coordinate system height

    # Pixel to Manim unit conversion factors
    px_to_manim_x = manim_frame_width / screen_width_px
    px_to_manim_y = manim_frame_height / screen_height_px

    # Get image dimensions to calculate appropriate scaling
    try:
        with Image.open(image_path) as img:
            img_width_px, img_height_px = img.size
            aspect_ratio = img_width_px / img_height_px

            # Convert image dimensions to Manim units
            img_width_manim = img_width_px * px_to_manim_x
            img_height_manim = img_height_px * px_to_manim_y

            print(f"Image dimensions: {img_width_px}x{img_height_px} pixels")
            print(f"Image dimensions in Manim units: {img_width_manim:.2f}x{img_height_manim:.2f}")
            print(f"Aspect ratio: {aspect_ratio:.2f}")
    except Exception as e:
        print(f"Warning: Could not determine image dimensions: {e}")
        print("Using default scaling factors")
        # Default to a typical aspect ratio if image can't be read
        aspect_ratio = 16 / 9
        img_width_manim = manim_frame_width
        img_height_manim = manim_frame_height / (16 / 9)

    # Calculate scaling factors to fill the screen
    # For Manim, we need to calculate how much to scale the image to fill the frame
    if aspect_ratio >= screen_width_px / screen_height_px:  # Wider than the screen
        # Scale based on width to fill screen width
        absolute_full_screen_scale = manim_frame_width / img_width_manim
    else:  # Taller than the screen aspect ratio
        # Scale based on height to fill screen height
        absolute_full_screen_scale = manim_frame_height / img_height_manim

    # Since we're starting at the initial_scale, we need to calculate how much to grow
    # to reach the full screen size
    full_screen_scale = absolute_full_screen_scale / initial_scale

    # Calculate 2/3 size relative to full screen
    final_scale_relative_to_full = 2 / 3

    # Calculate pan positions based on the actual frame width
    # We want to show the entire width of the image by panning from right to left
    # For very wide images, we need to pan more to show all content
    frame_half_width = manim_frame_width / 2

    # Pan parameters based on image dimensions and aspect ratio
    if aspect_ratio > 2.0:  # Very wide images
        pan_right = frame_half_width * 0.9  # 90% of half-width to the right
        pan_left = -frame_half_width * 0.9  # 90% of half-width to the left
    elif aspect_ratio > 16 / 9:  # Moderately wide
        pan_right = frame_half_width * 0.8  # 80% of half-width to the right
        pan_left = -frame_half_width * 0.8  # 80% of half-width to the left
    else:  # Standard or tall images
        pan_right = frame_half_width * 0.7  # 70% of half-width to the right
        pan_left = -frame_half_width * 0.7  # 70% of half-width to the left

    print(f"Frame dimensions: {manim_frame_width:.2f}x{manim_frame_height:.2f} Manim units")
    print(f"Pan right position: {pan_right:.2f}")
    print(f"Pan left position: {pan_left:.2f}")

    dsl = {
        "metadata": {
            "title": "ImageDisplayAnimation",
            "author": "DSL Generator",
            "resolution": "1080p",
            "background_color": "BLACK",
            "estimated_duration": 10,
        },
        "objects": [
            {
                "id": image_id,
                "type": "image",
                "properties": {
                    "file_path": image_path
                    # Height will be controlled through animations
                },
            }
        ],
        "actions": [
            # Create the image already at tiny size (0.01 scale)
            {
                "type": "create",
                "target": image_id,
                "position": [0, 0, 0],  # Center of the screen
                "animation": "fade_in",
                "duration": 0.5,
                "properties": {
                    "scale": initial_scale  # Start with tiny scale directly
                },
            },
            # Brief pause
            {"type": "wait", "duration": 0.5},
            # Grow to full screen size - this is a RELATIVE scaling
            {
                "type": "transform",
                "target": image_id,
                "properties": {
                    "scale": full_screen_scale  # Relative scale from initial_scale to target full size
                },
                "duration": 1.5,
                "easing": "smooth",
            },
            # Move to right side of screen to prepare for panning
            {
                "type": "move_to",
                "target": image_id,
                "position": [pan_right, 0, 0],  # Right side of screen, adapted to frame width
                "duration": 0.1,
            },
            # Pan from right to left to show full content
            {
                "type": "move_to",
                "target": image_id,
                "position": [pan_left, 0, 0],  # Left side of screen, adapted to frame width
                "duration": 10.0,
                "path": "line",  # Use straight line path for smooth panning
            },
            # Move back to center
            {
                "type": "move_to",
                "target": image_id,
                "position": [0, 0, 0],  # Center of screen
                "duration": 1.0,
                "path": "line",
            },
            # Scale down to 2/3 screen size - this is RELATIVE to full screen size
            {
                "type": "transform",
                "target": image_id,
                "properties": {
                    "scale": final_scale_relative_to_full  # Scale relative to full screen size
                },
                "duration": 1.0,
                "easing": "smooth",
            },
            # Final pause to view the image
            {"type": "wait", "duration": 2.0},
        ],
    }

    return dsl


def generate_dsl_file(image_path: str, output_file: str = None) -> str:
    """
    Generate a DSL JSON file for the image display animation.

    Args:
        image_path: Path to the image file
        output_file: Optional path to save the output JSON file
                     If not provided, a default name based on the image will be used

    Returns:
        Path to the generated JSON file
    """
    dsl_config = display_image(image_path)

    if output_file is None:
        image_filename = os.path.basename(image_path)
        base_name = os.path.splitext(image_filename)[0].replace(" ", "_").lower()
        output_file = f"image_display_{base_name}.json"

    with open(output_file, "w") as f:
        json.dump(dsl_config, f, indent=2)

    return output_file


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        image_path = sys.argv[1]
        output_path = sys.argv[2] if len(sys.argv) > 2 else None
        json_path = generate_dsl_file(image_path, output_path)
        print(f"DSL configuration generated and saved to: {json_path}")
    else:
        print("Usage: python -m dsl.animation_sequences.image_display <image_path> [output_file]")
