import json
from enum import Enum
from typing import Any, Literal, Optional, Union

from pydantic import BaseModel, field_validator, model_validator


# 枚举类型定义
class Resolution(str, Enum):
    RES_720P = "720p"
    RES_1080P = "1080p"
    RES_1440P = "1440p"
    RES_2160P = "2160p"


class AnimationType(str, Enum):
    FADE_IN = "fade_in"
    FADE_OUT = "fade_out"
    DRAW = "draw"
    WRITE = "write"
    UNWRITE = "unwrite"
    GROW = "grow"
    SHRINK = "shrink"
    NONE = "none"


class Direction(str, Enum):
    UP = "UP"
    DOWN = "DOWN"
    LEFT = "LEFT"
    RIGHT = "RIGHT"
    IN = "IN"
    OUT = "OUT"
    TOP_LEFT = "TOP_LEFT"
    TOP_RIGHT = "TOP_RIGHT"
    BOTTOM_LEFT = "BOTTOM_LEFT"
    BOTTOM_RIGHT = "BOTTOM_RIGHT"
    CENTER = "CENTER"


class Edge(str, Enum):
    LEFT = "LEFT"
    RIGHT = "RIGHT"
    TOP = "TOP"
    BOTTOM = "BOTTOM"
    CENTER_X = "CENTER_X"
    CENTER_Y = "CENTER_Y"


class LayoutType(str, Enum):
    HORIZONTAL = "horizontal"
    VERTICAL = "vertical"


class GraphMode(str, Enum):
    EDGES = "edges"
    CENTERS = "centers"


class DistributionDirection(str, Enum):
    HORIZONTAL = "HORIZONTAL"
    VERTICAL = "VERTICAL"


class Alignment(str, Enum):
    LEFT = "left"
    CENTER = "center"
    RIGHT = "right"


class CameraActionType(str, Enum):
    ZOOM = "zoom"
    PAN = "pan"
    MOVE_TO = "move_to"
    RESET = "reset"


class EasingType(str, Enum):
    LINEAR = "linear"
    SMOOTH = "smooth"
    ELASTIC = "elastic"


class PathType(str, Enum):
    LINE = "line"
    ARC = "arc"


class ReplaceAnimation(str, Enum):
    TRANSFORM = "transform"
    FADE = "fade"


# 基础模型


class Position(BaseModel):
    x: float
    y: float
    z: float = 0

    def to_list(self) -> list[float]:
        return [self.x, self.y, self.z]

    @classmethod
    def from_list(cls, pos_list: list[float]) -> "Position":
        if len(pos_list) == 2:
            return cls(x=pos_list[0], y=pos_list[1])
        return cls(x=pos_list[0], y=pos_list[1], z=pos_list[2])


class ScaleValue(BaseModel):
    x: Optional[float] = None
    y: Optional[float] = None
    z: Optional[float] = None
    uniform: Optional[float] = None

    @classmethod
    def from_value(cls, value: Union[float, list[float]]) -> "ScaleValue":
        if isinstance(value, (int, float)):
            return cls(uniform=float(value))
        elif isinstance(value, list):
            if len(value) == 1:
                return cls(uniform=value[0])
            elif len(value) == 2:
                return cls(x=value[0], y=value[1])
            else:
                return cls(x=value[0], y=value[1], z=value[2])
        return cls()


class Range(BaseModel):
    min: float
    max: float
    step: Optional[float] = None

    @classmethod
    def from_list(cls, range_list: list[float]) -> "Range":
        if len(range_list) == 2:
            return cls(min=range_list[0], max=range_list[1])
        return cls(min=range_list[0], max=range_list[1], step=range_list[2])


class Layout(BaseModel):
    type: LayoutType
    buffer: float = 0.25
    alignment: Optional[Direction] = None


class ReferenceFrame(BaseModel):
    start_edge_of: str  # 对象ID或场景锚点常量
    end_edge_of: str  # 对象ID或场景锚点常量
    mode: Literal["edges", "centers"] = "centers"


# 对象定义模型


class BaseObjectProperties(BaseModel):
    """所有对象共有的基础属性"""

    color: Optional[str] = "WHITE"
    z_index: Optional[int] = 0


# 形状属性
class ShapeProperties(BaseObjectProperties):
    """基本形状的共有属性"""

    fill_opacity: Optional[float] = 1.0
    stroke_width: Optional[float] = 4
    stroke_color: Optional[str] = "WHITE"


class CircleProperties(ShapeProperties):
    type: Literal["circle"] = "circle"
    radius: Optional[float] = 1.0


class SquareProperties(ShapeProperties):
    type: Literal["square"] = "square"
    side_length: Optional[float] = 2.0


class RectangleProperties(ShapeProperties):
    type: Literal["rectangle"] = "rectangle"
    width: Optional[float] = 2.0
    height: Optional[float] = 4.0


class TriangleProperties(ShapeProperties):
    type: Literal["triangle"] = "triangle"
    pass


class LineProperties(ShapeProperties):
    type: Literal["line"] = "line"
    start: Optional[list[float]] = [0, 0, 0]
    end: Optional[list[float]] = [1, 0, 0]

    @field_validator("start", "end")
    @classmethod
    def validate_position(cls, v: Optional[list[float]]) -> Optional[list[float]]:
        if v is not None and len(v) < 2:
            raise ValueError("Position must have at least 2 coordinates")
        return v


class ArrowProperties(LineProperties):
    type: Literal["arrow"] = "arrow"
    pass


class DotProperties(ShapeProperties):
    type: Literal["dot"] = "dot"
    radius: Optional[float] = 0.1


class StarProperties(ShapeProperties):
    type: Literal["star"] = "star"
    n: Optional[int] = 5
    radius: Optional[float] = 1.0


# 文本属性
class TextProperties(BaseObjectProperties):
    type: Literal["text"] = "text"
    content: str
    font_size: Optional[float] = 48
    alignment: Optional[Alignment] = Alignment.CENTER


class TexProperties(BaseObjectProperties):
    type: Literal["tex"] = "tex"
    content: str
    font_size: Optional[float] = 48


class MathTexProperties(TexProperties):
    type: Literal["math_tex"] = "math_tex"
    pass


# Code block properties
class CodeBackgroundConfig(BaseModel):
    buff: Optional[float] = 0.3
    fill_color: Optional[str] = "#222"
    stroke_color: Optional[str] = "WHITE"
    stroke_width: Optional[float] = 1
    corner_radius: Optional[float] = 0.2
    fill_opacity: Optional[float] = 1.0


class CodeParagraphConfig(BaseModel):
    font: Optional[str] = "Monospace"
    font_size: Optional[float] = 24
    line_spacing: Optional[float] = 0.5
    disable_ligatures: Optional[bool] = True


class CodeProperties(BaseObjectProperties):
    type: Literal["code"] = "code"
    code_string: Optional[str] = None
    code_file: Optional[str] = None
    language: Optional[str] = None
    formatter_style: Optional[str] = "vim"
    tab_width: Optional[int] = 4
    add_line_numbers: Optional[bool] = True
    line_numbers_from: Optional[int] = 1
    background: Optional[Literal["rectangle", "window"]] = "rectangle"
    background_config: Optional[CodeBackgroundConfig] = None
    paragraph_config: Optional[CodeParagraphConfig] = None

    @model_validator(mode="before")
    @classmethod
    def check_code_source(cls, values: Any) -> Any:
        if isinstance(values, dict):
            if not values.get("code_string") and not values.get("code_file"):
                raise ValueError("Either code_string or code_file must be provided for code type")
            if values.get("code_string") and values.get("code_file"):
                raise ValueError("Provide either code_string or code_file, not both")
        return values


# 图像和图表属性
class ImageProperties(BaseObjectProperties):
    type: Literal["image"] = "image"
    file_path: str
    scale: Optional[float] = None
    height: Optional[float] = None


class GraphProperties(BaseObjectProperties):
    type: Literal["graph"] = "graph"
    x_range: list[float]
    y_range: list[float]
    x_length: Optional[float] = None
    y_length: Optional[float] = None
    x_label: Optional[str] = None
    y_label: Optional[str] = None
    tips: Optional[bool] = False

    @field_validator("x_range", "y_range")
    @classmethod
    def validate_range(cls, v: list[float]) -> list[float]:
        if len(v) < 2 or len(v) > 3:
            raise ValueError("Range must have 2 or 3 values [min, max, step]")
        return v


class CoordinateSystemProperties(GraphProperties):
    type: Literal["coord_system", "coordinate_system"] = "coord_system"  # Allow both keys
    include_numbers: Optional[bool] = False
    axis_config: Optional[dict[str, Any]] = None


# 组合对象属性
class GroupProperties(BaseObjectProperties):
    type: Literal["group"] = "group"
    members: list[str]
    layout: Optional[Layout] = None

    @model_validator(mode="after")
    def check_members(self) -> "GroupProperties":
        if not self.members or len(self.members) == 0:
            raise ValueError("Group must have at least one member")
        return self


# Define the Union of all possible property types
PropertyTypes = Union[
    CircleProperties,
    SquareProperties,
    RectangleProperties,
    TriangleProperties,
    LineProperties,
    ArrowProperties,
    DotProperties,
    StarProperties,
    TextProperties,
    TexProperties,
    MathTexProperties,
    ImageProperties,
    GraphProperties,
    CoordinateSystemProperties,
    GroupProperties,
    CodeProperties,
]


# 对象定义
class ObjectDefinition(BaseModel):
    id: str
    type: str
    properties: PropertyTypes

    @field_validator("id")
    @classmethod
    def validate_id(cls, v: str) -> str:
        if not v or not isinstance(v, str) or " " in v:
            raise ValueError("ID must be a non-empty string without spaces")
        return v

    @model_validator(mode="before")
    @classmethod
    def check_properties_type(cls, data: Any) -> Any:
        if not isinstance(data, dict):
            return data  # Let standard validation handle non-dict cases

        obj_type = data.get("type")
        properties_data = data.get("properties")

        if obj_type is None or properties_data is None:
            return data  # Let standard validation raise errors if fields are missing

        # Map the string type name to the actual class
        type_to_property_class = {
            "circle": CircleProperties,
            "square": SquareProperties,
            "rectangle": RectangleProperties,
            "triangle": TriangleProperties,
            "line": LineProperties,
            "arrow": ArrowProperties,
            "dot": DotProperties,
            "star": StarProperties,
            "text": TextProperties,
            "tex": TexProperties,
            "math_tex": MathTexProperties,
            "image": ImageProperties,
            "graph": GraphProperties,
            "coord_system": CoordinateSystemProperties,
            "coordinate_system": CoordinateSystemProperties,  # Alias
            "group": GroupProperties,
            "code": CodeProperties,
        }

        if obj_type not in type_to_property_class:
            # Let standard validation handle unknown types later
            # We could raise here, but it's cleaner to let Pydantic do it
            return data

        expected_class = type_to_property_class[obj_type]

        # If properties are already the correct type (e.g., from nested models),
        # or not a dict, don't interfere
        if not isinstance(properties_data, dict):
            return data

        try:
            # Validate the properties dict against the specific expected class
            # This will apply the correct defaults (like radius=0.1 for Dot)
            validated_properties = expected_class.model_validate(properties_data)

            # Update the original data dict with the validated properties model instance
            data["properties"] = validated_properties

            # Optionally add the 'type' discriminator back into the properties for consistency
            # although it's not strictly needed for parsing anymore.
            # validated_properties.type = obj_type # Assuming type field exists

        except Exception:
            # If validation fails here, wrap the error or let standard validation catch it.
            # For simplicity, we let standard validation catch it by returning original data.
            # print(f"Pre-validation failed for {obj_type}: {e}")
            pass

        return data


# 动作模型


class BaseAction(BaseModel):
    type: str
    duration: Optional[float] = 1.0


class CreateAction(BaseAction):
    type: Literal["create"]
    target: str
    animation: Optional[AnimationType] = AnimationType.FADE_IN
    position: Optional[list[float]] = None

    @field_validator("position")
    @classmethod
    def validate_position(cls, v: Optional[list[float]]) -> Optional[list[float]]:
        if v is not None and len(v) < 2:
            raise ValueError("Position must have at least 2 coordinates")
        return v


class RemoveAction(BaseAction):
    type: Literal["remove"]
    target: str
    animation: Optional[AnimationType] = AnimationType.FADE_OUT


class TransformAction(BaseAction):
    type: Literal["transform"]
    target: str
    properties: dict[str, Any]
    easing: Optional[EasingType] = EasingType.SMOOTH

    @model_validator(mode="after")
    def validate_properties(self) -> "TransformAction":
        valid_props = {"position", "scale", "rotation", "color", "fill_opacity"}
        for key in self.properties.keys():
            if key not in valid_props:
                raise ValueError(f"Invalid transform property: {key}")
        return self


class MoveToAction(BaseAction):
    type: Literal["move_to"]
    target: str
    position: list[float]
    path: Optional[PathType] = PathType.LINE

    @field_validator("position")
    @classmethod
    def validate_position(cls, v: list[float]) -> list[float]:
        if len(v) < 2:
            raise ValueError("Position must have at least 2 coordinates")
        return v


class ArrangeAction(BaseAction):
    type: Literal["arrange"]
    target: str
    relative_to: str  # 对象ID或场景锚点常量
    direction: Direction
    buffer: Optional[float] = 0.25
    align_edge: Optional[Direction] = None


class AlignAction(BaseAction):
    type: Literal["align"]
    targets: list[str]
    reference_target: Optional[str] = None  # 对象ID或场景锚点常量
    edge: Edge

    @model_validator(mode="after")
    def validate_targets(self) -> "AlignAction":
        if not self.targets or len(self.targets) < 1:
            raise ValueError("Align action must have at least one target")
        return self


class DistributeAction(BaseAction):
    type: Literal["distribute"]
    targets: list[str]
    direction: DistributionDirection
    spacing: Optional[float] = None
    reference_frame: Optional[ReferenceFrame] = None

    @model_validator(mode="after")
    def validate_targets(self) -> "DistributeAction":
        if not self.targets or len(self.targets) < 2:
            raise ValueError("Distribute action must have at least two targets")
        return self


class HighlightAction(BaseAction):
    type: Literal["highlight"]
    target: str
    color: Optional[str] = "YELLOW"


class FocusAction(BaseAction):
    type: Literal["focus"]
    target: str
    scale_factor: Optional[float] = 1.2
    opacity_factor: Optional[float] = 0.5


class IndicateAction(BaseAction):
    type: Literal["indicate"]
    target: str
    scale_factor: Optional[float] = 1.1
    color: Optional[str] = "YELLOW"


class WaitAction(BaseAction):
    type: Literal["wait"]
    duration: float


class CameraAction(BaseAction):
    type: Literal["camera"]
    action: CameraActionType
    scale: Optional[float] = None  # 用于zoom
    position: Optional[list[float]] = None  # 用于pan/move_to

    @model_validator(mode="after")
    def validate_action_params(self) -> "CameraAction":
        if self.action == CameraActionType.ZOOM and self.scale is None:
            raise ValueError("Scale must be provided for zoom camera action")
        if self.action in [CameraActionType.PAN, CameraActionType.MOVE_TO] and (
            self.position is None or len(self.position) < 2
        ):
            raise ValueError("Position must be provided for pan/move_to camera action")
        return self


class ReplaceAction(BaseAction):
    type: Literal["replace"]
    source: str
    target: str
    animation: Optional[ReplaceAnimation] = ReplaceAnimation.TRANSFORM
    position_match: Optional[bool] = True


class AnimateGroupAction(BaseAction):
    type: Literal["animate_group"]
    actions: list[
        Union[
            TransformAction,
            MoveToAction,
            CreateAction,
            RemoveAction,
            ArrangeAction,
            HighlightAction,
            FocusAction,
            IndicateAction,
            AlignAction,
            DistributeAction,
            ReplaceAction,
        ]
    ]

    @model_validator(mode="after")
    def validate_actions(self) -> "AnimateGroupAction":
        if not self.actions or len(self.actions) < 1:
            raise ValueError("Animate group must have at least one action")

        # 禁止在animate_group中嵌套wait动作
        for action in self.actions:
            if action.type == "wait":
                raise ValueError("Wait actions are not allowed within animate_group")

        # 禁止嵌套animate_group
        for action in self.actions:
            if action.type == "animate_group":
                raise ValueError("Nested animate_group actions are not allowed")

        return self


class PlotFunctionAction(BaseAction):
    type: Literal["plot_function"]
    target: str  # 必须是已创建的 graph/coord_system
    function: str
    color: Optional[str] = None
    x_range: Optional[list[float]] = None
    animation: Optional[Literal["draw", "create", "none"]] = "draw"


class AddPointAction(BaseAction):
    type: Literal["add_point"]
    target: str  # 必须是已创建的 graph/coord_system
    position: list[float]
    color: Optional[str] = "WHITE"
    label: Optional[str] = None
    animation: Optional[Literal["fade_in", "grow", "none"]] = "fade_in"

    @field_validator("position")
    @classmethod
    def validate_position(cls, v: list[float]) -> list[float]:
        if len(v) < 2:
            raise ValueError("Position must have at least 2 coordinates")
        return v


# 元数据模型
class Metadata(BaseModel):
    title: str
    author: str
    resolution: Optional[Resolution] = Resolution.RES_1080P
    estimated_duration: Optional[float] = None
    background_color: Optional[str] = "BLACK"

    @field_validator("title")
    @classmethod
    def validate_title(cls, v: str) -> str:
        # 确保title是合法的Python类名
        if not v.isidentifier():
            raise ValueError(f"Title must be a valid Python identifier, got: {v}")
        return v


# 创建ActionNode类型的联合类型
ActionType = Union[
    CreateAction,
    RemoveAction,
    TransformAction,
    MoveToAction,
    ArrangeAction,
    AlignAction,
    DistributeAction,
    HighlightAction,
    FocusAction,
    IndicateAction,
    WaitAction,
    CameraAction,
    ReplaceAction,
    AnimateGroupAction,
    PlotFunctionAction,
    AddPointAction,
]


# DSL根模型
class ManimDSL(BaseModel):
    metadata: Metadata
    objects: Optional[list[ObjectDefinition]] = []
    actions: list[ActionType]

    @model_validator(mode="after")
    def validate_references(self) -> "ManimDSL":
        # 构建对象ID集合
        object_ids = {obj.id for obj in self.objects}

        # 检查组的成员是否在对象定义中
        for obj in self.objects:
            if obj.type == "group" and isinstance(obj.properties, GroupProperties):
                for member_id in obj.properties.members:
                    if member_id not in object_ids:
                        raise ValueError(f"Group {obj.id} references undefined object: {member_id}")

        # 验证所有action引用的对象都存在
        for action in self.actions:
            self._validate_action_references(action, object_ids)

        # 验证"先创建后使用"原则
        self._validate_create_before_use()

        return self

    def _validate_action_references(self, action: BaseAction, object_ids: set) -> None:
        """验证动作引用的对象都存在于对象定义中"""
        if hasattr(action, "target") and getattr(action, "target", None) is not None:
            target = getattr(action, "target")
            # 排除场景锚点常量
            if not target.startswith(("ORIGIN", "SCREEN_")):
                if target not in object_ids:
                    raise ValueError(f"{action.type} action references undefined object: {target}")

        if hasattr(action, "targets") and isinstance(getattr(action, "targets", None), list):
            targets = getattr(action, "targets")
            for target_id in targets:
                # 排除场景锚点常量
                if not target_id.startswith(("ORIGIN", "SCREEN_")):
                    if target_id not in object_ids:
                        raise ValueError(f"{action.type} action references undefined object: {target_id}")

        if hasattr(action, "source") and getattr(action, "source", None) is not None:
            source = getattr(action, "source")
            if source not in object_ids:
                raise ValueError(f"{action.type} action references undefined source object: {source}")

        # 检查animate_group内的动作
        if action.type == "animate_group" and isinstance(action, AnimateGroupAction):
            for sub_action in action.actions:
                self._validate_action_references(sub_action, object_ids)

    def _validate_create_before_use(self) -> None:
        """验证对象在使用前已创建"""
        created_objects = set()

        for action in self.actions:
            # 如果是创建动作，将对象ID添加到已创建集合
            if action.type == "create" and isinstance(action, CreateAction):
                target_id = action.target
                created_objects.add(target_id)

                # 如果创建的是组，确保其所有成员都已经被创建
                for obj in self.objects:
                    if obj.id == target_id and obj.type == "group":
                        if isinstance(obj.properties, GroupProperties):
                            for member_id in obj.properties.members:
                                if member_id not in created_objects:
                                    raise ValueError(f"Group {obj.id} member {member_id} is not created before group")

            # 检查动作引用的对象是否已创建
            self._check_action_targets_created(action, created_objects)

    def _check_action_targets_created(self, action: BaseAction, created_objects: set) -> None:
        """检查动作引用的对象是否已创建"""
        # 创建动作不需要检查目标是否已创建
        if action.type == "create":
            if hasattr(action, "target") and getattr(action, "target", None) is not None:
                target = getattr(action, "target")
                created_objects.add(target)
            return

        # 检查单个目标
        if hasattr(action, "target") and getattr(action, "target", None) is not None:
            target = getattr(action, "target")
            # 排除场景锚点常量
            if not target.startswith(("ORIGIN", "SCREEN_")):
                if target not in created_objects:
                    raise ValueError(f"{action.type} action references object {target} before it's created")

        # 检查多个目标
        if hasattr(action, "targets") and isinstance(getattr(action, "targets", None), list):
            targets = getattr(action, "targets")
            for target_id in targets:
                # 排除场景锚点常量
                if not target_id.startswith(("ORIGIN", "SCREEN_")):
                    if target_id not in created_objects:
                        raise ValueError(f"{action.type} action references object {target_id} before it's created")

        # 检查source对象
        if hasattr(action, "source") and getattr(action, "source", None) is not None:
            source = getattr(action, "source")
            if source not in created_objects:
                raise ValueError(f"{action.type} action references source object {source} before it's created")

        # 检查animate_group内的动作
        if action.type == "animate_group" and isinstance(action, AnimateGroupAction):
            for sub_action in action.actions:
                self._check_action_targets_created(sub_action, created_objects)


class ManimDSLParser:
    @staticmethod
    def parse_from_json(json_str: str) -> ManimDSL:
        """从JSON字符串解析DSL"""
        try:
            data = json.loads(json_str)
            return ManimDSL.model_validate(data)
        except Exception as e:
            raise ValueError(f"Failed to parse DSL JSON: {str(e)}")

    @staticmethod
    def parse_from_file(file_path: str) -> ManimDSL:
        """从文件解析DSL"""
        try:
            with open(file_path, encoding="utf-8") as f:
                json_str = f.read()
            return ManimDSLParser.parse_from_json(json_str)
        except Exception as e:
            raise ValueError(f"Failed to parse DSL file {file_path}: {str(e)}")


if __name__ == "__main__":
    # 简单测试用例
    sample_dsl = """
    {
        "metadata": {
            "title": "SimpleExample",
            "author": "DSL Parser",
            "resolution": "1080p",
            "background_color": "BLACK"
        },
        "objects": [
            {
                "id": "my_circle",
                "type": "circle",
                "properties": {
                    "radius": 1,
                    "color": "BLUE",
                    "fill_opacity": 0.5
                }
            },
            {
                "id": "my_text",
                "type": "text",
                "properties": {
                    "content": "Hello Manim",
                    "font_size": 36,
                    "color": "WHITE"
                }
            }
        ],
        "actions": [
            {
                "type": "create",
                "target": "my_circle",
                "animation": "fade_in",
                "duration": 1,
                "position": [0, 0, 0]
            },
            {
                "type": "wait",
                "duration": 0.5
            },
            {
                "type": "create",
                "target": "my_text",
                "animation": "write",
                "duration": 1,
                "position": [0, 2, 0]
            },
            {
                "type": "wait",
                "duration": 1
            },
            {
                "type": "transform",
                "target": "my_circle",
                "properties": {
                    "color": "RED",
                    "scale": 1.5
                },
                "duration": 2
            },
            {
                "type": "wait",
                "duration": 2
            }
        ]
    }
    """

    try:
        dsl = ManimDSLParser.parse_from_json(sample_dsl)
        print(f"Successfully parsed DSL with {len(dsl.objects)} objects and {len(dsl.actions)} actions.")
        print(f"Title: {dsl.metadata.title}")
    except ValueError as e:
        print(f"Error: {e}")
