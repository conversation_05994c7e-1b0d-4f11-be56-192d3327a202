"""
转场效果库 - 提供多种经典视频转场特效
"""

import random
from typing import Callable, Optional

from loguru import logger
from manim import *

EPSILON = 1e-6


def all_vmobjects(obj: Mobject):
    # 递归检查是否所有 submojbects 都是 VMobject
    if isinstance(obj, (Group, VGroup)):
        return all(all_vmobjects(submob) for submob in obj.submobjects)
    if not isinstance(obj, VMobject):
        logger.warning(f"all_vmobjects: {obj} is not VMobject")
        return False
    for submob in obj.submobjects:
        if not all_vmobjects(submob):
            return False
    return True


class TransitionEffects:
    """转场效果类，提供各种经典的视频转场特效"""

    @staticmethod
    def fade_out_in(old_mobj: Mobject, new_mobj: Optional[Mobject] = None, run_time: float = 1.0) -> list[Animation]:
        """经典淡入淡出转场"""
        animations = [FadeOut(old_mobj, run_time=run_time / 2)]
        if new_mobj:
            animations.append(FadeIn(new_mobj, run_time=run_time / 2))
        return animations

    @staticmethod
    def slide_left(old_mobj: Mobject, new_mobj: Optional[Mobject] = None, run_time: float = 1.0) -> list[Animation]:
        """向左滑动转场"""
        animations = [old_mobj.animate.shift(LEFT * config.frame_width).set_run_time(run_time / 2)]
        if new_mobj:
            new_mobj.shift(RIGHT * config.frame_width)
            animations.append(new_mobj.animate.shift(LEFT * config.frame_width).set_run_time(run_time / 2))
        return animations

    @staticmethod
    def slide_right(old_mobj: Mobject, new_mobj: Optional[Mobject] = None, run_time: float = 1.0) -> list[Animation]:
        """向右滑动转场"""
        animations = [old_mobj.animate.shift(RIGHT * config.frame_width).set_run_time(run_time / 2)]
        if new_mobj:
            new_mobj.shift(LEFT * config.frame_width)
            animations.append(new_mobj.animate.shift(RIGHT * config.frame_width).set_run_time(run_time / 2))
        return animations

    @staticmethod
    def slide_up(old_mobj: Mobject, new_mobj: Optional[Mobject] = None, run_time: float = 1.0) -> list[Animation]:
        """向上滑动转场"""
        animations = [old_mobj.animate.shift(UP * config.frame_height).set_run_time(run_time / 2)]
        if new_mobj:
            new_mobj.shift(DOWN * config.frame_height)
            animations.append(new_mobj.animate.shift(UP * config.frame_height).set_run_time(run_time / 2))
        return animations

    @staticmethod
    def slide_down(old_mobj: Mobject, new_mobj: Optional[Mobject] = None, run_time: float = 1.0) -> list[Animation]:
        """向下滑动转场"""
        animations = [old_mobj.animate.shift(DOWN * config.frame_height).set_run_time(run_time / 2)]
        if new_mobj:
            new_mobj.shift(UP * config.frame_height)
            animations.append(new_mobj.animate.shift(DOWN * config.frame_height).set_run_time(run_time / 2))
        return animations

    @staticmethod
    def zoom_out_in(old_mobj: Mobject, new_mobj: Optional[Mobject] = None, run_time: float = 1.0) -> list[Animation]:
        """缩放转场 - 先缩小消失，再放大出现"""
        animations = [old_mobj.animate.scale(EPSILON).set_opacity(0).set_run_time(run_time / 2)]
        if new_mobj:
            new_mobj.scale(EPSILON).set_opacity(0)
            animations.append(new_mobj.animate.scale(1 / EPSILON).set_opacity(1).set_run_time(run_time / 2))
        return animations

    @staticmethod
    def transformation(old_mobj: Mobject, new_mobj: Mobject, run_time: float = 1.0) -> list[Animation]:
        """旋转转场 - 使用 animate 避免动画冲突"""
        if not new_mobj:
            logger.warning("transformation transition requires new_mobj")
            return [FadeOut(old_mobj)]

        old_all_vmobjects = all_vmobjects(old_mobj)
        new_all_vmobjects = all_vmobjects(new_mobj)
        if not old_all_vmobjects or not new_all_vmobjects:
            logger.warning("transformation transition requires VMobject: {old_all_vmobjects}, {new_all_vmobjects}")
            return TransitionEffects.wipe_left(old_mobj, new_mobj, run_time)

        animations = [Transform(old_mobj, new_mobj, run_time=run_time)]
        return animations

    @staticmethod
    def wipe_left(old_mobj: Mobject, new_mobj: Optional[Mobject] = None, run_time: float = 1.0) -> list[Animation]:
        """从左到右擦除转场"""
        animations = [old_mobj.animate.shift(LEFT * config.frame_width)] if old_mobj else []
        if new_mobj:
            animations.append(FadeIn(new_mobj, run_time=run_time))
        return animations

    @staticmethod
    def flip_horizontal(
        old_mobj: Mobject, new_mobj: Optional[Mobject] = None, run_time: float = 1.0
    ) -> list[Animation]:
        """水平翻转转场"""
        animations = [
            old_mobj.animate.stretch(0, 0).set_rate_func(rate_functions.linear).set_run_time(run_time / 2)
        ]  # 水平压缩到0
        if new_mobj:
            original_new_mobj = new_mobj.copy()
            new_mobj.stretch(0, 0)  # 初始状态水平压缩
            animations.append(
                Transform(new_mobj, original_new_mobj, rate_func=rate_functions.linear).set_run_time(run_time / 2)
            )  # 恢复正常
        return [Succession(*animations)]


class TransitionManager:
    """转场管理器，负责选择和执行转场效果"""

    # 所有可用的转场效果
    TRANSITION_EFFECTS = {
        "fade": TransitionEffects.fade_out_in,
        "slide_left": TransitionEffects.slide_left,
        "slide_right": TransitionEffects.slide_right,
        "slide_up": TransitionEffects.slide_up,
        "slide_down": TransitionEffects.slide_down,
        "zoom": TransitionEffects.zoom_out_in,
        "transformation": TransitionEffects.transformation,
        "wipe_left": TransitionEffects.wipe_left,
        # "flip": TransitionEffects.flip_horizontal,
    }

    @classmethod
    def get_random_transition(cls) -> str:
        """随机选择一个转场效果"""
        return random.choice(list(cls.TRANSITION_EFFECTS.keys()))

    @classmethod
    def get_transition_function(cls, transition_name: str) -> Callable:
        """根据名称获取转场函数"""
        return cls.TRANSITION_EFFECTS.get(transition_name, TransitionEffects.fade_out_in)

    @classmethod
    def apply_transition(
        cls,
        scene: Scene,
        old_mobj: Mobject,
        new_mobj: Optional[Mobject] = None,
        transition_type: Optional[str] = None,
        run_time: float = 1.0,
    ):
        """应用转场效果"""
        if not transition_type:
            transition_type = cls.get_random_transition()

        logger.info(f"应用转场效果: {transition_type}")

        transition_func = cls.get_transition_function(transition_type)
        animations = transition_func(old_mobj, new_mobj, run_time)

        if animations:
            # # 如果有新对象，确保它被添加到场景中
            # if new_mobj and new_mobj not in scene.mobjects:
            #     scene.add(new_mobj)
            logger.info(animations)
            scene.play(*animations)
