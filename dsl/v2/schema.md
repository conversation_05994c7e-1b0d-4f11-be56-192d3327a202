# dsl/v2/schema.md (MVP)

这是 Manim DSL v2 MVP 的 JSO<PERSON> Schema 定义。

```json
{
  "schema_version": "2.0-mvp",
  "metadata": {
    "title": "string (Python Class Name)",
    "author": "string",
    "background_color": "string (Manim Color, default: BLACK)"
  },
  "objects": [
    // MVP: 只需支持 text 和 image
    {
      "id": "string (Unique ID, snake_case)",
      "type": "text",
      "properties": {
        "content": "string",
        "font_size": "number (Optional)",
        "color": "string (Optional, Manim Color)"
        // 初始不需要太多样式
      }
    },
    {
      "id": "string (Unique ID, snake_case)",
      "type": "image",
      "properties": {
        "file_path": "string",
        "height": "number (Optional, Manim units)"
      }
    }
    // 其他类型 (shape, code, group 等) 暂缓
  ],
  "actions": [
    // MVP: 核心是 layout_comparison, 可能加 wait
    {
      "type": "layout_comparison",
      "left_content_id": "string (ID from objects)",
      "right_content_id": "string (ID from objects)",
      "left_title": "string (Optional)",
      "right_title": "string (Optional)",
      "divider": "boolean (Optional, default: true)",
      "padding": "number (Optional, default: 0.5)",
      "animation": "string (Optional, e.g., 'fade_in', 'slide_from_sides', 'none', default: 'fade_in')",
      "duration": "number (Optional, default: 1.0)"
    },
    {
        "type": "wait",
        "duration": "number"
    }
    // 其他 action (create, transform, move_to, remove 等) 暂缓
  ]
}
```
