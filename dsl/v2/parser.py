# dsl/v2/parser.py
import json
import os
from typing import Any

from loguru import logger  # 使用 loguru


def parse_dsl_file(file_path: str) -> dict[str, Any]:
    """
    解析 DSL JSON 文件并返回其内容 (使用 loguru)。

    Args:
        file_path: DSL JSON 文件的路径。

    Returns:
        包含 DSL 内容的 Python 字典。

    Raises:
        FileNotFoundError: 如果文件不存在。
        json.JSONDecodeError: 如果文件不是有效的 JSON。
        ValueError: 如果缺少必需的顶级键。
        Exception: 其他潜在的文件读取或处理错误。
    """
    try:
        if os.path.exists(str(file_path)):
            with open(file_path, encoding="utf-8") as f:
                data = json.load(f)
        else:
            if isinstance(file_path, str):
                data = json.load(file_path)  # treat as dsl content
            else:
                data = file_path

        # 可以在这里添加更多验证，例如检查 objects 和 actions 是否为列表等
        if not isinstance(data.get("actions"), list):
            logger.warning("DSL 中的 'actions' 字段类型不是列表。将尝试继续，但这可能导致后续错误。")

        return data
    except FileNotFoundError:
        logger.error(f"DSL 文件未找到: {file_path}")
        raise
    except json.JSONDecodeError as e:
        logger.error(f"解析 DSL 文件时发生 JSON 错误: {file_path} - {e}")
        raise
    except ValueError:  # 捕获我们自己抛出的 ValueError
        # 错误已在上面记录，直接重新抛出
        raise
    except Exception as e:
        logger.error(f"读取或处理 DSL 文件时发生未知错误: {file_path} - {e}")
        raise
