# DSL v2: 动画描述语言到 Manim 代码生成器

## 简介 (Introduction)

`dsl/v2` 目录包含将自定义的动画领域特定语言 (DSL) 解析并生成对应 Manim Python 动画代码的核心逻辑。该版本 (v2) 旨在通过模块化和基于访问者模式的设计，提高代码的可扩展性和可维护性。其主要目标是将用户友好的 DSL 描述转换为可执行的 Manim 场景代码。

## 核心概念 (Core Concepts)

*   **DSL (Domain-Specific Language):** 一种专门设计的、用于描述动画场景、对象和动作的语言。它的语法定义可以在 `schema.md` 文件中找到（或未来会定义在那里）。目的是让用户能以更直观的方式创建动画，而无需直接编写复杂的 Manim 代码。
*   **AST (Abstract Syntax Tree):** DSL 文本首先被解析成抽象语法树。AST 是一种树状的数据结构，表示了 DSL 代码的结构和语义，是后续代码生成的基础。AST 节点的定义位于 `ast_nodes.py`。
*   **AST 构建器 (AST Builder):** `ast_builder.py` 文件负责将解析器（`parser.py`）产生的原始解析结果转换为我们在 `ast_nodes.py` 中定义的、结构更清晰的 AST 对象。
*   **访问者模式 (Visitor Pattern):** 这是 v2 代码生成的核心设计模式。通过遍历 AST 的每个节点，并为不同类型的节点调用专门的"访问者"函数，来实现代码的生成。这种模式将节点的数据结构与其处理逻辑解耦。
*   **代码生成器 (Code Generator):** `code_generator.py` 中的 `CodeGenerator` 类是驱动整个代码生成过程的核心。它实现了 `NodeVisitor` 基类，负责遍历 AST，并根据节点类型动态地加载和调用位于 `visitors/` 目录下的相应访问者函数。
*   **节点访问者 (Node Visitors):** `visitors/` 目录包含了针对特定 AST 节点类型的处理逻辑。每个 `.py` 文件（如 `scene.py`, `wait.py`）通常包含一个或多个 `visit_xxx` 函数，这些函数接收 AST 节点和代码生成器实例作为输入，并负责生成对应的 Manim 代码片段。

## 代码结构 (Directory Structure)

```
dsl/v2/
├── __init__.py           # 包初始化文件
├── ast_nodes.py          # 定义 DSL 的抽象语法树 (AST) 节点类
├── parser.py             # 使用 lark-parser 解析 DSL 文本，生成初始解析树
├── ast_builder.py        # 将 Lark 的解析树转换为自定义的 AST 节点对象
├── code_generator.py     # 核心代码生成器类 (CodeGenerator)，实现访问者模式分发逻辑
├── dsl_to_manim.py       # 主脚本，整合解析、AST 构建和代码生成，将 DSL 文件转换为 Manim 脚本
├── schema.md             # DSL 语法的定义或描述（预期）
└── visitors/             # 包含所有节点访问者模块的目录
    ├── __init__.py       # 包初始化文件
    ├── scene.py          # 处理 SceneNode 的访问者 (生成场景类定义和 construct 方法)
    └── wait.py           # 处理 WaitNode 的访问者 (生成 self.wait(...) 代码)
    # ... (其他节点类型的访问者)
```

## 工作流程 (Workflow)

从 DSL 文本到最终 Manim 代码的典型流程如下：

1.  **输入:** 提供包含 DSL 描述的文本文件。
2.  **解析:** `dsl_to_manim.py` 调用 `parser.py` 中的解析器，将 DSL 文本解析成 Lark 解析树。
3.  **AST 构建:** `dsl_to_manim.py` 调用 `ast_builder.py` 中的 `ASTBuilder`，将 Lark 解析树转换为由 `ast_nodes.py` 中定义的类构成的 AST。
4.  **代码生成:**
    *   创建一个 `CodeGenerator` 实例 (`code_generator.py`)。
    *   调用 `code_generator.generate(root_ast_node)` 方法，传入 AST 的根节点。
5.  **节点访问:** `CodeGenerator` 遍历 AST：
    *   对于每个节点，它根据节点类型确定对应的访问者模块名称（例如 `SceneNode` -> `scene`）。
    *   动态导入 `visitors/` 目录下的相应模块 (例如 `import .visitors.scene`)。
    *   调用该模块中的 `visit_xxx` 函数 (例如 `visit_scene(generator, node, **kwargs)`)。
6.  **代码片段生成:** 每个 `visit_xxx` 函数生成该节点对应的 Manim Python 代码片段，并通过 `generator` 实例的方法（如 `_add_line`）添加到最终结果中。
7.  **组装与输出:** `CodeGenerator` 将所有生成的代码片段组装起来，`dsl_to_manim.py` 最终输出完整的、可执行的 Manim Python 脚本。

## 如何扩展 (How to Extend)

如果需要支持新的 DSL 元素或 Manim 功能：

1.  **定义 AST 节点:** 在 `ast_nodes.py` 中为新的 DSL 元素定义一个新的 `Node` 子类。
2.  **更新 DSL 语法:** （如果需要）修改 `parser.py` 中的 Lark 语法，以识别新的 DSL 结构。
3.  **更新 AST 构建器:** 修改 `ast_builder.py` 中的 `ASTBuilder`，使其能够处理新的 Lark 解析树节点，并创建你在步骤 1 中定义的新 AST 节点实例。
4.  **创建访问者模块:** 在 `dsl/v2/visitors/` 目录下创建一个新的 Python 文件，例如 `new_feature.py`。
5.  **实现访问者函数:** 在 `new_feature.py` 文件中，实现一个名为 `visit_newfeaturenode(generator: CodeGenerator, node: NewFeatureNode, **kwargs)` 的函数（函数名遵循 `visit_<小写节点名>` 格式）。这个函数需要包含生成对应 Manim 代码的逻辑，并使用 `generator._add_line()` 等方法添加代码。
6.  **完成!** `CodeGenerator` 会自动发现并调用你新创建的访问者函数，无需修改 `code_generator.py` 本身。

## 注意事项 (Notes)

*   **日志:** 代码广泛使用了 `loguru` 库进行日志记录，方便调试和追踪代码生成过程。可以通过调整日志级别来控制输出的详细程度。
*   **动态导入:** `CodeGenerator` 使用 `importlib` 动态加载访问者模块，这是实现插件式扩展的关键。
*   **类型提示:** 代码库使用了类型提示 (`typing`) 以提高可读性和健壮性，特别是在 `TYPE_CHECKING` 块中避免循环导入。
