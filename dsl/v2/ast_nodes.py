from typing import Any, Literal, Optional

from pydantic import BaseModel, Field


class Node(BaseModel):
    """AST 节点基类 (使用 Pydantic)"""

    pass


# --- 元数据节点 ---


class MetadataNode(Node):
    """表示 DSL metadata 部分"""

    title: str
    author: Optional[str] = None
    background_color: str = "BLACK"  # Manim 颜色字符串，未来可以加验证


# --- 动作节点 ---


class ActionNode(Node):
    """动作节点的基类"""

    type: str  # 由子类具体定义
    params: dict[str, Any] = Field(default_factory=dict, description="动作的额外参数")


# --- 场景根节点 ---


class SceneNode(Node):
    """表示整个 DSL 文件的根 AST 节点"""

    type: Literal["scene"] = "scene"
    schema_version: Optional[str] = None  # 可以从原始数据获取
    metadata: MetadataNode
    # 简化后的 actions 字段，不再使用复杂的 Union 类型
    actions: list[ActionNode] = Field(default_factory=list)
