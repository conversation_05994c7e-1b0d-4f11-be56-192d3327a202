"""
effect: |
    创建纯文本内容的动态展示动画，专门用于文字信息的呈现。支持清单、要点、步骤等文本内容的动态显示，
    包含渐变背景、发光字体效果、逐字显示动画等视觉特效，适合纯文本内容的精美展示。

use_cases:
    - 展示要点清单、任务列表、检查清单等文本内容
    - 创建纯文字的动态内容展示，如学习要点、工作计划等
    - 制作带有视觉特效的文本演示，突出重要信息
    - 文字内容的分步展示和强调

params:
  scene:
    type: FeynmanScene
    desc: Manim场景实例（由系统自动传入）
  title:
    type: str
    desc: 文本展示的主标题
    required: true
  items:
    type: List[Dict]
    desc: 文本项目列表，每个项目包含text、tags字段，颜色会自动分配
    default: null
  narration:
    type: str
    desc: 在内容显示时播放的语音旁白文本
    required: true
  id:
    type: str
    desc: 创建的Manim Mobject的唯一标识符
    default: None

dsl_examples:
  - type: animate_text_only
    params:
      title: "📚 学习要点总结"
      items:
        - text: "理论知识学习"
          tags: "#基础概念 #核心原理"
        - text: "实践操作练习"
          tags: "#动手实践 #技能提升"
        - text: "目标达成检验"
          tags: "#效果评估 #持续改进"
      narration: "这是我们学习过程中需要重点关注的三个要点。"
  - type: animate_text_only
    params:
      title: "🎯 工作计划清单"
      items:
        - text: "制定详细计划"
          tags: "#时间安排 #任务分解"
        - text: "高效执行任务"
          tags: "#专注投入 #质量保证"
        - text: "跟踪进度反馈"
          tags: "#进度监控 #及时调整"
        - text: "完成总结复盘"
          tags: "#成果总结 #经验提炼"
      narration: "高效工作需要遵循这四个关键步骤。"

notes:
  - 专门用于纯文本内容的动态展示，不涉及图片或视频
  - 支持最多5个文本项目的展示
  - 每个文本项包含主文本、标签，颜色自动分配
  - 具有发光字体效果和逐字显示动画
  - 适合要点总结、清单展示、步骤说明等文本场景
  - 使用渐变背景和动态特效提升文本展示的视觉效果
"""

from typing import TYPE_CHECKING, Optional

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene

from loguru import logger
from manim import *

from dsl.v2.themes.theme_manager import get_current_theme
from dsl.v2.themes.theme_utils import ThemeUtils
from utils.title_utils import create_title


def _get_theme_aware_colors():
    """
    根据当前主题获取适配的颜色配置

    Returns:
        dict: 包含各种界面元素颜色的字典
    """
    current_theme = get_current_theme()
    theme_name = current_theme.name.lower()

    if theme_name == "dark":
        # 暗色主题配色
        return {
            "container_fill": "#2a3541",  # 容器填充色
            "container_stroke": "#4a5568",  # 容器边框色
            "title_line": "#4a5568",  # 标题线条色
            "item_bg_fill": "#374151",  # 项目背景填充色
            "item_bg_stroke": "#4a5568",  # 项目背景边框色
            "main_text": "#f7fafc",  # 主文本色
            "check_color": "#4ade80",  # 勾选标记色
            "check_glow": "#00ff7f",  # 勾选发光色
        }
    else:
        # 默认/浅色主题配色
        return {
            "container_fill": WHITE,
            "container_stroke": WHITE,
            "title_line": WHITE,
            "item_bg_fill": WHITE,
            "item_bg_stroke": WHITE,
            "main_text": WHITE,
            "check_color": "#4ade80",
            "check_glow": "#00ff7f",
        }


def animate_text_only(
    scene: "FeynmanScene",
    title: str,
    items: Optional[list[dict[str, str]]] = None,
    narration: Optional[str] = None,
    id: Optional[str] = None,
) -> None:
    """
    创建纯文本内容的动态展示动画

    专门用于文字信息的呈现，支持清单、要点、步骤等文本内容的动态显示，
    包含发光字体效果、逐字显示动画等视觉特效。

    Args:
        scene: Manim场景实例
        title: 文本展示的主标题
        items: 文本项目列表，每个项目包含text、tags字段
        narration: 语音旁白文本
        id: 唯一标识符
    """
    logger.info(f"开始创建纯文本动态展示: {title}")

    # Create title object for transition support

    # Handle clear_current_mobject for transition support

    unique_id = id or f"text_only_{abs(hash(title)) % 10000}"

    # 获取主题相关颜色
    colors = _get_theme_aware_colors()

    # 默认清单项目（复刻HTML中的内容）
    if items is None:
        items = [
            {"text": "智能防晒霜", "tags": "#物理防晒 #SPF50+"},
            {"text": "偏光墨镜", "tags": "#UV400 #驾驶友好"},
            {"text": "便携风扇", "tags": "#静音 #长续航"},
            {"text": "降温冰袖", "tags": "#凉感面料 #防晒"},
        ]

    # 为每个项目分配默认颜色
    cyclic_colors = ThemeUtils.get_cyclic_colors(len(items))
    for i, item in enumerate(items):
        if "color" not in item or not item["color"]:
            item["color"] = cyclic_colors[i]

    # 创建简单背景 - 只保留一个背景框
    def create_simple_background():
        """创建简洁的渐变背景，适当减弱光晕"""
        bg = Rectangle(width=14, height=8, fill_opacity=0.5)
        bg.set_fill(color=[DARK_BLUE, DARK_BROWN], opacity=0.5)
        bg.move_to(ORIGIN)

        # 环境光效 - 调整数量和亮度
        for i in range(3):  # 减少光点数量
            glow = Circle(radius=1.5 + i * 0.3, fill_opacity=0.08 - i * 0.02)  # 降低透明度
            glow.set_fill(color=BLUE_C)
            glow.shift(UP * (2 + i * 0.5) + RIGHT * (i * 1.5 - 1.5))

        return bg

    # 创建主容器 - 适当留出边距
    def create_main_container(mobj):
        """创建主容器，适当留出边距"""

        # 主容器
        main_container = SurroundingRectangle(
            mobj,
            buff=LARGE_BUFF,
            corner_radius=0.4,
            fill_color=colors["container_fill"],
            fill_opacity=0.3,
            stroke_color=colors["container_stroke"],
            stroke_width=1.2,
            stroke_opacity=0.2,
        )

        return main_container

    # 创建标题对象
    initial_title, title_text = create_title(title, scene=scene)

    # 添加标题发光效果
    title_glow = title_text.copy()
    title_glow.set_color("#87CEEB")
    title_glow.set_stroke(color="#87CEEB", width=3, opacity=0.8)
    title_combined = VGroup(title_glow, title_text)

    scene.clear_current_mobj(new_mobj=initial_title if scene.transition_enabled else None)

    # 创建标题下的横线
    title_line = Underline(title_combined, color=colors["title_line"], stroke_width=4, stroke_opacity=1.0)

    # 创建清单项目 - 优化文字样式，调整布局
    checklist_items = []
    item_positions = [UP * 1.4, UP * 0.5, DOWN * 0.4, DOWN * 1.3, DOWN * 2.2]  # 进一步增加项目间距避免重叠

    for i, item_data in enumerate(items):
        if i >= len(item_positions):
            break

        # 项目背景容器 - 调整大小适应新容器
        item_bg = RoundedRectangle(
            width=8,  # 适应新的容器宽度
            height=0.8,
            corner_radius=0.25,
            fill_color=colors["item_bg_fill"],
            fill_opacity=0.06,
            stroke_color=colors["item_bg_stroke"],
            stroke_width=0.8,
            stroke_opacity=0.15,
        )
        item_bg.move_to(item_positions[i])

        # 图标背景 - 使用项目主题色
        icon_bg = RoundedRectangle(
            width=0.7, height=0.7, corner_radius=0.15, fill_color=item_data["color"], fill_opacity=0.15, stroke_width=0
        )
        icon_bg.move_to(item_positions[i] + LEFT * 2.3)  # 调整图标位置适应新宽度

        # 修复左边图标显示 - 使用默认图标
        default_icons = ["☀️", "😎", "💨", "🧊", "⭐"]
        icon = Text(
            default_icons[i % len(default_icons)],
            font_size=32,  # 调整图标字体大小
            color=item_data["color"],  # 使用项目主题色而不是白色
            weight=BOLD,
        )
        icon.move_to(icon_bg.get_center())

        # 如果图标仍然不显示，添加一个备用的圆形图标
        icon_fallback = Circle(
            radius=0.25,
            fill_color=item_data["color"],
            fill_opacity=0.8,
            stroke_color=colors["container_stroke"],
            stroke_width=2,
        )
        icon_fallback.move_to(icon_bg.get_center())

        # 创建图标组合（优先显示emoji，如果不行则显示圆形）
        icon_display = VGroup(icon_fallback, icon)

        # 主文本 - 优化样式，增强层次感，居中对齐，添加发光效果
        main_text = Text(
            item_data["text"],
            font_size=22,  # 适当调整主文本字体
            color=colors["main_text"],
            weight=BOLD,
            font="Microsoft YaHei",
        )
        # 添加主文本发光效果
        main_text_glow = main_text.copy()
        main_text_glow.set_color(item_data["color"])
        main_text_glow.set_stroke(color=item_data["color"], width=2, opacity=0.6)
        main_text_combined = VGroup(main_text_glow, main_text)
        main_text_combined.move_to(item_positions[i] + UP * 0.12)  # 居中对齐，去掉左偏移

        # 标签文本 - 优化样式，与主文本区分，改为蓝色，添加发光效果
        tags = Text(
            item_data["tags"],
            font_size=16,  # 调整标签字体
            color="#60a5fa",  # 改为蓝色
            font="Microsoft YaHei",
            slant=ITALIC,  # 添加斜体效果
        )
        tags.set_opacity(0.8)
        # 添加标签发光效果
        tags_glow = tags.copy()
        tags_glow.set_color("#87CEEB")
        tags_glow.set_stroke(color="#87CEEB", width=1.5, opacity=0.4)
        tags_combined = VGroup(tags_glow, tags)
        tags_combined.move_to(item_positions[i] + DOWN * 0.18)  # 居中对齐，去掉左偏移

        # 修复右边勾选图标样式，添加发光效果
        check = Text(
            "✓",  # 使用简单的勾号
            font_size=22,
            color=colors["check_color"],
            weight=BOLD,
        )
        # 添加勾选图标发光效果
        check_glow = check.copy()
        check_glow.set_color(colors["check_glow"])
        check_glow.set_stroke(color=colors["check_glow"], width=2, opacity=0.7)
        check_combined = VGroup(check_glow, check)
        check_combined.move_to(item_positions[i] + RIGHT * 2.3)  # 调整勾选位置适应新宽度

        # 组合项目元素
        item_group = VGroup(item_bg, icon_bg, icon_display, main_text_combined, tags_combined, check_combined)
        checklist_items.append(item_group)

    # 组合所有元素，包含标题横线
    elements_group = VGroup(*checklist_items)
    elements_group.center()
    container = create_main_container(elements_group)

    # 执行动画序列
    logger.info(f"播放纯文本动态展示动画 '{unique_id}'")

    with scene.voiceover(text=narration) as tracker:
        # Handle animation section for transition support
        if scene.transition_enabled:
            scene.add(title_combined)

            title_line.scale(0.1).shift(DOWN * 3)
            scene.play(title_line.animate.scale(10).shift(UP * 3))
            # 1. 容器出现
            scene.play(FadeIn(container), run_time=0.8)
        else:
            # Always animate the text content with original effects
            # 1. 容器出现
            scene.play(FadeIn(container), run_time=0.8)

            # 2. 标题和下划线一起快速展示
            title_combined.scale(0.1).rotate(PI / 4).shift(DOWN * 3)
            scene.play(
                AnimationGroup(
                    title_combined.animate.scale(10).rotate(-PI / 4).shift(UP * 3),
                    title_line.animate.scale(10).shift(UP * 3),
                    lag_ratio=0.1,
                ),
                rate_func=rate_functions.ease_out_bounce,
                run_time=1.2,
            )

        # 标题发光脉冲效果
        scene.play(title_glow.animate.set_stroke(width=6, opacity=1.0), rate_func=there_and_back, run_time=0.5)

        # 4. 逐个显示清单项目 - 文本框内元素一起进入，但文字有逐字效果
        for i, (item_group, item_data) in enumerate(zip(checklist_items, items)):
            item_bg, icon_bg, icon_display, main_text_combined, tags_combined, check_combined = item_group
            main_text_glow, main_text = main_text_combined
            tags_glow, tags = tags_combined
            check_glow, check = check_combined

            # 整个项目从左滑入
            item_group.shift(LEFT * 8)
            scene.play(item_group.animate.shift(RIGHT * 8), rate_func=rate_functions.ease_out_back, run_time=0.8)

            # 图标发光脉冲
            scene.play(
                AnimationGroup(
                    # 图标发光效果
                    icon_display[1].animate.set_stroke(color=item_data["color"], width=3, opacity=0.8),
                    # 主文本逐字显示效果
                    Write(main_text, run_time=1.0),
                    Write(main_text_glow, run_time=1.0),
                    # 标签逐字显示效果
                    Write(tags, run_time=0.8),
                    Write(tags_glow, run_time=0.8),
                    # 勾选发光效果
                    check_glow.animate.set_stroke(width=5, opacity=1.0),
                    lag_ratio=0.1,
                ),
                run_time=1.2,
            )

            # 所有发光效果回归正常
            scene.play(
                AnimationGroup(
                    main_text_glow.animate.set_stroke(width=2, opacity=0.6),
                    tags_glow.animate.set_stroke(width=1.5, opacity=0.4),
                    check_glow.animate.set_stroke(width=2, opacity=0.7),
                    rate_func=there_and_back,
                    run_time=0.5,
                )
            )

            scene.wait(0.2)

        # 6. 整体轻微浮动效果
        scene.play(Circumscribe(container))

        # 等待语音完成 - 修复时间计算
        # 实际动画时长计算：
        # 背景渐入(1.2) + 容器出现(0.8) + 标题展示(1.2) + 标题发光(0.5) + 每个项目(0.8+1.2+0.5+0.2=2.7) + 整体浮动(2.0)
        estimated_animation_time = 5.7 + (len(items) * 2.7)
        narration_duration = tracker.duration if hasattr(tracker, "duration") else 3.0
        remaining_time = max(0, narration_duration - estimated_animation_time)
        if remaining_time > 0:
            scene.wait(remaining_time)

    # 创建显示组：initial_title用于转场，title_text用于显示，all_elements为实际内容
    # 转场系统会使用submobjects[0]（initial_title）作为转场对象
    # display_group = VGroup(initial_title, title_text, container, title_combined, title_line, *checklist_items)
    display_group = VGroup(initial_title, title_text, container, title_line, *checklist_items)
    # display_group = Group(content_group)

    # Update current_mobject saving - ensure title is first submobject
    scene.current_mobj = display_group
    scene.save_scene_state(content_type="text_only", mobject_id=unique_id)
    logger.info(f"纯文本动态展示动画完成: {unique_id}")
