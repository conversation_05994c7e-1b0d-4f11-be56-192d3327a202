"""
effect: |
    创建一个交互式emoji流程图动画，支持emoji列表或包含emoji的文本输入生成对应的流程图。
    支持多种布局和动画风格，自动调整大小以适应屏幕。

use_cases:
    - 展示工作流程或业务流程
    - 可视化步骤序列或决策树
    - 创建教学演示的流程图
    - 显示系统架构或数据流

params:
  scene:
    type: FeynmanScene
    desc: Manim场景实例（由系统自动传入）
  title:
    type: str
    desc: 当前内容的标题，会显示在内容上方
    required: true
  emoji_list:
    type: List[Tuple[str, str]]
    desc: emoji和对应关键词的列表，格式为[(emoji, keyword), ...]。与content二选一
    required: false
  content:
    type: str
    desc: 包含emoji和描述的流程文本。与emoji_list二选一
    required: false
  layout:
    type: str
    desc: 布局样式。可选值：horizontal, vertical, circular, grid
    default: horizontal
  connection_style:
    type: str
    desc: 连接线样式。可选值：arrow, line, curve
    default: arrow
  animation_style:
    type: str
    desc: 动画风格。可选值：sequence, simultaneous, cascade
    default: sequence
  max_count:
    type: int
    desc: 最大emoji数量限制（仅在使用content时有效）
    default: 8
  id:
    type: str
    desc: 创建的Manim Mobject的唯一标识符
    default: None
  narration:
    type: str
    desc: 在内容显示时播放的语音旁白文本
    required: true

dsl_examples:
  - type: animate_emoji_flowchart
    params:
      title: 电商购物流程
      emoji_list: [
        ["📱", "打开应用"],
        ["📚", "浏览商品"],
        ["🛒", "选择商品"],
        ["➕", "添加购物车"],
        ["💳", "结算支付"],
        ["✅", "订单确认"]
      ]
      layout: horizontal
      connection_style: arrow
      animation_style: sequence
      narration: 这是一个电商购物流程的演示
  - type: animate_emoji_flowchart
    params:
      title: 软件开发生命周期
      content: |
        需求分析 📋 收集用户需求
        设计阶段 ✏️ 制作原型设计
        开发实现 💻 编写代码
        测试验证 🔍 质量保证
        部署上线 🚀 发布产品
      layout: circular
      connection_style: curve
      animation_style: cascade
      max_count: 6
      narration: 软件开发生命周期的完整流程展示

notes:
  - 支持直接使用emoji列表或从文本中自动提取emoji和关键词
  - 布局算法会自动防止重叠并适应屏幕尺寸
  - 连接线会智能避开emoji边界
  - emoji列表长度会影响布局效果
"""

import math
import re
import uuid
from typing import Optional

from loguru import logger
from manim import *

# 导入animate_markdown.py中的emoji处理函数
from .animate_markdown import create_emoji_mobject, is_emoji


def find_emojis_in_text(text: str) -> list[tuple[int, int, str]]:
    """
    在文本中查找所有emoji及其位置

    Args:
        text: 要搜索的文本

    Returns:
        包含emoji位置和内容的列表: [(start_index, end_index, emoji_str), ...]
    """
    emoji_positions = []
    i = 0
    while i < len(text):
        char = text[i]
        # 处理可能的复合emoji（例如带修饰符的emoji）
        j = i + 1
        current_emoji = char

        # 尝试扩展emoji检测（复合emoji字符）
        while j < len(text) and (
            ord(text[j]) in [0xFE0F, 0xFE0E]  # 变体选择器
            or 0x1F3FB <= ord(text[j]) <= 0x1F3FF
        ):  # 肤色修饰符
            current_emoji += text[j]
            j += 1

        if is_emoji(char) or is_emoji(current_emoji):
            emoji_positions.append((i, j, current_emoji))
            i = j  # 跳过整个emoji
        else:
            i += 1

    return emoji_positions


def extract_emojis_and_labels_from_text(text: str, max_count: int = 8) -> list[tuple[str, str]]:
    """
    从文本中提取emoji和对应的标签

    Args:
        text: 输入文本
        max_count: 最大emoji数量

    Returns:
        [(emoji, label), ...] 列表
    """
    logger.info(f"Processing text: {text[:100]}...")

    # 使用animate_markdown.py中的方法检测emoji
    emoji_spans = find_emojis_in_text(text)
    logger.info(f"Found emoji spans: {emoji_spans}")

    if not emoji_spans:
        logger.warning("No emojis found in text")
        return []

    results = []

    for start, end, emoji in emoji_spans:
        if len(results) >= max_count:
            break

        # 提取emoji前面的文本作为标签（更精确的提取）
        # 向前查找最多10个字符，向后查找最多5个字符
        label_start = max(0, start - 10)
        label_end = min(len(text), end + 5)

        context = text[label_start:label_end]

        # 移除emoji本身
        context = context.replace(emoji, " ")

        # 移除常见的连接符和标点，只保留中文和英文
        context = re.sub(r'[→←↑↓➕✅❌🔸🔹▶️◀️⬆️⬇️➡️⬅️\s\-_=+\|\\/<>，。、；：？！（）【】「」《》""' "…]+", " ", context)

        # 提取关键词，优先取emoji前面的词
        words = [w.strip() for w in context.split() if w.strip() and len(w.strip()) >= 2]

        # 选择最简短的标签
        if words:
            # 优先选择最后一个词（通常是emoji前面最近的词）
            if len(words) >= 1:
                label = words[-1][:4]  # 最多4个字符
            else:
                label = words[0][:4]
        else:
            label = "步骤"

        results.append((emoji, label))
        logger.info(f"Extracted: emoji={emoji}, label={label}")

    logger.info(f"Final results: {results}")
    return results


class AutoLayoutManager:
    """自动布局管理器"""

    @staticmethod
    def calculate_positions(
        emoji_count: int, layout: str, screen_width: float, screen_height: float
    ) -> list[tuple[float, float]]:
        """计算emoji位置"""
        if emoji_count == 0:
            return []

        # 增加emoji间距到3倍emoji大小
        emoji_size = 1.0
        spacing = emoji_size * 3.0

        if layout == "horizontal":
            return AutoLayoutManager._horizontal_layout(emoji_count, spacing, screen_width)
        elif layout == "vertical":
            return AutoLayoutManager._vertical_layout(emoji_count, spacing, screen_height)
        elif layout == "circular":
            return AutoLayoutManager._circular_layout(emoji_count, spacing)
        elif layout == "grid":
            return AutoLayoutManager._grid_layout(emoji_count, spacing, screen_width, screen_height)
        else:
            return AutoLayoutManager._horizontal_layout(emoji_count, spacing, screen_width)

    @staticmethod
    def _horizontal_layout(count: int, spacing: float, screen_width: float) -> list[tuple[float, float]]:
        """水平布局"""
        total_width = (count - 1) * spacing
        scale_factor = min(1.0, (screen_width * 0.8) / total_width) if total_width > 0 else 1.0
        actual_spacing = spacing * scale_factor

        start_x = -(count - 1) * actual_spacing / 2
        positions = []
        for i in range(count):
            x = start_x + i * actual_spacing
            positions.append((x, 0))
        return positions

    @staticmethod
    def _vertical_layout(count: int, spacing: float, screen_height: float) -> list[tuple[float, float]]:
        """垂直布局"""
        total_height = (count - 1) * spacing
        scale_factor = min(1.0, (screen_height * 0.8) / total_height) if total_height > 0 else 1.0
        actual_spacing = spacing * scale_factor

        start_y = (count - 1) * actual_spacing / 2
        positions = []
        for i in range(count):
            y = start_y - i * actual_spacing
            positions.append((0, y))
        return positions

    @staticmethod
    def _circular_layout(count: int, spacing: float) -> list[tuple[float, float]]:
        """圆形布局"""
        if count == 1:
            return [(0, 0)]

        # 计算圆的半径
        circumference = count * spacing
        radius = circumference / (2 * math.pi)

        positions = []
        for i in range(count):
            angle = 2 * math.pi * i / count - math.pi / 2  # 从顶部开始
            x = radius * math.cos(angle)
            y = radius * math.sin(angle)
            positions.append((x, y))
        return positions

    @staticmethod
    def _grid_layout(
        count: int, spacing: float, screen_width: float, screen_height: float
    ) -> list[tuple[float, float]]:
        """网格布局"""
        cols = math.ceil(math.sqrt(count))
        rows = math.ceil(count / cols)

        total_width = (cols - 1) * spacing
        total_height = (rows - 1) * spacing

        scale_factor = min(
            (screen_width * 0.8) / total_width if total_width > 0 else 1.0,
            (screen_height * 0.8) / total_height if total_height > 0 else 1.0,
        )
        actual_spacing = spacing * scale_factor

        start_x = -(cols - 1) * actual_spacing / 2
        start_y = (rows - 1) * actual_spacing / 2

        positions = []
        for i in range(count):
            row = i // cols
            col = i % cols
            x = start_x + col * actual_spacing
            y = start_y - row * actual_spacing
            positions.append((x, y))
        return positions


def create_connection_line(
    start_pos: tuple[float, float], end_pos: tuple[float, float], style: str, emoji_size: float = 1.0
) -> Mobject:
    """创建连接线"""
    start_point = np.array([start_pos[0], start_pos[1], 0])
    end_point = np.array([end_pos[0], end_pos[1], 0])

    # 计算方向向量和偏移
    direction = end_point - start_point
    if np.linalg.norm(direction) == 0:
        return VGroup()  # 如果起点终点相同，返回空组

    direction_unit = direction / np.linalg.norm(direction)
    offset = emoji_size * 0.6  # 偏移量

    # 调整起点和终点避免与emoji重叠
    adjusted_start = start_point + direction_unit * offset
    adjusted_end = end_point - direction_unit * offset

    if style == "arrow":
        line = Arrow(
            start=adjusted_start,
            end=adjusted_end,
            color=BLACK,  # 改为黑色以适应白色背景
            stroke_width=6,  # 增加线条粗细
            buff=0,
        )
    elif style == "curve":
        # 创建弯曲的连接线
        mid_point = (adjusted_start + adjusted_end) / 2
        # 添加垂直偏移创建弧度
        perpendicular = np.array([-direction_unit[1], direction_unit[0], 0])
        curve_offset = perpendicular * 0.5
        mid_point += curve_offset

        line = CubicBezier(
            adjusted_start,
            adjusted_start + direction * 0.3,
            adjusted_end - direction * 0.3,
            adjusted_end,
            color=BLACK,  # 改为黑色以适应白色背景
            stroke_width=6,
        )
    else:  # line
        line = Line(
            start=adjusted_start,
            end=adjusted_end,
            color=BLACK,  # 改为黑色以适应白色背景
            stroke_width=6,
        )

    return line


def animate_emoji_flowchart(
    scene,
    title: str,
    emoji_list: Optional[list[tuple[str, str]]] = None,
    content: Optional[str] = None,
    layout: str = "horizontal",
    connection_style: str = "arrow",
    animation_style: str = "sequence",
    max_count: int = 8,
    narration: str = "",
    **kwargs,
):
    """
    创建emoji流程图动画

    Args:
        scene: Manim场景
        title: 当前内容的标题，会显示在内容上方
        emoji_list: emoji和关键词的列表，格式为[(emoji, keyword), ...]（可选）
        content: 包含emoji和描述的文本（可选）
        layout: 布局样式 (horizontal, vertical, circular, grid)
        connection_style: 连接线样式 (arrow, line, curve)
        animation_style: 动画风格 (sequence, simultaneous, cascade)
        max_count: 最大emoji数量（仅在使用content时有效）
        **kwargs: 其他参数
    """
    mobject_id = kwargs.get("id", f"emoji_flowchart_{str(uuid.uuid4())[:8]}")
    logger.info(f"Creating emoji flowchart (id: {mobject_id})")

    # Create title object
    from dsl.v2.themes.theme_utils import ThemeUtils

    title_text = Text(
        title,
        font=ThemeUtils.get_font("heading", "Microsoft YaHei"),
        color=ThemeUtils.get_color("text_primary", WHITE),
        font_size=ThemeUtils.get_font_size("h1"),
    )

    # Handle clear_current_mobject for transition support
    scene.clear_current_mobj(new_mobj=title_text if scene.transition_enabled else None)

    # 处理输入参数
    if emoji_list and content:
        logger.warning("Both emoji_list and content provided, using emoji_list")
        emoji_data = emoji_list
    elif emoji_list:
        emoji_data = emoji_list
    elif content:
        # 从文本中提取emoji和标签
        emoji_data = extract_emojis_and_labels_from_text(content, max_count)
    else:
        logger.error("Neither emoji_list nor content provided")
        return

    # 验证输入
    if not emoji_data:
        logger.warning("No emoji data available")
        return

    logger.info(f"Creating flowchart with {len(emoji_data)} emojis")

    # 获取屏幕尺寸
    screen_width = scene.camera.frame_width
    screen_height = scene.camera.frame_height

    # 计算位置
    positions = AutoLayoutManager.calculate_positions(len(emoji_data), layout, screen_width, screen_height)

    # 创建emoji和标签组合
    emoji_groups = []
    for i, ((emoji, label), (x, y)) in enumerate(zip(emoji_data, positions)):
        # 创建emoji
        emoji_mob = create_emoji_mobject(emoji)
        if not emoji_mob:
            logger.warning(f"Failed to create emoji mobject for {emoji}")
            continue

        emoji_mob.height = 1.0

        # 创建标签（使用黑色粗体文字）
        label_mob = Text(label, font_size=24, color=BLACK, weight=BOLD)

        # 组合emoji和标签
        group = Group(emoji_mob, label_mob)
        group.arrange(DOWN, buff=0.1)
        group.move_to([x, y, 0])

        emoji_groups.append(group)

    # 创建连接线
    connection_lines = []
    for i in range(len(positions) - 1):
        line = create_connection_line(positions[i], positions[i + 1], connection_style, emoji_size=1.0)
        connection_lines.append(line)

    # 创建完整的流程图组
    flowchart_group = Group()

    # 添加连接线
    for line in connection_lines:
        flowchart_group.add(line)

    # 添加emoji组
    for group in emoji_groups:
        flowchart_group.add(group)

    # 缩放以适应屏幕
    if flowchart_group.width > 0 and flowchart_group.height > 0:
        scale_factor = min(
            screen_width * 0.9 / flowchart_group.width, screen_height * 0.9 / flowchart_group.height, 1.0
        )
        flowchart_group.scale(scale_factor)

    # Create display group with title and flowchart content
    display_group = Group(title_text, flowchart_group)
    display_group.arrange(DOWN, buff=0.5)
    display_group.move_to(ORIGIN)

    # 执行动画
    with scene.voiceover(text=narration) as tracker:  # noqa: F841
        # Handle animation section for transition support
        if not scene.transition_enabled:
            # Normal animation - show title first
            scene.play(Write(title_text), run_time=1.0)
            scene.wait(0.5)
        else:
            scene.add(title_text)

        # Always animate the flowchart content with original effects
        if animation_style == "sequence":
            # 逐步显示：emoji -> 箭头 -> emoji -> 箭头...
            # 首先显示第一个emoji，加入动效
            if emoji_groups:
                # emoji出现时的动效组合：放大 + 淡入 + 轻微弹跳
                first_group = emoji_groups[0]
                first_group.scale(0.1)  # 先缩小
                scene.add(first_group)
                scene.play(
                    first_group.animate.scale(10).set_opacity(1),  # 放大到正常大小
                    run_time=0.5,
                )
                # 添加轻微弹跳效果
                scene.play(first_group.animate.scale(1.2).rotate(0.1), run_time=0.2)
                scene.play(first_group.animate.scale(1 / 1.2).rotate(-0.1), run_time=0.2)

            # 然后逐个显示：箭头 -> emoji
            for i in range(len(connection_lines)):
                # 显示连接线
                scene.play(Create(connection_lines[i]), run_time=0.6)
                # 显示下一个emoji（如果存在）
                if i + 1 < len(emoji_groups):
                    next_group = emoji_groups[i + 1]
                    next_group.scale(0.1)  # 先缩小
                    scene.add(next_group)
                    # emoji出现动效：缩放 + 淡入 + 弹跳
                    scene.play(next_group.animate.scale(10).set_opacity(1), run_time=0.5)
                    # 轻微弹跳
                    scene.play(next_group.animate.scale(1.2).rotate(0.1), run_time=0.2)
                    scene.play(next_group.animate.scale(1 / 1.2).rotate(-0.1), run_time=0.2)

        elif animation_style == "simultaneous":
            # 同时显示所有元素
            animations = []
            for line in connection_lines:
                animations.append(Create(line))
            for group in emoji_groups:
                animations.append(FadeIn(group))
            scene.play(*animations, run_time=2.0)
        elif animation_style == "cascade":
            # 瀑布式动画
            all_elements = connection_lines + emoji_groups
            animations = []
            for element in all_elements:
                if isinstance(element, (Arrow, Line, CubicBezier)):
                    animations.append(Create(element))
                else:
                    animations.append(FadeIn(element))
            scene.play(LaggedStart(*animations, lag_ratio=0.3), run_time=3.0)

    # Update current_mobject saving - ensure title is first submobject
    scene.current_mobj = display_group
    scene.save_scene_state("emoji_flowchart", mobject_id)
    setattr(scene, mobject_id, display_group)

    logger.info(f"Emoji flowchart (id: {mobject_id}) animation complete")
