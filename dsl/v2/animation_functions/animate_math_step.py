"""
effect: |
    在<PERSON>im场景中专业地展示数学解题步骤，支持逐步呈现和高亮强调。支持解析markdown格式的数学公式文本，
    智能识别变量、值、操作符等数学元素，并使用不同颜色和动画效果进行区分展示。

use_cases:
    - 数学定理证明的逐步展示
    - 方程求解过程的详细演示
    - 几何计算步骤的专业讲解
    - 代数运算过程的可视化

params:
  scene:
    type: FeynmanScene
    desc: <PERSON><PERSON>场景实例（由系统自动传入）
  title:
    type: str
    desc: 解题标题，会显示在步骤上方
    required: true
  steps_content:
    type: str
    desc: markdown格式的数学步骤描述，每行一个步骤，支持数学公式
    required: true
  narration:
    type: str
    desc: 在步骤展示时播放的语音旁白文本
    required: true
  id:
    type: str
    desc: 创建的Manim Mobject的唯一标识符
    default: None
  step_delay:
    type: float
    desc: 每步之间的延迟时间（秒）
    default: 1.5
  highlight_delay:
    type: float
    desc: 高亮效果的延迟时间（秒）
    default: 0.5

dsl_examples:
  - type: animate_math_step
    params:
      title: "三角函数值计算"
      steps_content: |
        - `AH = x√3 = 3 + √3`
        - `BH = BD - DH = 3 - √3`
        - `tan(B) = AH / BH`
          `= (3 + √3) / (3 - √3)`
          `= 2 + √3`
      narration: "让我们一步步计算这个三角函数的值，首先确定AH和BH的长度，然后应用正切函数的定义。"
  - type: animate_math_step
    params:
      title: "二次方程求解"
      steps_content: |
        - `x² + 2x - 3 = 0`
        - `(x + 3)(x - 1) = 0`
        - `x = -3 或 x = 1`
      narration: "这是一个标准的二次方程求解过程，我们使用因式分解的方法来找到解。"
      step_delay: 2.0
  - type: animate_math_step
    params:
      title: "复杂方程组求解过程"
      steps_content: |
        - `2x + 3y = 12`
        - `4x - y = 8`
        - `y = 4x - 8`
        - `2x + 3(4x - 8) = 12`
        - `2x + 12x - 24 = 12`
        - `14x = 36`
        - `x = 36/14 = 18/7`
        - `y = 4(18/7) - 8`
        - `y = 72/7 - 56/7`
        - `y = 16/7`
      narration: "这是一个复杂的方程组求解过程，我们使用替换法逐步消元求解，演示分屏显示功能。"
      step_delay: 1.2

notes:
  - 支持标准markdown列表格式，每个`-`项为一个步骤
  - 数学公式用反引号包围，会被解析为数学表达式
  - 自动识别变量（字母）、值（数字）、操作符（+、-、=、/等）
  - 支持分数的专业显示格式
  - 每个步骤会逐步出现，带有淡入动画效果
  - 重要的数学元素会有高亮强调效果
  - 最终结果会有特殊的强调样式
"""
from typing import TYPE_CHECKING, Optional, List, Dict, Any
import re
from loguru import logger
from manim import *

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene

from dsl.v2.themes.theme_manager import get_current_theme


def _create_math_or_text(expr: str) -> Mobject:
    """
    智能创建数学表达式或文本，并应用主题颜色
    支持等式/不等式左边的特殊着色，也支持纯文字内容
    
    Args:
        expr: 表达式字符串（可能是数学公式或纯文字）
        
    Returns:
        Mobject: 数学表达式或文本对象
    """
    theme = get_current_theme()
    
    # 检查是否包含数学符号
    math_symbols = ['=', '+', '-', '*', '/', '^', '√', '²', '³', '(', ')', '[', ']', 
                   'sin', 'cos', 'tan', 'log', 'ln', 'exp', '∑', '∫', '∞', 'π', 'α', 'β', 'γ']
    has_math = any(symbol in expr for symbol in math_symbols)
    
    # 如果不包含数学符号，直接使用Text
    if not has_math:
        logger.info(f"检测到纯文字内容，使用Text渲染: {expr}")
        return Text(expr, font_size=36, color=theme.colors.text_primary)
    
    try:
        # 处理特殊字符，转换为LaTeX格式
        latex_expr = expr
        
        # 处理根号
        latex_expr = latex_expr.replace("√3", "\\sqrt{3}")
        latex_expr = latex_expr.replace("√", "\\sqrt{}")
        
        # 处理上标
        latex_expr = latex_expr.replace("²", "^2")
        latex_expr = latex_expr.replace("³", "^3")
        latex_expr = latex_expr.replace("x²", "x^2")
        
        # 处理分数 - 简单的模式匹配
        # 处理 (a + b) / (c - d) 格式
        fraction_pattern = r'\((.*?)\)\s*/\s*\((.*?)\)'
        if re.search(fraction_pattern, latex_expr):
            latex_expr = re.sub(fraction_pattern, r'\\frac{\1}{\2}', latex_expr)
        
        # 处理希腊字母和函数
        latex_expr = latex_expr.replace("tan(", "\\tan(")
        latex_expr = latex_expr.replace("sin(", "\\sin(")
        latex_expr = latex_expr.replace("cos(", "\\cos(")
        
        # 处理中文字符
        if "或" in latex_expr:
            latex_expr = latex_expr.replace(" 或 ", " \\text{ 或 } ")
        
        # 检测等式/不等式并进行特殊处理
        equation_operators = ['=', '>', '<', '≥', '≤', '≠', '≈']
        has_equation = any(op in latex_expr for op in equation_operators)
        
        if has_equation:
            # 找到第一个等式/不等式符号
            for op in equation_operators:
                if op in latex_expr:
                    parts = latex_expr.split(op, 1)
                    if len(parts) == 2:
                        left_part = parts[0].strip()
                        right_part = parts[1].strip()
                        
                        # 使用颜色标记来区分左右部分
                        # 左边用主色并加粗，右边用普通颜色
                        # 注意：这里的颜色会在后面的set_color中被覆盖，主要是为了区分结构
                        latex_expr = f"\\mathbf{{{left_part}}} {op} {right_part}"
                        break
        
        logger.info(f"转换LaTeX表达式: {expr} -> {latex_expr}")
        
        # 使用MathTex创建LaTeX数学表达式
        math_obj = MathTex(latex_expr, font_size=36)
        
        # 应用主题颜色进行着色
        if len(math_obj.submobjects) > 0:
            # 为整个表达式设置基础颜色
            math_obj.set_color(theme.colors.text_primary)
            
            # 如果是等式/不等式，尝试对左边部分应用特殊颜色
            if has_equation and len(math_obj.submobjects) > 1:
                # 简单启发式：前半部分元素使用主色
                half_point = len(math_obj.submobjects) // 2
                for i, submobj in enumerate(math_obj.submobjects[:half_point]):
                    submobj.set_color(theme.colors.primary)
        
        return math_obj
        
    except Exception as e:
        logger.warning(f"LaTeX渲染失败，回退到普通文本: {e}")
        # 回退到普通文本
        return Text(expr, font_size=36, color=theme.colors.text_primary)


def _parse_steps_content(content: str) -> List[Dict[str, Any]]:
    """
    解析markdown格式的步骤内容，支持多种格式
    
    Args:
        content: markdown格式的步骤内容
        
    Returns:
        List[Dict]: 解析后的步骤列表
    """
    steps = []
    lines = content.strip().split('\n')
    current_step = None
    
    for line in lines:
        original_line = line
        line = line.strip()
        if not line:
            continue
        
        # 处理各种markdown格式
        if line.startswith('###'):
            # 三级标题作为步骤
            if current_step:
                steps.append(current_step)
            
            title_text = line[3:].strip()
            current_step = {
                'expressions': [title_text],
                'is_final': False,
                'is_title': True
            }
        elif line.startswith('- `') and line.endswith('`'):
            # 带反引号的列表项（数学表达式）
            if current_step:
                steps.append(current_step)
            
            math_expr = line[3:-1]  # 去掉 "- `" 和 "`"
            current_step = {
                'expressions': [math_expr],
                'is_final': False,
                'is_math': True
            }
        elif line.startswith('- '):
            # 普通列表项（文字内容）
            if current_step:
                steps.append(current_step)
                
            text_content = line[2:].strip()
            current_step = {
                'expressions': [text_content],
                'is_final': False,
                'is_text': True
            }
        elif line.startswith('  `') and line.endswith('`'):
            # 续行表达式（等号对齐）
            if current_step:
                math_expr = line.strip()[1:-1]  # 去掉反引号
                current_step['expressions'].append(math_expr)
                current_step['has_continuation'] = True
        elif line.startswith('`') and line.endswith('`'):
            # 单独的数学表达式
            if current_step:
                steps.append(current_step)
                
            math_expr = line[1:-1]  # 去掉反引号
            current_step = {
                'expressions': [math_expr],
                'is_final': False,
                'is_math': True
            }
        elif line.startswith('**') and line.endswith('**'):
            # 粗体强调文字
            if current_step:
                steps.append(current_step)
                
            bold_text = line[2:-2]  # 去掉 ** 标记
            current_step = {
                'expressions': [bold_text],
                'is_final': False,
                'is_bold': True
            }
        else:
            # 普通文字内容
            if current_step and not line.startswith(('###', '- ', '`')):
                # 如果是续行文字，添加到当前步骤
                current_step['expressions'].append(line)
            else:
                # 新的文字步骤
                if current_step:
                    steps.append(current_step)
                    
                current_step = {
                    'expressions': [line],
                    'is_final': False,
                    'is_text': True
                }
    
    if current_step:
        current_step['is_final'] = True  # 最后一步标记为最终结果
        steps.append(current_step)
    
    return steps


def animate_math_step(
    scene: "FeynmanScene",
    title: str,
    steps_content: str,
    narration: str,
    id: Optional[str] = None,
    step_delay: float = 1.5,
    highlight_delay: float = 0.5
) -> None:
    """
    在Manim场景中专业地展示数学解题步骤
    
    Args:
        scene: Manim场景实例
        title: 解题标题
        steps_content: markdown格式的数学步骤描述
        narration: 语音旁白文本
        id: 唯一标识符
        step_delay: 每步之间的延迟时间
        highlight_delay: 高亮效果的延迟时间
    """
    logger.info(f"开始创建数学步骤动画 - 标题: {title}")
    
    try:
        # 清理已有对象
        scene.clear_current_mobj()
        
        # 获取当前主题
        theme = get_current_theme()
        
        with scene.voiceover(narration) as tracker:
            # 创建标题，使用主题颜色
            title_text = Text(title, font_size=48, weight=BOLD)
            title_text.set_color(theme.colors.primary)
            title_text.to_edge(UP, buff=1.0)
            
            scene.play(FadeIn(title_text), run_time=0.8)
            scene.wait(0.5)
            
            # 解析步骤内容
            steps = _parse_steps_content(steps_content)
            logger.info(f"解析到 {len(steps)} 个步骤")
            
            if not steps:
                logger.warning("未找到有效的数学步骤")
                error_text = Text("未找到有效的数学步骤", font_size=36, color=theme.colors.error)
                scene.play(FadeIn(error_text))
                scene.current_mobj = error_text
                return
            
            # 分屏逻辑：每屏最多6步
            max_steps_per_screen = 6
            total_screens = (len(steps) + max_steps_per_screen - 1) // max_steps_per_screen
            logger.info(f"总共 {len(steps)} 步，将分为 {total_screens} 屏显示")
            
            all_content_groups = []
            
            for screen_idx in range(total_screens):
                start_step = screen_idx * max_steps_per_screen
                end_step = min((screen_idx + 1) * max_steps_per_screen, len(steps))
                current_screen_steps = steps[start_step:end_step]
                
                logger.info(f"第 {screen_idx + 1} 屏：显示步骤 {start_step + 1} 到 {end_step}")
                
                # 如果不是第一屏，先清空上一屏内容
                if screen_idx > 0:
                    scene.play(FadeOut(current_screen_content), run_time=0.8)
                    scene.wait(0.5)
                    
                    # 重新显示标题
                    scene.play(FadeIn(title_text), run_time=0.6)
                
                # 创建当前屏的步骤容器
                current_screen_content = VGroup()
                screen_step_mobjects = []
                
                # 逐步显示当前屏的步骤
                for local_i, step in enumerate(current_screen_steps):
                    global_i = start_step + local_i
                    logger.info(f"处理第 {global_i + 1} 步，包含 {len(step['expressions'])} 个表达式")
                    
                    step_group = VGroup()
                    
                    # 创建步骤标号（圆圈内的数字）
                    step_number = global_i + 1
                    number_text = Text(str(step_number), font_size=24, weight=BOLD)
                    number_text.set_color(theme.colors.text_primary)
                    
                    # 创建圆圈
                    circle = Circle(radius=0.25, color=theme.colors.primary, stroke_width=3)
                    circle.set_fill(color=theme.colors.primary, opacity=0.1)
                    
                    # 将数字放在圆圈中心
                    number_text.move_to(circle.get_center())
                    step_number_group = VGroup(circle, number_text)
                    
                    for j, expr in enumerate(step['expressions']):
                        # 根据步骤类型创建对应的显示对象
                        if step.get('is_title'):
                            # 标题样式
                            display_obj = Text(expr, font_size=40, weight=BOLD)
                            display_obj.set_color(theme.colors.primary)
                        elif step.get('is_bold'):
                            # 粗体文字
                            display_obj = Text(expr, font_size=36, weight=BOLD)
                            display_obj.set_color(theme.colors.accent)
                        elif step.get('is_text'):
                            # 普通文字
                            display_obj = Text(expr, font_size=32)
                            display_obj.set_color(theme.colors.text_primary)
                        else:
                            # 数学表达式或混合内容
                            display_obj = _create_math_or_text(expr)
                        
                        if j == 0:
                            # 第一行表达式，先定位步骤标号，再定位内容
                            step_number_group.next_to(title_text, DOWN, buff=1.0 + local_i * 0.8)
                            step_number_group.align_to(title_text, LEFT)
                            
                            # 内容紧跟在步骤标号右边
                            display_obj.next_to(step_number_group, RIGHT, buff=0.3)
                        else:
                            # 续行表达式处理
                            if step.get('has_continuation') and j > 0:
                                # 等号对齐：检查是否以等号开头
                                if expr.strip().startswith('='):
                                    # 找到第一行的等号位置进行对齐
                                    first_expr = step_group[1]  # step_group[0]是步骤标号
                                    display_obj.next_to(step_group[-1], DOWN, buff=0.2)
                                    
                                    # 尝试与第一行的等号对齐
                                    try:
                                        # 简单的等号对齐：向右偏移一些距离
                                        display_obj.align_to(first_expr, LEFT)
                                        display_obj.shift(RIGHT * 0.5)
                                    except:
                                        # 如果对齐失败，使用默认缩进
                                        display_obj.align_to(first_expr, LEFT)
                                        display_obj.shift(RIGHT * 1.0)
                                else:
                                    # 普通续行，与第一行内容左对齐并缩进
                                    first_content = step_group[1]  # step_group[0]是步骤标号
                                    display_obj.next_to(step_group[-1], DOWN, buff=0.2)
                                    display_obj.align_to(first_content, LEFT)
                                    display_obj.shift(RIGHT * 0.5)
                            else:
                                # 普通续行
                                first_content = step_group[1] if len(step_group) > 1 else step_number_group
                                display_obj.next_to(step_group[-1], DOWN, buff=0.2)
                                display_obj.align_to(first_content, LEFT)
                                if not step.get('is_title'):  # 标题不缩进
                                    display_obj.shift(RIGHT * 0.3)
                        
                        if j == 0:
                            # 第一个表达式时，将步骤标号也加入组
                            step_group.add(step_number_group)
                        
                        step_group.add(display_obj)
                    
                    # 添加步骤入场动画，带有高亮效果
                    scene.play(
                        *[FadeIn(expr, shift=UP * 0.5) for expr in step_group],
                        run_time=0.8
                    )
                    
                    # 添加高亮动画效果（对内容进行高亮，不包括步骤标号）
                    highlight_color = theme.colors.accent
                    content_objects = step_group[1:]  # 跳过第一个元素（步骤标号）
                    original_colors = []
                    
                    # 保存内容对象的原始颜色
                    for obj in content_objects:
                        original_colors.append(obj.get_color())
                    
                    # 高亮动画：闪烁效果（针对所有内容）
                    scene.play(
                        *[obj.animate.set_color(highlight_color) for obj in content_objects],
                        run_time=0.3
                    )
                    scene.play(
                        *[obj.animate.set_color(original_colors[idx]) for idx, obj in enumerate(content_objects)],
                        run_time=0.3
                    )
                    
                    # 添加脉冲高亮效果（针对所有内容）
                    pulse_scale = 1.05  # 稍微减小脉冲幅度，适配文字内容
                    scene.play(
                        *[obj.animate.scale(pulse_scale) for obj in content_objects],
                        run_time=0.2
                    )
                    scene.play(
                        *[obj.animate.scale(1/pulse_scale) for obj in content_objects],
                        run_time=0.2
                    )
                    
                    # 为步骤标号的圆圈添加特别的闪烁效果
                    step_number_obj = step_group[0]  # 步骤标号组（圆圈+数字）
                    circle_obj = step_number_obj[0]  # 圆圈
                    number_obj = step_number_obj[1]  # 数字
                    
                    scene.play(
                        circle_obj.animate.set_stroke(color=theme.colors.accent, width=5),
                        number_obj.animate.set_color(theme.colors.accent),
                        run_time=0.2
                    )
                    scene.play(
                        circle_obj.animate.set_stroke(color=theme.colors.primary, width=3),
                        number_obj.animate.set_color(theme.colors.text_primary),
                        run_time=0.2
                    )
                    
                    current_screen_content.add(step_group)
                    screen_step_mobjects.extend(step_group)
                    
                    # 如果不是第一步，立即让前面的步骤变透明（无动画，直接设置）
                    if local_i > 0:
                        prev_steps = VGroup(*screen_step_mobjects[:-len(step_group)])
                        prev_steps.set_opacity(0.6)
                    
                    # 步骤间延迟
                    if local_i < len(current_screen_steps) - 1:
                        scene.wait(step_delay)
                    else:
                        scene.wait(highlight_delay)
                
                # 当前屏结束，恢复透明度
                scene.play(
                    current_screen_content.animate.set_opacity(1.0),
                    run_time=0.5
                )
                
                # 为当前屏的最后一步添加特殊强调（无框高亮，只是颜色变化）
                if current_screen_steps and screen_idx == total_screens - 1:  # 只对最后一屏的最后一步强调
                    final_step = current_screen_content[-1]
                    # 只对内容部分进行最终强调，不包括步骤标号
                    final_content_objects = final_step[1:]  # 跳过步骤标号
                    scene.play(
                        *[obj.animate.set_color(theme.colors.success) for obj in final_content_objects],
                        run_time=0.6
                    )
                
                # 如果不是最后一屏，添加"继续"提示
                if screen_idx < total_screens - 1:
                    continue_text = Text("继续...", font_size=24, color=theme.colors.text_secondary)
                    continue_text.to_edge(DOWN, buff=0.5)
                    scene.play(FadeIn(continue_text), run_time=0.5)
                    scene.wait(1.0)
                    scene.play(FadeOut(continue_text), run_time=0.5)
                
                all_content_groups.append(VGroup(title_text.copy(), current_screen_content))
            
            # 设置当前对象为最后一屏的内容
            scene.current_mobj = VGroup(title_text, current_screen_content)
            
            logger.info("数学步骤动画创建完成")
            
    except Exception as e:
        logger.error(f"创建数学步骤动画时出错: {e}")
        # 错误恢复：显示简单的错误信息
        try:
            theme = get_current_theme()
            error_title = Text(title if title else "数学步骤", font_size=48)
            error_content = Text("内容加载出现问题", font_size=36, color=theme.colors.error)
            error_content.next_to(error_title, DOWN, buff=1.0)
            
            error_group = VGroup(error_title, error_content)
            scene.play(FadeIn(error_group))
            scene.current_mobj = error_group
            
        except Exception as recovery_error:
            logger.error(f"错误恢复也失败了: {recovery_error}")
            # 如果连错误恢复都失败，就保持静默
            pass 