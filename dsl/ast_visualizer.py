#!/usr/bin/env python3
import json

# 添加当前目录到路径
import os
import sys
from pathlib import Path
from typing import Any

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dsl.ast_builder import ActionNode, ASTBuilder, SceneAST
from dsl.parser import ManimDSLParser


class ASTVisualizer:
    """AST 可视化工具，将 AST 转换为JSON或树形结构"""

    @staticmethod
    def to_dict(ast: SceneAST) -> dict[str, Any]:
        """将AST转换为字典"""
        result = {
            "type": "scene",
            "metadata": {
                "title": ast.metadata.title,
                "author": ast.metadata.author,
                "resolution": str(ast.metadata.resolution),
                "background_color": ast.metadata.background_color,
                "estimated_duration": ast.metadata.estimated_duration,
            },
            "object_definitions": {},
            "action_sequence": [],
        }

        # 添加对象定义
        for obj_id, obj_def in ast.object_definitions.items():
            obj_dict = {
                "type": obj_def.object_type,
                "properties": {},
            }

            # 添加属性
            for key, value in obj_def.properties.items():
                if hasattr(value, "__str__"):
                    obj_dict["properties"][key] = str(value)
                else:
                    obj_dict["properties"][key] = value

            # 特殊处理组对象
            if obj_def.object_type == "group":
                obj_dict["members"] = obj_def.member_ids
                obj_dict["layout"] = obj_def.layout

            result["object_definitions"][obj_id] = obj_dict

        # 添加动作序列
        for action in ast.action_sequence:
            action_dict = {
                "type": action.action_type,
                "duration": action.duration,
            }

            # 添加动作特定属性
            for key, value in action.__dict__.items():
                if key not in ["node_type", "action_type", "duration"] and not key.startswith("_"):
                    if key == "child_actions" and action.action_type == "animate_group":
                        # 递归处理子动作
                        action_dict[key] = [
                            ASTVisualizer._action_to_dict(child_action) for child_action in action.child_actions
                        ]
                    else:
                        if hasattr(value, "__str__") and not isinstance(value, (dict, list)):
                            action_dict[key] = str(value)
                        else:
                            action_dict[key] = value

            result["action_sequence"].append(action_dict)

        return result

    @staticmethod
    def _action_to_dict(action: ActionNode) -> dict[str, Any]:
        """将单个动作节点转换为字典"""
        action_dict = {
            "type": action.action_type,
            "duration": action.duration,
        }

        # 添加动作特定属性
        for key, value in action.__dict__.items():
            if key not in ["node_type", "action_type", "duration"] and not key.startswith("_"):
                if hasattr(value, "__str__") and not isinstance(value, (dict, list)):
                    action_dict[key] = str(value)
                else:
                    action_dict[key] = value

        return action_dict

    @staticmethod
    def to_json(ast: SceneAST, indent: int = 2) -> str:
        """将AST转换为JSON字符串"""
        return json.dumps(ASTVisualizer.to_dict(ast), indent=indent)

    @staticmethod
    def print_tree(ast: SceneAST, include_props: bool = True) -> None:
        """以树形结构打印AST"""
        print("Scene AST:")
        print("├── Metadata:")
        print(f"│   ├── Title: {ast.metadata.title}")
        print(f"│   ├── Author: {ast.metadata.author}")
        print(f"│   ├── Resolution: {ast.metadata.resolution}")
        print(f"│   └── Background Color: {ast.metadata.background_color}")

        print(f"├── Object Definitions ({len(ast.object_definitions)}):")
        for i, (obj_id, obj_def) in enumerate(ast.object_definitions.items()):
            is_last = i == len(ast.object_definitions) - 1
            prefix = "└──" if is_last else "├──"
            child_prefix = "    " if is_last else "│   "

            print(f"│   {prefix} {obj_id} ({obj_def.object_type}):")

            if obj_def.object_type == "group":
                print(f"│   {child_prefix}└── Members: {obj_def.member_ids}")
                if obj_def.layout:
                    print(f"│   {child_prefix}    └── Layout: {obj_def.layout}")
            elif include_props:
                props = {
                    k: v
                    for k, v in obj_def.properties.items()
                    if v is not None and k not in ["__pydantic_initialised__"]
                }
                for j, (prop_key, prop_value) in enumerate(props.items()):
                    is_last_prop = j == len(props) - 1
                    prop_prefix = "└──" if is_last_prop else "├──"
                    print(f"│   {child_prefix}{prop_prefix} {prop_key}: {prop_value}")

        print(f"└── Action Sequence ({len(ast.action_sequence)}):")
        for i, action in enumerate(ast.action_sequence):
            is_last = i == len(ast.action_sequence) - 1
            prefix = "└──" if is_last else "├──"
            child_prefix = "    " if is_last else "│   "

            print(f"    {prefix} {i+1}. {action.action_type} (duration: {action.duration}s):")

            # 打印动作特有的属性
            attrs = {}
            for key, value in action.__dict__.items():
                if key not in ["node_type", "action_type", "duration"] and not key.startswith("_"):
                    if key == "child_actions" and action.action_type == "animate_group":
                        # 处理子动作
                        child_actions = [f"{a.action_type}" for a in value]
                        attrs[key] = child_actions
                    else:
                        attrs[key] = value

            for j, (attr_key, attr_value) in enumerate(attrs.items()):
                is_last_attr = j == len(attrs) - 1
                attr_prefix = "└──" if is_last_attr else "├──"

                if attr_key == "child_actions":
                    print(f"    {child_prefix}{attr_prefix} Child Actions:")
                    for k, child_action in enumerate(action.child_actions):
                        is_last_child = k == len(action.child_actions) - 1
                        child_action_prefix = "└──" if is_last_child else "├──"
                        grandchild_prefix = "    " if is_last_child else "│   "

                        print(f"    {child_prefix}    {child_action_prefix} {child_action.action_type}:")

                        # 打印子动作属性
                        child_attrs = {}
                        for c_key, c_value in child_action.__dict__.items():
                            if c_key not in ["node_type", "action_type", "duration"] and not c_key.startswith("_"):
                                child_attrs[c_key] = c_value

                        for child_index, (c_attr_key, c_attr_value) in enumerate(child_attrs.items()):
                            is_last_c_attr = child_index == len(child_attrs) - 1
                            c_attr_prefix = "└──" if is_last_c_attr else "├──"
                            print(
                                f"    {child_prefix}    {grandchild_prefix}{c_attr_prefix} {c_attr_key}: {c_attr_value}",
                            )
                else:
                    print(f"    {child_prefix}{attr_prefix} {attr_key}: {attr_value}")


def main() -> None:
    """主函数"""
    if len(sys.argv) < 2:
        print("Usage: python ast_visualizer.py <dsl_file_path> [--json|--tree]")
        return

    file_path = Path(sys.argv[1])
    if not file_path.exists():
        print(f"Error: File not found: {file_path}")
        return

    # 默认输出格式
    output_format = "tree"
    if len(sys.argv) > 2:
        if sys.argv[2] == "--json":
            output_format = "json"
        elif sys.argv[2] == "--tree":
            output_format = "tree"

    try:
        # 解析DSL并构建AST
        dsl = ManimDSLParser.parse_from_file(str(file_path))
        ast = ASTBuilder.build_ast(dsl)

        # 输出AST
        if output_format == "json":
            print(ASTVisualizer.to_json(ast))
        else:
            ASTVisualizer.print_tree(ast)

    except Exception as e:
        print(f"Error: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    # 如果没有命令行参数，使用示例DSL
    if len(sys.argv) < 2:
        # 创建一个示例DSL文件
        sample_file = Path("sample_dsl.json")
        sample_dsl = """
        {
            "metadata": {
                "title": "SampleVisualization",
                "author": "AST Visualizer",
                "resolution": "1080p",
                "background_color": "BLACK"
            },
            "objects": [
                {
                    "id": "circle1",
                    "type": "circle",
                    "properties": {
                        "radius": 1,
                        "color": "BLUE",
                        "fill_opacity": 0.5
                    }
                },
                {
                    "id": "text1",
                    "type": "text",
                    "properties": {
                        "content": "AST Visualization",
                        "font_size": 36,
                        "color": "WHITE"
                    }
                },
                {
                    "id": "group1",
                    "type": "group",
                    "properties": {
                        "members": ["circle1", "text1"],
                        "layout": {
                            "type": "vertical",
                            "buffer": 0.5
                        }
                    }
                }
            ],
            "actions": [
                {
                    "type": "create",
                    "target": "circle1",
                    "animation": "fade_in",
                    "duration": 1,
                    "position": [0, 0, 0]
                },
                {
                    "type": "create",
                    "target": "text1",
                    "animation": "write",
                    "duration": 1,
                    "position": [0, 1.5, 0]
                },
                {
                    "type": "wait",
                    "duration": 1
                },
                {
                    "type": "create",
                    "target": "group1",
                    "animation": "fade_in",
                    "duration": 1.5,
                    "position": [3, 0, 0]
                }
            ]
        }
        """

        with open(sample_file, "w") as f:
            f.write(sample_dsl.strip())

        print(f"Created sample DSL file: {sample_file.absolute()}")
        print(f"Run 'python {__file__} {sample_file} [--json|--tree]' to visualize it")
    else:
        main()
