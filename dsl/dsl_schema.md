# LLM 指导原则：生成 Manim DSL JSON (v1.1) - AST Approach

**目标受众:** 本文档主要面向 **大型语言模型 (LLM)**，旨在指导其将动画或场景的自然语言描述转换成结构化的 Manim DSL JSON 文件。

**核心目标与流程:** 生成的 JSON **必须** 严格遵循本文档定义的 Schema v1.1。这个 JSON 文件随后会被一个**解析器 (Parser)** 读取，转换成一个**抽象语法树 (AST)**。最后，一个**代码生成器 (Code Generator)** 会遍历 AST，生成最终的 Manim Python 代码。

**LLM 的任务:** 生成符合本 Schema 规范的、结构清晰的 JSON，准确表达动画的意图，以便后续的 AST 构建和代码生成能够顺利进行。

**重要提示：** 本 DSL 描述了动画的*意图*。最终能否成功生成可运行的 Manim Python 代码，还取决于生成的 JSON 是否符合*下游解析器、验证器和代码生成器*的隐式规则，以及 Manim 库本身的限制。请特别注意以下几点，以避免常见错误：
*   **ID 命名规则:** 严格遵守 `snake_case`。
*   **对象生命周期:** 确保对象在使用前已被顶级 `create` 动作创建。
*   **Manim 属性/值:** 使用 Manim 支持的颜色常量和属性，注意Manim中没有`CYAN`这个颜色。
*   **`animate_group` 限制:** 理解其并发执行的特性及约束。

**请务必遵守以下原则：**

## 一、 基本原则

-   **严格遵循 Schema:** 生成的 JSON **必须** 完全符合 DSL Schema v1.1 的结构和字段定义。这是成功解析为 AST 的前提。
-   **JSON 格式:** 输出 **必须** 是格式良好、语法正确的 JSON。
-   **声明式意图:** 专注于描述场景的**最终状态**和**状态之间的转换意图**（"对象是什么"、"它们如何变化"），而不是具体的实现步骤。AST 将捕捉这些意图。
-   **清晰与一致:** 使用清晰、有意义且 **全局唯一** 的 `id` 标识符。这对于在 AST 中正确引用对象节点至关重要。在整个 JSON 文件中保持命名和风格的一致性（**必须使用** `snake_case` 命名，例如 `my_object_id`）。
-   ⚠️ **警告：ID 命名规则 (ID Naming Rule):** ID **必须** 使用 `snake_case` (下划线分隔单词)。**绝对不要** 在 ID 中使用连字符 (`-`)，因为代码生成器会直接使用 ID 构建 Python 变量名，而连字符在 Python 变量名中是非法的，会导致 `SyntaxError`。

## 二、 元数据 (metadata)

-   **作用:** 提供场景的基本信息，这些信息通常会被解析并存储在 AST 的顶层元数据节点中。
-   **填写基本信息:** 提供 `title`（**必须是合法的 Python 类名**，代码生成器会用作 Manim 场景类名）和 `author`。
-   **设置视觉参数:** 指定 `resolution` (枚举值, **默认为 '1080p'** ) 和 `background_color` (颜色名或 #RRGGBB, **默认为 'BLACK'** )。这些将被 AST 捕获并用于设置场景。
-   **注意颜色值:** `background_color` 应使用 Manim 支持的颜色（见下文对象属性说明）。
-   `estimated_duration`: **可选的、信息性**字段，AST 可能会存储它，但代码生成器通常不强制执行。

```json
"metadata": {
  "title": "MyAstAnimation",
  "author": "LLM Assistant",
  "resolution": "1080p",
  "background_color": "DARK_BLUE",
  "estimated_duration": 20
}
```

## 三、 对象定义 (objects)

-   **目的: 定义对象蓝图 (Blueprints):** `objects` 列表用于定义场景中所有可能用到的元素的**静态属性蓝图**。解析器会为每个定义创建一个**对象定义节点 (ObjectDefinitionNode)** 或类似结构存储在 AST 中，包含其类型和静态属性。
-   **关键分离:** **严格区分静态定义与动态状态**。**不要在此处定义对象的位置、缩放、旋转等初始状态或随时间变化的属性**。这些动态方面由 `actions` 部分表达，并在 AST 中通过动作节点 (Action Nodes) 处理。
-   **唯一 ID:** 每个对象的 `id` **必须是全局唯一的字符串**。这是在 AST 和后续代码生成中引用对象定义的关键。
-   **类型和属性:** 在 `properties` 中**只包含** Schema 定义的、该类型适用的**静态属性**。AST 节点将存储这些静态值。
-   **Group 成员:** `group` 的 `properties.members` **必须** 引用在 `objects` 中定义的其他对象的 `id`。AST 在处理 `group` 定义时需要能够查找到这些成员的定义。

## 四、 动作序列 (actions)

-   **目的: 定义动画时间线:** `actions` 是一个**有序列表**，定义了动画的逐步进展和状态变化。解析器会将其转换为 AST 中一个**有序的动作节点序列 (Sequence of ActionNodes)**。
-   **顺序至关重要:** AST 严格保留此顺序，代码生成器将按此顺序生成 Manim 的 `play()` 或 `add()`/`remove()` 调用。
-   **【核心规则】先创建后使用 (AST 验证):**
    *   任何对象的 `id` **必须**先通过一个 `create` 动作（对应 AST 中的 `CreateActionNode`）引入到场景状态中，然后才能被后续的动作节点（如 `TransformActionNode`, `RemoveActionNode` 等）引用。
    *   对于 `group`，其 `members` 引用的对象 ID **必须** 在 `group` 本身的 `create` 动作之前被 `create`。
    *   这些规则通常在**AST 构建后的语义分析阶段**进行验证。
-   **动作类型:** 为每个步骤选择最合适的 `type`，这将决定解析器生成的具体 **ActionNode** 类型（如 `CreateActionNode`, `TransformActionNode`, `WaitActionNode` 等）。

### 4.1 关键规则：对象生命周期与动作引用 (Critical Rule: Object Lifecycle & Action Referencing)

*   **定义"已创建" (Definition of "Created"):** 一个对象 ID 只有在**先前步骤**中通过一个**顶级 `create` 动作**显式添加到场景后，才能在后续的独立动作（如 `transform`, `indicate`, `remove` 等）中被安全地引用。

*   ⚠️ **重要限制 (Important Limitation):**
    *   仅仅通过 `animate_group` 内部的 `create` 动作引入的对象，**不能** 被紧随该 `animate_group` 之后的*下一个独立动作*安全引用。
    *   仅仅通过 `replace` 动作的 `target` 引入的对象，**不能** 被紧随该 `replace` 动作之后的*下一个独立动作*安全引用。
    *   违反此规则通常会导致验证器报错（例如 `Value error, action references object X before it's created`）。

*   **正确处理模式 (Correct Handling Pattern):** 对于需要在创建后立即进行动画或交互的对象（特别是涉及 `animate_group` 或 `replace` 的情况），**必须** 使用以下模式：
    1.  **设置初始不可见:** 在 `objects` 定义中，设置对象的初始状态为不可见（例如 `"fill_opacity": 0`）。
    2.  **早期顶级创建:** 在 `actions` 序列的早期，使用一个**顶级 `create` 动作**（`\"animation\": \"none\"`）将对象添加到场景（此时对象不可见）。
    3.  **后续动画出现:** 在后续步骤中，使用 `transform` 动作来改变对象的属性（例如，将 `fill_opacity` 动画到 1）以实现视觉上的出现效果（如淡入）。
    4.  **安全引用:** 完成此操作后，该对象就可以被更晚的动作（如 `indicate`）安全引用。

    ```json
    // Example: Fade in logo then indicate
    {"type": "create", "target": "my_logo", "animation": "none", "position": [0,0,0] }, // Logo created invisible (opacity 0 in definition)
    {"type": "transform", "target": "my_logo", "properties": {"fill_opacity": 1}, "duration": 1}, // Fade it in
    {"type": "indicate", "target": "my_logo", "duration": 0.5} // Now safe to indicate
    ```

## 五、 关键动作类型详解 (映射到 AST 节点)

*以下解释了每个 JSON 动作类型如何表达意图，并通常如何映射到 AST 中的概念或节点。*

**create:**

-   **意图:** 在动画时间线的某个点，将 `objects` 中定义的某个对象蓝图实例化，并将其添加到场景中，可选地带有入场动画。
-   **AST 映射:** 通常解析为 `CreateActionNode`，包含：
    *   `target_object_id`: 要实例化的对象定义的 ID。
    *   `initial_position`: 对象实例的初始中心位置 (默认为 `[0,0,0]`)。
    *   `animation_type`: 入场动画类型 (`fade_in`, `draw`, `none` 等)。
    *   `duration`: 动画时长。
-   **代码生成:** 代码生成器根据 `CreateActionNode` 生成 `self.play(FadeIn(mobject))` 或 `self.add(mobject)` 等代码，并将 mobject 实例放置在指定位置。
-   **示例:**
    ```json
    {
      "type": "create",
      "target": "my_square",
      "position": [-3, 0, 0],
      "animation": "draw"
    }
    ```

**remove:**

-   **意图:** 从场景中移除一个先前已创建的对象实例，可选地带有离场动画。
-   **AST 映射:** 解析为 `RemoveActionNode`，包含 `target_object_id`, `animation_type`, `duration`。
-   **代码生成:** 生成 `self.play(FadeOut(mobject))` 或 `self.remove(mobject)`。

**transform:**

-   **意图:** 平滑地改变一个已存在对象实例的一个或多个属性到新的目标值。
-   **AST 映射:** 解析为 `TransformActionNode`，包含 `target_object_id` 以及一个包含**目标属性和值**的字典 (`target_properties`)，还有 `duration` 和 `easing`。
-   **代码生成:** 通常生成 `self.play(mobject.animate.set_color(...).move_to(...))` 或 `self.play(Transform(mobject, target_mobject))`（如果需要创建目标状态对象）。
-   **示例:**
    ```json
    {
      "type": "transform",
      "target": "my_square",
      "properties": {"color": "RED", "scale": 2},
      "duration": 1.5
    }
    ```

**move_to / arrange:**

-   **意图:** 改变已存在对象实例的位置，`move_to` 指定绝对位置，`arrange` 指定相对位置。
-   **AST 映射:** 解析为 `MoveToActionNode` 或 `ArrangeActionNode`，存储目标位置或相对定位参数。
-   **代码生成:** 生成 `self.play(mobject.animate.move_to(...))` 或 `self.play(mobject.animate.next_to(...))` / `self.play(mobject.animate.align_to(...))` 等。

**replace:**

-   **意图:** 用一个**新的对象实例**（基于 `objects` 中的定义）替换场景中一个**已存在的对象实例**。
-   **AST 映射:** 解析为 `ReplaceActionNode`，包含 `source_object_id`, `target_object_id` (新对象的定义 ID), `animation` 类型 (`transform` 或 `fade`), `duration`, `position_match`。
-   **代码生成:** 根据 `animation` 类型生成 `self.play(ReplacementTransform(source_obj, new_target_obj))` 或 `self.play(FadeOut(source_obj), FadeIn(new_target_obj))`。

**animate_group:**

-   **意图:** 将一组独立的动画动作**并发执行**，视觉上同时发生。
-   **AST 映射:** 解析为 `AnimateGroupNode`，其内部包含一个**子动作节点列表 (List of ActionNodes)**。这些子动作节点本身通常是 `TransformActionNode`, `MoveToActionNode`, 带动画的 `CreateActionNode`/`RemoveActionNode` 等。
-   **代码生成:** 代码生成器收集 `AnimateGroupNode` 内部所有子节点的动画指令（如 `.animate` 调用），并将它们放入**同一个 `self.play()`** 调用中。
-   **示例:**
    ```json
    {
      "type": "animate_group",
      "actions": [
        { "type": "move_to", "target": "obj1", "position": [1,1,0], "duration": 2 },
        { "type": "transform", "target": "obj2", "properties": {"color": "GREEN"}, "duration": 1 }
      ]
    }
    ```

**wait:**

-   **意图:** 在动画时间线上插入一个暂停。
-   **AST 映射:** 解析为 `WaitActionNode`，包含 `duration`。
-   **代码生成:** 生成 `self.wait(duration)`。

**align / distribute:**

-   **意图:** 对一组已存在的对象执行对齐或分布操作。
-   **AST 映射:** 解析为 `AlignActionNode` 或 `DistributeActionNode`，存储目标对象 ID 列表、对齐/分布参数和参考。
-   **代码生成:** 生成相应的 Manim 布局代码，可能涉及计算目标位置并启动并发的 `move_to` 动画（通常包装在 `animate_group` 或单个 `play` 中）。

## 六、 最佳实践与提示 (为了更好的 AST)

-   **逻辑分组:** 合理使用 `group` 对象（包括带 `layout` 的）来组织逻辑上相关的元素，这会反映在 AST 结构中，便于处理。
-   **分解复杂性:** 将复杂的变换分解为一系列简单的 `actions`，使 AST 更易于理解和处理。
-   **清晰的 ID:** 良好的 ID 命名有助于调试 AST 和生成的代码。
-   **Prompt 结构化:** 向 LLM 提供自然语言描述时，尝试按对象定义、然后按时间顺序描述动作的方式组织，有助于生成结构良好的 JSON 和 AST。
-   **验证:** 生成的 JSON 应能通过基于此 Schema 的验证，并且满足"先创建后使用"等逻辑约束（这些通常在 AST 语义分析阶段检查）。

**请 LLM 严格按照这些原则和 Schema 定义来生成 JSON 文件，为后续的 AST 构建和代码生成打下良好基础。**

## DSL Schema

version: 1.1

### 1.1 元数据 (Metadata)

```json
"metadata": {
  "title": "字符串 (必须是合法Python类名)",
  "author": "字符串",
  "resolution": "字符串 (枚举: 720p|1080p|1440p|2160p, 默认 '1080p')",
  "estimated_duration": "数字(秒, 可选)",
  "background_color": "颜色字符串(CSS颜色名或#RRGGBB, 默认 'BLACK')"
}
```

### 1.2 对象类型及属性 (Objects)

*(强调 ID 唯一性和推荐命名)*

```json
{
  "id": "全局唯一标识符 (字符串, 推荐 snake_case 或 kebab-case)",
  "type": "...",
  "properties": {
    // ... 静态属性 ...
    // **绝对不要** 在这里包含 position, scale, rotation 等动态或初始状态属性
  }
}
```

**基本形状 (Basic Shapes)**
```json
{
  "id": "my_shape_id",
  "type": "circle|square|rectangle|triangle|line|arrow|dot|star",
  "properties": {
    // 通用属性
    "color": "颜色字符串 (Manim常量名或Hex值, 默认 'WHITE')",
    "fill_opacity": "数字(0-1, 默认 0)",
    "stroke_width": "数字(默认 4)",
    "stroke_color": "颜色字符串 (Manim常量名或Hex值, 默认继承 color)",
    "z_index": "数字(默认 0)",

    // 特定形状属性
    "radius": "数字(circle)",
    "side_length": "数字(square)",
    "width": "数字(rectangle)",
    "height": "数字(rectangle)",
    "start": "[x, y, z] (line/arrow起点, 推荐z=0)",
    "end": "[x, y, z] (line/arrow终点, 推荐z=0)"
  }
}
```

### 1.3 文本和公式 (Text & Formulas)
```json
{
  "id": "my_text_id",
  "type": "text|tex|math_tex",
  "properties": {
    "content": "字符串", // **重要:** 若 type 为 'tex' 或 'math_tex', content **必须** 只包含 ASCII 字符 (因使用 LaTeX 编译)
    "font_size": "数字(默认 48)",
    "color": "颜色字符串 (Manim常量名或Hex值, 默认 'WHITE')",
    "alignment": "字符串(枚举: left|center|right, tex/math_tex通常忽略, 默认 'center' for text)"
  }
}
```

### 1.4 代码块 (Code Blocks)
```json
{
  "id": "my_code_block_id",
  "type": "code",
  "properties": {
    "code_string": "字符串 (代码内容, 与 code_file 二选一)",
    "code_file": "字符串 (代码文件路径, 与 code_string 二选一)",
    "language": "字符串 (可选, 指定编程语言, 例如 'python', 'json', 'cpp'. 如果未指定, 会尝试猜测)",
    "formatter_style": "字符串 (可选, Pygments 风格名称, 例如 'monokai', 'vim', 'native', 默认 'vim'. 控制高亮主题)",
    "tab_width": "数字 (可选, tab 对应的空格数, 默认 4)",
    "add_line_numbers": "布尔值(可选, 是否显示行号, 默认 true)",
    "line_numbers_from": "数字(可选, 起始行号, 默认 1)",
    "background": "字符串(可选, 背景形状 'rectangle' 或 'window', 默认 'rectangle')"
  }
}
```

### 1.5 图像和图表 (Images & Graphs)
```json
{
  "id": "my_image_or_graph_id",
  "type": "image|graph|coordinate_system",
  "properties": {
    // 图像属性
    "file_path": "字符串(image, 相对或绝对路径)",
    "height": "数字(image, Mobject单位, 控制大小)",

    // 图表/坐标系属性
    "x_range": "[最小值, 最大值, 步长]",
    "y_range": "[最小值, 最大值, 步长]",
    "x_length": "数字(坐标轴屏幕长度)",
    "y_length": "数字(坐标轴屏幕长度)",
    "x_label": "字符串(可选, **仅ASCII**)",
    "y_label": "字符串(可选, **仅ASCII**)",
    "include_numbers": "布尔值(coordinate_system, 默认 false)",
    "axis_config": { /* ... */ },
    "tips": "布尔值(graph箭头提示, 默认 false)"
  }
}
```

### 1.6 组合对象 (Groups)
```json
{
  "id": "my_group_id",
  "type": "group",
  "properties": {
    "members": ["对象ID1", "对象ID2", ...], // **必须** 引用已在 objects 中定义的 ID
    "layout": { // 可选: 定义组成员的自动布局 (在 create group 时生效)
      "type": "horizontal|vertical",
      "buffer": "数字(默认 0.25)",
      "alignment": "UP|DOWN|LEFT|RIGHT|CENTER (可选)"
    }
  }
}
```
*带有 `layout` 的 Group 在 `create` 时会自动根据布局属性排列其成员（成员需要先被 `create`）。排列后的整体位置由 `create` 动作的 `position` 控制。*

### 1.7 动作序列 (Action Sequence)

```json
"actions": [
  // ... Action Objects 按顺序排列 ...
]
```

#### 1.7.2 动作类型及属性 (Action Types)

**创建和删除 (Create & Remove)**
```json
{
  "type": "create",
  "target": "对象ID (来自 Objects)",
  "animation": "fade_in|draw|write|grow|none",
  "duration": "数字(秒, 默认 1)",
  "position": "[x, y, z](可选, 默认 [0,0,0])"
}

{
  "type": "remove",
  "target": "对象ID (必须已创建)",
  "animation": "fade_out|unwrite|shrink|none",
  "duration": "数字(秒, 默认 1)"
}
```

#### 1.7.3 变换和移动 (Transform & Move)
```json
{
  "type": "transform",
  "target": "对象ID (必须已创建)",
  "properties": { // 目标属性和值
    "position": "[x, y, z]",
    "scale": "数字 或 [x, y, z]比例",
    "rotation": "数字(绕Z轴度数)",
    "color": "颜色字符串 (Manim常量名或Hex值)",
    "fill_opacity": "数字(0-1)",
    // ... 其他可动画属性
  },
  "duration": "数字(秒, 默认 1)",
  "easing": "linear|smooth|elastic|... (默认 'smooth')"
}

{
  "type": "move_to",
  "target": "对象ID (必须已创建)",
  "position": "[x, y, z](目标中心位置)",
  "duration": "数字(秒, 默认 1)",
  "path": "line|arc (默认 'line')"
}

{
  "type": "arrange",
  "target": "对象ID (必须已创建)",
  "relative_to": "另一个对象ID (必须已创建) | 场景锚点常量", // 见 1.7.4
  "direction": "UP|DOWN|LEFT|RIGHT|IN|OUT|TOP_LEFT|...",
  "buffer": "数字(默认 0.25)",
  "align_edge": "UP|DOWN|LEFT|RIGHT|CENTER (可选)",
  "duration": "数字(秒, 默认 0.5)"
}
```

#### 1.7.4 布局控制 (Layout Control)

**场景锚点常量 (可用作 `relative_to`, `reference_target`, `start_edge_of`, `end_edge_of`):**

*   `"ORIGIN"` (等同于 `[0, 0, 0]`)
*   `"SCREEN_CENTER"` (屏幕中心)
*   `"SCREEN_LEFT"`, `"SCREEN_RIGHT"`, `"SCREEN_TOP"`, `"SCREEN_BOTTOM"` (屏幕边缘中心点)
*   `"SCREEN_TOP_LEFT"`, `"SCREEN_TOP_RIGHT"`, `"SCREEN_BOTTOM_LEFT"`, `"SCREEN_BOTTOM_RIGHT"` (屏幕角落)

```json
{
  "type": "align",
  "targets": ["对象ID1", ...], // 必须已创建
  "reference_target": "参考对象ID | 场景锚点常量 (可选)",
  "edge": "LEFT|RIGHT|TOP|BOTTOM|CENTER_X|CENTER_Y",
  "duration": "数字(秒, 默认 0.5)"
  // 示例: 将 target1 和 target2 的左边缘对齐到 'ref_obj' 的右边缘
  // { "type": "align", "targets": ["target1", "target2"], "reference_target": "ref_obj", "edge": "LEFT", "align_edge_to": "RIGHT" } (需要扩展 schema 支持 align_edge_to)
  // 简单示例: 将 target1, target2 左对齐 (以最左边的为准)
  // { "type": "align", "targets": ["target1", "target2"], "edge": "LEFT" }
}

{
  "type": "distribute",
  "targets": ["对象ID1", ...], // 必须已创建, 顺序决定分布顺序
  "direction": "HORIZONTAL|VERTICAL",
  "spacing": "数字 (可选, 固定间距)", // 不指定则均匀分布
  "reference_frame": { // 可选: 定义分布范围
      "start_edge_of": "对象ID | 场景锚点常量",
      "end_edge_of": "对象ID | 场景锚点常量",
      "mode": "edges|centers (默认 'centers')"
  },
  "duration": "数字(秒, 默认 0.5)"
  // 示例: 将 t1, t2, t3 在屏幕左右边缘之间水平均匀分布(基于中心)
  // { "type": "distribute", "targets": ["t1", "t2", "t3"], "direction": "HORIZONTAL", "reference_frame": { "start_edge_of": "SCREEN_LEFT", "end_edge_of": "SCREEN_RIGHT" } }
}
```

#### 1.7.5 高亮和聚焦 (Highlight & Focus)
```json
{
  "type": "highlight",
  "target": "对象ID (必须已创建)",
  "color": "颜色字符串 (Manim常量名或Hex值, 默认 'YELLOW')",
  "duration": "数字(秒, 默认 1)"
}

{
  "type": "focus",
  "target": "对象ID",
  "duration": "数字(秒, 默认1)",
  "scale_factor": "数字(默认1.2)",
  "opacity_factor": "数字(0-1, 其他对象变暗程度, 默认0.5)"
}

{
  "type": "indicate",
  "target": "对象ID",
  "duration": "数字(秒, 默认1)",
  "scale_factor": "数字(默认1.1)",
  "color": "颜色字符串(可选, 短暂改变颜色)"
}
```

#### 1.7.6 等待和相机控制 (Wait & Camera)
```json
{
  "type": "wait",
  "duration": "数字(秒, **必需**)"
}

{
  "type": "camera",
  "action": "zoom|pan|move_to|reset",
  "scale": "数字(zoom: 缩放因子 >1 放大, <1 缩小)",
  "position": "[x, y, z](pan/move_to: 相机中心目标位置, 推荐z=0)",
  "duration": "数字(秒, 默认1)"
}
```

#### 1.7.7 替换 (Replace)
```json
{
  "type": "replace",
  "source": "对象ID (必须已创建)",
  "target": "新对象ID (必须在 Objects 中定义)",
  "animation": "transform|fade (默认 'transform')",
  "duration": "数字(秒, 默认 1)",
  "position_match": "boolean(可选, 默认 true)"
}
```

#### 1.7.8 并发动画 (Concurrency)
```json
{
  "type": "animate_group",
  "actions": [
    // ... 包含动画效果的 Action Objects ...
    // 例如: { "type": "create", "target": "obj_a", "position": [-2,0,0], "animation": "fade_in", "duration": 1.5 },
    //       { "type": "move_to", "target": "obj_b", "position": [2,0,0], "duration": 1.5 },
    //       { "type": "transform", "target": "obj_c", "properties": {"color": "RED"}, "duration": 1 }
  ]
  // duration: 可选，数字(秒)。如果不指定，通常由内部最长动画决定。
}
```
*   **意图:** 将一组独立的**动画动作**并发执行，视觉上同时发生，通常映射到单个 `self.play()` 调用。
*   **支持的子动作:** 列表 `actions` 中可以包含**带有动画效果**的动作，如:
    *   `create` (带有 `animation` 如 `fade_in`, `grow`, `write`, `draw`)
    *   `remove` (带有 `animation` 如 `fade_out`, `unwrite`, `shrink`)
    *   `move_to`, `arrange`
    *   `transform`
    *   `replace` (带有 `animation` 如 `transform`, `fade`)
*   **工作机制:**
    *   对于 `create` 子动作：对象本身会先被实例化并设置好位置，然后其指定的**动画效果** (如 `FadeIn(obj)`) 会被加入到并发执行的 `play()` 中。如果 `animation` 为 `"none"`，对象只会被创建和定位，不会加入 `play()`。
    *   对于 `move_to`, `transform` 等子动作：对应的 `.animate` 调用会被加入到并发执行的 `play()` 中。
*   **重要限制:**
    *   `animate_group` 内部的 `actions` 列表中的 `target` **必须** 引用在**此 `animate_group` 动作之前**已经被顶级 `create` 动作创建的对象（除非该子动作本身就是 `create` 类型）。
    *   `animate_group` 内部 **绝对禁止** 嵌套 `'wait'` 动作。`animate_group` 用于并发执行动画，`wait` 会引入顺序暂停，这与并发目的冲突，会导致错误。
*   **持续时间:** `animate_group` 整体的动画时长通常由其内部 `actions` 中**最长的动画时长**决定。如果 `animate_group` 本身指定了 `duration` 属性，可能会覆盖内部时长（具体行为取决于代码生成器）。

### 1.8 图表和函数 (Graphing)
```
