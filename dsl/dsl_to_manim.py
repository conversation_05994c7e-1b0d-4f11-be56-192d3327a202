#!/usr/bin/env python3

import os
import sys
import tempfile
from pathlib import Path

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dsl.ast_builder import ASTBuilder
from dsl.manim_code import ManimCodeGenerator
from dsl.parser import ManimDSLParser


def generate_manim_code(dsl_file_path):
    """Generate Manim code from a DSL file."""
    try:
        # Parse DSL file
        dsl = ManimDSLParser.parse_from_file(dsl_file_path)

        # Build AST
        ast = ASTBuilder.build_ast(dsl)

        # Generate code
        generator = ManimCodeGenerator()
        code = generator.visit_scene(ast)

        # Create a temporary file for the generated code
        with tempfile.NamedTemporaryFile(suffix=".py", delete=False, mode="w") as f:
            f.write(code)
            output_file = f.name

        print(f"Generated Manim code in: {output_file}")
        return output_file

    except Exception as e:
        print(f"Error generating code: {e}")
        import traceback

        traceback.print_exc()
        return None


def render_animation(manim_file, quality="l"):
    """Render the animation using Manim."""
    cmd = f"manim -p --disable_caching -q{quality} {manim_file}"
    print(f"Running: {cmd}")
    os.system(cmd)


def render_dsl(dsl_file_path, quality="l"):
    """Generate Manim code from a DSL file and render the animation.

    Args:
        dsl_file_path: Path to the DSL JSON file
        quality: Manim quality setting (l=low, m=medium, h=high)

    Returns:
        Path to the generated Manim file or None if an error occurred
    """
    manim_file = generate_manim_code(dsl_file_path)
    if manim_file:
        render_animation(Path(manim_file), quality)
        return manim_file
    return None


def main():
    if len(sys.argv) < 2:
        print("Usage: python dsl_to_manim.py <dsl_file_path> [quality]")
        return

    dsl_file_path = sys.argv[1]
    quality = sys.argv[2] if len(sys.argv) > 2 else "l"

    # Generate Manim code and render animation
    render_dsl(dsl_file_path, quality)


if __name__ == "__main__":
    main()
