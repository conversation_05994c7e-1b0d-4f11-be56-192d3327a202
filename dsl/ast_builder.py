from enum import Enum
from typing import Any, Optional

from .parser import (
    ActionType,
    AddPointAction,
    AlignAction,
    AnimateGroupAction,
    ArrangeAction,
    CameraAction,
    CreateAction,
    DistributeAction,
    FocusAction,
    GroupProperties,
    HighlightAction,
    IndicateAction,
    ManimDSL,
    Metadata,
    MoveToAction,
    ObjectDefinition,
    PlotFunctionAction,
    RemoveAction,
    ReplaceAction,
    TransformAction,
    WaitAction,
)


class NodeType(str, Enum):
    SCENE = "scene"
    METADATA = "metadata"
    OBJECT_DEFINITION = "object_definition"
    ACTION = "action"
    GROUP = "group"


# AST节点基类
class ASTNode:
    """抽象语法树节点基类"""

    def __init__(self, node_type: NodeType):
        self.node_type = node_type

    def __repr__(self):
        result = ""
        for key, value in self.__dict__.items():
            if value is not None:
                result += f" {key}={value}"
        return result


# 元数据节点
class MetadataNode(ASTNode):
    """元数据节点，包含场景的全局设置"""

    def __init__(self, metadata: Metadata):
        super().__init__(NodeType.METADATA)
        self.title = metadata.title
        self.author = metadata.author
        self.resolution = metadata.resolution
        self.background_color = metadata.background_color
        self.estimated_duration = metadata.estimated_duration


# 对象定义节点
class ObjectDefinitionNode(ASTNode):
    """对象定义节点，表示场景中的一个对象蓝图"""

    def __init__(self, obj_def: ObjectDefinition):
        super().__init__(NodeType.OBJECT_DEFINITION)
        self.id = obj_def.id
        self.object_type = obj_def.type
        self.properties = obj_def.properties.model_dump()

        # 特殊处理组成员
        if self.object_type == "group" and isinstance(obj_def.properties, GroupProperties):
            self.member_ids = obj_def.properties.members
            # 如果有布局，提取布局信息
            if obj_def.properties.layout:
                self.layout = {
                    "type": obj_def.properties.layout.type,
                    "buffer": obj_def.properties.layout.buffer,
                    "alignment": obj_def.properties.layout.alignment,
                }
            else:
                self.layout = None
        else:
            self.member_ids = None
            self.layout = None


# 动作节点基类
class ActionNode(ASTNode):
    """动作节点基类，表示时间线上的一个动作"""

    def __init__(self, action_type: str, duration: Optional[float] = 1.0):
        super().__init__(NodeType.ACTION)
        self.action_type = action_type
        self.duration = duration


# 具体动作节点类型
class CreateActionNode(ActionNode):
    """创建动作节点，表示将对象添加到场景"""

    def __init__(self, action: CreateAction):
        super().__init__("create", action.duration)
        self.target_id = action.target
        self.animation = action.animation
        self.position = action.position


class RemoveActionNode(ActionNode):
    """移除动作节点，表示从场景中移除对象"""

    def __init__(self, action: RemoveAction):
        super().__init__("remove", action.duration)
        self.target_id = action.target
        self.animation = action.animation


class TransformActionNode(ActionNode):
    """变换动作节点，表示改变对象的属性"""

    def __init__(self, action: TransformAction):
        super().__init__("transform", action.duration)
        self.target_id = action.target
        self.target_properties = action.properties
        self.easing = action.easing


class MoveToActionNode(ActionNode):
    """移动到动作节点，表示将对象移动到指定位置"""

    def __init__(self, action: MoveToAction):
        super().__init__("move_to", action.duration)
        self.target_id = action.target
        self.position = action.position
        self.path = action.path


class ArrangeActionNode(ActionNode):
    """排列动作节点，表示相对于其他对象排列"""

    def __init__(self, action: ArrangeAction):
        super().__init__("arrange", action.duration)
        self.target_id = action.target
        self.relative_to_id = action.relative_to
        self.direction = action.direction
        self.buffer = action.buffer
        self.align_edge = action.align_edge


class AlignActionNode(ActionNode):
    """对齐动作节点，表示将多个对象对齐"""

    def __init__(self, action: AlignAction):
        super().__init__("align", action.duration)
        self.target_ids = action.targets
        self.reference_target_id = action.reference_target
        self.edge = action.edge


class DistributeActionNode(ActionNode):
    """分布动作节点，表示将多个对象均匀分布"""

    def __init__(self, action: DistributeAction):
        super().__init__("distribute", action.duration)
        self.target_ids = action.targets
        self.direction = action.direction
        self.spacing = action.spacing
        self.reference_frame = action.reference_frame.model_dump() if action.reference_frame else None


class HighlightActionNode(ActionNode):
    """高亮动作节点，表示高亮显示对象"""

    def __init__(self, action: HighlightAction):
        super().__init__("highlight", action.duration)
        self.target_id = action.target
        self.color = action.color


class FocusActionNode(ActionNode):
    """聚焦动作节点，表示聚焦于对象"""

    def __init__(self, action: FocusAction):
        super().__init__("focus", action.duration)
        self.target_id = action.target
        self.scale_factor = action.scale_factor
        self.opacity_factor = action.opacity_factor


class IndicateActionNode(ActionNode):
    """指示动作节点，表示指示对象"""

    def __init__(self, action: IndicateAction):
        super().__init__("indicate", action.duration)
        self.target_id = action.target
        self.scale_factor = action.scale_factor
        self.color = action.color


class WaitActionNode(ActionNode):
    """等待动作节点，表示暂停动画"""

    def __init__(self, action: WaitAction):
        super().__init__("wait", action.duration)


class CameraActionNode(ActionNode):
    """相机动作节点，表示相机移动或缩放"""

    def __init__(self, action: CameraAction):
        super().__init__("camera", action.duration)
        self.camera_action_type = action.action
        self.scale = action.scale
        self.position = action.position


class ReplaceActionNode(ActionNode):
    """替换动作节点，表示用一个对象替换另一个"""

    def __init__(self, action: ReplaceAction):
        super().__init__("replace", action.duration)
        self.source_id = action.source
        self.target_id = action.target
        self.animation = action.animation
        self.position_match = action.position_match


class AnimateGroupNode(ActionNode):
    """动画组节点，表示一组并发的动画"""

    def __init__(self, action: AnimateGroupAction):
        super().__init__("animate_group")
        self.child_actions: list[ActionNode] = []

        # 转换子动作为对应的ActionNode对象
        for sub_action in action.actions:
            child_node = ASTBuilder.create_action_node(sub_action)
            if child_node:
                self.child_actions.append(child_node)

        # 使用子动作中最长的持续时间
        self.duration = max(
            action.duration or 0,
            max((child.duration or 0) for child in self.child_actions) if self.child_actions else 0,
        )


class PlotFunctionActionNode(ActionNode):
    """绘制函数动作节点，表示在图表上绘制函数"""

    def __init__(self, action: PlotFunctionAction):
        super().__init__("plot_function", action.duration)
        self.target_graph_id = action.target
        self.function_str = action.function
        self.color = action.color
        self.x_range = action.x_range
        self.animation = action.animation


class AddPointActionNode(ActionNode):
    """添加点动作节点，表示在图表上添加点"""

    def __init__(self, action: AddPointAction):
        super().__init__("add_point", action.duration)
        self.target_graph_id = action.target
        self.position = action.position
        self.color = action.color
        self.label = action.label
        self.animation = action.animation


# 场景AST根节点
class SceneAST(ASTNode):
    """场景AST根节点，包含整个场景的所有信息"""

    def __init__(self, metadata_node: MetadataNode):
        super().__init__(NodeType.SCENE)
        self.metadata = metadata_node
        self.object_definitions: dict[str, ObjectDefinitionNode] = {}
        self.action_sequence: list[ActionNode] = []

    def add_object_definition(self, obj_def_node: ObjectDefinitionNode) -> None:
        """添加对象定义节点"""
        self.object_definitions[obj_def_node.id] = obj_def_node

    def add_action(self, action_node: ActionNode) -> None:
        """添加动作节点到动作序列"""
        self.action_sequence.append(action_node)


# AST生成器
class ASTBuilder:
    """AST生成器，将JSON DSL解析为AST"""

    @staticmethod
    def build_ast(dsl: ManimDSL) -> SceneAST:
        """从DSL构建AST"""
        # 创建元数据节点
        metadata_node = MetadataNode(dsl.metadata)

        # 创建场景AST根节点
        scene_ast = SceneAST(metadata_node)

        # 添加所有对象定义
        for obj_def in dsl.objects:
            obj_def_node = ObjectDefinitionNode(obj_def)
            scene_ast.add_object_definition(obj_def_node)

        # 添加所有动作
        for action in dsl.actions:
            action_node = ASTBuilder.create_action_node(action)
            if action_node:
                scene_ast.add_action(action_node)

        return scene_ast

    @staticmethod
    def create_action_node(action: ActionType) -> Optional[ActionNode]:
        """根据动作类型创建相应的动作节点"""
        if isinstance(action, CreateAction):
            return CreateActionNode(action)
        elif isinstance(action, RemoveAction):
            return RemoveActionNode(action)
        elif isinstance(action, TransformAction):
            return TransformActionNode(action)
        elif isinstance(action, MoveToAction):
            return MoveToActionNode(action)
        elif isinstance(action, ArrangeAction):
            return ArrangeActionNode(action)
        elif isinstance(action, AlignAction):
            return AlignActionNode(action)
        elif isinstance(action, DistributeAction):
            return DistributeActionNode(action)
        elif isinstance(action, HighlightAction):
            return HighlightActionNode(action)
        elif isinstance(action, FocusAction):
            return FocusActionNode(action)
        elif isinstance(action, IndicateAction):
            return IndicateActionNode(action)
        elif isinstance(action, WaitAction):
            return WaitActionNode(action)
        elif isinstance(action, CameraAction):
            return CameraActionNode(action)
        elif isinstance(action, ReplaceAction):
            return ReplaceActionNode(action)
        elif isinstance(action, AnimateGroupAction):
            return AnimateGroupNode(action)
        elif isinstance(action, PlotFunctionAction):
            return PlotFunctionActionNode(action)
        elif isinstance(action, AddPointAction):
            return AddPointActionNode(action)
        return None


# AST节点访问器接口
class ASTVisitor:
    """AST节点访问器接口，用于遍历AST"""

    def visit_scene(self, node: SceneAST) -> Any:
        """访问场景节点"""
        pass

    def visit_metadata(self, node: MetadataNode) -> Any:
        """访问元数据节点"""
        pass

    def visit_object_definition(self, node: ObjectDefinitionNode) -> Any:
        """访问对象定义节点"""
        pass

    def visit_action(self, node: ActionNode) -> Any:
        """访问动作节点"""
        pass
