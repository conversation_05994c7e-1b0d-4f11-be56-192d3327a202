"""Object generators for Manim code generation."""

from dsl.ast_builder import ObjectDefinitionNode


class ObjectGenerator:
    """Generates Manim code for different types of objects."""

    def __init__(self, code_generator):
        """Initialize the object generator.

        Args:
            code_generator: The parent code generator that provides utility methods.
        """
        self.code_gen = code_generator

    def create_object(self, obj_def: ObjectDefinitionNode, obj_var: str):
        """Generate code for creating different types of objects.

        Args:
            obj_def: Object definition from AST
            obj_var: Variable name to assign the object to
        """
        obj_type = obj_def.object_type

        # Dispatch to appropriate object generator method
        method_name = f"_create_{obj_type}"
        if hasattr(self, method_name):
            # Call the specific object generator method
            getattr(self, method_name)(obj_def, obj_var)
        else:
            # Fallback for unsupported types
            self.code_gen.add_line(f"# Unsupported object type: {obj_type}")
            self.code_gen.add_line(f"{obj_var} = Dot()")  # Default fallback

    # Basic shapes
    def _create_circle(self, obj_def: ObjectDefinitionNode, obj_var: str):
        radius = obj_def.properties.get("radius", 1.0)
        color = obj_def.properties.get("color", "WHITE")
        fill_opacity = obj_def.properties.get("fill_opacity", 0.0)
        stroke_width = obj_def.properties.get("stroke_width", 4)
        stroke_color = obj_def.properties.get("stroke_color", color)
        self.code_gen.add_line(
            f'{obj_var} = Circle(radius={radius}, color="{color}", fill_opacity={fill_opacity}, stroke_width={stroke_width}, stroke_color="{stroke_color}")',
        )

    def _create_square(self, obj_def: ObjectDefinitionNode, obj_var: str):
        side_length = obj_def.properties.get("side_length", 2.0)
        color = obj_def.properties.get("color", "WHITE")
        fill_opacity = obj_def.properties.get("fill_opacity", 0.0)
        stroke_width = obj_def.properties.get("stroke_width", 4)
        stroke_color = obj_def.properties.get("stroke_color", color)
        self.code_gen.add_line(
            f'{obj_var} = Square(side_length={side_length}, color="{color}", fill_opacity={fill_opacity}, stroke_width={stroke_width}, stroke_color="{stroke_color}")',
        )

    def _create_rectangle(self, obj_def: ObjectDefinitionNode, obj_var: str):
        width = obj_def.properties.get("width", 3.0)
        height = obj_def.properties.get("height", 2.0)
        color = obj_def.properties.get("color", "WHITE")
        fill_opacity = obj_def.properties.get("fill_opacity", 0.0)
        stroke_width = obj_def.properties.get("stroke_width", 4)
        stroke_color = obj_def.properties.get("stroke_color", color)
        self.code_gen.add_line(
            f'{obj_var} = Rectangle(width={width}, height={height}, color="{color}", fill_opacity={fill_opacity}, stroke_width={stroke_width}, stroke_color="{stroke_color}")',
        )

    def _create_triangle(self, obj_def: ObjectDefinitionNode, obj_var: str):
        color = obj_def.properties.get("color", "WHITE")
        fill_opacity = obj_def.properties.get("fill_opacity", 0.0)
        stroke_width = obj_def.properties.get("stroke_width", 4)
        stroke_color = obj_def.properties.get("stroke_color", color)
        self.code_gen.add_line(
            f'{obj_var} = Triangle(color="{color}", fill_opacity={fill_opacity}, stroke_width={stroke_width}, stroke_color="{stroke_color}")',
        )

    def _create_line(self, obj_def: ObjectDefinitionNode, obj_var: str):
        start = obj_def.properties.get("start", [0, 0, 0])
        end = obj_def.properties.get("end", [1, 0, 0])
        start_str = f"[{start[0]}, {start[1]}, {start[2] if len(start) > 2 else 0}]"
        end_str = f"[{end[0]}, {end[1]}, {end[2] if len(end) > 2 else 0}]"
        color = obj_def.properties.get("color", "WHITE")
        stroke_width = obj_def.properties.get("stroke_width", 4)
        self.code_gen.add_line(
            f'{obj_var} = Line(start=np.array({start_str}), end=np.array({end_str}), color="{color}", stroke_width={stroke_width})',
        )

    def _create_arrow(self, obj_def: ObjectDefinitionNode, obj_var: str):
        start = obj_def.properties.get("start", [0, 0, 0])
        end = obj_def.properties.get("end", [1, 0, 0])
        start_str = f"[{start[0]}, {start[1]}, {start[2] if len(start) > 2 else 0}]"
        end_str = f"[{end[0]}, {end[1]}, {end[2] if len(end) > 2 else 0}]"
        color = obj_def.properties.get("color", "WHITE")
        stroke_width = obj_def.properties.get("stroke_width", 4)
        self.code_gen.add_line(
            f'{obj_var} = Arrow(start=np.array({start_str}), end=np.array({end_str}), color="{color}", stroke_width={stroke_width})',
        )

    def _create_dot(self, obj_def: ObjectDefinitionNode, obj_var: str):
        radius = obj_def.properties.get("radius", 0.1)
        color = obj_def.properties.get("color", "WHITE")
        fill_opacity = obj_def.properties.get("fill_opacity", 1.0)
        self.code_gen.add_line(f'{obj_var} = Dot(radius={radius}, color="{color}", fill_opacity={fill_opacity})')

    def _create_star(self, obj_def: ObjectDefinitionNode, obj_var: str):
        color = obj_def.properties.get("color", "WHITE")
        radius = obj_def.properties.get("radius") or 1.0
        fill_opacity = obj_def.properties.get("fill_opacity", 1.0)
        n_points = obj_def.properties.get("n_points", 5)
        self.code_gen.add_line(
            f'{obj_var} = Star(n={n_points}, outer_radius={radius}, color="{color}", fill_opacity={fill_opacity})',
        )

    # Text and formulas
    def _create_text(self, obj_def: ObjectDefinitionNode, obj_var: str):
        content = obj_def.properties.get("content", "")
        font_size = obj_def.properties.get("font_size", 36)
        color = obj_def.properties.get("color", "WHITE")
        font = obj_def.properties.get("font", "Maple Mono NF CN")
        self.code_gen.add_line(
            f'{obj_var} = Text("""{content}""", font_size={font_size}, color="{color}", font="{font}")',
        )

    def _create_tex(self, obj_def: ObjectDefinitionNode, obj_var: str):
        content = obj_def.properties.get("content", "")
        font_size = obj_def.properties.get("font_size", 48)
        color = obj_def.properties.get("color", "WHITE")
        self.code_gen.add_line(f'{obj_var} = Tex(r"{content}", color="{color}", font_size={font_size})')

    def _create_math_tex(self, obj_def: ObjectDefinitionNode, obj_var: str):
        content = obj_def.properties.get("content", "")
        font_size = obj_def.properties.get("font_size", 48)
        color = obj_def.properties.get("color", "WHITE")
        self.code_gen.add_line(f'{obj_var} = MathTex(r"{content}", color="{color}", font_size={font_size})')

    # Code blocks
    def _create_code(self, obj_def: ObjectDefinitionNode, obj_var: str):
        code_string = obj_def.properties.get("code_string")
        code_file = obj_def.properties.get("code_file")
        language = obj_def.properties.get("language")  # Optional, let Manim guess if None
        formatter_style = obj_def.properties.get("formatter_style", "vim")
        tab_width = obj_def.properties.get("tab_width", 4)
        add_line_numbers = obj_def.properties.get("add_line_numbers", True)
        line_numbers_from = obj_def.properties.get("line_numbers_from", 1)
        background = obj_def.properties.get("background", "rectangle")
        background_config = obj_def.properties.get("background_config", {})
        paragraph_config = obj_def.properties.get("paragraph_config", {})

        if not code_string and not code_file:
            self.code_gen.add_line(f'# Warning: Code object "{obj_def.id}" has no code_string or code_file')
            self.code_gen.add_line(f'{obj_var} = Code(code_string="", language="text")')  # Minimal fallback
            return

        # Start Code Mobject creation
        self.code_gen.add_line(f"{obj_var} = Code(")
        if code_string:
            self.code_gen.add_line(f'    code_string="""{code_string}""",')
        elif code_file:
            self.code_gen.add_line(f'    code_file="{code_file}",')

        if language:
            self.code_gen.add_line(f'    language="{language}",')

        self.code_gen.add_line(f'    formatter_style="{formatter_style}",')
        self.code_gen.add_line(f"    tab_width={tab_width},")
        self.code_gen.add_line(f"    add_line_numbers={add_line_numbers},")
        self.code_gen.add_line(f"    line_numbers_from={line_numbers_from},")
        self.code_gen.add_line(f'    background="{background}",')

        if background_config:
            bg_config_str = ", ".join(
                [f'"{k}": "{v}"' if isinstance(v, str) else f'"{k}": {v}' for k, v in background_config.items()]
            )
            self.code_gen.add_line(f"    background_config={{{bg_config_str}}},")

        if paragraph_config:
            p_config_str = ", ".join(
                [f'"{k}": "{v}"' if isinstance(v, str) else f'"{k}": {v}' for k, v in paragraph_config.items()]
            )
            self.code_gen.add_line(f"    paragraph_config={{{p_config_str}}},")

        self.code_gen.add_line(")")

        # Initial opacity is handled by Manim's defaults or background_config['fill_opacity']
        # No need to set opacity separately unless transforming

    # Images and graphs
    def _create_image(self, obj_def: ObjectDefinitionNode, obj_var: str):
        file_path = obj_def.properties.get("file_path", "")
        scale = obj_def.properties.get("scale", 1)
        self.code_gen.add_line(f'{obj_var} = ImageMobject("{file_path}")')
        if scale:
            self.code_gen.add_line(f"{obj_var}.scale({scale})")

    def _create_coordinate_system(self, obj_def: ObjectDefinitionNode, obj_var: str):
        x_range = obj_def.properties.get("x_range", [-10, 10, 1])
        y_range = obj_def.properties.get("y_range", [-10, 10, 1])
        x_length = obj_def.properties.get("x_length", 6)
        y_length = obj_def.properties.get("y_length", 6)
        x_label = obj_def.properties.get("x_label", "")
        y_label = obj_def.properties.get("y_label", "")
        include_numbers = obj_def.properties.get("include_numbers", False)
        tips = obj_def.properties.get("tips", False)

        axis_config = {}
        if include_numbers:
            axis_config["include_numbers"] = True

        if tips:
            axis_config["include_tip"] = True

        axis_config_str = ", ".join([f'"{k}": {v}' for k, v in axis_config.items()]) if axis_config else ""

        self.code_gen.add_line(f"{obj_var} = Axes(")
        self.code_gen.add_line(f"    x_range=[{x_range[0]}, {x_range[1]}, {x_range[2]}],")
        self.code_gen.add_line(f"    y_range=[{y_range[0]}, {y_range[1]}, {y_range[2]}],")
        self.code_gen.add_line(f"    x_length={x_length}, y_length={y_length},")
        if axis_config_str:
            self.code_gen.add_line(f"    axis_config={{{axis_config_str}}},")
        self.code_gen.add_line(")")

        if x_label or y_label:
            self.code_gen.add_line(f"{obj_var}_labels = {obj_var}.get_axis_labels(")
            if x_label:
                self.code_gen.add_line(f'    x_label=r"{x_label}",')
            if y_label:
                self.code_gen.add_line(f'    y_label=r"{y_label}"')
            self.code_gen.add_line(")")
            self.code_gen.add_line(f"{obj_var} = VGroup({obj_var}, {obj_var}_labels)")

    def _create_graph(self, obj_def: ObjectDefinitionNode, obj_var: str):
        x_range = obj_def.properties.get("x_range", [-10, 10, 1])
        y_range = obj_def.properties.get("y_range", [-10, 10, 1])
        x_length = obj_def.properties.get("x_length", 6)
        y_length = obj_def.properties.get("y_length", 6)
        x_label = obj_def.properties.get("x_label", "")
        y_label = obj_def.properties.get("y_label", "")
        include_numbers = obj_def.properties.get("include_numbers", False)
        tips = obj_def.properties.get("tips", False)

        axis_config = {}
        if include_numbers:
            axis_config["include_numbers"] = True

        if tips:
            axis_config["include_tip"] = True

        axis_config_str = ", ".join([f'"{k}": {v}' for k, v in axis_config.items()]) if axis_config else ""

        self.code_gen.add_line(f"{obj_var}_axes = Axes(")
        self.code_gen.add_line(f"    x_range=[{x_range[0]}, {x_range[1]}, {x_range[2]}],")
        self.code_gen.add_line(f"    y_range=[{y_range[0]}, {y_range[1]}, {y_range[2]}],")
        self.code_gen.add_line(f"    x_length={x_length}, y_length={y_length},")
        if axis_config_str:
            self.code_gen.add_line(f"    axis_config={{{axis_config_str}}},")
        self.code_gen.add_line(")")
        self.code_gen.add_line(f"{obj_var} = {obj_var}_axes")  # For now, graph is just the axes

        if x_label or y_label:
            self.code_gen.add_line(f"{obj_var}_labels = {obj_var}_axes.get_axis_labels(")
            if x_label:
                self.code_gen.add_line(f'    x_label=r"{x_label}",')
            if y_label:
                self.code_gen.add_line(f'    y_label=r"{y_label}"')
            self.code_gen.add_line(")")
            self.code_gen.add_line(f"{obj_var} = VGroup({obj_var}_axes, {obj_var}_labels)")

    # Group objects
    def _create_group(self, obj_def: ObjectDefinitionNode, obj_var: str):
        members = obj_def.properties.get("members", [])
        if members:
            members_str = ", ".join([self.code_gen.obj_names[member_id] for member_id in members])
            self.code_gen.add_line(f"{obj_var} = VGroup({members_str})")

            # Handle layout if specified
            layout = obj_def.properties.get("layout", {})
            if layout:
                layout_type = layout.get("type", "")
                buffer = layout.get("buffer", 0.25)
                alignment = layout.get("alignment", "")

                align_str = ""
                if alignment:
                    align_dict = {
                        "UP": "aligned_edge=UP",
                        "DOWN": "aligned_edge=DOWN",
                        "LEFT": "aligned_edge=LEFT",
                        "RIGHT": "aligned_edge=RIGHT",
                        "CENTER": "",
                    }
                    align_str = align_dict.get(alignment, "")

                if layout_type == "horizontal":
                    self.code_gen.add_line(f"{obj_var}.arrange(RIGHT, buff={buffer}, {align_str})")
                elif layout_type == "vertical":
                    self.code_gen.add_line(f"{obj_var}.arrange(DOWN, buff={buffer}, {align_str})")
        else:
            self.code_gen.add_line(f"{obj_var} = VGroup()")  # Empty group
