"""Main Manim code generator that integrates all components."""

from dsl.ast_builder import ASTVisitor, SceneAST
from dsl.manim_code.object_generators import ObjectGenerator


class ManimCodeGenerator(ASTVisitor):
    """Main code generator that generates Manim code from an AST."""

    def __init__(self):
        """Initialize the code generator."""
        self.code_lines = []
        self.indent_level = 0
        self.obj_names = {}  # Maps object IDs to variable names
        self.current_scene = None  # Will be set by visit_scene

        # Initialize object generator
        self.object_generator = ObjectGenerator(self)

        # Import generators here to break circular dependency
        from dsl.manim_code.action_generators import ActionGenerator

        # Initialize action generators
        self.action_generator = ActionGenerator(self)

    def indent(self, text):
        """Indent a line of code according to the current indentation level."""
        return " " * 4 * self.indent_level + text

    def add_line(self, line=""):
        """Add a line of code to the code buffer with proper indentation."""
        self.code_lines.append(self.indent(line))

    def visit_scene(self, node: SceneAST):
        """Generate code for a scene AST node."""
        self.current_scene = node

        # Generate imports
        self.add_line("from manim import *")
        self.add_line("import numpy as np")

        # Check if we need camera movements
        needs_camera_movement = any(getattr(action, "action_type", None) == "camera" for action in node.action_sequence)

        if needs_camera_movement:
            self.scene_is_movable_camera = True

        self.add_line()

        # Configure resolution
        self.add_line("config.pixel_width = 1920")
        self.add_line("config.pixel_height = 1080")
        self.add_line(f'config.background_color = "{node.metadata.background_color}"')
        self.add_line()

        # Generate scene class with appropriate parent
        parent_class = "MovingCameraScene" if needs_camera_movement else "Scene"
        self.add_line(f"class {node.metadata.title}({parent_class}):")
        self.indent_level += 1
        self.add_line("def construct(self):")
        self.indent_level += 1

        # Define scene constants
        self.add_scene_constants()

        # Generate object variables for tracking
        for obj_id, obj_def in node.object_definitions.items():
            self.obj_names[obj_id] = f"mobj_{obj_id}"

        # Process actions
        for action in node.action_sequence:
            self.visit_action(action)

        # End indentation
        self.indent_level -= 2

        return "\n".join(self.code_lines)

    def add_scene_constants(self):
        """Define common scene constants that can be used as reference points."""
        self.add_line("# Scene reference points")
        self.add_line("SCREEN_CENTER = ORIGIN")
        self.add_line("SCREEN_LEFT = LEFT * config.frame_width / 2")
        self.add_line("SCREEN_RIGHT = RIGHT * config.frame_width / 2")
        self.add_line("SCREEN_TOP = UP * config.frame_height / 2")
        self.add_line("SCREEN_BOTTOM = DOWN * config.frame_height / 2")
        self.add_line("SCREEN_TOP_LEFT = SCREEN_TOP + SCREEN_LEFT")
        self.add_line("SCREEN_TOP_RIGHT = SCREEN_TOP + SCREEN_RIGHT")
        self.add_line("SCREEN_BOTTOM_LEFT = SCREEN_BOTTOM + SCREEN_LEFT")
        self.add_line("SCREEN_BOTTOM_RIGHT = SCREEN_BOTTOM + SCREEN_RIGHT")
        self.add_line()

    def visit_action(self, node):
        """Generate code for an action node."""
        # Try to process with the standard action generator
        if hasattr(node, "action_type"):
            # First try with the basic action generator
            self.action_generator.process_action(node)
