"""Action generators for Manim code generation."""

from typing import Optional, cast

import numpy as np

from dsl.ast_builder import (
    ActionNode,
    AddPointActionNode,
    AlignActionNode,
    AnimateGroupNode,
    ArrangeActionNode,
    CameraActionNode,
    CreateActionNode,
    DistributeActionNode,
    FocusActionNode,
    HighlightActionNode,
    IndicateActionNode,
    MoveToActionNode,
    ObjectDefinitionNode,
    PlotFunctionActionNode,
    RemoveActionNode,
    ReplaceActionNode,
    TransformActionNode,
    WaitActionNode,
)
from dsl.manim_code.code_generator import ManimCodeGenerator


class ActionGenerator:
    """Generates Manim code for different actions."""

    def __init__(self, code_generator: ManimCodeGenerator):
        """Initialize the action generator.

        Args:
            code_generator: The parent code generator that provides utility methods.
        """
        self.code_gen = code_generator

    def process_action(self, node: ActionNode):
        """Process an action node and generate the appropriate code.

        Args:
            node: The action node from the AST
        """
        # Add a comment to describe the action
        self.code_gen.add_line(f"# Action: {node.action_type}")

        # Dispatch to the appropriate action handler method
        method_name = f"_process_{node.action_type}"
        if hasattr(self, method_name):
            # Call the specific action handler method
            getattr(self, method_name)(node)
        else:
            self.code_gen.add_line(f"# Unsupported action type: {node.action_type}")

        # Add a blank line after each action
        # Don't add blank line if the previous line was already blank or if it's the start
        if self.code_gen.code_lines and self.code_gen.code_lines[-1].strip():
            self.code_gen.add_line()

    def _process_create(self, node: CreateActionNode):
        """Generate code for create action."""
        obj_def = None
        for (
            obj_id,
            definition,
        ) in self.code_gen.current_scene.object_definitions.items():
            if obj_id == node.target_id:
                obj_def = definition
                break

        if obj_def:
            obj_var = self.code_gen.obj_names[node.target_id]
            is_arrow = obj_def.object_type == "arrow"
            has_position = hasattr(node, "position") and node.position

            # Generate code for object creation based on its type
            self.code_gen.object_generator.create_object(obj_def, obj_var)

            # --- Arrow Specific Position Handling ---
            if is_arrow and has_position:
                # For arrows, 'position' acts as an anchor. Calculate absolute start/end.
                anchor_pos = np.array(
                    [node.position[0], node.position[1], node.position[2] if len(node.position) > 2 else 0]
                )
                start_offset = np.array(obj_def.properties.get("start", [0, 0, 0]))
                end_offset = np.array(obj_def.properties.get("end", [1, 0, 0]))

                abs_start = anchor_pos + start_offset
                abs_end = anchor_pos + end_offset

                # Modify the creation code to use absolute positions directly
                # Find the line where the arrow was created and modify it
                for i in range(len(self.code_gen.code_lines) - 1, -1, -1):
                    if self.code_gen.code_lines[i].strip().startswith(f"{obj_var} = Arrow("):
                        # Reconstruct the Arrow call with absolute start/end
                        # Extract original color and stroke_width (or other props)
                        left_index = self.code_gen.code_lines[i].find("Arrow(")
                        original_args = self.code_gen.code_lines[i][left_index + 6 : -1]
                        original_props = {}
                        for part in original_args.split(","):
                            key_value = part.split("=")
                            if len(key_value) == 2:
                                key = key_value[0].strip()
                                if key not in ["start", "end"]:
                                    original_props[key] = key_value[1].strip()

                        props_str = ", ".join([f"{k}={v}" for k, v in original_props.items()] + ["buff=0.0"])
                        self.code_gen.code_lines[
                            i
                        ] = f"        {obj_var} = Arrow(start=np.array([{abs_start[0]}, {abs_start[1]}, {abs_start[2]}]), end=np.array([{abs_end[0]}, {abs_end[1]}, {abs_end[2]}]), {props_str})"
                        break
                # Do not add a separate move_to for arrows handled this way
            # --- End Arrow Specific ---
            elif not is_arrow and has_position:  # Original positioning for non-arrows
                pos_str = (
                    f"[{node.position[0]}, {node.position[1]}, {node.position[2] if len(node.position) > 2 else 0}]"
                )
                self.code_gen.add_line(f"{obj_var}.move_to(np.array({pos_str}))")
            # else: Arrow without position or non-arrow without position - no move needed

            # Handle different animations
            animation = getattr(node, "animation", "none")
            duration = getattr(node, "duration", 1)

            if animation == "none":
                self.code_gen.add_line(f"self.add({obj_var})")
            elif animation == "fade_in":
                self.code_gen.add_line(
                    f"self.play(FadeIn({obj_var}), run_time={duration})",
                )
            elif animation == "grow":
                self.code_gen.add_line(
                    f"self.play(GrowFromCenter({obj_var}), run_time={duration})",
                )
            elif animation == "write":
                self.code_gen.add_line(
                    f"self.play(Write({obj_var}), run_time={duration})",
                )
            elif animation == "draw":
                self.code_gen.add_line(
                    f"self.play(Create({obj_var}), run_time={duration})",
                )
            else:
                # Default to Create for any other animation type
                self.code_gen.add_line(
                    f"self.play(Create({obj_var}), run_time={duration})",
                )
        else:
            self.code_gen.add_line(f"# No object definition found for create action: {node}")

    def _process_wait(self, node: WaitActionNode):
        """Generate code for wait action."""
        self.code_gen.add_line(f"self.wait({getattr(node, 'duration', 1)})")

    def _process_move_to(self, node: MoveToActionNode):
        """Generate code for move_to action."""
        obj_var = self.code_gen.obj_names.get(node.target_id)
        obj_def = self.code_gen.current_scene.object_definitions.get(node.target_id)
        is_arrow = obj_def and obj_def.object_type == "arrow"

        if obj_var and hasattr(node, "position") and node.position:
            if is_arrow and obj_def:
                # --- Arrow Specific Move Handling ---
                anchor_pos = np.array(
                    [node.position[0], node.position[1], node.position[2] if len(node.position) > 2 else 0]
                )
                start_offset = np.array(obj_def.properties.get("start", [0, 0, 0]))
                end_offset = np.array(obj_def.properties.get("end", [1, 0, 0]))
                abs_start = anchor_pos + start_offset
                abs_end = anchor_pos + end_offset
                duration = getattr(node, "duration", 1)

                start_str = f"np.array([{abs_start[0]}, {abs_start[1]}, {abs_start[2]}])"
                end_str = f"np.array([{abs_end[0]}, {abs_end[1]}, {abs_end[2]}])"

                self.code_gen.add_line(
                    f"self.play({obj_var}.animate.put_start_and_end_on({start_str}, {end_str}), run_time={duration})"
                )
                # --- End Arrow Specific ---
            elif not is_arrow:
                # Original move_to for non-arrows
                pos_str = (
                    f"[{node.position[0]}, {node.position[1]}, {node.position[2] if len(node.position) > 2 else 0}]"
                )
                path = getattr(node, "path", "line")
                duration = getattr(node, "duration", 1)

                if path == "arc":
                    self.code_gen.add_line(
                        f"self.play({obj_var}.animate.move_to(np.array({pos_str})), path_arc=PI/2, run_time={duration})",
                    )
                else:  # default is line
                    self.code_gen.add_line(
                        f"self.play({obj_var}.animate.move_to(np.array({pos_str})), run_time={duration})",
                    )
        else:
            self.code_gen.add_line(f"# Cannot generate move_to action for object: {node}")

    def _process_transform(self, node: TransformActionNode):
        """Generate code for transform action."""
        obj_var = self.code_gen.obj_names.get(node.target_id)
        if obj_var and hasattr(node, "target_properties"):
            props = []
            target_props = node.target_properties

            # Handle position separately if included
            position_code = ""
            if "position" in target_props:
                pos = target_props["position"]
                pos_str = f"[{pos[0]}, {pos[1]}, {pos[2] if len(pos) > 2 else 0}]"
                position_code = f".move_to(np.array({pos_str}))"

            # Handle scale
            scale_code = ""
            if "scale" in target_props:
                scale = target_props["scale"]
                if isinstance(scale, list):
                    scale_code = f".scale([{scale[0]}, {scale[1]}, {scale[2] if len(scale) > 2 else 1}])"
                else:
                    scale_code = f".scale({scale})"

            # Handle rotation
            rotation_code = ""
            if "rotation" in target_props:
                angle = target_props["rotation"]
                rotation_code = f".rotate({angle} * DEGREES)"

            # Handle other properties
            for prop, value in target_props.items():
                if prop == "fill_opacity":
                    props.append(f"set_fill(opacity={value})")
                elif prop == "color":
                    props.append(f'set_color("{value}")')
                elif prop == "stroke_width":
                    props.append(f"set_stroke(width={value})")
                elif prop == "stroke_color":
                    props.append(f'set_stroke(color="{value}")')
                # Position, scale, and rotation are handled separately

            # Combine animations
            duration = getattr(node, "duration", 1)
            easing = getattr(node, "easing", "smooth")

            # Map easing types to Manim rate functions
            easing_map = {
                "linear": "linear",
                "smooth": "smooth",
                # "elastic": "ease_in_out_elastic"
            }
            rate_func_name = easing_map.get(easing, "smooth")

            animation_parts = []

            # Add property modifications
            if props:
                props_str = ".".join(props)
                animation_parts.append(f"{obj_var}.animate.{props_str}")

            # Add position, scale, rotation animations
            if position_code:
                if animation_parts:
                    animation_parts[-1] += position_code
                else:
                    animation_parts.append(f"{obj_var}.animate{position_code}")

            if scale_code:
                if animation_parts:
                    animation_parts[-1] += scale_code
                else:
                    animation_parts.append(f"{obj_var}.animate{scale_code}")

            if rotation_code:
                if animation_parts:
                    animation_parts[-1] += rotation_code
                else:
                    animation_parts.append(f"{obj_var}.animate{rotation_code}")

            if animation_parts:
                animation_str = ", ".join(animation_parts)
                self.code_gen.add_line(
                    f"self.play({animation_str}, rate_func={rate_func_name}, run_time={duration})",
                )
        else:
            self.code_gen.add_line(f"# Cannot generate transform action for object: {node}")

    def _process_indicate(self, node: IndicateActionNode):
        """Generate code for indicate action."""
        obj_var = self.code_gen.obj_names.get(node.target_id)
        if obj_var:
            color = f'"{getattr(node, "color", "YELLOW")}"'
            scale_factor = getattr(node, "scale_factor", 1.1)
            duration = getattr(node, "duration", 1)
            self.code_gen.add_line(
                f"self.play(Indicate({obj_var}, color={color}, scale_factor={scale_factor}), run_time={duration})",
            )
        else:
            self.code_gen.add_line(f"# Cannot generate indicate action for object: {node}")

    def _process_remove(self, node: RemoveActionNode):
        """Generate code for remove action."""
        obj_var = self.code_gen.obj_names.get(node.target_id)
        if obj_var:
            animation = getattr(node, "animation", "fade_out")
            duration = getattr(node, "duration", 1)

            if animation == "shrink":
                self.code_gen.add_line(
                    f"self.play(FadeOut({obj_var}, scale=0.1), run_time={duration})",
                )
            elif animation == "unwrite":
                self.code_gen.add_line(
                    f"self.play(Unwrite({obj_var}), run_time={duration})",
                )
            elif animation == "none":
                self.code_gen.add_line(f"self.remove({obj_var})")
            else:  # Default to fade_out
                self.code_gen.add_line(
                    f"self.play(FadeOut({obj_var}), run_time={duration})",
                )
        else:
            self.code_gen.add_line(f"# Cannot generate remove action for object: {node}")

    def _process_animate_group(self, node: AnimateGroupNode):
        """Generate code for animate_group action."""
        animations = []
        # Keep track of objects created within this group to avoid duplicate creation lines
        created_in_group = set()
        creation_code_lines = []  # Store creation/positioning lines separately

        for child_action in node.child_actions:
            if child_action.action_type == "move_to":
                child_action = cast(MoveToActionNode, child_action)
                obj_var = self.code_gen.obj_names.get(child_action.target_id)
                obj_def = self.code_gen.current_scene.object_definitions.get(child_action.target_id)
                is_arrow = obj_def and obj_def.object_type == "arrow"

                if obj_var and hasattr(child_action, "position"):
                    if is_arrow and obj_def:
                        # --- Arrow Specific Move Handling within Group ---
                        anchor_pos = np.array(
                            [
                                child_action.position[0],
                                child_action.position[1],
                                child_action.position[2] if len(child_action.position) > 2 else 0,
                            ]
                        )
                        start_offset = np.array(obj_def.properties.get("start", [0, 0, 0]))
                        end_offset = np.array(obj_def.properties.get("end", [1, 0, 0]))
                        abs_start = anchor_pos + start_offset
                        abs_end = anchor_pos + end_offset

                        start_str = f"np.array([{abs_start[0]}, {abs_start[1]}, {abs_start[2]}])"
                        end_str = f"np.array([{abs_end[0]}, {abs_end[1]}, {abs_end[2]}])"
                        animations.append(f"{obj_var}.animate.put_start_and_end_on({start_str}, {end_str})")
                        # --- End Arrow Specific ---
                    elif not is_arrow:
                        # Original move_to for non-arrows
                        pos = child_action.position
                        pos_str = f"[{pos[0]}, {pos[1]}, {pos[2] if len(pos) > 2 else 0}]"
                        animations.append(f"{obj_var}.animate.move_to(np.array({pos_str}))")
                    # else: Arrow without definition? Should not happen

            elif child_action.action_type == "transform":
                child_action = cast(TransformActionNode, child_action)
                obj_var = self.code_gen.obj_names.get(child_action.target_id)
                if obj_var and hasattr(child_action, "target_properties"):
                    props = []
                    for prop, value in child_action.target_properties.items():
                        if prop == "fill_opacity":
                            props.append(f"set_fill(opacity={value})")
                        elif prop == "color":
                            props.append(f'set_color("{value}")')
                        elif prop == "scale":
                            if isinstance(value, list):
                                props.append(
                                    f"scale([{value[0]}, {value[1]}, {value[2] if len(value) > 2 else 1}])",
                                )
                            else:
                                props.append(f"scale({value})")
                        elif prop == "rotation":
                            props.append(f"rotate({value} * DEGREES)")

                    if props:
                        props_str = ".".join(props)
                        animations.append(f"{obj_var}.animate.{props_str}")

            elif child_action.action_type == "create":
                child_action = cast(CreateActionNode, child_action)
                obj_var = self.code_gen.obj_names.get(child_action.target_id)
                obj_def = self.code_gen.current_scene.object_definitions.get(child_action.target_id)

                if obj_var and obj_def and obj_var not in created_in_group:
                    # Store creation/positioning code to be added *before* the play call
                    current_lines_count = len(self.code_gen.code_lines)
                    self.code_gen.object_generator.create_object(obj_def, obj_var)
                    creation_code_lines.extend(self.code_gen.code_lines[current_lines_count:])
                    self.code_gen.code_lines = self.code_gen.code_lines[:current_lines_count]

                    if hasattr(child_action, "position") and child_action.position:
                        is_arrow = obj_def.object_type == "arrow"
                        if is_arrow:
                            # Simplified: Add full arrow absolute positioning if needed later
                            pass
                        else:
                            pos = child_action.position
                            pos_str = f"[{pos[0]}, {pos[1]}, {pos[2] if len(pos) > 2 else 0}]"
                            creation_code_lines.append(f"        {obj_var}.move_to(np.array({pos_str}))")

                    created_in_group.add(obj_var)

                    # Determine animation and add to list for self.play()
                    animation_type = getattr(child_action, "animation", "none")
                    if animation_type == "fade_in":
                        animations.append(f"FadeIn({obj_var})")
                    elif animation_type == "grow":
                        animations.append(f"GrowFromCenter({obj_var})")
                    elif animation_type == "write":
                        animations.append(f"Write({obj_var})")
                    elif animation_type == "draw":
                        animations.append(f"Create({obj_var})")
                    # 'none' animation type is implicitly handled by just creating/positioning
                    # If animation is 'none', add the object directly if no other animations are present
                    elif animation_type == "none" and not animations:
                        creation_code_lines.append(f"        self.add({obj_var})")

        # Add the creation/positioning lines before the play call
        self.code_gen.code_lines.extend(creation_code_lines)

        if animations:
            anim_str = ", ".join(animations)
            duration = getattr(node, "duration", 1)  # Use group's duration or default
            self.code_gen.add_line(f"self.play({anim_str}, run_time={duration})")
        elif created_in_group:  # Objects were created but not animated (e.g., all animation="none")
            add_vars = ", ".join([var for var in created_in_group if f"self.add({var})" not in creation_code_lines])
            if add_vars:
                self.code_gen.add_line("# Adding objects created with animation='none' in group")
                self.code_gen.add_line(f"self.add({add_vars})")
        else:
            self.code_gen.add_line(f"# No valid animations found or objects created in animate_group action: {node}")

    def _process_arrange(self, node: ArrangeActionNode):
        """Generate code for arrange action."""
        obj_var = self.code_gen.obj_names.get(node.target_id)
        rel_to = getattr(node, "relative_to_id", None)
        direction = getattr(node, "direction", "RIGHT")
        buffer = getattr(node, "buffer", 0.25)
        duration = getattr(node, "duration", 0.5)
        align_edge = getattr(node, "align_edge", None)
        if align_edge is not None:
            align_edge = align_edge.value

        # Map direction string to Manim constant
        direction_map = {
            "UP": "UP",
            "DOWN": "DOWN",
            "LEFT": "LEFT",
            "RIGHT": "RIGHT",
            "IN": "IN",
            "OUT": "OUT",
            "TOP_LEFT": "UL",
            "TOP_RIGHT": "UR",
            "BOTTOM_LEFT": "DL",
            "BOTTOM_RIGHT": "DR",
        }
        direction_const = direction_map.get(direction, "RIGHT")

        if obj_var and rel_to:
            # Check if relative_to is a constant or another object
            if rel_to in [
                "ORIGIN",
                "SCREEN_CENTER",
                "SCREEN_LEFT",
                "SCREEN_RIGHT",
                "SCREEN_TOP",
                "SCREEN_BOTTOM",
                "SCREEN_TOP_LEFT",
                "SCREEN_TOP_RIGHT",
                "SCREEN_BOTTOM_LEFT",
                "SCREEN_BOTTOM_RIGHT",
            ]:
                # It's a constant
                align_param = f"aligned_edge={align_edge}" if align_edge else ""
                self.code_gen.add_line(
                    f"self.play({obj_var}.animate.next_to({rel_to}, direction={direction_const}, buff={buffer}{', ' + align_param if align_param else ''}), run_time={duration})",
                )
            else:
                # It's an object
                rel_obj_var = self.code_gen.obj_names.get(rel_to)
                if rel_obj_var:
                    align_param = f"aligned_edge={align_edge}" if align_edge else ""
                    self.code_gen.add_line(
                        f"self.play({obj_var}.animate.next_to({rel_obj_var}, direction={direction_const}, buff={buffer}{', ' + align_param if align_param else ''}), run_time={duration})",
                    )
                else:
                    self.code_gen.add_line(f"# Cannot find reference object for arrange action: {node}")
        else:
            self.code_gen.add_line(f"# Cannot generate arrange action for object: {node}")

    def _process_align(self, node: AlignActionNode):
        """Generate code for align action."""
        targets = getattr(node, "target_ids", [])
        reference = getattr(node, "reference_target_id", None)
        edge = getattr(node, "edge", "LEFT")
        duration = getattr(node, "duration", 0.5)

        if targets:
            # Convert edge to Manim constant
            edge_map = {
                "LEFT": "LEFT",
                "RIGHT": "RIGHT",
                "TOP": "UP",
                "BOTTOM": "DOWN",
                # FIXME: Add these back in
                # "CENTER_X": "ORIGIN[0]",
                # "CENTER_Y": "ORIGIN[1]",
            }
            edge_const = edge_map.get(edge, "LEFT")

            # Create a list of animations for all targets
            animations = []

            # Handle reference target if specified
            if reference:
                # Check if reference is a constant or another object
                if reference in [
                    "ORIGIN",
                    "SCREEN_CENTER",
                    "SCREEN_LEFT",
                    "SCREEN_RIGHT",
                    "SCREEN_TOP",
                    "SCREEN_BOTTOM",
                    "SCREEN_TOP_LEFT",
                    "SCREEN_TOP_RIGHT",
                    "SCREEN_BOTTOM_LEFT",
                    "SCREEN_BOTTOM_RIGHT",
                ]:
                    ref_var = reference
                else:
                    ref_var = self.code_gen.obj_names.get(reference)

                # Create animations to align each target to the reference
                for target_id in targets:
                    target_var = self.code_gen.obj_names.get(target_id)
                    if target_var:
                        animations.append(
                            f"{target_var}.animate.align_to({ref_var}, {edge_const})",
                        )
            else:
                # If no reference, align to the first target
                if len(targets) > 1:
                    first_target_var = self.code_gen.obj_names.get(targets[0])
                    for target_id in targets[1:]:
                        target_var = self.code_gen.obj_names.get(target_id)
                        if target_var:
                            animations.append(
                                f"{target_var}.animate.align_to({first_target_var}, {edge_const})",
                            )

            if animations:
                anim_str = ", ".join(animations)
                self.code_gen.add_line(f"self.play({anim_str}, run_time={duration})")
        else:
            self.code_gen.add_line(f"# No targets specified for align action: {node}")

    def _process_distribute(self, node: DistributeActionNode):
        """Generate code for distribute action."""
        targets = getattr(node, "target_ids", [])
        direction = getattr(node, "direction", "HORIZONTAL")
        spacing = getattr(node, "spacing", None)
        duration = getattr(node, "duration", 0.5)
        ref_frame = getattr(node, "reference_frame", {})

        if targets and len(targets) > 1:
            target_vars = [
                self.code_gen.obj_names.get(target_id)
                for target_id in targets
                if self.code_gen.obj_names.get(target_id)
            ]

            # If we have more than one valid target
            if len(target_vars) > 1:
                direction_const = "RIGHT" if direction == "HORIZONTAL" else "DOWN"

                if ref_frame:
                    # Get reference frame boundaries
                    start_edge = ref_frame.get("start_edge_of")
                    end_edge = ref_frame.get("end_edge_of")
                    mode = ref_frame.get("mode", "centers")

                    if start_edge and end_edge:
                        # Convert to variables or constants
                        if start_edge in [
                            "ORIGIN",
                            "SCREEN_CENTER",
                            "SCREEN_LEFT",
                            "SCREEN_RIGHT",
                            "SCREEN_TOP",
                            "SCREEN_BOTTOM",
                            "SCREEN_TOP_LEFT",
                            "SCREEN_TOP_RIGHT",
                            "SCREEN_BOTTOM_LEFT",
                            "SCREEN_BOTTOM_RIGHT",
                        ]:
                            start_var = start_edge
                        else:
                            start_var = self.code_gen.obj_names.get(
                                start_edge,
                                "ORIGIN",
                            )

                        if end_edge in [
                            "ORIGIN",
                            "SCREEN_CENTER",
                            "SCREEN_LEFT",
                            "SCREEN_RIGHT",
                            "SCREEN_TOP",
                            "SCREEN_BOTTOM",
                            "SCREEN_TOP_LEFT",
                            "SCREEN_TOP_RIGHT",
                            "SCREEN_BOTTOM_LEFT",
                            "SCREEN_BOTTOM_RIGHT",
                        ]:
                            end_var = end_edge
                        else:
                            end_var = self.code_gen.obj_names.get(end_edge, "ORIGIN")

                        # Create a temporary group for distribution
                        temp_group = f"temp_group_{len(self.code_gen.code_lines)}"
                        targets_str = ", ".join(target_vars)
                        self.code_gen.add_line(f"{temp_group} = VGroup({targets_str})")

                        # Distribute objects
                        if spacing is not None:
                            if direction == "HORIZONTAL":
                                self.code_gen.add_line(
                                    f"{temp_group}.arrange(RIGHT, buff={spacing})",
                                )
                                if mode == "edges":
                                    self.code_gen.add_line(
                                        f"{temp_group}.get_left()[0] = {start_var}[0]",
                                    )
                                    self.code_gen.add_line(
                                        f"{temp_group}.get_right()[0] = {end_var}[0]",
                                    )
                                else:  # centers
                                    self.code_gen.add_line(
                                        f"{temp_group}.move_to(({start_var}[0] + {end_var}[0])/2 * RIGHT)",
                                    )
                                    self.code_gen.add_line(
                                        f"{temp_group}.scale_to_fit_width(abs({end_var}[0] - {start_var}[0]))",
                                    )
                            else:  # VERTICAL
                                self.code_gen.add_line(
                                    f"{temp_group}.arrange(DOWN, buff={spacing})",
                                )
                                if mode == "edges":
                                    self.code_gen.add_line(
                                        f"{temp_group}.get_top()[1] = {start_var}[1]",
                                    )
                                    self.code_gen.add_line(
                                        f"{temp_group}.get_bottom()[1] = {end_var}[1]",
                                    )
                                else:  # centers
                                    self.code_gen.add_line(
                                        f"{temp_group}.move_to(({start_var}[1] + {end_var}[1])/2 * UP)",
                                    )
                                    self.code_gen.add_line(
                                        f"{temp_group}.scale_to_fit_height(abs({end_var}[1] - {start_var}[1]))",
                                    )
                        else:
                            # Evenly distribute within the reference frame
                            self.code_gen.add_line(
                                f"{temp_group}.arrange({direction_const})",
                            )
                            if direction == "HORIZONTAL":
                                if mode == "edges":
                                    self.code_gen.add_line(
                                        f"{temp_group}.get_left()[0] = {start_var}[0]",
                                    )
                                    self.code_gen.add_line(
                                        f"{temp_group}.get_right()[0] = {end_var}[0]",
                                    )
                                else:  # centers
                                    self.code_gen.add_line(
                                        f"{temp_group}.move_to(({start_var}[0] + {end_var}[0])/2 * RIGHT)",
                                    )
                                    self.code_gen.add_line(
                                        f"{temp_group}.scale_to_fit_width(abs({end_var}[0] - {start_var}[0]))",
                                    )
                            else:  # VERTICAL
                                if mode == "edges":
                                    self.code_gen.add_line(
                                        f"{temp_group}.get_top()[1] = {start_var}[1]",
                                    )
                                    self.code_gen.add_line(
                                        f"{temp_group}.get_bottom()[1] = {end_var}[1]",
                                    )
                                else:  # centers
                                    self.code_gen.add_line(
                                        f"{temp_group}.move_to(({start_var}[1] + {end_var}[1])/2 * UP)",
                                    )
                                    self.code_gen.add_line(
                                        f"{temp_group}.scale_to_fit_height(abs({end_var}[1] - {start_var}[1]))",
                                    )

                        # Create animations for each target to move to its position in the group
                        animations = []
                        for i, target_var in enumerate(target_vars):
                            animations.append(
                                f"{target_var}.animate.move_to({temp_group}[{i}])",
                            )

                        anim_str = ", ".join(animations)
                        self.code_gen.add_line(
                            f"self.play({anim_str}, run_time={duration})",
                        )
                    else:
                        # No reference frame boundaries, just distribute among themselves
                        self._distribute_simple(
                            target_vars,
                            direction_const,
                            spacing,
                            duration,
                        )
                else:
                    # No reference frame, just distribute among themselves
                    self._distribute_simple(
                        target_vars,
                        direction_const,
                        spacing,
                        duration,
                    )
        else:
            self.code_gen.add_line(f"# Not enough targets specified for distribute action: {node}")

    def _distribute_simple(self, target_vars, direction_const, spacing, duration):
        """Helper method for simple distribution without reference frame."""
        temp_group = f"temp_group_{len(self.code_gen.code_lines)}"
        targets_str = ", ".join(target_vars)
        self.code_gen.add_line(f"{temp_group} = VGroup({targets_str})")

        if spacing is not None:
            self.code_gen.add_line(
                f"{temp_group}.arrange({direction_const}, buff={spacing})",
            )
        else:
            self.code_gen.add_line(f"{temp_group}.arrange({direction_const})")

        # Create animations to move objects to their positions in the group
        animations = []
        for i, target_var in enumerate(target_vars):
            animations.append(f"{target_var}.animate.move_to({temp_group}[{i}])")

        anim_str = ", ".join(animations)
        self.code_gen.add_line(f"self.play({anim_str}, run_time={duration})")

    # Additional methods from ActionGeneratorExtra
    def _process_replace(self, node: ReplaceActionNode):
        """Generate code for replace action."""
        source_id = getattr(node, "source_id", None)
        target_id = getattr(node, "target_id", None)
        animation = getattr(node, "animation", "transform")
        duration = getattr(node, "duration", 1)
        position_match = getattr(node, "position_match", True)

        if source_id and target_id:
            source_var = self.code_gen.obj_names.get(source_id)
            target_var = self.code_gen.obj_names.get(target_id)

            if source_var and target_var:
                # Create the target object
                obj_def: Optional[ObjectDefinitionNode] = None
                for (
                    obj_id,
                    definition,
                ) in self.code_gen.current_scene.object_definitions.items():
                    if obj_id == target_id:
                        obj_def = definition
                        break

                if obj_def:
                    # Create the target object temporarily with a different name
                    temp_var = f"{target_var}_temp"
                    self.code_gen.object_generator.create_object(obj_def, temp_var)

                    # Position matching if needed
                    if position_match:
                        self.code_gen.add_line(f"{temp_var}.move_to({source_var})")

                    # Perform the replacement animation
                    if animation == "transform":
                        self.code_gen.add_line(
                            f"self.play(ReplacementTransform({source_var}, {temp_var}), run_time={duration})",
                        )
                    else:  # fade
                        self.code_gen.add_line(
                            f"self.play(FadeOut({source_var}), FadeIn({temp_var}), run_time={duration})",
                        )

                    # Assign the temporary variable to the target variable
                    self.code_gen.add_line(f"{target_var} = {temp_var}")
        else:
            self.code_gen.add_line(f"# Missing source or target for replace action: {node}")

    def _process_highlight(self, node: HighlightActionNode):
        """Generate code for highlight action."""
        target_id = getattr(node, "target_id", None)
        color = getattr(node, "color", "YELLOW")
        duration = getattr(node, "duration", 1)

        if target_id:
            target_var = self.code_gen.obj_names.get(target_id)
            if target_var:
                self.code_gen.add_line(
                    f'self.play(Flash({target_var}, color="{color}", line_length=0.5), run_time={duration})',
                )
        else:
            self.code_gen.add_line(f"# No target for highlight action: {node}")

    def _process_focus(self, node: FocusActionNode):
        """Generate code for focus action."""
        target_id = getattr(node, "target_id", None)
        duration = getattr(node, "duration", 1)
        scale_factor = getattr(node, "scale_factor", 1.2)
        opacity_factor = getattr(node, "opacity_factor", 0.5)

        if target_id:
            target_var = self.code_gen.obj_names.get(target_id)
            if target_var:
                # Store original objects - filter for VMobjects to avoid type errors
                temp_group = f"temp_focus_others_{len(self.code_gen.code_lines)}"
                self.code_gen.add_line(
                    f"{temp_group} = VGroup(*[obj for obj in self.mobjects if obj != {target_var} and isinstance(obj, VMobject)])",
                )

                # Create animations
                self.code_gen.add_line("self.play(")
                self.code_gen.add_line(
                    f"    {target_var}.animate.scale({scale_factor}),",
                )
                self.code_gen.add_line(
                    f"    {temp_group}.animate.set_opacity({opacity_factor}),",
                )
                self.code_gen.add_line(f"    run_time={duration}")
                self.code_gen.add_line(")")
            else:
                self.code_gen.add_line(f"# Cannot find target object for focus action: {node}")
        else:
            self.code_gen.add_line(f"# No target specified for focus action: {node}")

    def _process_camera(self, node: CameraActionNode):
        """Generate code for camera action."""
        action = getattr(node, "camera_action_type", None)
        duration = getattr(node, "duration", 1)

        # Check if we're in a MovingCameraScene
        if not hasattr(self.code_gen, "scene_is_movable_camera"):
            # This shouldn't happen because visit_scene should have detected camera actions
            # But just in case, add a warning
            self.code_gen.add_line("# Warning: Camera actions require MovingCameraScene")
            self.code_gen.add_line("# Please ensure your scene class inherits from MovingCameraScene")
            self.code_gen.add_line("# These camera actions may not work correctly")
            self.code_gen.scene_is_movable_camera = True

        if action == "zoom":
            scale = getattr(node, "scale", 1.5)
            self.code_gen.add_line(
                f"self.play(self.camera.frame.animate.scale({scale}), run_time={duration})",
            )

        elif action == "pan" or action == "move_to":
            position = getattr(node, "position", [0, 0, 0])
            pos_str = f"[{position[0]}, {position[1]}, {position[2] if len(position) > 2 else 0}]"
            self.code_gen.add_line(
                f"self.play(self.camera.frame.animate.move_to(np.array({pos_str})), run_time={duration})",
            )

        elif action == "reset":
            self.code_gen.add_line("self.play(")
            self.code_gen.add_line("    self.camera.frame.animate.move_to(ORIGIN),")
            self.code_gen.add_line("    self.camera.frame.animate.set_scale(1.0),")
            self.code_gen.add_line(f"    run_time={duration}")
            self.code_gen.add_line(")")
        else:
            self.code_gen.add_line(f"# Unsupported camera action: {node}")

    def _process_plot_function(self, node: PlotFunctionActionNode):
        """Generate code for plot_function action."""
        target_id = getattr(node, "target_graph_id", None)
        function = getattr(node, "function_str", "x")
        color = getattr(node, "color", "BLUE")
        x_range = getattr(node, "x_range", None)
        animation = getattr(node, "animation", "draw")
        duration = getattr(node, "duration", 2)

        if target_id:
            target_var = self.code_gen.obj_names.get(target_id)
            if target_var:
                # Create a unique variable name for the graph
                graph_var = f"{target_var}_graph_{len(self.code_gen.code_lines)}"

                # Get x_range if specified, otherwise use the axes' x_range
                if x_range:
                    x_range_str = f"[{x_range[0]}, {x_range[1]}]"
                else:
                    x_range_str = f"{target_var}[0].x_range"

                # Define the function and create the graph
                func_name = f"func_{len(self.code_gen.code_lines)}"
                self.code_gen.add_line(f"def {func_name}(x):")
                self.code_gen.add_line(f"    return {function}")
                self.code_gen.add_line()

                # For coordinate systems, we need to check if target_var is a VGroup
                # We can do this by checking if it was created with VGroup() in the code
                # For coordinate systems, we need to check if target_var is a VGroup
                # We can do this by checking if it was created with VGroup() in any previous line
                if any("VGroup" in line and target_var in line for line in self.code_gen.code_lines):
                    # If it's a VGroup, the axes are the first element
                    axes_var = f"{target_var}[0]"
                else:
                    # If it's just axes, use it directly
                    axes_var = target_var

                self.code_gen.add_line(
                    f'{graph_var} = {axes_var}.plot({func_name}, x_range={x_range_str}, color="{color}")',
                )

                # Add the animation
                if animation == "draw":
                    self.code_gen.add_line(
                        f"self.play(Create({graph_var}), run_time={duration})",
                    )
                elif animation == "none":
                    self.code_gen.add_line(f"self.add({graph_var})")
                else:  # default to create
                    self.code_gen.add_line(
                        f"self.play(Create({graph_var}), run_time={duration})",
                    )
            else:
                self.code_gen.add_line(f"# Cannot find target graph for plot_function action: {node}")
        else:
            self.code_gen.add_line(f"# No target graph specified for plot_function action: {node}")

    def _process_add_point(self, node: AddPointActionNode):
        """Generate code for add_point action."""
        target_id = getattr(node, "target_graph_id", None)
        position = getattr(node, "position", [0, 0, 0])
        color = getattr(node, "color", "WHITE")
        label = getattr(node, "label", None)
        animation = getattr(node, "animation", "fade_in")
        duration = getattr(node, "duration", 0.5)

        if target_id and position:
            target_var = self.code_gen.obj_names.get(target_id)
            if target_var:
                # For coordinate systems, the actual axes are the first element of the VGroup
                # If it's just axes, use it directly
                if any("VGroup" in line and target_var in line for line in self.code_gen.code_lines):
                    axes_var = f"{target_var}[0]"
                else:
                    axes_var = target_var

                # Create point variable
                point_var = f"{target_var}_point_{len(self.code_gen.code_lines)}"

                self.code_gen.add_line(
                    f'{point_var} = Dot(point={axes_var}.c2p({position[0]}, {position[1]}), color="{color}")',
                )

                # Add label if specified
                if label:
                    label_var = f"{point_var}_label"
                    self.code_gen.add_line(
                        f'{label_var} = MathTex(r"{label}", color="{color}")',
                    )
                    self.code_gen.add_line(f"{label_var}.next_to({point_var}, UP)")

                    # Group point and label
                    group_var = f"{point_var}_group"
                    self.code_gen.add_line(
                        f"{group_var} = VGroup({point_var}, {label_var})",
                    )

                    # Add animation
                    if animation == "fade_in":
                        self.code_gen.add_line(
                            f"self.play(FadeIn({group_var}), run_time={duration})",
                        )
                    elif animation == "grow":
                        self.code_gen.add_line(
                            f"self.play(GrowFromCenter({point_var}), Write({label_var}), run_time={duration})",
                        )
                    elif animation == "none":
                        self.code_gen.add_line(f"self.add({group_var})")
                    else:  # default to fade_in
                        self.code_gen.add_line(
                            f"self.play(FadeIn({group_var}), run_time={duration})",
                        )
                else:
                    # No label, just animate the point
                    if animation == "fade_in":
                        self.code_gen.add_line(
                            f"self.play(FadeIn({point_var}), run_time={duration})",
                        )
                    elif animation == "grow":
                        self.code_gen.add_line(
                            f"self.play(GrowFromCenter({point_var}), run_time={duration})",
                        )
                    elif animation == "none":
                        self.code_gen.add_line(f"self.add({point_var})")
                    else:  # default to fade_in
                        self.code_gen.add_line(
                            f"self.play(FadeIn({point_var}), run_time={duration})",
                        )
        else:
            missing = []
            if not target_id:
                missing.append("target graph")
            if not position:
                missing.append("position")
            missing_str = " and ".join(missing)
            self.code_gen.add_line(f"# Missing {missing_str} for add_point action: {node}")
