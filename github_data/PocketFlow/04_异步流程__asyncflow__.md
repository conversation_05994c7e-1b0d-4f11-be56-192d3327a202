# Chapter 4: 异步流程 (AsyncFlow)


欢迎来到 PocketFlow 教程的第四章！在上一章 [第 3 章：异步节点 (AsyncNode)](03_异步节点__asyncnode__.md) 中，我们学习了如何创建 `AsyncNode` 来执行像网络请求这样的耗时 I/O 操作，而不会阻塞整个程序。`AsyncNode` 通过 `async/await` 让我们在等待时能够“抽身”去做别的事情，提高了程序的效率。

但是，想象一下，你有一个工作流程，包含好几个需要异步执行的步骤。比如：

1.  **获取用户信息** (需要调用用户服务 API，这是一个异步操作)
2.  **根据用户信息获取推荐商品** (需要调用推荐引擎 API，这是另一个异步操作)
3.  **发送包含推荐商品的邮件** (需要调用邮件服务 API，还是异步操作)

虽然每个步骤本身都可以用 `AsyncNode` 实现非阻塞等待，但如果我们用普通的 [流程 (Flow)](02_流程__flow__.md) 来连接这些 `AsyncNode`，流程本身在调度这些节点时仍然是 **同步** 的。也就是说，`Flow` 会按顺序 `await` 第一个 `AsyncNode` 完成，然后 `await` 第二个 `AsyncNode` 完成…… 即使 `AsyncNode` 内部的等待是高效的，`Flow` 本身的调度逻辑却不能在等待一个节点时去处理其他（可能已经准备好的）异步任务。

我们需要一种**本身也是异步**的流程控制器，它能够智能地管理和运行包含 `AsyncNode` 的工作流，充分利用异步操作带来的并发优势。

这正是 **异步流程 (AsyncFlow)** 所要解决的问题。

在本章中，我们将学习：

*   什么是异步流程 (AsyncFlow)？它和普通 `Flow` 有什么不同？
*   如何使用 `AsyncFlow` 连接和运行 `AsyncNode`？
*   `AsyncFlow` 如何在内部实现高效的异步调度？

## 什么是异步流程 (AsyncFlow)？

**异步流程 (AsyncFlow)** 是 [流程 (Flow)](02_流程__flow__.md) 的异步版本。它专门设计用来**管理和运行包含 [异步节点 (AsyncNode)](03_异步节点__asyncnode__.md) 的工作流**。

就像 `Flow` 负责根据节点间的连接和 `post` 返回的 action 来调度执行一样，`AsyncFlow` 也做同样的事情。但关键的区别在于，**`AsyncFlow` 的整个调度过程本身就是异步的**。

你可以把它想象成一个特别擅长 **多任务并发处理的项目经理**：

*   **普通项目经理 (`Flow`)**: 虽然手下的员工（`Node`s / `AsyncNode`s）可能在等待外部资源时可以做点别的小事 (`AsyncNode` 内部的 `await`)，但经理本人一次只能盯一个员工的任务。他必须等一个员工明确说“我这步彻底干完了”，才能去安排下一个员工。
*   **异步项目经理 (`AsyncFlow`)**: 这位经理更厉害。当他安排一个员工（`AsyncNode`）去做一件需要等待外部资源（比如等快递送达）的任务时，他不会傻等。他会记下这件事，然后立刻去检查其他员工的任务进展，看看有没有谁已经准备好可以开始下一步了。一旦快递送达，他又能立刻回来处理那个员工的后续工作。

`AsyncFlow` 就像这位异步项目经理，它在运行一个 `AsyncNode` 并遇到 `await`（节点暂停等待）时，**流程本身也会暂停，并将控制权交还给 Python 的异步事件循环**。这意味着事件循环可以利用这段时间去运行其他准备就绪的异步任务（可能是其他流程，也可能是完全无关的异步代码）。当节点等待的操作完成后，`AsyncFlow` 会被唤醒，继续从暂停的地方执行下去。

这种机制使得 `AsyncFlow` 能够高效地处理流程中各个 `AsyncNode` 的等待时间，**实现整个工作流层面的非阻塞执行**，特别适合构建需要高并发、高 I/O 吞吐量的应用。

## 使用 AsyncFlow：异步获取天气和建议

让我们构建一个简单的异步流程，它包含两个异步节点：

1.  `AsyncWeatherNode`: (复用上一章的例子) 模拟异步获取天气信息。
2.  `AsyncSuggestionNode`: 根据天气信息，模拟异步生成一条出行建议。

### 1. 定义异步节点

我们先定义这两个 `AsyncNode`。

```python
import asyncio
import random
from pocketflow import AsyncNode, AsyncFlow # 导入 AsyncNode 和 AsyncFlow

# --- 节点 1: 异步获取天气 (简化版) ---
class AsyncWeatherNode(AsyncNode):
    async def prep_async(self, shared):
        city = shared.get("city", "上海")
        print(f"天气节点：准备查询 '{city}' 的天气...")
        return city

    async def exec_async(self, city):
        delay = random.uniform(0.3, 0.8) # 模拟 API 延迟
        print(f"  天气节点：正在调用天气 API (等待 {delay:.2f} 秒)...")
        await asyncio.sleep(delay)
        weather = random.choice(["晴朗", "多云", "小雨"])
        print(f"  天气节点：获取到 '{city}' 的天气是 {weather}。")
        return weather

    async def post_async(self, shared, prep_res, exec_res):
        shared["current_weather"] = exec_res
        print("天气节点：已将天气存入 shared。")
        # 返回 "suggest" 动作，告诉流程下一步去获取建议
        return "suggest"

# --- 节点 2: 异步生成建议 ---
class AsyncSuggestionNode(AsyncNode):
    async def prep_async(self, shared):
        weather = shared.get("current_weather")
        if not weather:
            print("建议节点：未找到天气信息，跳过。")
            return None # 没有天气，就不执行建议
        print(f"建议节点：准备根据天气 '{weather}' 生成建议...")
        return weather

    async def exec_async(self, weather):
        delay = random.uniform(0.2, 0.5) # 模拟思考/API 延迟
        print(f"  建议节点：正在生成建议 (等待 {delay:.2f} 秒)...")
        await asyncio.sleep(delay)
        suggestion = "带伞出门" if weather == "小雨" else "天气不错，适合散步"
        print(f"  建议节点：生成的建议是 '{suggestion}'。")
        return suggestion

    async def post_async(self, shared, prep_res, exec_res):
        shared["suggestion"] = exec_res
        print("建议节点：已将建议存入 shared。")
        # 流程结束，返回 None 或 "done"
        return "done"

```

**代码解释**:

*   我们定义了两个简单的 `AsyncNode`，都继承自 `pocketflow.AsyncNode`。
*   它们的 `exec_async` 方法都使用 `await asyncio.sleep()` 来模拟耗时的异步操作。
*   `AsyncWeatherNode` 在完成后返回 `"suggest"` 动作。
*   `AsyncSuggestionNode` 在完成后返回 `"done"` 动作。
*   它们都使用 `shared` 字典来传递数据（城市名、天气、建议）。

### 2. 连接节点并创建 AsyncFlow

连接节点的方式和创建普通 `Flow` 完全一样，只是我们现在使用的是 `AsyncFlow` 类。

```python
# 创建节点实例
weather_node = AsyncWeatherNode()
suggestion_node = AsyncSuggestionNode()

# 连接节点：定义流程路径
# 天气节点完成后，如果返回 "suggest"，则转到建议节点
weather_node - "suggest" >> suggestion_node

# 创建 AsyncFlow 实例，并指定起始节点
weather_suggestion_flow = AsyncFlow(start=weather_node)

# 准备共享数据
shared_data = {"city": "杭州"}

# --- 运行 AsyncFlow ---
# 定义一个异步主函数来运行流程
async def main():
    print("开始运行异步天气建议流程...")
    start_time = asyncio.get_event_loop().time()

    # 使用 await flow.run_async() 来运行 AsyncFlow
    final_action = await weather_suggestion_flow.run_async(shared_data)

    end_time = asyncio.get_event_loop().time()
    print(f"\n异步流程运行结束。最终动作: '{final_action}'")
    print(f"最终共享数据: {shared_data}")
    print(f"总耗时: {end_time - start_time:.2f} 秒")

# 运行主异步函数
if __name__ == "__main__":
    asyncio.run(main())
```

**代码解释**:

1.  **`weather_node - "suggest" >> suggestion_node`**: 连接逻辑和 `Flow` 一样，当 `weather_node` 返回 `"suggest"` 时，执行 `suggestion_node`。
2.  **`weather_suggestion_flow = AsyncFlow(start=weather_node)`**: 我们创建的是 `AsyncFlow` 的实例，而不是 `Flow`。
3.  **`async def main(): ...`**: 因为 `AsyncFlow` 需要使用 `await` 来运行，所以我们的运行代码必须放在一个 `async` 函数中。
4.  **`await weather_suggestion_flow.run_async(shared_data)`**: 这是运行 `AsyncFlow` 的关键！注意是调用 `run_async()` 方法，并且前面要加 `await`。
5.  **`asyncio.run(main())`**: 启动 Python 的异步事件循环来执行我们的 `main` 函数。

当你运行这段代码时，你会看到两个节点的打印信息按顺序出现，中间会有模拟的等待时间。整个流程看起来是顺序执行的（先获取天气，再生成建议），但重要的是，在 `weather_node` 或 `suggestion_node` 内部执行 `await asyncio.sleep()` 时，**`AsyncFlow` 的执行也会暂停**，允许 `asyncio` 事件循环去处理其他可能存在的异步任务。如果这个 `AsyncFlow` 是一个大型异步应用（比如 Web 服务器）的一部分，这一点至关重要，因为它不会因为等待流程中的某个 API 调用而阻塞服务器处理其他网络请求。

流程图如下：

```mermaid
graph LR
    A[AsyncWeatherNode] -- "suggest" --> B(AsyncSuggestionNode);
    B -- "done" --> End((结束));
```

### 混合使用节点类型

`AsyncFlow` 非常灵活，它可以同时管理 `AsyncNode` 和普通的 `Node`。当 `AsyncFlow` 在执行流程时遇到一个节点：

*   如果它是 `AsyncNode` 的实例，`AsyncFlow` 会 `await node._run_async(shared)`。
*   如果它是普通 `Node` 的实例，`AsyncFlow` 会直接调用 `node._run(shared)` (同步执行)。

这意味着你可以在同一个异步流程中无缝混合使用同步和异步的节点，`AsyncFlow` 会自动处理它们。

```python
# 假设我们还有一个普通的同步节点
from pocketflow import Node

class SyncLogNode(Node):
    def exec(self, prep_res):
        print("同步日志节点：记录流程摘要...")
        # 这是一个快速的同步操作
        return "Logged"

    def post(self, shared, prep_res, exec_res):
        print("同步日志节点：完成。")
        return "finish" # 假设这是最后一步

# --- 创建和连接包含混合节点的 AsyncFlow ---
weather_node = AsyncWeatherNode()
suggestion_node = AsyncSuggestionNode()
log_node = SyncLogNode() # 普通节点实例

# 连接: Weather -> Suggestion -> Log
weather_node - "suggest" >> suggestion_node
suggestion_node - "done" >> log_node # 建议完成后去记录日志

# 使用 AsyncFlow
mixed_flow = AsyncFlow(start=weather_node)

# 运行 (在 async main 函数中)
async def main_mixed():
    print("\n开始运行混合节点类型的异步流程...")
    shared_mixed = {"city": "深圳"}
    await mixed_flow.run_async(shared_mixed)
    print("混合流程结束。")
    print(f"最终共享数据: {shared_mixed}")

if __name__ == "__main__":
    # 为了演示，我们只运行混合流程
    # asyncio.run(main()) # 运行之前的流程
    asyncio.run(main_mixed())
```

运行这段代码，你会看到异步节点和同步节点都能在 `AsyncFlow` 的调度下正确执行。`AsyncFlow` 在遇到 `log_node` 时，会像普通 `Flow` 一样同步调用它的方法。

## AsyncFlow 内部是如何工作的？

`AsyncFlow` 的核心在于它的编排逻辑 (`_orch_async`) 本身就是一个异步函数。

当你调用 `await flow.run_async(shared)` 时：

1.  **获取起始节点**: 和 `Flow` 一样，找到 `start_node`。
2.  **开始异步循环**: 进入一个 `async while` 循环。
3.  **运行当前节点 (异步地)**: 在循环中，它检查当前节点 `curr` 的类型：
    *   **如果是 `AsyncNode`**: 它会 `await curr._run_async(shared)`。如果 `_run_async` 内部（比如在 `exec_async` 中）遇到了 `await`（例如 `await asyncio.sleep()` 或 `await network_call()`），那么 `curr._run_async` 会暂停，**这个 `await` 会将控制权一路传递出去，导致 `_orch_async` 在这里也暂停**，最终将控制权交还给 `asyncio` 事件循环。
    *   **如果是普通 `Node`**: 它会直接调用 `curr._run(shared)`，这个调用是同步的，会执行完毕才返回。
4.  **获取 Action**: 节点运行完成后（无论是异步恢复执行完成，还是同步执行完成），返回 `post` 的结果 "action"。
5.  **查找下一个节点**: 使用 `get_next_node(curr, action)` 找到下一个节点。
6.  **更新当前节点**: 将查找到的节点设为新的 `curr`。
7.  **重复**: 回到步骤 3，继续异步循环。
8.  **结束**: 当 `get_next_node` 找不到下一个节点时，循环结束，`_orch_async` 完成，最终 `flow.run_async()` 返回最后一个 action。

这个过程的关键在于第 3 步：`await curr._run_async(shared)`。正是这个 `await` 使得 `AsyncFlow` 的调度器能够在等待异步节点时让出控制权。

下面是一个简化的时序图，展示了 `AsyncFlow` 调度 `AsyncNode` 时发生的情况：

```mermaid
sequenceDiagram
    participant User as 用户
    participant AsyncFlow as 异步流程
    participant Orch as AsyncFlow._orch_async()
    participant AsyncNodeA as 异步节点A (例如 获取数据)
    participant AsyncNodeB as 异步节点B (例如 发送邮件)
    participant EventLoop as asyncio事件循环

    User->>AsyncFlow: await run_async(shared)
    AsyncFlow->>Orch: await _orch_async(shared)
    Note over Orch: 当前节点 = AsyncNodeA
    Orch->>AsyncNodeA: await _run_async(shared)  # 步骤 3：Await 异步节点运行
    Note over AsyncNodeA: 节点A 开始执行 (例如 await API调用)
    AsyncNodeA->>EventLoop: 让出控制权 (因内部 await)
    Note over Orch: _orch_async 在 await _run_async 处暂停, 等待节点A
    EventLoop->>EventLoop: (处理其他就绪的异步任务...)
    EventLoop->>AsyncNodeA: 恢复节点A的执行
    AsyncNodeA-->>Orch: 返回 action (例如 "next") # 步骤 4：获取 Action
    Orch->>AsyncFlow: get_next_node(AsyncNodeA, "next") # 步骤 5：查找下一节点
    AsyncFlow-->>Orch: 返回下一个节点 (AsyncNodeB)
    Orch-->>Orch: 更新当前节点 = AsyncNodeB # 步骤 6：更新
    Orch->>AsyncNodeB: await _run_async(shared) # 步骤 3 (重复)
    Note over AsyncNodeB: 节点B 开始执行 (例如 await 发送邮件)
    AsyncNodeB->>EventLoop: 让出控制权
    Note over Orch: _orch_async 再次暂停等待节点B
    EventLoop->>EventLoop: (处理其他任务...)
    EventLoop->>AsyncNodeB: 恢复节点B
    AsyncNodeB-->>Orch: 返回 action (例如 "done")
    Orch->>AsyncFlow: get_next_node(AsyncNodeB, "done")
    AsyncFlow-->>Orch: 返回 None (流程结束) # 步骤 8
    Orch-->>AsyncFlow: 返回最后的 action
    AsyncFlow-->>User: 返回结果
```

**代码实现速览**:

`AsyncFlow` 的核心逻辑可以在 `pocketflow/__init__.py` 文件中找到。其简化结构如下：

```python
# (来自 pocketflow/__init__.py 的 AsyncFlow 简化版本)
import asyncio
import copy

# 首先 AsyncFlow 继承自 Flow 和 AsyncNode
# 这让它可以像 Flow 一样连接节点，也拥有 AsyncNode 的 async 方法框架
class AsyncFlow(Flow, AsyncNode):

    # 核心：异步编排逻辑
    async def _orch_async(self, shared, params=None):
        curr = copy.copy(self.start_node) # 复制起始节点
        p = (params or {**self.params}) # 合并参数
        last_action = None

        while curr: # 异步循环
            curr.set_params(p) # 设置节点参数

            # --- 关键判断 ---
            if isinstance(curr, AsyncNode):
                # 如果是 AsyncNode，使用 await 调用 _run_async
                last_action = await curr._run_async(shared)
            else:
                # 如果是普通 Node，直接调用 _run (同步)
                last_action = curr._run(shared)
            # --- 结束判断 ---

            # 查找并复制下一个节点
            curr = copy.copy(self.get_next_node(curr, last_action))

        return last_action # 返回最后一个 action

    # 异步运行入口
    async def _run_async(self, shared):
        # AsyncFlow 自身的 prep_async (通常不用)
        p = await self.prep_async(shared)
        # 调用异步编排逻辑
        o = await self._orch_async(shared)
        # AsyncFlow 自身的 post_async (通常返回编排结果)
        return await self.post_async(shared, p, o)

    # AsyncFlow 自身的 post_async 默认返回编排结果
    async def post_async(self, shared, prep_res, exec_res):
        return exec_res

    # 覆盖同步 run 方法，强制使用 run_async
    def _run(self, shared):
        raise RuntimeError("对于 AsyncFlow, 请使用 run_async() 而不是 run()")

```

这个结构清晰地展示了 `AsyncFlow` 如何通过其异步的 `_orch_async` 方法来驱动流程。最核心的部分是 `isinstance(curr, AsyncNode)` 的判断，它确保了 `AsyncFlow` 能够正确地 `await` 异步节点，并在它们等待时让出控制权，同时也能兼容执行同步节点。

## 总结

在本章中，我们学习了 PocketFlow 中用于构建高效异步工作流的关键组件——**异步流程 (AsyncFlow)**。

*   `AsyncFlow` 是 [流程 (Flow)](02_流程__flow__.md) 的异步版本，专门用于编排包含 [异步节点 (AsyncNode)](03_异步节点__asyncnode__.md) 的工作流。
*   它使用与 `Flow` 相同的语法 (`>>`, `- "action" >>`) 来连接节点。
*   通过调用 `await flow.run_async(shared)` 来运行。
*   其核心优势在于 **调度逻辑本身是异步的** (`_orch_async`)。当流程中的 `AsyncNode` 因 `await` 而暂停时，`AsyncFlow` 也会暂停并将控制权交还给事件循环，实现 **流程层面的非阻塞**。
*   `AsyncFlow` 可以无缝地混合编排 `AsyncNode` 和普通 `Node`。

通过使用 `AsyncFlow` 和 `AsyncNode`，我们可以构建出能够充分利用 `asyncio` 优势、处理高并发 I/O 操作的、响应迅速的 Python 应用。

我们已经了解了如何处理单个任务（`Node`）和异步任务（`AsyncNode`），以及如何将它们串联成顺序或带条件的流程（`Flow`, `AsyncFlow`）。但是，如果我们需要对一批数据执行相同的操作呢？比如，同时处理多个用户的请求，或者批量处理一堆文件？

**下一章**: [第 5 章：批处理节点 (BatchNode)](05_批处理节点__batchnode__.md) - 学习如何使用 `BatchNode` 来高效地对一组项目执行相同的处理逻辑。

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)