# Tutorial: PocketFlow

PocketFlow 是一个 **超级轻量级** 的框架，仅用约 *100行代码*，帮助开发者构建基于大语言模型（LLM）的应用。
它的核心是 **图（Graph）** 抽象：将复杂任务分解为 **节点（Node）**，并通过 **流程（Flow）** 来编排它们的执行顺序和数据流。
这使得构建如 *聊天机器人、RAG、工作流* 等应用像搭积木一样简单。
框架还提供了对 **异步（Async）** 操作和 **批处理（Batch）**（包括*并行批处理*）的支持，以提高性能和效率。


**Source Repository:** [None](None)

```mermaid
flowchart TD
    A0["节点 (Node)
"]
    A1["流程 (Flow)
"]
    A2["异步节点 (AsyncNode)
"]
    A3["异步流程 (AsyncFlow)
"]
    A4["批处理节点 (BatchNode)
"]
    A5["批处理流程 (BatchFlow)
"]
    A6["异步并行批处理节点/流程 (AsyncParallelBatchNode/Flow)
"]
    A1 -- "管理节点" --> A0
    A3 -- "管理异步节点" --> A2
    A2 -- "继承自" --> A0
    A3 -- "继承自" --> A1
    A4 -- "继承自" --> A0
    A5 -- "包装流程" --> A1
    A6 -- "并行化执行" --> A5
```

## Chapters

1. [节点 (Node)
](01_节点__node__.md)
2. [流程 (Flow)
](02_流程__flow__.md)
3. [异步节点 (AsyncNode)
](03_异步节点__asyncnode__.md)
4. [异步流程 (AsyncFlow)
](04_异步流程__asyncflow__.md)
5. [批处理节点 (BatchNode)
](05_批处理节点__batchnode__.md)
6. [批处理流程 (BatchFlow)
](06_批处理流程__batchflow__.md)
7. [异步并行批处理节点/流程 (AsyncParallelBatchNode/Flow)
](07_异步并行批处理节点_流程__asyncparallelbatchnode_flow__.md)


---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)