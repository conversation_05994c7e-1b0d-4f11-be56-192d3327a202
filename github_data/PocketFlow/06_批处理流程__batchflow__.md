# Chapter 6: 批处理流程 (BatchFlow)


欢迎来到 PocketFlow 教程的第六章！在上一章 [第 5 章：批处理节点 (BatchNode)](05_批处理节点__batchnode__.md) 中，我们学习了如何使用 `BatchNode` 对一批相似的数据项执行同一个**单一操作**（即同一个 `exec` 方法）。这对于批量翻译、批量格式转换等任务非常有用。

但是，如果我们想对一批数据执行的不是单个操作，而是一个包含**多个步骤的完整工作流程**呢？

想象一下，你需要处理一批图片文件（比如 `cat.jpg`, `dog.jpg`, `bird.jpg`），并且对**每一张图片**都要执行完全相同的处理流程：

1.  加载图片 (`LoadImage` 节点)
2.  应用滤镜 (`ApplyFilter` 节点)
3.  保存处理后的图片 (`SaveImage` 节点)

这是一个包含三个节点的[流程 (Flow)](02_流程__flow__.md)。我们当然可以为每张图片手动创建一个 `Flow` 实例并运行，但这显然非常繁琐。`BatchNode` 也不太适用，因为它只能重复执行单个节点的 `exec` 逻辑，无法直接应用一个多节点的流程。

我们需要一种方法，能够像 `BatchNode` 处理单个操作一样，**对一批输入重复执行一个完整的内部流程**。这就是 **批处理流程 (BatchFlow)** 的设计目的。

在本章中，我们将学习：

*   什么是批处理流程 (BatchFlow)？它和普通流程有什么不同？
*   `BatchFlow` 的 `prep` 和 `post` 方法的作用是什么？
*   如何创建一个 `BatchFlow` 来批量处理图片？
*   `BatchFlow` 是如何在内部为每个输入参数运行其内部流程的？

## 什么是批处理流程 (BatchFlow)？

**批处理流程 (BatchFlow)** 是 [流程 (Flow)](02_流程__flow__.md) 的一种扩展，专门用于**对一批不同的输入参数，重复执行一个内部定义的流程（或单个节点）**。

你可以把它想象成一个**自动化脚本管理器**：

1.  **准备任务清单 (`prep`)**: 管理器首先需要知道有哪些任务需要执行，以及每个任务的具体要求（参数）。`BatchFlow` 的 `prep` 方法就负责生成这个**任务参数列表**。它接收共享数据 `shared`，然后返回一个列表，列表中的**每一项通常是一个字典**，包含了运行一次内部流程所需的特定参数。例如，对于批量图片处理，`prep` 可能会返回 `[{'input_file': 'cat.jpg'}, {'input_file': 'dog.jpg'}, {'input_file': 'bird.jpg'}]`。
2.  **执行标准流程 (内部流程)**: 对于任务清单中的**每一个参数项**，管理器都会启动一次预定义的标准工作流程（这个标准流程就是你传递给 `BatchFlow` 的内部 `Flow` 或 `Node`）。在我们的例子中，就是为 `cat.jpg` 运行一次“加载->滤镜->保存”流程，然后为 `dog.jpg` 运行一次同样的流程，以此类推。
3.  **汇总报告 (`post`)**: 当所有任务都按部就班地执行完毕后，管理器可能会需要对所有任务的执行情况做一个总结。`BatchFlow` 的 `post` 方法会在所有批次执行完成后被调用。它接收 `prep` 返回的原始参数列表，并且可以访问 `shared` 数据（内部流程的执行结果通常会更新到 `shared` 中），让你有机会对整个批处理任务的结果进行最终的聚合或处理。

**与 `BatchNode` 的关键区别**：

*   `BatchNode` 的 `prep` 返回一个**数据项列表**，其 `exec` 方法对**每个数据项**执行一次。
*   `BatchFlow` 的 `prep` 返回一个**参数字典列表**，它会对**每个参数字典**运行一次其内部定义的**完整流程**。

## 示例：批量图片处理

让我们用 `BatchFlow` 来实现之前提到的批量图片处理任务。

### 1. 定义内部处理流程 (Base Flow)

首先，我们需要定义处理**单张**图片的标准流程。这个流程包含三个（模拟的）节点：加载、滤镜、保存。

```python
# 导入所需模块
import os
import time
from pocketflow import Node, Flow, BatchFlow

# --- 定义内部流程的节点 (模拟) ---

class LoadImage(Node):
    def prep(self, shared):
        # 从参数中获取输入文件名 (参数由 BatchFlow 提供)
        filename = self.params.get("input_file", "default.jpg")
        print(f"  加载节点：准备加载图片 '{filename}'...")
        return filename

    def exec(self, filename):
        # 模拟加载图片
        print(f"    加载节点：正在加载 '{filename}'...")
        time.sleep(0.2)
        # 假设加载后的图片数据
        image_data = f"数据<{filename}>"
        return image_data

    def post(self, shared, prep_res, exec_res):
        # 将加载的数据放入共享区域，供后续节点使用
        shared["current_image_data"] = exec_res
        print(f"    加载节点：'{prep_res}' 加载完成。")
        return "apply_filter" # 下一步：应用滤镜

class ApplyFilter(Node):
    def prep(self, shared):
        # 获取当前图片数据
        return shared.get("current_image_data")

    def exec(self, image_data):
        filter_type = self.params.get("filter_type", "默认滤镜") # 也可以从参数获取滤镜类型
        print(f"    滤镜节点：正在对 '{image_data}' 应用 '{filter_type}'...")
        time.sleep(0.3)
        filtered_data = f"{image_data}-已应用{filter_type}"
        return filtered_data

    def post(self, shared, prep_res, exec_res):
        shared["filtered_image_data"] = exec_res
        print(f"    滤镜节点：滤镜应用完成。")
        return "save" # 下一步：保存

class SaveImage(Node):
    def prep(self, shared):
        # 获取处理后的数据和原始文件名
        filename = self.params.get("input_file", "default.jpg")
        data_to_save = shared.get("filtered_image_data")
        return filename, data_to_save

    def exec(self, prep_res):
        filename, data_to_save = prep_res
        output_filename = f"processed_{filename}"
        print(f"    保存节点：正在将 '{data_to_save}' 保存到 '{output_filename}'...")
        time.sleep(0.1)
        # 模拟保存操作
        # 注意：这里是内部流程的结束，post 返回 None
        return output_filename

    def post(self, shared, prep_res, exec_res):
        print(f"    保存节点：'{exec_res}' 保存成功。\n")
        # 将本次处理的文件名和输出文件名存入 shared，供 BatchFlow 的 post 聚合
        input_file = prep_res[0]
        if "batch_results" not in shared:
            shared["batch_results"] = {}
        shared["batch_results"][input_file] = exec_res
        return None # 内部流程结束

# --- 创建处理单张图片的流程 ---
def create_single_image_flow():
    """创建一个处理单张图片的基础流程"""
    load_node = LoadImage()
    filter_node = ApplyFilter()
    save_node = SaveImage()

    # 连接节点
    load_node - "apply_filter" >> filter_node
    filter_node - "save" >> save_node

    # 创建并返回基础流程实例
    # 注意：这个流程将作为 BatchFlow 的 "内部流程"
    return Flow(start=load_node)

```

**代码解释**:

*   我们定义了三个简单的 `Node`：`LoadImage`, `ApplyFilter`, `SaveImage`，模拟了图片处理的步骤。
*   **关键点**：在 `LoadImage`, `ApplyFilter` 和 `SaveImage` 的 `prep` 或 `exec` 方法中，我们使用 `self.params.get("input_file", ...)` 来获取当前需要处理的图片文件名。这个 `input_file` 参数将由外层的 `BatchFlow` 在每次运行时动态提供。
*   在 `SaveImage` 的 `post` 方法中，我们将输入文件名和输出文件名的映射关系存入了 `shared["batch_results"]` 字典。这是一种常见的模式：内部流程的执行结果被写入共享数据，以便在外层 `BatchFlow` 的 `post` 方法中进行聚合。
*   `create_single_image_flow` 函数负责创建并连接这些节点，返回一个标准的 `Flow` 实例。这个 `Flow` 就是我们要重复执行的“标准流程”。

### 2. 定义批处理流程 (Batch Flow)

现在，我们来定义 `ImageBatchFlow`，它继承自 `pocketflow.BatchFlow`，并使用上面创建的 `single_image_flow` 作为其内部流程。

```python
# --- 定义批处理流程 ---
class ImageBatchFlow(BatchFlow):
    def prep(self, shared):
        """准备阶段：生成需要处理的图片列表及参数"""
        # 假设我们要处理这些图片
        # 实际应用中，这可能来自文件系统扫描或配置
        image_files = ["cat.jpg", "dog.jpg", "bird.jpg"]

        # 为每个图片文件生成一个参数字典
        # 这个列表就是 BatchFlow 要迭代处理的内容
        batch_params = [{"input_file": img, "filter_type": "模糊"} for img in image_files]

        print(f"批处理准备：将对 {len(batch_params)} 张图片执行处理流程。")
        print(f"批处理准备：参数列表: {batch_params}")
        return batch_params # 返回参数字典列表

    def post(self, shared, prep_res, exec_res):
        """后处理阶段：总结批处理结果"""
        # prep_res 是 prep 方法返回的参数列表
        # exec_res 在 BatchFlow 的默认实现中通常是 None

        print("\n批处理完成！总结：")
        results = shared.get("batch_results", {})
        if results:
            for input_img, output_img in results.items():
                print(f"  - 输入: {input_img} -> 输出: {output_img}")
        else:
            print("  未找到处理结果。")

        # BatchFlow 本身的流程可以在这里结束或继续
        return "batch_complete"

```

**代码解释**:

1.  **`ImageBatchFlow(BatchFlow)`**: 我们定义了一个继承自 `BatchFlow` 的类。
2.  **`prep`**: 这个方法的核心任务是返回一个**列表**，列表中的每一项都是一个**字典**。每个字典代表了运行一次内部流程（即 `single_image_flow`）所需的参数。在这个例子中，每个字典包含 `'input_file'` 和 `'filter_type'` 键。PocketFlow 会自动遍历这个列表。
3.  **`post`**: 在所有图片都处理完毕后（即内部流程对 `prep` 返回的每个参数都运行了一遍之后），这个方法会被调用。我们从 `shared` 中读取由内部流程（`SaveImage` 节点）写入的 `batch_results`，并打印一个总结。

### 3. 创建并运行批处理流程

最后，我们将内部流程和批处理流程组合起来并运行。

```python
# 1. 创建内部流程实例 (处理单张图片)
single_flow = create_single_image_flow()

# 2. 创建批处理流程实例，并将内部流程作为其 "start"
#    这意味着 ImageBatchFlow 会重复运行 single_flow
batch_image_processor = ImageBatchFlow(start=single_flow)

# 准备共享数据 (初始可以为空，因为参数由 prep 生成)
shared_data = {}

# 运行批处理流程！
print("开始运行批量图片处理流程...")
final_action = batch_image_processor.run(shared_data)
print(f"\n批处理流程最终返回动作: '{final_action}'")

# (可选) 查看最终的共享数据
# print("\n最终共享数据:", shared_data)

```

**代码解释**:

*   **`single_flow = create_single_image_flow()`**: 创建了处理单张图片的基础流程。
*   **`batch_image_processor = ImageBatchFlow(start=single_flow)`**: 这是关键！我们创建 `ImageBatchFlow` 实例时，将 `single_flow` 作为 `start` 参数传递进去。这告诉 `BatchFlow`：“当你需要执行内部流程时，就运行这个 `single_flow`”。
*   **`batch_image_processor.run(shared_data)`**: 启动整个批处理过程。

当你运行这段代码时，你会看到：

1.  `ImageBatchFlow` 的 `prep` 方法首先运行，打印出准备信息和参数列表。
2.  然后，`single_image_flow`（加载->滤镜->保存）会**依次**运行三次：
    *   第一次运行时，其内部节点访问 `self.params` 会得到 `{'input_file': 'cat.jpg', 'filter_type': '模糊'}`。
    *   第二次运行时，其内部节点访问 `self.params` 会得到 `{'input_file': 'dog.jpg', 'filter_type': '模糊'}`。
    *   第三次运行时，其内部节点访问 `self.params` 会得到 `{'input_file': 'bird.jpg', 'filter_type': '模糊'}`。
    你会看到每个文件的加载、滤镜、保存日志交替出现。
3.  所有内部流程运行完毕后，`ImageBatchFlow` 的 `post` 方法运行，打印出处理结果的总结。

## BatchFlow 内部是如何工作的？

当你调用 `batch_flow.run(shared)` 时，`BatchFlow` 的内部机制（主要在其 `_run` 方法中）与标准 `Flow` 有所不同：

1.  **调用 `prep(shared)`**: 首先调用自身的 `prep` 方法，获取一个**参数字典的列表** (`prep_res`)。比如 `[{'input': 'a'}, {'input': 'b'}]`。
2.  **开始循环**: 进入一个循环，遍历 `prep` 返回的**参数列表**。
3.  **运行内部流程 (针对每个参数)**: 对于列表中的**每一个参数字典** `bp` (例如 `{'input': 'a'}`):
    *   它调用内部流程的编排方法 `_orch(shared, merged_params)`。
    *   **关键点**: 传递给 `_orch` 的参数 `merged_params` 是 `BatchFlow` 自身的参数 (`self.params`) 和当前循环项的参数字典 `bp` 合并后的结果（通常是 `{**self.params, **bp}`）。这意味着内部流程的节点可以通过 `self.params` 访问到本次特定运行所需的参数（如 `self.params['input']` 的值会是 `'a'`）。
    *   内部流程 `_orch` 会像正常的 `Flow` 一样执行其包含的节点序列，使用传入的特定参数。
4.  **循环结束**: 当参数列表中的所有项都处理完毕后，循环结束。
5.  **调用 `post(shared, prep_res, exec_res)`**: 最后，调用 `BatchFlow` 自身的 `post` 方法。`prep_res` 是第 1 步生成的完整参数列表，`exec_res` 在 `BatchFlow` 的默认 `_run` 实现中通常是 `None`（因为内部流程的结果通常通过修改 `shared` 来传递，而不是作为返回值收集）。

下面是一个简化的时序图：

```mermaid
sequenceDiagram
    participant User as 用户
    participant BFlow as 批处理流程 (BatchFlow)
    participant Prep as BFlow.prep()
    participant Loop as BFlow._run() [内部循环]
    participant InnerFlow as 内部流程._orch()
    participant Post as BFlow.post()

    User->>BFlow: run(shared)
    BFlow->>Prep: prep(shared)
    Prep-->>BFlow: 返回参数列表 [参数1, 参数2] (prep_res)
    BFlow->>Loop: _run(shared)
    Note over Loop: 开始遍历参数列表 `prep_res`
    Loop->>InnerFlow: _orch(shared, {**BFlow参数, **参数1})  # 使用参数1运行内部流程
    InnerFlow-->>Loop: (内部流程执行完毕)
    Loop->>InnerFlow: _orch(shared, {**BFlow参数, **参数2})  # 使用参数2运行内部流程
    InnerFlow-->>Loop: (内部流程执行完毕)
    Note over Loop: 所有参数处理完毕
    Loop-->>BFlow: (循环结束)
    BFlow->>Post: post(shared, prep_res, None) # 注意 exec_res 为 None
    Post-->>BFlow: 返回 action
    BFlow-->>User: 返回 action
```

**代码实现速览**:

`BatchFlow` 的核心逻辑可以在 `pocketflow/__init__.py` 文件中找到。它覆盖了 `Flow` 的 `_run` 方法：

```python
# (来自 pocketflow/__init__.py 的简化版本)

class Flow(BaseNode):
    # ... Flow 的原有实现 ...
    def _orch(self, shared, params=None):
        # 这是运行单个流程实例的核心编排逻辑
        curr = copy.copy(self.start_node)
        p = (params or {**self.params}) # 使用传入的或自身的参数
        last_action = None
        while curr:
            curr.set_params(p) # 设置节点参数
            last_action = curr._run(shared) # 运行节点
            curr = copy.copy(self.get_next_node(curr, last_action)) # 获取下一个节点
        return last_action

# BatchFlow 继承自 Flow
class BatchFlow(Flow):
    # BatchFlow 覆盖了 _run 方法
    def _run(self, shared):
        # 1. 调用 prep 获取参数列表
        prep_results = self.prep(shared) or [] # prep_res 可能为 None

        # 2. 遍历参数列表中的每一项 bp (batch parameter)
        for bp in prep_results:
            # 3. 对每一项参数 bp，调用 *内部流程的 _orch* 方法
            #    关键：将 BatchFlow 自己的参数和当前批次的参数 bp 合并后传递给 _orch
            self._orch(shared, {**self.params, **bp})

        # 4. 循环结束后，调用 post 方法
        #    注意：exec_res 传递的是 None
        return self.post(shared, prep_results, None)

```

这段代码清晰地展示了 `BatchFlow` 如何在其 `_run` 方法中协调整个过程：先调用 `prep` 获取任务参数列表，然后循环遍历这个列表，为每个参数调用 `_orch`（继承自 `Flow`，用于运行内部流程）并传入合并后的参数，最后调用 `post` 进行收尾。

## 总结

在本章中，我们学习了 PocketFlow 中用于重复执行整个工作流程的 **批处理流程 (BatchFlow)**：

*   `BatchFlow` 是 [流程 (Flow)](02_流程__flow__.md) 的扩展，适用于对一批不同的**输入参数**，运行同一个**内部流程**。
*   它的 `prep` 方法负责生成一个**参数字典的列表**，每一项代表一次内部流程的运行输入。
*   它在其 `_run` 方法中遍历 `prep` 返回的列表，为**每个参数字典**调用其内部流程（通过 `start=` 指定）的 `_orch` 方法，并传入合并后的参数。
*   内部流程的节点可以通过 `self.params` 访问到特定于该次运行的参数。
*   它的 `post` 方法在所有批次运行完成后被调用，用于聚合结果（通常通过读取 `shared` 数据实现）。
*   我们通过一个批量处理图片的例子，展示了如何定义内部流程、定义 `BatchFlow`（包含 `prep` 和 `post`），以及如何将它们组合并运行。

`BatchFlow` 为需要将复杂工作流应用于多个输入的场景提供了一个强大而灵活的工具，例如批量处理文件、对多个用户执行相同操作序列等。

到目前为止，我们讨论的 `BatchNode` 和 `BatchFlow` 都是**按顺序**处理批次中的每一项的。在处理图片或翻译文档的例子中，这意味着处理完第一张图片（或第一种语言）后，才会开始处理第二张。如果每个处理步骤都比较耗时（尤其是涉及 I/O 操作时），这种顺序执行可能会很慢。

有没有办法让这些批处理任务**并行**执行，以加快整体速度呢？

**下一章**: [第 7 章：异步并行批处理节点/流程 (AsyncParallelBatchNode/Flow)](07_异步并行批处理节点_流程__asyncparallelbatchnode_flow__.md) - 学习如何利用 `asyncio` 实现批处理任务的并行执行，大幅提升效率。

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)