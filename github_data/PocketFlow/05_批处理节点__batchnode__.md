# Chapter 5: 批处理节点 (BatchNode)


欢迎来到第五章！在上一章 [第 4 章：异步流程 (AsyncFlow)](04_异步流程__asyncflow__.md) 中，我们学习了如何使用 `AsyncFlow` 来编排包含异步节点 (`AsyncNode`) 的工作流，从而在等待 I/O 操作时保持程序的高效运行。

现在，让我们考虑另一种常见场景：有时你需要对一大批相似的数据项执行完全相同的处理步骤。比如，假设你写了一篇很棒的 README 文档，并且想把它翻译成多种不同的语言，以便让更多人阅读。或者，你需要处理一个巨大的 CSV 文件，这个文件大到无法一次性读入内存，你想把它分成小块，逐块进行分析。

为每个数据项（每种语言的翻译任务，或每个 CSV 文件块）创建一个单独的 [节点 (Node)](01_节点__node__.md) 实例似乎不太现实，也会让 [流程 (Flow)](02_流程__flow__.md) 变得异常复杂。我们需要一种更优雅的方式来处理这类“批量”任务。

这正是 **批处理节点 (BatchNode)** 的用武之地！

在本章中，我们将学习：

*   什么是批处理节点 (BatchNode)？它和普通节点有什么不同？
*   `BatchNode` 的 `prep`, `exec`, `post` 方法各自负责什么？
*   如何创建一个 `BatchNode` 来将一篇文档翻译成多种语言？
*   `BatchNode` 是如何在内部按顺序处理批次中的每个项目的？

## 什么是批处理节点 (BatchNode)？

**批处理节点 (BatchNode)** 是 [节点 (Node)](01_节点__node__.md) 的一种扩展，专门用于 **对一批数据项执行相同的处理逻辑**。

你可以把它想象成一台**复印机**：

1.  **准备 (prep)**：你把一叠需要复印的原始文档（**一批数据项**）放进复印机的进纸器。`BatchNode` 的 `prep` 方法就像这个准备步骤，它接收共享数据 `shared`，然后**返回一个包含所有待处理项的可迭代对象**（比如一个列表或迭代器），这个列表里的每一项就是我们要单独处理的数据。
2.  **执行 (exec)**：复印机开始工作，但它并不是同时复印所有文档，而是**一张接一张地**复印。`BatchNode` 的 `exec` 方法也是这样，它会被**依次调用**，每次接收**批次中的一个数据项**作为输入，并执行核心的处理逻辑。这个 `exec` 方法和你为单个项目定义的处理逻辑完全一样。
3.  **后处理 (post)**：当所有文档都复印完后，你在出纸口得到一叠复印件（**包含所有单个执行结果的列表**）。`BatchNode` 的 `post` 方法接收这个结果列表，让你有机会对所有单个结果进行**汇总、整理或最终处理**。

总结一下 `BatchNode` 的三个核心方法与普通 `Node` 的不同之处：

*   **`prep(self, shared)`**：和 `Node` 类似，用于准备数据。但它的**返回值必须是一个可迭代对象**（例如列表、元组、生成器），包含了所有需要被 `exec` 单独处理的数据项。
*   **`exec(self, item)`**：这是核心处理逻辑，但它接收的参数 `item` 是 `prep` 返回的可迭代对象中的**单个元素**。这个方法会**为 `prep` 返回的每一个元素都执行一次**，而且是**按顺序执行**的。
*   **`post(self, shared, prep_res, exec_res_list)`**：和 `Node` 类似，用于后处理。但它接收的第三个参数 `exec_res_list` 是一个**列表**，包含了**每次调用 `exec` 方法得到的所有返回结果**（顺序与 `prep` 返回的迭代对象一致）。

**关键点**：`BatchNode` 的 `exec` 方法是**按顺序、逐个**处理批次中的项目的。它不是并行处理。如果你需要并行处理，可以关注后续章节 [第 7 章：异步并行批处理节点/流程](07_异步并行批处理节点_流程__asyncparallelbatchnode_flow__.md)。

## 示例：批量翻译文档

让我们用 `BatchNode` 来实现前面提到的“将一篇 README 文档翻译成多种语言”的任务。

### 1. 定义 TranslateTextNode

我们将创建一个名为 `TranslateTextNode` 的节点，继承自 `pocketflow.BatchNode`。

```python
import os
import time
from pocketflow import BatchNode, Flow # 导入 BatchNode 和 Flow

# 模拟调用 LLM 进行翻译的函数
def fake_translate(text, language):
    """模拟调用 LLM API 进行翻译 (同步)"""
    print(f"  [模拟 API] 正在将文本翻译成 {language}...")
    time.sleep(0.5) # 模拟网络或计算延迟
    # 简化处理：返回固定的翻译结果
    return f"这是 '{text[:20]}...' 的 {language} 翻译结果。"

# --- 定义我们的批量翻译节点 ---
class TranslateTextNode(BatchNode):
    def prep(self, shared):
        """准备阶段：创建翻译任务列表"""
        source_text = shared.get("text_to_translate", "默认文本")
        target_languages = shared.get("languages", ["中文", "西班牙文"]) # 默认翻译成这两种

        # 返回一个列表，每个元素是一个包含 (源文本, 目标语言) 的元组
        # 这个列表就是要被 exec 逐个处理的 "批次"
        tasks = [(source_text, lang) for lang in target_languages]
        print(f"准备阶段：创建了 {len(tasks)} 个翻译任务。")
        return tasks

    def exec(self, task_item):
        """执行阶段：翻译单个语言"""
        # 从任务项中解包出源文本和目标语言
        source_text, target_language = task_item

        print(f"执行阶段：开始翻译成 {target_language}...")
        # 调用（模拟的）翻译函数
        translation_result = fake_translate(source_text, target_language)

        # 返回包含语言和翻译结果的字典
        return {"language": target_language, "translation": translation_result}

    def post(self, shared, prep_res, exec_res_list):
        """后处理阶段：处理所有翻译结果"""
        print("后处理阶段：收到了所有翻译结果。")

        # exec_res_list 是一个列表，包含了每次 exec 调用的返回值
        # 例如: [{'language': '中文', 'translation': '...'}, {'language': '西班牙文', 'translation': '...'}]
        
        translations = {}
        for result in exec_res_list:
            lang = result["language"]
            text = result["translation"]
            translations[lang] = text
            print(f"  - {lang}: {text[:30]}...") # 打印部分结果

        # 将所有翻译结果整合后存回共享数据
        shared["all_translations"] = translations
        print("后处理阶段：所有翻译结果已保存到 shared['all_translations']")

        # 告诉流程下一步该做什么（这里我们假设完成）
        return "translations_done"

```

**代码解释**:

1.  **`TranslateTextNode(BatchNode)`**: 我们定义了一个继承自 `BatchNode` 的类。
2.  **`prep`**: 从 `shared` 获取源文本和目标语言列表。它返回一个列表 `tasks`，列表中的每个元素是一个元组 `(源文本, 目标语言)`。这个列表就是我们要处理的“批次”。
3.  **`exec`**: 接收 `prep` 返回列表中的**一个** `task_item`（即一个元组）。它解包出文本和语言，调用 `fake_translate` 进行模拟翻译，然后返回一个包含该语言翻译结果的字典。**PocketFlow 会自动为 `prep` 返回的 `tasks` 列表中的每一个元组调用一次 `exec` 方法**。
4.  **`post`**: 接收 `exec_res_list`，这是一个列表，收集了**所有** `exec` 调用返回的字典。我们遍历这个列表，将结果整理到一个字典中，并存回 `shared`。最后返回一个 action `"translations_done"`。

### 2. 使用 Flow 运行 BatchNode

现在，我们可以像使用普通 [节点 (Node)](01_节点__node__.md) 一样，将这个 `BatchNode` 放入一个 [流程 (Flow)](02_流程__flow__.md) 中运行。

```python
# 创建节点实例
translate_node = TranslateTextNode()

# (可选) 可以添加一个后续节点来展示结果
class ShowResultsNode(Node):
    def exec(self, _):
        print("\n--- 最终翻译结果 ---")
        translations = shared.get("all_translations", {})
        if translations:
            for lang, text in translations.items():
                print(f"{lang}: {text}")
        else:
            print("没有找到翻译结果。")
        return None

# 创建后续节点实例
show_results_node = ShowResultsNode()

# 连接节点：翻译节点完成后，如果返回 "translations_done"，则执行展示结果节点
translate_node - "translations_done" >> show_results_node

# 创建流程实例，从翻译节点开始
translation_flow = Flow(start=translate_node)

# 准备共享数据
shared_data = {
    "text_to_translate": "Hello PocketFlow!",
    "languages": ["中文", "西班牙文", "日文"] # 尝试翻译成三种语言
}

# 运行流程！
print("开始运行批量翻译流程...")
translation_flow.run(shared_data)
print("\n流程运行结束。")

# 检查最终的共享数据
# print("最终共享数据:", shared_data) # 可以取消注释查看
```

**代码解释**:

*   我们像往常一样创建了 `TranslateTextNode` 实例。
*   我们还创建了一个简单的 `ShowResultsNode`（普通 `Node`）用于在流程最后打印结果。
*   我们使用 `- "translations_done" >>` 将 `TranslateTextNode` 连接到 `ShowResultsNode`。
*   创建了一个包含源文本和目标语言列表的 `shared_data` 字典。
*   调用 `translation_flow.run(shared_data)` 启动流程。

当你运行这段代码时，你会看到：

1.  `prep` 方法打印准备信息。
2.  `exec` 方法会**依次**打印三次“开始翻译成...”和“[模拟 API]”的消息（因为我们指定了三种语言）。
3.  `post` 方法打印收到所有结果，并列出简略信息。
4.  最后，`ShowResultsNode` 会打印出所有完整的翻译结果。

注意 `exec` 的输出是按顺序出现的，证明了 `BatchNode` 默认是**顺序执行**批处理中的每一项的。

## BatchNode 内部是如何工作的？

当你调用 `batch_node.run(shared)` 时，PocketFlow 在 `BatchNode` 的 `_exec` 方法中增加了一个处理层：

1.  **调用 `prep(shared)`**: 和普通 `Node` 一样，准备数据。但这次期望返回一个**可迭代对象**（比如我们例子中的 `tasks` 列表）。
2.  **调用 `_exec(prep_res)` (BatchNode 的版本)**:
    *   PocketFlow 检查到这是一个 `BatchNode`。
    *   它内部会**遍历** `prep` 返回的那个可迭代对象 (`prep_res`)。
    *   对于可迭代对象中的**每一个 `item`**：
        *   它会调用**普通 `Node` 的 `_exec` 逻辑**来处理这个 `item`。（这个内部逻辑包含了重试和 fallback，也就是说，**批处理中的每一项都可以独立地重试或执行 fallback**，如果该项处理失败的话）。
        *   它收集每次处理 `item` 的结果。
    *   当所有 `item` 都处理完毕后，它返回一个包含所有单个结果的**列表** (`exec_res_list`)。
3.  **调用 `post(shared, prep_res, exec_res_list)`**: 使用包含所有结果的列表进行后处理。

下面是一个简化的时序图，展示了这个过程：

```mermaid
sequenceDiagram
    participant User as 用户
    participant BNode as 批处理节点 (BatchNode)
    participant Prep as BNode.prep()
    participant LoopingExec as BNode._exec() [内部循环]
    participant SingleExec as Node._exec() [处理单项]
    participant Post as BNode.post()

    User->>BNode: run(shared)
    BNode->>Prep: prep(shared)
    Prep-->>BNode: 返回 items (可迭代对象, 如 [任务1, 任务2])
    BNode->>LoopingExec: _exec(items)
    Note over LoopingExec: 开始遍历 items
    LoopingExec->>SingleExec: _exec(任务1)  # 处理第一项
    SingleExec-->>LoopingExec: 返回 结果1
    LoopingExec->>SingleExec: _exec(任务2)  # 处理第二项
    SingleExec-->>LoopingExec: 返回 结果2
    Note over LoopingExec: 所有项处理完毕
    LoopingExec-->>BNode: 返回结果列表 [结果1, 结果2] (exec_res_list)
    BNode->>Post: post(shared, items, [结果1, 结果2])
    Post-->>BNode: 返回 action
    BNode-->>User: 返回 action
```

**代码实现速览**:

`BatchNode` 的核心逻辑非常简洁，可以在 `pocketflow/__init__.py` 中看到它对 `Node` 的 `_exec` 方法的覆盖：

```python
# (来自 pocketflow/__init__.py 的简化版本)

class Node(BaseNode):
    # ... (Node 的原有实现, 包括带重试的 _exec) ...
    def _exec(self, prep_res):
        # 这个是处理单个项目的 _exec，包含重试逻辑
        for self.cur_retry in range(self.max_retries):
            try:
                # 调用用户定义的 exec 方法
                return self.exec(prep_res)
            except Exception as e:
                if self.cur_retry == self.max_retries - 1:
                    # 调用用户的 fallback 方法
                    return self.exec_fallback(prep_res, e)
                if self.wait > 0:
                    time.sleep(self.wait)

# BatchNode 继承自 Node
class BatchNode(Node):
    # BatchNode 覆盖了 _exec 方法
    def _exec(self, items):
        # 这个 _exec 接收 prep 返回的可迭代对象 items
        # 它使用列表推导式来遍历 items
        # 对于 items 中的每一个 i，它调用 *父类(Node)* 的 _exec 方法来处理这个单独的 i
        # super(BatchNode, self) 指向父类 Node
        # 最后返回一个包含所有单项处理结果的列表
        return [super(BatchNode, self)._exec(i) for i in (items or [])]

```

这段代码清晰地展示了 `BatchNode` 如何利用其父类 `Node` 的单项处理能力 (`super(BatchNode, self)._exec(i)`)，并将其应用于 `prep` 方法返回的整个批次中的每一项，最终将所有结果收集到一个列表中。

## 总结

在本章中，我们学习了 PocketFlow 中用于处理批量数据的 **批处理节点 (BatchNode)**：

*   `BatchNode` 是 [节点 (Node)](01_节点__node__.md) 的扩展，适用于对一批数据项执行**相同**的操作。
*   它的 `prep` 方法需要返回一个**可迭代对象**（批次）。
*   它的 `exec` 方法接收批次中的**单个项目**，并会被**依次调用**处理批次中的每一项。
*   它的 `post` 方法接收一个包含所有**单个 `exec` 结果**的列表，用于汇总处理。
*   我们通过一个将文档翻译成多种语言的例子，展示了如何定义和使用 `BatchNode`。
*   `BatchNode` 内部通过循环调用其父类 `Node` 的 `_exec` 方法（包含重试/fallback）来顺序处理批次中的每一项。

`BatchNode` 为需要按顺序处理大量相似任务的场景提供了一个简洁而强大的抽象。

但是，如果我们的批处理任务不是简单的顺序执行，而是涉及到一个包含多个节点的**子流程**呢？比如，对于每个需要翻译的语言，我们可能需要先获取该语言的特定术语库（一个节点），然后才进行翻译（另一个节点）？我们如何对一批数据应用一个完整的**流程**？

**下一章**: [第 6 章：批处理流程 (BatchFlow)](06_批处理流程__batchflow__.md) - 学习如何使用 `BatchFlow` 对一批参数或数据运行一个完整的子流程。

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)