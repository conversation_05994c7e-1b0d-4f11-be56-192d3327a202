# Chapter 3: 异步节点 (AsyncNode)


在上一章节 [第 2 章：流程 (Flow)](02_流程__flow__.md) 中，我们学会了如何使用 `Flow` 将多个独立的 [节点 (Node)](01_节点__node__.md) 连接起来，构建出有序的工作流程。`Flow` 就像一个调度员，确保数据在节点间正确传递，并根据节点的返回值（action）决定下一步该去哪里。

但是，我们之前创建的 `Node` 有一个潜在的限制：它们的 `prep`、`exec` 和 `post` 方法都是 **同步 (synchronous)** 执行的。这意味着，如果一个节点的 `exec` 方法需要执行一些耗时的操作，比如等待一个网络请求返回结果（像调用一个外部 API 获取天气信息），或者等待用户在命令行输入信息，那么整个程序就会在那里“卡住”，无法执行任何其他任务，直到这个耗时操作完成。这就好比一个客服代表在处理一个需要长时间等待回复的客户时，完全不能接听其他电话或处理其他工作，效率很低。

对于需要高效率和高响应性的应用，尤其是那些涉及大量网络通信、文件读写或用户交互的应用（这些通常被称为 **I/O 密集型**操作），这种阻塞行为是不可接受的。我们希望程序在等待这些慢操作时，能够“抽空”去处理其他事情。

这正是 **异步节点 (AsyncNode)** 闪亮登场的原因！

在本章中，我们将学习：

*   什么是异步节点 (AsyncNode)？它解决了什么问题？
*   `AsyncNode` 与普通 `Node` 的主要区别是什么？
*   如何使用 `async/await` 语法创建一个执行异步操作的 `AsyncNode`？
*   `AsyncNode` 内部是如何利用 `asyncio` 实现非阻塞等待的？

## 什么是异步节点 (AsyncNode)？

**异步节点 (AsyncNode)** 是 [节点 (Node)](01_节点__node__.md) 的异步版本，专门为需要执行 **I/O 密集型** 操作而设计。这些操作包括但不限于：

*   进行网络请求（例如，调用天气 API、访问数据库）
*   读写本地文件
*   等待用户输入
*   与其他异步服务交互

`AsyncNode` 的核心是使用了 Python 的 `async/await` 语法。这使得节点在执行这些需要等待的操作时，**不会阻塞** 整个程序的执行流。相反，它可以暂时“挂起”当前任务，让程序有机会去处理其他准备就绪的任务。

**类比：高效的客服代表**

想象一个客服中心：

*   **同步客服 (普通 `Node`)**: 一次只能处理一个客户。如果一个客户需要查资料很久，客服就只能干等着，不能接下一个电话。
*   **异步客服 (`AsyncNode`)**: 当一个客户需要等待时（比如客服需要去后台系统查询信息），这位客服会说“请稍等”，然后立刻去接听下一个电话或处理另一个简单请求。当后台信息查询完毕后，客服再回过头来继续服务之前的客户。

`AsyncNode` 就像这位高效的异步客服，它能在等待一个慢操作（如网络请求）完成的同时，让程序有机会去处理其他任务，从而显著提高整体的响应速度和吞吐量。

## `AsyncNode` vs. `Node`：主要区别

`AsyncNode` 在结构上与普通 `Node` 非常相似，但关键的区别在于它的核心方法都变成了异步函数：

| 特性         | 普通 `Node` (`pocketflow.Node`)                | 异步 `AsyncNode` (`pocketflow.AsyncNode`)                  |
| ------------ | ---------------------------------------------- | ---------------------------------------------------------- |
| **核心方法** | `prep()`<br>`exec()`<br>`post()`<br>`exec_fallback()` | `async def prep_async()`<br>`async def exec_async()`<br>`async def post_async()`<br>`async def exec_fallback_async()` |
| **执行方式** | `node.run(shared)`                             | `await node.run_async(shared)`                             |
| **适用场景** | CPU 密集型任务，或不需要等待 I/O 的快速操作  | I/O 密集型任务（网络、文件、用户输入等）                   |

注意这些方法的命名变化（增加了 `_async` 后缀）和 `async def` 关键字。同样，运行 `AsyncNode` 需要使用 `await` 关键字，并且必须在一个异步环境（例如 `async def main(): ... asyncio.run(main())`）中调用 `run_async` 方法。

## 示例：获取天气信息的异步节点

让我们创建一个 `AsyncNode` 来模拟获取指定城市的天气信息。我们将使用 `asyncio.sleep` 来模拟网络请求的延迟。

### 1. 模拟异步 API 调用

首先，我们定义一个模拟的异步函数来代表调用外部天气 API。

```python
# 导入 asyncio 库
import asyncio
import random

# 模拟异步获取天气的函数
async def get_weather_from_api(city: str):
    """模拟调用天气 API (异步)"""
    delay = random.uniform(0.5, 1.5) # 模拟网络延迟 0.5 到 1.5 秒
    print(f"  [API 模拟] 正在查询 '{city}' 的天气，预计需要 {delay:.2f} 秒...")
    await asyncio.sleep(delay) # 使用 await 等待，让出控制权

    # 模拟 API 可能失败的情况
    if random.random() < 0.1: # 10% 的概率失败
        print(f"  [API 模拟] 查询 '{city}' 天气失败！")
        raise ConnectionError(f"无法连接到 '{city}' 的天气服务")

    # 模拟成功返回结果
    temperature = random.randint(10, 30)
    weather = random.choice(["晴朗", "多云", "小雨"])
    print(f"  [API 模拟] 查询成功！'{city}' 当前 {temperature}°C，天气{weather}。")
    return f"{city}: {temperature}°C, {weather}"

```

**代码解释**:

*   我们定义了一个 `async def` 函数 `get_weather_from_api`。
*   `await asyncio.sleep(delay)` 是关键。当程序执行到 `await` 时，它会告诉事件循环：“这个操作需要等待 `delay` 秒，在我等待期间，你可以去做其他事情”。这期间程序不会卡住。
*   我们还模拟了 API 可能抛出 `ConnectionError` 异常的情况。

### 2. 定义 WeatherNode

现在，我们来定义 `WeatherNode`，它将使用上面的异步函数。

```python
# 导入 AsyncNode
from pocketflow import AsyncNode

# --- 定义我们的异步天气节点 ---
class WeatherNode(AsyncNode):
    def __init__(self, max_retries=1, wait=0.5):
        # 设置重试次数和等待时间 (同样适用于 AsyncNode)
        super().__init__(max_retries=max_retries, wait=wait)

    async def prep_async(self, shared):
        # 1. 准备阶段 (异步): 获取城市名称
        city = shared.get("city_name", "北京") # 默认查询北京
        print(f"准备阶段：将要查询 '{city}' 的天气。")
        return city

    async def exec_async(self, city):
        # 2. 执行阶段 (异步): 调用模拟的异步 API
        print("执行阶段：开始调用天气 API...")
        weather_info = await get_weather_from_api(city) # 使用 await 调用
        return weather_info

    async def exec_fallback_async(self, city, exc):
        # 异步后备逻辑: 如果 exec_async 重试后仍然失败
        print(f"!!! 执行阶段失败 (尝试 {self.max_retries + 1} 次): {exc}")
        return f"无法获取 '{city}' 的天气信息，请稍后再试。"

    async def post_async(self, shared, prep_res, exec_res):
        # 3. 后处理阶段 (异步): 将结果存回 shared
        print(f"后处理阶段：获取到天气信息 '{exec_res}'")
        shared["weather_result"] = exec_res
        print("后处理阶段：天气信息已保存到 shared['weather_result']")
        return "done" # 返回 action

```

**代码解释**:

*   `WeatherNode` 继承自 `pocketflow.AsyncNode`。
*   所有核心方法都使用了 `async def`，并且方法名后加了 `_async`。
*   在 `exec_async` 中，我们使用 `await get_weather_from_api(city)` 来调用我们的异步模拟 API 函数。当执行到这里时，如果 API 调用需要时间，`WeatherNode` 会暂停，让出控制权。
*   `exec_fallback_async` 同样是异步的，用于处理异步执行失败后的情况。
*   `prep_async` 和 `post_async` 在这个例子中虽然没有执行 `await` 操作，但它们也必须定义为 `async def` 以符合 `AsyncNode` 的接口。

### 3. 运行 AsyncNode

要运行 `AsyncNode`，我们需要在一个异步函数中使用 `await node.run_async(shared)`。

```python
import asyncio # 需要 asyncio 来运行异步代码

# 创建节点实例
weather_checker = WeatherNode(max_retries=2, wait=0.5) # 失败时重试2次，间隔0.5秒

# 准备共享数据
shared_data = {"city_name": "上海"}

# 定义一个异步主函数来运行节点
async def main():
    print("开始运行 WeatherNode...")
    action = await weather_checker.run_async(shared_data) # 使用 await run_async
    print(f"节点运行完成，返回的 action 是: '{action}'")
    print("共享数据中的天气结果:", shared_data.get("weather_result"))

    # --- 模拟一个失败的例子 ---
    print("\n开始运行 WeatherNode (模拟API失败)...")
    shared_data_fail = {"city_name": "未知城市"} # 假设这个城市查询总会失败 (通过修改模拟 API 实现)
    # (为了简单起见，我们假设'未知城市'一定会触发上面 API 模拟中的 ConnectionError)
    # 在实际应用中，你可以在模拟 API 函数里添加逻辑： if city == "未知城市": raise ConnectionError(...)
    # 这里我们直接运行，并期望它可能触发 fallback (因为 API 有 10% 概率失败)
    action_fail = await weather_checker.run_async(shared_data_fail)
    print(f"节点运行完成，返回的 action 是: '{action_fail}'")
    print("共享数据中的天气结果:", shared_data_fail.get("weather_result"))


# 运行主异步函数
if __name__ == "__main__":
    # 在 Python 脚本顶层直接运行异步函数需要 asyncio.run()
    # 在 Jupyter Notebook 中，你可以直接 await main() (如果环境支持)
    asyncio.run(main())
```

**代码解释**:

*   我们创建了 `WeatherNode` 实例。`max_retries` 和 `wait` 参数同样对 `AsyncNode` 生效。
*   我们定义了一个 `async def main()` 函数，因为 `await` 只能在 `async` 函数内部使用。
*   在 `main` 函数中，我们使用 `action = await weather_checker.run_async(shared_data)` 来执行异步节点。
*   最后，使用 `asyncio.run(main())` 来启动整个异步程序的执行。
*   当你运行这段代码时，你会看到模拟 API 调用时的打印信息，以及中间的等待时间。如果 API 调用（随机）失败并且重试次数用尽，你会看到 `exec_fallback_async` 被调用。

最重要的是，在 `await get_weather_from_api()` 或 `await asyncio.sleep()` 执行期间，程序并没有完全冻结。如果同时有其他异步任务在运行（例如，在 [异步流程 (AsyncFlow)](04_异步流程__asyncflow__.md) 中），程序会利用这段等待时间去执行那些任务。

## AsyncNode 内部是如何工作的？

`AsyncNode` 的魔法主要来自于 Python 内建的 `asyncio` 库和 `async/await` 语法。

当你调用 `await node.run_async(shared)` 时：

1.  **调用 `prep_async(shared)`**: 异步准备数据。如果其中有 `await`，它会暂停并让出控制权，完成后恢复。
2.  **调用 `_exec(prep_res)` (异步版本)**: 内部的异步执行封装。
    *   它包含一个异步循环，最多尝试 `max_retries + 1` 次。
    *   在循环中，它 `await` **`exec_async(prep_res)`**。如果 `exec_async` 内部有 `await`（比如 `await` 一个网络请求），节点会在这里暂停，**将控制权交还给 `asyncio` 的事件循环 (Event Loop)**。事件循环可以去运行其他已经准备好的异步任务。
    *   当被 `await` 的操作（如网络请求）完成后，事件循环会唤醒这个节点的 `exec_async`，让它从暂停的地方继续执行。
    *   如果 `exec_async` 成功，循环结束，返回结果。
    *   如果 `exec_async` 抛出异常，并且还有重试次数，它会 `await asyncio.sleep(wait)`（如果设置了 `wait > 0`，这又是一次让出控制权的机会），然后进行下一次尝试。
    *   如果所有尝试都失败了，它会 `await` **`exec_fallback_async(prep_res, exception)`** 并返回其结果。
3.  **调用 `post_async(shared, prep_res, exec_res)`**: 异步处理结果并决定下一步。同样，如果内部有 `await`，也会暂停和恢复。

下面是一个简化的时序图，展示了 `AsyncNode` 在遇到 `await` 时的行为：

```mermaid
sequenceDiagram
    participant User as 用户
    participant AsyncNode as 异步节点
    participant EventLoop as asyncio事件循环
    participant Network as 模拟网络I/O

    User->>AsyncNode: await run_async(shared)
    AsyncNode->>AsyncNode: await prep_async(shared)
    AsyncNode-->>AsyncNode: prep_res
    AsyncNode->>AsyncNode: await _exec(prep_res)
    Note over AsyncNode: 进入 exec_async
    AsyncNode->>Network: await 发起网络请求()
    Note over AsyncNode: 节点暂停, 让出控制权给事件循环
    EventLoop->>EventLoop: (处理其他任务...)
    Network-->>EventLoop: 网络响应到达
    EventLoop->>AsyncNode: 恢复 exec_async 执行
    AsyncNode-->>AsyncNode: exec_res (来自网络)
    AsyncNode->>AsyncNode: await post_async(shared, prep_res, exec_res)
    AsyncNode-->>AsyncNode: action
    AsyncNode-->>User: 返回 action
```

**代码实现速览**:

`AsyncNode` 的核心逻辑可以在 `pocketflow/__init__.py` 文件中找到。其简化结构如下：

```python
# (来自 pocketflow/__init__.py 的 AsyncNode 简化版本)
import asyncio

class AsyncNode(Node): # 继承自 Node，获得基础结构和同步回退
    # --- 异步接口方法 (需要子类实现) ---
    async def prep_async(self, shared):
        # 默认调用同步版本 (如果子类没实现异步版本)
        # 但推荐总是实现异步版本
        return self.prep(shared)

    async def exec_async(self, prep_res):
        # 必须由子类实现
        pass

    async def exec_fallback_async(self, prep_res, exc):
        # 默认调用同步版本 (如果子类没实现异步版本)
        # 但推荐总是实现异步版本
        return self.exec_fallback(prep_res, exc)

    async def post_async(self, shared, prep_res, exec_res):
        # 默认调用同步版本 (如果子类没实现异步版本)
        # 但推荐总是实现异步版本
        return self.post(shared, prep_res, exec_res)

    # --- 内部异步执行逻辑 ---
    async def _exec(self, prep_res):
        # (注意：父类 Node 的 _exec 是同步的, 这里重写了异步版本)
        for self.cur_retry in range(self.max_retries):
            try:
                # 尝试 await 异步执行核心逻辑
                return await self.exec_async(prep_res)
            except Exception as e:
                # 如果失败且是最后一次尝试
                if self.cur_retry == self.max_retries - 1:
                    # await 调用异步 fallback 逻辑
                    return await self.exec_fallback_async(prep_res, e)
                # 如果还有重试机会，并且设置了等待时间
                if self.wait > 0:
                    await asyncio.sleep(self.wait) # 异步等待

    # --- 运行入口 ---
    async def run_async(self, shared):
        # 公开的异步运行方法
        # (省略了一些检查, 比如 successors 警告)
        return await self._run_async(shared)

    async def _run_async(self, shared):
        # 内部异步运行逻辑
        p = await self.prep_async(shared)
        # 如果 prep_async 返回 None (或等效的跳过信号)，则跳过后续 (在实际代码中处理)
        e = await self._exec(p) # 调用重写的异步 _exec
        return await self.post_async(shared, p, e)

    # --- 阻止调用同步 run ---
    def _run(self, shared):
        raise RuntimeError("对于 AsyncNode, 请使用 run_async() 而不是 run()")

```

这个结构展示了 `AsyncNode` 如何定义一套与 `Node` 平行的 `*_async` 方法，并通过重写 `_exec` 和 `_run_async` 方法来整合 `async/await` 和异步重试/回退逻辑。

## 总结

在本章中，我们深入了解了 PocketFlow 中的 **异步节点 (AsyncNode)**：

*   `AsyncNode` 是为执行 **I/O 密集型** 操作（如网络请求、文件读写）而设计的，避免阻塞主程序。
*   它使用 `async def` 定义核心方法：`prep_async`, `exec_async`, `post_async`, `exec_fallback_async`。
*   通过 `await` 关键字调用 `AsyncNode` 的 `run_async` 方法来执行。
*   在 `await` 一个耗时操作时，`AsyncNode` 会让出控制权给 `asyncio` 事件循环，允许程序处理其他任务，实现**非阻塞**执行。
*   `AsyncNode` 同样支持内置的重试 (`max_retries`, `wait`) 和错误处理 (`exec_fallback_async`) 机制，不过是异步版本的。

我们通过一个模拟获取天气信息的例子，看到了如何定义和运行一个 `AsyncNode`。`AsyncNode` 为构建需要与外部世界高效交互（如调用 LLM API、访问数据库）的 PocketFlow 应用提供了关键能力。

单个 `AsyncNode` 可以在等待时允许其他（非 PocketFlow）异步任务运行。但是，如果我们想构建一个包含多个 `AsyncNode` 的工作流，并且希望这些异步节点能够高效地协同工作（比如，一个节点等待 API 时，流程可以先去执行另一个准备好的异步节点），我们需要什么呢？

没错，我们需要一个能够理解和编排 `AsyncNode` 的流程！

**下一章**: [第 4 章：异步流程 (AsyncFlow)](04_异步流程__asyncflow__.md) - 学习如何使用 `AsyncFlow` 来连接和管理 `AsyncNode`，构建真正非阻塞、高并发的工作流程。

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)