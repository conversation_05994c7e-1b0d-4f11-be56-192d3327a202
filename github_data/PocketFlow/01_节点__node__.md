# Chapter 1: 节点 (Node)


欢迎来到 PocketFlow 的世界！想象一下，你需要让计算机完成一项复杂的任务，比如分析一篇长文并生成摘要。这项任务可能包含好几个步骤：获取文章、调用大型语言模型（LLM）进行摘要、处理可能出现的网络错误、保存结果等等。如果把所有逻辑都写在一起，代码很快就会变得混乱不堪。这正是 PocketFlow **节点 (Node)** 概念要解决的问题。

**节点** 是 PocketFlow 中最基础、最重要的构建块。它帮助我们将复杂的任务拆分成一个个独立的、可管理的小单元。

在本章中，我们将学习：

*   什么是节点 (Node)？
*   节点如何将任务拆分为 `prep`、`exec` 和 `post` 三个阶段？
*   如何创建一个简单的节点来完成特定任务？
*   节点如何处理数据和错误？

## 什么是节点？

在 PocketFlow 中，**节点 (Node)** 代表了一个工作流中的 **单一、可重试的任务步骤**。你可以把它想象成一条工厂流水线上的一个 **工作站**：

1.  **接收输入**: 工作站从传送带上接收上游传递过来的零件或半成品（在 PocketFlow 中，这通常是共享数据 `shared`）。
2.  **执行任务**: 工作站执行自己特定的加工任务（比如，节点调用 LLM 进行文本处理）。
3.  **传递输出**: 工作站将加工好的产品放回传送带，传递给下一个工位，或者放入一个公共区域（节点将结果写回 `shared` 或决定流程的下一步）。

每个节点都封装了一个具体的、原子性的操作。这种设计使得构建复杂流程更加清晰和模块化。

## 节点的核心：`prep`, `exec`, `post`

每个节点内部通常定义了三个核心方法，对应任务执行的三个阶段：

1.  **`prep(self, shared)` (准备阶段)**:
    *   **作用**: 在执行核心任务之前，准备所需的数据。
    *   **输入**: `shared`，一个字典，包含了工作流中所有节点共享的数据。
    *   **输出**: 返回传递给 `exec` 方法的数据。如果此节点不应执行（例如，某些条件不满足），可以返回 `None` 来跳过 `exec` 和 `post`。
    *   **类比**: 工人从传送带上拿起零件，并检查零件是否合格，获取需要的工具。

2.  **`exec(self, prep_res)` (执行阶段)**:
    *   **作用**: 执行节点的核心业务逻辑。这通常是最重要的部分，比如调用 API、进行计算、操作数据等。
    *   **输入**: `prep` 方法的返回值 (`prep_res`)。
    *   **输出**: 返回核心逻辑的执行结果。
    *   **类比**: 工人使用工具对零件进行加工。

3.  **`post(self, shared, prep_res, exec_res)` (后处理阶段)**:
    *   **作用**: 处理 `exec` 的结果，并决定工作流的下一步。
    *   **输入**:
        *   `shared`: 共享数据字典。
        *   `prep_res`: `prep` 方法的返回值。
        *   `exec_res`: `exec` 方法的返回值。
    *   **输出**: 返回一个字符串，表示下一步要执行的操作（"action"）。这个字符串将用于 [流程 (Flow)](02_流程__flow__.md) 中决定跳转到哪个后续节点。如果没有后续步骤或流程结束，可以返回 `None` 或任何未定义的 action。你也可以在这个阶段修改 `shared` 字典，将结果存回共享区域。
    *   **类比**: 工人将加工好的产品放回传送带（更新 `shared`），并根据加工结果（例如，产品合格/不合格）决定是送到下一个常规工位还是送到质检工位（返回不同的 "action"）。

## 一个简单的例子：文本摘要节点

让我们通过一个例子来理解节点。假设我们要创建一个节点，它的任务是接收一段文本，调用（模拟的）LLM 服务生成摘要，然后打印摘要。

我们可以在 `shared` 字典中传递需要摘要的文本，并在 `shared` 中获取摘要结果。

```python
# 导入 PocketFlow Node
from pocketflow import Node
import time

# 假设我们有一个调用 LLM 的函数 (这里用一个简单的模拟函数代替)
def call_llm_for_summary(text):
    print(f"  正在调用 LLM 为 '{text[:20]}...' 生成摘要...")
    time.sleep(0.5) # 模拟网络延迟
    # 实际应用中这里会调用真正的 LLM API
    # 简化处理：返回文本的前 10 个字符作为摘要
    if not text:
        raise ValueError("输入文本不能为空") # 模拟 API 可能抛出的错误
    return f"摘要: {text[:10]}..."

# --- 定义我们的摘要节点 ---
class SummarizeNode(Node):
    def __init__(self, max_retries=2, wait=0.5):
        # 设置最多重试 2 次 (总共执行 3 次)，每次重试前等待 0.5 秒
        super().__init__(max_retries=max_retries, wait=wait)

    def prep(self, shared):
        # 1. 准备阶段：从共享数据中获取待摘要的文本
        text_to_summarize = shared.get("text_input", None)
        if not text_to_summarize:
            print("警告：在 shared 中未找到 'text_input'，跳过摘要。")
            return None # 返回 None 表示不执行 exec 和 post
        print(f"准备阶段：获取到文本 '{text_to_summarize[:20]}...'")
        return text_to_summarize

    def exec(self, text_to_summarize):
        # 2. 执行阶段：调用 LLM (模拟函数) 生成摘要
        print("执行阶段：开始生成摘要...")
        summary = call_llm_for_summary(text_to_summarize)
        return summary

    def exec_fallback(self, text_to_summarize, exc):
        # 如果 exec 重试多次后仍然失败，则执行这个后备逻辑
        print(f"!!! 执行阶段失败 (尝试 {self.max_retries + 1} 次): {exc}")
        print("!!! 提供一个备用摘要。")
        return "无法生成摘要，请稍后再试。"

    def post(self, shared, prep_res, exec_res):
        # 3. 后处理阶段：将摘要结果存回共享数据，并决定下一步
        print(f"后处理阶段：获取到摘要 '{exec_res}'")
        shared["summary_output"] = exec_res # 将结果保存回 shared
        print("后处理阶段：摘要已保存到 shared['summary_output']")
        # 返回 "done" 表示这个节点完成了它的任务
        # 在一个 Flow 中，这可以用来决定下一个节点
        return "done"

# --- 使用这个节点 ---
# 创建节点实例
summarizer = SummarizeNode()

# 准备共享数据 (模拟一个更大的流程中的数据传递)
shared_data = {"text_input": "PocketFlow 是一个用于构建基于 LLM 的应用的轻量级 Python 框架。"}

# 运行节点
print("开始运行 SummarizeNode...")
action = summarizer.run(shared_data) # 直接运行单个节点 (通常在 Flow 中运行)
print(f"节点运行完成，返回的 action 是: '{action}'")

# 查看共享数据是否被更新
print("共享数据中的摘要:", shared_data.get("summary_output"))
```

**代码解释**:

1.  **`SummarizeNode(Node)`**: 我们定义了一个名为 `SummarizeNode` 的类，它继承自 `pocketflow.Node`。这表示它是一个标准的 PocketFlow 节点。
2.  **`__init__`**: 我们调用了父类的构造函数 `super().__init__()`，并设置了 `max_retries=2`（意味着如果 `exec` 失败，它会重试2次，总共尝试3次）和 `wait=0.5`（每次重试前等待0.5秒）。
3.  **`prep`**: 从 `shared_data` 字典中获取键为 `"text_input"` 的值。如果找不到，就打印警告并返回 `None`。否则，返回找到的文本。
4.  **`exec`**: 调用（模拟的）`call_llm_for_summary` 函数来执行核心任务——生成摘要。
5.  **`exec_fallback`**: 这是节点的 **容错机制**。如果 `exec` 方法在尝试了 `max_retries` 次后仍然抛出异常（比如我们的模拟函数因为空文本抛出 `ValueError`），`exec_fallback` 会被调用。它接收 `prep` 的结果和捕获到的异常 `exc` 作为参数。这里我们简单地打印错误信息并返回一个备用的默认摘要。
6.  **`post`**: 将 `exec`（或 `exec_fallback`）返回的摘要结果 (`exec_res`) 存储回 `shared_data` 字典，键为 `"summary_output"`。然后返回字符串 `"done"`，表示这个节点的任务完成了。在一个完整的 [流程 (Flow)](02_流程__flow__.md) 中，这个返回值可以用来决定接下来运行哪个节点。
7.  **运行节点**: 我们创建了 `SummarizeNode` 的一个实例 `summarizer`，准备了一个包含输入文本的 `shared_data` 字典，然后调用 `summarizer.run(shared_data)` 来执行这个节点。最后，我们检查 `shared_data` 是否包含了 `"summary_output"`。

**尝试失败情况**:

如果我们给一个空的文本，`exec` 会失败。让我们看看会发生什么：

```python
# 尝试运行一个会失败并触发 fallback 的例子
shared_data_fail = {"text_input": ""}
print("\n开始运行 SummarizeNode (预期失败)...")
action_fail = summarizer.run(shared_data_fail)
print(f"节点运行完成，返回的 action 是: '{action_fail}'")
print("共享数据中的摘要:", shared_data_fail.get("summary_output"))
```

你会看到输出中包含重试信息，最终调用 `exec_fallback` 并将备用摘要存入 `shared_data`。这就是节点内置的健壮性！

## 节点内部是如何工作的？

当你调用 `node.run(shared)` 时，PocketFlow 在背后按顺序执行了以下步骤：

1.  **调用 `prep(shared)`**: 获取执行所需的数据。
2.  **调用 `_exec(prep_res)`**: 这是内部的执行封装。
    *   它包含一个循环，最多尝试 `max_retries + 1` 次。
    *   在循环中，它调用 **`exec(prep_res)`**。
    *   如果 `exec` 成功，循环结束，返回结果。
    *   如果 `exec` 抛出异常，并且还有重试次数，它会等待 `wait` 秒（如果设置了的话），然后进行下一次尝试。
    *   如果所有尝试都失败了，它会调用 **`exec_fallback(prep_res, exception)`** 并返回其结果。
3.  **调用 `post(shared, prep_res, exec_res)`**: 处理结果并决定下一步。

下面是一个简化的时序图，展示了这个过程：

```mermaid
sequenceDiagram
    participant User
    participant Node as MyNode
    participant Core as Node._exec()
    participant Exec as MyNode.exec()
    participant Fallback as MyNode.exec_fallback()

    User->>Node: run(shared)
    Node->>Node: prep(shared)
    Node-->>Node: prep_res
    Node->>Core: _exec(prep_res)
    loop Retries (max_retries + 1 times)
        Core->>Exec: exec(prep_res)
        alt Success
            Exec-->>Core: exec_res
            Core-->>Node: exec_res
            break
        else Failure (Exception)
            Exec-->>Core: Exception!
            opt Has Retries Left & wait > 0
               Core->>Core: sleep(wait)
            end
        end
    end
    opt All Retries Failed
        Core->>Fallback: exec_fallback(prep_res, exception)
        Fallback-->>Core: fallback_res
        Core-->>Node: fallback_res
    end
    Node->>Node: post(shared, prep_res, exec_res/fallback_res)
    Node-->>Node: action
    Node-->>User: action
```

**代码实现速览**:

节点的核心逻辑可以在 `pocketflow/__init__.py` 文件中的 `Node` 类找到。其简化结构如下：

```python
# (来自 pocketflow/__init__.py 的简化版本)
import time

class BaseNode: # 基础节点类，定义通用接口
    def __init__(self): ... # 初始化
    def prep(self, shared): pass # 准备阶段
    def exec(self, prep_res): pass # 执行阶段 (由子类实现)
    def post(self, shared, prep_res, exec_res): pass # 后处理阶段
    def _exec(self, prep_res): return self.exec(prep_res) # 内部执行调用
    def _run(self, shared): # 内部运行逻辑
        p = self.prep(shared)
        # 如果 prep 返回 None，后面不执行 (在实际代码中处理)
        e = self._exec(p)
        return self.post(shared, p, e)
    def run(self, shared): # 公开的运行方法
        # ... (省略了一些检查)
        return self._run(shared)

class Node(BaseNode): # 标准节点，增加了重试和 fallback
    def __init__(self, max_retries=1, wait=0):
        super().__init__()
        self.max_retries = max_retries # 最大重试次数
        self.wait = wait             # 重试间隔
        self.cur_retry = 0           # 当前重试次数 (内部使用)

    def exec_fallback(self, prep_res, exc):
        # 备用执行逻辑 (默认是重新抛出异常)
        raise exc

    def _exec(self, prep_res):
        # 内部执行逻辑，包含重试和 fallback
        for self.cur_retry in range(self.max_retries): # 注意循环次数
            try:
                # 尝试执行核心逻辑
                return self.exec(prep_res)
            except Exception as e:
                # 如果失败且是最后一次尝试
                if self.cur_retry == self.max_retries - 1:
                    # 调用 fallback 逻辑
                    return self.exec_fallback(prep_res, e)
                # 如果还有重试机会，并且设置了等待时间
                if self.wait > 0:
                    time.sleep(self.wait) # 等待
        # (实际代码中 range(self.max_retries) 可能稍有不同，但逻辑类似：尝试 N 次)
        # 如果循环结束还没成功（理论上不应发生，因为 fallback 会处理），
        # 最后一次尝试失败后会调用 fallback。简化展示。
```

这个结构清晰地展示了 `prep`, `exec`, `post` 的分离，以及 `Node` 类如何通过 `_exec` 方法添加重试和 `exec_fallback` 机制来增强任务的健壮性。

## 总结

在本章中，我们学习了 PocketFlow 的基础构建块——**节点 (Node)**。

*   节点是将复杂任务分解为 **独立、可重试** 步骤的方法。
*   每个节点通过 **`prep` (准备)**, **`exec` (执行)**, **`post` (后处理)** 三个阶段来完成其任务。
*   节点使用 **`shared`** 字典来共享数据。
*   `Node` 类内置了 **重试 (`max_retries`, `wait`)** 和 **错误处理 (`exec_fallback`)** 机制，让任务更健壮。

我们通过一个简单的文本摘要例子，看到了如何定义和运行一个节点。节点为构建可靠、模块化的 LLM 应用或其他复杂工作流提供了坚实的基础。

但是，单个节点通常不足以完成整个任务。我们如何将多个节点连接起来，形成一个完整的工作流呢？这正是下一章要介绍的内容。

**下一章**: [第 2 章：流程 (Flow)](02_流程__flow__.md) - 学习如何编排和连接多个节点，构建强大的工作流程。

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)