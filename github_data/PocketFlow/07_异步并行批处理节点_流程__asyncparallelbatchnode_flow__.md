# Chapter 7: 异步并行批处理节点/流程 (AsyncParallelBatchNode/Flow)


欢迎来到 PocketFlow 教程的最后一章！在前面的章节中，我们学习了 [批处理节点 (BatchNode)](05_批处理节点__batchnode__.md) 和 [批处理流程 (BatchFlow)](06_批处理流程__batchflow__.md)，它们能够方便地对一批数据或参数应用相同的操作或流程。但是，它们有一个特点：默认情况下，无论是 `BatchNode` 处理每个数据项，还是 `BatchFlow` 为每个参数运行内部流程，它们都是**按顺序一个接一个**执行的。

想象一下，你需要用一个耗时的 AI 服务（比如调用 LLM API）来处理 100 份文档。如果使用普通的 `BatchNode` 或 `BatchFlow`（即使是它们的异步版本 `AsyncBatchNode` 或 `AsyncBatchFlow`，虽然单次调用是异步的，但批次处理仍是顺序等待），你必须等待第一份文档处理完成，才能开始处理第二份，以此类推。如果每份文档处理需要 5 秒钟，那么总共就需要 500 秒！这就像只有一个复印机，即使复印本身很快，但复印一堆文件还是需要排队。

有没有办法让这 100 个处理任务**同时启动**，就像拥有 100 台复印机同时工作一样？这样，理论上总时间就会接近于处理**单个最慢任务**所需的时间，大大提高效率！

这就是 **异步并行批处理节点/流程 (AsyncParallelBatchNode/Flow)** 要解决的问题。它们是批处理能力的终极形态，专门为最大限度地加速那些可以**并发**执行的批处理任务而设计。

在本章中，我们将学习：

*   什么是异步并行批处理节点和流程？它们与之前的批处理版本有何不同？
*   它们如何利用 `asyncio` 实现真正的并行执行？
*   如何使用 `AsyncParallelBatchNode` 同时调用多次模拟 LLM API？
*   如何使用 `AsyncParallelBatchFlow` 同时对多张图片运行处理流程？
*   并行处理带来的显著性能提升。

## 什么是异步并行批处理？

简单来说，异步并行批处理就是批处理节点或流程的**异步**和**并行**版本。

*   **异步 (Async)**：意味着节点/流程的核心操作（如 `exec_async` 或内部流程运行）本身是基于 `async/await` 的，可以进行非阻塞的 I/O 等待，这与我们之前学习的 [异步节点 (AsyncNode)](03_异步节点__asyncnode__.md) 和 [异步流程 (AsyncFlow)](04_异步流程__asyncflow__.md) 类似。
*   **并行 (Parallel)**：这是与 `AsyncBatchNode/Flow` 的**关键区别**。`AsyncParallelBatchNode/Flow` 不再是一个接一个地 `await` 批次中的任务，而是利用 Python 的 `asyncio.gather` 功能，将批次中的**所有**异步任务**同时启动**。

**核心机制：`asyncio.gather`**

`asyncio.gather` 是 Python `asyncio` 库中的一个函数，它可以接收多个异步任务（在 PocketFlow 中，就是对批次中每个项目执行 `exec_async` 或运行一次内部 `_orch_async` 的任务），然后**并发地运行它们**。它会等待所有这些并发任务都完成后，再统一返回结果。

这就像你同时按下多台复印机的“开始”按钮，然后等待所有复印机都完成工作。

## 异步并行批处理节点 (AsyncParallelBatchNode)

`AsyncParallelBatchNode` 继承了 `AsyncNode` 和 `BatchNode` 的特性，但改变了批次执行的方式。

*   **`prep_async(self, shared)`** (异步): 和 `AsyncBatchNode` 一样，准备并返回一个包含所有待处理项的**可迭代对象**。因为是异步节点，所以方法是 `async def`。
*   **`exec_async(self, item)`** (异步): 和 `AsyncBatchNode` 一样，定义处理**单个项目**的异步逻辑。
*   **执行方式**: 与 `AsyncBatchNode` 不同，当 `AsyncParallelBatchNode` 运行时，它会为 `prep_async` 返回的**每一个 `item`** 都创建一个 `exec_async(item)` 的异步任务，然后使用 `asyncio.gather` **并发地运行所有这些任务**。
*   **`post_async(self, shared, prep_res, exec_res_list)`** (异步): 当 `asyncio.gather` 返回所有结果后（结果列表 `exec_res_list` 的顺序与 `prep_async` 返回的顺序一致），这个异步方法被调用，用于处理所有并发执行的结果。

**适用场景**：当你需要对一批数据独立地执行同一个**异步 I/O 密集型操作**（如调用外部 API、读写文件）并且希望它们并发进行以节省时间时，`AsyncParallelBatchNode` 是最佳选择。

### 示例：并行调用模拟 LLM API

让我们回到批量总结文档的例子。我们将使用 `AsyncParallelBatchNode` 来并发地调用模拟的 LLM API。为了对比，我们先定义一个顺序执行的 `AsyncBatchNode`。

```python
import asyncio
import time
from pocketflow import AsyncBatchNode, AsyncParallelBatchNode, AsyncFlow

# 模拟耗时 1 秒的异步 LLM 调用
async def dummy_llm_summarize(text_id):
    """模拟一个需要 1 秒的异步 LLM 调用"""
    print(f"  > 开始处理 {text_id}...")
    await asyncio.sleep(1) # 模拟网络 I/O 等待
    print(f"  < 完成处理 {text_id}!")
    return f"摘要({text_id})"

# 1. 顺序异步批处理节点 (用于对比)
class SequentialSummarizer(AsyncBatchNode):
    async def prep_async(self, shared):
        # 准备要处理的文档 ID 列表
        return shared.get("doc_ids", [])

    async def exec_async(self, doc_id):
        # 处理单个文档 ID
        summary = await dummy_llm_summarize(doc_id)
        return (doc_id, summary)

    async def post_async(self, shared, prep_res, exec_res_list):
        # 收集结果
        shared["sequential_results"] = dict(exec_res_list)
        return "done_sequential"

# 2. 并行异步批处理节点
class ParallelSummarizer(AsyncParallelBatchNode):
    async def prep_async(self, shared):
        # 准备工作与顺序版本相同
        return shared.get("doc_ids", [])

    async def exec_async(self, doc_id):
        # 处理逻辑也与顺序版本相同
        summary = await dummy_llm_summarize(doc_id)
        return (doc_id, summary)

    async def post_async(self, shared, prep_res, exec_res_list):
        # 收集结果
        shared["parallel_results"] = dict(exec_res_list)
        return "done_parallel"

# --- 运行对比 ---
async def main_node_comparison():
    shared_data = {"doc_ids": ["文档A", "文档B", "文档C"]}

    # 创建节点和流程
    seq_node = SequentialSummarizer()
    par_node = ParallelSummarizer()
    # 我们可以将它们放在同一个流程中顺序执行，或者分开执行
    # 这里我们创建两个独立的简单流程来分别测试
    seq_flow = AsyncFlow(start=seq_node)
    par_flow = AsyncFlow(start=par_node)

    print("\n--- 开始顺序执行 (AsyncBatchNode) ---")
    start_seq = time.time()
    await seq_flow.run_async(shared_data.copy()) # 使用副本避免共享数据干扰
    end_seq = time.time()
    print(f"顺序执行耗时: {end_seq - start_seq:.2f} 秒")
    # print(f"顺序结果: {shared_data['sequential_results']}") # 结果应为 {'文档A': '摘要(文档A)', ...}

    print("\n--- 开始并行执行 (AsyncParallelBatchNode) ---")
    start_par = time.time()
    # 需要一个新的共享数据字典副本
    shared_data_par = {"doc_ids": ["文档A", "文档B", "文档C"]}
    await par_flow.run_async(shared_data_par)
    end_par = time.time()
    print(f"并行执行耗时: {end_par - start_par:.2f} 秒")
    # print(f"并行结果: {shared_data_par['parallel_results']}") # 结果与顺序版相同，但更快得到

if __name__ == "__main__":
    asyncio.run(main_node_comparison())

```

**代码解释**：

*   `dummy_llm_summarize` 函数模拟一个需要等待 1 秒的异步操作。
*   `SequentialSummarizer` 使用 `AsyncBatchNode`。它会按顺序 `await dummy_llm_summarize("文档A")`，然后 `await dummy_llm_summarize("文档B")`，以此类推。
*   `ParallelSummarizer` 使用 `AsyncParallelBatchNode`。它的 `prep_async` 和 `exec_async` 与顺序版本完全相同！PocketFlow 内部会自动处理并行化。
*   运行 `main_node_comparison` 函数，你会看到：
    *   顺序执行大约需要 3 秒（因为有 3 个任务，每个 1 秒，顺序执行）。你会看到 "开始处理..." 和 "完成处理..." 的日志是成对按顺序出现的 (A开始->A结束->B开始->B结束...)。
    *   并行执行大约只需要 1 秒（接近于单个最慢任务的时间）。你会看到所有 "开始处理..." 的日志几乎同时出现，然后所有 "完成处理..." 的日志也几乎同时出现。

这个例子清晰地展示了 `AsyncParallelBatchNode` 在处理可以并发的 I/O 密集型任务时的巨大优势。

## 异步并行批处理流程 (AsyncParallelBatchFlow)

`AsyncParallelBatchFlow` 则是将 `asyncio.gather` 的能力应用到了 [批处理流程 (BatchFlow)](06_批处理流程__batchflow__.md) 上。

*   **`prep_async(self, shared)`** (异步): 和 `AsyncBatchFlow` 类似，准备并返回一个**参数字典的列表**，每个字典用于运行一次内部流程。
*   **内部流程 (Internal Flow)**: 你需要定义一个基础的 [异步流程 (AsyncFlow)](04_异步流程__asyncflow__.md)（或者单个 [异步节点 (AsyncNode)](03_异步节点__asyncnode__.md)），这个流程会被重复执行。
*   **执行方式**: 与 `AsyncBatchFlow` 不同，`AsyncParallelBatchFlow` 会为 `prep_async` 返回的**每一个参数字典**启动一次内部流程的运行（即调用内部流程的 `_orch_async`），然后使用 `asyncio.gather` **并发地运行所有这些内部流程实例**。
*   **`post_async(self, shared, prep_res, exec_res_list)`** (异步): 当所有并发运行的内部流程都结束后，这个异步方法被调用，用于最终的聚合处理。`exec_res_list` 通常包含 `asyncio.gather` 返回的每个内部流程的最终动作列表（或 `None`，取决于 `gather` 的配置）。内部流程的结果通常还是通过修改 `shared` 来聚合。

**适用场景**：当你需要对一批输入参数，各自独立地运行一个包含多个异步步骤的**完整工作流**，并且希望这些工作流实例能够并发执行时，`AsyncParallelBatchFlow` 是理想的选择。

### 示例：并行处理多张图片

让我们用 `AsyncParallelBatchFlow` 来并发地处理多张图片，每张图片应用多种滤镜。

#### 1. 定义内部异步流程 (处理单张图片和单个滤镜)

这个内部流程需要是 `AsyncFlow`，包含异步节点模拟加载、应用滤镜（耗时操作）、保存。

```python
import asyncio
import time
import os
from pocketflow import AsyncNode, AsyncFlow, AsyncParallelBatchFlow

# --- 模拟异步操作的节点 ---
class AsyncLoadImage(AsyncNode):
    async def prep_async(self, shared):
        # 从参数获取图片路径 (由 ParallelBatchFlow 提供)
        img_path = self.params.get("image_path", "")
        return img_path

    async def exec_async(self, img_path):
        print(f"  > 开始加载 {img_path}...")
        await asyncio.sleep(0.2) # 模拟 I/O
        print(f"  < 完成加载 {img_path}!")
        return f"数据<{img_path}>"

    async def post_async(self, shared, prep_res, exec_res):
        shared["_current_image_data"] = exec_res # 临时存放，避免并发冲突
        return "filter"

class AsyncApplyFilter(AsyncNode):
    async def prep_async(self, shared):
        img_data = shared.get("_current_image_data")
        filter_type = self.params.get("filter", "无") # 从参数获取滤镜类型
        return img_data, filter_type

    async def exec_async(self, prep_res):
        img_data, filter_type = prep_res
        print(f"    > 开始为 {img_data} 应用 {filter_type} 滤镜...")
        await asyncio.sleep(0.3) # 模拟处理
        print(f"    < 完成为 {img_data} 应用 {filter_type} 滤镜!")
        return f"{img_data}-{filter_type}处理"

    async def post_async(self, shared, prep_res, exec_res):
        shared["_filtered_image_data"] = exec_res
        return "save"

class AsyncSaveImage(AsyncNode):
    async def prep_async(self, shared):
        filtered_data = shared.get("_filtered_image_data")
        # 从参数构造输出文件名
        img_path = self.params.get("image_path", "unknown.jpg")
        filter_type = self.params.get("filter", "none")
        base, ext = os.path.splitext(os.path.basename(img_path))
        output_path = f"output/{base}_{filter_type}{ext}"
        return output_path, filtered_data

    async def exec_async(self, prep_res):
        output_path, data = prep_res
        print(f"      > 开始保存 {output_path}...")
        # 实际应用中会创建 output 目录
        await asyncio.sleep(0.1) # 模拟 I/O
        print(f"      < 完成保存 {output_path}!")
        return output_path # 返回保存的文件名

    async def post_async(self, shared, prep_res, exec_res):
        # 聚合结果到全局 shared
        job_id = f"{self.params['image_path']}_{self.params['filter']}"
        if "img_results" not in shared: shared["img_results"] = {}
        shared["img_results"][job_id] = exec_res # 记录这次任务的输出文件
        return None # 内部流程结束

# --- 创建处理单个图片-滤镜组合的异步流程 ---
def create_single_image_async_flow():
    load = AsyncLoadImage()
    fltr = AsyncApplyFilter()
    save = AsyncSaveImage()
    load - "filter" >> fltr
    fltr - "save" >> save
    # 因为这是内部流程，不需要指定 start=?, BatchFlow 会处理
    # 我们只需要返回起始节点即可
    return load 
```

**代码解释**:

*   我们定义了三个 `AsyncNode`，模拟图片处理的异步步骤。
*   `AsyncLoadImage` 和 `AsyncApplyFilter` 从 `self.params` 获取各自需要的 `image_path` 和 `filter` 参数。
*   `AsyncSaveImage` 将处理结果（输出文件名）记录在共享数据 `shared["img_results"]` 中，以便外部 `AsyncParallelBatchFlow` 的 `post` 方法可以访问。
*   `create_single_image_async_flow` 函数创建并连接了这些异步节点，返回了起始节点 `load`。

#### 2. 定义并行批处理流程

现在，我们定义 `ParallelImageProcessor`，它使用 `AsyncParallelBatchFlow` 并将上面创建的 `single_image_flow_start_node` 作为其内部流程的起点。

```python
# --- 定义并行批处理流程 ---
class ParallelImageProcessor(AsyncParallelBatchFlow):
    async def prep_async(self, shared):
        # 获取图片列表和要应用的滤镜列表
        images = shared.get("image_paths", ["img1.jpg", "img2.jpg"])
        filters = shared.get("filter_types", ["模糊", "锐化"])

        # 为每个 "图片-滤镜" 组合生成一个参数字典
        batch_params = []
        for img in images:
            for flt in filters:
                batch_params.append({"image_path": img, "filter": flt})
        
        print(f"准备处理 {len(images)} 张图片，每张应用 {len(filters)} 种滤镜。")
        print(f"总共 {len(batch_params)} 个并行任务。")
        return batch_params # 返回参数列表

    async def post_async(self, shared, prep_res, exec_res_list):
        # 所有内部流程都已并发执行完毕
        print("\n--- 所有图片处理任务已并发完成 ---")
        results = shared.get("img_results", {})
        print(f"共生成 {len(results)} 个结果文件:")
        # for job, output in results.items():
        #     print(f"  - 任务 {job} -> {output}")
        return "batch_done"

# --- 运行 ---
async def main_flow_comparison():
    # 1. 获取内部流程的起始节点
    inner_flow_start_node = create_single_image_async_flow()

    # 2. 创建并行批处理流程实例
    #    将内部流程的起始节点传递给 start 参数
    parallel_processor = ParallelImageProcessor(start=inner_flow_start_node)

    # 准备初始共享数据
    shared_init = {
        "image_paths": ["猫咪.jpg", "狗狗.jpg"],
        "filter_types": ["灰度", "棕褐色"]
    }

    print("\n--- 开始并行处理图片流程 (AsyncParallelBatchFlow) ---")
    start_time = time.time()
    await parallel_processor.run_async(shared_init)
    end_time = time.time()
    print(f"并行流程总耗时: {end_time - start_time:.2f} 秒")
    # print(f"最终结果摘要: {shared_init.get('img_results')}")

    # 对比：如果使用 AsyncBatchFlow (顺序异步) 会怎样？
    # 理论上耗时 = (0.2 + 0.3 + 0.1) 秒/任务 * 4 个任务 = 2.4 秒
    # 并行耗时 ≈ max(单个任务总时间) ≈ 0.6 秒 (取决于系统调度)

if __name__ == "__main__":
    # 运行节点对比 或 流程对比
    # asyncio.run(main_node_comparison())
    asyncio.run(main_flow_comparison())
```

**代码解释**:

*   `ParallelImageProcessor` 继承自 `AsyncParallelBatchFlow`。
*   它的 `prep_async` 方法为每个 `(图片, 滤镜)` 组合生成了一个参数字典。例如，对于 2 张图片和 2 种滤镜，会生成 4 个参数字典 `[{"image_path": "猫咪.jpg", "filter": "灰度"}, {"image_path": "猫咪.jpg", "filter": "棕褐色"}, ...]`。
*   创建 `ParallelImageProcessor` 实例时，我们将内部流程的起始节点 `create_single_image_async_flow()` 作为 `start` 参数传入。
*   调用 `parallel_processor.run_async(shared_init)` 后，PocketFlow 会：
    *   获取 `prep_async` 返回的 4 个参数字典。
    *   为**每一个**参数字典，启动一个内部流程的运行（从 `load` 节点开始，使用对应的参数）。
    *   使用 `asyncio.gather` **同时运行这 4 个独立的流程实例**。
    *   等待所有 4 个流程都运行完毕。
    *   调用 `post_async`。
*   你会看到各个图片、各种滤镜的加载、应用、保存日志**交错输出**，而不是严格按顺序。
*   总耗时会远小于顺序执行所有任务所需的时间。每个内部流程大约需要 0.2 + 0.3 + 0.1 = 0.6 秒。如果顺序执行 4 个任务，需要 2.4 秒。而并行执行，理想情况下只需要约 0.6 秒（取决于最长的那个流程实例）。

## 内部是如何工作的？

### AsyncParallelBatchNode

当你调用 `await node.run_async(shared)` 时：

1.  **`await prep_async(shared)`**: 获取要处理的 `items` 列表。
2.  **`await _exec(items)` (并行版本)**:
    *   PocketFlow 识别出这是 `AsyncParallelBatchNode`。
    *   它**不会**像 `AsyncBatchNode` 那样循环 `await`。
    *   相反，它会创建一个异步任务列表，其中每个任务是 `super()._exec(item)`（调用父类 `AsyncNode` 处理单个项目的异步 `_exec` 方法，这个方法本身包含重试逻辑）。
    *   然后，它调用 `await asyncio.gather(*任务列表)`。
    *   `asyncio.gather` 会**并发启动所有**单个项目的处理任务。
    *   当所有任务都完成后，`gather` 返回一个包含所有结果的列表（顺序与输入 `items` 一致）。
3.  **`await post_async(shared, prep_res, exec_res_list)`**: 使用 `gather` 返回的结果列表进行后处理。

**简化时序图**：

```mermaid
sequenceDiagram
    participant 用户
    participant ParNode as AsyncParallelBatchNode
    participant Gather as asyncio.gather
    participant ExecItem1 as 父类._exec(项目1)
    participant ExecItem2 as 父类._exec(项目2)
    participant Post as ParNode.post_async()

    用户->>ParNode: await run_async(shared)
    ParNode->>ParNode: await prep_async(shared)
    ParNode-->>ParNode: 返回 items [项目1, 项目2]
    ParNode->>Gather: await gather(父类._exec(项目1), 父类._exec(项目2), ...)
    Note over Gather: 同时启动所有 _exec 任务
    Gather->>ExecItem1: await _exec(项目1)
    Gather->>ExecItem2: await _exec(项目2)
    Note right of Gather: (并发等待...)
    ExecItem1-->>Gather: 返回 结果1
    ExecItem2-->>Gather: 返回 结果2
    Gather-->>ParNode: 返回结果列表 [结果1, 结果2] (exec_res_list)
    ParNode->>Post: await post_async(shared, items, [结果1, 结果2])
    Post-->>ParNode: 返回 action
    ParNode-->>用户: 返回 action
```

**代码速览 (`pocketflow/__init__.py`)**：

```python
class AsyncParallelBatchNode(AsyncNode, BatchNode): # 继承 AsyncNode 和 BatchNode
    async def _exec(self, items):
        # 覆盖 _exec 方法
        # 不是循环 await，而是直接调用 asyncio.gather
        # super(AsyncParallelBatchNode, self) 指向父类 AsyncNode
        # 对 items 中的每个 i，创建一个 super()._exec(i) 的异步任务
        tasks = (super(AsyncParallelBatchNode, self)._exec(i) for i in items)
        # 使用 gather 并发执行所有任务并等待结果
        return await asyncio.gather(*tasks)
```

### AsyncParallelBatchFlow

当你调用 `await flow.run_async(shared)` 时：

1.  **`await prep_async(shared)`**: 获取**参数字典**列表 (`prep_res`)。
2.  **`await _run_async(shared)` (并行版本)**:
    *   PocketFlow 识别出这是 `AsyncParallelBatchFlow`。
    *   它**不会**像 `AsyncBatchFlow` 那样循环 `await` 内部流程。
    *   它创建一个异步任务列表，其中每个任务是 `self._orch_async(shared, {**self.params, **bp})`（调用父类 `AsyncFlow` 运行单个内部流程的异步编排方法，传入特定参数 `bp`）。
    *   然后，它调用 `await asyncio.gather(*任务列表)`。
    *   `asyncio.gather` 会**并发启动所有**内部流程实例的执行。
    *   当所有内部流程都完成后，`gather` 返回结果（通常是每个流程的最后一个 action 列表，或根据 gather 设置）。
3.  **`await post_async(shared, prep_res, exec_res_list)`**: 使用 `gather` 返回的结果进行后处理。

**简化时序图**：

```mermaid
sequenceDiagram
    participant 用户
    participant ParFlow as AsyncParallelBatchFlow
    participant Gather as asyncio.gather
    participant Orch1 as 父类._orch_async(shared, 参数1)
    participant Orch2 as 父类._orch_async(shared, 参数2)
    participant Post as ParFlow.post_async()

    用户->>ParFlow: await run_async(shared)
    ParFlow->>ParFlow: await prep_async(shared)
    ParFlow-->>ParFlow: 返回 params [参数1, 参数2]
    ParFlow->>Gather: await gather(父类._orch_async(..., 参数1), 父类._orch_async(..., 参数2), ...)
    Note over Gather: 同时启动所有内部流程实例
    Gather->>Orch1: await _orch_async(..., 参数1)
    Gather->>Orch2: await _orch_async(..., 参数2)
    Note right of Gather: (并发等待...)
    Orch1-->>Gather: 返回 结果1 (例如, 'done')
    Orch2-->>Gather: 返回 结果2 (例如, 'done')
    Gather-->>ParFlow: 返回结果列表 [结果1, 结果2] (exec_res_list)
    ParFlow->>Post: await post_async(shared, params, [结果1, 结果2])
    Post-->>ParFlow: 返回 action
    ParFlow-->>用户: 返回 action
```

**代码速览 (`pocketflow/__init__.py`)**：

```python
class AsyncParallelBatchFlow(AsyncFlow, BatchFlow): # 继承 AsyncFlow 和 BatchFlow
    async def _run_async(self, shared):
        # 覆盖 _run_async 方法
        pr = await self.prep_async(shared) or [] # 获取参数列表

        # 准备要 gather 的任务列表
        # 对每个参数 bp，创建一个运行内部流程的任务 _orch_async
        tasks = (self._orch_async(shared, {**self.params, **bp}) for bp in pr)
        
        # 使用 gather 并发执行所有内部流程实例
        # 注意：这里 gather 的结果 (每个流程的 last_action) 通常不会直接使用
        # 结果主要通过内部流程修改 shared 来聚合
        await asyncio.gather(*tasks)

        # 调用 post_async 进行收尾，exec_res 传 None (因为 gather 的结果通常不直接用于 post)
        return await self.post_async(shared, pr, None) 
```

## 总结

在本章，我们探索了 PocketFlow 中实现最高并发批处理能力的 **异步并行批处理节点/流程 (AsyncParallelBatchNode/Flow)**：

*   它们是批处理节点/流程的**异步**和**并行**版本。
*   关键在于使用 `asyncio.gather` 来**并发执行**批次中的所有任务（单个 `exec_async` 或整个内部流程实例）。
*   `AsyncParallelBatchNode` 适用于对一批数据**并发**执行同一个**异步 I/O 密集型操作**。
*   `AsyncParallelBatchFlow` 适用于对一批参数**并发**运行同一个包含多个步骤的**异步工作流**。
*   两者都能显著减少处理总时间，特别是在任务相互独立且涉及大量等待（如网络请求）的场景下，性能提升可达数倍甚至数十倍。

通过结合 `AsyncNode`, `AsyncFlow`, `AsyncBatchNode/Flow`, 以及本章的 `AsyncParallelBatchNode/Flow`，你可以根据任务的具体需求（同步/异步、单任务/批处理、顺序/并行）灵活地选择最合适的 PocketFlow 组件，构建出既清晰又高效的 Python 应用，无论是简单的脚本还是复杂的基于 LLM 的系统。

这标志着我们 PocketFlow 核心概念教程的结束。希望这些章节能帮助你理解 PocketFlow 的设计哲学和基本用法，并为你使用 PocketFlow 构建自己的项目打下坚实的基础！祝你编码愉快！

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)