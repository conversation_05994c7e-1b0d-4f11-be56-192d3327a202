# Chapter 2: 流程 (Flow)


在上一章 [节点 (Node)](01_节点__node__.md) 中，我们学习了如何将复杂的任务拆分成一个个独立的、可重试的小单元——节点。我们创建了一个可以调用（模拟）LLM 来生成文本摘要的 `SummarizeNode`。

但是，现实中的任务往往不止一步。例如，我们的摘要任务可能还需要一个步骤来获取原始文本（比如从文件中读取），另一个步骤来保存摘要结果。如果我们只有独立的节点，就像只有一堆独立的工位，没有流水线把它们连接起来，那么这些节点如何协同工作呢？

这就是 **流程 (Flow)** 大显身手的地方！

在本章中，我们将学习：

*   什么是流程 (Flow)？
*   如何将多个节点连接起来形成一个工作流？
*   流程如何根据节点的输出来决定下一步的走向（条件分支）？
*   流程如何管理共享数据？

## 什么是流程 (Flow)？

**流程 (Flow)** 是 PocketFlow 中用来**编排和组织多个 [节点 (Node)](01_节点__node__.md) 的执行顺序和逻辑**的核心概念。

你可以把它想象成一个**工厂的装配线蓝图**或者一个**总调度中心**：

1.  **定义起点**: 蓝图指定了产品从哪个工位（起始节点）开始加工。
2.  **连接工位**: 蓝图清晰地画出了工位之间的连接路径。一个工位完成后，产品应该流向哪个工位。
3.  **条件判断**: 有时，根据一个工位加工的结果（比如质检合格或不合格），产品可能被送到不同的后续工位。流程也支持这种基于条件的跳转。
4.  **数据流动**: 就像零件在传送带上流动一样，数据（存储在 `shared` 字典中）在流程中的节点之间传递和共享。
5.  **组织结构**: 流程将独立的节点（工位）组织成一个**有向图 (Directed Graph)**，定义了任务执行的完整路径，从开始到结束。

更酷的是，一个完整的流程本身也可以被看作一个“超级节点”，嵌套在另一个更大的流程中，就像一个大的工厂里包含了几个小的、自成体系的装配线。

## 创建一个简单的流程：文本处理示例

让我们通过一个非常简单的例子来理解流程：创建一个接收用户输入文本，将其转换为大写，然后打印结果的工作流。

这个工作流包含三个节点：

1.  `InputNode`: 获取用户输入的文本。
2.  `UppercaseNode`: 将文本转换为大写。
3.  `OutputNode`: 打印转换后的文本。

### 1. 定义节点

首先，我们需要定义这三个节点。注意，它们的 `post` 方法现在的作用主要是返回一个 "action" 字符串，告诉流程下一步该做什么。对于简单的顺序流程，我们可以返回一个固定的字符串 (比如 "next") 或者让它默认 (`None`)。

```python
# 导入 PocketFlow 的 Node 和 Flow
from pocketflow import Node, Flow

# 节点 1: 获取输入
class InputNode(Node):
    def prep(self, shared):
        # 如果共享数据中还没有文本，就向用户请求输入
        if "user_text" not in shared:
            shared["user_text"] = input("请输入文本: ")
        return None # prep 不需要给 exec 传递特殊数据

    def post(self, shared, prep_res, exec_res):
        print("输入节点：已获取文本。")
        # 返回 "process" 动作，告诉流程下一步去处理文本
        return "process"

# 节点 2: 转换为大写
class UppercaseNode(Node):
    def prep(self, shared):
        # 从共享数据中获取需要处理的文本
        return shared.get("user_text", "")

    def exec(self, text_to_process):
        # 执行核心逻辑：转换为大写
        print("  处理节点：正在转换为大写...")
        return text_to_process.upper()

    def post(self, shared, prep_res, exec_res):
        # 将处理结果存回共享数据
        shared["processed_text"] = exec_res
        print(f"  处理节点：转换完成，结果: '{exec_res}'")
        # 返回 "output" 动作，告诉流程下一步去输出结果
        return "output"

# 节点 3: 打印输出
class OutputNode(Node):
    def prep(self, shared):
        # 从共享数据中获取要打印的文本
        return shared.get("processed_text", "没有找到要打印的文本。")

    def exec(self, text_to_print):
        # 执行核心逻辑：打印文本
        print(f"输出节点：最终结果是 -> {text_to_print}")
        return None # exec 没有有意义的返回值需要传递给 post

    def post(self, shared, prep_res, exec_res):
        # 这个节点是流程的终点，可以不返回 action 或返回 None
        return None # 或者 return "done" 等
```

**代码解释**:

*   我们定义了三个继承自 `Node` 的类。
*   `InputNode` 在 `prep` 阶段获取用户输入并存入 `shared` 字典，然后在 `post` 阶段返回 `"process"`。
*   `UppercaseNode` 在 `prep` 阶段从 `shared` 读取文本，在 `exec` 阶段执行转换，在 `post` 阶段将结果存回 `shared` 并返回 `"output"`。
*   `OutputNode` 在 `prep` 阶段从 `shared` 读取处理后的文本，在 `exec` 阶段打印，`post` 返回 `None` 表示流程在此结束。

### 2. 连接节点并创建流程

现在我们有了节点，需要用 `Flow` 把它们像串珠子一样串起来。

在 PocketFlow 中，连接节点非常直观：

*   `node1 >> node2`: 表示 `node1` 完成后（无论 `post` 返回什么 action，只要定义了默认路径），流程将执行 `node2`。这是最简单的顺序连接。
*   `node1 - "action_name" >> node2`: 表示当 `node1` 的 `post` 方法返回字符串 `"action_name"` 时，流程将执行 `node2`。这用于实现**条件分支**。

让我们连接上面定义的节点：

```python
# 创建节点实例
input_node = InputNode()
uppercase_node = UppercaseNode()
output_node = OutputNode()

# 连接节点：定义流程路径
# InputNode 返回 "process" 时，转到 UppercaseNode
input_node - "process" >> uppercase_node
# UppercaseNode 返回 "output" 时，转到 OutputNode
uppercase_node - "output" >> output_node

# 创建流程实例，并指定起始节点
text_flow = Flow(start=input_node)

# 准备共享数据字典 (初始为空)
shared_data = {}

# 运行流程！
print("开始运行文本处理流程...")
text_flow.run(shared_data)
print("流程运行结束。")

# 检查共享数据
print("最终共享数据:", shared_data)
```

**代码解释**:

1.  **`input_node - "process" >> uppercase_node`**: 这行代码的意思是：当 `input_node` 执行完毕且其 `post` 方法返回字符串 `"process"` 时，流程的下一个节点应该是 `uppercase_node`。
2.  **`uppercase_node - "output" >> output_node`**: 类似地，当 `uppercase_node` 的 `post` 返回 `"output"` 时，流程将转到 `output_node`。
3.  **`text_flow = Flow(start=input_node)`**: 我们创建了一个 `Flow` 对象，并告诉它流程应该从 `input_node` 开始执行。
4.  **`text_flow.run(shared_data)`**: 这会启动整个流程。PocketFlow 会自动按照我们定义的连接关系，依次执行 `InputNode` -> `UppercaseNode` -> `OutputNode`。共享数据 `shared_data` 会在整个流程中传递，节点可以读取和修改它。

当你运行这段代码时，它会提示你输入文本，然后依次执行三个节点，最后打印出大写的结果和最终的 `shared_data`。

这是一个简单的流程图，展示了节点间的连接：

```mermaid
graph LR
    A[InputNode] -- "process" --> B(UppercaseNode);
    B -- "output" --> C(OutputNode);
```

## 添加条件分支

上面的例子是一个简单的线性流程。但如果我们需要根据条件执行不同的路径呢？比如，让用户选择是转换为大写还是小写。

我们可以修改 `InputNode`，让它询问用户的选择，并在 `post` 方法中根据选择返回不同的 "action"。

### 1. 修改 InputNode 和添加 LowercaseNode

```python
from pocketflow import Node, Flow # 重新导入，以防是在新单元格运行

# 节点 1 (修改版): 获取输入和选择
class InputNodeWithChoice(Node):
    def prep(self, shared):
        if "user_text" not in shared:
            shared["user_text"] = input("请输入文本: ")
        return None

    def post(self, shared, prep_res, exec_res):
        print("输入节点：已获取文本。")
        while True:
            choice = input("选择操作: 1. 转大写 2. 转小写\n你的选择: ")
            if choice == "1":
                return "to_upper" # 返回 "to_upper" 动作
            elif choice == "2":
                return "to_lower" # 返回 "to_lower" 动作
            else:
                print("无效选择，请重新输入。")

# 新节点: 转换为小写
class LowercaseNode(Node):
    def prep(self, shared):
        return shared.get("user_text", "")

    def exec(self, text_to_process):
        print("  处理节点：正在转换为小写...")
        return text_to_process.lower()

    def post(self, shared, prep_res, exec_res):
        shared["processed_text"] = exec_res
        print(f"  处理节点：转换完成，结果: '{exec_res}'")
        # 转换完成后，都去输出节点
        return "output"

# 复用之前的 UppercaseNode 和 OutputNode
class UppercaseNode(Node): # (和之前一样，为完整性重写)
    def prep(self, shared): return shared.get("user_text", "")
    def exec(self, text): print("  处理节点：正在转换为大写..."); return text.upper()
    def post(self, shared, prep_res, exec_res):
        shared["processed_text"] = exec_res
        print(f"  处理节点：转换完成，结果: '{exec_res}'"); return "output"

class OutputNode(Node): # (和之前一样)
    def prep(self, shared): return shared.get("processed_text", "无结果")
    def exec(self, text): print(f"输出节点：最终结果是 -> {text}"); return None
    def post(self, shared, prep_res, exec_res): return None
```

**代码解释**:

*   `InputNodeWithChoice` 的 `post` 方法现在包含一个循环，直到用户输入 "1" 或 "2"，然后返回 `"to_upper"` 或 `"to_lower"`。
*   我们新增了 `LowercaseNode`，其逻辑与 `UppercaseNode` 类似，但执行小写转换。它的 `post` 方法也返回 `"output"`，表示处理完成后应转到输出节点。

### 2. 连接节点以实现分支

现在，我们需要根据 `InputNodeWithChoice` 返回的不同 action 来连接不同的处理节点。

```python
# 创建节点实例
input_choice_node = InputNodeWithChoice()
uppercase_node = UppercaseNode()
lowercase_node = LowercaseNode() # 创建小写节点实例
output_node = OutputNode()

# 连接节点，包含条件分支
# 如果 InputNode 返回 "to_upper", 去 UppercaseNode
input_choice_node - "to_upper" >> uppercase_node
# 如果 InputNode 返回 "to_lower", 去 LowercaseNode
input_choice_node - "to_lower" >> lowercase_node

# 无论从 UppercaseNode 还是 LowercaseNode 出来 (它们都返回 "output"), 都去 OutputNode
uppercase_node - "output" >> output_node
lowercase_node - "output" >> output_node

# 创建流程，起始节点是新的输入节点
conditional_flow = Flow(start=input_choice_node)

# 运行流程
shared_data_cond = {}
print("\n开始运行带条件分支的文本处理流程...")
conditional_flow.run(shared_data_cond)
print("流程运行结束。")
```

**代码解释**:

*   `input_choice_node - "to_upper" >> uppercase_node`: 定义了当 action 为 `"to_upper"` 时的路径。
*   `input_choice_node - "to_lower" >> lowercase_node`: 定义了当 action 为 `"to_lower"` 时的路径。
*   `uppercase_node - "output" >> output_node` 和 `lowercase_node - "output" >> output_node`: 定义了两个处理节点完成后的共同路径（都去输出）。

现在运行 `conditional_flow.run()`，程序会根据你的选择执行不同的转换路径！

这个带分支的流程可以用下图表示：

```mermaid
graph LR
    A[InputNodeWithChoice] -- "to_upper" --> B(UppercaseNode);
    A -- "to_lower" --> C(LowercaseNode);
    B -- "output" --> D(OutputNode);
    C -- "output" --> D;
```

## 流程嵌套（简述）

PocketFlow 的一个强大之处在于 `Flow` 本身也遵循与 `Node` 相似的接口（它们都继承自 `BaseNode`）。这意味着你可以将一个完整的流程（比如我们上面创建的 `text_flow` 或 `conditional_flow`）当作一个节点，嵌入到另一个更大的流程中。

这对于构建模块化、可复用的复杂工作流非常有用。你可以将一个子任务封装成一个独立的 Flow，然后在需要的地方调用它。

例如，你可以创建一个 `MainFlow`，它的一个步骤是运行我们之前定义的 `conditional_flow`：

```python
# 假设 conditional_flow 已经定义好了 (如上)

# 创建一个简单的包装节点，用于启动子流程
class RunSubflowNode(Node):
    def exec(self, prep_res):
        print("主流程：即将运行子流程...")
        sub_shared = {} # 可以给子流程独立的 shared，或传递主流程的
        conditional_flow.run(sub_shared) # 运行子流程
        print("主流程：子流程运行完毕。")
        return "done" # 子流程完成后，主流程继续

# 创建主流程的其他节点 (示例)
class EndMainFlowNode(Node):
    def exec(self, prep_res): print("主流程：结束。"); return None

# 创建实例
start_main = RunSubflowNode()
end_main = EndMainFlowNode()

# 连接主流程节点
start_main - "done" >> end_main

# 创建主流程
main_flow = Flow(start=start_main)

# 运行主流程 (它会触发子流程的运行)
print("\n开始运行主流程...")
main_flow.run({})
print("主流程结束。")
```

这个例子展示了基本的嵌套概念，但实际应用中可能会更复杂，涉及到主流程和子流程之间的数据传递。

## 流程内部是如何工作的？

当你调用 `flow.run(shared)` 时，PocketFlow 在后台为你做了什么？

1.  **获取起始节点**: 流程找到你在创建 `Flow` 时指定的 `start_node`。
2.  **设置参数 (可选)**: 如果 `Flow` 或节点有参数 (`params`)，会进行设置。
3.  **开始循环**: 进入一个循环，只要当前节点不为空，就一直执行。
4.  **运行当前节点**: 调用当前节点的 `_run(shared)` 方法（这会依次执行节点的 `prep`, `_exec` (含重试), `post`）。
5.  **获取 Action**: `_run` 方法返回 `post` 的结果，即 "action" 字符串。
6.  **查找下一个节点**: 流程使用 `get_next_node(current_node, action)` 方法，根据当前节点和返回的 action，查找连接配置中指定的下一个节点。
    *   它会查找 `current_node.successors[action]`。
    *   如果找不到特定 action 的路径，它会尝试查找默认路径 `current_node.successors["default"]`（通过 `>>` 连接设置）。
    *   如果都找不到，流程结束。
7.  **更新当前节点**: 将查找到的下一个节点设为新的当前节点。
8.  **重复**: 回到步骤 4，继续执行新的当前节点。
9.  **结束**: 当 `get_next_node` 找不到下一个节点时，循环结束，`flow.run()` 执行完毕。

下面是一个简化的时序图，展示了这个 **编排 (Orchestration)** 过程：

```mermaid
sequenceDiagram
    participant User
    participant MyFlow as Flow
    participant Orch as MyFlow._orch()
    participant NodeA as 当前节点A (例如 InputNode)
    participant NodeB as 下一个节点B (例如 UppercaseNode)

    User->>MyFlow: run(shared)
    MyFlow->>Orch: _orch(shared)
    Note over Orch: 当前节点 = 起始节点 (NodeA)
    loop 只要当前节点不为空
        Orch->>NodeA: _run(shared) (执行 prep, exec, post)
        NodeA-->>Orch: 返回 action (例如 "process")
        Orch->>MyFlow: get_next_node(NodeA, "process")
        MyFlow-->>Orch: 返回下一个节点 (NodeB)
        Orch-->>Orch: 更新当前节点 = NodeB
        Orch->>NodeB: _run(shared) (执行 prep, exec, post)
        NodeB-->>Orch: 返回 action (例如 "output")
        Orch->>MyFlow: get_next_node(NodeB, "output")
        MyFlow-->>Orch: 返回下一个节点 (例如 NodeC 或 None)
        Orch-->>Orch: 更新当前节点 = 下一个节点
        Note over Orch: 重复或结束循环
    end
    Orch-->>MyFlow: 返回最后的 action
    MyFlow-->>User: 返回结果
```

**代码实现速览**:

流程的核心逻辑可以在 `pocketflow/__init__.py` 文件中的 `Flow` 类找到。其简化结构如下：

```python
# (来自 pocketflow/__init__.py 的简化版本)
import copy
import warnings

class Flow(BaseNode): # Flow 也继承自 BaseNode
    def __init__(self, start=None):
        super().__init__()
        self.start_node = start # 记录起始节点

    def start(self, start): # 设置起始节点的方法
        self.start_node = start
        return start

    def get_next_node(self, curr, action):
        """根据当前节点和 action 查找下一个节点"""
        # 尝试查找指定 action 的后继节点
        nxt = curr.successors.get(action or "default") # 如果 action 为空或 None，尝试 "default"
        if not nxt and action and "default" in curr.successors:
             # 如果指定 action 的路径不存在，尝试默认路径（由 >> 定义）
             nxt = curr.successors.get("default")

        # 如果还是找不到，并且当前节点定义了后继节点，说明 action 没匹配上
        if not nxt and curr.successors:
            warnings.warn(f"流程结束：在节点 {type(curr).__name__} 的后继者中未找到动作 '{action}'。可用的动作: {list(curr.successors.keys())}")
        return nxt # 返回找到的节点，或者 None

    def _orch(self, shared, params=None):
        """核心编排逻辑"""
        # 复制起始节点，避免修改原始流程定义
        curr = copy.copy(self.start_node)
        p = (params or {**self.params}) # 合并参数
        last_action = None

        while curr: # 循环直到没有下一个节点
            curr.set_params(p) # 设置节点参数 (如果需要)
            last_action = curr._run(shared) # 运行当前节点，获取 action
            # 复制下一个节点，避免修改原始流程定义
            curr = copy.copy(self.get_next_node(curr, last_action)) # 查找并更新到下一个节点

        return last_action # 返回最后一个 action

    def _run(self, shared):
        """运行流程的入口"""
        # Flow 节点本身的 prep (通常用不到，除非嵌套)
        p = self.prep(shared)
        # 调用编排逻辑
        o = self._orch(shared)
        # Flow 节点本身的 post (通常返回编排结果)
        return self.post(shared, p, o)

    def post(self, shared, prep_res, exec_res):
        """Flow 的 post 默认返回编排循环的结果 (最后一个 action)"""
        return exec_res
```

这个结构清晰地展示了 `Flow` 如何通过 `_orch` 方法循环执行节点，并使用 `get_next_node` 根据节点的 `post` 返回值（action）和连接定义（`>>` 或 `- "action" >>`）来决定流程的下一步。

## 总结

在本章中，我们学习了 PocketFlow 中用于编排和连接节点的 **流程 (Flow)**。

*   流程就像一个**蓝图或总调度**，定义了多个 [节点 (Node)](01_节点__node__.md) 的执行顺序和逻辑。
*   我们使用 `>>` (默认连接) 和 `- "action" >>` (条件连接) 来**定义节点之间的路径**。
*   节点的 `post` 方法返回的 **"action" 字符串** 决定了流程在条件分支处的走向。
*   `Flow` 实例通过调用 `run(shared_data)` 来启动，`shared_data` 在所有节点间共享。
*   流程本身也可以作为节点嵌套在其他流程中，实现模块化。
*   我们了解了 `Flow` 内部的 `_orch` 方法是如何循环执行节点并根据 action 查找下一个节点的。

通过将独立的节点用流程连接起来，我们可以构建出结构清晰、逻辑明确、可扩展的工作流，无论是简单的文本处理还是复杂的 LLM 应用。

但是，到目前为止，我们所有的节点 (`prep`, `exec`, `post`) 都是同步执行的。如果一个节点的 `exec` 涉及到耗时的操作，比如真正的网络请求或复杂的计算，它会阻塞整个流程。如何处理这种情况呢？

**下一章**: [第 3 章：异步节点 (AsyncNode)](03_异步节点__asyncnode__.md) - 学习如何定义和使用异步节点，让你的流程在等待 I/O 操作时不会被阻塞，提高效率。

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)