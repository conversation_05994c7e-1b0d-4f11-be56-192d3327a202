# Chapter 4: MCP 提示


在上一章 [MCP 资源](03_mcp_资源_.md) 中，我们了解了 MCP 服务器如何像提供原材料或参考手册一样，向 AI 助手提供结构化的信息片段。我们看到了服务器如何提供数据库模式或动态更新的备忘录。

现在，假设你面对一个稍微复杂的任务，比如你想用 [SQLite 服务器](09_数据库服务器__sqlite_示例__.md) 来分析一个全新的业务领域（比如“零售销售分析”），但你不太确定如何开始——需要创建哪些表？插入什么示例数据？先问什么问题？

如果能有一个“启动模板”或者“引导流程”，根据你选择的主题，自动帮你设置好基础环境，并引导你开始分析，那该多好！

这就是 **MCP 提示 (MCP Prompt)** 的用武之地。它们为常见的任务或对话流程提供了预定义的起点。

## 什么是 MCP 提示？

> MCP 提示是 [MCP 服务器](01_mcp_服务器_.md) 提供的预定义交互起点或模板。它们就像工具棚里预填好的请求表格或项目蓝图。用户或 AI 助手可以选择一个提示来开始一个结构化的对话或任务流程。

**打个比方：**

想象一下你在一家提供各种服务的公司（比如宜家）。

*   [MCP 服务器](01_mcp_服务器_.md) 就像是公司的不同部门（厨房设计部、卧室设计部）。
*   [MCP 工具](02_mcp_工具_.md) 是部门提供的具体服务（测量尺寸、安装橱柜）。
*   [MCP 资源](03_mcp_资源_.md) 是部门提供的参考资料或材料样本（产品目录、颜色样本）。
*   **MCP 提示** 则像是这家公司提供的**“套餐服务”**或**“项目启动问卷”**。例如，“厨房焕新套餐”或者“卧室储物解决方案设计问卷”。

当你选择一个“套餐服务”时，服务人员（AI 助手）会根据这个套餐的流程，一步步引导你完成设计和购买。这个套餐（提示）已经预先设定好了大致的步骤和需要考虑的问题。

**关键特性：**

1.  **结构化起点 (Structured Start):** 提示提供了一个预设的对话开端，通常包含一个或多个初始消息（可能是用户消息，也可能是助手消息）。
2.  **参数化 (Parameterization, 可选):** 很多提示允许传入参数，以根据用户的具体需求定制启动流程。例如，SQLite 服务器的 `mcp-demo` 提示接受一个 `topic` 参数，用来决定分析的主题。
3.  **引导流程 (Guided Flow):** 提示的设计目的通常是引导用户或 AI 完成一个特定的工作流，通过预设的消息和可能的后续交互，让复杂的任务变得更容易上手。

**提示的发现：**

与工具和资源类似，AI 助手通过向服务器发送 `listPrompts` 请求来发现可用的提示。服务器会返回一个列表，包含每个提示的名称、描述以及它可能接受的参数。

## 使用 MCP 提示解决用例：启动 SQLite 演示

让我们回到刚才的用例：使用 `sqlite` 服务器提供的 `mcp-demo` 提示来启动一个关于“零售销售”主题的数据分析场景。

1.  **用户选择:** 你在 AI 助手（例如 Claude 桌面版）的 MCP 菜单中，看到了来自 `sqlite` 服务器的 `mcp-demo` 提示。你选择了这个提示，并且被要求提供一个 `topic` 参数，你输入了“零售销售”。
2.  **AI 决策:** AI 助手知道用户选择了 `mcp-demo` 提示，并提供了 `topic="零售销售"` 参数。
3.  **MCP 请求 (获取提示):** AI 助手向 `sqlite` 服务器发送一个 `getPrompt` 请求。这个请求是一个 JSON 消息，内容类似：
    ```json
    {
      "jsonrpc": "2.0",
      "method": "getPrompt", // 请求类型：获取提示内容
      "params": {
        "name": "mcp-demo",    // 要获取的提示名称
        "arguments": {       // 传递给提示的参数
          "topic": "零售销售"
        }
      }
    }
    ```
4.  **服务器处理:** `sqlite` 服务器收到请求。
    *   它解析请求，确认要获取 `mcp-demo` 提示。
    *   它找到处理 `mcp-demo` 提示的逻辑，并提取 `topic` 参数的值 "零售销售"。
    *   服务器根据这个 `topic`，生成一个或多个初始对话消息。这些消息通常设计用来启动演示流程，可能包含对主题的介绍、下一步计划等。例如，它可能会生成一个看起来像是用户发出的第一条消息，内容是指导 AI 如何开始这个零售销售分析场景的长篇说明。
5.  **MCP 响应:** `sqlite` 服务器将生成的初始消息包装在 JSON 响应中，发送回 AI 助手：
    ```json
    {
      "jsonrpc": "2.0",
      "result": { // 成功的结果
        "description": "零售销售的演示模板", // 提示的描述（可能基于参数生成）
        "messages": [           // 包含初始消息的数组
          {
            "role": "user",      // 这条消息的角色（通常是 'user'）
            "content": {
              "type": "text",
              "text": "哦，你好！我看到你选择了‘零售销售’这个主题。让我们开始吧！🚀 [...这里是基于零售销售主题生成的一长段引导AI开始演示的文字...]" // 提示生成的初始文本
            }
          }
          // 可能还有更多初始消息
        ]
      }
    }
    ```
6.  **开始对话:** AI 助手收到响应，提取 `messages` 数组中的内容。它会将这些消息（尤其是 `role: "user"` 的消息）视为对话的开始，然后基于这些消息生成它的第一个回复，从而正式启动由 `mcp-demo` 提示所定义的引导流程。例如，AI 可能会回复：“好的，我已经收到了关于零售销售分析的启动说明。首先，我们需要创建一些相关的数据库表...”

通过这个流程，仅仅通过选择一个提示并提供一个主题，用户就启动了一个结构化的、针对特定任务（零售销售分析）的引导式对话。AI 助手则获得了清晰的初始指令，知道接下来该如何利用可用的 [MCP 工具](02_mcp_工具_.md)（如 `create_table`, `write_query`）来推进任务。

## 代码示例：定义和处理提示

让我们看看服务器代码是如何定义和处理提示的。我们将以 `sqlite` 服务器的 `mcp-demo` 提示为例 (代码来自 `src/sqlite/src/mcp_server_sqlite/server.py`，并进行简化)。

**1. 定义提示 (响应 `listPrompts` 请求):**

服务器需要告诉客户端它提供了哪些提示，以及这些提示需要什么参数。

```python
# 导入 MCP 类型
import mcp.types as types
import logging

logger = logging.getLogger(__name__)

# ... (服务器实例 'server' 已创建) ...

# 使用 @server.list_prompts() 装饰器注册处理函数
@server.list_prompts()
async def handle_list_prompts() -> list[types.Prompt]:
    """处理 listPrompts 请求，返回可用的提示列表"""
    logger.debug("正在处理 list_prompts 请求")
    return [
        types.Prompt(
            name="mcp-demo", # 提示的唯一名称
            description="一个引导用户使用 SQLite 服务器进行数据库操作和分析的演示提示", # 提示的描述
            arguments=[      # 定义提示接受的参数列表
                types.PromptArgument(
                    name="topic",        # 参数名称
                    description="用于初始化数据库数据的分析主题", # 参数描述
                    required=True,       # 此参数是必需的
                )
            ],
        )
        # 可能还有其他提示...
    ]
```

这段代码定义了一个名为 `mcp-demo` 的提示。
*   `name`: 提示的唯一标识符。
*   `description`: 给 AI 和用户看的说明，解释这个提示是做什么的。
*   `arguments`: 一个列表，定义了该提示需要哪些参数。这里定义了一个必需的参数 `topic`。AI 客户端会根据这个定义来提示用户输入参数。

**2. 处理 `getPrompt` 请求:**

当 AI 客户端请求获取某个提示的内容时，服务器需要根据提供的参数生成初始消息。

```python
# 导入 MCP 类型
import mcp.types as types
import logging

logger = logging.getLogger(__name__)

# 一个包含长篇引导说明的模板字符串 (极度简化)
# 实际的模板会更复杂，包含详细的步骤说明
PROMPT_TEMPLATE = """
你好！看起来你对 '{topic}' 很感兴趣。
这是一个演示，我们将用 MCP 工具来：
1. 创建关于 '{topic}' 的表。
2. 填充示例数据。
3. 进行一些分析。
让我们开始吧！
<demo-instructions>...</demo-instructions>
"""

# ... (服务器实例 'server' 已创建) ...

# 使用 @server.get_prompt() 装饰器注册处理函数
@server.get_prompt()
async def handle_get_prompt(name: str, arguments: dict[str, str] | None) -> types.GetPromptResult:
    """处理 getPrompt 请求，根据参数生成初始对话消息"""
    logger.debug(f"正在处理 get_prompt 请求，提示名称: {name}，参数: {arguments}")

    if name != "mcp-demo":
        logger.error(f"未知的提示: {name}")
        raise ValueError(f"未知的提示: {name}")

    if not arguments or "topic" not in arguments:
        logger.error("缺少必需的参数: topic")
        raise ValueError("缺少必需的参数: topic")

    topic = arguments["topic"] # 获取 'topic' 参数的值

    # 使用 topic 参数格式化模板字符串，生成实际的提示文本
    prompt_text = PROMPT_TEMPLATE.format(topic=topic)
    logger.debug(f"已为主题 '{topic}' 生成提示文本")

    # 返回包含初始消息的结果
    return types.GetPromptResult(
        description=f"{topic} 的演示模板", # 可以动态生成描述
        messages=[                   # 初始消息列表
            types.PromptMessage(
                role="user",         # 消息角色，通常是 'user'
                content=types.TextContent(type="text", text=prompt_text.strip()), # 消息内容
            )
        ],
    )

```

这个 `handle_get_prompt` 函数：
1.  接收 `getPrompt` 请求，包含提示名称 (`name`) 和参数 (`arguments`)。
2.  检查是否是 `mcp-demo` 提示，并验证必需的 `topic` 参数是否存在。
3.  获取 `topic` 的值。
4.  使用 `topic` 值填充 `PROMPT_TEMPLATE` 字符串，生成具体的初始对话文本。
5.  构造并返回一个 `GetPromptResult` 对象，其中 `messages` 列表包含了这个生成的初始消息，角色被设为 `user`。AI 客户端收到这个响应后，就会把这段文本作为对话的起点。

## 内部实现：请求提示流程

让我们梳理一下当 AI 获取并使用一个提示时，幕后发生了什么。

**非代码流程 (`getPrompt`):**

1.  **用户选择：** 用户在 AI 客户端界面选择了一个 MCP 提示（例如，`mcp-demo`）并可能提供了参数（例如，`topic="零售销售"`）。
2.  **AI 决策：** AI 客户端识别出用户的选择和输入的参数。
3.  **构造请求：** AI 客户端根据提示名称和参数构造一个 `getPrompt` 请求。
4.  **发送请求：** AI 通过 MCP 连接将 JSON-RPC 请求发送给相应的 [MCP 服务器](01_mcp_服务器_.md)（例如 `sqlite` 服务器）。
5.  **服务器接收与解析：** 服务器接收到请求，解析 JSON，识别出是 `getPrompt` 方法，并获取提示名称和参数。
6.  **查找处理函数：** 服务器查找注册用来处理 `getPrompt` 请求的函数（例如，使用 `@server.get_prompt()` 装饰的函数）。
7.  **执行处理函数：** 服务器调用该函数，并将请求参数传递给它。
8.  **生成初始消息：** 处理函数内部：
    *   验证提示名称和参数。
    *   根据参数值，使用预定义的模板或其他逻辑，生成一个或多个初始对话消息（`PromptMessage` 对象）。
9.  **构造响应：** 处理函数将生成的消息包装成 `GetPromptResult` 响应格式。
10. **发送响应：** 服务器通过 MCP 连接将包含初始消息的 JSON-RPC 响应发送回 AI 客户端。
11. **AI 处理响应并开始对话：** AI 客户端接收响应，解析 JSON，提取 `messages` 数组。它将这些消息视为对话的开始，并基于这些内容生成自己的第一条回复，从而启动提示所定义的交互流程。

**时序图示例 (`getPrompt` 获取 `mcp-demo`):**

```mermaid
sequenceDiagram
    participant 用户
    participant Claude (AI 客户端)
    participant SQLite服务器

    用户->>Claude (AI 客户端): 选择 "mcp-demo" 提示, 输入 topic="零售销售"
    Claude (AI 客户端)->>SQLite服务器: MCP 请求 (getPrompt, name: "mcp-demo", args: {topic: "零售销售"})
    SQLite服务器->>SQLite服务器: 查找并执行 getPrompt 处理函数
    SQLite服务器->>SQLite服务器: 使用 topic="零售销售" 生成初始消息文本
    SQLite服务器->>Claude (AI 客户端): MCP 响应 (result: {messages: [{role:"user", text:"你好！...让我们开始吧！..."}]})
    Claude (AI 客户端)->>用户: (基于收到的初始消息) "好的，我们来分析零售销售数据。首先，需要创建表..."
```

这个图表清晰地展示了从用户选择提示到 AI 获取初始对话内容并开始引导式交互的完整流程。

## 总结

在本章中，我们学习了 MCP 服务器提供的第三种核心能力：**MCP 提示**。我们了解到：

*   MCP 提示是预定义的交互起点或模板，就像项目启动问卷或套餐服务。
*   它们帮助用户和 AI 助手轻松启动结构化的对话或复杂的工作流程。
*   提示可以接受参数，以便根据具体需求进行定制。
*   AI 客户端通过 `listPrompts` 发现提示，通过 `getPrompt` 获取指定提示的初始对话消息。
*   服务器负责根据请求和参数生成这些初始消息。
*   提示与 [MCP 工具](02_mcp_工具_.md) 和 [MCP 资源](03_mcp_资源_.md) 协同工作，为 AI 提供了执行任务（工具）、获取信息（资源）以及启动流程（提示）的全方位能力。

理解了 MCP 服务器、工具、资源和提示这几个核心概念后，我们将在下一章看一个特别的服务器：[Everything 服务器](05_everything_服务器_.md)，它是一个集成了所有这些功能的综合示例，非常适合用来测试和理解 MCP 的各项特性。

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)