# Chapter 2: MCP 工具


在上一章 [MCP 服务器](01_mcp_服务器_.md) 中，我们了解了 MCP 服务器就像是 AI 助手的专用工具棚或服务台，负责安全地连接 AI 与外部世界。现在，我们将打开这个工具棚，仔细看看里面存放的具体“电动工具”—— **MCP 工具**。

## 动机：AI 如何“做事”？

想象一下，你让 Claude 帮你执行一个具体的操作：“检查我本地 `my_project` 仓库的 Git 状态”。

我们知道，直接让 AI 运行 `git status` 命令是不安全的。那么，AI 如何通过我们在 [第 1 章](01_mcp_服务器_.md) 中介绍的 `git` 服务器来完成这个任务呢？它需要一种标准的方式来请求 `git` 服务器执行这个特定的“检查状态”动作。

这就是 **MCP 工具** 发挥作用的地方。它们是 MCP 服务器提供的、定义清晰、可供 AI 调用的具体操作。

## 什么是 MCP 工具？

> MCP 工具是 MCP 服务器提供的具体操作或动作。它们就像工具棚里的电动工具（例如，`git_status` 像一个版本检查器，`read_file` 像一个文件阅读器，`search_users` 像一个用户目录搜索器）。AI 助手可以通过调用这些工具来执行特定的任务，例如读取文件、提交代码更改、查询数据库或搜索网页。每个工具都有明确定义的输入和输出。

**打个比方：**

如果说 MCP 服务器是工具棚，那么 MCP 工具就是工具棚里陈列的各种电动工具：

*   `git` 服务器（工具棚）可能提供：
    *   `git_status` 工具（版本状态检查器）
    *   `git_diff` 工具（代码差异比较器）
    *   `git_commit` 工具（代码提交器）
*   `filesystem` 服务器（工具棚）可能提供：
    *   `read_file` 工具（文件阅读器）
    *   `write_file` 工具（文件写入器）
    *   `list_directory` 工具（目录列表器）
*   `brave-search` 服务器（工具棚）可能提供：
    *   `brave_web_search` 工具（网页搜索器）
    *   `brave_local_search` 工具（本地商家搜索器）

AI 助手（比如 Claude）就像一个工匠，当它需要执行某个特定任务时，它会从相应的服务器（工具棚）中挑选合适的工具，并按照工具的说明书（输入/输出定义）来使用它。

**关键特性：**

1.  **明确的操作 (Action):** 每个工具执行一个非常具体的操作。
2.  **清晰的输入 (Input):** 每个工具都明确定义了它需要哪些参数才能工作。例如，`read_file` 需要一个 `path` 参数，`brave_web_search` 需要一个 `query` 参数。这些输入要求通常用 JSON Schema 来描述。
3.  **明确的输出 (Output):** 每个工具也定义了它会返回什么结果（成功时）或错误信息（失败时）。例如，`read_file` 返回文件内容，`git_status` 返回状态文本。

![工具输入输出](https://modelcontextprotocol.io/assets/images/mcp-tools-light-a48628370c859f4b8ea7309ebbd08f59.png)

**工具的发现：**

AI 助手如何知道一个服务器提供了哪些工具呢？它会在与服务器建立连接后，发送一个名为 `listTools` 的请求。服务器会响应该请求，列出它提供的所有工具及其描述和输入/输出规范。这样，AI 助手就能“看到”工具棚里有哪些工具可用了。

## 使用 MCP 工具解决用例

让我们回到之前的用例：让 Claude 检查 `my_project` 仓库的 Git 状态。假设 `my_project` 位于 `/path/to/allowed/my_project`，并且我们已经配置并运行了 `git` 服务器，允许访问该路径。

1.  **用户请求:** 你对 Claude 说：“检查 `/path/to/allowed/my_project` 的 Git 状态。”
2.  **AI 决策:** Claude 分析请求，识别出需要执行 Git 状态检查。它知道自己连接了一个名为 `git` 的 [MCP 服务器](01_mcp_服务器_.md)，并且通过 `listTools` 了解到该服务器提供了一个名为 `git_status` 的工具，这个工具需要一个 `repo_path` 参数。
3.  **MCP 请求 (调用工具):** Claude 向 `git` 服务器发送一个 `callTool` 请求。这个请求是一个 JSON 消息，内容类似：

    ```json
    {
      "jsonrpc": "2.0",
      "method": "callTool", // 请求类型：调用工具
      "params": {
        "name": "git_status", // 要调用的工具名称
        "arguments": {       // 传递给工具的参数
          "repo_path": "/path/to/allowed/my_project"
        }
      }
    }
    ```
4.  **服务器处理:** `git` 服务器收到请求。
    *   它解析请求，确认要调用 `git_status` 工具。
    *   它验证 `arguments` 是否符合 `git_status` 工具定义的输入模式（`inputSchema`）。
    *   它检查 `repo_path` 是否在允许的访问范围内。
    *   验证通过后，服务器在 **安全的沙箱环境** 中执行 `git status` 命令。
    *   服务器获取 `git status` 的输出文本。
5.  **MCP 响应:** `git` 服务器将 Git 状态的输出包装成 JSON 响应，发送回 Claude：

    ```json
    {
      "jsonrpc": "2.0",
      "result": { // 成功的结果
        "content": [
          {
            "type": "text",
            "text": "On branch main\nYour branch is up to date with 'origin/main'.\n\nnothing to commit, working tree clean" // Git status 的实际输出
          }
        ],
        "isError": false // 表示没有发生错误
      }
    }
    ```
6.  **呈现结果:** Claude 收到响应，提取 `text` 内容，并将其展示给你：“`/path/to/allowed/my_project` 的 Git 状态是：\nOn branch main\nYour branch is up to date with 'origin/main'.\n\nnothing to commit, working tree clean”。

通过这个流程，AI 助手利用 `git` 服务器提供的 `git_status` 工具，安全地完成了用户的请求，而无需直接执行任何命令。

## 代码示例：定义和处理工具

让我们看看在服务器代码中，工具是如何被定义和处理的。我们将以 `brave-search` 服务器中的 `brave_web_search` 工具为例（代码来自 `src/brave-search/index.ts`）。

**1. 定义工具 (Tool Definition):**

服务器首先需要定义它提供的工具，包括名称、描述和输入参数的规范（`inputSchema`）。

```typescript
// 导入 SDK 类型
import { Tool } from "@modelcontextprotocol/sdk/types.js";

// 定义 brave_web_search 工具
const WEB_SEARCH_TOOL: Tool = {
  name: "brave_web_search", // 工具的唯一名称
  description:                 // 工具的描述，告诉 AI 何时以及如何使用它
    "Performs a web search using the Brave Search API, ideal for general queries...",
  inputSchema: {               // 定义工具需要的输入参数 (使用 JSON Schema)
    type: "object",
    properties: {
      query: {                 // 需要一个名为 'query' 的参数
        type: "string",        // 参数类型是字符串
        description: "Search query (max 400 chars, 50 words)" // 参数描述
      },
      count: {                 // 可选参数 'count'
        type: "number",
        description: "Number of results (1-20, default 10)",
        default: 10            // 默认值
      },
      // ... 其他可选参数 offset 等
    },
    required: ["query"],       // 'query' 参数是必需的
  },
};

// ... (可能还有 LOCAL_SEARCH_TOOL 的定义)
```

这段代码定义了一个名为 `brave_web_search` 的工具。

*   `name`: 工具的唯一标识符。
*   `description`: 给 AI 看的说明，解释工具的功能和适用场景。
*   `inputSchema`: 使用 JSON Schema 格式严格定义了工具需要哪些输入参数、参数的类型、是否必需以及默认值等。这有助于 AI 构造正确的请求，服务器也可以用它来验证输入。

**2. 响应 `listTools` 请求:**

服务器需要告诉客户端它有哪些工具。这是通过处理 `listTools` 请求完成的。

```typescript
// 导入 SDK 和请求模式
import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { ListToolsRequestSchema } from "@modelcontextprotocol/sdk/types.js";

// ... (服务器初始化代码) ...
const server = new Server(/* ... */);

// ... (WEB_SEARCH_TOOL 和 LOCAL_SEARCH_TOOL 的定义) ...

// 注册处理 listTools 请求的函数
server.setRequestHandler(ListToolsRequestSchema, async () => {
  // 返回一个包含所有可用工具定义的列表
  return {
    tools: [WEB_SEARCH_TOOL, LOCAL_SEARCH_TOOL],
  };
});
```

当 AI 客户端发送 `listTools` 请求时，这个处理函数会被调用，它会返回一个包含 `WEB_SEARCH_TOOL` 和 `LOCAL_SEARCH_TOOL` 定义的列表。

**3. 处理 `callTool` 请求:**

这是工具的核心逻辑所在。服务器需要根据客户端请求中指定的工具名称和参数，执行相应的操作。

```typescript
// 导入 SDK 和请求模式
import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { CallToolRequestSchema } from "@modelcontextprotocol/sdk/types.js";
// ... (其他导入和工具定义) ...

// (这是一个简化的辅助函数，用于检查参数类型)
function isBraveWebSearchArgs(args: unknown): args is { query: string; count?: number } {
  return (
    typeof args === "object" && args !== null && "query" in args && typeof (args as any).query === 'string'
  );
}

// (实际执行 Brave Web 搜索的函数，这里省略细节)
async function performWebSearch(query: string, count: number = 10, offset: number = 0): Promise<string> {
  // ... (调用 Brave API, 处理结果, 返回字符串) ...
  console.error(`执行网页搜索: ${query}, 数量: ${count}`);
  // (实际代码会调用 fetch 与 Brave API 交互)
  return `关于 "${query}" 的搜索结果...`; // 简化返回
}

// (类似的函数用于本地搜索 performLocalSearch)

// 注册处理 callTool 请求的函数
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  try {
    // 从请求中解构出工具名称和参数
    const { name, arguments: args } = request.params;

    if (!args) {
      throw new Error("No arguments provided");
    }

    // 根据工具名称执行不同的逻辑
    switch (name) {
      case "brave_web_search": {
        // 验证参数是否符合预期 (实际代码会更严格)
        if (!isBraveWebSearchArgs(args)) {
          throw new Error("Invalid arguments for brave_web_search");
        }
        const { query, count = 10 } = args; // 使用默认值
        // 调用实际执行搜索的函数
        const results = await performWebSearch(query, count);
        // 返回成功结果
        return {
          content: [{ type: "text", text: results }], // 将结果包装在 content 数组中
          isError: false,                          // 标记为非错误
        };
      }

      case "brave_local_search": {
        // ... (处理本地搜索工具调用的逻辑) ...
        return { /* ... */ };
      }

      // 如果工具名称未知
      default:
        return {
          content: [{ type: "text", text: `Unknown tool: ${name}` }],
          isError: true, // 标记为错误
        };
    }
  } catch (error) { // 捕获处理过程中可能发生的任何错误
    // 返回错误结果
    return {
      content: [
        {
          type: "text",
          text: `Error: ${error instanceof Error ? error.message : String(error)}`,
        },
      ],
      isError: true, // 标记为错误
    };
  }
});
```

这个处理函数：

1.  接收 `callTool` 请求。
2.  提取工具名称 (`name`) 和参数 (`arguments`)。
3.  使用 `switch` 语句根据 `name` 分发到不同的处理逻辑。
4.  对于 `brave_web_search`：
    *   验证 `arguments` 的基本结构。
    *   调用 `performWebSearch` 函数执行实际的搜索。
    *   将返回的搜索结果字符串包装成 `{ type: "text", text: ... }` 的格式，放入 `content` 数组。
    *   构建并返回一个成功的响应对象，其中 `isError` 为 `false`。
5.  如果工具名称未知或处理过程中发生错误，则捕获错误并返回一个包含错误信息的响应对象，其中 `isError` 为 `true`。

## 内部实现：工具调用流程

让我们梳理一下当 AI 调用一个工具时，幕后发生了什么。

**非代码流程：**

1.  **AI 决策：** AI 根据用户请求或自身任务目标，决定需要执行某个操作（例如，进行网页搜索）。
2.  **工具选择：** AI 查找已连接的服务器，找到提供合适工具（例如，`brave-search` 服务器的 `brave_web_search` 工具）的服务器。
3.  **构造请求：** AI 根据工具的 `inputSchema` 构造 `callTool` 请求，包含工具名称和必要的参数（例如，`{ "name": "brave_web_search", "arguments": { "query": "MCP 是什么？" } }`）。
4.  **发送请求：** AI 通过 MCP 连接将 JSON-RPC 请求发送给 `brave-search` 服务器。
5.  **服务器接收与解析：** 服务器接收到请求，解析 JSON，识别出是 `callTool` 方法。
6.  **查找处理函数：** 服务器查找注册用来处理 `callTool` 请求的函数（即我们上面看到的 `server.setRequestHandler(CallToolRequestSchema, ...)`）。
7.  **执行处理函数：** 服务器调用该函数，并将请求参数传递给它。
8.  **工具逻辑执行：** 处理函数内部：
    *   根据工具名称 (`brave_web_search`) 找到具体实现。
    *   验证传入的 `arguments` 是否符合 `inputSchema`。
    *   执行核心操作（调用 `performWebSearch`，该函数内部会与 Brave Search API 通信）。
    *   获取操作结果（搜索结果字符串）。
9.  **构造响应：** 处理函数将结果包装成 MCP 响应格式（例如，`{ "content": [{ "type": "text", "text": "搜索结果..." }], "isError": false }`）。
10. **发送响应：** 服务器通过 MCP 连接将 JSON-RPC 响应发送回 AI 客户端。
11. **AI 处理响应：** AI 接收响应，解析 JSON，提取 `content` 中的信息，并将其用于生成回复或进行下一步操作。

**时序图示例 (`brave_web_search`):**

```mermaid
sequenceDiagram
    participant 用户
    participant Claude (AI 客户端)
    participant BraveSearch服务器
    participant BraveSearchAPI (外部)

    用户->>Claude (AI 客户端): "MCP 是什么？"
    Claude (AI 客户端)->>Claude (AI 客户端): 决定使用 brave_web_search 工具
    Claude (AI 客户端)->>BraveSearch服务器: MCP 请求 (callTool: brave_web_search, args: {query: "MCP 是什么？"})
    BraveSearch服务器->>BraveSearch服务器: 查找并执行 callTool 处理函数
    BraveSearch服务器->>BraveSearch服务器: 验证参数, 调用 performWebSearch
    BraveSearch服务器->>BraveSearchAPI (外部): 发起搜索请求 (query="MCP 是什么？")
    BraveSearchAPI (外部)-->>BraveSearch服务器: 返回搜索结果数据
    BraveSearch服务器->>BraveSearch服务器: 格式化结果为文本
    BraveSearch服务器->>Claude (AI 客户端): MCP 响应 (content: [{type: "text", text: "搜索结果..."}], isError: false)
    Claude (AI 客户端)->>用户: "根据 Brave 搜索结果，MCP 是模型上下文协议..."
```

这个图表清晰地展示了从用户提问到 AI 调用工具、服务器与外部 API 交互，最终返回结果给用户的完整流程。

## 总结

在本章中，我们深入了解了 MCP 服务器提供的核心能力之一：**MCP 工具**。我们学到了：

*   MCP 工具是服务器提供的具体、可执行的操作，就像工具箱里的电动工具。
*   每个工具都有明确的名称、描述、输入（`inputSchema`）和输出。
*   AI 客户端通过 `listTools` 发现服务器提供的工具。
*   AI 客户端通过 `callTool` 请求来调用特定的工具，并提供所需的参数。
*   服务器负责验证输入、执行工具对应的安全操作，并将结果返回给 AI。
*   这个机制使得 AI 能够在不直接访问外部系统的情况下，安全、可控地完成各种任务。

理解了 MCP 工具之后，我们将在下一章探讨 MCP 服务器提供的另一种重要能力：[MCP 资源](03_mcp_资源_.md)，了解服务器如何向 AI 提供结构化的信息片段。

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)