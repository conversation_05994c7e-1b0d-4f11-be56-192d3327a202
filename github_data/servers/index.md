# Tutorial: servers

这个项目 (`servers`) 提供了一系列遵循 **模型上下文协议 (MCP)** 的*参考服务器实现*。可以把它想象成一个 AI 助手的工具箱，其中每个服务器（例如 `文件系统服务器`、`Git 服务器`、`数据库服务器` 或 `API 集成服务器`）都专注于特定领域，充当一个专门的服务台。这些服务器通过提供 *工具*（具体操作）、*资源*（数据或上下文）和 *提示*（交互起点），让 AI 能够安全、可控地与外部世界互动或访问信息。项目还包含了一个 `Everything 服务器`，作为 MCP 功能的“样品间”，用于测试和演示协议的各种特性。许多服务器的运行都需要进行*认证与配置*。


**Source Repository:** [None](None)

```mermaid
flowchart TD
    A0["MCP 服务器
"]
    A1["MCP 工具
"]
    A2["MCP 资源
"]
    A3["MCP 提示
"]
    A4["文件系统服务器
"]
    A5["Git 服务器
"]
    A6["数据库服务器 (SQLite 示例)
"]
    A7["API 集成服务器 (GitHub 示例)
"]
    A8["Everything 服务器
"]
    A9["认证与配置
"]
    A0 -- "提供工具" --> A1
    A0 -- "提供资源" --> A2
    A0 -- "提供提示" --> A3
    A4 -- "实现" --> A0
    A5 -- "实现" --> A0
    A6 -- "实现" --> A0
    A7 -- "实现" --> A0
    A8 -- "实现" --> A0
    A0 -- "需要配置/认证" --> A9
    A8 -- "演示工具" --> A1
    A8 -- "演示资源" --> A2
    A8 -- "演示提示" --> A3
```

## Chapters

1. [MCP 服务器
](01_mcp_服务器_.md)
2. [MCP 工具
](02_mcp_工具_.md)
3. [MCP 资源
](03_mcp_资源_.md)
4. [MCP 提示
](04_mcp_提示_.md)
5. [Everything 服务器
](05_everything_服务器_.md)
6. [认证与配置
](06_认证与配置_.md)
7. [文件系统服务器
](07_文件系统服务器_.md)
8. [Git 服务器
](08_git_服务器_.md)
9. [数据库服务器 (SQLite 示例)
](09_数据库服务器__sqlite_示例__.md)
10. [API 集成服务器 (GitHub 示例)
](10_api_集成服务器__github_示例__.md)


---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)