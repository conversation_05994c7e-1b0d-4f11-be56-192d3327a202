# Chapter 7: 文件系统服务器


在上一章 [认证与配置](06_认证与配置_.md) 中，我们讨论了如何安全地给 MCP 服务器传递“钥匙”（认证信息）和“说明书”（配置信息）。现在，让我们来看一个具体的服务器实现，它非常依赖于我们刚刚学到的配置概念——**文件系统服务器**。

## 动机：让 AI 安全地操作你的本地文件

想象一下，你希望你的 AI 助手帮你做一些涉及本地文件的事情：

*   “帮我看看 `/home/<USER>/projects/my_project/README.md` 文件里写了什么？”
*   “在 `/home/<USER>/documents/notes/` 目录下创建一个名为 `meeting_summary.txt` 的新文件。”
*   “搜索 `/home/<USER>/projects/` 目录下所有包含 ‘TODO’ 的 Python 文件。”

直接让 AI 访问你的整个文件系统就像给一个实习生你办公室所有文件柜的万能钥匙——太危险了！它可能会意外删除重要文件、读取隐私信息，或者修改系统配置。我们需要一种更安全的方式。

**文件系统服务器** 就是这个安全的解决方案。它就像一个只能访问特定文件柜（允许的目录）的图书管理员机器人。AI 助手可以指示它读取、写入、编辑、移动文件，或创建、列出、搜索目录，但所有操作都严格限制在你预先设定的安全范围内。

## 核心概念：沙盒 (Sandbox)

文件系统服务器最重要的特性是它在一个严格定义的 **“沙盒”** 中运行。这个沙盒就是你在配置服务器时指定的一组**允许访问的目录**。

**打个比方：**

图书管理员机器人（文件系统服务器）被告知，它**只能**在图书馆的“技术书籍区”和“杂志阅览区”（允许的目录）活动。它绝对不能进入“馆长办公室”或“珍本典藏室”（不允许的目录）。任何试图访问这些禁区的指令都会被机器人拒绝。

![沙盒图示](https://modelcontextprotocol.io/_next/image?url=%2Fassets%2Fimages%2Ffilesystem-light-09e2fa17547602f2b187064cc2bf7438.png&w=2048&q=75)
*（图片来源：modelcontextprotocol.io）*

这种沙盒机制确保了即使 AI 助手收到了一个恶意的或错误的指令（比如“删除 /etc 目录”），文件系统服务器也会因为目标路径不在允许的沙盒范围内而拒绝执行，从而保护了你的系统安全。

## 如何使用：配置沙盒

要使用文件系统服务器，你首先需要在你的 AI 助手客户端（如 Claude 桌面版）的配置文件中告诉它两件事：

1.  如何启动文件系统服务器。
2.  哪些目录是允许访问的（即定义沙盒）。

这正是我们在上一章 [认证与配置](06_认证与配置_.md) 中讨论的**命令行参数**配置的应用场景。

以下是如何在 Claude 桌面版的 `claude_desktop_config.json` 文件中配置的示例：

### 使用 NPX（适用于 Node.js 环境）

这是最简单的方式，如果你本地安装了 Node.js 和 npm/npx。

```json
{
  "mcpServers": {
    "filesystem": { // 给服务器起个名字，比如 "filesystem"
      "command": "npx", // 使用 npx 命令启动
      "args": [        // 传递给 npx 的参数列表
        "-y",          // npx 参数：自动安装依赖
        "@modelcontextprotocol/server-filesystem", // 文件系统服务器的 npm 包名
        // ↓↓↓ 定义沙盒：允许访问的目录列表 ↓↓↓
        "/home/<USER>/projects", // 允许访问用户的 projects 目录
        "/tmp/shared_files"      // 也允许访问 /tmp/shared_files 目录
        // ↑↑↑ 沙盒定义结束 ↑↑↑
      ]
      // 注意：通常不需要 "env" 字段，因为目录路径不是敏感信息
    }
    // ... 其他服务器配置 ...
  }
}
```

**解释：**

*   `command`: "npx" 告诉客户端使用 npx 来运行服务器。
*   `args`: 这是一个数组，包含了要传递给 `npx` 命令的所有参数。
    *   前两个参数 `-y` 和 `@modelcontextprotocol/server-filesystem` 是告诉 npx 运行哪个包。
    *   **关键在于**，后面的参数 `/home/<USER>/projects` 和 `/tmp/shared_files` 就是你指定的**允许目录**。文件系统服务器启动时会读取这些参数，并将它们设置为自己的操作范围（沙盒）。

### 使用 Docker（适用于容器化环境）

如果你更喜欢使用 Docker，配置会略有不同，你需要将本地目录挂载到容器内部。

```json
{
  "mcpServers": {
    "filesystem": {
      "command": "docker", // 使用 docker 命令启动
      "args": [
        "run",
        "-i", // 允许交互式输入
        "--rm", // 容器退出后自动删除
        // ↓↓↓ 将本地目录挂载到容器内的 /projects 目录下 ↓↓↓
        "--mount", "type=bind,src=/home/<USER>/projects,dst=/projects/my_projects", // 本地目录 -> 容器内路径
        "--mount", "type=bind,src=/tmp/shared_files,dst=/projects/shared,ro", // 挂载为只读 (ro)
        // ↑↑↑ 挂载定义结束 ↑↑↑
        "mcp/filesystem", // Docker 镜像名称
        // ↓↓↓ 告知服务器在容器内的哪个目录操作 (沙盒) ↓↓↓
        "/projects" // 服务器只能访问容器内的 /projects 目录及其子目录
        // ↑↑↑ 沙盒定义结束 ↑↑↑
      ]
    }
  }
}
```

**解释：**

*   `--mount`: 这个 Docker 参数将你本地的目录（`src=...`）映射到 Docker 容器内部的一个路径（`dst=...`）。所有你希望服务器能访问的本地目录都需要这样挂载。`ro` 表示只读挂载。
*   `mcp/filesystem`: 这是文件系统服务器的 Docker 镜像名称。
*   `/projects`: 这是传递给容器内文件系统服务器的**命令行参数**，告诉它在容器内部，`/projects` 目录是允许操作的根目录（沙盒）。服务器将只能看到和操作通过 `--mount` 挂载进来的、位于容器内 `/projects` 下的文件和目录。

配置完成后，当你启动 AI 助手时，它就会根据这些信息启动文件系统服务器，并确保它只在指定的沙盒内运行。

## 如何使用：调用工具

一旦服务器配置好并运行起来，AI 助手就可以通过调用它的 [MCP 工具](02_mcp_工具_.md) 来执行文件操作了。文件系统服务器提供了一系列有用的工具（具体列表可以在 `src/filesystem/README.md` 中找到），以下是一些关键工具的中文说明：

*   `read_file` (读取文件): 读取指定路径的**单个**文件的全部内容。
    *   输入: `{ "path": "文件的路径" }`
    *   输出: 文件内容（文本）
*   `write_file` (写入文件): 在指定路径创建新文件或**覆盖**已有文件。**（请谨慎使用！）**
    *   输入: `{ "path": "文件路径", "content": "要写入的内容" }`
    *   输出: 操作成功的消息
*   `edit_file` (编辑文件): 对文件进行更精细的编辑，例如查找并替换特定行。支持“试运行”（dry run）来预览更改。
    *   输入: `{ "path": "文件路径", "edits": [{"oldText": "旧内容", "newText": "新内容"}, ...], "dryRun": true/false }`
    *   输出: 更改的差异（diff）或操作成功的消息
*   `list_directory` (列出目录): 列出指定目录下的所有文件和子目录，并用 `[FILE]` 或 `[DIR]` 标记类型。
    *   输入: `{ "path": "目录路径" }`
    *   输出: 目录内容列表（文本）
*   `create_directory` (创建目录): 创建一个新的目录，如果父目录不存在也会一并创建。
    *   输入: `{ "path": "要创建的目录路径" }`
    *   输出: 操作成功的消息
*   `move_file` (移动/重命名文件或目录): 将文件或目录从源路径移动到目标路径。
    *   输入: `{ "source": "源路径", "destination": "目标路径" }`
    *   输出: 操作成功的消息
*   `search_files` (搜索文件): 在指定目录下递归地搜索文件名或目录名包含特定模式的文件或目录。
    *   输入: `{ "path": "起始目录", "pattern": "搜索模式", "excludePatterns": ["排除模式1", ...] }`
    *   输出: 匹配的文件/目录路径列表（文本）
*   `get_file_info` (获取文件信息): 获取文件或目录的元数据，如大小、创建时间、修改时间、权限等。
    *   输入: `{ "path": "文件或目录路径" }`
    *   输出: 文件信息的详细描述（文本）
*   `list_allowed_directories` (列出允许的目录): **这个工具非常有用！** 它会返回服务器配置时被允许访问的所有目录列表。你可以用它来确认沙盒的范围。
    *   输入: 无
    *   输出: 允许的目录列表（文本）

## 解决用例：读取 README 文件

现在，让我们回到最初的用例：让 AI 助手读取 `/home/<USER>/projects/my_project/README.md` 文件。假设你已经按照上面的 NPX 示例配置了客户端，允许访问 `/home/<USER>/projects` 目录。

1.  **你向 AI 提问:** “请告诉我 `/home/<USER>/projects/my_project/README.md` 文件的内容。”
2.  **AI 决定:** AI 助手分析你的请求，识别出需要读取文件。它知道自己连接了一个名为 `filesystem` 的服务器，并且这个服务器有一个 `read_file` 工具。
3.  **AI 发送请求:** AI 助手向 `filesystem` 服务器发送一个 `callTool` [MCP 请求](01_mcp_服务器_.md)，内容大致如下：
    ```json
    {
      "method": "callTool",
      "params": {
        "name": "read_file", // 要调用的工具名
        "arguments": {       // 传递给工具的参数
          "path": "/home/<USER>/projects/my_project/README.md"
        }
      }
    }
    ```
4.  **服务器接收请求:** `filesystem` 服务器收到这个 JSON 请求。
5.  **服务器验证路径 (关键步骤):** 服务器提取出请求的路径 `/home/<USER>/projects/my_project/README.md`。它**检查**这个路径是否位于启动时配置的**允许目录** (`/home/<USER>/projects`) 之内。
    *   在这个例子中，路径是合法的，因为它在允许的 `/home/<USER>/projects` 目录下。验证通过！
    *   如果用户请求的是 `/etc/passwd`，服务器会发现这个路径不在允许的目录列表中，验证会**失败**。
6.  **服务器执行操作:** 验证通过后，服务器使用操作系统的文件系统功能（Node.js 的 `fs` 模块）来读取 `/home/<USER>/projects/my_project/README.md` 文件的内容。
7.  **服务器发送响应:** 服务器将读取到的文件内容打包成一个成功的 MCP 响应，发送回 AI 助手：
    ```json
    {
      "result": {
        "content": [{ "type": "text", "text": "这是 README 文件的内容..." }],
        "isError": false
      }
    }
    ```
    *   如果第 5 步验证失败，或者读取文件时发生其他错误，`result` 中 `isError` 会是 `true`，`content` 会包含错误信息。
8.  **AI 呈现结果:** AI 助手收到响应，提取出文件内容，并将其展示给你。

通过这个流程，AI 成功地读取了文件，但所有的操作都经过了文件系统服务器的安全检查，确保了操作始终在预设的沙盒范围内进行。

## 内部实现：沙盒是如何工作的？

让我们更深入地了解文件系统服务器内部是如何实现这种安全性的。

### 非代码流程

1.  **启动与配置:**
    *   AI 助手客户端根据配置文件，使用 `npx` 或 `docker` 启动文件系统服务器进程。
    *   关键的**命令行参数**（允许的目录列表）被传递给新启动的服务器进程。例如，`['/home/<USER>/projects', '/tmp/shared']`。
2.  **服务器初始化:**
    *   文件系统服务器进程启动后，首先解析传递给它的命令行参数。
    *   它将这些路径**标准化**（例如，解析 `~` 为家目录，统一路径分隔符）并存储在一个内部列表 `allowedDirectories` 中。这个列表定义了沙盒的边界。
3.  **接收工具调用请求:**
    *   服务器通过 MCP 连接接收到来自 AI 助手的 `callTool` 请求（比如 `read_file`）。
4.  **解析目标路径:**
    *   服务器从请求的 `arguments` 中提取出目标文件或目录的路径（例如，`"/home/<USER>/projects/my_project/README.md"`）。
5.  **路径验证 (核心安全检查):**
    *   服务器调用一个内部的 `validatePath` 函数。
    *   这个函数首先将请求的路径也进行**标准化和绝对化**，确保路径格式一致。
    *   然后，它**遍历**内部存储的 `allowedDirectories` 列表。
    *   它检查标准化的请求路径是否**以任何一个**允许的目录路径开头。
    *   **同时**，它还需要处理**符号链接 (symlinks)**，确保符号链接指向的目标也在允许的目录下。（这是为了防止有人创建一个指向 `/etc` 的符号链接放在允许的目录下来绕过检查）。
    *   如果请求路径（及其真实路径，如果是符号链接）位于**至少一个**允许的目录下，验证通过。
    *   如果请求路径不在**任何**允许的目录下，或者符号链接指向了不允许的位置，验证**失败**。
6.  **执行或拒绝:**
    *   如果 `validatePath` 成功，服务器继续执行对应的文件系统操作（使用 Node.js 的 `fs` 模块，如 `fs.readFile`, `fs.writeFile` 等）。
    *   如果 `validatePath` 失败，服务器**立即停止**，并构造一个包含“访问被拒绝 (Access denied)”错误的 MCP 响应。
7.  **返回结果:**
    *   服务器将操作结果（文件内容、成功消息或错误信息）打包成 MCP 响应发送回 AI 助手。

### 时序图示例 (读取文件)

```mermaid
sequenceDiagram
    participant 用户
    participant AI助手 (客户端)
    participant 文件系统服务器 (进程)
    participant 操作系统 (FS)

    用户 ->> AI助手 (客户端): "读 /projects/my_project/README.md"
    AI助手 (客户端) ->> 文件系统服务器 (进程): MCP 请求 (callTool: read_file, path: "/projects/my_project/README.md")
    文件系统服务器 (进程) ->> 文件系统服务器 (进程): 解析路径 "/projects/my_project/README.md"
    文件系统服务器 (进程) ->> 文件系统服务器 (进程): 调用 validatePath() 检查路径是否在 allowedDirectories ['/projects'] 内
    Note right of 文件系统服务器 (进程): 验证通过!
    文件系统服务器 (进程) ->> 操作系统 (FS): 请求读取文件 "/projects/my_project/README.md"
    操作系统 (FS) -->> 文件系统服务器 (进程): 返回文件内容
    文件系统服务器 (进程) ->> AI助手 (客户端): MCP 响应 (content: "文件内容...", isError: false)
    AI助手 (客户端) ->> 用户: "这是文件内容：..."
```

这个图表清晰地展示了服务器在执行文件操作前，必须先进行路径验证的关键步骤。

### 代码实现概览

文件系统服务器的核心逻辑位于 `src/filesystem/index.ts` 文件中。以下是一些关键部分的简化代码示例，帮助理解其内部工作：

**1. 解析命令行参数 (获取允许目录)**

服务器启动时，首先要做的是获取定义沙盒的命令行参数。

```typescript
// index.ts

// process.argv 是一个包含所有命令行参数的数组
// 例如: ['node', 'script.js', '/home/<USER>/projects', '/tmp/shared']
const args = process.argv.slice(2); // 获取 'script.js' 之后的所有参数

if (args.length === 0) {
  // 如果没有提供任何目录，打印错误并退出
  console.error("错误：至少需要提供一个允许访问的目录作为命令行参数！");
  process.exit(1);
}

// 扩展路径中的 '~' 并解析为绝对、标准化的路径
const allowedDirectories = args.map(dir =>
  normalizePath(path.resolve(expandHome(dir)))
);

console.error("服务器启动，允许访问的目录:", allowedDirectories);
```

这段代码读取命令行传入的目录列表，进行标准化处理后，存储在 `allowedDirectories` 数组中。这就是服务器的“沙盒”定义。

**2. 路径验证函数 (核心安全机制)**

这是服务器安全性的核心。每次文件操作前，都会用类似下面的逻辑来检查路径。

```typescript
// index.ts (validatePath 函数简化概念)

async function validatePath(requestedPath: string): Promise<string> {
  // 1. 标准化和绝对化请求的路径
  const absoluteRequested = path.resolve(expandHome(requestedPath));
  const normalizedRequested = normalizePath(absoluteRequested);

  // 2. 检查标准化后的路径是否以任何一个允许的目录开头
  const isAllowed = allowedDirectories.some(allowedDir =>
    normalizedRequested.startsWith(allowedDir + path.sep) || normalizedRequested === allowedDir
  );

  if (!isAllowed) {
    // 如果不以任何允许目录开头，直接拒绝
    throw new Error(`访问被拒绝 - 路径 ${normalizedRequested} 不在允许的目录内: ${allowedDirectories.join(', ')}`);
  }

  // 3. (简化) 处理符号链接，检查真实路径是否也在允许范围内
  try {
    const realPath = await fs.realpath(absoluteRequested); // 获取真实路径
    const normalizedReal = normalizePath(realPath);
    const isRealPathAllowed = allowedDirectories.some(allowedDir =>
        normalizedReal.startsWith(allowedDir + path.sep) || normalizedReal === allowedDir
    );
    if (!isRealPathAllowed) {
      throw new Error("访问被拒绝 - 符号链接指向了不允许的目录");
    }
    return realPath; // 返回真实路径供后续使用
  } catch (error) {
      // 如果文件不存在 (例如写入操作)，检查父目录是否允许
      // (此处省略了对父目录的检查逻辑，实际代码更复杂)
      if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
          // 假设父目录检查通过 (简化)
          return absoluteRequested; // 返回原始绝对路径
      }
      throw error; // 其他错误则抛出
  }
}
```

这个 `validatePath` 函数是保护用户文件系统的关键屏障。它确保任何文件操作的目标路径都经过严格检查，必须位于允许的沙盒范围内。

**3. 处理 `read_file` 工具调用**

当服务器收到 `read_file` 工具的调用请求时，它的处理函数会执行以下步骤：

```typescript
// index.ts (CallToolRequestSchema 处理函数简化片段)

server.setRequestHandler(CallToolRequestSchema, async (request) => {
  try {
    const { name, arguments: args } = request.params;

    if (name === "read_file") {
      // 1. 解析并验证输入参数 (确保有 'path')
      const parsedArgs = ReadFileArgsSchema.parse(args); // 使用 zod 进行验证

      // 2. 验证请求的路径是否在沙盒内 (!!! 核心安全检查 !!!)
      const validPath = await validatePath(parsedArgs.path);

      // 3. 如果验证通过，执行文件读取
      const content = await fs.readFile(validPath, "utf-8");

      // 4. 返回成功响应和文件内容
      return {
        content: [{ type: "text", text: content }],
      };
    }
    // ... 处理其他工具 ...

  } catch (error) {
    // 如果 validatePath 失败或 fs 操作出错，捕获错误
    const errorMessage = error instanceof Error ? error.message : String(error);
    // 返回错误响应
    return {
      content: [{ type: "text", text: `错误: ${errorMessage}` }],
      isError: true,
    };
  }
});
```

这个处理流程清晰地展示了先验证路径 (`validatePath`)，再执行操作 (`fs.readFile`) 的模式。其他工具如 `write_file`, `list_directory` 等的处理逻辑也遵循类似的模式：**先验证，后执行**。

**4. 处理 `list_allowed_directories` 工具调用**

这个工具的实现非常简单，它直接返回服务器启动时存储的允许目录列表。

```typescript
// index.ts (CallToolRequestSchema 处理函数简化片段)

    // ... 在 switch 或 if/else 语句中 ...
    if (name === "list_allowed_directories") {
      // 直接返回存储在 allowedDirectories 变量中的列表
      return {
        content: [{
          type: "text",
          text: `允许的目录:\n${allowedDirectories.join('\n')}`
        }],
      };
    }
```

调用这个工具可以帮助用户或 AI 助手了解当前文件系统服务器的操作边界在哪里。

## 总结

在本章中，我们深入了解了**文件系统服务器**，一个具体且非常有用的 MCP 服务器实现。我们学到了：

*   文件系统服务器允许 AI 助手安全地与你的本地文件系统交互。
*   它的核心安全机制是**沙盒**：服务器只能在启动时通过**命令行参数**配置的**允许目录**内操作。
*   你需要根据使用场景（NPX 或 Docker）在 AI 助手客户端正确配置这些允许的目录。
*   服务器提供了一系列文件操作 [MCP 工具](02_mcp_工具_.md)，如 `read_file`, `write_file`, `list_directory`, `search_files` 等。
*   服务器内部通过严格的**路径验证** (`validatePath`) 来确保所有操作都在沙盒范围内，防止越权访问。
*   `list_allowed_directories` 工具可以用来查看当前服务器的沙盒范围。

文件系统服务器展示了如何通过仔细的配置和内部安全检查，将强大的能力（如文件访问）以安全可控的方式提供给 AI 助手。

接下来，我们将探索另一个常用的服务器类型，它专注于与软件开发流程紧密相关的操作：[Git 服务器](08_git_服务器_.md)。我们将看看 AI 如何通过 MCP 与你的本地 Git 仓库进行交互。

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)