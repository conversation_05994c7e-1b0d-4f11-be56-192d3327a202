# Chapter 9: 数据库服务器 (SQLite 示例)


欢迎来到教程的第九章！在上一章 [Git 服务器](08_git_服务器_.md) 中，我们探讨了如何让 AI 助手安全地与你的本地 Git 仓库进行交互。现在，我们将目光投向数据存储和分析的核心——数据库。本章将介绍 **数据库服务器**，并以 **SQLite** 这个轻量级数据库为例，看看 AI 助手如何扮演一个数据库管理员的角色。

## 动机：让 AI 安全地与数据对话

想象一下，你正在进行一个数据分析项目，你希望 AI 助手能帮你处理一些数据库相关的任务：

*   “帮我查一下我们 `sales.db` 数据库里，华东地区的所有销售记录。”
*   “统计一下每个产品类别上个月的总销售额是多少？”
*   “在客户表 `customers` 里，添加一个新客户的信息。”
*   “分析销售数据，看看有没有什么值得注意的业务洞察？”

像直接操作文件系统或 Git 一样，让 AI 直接连接并操作生产数据库可能会带来风险：错误的查询可能导致性能问题，不当的修改可能损坏数据，甚至敏感信息可能被泄露。我们需要一个安全的中间层。

**数据库服务器** 就是这个安全的中间层。它扮演着一个专业的“数据库管理员”的角色，AI 助手可以通过它来提交 SQL 查询请求，而服务器则负责安全地执行这些请求，并将结果返回给 AI。

## 核心概念：数据库的看门人和执行者 (以 SQLite 为例)

数据库服务器就像一个经验丰富的数据库管理员 (DBA)，AI 助手不能直接闯入数据库机房。相反，AI 需要向这位管理员提交工作请求（SQL 查询）：

*   **读取请求 (`read_query`)**: “请帮我查阅这份销售报告 (执行 SELECT 查询)。”
*   **修改请求 (`write_query`)**: “请更新一下客户的联系方式 (执行 UPDATE 查询)。” 或 “请登记这笔新的交易 (执行 INSERT 查询)。”
*   **结构查询 (`list_tables`, `describe_table`)**: “我们的数据库里有哪些表格？” 或 “`products` 这个表格有哪些列？”
*   **创建请求 (`create_table`)**: “请帮我创建一个新的表格来存储员工信息。”

管理员（数据库服务器）会理解这些请求，验证它们（比如，`read_query` 工具只接受 SELECT 语句），然后使用标准的数据库接口（比如 Python 的 `sqlite3` 库）来安全地执行，并将结果或状态返回给 AI。

**为什么是 SQLite？**

SQLite 是一个非常流行的**嵌入式**数据库。与需要独立服务器进程的 MySQL 或 PostgreSQL 不同，SQLite 将整个数据库存储在**一个单独的文件**中。这使得它非常轻量、易于部署和管理，特别适合本地数据存储、测试或小型应用。我们的 `sqlite` 服务器示例就是围绕着操作这个数据库文件来构建的。

## 如何使用：配置数据库文件路径

要让 SQLite 服务器知道它应该操作**哪个**数据库文件，你需要在 AI 助手客户端（例如 Claude 桌面版）的配置文件中指定这个文件的路径。这正是我们在 [认证与配置](06_认证与配置_.md) 章节中学到的**命令行参数**的应用。

你需要使用 `--db-path` 参数来告诉服务器数据库文件的位置。

以下是几种常见的配置方式（参考 `src/sqlite/README.md`）：

### 使用 uvx（推荐，适用于 Python 环境）

如果你的环境安装了 `uv`（一个 Python 包管理器）。

```json
{
  "mcpServers": {
    "sqlite": { // 服务器名称
      "command": "uvx", // 使用 uvx 命令启动
      "args": [         // 传递给 uvx 的参数
        "mcp-server-sqlite", // SQLite 服务器的包名
        "--db-path",         // <= 指定数据库文件路径的参数名
        "~/my_analysis.db" // <= 替换成你想要使用的数据库文件路径 (可以是新文件)
      ]
    }
    // ... 其他服务器配置 ...
  }
}
```

**解释：**

*   `command`: "uvx" 指示客户端使用 `uvx` 运行服务器。
*   `args`: 列表中的 `mcp-server-sqlite` 是要运行的 Python 包。
*   **关键在于：** `--db-path ~/my_analysis.db` 这个命令行参数。`--db-path` 告诉 SQLite 服务器接下来的字符串是数据库文件的路径。服务器启动时会读取这个参数。如果文件不存在，它会自动创建。

### 使用 Docker

如果你使用 Docker，配置需要将包含数据库文件的本地目录挂载到容器内。

```json
{
  "mcpServers": {
    "sqlite": {
      "command": "docker",
      "args": [
        "run", "--rm", "-i", // Docker 参数
        // ↓↓↓ 将本地目录挂载到容器内的 /data 目录 ↓↓↓
        "--mount", "type=bind,src=/path/to/your/local/data/folder,dst=/data",
        // ↑↑↑ 挂载定义结束 ↑↑↑
        "mcp/sqlite", // Docker 镜像名
        // ↓↓↓ 告知服务器在容器内的哪个路径操作 ↓↓↓
        "--db-path", "/data/my_analysis.db" // 服务器操作容器内的 /data/my_analysis.db 文件
        // ↑↑↑ 数据库路径参数结束 ↑↑↑
      ]
    }
  }
}
```

**解释：**

*   `--mount`: 将你本地的数据目录 `src=...` 挂载到容器内部的 `dst=/data`。
*   `mcp/sqlite`: SQLite 服务器的 Docker 镜像。
*   `--db-path /data/my_analysis.db`: 告诉在**容器内部**运行的 SQLite 服务器，它应该使用 `/data/my_analysis.db` 这个路径作为数据库文件。

配置完成后，启动 AI 助手时，它就会启动 SQLite 服务器，并且服务器会“知道”要使用哪个数据库文件。

## 如何使用：调用工具

配置好服务器后，AI 助手就可以通过调用其提供的 [MCP 工具](02_mcp_工具_.md) 来与数据库交互了。SQLite 服务器提供了以下主要工具：

*   `read_query` (读取查询): 执行 `SELECT` 查询来从数据库读取数据。
    *   输入: `{ "query": "你的 SELECT SQL 语句" }`
    *   输出: 查询结果，通常是一个包含对象的列表，每个对象代表一行数据。
*   `write_query` (写入查询): 执行 `INSERT`, `UPDATE`, 或 `DELETE` 查询来修改数据。
    *   输入: `{ "query": "你的 INSERT/UPDATE/DELETE SQL 语句" }`
    *   输出: 一个包含受影响行数的对象，例如 `[{ "affected_rows": 1 }]`。
*   `create_table` (创建表格): 执行 `CREATE TABLE` 语句来创建新的表格。
    *   输入: `{ "query": "你的 CREATE TABLE SQL 语句" }`
    *   输出: 确认表格创建成功的消息。
*   `list_tables` (列出表格): 获取数据库中所有表格的名称列表。
    *   输入: 无
    *   输出: 一个包含表格名称列表的对象。
*   `describe_table` (描述表格): 获取指定表格的结构信息（列名和数据类型）。
    *   输入: `{ "table_name": "要描述的表格名称" }`
    *   输出: 一个包含列定义的列表的对象。
*   `append_insight` (追加洞察): **(独特功能!)** 将在数据分析过程中发现的业务洞察记录下来。
    *   输入: `{ "insight": "你发现的业务洞察描述" }`
    *   输出: 确认洞察已添加的消息。这个操作还会更新一个特殊的 [MCP 资源](03_mcp_资源_.md)。

## 解决用例：查询销售数据

让我们回到最初的用例：让 AI 助手查询华东地区的销售记录。假设你已经配置好了服务器，指定了数据库文件路径（比如 `~/sales_data.db`），并且这个数据库里有一个 `sales` 表，包含 `region`, `product`, `amount` 等列。

1.  **你向 AI 提问:** “帮我查一下 `sales` 表里 '华东' 地区的所有销售记录。”
2.  **AI 决定:** AI 分析请求，识别出需要执行只读查询。它知道连接了 `sqlite` 服务器，并有 `read_query` 工具可用。
3.  **AI 发送请求:** AI 向 `sqlite` 服务器发送 `callTool` [MCP 请求](01_mcp_服务器_.md)，内容大致如下：
    ```json
    {
      "method": "callTool",
      "params": {
        "name": "read_query", // 工具名
        "arguments": {        // 参数
          "query": "SELECT * FROM sales WHERE region = '华东'" // 要执行的 SQL 查询
        }
      }
    }
    ```
4.  **服务器接收请求:** `sqlite` 服务器收到这个 JSON 请求。
5.  **服务器验证与准备:** 服务器解析请求，知道要调用 `read_query`。它会检查传入的 `query` 是否真的是一个 `SELECT` 语句（因为 `read_query` 工具设计为只读）。验证通过。
6.  **服务器执行操作:** 服务器使用 Python 的 `sqlite3` 库连接到配置时指定的数据库文件 (`~/sales_data.db`)。然后，它执行收到的 SQL 查询 `SELECT * FROM sales WHERE region = '华东'`。
    ```python
    # server.py (概念简化)
    import sqlite3
    from contextlib import closing

    db_path = "~/sales_data.db" # 从配置中获取

    def execute_read_query(query: str) -> list[dict]:
        with closing(sqlite3.connect(db_path)) as conn:
            conn.row_factory = sqlite3.Row # 让结果可以按列名访问
            with closing(conn.cursor()) as cursor:
                cursor.execute(query)
                results = [dict(row) for row in cursor.fetchall()]
                return results

    # 在 call_tool 处理函数中...
    if name == "read_query":
        # ... (验证 query 是 SELECT) ...
        results = execute_read_query(arguments["query"])
        # ... (格式化 results 并返回) ...
    ```
7.  **服务器发送响应:** 服务器将从数据库查询到的结果（一个包含多行数据的列表）格式化后，打包成成功的 MCP 响应：
    ```json
    {
      "result": {
        "content": [{
          "type": "text",
          // 查询结果通常会格式化为易读的字符串或 JSON 字符串
          "text": "[{'region': '华东', 'product': 'A', 'amount': 100}, {'region': '华东', 'product': 'B', 'amount': 150}, ...]"
        }],
        "isError": false
      }
    }
    ```
    如果 SQL 语法错误或执行出错，`isError` 会是 `true`。
8.  **AI 呈现结果:** AI 助手收到响应，解析结果，并以易于理解的方式（比如表格）呈现给你。

通过这个流程，AI 助手利用数据库服务器提供的工具，安全、准确地完成了数据库查询任务。

## 独特之处：记录洞察与动态备忘录

SQLite 服务器有一个非常有特色的功能，结合了 `append_insight` 工具和 `memo://insights` 资源：

1.  **发现洞察:** 当你和 AI 一起分析数据（例如，通过多次调用 `read_query`）并发现了一些有价值的业务信息时（比如“华东地区的 A 产品销售额远超预期！”），你可以让 AI 使用 `append_insight` 工具记录下来。
    ```json
    // AI 发送的 MCP 请求
    {
      "method": "callTool",
      "params": {
        "name": "append_insight",
        "arguments": {
          "insight": "华东地区的 A 产品销售额远超预期！"
        }
      }
    }
    ```
2.  **服务器记录:** 服务器收到请求后，会将这个 `insight` 字符串添加到一个内部的列表中。
3.  **更新备忘录资源:** 同时，服务器会**自动更新**一个名为 `memo://insights` 的特殊 [MCP 资源](03_mcp_资源_.md)。这个资源的内容是一个根据所有已记录洞察动态生成的“业务洞察备忘录”（通常是纯文本）。
4.  **通知订阅者:** 如果 AI 助手之前订阅了这个 `memo://insights` 资源，服务器在更新资源内容后，会向助手发送一个 `notifications/resources/updated` 通知，告知备忘录已更新。
5.  **AI 获取最新备忘录:** 收到通知后，AI 助手可以再次使用 `readResource` 请求来获取 `memo://insights` 的最新内容，从而在后续的对话中利用这些积累的洞察。

这个机制将数据分析的过程和结果（洞察）紧密地结合起来，形成了一个动态的知识库。

## 内部实现：幕后发生的事情

让我们看看 SQLite 服务器（Python 实现）内部是如何运作的。

### 非代码流程

1.  **启动与配置:**
    *   AI 助手客户端使用 `uvx` 或 `docker` 启动 SQLite 服务器进程。
    *   `--db-path` 命令行参数（数据库文件路径）被传递给服务器。
2.  **服务器初始化 (`__init__.py` -> `server.py`):**
    *   服务器的启动脚本使用 `argparse` 解析命令行参数，获取数据库路径。
    *   核心服务器逻辑 (`main` 函数) 创建 `SqliteDatabase` 类的实例，传入数据库路径。
    *   `SqliteDatabase` 类在初始化时，会检查数据库文件和目录是否存在，如果不存在则创建，并准备好数据库连接（虽然连接通常在每次查询时建立和关闭）。它还初始化一个空的 `insights` 列表。
3.  **接收工具调用 (`server.py`):**
    *   服务器通过 MCP 连接接收到 `callTool` 请求（如 `read_query` 或 `append_insight`）。
4.  **处理工具调用 ( `@server.call_tool()` ):**
    *   根据工具名称 (`name`) 匹配到相应的处理逻辑。
    *   **验证:** 对输入参数进行验证（例如，`read_query` 只接受 SELECT，`append_insight` 需要 `insight` 参数）。
    *   **执行:**
        *   对于 SQL 工具 (`read_query`, `write_query`, `create_table` 等): 调用 `SqliteDatabase` 实例的 `_execute_query` 方法。该方法内部使用 Python 的 `sqlite3` 库连接数据库文件，执行 SQL 语句，处理事务（提交或回滚），并返回结果或影响的行数。
        *   对于 `append_insight`: 将 `insight` 参数添加到 `SqliteDatabase` 实例的 `insights` 列表中。然后，调用内部的 `_synthesize_memo` 方法（仅用于更新内部状态或日志，实际资源读取由 `read_resource` 处理）。最重要的是，它会调用 `server.request_context.session.send_resource_updated(AnyUrl("memo://insights"))` 来通知客户端 `memo://insights` 资源已更新。
5.  **返回结果:**
    *   将执行结果（查询数据、成功消息或错误信息）格式化为 MCP 响应，并发送回 AI 助手。

### 序列图示例 (调用 `read_query`)

```mermaid
sequenceDiagram
    participant AI助手 (客户端)
    participant SQLite服务器 (Python进程)
    participant SqliteDatabase 类
    participant sqlite3 库
    participant 数据库文件 (.db)

    AI助手 (客户端) ->> SQLite服务器 (Python进程): MCP 请求 (callTool: read_query, query: "SELECT ...")
    SQLite服务器 (Python进程) ->> SQLite服务器 (Python进程): 解析请求, 定位到 read_query 逻辑
    SQLite服务器 (Python进程) ->> SqliteDatabase 类: 调用 _execute_query("SELECT ...")
    SqliteDatabase 类 ->> sqlite3 库: 连接数据库文件
    SqliteDatabase 类 ->> sqlite3 库: 执行 "SELECT ..." 查询
    sqlite3 库 ->> 数据库文件 (.db): 读取数据
    数据库文件 (.db) -->> sqlite3 库: 返回数据
    sqlite3 库 -->> SqliteDatabase 类: 返回查询结果
    SqliteDatabase 类 ->> sqlite3 库: 关闭连接
    SqliteDatabase 类 -->> SQLite服务器 (Python进程): 返回结果列表
    SQLite服务器 (Python进程) ->> SQLite服务器 (Python进程): 格式化结果为 TextContent
    SQLite服务器 (Python进程) ->> AI助手 (客户端): MCP 响应 (content: "[{...}, {...}]", isError: false)
```

### 代码实现概览

核心逻辑位于 `src/sqlite/src/mcp_server_sqlite/server.py` 和 `__init__.py`。

**1. 解析数据库路径 (在 `__init__.py` 中)**

使用 `argparse` 获取 `--db-path`。

```python
# src/sqlite/src/mcp_server_sqlite/__init__.py (简化)
import argparse
from . import server
import asyncio

def main():
    """包的主要入口点"""
    parser = argparse.ArgumentParser(description='SQLite MCP 服务器')
    parser.add_argument('--db-path', # 参数名
                       default="./sqlite_mcp_server.db", # 默认值
                       help='SQLite 数据库文件的路径') # 帮助信息

    args = parser.parse_args() # 解析命令行传入的参数
    asyncio.run(server.main(args.db_path)) # 将路径传递给服务器主逻辑

# ...
```

**2. 数据库交互类 (在 `server.py` 中)**

`SqliteDatabase` 类封装了数据库操作和洞察管理。

```python
# src/sqlite/src/mcp_server_sqlite/server.py (简化)
import sqlite3
from contextlib import closing
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class SqliteDatabase:
    def __init__(self, db_path: str):
        # 处理路径 (~), 确保目录存在
        self.db_path = str(Path(db_path).expanduser())
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
        self.insights: list[str] = [] # 初始化洞察列表
        logger.info(f"数据库文件路径: {self.db_path}")

    def _execute_query(self, query: str, params: dict | None = None) -> list[dict]:
        """执行 SQL 查询并返回结果列表"""
        logger.debug(f"执行查询: {query[:100]}...") # 记录日志 (截断长查询)
        try:
            # 使用 closing 确保连接和游标被关闭
            with closing(sqlite3.connect(self.db_path)) as conn:
                conn.row_factory = sqlite3.Row # 让结果可以通过列名访问
                with closing(conn.cursor()) as cursor:
                    cursor.execute(query, params or {})
                    # 判断是读操作还是写操作
                    if query.strip().upper().startswith('SELECT'):
                        results = [dict(row) for row in cursor.fetchall()]
                        logger.debug(f"读取查询返回 {len(results)} 行")
                        return results
                    else: # INSERT, UPDATE, DELETE, CREATE 等
                        conn.commit() # 提交更改
                        affected = cursor.rowcount
                        logger.debug(f"写入查询影响了 {affected} 行")
                        return [{"affected_rows": affected}]
        except Exception as e:
            logger.error(f"数据库错误: {e}")
            raise # 重新抛出异常，让上层处理

    def _synthesize_memo(self) -> str:
        """生成业务洞察备忘录文本"""
        if not self.insights:
            return "尚未发现任何业务洞察。"
        # 格式化洞察列表
        insights_text = "\n".join(f"- {insight}" for insight in self.insights)
        memo = f"📊 业务洞察备忘录 📊\n\n关键洞察:\n{insights_text}"
        logger.debug(f"已生成包含 {len(self.insights)} 条洞察的备忘录")
        return memo

    def add_insight(self, insight: str):
        """添加一条业务洞察"""
        self.insights.append(insight)
        logger.info(f"已添加洞察: {insight}")
```

**3. 处理工具调用 (在 `server.py` 中)**

`@server.call_tool()` 装饰器下的函数处理具体的工具逻辑。

```python
# src/sqlite/src/mcp_server_sqlite/server.py (简化)
from mcp.server import Server
import mcp.types as types
from pydantic import AnyUrl

# ... (SqliteDatabase 类定义) ...

# main 函数中创建 db 实例
# async def main(db_path: str):
#    db = SqliteDatabase(db_path)
#    server = Server("sqlite-manager")
#    ...

# Call tool handler
@server.call_tool()
async def handle_call_tool(name: str, arguments: dict | None) -> list[types.Content]:
    try:
        # --- SQL 相关工具 ---
        if name in ["read_query", "write_query", "create_table"]:
            if not arguments or "query" not in arguments:
                raise ValueError("缺少 'query' 参数")
            query = arguments["query"]

            # 添加基本的 SQL 类型验证
            if name == "read_query" and not query.strip().upper().startswith("SELECT"):
                 raise ValueError("read_query 只允许 SELECT 语句")
            if name == "write_query" and query.strip().upper().startswith("SELECT"):
                 raise ValueError("write_query 不允许 SELECT 语句")
            if name == "create_table" and not query.strip().upper().startswith("CREATE TABLE"):
                 raise ValueError("create_table 只允许 CREATE TABLE 语句")

            results = db._execute_query(query) # 调用数据库类的方法
            return [types.TextContent(type="text", text=str(results))] # 返回结果

        # --- 表结构工具 ---
        elif name == "list_tables":
            results = db._execute_query("SELECT name FROM sqlite_master WHERE type='table'")
            return [types.TextContent(type="text", text=str(results))]
        elif name == "describe_table":
            # ... (类似地调用 _execute_query 执行 PRAGMA table_info) ...

        # --- 洞察工具 ---
        elif name == "append_insight":
            if not arguments or "insight" not in arguments:
                raise ValueError("缺少 'insight' 参数")
            insight = arguments["insight"]
            db.add_insight(insight) # 调用数据库类的方法添加洞察

            # !!! 发送资源更新通知 !!!
            await server.request_context.session.send_resource_updated(
                AnyUrl("memo://insights") # 告知哪个资源更新了
            )
            logger.info("已发送 memo://insights 更新通知")

            return [types.TextContent(type="text", text="洞察已添加到备忘录")]

        else:
            raise ValueError(f"未知的工具: {name}")

    except Exception as e: # 捕获所有可能的错误
        logger.error(f"处理工具 '{name}' 时出错: {e}")
        # 返回错误信息给客户端
        return [types.TextContent(type="text", text=f"错误: {str(e)}")]
```

**4. 处理资源读取 (在 `server.py` 中)**

`@server.read_resource()` 装饰器下的函数负责提供资源内容。

```python
# src/sqlite/src/mcp_server_sqlite/server.py (简化)
@server.read_resource()
async def handle_read_resource(uri: AnyUrl) -> str:
    logger.debug(f"处理 read_resource 请求，URI: {uri}")
    if str(uri) == "memo://insights":
        # 调用方法动态生成当前的备忘录内容
        memo_content = db._synthesize_memo()
        return memo_content # 直接返回备忘录文本
    else:
        logger.error(f"未知的资源 URI: {uri}")
        raise ValueError(f"未知的资源 URI: {uri}")
```

这些代码片段展示了 SQLite 服务器如何结合命令行配置、数据库操作库 (`sqlite3`) 和 MCP SDK 的功能（工具处理、资源处理、通知）来提供其服务。

## 总结

在本章中，我们深入了解了 **数据库服务器**，并以 **SQLite** 实现为例。我们学到了：

*   数据库服务器充当 AI 助手与数据库之间的安全**代理**，负责执行 SQL 查询。
*   SQLite 服务器特别适合处理本地的 **SQLite 数据库文件**。
*   你需要通过**命令行参数** (`--db-path`) 在客户端配置中指定数据库文件的路径。
*   服务器提供了一系列用于读 (`read_query`)、写 (`write_query`)、创建表格 (`create_table`) 和查询结构的 (`list_tables`, `describe_table`) [MCP 工具](02_mcp_工具_.md)。
*   SQLite 服务器还具有独特的 `append_insight` 工具和 `memo://insights` [MCP 资源](03_mcp_资源_.md)，用于在分析过程中记录和汇总业务洞察。
*   服务器内部使用标准的数据库库（如 `sqlite3`）来安全地执行操作。

数据库服务器为 AI 助手提供了强大的数据访问和处理能力，为进行数据分析、报告生成等任务打开了大门。

在下一章，我们将转向另一种常见的集成场景：与外部 Web API 的交互。我们将以 GitHub 为例，探讨如何构建一个 [API 集成服务器 (GitHub 示例)](10_api_集成服务器__github_示例__.md)。

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)