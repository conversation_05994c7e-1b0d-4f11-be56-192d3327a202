# Chapter 6: 认证与配置


在前一章 [Everything 服务器](05_everything_服务器_.md) 中，我们看到了一个展示 MCP 协议各种功能的“样品间”服务器。它帮助我们理解了 [MCP 工具](02_mcp_工具_.md)、[MCP 资源](03_mcp_资源_.md) 和 [MCP 提示](04_mcp_提示_.md) 在实践中如何运作。

但是，当我们开始使用那些需要访问 **私有数据** 或需要 **特定设置** 的服务器时，比如 [GitHub 服务器](10_api_集成服务器__github_示例__.md) 需要访问你的私有仓库，或者 [文件系统服务器](07_文件系统服务器_.md) 需要知道允许它访问哪个目录，我们该如何安全地提供这些“钥匙”和“说明书”呢？

这就是本章要讨论的核心问题：**认证** 和 **配置**。

## 动机：给服务器“钥匙”和“说明书”

想象一下我们之前提到的几个场景：

*   你希望 AI 助手通过 [GitHub 服务器](10_api_集成服务器__github_示例__.md) 帮你整理私有仓库 `my-secret-project` 里的 issues。GitHub 服务器怎么证明它有权访问你的私有仓库呢？它需要一把“钥匙”（比如你的个人访问令牌 Personal Access Token, PAT）。
*   你希望 AI 助手通过 [文件系统服务器](07_文件系统服务器_.md) 读取你电脑上 `/home/<USER>/documents` 目录下的文件，但**绝不能**访问其他目录。文件系统服务器怎么知道这个限制呢？它需要一份“操作说明”（指定允许访问的目录）。

直接把密钥或者路径写在 AI 助手的聊天窗口里显然是不安全的。我们需要一种机制，让你可以安全、可靠地将这些敏感信息（认证凭证）和运行参数（配置信息）传递给你启动的 MCP 服务器进程。

**认证与配置** 就是解决这个问题的机制。它们是许多 MCP 服务器运行的基础要求。

## 关键概念：认证与配置

虽然听起来有点技术化，但这两个概念其实很直观：

### 1. 认证 (Authentication)

> 认证是验证“你是谁”以及“你被允许做什么”的过程。在 MCP 的世界里，这通常意味着服务器需要向外部服务（如 GitHub API、Google Drive API）证明它有权代表你执行操作。

**打个比方：**

认证就像是进入一个需要许可才能进入的地方（比如一个只有会员才能进的俱乐部，或者你的私人工具棚）时，你需要出示的 **“钥匙”** 或 **“会员卡”**。

*   对于 [GitHub 服务器](10_api_集成服务器__github_示例__.md) 来说，这把“钥匙”通常是你的 **GitHub 个人访问令牌 (PAT)**。服务器拿着这个令牌去访问 GitHub API，GitHub 确认令牌有效且有相应权限后，才允许服务器操作。
*   对于 [Google Drive 服务器 (src/gdrive)](src/gdrive/README.md) 来说，这把“钥匙”可能是一组 **OAuth 凭据**。服务器通过 OAuth 流程获取访问令牌，然后用这个令牌向 Google Drive API 证明自己的身份和权限。
*   对于 [Brave Search 服务器 (src/brave-search)](src/brave-search/README.md) 来说，这把“钥匙”是 **Brave API 密钥**。

没有正确的“钥匙”（认证凭据），服务器就无法访问需要授权的服务或数据。

### 2. 配置 (Configuration)

> 配置是指为服务器设置运行参数或操作指南的过程。它告诉服务器应该如何工作，比如应该连接哪个数据库、允许访问哪些文件路径、使用哪个模型等。

**打个比方：**

配置就像是你给进入工具棚的工人（服务器进程）下达的 **“工作指令”** 或 **“设置说明书”**。

*   对于 [文件系统服务器](07_文件系统服务器_.md)，配置指定了它**唯一被允许**访问的目录，比如 `/home/<USER>/projects`。任何访问这个目录之外的操作都会被拒绝。
*   对于 [数据库服务器 (SQLite 示例)](09_数据库服务器__sqlite_示例__.md) 或 [PostgreSQL 服务器 (src/postgres)](src/postgres/README.md)，配置可能包括数据库文件的路径、数据库服务器的地址、用户名、密码（当然，密码也是一种认证信息！）。
*   对于 [API 集成服务器 (GitHub 示例)](10_api_集成服务器__github_示例__.md)，除了认证令牌，配置还可能包括 GitHub 企业版的 API 地址（如果不是公共版 github.com）。

配置让服务器的行为符合你的预期和安全要求。

## 如何传递认证和配置信息？

那么，AI 助手客户端（比如 Claude 桌面版）是如何将这些“钥匙”（认证信息）和“说明书”（配置信息）安全地传递给它启动的 MCP 服务器进程呢？主要有两种常见且标准的方式：

1.  **环境变量 (Environment Variables)**
2.  **命令行参数 (Command-Line Arguments)**

这两种方式都是在 AI 助手**启动** MCP 服务器进程时由 AI 助手设置的。让我们看看它们是如何工作的。

### 1. 环境变量 (Environment Variables)

> 环境变量是操作系统层面的一种机制，允许你在启动一个程序（进程）之前，为它设置一些变量。这些变量就像是贴在这个程序运行环境里的“小纸条”，程序内部可以读取它们。

**打个比方：**

想象 AI 助手（老板）要派一个工人（服务器进程）去特定的工具棚（比如 GitHub 工具棚）干活。老板在工人进去之前，偷偷地在工具棚的墙上贴了一张写有“工具棚钥匙（API Token）在这里：ABCDEFG”的纸条。只有进入这个工具棚的工人才能看到这张纸条。

**使用场景：** 环境变量非常适合传递 **敏感信息**，比如 API 密钥、访问令牌、密码等。因为这些变量通常不会在系统的进程列表中直接显示出来，相对更安全。

**如何在客户端配置？**

在 AI 助手客户端（如 Claude 桌面版）的配置文件中，通常会有一个 `env` 字段，允许你为特定的 MCP 服务器指定环境变量。

**示例：为 GitHub 服务器设置 PAT**

假设你想让 Claude 桌面版使用 [GitHub 服务器](10_api_集成服务器__github_示例__.md)，并且你的 GitHub PAT 是 `ghp_YOUR_SECRET_TOKEN`。你可以在 Claude 桌面的配置文件 (`claude_desktop_config.json`) 中这样设置：

```json
{
  "mcpServers": {
    "github": { // 服务器名称
      "command": "npx", // 启动命令
      "args": [        // 传递给 npx 的参数
        "-y",
        "@modelcontextprotocol/server-github" // GitHub 服务器包名
      ],
      "env": { // <= 重点在这里：设置环境变量
        "GITHUB_PERSONAL_ACCESS_TOKEN": "ghp_YOUR_SECRET_TOKEN" // 服务器内部会读取这个变量
      }
    }
    // ... 其他服务器配置 ...
  }
}
```

**解释：**

*   当 Claude 桌面版需要启动 `github` 服务器时，它会执行 `npx -y @modelcontextprotocol/server-github` 命令。
*   但在执行命令**之前**，它会先设置一个名为 `GITHUB_PERSONAL_ACCESS_TOKEN` 的环境变量，其值为你提供的令牌。
*   然后，启动的 GitHub 服务器进程就可以在其内部代码中通过 `process.env.GITHUB_PERSONAL_ACCESS_TOKEN` (在 Node.js 中) 或类似方式读取这个令牌，并用它来访问 GitHub API。

其他服务器也类似，比如 [Brave Search 服务器 (src/brave-search)](src/brave-search/README.md) 会读取 `BRAVE_API_KEY` 环境变量。

### 2. 命令行参数 (Command-Line Arguments)

> 命令行参数是你在启动一个程序时，直接跟在程序名称后面传递给程序的信息。

**打个比方：**

还是那个老板派工人去工具棚的例子。这次，老板不是贴纸条，而是在工人（服务器进程）进入工具棚的那一刻，直接**大声告诉**他：“你只能使用标记为‘项目A’的那个架子上的工具！” （对应于允许访问的目录）。

**使用场景：** 命令行参数通常用于传递 **非敏感的配置信息**，比如文件路径、开关选项、模式设置等。因为这些参数可能会在系统的进程列表中被看到。

**如何在客户端配置？**

在 AI 助手客户端的配置文件中，`args` 字段就用来定义要传递给服务器启动命令的命令行参数。

**示例：为文件系统服务器指定允许的目录**

假设你希望 [文件系统服务器](07_文件系统服务器_.md) 只能访问 `/home/<USER>/projects` 和 `/tmp/shared` 这两个目录。你可以在配置中这样设置：

```json
{
  "mcpServers": {
    "filesystem": { // 服务器名称
      "command": "npx", // 启动命令
      "args": [        // <= 重点在这里：传递命令行参数
        "-y",
        "@modelcontextprotocol/server-filesystem", // 文件系统服务器包名
        "/home/<USER>/projects",                   // 第一个允许的目录
        "/tmp/shared"                            // 第二个允许的目录
      ]
      // 注意：这里没有 "env" 字段，因为目录路径不是敏感信息
    }
    // ... 其他服务器配置 ...
  }
}
```

**解释：**

*   当 Claude 桌面版启动 `filesystem` 服务器时，它实际执行的命令是 `npx -y @modelcontextprotocol/server-filesystem /home/<USER>/projects /tmp/shared`。
*   `/home/<USER>/projects` 和 `/tmp/shared` 这两个字符串就作为命令行参数被传递给了服务器进程。
*   文件系统服务器进程启动后，在其内部代码中会解析这些命令行参数，得知只有这两个目录是允许访问的，并据此限制其操作范围。

类似地，[Git 服务器 (src/git)](src/git/README.md) 可以通过命令行参数 `--repository path/to/repo` 来指定要操作的本地仓库路径。

## 内部实现：服务器如何获取配置？

了解了客户端如何传递信息，我们再来看看服务器端（用 TypeScript 或 Python 编写）是如何接收和使用这些信息的。

**非代码流程：**

1.  **客户端读取配置：** AI 助手客户端（如 Claude 桌面版）首先读取其 JSON 配置文件，找到目标服务器（如 `github` 或 `filesystem`）的配置项。
2.  **准备启动命令：** 客户端根据配置中的 `command` 和 `args` 准备好要执行的命令（例如 `npx @modelcontextprotocol/server-github` 或 `npx @modelcontextprotocol/server-filesystem /path/to/allowed`）。
3.  **设置环境变量 (如有)：** 如果配置中有 `env` 块，客户端在操作系统层面为即将启动的子进程设置这些环境变量（例如 `GITHUB_PERSONAL_ACCESS_TOKEN="ghp_..."`）。
4.  **启动服务器进程：** 客户端执行准备好的命令，启动 MCP 服务器作为一个**子进程**。命令行参数（`args` 中的非命令部分，如 `/path/to/allowed`）会传递给这个子进程。
5.  **服务器进程启动：** MCP 服务器的代码开始执行。
6.  **读取环境变量：** 在服务器的初始化代码中，它会使用相应语言的标准库来读取**环境变量**。例如，读取 `process.env.GITHUB_PERSONAL_ACCESS_TOKEN` 或 `os.environ.get("BRAVE_API_KEY")`。它会将读取到的值（令牌、密钥）存储起来，以便后续与外部 API 通信时使用。
7.  **解析命令行参数：** 服务器代码还会解析启动时传递给它的**命令行参数**。例如，解析 `process.argv` 或使用 `argparse` 库来获取允许的目录列表或仓库路径。它会将这些配置值存储起来，用于后续操作的验证或执行。
8.  **使用配置运行：** 服务器现在拥有了执行任务所需的认证信息（来自环境变量）和操作参数（来自命令行参数），可以开始处理来自客户端的 [MCP 工具](02_mcp_工具_.md) 调用或 [MCP 资源](03_mcp_资源_.md) 请求了。

**时序图示例 (启动文件系统服务器):**

```mermaid
sequenceDiagram
    participant 客户端 (如 Claude 桌面)
    participant 操作系统
    participant 文件系统服务器进程

    客户端 (如 Claude 桌面) ->> 客户端 (如 Claude 桌面): 读取配置 (找到 filesystem 服务器, command='npx', args=['...', '/allowed/path'])
    客户端 (如 Claude 桌面) ->> 操作系统: 请求启动进程: 'npx ... /allowed/path'
    操作系统 ->> 文件系统服务器进程: 创建进程, 并将 '/allowed/path' 作为参数传递
    文件系统服务器进程 ->> 文件系统服务器进程: (在 Node.js 中) 读取 process.argv 获取参数
    文件系统服务器进程 ->> 文件系统服务器进程: 解析出允许的路径 '/allowed/path'
    文件系统服务器进程 ->> 文件系统服务器进程: 使用此路径配置内部访问控制
    Note over 文件系统服务器进程: 服务器现在只允许操作 '/allowed/path' 目录
```

**代码片段解析 (简化版)：**

*   **TypeScript (读取环境变量 - 概念示例):**
    很多服务器在初始化时会检查必要的环境变量。

    ```typescript
    // (概念来自 src/brave-search/index.ts 或 src/github/index.ts)

    // 在服务器启动的早期阶段
    const apiKey = process.env.BRAVE_API_KEY; // 读取环境变量
    const githubToken = process.env.GITHUB_PERSONAL_ACCESS_TOKEN;

    if (!apiKey && !githubToken) {
      // 如果必需的令牌/密钥不存在，则打印错误并可能退出
      console.error("错误：缺少必要的 API 密钥环境变量！");
      // process.exit(1); // 实际代码可能会退出
    }

    // 后续代码可以使用 apiKey 或 githubToken 来与外部 API 交互
    // 例如，在调用 fetch 时添加到请求头中
    // const headers = { 'Authorization': `Bearer ${githubToken}` };
    // fetch('https://api.github.com/...', { headers });
    ```
    这个片段展示了如何在 Node.js 服务器中读取环境变量，并检查它们是否存在。

*   **Python (读取环境变量 - 概念示例):**
    Python 服务器使用 `os` 模块。

    ```python
    # (概念来自 src/sentry/src/mcp_server_sentry/server.py)
    import os
    import sys

    # 在服务器启动时
    auth_token = os.environ.get("SENTRY_AUTH_TOKEN") # 读取环境变量

    if not auth_token:
        print("错误：未设置 SENTRY_AUTH_TOKEN 环境变量", file=sys.stderr)
        # sys.exit(1) # 实际代码可能会退出

    # 后续可以使用 auth_token 与 Sentry API 交互
    # headers = {"Authorization": f"Bearer {auth_token}"}
    # response = requests.get(url, headers=headers)
    ```
    这个 Python 片段功能类似，使用 `os.environ.get` 来安全地读取环境变量。

*   **TypeScript (读取命令行参数):**
    `process.argv` 是一个包含所有命令行参数的数组。第一个元素是 Node.js 可执行文件路径，第二个是脚本文件路径，之后是传递的参数。

    ```typescript
    // (概念来自 src/filesystem/index.ts)

    // process.argv 类似于 ['/usr/bin/node', '/path/to/script.js', '/allowed/path1', '/allowed/path2']
    const allowedPaths = process.argv.slice(2); // 获取从第三个元素开始的所有参数

    if (allowedPaths.length === 0) {
      console.error("错误：至少需要提供一个允许访问的目录作为命令行参数！");
      // process.exit(1);
    }

    console.log("服务器将限制访问以下目录:", allowedPaths);

    // 服务器内部逻辑会使用 allowedPaths 数组来检查文件操作是否合法
    function isPathAllowed(requestedPath: string): boolean {
      // 检查 requestedPath 是否在 allowedPaths 列表中的某个目录下
      // ... (省略具体实现) ...
      return true; // 简化
    }
    ```
    这个片段展示了如何从 `process.argv` 获取命令行参数，服务器可以用这些参数来配置其行为（如访问控制）。

*   **Python (使用 argparse 读取命令行参数):**
    Python 通常使用 `argparse` 模块来更优雅地处理命令行参数。

    ```python
    # (概念来自 src/git/src/mcp_server_git/server.py)
    import argparse
    import sys

    # 定义期望的参数
    parser = argparse.ArgumentParser(description="Git MCP 服务器")
    parser.add_argument(
        "--repository", # 参数名，例如 --repository path/to/repo
        type=str,       # 参数类型
        required=True,  # 这个参数是必需的
        help="要操作的 Git 仓库路径",
    )
    parser.add_argument(
        "--log-level",  # 另一个可选参数
        default="INFO", # 默认值
        help="日志级别 (DEBUG, INFO, WARNING, ERROR)"
    )

    # 解析实际传入的参数
    try:
      # args = parser.parse_args() # 实际代码使用这个
      # 为了演示，我们假设命令行是 '... --repository /my/repo'
      args = parser.parse_args(['--repository', '/my/repo']) # 模拟解析
    except SystemExit:
       sys.exit(1) # 如果参数错误，argparse 会自动退出

    # 使用解析后的参数
    repo_path = args.repository
    log_level = args.log_level

    print(f"配置：仓库路径 = {repo_path}, 日志级别 = {log_level}")

    # 服务器内部会使用 repo_path 来执行 git 命令
    # e.g., subprocess.run(['git', '-C', repo_path, 'status'])
    ```
    这个 Python 片段展示了使用 `argparse` 来定义和解析命令行参数，这使得处理参数更加健壮和用户友好。服务器随后可以使用 `args.repository` 等属性来获取配置值。

## 总结

在本章中，我们了解了为什么需要**认证**和**配置**，以及它们对于安全、正确地运行 MCP 服务器的重要性。我们学到了：

*   **认证** 是服务器用来向外部服务证明身份的“钥匙”（如 API 令牌）。
*   **配置** 是服务器运行所需的“说明书”（如允许的目录、数据库地址）。
*   AI 助手客户端通过两种主要方式将这些信息传递给它启动的服务器进程：
    *   **环境变量 (`env`)**: 通常用于传递**敏感**的认证信息（密钥、令牌）。
    *   **命令行参数 (`args`)**: 通常用于传递**非敏感**的配置信息（路径、选项）。
*   服务器在其启动代码中读取环境变量和解析命令行参数，以获取运行所需的凭证和设置。

理解了这些基础机制后，我们就能更好地配置和使用需要访问特定资源或具有特定行为限制的 MCP 服务器了。

接下来，我们将深入研究第一个具体的、非常有用的服务器实现，它大量运用了本章讨论的配置概念：[文件系统服务器](07_文件系统服务器_.md)。

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)