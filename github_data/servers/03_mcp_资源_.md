# Chapter 3: MCP 资源


在前一章 [MCP 工具](02_mcp_工具_.md) 中，我们了解了 MCP 服务器如何提供像电动工具一样的“操作”来让 AI 助手执行特定任务。但是，除了执行动作，AI 助手有时还需要一些背景信息或者“原材料”来更好地理解情况或完成更复杂的任务。这就是 **MCP 资源 (MCP Resource)** 登场的时候了。

## 动机：AI 如何获取上下文信息？

想象一下，你正在使用一个连接了 [SQLite 服务器](09_数据库服务器__sqlite_示例__.md) 的 AI 助手。在让它帮你写一个复杂的 SQL 查询之前，AI 可能需要先了解数据库里有哪些表，以及这些表的结构（即它们的列和数据类型）。它怎么获取这些信息呢？

或者，假设你正在进行一个数据分析项目。你希望有一个地方能实时记录分析过程中发现的“业务洞察”，并且 AI 助手可以随时查阅这个最新的“洞察备忘录”。

直接让 AI 访问数据库的元数据表或者维护一个共享文档可能并不方便或安全。我们需要一种标准化的方式，让 [MCP 服务器](01_mcp_服务器_.md) 能够向 AI 提供这些静态或动态的“信息片段”。

**MCP 资源** 就是为此设计的。它们是服务器提供的、可供 AI 读取的数据源或上下文信息。

## 什么是 MCP 资源？

> MCP 资源是 MCP 服务器提供的数据源或上下文信息。它们就像工具棚里的原材料或参考手册（例如，文件内容、数据库表的模式、项目列表、洞察备忘录）。AI 助手可以读取这些资源来获取执行任务所需的信息。某些资源还可以被订阅，以便在内容更新时接收通知，例如 SQLite 服务器的 `memo://insights` 资源。

**打个比方：**

再次回到我们的工具棚比喻：

*   [MCP 服务器](01_mcp_服务器_.md) 是工具棚。
*   [MCP 工具](02_mcp_工具_.md) 是工具棚里的电动工具（电钻、锯子），用来执行具体操作。
*   **MCP 资源** 则是工具棚里存放的**原材料**（木材、螺丝）或者**参考资料**（设计蓝图、操作手册、项目清单、备忘录）。

AI 助手（工匠）在执行任务时，不仅需要工具，有时也需要查阅这些资源：

*   在开始构建（写 SQL 查询）之前，可能需要查看数据库的**模式（Schema）**（设计蓝图）。
*   在进行分析时，可能需要查阅不断更新的**洞察备忘录（Insights Memo）**。
*   可能需要读取某个**配置文件**的内容。

**关键特性：**

1.  **信息提供 (Information Provider):** 资源的主要目的是提供数据或上下文，而不是执行操作。
2.  **唯一标识符 (URI):** 每个资源都有一个唯一的标识符，称为 URI (Uniform Resource Identifier)，类似于网址。例如，`postgres` 服务器可能提供 `postgres://mydatabase.com/public/users/schema` 来代表 `users` 表的模式；`sqlite` 服务器可能提供 `memo://insights` 代表洞察备忘录。
3.  **数据类型 (MIME Type):** 服务器会告知 AI 资源内容的格式，使用 MIME 类型来表示，例如 `text/plain` (纯文本)、`application/json` (JSON 数据)、`image/png` (PNG 图片)。
4.  **可订阅性 (Subscribability, 可选):** 某些资源的内容可能会变化（比如那个洞察备忘录）。服务器可以允许 AI “订阅”这些资源。一旦订阅，当资源内容更新时，服务器会主动发送通知给 AI，告知其内容已改变。

**资源的发现：**

和工具类似，AI 助手通过向服务器发送 `listResources` 请求来发现可用的资源。服务器会返回一个列表，包含每个资源的 URI、名称、描述和 MIME 类型等信息。

## 使用 MCP 资源解决用例

让我们看看 AI 如何使用资源来解决我们之前的用例。

**用例 1：获取数据库表模式**

假设我们有一个连接到 [PostgreSQL 服务器](09_数据库服务器__sqlite_示例__.md) (虽然标题是 SQLite，但 `src/postgres` 提供了 schema 作为资源的例子) 的 AI 助手，并且该服务器允许访问名为 `mydb` 的数据库。

1.  **AI 需求:** AI 需要了解 `mydb` 数据库中 `users` 表的结构。
2.  **发现资源:** AI 向 `postgres` 服务器发送 `listResources` 请求。服务器返回的列表中可能包含类似以下的资源信息：
    ```json
    {
      "uri": "postgres://mydb/public/users/schema", // 资源的唯一标识符
      "name": "\"users\" database schema",         // 资源的可读名称
      "mimeType": "application/json"              // 资源内容的格式
    }
    ```
3.  **读取资源:** AI 决定读取这个资源。它向 `postgres` 服务器发送一个 `readResource` 请求，指定要读取的 URI：
    ```json
    {
      "jsonrpc": "2.0",
      "method": "readResource", // 请求类型：读取资源
      "params": {
        "uri": "postgres://mydb/public/users/schema" // 要读取的资源 URI
      }
    }
    ```
4.  **服务器处理:** `postgres` 服务器收到请求。
    *   它解析 URI，识别出请求的是 `users` 表的模式。
    *   服务器查询数据库的元数据表 (例如 `information_schema.columns`) 来获取 `users` 表的列名和数据类型。
    *   服务器将查询结果格式化为 JSON。
5.  **MCP 响应:** 服务器将包含表模式的 JSON 数据包装在响应中发回给 AI：
    ```json
    {
      "jsonrpc": "2.0",
      "result": {
        "contents": [ // 内容是一个数组，通常只包含一个元素
          {
            "uri": "postgres://mydb/public/users/schema",
            "mimeType": "application/json",
            "text": "[{\"column_name\": \"id\", \"data_type\": \"integer\"}, {\"column_name\": \"username\", \"data_type\": \"character varying\"}, ...]" // 包含模式的 JSON 字符串
          }
        ]
      }
    }
    ```
6.  **AI 使用信息:** AI 收到响应，解析 JSON 数据，了解了 `users` 表的结构，现在可以更有根据地帮助用户构建 SQL 查询了。

**用例 2：访问并订阅洞察备忘录 (以 `sqlite` 服务器为例)**

假设 AI 正在使用 [SQLite 服务器](09_数据库服务器__sqlite_示例__.md) 进行数据分析，并使用 `append_insight` 工具不断记录发现。服务器提供了一个名为 `memo://insights` 的资源来汇总这些洞察。

1.  **AI 发现并读取:** AI 通过 `listResources` 发现 `memo://insights` (MIME 类型可能是 `text/plain`)，并可以通过 `readResource` 读取其当前内容（一个格式化的备忘录文本）。
2.  **订阅资源:** 由于这个备忘录会更新，AI 可能决定订阅它。AI 发送 `subscribe` 请求：
    ```json
    {
      "jsonrpc": "2.0",
      "method": "subscribe", // 请求类型：订阅资源
      "params": {
        "uri": "memo://insights" // 要订阅的资源 URI
      }
    }
    ```
3.  **服务器确认订阅:** 服务器记录下这个订阅，并返回成功响应。
4.  **内容更新与通知:** 当用户或其他操作（比如调用 `append_insight` 工具）导致备忘录内容改变时，`sqlite` 服务器内部会更新备忘录内容。然后，服务器会**主动**向所有订阅了 `memo://insights` 的客户端（包括我们的 AI 助手）发送一个 `notifications/resources/updated` 通知：
    ```json
    {
      "jsonrpc": "2.0",
      "method": "notifications/resources/updated", // 通知类型：资源已更新
      "params": {
        "uri": "memo://insights" // 哪个资源更新了
      }
    }
    ```
5.  **AI 重新读取:** AI 收到这个通知后，就知道 `memo://insights` 的内容已经变了。如果需要最新的内容，它可以再次发送 `readResource` 请求来获取更新后的备忘录。

通过这种方式，AI 可以访问到动态变化的信息，而不需要反复轮询检查。

## 代码示例：定义和处理资源

让我们看看服务器代码如何定义和处理资源。

**1. 定义资源 (响应 `listResources` 请求):**

服务器需要告诉客户端它提供了哪些资源。这通常在 `listResources` 请求的处理函数中完成。

*   **TypeScript 示例 (`src/postgres/index.ts` - 简化版):**
    ```typescript
    // 导入 SDK 类型
    import { ListResourcesRequestSchema } from "@modelcontextprotocol/sdk/types.js";
    import pg from "pg"; // 假设已连接到数据库

    // ... (服务器和数据库连接池设置) ...
    const pool = new pg.Pool(/* ... */);
    const resourceBaseUrl = new URL(/* ... */); // 基础 URL 用于构建资源 URI
    const SCHEMA_PATH = "schema"; // 路径的一部分

    // 注册 listResources 请求的处理函数
    server.setRequestHandler(ListResourcesRequestSchema, async () => {
      const client = await pool.connect();
      try {
        // 查询数据库获取所有表名
        const result = await client.query(
          "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'"
        );
        // 为每个表创建一个资源定义
        return {
          resources: result.rows.map((row) => ({
            // 构建唯一的 URI
            uri: new URL(`${row.table_name}/${SCHEMA_PATH}`, resourceBaseUrl).href,
            // 告知内容类型
            mimeType: "application/json",
            // 资源的可读名称
            name: `"${row.table_name}" database schema`,
          })),
        };
      } finally {
        client.release(); // 释放数据库连接
      }
    });
    ```
    这个处理函数查询数据库获取所有表名，然后为每个表创建一个资源对象，包含 URI、MIME 类型和名称，最后返回这个资源列表。

*   **Python 示例 (`src/sqlite/src/mcp_server_sqlite/server.py` - 简化版):**
    ```python
    # 导入 SDK 类型和 pydantic 用于 URL 处理
    import mcp.types as types
    from pydantic import AnyUrl
    import logging

    logger = logging.getLogger(__name__) # 日志记录器

    # ... (服务器设置) ...

    # 使用 @server.list_resources() 装饰器注册处理函数
    @server.list_resources()
    async def handle_list_resources() -> list[types.Resource]:
        logger.debug("处理 list_resources 请求") # 记录日志
        # 返回包含 memo://insights 资源的列表
        return [
            types.Resource(
                uri=AnyUrl("memo://insights"), # 资源的 URI
                name="Business Insights Memo",   # 资源名称
                description="A living document of discovered business insights", # 描述
                mimeType="text/plain",           # MIME 类型
            )
        ]
    ```
    这个 Python 处理函数更简单，它直接返回一个包含 `memo://insights` 资源定义的列表。

**2. 处理 `readResource` 请求:**

当 AI 请求读取某个资源时，服务器需要根据 URI 获取或生成相应的内容。

*   **TypeScript 示例 (`src/postgres/index.ts` - 简化版):**
    ```typescript
    // 导入 SDK 类型
    import { ReadResourceRequestSchema } from "@modelcontextprotocol/sdk/types.js";
    import pg from "pg";

    // ... (服务器和数据库连接池设置) ...
    const pool = new pg.Pool(/* ... */);
    const SCHEMA_PATH = "schema";

    // 注册 readResource 请求的处理函数
    server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
      const resourceUrl = new URL(request.params.uri); // 获取请求的 URI

      // 从 URI 中解析出表名
      const pathComponents = resourceUrl.pathname.split("/");
      const schema = pathComponents.pop();
      const tableName = pathComponents.pop();

      if (schema !== SCHEMA_PATH) { // 验证 URI 格式
        throw new Error("无效的资源 URI");
      }

      const client = await pool.connect();
      try {
        // 查询数据库获取表的列信息
        const result = await client.query(
          "SELECT column_name, data_type FROM information_schema.columns WHERE table_name = $1",
          [tableName] // 使用参数化查询防止 SQL 注入
        );

        // 返回包含查询结果（格式化为 JSON 字符串）的内容
        return {
          contents: [
            {
              uri: request.params.uri,      // 返回请求的 URI
              mimeType: "application/json", // 返回声明的 MIME 类型
              text: JSON.stringify(result.rows, null, 2), // 将结果转为 JSON 文本
            },
          ],
        };
      } finally {
        client.release();
      }
    });
    ```
    这个处理函数解析 URI 获取表名，查询数据库获取模式信息，并将结果作为 JSON 文本返回。

*   **Python 示例 (`src/sqlite/src/mcp_server_sqlite/server.py` - 简化版):**
    ```python
    # 导入 SDK 类型和 pydantic
    import mcp.types as types
    from pydantic import AnyUrl
    import logging

    logger = logging.getLogger(__name__)

    # 假设 'db' 是一个包含 _synthesize_memo 方法的数据库操作类实例
    class SqliteDatabase:
        # ... (省略 __init__ 和其他方法) ...
        def _synthesize_memo(self) -> str:
            # ... (生成备忘录内容的逻辑) ...
            logger.debug(f"正在合成包含 {len(self.insights)} 条洞察的备忘录")
            # (实际代码会根据 self.insights 生成文本)
            return f"业务洞察备忘录:\n- {self.insights[0] if self.insights else '暂无洞察'}"

    db = SqliteDatabase("path/to/database.db") # 创建实例

    # ... (服务器设置) ...

    # 使用 @server.read_resource() 装饰器注册处理函数
    @server.read_resource()
    async def handle_read_resource(uri: AnyUrl) -> str:
        logger.debug(f"处理 read_resource 请求，URI: {uri}")
        if str(uri) == "memo://insights":
            # 调用方法生成当前的备忘录内容
            memo_content = db._synthesize_memo()
            return memo_content # 直接返回备忘录文本
        else:
            logger.error(f"未知的资源 URI: {uri}")
            raise ValueError(f"未知的资源 URI: {uri}")
    ```
    这个 Python 处理函数检查 URI 是否为 `memo://insights`。如果是，它调用 `db._synthesize_memo()` 方法来动态生成当前的备忘录内容，并将其作为纯文本返回。

**(可选) 3. 处理订阅和通知:**

对于像 `memo://insights` 这样可变且可订阅的资源，服务器还需要：

*   **处理 `subscribe` 请求:** 记录客户端对某个 URI 的订阅。
*   **处理 `unsubscribe` 请求:** 移除订阅记录。
*   **发送 `notifications/resources/updated` 通知:** 当资源内容变化时，查找所有订阅了该 URI 的客户端，并向它们发送通知。

我们来看一下 `src/sqlite/src/mcp_server_sqlite/server.py` 中 `append_insight` 工具是如何触发通知的（简化版）：

```python
import mcp.types as types
from pydantic import AnyUrl
import logging

logger = logging.getLogger(__name__)
db = SqliteDatabase("path/to/database.db") # 假设 db 实例已创建

# ... (服务器设置) ...

# append_insight 工具的处理函数 (简化)
@server.call_tool()
async def handle_call_tool(name: str, arguments: dict | None) -> list:
    if name == "append_insight":
        if not arguments or "insight" not in arguments:
            raise ValueError("缺少 insight 参数")

        db.insights.append(arguments["insight"]) # 1. 添加新的洞察
        _ = db._synthesize_memo()           # 2. (内部可能更新备忘录状态)

        # 3. 发送资源更新通知！
        #    server.request_context.session 可访问当前连接的会话
        await server.request_context.session.send_resource_updated(
            AnyUrl("memo://insights") # 告知哪个 URI 更新了
        )
        logger.info("已发送 memo://insights 更新通知")

        return [types.TextContent(type="text", text="洞察已添加到备忘录")]
    # ... (处理其他工具) ...
```
当 `append_insight` 工具被调用时，它不仅更新了内部的洞察列表 (`db.insights`)，还通过 `server.request_context.session.send_resource_updated()` 向**当前**请求的客户端发送了通知。 （注意：一个更完整的实现需要维护一个所有订阅者的列表，并向所有订阅者发送通知，而不仅仅是当前请求的客户端。）

## 内部实现：资源访问流程

让我们梳理一下当 AI 读取一个资源时，幕后发生了什么。

**非代码流程 (`readResource`):**

1.  **AI 决策：** AI 根据任务需要（例如，了解表结构），决定需要读取某个资源。
2.  **确定 URI:** AI 通过 `listResources` 的结果或者预先知道的 URI (比如 `memo://insights`)，确定要读取的资源的 URI。
3.  **构造请求：** AI 构造一个 `readResource` 请求，包含目标资源的 URI。
4.  **发送请求：** AI 通过 MCP 连接将 JSON-RPC 请求发送给相应的 [MCP 服务器](01_mcp_服务器_.md)。
5.  **服务器接收与解析：** 服务器接收到请求，解析 JSON，识别出是 `readResource` 方法，并获取 URI 参数。
6.  **查找处理函数：** 服务器查找注册用来处理 `readResource` 请求的函数（例如，使用 `@server.read_resource()` 装饰的函数或 `server.setRequestHandler(ReadResourceRequestSchema, ...)` 注册的函数）。
7.  **执行处理函数：** 服务器调用该函数，并将请求参数（URI）传递给它。
8.  **资源内容获取/生成：** 处理函数内部：
    *   解析 URI 以确定具体请求的是什么。
    *   根据 URI，执行相应的操作来获取或生成资源内容（例如，查询数据库元数据、读取文件、调用内部方法生成备忘录文本）。
    *   获取或生成资源的内容（文本、JSON、二进制数据等）和其 MIME 类型。
9.  **构造响应：** 处理函数将获取到的内容和元数据包装成 MCP `readResource` 响应格式（一个包含 `uri`, `mimeType`, `text`/`blob` 的对象数组）。
10. **发送响应：** 服务器通过 MCP 连接将 JSON-RPC 响应发送回 AI 客户端。
11. **AI 处理响应：** AI 接收响应，解析 JSON，提取 `contents` 数组中的资源内容，并将其用于后续任务。

**时序图示例 (`readResource` 读取 `memo://insights`):**

```mermaid
sequenceDiagram
    participant 用户
    participant Claude (AI 客户端)
    participant SQLite服务器

    用户->>Claude (AI 客户端): "总结一下我们目前的业务洞察"
    Claude (AI 客户端)->>Claude (AI 客户端): 决定读取 memo://insights 资源
    Claude (AI 客户端)->>SQLite服务器: MCP 请求 (readResource, params: {uri: "memo://insights"})
    SQLite服务器->>SQLite服务器: 查找并执行 readResource 处理函数
    SQLite服务器->>SQLite服务器: 调用 db._synthesize_memo() 生成备忘录文本
    SQLite服务器->>Claude (AI 客户端): MCP 响应 (result: {contents: [{uri: "memo://...", mimeType: "text/plain", text: "备忘录内容..."}]})
    Claude (AI 客户端)->>用户: "根据当前的备忘录，我们发现以下几点..."
```

这个图表展示了 AI 如何请求并获取一个动态生成的资源内容。

## 总结

在本章中，我们探讨了 MCP 服务器提供的第二种核心能力：**MCP 资源**。我们了解到：

*   MCP 资源是服务器提供的数据源或上下文信息，如同工具棚里的原材料或参考手册。
*   AI 助手通过读取资源来获取执行任务所需的信息，例如数据库模式、配置文件或动态生成的报告。
*   每个资源由唯一的 URI 标识，并具有明确的 MIME 类型。
*   服务器通过 `listResources` 告知 AI 可用的资源，通过 `readResource` 提供资源内容。
*   某些资源可以被订阅 (`subscribe`)，服务器会在内容更新时主动发送通知 (`notifications/resources/updated`)。
*   这使得 AI 能够以标准化的方式访问静态和动态的信息，补充了 [MCP 工具](02_mcp_工具_.md) 提供的操作能力。

理解了 MCP 工具和 MCP 资源后，我们将在下一章探索 MCP 服务器提供的第三种能力：[MCP 提示](04_mcp_提示_.md)，了解它们如何帮助引导 AI 和用户完成特定任务。

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)