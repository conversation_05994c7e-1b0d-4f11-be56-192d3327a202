# Chapter 1: MCP 服务器


欢迎来到 `servers` 项目的教程！在本系列中，我们将探索模型上下文协议（Model Context Protocol, MCP）的核心概念，一步步带你了解如何利用它来安全地扩展 AI 助手的能力。

## 动机：让 AI 安全地与世界互动

想象一下，你正在与一个像 Claude 这样的大型语言模型（LLM）助手聊天。你希望它能帮你完成一些与外部世界相关的任务，比如：

*   “帮我看看 `/home/<USER>/projects/my_project/README.md` 文件里写了什么？”
*   “总结一下 `https://example.com/article` 这篇文章的内容。”
*   “我本地 Git 仓库 `my_project` 的最新提交是什么？”

直接让 AI 访问你的文件系统、网络或执行命令是非常危险的。它可能会意外删除文件、访问敏感信息或执行恶意代码。那么，我们如何才能让 AI 助手在安全可控的前提下，拥有与外部世界交互的能力呢？

**MCP 服务器** 就是答案。它们充当了 AI 助手与外部系统之间的安全、专业的“服务台”或“工具棚”。

## 什么是 MCP 服务器？

> MCP 服务器是模型上下文协议（MCP）的核心单元。它就像一个为 AI 助手准备的专用工具棚或服务台。每个服务器都专注于特定的领域（如文件系统、Git、数据库、Web 搜索），并提供一组功能（工具、资源、提示），让 AI 助手能够安全、可控地与外部世界互动或访问信息。代码库中的每个目录（例如 `src/filesystem`、`src/git`）都代表一个这样的服务器实现。

**打个比方：**

把 AI 助手想象成一个项目经理。当项目经理需要完成一项专业任务时（比如修理水管、布线或检查代码），他们不会自己动手，而是会去找相应的专家（水管工、电工、代码审查员）。

MCP 服务器就扮演着这些“专家”的角色。每个服务器都是特定领域的专家：

*   `filesystem` 服务器是文件系统专家，知道如何安全地读写文件、列出目录。
*   `git` 服务器是 Git 版本控制专家，知道如何检查状态、查看提交记录。
*   `brave-search` 服务器是网页搜索专家，可以帮助查找信息。
*   `postgres` 服务器是数据库专家，可以查询数据库。

等等。

<br/>

**服务器提供什么？**

每个 MCP 服务器通常会提供以下一种或多种能力，我们将在后续章节详细介绍：

1.  **[MCP 工具](02_mcp_工具_.md)**：服务器能执行的具体操作。例如，`filesystem` 服务器提供 `read_file`（读取文件）和 `list_directory`（列出目录）工具。
2.  **[MCP 资源](03_mcp_资源_.md)**：服务器能提供的信息片段。例如，`postgres` 服务器可以提供数据库的 `schema`（结构）作为资源。
3.  **[MCP 提示](04_mcp_提示_.md)**：预先定义好的对话模板，可以引导 AI 助手和用户完成特定任务。

**核心思想：安全与控制**

MCP 服务器的核心价值在于**安全**和**控制**。AI 助手不直接与外部系统交互，而是通过 MCP 服务器这个中介。服务器可以配置权限，确保 AI 助手只能执行被允许的操作，只能访问被授权的数据。例如，`filesystem` 服务器可以被配置为只允许访问 `/home/<USER>/projects` 这个目录下的文件。

**代码库结构**

在这个 `servers` 代码库中，每一个 `src/` 下的子目录都代表一个 MCP 服务器的参考实现。你可以浏览这些目录来了解各种服务器的功能。

```markdown
# Model Context Protocol 服务器

该仓库是 [模型上下文协议](https://modelcontextprotocol.io/) (MCP) 的 *参考实现* 集合...

## 🌟 参考服务器

这些服务器旨在演示 MCP 特性以及 TypeScript 和 Python SDK。

- **[AWS KB Retrieval](src/aws-kb-retrieval-server)** - 使用 Bedrock Agent Runtime 从 AWS 知识库检索
- **[Brave Search](src/brave-search)** - 使用 Brave Search API 进行 Web 和本地搜索
- **[Everything](src/everything)** - 包含提示、资源和工具的参考/测试服务器
- **[Fetch](src/fetch)** - Web 内容获取和转换，以供 LLM 高效使用
- **[Filesystem](src/filesystem)** - 具有可配置访问控制的安全文件操作
- **[Git](src/git)** - 读取、搜索和操作 Git 仓库的工具
- **[GitHub](src/github)** - 仓库管理、文件操作和 GitHub API 集成
- **[Memory](src/memory)** - 基于知识图的持久化记忆系统
- **[PostgreSQL](src/postgres)** - 具有模式检查功能的只读数据库访问
- ... 还有更多!
```

上面的 `README.md` 片段展示了各种各样的参考服务器，它们体现了 MCP 的灵活性和可扩展性。

## MCP 服务器如何工作？ (使用视角)

MCP 服务器遵循客户端-服务器模型。AI 助手（例如 Claude 桌面版）充当 **客户端**，而你运行的特定功能程序（如 `filesystem` 服务器）充当 **服务器**。

它们之间的通信遵循**模型上下文协议 (MCP)**，这是一种基于 JSON-RPC 的标准协议。简单来说，客户端向服务器发送 JSON 格式的请求，服务器处理后返回 JSON 格式的响应。

**示例：使用文件系统服务器**

让我们回到最初的用例：让 Claude 读取本地文件 `/path/to/allowed/files/notes.txt`。

1.  **配置客户端：**
    你需要在 AI 助手（客户端）的配置中告诉它如何启动和连接到文件系统服务器。对于 Claude 桌面版，配置可能如下所示：

    ```json
    {
      "mcpServers": {
        "filesystem": {
          "command": "npx",
          "args": ["-y", "@modelcontextprotocol/server-filesystem", "/path/to/allowed/files"]
        }
      }
    }
    ```

    这段 JSON 配置告诉 Claude：
    *   有一个名为 `filesystem` 的服务器。
    *   要启动它，请运行 `npx` 命令。
    *   传递给 `npx` 的参数是 `-y @modelcontextprotocol/server-filesystem` 和 `/path/to/allowed/files`。
    *   `@modelcontextprotocol/server-filesystem` 是服务器的程序包。
    *   `/path/to/allowed/files` 是一个参数，告诉文件系统服务器只允许访问这个目录。

2.  **用户请求：**
    你向 Claude 发出请求：“请读取 `/path/to/allowed/files/notes.txt` 文件。”

3.  **AI 决策：**
    Claude 分析你的请求，意识到需要读取文件内容。它知道自己有一个名为 `filesystem` 的服务器，并且这个服务器很可能有一个类似 `read_file` 的工具。

4.  **MCP 请求：**
    Claude（客户端）向 `filesystem` 服务器发送一个 MCP 请求。这个请求本质上是一个 JSON 消息，说明要调用 `read_file` 工具，并提供参数 `{ "path": "/path/to/allowed/files/notes.txt" }`。

5.  **服务器处理：**
    `filesystem` 服务器接收到请求。
    *   它首先检查请求的路径 `/path/to/allowed/files/notes.txt` 是否在启动时配置的允许目录 `/path/to/allowed/files` 之内。
    *   检查通过后，服务器执行文件读取操作。
    *   服务器将文件内容打包成 MCP 响应格式。

6.  **MCP 响应：**
    服务器将包含文件内容的 JSON 响应发送回 Claude。

7.  **呈现结果：**
    Claude 接收到响应，提取文件内容，并将其呈现给你。

通过这个过程，AI 助手成功读取了文件内容，但它从未直接访问文件系统。所有的交互都通过受控的 MCP 服务器进行，确保了安全。

## 深入了解：服务器内部实现

让我们简单了解一下 MCP 服务器内部是如何工作的。

**非代码流程：**

1.  **启动：** 服务器进程启动。这通常是通过命令行运行的，比如我们上面看到的 `npx @modelcontextprotocol/server-filesystem ...`。
2.  **连接：** 客户端（如 Claude 桌面版）根据配置启动服务器进程，并通过标准输入/输出（stdio）或服务器发送事件（SSE）等传输方式与服务器建立连接。
3.  **握手：** 客户端和服务器交换初始化信息，包括它们各自支持的功能（例如，服务器告诉客户端它有哪些工具）。
4.  **请求处理循环：** 服务器进入等待状态，监听来自客户端的请求。
5.  **收到请求：** 当客户端发送请求时（例如，调用 `read_file` 工具），服务器接收到这个 JSON-RPC 消息。
6.  **验证与执行：** 服务器解析请求，验证参数（例如，路径是否合法），检查权限（路径是否在允许范围内），然后执行相应的操作（读取文件）。
7.  **发送响应：** 服务器将操作结果（文件内容或错误信息）打包成 JSON-RPC 响应，通过连接发送回客户端。
8.  **继续监听：** 服务器返回等待状态，准备处理下一个请求。

**交互时序图示例：**

下面是一个简化的时序图，展示了读取文件的交互过程：

```mermaid
sequenceDiagram
    participant 用户
    participant Claude桌面 (客户端)
    participant 文件系统服务器

    用户 ->> Claude桌面 (客户端): "请读取 /path/to/notes.txt"
    Claude桌面 (客户端) ->> 文件系统服务器: MCP 请求 (调用工具: read_file, 参数: { path: "/path/to/notes.txt" })
    文件系统服务器 ->> 文件系统服务器: 检查路径是否在允许范围内
    文件系统服务器 ->> 操作系统: 读取文件 "/path/to/notes.txt"
    操作系统 -->> 文件系统服务器: 文件内容
    文件系统服务器 ->> Claude桌面 (客户端): MCP 响应 (内容: "文件内容...")
    Claude桌面 (客户端) ->> 用户: "这是文件内容：..."

```

**代码简析**

MCP 服务器通常使用官方提供的 SDK（软件开发工具包）来简化开发。以下是一些关键概念和代码片段（以 TypeScript SDK 为例，并进行简化）：

1.  **服务器初始化：**
    创建一个服务器实例，定义其名称和版本，并声明其能力。

    ```typescript
    // 导入 SDK
    import { Server } from "@modelcontextprotocol/sdk/server/index.js";
    import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";

    // 创建服务器实例
    const server = new Server(
      {
        name: "my-simple-server", // 服务器名称
        version: "1.0.0",      // 服务器版本
      },
      {
        capabilities: {      // 声明服务器能力
          tools: {},         // 支持工具 (具体工具在后面定义)
          // resources: {},  // 可以添加资源支持
          // prompts: {},    // 可以添加提示支持
        },
      }
    );
    ```

    这段代码创建了一个名为 `my-simple-server` 的服务器。`capabilities` 对象告诉客户端这个服务器支持哪些 MCP 功能（这里只声明了支持工具）。

2.  **处理请求 - 列出工具：**
    服务器需要响应 `listTools` 请求，告诉客户端它有哪些可用的工具。

    ```typescript
    import { ListToolsRequestSchema, Tool } from "@modelcontextprotocol/sdk/types.js";
    import { z } from "zod"; // 用于定义输入模式
    import { zodToJsonSchema } from "zod-to-json-schema"; // 转换工具

    // 定义 echo 工具的输入模式
    const EchoSchema = z.object({
      message: z.string().describe("要回显的消息"),
    });

    // 工具定义
    const ECHO_TOOL: Tool = {
      name: "echo",                        // 工具名称
      description: "简单地回显输入的消息",  // 工具描述
      inputSchema: zodToJsonSchema(EchoSchema) // 定义输入需要什么参数
    };

    // 注册 listTools 请求的处理函数
    server.setRequestHandler(ListToolsRequestSchema, async () => {
      // 返回服务器支持的工具列表
      return {
        tools: [ECHO_TOOL], // 在这里列出所有工具
      };
    });
    ```

    这里定义了一个名为 `echo` 的简单工具，它接受一个 `message` 字符串作为输入。`setRequestHandler` 用于注册处理 `listTools` 请求的函数，该函数返回包含 `ECHO_TOOL` 的列表。

3.  **处理请求 - 调用工具：**
    服务器需要响应 `callTool` 请求，根据客户端指定的工具名称和参数执行操作。

    ```typescript
    import { CallToolRequestSchema } from "@modelcontextprotocol/sdk/types.js";

    // 注册 callTool 请求的处理函数
    server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params; // 获取工具名称和参数

      // 检查是否是 echo 工具
      if (name === "echo") {
        // 验证参数是否符合 EchoSchema (简化起见，这里省略了严格验证)
        const message = (args as { message: string }).message;
        // 执行工具操作并返回结果
        return {
          content: [{ type: "text", text: `服务器回显: ${message}` }],
        };
      }

      // 如果是未知的工具名称，则抛出错误
      throw new Error(`未知的工具: ${name}`);
    });
    ```

    这个处理函数检查被调用的工具名称。如果是 `echo`，它会提取 `message` 参数，然后返回一个包含回显文本的响应。

4.  **连接与运行：**
    最后，需要将服务器连接到一个传输层（例如 `StdioServerTransport`，使用标准输入/输出）并开始运行。

    ```typescript
    async function runServer() {
      // 创建一个标准输入/输出传输实例
      const transport = new StdioServerTransport();
      // 连接服务器到传输层
      await server.connect(transport);
      // 打印日志，表示服务器已运行
      console.error("简单 MCP 服务器正在通过 stdio 运行");
    }

    runServer().catch(console.error);
    ```

这些只是非常简化的例子，但它们展示了使用 MCP SDK 构建服务器的基本流程：定义服务器、声明能力、处理请求。实际的服务器（如 `filesystem` 或 `git`）会有更复杂的逻辑来执行它们各自领域的操作，并进行更严格的安全检查。

## 总结

在本章中，我们介绍了 MCP 服务器的基本概念。我们了解到：

*   MCP 服务器是专门为 AI 助手提供服务的后端单元，用于安全地与外部世界交互。
*   每个服务器专注于特定领域（文件、Git、搜索等），并提供 [MCP 工具](02_mcp_工具_.md)、[MCP 资源](03_mcp_资源_.md) 或 [MCP 提示](04_mcp_提示_.md)。
*   服务器通过 MCP 协议与 AI 助手（客户端）通信，通常使用 JSON-RPC。
*   配置和运行服务器允许 AI 助手在受控的环境中使用其功能。

MCP 服务器是扩展 AI 能力的关键组件。理解了服务器是什么之后，我们将在下一章深入探讨服务器提供的核心功能之一：**[MCP 工具](02_mcp_工具_.md)**。

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)