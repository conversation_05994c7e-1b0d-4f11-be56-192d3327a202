# Chapter 8: Git 服务器


在上一章 [文件系统服务器](07_文件系统服务器_.md) 中，我们看到了如何通过配置沙盒，让 AI 助手能够安全地与你的本地文件进行交互。现在，我们将注意力转向另一个在软件开发中至关重要的工具：Git。本章将介绍 **Git 服务器**，这是一个让 AI 助手能够像版本控制专家一样与你的 Git 仓库协作的 [MCP 服务器](01_mcp_服务器_.md)。

## 动机：让 AI 参与版本控制

想象一下你正在编码，希望 AI 助手能帮你处理一些 Git 相关的任务：

*   “帮我检查一下我本地 `my-awesome-project` 项目的 Git 状态，看看有哪些文件被修改了？”
*   “我刚刚完成了 X 功能的修改，帮我把 `src/feature_x.py` 文件添加到暂存区。”
*   “写一个提交信息，总结一下暂存区的修改，然后帮我提交这些更改。”
*   “切换到 `develop` 分支，看看最新的提交记录是什么？”

和文件系统一样，直接让 AI 在你的终端里执行 `git` 命令存在风险。错误的命令可能会丢失工作、弄乱仓库历史，或者将未完成的代码推送到远程仓库。我们需要一个更安全、更可控的方式让 AI 参与到版本控制流程中。

**Git 服务器** 就是这个安全的接口。它充当了 AI 助手和你的本地 Git 仓库之间的桥梁，像一个专业的 Git 助手，可以根据 AI 的指令安全地执行各种 Git 操作。

## 核心概念：Git 操作的代理

Git 服务器本身**不是**一个新的版本控制系统，它也不存储你的代码。它更像是一个理解 Git 命令并能安全执行它们的**代理**或**专家**。

**打个比方：**

想象 Git 服务器是一个经验丰富的图书馆管理员，专门负责管理项目的手稿版本（你的 Git 仓库）。AI 助手（项目作者）不能直接跑进档案室（你的文件系统）随意翻阅或修改手稿。相反，作者会告诉管理员：

*   “请帮我核对一下‘第三章’手稿（某个文件）的最新修改记录（`git status` 或 `git diff`）。”
*   “我写好了一段新的内容，请把它添加到‘第五章’手稿的待定稿件中（`git add`）。”
*   “我已经确认了这些修改，请将它们正式存档，并备注‘完成了第五章初稿’（`git commit`）。”

管理员（Git 服务器）会理解这些请求，并使用标准的存档工具（实际的 Git 命令或库）来安全、准确地完成操作。

## 如何使用：配置仓库路径

要让 Git 服务器知道它应该操作**哪个**本地 Git 仓库，你需要像配置 [文件系统服务器](07_文件系统服务器_.md) 那样，在 AI 助手客户端（比如 Claude 桌面版）的配置文件中指定仓库的路径。这通常通过**命令行参数**来完成。

下面是几种常见的配置方式（参考 `src/git/README.md`）：

### 使用 uvx（推荐，适用于 Python 环境）

如果你的环境安装了 `uv`（一个 Python 包管理器），这是推荐的方式。

```json
{
  "mcpServers": {
    "git": { // 服务器名称
      "command": "uvx", // 使用 uvx 命令启动
      "args": [         // 传递给 uvx 的参数
        "mcp-server-git", // Git 服务器的包名
        "--repository",   // <= 指定仓库路径的参数名
        "path/to/your/git/repo" // <= 替换成你本地 Git 仓库的实际路径
      ]
    }
    // ... 其他服务器配置 ...
  }
}
```

**解释：**

*   `command`: "uvx" 指示客户端使用 `uvx` 运行服务器。
*   `args`: 列表中的 `mcp-server-git` 是要运行的 Python 包。
*   **关键在于：** `--repository path/to/your/git/repo` 这个命令行参数。`--repository` 告诉 Git 服务器接下来的字符串是你希望它操作的仓库路径。服务器启动时会读取这个参数，并将其作为后续所有 Git 操作的目标仓库。

### 使用 PIP 安装后运行

如果你使用 `pip` 安装了 `mcp-server-git` 包。

```json
{
  "mcpServers": {
    "git": {
      "command": "python", // 使用 python 命令
      "args": [
        "-m",             // python 参数，表示运行一个模块
        "mcp_server_git", // Git 服务器模块名
        "--repository",   // <= 指定仓库路径
        "path/to/your/git/repo" // <= 替换成你的仓库路径
      ]
    }
  }
}
```

**解释：**

*   这种方式类似，只是启动命令变成了 `python -m mcp_server_git`。
*   同样地，`--repository path/to/your/git/repo` 指定了目标仓库。

### 使用 Docker

如果你使用 Docker，配置会涉及卷挂载，将本地仓库目录映射到容器内。

```json
{
  "mcpServers": {
    "git": {
      "command": "docker",
      "args": [
        "run", "--rm", "-i", // Docker 参数
        // ↓↓↓ 将本地仓库挂载到容器内的 /repo 目录 ↓↓↓
        "--mount", "type=bind,src=/full/path/to/your/git/repo,dst=/repo",
        // ↑↑↑ 挂载定义结束 ↑↑↑
        "mcp/git", // Docker 镜像名
        // ↓↓↓ 告知服务器在容器内的哪个路径操作 ↓↓↓
        "--repository", "/repo" // 服务器现在操作容器内的 /repo 目录
        // ↑↑↑ 仓库路径参数结束 ↑↑↑
      ]
    }
  }
}
```

**解释：**

*   `--mount`: 将你本地的仓库目录 `src=...` 挂载到容器内部的 `dst=/repo`。
*   `mcp/git`: Git 服务器的 Docker 镜像。
*   `--repository /repo`: 这里告诉在**容器内部**运行的 Git 服务器，它应该操作 `/repo` 这个路径。

配置完成后，启动 AI 助手时，它就会按照你的设置启动 Git 服务器，并让服务器“知道”要操作哪个仓库。

## 如何使用：调用工具

配置好服务器后，AI 助手就可以通过调用 Git 服务器提供的 [MCP 工具](02_mcp_工具_.md) 来执行 Git 操作了。这些工具是对常见 Git 命令的安全封装。以下是一些主要的工具（参考 `src/git/README.md`）：

*   `git_status` (获取状态): 显示工作区的状态（哪些文件被修改、暂存等）。
    *   输入: `{ "repo_path": "仓库路径" }`
    *   输出: Git 状态的文本描述。
*   `git_diff_unstaged` (查看未暂存差异): 显示工作目录中已修改但**未暂存**的文件差异。
    *   输入: `{ "repo_path": "仓库路径" }`
    *   输出: `git diff` 的文本输出。
*   `git_diff_staged` (查看已暂存差异): 显示已**暂存**但**未提交**的文件差异。
    *   输入: `{ "repo_path": "仓库路径" }`
    *   输出: `git diff --cached` 的文本输出。
*   `git_add` (添加到暂存区): 将指定文件或文件的更改添加到 Git 暂存区。
    *   输入: `{ "repo_path": "仓库路径", "files": ["文件路径1", "文件路径2", ...] }`
    *   输出: 确认暂存成功的消息。
*   `git_commit` (提交更改): 将暂存区的所有更改记录为一个新的提交。
    *   输入: `{ "repo_path": "仓库路径", "message": "提交信息" }`
    *   输出: 包含新提交哈希值的确认消息。
*   `git_log` (查看日志): 显示提交历史记录。
    *   输入: `{ "repo_path": "仓库路径", "max_count": 10 }` (max_count 可选, 默认10)
    *   输出: 包含提交哈希、作者、日期和消息的日志条目列表（文本）。
*   `git_create_branch` (创建分支): 创建一个新的分支。
    *   输入: `{ "repo_path": "仓库路径", "branch_name": "新分支名", "base_branch": "起始分支名或提交" }` (base_branch 可选)
    *   输出: 确认分支创建成功的消息。
*   `git_checkout` (切换分支): 切换到指定的分支。
    *   输入: `{ "repo_path": "仓库路径", "branch_name": "要切换到的分支名" }`
    *   输出: 确认切换分支成功的消息。
*   `git_reset` (重置暂存区): 取消所有已暂存的更改（相当于 `git reset HEAD`）。
    *   输入: `{ "repo_path": "仓库路径" }`
    *   输出: 确认重置成功的消息。
*   `git_show` (显示提交内容): 显示特定提交的详细信息和更改内容。
    *   输入: `{ "repo_path": "仓库路径", "revision": "提交哈希、分支名或标签" }`
    *   输出: 提交的详细信息和差异（文本）。
*   `git_init` (初始化仓库): 在指定路径初始化一个新的 Git 仓库。
    *   输入: `{ "repo_path": "要初始化的目录路径" }`
    *   输出: 确认初始化成功的消息。

**重要提示：** 大部分工具都需要 `repo_path` 参数。虽然你在服务器启动时已经通过 `--repository` 指定了一个默认仓库，但这些工具通常设计为需要你**再次**明确提供 `repo_path`。这似乎有些冗余，可能是为了未来支持同时操作多个仓库，或者只是当前实现的一个特性。**在实际使用中，你需要确保传递给工具的 `repo_path` 与你启动服务器时配置的 `--repository` 路径（或 Docker 挂载的目标路径）相匹配。**

## 解决用例：检查 Git 状态

让我们回到之前的用例：让 AI 助手检查 `/path/to/your/git/repo` 仓库的 Git 状态。假设你已经按照上面的 `uvx` 示例配置了客户端。

1.  **你向 AI 提问:** “检查一下 `/path/to/your/git/repo` 的状态。”
2.  **AI 决定:** AI 分析请求，识别出需要检查 Git 状态。它知道连接了 `git` 服务器，并有 `git_status` 工具可用。
3.  **AI 发送请求:** AI 向 `git` 服务器发送 `callTool` [MCP 请求](01_mcp_服务器_.md)，内容大致如下：
    ```json
    {
      "method": "callTool",
      "params": {
        "name": "git_status", // 要调用的工具名
        "arguments": {        // 传递给工具的参数
          "repo_path": "/path/to/your/git/repo" // 必须再次提供仓库路径
        }
      }
    }
    ```
4.  **服务器接收请求:** `git` 服务器收到这个 JSON 请求。
5.  **服务器准备执行:** 服务器解析请求，知道要调用 `git_status` 工具，并获取了 `repo_path` 参数。
6.  **服务器执行操作 (安全地):** 服务器**不会**直接在 shell 中执行 `git status` 命令。相反，它会使用一个 **Git 库** (比如 Python 的 `GitPython` 库，如 `src/git/src/mcp_server_git/server.py` 中所示) 来与指定的仓库 (`repo_path`) 进行交互。它会调用库中对应 `git status` 的函数。
    ```python
    # server.py (概念简化)
    import git # 导入 GitPython 库

    def git_status(repo: git.Repo) -> str:
        # 调用库函数执行 status 操作
        return repo.git.status()
    
    # 在 call_tool 处理函数中...
    repo = git.Repo(repo_path) # 使用路径创建仓库对象
    status_output = git_status(repo) # 调用上面的函数
    ```
    使用库的好处是更安全，避免了命令注入等风险，并且可以更好地处理输出和错误。
7.  **服务器发送响应:** 服务器将 `git_status` 函数返回的文本输出打包成成功的 MCP 响应：
    ```json
    {
      "result": {
        "content": [{
          "type": "text",
          "text": "Repository status:\nOn branch main\nYour branch is up to date with 'origin/main'.\n\nnothing to commit, working tree clean" // Git status 的实际输出
        }],
        "isError": false
      }
    }
    ```
    如果执行出错（例如路径无效），`isError` 会是 `true`。
8.  **AI 呈现结果:** AI 助手收到响应，提取状态文本，并展示给你。

通过这个过程，AI 利用 Git 服务器提供的工具，安全地执行了 Git 命令，并将结果返回给你。

## 内部实现：幕后发生的事情

让我们看看 Git 服务器（以 Python 实现为例）内部是如何工作的。

### 非代码流程

1.  **启动与配置:**
    *   AI 助手客户端根据配置，使用 `uvx`、`python -m` 或 `docker` 启动 Git 服务器进程。
    *   `--repository` 命令行参数（包含仓库路径）被传递给服务器进程。
2.  **服务器初始化:**
    *   Git 服务器的启动脚本（如 `src/git/src/mcp_server_git/__init__.py` 中的 `main` 函数）使用 `click` 或 `argparse` 等库解析命令行参数，获取 `--repository` 的值。
    *   这个路径（或指示路径不存在的标记）被传递给核心服务器逻辑（如 `serve` 函数）。
    *   服务器逻辑（可能）会存储这个路径，并可能进行初步验证（例如，检查路径是否存在以及是否为有效的 Git 仓库，如 `serve` 函数中所示）。
3.  **接收工具调用请求:**
    *   服务器通过 MCP 连接接收到 `callTool` 请求（比如 `git_status`）。
4.  **解析工具与参数:**
    *   服务器的 `@server.call_tool()` 处理函数被调用。它获取到工具名称 (`name`) 和参数 (`arguments`)。
    *   参数通常会使用 Pydantic 模型（如 `GitStatus`）进行验证，确保 `repo_path` 等必需字段存在且类型正确。
5.  **执行 Git 操作 (使用库):**
    *   处理函数根据工具名称（`match name:`）执行相应的逻辑。
    *   **关键：** 它使用 `arguments["repo_path"]` 提供的路径创建一个 `git.Repo` 对象。**这再次强调了为什么调用工具时通常需要 `repo_path` 参数。**
    *   它调用封装了 Git 操作的 Python 函数（如 `git_status(repo)`, `git_add(repo, files)` 等）。
    *   这些函数内部使用 `GitPython` 库 (`repo.git.status()`, `repo.index.add()`, `repo.index.commit()`) 来与 Git 仓库交互。
6.  **捕获结果与错误:**
    *   Git 库操作的输出（通常是字符串或列表）或抛出的异常被捕获。
7.  **返回结果:**
    *   处理函数将结果格式化为 `TextContent`（或其他 MCP 内容类型），并包装成 MCP 响应。如果是错误，则设置 `isError: true`。
    *   响应通过 MCP 连接发送回 AI 助手。

### 时序图示例 (调用 `git_status`)

```mermaid
sequenceDiagram
    participant 客户端 (如 Claude)
    participant Git服务器 (Python进程)
    participant GitPython库
    participant Git命令行 (底层)

    客户端 (如 Claude)->>Git服务器 (Python进程): MCP 请求 (callTool: git_status, args: {repo_path: "/path/to/repo"})
    Git服务器 (Python进程)->>Git服务器 (Python进程): 解析请求, 定位到 git_status 逻辑
    Git服务器 (Python进程)->>GitPython库: 创建 Repo 对象 (path="/path/to/repo")
    Git服务器 (Python进程)->>GitPython库: 调用 repo.git.status()
    GitPython库->>Git命令行 (底层): (可能)执行 'git status' 命令
    Git命令行 (底层)-->>GitPython库: 返回 status 输出
    GitPython库-->>Git服务器 (Python进程): 返回 status 文本
    Git服务器 (Python进程)->>Git服务器 (Python进程): 格式化结果为 TextContent
    Git服务器 (Python进程)->>客户端 (如 Claude): MCP 响应 (content: [{type: "text", text: "Git status 输出..."}], isError: false)
```

这个图表展示了服务器如何使用 Git 库作为中间层来安全地执行 Git 操作。

### 代码实现概览

核心逻辑位于 `src/git/src/mcp_server_git/server.py`。

**1. 解析命令行参数 (在 `__init__.py` 中)**

启动入口使用 `click` 库来定义和获取 `--repository` 参数。

```python
# src/git/src/mcp_server_git/__init__.py (简化)
import click
from pathlib import Path
from .server import serve
import asyncio
import logging
import sys

@click.command()
@click.option("--repository", "-r", type=Path, help="Git repository path") # 定义参数
@click.option("-v", "--verbose", count=True) # 日志级别参数
def main(repository: Path | None, verbose: bool) -> None:
    """MCP Git Server - Git functionality for MCP"""
    # ... 设置日志级别 ...
    logging.basicConfig(level=logging_level, stream=sys.stderr)
    # 将解析到的 repository 路径传递给 serve 函数
    asyncio.run(serve(repository))

if __name__ == "__main__":
    main()
```

**2. 定义工具和输入模式 (在 `server.py` 中)**

使用 `@server.list_tools()` 装饰器和 Pydantic 模型来定义工具。

```python
# src/git/src/mcp_server_git/server.py (简化)
from mcp.server import Server
from mcp.types import Tool, TextContent
from pydantic import BaseModel
import git
from pathlib import Path

# 定义 git_status 的输入模型
class GitStatus(BaseModel):
    repo_path: str # 需要仓库路径

# ... 其他工具的 Pydantic 模型定义 ...

server = Server("mcp-git") # 创建服务器实例

@server.list_tools()
async def list_tools() -> list[Tool]:
    return [
        Tool(
            name="git_status", # 工具名
            description="显示工作区状态", # 描述
            inputSchema=GitStatus.schema(), # 使用 Pydantic 模型生成 JSON Schema
        ),
        # ... 其他工具的定义 ...
    ]
```

**3. 处理工具调用 (在 `server.py` 中)**

使用 `@server.call_tool()` 装饰器处理调用请求，内部使用 `GitPython` 库。

```python
# src/git/src/mcp_server_git/server.py (简化)

# 实际执行 git status 的函数
def git_status(repo: git.Repo) -> str:
    return repo.git.status()

# ... 其他 Git 操作的函数 ...

@server.call_tool()
async def call_tool(name: str, arguments: dict) -> list[TextContent]:
    # 获取 repo_path (假设参数已验证)
    repo_path = Path(arguments["repo_path"])

    # 对于非 init 命令，需要一个 Repo 对象
    if name != "git_init":
        try:
            repo = git.Repo(repo_path) # 使用路径创建 Repo 对象
        except git.InvalidGitRepositoryError:
            return [TextContent(type="text", text=f"错误: {repo_path} 不是有效的 Git 仓库")]
        except Exception as e:
            return [TextContent(type="text", text=f"错误: 无法访问仓库 {repo_path}: {e}")]

    # 根据工具名称执行操作
    match name:
        case "git_status":
            status = git_status(repo) # 调用执行 status 的函数
            return [TextContent(type="text", text=f"仓库状态:\n{status}")]

        case "git_add":
            files = arguments["files"]
            result = git_add(repo, files) # 调用执行 add 的函数
            return [TextContent(type="text", text=result)]

        # ... 处理其他 case ...

        case "git_init": # 特殊处理 init，因为它创建仓库
            result = git_init(str(repo_path))
            return [TextContent(type="text", text=result)]

        case _:
            raise ValueError(f"未知的工具: {name}")

# ... (服务器运行逻辑) ...
async def serve(repository: Path | None) -> None:
    logger = logging.getLogger(__name__)
    # ... (可能在这里使用 repository 参数进行初始检查或设置默认值) ...
    
    # 注册上面的 list_tools 和 call_tool 处理函数
    # ...
    
    options = server.create_initialization_options()
    async with stdio_server() as (read_stream, write_stream):
        await server.run(read_stream, write_stream, options, raise_exceptions=True)
```

代码展示了如何根据工具名称分派任务，并使用 `GitPython` 库来与指定的仓库进行交互，从而安全地执行 Git 操作。

## 总结

在本章中，我们了解了 **Git 服务器**，它为 AI 助手提供了一个与本地 Git 仓库进行交互的安全接口。我们学到了：

*   Git 服务器充当 Git 操作的**代理**，使得 AI 可以执行检查状态、暂存、提交、切换分支等任务，而无需直接运行 `git` 命令。
*   你需要通过**命令行参数** (`--repository`) 在客户端配置中指定服务器要操作的本地 Git **仓库路径**。
*   AI 助手通过调用服务器提供的各种 [MCP 工具](02_mcp_工具_.md)（如 `git_status`, `git_add`, `git_commit`）来执行操作。
*   服务器内部使用像 `GitPython` 这样的库来安全地执行 Git 命令，而不是直接操作 shell。
*   虽然服务器启动时指定了仓库路径，但许多工具的参数**仍然需要**提供 `repo_path`。

Git 服务器使得 AI 助手能够更好地融入软件开发的工作流程中，以一种安全可控的方式协助进行版本控制。

接下来，我们将目光投向数据存储的另一个重要领域：数据库。在下一章中，我们将探讨 [数据库服务器 (SQLite 示例)](09_数据库服务器__sqlite_示例__.md)，看看 AI 如何安全地查询和（在某些情况下）操作数据库。

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)