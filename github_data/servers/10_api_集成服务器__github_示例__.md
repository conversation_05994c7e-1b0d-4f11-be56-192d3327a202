# Chapter 10: API 集成服务器 (GitHub 示例)


在上一章 [数据库服务器 (SQLite 示例)](09_数据库服务器__sqlite_示例__.md) 中，我们看到了 AI 助手如何通过专门的服务器安全地与数据库交互。现在，我们将探索另一种常见的集成场景：让 AI 助手与外部的 Web API 对话。本章将以 GitHub 为例，介绍 **API 集成服务器**。

## 动机：让 AI 成为你的 GitHub 助手

想象一下你正在开发一个项目，突然发现了一个 bug。你希望你的 AI 助手能帮你做点什么：

“嘿，帮我在我的 `my-cool-project` 仓库里创建一个新的 issue，标题是‘用户登录页面按钮失效’，描述一下复现步骤。”

直接让 AI 访问 GitHub 网站并点击按钮显然不现实。你也不想手动复制粘贴错误信息，然后切换到 GitHub 界面去创建 issue。如果 AI 能像一个 GitHub 助理一样，直接帮你完成这个任务就好了！

但是，让 AI 直接调用 GitHub 的 API 也需要处理很多细节：如何进行身份验证？如何构造正确的 API 请求？如何解析返回的结果？这对于 AI 来说太复杂，也不安全。

**API 集成服务器** 就是为了解决这类问题而生的。它充当了 AI 助手和特定 Web API（如 GitHub API）之间的专业“翻译”和“执行者”。

## 什么是 API 集成服务器？

> API 集成服务器是一种特殊的 [MCP 服务器](01_mcp_服务器_.md)，它充当 AI 助手与某个特定的外部 Web API 之间的桥梁。它封装了与该 API 通信的所有复杂细节，包括认证、请求格式化、错误处理和响应解析。AI 助手只需要通过标准的 [MCP 工具](02_mcp_工具_.md) 与之交互，服务器就会代表助手去和外部 API “对话”。

**打个比方：**

想象一下，你想让你的智能管家（AI 助手）帮你预订一家法国餐厅。管家自己可能不懂法语（API 细节），也不知道如何与餐厅的预订系统（API）交互。

这时，你需要一个**专业的“法国餐厅预订代理”**（API 集成服务器）。这个代理精通法语，了解餐厅的预订流程（API 规范），并且有餐厅的授权（API 密钥或令牌）可以帮你预订。

你的智能管家只需要用通用语言告诉代理：“帮我在‘Le Fancy Restaurant’预订一个今晚 7 点两人桌。” 代理就会处理所有与餐厅沟通的细节，完成预订，然后告诉管家结果。

**GitHub 服务器就是这样一个代理**，专门负责与 GitHub API 打交道。

## GitHub 服务器：你的 GitHub 专家

GitHub 服务器 (`@modelcontextprotocol/server-github`) 就是一个典型的 API 集成服务器。它就像一个 GitHub 专家，AI 助手可以通过它来执行各种 GitHub 操作，例如：

*   搜索仓库 (`search_repositories`)
*   创建或更新文件 (`create_or_update_file`, `push_files`)
*   管理 Issue (`create_issue`, `list_issues`, `update_issue`, `get_issue`, `add_issue_comment`)
*   管理 Pull Request (`create_pull_request`, `get_pull_request`, `list_pull_requests`, `merge_pull_request`, ...)
*   管理分支 (`create_branch`)
*   还有更多... (可以查看 `src/github/README.md` 获取完整列表)

它处理了与 GitHub API 通信的所有细节，让 AI 助手可以专注于任务本身，而不需要了解 REST API、认证令牌等底层技术。

**关键点：认证**

要让 GitHub 服务器代表你执行操作（比如在你的私有仓库创建 Issue），它需要向 GitHub 证明自己得到了你的授权。这通常通过 **GitHub 个人访问令牌 (Personal Access Token - PAT)** 来实现。这就像是给 GitHub 服务器一把能打开你 GitHub 账户特定权限的“钥匙”。关于如何安全地传递这把“钥匙”，我们在 [第 6 章：认证与配置](06_认证与配置_.md) 中已经详细讨论过了。

## 如何使用：配置 GitHub 服务器

使用 GitHub 服务器的第一步就是在你的 AI 助手客户端（如 Claude 桌面版）中配置它，并提供必要的认证信息（你的 PAT）。

**1. 获取 GitHub PAT:**
你需要先在 GitHub 网站上创建一个 PAT。
*   前往 GitHub 的 [Personal access tokens](https://github.com/settings/tokens) 页面（需要登录）。
*   点击 "Generate new token"。
*   给令牌起个名字（例如 "mcp-server-token"）。
*   选择合适的过期时间。
*   选择令牌的**作用域 (scopes)**。你需要根据希望 AI 助手执行的操作来勾选相应的权限。例如，要创建 Issue，你需要 `repo` 作用域下的 `public_repo` (用于公共仓库) 或完整的 `repo` (用于私有仓库)。要管理仓库，可能还需要 `admin:repo_hook` 等。**请仔细阅读每个作用域的描述，并遵循最小权限原则。**
*   点击 "Generate token"。
*   **立即复制生成的令牌**，并妥善保管。这个令牌只会显示一次！

**2. 在客户端配置服务器:**
获取 PAT 后，你需要将其配置到 AI 助手的配置文件（例如 Claude 桌面的 `claude_desktop_config.json`）中。我们使用 [第 6 章：认证与配置](06_认证与配置_.md) 中介绍的**环境变量**方式来安全地传递 PAT。

**使用 NPX 配置示例:**

```json
{
  "mcpServers": {
    "github": { // 给服务器起个名字
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-github" // GitHub 服务器的 npm 包
      ],
      "env": { // <= 使用环境变量传递敏感信息
        "GITHUB_PERSONAL_ACCESS_TOKEN": "ghp_你的GitHub令牌粘贴在这里" // <= 把你的 PAT 放在这里
      }
    }
    // ... 其他服务器配置 ...
  }
}
```

**使用 Docker 配置示例:**

```json
{
  "mcpServers": {
    "github": {
      "command": "docker",
      "args": [
        "run",
        "-i", // 允许交互
        "--rm", // 退出后删除容器
        "-e", // <= Docker 参数，用于设置环境变量
        "GITHUB_PERSONAL_ACCESS_TOKEN", // 环境变量名称
        "mcp/github" // Docker 镜像名称
      ],
      "env": { // <= 客户端将这个值传递给 Docker
        "GITHUB_PERSONAL_ACCESS_TOKEN": "ghp_你的GitHub令牌粘贴在这里"
      }
    }
  }
}
```

**解释：**

*   `env` 字段告诉 AI 助手客户端，在启动 `github` 服务器进程之前，需要设置一个名为 `GITHUB_PERSONAL_ACCESS_TOKEN` 的环境变量。
*   这个环境变量的值就是你刚刚生成的 GitHub PAT。
*   GitHub 服务器进程启动后，会读取这个环境变量，并用它来向 GitHub API 进行认证。

配置完成后，AI 助手启动时就能带上正确的“钥匙”来运行 GitHub 服务器了。

## 如何使用：调用 GitHub 工具

配置好服务器并运行后，AI 助手就可以使用 GitHub 服务器提供的各种 [MCP 工具](02_mcp_工具_.md)了。这些工具是对 GitHub API 功能的安全封装。以下是一些常用的工具示例：

*   `search_repositories` (搜索仓库)
    *   描述: 根据关键词搜索 GitHub 仓库。
    *   输入: `{ "query": "搜索关键词" }` (还有分页等可选参数)
    *   输出: 匹配的仓库列表信息。
*   `create_issue` (创建 Issue)
    *   描述: 在指定的仓库中创建一个新的 Issue。
    *   输入: `{ "owner": "仓库所有者用户名或组织名", "repo": "仓库名", "title": "Issue 标题", "body": "Issue 描述 (可选)" }` (还有标签、分配人等可选参数)
    *   输出: 新创建的 Issue 的详细信息。
*   `get_file_contents` (获取文件内容)
    *   描述: 获取仓库中指定文件或目录的内容。
    *   输入: `{ "owner": "所有者", "repo": "仓库名", "path": "文件或目录路径", "branch": "分支名 (可选)" }`
    *   输出: 文件内容（如果是文件）或目录列表（如果是目录）。
*   `create_pull_request` (创建 PR)
    *   描述: 创建一个新的 Pull Request (拉取请求)。
    *   输入: `{ "owner": "所有者", "repo": "仓库名", "title": "PR 标题", "head": "包含更改的分支名", "base": "要合并到的目标分支名" }` (还有描述、草稿状态等可选参数)
    *   输出: 新创建的 PR 的详细信息。
*   `list_issues` (列出 Issues)
    *   描述: 列出仓库中的 Issues，可以按状态、标签等过滤。
    *   输入: `{ "owner": "所有者", "repo": "仓库名", "state": "open" | "closed" | "all" (可选) }` (还有标签、排序、分页等可选参数)
    *   输出: 符合条件的 Issue 列表。

要了解所有可用的工具及其详细参数，请查阅 `src/github/README.md` 文件。

## 解决用例：创建 GitHub Issue

现在，让我们回到本章开头的用例：让 AI 助手在 `octocat/Spoon-Knife` 仓库创建一个标题为“文档链接失效”，内容为“README 文件中的安装指南链接无法访问”的 Issue。

1.  **你向 AI 提问:** “请在 `octocat/Spoon-Knife` 仓库创建一个 Issue，标题是‘文档链接失效’，内容是‘README 文件中的安装指南链接无法访问’。”
2.  **AI 决定:** AI 分析你的请求，识别出需要创建一个 GitHub Issue。它知道自己连接了 `github` 服务器，并且这个服务器有一个 `create_issue` 工具。
3.  **AI 发送请求:** AI 助手向 `github` 服务器发送一个 `callTool` [MCP 请求](01_mcp_服务器_.md)，内容大致如下：
    ```json
    {
      "method": "callTool",
      "params": {
        "name": "create_issue", // 工具名
        "arguments": {        // 参数
          "owner": "octocat",
          "repo": "Spoon-Knife",
          "title": "文档链接失效",
          "body": "README 文件中的安装指南链接无法访问"
        }
      }
    }
    ```
4.  **服务器接收请求:** `github` 服务器收到这个 JSON 请求。
5.  **服务器准备与认证:** 服务器解析请求，知道要调用 `create_issue` 工具，并获取了 `owner`, `repo`, `title`, `body` 参数。服务器从启动时设置的环境变量中读取 `GITHUB_PERSONAL_ACCESS_TOKEN`。
6.  **服务器执行操作 (调用 API):** 服务器使用读取到的 PAT，构造一个向 GitHub API 发送的请求。它会向 `https://api.github.com/repos/octocat/Spoon-Knife/issues` 这个地址发送一个 HTTP POST 请求，请求体包含 `title` 和 `body`。请求头中会包含 `Authorization: Bearer ghp_你的GitHub令牌...` 用于认证。
7.  **GitHub API 处理:** GitHub API 接收到请求，验证令牌和权限，然后在 `octocat/Spoon-Knife` 仓库中创建新的 Issue。创建成功后，API 返回包含新 Issue 信息的 JSON 数据。
8.  **服务器处理响应:** `github` 服务器收到来自 GitHub API 的成功响应（JSON 数据）。
9.  **服务器发送 MCP 响应:** 服务器将 API 返回的 Issue 信息（可能经过一些格式化）打包成一个成功的 MCP 响应，发送回 AI 助手：
    ```json
    {
      "result": {
        "content": [{
          "type": "text",
          // 这里是包含新 Issue 信息的 JSON 字符串或格式化文本
          "text": "{\"url\": \"...\", \"number\": 123, \"title\": \"文档链接失效\", ...}"
        }],
        "isError": false
      }
    }
    ```
    如果 API 调用失败（例如令牌无效、权限不足、仓库不存在），GitHub API 会返回错误，服务器会捕获这个错误，并返回一个 `isError: true` 的 MCP 响应，其中包含错误信息。
10. **AI 呈现结果:** AI 助手收到响应，解析出新创建 Issue 的信息（比如 Issue 编号和链接），并告诉你：“好的，我已经为你创建了 Issue #123，链接是：[...]"。

通过这个流程，AI 助手在你的指令下，通过 GitHub 服务器这个“代理”，成功地与 GitHub API 交互并完成了任务。

## 内部实现：幕后探秘

让我们简单了解一下 GitHub 服务器（TypeScript 实现）内部是如何工作的。

### 非代码流程

1.  **启动与认证:**
    *   AI 助手客户端根据配置，使用 `npx` 或 `docker` 启动 GitHub 服务器进程。
    *   包含 GitHub PAT 的环境变量 `GITHUB_PERSONAL_ACCESS_TOKEN` 被设置并传递给服务器进程。
    *   服务器进程启动后，在其初始化代码中读取这个环境变量，并将其存储起来以备后用。
2.  **接收工具调用:**
    *   服务器通过 MCP 连接接收到 `callTool` 请求（比如 `create_issue`）。
3.  **解析与分发:**
    *   服务器的 `callTool` 请求处理函数被触发。
    *   它解析请求中的工具名称 (`name`) 和参数 (`arguments`)。通常会使用像 `zod` 这样的库来验证参数是否符合预期格式。
    *   根据工具名称，将请求分发给相应的处理逻辑（通常是导入的单独函数，例如 `createIssue` 函数）。
4.  **构造并执行 API 请求:**
    *   具体的工具处理函数（如 `createIssue`）接收到验证后的参数。
    *   它调用一个通用的 HTTP 请求函数（如 `githubRequest`）。
    *   `githubRequest` 函数负责：
        *   构造目标 GitHub API 的 URL (例如 `https://api.github.com/repos/{owner}/{repo}/issues`)。
        *   准备请求体 (例如，包含 `title` 和 `body` 的 JSON 对象)。
        *   设置必要的 HTTP 请求头，最重要的是添加 `Authorization: Bearer <你的 PAT>` 用于认证。
        *   使用 `fetch` API 发起实际的 HTTP 请求。
5.  **处理 API 响应:**
    *   `githubRequest` 函数等待 GitHub API 的响应。
    *   它检查响应状态码。如果是 `2xx` (成功)，则解析响应体 (通常是 JSON)。
    *   如果是 `4xx` 或 `5xx` (错误)，则解析错误信息，并抛出一个自定义的错误类型 (如 `GitHubAuthenticationError`, `GitHubResourceNotFoundError` 等)。
6.  **返回 MCP 结果:**
    *   工具处理函数接收到 `githubRequest` 返回的数据或捕获到其抛出的错误。
    *   它将成功的数据或格式化后的错误信息包装成 MCP 响应格式 (`{ content: [...], isError: ... }`)。
    *   这个 MCP 响应通过连接发送回 AI 助手。

### 时序图示例 (创建 Issue)

```mermaid
sequenceDiagram
    participant 用户
    participant AI助手 (客户端)
    participant GitHub服务器 (MCP)
    participant githubRequest (内部工具)
    participant GitHub API (外部)

    用户 ->> AI助手 (客户端): "帮我在 octocat/Spoon-Knife 创建 Issue..."
    AI助手 (客户端) ->> GitHub服务器 (MCP): MCP 请求 (callTool: create_issue, args: {...})
    GitHub服务器 (MCP) ->> GitHub服务器 (MCP): 解析请求, 调用内部 createIssue 函数
    GitHub服务器 (MCP) ->> githubRequest (内部工具): 调用 githubRequest(url, {method: 'POST', body: {...}})
    githubRequest (内部工具) ->> githubRequest (内部工具): 读取 PAT, 构造请求头 (Authorization: Bearer ...)
    githubRequest (内部工具) ->> GitHub API (外部): 发送 HTTP POST 请求到 /repos/.../issues
    GitHub API (外部) -->> githubRequest (内部工具): 返回 HTTP 201 Created 和 Issue JSON 数据
    githubRequest (内部工具) -->> GitHub服务器 (MCP): 返回解析后的 Issue 数据
    GitHub服务器 (MCP) ->> GitHub服务器 (MCP): 格式化结果为 TextContent
    GitHub服务器 (MCP) ->> AI助手 (客户端): MCP 响应 (content: Issue JSON, isError: false)
    AI助手 (客户端) ->> 用户: "Issue #123 已创建！链接是..."
```

### 代码实现概览

核心逻辑位于 `src/github/index.ts` (主服务器文件), `src/github/operations/*.ts` (具体工具实现) 和 `src/github/common/utils.ts` (通用工具函数)。

**1. 读取 PAT (在 `common/utils.ts` 的 `githubRequest` 函数中)**

这个通用函数在每次调用 GitHub API 时都会读取 PAT。

```typescript
// src/github/common/utils.ts (简化)
import { getUserAgent } from "universal-user-agent";
import { createGitHubError } from "./errors.js";
import { VERSION } from "./version.js";

// 定义请求选项类型
type RequestOptions = {
  method?: string;
  body?: unknown;
  headers?: Record<string, string>;
}

const USER_AGENT = `modelcontextprotocol/servers/github/v${VERSION} ${getUserAgent()}`;

// 通用的 GitHub API 请求函数
export async function githubRequest(
  url: string,
  options: RequestOptions = {}
): Promise<unknown> {
  // 准备请求头
  const headers: Record<string, string> = {
    "Accept": "application/vnd.github.v3+json",
    "Content-Type": "application/json",
    "User-Agent": USER_AGENT,
    ...options.headers,
  };

  // >>> 关键: 如果环境变量中有 PAT，则添加到 Authorization 头 <<<
  if (process.env.GITHUB_PERSONAL_ACCESS_TOKEN) {
    headers["Authorization"] = `Bearer ${process.env.GITHUB_PERSONAL_ACCESS_TOKEN}`;
  } else {
    // (实际代码可能在这里警告或抛出错误，表明缺少认证)
    console.warn("警告：未找到 GITHUB_PERSONAL_ACCESS_TOKEN 环境变量");
  }

  // 使用 fetch API 发送请求
  const response = await fetch(url, {
    method: options.method || "GET",
    headers, // 包含认证信息的头
    body: options.body ? JSON.stringify(options.body) : undefined,
  });

  // (省略响应处理和错误处理...)
  const responseBody = await parseResponseBody(response); // 解析响应

  if (!response.ok) { // 如果响应状态码不是 2xx
    throw createGitHubError(response.status, responseBody); // 抛出自定义错误
  }

  return responseBody; // 返回解析后的响应体
}

// (省略 parseResponseBody 和 buildUrl 函数)
```
这段代码展示了在每次调用 `githubRequest` 时，如何检查环境变量 `GITHUB_PERSONAL_ACCESS_TOKEN` 并将其添加到请求头中，从而实现对 GitHub API 的认证。

**2. 处理 `create_issue` 工具调用 (在 `index.ts` 主函数中分发，在 `operations/issues.ts` 中实现)**

主服务器文件 `index.ts` 负责接收请求并分发给相应的操作函数。

```typescript
// src/github/index.ts (简化)
import { server } from "./server"; // 假设 server 实例已创建和配置
import { CallToolRequestSchema } from "@modelcontextprotocol/sdk/types.js";
import { z } from 'zod';
// 导入操作函数
import * as issues from './operations/issues.js';
// ... 其他导入 ...

server.setRequestHandler(CallToolRequestSchema, async (request) => {
  try {
    if (!request.params.arguments) {
      throw new Error("需要参数");
    }

    // 根据工具名称分发
    switch (request.params.name) {
      // ... 其他工具 case ...

      case "create_issue": {
        // 1. 使用 zod 验证输入参数是否符合预期模式
        const args = issues.CreateIssueSchema.parse(request.params.arguments);
        // 从参数中分离 owner, repo 和其他选项
        const { owner, repo, ...options } = args;

        // 2. 调用导入的 createIssue 操作函数
        const issue = await issues.createIssue(owner, repo, options);

        // 3. 格式化成功响应
        return {
          content: [{ type: "text", text: JSON.stringify(issue, null, 2) }],
        };
      }

      // ... 其他工具 case ...

      default:
        throw new Error(`未知的工具: ${request.params.name}`);
    }
  } catch (error) {
    // (省略错误处理，实际代码会格式化 zod 错误和 GitHub API 错误)
    const message = error instanceof Error ? error.message : String(error);
    return { content: [{ type: 'text', text: `错误: ${message}` }], isError: true };
  }
});

// (省略服务器运行代码 runServer())
```

`index.ts` 文件中的 `callTool` 处理器负责接收 MCP 请求，验证参数，然后调用从 `operations/issues.ts` 导入的 `createIssue` 函数来执行具体操作。

**3. `createIssue` 操作函数 (在 `operations/issues.ts` 中)**

这个函数负责调用通用的 `githubRequest` 来与 API 交互。

```typescript
// src/github/operations/issues.ts (简化)
import { z } from "zod";
// 导入通用的 githubRequest 函数
import { githubRequest, buildUrl } from "../common/utils.js";

// 定义 createIssue 需要的选项模式
export const CreateIssueOptionsSchema = z.object({
  title: z.string(),
  body: z.string().optional(),
  // ... 其他选项如 assignees, milestone, labels ...
});

// 定义 createIssue 的完整输入模式 (包含 owner 和 repo)
export const CreateIssueSchema = z.object({
  owner: z.string(),
  repo: z.string(),
  ...CreateIssueOptionsSchema.shape, // 合并选项模式
});

// 创建 Issue 的函数
export async function createIssue(
  owner: string, // 仓库所有者
  repo: string,  // 仓库名称
  options: z.infer<typeof CreateIssueOptionsSchema> // 包含 title, body 等
) {
  // 调用通用的 githubRequest 函数
  return githubRequest(
    // 构造 GitHub API 的 URL
    `https://api.github.com/repos/${owner}/${repo}/issues`,
    {
      method: "POST", // 使用 POST 方法
      body: options, // 将 title, body 等作为请求体发送
    }
  );
}

// (省略 listIssues, updateIssue, addIssueComment 等其他函数)
```
`createIssue` 函数本身非常简洁。它接收必要的参数（owner, repo 和包含 title/body 的 options 对象），然后直接调用 `githubRequest`，指定 API 的 URL、HTTP 方法 (`POST`) 和请求体 (`options`)。所有认证和实际的 HTTP 通信都由 `githubRequest` 处理。

这种将通用逻辑（如认证、HTTP 请求）和具体操作逻辑（如创建 Issue、搜索仓库）分离的方式，使得代码更易于维护和扩展。

## 总结

在本章中，我们学习了 **API 集成服务器** 的概念，并以 **GitHub 服务器** 作为具体示例。我们了解到：

*   API 集成服务器是 AI 助手与外部 Web API 之间的**安全桥梁和翻译器**。
*   GitHub 服务器封装了与 GitHub API 交互的复杂性，使 AI 能够执行搜索仓库、管理 Issue/PR、操作文件等任务。
*   使用 GitHub 服务器需要先获取一个具有适当权限的 **GitHub 个人访问令牌 (PAT)**。
*   需要在 AI 助手客户端配置中，通过**环境变量**安全地将 PAT 传递给服务器。
*   AI 助手通过调用服务器提供的各种 [MCP 工具](02_mcp_工具_.md)（如 `create_issue`）来完成任务。
*   服务器内部使用 PAT 向 GitHub API 进行认证，并代为执行操作。

API 集成服务器极大地扩展了 AI 助手的实用性，让它们能够接入并利用丰富多样的在线服务。

---

至此，我们已经探索了 MCP 的核心概念（服务器、工具、资源、提示）、通用服务器（Everything）、基础服务器（文件系统、Git、数据库）以及 API 集成服务器（GitHub）。本系列教程希望能帮助你理解 MCP 的工作原理以及如何利用 `servers` 项目中的参考实现来安全地扩展 AI 助手的能力。祝你在构建智能、安全的 AI 应用道路上一帆风顺！

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)