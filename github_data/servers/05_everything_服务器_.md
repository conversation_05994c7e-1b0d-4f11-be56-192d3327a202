# Chapter 5: Everything 服务器


在前面的章节中，我们分别探讨了 [MCP 服务器](01_mcp_服务器_.md) 的基本概念，以及它们提供的核心能力：[MCP 工具](02_mcp_工具_.md)、[MCP 资源](03_mcp_资源_.md) 和 [MCP 提示](04_mcp_提示_.md)。我们了解了 AI 助手如何利用这些构建块来安全地与外部世界交互、获取信息和启动任务流程。

现在，想象一下你正在开发一个自己的 MCP **客户端**（比如一个新的 AI 助手界面），或者你想深入理解 MCP 协议的各种细微之处。你需要一个“靶场”或者“样品间”来测试你的客户端实现，看看它是否能正确处理各种类型的工具调用、资源请求（包括分页和订阅）、提示交互、日志消息、采样请求等等。

这就是本章的主角——**Everything 服务器**——的作用所在。

## 动机：一个全面的 MCP 功能“样品间”

开发和测试 MCP 客户端时，如果每次只想测试一个特定功能（比如资源订阅），就需要找到并运行一个恰好实现了该功能的服务器（比如 [SQLite 服务器](09_数据库服务器__sqlite_示例_.md) 的 `memo://insights`）。如果你想测试多种功能，可能需要同时运行多个不同的服务器，这会很麻烦。

**Everything 服务器**解决了这个问题。它是一个特殊的 MCP 服务器，其唯一目的就是**展示和测试 MCP 协议的几乎所有特性**。它就像一个 MCP 功能的“大杂烩”或“样品间”，专门设计用来帮助开发者理解 MCP 的能力范围，并验证他们的客户端实现是否健壮。

> **核心思想：** Everything 服务器本身不执行任何有意义的实际任务（比如操作文件或搜索网页），但它全面地实现了 MCP 协议的各种功能点，是一个理想的测试和学习工具。

## Everything 服务器展示了什么？

根据它的 `README.md` 文件（位于 `src/everything/README.md`），Everything 服务器实现了以下 MCP 功能，让我们回顾一下这些概念，看看它是如何展示的：

1.  **[MCP 工具](02_mcp_工具_.md)**：
    *   **`echo`**: 最简单的工具，将输入的消息原样返回。用于测试基本的工具调用和响应处理。
    *   **`add`**: 将两个数字相加。测试带有数字类型输入的工具。
    *   **`longRunningOperation`**: 模拟一个耗时操作，并在此期间发送进度更新通知。测试客户端对 `notifications/progress` 的处理。
    *   **`sampleLLM`**: 请求客户端（AI 助手）进行一次 LLM（大语言模型）采样。测试 `sampling/createMessage` 这个特殊请求。
    *   **`getTinyImage`**: 返回一个小的 PNG 图片数据。测试处理二进制 (`image`) 内容类型。
    *   **`printEnv`**: 打印服务器运行时的环境变量。用于调试服务器配置。
    *   **`annotatedMessage`**: 返回带有**注解 (Annotations)** 的消息。注解是附加到消息内容的元数据（例如优先级、目标受众），客户端可以用它来决定如何显示信息。这是测试注解处理的好例子。
    *   **`getResourceReference`**: 返回一个包含**资源引用 (Resource Reference)** 的消息。这种消息可以直接嵌入一个 [MCP 资源](03_mcp_资源_.md) 的内容或链接，测试客户端对 `type: "resource"` 内容的处理。

2.  **[MCP 资源](03_mcp_资源_.md)**：
    *   提供了 100 个测试资源（URI 格式为 `test://static/resource/{数字}`）。
    *   偶数 ID 的资源是纯文本 (`text/plain`)。
    *   奇数 ID 的资源是二进制数据 (`application/octet-stream`)。
    *   支持**分页 (Pagination)**：`listResources` 请求可以分批返回资源列表，测试客户端处理 `nextCursor`。
    *   支持**订阅 (Subscription)**：客户端可以订阅资源，服务器会模拟资源更新并发送 `notifications/resources/updated` 通知（大约每 10 秒一次）。
    *   支持**资源模板 (Resource Templates)**：提供了 `test://static/resource/{id}` 这样的模板，让客户端知道可以通过替换 `{id}` 来构造有效的资源 URI。

3.  **[MCP 提示](04_mcp_提示_.md)**：
    *   **`simple_prompt`**: 一个不需要参数的简单提示。
    *   **`complex_prompt`**: 一个需要必需参数 (`temperature`) 和可选参数 (`style`) 的复杂提示，返回包含多轮消息和图片的对话。测试参数处理和多消息/多类型内容的提示。
    *   **`resource_prompt`**: 一个需要 `resourceId` 参数，并在初始消息中嵌入相应 [MCP 资源](03_mcp_资源_.md) 引用的提示。测试在提示中直接使用资源。

4.  **其他特性**：
    *   **日志记录 (Logging)**：服务器会定期（约每 20 秒）发送不同级别的日志消息 (`notifications/message`)，测试客户端的日志处理和级别过滤 (`SetLevelRequestSchema`)。
    *   **标准错误输出 (Stderr)**: 服务器会定期 (约每 30 秒) 发送标准错误流消息 (`notifications/stderr`)。
    *   **自动完成 (Completion)**：为提示参数和资源引用提供自动完成建议，测试 `complete` 请求。
    *   **SSE 传输 (Server-Sent Events)**：除了标准的 stdio 传输，还提供了一个 SSE 的运行方式 (`sse.ts`)，用于测试基于 HTTP 的 MCP 连接。

基本上，你能想到的 MCP 主要功能，Everything 服务器都提供了一个示例实现。

## 如何使用 Everything 服务器？

Everything 服务器的主要用途是**测试 MCP 客户端**。假设你正在开发一个类似 Claude 桌面版的应用，你想确保你的应用能正确：
*   列出并调用所有类型的工具，包括带进度条的。
*   正确显示带注解的消息。
*   正确处理分页和订阅资源。
*   正确处理带参数和嵌入资源的提示。
*   正确接收和显示日志。
*   ...等等。

你可以按照以下步骤使用 Everything 服务器：

1.  **配置客户端连接：**
    在你的客户端配置文件（例如 Claude 桌面版的 `claude_desktop_config.json`）中，添加 Everything 服务器的启动配置。根据其 `README.md`，配置如下：

    ```json
    {
      "mcpServers": {
        "everything": { // 给这个服务器起个名字，比如 "everything"
          "command": "npx", // 使用 npx 来运行
          "args": [
            "-y", // npx 参数，自动安装包
            "@modelcontextprotocol/server-everything" // Everything 服务器的 npm 包名
          ]
        }
      }
    }
    ```
    这段配置告诉客户端：有一个名为 `everything` 的服务器，需要通过运行 `npx -y @modelcontextprotocol/server-everything` 命令来启动它。

2.  **启动客户端：**
    启动你的 MCP 客户端。客户端会根据配置找到 `everything` 服务器的配置，并执行 `npx` 命令来启动服务器进程，然后通过标准输入/输出 (stdio) 或其他配置的传输方式（如 SSE）建立 MCP 连接。

3.  **探索和测试：**
    一旦连接成功，你就可以在客户端界面中（或者通过编程方式）与 Everything 服务器交互了：
    *   **发现功能：** 发送 `listTools`, `listResources`, `listPrompts` 请求，看看服务器报告了哪些可用的工具、资源和提示。你的客户端应该能正确解析和显示这些列表。
    *   **调用简单工具：** 尝试调用 `echo` 工具，例如发送 `callTool` 请求，参数为 `{ "name": "echo", "arguments": { "message": "你好 MCP!" } }`。检查你的客户端是否收到了包含 `"Echo: 你好 MCP!"` 的响应。
    *   **读取资源：** 尝试读取一个文本资源，例如发送 `readResource` 请求，参数为 `{ "uri": "test://static/resource/2" }`。检查客户端是否收到了包含 `"Resource 2: This is a plaintext resource"` 内容的响应。
    *   **获取简单提示：** 尝试获取 `simple_prompt`，发送 `getPrompt` 请求，参数为 `{ "name": "simple_prompt" }`。检查客户端是否收到了包含预定义用户消息的响应。
    *   **测试高级功能：** 逐步测试更复杂的功能，比如调用 `longRunningOperation` 并观察进度通知，调用 `annotatedMessage` 并检查注解的处理，订阅资源 `test://static/resource/10` 并等待更新通知等。

通过与 Everything 服务器进行这些交互，你可以全面地验证你的 MCP 客户端是否符合协议规范，并且能够处理各种边界情况。

## 内部实现概览

Everything 服务器主要是用 TypeScript 和官方的 `@modelcontextprotocol/sdk` 构建的。它的核心代码位于 `src/everything/everything.ts` 文件中。

**非代码流程：**

当客户端与 Everything 服务器交互时，大致流程如下：

1.  **客户端启动服务器：** 如上所述，客户端通过 `npx` 命令启动服务器进程。
2.  **建立连接：** 服务器启动后，通过 `StdioServerTransport` (标准模式) 或 `SSEServerTransport` (SSE 模式) 与客户端建立 MCP 连接。
3.  **服务器初始化：** 服务器代码 (`createServer` 函数) 创建一个 `Server` 实例，并声明它支持的功能（工具、资源、提示、日志等）。它还会设置一些定时器，用于模拟资源更新和发送日志消息。
4.  **客户端请求：** 客户端发送一个 MCP 请求，比如 `listTools` 或 `callTool`。
5.  **服务器路由：** SDK 接收到请求，根据请求的 `method` (例如 `"listTools"`) 查找对应的处理函数。这些处理函数是通过 `server.setRequestHandler()` 注册的。
6.  **执行处理函数：** 服务器执行找到的处理函数。
    *   对于 `listTools`，函数会返回预定义的工具列表（包含 `echo`, `add` 等）。
    *   对于 `callTool` (例如调用 `echo`)，函数会解析参数，执行简单的逻辑（直接返回处理后的消息），然后返回结果。
    *   对于 `listResources`，函数会根据分页参数 (`cursor`) 从 `ALL_RESOURCES` 数组中切片，并计算下一个 `nextCursor`。
    *   对于 `subscribe`，函数会将请求的 URI 添加到 `subscriptions` 集合中，并可能触发一次采样请求。
    *   ... 其他请求同理。
7.  **服务器响应：** 处理函数返回结果，SDK 将其包装成 JSON-RPC 响应格式，通过连接发送回客户端。
8.  **（异步）通知：** 服务器内部的定时器会触发，例如，每 10 秒检查 `subscriptions` 集合，并为每个订阅的 URI 发送 `notifications/resources/updated` 通知；或者随机选择日志消息发送 `notifications/message` 通知。

**时序图示例 (调用 `echo` 工具):**

```mermaid
sequenceDiagram
    participant 客户端 (例如 Claude 桌面)
    participant Everything服务器

    客户端 (例如 Claude 桌面)->>Everything服务器: MCP 请求 (listTools)
    Everything服务器-->>客户端 (例如 Claude 桌面): MCP 响应 (包含 echo, add 等工具列表)
    客户端 (例如 Claude 桌面)->>Everything服务器: MCP 请求 (callTool: echo, args: {message: "你好"})
    Everything服务器->>Everything服务器: 执行 echo 工具的处理逻辑 (在 callTool 处理函数中)
    Everything服务器-->>客户端 (例如 Claude 桌面): MCP 响应 (content: [{type: "text", text: "Echo: 你好"}])
```

这个简单的图表展示了客户端如何发现 `echo` 工具，然后成功调用它的过程。

## 代码片段解析

让我们看几个 `src/everything/everything.ts` 中的简化代码片段，了解它是如何实现各项功能的。

**1. 服务器初始化和能力声明:**

```typescript
// 导入 SDK
import { Server } from "@modelcontextprotocol/sdk/server/index.js";
// ... 其他导入 ...

export const createServer = () => {
  // 创建服务器实例，提供名称和版本
  const server = new Server(
    {
      name: "example-servers/everything",
      version: "1.0.0",
    },
    {
      // 声明服务器支持的所有 MCP 功能
      capabilities: {
        prompts: {}, // 支持提示
        resources: { subscribe: true }, // 支持资源，且允许订阅
        tools: {}, // 支持工具
        logging: {}, // 支持日志记录
      },
    }
  );

  // ... (设置定时器等) ...

  return { server, cleanup };
};
```
这段代码创建了一个 `Server` 对象，并通过 `capabilities` 清楚地告诉客户端，它支持提示、资源（包括订阅）、工具和日志记录。

**2. 定义和处理 `echo` 工具:**

```typescript
// 导入 zod 用于模式定义
import { z } from "zod";
import { zodToJsonSchema } from "zod-to-json-schema";
// ... 其他导入 ...

// 定义 echo 工具的输入模式
const EchoSchema = z.object({
  message: z.string().describe("要回显的消息"), // 需要一个 message 字符串
});

// 枚举工具名称 (方便引用)
enum ToolName {
  ECHO = "echo",
  // ... 其他工具名称 ...
}

// 在 listTools 请求的处理函数中定义 echo 工具
server.setRequestHandler(ListToolsRequestSchema, async () => {
  const tools: Tool[] = [
    {
      name: ToolName.ECHO, // 工具名称
      description: "回显输入的消息", // 工具描述
      // 将 zod 模式转换为 JSON Schema 作为输入规范
      inputSchema: zodToJsonSchema(EchoSchema) as ToolInput,
    },
    // ... 其他工具定义 ...
  ];
  return { tools };
});

// 在 callTool 请求的处理函数中处理 echo 工具调用
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params; // 获取工具名和参数

  if (name === ToolName.ECHO) {
    // 使用 zod 验证输入参数是否符合 EchoSchema
    const validatedArgs = EchoSchema.parse(args);
    // 返回包含回显文本的响应内容
    return {
      content: [{ type: "text", text: `Echo: ${validatedArgs.message}` }],
    };
  }
  // ... 处理其他工具 ...
});
```
这里展示了定义 `echo` 工具（名称、描述、输入模式）并处理其调用的完整流程：验证输入，执行简单操作，返回结果。

**3. 定义和处理资源 (分页和读取):**

```typescript
// 定义所有资源的数组 (简化创建过程)
const ALL_RESOURCES: Resource[] = Array.from({ length: 100 }, (_, i) => {
  const uri = `test://static/resource/${i + 1}`;
  // ... (根据 i 的奇偶性创建文本或二进制资源对象) ...
});

const PAGE_SIZE = 10; // 每页显示 10 个

// 处理 listResources 请求 (支持分页)
server.setRequestHandler(ListResourcesRequestSchema, async (request) => {
  const cursor = request.params?.cursor; // 获取分页游标
  let startIndex = 0;
  if (cursor) { // 如果有游标，解码它
    startIndex = parseInt(atob(cursor), 10);
    // (省略了错误处理)
  }

  // 计算当前页的结束索引
  const endIndex = Math.min(startIndex + PAGE_SIZE, ALL_RESOURCES.length);
  // 获取当前页的资源
  const resources = ALL_RESOURCES.slice(startIndex, endIndex);

  let nextCursor: string | undefined;
  if (endIndex < ALL_RESOURCES.length) { // 如果还有更多资源
    // 计算并编码下一页的起始索引作为游标
    nextCursor = btoa(endIndex.toString());
  }

  // 返回当前页的资源和下一页的游标
  return { resources, nextCursor };
});

// 处理 readResource 请求
server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
  const uri = request.params.uri; // 获取请求的 URI
  // 检查 URI 是否符合预期的模式
  if (uri.startsWith("test://static/resource/")) {
    // 从 URI 中解析出资源索引
    const index = parseInt(uri.split("/").pop() ?? "", 10) - 1;
    // 检查索引是否有效
    if (index >= 0 && index < ALL_RESOURCES.length) {
      const resource = ALL_RESOURCES[index]; // 获取资源对象
      // 返回包含该资源内容的响应
      return { contents: [resource] };
    }
  }
  // 如果 URI 无效，抛出错误
  throw new Error(`未知的资源: ${uri}`);
});
```
这段代码展示了如何创建一组模拟资源，并在处理 `listResources` 时实现分页逻辑，以及在处理 `readResource` 时根据 URI 查找并返回特定资源。

**4. 处理订阅和更新通知:**

```typescript
let subscriptions: Set<string> = new Set(); // 用于存储订阅的 URI
let subsUpdateInterval: NodeJS.Timeout | undefined; // 定时器句柄

// 处理 subscribe 请求
server.setRequestHandler(SubscribeRequestSchema, async (request) => {
  const { uri } = request.params;
  subscriptions.add(uri); // 将 URI 添加到订阅集合
  // ... (可能触发一次采样请求) ...
  return {}; // 返回空对象表示成功
});

// 处理 unsubscribe 请求
server.setRequestHandler(UnsubscribeRequestSchema, async (request) => {
  subscriptions.delete(request.params.uri); // 从集合中移除 URI
  return {};
});

// 设置定时器，定期发送更新通知
subsUpdateInterval = setInterval(() => {
  for (const uri of subscriptions) { // 遍历所有已订阅的 URI
    // 发送资源更新通知
    server.notification({
      method: "notifications/resources/updated",
      params: { uri }, // 告知哪个 URI 更新了
    });
  }
  // (实际代码中是 10000 毫秒，即 10 秒)
}, 10000); // 每 10 秒触发一次
```
代码演示了如何记录客户端的订阅请求，并在定时器触发时，向所有订阅者发送资源更新通知。

**5. 其他功能点 (简述):**

*   **注解 (Annotations):** 在 `callTool` 处理 `annotatedMessage` 工具时，返回的消息内容会包含 `annotations` 字段，例如 `{ priority: 1.0, audience: ["user", "assistant"] }`。
*   **资源引用 (Resource Reference):** 在 `callTool` 处理 `getResourceReference` 工具时，返回的 `content` 数组中会包含一个类型为 `resource` 的项 `{ type: "resource", resource: resource }`，其中 `resource` 是被引用的资源对象。 `resource_prompt` 提示的 `getPrompt` 处理器也会返回类似的内容。
*   **采样 (Sampling):** `sampleLLM` 工具会调用 `requestSampling` 辅助函数，该函数构造一个 `sampling/createMessage` 请求并使用 `server.request()` 发送给客户端。
*   **日志 (Logging):** 另一个定时器 (`logsUpdateInterval`) 会定期随机选择日志级别和消息，并使用 `server.notification()` 发送 `notifications/message`。`SetLevelRequestSchema` 处理器则用来更新服务器端的日志过滤级别 `logLevel`。

通过浏览 `src/everything/everything.ts` 的完整代码，你可以找到 MCP 协议中几乎每个方面（如 `complete`, `notifications/stderr` 等）的具体实现示例。

## 总结

在本章中，我们认识了 Everything 服务器，一个特殊的 MCP 服务器，它就像一个协议功能的“大阅兵”或“样品间”。我们了解到：

*   Everything 服务器的主要目的是**测试 MCP 客户端**和**演示 MCP 协议的各种特性**。
*   它实现了广泛的功能，包括各种类型的[MCP 工具](02_mcp_工具_.md)（带进度、注解、采样请求、资源引用）、[MCP 资源](03_mcp_资源_.md)（分页、订阅、模板、不同 MIME 类型）以及[MCP 提示](04_mcp_提示_.md)（带参数、嵌入资源）等。
*   通过将其配置到 MCP 客户端（如 Claude 桌面版）中，开发者可以全面测试其客户端对 MCP 协议各种方面的处理能力。
*   它的源代码 (`src/everything/everything.ts`) 是学习如何使用官方 SDK 实现各种 MCP 功能的宝贵参考。

虽然 Everything 服务器本身不解决实际问题，但它对于构建健壮的 MCP 客户端和深入理解 MCP 协议至关重要。

在了解了 MCP 的核心概念和 Everything 服务器这个“万能”示例之后，接下来的章节我们将开始关注在实际应用中非常重要的方面：**认证**（确保服务器和客户端是可信的）和**配置**（如何安全地向服务器传递设置）。敬请期待下一章：[认证与配置](06_认证与配置_.md)。

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)