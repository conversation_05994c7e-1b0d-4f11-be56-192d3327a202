# 设计文档: DSL v2 功能扩展

**版本:** 0.1
**日期:** 2024-07-28
**作者:** Gemini & User

## 1. 目标回顾 (Goal Review)

当前 `dsl/v2` 的动画描述语言功能较为基础。为了能够生成如 `output/storyboard_mcp_nasdq.json` 中描述的丰富视觉效果，我们需要扩展 DSL，增加更多高层级、效果导向的函数，使其能够描述和生成更复杂的动画、布局和图表。目标是让大型语言模型 (LLM) 能够更容易地使用这些函数来创建所需的 Manim 动画。

## 2. 设计原则 (Design Principles)

*   **效果封装 (Effect Encapsulation):** 每个 DSL 函数应对应一个相对完整的视觉效果或叙事单元。
*   **LLM 友好 (LLM-Friendly):** 函数名和参数应直观，关注"内容"和"意图"。
*   **区分度 (Distinctiveness):** 不同函数应有清晰的适用场景和视觉区分度。
*   **可配置性 (Configurability):** 在高层封装的同时，保留关键参数（如数据、文本、样式）的配置能力。

## 3. DSL 函数详解 (DSL Function Details)

以下是我们提议新增的 8 个高层级 DSL 函数：

### 3.1 `AnimateCounter`

*   **目的/效果:** 创建一个动态变化的数字计数器，可以附带标签，并有强调效果。
*   **参数:**
    *   `target_value`: (必需) 最终计数值 (数字)。
    *   `label`: (可选) 显示在数字旁边的文本标签 (字符串)。
    *   `start_value`: (可选, 默认 0) 起始计数值 (数字)。
    *   `duration`: (可选, 默认 2) 动画持续时间 (秒)。
    *   `effect`: (可选, 默认 "zoom") 结尾强调效果 ('zoom', 'flash', 'none')。
    *   `unit`: (可选) 单位，如 "K+", "%" (字符串)。
*   **Manim 概念:** `DecimalNumber.animate.set_value()`, `Text`, `Transform`, `LaggedStart`.
*   **AST 节点:** `AnimateCounterNode`
*   **Visitor:** `visitors.animate_counter.visit_animate_counter`

### 3.2 `SideBySideComparison`

*   **目的/效果:** 创建一个分屏对比布局，左右两侧展示不同内容。
*   **参数:**
    *   `left_content`: (必需) 左侧内容 (字符串 - 文本, 代码, JSON, 或图像路径)。
    *   `right_content`: (必需) 右侧内容 (字符串)。
    *   `left_title`: (可选) 左侧标题 (字符串)。
    *   `right_title`: (可选) 右侧标题 (字符串)。
    *   `left_type`: (可选, 默认 "text") 左侧内容类型 ('text', 'code', 'json', 'image')。
    *   `right_type`: (可选, 默认 "text") 右侧内容类型。
    *   `transition`: (可选, 默认 "fadeIn") 内容出现动画 ('fadeIn', 'slideUp', 'none')。
    *   `vs_symbol`: (可选, 默认 True) 是否在中间显示 'VS' 符号 (布尔值)。
*   **Manim 概念:** `VGroup`, `Arrange`, `AnimationGroup`, `Code`, `Text`, `ImageMobject`.
*   **AST 节点:** `SideBySideComparisonNode`
*   **Visitor:** `visitors.side_by_side_comparison.visit_side_by_side_comparison`

### 3.3 `AnimateArchitectureDiagram`

*   **目的/效果:** 绘制并动画展示一个架构图或流程图。
*   **参数:**
    *   `nodes`: (必需) 节点列表，每个节点包含 `id`, `label`, (可选) `icon` 等信息 (列表)。
    *   `edges`: (必需) 连接边列表，每个边包含 `source_id`, `target_id`, (可选) `label`, `direction` ('->', '<-', '--') 等信息 (列表)。
    *   `layout`: (可选, 默认 "spring") 布局算法 ('spring', 'circular', 'planar', 'manual' - manual 需要节点提供位置信息)。
    *   `animation_type`: (可选, 默认 "drawConnections") 出现动画 ('drawConnections', 'nodesAppear', 'fadeIn')。
    *   `flow_path`: (可选) 需要高亮的数据流路径 (节点 ID 列表)。
*   **Manim 概念:** `Graph`, `DiGraph`, `Create`, `ShowPassingFlash`, layout algorithms, `ImageMobject` (for icons).
*   **AST 节点:** `AnimateArchitectureDiagramNode`
*   **Visitor:** `visitors.animate_architecture_diagram.visit_animate_architecture_diagram`

### 3.4 `AnimateChart`

*   **目的/效果:** 创建并动画展示指定类型的图表。
*   **参数:**
    *   `chart_type`: (必需) 图表类型 ('line', 'bar', 'radar')。
    *   `data`: (必需) 图表数据 (格式依赖于 `chart_type`，例如 line/bar 是 {label: value} 字典或列表，radar 是 {axis_label: value} 字典)。
    *   `title`: (可选) 图表标题 (字符串)。
    *   `animation_style`: (可选, 默认 "grow") 图表出现/更新动画 ('grow', 'fadeIn', 'draw', 'update')。
    *   `options`: (可选) 其他图表特定选项，如坐标轴标签、颜色等 (字典)。
*   **Manim 概念:** `Axes`, `LineGraph`, `BarChart`, custom radar chart logic, `Create`, `Transform`.
*   **AST 节点:** `AnimateChartNode`
*   **Visitor:** `visitors.animate_chart.visit_animate_chart`

### 3.5 `HighlightSequence`

*   **目的/效果:** 按顺序高亮一系列预先定义的元素。
*   **参数:**
    *   `elements`: (必需) 需要高亮的元素标识符列表 (字符串列表)。这些标识符需要通过其他 DSL 命令（如 `DisplayFormattedContent` 的 `id` 参数）预先定义。
    *   `highlight_type`: (可选, 默认 "flash") 高亮方式 ('flash', 'box', 'underline', 'color')。
    *   `duration_per_item`: (可选, 默认 1) 每个元素的高亮持续时间 (秒)。
    *   `color`: (可选) 高亮颜色。
*   **Manim 概念:** `Succession`, `Indicate`, `ShowPassingFlash`, `FocusOn`, Mobject Caching/Referencing.
*   **AST 节点:** `HighlightSequenceNode`
*   **Visitor:** `visitors.highlight_sequence.visit_highlight_sequence`

### 3.6 `ShowMediaWithAnnotation`

*   **目的/效果:** 展示一个图像或视频占位符，并添加标注或时间轴。
*   **参数:**
    *   `media_path`: (必需) 媒体文件路径 (字符串)。
    *   `media_type`: (可选, 默认 "image") 媒体类型 ('image', 'placeholder')。 (暂不支持 video)。
    *   `annotations`: (可选) 标注列表，每个标注包含 `text`, `position` (如 'topLeft', 'bottom', 或相对于媒体的坐标), `appearance_time` (可选, 秒) 等信息 (列表)。
    *   `show_timer`: (可选, 默认 False) 是否显示一个计时器 (布尔值)。
    *   `duration`: (可选) 如果显示计时器，计时器的总时长 (秒)。
*   **Manim 概念:** `ImageMobject`, `Text`, `VGroup`, `ValueTracker`, `DecimalNumber`.
*   **AST 节点:** `ShowMediaWithAnnotationNode`
*   **Visitor:** `visitors.show_media_with_annotation.visit_show_media_with_annotation`

### 3.7 `DisplayFormattedContent`

*   **目的/效果:** 以格式化的方式展示内容，如代码、JSON、Markdown 等。
*   **参数:**
    *   `content`: (必需) 要展示的内容 (字符串)。
    *   `content_type`: (可选, 默认 "text") 内容类型 ('text', 'code', 'json', 'markdown')。
    *   `language`: (可选) 如果 `content_type` 是 'code'，指定语言用于语法高亮 (字符串)。
    *   `style`: (可选, 默认 "default") 样式主题 (字符串)。
    *   `animation`: (可选, 默认 "write") 出现动画 ('write', 'fadeIn', 'none')。
    *   `id`: (可选) 为此内容块分配一个唯一标识符，用于后续被 `HighlightSequence` 或 `EmphasizeElement` 引用 (字符串)。
*   **Manim 概念:** `Code`, `Text`, `Paragraph`, `MarkupText`, `Write`. Mobject Caching/Referencing.
*   **AST 节点:** `DisplayFormattedContentNode`
*   **Visitor:** `visitors.display_formatted_content.visit_display_formatted_content`

### 3.8 `EmphasizeElement`

*   **目的/效果:** 对场景中的某个元素应用强调效果。
*   **参数:**
    *   `target_element_id`: (必需) 需要强调的元素的 ID (字符串，由其他命令如 `DisplayFormattedContent` 的 `id` 参数定义)。
    *   `effect_type`: (可选, 默认 "zoom_in_out") 强调效果 ('zoom_in_out', 'flash', 'pulse', 'highlight_box')。
    *   `scale_factor`: (可选, 默认 1.5) 如果效果涉及缩放，指定缩放比例。
    *   `color`: (可选) 如果效果涉及颜色，指定颜色。
    *   `duration`: (可选, 默认 1) 效果持续时间 (秒)。
*   **Manim 概念:** `Transform`, `FocusOn`, `Flash`, `Wiggle`, custom magnifier logic, Mobject Caching/Referencing.
*   **AST 节点:** `EmphasizeElementNode`
*   **Visitor:** `visitors.emphasize_element.visit_emphasize_element`

## 4. 关键技术点 (Key Technical Considerations)

*   **元素引用 (Element Referencing):** `HighlightSequence` 和 `EmphasizeElement` 需要能够引用场景中由其他命令创建的 Mobject。这可能需要在 `CodeGenerator` 中维护一个 Mobject 缓存/字典，通过用户在 DSL 中指定的 `id` 来存储和查找 Mobject 实例。
*   **DSL 语法定义:** 我们需要决定这些命令在 DSL 文件中如何表示。是继续使用 JSON 结构，还是定义更类似编程语言的语法？考虑到 LLM 的生成能力，JSON 可能是更稳妥的选择，每个命令可以是一个 JSON 对象，包含 `command_name` 和对应的参数。
*   **Visitor 实现:** 每个新的 Visitor 函数都需要仔细设计，以生成健壮、高效且视觉效果符合预期的 Manim 代码。需要处理好参数的默认值和可选性。
*   **错误处理:** 需要在 Parser, AST Builder 和 Code Generator 中添加适当的错误处理，以应对无效的 DSL 输入或参数。

## 5. 下一步计划 (Next Steps)

1.  **确认设计:** 请确认此设计文档是否满足需求。
2.  **实现规划:** 确定实现的优先级和顺序。建议分阶段实现，例如先实现几个核心函数（如 `DisplayFormattedContent`, `SideBySideComparison`, `AnimateCounter`），再逐步添加其他函数。
3.  **分支与开发:** 创建新的 Git 分支，开始实现 AST 节点定义、Parser/AST Builder 的更新以及 Visitor 函数的编写和测试。
