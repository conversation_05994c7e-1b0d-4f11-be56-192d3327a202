# 设计文档: 为 DisplayFormattedContentNode 添加语音旁白 (TTS) 功能

**版本:** 1.0
**日期:** 2024-08-03

## 1. 问题陈述

*   **目标:** 扩展 `DisplayFormattedContentNode`，使其能够为显示的文本、代码等内容添加同步的语音旁白。
*   **价值:** 增强生成视频的表现力、信息传递效率和可访问性。
*   **核心需求:**
    *   DSL 节点 (`DisplayFormattedContentNode`) 需添加 `narration: Optional[str]` 属性控制 TTS。
    *   生成的 Manim 代码利用 `FeynmanScene` 的 `self.voiceover` 包裹动画/添加操作。
    *   将 `narration` 文本传递给 `voiceover`。
    *   动画效果（`Write`, `FadeIn`, `none`）在 `voiceover` 上下文中执行。
    *   依赖 `FeynmanScene` 已通过 `set_speech_service` 配置好 TTS 服务。

## 2. 设计方案

1.  **修改 DSL 节点定义 (`dsl/v2/ast_nodes.py`):**
    *   在 `DisplayFormattedContentNode` 类中，添加新字段 `narration: Optional[str] = None`。

2.  **修改代码生成器访问者 (`dsl/v2/visitors/display_formatted_content.py`):**
    *   在 `visit_display_formatted_content` 函数中，检查 `node.narration` 是否有效（非 `None` 且非空）。
    *   **如果 `narration` 有效:**
        *   使用 `with self.voiceover(f"{repr(node.narration)}") as tracker:` 包裹 Mobject 的添加/动画代码 (`self.play(...)` 或 `self.add(...)`)。
        *   将原动画/添加代码缩进到 `with` 块内。
        *   保留 `FadeIn` 和 `AddTextLetterByLetter` 的 `run_time` 参数。`manim-voiceover` 会处理同步。
        *   移除 `none` 动画后的 `self.wait(0.5)`，交由 `voiceover` 控制等待。
    *   **如果 `narration` 无效:**
        *   保持现有逻辑，不使用 `voiceover`。

## 3. 实现细节

*   `tracker.duration` 暂时不显式用于控制动画时长。
*   `AddTextLetterByLetter` 的 `run_time` 保持不变。
*   创建、定位、缩放 Mobject 的代码在 `with` 块之外或之前执行。

## 4. 待确认/未来考虑

*   后续如果需要更精细的同步控制，可以考虑使用 `tracker.duration`。
*   测试不同内容类型（文本、代码、Markdown）和动画效果下的 TTS 同步情况。
