#!/usr/bin/env python3
"""
素材扩充工具基类
定义了工具的基本接口和分类枚举
"""

from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, Optional


class ToolCategory(Enum):
    """工具分类枚举"""

    CONTENT_ORGANIZATION = "A_内容结构化组织"  # timeline、表格、公式、框架图、思维导图等
    MULTIMODAL_PRESENTATION = "B_多模态呈现"  # 动态数据图、图片展示、emoji、插图等
    DEEP_INSIGHTS = "C_深度洞察"  # 对比、评估、insights洞察、深度提问和问答
    SMART_INTERACTION = "D_智能交互"  # 详细例子、模拟器、交互式媒体等


class EnhancementTool(ABC):
    """素材扩充工具基类 - 替代原Strategy架构"""

    # 工具元信息 - 供智能选择器使用
    tool_name: str = ""
    tool_description: str = ""
    tool_category: ToolCategory = None

    @abstractmethod
    def get_tool_info(self) -> str:
        """获取工具信息 - 供智能选择器决策使用"""
        pass

    @abstractmethod
    def can_apply(self, content: str, purpose: str, context: dict[str, Any]) -> bool:
        """简化的可用性检查 - 只检查基本配置条件"""
        pass

    @abstractmethod
    def apply_tool(self, content: str, output_dir: str, context: dict[str, Any], focus: str = None) -> Optional[str]:
        """应用工具，执行具体的扩充操作"""
        pass

    @abstractmethod
    def generate_intro(self, tool_result: dict[str, Any]) -> str:
        """生成工具输出的介绍文本"""
        pass
