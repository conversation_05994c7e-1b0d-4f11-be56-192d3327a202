#!/usr/bin/env python3

from manim import *
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dsl.v2.core.scene import FeynmanScene

class TestGrayBackground(FeynmanScene):
    def construct(self):
        # 设置背景类型为灰色
        self.background_type = "gray"
        
        # 添加背景
        self.add_background()
        
        # 添加一些测试内容
        test_text = Text("测试文字", font_size=72, color=WHITE)
        test_text.move_to(ORIGIN)
        self.add(test_text)
        
        # 添加一个正方形作为参考
        reference_square = Square(side_length=2, color=RED, stroke_width=4)
        reference_square.move_to(UP * 2)
        self.add(reference_square)
        
        # 添加一个圆形作为参考
        reference_circle = Circle(radius=1, color=BLUE, stroke_width=4)
        reference_circle.move_to(DOWN * 2)
        self.add(reference_circle)
        
        # 保持画面3秒
        self.wait(3)

if __name__ == "__main__":
    # 渲染场景
    scene = TestGrayBackground()
    scene.render()