#!/bin/bash

# 1. 执行Python代码生成agent
echo "正在执行代码生成agent..."
python3 -m agents.code_generation_agent

# 2. 从配置文件中获取生成的Python文件路径
# 正确提取output_py路径
OUTPUT_PY=$(grep "output_py:" config/config.yaml | sed 's/.*output_py: *"//' | sed 's/".*$//')
if [ -z "$OUTPUT_PY" ]; then
  # 如果没有引号，尝试不同的提取方式
  OUTPUT_PY=$(grep "output_py:" config/config.yaml | sed 's/.*output_py: *//')
fi
echo "使用生成的Python文件: $OUTPUT_PY"

# 获取output_md的路径和目录
OUTPUT_MD=$(grep "output_md:" config/config.yaml | sed 's/.*output_md: *"//' | sed 's/".*$//')
if [ -z "$OUTPUT_MD" ]; then
  # 如果没有引号，尝试不同的提取方式
  OUTPUT_MD=$(grep "output_md:" config/config.yaml | sed 's/.*output_md: *//')
fi
TMP_SCRIPT_DIR=$(dirname "$OUTPUT_MD")
echo "使用output_md目录: $TMP_SCRIPT_DIR"

# 获取video_file路径
VIDEO_FILE=$(grep "video_file:" config/config.yaml | sed 's/.*video_file: *"//' | sed 's/".*$//')
if [ -z "$VIDEO_FILE" ]; then
  # 如果没有引号，尝试不同的提取方式
  VIDEO_FILE=$(grep "video_file:" config/config.yaml | sed 's/.*video_file: *//')
fi
echo "使用视频输出路径: $VIDEO_FILE"

# 确保目录存在
mkdir -p "$TMP_SCRIPT_DIR"
mkdir -p "$(dirname "$VIDEO_FILE")"
echo "使用临时脚本目录: $TMP_SCRIPT_DIR"

# 3. 在根目录创建临时脚本
TMP_SCRIPT="./temp_script.sh"

# 获取模块路径
MODULE_PATH=$(echo "$OUTPUT_PY" | sed 's/\.py$//' | tr '/' '.')
echo "模块路径: $MODULE_PATH"

# 创建临时脚本内容
cat > "$TMP_SCRIPT" << EOF
#!/bin/bash
echo "正在执行生成的代码示例..."
echo "==============================================="
python3 -m $MODULE_PATH
echo "==============================================="
echo "代码执行完成"
EOF

# 添加执行权限
chmod +x "$TMP_SCRIPT"
echo "已创建临时脚本: $TMP_SCRIPT"

# 4. 使用terminal_recorder.sh执行录制
echo "开始录制终端演示..."
sh terminal_recorder.sh -s "$TMP_SCRIPT" -o "$VIDEO_FILE"

echo "所有步骤已完成!"

# 5. 删除临时脚本
echo "正在清理临时文件..."
rm -f "$TMP_SCRIPT"
echo "临时脚本已删除"
