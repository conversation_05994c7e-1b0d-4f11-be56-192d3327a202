"""
README内容分析模块

负责分析GitHub仓库README内容，提取关键信息，生成分镜内容
"""

import json
import logging
import re
from typing import Any

import openai

logger = logging.getLogger(__name__)

# README分析提示词
README_ANALYSIS_PROMPT = """
分析以下GitHub仓库的README内容，提取关键信息并生成视频分镜内容。

README内容:
{readme_content}

仓库URL: {repo_url}

请提取以下内容:
1. 项目的核心目的和价值
2. 主要功能和特性
3. 技术实现细节
4. 使用方法和例子
5. 项目架构
6. 项目优势和创新点

基于上述信息，生成5-8个视频分镜，确保分镜内容覆盖README的重要信息，并关联到抓取的媒体素材。每个分镜应该包含:
- 分镜名: 简短的标题，描述分镜主题
- 分镜内容: 详细的讲解内容，长度15-50字之间
- 素材名: 关联的媒体文件路径
- 视觉动效建议: 如何在视频中展示该内容

媒体素材列表:
{media_elements}

请按照如下JSON格式输出:
[
  {{
    "分镜名": "标题",
    "分镜内容": "讲解内容",
    "素材名": "媒体文件路径",
    "视觉动效建议": "视觉动效描述"
  }}
]
"""


class ReadmeAnalyzer:
    """README内容分析器"""

    def __init__(self, model_type: str = "gpt-3.5-turbo", api_key: str = None, api_base: str = None):
        """
        初始化分析器

        Args:
            model_type: 使用的语言模型类型
            api_key: OpenAI API密钥
            api_base: OpenAI API基础URL
        """
        self.model_type = model_type

        # 配置OpenAI客户端
        if api_key:
            openai.api_key = api_key

        if api_base:
            openai.api_base = api_base

        logger.info(f"README分析器初始化完成，使用模型: {model_type}")

    def analyze_readme(
        self, readme_content: str, repo_url: str, media_elements: list[dict[str, Any]]
    ) -> list[dict[str, str]]:
        """
        分析README内容，生成分镜

        Args:
            readme_content: README内容
            repo_url: 仓库URL
            media_elements: 媒体元素列表

        Returns:
            分镜内容列表
        """
        # 如果README内容太长，截取前5000个字符
        if len(readme_content) > 5000:
            logger.info(f"README内容过长（{len(readme_content)}字符），截取前5000个字符")
            readme_content = readme_content[:5000] + "..."

        # 准备媒体元素信息
        media_info = []
        for media in media_elements:
            media_info.append({"type": media["type"], "path": media["local_path"], "text": media["nearest_text"]})

        # 构建分析提示词
        prompt = README_ANALYSIS_PROMPT.format(
            readme_content=readme_content,
            repo_url=repo_url,
            media_elements=json.dumps(media_info, ensure_ascii=False, indent=2),
        )

        # 调用API进行分析
        try:
            response = self._call_openai_api(prompt)

            # 解析响应
            storyboard = self._parse_response(response)

            logger.info(f"README分析完成，生成了 {len(storyboard)} 个分镜")

            return storyboard

        except Exception as e:
            logger.error(f"README分析失败: {e}")

            # 返回一个基础分镜结构
            basic_storyboard = self._generate_basic_storyboard(readme_content, repo_url, media_elements)
            logger.info(f"生成了基础分镜 {len(basic_storyboard)} 个")

            return basic_storyboard

    def _call_openai_api(self, prompt: str) -> str:
        """
        调用OpenAI API

        Args:
            prompt: 提示词

        Returns:
            API响应内容
        """
        try:
            # 调用OpenAI API
            messages = [{"role": "user", "content": prompt}]
            response = openai.ChatCompletion.create(
                model=self.model_type, messages=messages, max_tokens=2000, temperature=0.7
            )

            # 提取响应内容
            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"调用OpenAI API失败: {e}")
            raise

    def _parse_response(self, response: str) -> list[dict[str, str]]:
        """
        解析API响应内容

        Args:
            response: API响应内容

        Returns:
            分镜内容列表
        """
        # 尝试解析JSON响应
        try:
            # 首先尝试直接解析整个响应
            try:
                storyboard = json.loads(response)
                if isinstance(storyboard, list):
                    return storyboard
            except:
                pass

            # 查找JSON数组
            json_match = re.search(r"\[\s*\{.*\}\s*\]", response, re.DOTALL)
            if json_match:
                storyboard = json.loads(json_match.group(0))
                if isinstance(storyboard, list):
                    return storyboard

            # 尝试从代码块中提取JSON
            json_block_match = re.search(r"```(?:json)?\s*([\s\S]*?)```", response)
            if json_block_match:
                storyboard = json.loads(json_block_match.group(1))
                if isinstance(storyboard, list):
                    return storyboard

            # 如果仍然无法解析，记录错误并返回空列表
            logger.error(f"无法解析API响应为JSON: {response[:100]}...")
            return []

        except Exception as e:
            logger.error(f"解析响应失败: {e}")
            return []

    def _generate_basic_storyboard(
        self, readme_content: str, repo_url: str, media_elements: list[dict[str, Any]]
    ) -> list[dict[str, str]]:
        """
        当API调用失败时，生成基础分镜

        Args:
            readme_content: README内容
            repo_url: 仓库URL
            media_elements: 媒体元素列表

        Returns:
            基础分镜内容列表
        """
        # 从README内容中提取标题
        title_match = re.search(r"#\s+(.+)", readme_content)
        title = title_match.group(1) if title_match else "项目介绍"

        # 从URL中提取仓库名
        repo_name = repo_url.split("/")[-1].replace(".git", "")

        # 提取README的前几段作为项目描述
        description = ""
        paragraphs = re.split(r"\n\s*\n", readme_content)
        if paragraphs:
            description = paragraphs[0]
            if len(description) > 100:
                description = description[:97] + "..."

        # 创建基础分镜
        storyboard = [
            {
                "分镜名": "项目介绍",
                "分镜内容": f"{title} - {description}",
                "素材名": media_elements[0]["local_path"] if media_elements else "",
                "视觉动效建议": "缓慢展示项目标题和描述，强调关键词",
            }
        ]

        # 如果有媒体元素，添加更多分镜
        if len(media_elements) > 0:
            # 添加功能展示分镜
            storyboard.append(
                {
                    "分镜名": "核心功能",
                    "分镜内容": f"{repo_name}的主要功能和特性",
                    "素材名": media_elements[0]["local_path"],
                    "视觉动效建议": "展示媒体内容，突出核心功能点",
                }
            )

        if len(media_elements) > 1:
            # 添加使用方法分镜
            storyboard.append(
                {
                    "分镜名": "使用方法",
                    "分镜内容": "如何安装和使用该项目",
                    "素材名": media_elements[1]["local_path"],
                    "视觉动效建议": "逐步展示安装和使用步骤",
                }
            )

        # 总是添加总结分镜
        storyboard.append(
            {
                "分镜名": "总结",
                "分镜内容": f"{repo_name}是一个强大的工具，值得尝试",
                "素材名": media_elements[-1]["local_path"] if media_elements else "",
                "视觉动效建议": "展示项目徽标和URL，鼓励用户尝试",
            }
        )

        return storyboard
