# 代码生成Agent (CodeGenerationAgent)

这是一个基于CAMEL框架的代码生成Agent，能够读取开源项目的README和示例代码，并根据用户需求生成可运行的代码。

## 功能特点

- **支持远程GitHub仓库** - 只需提供GitHub URL，无需本地克隆
- 自动分析开源项目的结构和示例代码
- 根据用户需求生成带有详细中文注释的可运行代码
- 支持多种编程语言和项目类型
- 保留生成过程中的中间结果，便于后续分析和改进
- 可以直接运行生成的代码并获取执行结果

## 安装依赖

确保您已安装以下依赖：

```bash
pip install camel-ai pyyaml loguru
```

## 使用方法

### 命令行方式

```bash
# 使用远程GitHub仓库
python agents/code_generation_agent.py --repo-url https://github.com/username/repo --prompt "你的代码需求描述"

# 或使用本地仓库路径
python agents/code_generation_agent.py --repo /path/to/local/repo --prompt "你的代码需求描述"
```

参数说明：
- `--repo-url`: GitHub仓库URL（优先使用）
- `--repo`: 本地仓库路径
- `--prompt`: 用户需求描述
- `--config`: 配置文件路径（默认为 config/config.yaml）

### 交互式使用

可以使用提供的测试脚本进行交互式体验：

```bash
python test_code_generation.py
```

脚本会引导您选择仓库来源（远程或本地）并输入代码需求。

### 在其他代码中调用

```python
from agents.code_generation_agent import CodeGenerationAgent

# 初始化代码生成Agent
agent = CodeGenerationAgent()

# 方式1: 设置远程仓库URL
agent.set_repo_url("https://github.com/username/repo")

# 方式2: 设置本地仓库路径
# agent.set_repo_path("/path/to/local/repo")

# 收集仓库信息
repo_info = agent.collect_repo_info()
if "error" in repo_info:
    print(f"错误: {repo_info['error']}")
    exit(1)

# 生成代码
result = agent.generate_code("实现一个数据可视化功能，展示项目中的xxx数据", repo_info)

# 可选：运行生成的代码
for code_file in result['code_files']:
    output = agent.run_generated_code(code_file)
    print(output)
```

## 配置选项

在 `config/config.yaml` 中可以配置以下选项：

```yaml
model:
  platform: "openrouter"  # 模型平台
  type: "google/gemini-2.0-flash-lite-001"  # 模型类型
  temperature: 0.7
  max_tokens: 4096
  api:
    openai_compatibility_api_base_url: "https://your-api-base-url"
    openai_compatibility_api_key: "your-api-key"

github:
  project_url: "https://github.com/username/repo"  # 远程仓库URL
  local_repo_path: "/path/to/local/repo"  # 本地仓库路径（可选）
```

## 工作原理

代码生成Agent的工作流程：

1. **仓库获取**：
   - 远程模式：使用Git命令将GitHub仓库克隆到临时目录
   - 本地模式：直接使用指定的本地仓库路径
   
2. **信息收集**：
   - 解析README文件
   - 收集examples目录下的示例代码
   - 分析项目结构和重要配置文件
   
3. **代码生成**：
   - 构建包含仓库信息和用户需求的提示
   - 调用大语言模型生成代码
   - 提取和保存生成的代码
   
4. **代码执行**（可选）：
   - 执行生成的代码
   - 收集并展示执行结果

## 输出内容

代码生成完成后，会在 `output/codegen/` 目录下生成以下文件：

- `repo_info.json`: 收集到的仓库信息
- `prompt.txt`: 发送给模型的完整提示
- `response.txt`: 模型的原始回复
- `generated_code_*.py`: 生成的代码文件
- `result.json`: 生成结果的摘要
- `run_results.txt`: 运行代码的输出结果

## 示例

用户需求：
```
实现一个命令行工具，使用项目中的核心功能分析一个文本文件中的情感
```

生成的代码示例：
```python
# 情感分析命令行工具
# 这个脚本提供了一个简单的命令行界面，用来分析文本文件中的情感

import argparse
import sys
from pathlib import Path

# 导入项目的情感分析核心功能
from sentiment_analyzer.core import analyze_sentiment

def main():
    """
    主函数，解析命令行参数并执行情感分析
    """
    parser = argparse.ArgumentParser(description="文本情感分析工具")
    parser.add_argument("input_file", help="要分析的文本文件路径")
    parser.add_argument("--output", "-o", help="结果输出文件路径（默认输出到控制台）")
    parser.add_argument("--format", "-f", choices=["json", "text"], default="text",
                        help="输出格式：json或text（默认为text）")
    parser.add_argument("--verbose", "-v", action="store_true",
                        help="显示详细结果")
    
    args = parser.parse_args()
    
    # 检查输入文件是否存在
    input_path = Path(args.input_file)
    if not input_path.exists():
        print(f"错误: 输入文件 '{args.input_file}' 不存在", file=sys.stderr)
        return 1
    
    # 读取输入文件
    try:
        with open(input_path, "r", encoding="utf-8") as f:
            text = f.read()
    except Exception as e:
        print(f"错误: 无法读取输入文件 - {str(e)}", file=sys.stderr)
        return 1
    
    # 分析情感
    try:
        result = analyze_sentiment(text, detailed=args.verbose)
    except Exception as e:
        print(f"错误: 情感分析失败 - {str(e)}", file=sys.stderr)
        return 1
    
    # 处理输出
    if args.format == "json":
        import json
        output = json.dumps(result, ensure_ascii=False, indent=2)
    else:
        output = format_text_output(result, args.verbose)
    
    # 输出结果
    if args.output:
        try:
            with open(args.output, "w", encoding="utf-8") as f:
                f.write(output)
            print(f"结果已保存到 {args.output}")
        except Exception as e:
            print(f"错误: 无法写入输出文件 - {str(e)}", file=sys.stderr)
            return 1
    else:
        print(output)
    
    return 0

def format_text_output(result, verbose=False):
    """
    将结果格式化为易读的文本输出
    
    Args:
        result: 情感分析结果
        verbose: 是否显示详细结果
        
    Returns:
        格式化后的文本
    """
    output = []
    output.append(f"总体情感: {result['sentiment']}")
    output.append(f"情感分数: {result['score']:.2f} (范围: -1 到 1)")
    
    if verbose and 'details' in result:
        output.append("\n详细分析:")
        for detail in result['details']:
            output.append(f"- {detail['aspect']}: {detail['sentiment']} ({detail['score']:.2f})")
            if 'text' in detail:
                output.append(f"  \"{detail['text']}\"")
    
    return "\n".join(output)

if __name__ == "__main__":
    sys.exit(main())
```

## 许可证

MIT License 