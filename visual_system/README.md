# Visual System Framework

A comprehensive framework for creating visual presentations with intelligent defaults and optimized for large language model interaction.

## Overview

The Visual System Framework provides a structured way to create and organize visual content with minimal configuration. It's designed to be intuitive for large language models to use while providing powerful defaults and intelligent behavior.

The framework consists of two main parts:
1. **Elements** - Building blocks for content (text, image, video, chart, etc.)
2. **Organization** - Ways to arrange and animate elements (layouts, patterns, animations)

## Features

- **Intelligent Defaults**: Elements automatically configure themselves based on content characteristics
- **Clear Semantics**: Functions and parameters follow consistent, semantic naming
- **Minimal Required Parameters**: Only essential parameters are required, everything else has smart defaults
- **Composable Structure**: Elements and organizations can be freely combined
- **LLM-Friendly API**: Designed for easy use by large language models

## Quick Start

Here's a simple example of creating a visual presentation:

```python
from visual_system.elements import TextElement, ImageElement
from visual_system.organization import Layout, ContentScene

# Create elements
title = TextElement("Hello World", {"role": "title"})
intro = TextElement("This is an example of the visual system framework.")
image = ImageElement("example.jpg", {"role": "hero"})

# Create layout
main_layout = Layout([title, intro, image], "flow")

# Create scene
scene = ContentScene([title, intro, image], [main_layout])
```

## Element Types

The framework includes these element types:

- **Text**: Paragraphs, titles, captions, and other text content
- **Image**: Photos, diagrams, icons, and other visual elements
- **Video**: Video content with intelligent playback controls
- **Chart**: Data visualizations with automatic type detection
- **Table**: Structured data with formatting options
- **Code**: Source code with syntax highlighting
- **Formula**: Mathematical formulas with LaTeX support
- **Matrix**: Data matrices with visualization options
- **Geometry**: Geometric shapes and diagrams

## Organization Types

Content can be organized using:

- **Layout**: Spatial arrangement of elements
- **Content Pattern**: Semantic relationships between elements
- **Composite Animation**: Coordinated animations across elements
- **Content Scene**: Complete scenes combining elements and organizations
- **Content Template**: Pre-configured templates for common content types

## Examples

Check the `examples` directory for complete implementations:

- `quicksort_explanation.py`: Demonstrates creating an algorithm explanation
- `product_showcase.py`: Shows how to use templates for product presentations

## Usage Notes

- All element creation functions return dictionaries that can be passed to organization functions
- Elements have unique IDs that are auto-generated if not provided
- The framework is designed to be used with a rendering engine (not included)

## Advanced Features

- Automatic role detection based on content characteristics
- Smart animation timing based on content complexity
- Responsive layout configurations
- Semantic relationships between elements
