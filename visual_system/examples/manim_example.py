#!/usr/bin/env python3
"""
使用Manim示例

展示如何使用Manim库直接创建和渲染一个简单场景
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from manim import Scene, Text, MathTex, config, UP, DOWN, ORIGIN, Write, FadeIn
from visual_system.elements import TextElement


class MathVisualizationScene(Scene):
    """数学公式可视化场景"""
    
    def construct(self):
        """构建场景内容"""
        
        # 创建元素
        title_element = TextElement(
            "数学公式可视化",
            {"role": "title", "font_size": 48, "color": "white"}
        )
        title_obj = title_element.create_manim_object()
        title_obj.to_edge(UP, buff=1)
        
        subtitle_element = TextElement(
            "使用Manim和visual_system元素创建的演示",
            {"role": "subtitle", "font_size": 32, "color": "#AAAAAA"}
        )
        subtitle_obj = subtitle_element.create_manim_object()
        subtitle_obj.next_to(title_obj, DOWN, buff=0.5)
        
        # 创建数学公式
        formula = MathTex(r"e^{i\pi} + 1 = 0", font_size=64, color="white")
        formula.move_to(ORIGIN)
        
        # 创建解释文本
        explanation_element = TextElement(
            "欧拉恒等式结合了数学中五个最重要的常数，以及加法、乘法和指数运算。",
            {"role": "body", "font_size": 28, "color": "#BBBBBB"}
        )
        explanation_obj = explanation_element.create_manim_object()
        explanation_obj.next_to(formula, DOWN, buff=1)
        
        # 播放动画
        self.play(Write(title_obj))
        self.wait(0.5)
        self.play(FadeIn(subtitle_obj))
        self.wait(1)
        
        self.play(Write(formula, run_time=2.0))
        self.wait(1)
        
        self.play(FadeIn(explanation_obj))
        self.wait(2)


def main():
    """主函数"""
    # 设置输出文件
    output_file = "math_visualization.mp4"
    print(f"渲染场景到 {output_file}")
    
    # 配置Manim
    config.output_file = output_file
    
    # 直接运行Manim场景
    from manim import tempconfig
    with tempconfig({"preview": True, "quality": "high_quality"}):
        scene = MathVisualizationScene()
        scene.render()
    
    print("渲染完成！")


if __name__ == "__main__":
    main() 