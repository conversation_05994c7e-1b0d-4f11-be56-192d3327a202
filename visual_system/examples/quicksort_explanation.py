#!/usr/bin/env python3
"""
Example: Quicksort Algorithm Explanation

This example demonstrates how to use the visual system framework to create
a visual explanation of the quicksort algorithm.
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

# 导入 visual_system 的元素和组织结构
from visual_system.elements import (
    ArrayElement,
    CodeElement,
    ImageElement,
    TextElement,
)
from visual_system.organization import (
    CompositeAnimation,
    ContentPattern,
    ContentScene,
    Layout,
)
# 导入manim场景基类和常量
from manim import (
    Scene, 
    config,
    UP, 
    DOWN, 
    LEFT, 
    RIGHT, 
    ORIGIN, 
    Write, 
    FadeIn, 
    FadeOut
)


def create_quicksort_explanation():
    """Create a visual explanation of the quicksort algorithm"""

    # 1. Create elements
    title = TextElement(
        "Quicksort Algorithm Explained",
        {"role": "title"},
    )

    intro_text = TextElement(
        "Quicksort is an efficient sorting algorithm that uses a divide-and-conquer strategy. "
        "It's widely used due to its average-case performance of O(n log n).",
        {"role": "body"},
    )

    algorithm_image = ImageElement(
        "quicksort_diagram.png",
        {
            "role": "diagram",
            "alt": "Quicksort partition visualization",
        },
    )

    code_sample = CodeElement(
        """
def quicksort(arr, low, high):
    if low < high:
        # Select pivot and partition
        pivot_index = partition(arr, low, high)
        # Recursively sort subarrays
        quicksort(arr, low, pivot_index - 1)
        quicksort(arr, pivot_index + 1, high)

def partition(arr, low, high):
    pivot = arr[high]  # Choose rightmost element as pivot
    i = low - 1  # Index of smaller element

    for j in range(low, high):
        # If current element is smaller than the pivot
        if arr[j] <= pivot:
            i += 1
            arr[i], arr[j] = arr[j], arr[i]

    # Place pivot in its final position
    arr[i + 1], arr[high] = arr[high], arr[i + 1]
    return i + 1
""",
        {
            "language": "python",
            "line_numbers": True,
            "highlight_lines": [3, 5, 7],
        },
    )

    # Create an array visualization using elements
    array_example = ArrayElement(
        [38, 27, 43, 3, 9, 82, 10],
        {
            "type": "numeric",
            "style": "block",
            "layout": "horizontal",
            "indexed": True,
            "color_map": {
                "range": [0, 100],
                "low_color": "#33AAFF",
                "high_color": "#FF5500",
            },
        },
    )

    # 使用Elements接口的sort方法
    array_example.animate_sort(
        algorithm="quicksort",
        scene=None,  # 稍后在render方法中会自动传入scene参数
    )

    complexity_text = TextElement(
        "Time Complexity:\n"
        "• Best case: O(n log n)\n"
        "• Average case: O(n log n)\n"
        "• Worst case: O(n²)\n\n"
        "Space Complexity: O(log n)",
        {"role": "note"},
    )

    conclusion = TextElement(
        "Quicksort is often the practical choice for sorting due to its efficiency, "
        "low space requirements, and good cache performance. However, in worst-case scenarios "
        "(e.g., already sorted array), its performance degrades significantly unless "
        "pivot selection strategies are improved.",
        {"role": "body"},
    )

    # Create step-by-step array states with elements
    step1_array = ArrayElement(
        [38, 27, 43, 3, 9, 82, 10],
        {
            "type": "numeric",
            "style": "block",
            "layout": "horizontal",
            "indexed": True,
            "highlight_indices": {"6": "#FF5500"},  # Highlight pivot
            "title": "Step 1: Select pivot (10)",
        },
    )

    step2_array = ArrayElement(
        [3, 9, 10, 38, 27, 43, 82],
        {
            "type": "numeric",
            "style": "block",
            "layout": "horizontal",
            "indexed": True,
            "highlight_indices": {"2": "#33AA33"},  # Highlight partition position
            "title": "Step 2: Partition around pivot",
        },
    )

    step3_array = ArrayElement(
        [3, 9, 10, 27, 38, 43, 82],
        {
            "type": "numeric",
            "style": "block",
            "layout": "horizontal",
            "indexed": True,
            "title": "Step 3: Recursively sort subarrays",
        },
    )

    # 2. Create layouts using Layout
    main_layout = Layout(
        [
            title,
            intro_text,
            algorithm_image,
            code_sample
        ],
        "flow",
        {
            "direction": "vertical", 
            "spacing": 0.5
        }
    )

    array_steps_layout = Layout(
        [
            (step1_array, {"position": "start"}),
            (step2_array, {"position": "right", "related_to": step1_array, "margin": 0.5}),
            (step3_array, {"position": "right", "related_to": step2_array, "margin": 0.5})
        ],
        "flow",
        {
            "direction": "horizontal",
            "spacing": 0.8,
            "gap": 24
        }
    )

    array_animation_layout = Layout(
        [array_example],
        "centered",
        {
            "padding": 1.0
        }
    )

    footer_layout = Layout(
        [
            (complexity_text, {"position": "left"}),
            (conclusion, {"position": "right", "related_to": complexity_text, "margin": 0.8})
        ],
        "split",
        {
            "direction": "horizontal",
            "ratio": "1:2",
            "gap": 0.5
        }
    )

    # 3. Create content organization pattern using ContentPattern
    explanation_pattern = ContentPattern(
        [intro_text, algorithm_image, array_example, code_sample, complexity_text, conclusion],
        "sequence",
        {
            "transition": "smooth"
        }
    )

    # 4. Create animation using CompositeAnimation
    reveal_animation = CompositeAnimation(
        [
            title,
            intro_text,
            algorithm_image,
            code_sample,
            array_steps_layout,
            array_example,
            complexity_text,
            conclusion,
        ],
        "staggered",
        {
            "delay": 0.3,
            "direction": "forward",
            "duration": 0.8,
            "easing": "ease-out"
        }
    )

    # 5. Create final scene using ContentScene
    algorithm_scene = ContentScene(
        [
            title,
            intro_text,
            algorithm_image,
            code_sample,
            array_example,
            step1_array,
            step2_array,
            step3_array,
            complexity_text,
            conclusion,
        ],
        [
            main_layout,
            array_steps_layout,
            array_animation_layout,
            footer_layout,
            explanation_pattern,
            reveal_animation,
        ],
        {
            "purpose": "explanation",
            "theme": "light",
            "title": "Quicksort Algorithm Explanation",
        },
    )

    # 返回场景
    return algorithm_scene


# 创建Manim场景类
class QuicksortExplanationScene(Scene):
    """Manim场景：快速排序算法解释"""
    
    def construct(self):
        """构建场景内容并渲染"""
        # 直接创建演示场景，不再使用复杂的布局系统
        self.show_quicksort_explanation()
        
    def show_quicksort_explanation(self):
        """展示快速排序算法的说明"""
        # 创建标题文本
        title_element = TextElement(
            "快速排序算法",
            {"role": "title", "font_size": 48}
        )
        title_obj = title_element.create_manim_object()
        self.play(Write(title_obj))
        self.wait(1)
        self.play(title_obj.animate.to_edge(UP))
        
        # 创建介绍文本
        intro_element = TextElement(
            "快速排序是一种高效的分治排序算法，平均时间复杂度为O(n log n)",
            {"role": "body", "font_size": 24}
        )
        intro_obj = intro_element.create_manim_object()
        intro_obj.next_to(title_obj, DOWN, buff=0.5)
        self.play(FadeIn(intro_obj))
        self.wait(1)
        
        # 创建数组可视化
        array_data = [38, 27, 43, 3, 9, 82, 10]
        array_element = ArrayElement(
            array_data,
            {
                "type": "numeric",
                "style": "block",
                "layout": "horizontal",
                "indexed": True,
                "color_map": {
                    "range": [0, 100],
                    "low_color": "#33AAFF",
                    "high_color": "#FF5500",
                },
            },
        )
        
        # 将数组移动到屏幕中部
        array_obj = array_element.create_manim_object()
        array_obj.move_to(ORIGIN)
        self.play(FadeIn(array_obj))
        self.wait(1)
        
        # 执行排序动画
        self.play(FadeOut(intro_obj))  # 移除介绍文本
        
        # 创建排序步骤说明
        step_element = TextElement(
            "开始快速排序过程", 
            {"role": "heading", "font_size": 32}
        )
        step_obj = step_element.create_manim_object()
        step_obj.next_to(title_obj, DOWN, buff=0.5)
        self.play(Write(step_obj))
        self.wait(1)
        
        # 使用新实现的animate_sort方法进行排序可视化
        array_element.animate_sort(algorithm="quicksort", scene=self)
        
        # 显示完成信息
        conclusion_element = TextElement(
            "快速排序在平均情况下的时间复杂度为O(n log n)，是实际应用中常用的排序算法",
            {"role": "body", "font_size": 24}
        )
        conclusion_obj = conclusion_element.create_manim_object()
        conclusion_obj.next_to(array_obj, DOWN, buff=1)
        self.play(FadeIn(conclusion_obj))
        self.wait(2)


if __name__ == "__main__":
    # 使用命令行参数
    import sys
    
    # 设置输出文件
    output_file = "quicksort_explanation.mp4"
    
    print(f"Rendering quicksort explanation to {output_file}")
    
    # 配置Manim
    config.output_file = output_file
    
    # 直接运行Manim场景
    from manim import tempconfig
    with tempconfig({"preview": True, "quality": "medium_quality"}):
        scene = QuicksortExplanationScene()
        scene.render()
    
    print("Rendering complete!")
