#!/usr/bin/env python3
"""
使用Manim实现快速排序可视化示例

展示如何使用Manim和visual_system元素创建快速排序算法可视化
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from manim import Scene, config, UP, DOWN, RIGHT, LEFT, ORIGIN, VGroup, Code, Write, FadeIn, Create
from visual_system.elements import TextElement, ArrayElement


class QuicksortVisualizationScene(Scene):
    """快速排序可视化场景"""
    
    def construct(self):
        """构建场景内容"""
        
        # 创建标题元素
        title_element = TextElement(
            "快速排序算法可视化",
            {"role": "title", "font_size": 48, "color": "white"}
        )
        title_obj = title_element.create_manim_object()
        title_obj.to_edge(UP, buff=0.5)
        
        # 创建描述元素
        description_element = TextElement(
            "快速排序是一种高效的排序算法，使用分治策略。平均时间复杂度为O(n log n)。",
            {"role": "body", "font_size": 28, "color": "#BBBBFF"}
        )
        description_obj = description_element.create_manim_object()
        description_obj.next_to(title_obj, DOWN, buff=0.5)
        
        # 创建初始数组
        array_data = [38, 27, 43, 3, 9, 82, 10, 15, 52, 21]
        array_element = ArrayElement(
            array_data,
            {
                "type": "numeric",
                "style": "block",
                "layout": "horizontal",
                "indexed": True,
                "color_map": {
                    "range": [0, 100],
                    "low_color": "#33AAFF",
                    "high_color": "#FF5500"
                }
            }
        )
        array_obj = array_element.create_manim_object()
        array_obj.next_to(description_obj, DOWN, buff=1)
        
        # 创建代码示例
        code_text = """
def quicksort(arr, low, high):
    if low < high:
        # 选取基准点并分区
        pivot_index = partition(arr, low, high)
        # 递归排序子数组
        quicksort(arr, low, pivot_index - 1)
        quicksort(arr, pivot_index + 1, high)

def partition(arr, low, high):
    pivot = arr[high]  # 选择最右侧元素作为基准
    i = low - 1  # 小于基准的元素索引

    for j in range(low, high):
        # 如果当前元素小于等于基准
        if arr[j] <= pivot:
            i += 1
            arr[i], arr[j] = arr[j], arr[i]

    # 将基准放到最终位置
    arr[i + 1], arr[high] = arr[high], arr[i + 1]
    return i + 1
"""
        code_obj = Code(
            code=code_text,
            language="python",
            font="Monospace",
            font_size=16,
            line_spacing=0.8,
            background_stroke_width=0,
            background_stroke_color="white",
            insert_line_no=True,
            style="monokai",
            background="rectangle"
        )
        code_obj.scale(0.8)
        code_obj.to_edge(RIGHT, buff=0.5)
        
        # 创建步骤说明
        steps_element = TextElement(
            "快速排序步骤:\n"
            "1. 选择一个基准元素（通常是最右侧元素）\n"
            "2. 将小于基准的元素放在左侧，大于基准的放在右侧\n"
            "3. 基准元素放置在最终位置\n"
            "4. 递归处理左右两个子数组",
            {"role": "note", "font_size": 24, "color": "#AAFFAA"}
        )
        steps_obj = steps_element.create_manim_object()
        steps_obj.scale(0.8)
        steps_obj.to_edge(LEFT, buff=0.5)
        steps_obj.shift(DOWN * 2)
        
        # 时间复杂度说明
        complexity_element = TextElement(
            "时间复杂度:\n"
            "• 最佳情况: O(n log n)\n"
            "• 平均情况: O(n log n)\n"
            "• 最差情况: O(n²)",
            {"role": "note", "font_size": 24, "color": "#FFAAAA"}
        )
        complexity_obj = complexity_element.create_manim_object()
        complexity_obj.scale(0.8)
        complexity_obj.next_to(steps_obj, RIGHT, buff=1)
        
        # 播放动画
        self.play(Write(title_obj))
        self.play(FadeIn(description_obj))
        self.wait(0.5)
        
        self.play(Create(array_obj))
        self.wait(0.5)
        
        self.play(Write(code_obj), run_time=2)
        self.wait(0.5)
        
        self.play(FadeIn(steps_obj), FadeIn(complexity_obj))
        self.wait(1)
        
        # 执行排序动画
        array_element.animate_sort(algorithm="quicksort", scene=self)
        
        self.wait(2)


def main():
    """主函数"""
    # 设置输出文件
    output_file = "quicksort_visualization.mp4"
    print(f"渲染快速排序可视化到 {output_file}")
    
    # 配置Manim
    config.output_file = output_file
    
    # 直接运行Manim场景
    from manim import tempconfig
    with tempconfig({"preview": True, "quality": "high_quality", "background_color": "#1E1E2E"}):
        scene = QuicksortVisualizationScene()
        scene.render()
    
    print("渲染完成！")


if __name__ == "__main__":
    main() 