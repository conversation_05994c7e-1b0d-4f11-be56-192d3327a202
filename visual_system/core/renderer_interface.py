"""
渲染器接口定义
"""

from abc import ABC, abstractmethod


class RendererInterface(ABC):
    """
    渲染器接口，定义所有渲染器必须实现的方法
    """
    
    @abstractmethod
    def create_text_element(self, text, properties=None, animation=None, id=None):
        """
        创建文本元素
        
        Args:
            text (str): 文本内容
            properties (dict, optional): 文本属性
            animation (dict, optional): 动画配置
            id (str, optional): 元素ID
        """
        pass
    
    @abstractmethod
    def create_image_element(self, source, properties=None, animation=None, id=None):
        """
        创建图像元素
        
        Args:
            source (str): 图像源路径
            properties (dict, optional): 图像属性
            animation (dict, optional): 动画配置
            id (str, optional): 元素ID
        """
        pass
    
    @abstractmethod
    def create_chart_element(self, data, properties=None, animation=None, id=None):
        """
        创建图表元素
        
        Args:
            data: 图表数据
            properties (dict, optional): 图表属性
            animation (dict, optional): 动画配置
            id (str, optional): 元素ID
        """
        pass
    
    @abstractmethod
    def create_array_element(self, data, properties=None, animation=None, id=None):
        """
        创建数组元素
        
        Args:
            data (list): 数组数据
            properties (dict, optional): 数组属性
            animation (dict, optional): 动画配置
            id (str, optional): 元素ID
        """
        pass
    
    @abstractmethod
    def create_video_element(self, source, properties=None, animation=None, id=None):
        """
        创建视频元素
        
        Args:
            source (str): 视频源路径
            properties (dict, optional): 视频属性，如控件显示、自动播放、循环等
            animation (dict, optional): 动画配置
            id (str, optional): 元素ID
        """
        pass
    
    @abstractmethod
    def create_audio_element(self, source, properties=None, animation=None, id=None):
        """
        创建音频元素
        
        Args:
            source (str): 音频源路径
            properties (dict, optional): 音频属性
            animation (dict, optional): 动画配置
            id (str, optional): 元素ID
        """
        pass
    
    @abstractmethod
    def create_code_element(self, code, properties=None, animation=None, id=None):
        """
        创建代码元素
        
        Args:
            code (str): 代码内容
            properties (dict, optional): 代码属性，如语言、高亮等
            animation (dict, optional): 动画配置
            id (str, optional): 元素ID
        """
        pass
    
    @abstractmethod
    def create_table_element(self, data, properties=None, animation=None, id=None):
        """
        创建表格元素
        
        Args:
            data: 表格数据
            properties (dict, optional): 表格属性
            animation (dict, optional): 动画配置
            id (str, optional): 元素ID
        """
        pass
    
    @abstractmethod
    def create_formula_element(self, formula, properties=None, animation=None, id=None):
        """
        创建公式元素
        
        Args:
            formula (str): 公式内容，通常是LaTeX格式
            properties (dict, optional): 公式属性
            animation (dict, optional): 动画配置
            id (str, optional): 元素ID
        """
        pass
    
    @abstractmethod
    def create_matrix_element(self, data, properties=None, animation=None, id=None):
        """
        创建矩阵元素
        
        Args:
            data: 矩阵数据
            properties (dict, optional): 矩阵属性
            animation (dict, optional): 动画配置
            id (str, optional): 元素ID
        """
        pass
    
    @abstractmethod
    def create_geometry_element(self, geometry_type, properties=None, animation=None, id=None):
        """
        创建几何元素
        
        Args:
            geometry_type (str): 几何类型
            properties (dict, optional): 几何属性
            animation (dict, optional): 动画配置
            id (str, optional): 元素ID
        """
        pass
    
    @abstractmethod
    def create_model3d_element(self, model_source, properties=None, animation=None, id=None):
        """
        创建3D模型元素
        
        Args:
            model_source (str): 模型源路径
            properties (dict, optional): 模型属性
            animation (dict, optional): 动画配置
            id (str, optional): 元素ID
        """
        pass
    
    @abstractmethod
    def create_animation_element(self, target_elements, animation_type, properties=None, id=None):
        """
        创建动画元素
        
        Args:
            target_elements (list): 目标元素列表
            animation_type (str): 动画类型
            properties (dict, optional): 动画属性
            id (str, optional): 元素ID
        """
        pass
    
    @abstractmethod
    def create_array_operation_element(self, array_element, operation, properties=None, animation=None, id=None):
        """
        创建数组操作元素
        
        Args:
            array_element: 数组元素
            operation (str): 操作类型
            properties (dict, optional): 操作属性
            animation (dict, optional): 动画配置
            id (str, optional): 元素ID
        """
        pass
    
    @abstractmethod
    def create_layout(self, elements, layout_type, properties=None, id=None):
        """
        创建布局
        
        Args:
            elements (list): 要布局的元素
            layout_type (str): 布局类型
            properties (dict, optional): 布局属性
            id (str, optional): 布局ID
        """
        pass
    
    @abstractmethod
    def create_content_scene(self, elements, organizations, properties=None, id=None):
        """
        创建内容场景
        
        Args:
            elements (list): 元素列表
            organizations (list): 组织结构列表
            properties (dict, optional): 场景属性
            id (str, optional): 场景ID
        """
        pass
    
    @abstractmethod
    def create_content_pattern(self, elements, pattern_type, properties=None, id=None):
        """
        创建内容模式
        
        Args:
            elements (list): 元素列表
            pattern_type (str): 模式类型
            properties (dict, optional): 模式属性
            id (str, optional): 模式ID
        """
        pass
    
    @abstractmethod
    def create_composite_animation(self, elements, animation_type, properties=None, id=None):
        """
        创建组合动画
        
        Args:
            elements (list): 元素列表
            animation_type (str): 动画类型
            properties (dict, optional): 动画属性
            id (str, optional): 动画ID
        """
        pass
    
    @abstractmethod
    def create_content_template(self, template_type, content_elements=None, properties=None, id=None):
        """
        创建内容模板
        
        Args:
            template_type (str): 模板类型
            content_elements (dict, optional): 内容元素映射
            properties (dict, optional): 模板属性
            id (str, optional): 模板ID
        """
        pass
    
    @abstractmethod
    def render(self, scene, output_file=None, **kwargs):
        """
        渲染场景
        
        Args:
            scene: 要渲染的场景
            output_file (str, optional): 输出文件路径
            **kwargs: 其他渲染参数
        """
        pass 