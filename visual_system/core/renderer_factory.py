"""
渲染器工厂模块，用于创建不同类型的渲染器
"""

from visual_system.core.renderer_interface import RendererInterface


# 已注册的渲染器类型
_REGISTERED_RENDERERS = {
    # 可以在此添加更多渲染器类型
    # "manim": <PERSON><PERSON><PERSON><PERSON><PERSON>,
    # "plotly": Plot<PERSON><PERSON><PERSON><PERSON>,
    # "d3": D3<PERSON><PERSON><PERSON>,
    # "unity": <PERSON><PERSON><PERSON><PERSON>,
}

# 已注册的元素类型
_REGISTERED_ELEMENTS = {
    "text": "TextElement",
    "image": "ImageElement",
    "video": "VideoElement",
    "audio": "AudioElement",
    "chart": "ChartElement",
    "code": "CodeElement",
    "table": "TableElement",
    "formula": "FormulaElement",
    "matrix": "MatrixElement",
    "geometry": "GeometryElement",
    "model3d": "Model3DElement",
    "animation": "AnimationElement",
    "array": "ArrayElement",
    "array_operation": "ArrayOperationElement",
}


def create_renderer(renderer_type, config=None):
    """
    创建指定类型的渲染器
    
    Args:
        renderer_type (str): 渲染器类型
        config (dict, optional): 渲染器配置
        
    Returns:
        RendererInterface: 渲染器实例
        
    Raises:
        ValueError: 如果指定的渲染器类型不存在
    """
    if renderer_type not in _REGISTERED_RENDERERS:
        raise ValueError(f"不支持的渲染器类型: {renderer_type}。支持的类型: {list(_REGISTERED_RENDERERS.keys())}")
    
    renderer_class = _REGISTERED_RENDERERS[renderer_type]
    return renderer_class(config)


def register_renderer(renderer_type, renderer_class):
    """
    注册新的渲染器类型
    
    Args:
        renderer_type (str): 渲染器类型名称
        renderer_class: 渲染器类
        
    Returns:
        bool: 是否成功注册
    """
    if renderer_type in _REGISTERED_RENDERERS:
        return False
    
    _REGISTERED_RENDERERS[renderer_type] = renderer_class
    return True


def register_element(element_type, element_class):
    """
    注册新的元素类型
    
    Args:
        element_type (str): 元素类型名称
        element_class (str): 元素类名
        
    Returns:
        bool: 是否成功注册
    """
    if element_type in _REGISTERED_ELEMENTS:
        return False
    
    _REGISTERED_ELEMENTS[element_type] = element_class
    return True


def get_available_renderers():
    """
    获取所有可用的渲染器类型
    
    Returns:
        list: 可用渲染器类型列表
    """
    return list(_REGISTERED_RENDERERS.keys())


def get_available_elements():
    """
    获取所有可用的元素类型
    
    Returns:
        list: 可用元素类型列表
    """
    return list(_REGISTERED_ELEMENTS.keys()) 