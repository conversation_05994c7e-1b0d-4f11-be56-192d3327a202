"""
Visual System - 一个灵活的可视化系统，支持多种渲染引擎

主要组件:
- elements: 视觉元素 (文本、图像、图表等)
- organization: 组织结构 (布局、场景、模式等)
- utils: 工具函数
- core: 核心接口和工厂
"""

from visual_system.elements import (
    BaseElement,
    TextElement,
    ImageElement,
    VideoElement,
    ChartElement,
    TableElement,
    CodeElement,
    FormulaElement,
    MatrixElement,
    GeometryElement,
    ElementAnimation,
    AudioElement,
    Model3DElement,
)

from visual_system.organization import (
    Layout,
    ContentPattern,
    CompositeAnimation,
    ContentScene,
    ContentTemplate,
)

from visual_system.core import (
    RendererInterface,
    create_renderer,
    register_renderer,
    register_element,
    get_available_renderers,
    get_available_elements,
)

__all__ = [
    # 元素
    "BaseElement",
    "TextElement",
    "ImageElement",
    "VideoElement",
    "ChartElement",
    "TableElement",
    "CodeElement",
    "FormulaElement",
    "MatrixElement",
    "GeometryElement",
    "ElementAnimation",
    "AudioElement",
    "Model3DElement",
    
    # 组织结构
    "Layout",
    "ContentPattern",
    "CompositeAnimation",
    "ContentScene",
    "ContentTemplate",
    
    # 渲染器接口和工厂
    "RendererInterface",
    "create_renderer",
    "register_renderer",
    "register_element",
    "get_available_renderers",
    "get_available_elements",
]

__version__ = "0.1.0"
