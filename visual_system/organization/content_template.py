from visual_system.utils.helpers import generate_id


class ContentTemplate:
    """
    Create a specialized content template with predefined structure

    Args:
        template_type (str): Template type -
            "tutorial" - Tutorial template
            "product" - Product showcase template
            "data_report" - Data report template
            "comparison" - Comparison template
            "process" - Process template
            "case_study" - Case study template
            "concept" - Concept explanation template
            "timeline" - Timeline template
            "profile" - Profile template
            "showcase" - Showcase template
        content_elements (dict, optional): Content element mapping based on template-defined roles
        properties (dict, optional): Template-specific properties
        id (str, optional): Template unique identifier, auto-generated if not provided

    Examples:
        # Create a tutorial template
        tutorial = ContentTemplate("tutorial", {
            "title": title_element,
            "intro": intro_element,
            "step_items": [step1, step2, step3],
            "conclusion": conclusion_element
        })

        # Create a product showcase template
        product = ContentTemplate("product", {
            "title": product_name,
            "hero_image": product_image,
            "description": product_description,
            "feature_items": [feature1, feature2, feature3]
        })
    """
    def __init__(self, template_type, content_elements=None, properties=None, id=None):
        self.template_type = template_type
        self.content_elements = content_elements or {}
        self.properties = properties or {}
        self.id = id or generate_id()
        self.processed_elements = {}
        
        # 应用模板默认值
        self._apply_template_presets()
        
        # 处理内容元素
        self._process_content_elements()
        
    def _apply_template_presets(self):
        """应用模板预设"""
        # Template presets
        template_presets = {
            "tutorial": {
                "sections": ["introduction", "prerequisites", "steps", "conclusion"],
                "layout_type": "flow",
                "animation_type": "sequence_in",
                "pattern_type": "sequence",
                "element_roles": {
                    "title": {"required": True, "type": "text"},
                    "intro": {"required": True, "type": "text"},
                    "step_items": {"required": True, "type": "array", "min_count": 1},
                    "conclusion": {"required": False, "type": "text"},
                },
            },
            "product": {
                "sections": ["hero", "features", "details", "cta"],
                "layout_type": "flow",
                "animation_type": "reveal",
                "pattern_type": "concept_detail",
                "element_roles": {
                    "title": {"required": True, "type": "text"},
                    "hero_image": {"required": True, "type": "image"},
                    "description": {"required": True, "type": "text"},
                    "feature_items": {"required": True, "type": "array", "min_count": 2},
                    "cta": {"required": False, "type": "text"},
                },
            },
            "data_report": {
                "sections": ["summary", "key_findings", "details", "conclusion"],
                "layout_type": "flow",
                "animation_type": "build_up",
                "pattern_type": "overview_detail",
                "element_roles": {
                    "title": {"required": True, "type": "text"},
                    "summary": {"required": True, "type": "text"},
                    "charts": {"required": True, "type": "array", "min_count": 1},
                    "insights": {"required": True, "type": "array", "min_count": 2},
                    "conclusion": {"required": False, "type": "text"},
                },
            },
            "comparison": {
                "sections": ["introduction", "comparison", "conclusion"],
                "layout_type": "split",
                "animation_type": "synchronized",
                "pattern_type": "comparison",
                "element_roles": {
                    "title": {"required": True, "type": "text"},
                    "intro": {"required": False, "type": "text"},
                    "left_items": {"required": True, "type": "array", "min_count": 1},
                    "right_items": {"required": True, "type": "array", "min_count": 1},
                    "conclusion": {"required": False, "type": "text"},
                },
            },
            "case_study": {
                "sections": ["overview", "challenge", "solution", "results"],
                "layout_type": "flow",
                "animation_type": "sequence_in",
                "pattern_type": "problem_solution",
                "element_roles": {
                    "title": {"required": True, "type": "text"},
                    "overview": {"required": True, "type": "text"},
                    "challenge": {"required": True, "type": "text"},
                    "solution": {"required": True, "type": "text"},
                    "results": {"required": True, "type": "text"},
                    "images": {"required": False, "type": "array"},
                },
            },
        }

        # 应用所选模板类型的默认设置
        if self.template_type in template_presets:
            for key, value in template_presets[self.template_type].items():
                self.properties.setdefault(key, value)
    
    def _process_content_elements(self):
        """处理内容元素"""
        # 验证和处理内容元素
        if "element_roles" in self.properties:
            for role, requirements in self.properties["element_roles"].items():
                if role in self.content_elements:
                    self.processed_elements[role] = self.content_elements[role]
                elif requirements.get("required", False):
                    # 创建占位元素
                    if requirements.get("type") == "text":
                        self.processed_elements[role] = f"[{role.replace('_', ' ').title()}]"
                    elif requirements.get("type") == "image":
                        self.processed_elements[role] = f"placeholder_{role}.png"
                    elif requirements.get("type") == "array":
                        self.processed_elements[role] = []

        self.properties["content"] = self.processed_elements
    
    def to_dict(self):
        """转换为字典表示"""
        return {
            "organization_type": "content_template",
            "template_type": self.template_type,
            "properties": self.properties,
            "id": self.id,
        }
    
    def add_content_element(self, role, element):
        """添加内容元素"""
        self.content_elements[role] = element
        self._process_content_elements()
        return self
    
    def update_properties(self, new_properties):
        """更新模板属性"""
        self.properties.update(new_properties)
        return self
