from typing import Optional

from manim import DOWN, LEFT, RIGHT, UP, Animation, Create, FadeIn, Scene, VGroup

from visual_system.utils.helpers import generate_id


class ContentPattern:
    """
    Create a content pattern that applies semantic structure to elements

    Args:
        elements (list): Elements to arrange in a pattern
        pattern_type (str): Pattern type -
            "sequence" - Sequential presentation of steps or stages
            "comparison" - Comparison between items
            "hierarchy" - Hierarchical organization
            "process" - Process or workflow
            "problem_solution" - Problem and solution structure
            "cause_effect" - Cause and effect relationship
            "question_answer" - Question and answer format
            "thesis_support" - Thesis with supporting evidence
            "concept_example" - Concept with examples
            "case_study" - Case study presentation
            "data_report" - Data-driven report pattern
        properties (dict, optional): Pattern properties specific to the pattern type
        id (str, optional): Pattern unique identifier, auto-generated if not provided

    Examples:
        # Create a simple sequence pattern
        sequence = ContentPattern([intro, step1, step2, step3, conclusion], "sequence")

        # Create a comparison pattern with specific properties
        comparison = ContentPattern([item1, item2, item3], "comparison", {
            "criteria": ["price", "performance", "reliability"],
            "highlight_differences": True
        })

        # Create a hierarchical structure
        hierarchy = ContentPattern([main_topic, subtopic1, subtopic2, detail1, detail2], "hierarchy", {
            "direction": "top-down",
            "level_indicator": "indent"
        })

        # Create a process flow
        process = ContentPattern([start, process1, process2, end], "process", {
            "flow_direction": "left-to-right",
            "show_connections": True,
            "connection_style": "arrow"
        })

        # Create a problem-solution pattern
        problem_solution = ContentPattern([problem, solution1, solution2], "problem_solution", {
            "problem_emphasis": "strong",
            "solution_count": "multiple"
        })

        # Create a cause-effect relationship
        cause_effect = ContentPattern([cause1, cause2, effect], "cause_effect", {
            "cause_count": "multiple",
            "effect_emphasis": "strong"
        })

        # Create a question-answer format
        qa = ContentPattern([question1, answer1, question2, answer2], "question_answer", {
            "question_style": "highlight",
            "answer_visibility": "reveal"
        })

        # Create a thesis with supporting evidence
        thesis = ContentPattern([main_thesis, evidence1, evidence2, conclusion], "thesis_support", {
            "thesis_position": "start",
            "evidence_style": "bulleted"
        })

        # Create a concept with examples
        concept = ContentPattern([main_concept, example1, example2], "concept_example", {
            "concept_emphasis": "clear",
            "example_count": "multiple"
        })

        # Create a case study presentation
        case_study = ContentPattern([background, situation, analysis, outcome], "case_study", {
            "structure": "narrative",
            "detail_level": "comprehensive"
        })

        # Create a data report pattern
        report = ContentPattern([summary, chart1, chart2, insights], "data_report", {
            "focus": "trends",
            "insight_level": "detailed"
        })
    """

    def __init__(self, elements, pattern_type, properties=None, id=None):
        self.elements = elements
        self.pattern_type = pattern_type
        self.properties = properties or {}
        self.id = id or generate_id()
        self._apply_pattern_defaults()
        self.manim_group = None

    def _apply_pattern_defaults(self):
        """应用模式类型默认属性"""
        # 模式类型默认设置
        pattern_defaults = {
            "sequence": {
                "direction": "linear",
                "numbering": True,
                "transitions": "smooth",
            },
            "comparison": {
                "layout": "side-by-side",
                "criteria_visibility": "emphasized",
                "highlight_differences": True,
            },
            "hierarchy": {
                "direction": "top-down",
                "level_indicator": "indent",
                "collapse_levels": False,
            },
            "process": {
                "flow_direction": "left-to-right",
                "show_connections": True,
                "connection_style": "arrow",
            },
            "problem_solution": {
                "problem_emphasis": "moderate",
                "solution_count": "multiple",
                "connection_visibility": "explicit",
            },
            "cause_effect": {
                "cause_count": "multiple",
                "effect_emphasis": "strong",
                "relationship_style": "direct",
            },
            "question_answer": {
                "question_style": "highlight",
                "answer_visibility": "immediate",
                "question_grouping": "related",
            },
            "thesis_support": {
                "thesis_position": "start",
                "evidence_style": "bulleted",
                "conclusion_presence": True,
            },
            "concept_example": {
                "concept_emphasis": "clear",
                "example_count": "multiple",
                "abstraction_level": "mixed",
            },
            "case_study": {
                "structure": "narrative",
                "detail_level": "comprehensive",
                "outcome_emphasis": "balanced",
            },
            "data_report": {
                "summary_position": "start",
                "insight_visibility": "highlighted",
                "data_granularity": "appropriate",
                "trend_emphasis": True,
            },
        }

        # 应用对应模式类型的默认设置
        if self.pattern_type in pattern_defaults:
            for key, value in pattern_defaults[self.pattern_type].items():
                self.properties.setdefault(key, value)

    def to_dict(self):
        """转换为字典表示"""
        # 提取元素ID
        element_ids = []
        for e in self.elements:
            if isinstance(e, dict) and "id" in e:
                element_ids.append(e["id"])
            elif hasattr(e, "id"):
                element_ids.append(e.id)
            else:
                element_ids.append(e)

        return {
            "organization_type": "content_pattern",
            "pattern_type": self.pattern_type,
            "elements": element_ids,
            "properties": self.properties,
            "id": self.id,
        }

    def add_element(self, element):
        """添加元素到内容模式"""
        self.elements.append(element)
        return self

    def update_properties(self, new_properties):
        """更新内容模式属性"""
        self.properties.update(new_properties)
        return self

    def to_manim_object(self) -> VGroup:
        """
        转换为Manim对象

        Returns:
            VGroup: Manim中表示内容模式的VGroup对象
        """
        # 获取所有元素的Manim对象
        manim_elements = []
        for element in self.elements:
            if hasattr(element, "to_manim_object"):
                manim_obj = element.to_manim_object()
                if manim_obj is not None:
                    manim_elements.append(manim_obj)

        # 创建一个VGroup包含所有元素
        self.manim_group = VGroup(*manim_elements)

        # 根据模式类型应用不同的排列逻辑
        self._apply_pattern_arrangement()

        return self.manim_group

    def _apply_pattern_arrangement(self):
        """根据模式类型应用排列逻辑"""
        if not self.manim_group:
            return

        # 根据模式类型应用不同的排列
        arrangement_methods = {
            "sequence": self._arrange_sequence,
            "comparison": self._arrange_comparison,
            "hierarchy": self._arrange_hierarchy,
            "process": self._arrange_process,
            "problem_solution": self._arrange_problem_solution,
            "cause_effect": self._arrange_cause_effect,
            "question_answer": self._arrange_question_answer,
            "thesis_support": self._arrange_thesis_support,
            "concept_example": self._arrange_concept_example,
            "case_study": self._arrange_case_study,
            "data_report": self._arrange_data_report,
        }

        if self.pattern_type in arrangement_methods:
            arrangement_methods[self.pattern_type]()

    def _arrange_sequence(self):
        """排列顺序序列"""
        direction_prop = self.properties.get("direction", "linear")

        if direction_prop == "linear":
            # 线性排列
            self.manim_group.arrange(DOWN, buff=0.5)
        elif direction_prop == "circular":
            # 环形排列（在实际实现中需要更复杂的逻辑）
            pass
        elif direction_prop == "zigzag":
            # 之字形排列（在实际实现中需要更复杂的逻辑）
            pass

    def _arrange_comparison(self):
        """排列比较模式"""
        layout = self.properties.get("layout", "side-by-side")

        if layout == "side-by-side":
            # 并排排列
            self.manim_group.arrange(RIGHT, buff=1.0)
        elif layout == "grid":
            # 网格排列（在实际实现中需要更复杂的逻辑）
            pass

    def _arrange_hierarchy(self):
        """排列层次结构"""
        direction = self.properties.get("direction", "top-down")

        if direction == "top-down":
            # 自上而下排列
            self.manim_group.arrange(DOWN, buff=0.5)
            # 在实际实现中应该处理缩进等层次结构
        elif direction == "left-to-right":
            # 从左到右排列
            self.manim_group.arrange(RIGHT, buff=0.5)

    def _arrange_process(self):
        """排列流程"""
        direction = self.properties.get("flow_direction", "left-to-right")

        if direction == "left-to-right":
            self.manim_group.arrange(RIGHT, buff=1.0)
        elif direction == "top-down":
            self.manim_group.arrange(DOWN, buff=1.0)

    def _arrange_problem_solution(self):
        """排列问题解决方案结构"""
        # 简单实现：问题在左，解决方案在右
        self.manim_group.arrange(RIGHT, buff=1.5)

    def _arrange_cause_effect(self):
        """排列因果关系"""
        # 简单实现：原因在左，结果在右
        self.manim_group.arrange(RIGHT, buff=1.0)

    def _arrange_question_answer(self):
        """排列问答结构"""
        # 简单实现：问题在上，答案在下
        self.manim_group.arrange(DOWN, buff=0.5)

    def _arrange_thesis_support(self):
        """排列论点和支持证据"""
        thesis_position = self.properties.get("thesis_position", "start")

        if thesis_position == "start":
            # 论点在顶部，支持证据在下方
            self.manim_group.arrange(DOWN, buff=0.5)
        elif thesis_position == "end":
            # 支持证据在上方，论点在底部
            self.manim_group.arrange(DOWN, buff=0.5)
            # 在实际实现中应该调整顺序

    def _arrange_concept_example(self):
        """排列概念和示例结构"""
        # 概念在上，示例在下
        self.manim_group.arrange(DOWN, buff=0.7)

    def _arrange_case_study(self):
        """排列案例研究结构"""
        structure = self.properties.get("structure", "narrative")

        if structure == "narrative":
            # 叙述性结构，线性排列
            self.manim_group.arrange(DOWN, buff=0.5)
        elif structure == "analytical":
            # 分析性结构，可能需要更复杂的排列
            self.manim_group.arrange(DOWN, buff=0.5)

    def _arrange_data_report(self):
        """排列数据报告结构"""
        summary_position = self.properties.get("summary_position", "start")

        if summary_position == "start":
            # 摘要在顶部，数据图表和见解在下方
            self.manim_group.arrange(DOWN, buff=0.7)
        elif summary_position == "end":
            # 数据图表和见解在上方，摘要在底部
            self.manim_group.arrange(DOWN, buff=0.7)
            # 在实际实现中应该调整顺序

    def create_animations(self) -> list[Animation]:
        """
        创建与内容模式相关的动画

        Returns:
            List[Animation]: Manim动画列表
        """
        animations = []

        # 根据模式类型创建合适的动画
        if not self.manim_group:
            self.to_manim_object()

        # 基本动画：创建整个组
        transitions = self.properties.get("transitions", "smooth")

        if transitions == "smooth":
            animations.append(FadeIn(self.manim_group))
        elif transitions == "sequential":
            # 在真实实现中，这里应该为每个元素创建单独的动画，并按顺序播放
            for element in self.manim_group:
                animations.append(Create(element))
        elif transitions == "emphasized":
            # 在真实实现中，这里应该创建带有强调效果的动画
            animations.append(Create(self.manim_group))

        return animations

    def get_manim_object(self) -> Optional[VGroup]:
        """
        获取Manim对象

        Returns:
            Optional[VGroup]: Manim VGroup对象，如果尚未创建则返回None
        """
        if not self.manim_group:
            self.to_manim_object()
        return self.manim_group


# 测试场景
class ContentPatternDemo(Scene):
    def construct(self):
        # 导入TextElement用于创建测试内容
        from visual_system.elements.text import TextElement

        # 首先创建一个标题
        title = TextElement("Content Pattern Examples", {"role": "title", "align": "center"})
        title_obj = title.create_manim_object()
        title_obj.to_edge(UP, buff=1)
        self.play(title.create_animation(title_obj))

        # 创建序列模式
        sequence_title = TextElement("Sequence Pattern", {"role": "heading", "align": "left"})
        sequence_title_obj = sequence_title.create_manim_object()
        sequence_title_obj.next_to(title_obj, DOWN, buff=1)
        sequence_title_obj.to_edge(LEFT, buff=1)
        self.play(sequence_title.create_animation(sequence_title_obj))

        # 序列模式元素
        intro = TextElement("Introduction", {"size": "medium"})
        step1 = TextElement("Step 1", {"size": "medium"})
        step2 = TextElement("Step 2", {"size": "medium"})
        step3 = TextElement("Step 3", {"size": "medium"})
        conclusion = TextElement("Conclusion", {"size": "medium"})

        # 创建序列模式
        sequence = ContentPattern(
            [intro, step1, step2, step3, conclusion],
            "sequence",
            {"direction": "linear", "transitions": "sequential"},
        )

        # 获取Manim对象并设置位置
        sequence_obj = sequence.to_manim_object()
        sequence_obj.scale(0.6)  # 缩小以适应屏幕
        sequence_obj.next_to(sequence_title_obj, DOWN, buff=0.5)
        sequence_obj.to_edge(LEFT, buff=2)

        # 播放序列动画
        seq_animations = sequence.create_animations()
        for anim in seq_animations:
            self.play(anim)

        self.wait(1)

        # 创建比较模式
        comparison_title = TextElement("Comparison Pattern", {"role": "heading", "align": "left"})
        comparison_title_obj = comparison_title.create_manim_object()
        comparison_title_obj.next_to(sequence_obj, DOWN, buff=1)
        comparison_title_obj.to_edge(LEFT, buff=1)
        self.play(comparison_title.create_animation(comparison_title_obj))

        # 比较模式元素
        item1 = TextElement("Option A", {"size": "medium"})
        item2 = TextElement("Option B", {"size": "medium"})
        item3 = TextElement("Option C", {"size": "medium"})

        # 创建比较模式
        comparison = ContentPattern(
            [item1, item2, item3],
            "comparison",
            {"layout": "side-by-side", "highlight_differences": True},
        )

        # 获取Manim对象并设置位置
        comparison_obj = comparison.to_manim_object()
        comparison_obj.scale(0.7)  # 缩小以适应屏幕
        comparison_obj.next_to(comparison_title_obj, DOWN, buff=0.5)
        comparison_obj.to_edge(LEFT, buff=2)

        # 播放比较动画
        comp_animations = comparison.create_animations()
        for anim in comp_animations:
            self.play(anim)

        self.wait(1)

        # 创建层次结构模式
        hierarchy_title = TextElement("Hierarchy Pattern", {"role": "heading", "align": "left"})
        hierarchy_title_obj = hierarchy_title.create_manim_object()
        hierarchy_title_obj.next_to(comparison_obj, DOWN, buff=1)
        hierarchy_title_obj.to_edge(LEFT, buff=1)
        self.play(hierarchy_title.create_animation(hierarchy_title_obj))

        # 层次结构元素
        main_topic = TextElement("Main Topic", {"size": "medium", "style": "bold"})
        subtopic1 = TextElement("Subtopic 1", {"size": "small"})
        subtopic2 = TextElement("Subtopic 2", {"size": "small"})
        detail1 = TextElement("Detail 1", {"size": "small"})
        detail2 = TextElement("Detail 2", {"size": "small"})

        # 创建层次结构模式
        hierarchy = ContentPattern(
            [main_topic, subtopic1, subtopic2, detail1, detail2],
            "hierarchy",
            {"direction": "top-down", "level_indicator": "indent"},
        )

        # 获取Manim对象并设置位置
        hierarchy_obj = hierarchy.to_manim_object()
        hierarchy_obj.scale(0.6)  # 缩小以适应屏幕
        hierarchy_obj.next_to(hierarchy_title_obj, DOWN, buff=0.5)
        hierarchy_obj.to_edge(LEFT, buff=2)

        # 播放层次结构动画
        hier_animations = hierarchy.create_animations()
        for anim in hier_animations:
            self.play(anim)

        self.wait(1)

        # 创建流程模式
        process_title = TextElement("Process Pattern", {"role": "heading", "align": "left"})
        process_title_obj = process_title.create_manim_object()
        process_title_obj.next_to(hierarchy_obj, DOWN, buff=1)
        process_title_obj.to_edge(LEFT, buff=1)
        self.play(process_title.create_animation(process_title_obj))

        # 流程元素
        start = TextElement("Start", {"size": "medium"})
        process1 = TextElement("Process 1", {"size": "medium"})
        process2 = TextElement("Process 2", {"size": "medium"})
        end = TextElement("End", {"size": "medium"})

        # 创建流程模式
        process = ContentPattern(
            [start, process1, process2, end],
            "process",
            {"flow_direction": "left-to-right", "show_connections": True},
        )

        # 获取Manim对象并设置位置
        process_obj = process.to_manim_object()
        process_obj.scale(0.6)  # 缩小以适应屏幕
        process_obj.next_to(process_title_obj, DOWN, buff=0.5)
        process_obj.to_edge(LEFT, buff=2)

        # 播放流程动画
        proc_animations = process.create_animations()
        for anim in proc_animations:
            self.play(anim)

        self.wait(2)


if __name__ == "__main__":
    # 运行测试用例
    from manim import config

    config.preview = True
    scene = ContentPatternDemo()
    scene.render()
