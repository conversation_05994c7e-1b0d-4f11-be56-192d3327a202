import os
import sys
import uuid

# 将项目根目录添加到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../.."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from visual_system.utils.helpers import generate_id
from manim import (
    DOWN, LEFT, RIGHT, UP, WHITE, BLUE, GREEN, YELLOW, RED, ORIGIN, BLUE_D, BLUE_E, RED_E, GREEN_E, PURPLE_E, GREY_D,
    VGroup, Text, Rectangle, RoundedRectangle, FadeIn, Create, Write, FadeOut, 
    Scene, config, Mobject, BLACK, Triangle, Circle, Square
)


class Layout(VGroup):
    """
    创建基于相对位置的布局，用于组织和排列Manim元素
    
    布局类提供多种预设布局类型，使元素能够根据指定的关系自动排列。
    核心功能包括：
    - 多种布局类型：流式、网格、卡片、分割、相对位置等
    - 灵活的元素位置引用：允许通过名称/ID引用其他元素
    - 自动大小适配：根据内容自动调整布局大小以适应画布
    
    Args:
        elements (list): 要布局的元素列表。有两种添加元素的方式：
            1. 直接添加元素: [element1, element2, ...]
            2. 带位置信息的元素: [(element1, {"position": "left"}), (element2, {"position": "right"}), ...]
               
        layout_type (str): 布局类型，支持以下类型：
            - "flow": 流式布局，元素按顺序水平或垂直排列
            - "flex": 弹性布局，灵活排列元素并支持不同的对齐方式
            - "grid": 网格布局，将元素排列在规则的网格中
            - "relative": 相对位置布局，元素相互之间按照指定关系定位
            - "stack": 堆叠布局，将元素堆叠在一起，可部分重叠
            - "card": 卡片布局，为内容创建卡片式容器
            - "split": 分割布局，将内容分为左右或上下两部分
            - "tabs": 标签页布局，创建带标签的内容区域
            - "sidebar": 侧边栏布局，将内容分为侧边栏和主内容区
            - "centered": 居中布局，居中显示所有元素
            
        properties (dict, optional): 布局属性，具体取决于布局类型。常用属性包括：
            - 通用属性：
                - "gap": 元素间距
                - "padding": 内边距
                - "margin": 外边距
                
            - 特定布局属性举例：
                - 流式布局: {"direction": "horizontal"/"vertical", "spacing": 0.5}
                - 网格布局: {"columns": 3, "gap": 0.5}
                - 分割布局: {"ratio": "2:1", "direction": "horizontal", "gap": 0.5}
                
        id (str, optional): 布局的唯一标识符，如不提供则自动生成
        
        auto_scale (bool, optional): 是否自动缩放以适应画布，默认为True
    
    位置信息规范:
        位置信息可包含以下键：
        
        基本引用:
            "name": 元素的名称，可被其他元素引用
            "id": 元素的ID，可被其他元素引用
        
        相对位置:
            "position": 相对位置，如"left", "right", "above", "below"
                也支持中文：如"左边", "右侧", "上方", "下面"
            "related_to": 参照元素的名称、ID或索引
            "reference": 同related_to，指定参照元素
        
        对齐和边距:
            "aligned": 对齐方式，如"left", "center", "right", "top", "bottom"
                也支持中文：如"左对齐", "居中", "右对齐"
            "margin"/"buff"/"padding": 元素之间的边距
        
        精确定位:
            "shift": [x, y] 额外的位置偏移，如[1, -0.5]表示向右1个单位，向下0.5个单位
            "to_edge": 靠边对齐，如"left", "right", "top", "bottom"
    
    示例:
        ```python
        # 创建流式布局
        flow_layout = Layout(
            [title, paragraph, image], 
            "flow", 
            {"direction": "vertical", "spacing": 0.5}
        )
        
        # 创建网格布局
        grid_layout = Layout(
            [image1, image2, image3, image4], 
            "grid", 
            {"columns": 2, "gap": 0.5}
        )
        
        # 创建分割布局，使用位置指定
        split_layout = Layout(
            [(left_content, {"position": "left"}),
             (right_content, {"position": "right"})], 
            "split", 
            {"ratio": "2:1", "direction": "horizontal"}
        )
        
        # 创建相对位置布局，通过名称引用元素
        relative_layout = Layout([
            (title, {"name": "title", "position": "start"}),
            (paragraph, {"related_to": "title", "position": "below", "margin": 0.5}),
            (image, {"related_to": "paragraph", "position": "right", "aligned": "top"})
        ], "relative")
        
        # 动态添加元素
        layout.add_element(new_element, {"position": "below", "related_to": "title"})
        
        # 更新布局属性
        layout.update_properties({"gap": 1})
        ```
    """

    # Layout presets
    layout_presets = {
        "flow": {
            "direction": "vertical",
            "spacing": 0.5,
            "align": "start",
        },
        "flex": {
            "direction": "row",
            "justify": "space-between",
            "align": "center",
            "wrap": True,
        },
        "grid": {
            "columns": 3,
            "row_alignments": "center",
            "col_alignments": "center",
            "gap": 0.5,
            "column_width": None,  # Added from visual_system
        },
        "relative": {
            "reference": "previous",
            "default_position": "below",
        },
        "stack": {
            "align": "center",
            "overlap": "partial",
        },
        "card": {
            "padding": 0.5,
            "shadow": True,
            "rounded": True,
        },
        "split": {
            "ratio": "1:1",
            "direction": "horizontal",
            "gap": 0.25,
        },
        "tabs": {
            "default_tab": 0,
            "tab_position": "top",
            "tab_height": 0.5,
        },
        "sidebar": {
            "side": "left",
            "width": 3,
            "gap": 0.5,
        },
        "centered": {
            "vertical": True,
            "horizontal": True,
            "max_width": 8,
        },
    }

    def __init__(self, elements, layout_type, properties=None, id=None, auto_scale=True):
        super().__init__()
        
        # 处理元素位置信息：如果元素是元组 (element, position_data)，则提取位置信息
        self.elements = []
        self.element_positions = []
        
        for item in elements:
            if isinstance(item, tuple) and len(item) == 2:
                element, position_data = item
                self.elements.append(element)
                self.element_positions.append(position_data)
            else:
                self.elements.append(item)
                self.element_positions.append({})  # 默认空位置数据
                
        self.layout_type = layout_type
        self.properties = properties or {}
        self.id = id or self._generate_id()
        self.auto_scale = auto_scale
        
        # 构建元素ID映射，用于通过ID或名称引用元素
        self._build_element_id_map()
        
        # 将位置信息整合到properties中
        self._integrate_position_data()

        # Add all elements as submobjects
        for element in self.elements:
            self.add(element)

        # Apply layout presets
        self._apply_presets()

        # Apply the appropriate layout
        self._apply_layout()
        
        # 如果启用自动缩放，适应画布尺寸
        if self.auto_scale:
            self._auto_scale_to_fit()

    def _generate_id(self):
        """Generate a unique identifier for the layout"""
        try:
            # Try to use the helper function from visual_system if available
            return generate_id()
        except (ImportError, NameError):
            # Fall back to uuid method if the helper is not available
            return str(uuid.uuid4())[:8]
    
    def _build_element_id_map(self):
        """构建元素ID和名称的映射表，用于相对位置引用"""
        self.element_id_map = {}
        
        for i, element in enumerate(self.elements):
            # 如果元素有ID属性，将其加入映射表
            if hasattr(element, 'id'):
                self.element_id_map[element.id] = i
            
            # 如果元素位置数据中包含id或name，也加入映射表
            if i < len(self.element_positions):
                pos_data = self.element_positions[i]
                if 'id' in pos_data:
                    self.element_id_map[pos_data['id']] = i
                if 'name' in pos_data:
                    self.element_id_map[pos_data['name']] = i
            
            # 默认添加索引映射
            self.element_id_map[str(i)] = i
            
            # 如果元素是Text对象，尝试使用其文本内容作为引用
            if hasattr(element, 'text') and isinstance(element.text, str):
                self.element_id_map[element.text] = i
            
            # 处理VGroup中包含Text的情况
            if hasattr(element, 'submobjects'):
                for submob in element.submobjects:
                    if hasattr(submob, 'text') and isinstance(submob.text, str):
                        self.element_id_map[submob.text] = i
                        break  # 只使用第一个Text对象的文本
            
    def _integrate_position_data(self):
        """将各元素的位置数据整合到properties中"""
        if self.layout_type == "relative":
            # 对于相对布局，直接构建positions属性
            if "positions" not in self.properties:
                positions = []
                for i, pos_data in enumerate(self.element_positions):
                    position_entry = {"reference": i-1 if i > 0 else None}
                    position_entry.update(pos_data)
                    positions.append(position_entry)
                self.properties["positions"] = positions
        
        elif self.layout_type == "split":
            # 对于split布局，根据位置信息调整元素的顺序
            if len(self.elements) >= 2:
                # 检查是否有元素指定了left/right位置
                left_idx = None
                right_idx = None
                
                for i, pos_data in enumerate(self.element_positions):
                    if pos_data.get("position") == "left":
                        left_idx = i
                    elif pos_data.get("position") == "right":
                        right_idx = i
                
                # 如果同时找到了左右元素，可能需要调整顺序
                if left_idx is not None and right_idx is not None and left_idx > right_idx:
                    # 交换元素位置
                    self.elements[left_idx], self.elements[right_idx] = self.elements[right_idx], self.elements[left_idx]
        
        elif self.layout_type == "sidebar":
            # 对于sidebar布局，根据位置信息调整元素顺序
            sidebar_idx = None
            content_idxs = []
            
            for i, pos_data in enumerate(self.element_positions):
                if pos_data.get("position") == "sidebar":
                    sidebar_idx = i
                elif pos_data.get("position") == "content":
                    content_idxs.append(i)
            
            # 如果找到了明确的sidebar元素，确保它是第一个
            if sidebar_idx is not None and sidebar_idx > 0:
                # 将sidebar元素移到第一位
                sidebar_element = self.elements.pop(sidebar_idx)
                self.elements.insert(0, sidebar_element)
                
                # 同样调整位置数据
                sidebar_pos_data = self.element_positions.pop(sidebar_idx)
                self.element_positions.insert(0, sidebar_pos_data)

    def _apply_presets(self):
        """Apply defaults for the selected layout type"""
        if self.layout_type in self.layout_presets:
            for key, value in self.layout_presets[self.layout_type].items():
                self.properties.setdefault(key, value)

    def _apply_layout(self):
        """Apply the appropriate layout based on layout_type"""
        layout_methods = {
            "flow": self._apply_flow_layout,
            "flex": self._apply_flex_layout,
            "grid": self._apply_grid_layout,
            "relative": self._apply_relative_layout,
            "stack": self._apply_stack_layout,
            "card": self._apply_card_layout,
            "split": self._apply_split_layout,
            "tabs": self._apply_tabs_layout,
            "sidebar": self._apply_sidebar_layout,
            "centered": self._apply_centered_layout,
        }

        if self.layout_type in layout_methods:
            layout_methods[self.layout_type]()
            
    def _auto_scale_to_fit(self):
        """根据画布大小自动缩放布局以避免溢出"""
        # 计算当前布局的边界框
        width = self.get_width()
        height = self.get_height()
        
        # 获取可用的画布尺寸（留些边距）
        available_width = config.frame_width * 0.9
        available_height = config.frame_height * 0.9
        
        # 计算缩放比例
        width_scale = available_width / width if width > available_width else 1
        height_scale = available_height / height if height > available_height else 1
        
        # 使用最小的缩放比例以确保完全适应画布
        scale_factor = min(width_scale, height_scale)
        
        # 只有当需要缩小时才应用缩放
        if scale_factor < 1:
            self.scale(scale_factor)
            
            # 检查元素间距是否过小，如果是则调整
            if self.layout_type in ["flow", "grid", "flex"] and "gap" in self.properties:
                # 不要让间距小于最小值
                min_gap = 0.1
                if self.properties["gap"] * scale_factor < min_gap:
                    # 重新应用布局，但使用较大的间距
                    self.properties["gap"] = min_gap
                    # 重新排列元素
                    self._apply_layout()
                    # 再次检查和缩放
                    width = self.get_width()
                    height = self.get_height()
                    width_scale = available_width / width if width > available_width else 1
                    height_scale = available_height / height if height > available_height else 1
                    new_scale = min(width_scale, height_scale)
                    if new_scale < 1:
                        self.scale(new_scale)

    def _apply_flow_layout(self):
        """Arrange elements in a flow layout"""
        direction = self.properties.get("direction", "vertical")
        spacing = self.properties.get("spacing", 0.5)
        align = self.properties.get("align", "start")

        # Convert direction string to vector if needed
        direction_map = {"vertical": DOWN, "horizontal": RIGHT, "down": DOWN, "up": UP, "right": RIGHT, "left": LEFT}

        if direction in direction_map:
            direction = direction_map[direction]

        # Apply the arrange method to position elements
        self.arrange(direction, buff=spacing)

        # Handle alignment
        if align == "center":
            if direction in [DOWN, UP]:
                self.arrange_submobjects(DOWN, center=True, buff=spacing)
            else:
                self.arrange_submobjects(RIGHT, center=True, buff=spacing)
        elif align == "end":
            if direction in [DOWN, UP]:
                self.arrange_submobjects(DOWN, aligned_edge=RIGHT, buff=spacing)
            else:
                self.arrange_submobjects(RIGHT, aligned_edge=DOWN, buff=spacing)

    def _apply_flex_layout(self):
        """Arrange elements in a flexible layout"""
        direction = self.properties.get("direction", "row")
        justify = self.properties.get("justify", "space-between")
        # align = self.properties.get("align", "center")
        # wrap = self.properties.get("wrap", True)

        # Convert direction string to vector
        direction_map = {"row": RIGHT, "column": DOWN, "right": RIGHT, "left": LEFT, "down": DOWN, "up": UP}

        if direction in direction_map:
            direction = direction_map[direction]

        # Basic arrangement
        self.arrange(direction)

        # Apply justify
        if justify == "space-between":
            # Spread elements equally
            if len(self.elements) > 1:
                first = self.elements[0]
                last = self.elements[-1]
                if direction in [RIGHT, LEFT]:
                    first.to_edge(LEFT)
                    last.to_edge(RIGHT)
                else:
                    first.to_edge(UP)
                    last.to_edge(DOWN)

                # Position middle elements
                if len(self.elements) > 2:
                    middle_elements = self.elements[1:-1]
                    VGroup(*middle_elements).arrange(
                        direction, buff=(last.get_center() - first.get_center()).norm() / (len(middle_elements) + 1)
                    )

        elif justify == "center":
            self.center()

    def _apply_grid_layout(self):
        """Arrange elements in a grid layout"""
        columns = self.properties.get("columns", 3)
        gap = self.properties.get("gap", 0.5)
        column_width = self.properties.get("column_width", None)

        # Handle auto-fit columns case from visual_system
        if columns == "auto-fit" and column_width is not None:
            # Convert pixel width to manim units if needed
            if isinstance(column_width, str) and column_width.endswith("px"):
                try:
                    pixel_width = float(column_width.replace("px", ""))
                    # Approximate conversion from pixels to manim units
                    manim_width = pixel_width / 100
                    column_width = manim_width
                except ValueError:
                    column_width = 2.5  # Default fallback

            # Calculate columns based on available width and column width
            available_width = config.frame_width * 0.8
            columns = max(1, int(available_width / column_width))

        # 处理元素位置信息，对于网格布局，可以使用area属性指定元素占据的网格区域
        # 将修改后的元素按网格区域排列
        element_areas = []
        for i, pos_data in enumerate(self.element_positions):
            if "area" in pos_data:
                # 解析面积定义，例如 "1,2,2,3" 表示从(1,2)开始到(2,3)的区域
                element_areas.append((i, pos_data["area"]))
        
        # 如果有元素指定了网格区域，需要特殊处理
        if element_areas:
            # 这里可以添加处理网格区域的代码，但这需要更复杂的实现
            # 简单起见，现在只打印一个警告
            print("Warning: Grid area positioning is not fully implemented yet.")
        
        # 使用Manim的内置arrange_in_grid方法
        self.arrange_in_grid(cols=columns, buff=gap)

    def _apply_relative_layout(self):
        """Position elements relative to each other"""
        # 自动设置相对位置，但优先使用用户指定的位置
        self._setup_relative_positions()

        # Apply positions based on the setup
        positions = self.properties.get("positions", [])

        for i, position_data in enumerate(positions):
            if i == 0:
                # First element is positioned at origin or specified position
                if "position" in position_data and position_data["position"] == "start":
                    self.elements[i].move_to(ORIGIN)
            else:
                # 处理相对位置
                self._position_element_relative(i, position_data)

    def _position_element_relative(self, index, position_data):
        """根据位置数据相对定位元素
        
        Args:
            index: 元素索引
            position_data: 位置数据字典，可以包含多种引用方式
                - reference: 参考元素索引或ID/名称
                - position: 位置指示词，如"left"、"right"、"above"、"below"等
                - to_edge: 靠边对齐，如"left"、"right"、"top"、"bottom"
                - aligned: 对齐方式，如"center"、"left"、"right"等
                - margin/buff: 边距
        """
        if index >= len(self.elements):
            return
            
        # 获取当前元素
        element = self.elements[index]
        
        # 处理靠边对齐
        if "to_edge" in position_data:
            edge = position_data["to_edge"]
            buff = self._get_margin(position_data)
            if edge == "left":
                element.to_edge(LEFT, buff=buff)
            elif edge == "right":
                element.to_edge(RIGHT, buff=buff)
            elif edge == "top":
                element.to_edge(UP, buff=buff)
            elif edge == "bottom":
                element.to_edge(DOWN, buff=buff)
            return
                
        # 处理相对于其他元素的位置
        reference_id = position_data.get("reference")
        reference_idx = self._resolve_reference(reference_id, index)
        
        if reference_idx is None and "related_to" in position_data:
            # 尝试使用更灵活的related_to字段查找参考元素
            reference_id = position_data.get("related_to")
            reference_idx = self._resolve_reference(reference_id, index)
            
        if reference_idx is None:
            # 如果没有有效的参考元素，使用默认的位置
            reference_idx = index - 1
        
        reference = self.elements[reference_idx] if 0 <= reference_idx < len(self.elements) else None
        
        if reference is None:
            return
            
        # 获取位置和边距
        position = position_data.get("position", "below")
        margin = self._get_margin(position_data)
            
        # 处理方向映射
        direction_map = {
            "below": DOWN, "under": DOWN, "bottom": DOWN, "down": DOWN,
            "above": UP, "over": UP, "top": UP, "up": UP,
            "right": RIGHT, "after": RIGHT,
            "left": LEFT, "before": LEFT
        }
        
        # 支持中文方向名称
        direction_map_cn = {
            "下方": DOWN, "下面": DOWN, "底部": DOWN, "向下": DOWN,
            "上方": UP, "上面": UP, "顶部": UP, "向上": UP,
            "右侧": RIGHT, "右边": RIGHT, "向右": RIGHT, "后面": RIGHT,
            "左侧": LEFT, "左边": LEFT, "向左": LEFT, "前面": LEFT
        }
        direction_map.update(direction_map_cn)
        
        # 获取方向向量
        direction = direction_map.get(position, DOWN)
        
        # 处理对齐方式
        aligned_edge = None
        if "aligned" in position_data:
            alignment = position_data["aligned"]
            if alignment == "center":
                # 居中对齐，不设置aligned_edge
                pass
            elif alignment == "left":
                aligned_edge = LEFT
            elif alignment == "right":
                aligned_edge = RIGHT
            elif alignment == "top":
                aligned_edge = UP
            elif alignment == "bottom":
                aligned_edge = DOWN
            elif alignment in ["垂直居中", "水平居中"]:
                # 中文对齐方式
                pass
            elif alignment in ["左对齐", "左边对齐"]:
                aligned_edge = LEFT
            elif alignment in ["右对齐", "右边对齐"]:
                aligned_edge = RIGHT
            elif alignment in ["顶部对齐", "上边对齐"]:
                aligned_edge = UP
            elif alignment in ["底部对齐", "下边对齐"]:
                aligned_edge = DOWN
        
        # 应用相对位置
        if aligned_edge:
            element.next_to(reference, direction, buff=margin, aligned_edge=aligned_edge)
        else:
            element.next_to(reference, direction, buff=margin)
            
        # 处理额外偏移
        if "shift" in position_data:
            shift_data = position_data["shift"]
            if isinstance(shift_data, (list, tuple)) and len(shift_data) == 2:
                x_shift, y_shift = shift_data
                element.shift(RIGHT * float(x_shift) + UP * float(y_shift))
            
    def _resolve_reference(self, reference, current_index):
        """解析参考元素的标识，返回对应的索引
        
        Args:
            reference: 参考元素的标识，可以是索引、ID或名称
            current_index: 当前元素的索引
            
        Returns:
            int: 参考元素的索引，如果无法解析则返回None
        """
        if reference is None:
            return None
            
        # 如果是整数索引，直接返回
        if isinstance(reference, int):
            if 0 <= reference < len(self.elements):
                return reference
            return None
            
        # 处理字符串引用
        if isinstance(reference, str):
            # 尝试转换为索引
            try:
                idx = int(reference)
                if 0 <= idx < len(self.elements):
                    return idx
            except ValueError:
                pass
                
            # 查找ID/名称映射
            if reference in self.element_id_map:
                return self.element_id_map[reference]
                
            # 特殊引用
            if reference == "previous" or reference == "前一个":
                return current_index - 1 if current_index > 0 else None
            if reference == "first" or reference == "第一个":
                return 0
            if reference == "last" or reference == "最后一个":
                return len(self.elements) - 1
        
        return None
        
    def _get_margin(self, position_data):
        """从位置数据中获取边距值
        
        Args:
            position_data: 位置数据字典
            
        Returns:
            float: 边距值，默认为0.5
        """
        # 检查多种可能的边距名称
        for margin_key in ["margin", "buff", "padding", "space", "间距", "边距"]:
            if margin_key in position_data:
                margin = position_data[margin_key]
                # 转换像素值
                if isinstance(margin, str) and margin.endswith("px"):
                    try:
                        pixel_margin = float(margin.replace("px", ""))
                        # 近似转换为Manim单位
                        return pixel_margin / 100
                    except ValueError:
                        return 0.5
                return float(margin)
        return 0.5

    def _setup_relative_positions(self):
        """Setup relative layout position information"""
        if "positions" not in self.properties:
            element_count = len(self.elements)
            positions = []

            for i in range(element_count):
                if i == 0:
                    # First element has no reference
                    positions.append({"reference": None, "position": "start"})
                else:
                    # 使用用户指定的位置数据，如果有的话
                    position_data = self.element_positions[i].copy()
                    
                    # 如果没有指定位置，则根据元素类型推断位置
                    if "position" not in position_data:
                        ref_element = self.elements[i - 1]
                        ref_type = self._get_element_type(ref_element)

                        if ref_type in ["Text", "MathTex", "Tex", "Table", "text", "chart", "table"]:
                            # Text, formulas, tables typically flow vertically
                            position_data["position"] = "below"
                        elif ref_type in ["ImageMobject", "Rectangle", "Circle", "image", "video"]:
                            # Images and shapes might be side by side or vertical
                            position_data["position"] = "right" if i % 2 == 1 else "below"
                        else:
                            # Default placement below
                            position_data["position"] = "below"
                    
                    # 如果没有指定参考元素，则使用前一个元素作为参考
                    if "reference" not in position_data:
                        position_data["reference"] = i - 1
                        
                    positions.append(position_data)

            self.properties["positions"] = positions

    def _get_element_type(self, element):
        """Determine the type of an element"""
        if isinstance(element, dict) and "element_type" in element:
            # Support for visual_system's element type
            return element["element_type"]
        elif hasattr(element, "element_type"):
            return element.element_type
        return type(element).__name__

    def _apply_stack_layout(self):
        """Arrange elements in a stack (overlapping)"""
        # align = self.properties.get("align", "center")
        overlap = self.properties.get("overlap", "partial")

        # Position the base element
        if self.elements:
            base = self.elements[0]
            base.move_to(ORIGIN)

            # Stack other elements on top
            for i, element in enumerate(self.elements[1:], 1):
                element.move_to(base)

                # Apply offset for partial overlap
                if overlap == "partial":
                    offset = 0.1 * i * UP + 0.1 * i * RIGHT
                    element.shift(offset)

                # 根据元素位置数据调整堆叠位置
                if self.element_positions[i]:
                    pos_data = self.element_positions[i]
                    if "offset_x" in pos_data and "offset_y" in pos_data:
                        # 用户指定的偏移量覆盖默认偏移
                        offset_x = float(pos_data["offset_x"])
                        offset_y = float(pos_data["offset_y"])
                        element.shift(RIGHT * offset_x + UP * offset_y)

                # Handle z-index to make stacking visible
                # Higher z-index = appears on top
                element.set_z_index(i)

    def _apply_card_layout(self):
        """Create a card layout with content and optional border"""
        padding = self.properties.get("padding", 0.5)
        shadow = self.properties.get("shadow", True)
        rounded = self.properties.get("rounded", True)

        # Convert pixel padding to manim units if needed
        if isinstance(padding, str) and padding.endswith("px"):
            try:
                pixel_padding = float(padding.replace("px", ""))
                # Approximate conversion from pixels to manim units
                padding = pixel_padding / 100
            except ValueError:
                padding = 0.5  # Default fallback

        # Arrange contents in a flow layout inside
        inner_group = VGroup(*self.elements)
        inner_group.arrange(DOWN, buff=padding / 2)

        # Create card background
        background_width = inner_group.get_width() + padding * 2
        background_height = inner_group.get_height() + padding * 2

        if rounded:
            background = RoundedRectangle(
                width=background_width, height=background_height, corner_radius=0.2, fill_opacity=0.1, stroke_width=1
            )
        else:
            background = Rectangle(width=background_width, height=background_height, fill_opacity=0.1, stroke_width=1)

        # Add shadow if requested
        if shadow:
            shadow_rect = background.copy()
            shadow_rect.set_fill(BLACK, opacity=0.08)
            shadow_rect.set_stroke(width=0)
            shadow_rect.shift(0.1 * DOWN + 0.1 * RIGHT)
            shadow_rect.set_z_index(-1)
            self.add(shadow_rect)

        # Add background as first element so it's behind content
        background.set_z_index(-0.5)
        self.add(background)

        # Center everything
        inner_group.move_to(background.get_center())

    def _apply_split_layout(self):
        """Create a split layout dividing content into sections"""
        ratio = self.properties.get("ratio", "1:1")
        direction = self.properties.get("direction", "horizontal")
        gap = self.properties.get("gap", 0.25)

        # Parse ratio
        try:
            left_ratio, right_ratio = map(int, ratio.split(":"))
            total_ratio = left_ratio + right_ratio
            left_frac = left_ratio / total_ratio
            right_frac = right_ratio / total_ratio
        except ValueError:
            # Default to even split if ratio parsing fails
            left_frac = right_frac = 0.5

        # Divide elements based on direction
        if len(self.elements) < 2:
            return

        # 根据用户指定的位置重新排序左右元素
        left_idx = 0
        right_idx = 1
        
        for i, pos_data in enumerate(self.element_positions):
            if pos_data.get("position") == "left":
                left_idx = i
            elif pos_data.get("position") == "right":
                right_idx = i
                
        left_content = self.elements[left_idx]
        right_content = self.elements[right_idx]

        if direction == "horizontal":
            # Position left and right content
            width = config.frame_width * 0.8
            left_width = width * left_frac
            right_width = width * right_frac

            left_content.move_to(LEFT * (right_width + gap) / 2)
            right_content.move_to(RIGHT * (left_width + gap) / 2)
        else:  # vertical
            # Position top and bottom content
            height = config.frame_height * 0.8
            top_height = height * left_frac
            bottom_height = height * right_frac

            left_content.move_to(UP * (bottom_height + gap) / 2)
            right_content.move_to(DOWN * (top_height + gap) / 2)

    def _apply_tabs_layout(self):
        """Create a tabbed interface layout"""
        default_tab = self.properties.get("default_tab", 0)
        tab_position = self.properties.get("tab_position", "top")
        tab_height = self.properties.get("tab_height", 0.5)

        if not self.elements:
            return

        # Create tabs
        tab_labels = []
        tab_content = self.elements.copy()

        # Generate labels if needed
        for i, content in enumerate(tab_content):
            # 检查用户是否为此标签提供了自定义属性
            tab_label = None
            if i < len(self.element_positions) and "tab_label" in self.element_positions[i]:
                tab_label = self.element_positions[i]["tab_label"]
                
            # Create a simple tab label
            tab_width = max(1, content.get_width() / 2)
            label = Rectangle(width=tab_width, height=tab_height, fill_opacity=0.1)

            # Highlight the active tab
            if i == default_tab:
                label.set_fill(BLUE_D, opacity=0.3)

            # Add label text
            tab_text = Text(tab_label if tab_label else f"Tab {i+1}", font_size=24)
            tab_text.set_width(tab_width * 0.8)
            tab_text.move_to(label)
            label.add(tab_text)

            tab_labels.append(label)

        # Arrange tab labels horizontally
        tab_group = VGroup(*tab_labels)
        tab_group.arrange(RIGHT, buff=0.1)

        # Show only active tab content
        for i, content in enumerate(tab_content):
            if i != default_tab:
                content.set_opacity(0)
            else:
                active_content = content

        # Position the elements
        if tab_position == "top":
            tab_group.to_edge(UP)
            active_content.next_to(tab_group, DOWN, buff=0.5)
        else:  # bottom
            tab_group.to_edge(DOWN)
            active_content.next_to(tab_group, UP, buff=0.5)

        # Add tabs to the layout
        for label in tab_labels:
            self.add(label)

    def _apply_sidebar_layout(self):
        """Create a sidebar layout"""
        side = self.properties.get("side", "left")
        width = self.properties.get("width", 3)
        gap = self.properties.get("gap", 0.5)

        # Convert pixel width to manim units if needed
        if isinstance(width, str):
            if width.endswith("px"):
                try:
                    pixel_width = float(width.replace("px", ""))
                    # Approximate conversion from pixels to manim units
                    width = pixel_width / 100
                except ValueError:
                    width = 3  # Default fallback

        if len(self.elements) < 2:
            return

        # 查找指定为侧边栏和主内容的元素
        sidebar_idx = 0  # 默认第一个是侧边栏
        main_content_elements = self.elements[1:]  # 默认剩余元素是主内容
        
        for i, pos_data in enumerate(self.element_positions):
            if pos_data.get("position") == "sidebar":
                sidebar_idx = i
        
        sidebar = self.elements[sidebar_idx]
        # 排除侧边栏元素
        main_content_elements = [e for i, e in enumerate(self.elements) if i != sidebar_idx]
        main_content = VGroup(*main_content_elements)

        # Arrange main content
        main_content.arrange(DOWN)

        # Create sidebar background
        sidebar_height = config.frame_height * 0.8
        sidebar_bg = Rectangle(width=width, height=sidebar_height, fill_color=GREY_D, fill_opacity=0.1, stroke_width=1)

        # Position sidebar based on side parameter
        if side == "left":
            sidebar_bg.to_edge(LEFT)
            main_content.move_to(RIGHT * (width / 2 + gap + main_content.get_width() / 2))
        else:  # right
            sidebar_bg.to_edge(RIGHT)
            main_content.move_to(LEFT * (width / 2 + gap + main_content.get_width() / 2))

        # Position sidebar content
        sidebar.move_to(sidebar_bg)

        # Add sidebar background
        self.add(sidebar_bg)
        sidebar_bg.set_z_index(-1)

    def _apply_centered_layout(self):
        """Center all elements with optional maximum width"""
        vertical = self.properties.get("vertical", True)
        horizontal = self.properties.get("horizontal", True)
        max_width = self.properties.get("max_width", 8)

        # Convert pixel max_width to manim units if needed
        if isinstance(max_width, str) and max_width.endswith("px"):
            try:
                pixel_max_width = float(max_width.replace("px", ""))
                # Approximate conversion from pixels to manim units
                max_width = pixel_max_width / 100
            except ValueError:
                max_width = 8  # Default fallback

        # Arrange elements vertically
        if vertical:
            self.arrange(DOWN)

        # Constrain width if needed
        if max_width:
            for element in self.elements:
                if element.get_width() > max_width:
                    element.set_width(max_width)

        # Center the group
        if horizontal:
            self.center_x()
        if vertical:
            self.center_y()

    def add_element(self, element, position_data=None):
        """向布局中添加新元素
        
        Args:
            element: 要添加的Manim元素
            position_data (dict, optional): 元素的位置信息，例如：
                {"position": "below", "related_to": "title", "margin": 0.5}
                
        Returns:
            Layout: 返回自身以支持链式调用
                
        示例:
            ```python
            # 添加元素到布局底部
            layout.add_element(new_text, {"position": "below"})
            
            # 添加元素到指定元素的右侧
            layout.add_element(image, {
                "position": "right", 
                "related_to": "title",
                "aligned": "center"
            })
            ```
        """
        position_data = position_data or {}
        self.elements.append(element)
        self.element_positions.append(position_data)
        self.add(element)
        self._apply_layout()
        return self

    def update_properties(self, new_properties):
        """更新布局属性并重新应用布局
        
        Args:
            new_properties (dict): 要更新的布局属性
                
        Returns:
            Layout: 返回自身以支持链式调用
                
        示例:
            ```python
            # 更新流式布局的方向
            flow_layout.update_properties({"direction": "horizontal"})
            
            # 更新网格布局的列数和间距
            grid_layout.update_properties({"columns": 4, "gap": 0.8})
            
            # 更新分割布局的比例
            split_layout.update_properties({"ratio": "1:2"})
            ```
        """
        self.properties.update(new_properties)
        self._apply_layout()
        return self

    def to_dict(self):
        """转换为字典表示形式，用于序列化
        
        Returns:
            dict: 布局的字典表示
        """
        # Extract element IDs for layout
        element_ids = []
        for e in self.elements:
            if isinstance(e, dict) and "id" in e:
                element_ids.append(e["id"])
            elif hasattr(e, "id"):
                element_ids.append(e.id)
            else:
                # For elements without ID, use object reference
                element_ids.append(str(id(e)))

        return {
            "organization_type": "layout",
            "layout_type": self.layout_type,
            "elements": element_ids,
            "properties": self.properties,
            "id": self.id,
        }
        
    def to_manim(self):
        """返回自身，因为Layout本身已经是Manim对象
        
        Returns:
            Layout: 返回自身
        """
        return self
        
    def convert_to_manim_units(self, value):
        """将像素值转换为Manim单位
        
        Args:
            value: 要转换的值，可以是数字或带有'px'后缀的字符串
            
        Returns:
            float: 转换后的Manim单位值
        """
        if isinstance(value, str) and value.endswith("px"):
            try:
                pixel_value = float(value.replace("px", ""))
                # 近似转换，根据项目需要调整此比例
                return pixel_value / 100
            except ValueError:
                pass
        return value


class ManimLayoutAdapter:
    """适配器类，将Layout转换为Manim对象"""
    
    def __init__(self, elements, layout_type, properties, layout_id):
        self.elements = elements
        self.layout_type = layout_type
        self.properties = properties
        self.id = layout_id
        # 使用更适合Manim的默认值
        self.default_buff = 0.5 
        self.small_buff = 0.25
        
    def create_layout(self):
        """基于布局类型创建相应的Manim对象
        
        Returns:
            VGroup 或 Layout: 包含所有元素的Manim对象
        """
        # 将所有元素转换为Manim对象
        manim_elements = []
        for element in self.elements:
            # 检查元素是否有效且可转换为Manim对象
            manim_obj = None
            if hasattr(element, "create_manim_object"):
                manim_obj = element.create_manim_object()
            elif hasattr(element, "to_manim"):
                manim_obj = element.to_manim()
            elif isinstance(element, Mobject): # 检查是否已经是Manim对象
                 manim_obj = element
            
            if manim_obj:
                 manim_elements.append(manim_obj)
            else:
                 # 对于无法处理的元素，可以记录日志或跳过
                 print(f"Warning: Element {element} could not be converted to a Manim object.")
        
        # 如果没有元素，返回空组
        if not manim_elements:
            return VGroup()
            
        # 创建Layout实例，它已经继承自VGroup并实现了布局逻辑
        # 直接使用当前模块的Layout类而不是从外部导入，避免循环导入
        layout = Layout(manim_elements, self.layout_type, self.properties, self.id)
        return layout


# 测试场景
class LayoutDemo(Scene):
    def construct(self):
        from visual_system.elements.text import TextElement
        
        # 创建标题
        title = TextElement("布局系统演示", {"role": "title", "align": "center"})
        title_obj = title.create_manim_object()
        title_obj.to_edge(UP, buff=1)
        self.play(title.create_animation(title_obj))
        
        # 创建不同类型的布局用于演示
        self.demonstrate_flow_layout()
        self.wait(1)
        self.clear()
        self.add(title_obj)
        
        self.demonstrate_grid_layout()
        self.wait(1)
        self.clear()
        self.add(title_obj)
        
        self.demonstrate_card_layout()
        self.wait(1)
        self.clear()
        self.add(title_obj)
        
        self.demonstrate_split_layout()
        self.wait(1)
        self.clear()
        self.add(title_obj)
        
        self.demonstrate_stack_layout()
        self.wait(1)
    
    def demonstrate_flow_layout(self):
        from visual_system.elements.text import TextElement
        
        # 创建标题
        section_title = TextElement("流式布局 (Flow Layout)", {"role": "heading"})
        section_title_obj = section_title.create_manim_object()
        section_title_obj.to_edge(UP, buff=2)
        
        # 创建流式布局元素
        items = [
            TextElement(f"项目 {i}", {"size": "medium"}) for i in range(1, 6)
        ]
        
        # 创建布局
        flow_layout = Layout([item.create_manim_object() for item in items], "flow", {"direction": "vertical", "spacing": 0.5})
        flow_layout.next_to(section_title_obj, DOWN, buff=0.5)
        
        # 显示布局
        self.play(section_title.create_animation(section_title_obj))
        self.play(FadeIn(flow_layout))  # 改为FadeIn动画，避免Create可能不适用于VGroup
        
        # 显示说明
        description = TextElement(
            "流式布局按顺序垂直或水平排列元素", 
            {"role": "caption", "size": "small"}
        )
        desc_obj = description.create_manim_object()
        desc_obj.next_to(flow_layout, DOWN, buff=0.8)
        self.play(description.create_animation(desc_obj))
    
    def demonstrate_grid_layout(self):
        section_title = Text("网格布局", font_size=40)
        section_title.to_edge(UP)
        self.play(FadeIn(section_title))

        # Create elements for grid
        items = []
        colors = [BLUE, GREEN, RED, YELLOW]
        for i in range(6):
            square = Square(side_length=1.2, fill_opacity=0.8, fill_color=colors[i % len(colors)])
            text = Text(f"项目 {i+1}", font_size=20, color=WHITE)
            text.move_to(square)
            item = VGroup(square, text)
            items.append(item)

        # Create and show grid layout
        grid_layout = Layout(items, "grid", {"columns": 3, "gap": 0.5})

        self.play(FadeIn(grid_layout))
        self.wait(1)

        # Update grid properties to show dynamic layout changes
        self.play(grid_layout.animate.update_properties({"columns": 2, "gap": 0.8}))
        self.wait(1)

        self.play(FadeOut(grid_layout), FadeOut(section_title))
    
    def demonstrate_card_layout(self):
        section_title = Text("卡片布局", font_size=40)
        section_title.to_edge(UP)
        self.play(FadeIn(section_title))

        # Create card elements
        card_title = Text("卡片标题", font_size=30)
        card_text = Text("这是卡片内容，用于展示描述性文本。", font_size=18)
        card_button = RoundedRectangle(width=2, height=0.7, corner_radius=0.2, fill_opacity=0.8, fill_color=BLUE)
        button_text = Text("按钮", font_size=18, color=WHITE).move_to(card_button)
        button_group = VGroup(card_button, button_text)

        # Create and show card layout
        card_layout = Layout(
            [card_title, card_text, button_group], "card", {"padding": 0.5, "shadow": True, "rounded": True}
        )

        self.play(FadeIn(card_layout))
        self.wait(1)

        # Show a second card with different properties
        card2_title = Text("另一个卡片", font_size=30)
        card2_text = Text("使用不同的属性设置。", font_size=18)

        card2_layout = Layout([card2_title, card2_text], "card", {"padding": 0.8, "shadow": False, "rounded": False})
        card2_layout.next_to(card_layout, RIGHT, buff=1)

        self.play(FadeIn(card2_layout))
        self.wait(1)

        self.play(FadeOut(card_layout), FadeOut(card2_layout), FadeOut(section_title))
    
    def demonstrate_split_layout(self):
        section_title = Text("分割布局", font_size=40)
        section_title.to_edge(UP)
        self.play(FadeIn(section_title))

        # Create elements for split layout
        left_content = VGroup(Text("左侧", font_size=30), Text("内容区域", font_size=20)).arrange(DOWN)

        right_content = VGroup(Text("右侧", font_size=30), Text("内容区域", font_size=20)).arrange(DOWN)

        # Create and show horizontal split layout
        split_layout = Layout([left_content, right_content], "split", {"ratio": "1:1", "direction": "horizontal"})

        self.play(FadeIn(split_layout))
        self.wait(1)

        # Update to a different ratio
        self.play(split_layout.animate.update_properties({"ratio": "2:1"}))
        self.wait(1)

        # Change to vertical split
        self.play(split_layout.animate.update_properties({"direction": "vertical"}))
        self.wait(1)

        self.play(FadeOut(split_layout), FadeOut(section_title))
    
    def demonstrate_stack_layout(self):
        from visual_system.elements.text import TextElement
        
        # 创建标题
        section_title = TextElement("堆叠布局 (Stack Layout)", {"role": "heading"})
        section_title_obj = section_title.create_manim_object()
        section_title_obj.to_edge(UP, buff=2)
        
        # 创建堆叠元素
        items = [
            TextElement(f"堆叠层 {i}", {"style": "highlight"}) 
            for i in range(1, 4)
        ]
        
        # 创建布局
        stack_layout = Layout([item.create_manim_object() for item in items], "stack", {"align": "center", "overlap": "partial"})
        stack_layout.next_to(section_title_obj, DOWN, buff=0.5)
        
        # 显示布局
        self.play(section_title.create_animation(section_title_obj))
        self.play(FadeIn(stack_layout))  # 改为FadeIn动画
        
        # 显示说明
        description = TextElement(
            "堆叠布局将元素相互重叠放置", 
            {"role": "caption", "size": "small"}
        )
        desc_obj = description.create_manim_object()
        desc_obj.next_to(stack_layout, DOWN, buff=0.8)
        self.play(description.create_animation(desc_obj))


class LayoutDemonstration(Scene):
    """
    This scene demonstrates the Layout class with various layout types.
    Run with: manim -pql layout_test.py LayoutDemonstration
    """

    def construct(self):
        # Demonstration title
        title = Text("布局演示", font_size=48)
        self.play(Write(title))
        self.wait(1)
        self.play(FadeOut(title))

        # Flow Layout
        self.demonstrate_flow_layout()

        # Grid Layout
        self.demonstrate_grid_layout()

        # Card Layout
        self.demonstrate_card_layout()

        # Split Layout
        self.demonstrate_split_layout()

        # Sidebar Layout
        self.demonstrate_sidebar_layout()

        # Final message
        final_message = Text("布局系统演示完成", font_size=36)
        self.play(FadeIn(final_message))
        self.wait(1)

    def demonstrate_flow_layout(self):
        section_title = Text("流式布局", font_size=40)
        section_title.to_edge(UP)
        self.play(FadeIn(section_title))

        # Create elements for flow layout
        text1 = Text("这是一个流式布局", font_size=24)
        text2 = Text("元素按顺序排列", font_size=24)
        text3 = Text("一个接一个", font_size=24)

        # Create and show vertical flow layout
        vertical_flow = Layout(
            [text1.copy(), text2.copy(), text3.copy()], "flow", {"direction": "vertical", "spacing": 0.5}
        )
        vertical_flow.to_edge(LEFT).shift(RIGHT * 2)

        # Create and show horizontal flow layout
        horizontal_flow = Layout(
            [text1.copy(), text2.copy(), text3.copy()], "flow", {"direction": "horizontal", "spacing": 0.5}
        )
        horizontal_flow.to_edge(RIGHT).shift(LEFT * 2 + DOWN * 2)

        self.play(FadeIn(vertical_flow))
        self.play(FadeIn(horizontal_flow))
        self.wait(1)

        self.play(FadeOut(vertical_flow), FadeOut(horizontal_flow), FadeOut(section_title))

    def demonstrate_grid_layout(self):
        section_title = Text("网格布局", font_size=40)
        section_title.to_edge(UP)
        self.play(FadeIn(section_title))

        # Create elements for grid
        items = []
        colors = [BLUE, GREEN, RED, YELLOW]
        for i in range(6):
            square = Square(side_length=1.2, fill_opacity=0.8, fill_color=colors[i % len(colors)])
            text = Text(f"项目 {i+1}", font_size=20, color=WHITE)
            text.move_to(square)
            item = VGroup(square, text)
            items.append(item)

        # Create and show grid layout
        grid_layout = Layout(items, "grid", {"columns": 3, "gap": 0.5})

        self.play(FadeIn(grid_layout))
        self.wait(1)

        # Update grid properties to show dynamic layout changes
        self.play(grid_layout.animate.update_properties({"columns": 2, "gap": 0.8}))
        self.wait(1)

        self.play(FadeOut(grid_layout), FadeOut(section_title))

    def demonstrate_card_layout(self):
        section_title = Text("卡片布局", font_size=40)
        section_title.to_edge(UP)
        self.play(FadeIn(section_title))

        # Create card elements
        card_title = Text("卡片标题", font_size=30)
        card_text = Text("这是卡片内容，用于展示描述性文本。", font_size=18)
        card_button = RoundedRectangle(width=2, height=0.7, corner_radius=0.2, fill_opacity=0.8, fill_color=BLUE)
        button_text = Text("按钮", font_size=18, color=WHITE).move_to(card_button)
        button_group = VGroup(card_button, button_text)

        # Create and show card layout
        card_layout = Layout(
            [card_title, card_text, button_group], "card", {"padding": 0.5, "shadow": True, "rounded": True}
        )

        self.play(FadeIn(card_layout))
        self.wait(1)

        # Show a second card with different properties
        card2_title = Text("另一个卡片", font_size=30)
        card2_text = Text("使用不同的属性设置。", font_size=18)

        card2_layout = Layout([card2_title, card2_text], "card", {"padding": 0.8, "shadow": False, "rounded": False})
        card2_layout.next_to(card_layout, RIGHT, buff=1)

        self.play(FadeIn(card2_layout))
        self.wait(1)

        self.play(FadeOut(card_layout), FadeOut(card2_layout), FadeOut(section_title))

    def demonstrate_split_layout(self):
        section_title = Text("分割布局", font_size=40)
        section_title.to_edge(UP)
        self.play(FadeIn(section_title))

        # Create elements for split layout
        left_content = VGroup(Text("左侧", font_size=30), Text("内容区域", font_size=20)).arrange(DOWN)

        right_content = VGroup(Text("右侧", font_size=30), Text("内容区域", font_size=20)).arrange(DOWN)

        # Create and show horizontal split layout
        split_layout = Layout([left_content, right_content], "split", {"ratio": "1:1", "direction": "horizontal"})

        self.play(FadeIn(split_layout))
        self.wait(1)

        # Update to a different ratio
        self.play(split_layout.animate.update_properties({"ratio": "2:1"}))
        self.wait(1)

        # Change to vertical split
        self.play(split_layout.animate.update_properties({"direction": "vertical"}))
        self.wait(1)

        self.play(FadeOut(split_layout), FadeOut(section_title))

    def demonstrate_sidebar_layout(self):
        section_title = Text("侧边栏布局", font_size=40)
        section_title.to_edge(UP)
        self.play(FadeIn(section_title))

        # Create sidebar items
        sidebar_items = VGroup(
            Text("菜单项 1", font_size=20), Text("菜单项 2", font_size=20), Text("菜单项 3", font_size=20)
        ).arrange(DOWN, buff=0.5)

        # Create main content
        main_content = VGroup(
            Text("主要内容区域", font_size=36),
            Text("这是主内容区域", font_size=24),
            Text("包含多个元素", font_size=24),
        ).arrange(DOWN)

        # Create and show sidebar layout
        sidebar_layout = Layout([sidebar_items, main_content], "sidebar", {"side": "left", "width": 3})

        self.play(FadeIn(sidebar_layout))
        self.wait(1)

        # Change to right sidebar
        self.play(sidebar_layout.animate.update_properties({"side": "right"}))
        self.wait(1)

        self.play(FadeOut(sidebar_layout), FadeOut(section_title))


# 额外的示例展示各种布局
class FlowLayoutExample(Scene):
    def construct(self):
        title = Text("流式布局示例", font_size=36)
        title.to_edge(UP)
        self.play(Write(title))

        # 创建元素
        elements = [
            Square(side_length=1, fill_opacity=0.8, fill_color=BLUE),
            Circle(radius=0.5, fill_opacity=0.8, fill_color=RED),
            Triangle(fill_opacity=0.8, fill_color=GREEN),
            Rectangle(width=1.5, height=0.8, fill_opacity=0.8, fill_color=YELLOW),
        ]

        # 为每个形状添加标签
        for i, element in enumerate(elements):
            label = Text(f"项目 {i+1}", font_size=16)
            label.move_to(element)
            element.add(label)

        # 创建流式布局
        flow_layout = Layout(elements, "flow", {"direction": "horizontal", "spacing": 0.8})
        flow_layout.next_to(title, DOWN, buff=1)

        self.play(FadeIn(flow_layout))
        self.wait(1)

        # 改变方向为垂直
        self.play(flow_layout.animate.update_properties({"direction": "vertical"}))
        self.wait(1)


class StackLayoutExample(Scene):
    def construct(self):
        title = Text("堆叠布局示例", font_size=36)
        title.to_edge(UP)
        self.play(Write(title))

        # 创建元素（卡片）
        elements = []
        colors = [BLUE_E, RED_E, GREEN_E, PURPLE_E]
        for i in range(4):
            card = RoundedRectangle(
                width=4,
                height=2.5,
                corner_radius=0.2,
                fill_opacity=0.9,
                fill_color=colors[i],
                stroke_width=2,
                stroke_color=WHITE,
            )
            label = Text(f"卡片 {i+1}", font_size=24, color=WHITE)
            label.move_to(card)
            card_group = VGroup(card, label)
            elements.append(card_group)

        # 创建堆叠布局
        stack_layout = Layout(elements, "stack", {"overlap": "partial"})
        stack_layout.next_to(title, DOWN, buff=1)

        self.play(FadeIn(stack_layout))
        self.wait(2)


# 新增演示位置指定和自动缩放的示例
class SplitLayoutWithPositionExample(Scene):
    def construct(self):
        title = Text("分割布局示例 - 带位置指定", font_size=36)
        title.to_edge(UP)
        self.play(Write(title))

        # 创建左侧内容 - 一个大图表
        left_title = Text("左侧内容", font_size=30)
        large_chart = Rectangle(width=6, height=4, fill_opacity=0.8, fill_color=BLUE_E)
        left_content = VGroup(left_title, large_chart).arrange(DOWN, buff=0.5)
        
        # 创建右侧内容 - 文本列表
        right_title = Text("右侧内容", font_size=30)
        right_items = VGroup(
            *[Text(f"• 项目 {i+1}", font_size=24) for i in range(5)]
        ).arrange(DOWN, buff=0.3, aligned_edge=LEFT)
        right_content = VGroup(right_title, right_items).arrange(DOWN, buff=0.5)
        
        # 创建带位置指定的分割布局
        split_layout = Layout(
            [(left_content, {"position": "left"}), 
             (right_content, {"position": "right"})], 
            "split", 
            {"ratio": "3:2", "direction": "horizontal", "gap": 0.5},
            auto_scale=True  # 启用自动缩放
        )
        
        split_layout.next_to(title, DOWN, buff=0.8)
        self.play(FadeIn(split_layout))
        self.wait(2)
        
        # 演示修改比例
        self.play(split_layout.animate.update_properties({"ratio": "1:2"}))
        self.wait(1)
        
        # 添加说明
        note = Text("注意：布局会自动缩放以适应画布", font_size=20, color=YELLOW)
        note.to_edge(DOWN, buff=0.5)
        self.play(FadeIn(note))
        self.wait(2)


# 新增演示网格布局与位置指定的示例
class GridLayoutWithPositionExample(Scene):
    def construct(self):
        title = Text("网格布局示例 - 自动调整大小", font_size=36)
        title.to_edge(UP)
        self.play(Write(title))
        
        # 创建大小不一的元素
        elements = []
        colors = [BLUE_E, RED_E, GREEN_E, YELLOW, PURPLE_E, BLUE_D]
        sizes = [(1.5, 1.5), (2, 1), (1, 2), (2.5, 1.5), (1.2, 1.2), (1.8, 1.8)]
        
        for i, (size, color) in enumerate(zip(sizes, colors)):
            width, height = size
            rect = Rectangle(
                width=width, 
                height=height, 
                fill_opacity=0.8, 
                fill_color=color,
                stroke_width=2
            )
            label = Text(f"元素 {i+1}", font_size=20, color=WHITE)
            label.move_to(rect)
            item = VGroup(rect, label)
            
            # 为部分元素指定位置（网格区域）
            if i == 0:
                # 第一个元素没有指定位置信息
                elements.append(item)
            else:
                # 基于元素大小，为部分元素设置特定位置提示
                position_data = {}
                if width > 2 or height > 2:
                    position_data = {"area": f"{i},{i},{i+1},{i+1}"}  # 简单位置标记
                elements.append((item, position_data))
        
        # 创建网格布局，包含自动缩放功能
        grid_layout = Layout(
            elements, 
            "grid", 
            {"columns": 3, "gap": 0.5},
            auto_scale=True
        )
        
        grid_layout.next_to(title, DOWN, buff=0.8)
        self.play(FadeIn(grid_layout))
        self.wait(2)
        
        # 修改列数，观察自动调整
        self.play(grid_layout.animate.update_properties({"columns": 2, "gap": 0.8}))
        self.wait(1)
        
        # 添加元素，观察布局变化
        new_element = Circle(radius=1.2, fill_opacity=0.8, fill_color=GREEN)
        new_label = Text("新元素", font_size=20, color=WHITE)
        new_label.move_to(new_element)
        new_item = VGroup(new_element, new_label)
        
        # 添加新元素
        grid_layout.add_element(new_item, {"position": "right"})
        self.play(FadeIn(new_item))
        self.wait(2)
        
        # 添加说明
        explanation = Text("布局会自动调整以适应元素大小和数量", font_size=20, color=YELLOW)
        explanation.to_edge(DOWN, buff=0.5)
        self.play(FadeIn(explanation))
        self.wait(2)


# 添加一个新的示例类，展示增强的相对位置功能
class EnhancedRelativePositionExample(Scene):
    """
    演示Layout类的增强相对位置功能
    
    这个示例展示了如何使用Layout的相对位置功能来创建复杂而精确的布局，
    包括通过名称引用元素、使用不同的位置表达方式以及调整对齐方式等。
    """
    
    def construct(self):
        """构建场景并演示相对位置布局功能"""
        # 创建标题
        title = Text("增强的相对位置布局演示", font_size=36)
        title.to_edge(UP)
        self.play(Write(title))
        
        # Step 1: 创建一个中心元素作为参考点
        self.add_caption("1. 创建一个中心元素作为参考点")
        center_box = Square(side_length=2, fill_opacity=0.8, fill_color=BLUE_E)
        center_text = Text("中心元素", font_size=24)
        center_text.move_to(center_box)
        center_element = VGroup(center_box, center_text)
        
        # Step 2: 创建布局并添加元素
        self.add_caption("2. 创建布局并定位其他元素", DOWN * 3)
        
        # 创建相对布局的元素列表，每个元素都包含位置信息
        elements = [
            # 中心元素，名称为"center"，便于其他元素引用
            (center_element, {"name": "center", "position": "start"}),
            
            # 左侧元素，相对于中心元素
            (Text("左侧元素", font_size=24), 
                {"related_to": "center", "position": "left", "margin": 1}),
            
            # 右侧元素，相对于中心元素
            (Text("右侧元素", font_size=24), 
                {"related_to": "center", "position": "right", "margin": 1}),
            
            # 上方元素，相对于中心元素
            (Text("上方元素", font_size=24), 
                {"related_to": "center", "position": "above", "margin": 1}),
            
            # 下方元素，相对于中心元素
            (Text("下方元素", font_size=24), 
                {"related_to": "center", "position": "below", "margin": 1}),
        ]
        
        # 创建相对布局
        basic_layout = Layout(elements, "relative", auto_scale=True)
        
        self.play(FadeIn(basic_layout))
        self.wait(2)
        
        # Step 3: 展示高级位置功能 - 添加更多元素以展示高级引用
        self.add_caption("3. 展示高级位置功能", UP * 3)
        
        # 创建更复杂的布局元素列表
        advanced_elements = elements.copy() + [
            # 圆形，位于"左侧元素"的左边，居中对齐
            (Circle(radius=0.8, fill_opacity=0.7, fill_color=GREEN), 
                {"related_to": "左侧元素", "position": "left", "margin": 0.5, "aligned": "center"}),
                
            # 三角形，位于"右侧元素"的右边，居中对齐
            (Triangle(fill_opacity=0.7, fill_color=RED), 
                {"related_to": "右侧元素", "position": "right", "margin": 0.5, "aligned": "center"}),
                
            # 自由位置元素，使用shift进行精确定位
            (Text("自由位置", font_size=18), 
                {"related_to": "center", "position": "above", "shift": [1.5, 1], "margin": 0.3}),
                
            # 对齐示例，与"下方元素"左对齐
            (Text("左对齐示例", font_size=18, color=YELLOW), 
                {"related_to": "下方元素", "position": "below", "margin": 0.3, "aligned": "left"}),
                
            # 通过元素内容引用
            (Rectangle(width=1.2, height=0.6, fill_opacity=0.7, fill_color=BLUE_D),
                {"related_to": "上方元素", "position": "above", "margin": 0.3})
        ]
        
        # 创建高级相对布局
        advanced_layout = Layout(advanced_elements, "relative", auto_scale=True)
        
        # 用高级布局替换基本布局
        self.play(FadeOut(basic_layout))
        self.play(FadeIn(advanced_layout))
        self.wait(2)
        
        # Step 4: 添加动态元素，展示动态调整布局
        self.add_caption("4. 动态添加新元素并调整布局", LEFT * 4 + DOWN * 1.5)
        
        new_element = Text("新添加的元素", font_size=18, color=GREEN)
        self.play(FadeIn(new_element))
        
        # 动态添加元素并指定位置
        advanced_layout.add_element(new_element, {
            "related_to": "右侧元素", 
            "position": "below", 
            "margin": 0.3, 
            "aligned": "right"
        })
        
        self.wait(1)
        
        # 添加说明
        explanation = VGroup(
            Text("✓ 可以通过元素名称引用其他元素", font_size=18),
            Text("✓ 支持更丰富的位置描述（上/下/左/右）", font_size=18),
            Text("✓ 可以指定对齐方式（居中/左/右对齐）", font_size=18),
            Text("✓ 可以设置精确位置偏移", font_size=18),
            Text("✓ 支持动态添加元素", font_size=18)
        ).arrange(DOWN, aligned_edge=LEFT, buff=0.3)
        explanation.to_edge(DOWN, buff=0.5)
        self.play(FadeIn(explanation))
        self.wait(2)
    
    def add_caption(self, text, position=DOWN*2):
        """添加说明文字并显示"""
        caption = Text(text, font_size=24, color=YELLOW)
        caption.shift(position)
        self.play(FadeIn(caption))
        self.wait(1)
        self.play(FadeOut(caption))


class LayoutUsageGuide(Scene):
    """
    Layout类的使用指南示例
    
    这个场景演示了Layout类的基本使用方法，
    包括不同布局类型的创建方式和常见操作。
    """
    
    def construct(self):
        """构建场景并展示Layout类的基本用法"""
        # 创建标题
        title = Text("布局系统使用指南", font_size=40)
        title.to_edge(UP, buff=0.5)
        self.play(Write(title))
        
        # 1. 展示基本布局类型
        self.show_layout_types()
        self.wait(1)
        self.clear()
        self.add(title)
        
        # 2. 展示相对位置功能
        self.show_relative_positioning()
        self.wait(1)
        self.clear()
        self.add(title)
        
        # 3. 展示自动缩放和动态调整
        self.show_auto_scaling()
        self.wait(1)
        
        # 最终提示
        conclusion = Text("更多布局示例请参考各专项布局类", font_size=24)
        conclusion.to_edge(DOWN)
        self.play(FadeIn(conclusion))
        self.wait(2)
    
    def show_layout_types(self):
        """展示几种基本布局类型"""
        subtitle = Text("1. 基本布局类型", font_size=32)
        subtitle.next_to(self.mobjects[0], DOWN, buff=0.5)
        self.play(FadeIn(subtitle))
        
        # 创建示例元素
        elements = []
        for i in range(4):
            square = Square(side_length=0.7, fill_opacity=0.8, 
                           fill_color=[BLUE_E, RED_E, GREEN_E, YELLOW][i])
            label = Text(f"{i+1}", font_size=20)
            label.move_to(square)
            elements.append(VGroup(square, label))
        
        # 展示代码和布局
        code_text = """
# 流式布局(Flow) - 水平或垂直排列元素
flow = Layout(elements, "flow", {"direction": "horizontal"})

# 网格布局(Grid) - 将元素排列成网格
grid = Layout(elements, "grid", {"columns": 2})

# 分割布局(Split) - 分为左右或上下两部分
split = Layout(elements[:2], "split", {"ratio": "1:1"})

# 卡片布局(Card) - 为元素添加卡片式容器
card = Layout([text, button], "card", {"rounded": True})
"""
        
        code = Text(code_text, font_size=18, font="Monospace")
        code.next_to(subtitle, DOWN, buff=0.5)
        code.align_to(subtitle, LEFT).shift(RIGHT * 0.5)
        self.play(Create(code))
        
        # 创建流式布局
        flow_layout = Layout(elements, "flow", {"direction": "horizontal", "spacing": 0.5})
        flow_layout.next_to(code, DOWN, buff=0.5)
        flow_layout.align_to(code, LEFT).shift(RIGHT * 0.5)
        
        flow_label = Text("Flow Layout 示例", font_size=20)
        flow_label.next_to(flow_layout, LEFT)
        
        self.play(FadeIn(flow_label), FadeIn(flow_layout))
        self.wait(1)
        
        # 切换到网格布局
        grid_layout = Layout(elements, "grid", {"columns": 2, "gap": 0.5})
        grid_layout.next_to(code, DOWN, buff=0.5)
        grid_layout.align_to(code, LEFT).shift(RIGHT * 0.5)
        
        grid_label = Text("Grid Layout 示例", font_size=20)
        grid_label.next_to(grid_layout, LEFT)
        
        self.play(FadeOut(flow_layout), FadeOut(flow_label))
        self.play(FadeIn(grid_label), FadeIn(grid_layout))
        self.wait(1)
    
    def show_relative_positioning(self):
        """展示相对位置功能"""
        subtitle = Text("2. 相对位置布局", font_size=32)
        subtitle.next_to(self.mobjects[0], DOWN, buff=0.5)
        self.play(FadeIn(subtitle))
        
        # 示例代码
        code_text = """
# 通过名称引用其他元素
elements = [
    (title, {"name": "title", "position": "start"}),
    (image, {"related_to": "title", "position": "below"}),
    (text, {"related_to": "title", "position": "right"})
]

# 创建相对位置布局
layout = Layout(elements, "relative")

# 动态添加元素
layout.add_element(button, {"related_to": "image", 
                            "position": "below",
                            "aligned": "center"})
"""
        
        code = Text(code_text, font_size=18, font="Monospace")
        code.next_to(subtitle, DOWN, buff=0.5)
        self.play(Create(code))
        
        # 创建实际的相对位置布局示例
        title_box = Rectangle(width=3, height=0.8, fill_opacity=0.8, fill_color=BLUE)
        title_text = Text("标题文本", font_size=24)
        title_text.move_to(title_box)
        title_elem = VGroup(title_box, title_text)
        
        image_box = Square(side_length=1.5, fill_opacity=0.8, fill_color=GREEN)
        image_text = Text("图片", font_size=20)
        image_text.move_to(image_box)
        image_elem = VGroup(image_box, image_text)
        
        text_box = Rectangle(width=2, height=1.5, fill_opacity=0.8, fill_color=YELLOW)
        text_content = Text("文本内容", font_size=20)
        text_content.move_to(text_box)
        text_elem = VGroup(text_box, text_content)
        
        # 创建相对布局
        elements = [
            (title_elem, {"name": "title", "position": "start"}),
            (image_elem, {"related_to": "title", "position": "below", "margin": 0.5}),
            (text_elem, {"related_to": "title", "position": "right", "margin": 1, "aligned": "top"})
        ]
        
        relative_layout = Layout(elements, "relative")
        relative_layout.next_to(code, DOWN, buff=1)
        
        self.play(FadeIn(relative_layout))
        self.wait(1)
        
        # 动态添加按钮元素
        button = RoundedRectangle(width=1.5, height=0.6, corner_radius=0.2, 
                                fill_opacity=0.8, fill_color=RED)
        button_text = Text("按钮", font_size=20)
        button_text.move_to(button)
        button_elem = VGroup(button, button_text)
        
        self.play(FadeIn(button_elem))
        
        # 添加到布局
        relative_layout.add_element(button_elem, {
            "related_to": "图片",  # 可以通过Text内容引用
            "position": "below", 
            "margin": 0.5, 
            "aligned": "center"
        })
        
        self.wait(1)
    
    def show_auto_scaling(self):
        """展示自动缩放和布局调整功能"""
        subtitle = Text("3. 自动缩放与布局调整", font_size=32)
        subtitle.next_to(self.mobjects[0], DOWN, buff=0.5)
        self.play(FadeIn(subtitle))
        
        # 示例代码
        code_text = """
# 启用自动缩放功能
layout = Layout(elements, "grid", {"columns": 3}, auto_scale=True)

# 调整布局属性
layout.update_properties({"columns": 2, "gap": 0.8})
"""
        
        code = Text(code_text, font_size=18, font="Monospace")
        code.next_to(subtitle, DOWN, buff=0.5)
        self.play(Create(code))
        
        # 创建一系列大小不同的元素
        elements = []
        sizes = [(1, 1), (1.5, 0.8), (0.8, 1.2), (1.2, 1.2), (1.5, 1.5), (1, 1)]
        colors = [BLUE_E, RED_E, GREEN_E, YELLOW, PURPLE_E, BLUE_D]
        
        for i, (size, color) in enumerate(zip(sizes, colors)):
            width, height = size
            rect = Rectangle(
                width=width, 
                height=height, 
                fill_opacity=0.8, 
                fill_color=color,
                stroke_width=2
            )
            label = Text(f"元素 {i+1}", font_size=16)
            label.move_to(rect)
            elements.append(VGroup(rect, label))
        
        # 创建网格布局并展示自动缩放
        note = Text("注意：当元素过大时，布局会自动缩放以适应画布", font_size=18)
        note.next_to(code, DOWN, buff=0.5)
        self.play(FadeIn(note))
        
        # 创建初始网格布局
        grid_layout = Layout(elements, "grid", {"columns": 3, "gap": 0.5}, auto_scale=True)
        grid_layout.next_to(note, DOWN, buff=0.5)
        
        self.play(FadeIn(grid_layout))
        self.wait(1)
        
        # 改变列数，观察自动调整
        self.play(grid_layout.animate.update_properties({"columns": 2, "gap": 0.8}))
        self.wait(1)


if __name__ == "__main__":
    # 运行测试场景
    config.preview = True
    # 根据需要选择要运行的示例场景
    # scene = LayoutDemo()  # 综合布局演示
    # scene = SplitLayoutWithPositionExample()  # 分割布局示例
    # scene = GridLayoutWithPositionExample()  # 网格布局示例
    scene = EnhancedRelativePositionExample()  # 增强相对位置示例
    # scene = LayoutUsageGuide()  # 布局使用指南
    scene.render()