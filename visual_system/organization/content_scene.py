from visual_system.utils.helpers import generate_id


class ContentScene:
    """
    Create a content scene that combines elements and organizations into a cohesive presentation

    Args:
        elements (list): Elements to include in the scene
        organizations (list): Organizations that structure the elements
        properties (dict, optional): Scene properties
        id (str, optional): Scene unique identifier, auto-generated if not provided

    Properties:
        purpose (str): Scene purpose - "educational", "presentation", "storytelling", 
                      "data", "interactive", "promotional"
        theme (str): Visual theme - "light", "dark", "colorful", "minimal", "corporate", etc.
        title (str): Scene title
        description (str): Scene description
        aspect_ratio (str): Scene aspect ratio - "16:9", "4:3", "1:1", "9:16", etc.
        duration (float): Target duration in seconds
        transitions (dict): Scene transition settings

    Examples:
        # Create a simple scene with a layout
        scene = ContentScene([title, image, text], [main_layout])

        # Create a complex scene with multiple organizations
        scene = ContentScene(
            [title, chart1, chart2, table, image, text],
            [header_layout, main_layout, animation_sequence, content_pattern],
            {
                "purpose": "data",
                "theme": "corporate",
                "title": "Quarterly Results",
                "duration": 120
            }
        )
    """
    def __init__(self, elements, organizations, properties=None, id=None):
        self.elements = elements
        self.organizations = organizations
        self.properties = properties or {}
        self.id = id or generate_id()
        self._apply_defaults()
        
    def _apply_defaults(self):
        """应用默认属性"""
        # 根据目的设置默认属性
        if "purpose" in self.properties:
            purpose = self.properties["purpose"]
            
            if purpose == "educational":
                self.properties.setdefault("theme", "light")
                self.properties.setdefault("pace", "moderate")
                
            elif purpose == "presentation":
                self.properties.setdefault("theme", "corporate")
                self.properties.setdefault("aspect_ratio", "16:9")
                
            elif purpose == "storytelling":
                self.properties.setdefault("theme", "dynamic")
                self.properties.setdefault("pace", "narrative")
                
            elif purpose == "data":
                self.properties.setdefault("theme", "analytic")
                self.properties.setdefault("focus", "clarity")
                
            elif purpose == "interactive":
                self.properties.setdefault("theme", "engaging")
                self.properties.setdefault("response_time", "immediate")
                
            elif purpose == "promotional":
                self.properties.setdefault("theme", "brand")
                self.properties.setdefault("pace", "energetic")
                
        # 设置其他默认值
        self.properties.setdefault("aspect_ratio", "16:9")
        self.properties.setdefault("theme", "light")
        
    def to_dict(self):
        """转换为字典表示"""
        # 提取元素ID
        element_ids = []
        for e in self.elements:
            if isinstance(e, dict) and "id" in e:
                element_ids.append(e["id"])
            elif hasattr(e, 'id'):
                element_ids.append(e.id)
            else:
                element_ids.append(e)
                
        # 提取组织ID
        organization_ids = []
        for o in self.organizations:
            if isinstance(o, dict) and "id" in o:
                organization_ids.append(o["id"])
            elif hasattr(o, 'id'):
                organization_ids.append(o.id)
            else:
                organization_ids.append(o)
                
        return {
            "content_type": "scene",
            "elements": element_ids,
            "organizations": organization_ids,
            "properties": self.properties,
            "id": self.id,
        }
        
    def add_element(self, element):
        """添加元素到场景"""
        self.elements.append(element)
        return self
        
    def add_organization(self, organization):
        """添加组织到场景"""
        self.organizations.append(organization)
        return self
        
    def update_properties(self, new_properties):
        """更新场景属性"""
        self.properties.update(new_properties)
        return self
