from visual_system.utils.helpers import generate_id


class CompositeAnimation:
    """
    Create a composite animation that coordinates multiple element animations

    Args:
        elements (list): Elements to animate
        animation_type (str): Overall animation type -
            "sequence" - Sequential animation of elements
            "parallel" - Parallel animation of elements
            "staggered" - Staggered animation with delay between elements
            "cascade" - Cascade effect animation
            "build_up" - Progressive build-up animation
            "spotlight" - Spotlight focus animation
            "wave" - Wave-like animation effect
            "ping_pong" - Back and forth animation
            "coordinated" - Coordinated movement animation
            "convergence" - Elements converging to a point
            "divergence" - Elements diverging from a point
        properties (dict, optional): Animation properties specific to the animation type
        id (str, optional): Animation unique identifier, auto-generated if not provided

    Examples:
        # Create a simple sequence animation
        sequence = CompositeAnimation([title, image, text], "sequence")

        # Create a staggered animation with specific properties
        staggered = CompositeAnimation([item1, item2, item3, item4], "staggered", {
            "delay": 0.2,
            "direction": "left-to-right",
            "easing": "ease-out"
        })

        # Create a coordinated animation
        coordinated = CompositeAnimation([chart, legend, insights], "coordinated", {
            "duration": 1.5,
            "sync_point": "middle"
        })
    """
    def __init__(self, elements, animation_type, properties=None, id=None):
        self.elements = elements
        self.animation_type = animation_type
        self.properties = properties or {}
        self.id = id or generate_id()
        self._apply_animation_defaults()
        
    def _apply_animation_defaults(self):
        """应用动画默认属性"""
        # 动画类型默认设置
        animation_defaults = {
            "sequence": {
                "delay": 0.3,
                "order": "forward",
                "overlap": 0.1,
            },
            "parallel": {
                "duration": 1.0,
                "sync": True,
                "easing": "ease-in-out",
            },
            "staggered": {
                "delay": 0.15,
                "direction": "forward",
                "duration": 0.8,
            },
            "cascade": {
                "delay": 0.08,
                "direction": "top-to-bottom",
                "acceleration": "constant",
            },
            "build_up": {
                "pace": "moderate",
                "base_duration": 0.5,
                "final_pause": 0.5,
            },
            "spotlight": {
                "focus_scale": 1.1,
                "dim_opacity": 0.3,
                "transition": 0.4,
            },
            "wave": {
                "frequency": 0.2,
                "amplitude": 0.8,
                "direction": "horizontal",
            },
            "ping_pong": {
                "iterations": 2,
                "pause_between": 0.2,
                "easing": "ease-in-out",
            },
            "coordinated": {
                "sync_points": ["start", "middle", "end"],
                "path_type": "natural",
                "timing_function": "ease",
            },
            "convergence": {
                "target": "center",
                "final_scale": 0.9,
                "stagger": 0.05,
            },
            "divergence": {
                "origin": "center",
                "start_scale": 0.9,
                "stagger": 0.05,
            },
        }

        # 应用对应动画类型的默认设置
        if self.animation_type in animation_defaults:
            for key, value in animation_defaults[self.animation_type].items():
                self.properties.setdefault(key, value)
                
        # 设置共通属性
        self.properties.setdefault("duration", 1.0)
        self.properties.setdefault("easing", "ease")
        
    def to_dict(self):
        """转换为字典表示"""
        # 提取元素ID
        element_ids = []
        for e in self.elements:
            if isinstance(e, dict) and "id" in e:
                element_ids.append(e["id"])
            elif hasattr(e, 'id'):
                element_ids.append(e.id)
            else:
                element_ids.append(e)
                
        return {
            "organization_type": "composite_animation",
            "animation_type": self.animation_type,
            "elements": element_ids,
            "properties": self.properties,
            "id": self.id,
        }
        
    def add_element(self, element):
        """添加元素到组合动画"""
        self.elements.append(element)
        return self
        
    def update_properties(self, new_properties):
        """更新动画属性"""
        self.properties.update(new_properties)
        return self
