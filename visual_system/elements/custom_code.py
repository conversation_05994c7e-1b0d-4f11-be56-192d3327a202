from manim import (
    BLACK,
    DOWN,
    LEFT,
    UP,
    WHITE,
    YELLOW,
    AnimationGroup,
    Code,
    Create,
    FadeIn,
    FadeOut,
    Indicate,
    Rectangle,
    Scene,
    Succession,
    Text,
    VGroup,
    Wait,
    Write,
    config,
)

from visual_system.elements.base import BaseElement


class CodeElement(BaseElement):
    """
    Create a code element with intelligent defaults

    Args:
        code (str): Code content
        properties (dict, optional): Code properties with these common keys:
            language (str): Programming language
            is_code (bool): Whether content is code (vs pseudocode)
            line_numbers (bool): Whether to show line numbers
            highlight_lines (list): Line numbers to highlight
            highlight_ranges (list): Line ranges to highlight
            title (str): Code block title/filename
            theme (str): Color theme - "light", "dark", "material", etc.
        animation (dict, optional): Code animation settings
        id (str, optional): Element unique identifier, auto-generated if not provided

    Returns:
        dict: Configured code element

    Examples:
        # Create a simple Python code block
        python_code = CodeElement('''
        def hello_world():
            print("Hello, world!")
        ''', {"language": "python"})

        # Create a code block with line highlighting
        highlighted_code = CodeElement('''
        function calculate(a, b) {
            let result = a + b;
            console.log(result);
            return result;
        }
        ''', {
            "language": "javascript",
            "highlight_lines": [2],
            "line_numbers": True,
            "title": "calculate.js"
        })
    """

    # 创建代码元素，提供智能默认值
    #
    # 参数:
    #   code (str): 代码内容
    #   properties (dict, 可选): 代码属性，常用键值：
    #     language (str): 编程语言
    #     is_code (bool): 是否为代码(相对于伪代码)
    #     line_numbers (bool): 是否显示行号
    #     highlight_lines (list): 要高亮的行号
    #     highlight_ranges (list): 要高亮的行范围
    #     title (str): 代码块标题/文件名
    #     theme (str): 颜色主题 - "light"(亮色), "dark"(暗色), "material"(质感), 等
    #   animation (dict, 可选): 代码动效设置
    #   id (str, 可选): 元素唯一标识符，不提供则自动生成

    def __init__(self, code, properties=None, animation=None, id=None):
        props = properties or {}

        # 推断语言
        if props.get("language") is None:
            # 简单语言推断，实际应用中可能需要更复杂的逻辑
            code_lower = code.lower()
            if "def " in code_lower and ":" in code_lower:
                props["language"] = "python"
            elif "function" in code_lower and "{" in code_lower:
                props["language"] = "javascript"
            elif "class" in code_lower and "{" in code_lower:
                props["language"] = "java"
            elif "#include" in code_lower:
                props["language"] = "c"
            elif "<html" in code_lower:
                props["language"] = "html"
            else:
                props["language"] = "plaintext"

        # 设置合理的默认值
        props.setdefault("is_code", True)
        props.setdefault("line_numbers", True)  # 默认显示行号
        props.setdefault("theme", "dark")  # 默认使用暗色主题

        # 应用默认动效
        anim = animation
        if anim is None:
            anim = {"type": "typewriter", "duration": min(1.5, 0.1 * len(code.strip().split("\n")))}

        super().__init__("code", code, props, anim, id)

    def create_manim_object(self):
        """创建对应的Manim对象"""
        props = self.properties
        code_content = self.content

        # 设置语法高亮语言
        language = props.get("language", "python")

        # 设置是否显示行号
        line_numbers = props.get("line_numbers", True)

        # 获取高亮行
        highlight_lines = props.get("highlight_lines", [])

        # 获取代码块标题
        title = props.get("title", None)

        # 获取主题
        theme = props.get("theme", "dark")

        # 设置背景和文本颜色
        background_color = BLACK if theme == "dark" else WHITE
        text_color = WHITE if theme == "dark" else BLACK

        # 设置 Pygments 样式 - 使用更鲜明的彩色语法高亮
        pygments_style = "monokai" if theme == "dark" else "default"  # 高对比度，多彩色的语法高亮

        # 创建代码对象
        try:
            # 使用Manim的Code类 - 使用最基本的必需参数，避免兼容性问题
            code_obj = Code(
                code=code_content, language=language, font_size=24, insert_line_no=line_numbers, style=pygments_style
            )

            # 手动设置背景矩形
            if hasattr(code_obj, "background_mobject"):
                code_obj.background_mobject.set_fill(background_color, opacity=1.0)
                code_obj.background_mobject.set_stroke(WHITE if theme == "dark" else BLACK, width=1)

            # 确保行号显示 - 修正: code.line_numbers是Paragraph对象
            if line_numbers and hasattr(code_obj, "line_numbers") and hasattr(code_obj.line_numbers, "submobjects"):
                for line_num in code_obj.line_numbers.submobjects:
                    line_num.set_color(text_color)

            # 高亮指定行 - 修正: code.code是Paragraph对象
            for line in highlight_lines:
                if 0 < line <= len(code_content.strip().split("\n")):
                    # 获取正确的行索引（考虑到可能有空行）
                    line_idx = line - 1

                    # 确保索引在有效范围内
                    if (
                        hasattr(code_obj, "code")
                        and hasattr(code_obj.code, "submobjects")
                        and line_idx < len(code_obj.code.submobjects)
                    ):
                        # 高亮代码行
                        line_vgroup = code_obj.code.submobjects[line_idx]
                        line_vgroup.set_color(YELLOW)

                        # 高亮行号
                        if (
                            hasattr(code_obj, "line_numbers")
                            and hasattr(code_obj.line_numbers, "submobjects")
                            and line_idx < len(code_obj.line_numbers.submobjects)
                        ):
                            code_obj.line_numbers.submobjects[line_idx].set_color(YELLOW)

                        # 添加行背景高亮
                        line_height = line_vgroup.height * 1.5

                        # 创建高亮背景矩形
                        highlight_rect = Rectangle(
                            width=code_obj.width * 0.95,
                            height=line_height,
                            fill_color=YELLOW,
                            fill_opacity=0.2,  # 略微提高不透明度
                            stroke_width=0,
                        )

                        # 将矩形放到该行代码的位置
                        highlight_rect.move_to(line_vgroup)

                        # 将矩形添加到代码对象中，放在最底层
                        code_obj.add_to_back(highlight_rect)

            # 如果有标题，添加标题文本
            if title:
                title_text = Text(title, font_size=30, color=text_color)
                title_text.next_to(code_obj, UP, buff=0.3)
                code_obj = VGroup(title_text, code_obj)

        except Exception as e:
            # 如果Manim的Code类失败，使用备用方案
            print(f"Warning: Could not create Code object: {e}")

            # 创建一个简单的矩形加文本
            # 计算适当的矩形尺寸
            code_lines = code_content.strip().split("\n")
            max_line_length = max(len(line) for line in code_lines)
            rect_width = max(6, max_line_length * 0.15)  # 根据最长行调整宽度
            rect_height = max(3, len(code_lines) * 0.5)  # 根据行数调整高度

            rect = Rectangle(
                width=rect_width,
                height=rect_height,
                stroke_color=WHITE,
                stroke_width=2,  # 加粗边框
                fill_color=BLACK,
                fill_opacity=1.0,
            )

            # 逐行创建文本，以便于高亮特定行
            line_text_objects = VGroup()
            for i, line in enumerate(code_lines):
                line_color = YELLOW if i + 1 in highlight_lines else text_color
                line_text = Text(
                    line,
                    font_size=24,
                    color=line_color,
                )
                line_text_objects.add(line_text)

            # 垂直排列文本行
            line_text_objects.arrange(DOWN, aligned_edge=LEFT)
            line_text_objects.move_to(rect.get_center())

            code_obj = VGroup(rect, line_text_objects)

            # 如果有标题，添加标题文本
            if title:
                title_text = Text(title, font_size=30, color=text_color)
                title_text.next_to(rect, UP, buff=0.3)
                code_obj.add(title_text)

        return code_obj

    def create_animation(self, mobject):
        """根据animation属性创建Manim动画"""
        anim = self.animation
        if anim is None:
            return FadeIn(mobject)

        anim_type = anim.get("type", "typewriter")
        duration = anim.get("duration", 1.5)

        if anim_type == "typewriter":
            return Write(mobject, run_time=duration)

        elif anim_type == "fade":
            return FadeIn(mobject, run_time=duration)

        elif anim_type == "build":
            return Create(mobject, run_time=duration)

        elif anim_type == "highlight":
            return Indicate(mobject, color=YELLOW, run_time=duration)

        # 默认动画
        return Write(mobject, run_time=duration)

    def create_sequential_highlight_animation(self, code_obj=None):
        """创建逐行高亮动画

        Args:
            code_obj: 代码对象，如果为None则会自动创建

        Returns:
            创建的动画序列
        """
        # 获取代码内容
        code_content = self.content

        # 如果没有提供code_obj，则创建一个
        if code_obj is None:
            code_obj = self.create_manim_object()

        # 获取代码行数
        lines = code_content.strip().split("\n")
        line_count = len(lines)

        # 创建用于存储动画序列的列表
        animations = []

        # 创建高亮矩形 - 先创建但不显示
        highlight_rect = Rectangle(
            width=code_obj.width * 0.95,
            height=0.3,  # 默认高度，会在循环中根据实际行高调整
            fill_color=YELLOW,
            fill_opacity=0.3,
            stroke_width=0,
        )

        # 确保高亮矩形一开始不可见
        highlight_rect.move_to(code_obj).shift(DOWN * 10)  # 移到屏幕之外

        # 添加代码显示动画
        animations.append(
            AnimationGroup(
                FadeIn(code_obj),
                Create(highlight_rect.copy().set_opacity(0)),  # 创建透明矩形以便后续显示
                lag_ratio=0,
            )
        )

        # 等待短暂时间
        animations.append(Wait(0.5))

        # 尝试多种方式获取代码行
        code_lines = None

        # 方法1: 直接尝试访问code.submobjects
        if hasattr(code_obj, "code") and hasattr(code_obj.code, "submobjects"):
            code_lines = code_obj.code.submobjects

        # 方法2: 如果code_obj是VGroup，尝试在其子对象中查找代码
        elif isinstance(code_obj, VGroup):
            for mob in code_obj.submobjects:
                if hasattr(mob, "code") and hasattr(mob.code, "submobjects"):
                    code_lines = mob.code.submobjects
                    break

        # 如果找到了代码行，创建逐行高亮动画
        if code_lines and len(code_lines) > 0:
            # 逐行高亮
            for i in range(min(line_count, len(code_lines))):
                # 获取当前行
                line = code_lines[i]

                # 只有当行内有内容时才进行高亮
                if hasattr(line, "submobjects") and len(line.submobjects) > 0:
                    # 计算当前行高
                    line_height = line.height * 1.5

                    # 调整矩形高度
                    highlight_rect.height = line_height

                    # 确保矩形与行对齐
                    target_position = line.get_center()

                    # 移动高亮矩形 - 使用短时间快速移动
                    animations.append(highlight_rect.animate.set_opacity(0.3).move_to(target_position))

                    # 停留在该行一段时间
                    animations.append(Wait(0.7))

            # 完成后隐藏高亮矩形
            animations.append(FadeOut(highlight_rect))

        else:
            # 后备方案：使用矩形对整个代码块进行高亮
            print("警告: 无法获取代码行对象，使用后备方案进行高亮")

            # 创建一个高亮矩形，逐渐从上到下移动
            if line_count > 0:
                highlight_height = code_obj.height / line_count
                highlight_rect = Rectangle(
                    width=code_obj.width * 0.95,
                    height=highlight_height,
                    fill_color=YELLOW,
                    fill_opacity=0.3,
                    stroke_width=0,
                )

                # 起始位置在代码顶部
                highlight_rect.move_to(code_obj.get_top() + DOWN * highlight_height / 2)

                # 添加矩形到动画中
                animations.append(Create(highlight_rect))
                animations.append(Wait(0.3))

                # 从上到下移动
                total_height = code_obj.height
                steps = min(10, line_count)  # 最多10步
                step_size = total_height / steps

                for i in range(steps):
                    target_y = code_obj.get_top()[1] - highlight_height / 2 - i * step_size
                    target_position = [code_obj.get_center()[0], target_y, 0]
                    animations.append(highlight_rect.animate.move_to(target_position))
                    animations.append(Wait(0.5))

                # 完成后移除
                animations.append(FadeOut(highlight_rect))
            else:
                # 如果连行数都无法确定，只对整个代码对象进行简单高亮
                animations.append(Indicate(code_obj, color=YELLOW))

        # 创建动画序列，使用较大的lag_ratio确保动画按顺序执行
        return Succession(*animations)


# 测试场景
class CodeElementDemo(Scene):
    def construct(self):
        # 创建Python代码示例 - 带有语法高亮和行号
        python_code = CodeElement(
            """
def hello_world():
    print("Hello, world!")
    return True
""",
            {
                "language": "python",
                "title": "hello.py",
                "highlight_lines": [2],  # 高亮第2行
            },
        )

        python_obj = python_code.create_manim_object()
        python_obj.to_edge(UP, buff=1)
        python_anim = python_code.create_animation(python_obj)

        # 创建带高亮行的JavaScript代码示例
        js_code = CodeElement(
            """
function calculate(a, b) {
    let result = a + b;
    console.log(result);
    return result;
}
""",
            {
                "language": "javascript",
                "highlight_lines": [2],
                "title": "calculate.js",
            },
        )

        js_obj = js_code.create_manim_object()
        js_obj.next_to(python_obj, DOWN, buff=1)
        js_anim = js_code.create_animation(js_obj)

        # 播放代码展示动画
        self.play(python_anim)
        self.wait(0.5)
        self.play(js_anim)
        self.wait(1)

        # 清空场景，展示逐行高亮功能和彩色代码展示
        self.clear()

        # 创建更复杂的Python代码用于逐行高亮演示 - 包含更多Python语法元素展示彩色高亮
        code_content = '''
def fibonacci(n):
    """计算斐波那契数列第n项"""
    if n <= 0:
        return 0
    elif n == 1:
        return 1
    else:
        # 递归计算
        return fibonacci(n-1) + fibonacci(n-2)

# 计算前10个斐波那契数
results = []
for i in range(10):
    # 调用函数并存储结果
    result = fibonacci(i)
    results.append(result)
    print(f"fibonacci({i}) = {result}")  # 格式化输出

assert results[-1] == 34  # 验证结果
'''

        python_highlight_code = CodeElement(
            code_content,
            {
                "language": "python",
                "title": "fibonacci.py",
            },
        )

        # 创建逐行高亮动画对象和动画
        seq_highlight_obj = python_highlight_code.create_manim_object()
        seq_highlight_animation = python_highlight_code.create_sequential_highlight_animation(seq_highlight_obj)

        # 播放逐行高亮动画
        self.play(seq_highlight_animation)

        self.wait(2)


# 原有主函数
if __name__ == "__main__":
    # 设置配置确保正确渲染
    config.preview = True
    config.pixel_height = 1080
    config.pixel_width = 1920

    # 取消下面的注释以运行主演示
    scene = CodeElementDemo()
    scene.render()
