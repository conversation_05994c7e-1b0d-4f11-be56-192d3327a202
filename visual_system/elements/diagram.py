"""
图解元素模块
"""

from visual_system.elements.base import BaseElement


class DiagramElement(BaseElement):
    """
    图解元素类
    
    用于创建各种图解、示意图和流程图
    """
    
    def __init__(self, diagram_type, properties=None, animation=None, id=None):
        """
        初始化图解元素
        
        Args:
            diagram_type (str): 图解类型
            properties (dict, optional): 图解属性
            animation (dict, optional): 动画配置
            id (str, optional): 元素ID
        """
        self.diagram_type = diagram_type
        super().__init__("diagram", diagram_type, properties, animation, id) 