from visual_system.elements.base import BaseElement


class ChartElement(BaseElement):
    """
    Create a chart element with intelligent defaults based on data characteristics

    Args:
        data (dict/list): Chart data or data source reference
        properties (dict, optional): Chart properties with these common keys:
            type (str): Chart type - "bar", "line", "pie", "scatter", "area", "radar", "heatmap", etc.
            purpose (str): Chart purpose - "comparison", "trend", "distribution", "correlation", etc.
            title (str): Chart title
            legend (bool): Whether to show legend
            axes (dict): Axes settings
            palette (str): Color scheme - "default", "categorical", "sequential", "diverging", etc.
            interactive (bool): Whether interactive
        animation (dict, optional): Chart animation settings with these common types:
            "progressive" - Progressive reveal of data points/bars
            "build" - Build up effect (e.g., growing bars/lines)
            "fade" - Fade in effect for chart elements
            "draw" - Drawing effect for lines/paths
            "scatter" - Scatter-in effect for points
            "slice" - Slice-in effect for pie/donut charts
            "wipe" - Wipe effect from axis
            "sequence" - Sequence reveal of series
            "highlight" - Sequential highlight of data points
        id (str, optional): Element unique identifier, auto-generated if not provided

    Returns:
        dict: Configured chart element

    Examples:
        # Create a bar chart for comparison
        bar_chart = ChartElement([
            {"category": "A", "value": 10},
            {"category": "B", "value": 15},
            {"category": "C", "value": 8}
        ], {"type": "bar"})

        # Create a line chart for trend visualization
        line_chart = ChartElement([
            {"date": "2021-01", "value": 50},
            {"date": "2021-02", "value": 55},
            {"date": "2021-03", "value": 48}
        ], {
            "type": "line",
            "title": "Monthly Trend",
            "axes": {"x": {"title": "Month"}, "y": {"title": "Value"}}
        })

        # Create a pie chart with slice animation
        pie_chart = ChartElement([
            {"label": "Category A", "value": 30},
            {"label": "Category B", "value": 45},
            {"label": "Category C", "value": 25}
        ], {
            "type": "pie",
            "title": "Distribution"
        }, {
            "type": "slice",
            "duration": 1.5,
            "easing": "ease-out"
        })
    """
    # 创建图表元素，基于数据特征提供智能默认值
    #
    # 参数:
    #   data (dict/list): 图表数据或数据源引用
    #   properties (dict, 可选): 图表属性，常用键值：
    #     type (str): 图表类型 - "bar"(柱状图), "line"(线图), "pie"(饼图),
    #                "scatter"(散点图), "area"(面积图), "radar"(雷达图), "heatmap"(热力图), 等
    #     purpose (str): 图表目的 - "comparison"(比较), "trend"(趋势),
    #                  "distribution"(分布), "correlation"(相关性), 等
    #     title (str): 图表标题
    #     legend (bool): 是否显示图例
    #     axes (dict): 坐标轴设置
    #     palette (str): 配色方案 - "default"(默认), "categorical"(分类),
    #                  "sequential"(序列), "diverging"(发散), 等
    #     interactive (bool): 是否可交互
    #   animation (dict, 可选): 图表动效设置，常用类型：
    #     "progressive" - 数据点/条形的渐进显示
    #     "build" - 构建效果(如增长的条形/线条)
    #     "fade" - 图表元素的淡入效果
    #     "draw" - 线条/路径的绘制效果
    #     "scatter" - 点的散布效果
    #     "slice" - 饼图/环形图的切片效果
    #     "wipe" - 从坐标轴开始的擦除效果
    #     "sequence" - 序列数据的依次显示
    #     "highlight" - 数据点的顺序高亮
    #   id (str, 可选): 元素唯一标识符，不提供则自动生成

    def __init__(self, data, properties=None, animation=None, id=None):
        props = properties or {}

        # 根据数据特征推断图表类型和目的
        if props.get("type") is None:
            if isinstance(data, dict) and "nodes" in data and "links" in data:
                props["type"] = "network"
                props.setdefault("purpose", "relationship")
            elif isinstance(data, list):
                # 分析数据结构推断合适的图表类型
                if len(data) > 0 and isinstance(data[0], dict):
                    if "category" in data[0] and "value" in data[0]:
                        props["type"] = "bar"
                        props.setdefault("purpose", "comparison")
                    elif "date" in data[0]:
                        props["type"] = "line"
                        props.setdefault("purpose", "trend")
                    elif "label" in data[0] and "value" in data[0]:
                        props["type"] = "pie"
                        props.setdefault("purpose", "distribution")
                    else:
                        props["type"] = "bar"  # 默认
                else:
                    props["type"] = "bar"  # 默认
            else:
                props["type"] = "bar"  # 默认

        # 根据图表类型设置合理的默认属性
        if props.get("type") == "bar":
            props.setdefault("axes", {"x": {"title": "类别"}, "y": {"title": "数值"}})
            props.setdefault(
                "legend",
                True
                if isinstance(data, list) and len(data) > 0 and isinstance(data[0], dict) and "series" in data[0]
                else False,
            )
        elif props.get("type") == "line":
            props.setdefault("axes", {"x": {"title": "时间"}, "y": {"title": "数值"}})
            props.setdefault("legend", True)
        elif props.get("type") == "pie":
            props.setdefault("legend", True)

        # 应用默认动效
        anim = animation
        if anim is None:
            if props.get("type") == "bar":
                anim = {"type": "build", "duration": 1.0, "easing": "ease-out"}
            elif props.get("type") == "line":
                anim = {"type": "draw", "duration": 1.5, "easing": "ease-in-out"}
            elif props.get("type") == "pie":
                anim = {"type": "slice", "duration": 1.2, "easing": "ease-out"}
            elif props.get("type") == "scatter":
                anim = {"type": "scatter", "duration": 1.0, "delay_factor": 0.05}
            elif props.get("type") == "area":
                anim = {"type": "wipe", "duration": 1.2, "direction": "left-to-right"}
            elif props.get("type") == "heatmap":
                anim = {"type": "fade", "duration": 1.0, "pattern": "cells"}
            else:
                anim = {"type": "fade", "duration": 1.0}

        super().__init__("chart", data, props, anim, id)
