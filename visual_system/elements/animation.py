def ElementAnimation(type, duration=1.0, delay=0, easing="ease", properties=None):
    """
    Define element-level animation with intelligent defaults

    Args:
        type (str): Animation type -
            "fade" - Fade in/out
            "slide" - Slide (left, right, up, down)
            "scale" - Scale in/out
            "rotate" - Rotate
            "highlight" - Highlight
            "typewriter" - Typewriter effect
            "draw" - Drawing effect
            "reveal" - Reveal effect
            "bounce" - <PERSON><PERSON><PERSON> effect
            "pulse" - Pulse effect
            "shake" - Shake effect
            "blur" - Blur effect
        duration (float, optional): Duration in seconds. Defaults to 1.0.
        delay (float, optional): Delay in seconds. Defaults to 0.
        easing (str, optional): Easing function - "linear", "ease", "ease-in", "ease-out", etc.
            Defaults to "ease".
        properties (dict, optional): Animation-specific additional properties

    Returns:
        dict: Configured animation settings

    Examples:
        # Create a fade-in animation
        fade_in = ElementAnimation("fade", 0.8)

        # Create a typewriter effect with delay
        typing = ElementAnimation("typewriter", 2.0, 0.5, "ease-out")

        # Create a custom slide animation
        slide = ElementAnimation("slide", 1.0, 0, "ease-in-out", {
            "direction": "left",
            "distance": "100px"
        })
    """
    props = properties or {}

    # Set reasonable defaults based on animation type
    if type == "typewriter":
        duration = max(duration, 1.5)  # Typewriter typically needs more time
    elif type in ["draw", "reveal"]:
        duration = max(duration, 1.2)  # Drawing/revealing typically needs more time

    # Build the basic animation settings
    animation = {
        "type": type,
        "duration": duration,
        "delay": delay,
        "easing": easing,
    }

    # Merge with additional properties
    animation.update(props)

    return animation


from visual_system.elements.base import BaseElement


class AnimationElement(BaseElement):
    """
    创建一个专用的动画元素，用于呈现复杂动画

    Args:
        source (str or dict): 动画源数据 - 可以是JSON动画数据、路径或动画配置对象
        properties (dict, optional): 动画元素属性 - 控制播放行为和外观
            - type (str): 动画类型 - "lottie", "css", "webgl", "svg", "gif", "video"
            - autoplay (bool): 是否自动播放，默认 True
            - loop (bool): 是否循环播放，默认 False
            - controls (bool): 是否显示控制器，默认 False
            - width (str): 宽度，如 "100%", "300px"
            - height (str): 高度，如 "200px"
            - background (str): 背景色，如 "#f0f0f0"
            - quality (str): 渲染质量 - "low", "medium", "high"
            - interactive (bool): 是否可交互式
        animation (dict, optional): 元素本身的动画效果（入场和退场动画）
        id (str, optional): 元素唯一标识符

    Examples using renderer:
        # Create a Lottie animation
        lottie_anim = renderer.create_animation_element("path/to/animation.json", {
            "type": "lottie",
            "loop": True,
            "autoplay": True,
            "width": "400px",
            "height": "300px"
        })

        # Create a CSS animation
        css_anim = renderer.create_animation_element({
            "keyframes": {...},
            "settings": {...}
        }, {
            "type": "css",
            "duration": 2.0,
            "loop": False
        })

        # Create a WebGL particle animation
        particles = renderer.create_animation_element({
            "particleCount": 1000,
            "particleSize": 2,
            "colors": ["#ff0000", "#00ff00", "#0000ff"],
            "speed": 1.5
        }, {
            "type": "webgl",
            "quality": "high",
            "background": "#000000",
            "interactive": True
        })
    """

    def __init__(self, source, properties=None, animation=None, id=None):
        # 设置默认属性
        props = properties or {}

        # 判断动画类型和设置合理的默认值
        if "type" not in props:
            # 尝试从源数据推断动画类型
            if isinstance(source, str) and source.endswith((".json", ".lottie")):
                props["type"] = "lottie"
            elif isinstance(source, str) and source.endswith(".gif"):
                props["type"] = "gif"
            elif isinstance(source, str) and source.endswith(".svg"):
                props["type"] = "svg"
            elif isinstance(source, dict) and "keyframes" in source:
                props["type"] = "css"
            elif isinstance(source, dict) and "particleCount" in source:
                props["type"] = "webgl"
            else:
                props["type"] = "lottie"  # 默认使用Lottie

        # 设置播放控制的默认值
        if "autoplay" not in props:
            props["autoplay"] = True

        if "loop" not in props:
            # 基于动画类型设置循环默认值
            if props["type"] in ["gif", "webgl", "particles"]:
                props["loop"] = True
            else:
                props["loop"] = False

        if "controls" not in props:
            props["controls"] = props["type"] != "gif"

        # 设置尺寸默认值
        if "width" not in props:
            props["width"] = "100%"

        if "height" not in props:
            props["height"] = "auto"

        super().__init__("animation", source, props, animation, id)
