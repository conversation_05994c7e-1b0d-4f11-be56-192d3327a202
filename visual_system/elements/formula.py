"""
公式元素模块，用于创建数学和科学公式
"""

from manim import (
    BLUE,
    DOWN,
    RIGHT,
    UP,
    WHITE,
    YELLOW,
    Create,
    FadeIn,
    Flash,
    GrowFromCenter,
    Indicate,
    MathTex,
    Scene,
    SurroundingRectangle,
    Tex,
    Text,
    VGroup,
    Write,
    config,
)

from visual_system.elements.base import BaseElement


class FormulaElement(BaseElement):
    """公式元素类，用于创建和显示数学和科学公式"""

    def __init__(self, formula, properties=None, animation=None, id=None):
        """
        初始化公式元素

        参数:
            formula (str): LaTeX格式的公式
            properties (dict, optional): 公式的属性，常用键值：
                role (str): 公式角色 - "equation"(方程式), "theorem"(定理),
                          "definition"(定义), "identity"(恒等式), "relationship"(关系)
                size (str): 大小 - "small"(小), "medium"(中), "large"(大)
                numbered (bool): 是否显示公式编号
                aligned (bool): 是否使用aligned环境
                color (str): 公式颜色
            animation (dict, optional): 动画配置
            id (str, optional): 元素ID
        """
        props = properties or {}

        # 应用默认值
        props.setdefault("size", "medium")
        props.setdefault("numbered", formula.count("=") > 0)

        if "role" not in props:
            # 根据公式内容推断角色
            if "=" in formula:
                props["role"] = "equation"
            elif any(kw in formula for kw in ["theorem", "lemma", "proof", "\\begin{theorem}"]):
                props["role"] = "theorem"
            elif any(kw in formula for kw in ["\\triangleq", "\\coloneqq", "\\define", "\\overset{def}{=}"]):
                props["role"] = "definition"
            else:
                props["role"] = "equation"

        # 应用默认动效
        anim = animation
        if anim is None:
            anim = {"type": "write", "duration": 1.2}

        super().__init__("formula", formula, props, anim, id)
        self.formula = formula

    def create_manim_object(self):
        """创建对应的Manim对象"""
        props = self.properties
        formula = self.formula

        # 设置大小
        font_size = 36  # 默认大小
        if props.get("size") == "large":
            font_size = 48
        elif props.get("size") == "medium":
            font_size = 36
        elif props.get("size") == "small":
            font_size = 24

        # 设置颜色
        color = props.get("color", WHITE)

        # 是否显示公式编号
        numbered = props.get("numbered", False)

        # 是否使用aligned环境
        aligned = props.get("aligned", "\\begin{align" in formula)

        # 创建公式对象
        try:
            # 检查是否已经是LaTeX格式
            is_math = formula.strip().startswith("$") or formula.strip().startswith("\\")

            if aligned:
                # 处理已对齐的公式
                if not formula.strip().startswith("\\begin{align"):
                    formula = "\\begin{align}" + formula + "\\end{align}"
                formula_obj = MathTex(formula, font_size=font_size, color=color)
            elif is_math:
                # 数学公式
                formula_obj = MathTex(formula, font_size=font_size, color=color)
            else:
                # 普通LaTeX
                formula_obj = Tex(formula, font_size=font_size, color=color)

            # 如果需要显示公式编号
            if numbered and "=" in formula:
                # 添加编号逻辑，创建一个简单的编号文本
                role = props.get("role", "equation")
                prefix = {"equation": "Eq.", "theorem": "Theorem", "definition": "Def.", "identity": "Id."}.get(
                    role,
                    "",
                )

                # 公式编号
                eq_number = Text(f"({prefix} 1)", font_size=font_size * 0.6, color=color)
                eq_number.next_to(formula_obj, RIGHT, buff=0.5)

                # 组合公式和编号
                formula_obj = VGroup(formula_obj, eq_number)

            # 为定理或定义添加特殊处理
            if props.get("role") in ["theorem", "definition"]:
                highlight_color = BLUE if props.get("role") == "theorem" else YELLOW
                highlight_rect = SurroundingRectangle(
                    formula_obj,
                    color=highlight_color,
                    buff=0.2,
                    stroke_width=2,
                    stroke_opacity=0.5,
                )
                formula_obj = VGroup(highlight_rect, formula_obj)

        except Exception as e:
            # 创建失败时的备用方案
            print(f"Warning: Could not create formula object: {e}")
            # 创建一个简单的文本对象
            formula_obj = Text(formula, font_size=font_size, color=color)

        return formula_obj

    def create_animation(self, mobject):
        """根据animation属性创建Manim动画"""
        anim = self.animation
        if anim is None:
            return Write(mobject, run_time=1.2)

        anim_type = anim.get("type", "write")
        duration = anim.get("duration", 1.2)

        if anim_type == "write":
            return Write(mobject, run_time=duration)

        elif anim_type == "fade":
            return FadeIn(mobject, run_time=duration)

        elif anim_type == "flash":
            # 闪光效果
            return Flash(mobject, line_length=0.1, run_time=duration, color=YELLOW)

        elif anim_type == "grow":
            return GrowFromCenter(mobject, run_time=duration)

        elif anim_type == "create":
            # 尝试使用较新的Create，如果不可用则使用ShowCreation
            return Create(mobject, run_time=duration)

        elif anim_type == "highlight":
            return Indicate(mobject, run_time=duration, color=YELLOW)

        # 默认动画
        return Write(mobject, run_time=duration)

    """
    Create a mathematical formula element using LaTeX syntax

    Args:
        formula (str): LaTeX formula string
        properties (dict, optional): Formula properties with these common keys:
            role (str): Formula role - "equation", "theorem", "definition", "identity", "relationship"
            size (str): Size - "small", "medium", "large"
            numbered (bool): Whether to display an equation number
            aligned (bool): Whether to use aligned environment
            color (str): Color of the formula
        animation (dict, optional): Formula animation settings
        id (str, optional): Element unique identifier, auto-generated if not provided

    Returns:
        dict: Configured formula element

    Examples:
        # Create a basic formula
        basic_formula = FormulaElement("E = mc^2")

        # Create a more complex formula with explicit properties
        complex_formula = FormulaElement(
            "\\int_{0}^{\\infty} e^{-x^2} dx = \\frac{\\sqrt{\\pi}}{2}",
            {"role": "identity", "numbered": True}
        )
    """
    # 创建数学公式元素，使用LaTeX语法
    #
    # 参数:
    #   formula (str): LaTeX公式字符串
    #   properties (dict, 可选): 公式属性，常用键值：
    #     role (str): 公式角色 - "equation"(方程式), "theorem"(定理),
    #                "definition"(定义), "identity"(恒等式), "relationship"(关系)
    #     size (str): 大小 - "small"(小), "medium"(中), "large"(大)
    #     numbered (bool): 是否显示公式编号
    #     aligned (bool): 是否使用aligned环境
    #     color (str): 公式颜色
    #   animation (dict, 可选): 公式动效设置
    #   id (str, 可选): 元素唯一标识符，不提供则自动生成


# 测试场景
class FormulaElementDemo(Scene):
    def construct(self):
        # 测试各种类型的公式

        # 简单公式
        basic_formula = FormulaElement("E = mc^2", {"role": "equation", "numbered": True})
        basic_obj = basic_formula.create_manim_object()
        basic_obj.to_edge(UP, buff=1)
        basic_anim = basic_formula.create_animation(basic_obj)

        # 复杂的数学公式
        complex_formula = FormulaElement(
            "\\int_{0}^{\\infty} e^{-x^2} dx = \\frac{\\sqrt{\\pi}}{2}",
            {"role": "identity", "numbered": True, "size": "large"},
        )
        complex_obj = complex_formula.create_manim_object()
        complex_obj.next_to(basic_obj, DOWN, buff=0.8)
        complex_anim = complex_formula.create_animation(complex_obj)

        # 定理
        theorem_formula = FormulaElement(
            "\\text{For all } x, y \\in \\mathbb{R}, \\quad (x+y)^2 = x^2 + 2xy + y^2",
            {"role": "theorem"},
        )
        theorem_obj = theorem_formula.create_manim_object()
        theorem_obj.next_to(complex_obj, DOWN, buff=0.8)
        theorem_anim = theorem_formula.create_animation(theorem_obj)

        # 定义
        definition_formula = FormulaElement(
            "f(x) \\triangleq x^2 + 2x + 1",
            {"role": "definition", "size": "medium"},
        )
        definition_obj = definition_formula.create_manim_object()
        definition_obj.next_to(theorem_obj, DOWN, buff=0.8)
        definition_anim = definition_formula.create_animation(definition_obj)

        # 播放所有动画
        self.play(basic_anim)
        self.play(complex_anim)
        self.play(theorem_anim)
        self.play(definition_anim)
        self.wait(1)


if __name__ == "__main__":
    # 运行测试用例
    config.preview = True
    scene = FormulaElementDemo()
    scene.render()
