from visual_system.utils.helpers import generate_id


class BaseElement:
    """
    Base class for all visual elements
    
    Args:
        type (str): Element type
        content (any): Element content
        properties (dict): Element-specific properties
        animation (dict): Element-level animation settings
        id (str): Unique element identifier, auto-generated if not provided
    """
    # 所有可视元素的基类
    #
    # 参数:
    #   type (str): 元素类型
    #   content (any): 元素内容
    #   properties (dict): 元素特有属性，如果不提供则为空字典
    #   animation (dict): 元素级动效设置
    #   id (str): 元素唯一标识符，不提供则自动生成

    def __init__(self, type, content, properties=None, animation=None, id=None):
        self.element_type = type
        self.content = content
        self.properties = properties or {}
        self.animation = animation
        self.id = id or generate_id()
    
    def to_dict(self):
        """Convert element to dictionary representation"""
        return {
            "element_type": self.element_type,
            "content": self.content,
            "properties": self.properties,
            "animation": self.animation,
            "id": self.id,
        }
    
    def update_properties(self, new_properties):
        """Update element properties"""
        self.properties.update(new_properties)
        return self
    
    def set_animation(self, animation):
        """Set element animation"""
        self.animation = animation
        return self


# 为了向后兼容，添加Element作为BaseElement的别名
Element = BaseElement
