from visual_system.elements.base import BaseElement


class GeometryElement(BaseElement):
    """
    Create a geometry element with intelligent defaults based on shape characteristics

    Args:
        shape_type (str): Type of geometry - "rectangle", "circle", "triangle", "polygon", "line", "arrow", "ellipse", "star"
        properties (dict, optional): Geometry properties with these common keys:
            dimensions (dict): Shape dimensions - e.g. {"width": 100, "height": 50} or {"radius": 30}
            fill (str): Fill color - hex code, rgb/rgba value, or color name
            stroke (dict): Stroke properties - {"color": "#000", "width": 2, "style": "solid"}
            opacity (float): Opacity value between 0.0 and 1.0
            rotation (float): Rotation in degrees
            position (dict): Position coordinates - {"x": 0, "y": 0}
        animation (dict, optional): Geometry animation settings with these common types:
            "draw" - Progressive drawing effect
            "morph" - Morph from one shape to another
            "pulse" - Pulsing effect
            "rotate" - Rotation effect
            "path" - Move along a path
            "bounce" - Bouncing effect
            "scale" - Scale up/down effect
            "fade" - Fade in/out effect
        id (str, optional): Element unique identifier, auto-generated if not provided

    Examples:
        # Create a circle
        circle = GeometryElement("circle", {
            "dimensions": {"radius": 50},
            "fill": "#3498db",
            "opacity": 0.8
        })

        # Create a rectangle with animation
        rect = GeometryElement("rectangle",
                             {"dimensions": {"width": 200, "height": 100}, "fill": "red"},
                             {"type": "draw", "duration": 1.5})
    """

    # 创建几何元素，基于形状特征提供智能默认值
    #
    # 参数:
    #   shape_type (str): 几何类型 - "rectangle"(矩形), "circle"(圆形), "triangle"(三角形),
    #                    "polygon"(多边形), "line"(线条), "arrow"(箭头), "ellipse"(椭圆), "star"(星形)
    #   properties (dict, 可选): 几何属性，常用键值：
    #     dimensions (dict): 形状尺寸 - 例如 {"width": 100, "height": 50} 或 {"radius": 30}
    #     fill (str): 填充颜色 - 十六进制代码、rgb/rgba值或颜色名称
    #     stroke (dict): 描边属性 - {"color": "#000", "width": 2, "style": "solid"}
    #     opacity (float): 不透明度，0.0到1.0之间
    #     rotation (float): 旋转度数
    #     position (dict): 位置坐标 - {"x": 0, "y": 0}
    #   animation (dict, 可选): 几何动画设置，常用类型：
    #     "draw" - 渐进绘制效果
    #     "morph" - 从一个形状变为另一个形状
    #     "pulse" - 脉冲效果
    #     "rotate" - 旋转效果
    #     "path" - 沿路径移动
    #     "bounce" - 弹跳效果
    #     "scale" - 放大/缩小效果
    #     "fade" - 淡入/淡出效果
    #   id (str, 可选): 元素唯一标识符，不提供则自动生成

    def __init__(self, shape_type, properties=None, animation=None, id=None):
        props = properties or {}

        # 设置默认尺寸
        if "dimensions" not in props:
            if shape_type == "circle":
                props["dimensions"] = {"radius": 50}
            elif shape_type == "rectangle":
                props["dimensions"] = {"width": 100, "height": 80}
            elif shape_type == "triangle":
                props["dimensions"] = {"width": 100, "height": 100}
            elif shape_type == "line":
                props["dimensions"] = {"length": 100}
            elif shape_type == "ellipse":
                props["dimensions"] = {"radiusX": 60, "radiusY": 40}
            else:
                props["dimensions"] = {"width": 100, "height": 100}

        # 设置默认样式
        props.setdefault("fill", "#3498db")
        props.setdefault("opacity", 1.0)

        if "stroke" not in props:
            props["stroke"] = {"color": "#000000", "width": 1, "style": "solid"}

        # 应用默认动效
        anim = animation
        if anim is None:
            if shape_type == "circle" or shape_type == "ellipse":
                anim = {"type": "scale", "duration": 0.8, "from": 0.5, "to": 1.0}
            elif shape_type == "rectangle":
                anim = {"type": "draw", "duration": 1.0}
            elif shape_type == "line" or shape_type == "arrow":
                anim = {"type": "draw", "duration": 0.9, "direction": "left-to-right"}
            elif shape_type == "polygon" or shape_type == "star":
                anim = {"type": "rotate", "duration": 1.2, "angle": 360}
            else:
                anim = {"type": "fade", "duration": 0.7}

        super().__init__("geometry", shape_type, props, anim, id)
