from visual_system.elements.base import BaseElement


class TableElement(BaseElement):
    """
    Create a table element with intelligent defaults based on data characteristics

    Args:
        data (list/dict): Table data
        properties (dict, optional): Table properties with these common keys:
            headers (list): Column headers
            style (str): Table style - "simple", "striped", "bordered", "minimal", "highlight"
            caption (str): Table caption/title
            align (dict): Column alignment - {"col_index": "left/center/right"}
            width (str): Table width - "auto", "100%", specific value
            sort (bool/dict): Sorting options - {column: direction}
            highlight (list/dict): Rows/columns/cells to highlight
            on_select (dict): Selection behavior
        animation (dict, optional): Table animation settings
        id (str, optional): Element unique identifier, auto-generated if not provided

    Returns:
        dict: Configured table element

    Examples:
        # Create a simple table with headers
        simple_table = TableElement([
            ["<PERSON>", 28, "Engineer"],
            ["<PERSON>", 32, "<PERSON>"],
            ["<PERSON>", 45, "<PERSON>"]
        ], {
            "headers": ["Name", "Age", "Profession"]
        })

        # Create a more complex table with specific settings
        advanced_table = TableElement([
            {"name": "Product A", "price": 99.99, "stock": 123},
            {"name": "Product B", "price": 149.99, "stock": 42},
            {"name": "Product C", "price": 29.99, "stock": 89}
        ], {
            "style": "bordered",
            "caption": "Product Inventory",
            "align": {"price": "right", "stock": "center"},
            "highlight": {"rows": [1], "columns": ["price"]}
        })
    """
    # 创建表格元素，基于数据特征提供智能默认值
    #
    # 参数:
    #   data (list/dict): 表格数据
    #   properties (dict, 可选): 表格属性，常用键值：
    #     headers (list): 列标题
    #     style (str): 表格样式 - "simple"(简单), "striped"(条纹),
    #                "bordered"(带边框), "minimal"(极简), "highlight"(高亮)
    #     caption (str): 表格标题/说明
    #     align (dict): 列对齐方式 - {"列索引": "left/center/right"}
    #     width (str): 表格宽度 - "auto", "100%", 具体数值
    #     sort (bool/dict): 排序选项 - {列: 方向}
    #     highlight (list/dict): 要高亮的行/列/单元格
    #     on_select (dict): 选择行为
    #   animation (dict, 可选): 表格动效设置
    #   id (str, 可选): 元素唯一标识符，不提供则自动生成

    def __init__(self, data, properties=None, animation=None, id=None):
        props = properties or {}
        
        # 推断表格结构
        if isinstance(data, list) and len(data) > 0:
            # 检查是否有标题行
            if "headers" not in props:
                # 如果第一行是字典，使用键作为标题
                if isinstance(data[0], dict):
                    props["headers"] = list(data[0].keys())
                # 否则假定没有标题
            
        # 设置默认样式
        props.setdefault("style", "simple")
        
        # 设置默认宽度
        props.setdefault("width", "auto")
        
        # 应用默认动效
        anim = animation
        if anim is None:
            anim = {"type": "fade", "duration": 0.8}

        super().__init__("table", data, props, anim, id)
