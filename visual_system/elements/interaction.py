"""
交互元素模块，用于创建交互式元素
"""

from visual_system.elements.base import BaseElement


class InteractionElement(BaseElement):
    """交互元素类，用于创建用户可交互的组件"""

    def __init__(self, interaction_id, properties=None, animation=None, id=None):
        """
        初始化交互元素
        
        参数:
            interaction_id (str): 交互组件的标识符
            properties (dict, optional): 交互组件的属性，如:
                type (str): 交互类型 - "drag_drop", "slider", "button", "drag_connect"等
                instructions (str): 给用户的操作说明
            animation (dict, optional): 动画配置
            id (str, optional): 元素ID
        """
        super().__init__("interaction", interaction_id, properties, animation, id)
        self.interaction_id = interaction_id 