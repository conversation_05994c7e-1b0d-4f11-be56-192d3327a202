"""
视频元素模块，用于嵌入和控制视频内容
"""

from visual_system.elements.base import BaseElement


class VideoElement(BaseElement):
    """视频元素类，用于嵌入和控制视频内容"""

    def __init__(self, source, properties=None, animation=None, id=None):
        """
        初始化视频元素
        
        参数:
            source (str): 视频的源文件路径或URL
            properties (dict, optional): 视频的属性，如:
                role (str): 视频角色 - "main"(主要内容), "background"(背景),
                          "overlay"(叠加层), "intro"(介绍), "tutorial"(教程),
                          "demo"(演示), "explanation"(解释)
                controls (bool): 是否显示控件
                autoplay (bool): 是否自动播放
                loop (bool): 是否循环播放
                muted (bool): 是否静音
                start_time (float): 开始时间（秒）
                end_time (float): 结束时间（秒）
                aspect (str): 宽高比 - "16:9", "4:3", "1:1", "original"(原始)
            animation (dict, optional): 动画配置
            id (str, optional): 元素ID
        """
        props = properties or {}

        # 从文件名推断角色
        if props.get("role") is None:
            lower_source = source.lower()
            if any(kw in lower_source for kw in ["bg", "background", "loop"]):
                props["role"] = "background"
            elif any(kw in lower_source for kw in ["intro", "opening"]):
                props["role"] = "intro"
            elif any(kw in lower_source for kw in ["tutorial", "how-to", "howto", "guide"]):
                props["role"] = "tutorial"
            elif any(kw in lower_source for kw in ["demo", "sample", "example"]):
                props["role"] = "demo"
            else:
                props["role"] = "main"  # 默认为主要内容

        # 根据角色设置合理的默认属性
        if props.get("role") == "main":
            props.setdefault("controls", True)
            props.setdefault("autoplay", False)
            props.setdefault("loop", False)
        elif props.get("role") == "background":
            props.setdefault("controls", False)
            props.setdefault("autoplay", True)
            props.setdefault("loop", True)
            props.setdefault("muted", True)
        elif props.get("role") == "intro":
            props.setdefault("controls", False)
            props.setdefault("autoplay", True)
            props.setdefault("loop", False)

        # 应用默认宽高比
        props.setdefault("aspect", "16:9")

        super().__init__(props, animation, id)
        self.source = source

    """
    Create a video element with intelligent defaults based on source characteristics

    Args:
        source (str): Video source/path
        properties (dict, optional): Video properties with these common keys:
            role (str): Video role - "main", "background", "overlay", "intro", "tutorial", "demo", "explanation"
            controls (bool): Whether to display controls
            autoplay (bool): Whether to autoplay
            loop (bool): Whether to loop
            muted (bool): Whether audio is muted
            start_time (float): Start time in seconds
            end_time (float): End time in seconds
            aspect (str): Aspect ratio - "16:9", "4:3", "1:1", "original"
        animation (dict, optional): Video container animation settings
        id (str, optional): Element unique identifier, auto-generated if not provided

    Returns:
        dict: Configured video element

    Examples:
        # Create a main video with controls
        main_video = VideoElement("main_content.mp4", {"role": "main"})

        # Create a background video with loop and specific settings
        bg_video = VideoElement("background_loop.mp4", {
            "role": "background",
            "autoplay": True,
            "loop": True,
            "muted": True,
            "controls": False
        })
    """
    # 创建视频元素，基于源文件特征提供智能默认值
    #
    # 参数:
    #   source (str): 视频源/路径
    #   properties (dict, 可选): 视频属性，常用键值：
    #     role (str): 视频角色 - "main"(主要内容), "background"(背景),
    #                "overlay"(叠加层), "intro"(介绍), "tutorial"(教程),
    #                "demo"(演示), "explanation"(解释)
    #     controls (bool): 是否显示控件
    #     autoplay (bool): 是否自动播放
    #     loop (bool): 是否循环播放
    #     muted (bool): 是否静音
    #     start_time (float): 开始时间（秒）
    #     end_time (float): 结束时间（秒）
    #     aspect (str): 宽高比 - "16:9", "4:3", "1:1", "original"(原始)
    #   animation (dict, 可选): 视频容器动效设置
    #   id (str, 可选): 元素唯一标识符，不提供则自动生成
