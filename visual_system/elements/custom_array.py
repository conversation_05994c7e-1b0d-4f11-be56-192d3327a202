"""
数组元素模块，用于可视化数组和排序算法
"""

import numpy as np
from manim import (
    BLACK,
    BLUE,
    DOWN,
    GRAY,
    GREEN,
    LEFT,
    ORIGIN,
    RED,
    RIGHT,
    UP,
    UR,
    WHITE,
    YELLOW,
    Create,
    FadeIn,
    FadeOut,
    Indicate,
    Integer,
    Rectangle,
    Scene,
    Square,
    Text,
    Transform,
    VGroup,
    config,
)

from visual_system.elements.base import BaseElement


class ArrayElement(BaseElement):
    """
    数组元素类，用于可视化数组数据结构及其操作

    Args:
        data (list): 数组数据或初始值
        properties (dict, optional): 数组属性，包含这些常用键值：
            type (str): 数组类型 - "numeric", "string", "object", "mixed"
            style (str): 显示风格 - "block", "bar", "pill", "card", "minimal"
            layout (str): 布局方式 - "horizontal", "vertical", "grid", "circular"
            indexed (bool): 是否显示索引
            labels (list): 额外标签
            highlight_indices (list): 高亮显示的索引列表
            color_map (dict/function): 值到颜色的映射
            operations (list): 可用的操作列表 - "add", "remove", "search", "sort", "reverse", "shuffle"
            group_by (function/str): 分组方式
            compare_function (function): 用于排序和比较的函数
        animation (dict, optional): 数组动画设置，包含这些常用类型：
            "add" - 添加元素的动画
            "remove" - 移除元素的动画
            "swap" - 交换元素的动画
            "highlight" - 高亮元素的动画
            "sort" - 排序动画，支持多种算法
            "search" - 搜索动画，可视化搜索过程
            "update" - 更新元素的动画
            "shift" - 整体移动的动画
            "compare" - 比较元素的动画
        id (str, optional): 元素唯一标识符，自动生成如果未提供

    Examples using renderer:
        # 创建一个简单的数字数组
        number_array = renderer.create_array_element(
            [1, 5, 3, 9, 2, 7],
            {"type": "numeric", "style": "bar"}
        )

        # 创建带高亮和自定义样式的数组
        highlighted_array = renderer.create_array_element(
            ["apple", "banana", "orange", "grape", "kiwi"],
            {
                "type": "string",
                "style": "card",
                "highlight_indices": [1, 3],
                "color_map": {"apple": "#ff6666", "orange": "#ffaa66"}
            }
        )

        # 创建带排序动画的数组
        sortable_array = renderer.create_array_element(
            [8, 4, 6, 2, 9, 5, 7, 1, 3],
            {
                "type": "numeric",
                "style": "block",
                "operations": ["sort", "shuffle", "reverse"]
            },
            {
                "type": "sort",
                "algorithm": "bubble",  # 也支持 "quick", "merge", "insertion", "selection" 等
                "duration": 2.0,
                "show_comparisons": True
            }
        )
    """

    # 创建数组元素，基于数据特征提供智能默认值
    #
    # 参数:
    #   data (list): 数组数据或初始值
    #   properties (dict, 可选): 数组属性，常用键值：
    #     type (str): 数组类型 - "numeric"(数值型), "string"(字符串型),
    #                "object"(对象型), "mixed"(混合型)
    #     style (str): 显示风格 - "block"(块状), "bar"(条形), "pill"(药丸形),
    #                "card"(卡片), "minimal"(极简)
    #     layout (str): 布局方式 - "horizontal"(水平), "vertical"(垂直),
    #                 "grid"(网格), "circular"(环形)
    #     indexed (bool): 是否显示索引
    #     labels (list): 额外标签
    #     highlight_indices (list): 高亮显示的索引列表
    #     color_map (dict/function): 值到颜色的映射
    #     operations (list): 可用的操作列表 - "add"(添加), "remove"(删除),
    #                      "search"(搜索), "sort"(排序), "reverse"(反转),
    #                      "shuffle"(洗牌)
    #     group_by (function/str): 分组方式
    #     compare_function (function): 用于排序和比较的函数
    #   animation (dict, 可选): 数组动画设置，常用类型：
    #     "add" - 添加元素的动画
    #     "remove" - 移除元素的动画
    #     "swap" - 交换元素的动画
    #     "highlight" - 高亮元素的动画
    #     "sort" - 排序动画，支持多种算法
    #     "search" - 搜索动画，可视化搜索过程
    #     "update" - 更新元素的动画
    #     "shift" - 整体移动的动画
    #     "compare" - 比较元素的动画
    #   id (str, 可选): 元素唯一标识符，不提供则自动生成

    def __init__(self, data, properties=None, animation=None, id=None):
        """
        初始化数组元素

        Args:
            data (list): 数组数据
            properties (dict, optional): 数组属性
            animation (dict, optional): 数组动画设置
            id (str, optional): 元素唯一标识符
        """
        # 确保数据是列表
        if not isinstance(data, list):
            data = list(data) if hasattr(data, "__iter__") else [data]

        # 推断数组类型
        props = properties or {}
        if props.get("type") is None:
            if not data:
                props["type"] = "mixed"  # 空数组默认为混合类型
            elif all(isinstance(item, (int, float)) for item in data):
                props["type"] = "numeric"
            elif all(isinstance(item, str) for item in data):
                props["type"] = "string"
            elif all(isinstance(item, dict) for item in data):
                props["type"] = "object"
            else:
                props["type"] = "mixed"

        # 根据类型和数据特性设置默认属性
        if props.get("style") is None:
            if props.get("type") == "numeric":
                props["style"] = "bar"  # 数字默认用条形显示
            elif props.get("type") == "string":
                props["style"] = "pill"  # 字符串默认用药丸形显示
            else:
                props["style"] = "card"  # 对象或混合类型用卡片显示

        # 设置默认布局
        props.setdefault("layout", "horizontal")

        # 根据数组大小决定是否默认显示索引
        props.setdefault("indexed", len(data) <= 20)  # 元素较少时默认显示索引

        # 设置默认可用操作
        if "operations" not in props:
            if props.get("type") == "numeric":
                props["operations"] = ["add", "remove", "search", "sort", "reverse"]
            elif props.get("type") == "string":
                props["operations"] = ["add", "remove", "search", "sort", "reverse"]
            else:
                props["operations"] = ["add", "remove", "search"]

        # 应用默认动画
        if animation is None:
            # 根据数组类型和风格选择默认动画
            if "sort" in props.get("operations", []):
                animation = {
                    "type": "sort",
                    "algorithm": "quick" if len(data) > 10 else "bubble",
                    "duration": 0.05 * len(data),
                    "easing": "ease-in-out",
                }
            elif "search" in props.get("operations", []):
                animation = {
                    "type": "search",
                    "algorithm": "binary" if props.get("type") == "numeric" else "linear",
                    "duration": 1.0,
                    "highlight_color": "#ffcc00",
                }
            else:
                animation = {
                    "type": "add",
                    "duration": 0.8,
                    "direction": "end",
                }

        # 调用父类初始化
        super().__init__("array", data, props, animation, id)

    def create_manim_object(self):
        """创建对应的Manim对象"""
        data = self.content
        props = self.properties

        # 获取属性
        array_type = props.get("type", "numeric")
        style = props.get("style", "block")
        layout = props.get("layout", "horizontal")
        indexed = props.get("indexed", True)
        highlight_indices = props.get("highlight_indices", [])

        # 创建数组元素
        elements = []
        indices = []

        # 确定布局方向
        direction = RIGHT if layout == "horizontal" else DOWN

        # 创建单个元素的函数
        def create_element(value, index):
            # 根据类型和风格创建不同的元素
            if style == "block":
                # 块状风格，使用正方形
                block = Square(side_length=0.8, fill_opacity=0.8, fill_color=WHITE, stroke_color=BLACK)

                # 根据类型设置内容显示
                if array_type == "numeric":
                    text = Text(str(value), font_size=24, color=BLACK)
                else:
                    text = Text(str(value)[:5], font_size=20, color=BLACK)  # 截断长文本

                text.move_to(block.get_center())
                element = VGroup(block, text)

            elif style == "bar":
                # 条形风格，高度根据数值（仅适用于数值型）
                if array_type == "numeric":
                    # 获取数组中的最大值作为标准化基准
                    max_val = max([abs(x) for x in data]) if data else 1
                    normalized_height = abs(value) / max_val * 2.0 + 0.2  # 至少有一个基础高度

                    # 如果为负值，朝下
                    if value < 0:
                        bar = Rectangle(
                            width=0.6,
                            height=normalized_height,
                            fill_opacity=0.8,
                            fill_color=RED,
                            stroke_color=BLACK,
                        ).next_to(ORIGIN, DOWN, buff=0)
                    else:
                        bar = Rectangle(
                            width=0.6,
                            height=normalized_height,
                            fill_opacity=0.8,
                            fill_color=BLUE,
                            stroke_color=BLACK,
                        ).next_to(ORIGIN, UP, buff=0)

                    # 添加数值文本
                    text = Text(str(value), font_size=20, color=WHITE)

                    if value < 0:
                        text.next_to(bar, DOWN, buff=0.1)
                    else:
                        text.next_to(bar, UP, buff=0.1)

                    element = VGroup(bar, text)
                else:
                    # 非数值型不适合用条形图，改用块状
                    block = Rectangle(width=0.8, height=0.8, fill_opacity=0.8, fill_color=BLUE, stroke_color=BLACK)
                    text = Text(str(value)[:5], font_size=20, color=WHITE)
                    text.move_to(block.get_center())
                    element = VGroup(block, text)

            elif style == "pill":
                # 药丸形风格，适合字符串
                pill = Rectangle(
                    width=len(str(value)) * 0.15 + 0.4,
                    height=0.6,
                    fill_opacity=0.8,
                    fill_color=GREEN,
                    stroke_color=BLACK,
                )
                text = Text(str(value), font_size=18, color=WHITE)
                text.move_to(pill.get_center())
                element = VGroup(pill, text)

            elif style == "card":
                # 卡片风格，适合对象或混合类型
                card = Rectangle(
                    width=1.2,
                    height=1.5,
                    fill_opacity=0.8,
                    fill_color=GRAY,
                    stroke_color=BLACK,
                )

                # 如果是对象类型，显示一些属性
                if array_type == "object" and isinstance(value, dict):
                    card_texts = VGroup()
                    y_offset = 0.3

                    # 显示最多三个键值对
                    for i, (k, v) in enumerate(list(value.items())[:3]):
                        key_text = Text(f"{k}:", font_size=14, color=WHITE)
                        value_text = Text(f"{v}"[:8], font_size=14, color=WHITE)

                        key_text.move_to(card.get_center() + UP * y_offset + LEFT * 0.3)
                        value_text.move_to(card.get_center() + UP * y_offset + RIGHT * 0.3)

                        card_texts.add(key_text, value_text)
                        y_offset -= 0.3

                    element = VGroup(card, card_texts)
                else:
                    # 简单显示值
                    text = Text(str(value)[:10], font_size=16, color=WHITE)
                    text.move_to(card.get_center())
                    element = VGroup(card, text)

            else:  # minimal
                # 极简风格
                text = Text(str(value), font_size=24, color=WHITE)
                element = text

            # 如果索引在高亮列表中，应用高亮效果
            if index in highlight_indices:
                highlight = Rectangle(
                    width=element.width + 0.2,
                    height=element.height + 0.2,
                    fill_opacity=0.2,
                    fill_color=YELLOW,
                    stroke_color=YELLOW,
                )
                highlight.move_to(element.get_center())
                element = VGroup(highlight, element)

            return element

        # 创建所有元素
        for i, item in enumerate(data):
            # 创建元素
            element = create_element(item, i)
            elements.append(element)

            # 如果显示索引，为每个元素添加索引标签
            if indexed:
                index_text = Integer(i, color=WHITE, font_size=16)

                if layout == "horizontal":
                    index_text.next_to(element, DOWN, buff=0.2)
                else:
                    index_text.next_to(element, LEFT, buff=0.2)

                indices.append(index_text)

        # 根据布局组织元素
        if layout == "horizontal" or layout == "vertical":
            array_group = VGroup(*elements).arrange(direction, buff=0.5)

            if indexed:
                indices_group = VGroup(*indices)
                result = VGroup(array_group, indices_group)
            else:
                result = array_group

        elif layout == "grid":
            # 计算合适的网格尺寸
            num_elements = len(elements)
            grid_width = int(np.ceil(np.sqrt(num_elements)))

            # 创建网格
            grid = VGroup()
            for i in range(0, num_elements, grid_width):
                row = VGroup(*elements[i : min(i + grid_width, num_elements)]).arrange(RIGHT, buff=0.5)
                grid.add(row)

            grid.arrange(DOWN, buff=0.5)
            result = grid

            # 如果显示索引，为每个元素在网格中添加索引标签
            if indexed:
                for i, index_text in enumerate(indices):
                    # element_pos = i % grid_width, i // grid_width
                    index_text.next_to(elements[i], DOWN, buff=0.2)

                indices_group = VGroup(*indices)
                result = VGroup(grid, indices_group)

        elif layout == "circular":
            # 创建环形布局
            num_elements = len(elements)
            radius = 1.5 + 0.1 * num_elements  # 根据元素数量调整半径

            for i, element in enumerate(elements):
                angle = i * 2 * np.pi / num_elements
                pos = radius * np.array([np.cos(angle), np.sin(angle), 0])
                element.move_to(pos)

            array_group = VGroup(*elements)

            # 处理索引标签，将索引放在圆外
            if indexed:
                for i, index_text in enumerate(indices):
                    angle = i * 2 * np.pi / num_elements
                    pos = (radius + 0.5) * np.array([np.cos(angle), np.sin(angle), 0])
                    index_text.move_to(pos)

                indices_group = VGroup(*indices)
                result = VGroup(array_group, indices_group)
            else:
                result = array_group

        else:
            # 默认水平布局
            array_group = VGroup(*elements).arrange(RIGHT, buff=0.5)

            if indexed:
                indices_group = VGroup(*indices)
                result = VGroup(array_group, indices_group)
            else:
                result = array_group

        return result

    def create_animation(self, mobject):
        """根据animation属性创建Manim动画"""
        anim = self.animation
        if anim is None:
            return FadeIn(mobject)

        anim_type = anim.get("type", "add")
        duration = anim.get("duration", 1.0)

        if anim_type == "add":
            direction = anim.get("direction", "end")

            # 如果方向为"end"，则从右侧添加元素
            if direction == "end":
                return FadeIn(mobject, shift=LEFT, run_time=duration)
            # 如果方向为"start"，则从左侧添加元素
            elif direction == "start":
                return FadeIn(mobject, shift=RIGHT, run_time=duration)
            else:
                return FadeIn(mobject, run_time=duration)

        elif anim_type == "remove":
            return FadeOut(mobject, run_time=duration)

        elif anim_type == "swap":
            # 简单交换动画（实际的交换逻辑通常需要更复杂的处理）
            return Create(mobject, run_time=duration)

        elif anim_type == "highlight":
            highlight_color = anim.get("highlight_color", YELLOW)
            return Indicate(mobject, color=highlight_color, run_time=duration)

        elif anim_type == "sort" or anim_type == "search":
            # 这些动画需要在Scene内部实现，这里提供一个基本的占位符效果
            return FadeIn(mobject, run_time=duration)

        # 默认为淡入动画
        return FadeIn(mobject, run_time=duration)

    def get_element_at(self, index):
        """
        获取指定索引的元素

        Args:
            index (int): 元素索引

        Returns:
            any: 元素值，如果索引无效则返回None
        """
        if 0 <= index < len(self.content):
            return self.content[index]
        return None

    def set_element_at(self, index, value):
        """
        设置指定索引的元素

        Args:
            index (int): 元素索引
            value: 新值

        Returns:
            bool: 是否成功设置
        """
        if 0 <= index < len(self.content):
            self.content[index] = value
            return True
        return False

    def swap_elements(self, i, j):
        """
        交换数组中的两个元素

        Args:
            i (int): 第一个元素索引
            j (int): 第二个元素索引

        Returns:
            bool: 是否成功交换
        """
        if (0 <= i < len(self.content)) and (0 <= j < len(self.content)):
            self.content[i], self.content[j] = self.content[j], self.content[i]
            return True
        return False

    def highlight_element(self, index, color=None):
        """
        高亮数组元素

        Args:
            index (int): 元素索引
            color (str, optional): 高亮颜色，不提供则使用默认高亮色

        Returns:
            bool: 是否成功高亮
        """
        if 0 <= index < len(self.content):
            # 确保有highlight_indices属性
            if "highlight_indices" not in self.properties:
                self.properties["highlight_indices"] = []

            # 确保highlight_indices是列表而不是字典
            if isinstance(self.properties["highlight_indices"], dict):
                self.properties["highlight_indices"] = list(self.properties["highlight_indices"].keys())

            # 设置高亮
            if index not in self.properties["highlight_indices"]:
                self.properties["highlight_indices"].append(index)
            return True
        return False

    def clear_highlights(self):
        """
        清除所有高亮

        Returns:
            bool: 是否成功清除
        """
        self.properties["highlight_indices"] = []
        return True

    def sort(self, algorithm="quicksort"):
        """
        排序数组

        Args:
            algorithm (str): 排序算法 - "quicksort", "mergesort", "bubblesort"

        Returns:
            bool: 是否成功排序
        """
        # 只实现简单的原地排序，不考虑动画

        self.content.sort()
        return True

    def partition(self, low, high):
        """
        快速排序的分区操作

        Args:
            low (int): 低位索引
            high (int): 高位索引

        Returns:
            int: 分区点索引
        """
        pivot = self.content[high]
        i = low - 1

        for j in range(low, high):
            if self.content[j] <= pivot:
                i += 1
                self.swap_elements(i, j)

        self.swap_elements(i + 1, high)
        return i + 1

    def quicksort_recursive(self, low, high):
        """
        递归实现快速排序

        Args:
            low (int): 低位索引
            high (int): 高位索引
        """
        if low < high:
            pi = self.partition(low, high)
            self.quicksort_recursive(low, pi - 1)
            self.quicksort_recursive(pi + 1, high)

    def quicksort(self):
        """
        使用快速排序算法排序数组

        Returns:
            bool: 是否成功排序
        """
        self.quicksort_recursive(0, len(self.content) - 1)
        return True

    def visualize_sort(self, scene, algorithm="bubble", position=None):
        """
        可视化数组排序过程，动态展示每一步变化

        Args:
            scene (Scene): Manim场景对象
            algorithm (str): 排序算法 - "bubble", "selection", "insertion", "quick"
            position (np.array, optional): 初始位置，不提供则使用当前位置

        Returns:
            VGroup: 最终的数组对象
        """
        # 保存原始数据的副本
        original_data = self.content.copy()

        # 创建初始数组对象
        array_obj = self.create_manim_object()
        if position is not None:
            array_obj.move_to(position)

        # 添加到场景
        scene.play(FadeIn(array_obj))
        scene.wait(0.5)

        # 根据算法类型执行排序可视化
        if algorithm == "bubble":
            self._visualize_bubble_sort(scene, array_obj)
        elif algorithm == "selection":
            self._visualize_selection_sort(scene, array_obj)
        elif algorithm == "insertion":
            self._visualize_insertion_sort(scene, array_obj)
        elif algorithm == "quick":
            # 为快速排序创建递归可视化
            self._quicksort_steps = []
            self._collect_quicksort_steps(0, len(self.content) - 1)
            self._visualize_quick_sort(scene, array_obj)
        else:
            # 默认使用冒泡排序
            self._visualize_bubble_sort(scene, array_obj)

        # 恢复原始数据（仅用于演示，实际应用中可能需要保持排序后的状态）
        if original_data != self.content:
            scene.wait(1)  # 在还原前等待一下，让观众看清最终排序结果

            # 可选：显示"还原到原始数据"的文本提示
            restore_text = Text("还原到原始数据", font_size=24)
            restore_text.to_edge(DOWN)
            scene.play(FadeIn(restore_text))

            # 恢复原始数据并创建对象
            self.content = original_data
            original_obj = self.create_manim_object()
            original_obj.move_to(array_obj.get_center())

            # 变换回原始状态
            scene.play(Transform(array_obj, original_obj))
            scene.play(FadeOut(restore_text))

        return array_obj

    def _visualize_bubble_sort(self, scene, array_obj):
        """可视化冒泡排序过程"""
        n = len(self.content)

        for i in range(n):
            # 用于标记这轮是否发生了交换
            swapped = False

            for j in range(0, n - i - 1):
                # 创建比较元素的高亮效果
                curr_array = self.content.copy()
                curr_props = self.properties.copy()
                curr_props["highlight_indices"] = [j, j + 1]

                curr_element = ArrayElement(curr_array, curr_props)
                curr_obj = curr_element.create_manim_object()
                curr_obj.move_to(array_obj.get_center())

                # 显示当前比较的元素
                scene.play(Transform(array_obj, curr_obj), run_time=0.5)

                # 如果需要交换
                if self.content[j] > self.content[j + 1]:
                    # 交换元素
                    self.content[j], self.content[j + 1] = self.content[j + 1], self.content[j]
                    swapped = True

                    # 创建交换后的数组对象
                    swapped_element = ArrayElement(self.content.copy(), curr_props)
                    swapped_obj = swapped_element.create_manim_object()
                    swapped_obj.move_to(array_obj.get_center())

                    # 显示交换过程
                    scene.play(Transform(array_obj, swapped_obj), run_time=0.8)

            # 如果这轮没有发生交换，排序已完成
            if not swapped:
                break

        # 显示最终排序结果（无高亮）
        final_element = ArrayElement(self.content.copy(), self.properties.copy())
        final_obj = final_element.create_manim_object()
        final_obj.move_to(array_obj.get_center())
        scene.play(Transform(array_obj, final_obj), run_time=0.5)

    def _visualize_selection_sort(self, scene, array_obj):
        """可视化选择排序过程"""
        n = len(self.content)

        for i in range(n):
            # 假设当前索引的元素是最小的
            min_idx = i

            # 高亮当前位置
            curr_props = self.properties.copy()
            curr_props["highlight_indices"] = [min_idx]

            curr_element = ArrayElement(self.content.copy(), curr_props)
            curr_obj = curr_element.create_manim_object()
            curr_obj.move_to(array_obj.get_center())

            scene.play(Transform(array_obj, curr_obj), run_time=0.5)

            # 遍历未排序的元素找最小值
            for j in range(i + 1, n):
                # 高亮比较的元素
                compare_props = self.properties.copy()
                compare_props["highlight_indices"] = [min_idx, j]

                compare_element = ArrayElement(self.content.copy(), compare_props)
                compare_obj = compare_element.create_manim_object()
                compare_obj.move_to(array_obj.get_center())

                scene.play(Transform(array_obj, compare_obj), run_time=0.3)

                # 找到新的最小值
                if self.content[j] < self.content[min_idx]:
                    min_idx = j

                    # 高亮新的最小值
                    min_props = self.properties.copy()
                    min_props["highlight_indices"] = [min_idx]

                    min_element = ArrayElement(self.content.copy(), min_props)
                    min_obj = min_element.create_manim_object()
                    min_obj.move_to(array_obj.get_center())

                    scene.play(Transform(array_obj, min_obj), run_time=0.3)

            # 如果最小值不是当前位置，进行交换
            if min_idx != i:
                self.content[i], self.content[min_idx] = self.content[min_idx], self.content[i]

                # 创建交换后的数组对象
                swap_props = self.properties.copy()
                swap_props["highlight_indices"] = [i]

                swap_element = ArrayElement(self.content.copy(), swap_props)
                swap_obj = swap_element.create_manim_object()
                swap_obj.move_to(array_obj.get_center())

                scene.play(Transform(array_obj, swap_obj), run_time=0.8)

        # 显示最终排序结果（无高亮）
        final_element = ArrayElement(self.content.copy(), self.properties.copy())
        final_obj = final_element.create_manim_object()
        final_obj.move_to(array_obj.get_center())
        scene.play(Transform(array_obj, final_obj), run_time=0.5)

    def _visualize_insertion_sort(self, scene, array_obj):
        """可视化插入排序过程"""
        n = len(self.content)

        for i in range(1, n):
            key = self.content[i]
            j = i - 1

            # 高亮当前处理的元素
            curr_props = self.properties.copy()
            curr_props["highlight_indices"] = [i]

            curr_element = ArrayElement(self.content.copy(), curr_props)
            curr_obj = curr_element.create_manim_object()
            curr_obj.move_to(array_obj.get_center())

            scene.play(Transform(array_obj, curr_obj), run_time=0.5)

            # 向前比较并移动元素
            while j >= 0 and self.content[j] > key:
                # 高亮比较的元素
                compare_props = self.properties.copy()
                compare_props["highlight_indices"] = [j, j + 1]

                compare_element = ArrayElement(self.content.copy(), compare_props)
                compare_obj = compare_element.create_manim_object()
                compare_obj.move_to(array_obj.get_center())

                scene.play(Transform(array_obj, compare_obj), run_time=0.3)

                # 移动元素
                self.content[j + 1] = self.content[j]
                j -= 1

                # 如果继续向前比较，更新显示
                if j >= 0:
                    move_props = self.properties.copy()
                    move_props["highlight_indices"] = [j, j + 1]

                    move_element = ArrayElement(self.content.copy(), move_props)
                    move_obj = move_element.create_manim_object()
                    move_obj.move_to(array_obj.get_center())

                    scene.play(Transform(array_obj, move_obj), run_time=0.3)

            # 插入元素
            self.content[j + 1] = key

            # 显示插入后的状态
            insert_props = self.properties.copy()
            insert_props["highlight_indices"] = [j + 1]

            insert_element = ArrayElement(self.content.copy(), insert_props)
            insert_obj = insert_element.create_manim_object()
            insert_obj.move_to(array_obj.get_center())

            scene.play(Transform(array_obj, insert_obj), run_time=0.5)

        # 显示最终排序结果（无高亮）
        final_element = ArrayElement(self.content.copy(), self.properties.copy())
        final_obj = final_element.create_manim_object()
        final_obj.move_to(array_obj.get_center())
        scene.play(Transform(array_obj, final_obj), run_time=0.5)

    def _collect_quicksort_steps(self, low, high):
        """收集快速排序的步骤，用于可视化"""
        # 创建原始数据的副本，避免修改原始数据
        temp_data = self.content.copy()
        steps = []

        def collect_partition_steps(arr, low, high):
            """收集分区步骤，用于可视化"""
            pivot = arr[high]
            i = low - 1

            # 添加选择枢轴的步骤
            steps.append({"type": "pivot", "pivot_idx": high, "range": (low, high), "array_state": arr.copy()})

            for j in range(low, high):
                # 添加比较步骤
                steps.append({"type": "compare", "indices": [j, high], "range": (low, high), "array_state": arr.copy()})

                if arr[j] <= pivot:
                    i += 1

                    # 如果需要交换，添加交换步骤
                    if i != j:
                        steps.append(
                            {"type": "swap", "indices": [i, j], "range": (low, high), "array_state": arr.copy()}
                        )
                        arr[i], arr[j] = arr[j], arr[i]
                        steps.append(
                            {"type": "after_swap", "indices": [i, j], "range": (low, high), "array_state": arr.copy()}
                        )

            # 交换枢轴到正确位置的步骤
            i += 1
            if i != high:
                steps.append({"type": "swap", "indices": [i, high], "range": (low, high), "array_state": arr.copy()})
                arr[i], arr[high] = arr[high], arr[i]
                steps.append(
                    {"type": "after_swap", "indices": [i, high], "range": (low, high), "array_state": arr.copy()}
                )

            # 添加分区完成步骤
            steps.append({"type": "partition_done", "pivot_idx": i, "range": (low, high), "array_state": arr.copy()})

            return i

        def recursive_collect_steps(arr, low, high):
            """递归收集快速排序步骤"""
            if low < high:
                # 收集分区步骤
                pivot_idx = collect_partition_steps(arr, low, high)

                # 递归收集左右子数组的步骤
                recursive_collect_steps(arr, low, pivot_idx - 1)
                recursive_collect_steps(arr, pivot_idx + 1, high)

        # 使用临时数据收集步骤
        recursive_collect_steps(temp_data, low, high)

        # 保存步骤数据
        self._quicksort_steps = steps

    def _visualize_quick_sort(self, scene, array_obj):
        """可视化快速排序过程"""
        # 保存原始数据的副本
        original_data = self.content.copy()

        # 重置数据为原始状态
        self.content = original_data.copy()

        # 如果没有步骤，先收集步骤
        if not hasattr(self, "_quicksort_steps") or not self._quicksort_steps:
            self._collect_quicksort_steps(0, len(self.content) - 1)

        # 执行收集的步骤
        for step in self._quicksort_steps:
            step_type = step["type"]

            # 更新当前数组状态为步骤中的状态
            if "array_state" in step:
                current_state = step["array_state"]
            else:
                # 兼容旧步骤格式
                continue

            # 根据步骤类型创建不同的可视化效果
            if step_type == "pivot":
                # 高亮枢轴
                pivot_props = self.properties.copy()
                pivot_props["highlight_indices"] = [step["pivot_idx"]]

                # 使用步骤中的数组状态创建元素
                pivot_element = ArrayElement(current_state.copy(), pivot_props)
                pivot_obj = pivot_element.create_manim_object()
                pivot_obj.move_to(array_obj.get_center())

                # 添加文本说明
                pivot_text = Text(f"选择枢轴: {current_state[step['pivot_idx']]}", font_size=24)
                pivot_text.to_edge(DOWN)

                scene.play(Transform(array_obj, pivot_obj), FadeIn(pivot_text), run_time=0.5)
                scene.wait(0.3)
                scene.play(FadeOut(pivot_text))

            elif step_type == "compare":
                # 高亮比较的元素
                compare_props = self.properties.copy()
                compare_props["highlight_indices"] = step["indices"]

                # 使用步骤中的数组状态创建元素
                compare_element = ArrayElement(current_state.copy(), compare_props)
                compare_obj = compare_element.create_manim_object()
                compare_obj.move_to(array_obj.get_center())

                scene.play(Transform(array_obj, compare_obj), run_time=0.3)

            elif step_type == "swap" or step_type == "after_swap":
                # 高亮交换的元素
                swap_props = self.properties.copy()
                swap_props["highlight_indices"] = step["indices"]

                # 使用步骤中的数组状态创建元素
                swap_element = ArrayElement(current_state.copy(), swap_props)
                swap_obj = swap_element.create_manim_object()
                swap_obj.move_to(array_obj.get_center())

                # 如果是交换后状态，更新数组内容
                if step_type == "after_swap":
                    self.content = current_state.copy()

                scene.play(Transform(array_obj, swap_obj), run_time=0.5)

            elif step_type == "partition_done":
                # 高亮已放置在正确位置的枢轴
                done_props = self.properties.copy()
                done_props["highlight_indices"] = [step["pivot_idx"]]

                # 使用步骤中的数组状态创建元素
                done_element = ArrayElement(current_state.copy(), done_props)
                done_obj = done_element.create_manim_object()
                done_obj.move_to(array_obj.get_center())

                # 添加文本说明
                done_text = Text(f"枢轴 {current_state[step['pivot_idx']]} 已放置在正确位置", font_size=24)
                done_text.to_edge(DOWN)

                scene.play(Transform(array_obj, done_obj), FadeIn(done_text), run_time=0.5)
                scene.wait(0.3)
                scene.play(FadeOut(done_text))

        # 显示最终排序结果（无高亮）
        final_element = ArrayElement(self.content.copy(), self.properties.copy())
        final_obj = final_element.create_manim_object()
        final_obj.move_to(array_obj.get_center())
        scene.play(Transform(array_obj, final_obj), run_time=0.5)


class ArrayOperationElement(BaseElement):
    """
    创建一个数组操作元素，用于可视化展示对数组的特定操作

    Args:
        array_id (str): 要操作的数组元素ID
        operation (str): 操作类型 - "add", "remove", "search", "sort", "reverse",
                        "shuffle", "filter", "map", "reduce"
        properties (dict, optional): 操作属性，常见键值：
            parameters (dict): 操作参数，根据操作类型不同而不同
                - add: {"value": item, "position": "start"/"end"/index}
                - remove: {"index": index} 或 {"value": value}
                - search: {"value": item, "algorithm": "linear"/"binary"}
                - sort: {"algorithm": "bubble"/"quick"/"merge"/etc, "order": "asc"/"desc"}
                - filter: {"condition": function/expression}
                - map: {"function": function/expression}
            show_code (bool): 是否显示操作的伪代码
            show_complexity (bool): 是否显示操作的时间/空间复杂度
            interactive (bool): 是否允许用户交互干预操作
            step_by_step (bool): 是否逐步展示操作过程
            highlight_changes (bool): 是否高亮变化的元素
        animation (dict, optional): 操作动画设置
            speed (float): 动画速度倍率
            pause_points (list): 动画暂停点
            highlight_colors (dict): 高亮颜色映射
            transitions (str): 过渡效果 - "smooth", "discrete", "elastic"
            narration (bool): 是否包含解释性文字
        id (str, optional): 元素唯一标识符，自动生成如果未提供

    Examples using renderer:
        # 创建一个排序操作
        sort_operation = renderer.create_array_operation_element(
            "my_array",
            "sort",
            {
                "parameters": {"algorithm": "quick", "order": "asc"},
                "show_code": True,
                "step_by_step": True
            }
        )

        # 创建一个搜索操作
        search_operation = renderer.create_array_operation_element(
            "my_array",
            "search",
            {
                "parameters": {"value": 42, "algorithm": "binary"},
                "show_complexity": True
            }
        )

        # 创建一个添加元素操作
        add_operation = renderer.create_array_operation_element(
            "my_array",
            "add",
            {
                "parameters": {"value": "new item", "position": "end"},
                "highlight_changes": True
            }
        )
    """

    # 创建数组操作元素，用于可视化展示对数组的特定操作
    #
    # 参数:
    #   array_id (str): 要操作的数组元素ID
    #   operation (str): 操作类型 - "add"(添加), "remove"(删除), "search"(搜索),
    #                   "sort"(排序), "reverse"(反转), "shuffle"(洗牌),
    #                   "filter"(过滤), "map"(映射), "reduce"(归约)
    #   properties (dict, 可选): 操作属性，常见键值：
    #     parameters (dict): 操作参数，根据操作类型不同而不同
    #       - add: {"value": 值, "position": "start"/"end"/索引}
    #       - remove: {"index": 索引} 或 {"value": 值}
    #       - search: {"value": 值, "algorithm": "linear"/"binary"}
    #       - sort: {"algorithm": "bubble"/"quick"/"merge"/等, "order": "asc"/"desc"}
    #       - filter: {"condition": 函数/表达式}
    #       - map: {"function": 函数/表达式}
    #     show_code (bool): 是否显示操作的伪代码
    #     show_complexity (bool): 是否显示操作的时间/空间复杂度
    #     interactive (bool): 是否允许用户交互干预操作
    #     step_by_step (bool): 是否逐步展示操作过程
    #     highlight_changes (bool): 是否高亮变化的元素
    #   animation (dict, 可选): 操作动画设置
    #     speed (float): 动画速度倍率
    #     pause_points (list): 动画暂停点
    #     highlight_colors (dict): 高亮颜色映射
    #     transitions (str): 过渡效果 - "smooth"(平滑), "discrete"(离散), "elastic"(弹性)
    #     narration (bool): 是否包含解释性文字

    def __init__(self, array_id, operation, properties=None, animation=None, id=None):
        props = properties or {}

        # 设置默认属性
        if props.get("parameters") is None:
            props["parameters"] = {}

        # 根据操作类型设置特定属性
        if operation == "add":
            props.setdefault("show_code", False)
            props.setdefault("highlight_changes", True)
            if "parameters" in props and "position" not in props["parameters"]:
                props["parameters"]["position"] = "end"

        elif operation == "remove":
            props.setdefault("show_code", False)
            props.setdefault("highlight_changes", True)

        elif operation == "search":
            props.setdefault("show_code", True)
            props.setdefault("show_complexity", True)
            props.setdefault("step_by_step", True)
            if "parameters" in props and "algorithm" not in props["parameters"]:
                props["parameters"]["algorithm"] = "linear"

        elif operation == "sort":
            props.setdefault("show_code", True)
            props.setdefault("show_complexity", True)
            props.setdefault("step_by_step", True)
            if "parameters" in props and "algorithm" not in props["parameters"]:
                props["parameters"]["algorithm"] = "quick"
            if "parameters" in props and "order" not in props["parameters"]:
                props["parameters"]["order"] = "asc"

        elif operation == "filter":
            props.setdefault("show_code", True)
            props.setdefault("highlight_changes", True)

        elif operation == "map":
            props.setdefault("show_code", True)
            props.setdefault("highlight_changes", True)

        # 应用默认动画
        anim = animation
        if anim is None:
            anim = {
                "type": operation,
                "duration": 1.5 if operation in ["sort", "search"] else 0.8,
                "easing": "ease-in-out",
            }

            if operation == "sort":
                anim.setdefault("speed", 1.0)
                algorithm = props.get("parameters", {}).get("algorithm", "quick")
                if algorithm == "bubble":
                    anim["speed"] = 0.5  # 冒泡排序动画慢一些，更清晰
                elif algorithm in ["merge", "quick"]:
                    anim["speed"] = 1.5  # 高效排序算法动画快一些

            elif operation == "search":
                anim.setdefault(
                    "highlight_colors",
                    {
                        "current": "#66CCFF",
                        "found": "#66FF66",
                        "not_found": "#FF6666",
                    },
                )

        # 创建操作数据对象
        operation_data = {
            "array_id": array_id,
            "operation_type": operation,
            "parameters": props["parameters"],
        }

        super().__init__("array_operation", operation_data, props, anim, id)

    def create_manim_object(self):
        """创建对应的Manim对象"""
        operation_data = self.content
        props = self.properties

        # 获取操作类型
        operation_type = operation_data.get("operation_type", "add")
        parameters = operation_data.get("parameters", {})

        # 创建操作标题
        title_text = f"{operation_type.capitalize()} Operation"
        title = Text(title_text, font_size=36, color=WHITE)

        # 创建参数显示
        param_texts = []
        for key, value in parameters.items():
            param_text = Text(f"{key}: {value}", font_size=24, color=WHITE)
            param_texts.append(param_text)

        params_group = VGroup(*param_texts).arrange(DOWN, buff=0.3)
        params_group.next_to(title, DOWN, buff=0.5)

        # 如果需要显示伪代码
        if props.get("show_code", False):
            code_title = Text("Pseudocode:", font_size=30, color=YELLOW)
            code_title.next_to(params_group, DOWN, buff=0.8)

            # 根据操作类型生成伪代码
            code_text = ""
            if operation_type == "search":
                algorithm = parameters.get("algorithm", "linear")
                if algorithm == "linear":
                    code_text = """
for i = 0 to length-1:
    if array[i] == target:
        return i
return -1  # Not found
"""
                elif algorithm == "binary":
                    code_text = """
low = 0
high = length - 1
while low <= high:
    mid = (low + high) // 2
    if array[mid] == target:
        return mid
    elif array[mid] < target:
        low = mid + 1
    else:
        high = mid - 1
return -1  # Not found
"""

            elif operation_type == "sort":
                algorithm = parameters.get("algorithm", "quick")
                if algorithm == "bubble":
                    code_text = """
for i = 0 to length-1:
    for j = 0 to length-i-1:
        if array[j] > array[j+1]:
            swap(array[j], array[j+1])
"""
                elif algorithm == "quick":
                    code_text = """
function quicksort(arr, low, high):
    if low < high:
        pivot = partition(arr, low, high)
        quicksort(arr, low, pivot-1)
        quicksort(arr, pivot+1, high)

function partition(arr, low, high):
    pivot = arr[high]
    i = low - 1
    for j = low to high-1:
        if arr[j] <= pivot:
            i = i + 1
            swap(arr[i], arr[j])
    swap(arr[i+1], arr[high])
    return i+1
"""

            # 创建代码文本
            code = Text(code_text, font_size=18, color=GREEN)
            code.next_to(code_title, DOWN, buff=0.3)

            # 如果要显示复杂度
            if props.get("show_complexity", False):
                complexity_title = Text("Complexity:", font_size=30, color=YELLOW)
                complexity_title.next_to(code, DOWN, buff=0.8)

                time_complexity = ""
                space_complexity = ""

                if operation_type == "search":
                    algorithm = parameters.get("algorithm", "linear")
                    if algorithm == "linear":
                        time_complexity = "O(n)"
                        space_complexity = "O(1)"
                    elif algorithm == "binary":
                        time_complexity = "O(log n)"
                        space_complexity = "O(1)"

                elif operation_type == "sort":
                    algorithm = parameters.get("algorithm", "quick")
                    if algorithm == "bubble":
                        time_complexity = "O(n²)"
                        space_complexity = "O(1)"
                    elif algorithm == "quick":
                        time_complexity = "O(n log n) average, O(n²) worst"
                        space_complexity = "O(log n)"

                # 创建复杂度文本
                time_text = Text(f"Time: {time_complexity}", font_size=24, color=WHITE)
                space_text = Text(f"Space: {space_complexity}", font_size=24, color=WHITE)
                complexity_group = VGroup(time_text, space_text).arrange(DOWN, buff=0.3)
                complexity_group.next_to(complexity_title, DOWN, buff=0.3)

                # 组合所有元素
                result = VGroup(title, params_group, code_title, code, complexity_title, complexity_group)
            else:
                # 没有复杂度信息
                result = VGroup(title, params_group, code_title, code)
        else:
            # 没有代码显示
            result = VGroup(title, params_group)

        return result

    def create_animation(self, mobject):
        """根据animation属性创建Manim动画"""
        anim = self.animation
        if anim is None:
            return FadeIn(mobject)

        duration = anim.get("duration", 1.0)

        # 基本动画效果
        return FadeIn(mobject, run_time=duration)


# 创建排序可视化测试类
class SortingVisualizationDemo(Scene):
    def construct(self):
        title = Text("排序算法可视化", font_size=36)
        title.to_edge(UP)
        self.play(FadeIn(title))

        # 创建一个随机数组
        numbers = [9, 5, 2, 7, 3, 8, 1, 6, 4]

        # 冒泡排序演示
        bubble_title = Text("冒泡排序", font_size=30)
        bubble_title.next_to(title, DOWN, buff=0.5)
        self.play(FadeIn(bubble_title))

        array = ArrayElement(
            numbers.copy(),
            {"type": "numeric", "style": "bar", "indexed": True},
        )
        array.visualize_sort(self, algorithm="bubble", position=ORIGIN)

        self.play(FadeOut(bubble_title))
        self.wait(0.5)

        # 选择排序演示
        selection_title = Text("选择排序", font_size=30)
        selection_title.next_to(title, DOWN, buff=0.5)
        self.play(FadeIn(selection_title))

        array = ArrayElement(
            numbers.copy(),
            {"type": "numeric", "style": "bar", "indexed": True},
        )
        array.visualize_sort(self, algorithm="selection", position=ORIGIN)

        self.play(FadeOut(selection_title))
        self.wait(0.5)

        # 插入排序演示
        insertion_title = Text("插入排序", font_size=30)
        insertion_title.next_to(title, DOWN, buff=0.5)
        self.play(FadeIn(insertion_title))

        array = ArrayElement(
            numbers.copy(),
            {"type": "numeric", "style": "bar", "indexed": True},
        )
        array.visualize_sort(self, algorithm="insertion", position=ORIGIN)

        self.play(FadeOut(insertion_title))
        self.wait(0.5)

        # 快速排序演示
        quick_title = Text("快速排序", font_size=30)
        quick_title.next_to(title, DOWN, buff=0.5)
        self.play(FadeIn(quick_title))

        array = ArrayElement(
            numbers.copy(),
            {"type": "numeric", "style": "bar", "indexed": True},
        )
        array.visualize_sort(self, algorithm="quick", position=ORIGIN)

        self.play(FadeOut(quick_title))
        self.wait(0.5)

        # 清理场景
        self.play(FadeOut(title))
        self.wait(1)


if __name__ == "__main__":
    # 运行测试用例
    config.preview = True

    # 选择要运行的测试用例
    # scene = ArrayOperationDemo()
    # scene = ArrayVisualizationDemo()
    # 测试数组可视化和操作动画
    class ArrayVisualizationDemo(Scene):
        def construct(self):
            # 创建一个数字数组（条形样式）
            numbers = [5, 2, 8, 1, 7, 3]
            number_array = ArrayElement(
                numbers,
                {"type": "numeric", "style": "bar", "indexed": True},
            )
            number_obj = number_array.create_manim_object()
            number_obj.scale(0.8).to_edge(UP)

            # 创建一个字符串数组（药丸样式）
            strings = ["apple", "banana", "orange", "grape", "kiwi"]
            string_array = ArrayElement(
                strings,
                {
                    "type": "string",
                    "style": "pill",
                    "highlight_indices": [1, 3],
                },
            )
            string_obj = string_array.create_manim_object()
            string_obj.scale(0.8).move_to(ORIGIN)

            # 创建一个对象数组（卡片样式）
            objects = [
                {"id": 1, "name": "Alice"},
                {"id": 2, "name": "Bob"},
                {"id": 3, "name": "Charlie"},
            ]
            object_array = ArrayElement(
                objects,
                {"type": "object", "style": "card", "layout": "horizontal"},
            )
            object_obj = object_array.create_manim_object()
            object_obj.scale(0.8).to_edge(DOWN)

            # 播放创建数组的动画
            self.play(FadeIn(number_obj))
            self.play(FadeIn(string_obj))
            self.play(FadeIn(object_obj))
            self.wait(1)

            # 排序操作演示
            # 为数字数组创建排序操作对象
            # sort_op = ArrayOperationElement(
            #     "number_array",
            #     "sort",
            #     {
            #         "parameters": {"algorithm": "bubble", "order": "asc"},
            #         "show_code": True,
            #         "step_by_step": True,
            #     },
            # )

            # 执行排序（手动模拟动画效果）
            sorted_numbers = sorted(numbers)
            sorted_array = ArrayElement(
                sorted_numbers,
                {"type": "numeric", "style": "bar", "indexed": True},
            )
            sorted_obj = sorted_array.create_manim_object()
            sorted_obj.scale(0.8).to_edge(UP)

            # 搜索操作演示
            search_op = ArrayOperationElement(
                "string_array",
                "search",
                {
                    "parameters": {"value": "banana", "algorithm": "linear"},
                    "show_code": True,
                    "highlight_changes": True,
                },
            )
            search_obj = search_op.create_manim_object()
            search_obj.scale(0.5).to_corner(UR)

            # 播放排序动画
            self.play(Transform(number_obj, sorted_obj))
            self.play(FadeIn(search_obj))

            # 高亮搜索结果
            highlighted_strings = ArrayElement(
                strings,
                {
                    "type": "string",
                    "style": "pill",
                    "highlight_indices": [1],
                },
            )
            highlighted_obj = highlighted_strings.create_manim_object()
            highlighted_obj.scale(0.8).move_to(ORIGIN)

            self.play(Transform(string_obj, highlighted_obj))
            self.wait(2)
