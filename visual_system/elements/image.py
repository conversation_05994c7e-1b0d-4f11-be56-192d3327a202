import os

from manim import (
    BLUE,
    DOWN,
    GREEN,
    LEFT,
    PI,
    RED,
    RIGHT,
    UP,
    WHITE,
    YELLOW,
    FadeIn,
    FadeOut,
    ImageMobject,
    Indicate,
    Rectangle,
    Rotating,
    Scene,
    Succession,
    Text,
    Transform,
    VGroup,
    config,
)

from visual_system.elements.base import BaseElement


class ImageElement(BaseElement):
    """
    Create an image element with intelligent defaults based on source characteristics

    Args:
        source (str): Image source/path
        properties (dict, optional): Image properties with these common keys:
            role (str): Image role - "hero", "thumbnail", "icon", "diagram", "photo", "background", "logo"
            alt (str): Alternative text for accessibility
            aspect (str): Aspect ratio - "square", "landscape", "portrait", "original", "16:9", "4:3", "1:1"
            fit (str): Fitting method - "contain", "cover", "fill", "scale-down"
            focus (str): Focus point - "center", "top", "left", "bottom", "right", "top-left", etc.
        animation (dict, optional): Image animation settings with these common types:
            "fade" - Fade in/out effect
            "slide" - Slide from direction (left, right, top, bottom)
            "zoom" - Zoom in/out effect
            "reveal" - Gradual reveal effect
            "parallax" - Parallax scrolling effect
            "ken-burns" - Ken Burns pan and zoom effect
            "blur" - Blur in/out effect
            "rotate" - Rotation effect
            "pulse" - Pulsing effect
        id (str, optional): Element unique identifier, auto-generated if not provided

    Examples:
        # Create a hero image
        hero = ImageElement("hero_banner.jpg", {"role": "hero"})

        # Create a logo with specific properties
        logo = ImageElement("company_logo.png", {
            "role": "logo",
            "alt": "Company Logo",
            "aspect": "original"
        })

        # Create an image with zoom animation
        animated_image = ImageElement("product_photo.jpg",
                                    {"role": "photo"},
                                    {"type": "zoom", "duration": 1.2, "scale": 1.1})
    """

    # 创建图像元素，基于源文件特征提供智能默认值
    #
    # 参数:
    #   source (str): 图像源/路径
    #   properties (dict, 可选): 图像属性，常用键值：
    #     role (str): 图像角色 - "hero"(主视觉), "thumbnail"(缩略图), "icon"(图标),
    #               "diagram"(图表), "photo"(照片), "background"(背景), "logo"(标志)
    #     alt (str): 替代文本(无障碍访问)
    #     aspect (str): 宽高比 - "square"(正方形), "landscape"(横向), "portrait"(纵向),
    #                 "original"(原始比例), "16:9", "4:3", "1:1"
    #     fit (str): 适应方式 - "contain"(包含), "cover"(覆盖), "fill"(填充), "scale-down"(缩小适应)
    #     focus (str): 焦点 - "center"(中心), "top"(顶部), "left"(左侧), 等
    #   animation (dict, 可选): 图像动效设置，常用类型：
    #     "fade" - 淡入/淡出效果
    #     "slide" - 从指定方向滑入(左、右、上、下)
    #     "zoom" - 放大/缩小效果
    #     "reveal" - 渐进显示效果
    #     "parallax" - 视差滚动效果
    #     "ken-burns" - 肯·伯恩斯平移缩放效果
    #     "blur" - 模糊淡入/淡出效果
    #     "rotate" - 旋转效果
    #     "pulse" - 脉冲效果
    #   id (str, 可选): 元素唯一标识符，不提供则自动生成

    def __init__(self, source, properties=None, animation=None, id=None):
        props = properties or {}

        # 从文件名推断角色
        if props.get("role") is None:
            lower_source = source.lower()
            if any(kw in lower_source for kw in ["logo", "brand", "icon"]):
                props["role"] = "logo"
            elif any(kw in lower_source for kw in ["diagram", "chart", "flow", "graph"]):
                props["role"] = "diagram"
            elif any(kw in lower_source for kw in ["bg", "background", "pattern", "texture"]):
                props["role"] = "background"
            else:
                props["role"] = "photo"

        # 根据角色设置合理的默认属性
        if props.get("role") == "hero":
            props.setdefault("aspect", "landscape")
            props.setdefault("fit", "cover")
        elif props.get("role") == "logo":
            props.setdefault("aspect", "original")
            props.setdefault("fit", "contain")
        elif props.get("role") == "icon":
            props.setdefault("aspect", "square")

        # 应用默认动效
        anim = animation
        if anim is None:
            if props.get("role") == "hero":
                anim = {"type": "zoom", "duration": 1.2, "scale": 1.05, "direction": "in"}
            elif props.get("role") == "logo":
                anim = {"type": "fade", "duration": 0.8}
            elif props.get("role") == "photo":
                anim = {"type": "reveal", "duration": 0.9, "direction": "bottom"}
            elif props.get("role") == "diagram":
                anim = {"type": "fade", "duration": 1.0}
            elif props.get("role") == "background":
                anim = {"type": "fade", "duration": 1.5}

        super().__init__("image", source, props, anim, id)

    def create_manim_object(self):
        """创建对应的Manim对象"""
        props = self.properties
        source_path = self.content

        # 处理图像内容
        alt_text = props.get("alt", os.path.basename(source_path))
        aspect = props.get("aspect", "original")
        role = props.get("role", "photo")
        # fit = props.get("fit", "contain")
        # focus = props.get("focus", "center")

        # 加载图像
        try:
            # 确保文件路径存在
            if not os.path.exists(source_path):
                # 如果文件不存在，创建一个占位符矩形
                print(f"Warning: Image file not found: {source_path}")
                img_obj = self._create_placeholder(alt_text, aspect, role)
            else:
                # 创建ImageMobject
                img_obj = ImageMobject(source_path)

                # 应用宽高比设置
                if aspect != "original":
                    if aspect == "square":
                        # 正方形裁剪
                        height = img_obj.height
                        img_obj.width = height
                    elif aspect == "16:9":
                        # 16:9宽高比
                        img_obj.stretch_to_fit_width(img_obj.height * 16 / 9)
                    elif aspect == "4:3":
                        # 4:3宽高比
                        img_obj.stretch_to_fit_width(img_obj.height * 4 / 3)

                # 根据角色调整尺寸
                if role == "icon":
                    img_obj.scale(0.5)
                elif role == "hero":
                    img_obj.scale(1.5)

                # 如果有替代文本，添加标签
                if alt_text:
                    alt = Text(alt_text, font_size=24).next_to(img_obj, DOWN)
                    img_obj = VGroup(img_obj, alt)

        except Exception as e:
            # 图像加载失败，创建一个占位符
            print(f"Error loading image: {e}")
            img_obj = self._create_placeholder(alt_text, aspect, role)

        return img_obj

    def _create_placeholder(self, alt_text, aspect, role):
        """创建一个占位符矩形"""
        # 根据宽高比设置矩形尺寸
        width, height = 4, 3
        if aspect == "square":
            width, height = 3, 3
        elif aspect == "portrait":
            width, height = 3, 4
        elif aspect == "16:9":
            width, height = 16 / 9 * 3, 3
        elif aspect == "4:3":
            width, height = 4, 3

        # 根据角色设置颜色
        color = WHITE
        if role == "hero":
            color = BLUE
        elif role == "logo":
            color = RED
        elif role == "icon":
            color = GREEN

        # 创建矩形
        rect = Rectangle(
            width=width,
            height=height,
            fill_opacity=0.2,
            color=color,
        )

        # 添加替代文本
        if alt_text:
            alt = Text(alt_text, font_size=24).move_to(rect)
            return VGroup(rect, alt)

        return rect

    def create_animation(self, mobject):
        """根据animation属性创建Manim动画"""
        anim = self.animation
        if anim is None:
            return FadeIn(mobject)

        anim_type = anim.get("type", "fade")
        duration = anim.get("duration", 1.0)

        if anim_type == "fade":
            return FadeIn(mobject, run_time=duration)

        elif anim_type == "slide":
            direction = anim.get("direction", "right")

            # 设置正确的滑入方向
            if direction == "right":
                shift_vector = LEFT * 1.0
            elif direction == "left":
                shift_vector = RIGHT * 1.0
            elif direction == "top":
                shift_vector = DOWN * 1.0
            elif direction == "bottom":
                shift_vector = UP * 1.0
            else:
                shift_vector = LEFT * 1.0

            return FadeIn(mobject, shift=shift_vector, run_time=duration)

        elif anim_type == "zoom":
            direction = anim.get("direction", "in")
            scale = anim.get("scale", 1.2)

            if direction == "in":
                # 从小到大
                start_scale = mobject.copy().scale(1 / scale)
                return Transform(start_scale, mobject, run_time=duration)
            else:
                # 从大到小
                start_scale = mobject.copy().scale(scale)
                return Transform(start_scale, mobject, run_time=duration)

        elif anim_type == "reveal":
            direction = anim.get("direction", "bottom")

            if direction == "bottom":
                # 从底部揭示
                mobject_copy = mobject.copy()
                anim1 = FadeIn(mobject, run_time=duration / 2)
                anim2 = FadeOut(mobject_copy, run_time=duration / 2, shift=UP)
                return Succession(anim1, anim2)
            else:
                # 默认揭示方式
                return FadeIn(mobject, run_time=duration)

        elif anim_type == "rotate":
            angle = anim.get("angle", PI / 4)
            return Rotating(mobject, radians=angle, run_time=duration)

        elif anim_type == "pulse":
            # 创建脉冲效果
            return Indicate(mobject, color=YELLOW, run_time=duration)

        # 默认为淡入效果
        return FadeIn(mobject, run_time=duration)


# 测试场景
class ImageElementDemo(Scene):
    def construct(self):
        # 模拟图像路径（注意：这些文件应该实际存在才能正常工作）
        # 这里使用占位符路径，实际运行时会使用占位符矩形
        hero_path = "hero_banner.jpg"
        logo_path = "company_logo.png"
        photo_path = "product_photo.jpg"

        # 创建示例图像元素
        hero = ImageElement(hero_path, {"role": "hero", "alt": "Hero Banner"})
        hero_obj = hero.create_manim_object()
        hero_obj.to_edge(UP, buff=1)
        hero_anim = hero.create_animation(hero_obj)

        # 创建logo
        logo = ImageElement(
            logo_path,
            {
                "role": "logo",
                "alt": "Company Logo",
                "aspect": "square",
            },
        )
        logo_obj = logo.create_manim_object()
        logo_obj.next_to(hero_obj, DOWN, buff=1)
        logo_anim = logo.create_animation(logo_obj)

        # 创建带缩放动画的图像
        animated_image = ImageElement(
            photo_path,
            {"role": "photo", "alt": "Product Photo"},
            {"type": "zoom", "duration": 1.2, "scale": 1.1},
        )
        animated_obj = animated_image.create_manim_object()
        animated_obj.next_to(logo_obj, DOWN, buff=1)
        animated_anim = animated_image.create_animation(animated_obj)

        # 播放动画
        self.play(hero_anim)
        self.play(logo_anim)
        self.play(animated_anim)
        self.wait(1)


if __name__ == "__main__":
    # 运行测试用例
    config.preview = True
    scene = ImageElementDemo()
    scene.render()
