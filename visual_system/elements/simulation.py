from visual_system.elements.base import BaseElement


class SimulationElement(BaseElement):
    """
    Create a physics or data simulation element with intelligent defaults based on simulation type

    Args:
        sim_type (str): Type of simulation - "particles", "fluid", "gravity", "graph", "wave", "pendulum", "spring", "dataflow"
        properties (dict, optional): Simulation properties with these common keys:
            complexity (str): Simulation complexity - "simple", "moderate", "complex"
            particles (int): Number of particles/entities in simulation (for particle-based sims)
            speed (float): Simulation speed factor (1.0 = real-time)
            forces (dict): Physics forces - {"gravity": 9.8, "friction": 0.1, "wind": [0.5, 0.0]}
            bounds (dict): Simulation boundaries - {"width": 800, "height": 600}
            colors (list): Color palette for visualization
            interactive (bool): Whether simulation can be interacted with
        animation (dict, optional): Simulation animation settings with these common types:
            "start" - Starting animation for simulation
            "reset" - Reset simulation state
            "transition" - Transition between simulation states
            "highlight" - Highlight certain elements in simulation
            "slow-motion" - Slow down simulation
            "accelerate" - Speed up simulation
        id (str, optional): Element unique identifier, auto-generated if not provided

    Examples:
        # Create a particle system
        particles = SimulationElement("particles", {
            "complexity": "moderate",
            "particles": 200,
            "interactive": True
        })

        # Create a data flow simulation with specific animation
        dataflow = SimulationElement("dataflow",
                                   {"complexity": "complex", "speed": 0.8},
                                   {"type": "start", "duration": 2.0, "easing": "ease-in-out"})
    """

    # 创建物理或数据模拟元素，基于模拟类型提供智能默认值
    #
    # 参数:
    #   sim_type (str): 模拟类型 - "particles"(粒子), "fluid"(流体), "gravity"(重力),
    #                  "graph"(图表), "wave"(波形), "pendulum"(钟摆), "spring"(弹簧), "dataflow"(数据流)
    #   properties (dict, 可选): 模拟属性，常用键值：
    #     complexity (str): 模拟复杂度 - "simple"(简单), "moderate"(中等), "complex"(复杂)
    #     particles (int): 模拟中的粒子/实体数量(用于基于粒子的模拟)
    #     speed (float): 模拟速度因子(1.0 = 实时)
    #     forces (dict): 物理力 - {"gravity": 9.8, "friction": 0.1, "wind": [0.5, 0.0]}
    #     bounds (dict): 模拟边界 - {"width": 800, "height": 600}
    #     colors (list): 可视化调色板
    #     interactive (bool): 模拟是否可交互
    #   animation (dict, 可选): 模拟动画设置，常用类型：
    #     "start" - 模拟启动动画
    #     "reset" - 重置模拟状态
    #     "transition" - 模拟状态之间的过渡
    #     "highlight" - 高亮模拟中的特定元素
    #     "slow-motion" - 放慢模拟
    #     "accelerate" - 加速模拟
    #   id (str, 可选): 元素唯一标识符，不提供则自动生成

    def __init__(self, sim_type, properties=None, animation=None, id=None):
        props = properties or {}

        # 设置默认复杂度
        props.setdefault("complexity", "moderate")

        # 根据模拟类型设置默认属性
        if sim_type == "particles":
            props.setdefault("particles", 100)
            props.setdefault("forces", {"gravity": 0.1, "friction": 0.02})
            props.setdefault("colors", ["#4285F4", "#34A853", "#FBBC05", "#EA4335"])
        elif sim_type == "fluid":
            props.setdefault("viscosity", 1.0)
            props.setdefault("resolution", 64)
            props.setdefault("colors", ["#0099FF", "#00CCFF", "#00FFFF"])
        elif sim_type == "gravity":
            props.setdefault("bodies", 5)
            props.setdefault("gravity", 9.8)
            props.setdefault("trailEffect", True)
        elif sim_type == "graph":
            props.setdefault("nodes", 20)
            props.setdefault("edges", 30)
            props.setdefault("layout", "force-directed")
        elif sim_type == "wave":
            props.setdefault("amplitude", 50)
            props.setdefault("frequency", 0.5)
            props.setdefault("damping", 0.02)
        elif sim_type == "pendulum":
            props.setdefault("length", 200)
            props.setdefault("gravity", 9.8)
            props.setdefault("damping", 0.05)
        elif sim_type == "spring":
            props.setdefault("stiffness", 0.1)
            props.setdefault("restLength", 100)
            props.setdefault("damping", 0.1)
        elif sim_type == "dataflow":
            props.setdefault("flowRate", 1.0)
            props.setdefault("dataSources", 3)
            props.setdefault("dataTransformations", 2)

        # 设置通用属性默认值
        props.setdefault("speed", 1.0)
        props.setdefault("bounds", {"width": 800, "height": 600})
        props.setdefault("interactive", False)

        # 应用默认动效
        anim = animation
        if anim is None:
            if sim_type == "particles" or sim_type == "fluid":
                anim = {"type": "start", "duration": 1.5, "easing": "ease-out"}
            elif sim_type == "gravity" or sim_type == "pendulum":
                anim = {"type": "start", "duration": 2.0, "delay": 0.5}
            elif sim_type == "graph" or sim_type == "dataflow":
                anim = {"type": "transition", "duration": 1.8, "stages": 3}
            elif sim_type == "wave":
                anim = {"type": "start", "duration": 1.2, "easing": "ease-in-out"}
            else:
                anim = {"type": "start", "duration": 1.0}

        super().__init__("simulation", sim_type, props, anim, id)
