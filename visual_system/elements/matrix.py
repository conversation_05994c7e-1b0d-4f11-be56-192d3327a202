from visual_system.elements.base import BaseElement


class MatrixElement(BaseElement):
    """
    Create a matrix visualization element with intelligent defaults

    Args:
        data (list/array): Matrix data (2D array)
        properties (dict, optional): Matrix properties with these common keys:
            brackets (str): Bracket style - "square", "round", "curly", "none"
            highlight_cells (dict/list): Cell coordinates to highlight
            labels (dict): Row and column labels
            cell_size (str): Size of cells - "small", "medium", "large"
            determinant (bool): Whether to show determinant
            colormap (str/dict): Color mapping for values
            annotate (bool): Whether to annotate with values
        animation (dict, optional): Matrix animation settings
        id (str, optional): Element unique identifier, auto-generated if not provided

    Returns:
        dict: Configured matrix element

    Examples:
        # Create a simple 2x2 matrix
        matrix_2x2 = MatrixElement([
            [1, 2],
            [3, 4]
        ])

        # Create a matrix with specific settings
        custom_matrix = MatrixElement([
            [10, 20, 30],
            [40, 50, 60]
        ], {
            "brackets": "square",
            "highlight_cells": {"(0,1)": "#FFCC00"},
            "labels": {"rows": ["R1", "R2"], "cols": ["A", "B", "C"]}
        })
    """
    # 创建矩阵可视化元素，提供智能默认值
    #
    # 参数:
    #   data (list/array): 矩阵数据(二维数组)
    #   properties (dict, 可选): 矩阵属性，常用键值：
    #     brackets (str): 括号样式 - "square"(方括号), "round"(圆括号),
    #                   "curly"(花括号), "none"(无括号)
    #     highlight_cells (dict/list): 要高亮的单元格坐标
    #     labels (dict): 行和列标签
    #     cell_size (str): 单元格大小 - "small"(小), "medium"(中), "large"(大)
    #     determinant (bool): 是否显示行列式
    #     colormap (str/dict): 数值的颜色映射
    #     annotate (bool): 是否用数值进行标注
    #   animation (dict, 可选): 矩阵动效设置
    #   id (str, 可选): 元素唯一标识符，不提供则自动生成

    def __init__(self, data, properties=None, animation=None, id=None):
        props = properties or {}
        
        # 设置合理的默认值
        if props.get("brackets") is None:
            # 根据矩阵大小设置括号类型
            if isinstance(data, list) and len(data) == 1 and isinstance(data[0], list) and len(data[0]) > 1:
                # 向量使用圆括号
                props["brackets"] = "round"
            else:
                # 矩阵使用方括号
                props["brackets"] = "square"
        
        # 矩阵尺寸相关设置
        rows = len(data) if isinstance(data, list) else 0
        cols = len(data[0]) if rows > 0 and isinstance(data[0], list) else 0
        
        # 如果是方阵，增加行列式选项
        if rows == cols and rows > 1:
            props.setdefault("determinant", cols <= 3)  # 只对小型方阵默认显示行列式
        
        # 根据矩阵大小设置是否默认注释
        props.setdefault("annotate", rows * cols <= 16)  # 只对小型矩阵默认显示数值标注
        
        # 应用默认动效
        anim = animation
        if anim is None:
            anim = {"type": "fade", "duration": 0.8}

        super().__init__("matrix", data, props, anim, id)
