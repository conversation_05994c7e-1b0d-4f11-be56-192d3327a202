from visual_system.elements.base import BaseElement


class AudioElement(BaseElement):
    """
    Create an audio element with intelligent defaults based on source characteristics

    Args:
        source (str): Audio source/path
        properties (dict, optional): Audio properties with these common keys:
            role (str): Audio role - "music", "speech", "sound_effect", "background", "narration", "ambient"
            controls (bool): Whether to display controls
            autoplay (bool): Whether to autoplay
            loop (bool): Whether to loop
            volume (float): Initial volume (0.0-1.0)
            duration (float): Duration in seconds (auto-detected when possible)
            visualizer (str): Visualization style - "waveform", "spectrum", "none"
        animation (dict, optional): Audio container animation settings
        id (str, optional): Element unique identifier, auto-generated if not provided

    Returns:
        dict: Configured audio element

    Examples:
        # Create a music track with controls
        music = AudioElement("background_music.mp3", {"role": "music"})

        # Create a speech audio with specific settings
        speech = AudioElement("interview.mp3", {
            "role": "speech",
            "visualizer": "waveform",
            "controls": True,
            "volume": 0.8
        })
    """
    # 创建音频元素，基于源文件特征提供智能默认值
    #
    # 参数:
    #   source (str): 音频源/路径
    #   properties (dict, 可选): 音频属性，常用键值：
    #     role (str): 音频角色 - "music"(音乐), "speech"(语音), "sound_effect"(音效),
    #                "background"(背景音), "narration"(旁白), "ambient"(环境音)
    #     controls (bool): 是否显示控件
    #     autoplay (bool): 是否自动播放
    #     loop (bool): 是否循环播放
    #     volume (float): 初始音量(0.0-1.0)
    #     duration (float): 持续时间(秒)，可能时自动检测
    #     visualizer (str): 可视化样式 - "waveform"(波形), "spectrum"(频谱), "none"(无)
    #   animation (dict, 可选): 音频容器动效设置
    #   id (str, 可选): 元素唯一标识符，不提供则自动生成

    def __init__(self, source, properties=None, animation=None, id=None):
        props = properties or {}

        # 从文件名推断角色
        if props.get("role") is None:
            lower_source = source.lower()
            if any(kw in lower_source for kw in ["music", "song", "track", "beat"]):
                props["role"] = "music"
            elif any(kw in lower_source for kw in ["speech", "talk", "interview", "podcast"]):
                props["role"] = "speech"
            elif any(kw in lower_source for kw in ["effect", "sfx", "sound"]):
                props["role"] = "sound_effect"
            elif any(kw in lower_source for kw in ["narration", "narrate", "narrator"]):
                props["role"] = "narration"
            elif any(kw in lower_source for kw in ["ambient", "atmosphere", "environment"]):
                props["role"] = "ambient"
            else:
                props["role"] = "music"  # 默认为音乐

        # 根据角色设置合理的默认属性
        if props.get("role") == "music":
            props.setdefault("controls", True)
            props.setdefault("visualizer", "waveform")
            props.setdefault("loop", True)
        elif props.get("role") == "speech" or props.get("role") == "narration":
            props.setdefault("controls", True)
            props.setdefault("visualizer", "waveform")
            props.setdefault("loop", False)
        elif props.get("role") == "sound_effect":
            props.setdefault("controls", False)
            props.setdefault("visualizer", "none")
            props.setdefault("loop", False)
        elif props.get("role") == "background" or props.get("role") == "ambient":
            props.setdefault("controls", False)
            props.setdefault("autoplay", True)
            props.setdefault("loop", True)
            props.setdefault("volume", 0.5)

        # 应用默认动效
        anim = animation
        if anim is None and props.get("visualizer") != "none":
            anim = {"type": "fade", "duration": 0.8}

        super().__init__("audio", source, props, anim, id)
