from visual_system.elements.base import BaseElement


class Model3DElement(BaseElement):
    """
    Create a 3D model element with intelligent defaults based on model characteristics

    Args:
        source (str/dict): Model source path or configuration object
        properties (dict, optional): Model properties with these common keys:
            format (str): Model format - "gltf", "obj", "stl", "fbx", "usdz"
            controls (str): Control type - "orbit", "fly", "walk", "none", "custom"
            lighting (str/dict): Lighting setup - "studio", "outdoor", "soft", "dramatic", or custom
            background (str/dict): Background setting - color, environment map, or gradient
            animation_clips (list): Animation clips to include or play
            auto_rotate (bool): Whether to auto-rotate the model
            camera (dict): Camera settings - position, target, fov
            materials (dict): Material overrides
            scale (float/vector): Scale factor or vector
            position (vector): Initial position
            rotation (vector): Initial rotation in degrees
            interactive (bool): Whether viewer can interact with model
        animation (dict, optional): Model animation settings
        id (str, optional): Element unique identifier, auto-generated if not provided

    Returns:
        dict: Configured 3D model element

    Examples:
        # Create a basic 3D model
        basic_model = Model3DElement("model/robot.glb")

        # Create a model with specific properties
        detailed_model = Model3DElement("models/car.glb", {
            "controls": "orbit",
            "auto_rotate": True,
            "camera": {
                "position": [5, 2, 5],
                "target": [0, 0, 0],
                "fov": 45
            },
            "lighting": "studio",
            "background": "#f5f5f5"
        })

        # Create a model with animation settings
        animated_model = Model3DElement("models/character.glb", {
            "animation_clips": ["walk", "idle"],
            "materials": {
                "body": {"color": "#a0522d"},
                "eyes": {"emissive": "#ffffff", "emissiveIntensity": 0.5}
            }
        }, {
            "type": "reveal",
            "duration": 1.5
        })
    """
    # 创建3D模型元素，基于模型特征提供智能默认值
    #
    # 参数:
    #   source (str/dict): 模型源路径或配置对象
    #   properties (dict, 可选): 模型属性，常用键值：
    #     format (str): 模型格式 - "gltf", "obj", "stl", "fbx", "usdz"
    #     controls (str): 控制类型 - "orbit"(轨道控制), "fly"(飞行控制),
    #                   "walk"(行走控制), "none"(无控制), "custom"(自定义)
    #     lighting (str/dict): 光照设置 - "studio"(工作室), "outdoor"(户外),
    #                        "soft"(柔和), "dramatic"(戏剧性), 或自定义配置
    #     background (str/dict): 背景设置 - 颜色、环境贴图或渐变
    #     animation_clips (list): 要包含或播放的动画片段
    #     auto_rotate (bool): 是否自动旋转模型
    #     camera (dict): 相机设置 - 位置、目标、视场角
    #     materials (dict): 材质覆盖
    #     scale (float/vector): 缩放因子或向量
    #     position (vector): 初始位置
    #     rotation (vector): 初始旋转(角度)
    #     interactive (bool): 观看者是否可与模型交互
    #   animation (dict, 可选): 模型动效设置
    #   id (str, 可选): 元素唯一标识符，不提供则自动生成

    def __init__(self, source, properties=None, animation=None, id=None):
        props = properties or {}
        
        # 推断模型格式
        if props.get("format") is None and isinstance(source, str):
            extension = source.lower().split(".")[-1]
            if extension in ["gltf", "glb"]:
                props["format"] = "gltf"
            elif extension == "obj":
                props["format"] = "obj"
            elif extension == "stl":
                props["format"] = "stl"
            elif extension == "fbx":
                props["format"] = "fbx"
            elif extension == "usdz":
                props["format"] = "usdz"
            else:
                props["format"] = "gltf"  # 默认格式
        
        # 设置默认属性
        # 默认使用轨道控制
        props.setdefault("controls", "orbit")
        
        # 默认光照设置
        props.setdefault("lighting", "studio")
        
        # 默认背景
        props.setdefault("background", "#f5f5f5")
        
        # 默认为交互式
        props.setdefault("interactive", True)
        
        # 应用默认动效
        anim = animation
        if anim is None:
            anim = {"type": "reveal", "duration": 1.2}

        super().__init__("model3d", source, props, anim, id)
