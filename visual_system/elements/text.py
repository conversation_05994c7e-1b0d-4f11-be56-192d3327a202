from manim import (
    DOWN,
    LEFT,
    RED,
    RIGHT,
    UP,
    WHITE,
    YELLOW,
    ApplyWave,
    FadeIn,
    Indicate,
    LaggedStart,
    Scene,
    SurroundingRectangle,
    Text,
    VGroup,
    Write,
    config,
)

from visual_system.elements.base import BaseElement


class TextElement(BaseElement):
    """
    Create a text element with intelligent defaults based on content characteristics

    Args:
        content (str): Text content
        properties (dict, optional): Text properties with these common keys:
            role (str): Text role - "title", "heading", "subheading", "body", "caption", "quote", "note"
            style (str): Style - "normal", "bold", "italic", "code", "link", "highlight"
            size (str): Size - "small", "medium", "large", "xlarge"
            align (str): Alignment - "left", "center", "right", "justify"
            color (str): Color value
        animation (dict, optional): Text animation settings with these common types:
            "typewriter" - Typewriter effect, character by character
            "fade" - Fade in/out effect
            "slide" - Slide from direction
            "scale" - Scale up/down effect
            "highlight" - Temporary highlight effect
            "wave" - Wave/flowing text effect
            "blur" - Blur in/out effect
            "color" - Color change effect
            "split" - Split by character/word effect
        id (str, optional): Element unique identifier, auto-generated if not provided

    Returns:
        dict: Configured text element

    Examples:
        # Create a title
        title = TextElement("Document Title", {"role": "title"})

        # Create body text
        body = TextElement("This is the main content of the document.")

        # Create a highlighted note
        note = TextElement("Important information", {"role": "note", "style": "highlight"})

        # Create text with typewriter animation
        animated_text = TextElement("This text appears gradually",
                                  {"role": "body"},
                                  {"type": "typewriter", "duration": 2.0})
    """

    # 创建文本元素，基于内容特征提供智能默认值
    #
    # 参数:
    #   content (str): 文本内容
    #   properties (dict, 可选): 文本属性，常用键值：
    #     role (str): 文本角色 - "title"(标题), "heading"(小标题), "subheading"(子标题),
    #                "body"(正文), "caption"(说明文字), "quote"(引用), "note"(注释)
    #     style (str): 风格 - "normal"(普通), "bold"(粗体), "italic"(斜体),
    #                "code"(代码), "link"(链接), "highlight"(高亮)
    #     size (str): 大小 - "small"(小), "medium"(中), "large"(大), "xlarge"(特大)
    #     align (str): 对齐方式 - "left"(左对齐), "center"(居中), "right"(右对齐), "justify"(两端对齐)
    #     color (str): 颜色值
    #   animation (dict, 可选): 文本动效设置，常用类型：
    #     "typewriter" - 打字机效果，逐字符显示
    #     "fade" - 淡入/淡出效果
    #     "slide" - 从指定方向滑入
    #     "scale" - 缩放效果
    #     "highlight" - 临时高亮效果
    #     "wave" - 波浪/流动文字效果
    #     "blur" - 模糊淡入/淡出效果
    #     "color" - 颜色变化效果
    #     "split" - 按字符/单词分裂效果
    #   id (str, 可选): 元素唯一标识符，不提供则自动生成

    def __init__(self, content, properties=None, animation=None, id=None):
        props = properties or {}

        # 基于内容特征设置合理的默认值
        if props.get("role") is None:
            if len(content) < 50:
                if content.isupper() or (len(content) < 80 and content.endswith((":", "："))):
                    props["role"] = "heading"
                else:
                    props["role"] = "body"
            else:
                props["role"] = "body"

        # 基于角色设置合理的默认属性
        if props.get("role") == "title":
            props.setdefault("size", "xlarge")
            props.setdefault("style", "bold")
        elif props.get("role") == "heading":
            props.setdefault("size", "large")
            props.setdefault("style", "bold")
        elif props.get("role") == "caption":
            props.setdefault("size", "small")

        # 为特定角色应用默认动画
        anim = animation
        if anim is None:
            if props.get("role") in ["title", "heading"]:
                anim = {"type": "fade", "duration": 0.8}
            elif props.get("role") == "quote":
                anim = {"type": "scale", "duration": 0.6, "direction": "in"}
            elif props.get("role") == "body" and len(content) < 100:
                anim = {"type": "typewriter", "duration": 1.5}
            elif props.get("role") == "note":
                anim = {"type": "highlight", "duration": 1.0, "color": "#ffffbb"}

        super().__init__("text", content, props, anim, id)

    def create_manim_object(self):
        """创建对应的Manim对象"""
        props = self.properties
        content = self.content

        # 根据角色设置尺寸
        font_size = 22  # 默认大小
        if props.get("size") == "xlarge":
            font_size = 40
        elif props.get("size") == "large":
            font_size = 32
        elif props.get("size") == "medium":
            font_size = 22
        elif props.get("size") == "small":
            font_size = 18

        # 设置颜色
        color = props.get("color", WHITE)

        # 创建Text对象
        text_obj = Text(
            content,
            font_size=font_size,
            color=color,
            weight="BOLD" if props.get("style") == "bold" else "NORMAL",
            slant="ITALIC" if props.get("style") == "italic" else "NORMAL",
        )

        # 设置对齐方式
        align = props.get("align", "left")
        if align == "center":
            text_obj.center()
        elif align == "right":
            text_obj.to_edge(RIGHT)
        elif align == "left":
            text_obj.to_edge(LEFT)

        # 处理高亮样式
        if props.get("style") == "highlight":
            # 创建背景
            highlight_color = YELLOW
            if props.get("role") == "note":
                highlight_color = YELLOW

            # 创建背景矩形
            background = SurroundingRectangle(
                text_obj,
                color=highlight_color,
                fill_opacity=0.3,
                buff=0.2,
            )
            text_obj = VGroup(background, text_obj)

        return text_obj

    def create_animation(self, mobject):
        """根据animation属性创建Manim动画"""
        anim = self.animation
        if anim is None:
            return FadeIn(mobject)

        anim_type = anim.get("type", "fade")
        duration = anim.get("duration", 1.0)

        if anim_type == "typewriter":
            return Write(mobject, run_time=duration)

        elif anim_type == "fade":
            return FadeIn(mobject, run_time=duration)

        elif anim_type == "slide":
            direction = anim.get("direction", "right")

            # 直接根据direction返回正确的FadeIn动画
            if direction == "right":
                return FadeIn(mobject, shift=LEFT * 1.0, run_time=duration)
            elif direction == "left":
                return FadeIn(mobject, shift=RIGHT * 1.0, run_time=duration)
            elif direction == "top":
                return FadeIn(mobject, shift=DOWN * 1.0, run_time=duration)
            elif direction == "bottom":
                return FadeIn(mobject, shift=UP * 1.0, run_time=duration)
            else:
                # 默认情况
                return FadeIn(mobject, run_time=duration)

        elif anim_type == "scale":
            from manim import GrowFromCenter

            return GrowFromCenter(mobject, run_time=duration)

        elif anim_type == "highlight":
            # 使用闪光效果
            return Indicate(mobject, color=YELLOW, run_time=duration)

        elif anim_type == "wave":
            # 实现波浪效果
            return ApplyWave(mobject, run_time=duration)

        elif anim_type == "blur":
            # 改用淡入淡出效果替代模糊效果
            from manim import FadeOut, Succession

            temp = mobject.copy()
            return Succession(
                FadeOut(mobject, run_time=duration / 2),
                FadeIn(temp, run_time=duration / 2),
            )

        elif anim_type == "color":
            # 实现颜色变化效果
            target_color = anim.get("color", YELLOW)
            # 使用两种方式，确保至少一种生效
            try:
                return mobject.animate.set_color(target_color)
            except Exception:
                # 如果新语法不行，尝试传统方式
                from manim import ApplyMethod

                return ApplyMethod(mobject.set_color, target_color, run_time=duration)

        elif anim_type == "split":
            # 分裂效果，更安全的实现
            try:
                if hasattr(mobject, "submobjects") and len(mobject.submobjects) > 0:
                    submobjects = list(mobject.submobjects)
                    if len(submobjects) > 0:
                        return LaggedStart(
                            *[FadeIn(letter, shift=DOWN * 0.5) for letter in submobjects],
                            lag_ratio=0.1,
                            run_time=duration,
                        )
            except Exception:
                pass
            # 如果上面的实现不成功，回退到基本动画
            return FadeIn(mobject, run_time=duration)

        # 默认动画
        return FadeIn(mobject, run_time=duration)


# 测试场景
class TextElementDemo(Scene):
    def construct(self):
        # 测试各种类型的文本元素

        # 标题
        title = TextElement("Feynman Visual System", {"role": "title", "align": "center"})
        title_obj = title.create_manim_object()
        title_obj.to_edge(UP, buff=1)
        title_anim = title.create_animation(title_obj)

        # 标题
        heading = TextElement("Text Element Types:", {"role": "heading"})
        heading_obj = heading.create_manim_object()
        heading_obj.next_to(title_obj, DOWN, buff=0.8)
        heading_obj.to_edge(LEFT, buff=1)
        heading_anim = heading.create_animation(heading_obj)

        # 正文
        body = TextElement("This is a body text that shows normal content formatting.")
        body_obj = body.create_manim_object()
        body_obj.next_to(heading_obj, DOWN, buff=0.5)
        body_obj.to_edge(LEFT, buff=1)
        body_anim = body.create_animation(body_obj)

        # 带样式文本
        styles = [
            TextElement("Bold Style", {"style": "bold"}),
            TextElement("Italic Style", {"style": "italic"}),
            TextElement("Highlighted Text", {"style": "highlight"}),
        ]

        style_objs = []
        style_anims = []

        for i, style_elem in enumerate(styles):
            obj = style_elem.create_manim_object()
            if i == 0:
                obj.next_to(body_obj, DOWN, buff=0.5)
            else:
                obj.next_to(style_objs[i - 1], DOWN, buff=0.3)
            obj.to_edge(LEFT, buff=2)
            style_objs.append(obj)
            style_anims.append(style_elem.create_animation(obj))

        # 注释文本
        note = TextElement("Important Note: This is highlighted automatically", {"role": "note"})
        note_obj = note.create_manim_object()
        note_obj.next_to(style_objs[-1], DOWN, buff=0.7)
        note_obj.to_edge(LEFT, buff=1)
        note_anim = note.create_animation(note_obj)

        # 不同动画类型
        animations_heading = TextElement("Animation Types:", {"role": "heading"})
        animations_heading_obj = animations_heading.create_manim_object()
        animations_heading_obj.next_to(note_obj, DOWN, buff=0.8)
        animations_heading_obj.to_edge(LEFT, buff=1)
        animations_heading_anim = animations_heading.create_animation(animations_heading_obj)

        animation_texts = [
            TextElement("Typewriter Effect", {}, {"type": "typewriter", "duration": 1.5}),
            TextElement("Fade Effect", {}, {"type": "fade", "duration": 0.8}),
            TextElement("Slide From Left", {}, {"type": "slide", "direction": "right", "duration": 0.8}),
            TextElement("Scale Effect", {}, {"type": "scale", "duration": 0.8}),
            TextElement("Highlight Effect", {}, {"type": "highlight", "duration": 1.2}),
            TextElement("Wave Effect", {}, {"type": "wave", "duration": 1.0}),
            TextElement("Blur Effect", {}, {"type": "blur", "duration": 1.0}),
            TextElement("Color Change", {}, {"type": "color", "duration": 1.0, "color": RED}),
            TextElement("Split Effect", {}, {"type": "split", "duration": 1.5}),
        ]

        anim_objs = []
        anim_anims = []

        for i, anim_elem in enumerate(animation_texts):
            obj = anim_elem.create_manim_object()
            if i == 0:
                obj.next_to(animations_heading_obj, DOWN, buff=0.5)
            else:
                obj.next_to(anim_objs[i - 1], DOWN, buff=0.3)
            obj.to_edge(LEFT, buff=2)
            anim_objs.append(obj)
            anim_anims.append(anim_elem.create_animation(obj))

        # 播放所有动画
        self.play(title_anim)
        self.play(heading_anim)
        self.play(body_anim)

        # 播放样式动画
        for anim in style_anims:
            self.play(anim)

        self.play(note_anim)
        self.play(animations_heading_anim)

        # 播放各种动画效果
        for anim in anim_anims:
            self.play(anim)

        self.wait(1)


if __name__ == "__main__":
    # 运行测试用例
    config.preview = True
    scene = TextElementDemo()
    scene.render()
