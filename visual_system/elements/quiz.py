"""
测验元素模块
"""

from visual_system.elements.base import BaseElement


class QuizElement(BaseElement):
    """
    测验元素类
    
    用于创建交互式测验和问题
    """
    
    def __init__(self, questions, properties=None, animation=None, id=None):
        """
        初始化测验元素
        
        Args:
            questions (list): 问题列表
            properties (dict, optional): 测验属性
            animation (dict, optional): 动画配置
            id (str, optional): 元素ID
        """
        self.questions = questions
        super().__init__("quiz", questions, properties, animation, id) 