#!/bin/bash

# 确保output目录存在
mkdir -p output
mkdir -p output/camels

# 获取当前时间作为文件名（日志用）
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 默认输出文件路径
OUTPUT_FILE="output/camels/example.mp4"

# 日志文件路径
LOG_FILE="output/camels/demo_${TIMESTAMP}.log"

# 默认录制区域参数
REGION_X=0
REGION_Y=100
REGION_WIDTH=1675
REGION_HEIGHT=892

# 默认最大录制时长（秒）
MAX_DURATION=90

# 自动检测终端尺寸
AUTO_DETECT_SIZE=true

# 是否调整为16:9比例
ADJUST_ASPECT_RATIO=true

# 默认的演示脚本路径
DEFAULT_SCRIPT="output/codegen/camel.sh"

# 处理命令行参数
while getopts "x:y:w:h:d:s:o:" opt; do
  case $opt in
    x) REGION_X=$OPTARG ;;
    y) REGION_Y=$OPTARG ;;
    w) REGION_WIDTH=$OPTARG ;;
    h) REGION_HEIGHT=$OPTARG ;;
    d) MAX_DURATION=$OPTARG ;;
    s) TMP_SCRIPT=$OPTARG ;;
    o) OUTPUT_FILE=$OPTARG ;;
    \?)
      echo "无效的选项: -$OPTARG" >&2
      exit 1
      ;;
  esac
done

# 如果未指定脚本，使用默认脚本
if [ -z "$TMP_SCRIPT" ]; then
  TMP_SCRIPT="$DEFAULT_SCRIPT"
fi

# 显示帮助信息
if [ "$1" == "-h" ] || [ "$1" == "--help" ]; then
  echo "用法: $0 [-x 左上角X坐标] [-y 左上角Y坐标] [-w 宽度] [-h 高度] [-d 最大秒数] [-s 演示脚本路径] [-o 输出文件路径]"
  echo "  -x 数值    区域左上角X坐标，默认会自动检测终端位置"
  echo "  -y 数值    区域左上角Y坐标，默认会自动检测终端位置"
  echo "  -w 数值    区域宽度，默认会自动检测终端宽度"
  echo "  -h 数值    区域高度，默认会自动检测终端高度"
  echo "  -d 秒数    最大录制时长（默认60秒）"
  echo "  -s 路径    演示脚本路径（默认为 $DEFAULT_SCRIPT）"
  echo "  -o 路径    视频输出文件路径（默认为 output/camels/example.mp4）"
  echo "  --help     显示帮助信息"
  exit 0
fi

# 检查ffmpeg是否已安装
if ! command -v ffmpeg &> /dev/null; then
    echo "错误: 未找到ffmpeg命令"
    echo "请通过以下命令安装ffmpeg:"
    echo "  brew install ffmpeg    # macOS"
    exit 1
fi

# 检查macOS系统
if [[ "$(uname)" != "Darwin" ]]; then
    echo "错误: 此脚本仅支持macOS系统"
    exit 1
fi

# 检查是否安装了必要的工具
if ! command -v osascript &> /dev/null; then
    echo "错误: 未找到osascript命令"
    exit 1
fi

# 检查Python依赖
if [ -f "output/codegen/economic.py" ]; then
    # 检查camel库是否已安装
    if ! python3 -c "import camel" &> /dev/null; then
        echo "警告: 未找到camel Python库，将尝试安装..."
        pip3 install camel-ai
    fi
    # 检查AppKit库是否已安装
    if ! python3 -c "import AppKit" &> /dev/null; then
        echo "警告: 未找到AppKit Python库，将尝试安装..."
        pip3 install pyobjc
    fi
    # 检查Quartz库是否已安装
    if ! python3 -c "import Quartz" &> /dev/null; then
        echo "警告: 未找到Quartz Python库，将尝试安装..."
        pip3 install pyobjc
    fi
fi

# 确保演示脚本存在并有执行权限
if [ ! -f "$TMP_SCRIPT" ]; then
    echo "错误: 找不到演示脚本: $TMP_SCRIPT"
    exit 1
fi

# FIFO用于进程间通信
FIFO_FILE="/tmp/ffmpeg_control_$$"
mkfifo "$FIFO_FILE" 2>/dev/null || true

# 自动检测当前终端窗口位置和尺寸
if [ "$AUTO_DETECT_SIZE" = true ]; then
    echo "正在检测当前终端窗口位置和尺寸..."

    # 使用AppleScript获取当前Terminal或iTerm2窗口的位置和尺寸
    # 为避免解析错误，将每个值分别获取
    WINDOW_X=$(osascript -e '
    tell application "System Events"
        set frontApp to name of first application process whose frontmost is true
        if frontApp is "Terminal" then
            tell process "Terminal"
                return position of first window
            end tell
        else if frontApp is "iTerm2" then
            tell process "iTerm2"
                return position of first window
            end tell
        else
            return "0, 0"
        end if
    end tell' | awk -F", " '{print $1}')

    WINDOW_Y=$(osascript -e '
    tell application "System Events"
        set frontApp to name of first application process whose frontmost is true
        if frontApp is "Terminal" then
            tell process "Terminal"
                return position of first window
            end tell
        else if frontApp is "iTerm2" then
            tell process "iTerm2"
                return position of first window
            end tell
        else
            return "0, 0"
        end if
    end tell' | awk -F", " '{print $2}')

    WINDOW_WIDTH=$(osascript -e '
    tell application "System Events"
        set frontApp to name of first application process whose frontmost is true
        if frontApp is "Terminal" then
            tell process "Terminal"
                return size of first window
            end tell
        else if frontApp is "iTerm2" then
            tell process "iTerm2"
                return size of first window
            end tell
        else
            return "1280, 720"
        end if
    end tell' | awk -F", " '{print $1}')

    WINDOW_HEIGHT=$(osascript -e '
    tell application "System Events"
        set frontApp to name of first application process whose frontmost is true
        if frontApp is "Terminal" then
            tell process "Terminal"
                return size of first window
            end tell
        else if frontApp is "iTerm2" then
            tell process "iTerm2"
                return size of first window
            end tell
        else
            return "1280, 720"
        end if
    end tell' | awk -F", " '{print $2}')

    # 确保所有值都是数字
    if ! [[ "$WINDOW_X" =~ ^[0-9]+$ ]]; then WINDOW_X=0; fi
    if ! [[ "$WINDOW_Y" =~ ^[0-9]+$ ]]; then WINDOW_Y=0; fi
    if ! [[ "$WINDOW_WIDTH" =~ ^[0-9]+$ ]]; then WINDOW_WIDTH=1280; fi
    if ! [[ "$WINDOW_HEIGHT" =~ ^[0-9]+$ ]]; then WINDOW_HEIGHT=720; fi

    # 调整为16:9比例
    if [ "$ADJUST_ASPECT_RATIO" = true ]; then
        echo "正在调整终端窗口为16:9比例..."

        # 获取当前应用名称
        CURRENT_APP=$(osascript -e '
        tell application "System Events"
            return name of first application process whose frontmost is true
        end tell')

        # 计算16:9比例下的新尺寸
        # 基于当前宽度计算新高度
        NEW_HEIGHT=$((WINDOW_WIDTH * 9 / 16))

        # 调整窗口大小
        osascript -e "
        tell application \"System Events\"
            if \"$CURRENT_APP\" is \"Terminal\" then
                tell process \"Terminal\"
                    set size of first window to {$WINDOW_WIDTH, $NEW_HEIGHT}
                end tell
            else if \"$CURRENT_APP\" is \"iTerm2\" then
                tell process \"iTerm2\"
                    set size of first window to {$WINDOW_WIDTH, $NEW_HEIGHT}
                end tell
            end if
        end tell"

        # 更新窗口高度变量
        WINDOW_HEIGHT=$NEW_HEIGHT
        echo "窗口已调整为: 宽=$WINDOW_WIDTH, 高=$WINDOW_HEIGHT (16:9比例)"
    fi

    # 设置录制区域
    REGION_X=$WINDOW_X
    REGION_Y=$WINDOW_Y
    REGION_WIDTH=$((WINDOW_WIDTH * 2))
    REGION_HEIGHT=$((WINDOW_HEIGHT * 2))

    # 调整标题栏的高度（估计值）
    TITLE_BAR_HEIGHT=22
    REGION_Y=$((REGION_Y + TITLE_BAR_HEIGHT))
    REGION_HEIGHT=$((REGION_HEIGHT - TITLE_BAR_HEIGHT))

    echo "检测到窗口位置: X=$REGION_X, Y=$REGION_Y"
    echo "检测到窗口尺寸: 宽=$REGION_WIDTH, 高=$REGION_HEIGHT"
fi

# 给临时脚本添加执行权限
chmod +x "$TMP_SCRIPT" 2>/dev/null || true

echo "===== 命令演示录制工具 ====="
echo "本工具将录制终端窗口并执行预设命令"
echo "录制文件将保存至: $OUTPUT_FILE"
echo "日志文件将保存至: $LOG_FILE"
echo "最大录制时长: ${MAX_DURATION}秒"
echo "使用演示脚本: $TMP_SCRIPT"
echo ""

# 获取屏幕分辨率 - 使用更可靠的方式
SCREEN_WIDTH=$(osascript -e 'tell application "Finder" to get bounds of window of desktop' | sed 's/, /\n/g' | sed -n '3p')
SCREEN_HEIGHT=$(osascript -e 'tell application "Finder" to get bounds of window of desktop' | sed 's/, /\n/g' | sed -n '4p')

echo "检测到屏幕分辨率: ${SCREEN_WIDTH}x${SCREEN_HEIGHT}"


# 设置录制命令 - 注意：在macOS中，ffmpeg的crop滤镜参数顺序为"宽:高:x:y"
echo "使用区域录制模式，区域: $REGION_X,$REGION_Y ${REGION_WIDTH}x${REGION_HEIGHT}"
FFMPEG_CMD="ffmpeg -f avfoundation -i \"1:none\" -t $MAX_DURATION  -r 30 -c:v libx264 -preset ultrafast -pix_fmt yuv420p -y -filter:v \"crop=w=${REGION_WIDTH}:h=${REGION_HEIGHT}:x=${REGION_X}:y=${REGION_Y}\" \"$OUTPUT_FILE\""

echo "请确保终端窗口在前台并可见"
echo ""
echo "将在3秒后开始录制并执行演示命令..."
echo "3..."
sleep 1
echo "2..."
sleep 1
echo "1..."
sleep 1

# 创建一个命令文件用于停止录制
STOP_CMD_FILE="/tmp/stop_record_$$.sh"
cat > "$STOP_CMD_FILE" << EOF
#!/bin/bash
# 查找并停止ffmpeg进程
PID=\$(ps aux | grep "[f]fmpeg.*$OUTPUT_FILE" | awk '{print \$2}')
if [ -n "\$PID" ]; then
    kill -TERM \$PID 2>/dev/null || kill -KILL \$PID 2>/dev/null
fi
rm -f "$STOP_CMD_FILE"
EOF
chmod +x "$STOP_CMD_FILE"

# 确保输出目录存在
mkdir -p "$(dirname "$OUTPUT_FILE")"
mkdir -p "$(dirname "$LOG_FILE")"

# 开启屏幕录制
echo "启动录制..."
clear # 执行清屏操作
eval "$FFMPEG_CMD" > /dev/null 2>&1 &
FFMPEG_PID=$!


# 检查录制是否已经开始
if ! ps -p $FFMPEG_PID > /dev/null; then
    echo "录制进程启动失败，请检查ffmpeg配置"
    rm -f "$STOP_CMD_FILE"
    exit 1
fi

echo "录制已开始 (PID: $FFMPEG_PID)，执行演示命令..."

# 启动一个后台进程监控脚本进程
(
    # 记录开始执行脚本的时间
    echo "======== 脚本执行开始: $(date) ========" > "$LOG_FILE"
    echo "执行脚本: $TMP_SCRIPT" >> "$LOG_FILE"
    echo "" >> "$LOG_FILE"

    # 执行clear命令以清空屏幕
    clear

    # 执行演示脚本并将所有输出（标准输出和错误输出）重定向到日志文件
    # tee -a 命令用于同时显示在屏幕上并保存到日志文件
    bash "$TMP_SCRIPT" 2>&1 | tee -a "$LOG_FILE"
    SCRIPT_EXIT_CODE=$?

    # 记录脚本退出代码
    echo "" >> "$LOG_FILE"
    echo "脚本退出代码: $SCRIPT_EXIT_CODE" >> "$LOG_FILE"
    echo "======== 脚本执行结束: $(date) ========" >> "$LOG_FILE"

    # 等待一小段时间确保结束画面被录制
    sleep 1

    # 停止录制
    "$STOP_CMD_FILE"

    # 通知主进程演示完成
    # 使用 echo 命令，如果FIFO已经不存在，就不报错
    echo "DEMO_COMPLETE" > "$FIFO_FILE" 2>/dev/null || true
) &
SCRIPT_RUNNER_PID=$!

# 启动超时监控进程
(
    sleep $MAX_DURATION
    if ps -p $FFMPEG_PID > /dev/null 2>&1; then
        "$STOP_CMD_FILE"

        # 终止演示脚本进程
        if ps -p $SCRIPT_RUNNER_PID > /dev/null 2>&1; then
            kill -TERM $SCRIPT_RUNNER_PID 2>/dev/null
        fi

        # 通知主进程超时
        echo "TIMEOUT" > "$FIFO_FILE" 2>/dev/null || true
    fi
) &
TIMER_PID=$!

# 等待演示完成或超时 - 添加超时处理以防止无限等待
if [ -p "$FIFO_FILE" ]; then
    # 添加超时处理，最多等待MAX_DURATION+10秒
    read -t $((MAX_DURATION + 10)) -r STATUS < "$FIFO_FILE" || STATUS="READ_TIMEOUT"
else
    echo "警告: FIFO文件不存在或已损坏，跳过等待"
    STATUS="FIFO_ERROR"
fi

# 确保所有子进程都已终止
if ps -p $SCRIPT_RUNNER_PID > /dev/null 2>&1; then
    kill -TERM $SCRIPT_RUNNER_PID 2>/dev/null
fi

if ps -p $TIMER_PID > /dev/null 2>&1; then
    kill -TERM $TIMER_PID 2>/dev/null
fi

# 确保ffmpeg已经停止
if ps -p $FFMPEG_PID > /dev/null 2>&1; then
    kill -KILL $FFMPEG_PID 2>/dev/null
    sleep 1
fi

# 清理临时文件（只清理脚本创建的临时文件，不清理用户指定的脚本）
rm -f "$STOP_CMD_FILE"
[ -p "$FIFO_FILE" ] && rm -f "$FIFO_FILE"

# 等待一下确保文件已完全写入
sleep 2

# 检查录制是否成功
if [ -f "$OUTPUT_FILE" ] && [ -s "$OUTPUT_FILE" ]; then
    echo "录制完成!"
    echo "视频已保存至: $OUTPUT_FILE"
    echo "日志已保存至: $LOG_FILE"
    echo "文件信息:"
    ls -lh "$OUTPUT_FILE"

    # 显示视频信息
    ffmpeg -i "$OUTPUT_FILE" 2>&1 | grep "Duration" || echo "无法获取视频时长信息"

else
    echo "录制失败或保存文件出错"
    exit 1
fi

echo ""
echo "脚本执行完毕"
