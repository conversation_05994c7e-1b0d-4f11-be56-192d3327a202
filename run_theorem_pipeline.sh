#!/bin/bash

# 数学题目处理流水线 - 智能意图分类驱动
# 支持从config.yaml的intent_input自动识别数学题目并处理

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${PURPLE}[SUCCESS]${NC} $1"
}

log_header() {
    echo -e "${CYAN}$1${NC}"
}

# 显示帮助信息
show_help() {
    echo "数学题目处理流水线 - 智能意图分类驱动"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "智能模式（推荐）:"
    echo "  默认模式           从config.yaml的intent_input自动读取数学题目"
    echo ""
    echo "控制选项:"
    echo "  --verbose          详细输出模式"
    echo "  --help, -h         显示此帮助信息"
    echo ""
    echo "配置要求："
    echo "  在config.yaml中设置material.intent_input:"
    echo "    material:"
    echo "      intent_input:"
    echo "        file: \"./math_problem.png\"  # 数学题目图片路径"
    echo "        chat: \"讲解这道几何题\"      # 解题要求（可选）"
    echo ""
    echo "使用示例:"
    echo "  # 智能模式：自动识别数学题目"
    echo "  $0"
    echo ""
    echo "  # 详细输出模式"
    echo "  $0 --verbose"
}

# 检查配置文件中的intent_input
check_intent_input() {
    local config_file="config/config.yaml"

    if [ ! -f "$config_file" ]; then
        log_error "配置文件不存在: $config_file"
        exit 1
    fi

    # 使用python来检查intent_input配置
    local check_result=$(python3 -c "
import yaml
import sys
try:
    with open('$config_file', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    material_config = config.get('material', {})
    intent_input = material_config.get('intent_input', {})
    file_path = intent_input.get('file', '').strip()
    chat = intent_input.get('chat', '').strip()
    
    if not file_path:
        print('ERROR: material.intent_input.file 未配置或为空')
        sys.exit(1)
    
    print(f'SUCCESS:{file_path}:{chat}')
except Exception as e:
    print(f'ERROR: {str(e)}')
    sys.exit(1)
")

    if [[ $check_result == ERROR:* ]]; then
        local error_msg=${check_result#ERROR: }
        log_error "配置检查失败: $error_msg"
        log_warning "请在config.yaml中正确配置material.intent_input："
        log_warning "  material:"
        log_warning "    intent_input:"
        log_warning "      file: \"./math_problem.png\"  # 数学题目图片路径"
        log_warning "      chat: \"讲解这道几何题\"      # 解题要求（可选）"
        exit 1
    fi

    echo "$check_result"
}

# 运行智能意图分类和处理流程
run_smart_workflow() {
    local verbose="$1"
    
    log_step "运行智能意图分类和数学题目处理..."
    
    # 构建Python命令
    local python_cmd="python feynman_workflow_refactored.py"
    
    if [[ "$verbose" == "true" ]]; then
        python_cmd="$python_cmd --verbose"
    fi
    
    log_info "执行命令: $python_cmd"
    
    # 执行智能工作流
    if eval "$python_cmd"; then
        log_success "✅ 智能工作流完成"
        return 0
    else
        log_error "❌ 智能工作流失败"
        return 1
    fi
}

# 执行命令的函数
run_command() {
    local cmd="$1"
    local description="$2"

    log_step "$description"
    log_info "执行命令: $cmd"

    if eval "$cmd"; then
        log_success "✅ $description 执行成功"
        return 0
    else
        log_error "❌ $description 执行失败"
        return 1
    fi
}

# 主函数
main() {
    local verbose="false"
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --verbose)
                verbose="true"
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done

    echo "=================================================="
    log_header "🎬 数学题目处理流水线 - 智能意图分类驱动"
    echo "=================================================="

    # 检查必要的依赖
    if ! command -v python3 &> /dev/null; then
        log_error "需要安装 Python3"
        exit 1
    fi

    if ! python3 -c "import yaml" 2>/dev/null; then
        log_error "需要安装 PyYAML: pip install PyYAML"
        exit 1
    fi

    # 检查intent_input配置
    log_info "检查配置文件..."
    local config_check=$(check_intent_input)
    if [[ $config_check == SUCCESS:* ]]; then
        # 解析配置信息
        local config_parts=${config_check#SUCCESS:}
        local file_path=$(echo "$config_parts" | cut -d':' -f1)
        local chat_instruction=$(echo "$config_parts" | cut -d':' -f2-)
        
        log_info "📁 数学题目文件: $file_path"
        if [[ -n "$chat_instruction" ]]; then
            log_info "💬 解题要求: $chat_instruction"
        else
            log_info "💬 解题要求: 使用默认分析"
        fi
    else
        exit 1
    fi

    # 运行智能工作流
    echo ""
    log_info "🚀 开始执行智能意图分类和处理流程..."
    
    if run_smart_workflow "$verbose"; then
        echo ""
        echo "=================================================="
        log_success "🎉 数学题目处理流水线执行成功！"
        log_info "✨ 工作流已完成，请查看输出目录中的结果"
        
        # 尝试显示输出目录信息
        if [ -d "output" ]; then
            log_info "📂 输出目录结构："
            ls -la output/ | head -10
        fi
        
    else
        echo ""
        echo "=================================================="
        log_error "⚠️  数学题目处理流水线执行失败"
        log_error "请检查日志信息和配置设置"
        exit 1
    fi
    echo "=================================================="
}

# 检查脚本是否被直接执行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
