#!/usr/bin/env python3
"""
重构后的自动工作流脚本 - 智能意图分类驱动

该脚本执行以下步骤：
0. 智能意图分类，识别用户输入类型
1. 根据分类结果直接调用对应的处理功能
2. 生成动画脚本故事板
3. 渲染最终视频

支持的内容类型：
- GitHub项目 (github)
- PDF文档/论文 (pdf)
- 网页内容 (webpage)
- 本地文件 (local_file)
- 数学题目 (math_problem) - 使用example_explain流程
- 概念解释 (concept_explain) - 使用example_explain流程
- 普通聊天 (chat)

用法：
# 智能模式 - 自动从config.yaml读取intent_input
python feynman_workflow_refactored.py

# 定理/概念解释专用模式（仅适用于纯chat内容的原理介绍）
python feynman_workflow_refactored.py --theorem-mode

# 跳过意图分类，使用传统模式
python feynman_workflow_refactored.py --skip-intent

# 详细输出模式
python feynman_workflow_refactored.py --verbose
"""

import argparse
import os
import subprocess
import sys
import threading
import time

import yaml
from loguru import logger

from utils.create_llm_model import create_model


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="智能意图分类驱动的视频生成工作流")

    # 配置文件参数
    parser.add_argument("--config", default="config/config.yaml", help="配置文件路径")

    # 传统模式参数（向后兼容）
    legacy_group = parser.add_argument_group("传统配置模式（向后兼容）")
    legacy_group.add_argument(
        "--source",
        choices=["github", "pdf", "webpage", "local_file", "chat"],
        help="指定材料源类型 (如果不指定，将根据配置文件自动判断)",
    )
    legacy_group.add_argument("--purpose", help="视频目的描述 (覆盖配置文件中的设置)")
    legacy_group.add_argument("--chat-mode", action="store_true", help="启用chat模式，通过用户输入的purpose生成视频")

    # 素材扩充选项
    enhance_group = parser.add_argument_group("素材扩充选项")
    enhance_group.add_argument("--enable-video", action="store_true", help="强制启用录屏扩充")
    enhance_group.add_argument("--disable-video", action="store_true", help="禁用录屏扩充")
    enhance_group.add_argument("--enable-image", action="store_true", help="启用图片生成扩充")
    enhance_group.add_argument("--enable-audio", action="store_true", help="启用音频合成扩充")

    # 调试选项
    debug_group = parser.add_argument_group("调试选项")
    debug_group.add_argument("--verbose", "-v", action="store_true", help="详细输出模式")
    debug_group.add_argument("--skip-intent", action="store_true", help="跳过意图分类，直接使用传统模式")
    debug_group.add_argument(
        "--theorem-mode", action="store_true", help="定理/概念解释专用模式（仅适用于纯chat内容的原理介绍）"
    )

    return parser.parse_args()


def load_config(config_path):
    """加载配置文件"""
    logger.info(f"正在加载配置文件: {config_path}")
    try:
        with open(config_path, encoding="utf-8") as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {str(e)}")
        sys.exit(1)


def run_command(cmd, desc, verbose=True):
    """运行命令并实时显示日志输出"""
    logger.info(f"开始执行: {desc}")
    logger.info("分隔线======================================")
    if verbose:
        logger.info(f"执行命令: {cmd}")

    try:
        process = subprocess.Popen(
            cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, bufsize=1
        )

        def handle_output(stream, is_error=False):
            for line in stream:
                line = line.rstrip()
                if not line:
                    continue

                if is_error:
                    logger.warning(line)
                else:
                    logger.info(line)

        stdout_thread = threading.Thread(target=handle_output, args=(process.stdout, False))
        stderr_thread = threading.Thread(target=handle_output, args=(process.stderr, True))

        stdout_thread.start()
        stderr_thread.start()

        stdout_thread.join()
        stderr_thread.join()

        return_code = process.wait()

        logger.info("======================================")
        logger.info(f"命令执行完成: {desc}")

        if return_code == 0:
            return True
        else:
            logger.error(f"命令执行失败，返回码: {return_code}")
            return False
    except Exception as e:
        logger.error(f"命令执行异常: {str(e)}")
        logger.info("======================================")
        return False


def ensure_output_directory(project_name):
    """确保输出目录存在"""
    output_dir = f"output/{project_name}"
    os.makedirs(output_dir, exist_ok=True)
    return output_dir


def analyze_image_with_ai(image_path, purpose, config_path="config/config.yaml"):
    """使用AI模型分析图片内容"""
    try:
        import base64

        from camel.messages import BaseMessage

        from utils.common import AgentFactory

        # 加载配置和创建模型
        model = create_model()

        # 创建图片分析代理
        analyzer_agent = AgentFactory.create_analyzer_agent(
            model,
            "图片内容分析专家",
            "你是一个专业的图片内容分析专家，能够详细分析图片中的内容，包括文字、图表、图形、场景等，并生成结构化的markdown报告。",
        )

        # 读取并编码图片
        with open(image_path, "rb") as f:
            image_data = f.read()

        # 检查图片大小，如果过大则压缩
        image_size_mb = len(image_data) / (1024 * 1024)
        if image_size_mb > 10:  # 如果图片超过10MB，进行压缩
            logger.warning(f"图片文件较大 ({image_size_mb:.2f}MB)，尝试压缩...")
            try:
                import io

                from PIL import Image

                # 打开图片并压缩
                with Image.open(image_path) as img:
                    # 转换为RGB模式（如果是RGBA或其他模式）
                    if img.mode in ("RGBA", "LA", "P"):
                        img = img.convert("RGB")

                    # 计算压缩比例
                    max_dimension = 2048
                    if max(img.size) > max_dimension:
                        ratio = max_dimension / max(img.size)
                        new_size = (int(img.size[0] * ratio), int(img.size[1] * ratio))
                        img = img.resize(new_size, Image.Resampling.LANCZOS)

                    # 保存为JPEG格式并压缩
                    buffer = io.BytesIO()
                    img.save(buffer, format="JPEG", quality=85, optimize=True)
                    image_data = buffer.getvalue()

                logger.info(f"图片已压缩至 {len(image_data) / (1024 * 1024):.2f}MB")
            except ImportError:
                logger.warning("PIL库未安装，无法压缩图片，将使用原始图片")
            except Exception as e:
                logger.warning(f"图片压缩失败: {e}，将使用原始图片")

        # Base64编码
        image_base64 = base64.b64encode(image_data).decode("utf-8")

        # 获取图片文件名和扩展名
        filename = os.path.basename(image_path)
        file_ext = os.path.splitext(filename)[1].lower()

        # 构建分析提示
        analysis_prompt = f"""
请详细分析这张图片的内容，生成结构化的markdown报告。

**分析目标**: {purpose}

**分析要求**:
1. **整体描述**: 图片的主要内容和主题
2. **详细内容**:
   - 如果包含文字，请提取并整理所有可读文字
   - 如果包含图表、表格，请描述数据和结构
   - 如果包含流程图、架构图，请说明各部分的关系
   - 如果包含界面截图，请描述界面元素和功能
3. **技术信息**: 如果是技术相关图片，请提取技术要点
4. **关键见解**: 从图片中提取的重要信息和结论
6. **应用价值**: 这些内容在实际应用中的价值
7. **内容总结**: 对图片内容和解读的总结，比如解题的步骤总结等，不超过100字

**输出格式**:
```markdown
# 图片内容分析报告

## 基本信息
- **文件名**: {filename}
- **分析时间**: [当前时间]
- **图片类型**: [图片类型描述]

## 整体描述
[图片的主要内容和主题描述]

## 详细内容分析

### 文字内容
[如果有文字，请逐一提取并整理]

### 图表数据
[如果有图表、表格，请详细描述]

### 结构组织
[如果有流程图、架构图等，请描述结构关系]

### 界面元素
[如果是界面截图，请描述各功能模块]

## 技术要点
[提取的技术信息和要点]

## 关键见解
[从图片中得出的重要结论和见解]

## 应用价值
[这些内容的实际应用价值和意义]

## 内容总结
[对图片内容和解读的总结，比如解题的步骤总结等]
```

请基于图片内容生成详细的分析报告，确保信息准确完整。
"""

        # 构建包含图片的消息
        message_content = [
            {"type": "text", "text": analysis_prompt},
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/{file_ext[1:] if file_ext else 'jpeg'};base64,{image_base64}",
                    "detail": "high",  # 使用高精度模式以获得更详细的分析
                },
            },
        ]

        # 调用模型分析
        logger.info(f"开始AI分析图片: {filename}")
        response = analyzer_agent.step(BaseMessage.make_user_message(role_name="User", content=message_content))

        analysis_result = response.msg.content
        logger.info(f"图片分析完成，生成内容长度: {len(analysis_result)} 字符")

        return analysis_result

    except Exception as e:
        logger.error(f"AI图片分析失败: {e}")
        # 返回基本的图片信息作为备用方案
        filename = os.path.basename(image_path)
        return f"""# 图片内容分析报告

## 基本信息
- **文件名**: {filename}
- **文件路径**: {image_path}
- **状态**: AI分析失败

## 错误信息
{str(e)}

## 建议
请检查以下内容：
1. 确保图片文件有效且未损坏
2. 检查AI模型配置是否正确
3. 确认模型是否支持视觉分析功能
4. 检查网络连接是否正常

## 手动分析
请手动查看图片内容并补充相关信息。
"""


def run_intent_classification(config_path: str):
    """
    运行智能意图分类，从配置文件读取输入

    Args:
        config_path: 配置文件路径

    Returns:
        (success, classification_result): 成功标志、分类结果
    """
    try:
        # 导入意图分类Agent
        from agents.intent_classification_agent import SmartIntentClassificationAgent

        logger.info("🎯 开始智能意图分类...")

        # 创建意图分类代理
        agent = SmartIntentClassificationAgent(config_path)

        # 从配置文件读取输入并处理意图
        result = agent.process_intent_from_config()

        # 检查处理结果
        if "error" in result:
            logger.error(f"❌ 意图分类失败: {result['error']}")
            return False, None

        # 输出分类结果
        classification = result["classification"]

        logger.info("✅ 智能意图分类完成！")
        logger.info(f"📋 处理类型: {classification['type']}")
        logger.info(f"📋 主分类: {classification['category']}")
        logger.info(f"🏷️  子分类: {classification['subcategory']}")
        logger.info(
            f"📝 提取信息: {classification['extracted_info'].get('topic', classification['extracted_info'].get('purpose', ''))}"
        )
        logger.info(f"✅ 安全检查: {result['safety_check']}")

        return True, classification

    except Exception as e:
        logger.exception(f"意图分类过程中发生错误: {e}")
        return False, None


def generate_project_name(classification):
    """根据分类结果生成项目名称"""
    info = classification.get("extracted_info", {})
    category = classification.get("category", "")
    subcategory = classification.get("subcategory", "")

    if category == "github":
        return info.get("repo", "github_project")
    elif category == "pdf":
        arxiv_id = info.get("arxiv_id", "")
        if arxiv_id:
            return arxiv_id
        return f"pdf_{subcategory}"
    elif category == "image" or category == "local_file":
        # 对于文件（包括图片），使用文件名（去掉后缀）作为工程名
        file_path = info.get("file_path", "")
        if file_path:
            from pathlib import Path

            file_name = Path(file_path).stem  # 获取文件名（不含后缀）
            if file_name:
                # 清理文件名，移除特殊字符，保留中文、英文、数字
                import re

                clean_name = re.sub(r"[^\w\u4e00-\u9fff]", "_", file_name)
                return clean_name

        # 如果没有文件路径，回退到原来的逻辑
        if subcategory == "math_problem":
            topic = info.get("topic", "数学题目")
            return f"math_{topic}"
        else:
            topic = info.get("topic", "local_file")
            return topic
    elif category == "chat":
        if subcategory == "concept_introduction":
            # concept_explain类型
            topic = info.get("topic", "概念解释")
            return f"concept_{topic}"
        else:
            # 普通chat类型
            topic = info.get("topic", "chat_content")
            # 使用时间戳确保唯一性
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            return f"chat_{topic}_{timestamp}"
    else:
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        return f"{category}_{subcategory}_{timestamp}"


def process_github_content(classification, project_name, output_dir):
    """处理GitHub内容"""
    info = classification["extracted_info"]
    github_url = info["url"]

    analysis_file = f"{output_dir}/project_analysis.md"
    if os.path.exists(analysis_file):
        logger.info(f"GitHub分析文件已存在: {analysis_file}，跳过处理")
        return analysis_file

    # 使用重构后的GitHub代理
    step1_cmd = f"python -m agents.github_source_agent_refactored --repo {github_url}"

    if not run_command(step1_cmd, "处理GitHub项目"):
        logger.error("GitHub源处理失败")
        return None

    if not os.path.exists(analysis_file):
        logger.error(f"GitHub分析文件未生成: {analysis_file}")
        return None

    return analysis_file


def process_pdf_content(classification, project_name, output_dir, max_num_pages):
    """处理PDF内容"""
    info = classification["extracted_info"]
    pdf_url = info["url"]

    analysis_file = f"{output_dir}/{project_name}.md"
    if os.path.exists(analysis_file):
        logger.info(f"PDF分析文件已存在: {analysis_file}，跳过处理")
        return analysis_file

    step1_cmd = f"python -c \"from tools.pdf_toolkit import PDFToolkit; result = PDFToolkit().extract_pdf('{pdf_url}', 'output', {max_num_pages}); print(f'PDF处理完成: {{result.get(\\\"markdown_file\\\", \\\"未知\\\")}}')\""
    if not run_command(step1_cmd, "处理PDF文件"):
        logger.error("PDF源处理失败")
        return None

    if not os.path.exists(analysis_file):
        logger.error(f"PDF分析文件未生成: {analysis_file}")
        return None

    # 在生成的markdown文件中添加原始PDF URL
    try:
        with open(analysis_file, encoding="utf-8") as f:
            content = f.read()

        # 在文件开头添加URL信息
        url_header = f"**原始PDF链接**: {pdf_url}\n\n"

        with open(analysis_file, "w", encoding="utf-8") as f:
            f.write(url_header + content)

        logger.info(f"已在PDF分析文件中添加原始URL: {pdf_url}")

    except Exception as e:
        logger.error(f"无法在PDF文件中添加URL信息: {e}")

    return analysis_file


def process_webpage_content(classification, project_name, output_dir):
    """处理网页内容"""
    info = classification["extracted_info"]
    webpage_url = info["url"]

    analysis_file = f"{output_dir}/webpage_analysis.md"
    if os.path.exists(analysis_file):
        logger.info(f"网页分析文件已存在: {analysis_file}，跳过处理")
        return analysis_file

    try:
        from tools.webpage_toolkit import WebpageToolkit

        # 使用WebpageToolkit提取网页内容
        toolkit = WebpageToolkit(timeout=30, use_cache=True)
        base_output_dir = os.path.dirname(output_dir)
        result = toolkit.extract_webpage(url=webpage_url, output_base_dir=base_output_dir, project_name=project_name)

        if "error" in result:
            logger.error(f"网页内容提取失败: {result['error']}")
            # 创建错误提示文件
            with open(analysis_file, "w", encoding="utf-8") as f:
                f.write("# 网页内容分析\n\n")
                f.write(f"**源URL:** {webpage_url}\n\n")
                f.write("**状态:** 提取失败\n\n")
                f.write(f"**错误信息:** {result['error']}\n\n")
                f.write("请检查URL是否有效，或者网站是否存在访问限制。\n")
            return analysis_file

        # 处理生成的markdown文件
        generated_markdown = result.get("markdown_file")
        if not generated_markdown or not os.path.exists(generated_markdown):
            logger.error(f"生成的markdown文件不存在: {generated_markdown}")
            return None

        if os.path.abspath(generated_markdown) != os.path.abspath(analysis_file):
            import shutil

            shutil.copy2(generated_markdown, analysis_file)
            logger.info(f"已将文件从 {generated_markdown} 复制到 {analysis_file}")

        logger.info(f"网页内容提取成功: {analysis_file}")
        return analysis_file

    except ImportError as e:
        logger.error(f"无法导入WebpageToolkit: {e}")
        return None
    except Exception as e:
        logger.error(f"网页处理过程中发生错误: {e}")
        return None


def process_local_file_content(classification, project_name, output_dir):
    """处理本地文件内容"""
    info = classification["extracted_info"]
    file_path = info["file_path"]

    analysis_file = f"{output_dir}/local_file_analysis.md"
    if os.path.exists(analysis_file):
        logger.info(f"本地文件分析已存在: {analysis_file}，跳过处理")
        return analysis_file

    try:
        import shutil
        from pathlib import Path

        path_obj = Path(file_path)
        ext = path_obj.suffix.lower()

        # 处理图片文件
        image_extensions = [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".tiff", ".tif", ".svg", ".webp"]
        if ext in image_extensions:
            logger.info(f"检测到图片文件，开始AI分析: {file_path}")

            # 使用AI分析图片内容（从原来的代码中导入分析函数）
            purpose = info.get("purpose", "分析图片内容，提取关键信息")
            analysis_result = analyze_image_with_ai(file_path, purpose)

            # 保存分析结果
            with open(analysis_file, "w", encoding="utf-8") as f:
                f.write(analysis_result)

            # 复制原始图片到输出目录
            media_dir = os.path.join(output_dir, "media")
            os.makedirs(media_dir, exist_ok=True)

            original_filename = os.path.basename(file_path)
            target_image_path = os.path.join(media_dir, original_filename)
            shutil.copy2(file_path, target_image_path)

            # 在markdown中添加原始图片引用
            with open(analysis_file, "a", encoding="utf-8") as f:
                f.write("\n\n## 原始图片\n\n")
                f.write(f"![原始图片]({os.path.join('media', original_filename)})\n\n")
                f.write(f"**图片路径**: `{target_image_path}`\n")

            return analysis_file

        # 处理PDF文件
        elif ext == ".pdf":
            logger.info(f"检测到PDF文件，开始解析: {file_path}")
            from tools.pdf_toolkit import PDFToolkit

            pdf_toolkit = PDFToolkit()
            base_output_dir = os.path.dirname(output_dir)
            result = pdf_toolkit.extract_pdf(file_path, base_output_dir)

            if "error" in result:
                logger.error(f"PDF解析失败: {result['error']}")
                with open(analysis_file, "w", encoding="utf-8") as f:
                    f.write("# 本地PDF文件分析\n\n")
                    f.write(f"**文件路径:** {file_path}\n\n")
                    f.write("**状态:** 解析失败\n\n")
                    f.write(f"**错误信息:** {result['error']}\n\n")
                return analysis_file

            generated_markdown = result.get("markdown_file")
            if generated_markdown and os.path.exists(generated_markdown):
                if os.path.abspath(generated_markdown) != os.path.abspath(analysis_file):
                    shutil.copy2(generated_markdown, analysis_file)
                return analysis_file

        # 处理Office文件
        elif ext in [".docx", ".pptx", ".xlsx", ".xls", ".doc", ".ppt"]:
            logger.info(f"检测到Office文件，使用MarkItDown转换: {file_path}")
            from markitdown import MarkItDown

            md_converter = MarkItDown()
            result = md_converter.convert(file_path)

            if result and result.text_content:
                with open(analysis_file, "w", encoding="utf-8") as f:
                    f.write(result.text_content)
                return analysis_file

        # 其他文件类型的基本处理
        else:
            with open(analysis_file, "w", encoding="utf-8") as f:
                f.write(f"# 本地文件分析\n\n文件路径: {file_path}\n\n")
                f.write(f"文件类型: {ext}\n\n")
                f.write("注意：该文件类型需要手动处理或添加相应的处理逻辑。\n")

        logger.info(f"本地文件处理完成: {analysis_file}")
        return analysis_file

    except Exception as e:
        logger.error(f"本地文件处理失败: {e}")
        return None


def process_concept_explain(classification, project_name, output_dir):
    """处理概念介绍（使用example_explain流程）"""
    info = classification["extracted_info"]
    topic = info.get("topic", "概念解释")
    purpose = info.get("purpose", "解释概念")
    instruction = info.get("instruction", purpose)  # noqa: F841

    logger.info(f"💡 处理概念介绍: {topic}")

    # 确保输出目录结构
    os.makedirs(output_dir, exist_ok=True)

    # 直接调用ExampleExplainAgent
    try:
        # 导入ExampleExplainAgent
        from agents.example_explain_agent_refactor import ExampleExplainAgent

        # 加载原始配置
        original_config_path = "config/config.yaml"
        with open(original_config_path, encoding="utf-8") as f:
            original_config = yaml.safe_load(f)

        # 创建临时配置，保留原始配置的model部分，设置example_explain部分
        temp_config = {
            "model": original_config.get("model", {}),
            "example_explain": {"purpose": purpose, "topic": topic, "max_rounds": 1, "quality_threshold": "良好"},
        }

        # 创建临时配置文件
        temp_config_path = f"{output_dir}/temp_config.yaml"
        with open(temp_config_path, "w", encoding="utf-8") as f:
            yaml.dump(temp_config, f, indent=2, allow_unicode=True)

        logger.info(f"设置概念解释配置: topic={topic}, purpose={purpose}")
        logger.info(f"临时配置文件: {temp_config_path}")

        # 创建Agent实例，使用临时配置文件
        agent = ExampleExplainAgent(config_path=temp_config_path)
        agent.config.output_dir = output_dir

        # 运行example_explain流程
        result = agent.run()

        # 清理临时配置文件
        if os.path.exists(temp_config_path):
            os.remove(temp_config_path)

        if result.get("success", False):
            explain_file = f"{output_dir}/example_explain.md"
            if os.path.exists(explain_file):
                logger.info(f"概念解释文件已生成: {explain_file}")
                return explain_file
            else:
                logger.warning(f"概念解释文件未找到: {explain_file}")
                return None
        else:
            logger.error("概念解释生成失败")
            return None

    except Exception as e:
        logger.error(f"概念解释处理失败: {e}")
        return None


def process_math_problem(classification, project_name, output_dir):
    """处理数学题目（使用local_file流程，但采用数学题目专用prompt）"""
    info = classification["extracted_info"]
    file_path = info["file_path"]
    topic = info.get("topic", "数学题目")
    purpose = info.get("purpose", "解析数学题目")

    logger.info(f"🧮 处理数学题目: {topic}")

    # 确保输出目录结构
    os.makedirs(output_dir, exist_ok=True)

    analysis_file = f"{output_dir}/math_analysis.md"
    if os.path.exists(analysis_file):
        logger.info(f"数学题目分析文件已存在: {analysis_file}，跳过处理")
        return analysis_file

    try:
        import shutil
        from pathlib import Path

        path_obj = Path(file_path)
        ext = path_obj.suffix.lower()

        # 处理图片文件（数学题目通常是图片格式）
        image_extensions = [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".tiff", ".tif", ".svg", ".webp"]
        if ext in image_extensions:
            logger.info(f"检测到数学题目图片，开始AI分析: {file_path}")

            # 使用数学题目专用的purpose和prompt
            math_purpose = f"""
分析这道数学题目的内容，请详细解读题目要求、已知条件、求解目标，并提供详细的解题思路和步骤。

**分析目标**: {purpose}

**分析重点**:
1. 题目类型识别（几何、代数、概率等）
2. 已知条件提取
3. 求解目标确定
4. 解题思路分析
5. 详细解题步骤
6. 关键知识点总结
7. 类似题目的解题技巧

请生成结构化的数学题目分析报告，便于后续生成教学动画。
"""

            analysis_result = analyze_image_with_ai(file_path, math_purpose)

            # 保存分析结果
            with open(analysis_file, "w", encoding="utf-8") as f:
                f.write(analysis_result)

            # 复制原始图片到输出目录
            media_dir = os.path.join(output_dir, "media")
            os.makedirs(media_dir, exist_ok=True)

            original_filename = os.path.basename(file_path)
            target_image_path = os.path.join(media_dir, original_filename)
            shutil.copy2(file_path, target_image_path)

            # 在markdown中添加原始图片引用
            with open(analysis_file, "a", encoding="utf-8") as f:
                f.write("\n\n## 原始数学题目\n\n")
                f.write(f"![数学题目]({os.path.join('media', original_filename)})\n\n")
                f.write(f"**题目文件**: `{target_image_path}`\n")
                f.write(f"**题目类型**: {topic}\n")

            logger.info(f"数学题目分析完成: {analysis_file}")
            return analysis_file

        else:
            # 非图片类型的数学题目文件
            logger.warning(f"数学题目文件不是图片格式: {ext}")
            with open(analysis_file, "w", encoding="utf-8") as f:
                f.write("# 数学题目分析\n\n")
                f.write(f"**文件路径**: {file_path}\n")
                f.write(f"**文件类型**: {ext}\n")
                f.write(f"**题目类型**: {topic}\n\n")
                f.write("**注意**: 该文件不是图片格式，需要手动处理或转换为图片格式。\n")
            return analysis_file

    except Exception as e:
        logger.error(f"数学题目处理失败: {e}")
        return None
    finally:
        # 清理环境变量
        for env_var in ["MATH_PROBLEM_FILE", "MATH_PROBLEM_TOPIC", "MATH_PROBLEM_PURPOSE"]:
            if env_var in os.environ:
                del os.environ[env_var]


def process_chat_content(classification, project_name, output_dir):
    """处理聊天内容（直接生成素材）"""
    info = classification["extracted_info"]
    purpose = info.get("purpose", info.get("instruction", ""))

    logger.info(f"💬 处理聊天内容: {purpose}")

    # 直接调用material_agent生成素材
    intro_md_path = f"{output_dir}/{project_name}_intro.md"
    if os.path.exists(intro_md_path):
        logger.info(f"聊天素材文件已存在: {intro_md_path}，跳过处理")
        return intro_md_path

    # 将classification数据转换为JSON字符串
    import json

    classification_json = json.dumps(classification, ensure_ascii=False)

    step_cmd = (
        f"python -m agents.material_agent_refactored "
        f'--purpose "{purpose}" '
        f"--output {intro_md_path} "
        f"--classification-data '{classification_json}'"
    )

    if not run_command(step_cmd, "生成聊天模式素材"):
        logger.error("聊天模式素材生成失败")
        return None

    if not os.path.exists(intro_md_path):
        logger.error(f"聊天素材文件未生成: {intro_md_path}")
        return None

    return intro_md_path


def build_enhancement_config(args):
    """构建素材扩充配置"""
    if args.enable_video or args.disable_video or args.enable_image or args.enable_audio:
        return {
            "video_recording": (args.enable_video or not args.disable_video) and not args.disable_video,
            "image_generation": args.enable_image,
            "audio_synthesis": args.enable_audio,
        }
    return None


def run_workflow_for_classification(classification, project_name, args):
    """根据分类结果运行对应的工作流"""

    classification_type = classification["type"]

    # 对于chat模式的概念解释，先删除已存在的工程文件夹
    if classification_type in ["concept_explain", "chat"]:
        import shutil

        project_output_dir = f"output/{project_name}"
        if os.path.exists(project_output_dir):
            logger.info(f"🗑️  删除已存在的项目文件夹: {project_output_dir}")
            try:
                shutil.rmtree(project_output_dir)
                logger.info("✅ 项目文件夹删除成功")
            except Exception as e:
                logger.warning(f"⚠️  删除项目文件夹失败: {e}")

    output_dir = ensure_output_directory(project_name)

    logger.info(f"📁 项目目录: {output_dir}")
    logger.info(f"🎯 处理类型: {classification_type}")

    # 第一步：根据类型处理内容
    analysis_file = None

    if classification_type == "github":
        analysis_file = process_github_content(classification, project_name, output_dir)
    elif classification_type == "pdf":
        analysis_file = process_pdf_content(classification, project_name, output_dir, args.max_num_pages)
    elif classification_type == "webpage":
        analysis_file = process_webpage_content(classification, project_name, output_dir)
    elif classification_type == "local_file":
        analysis_file = process_local_file_content(classification, project_name, output_dir)
    elif classification_type == "math_problem":
        # 数学题目使用和local_file相同的流程，但使用数学专用prompt
        analysis_file = process_math_problem(classification, project_name, output_dir)
    elif classification_type == "concept_explain":
        # 概念解释使用example_explain流程，然后生成代码
        analysis_file = process_concept_explain(classification, project_name, output_dir)
        if analysis_file:
            # 概念解释完成后，运行代码生成
            info = classification["extracted_info"]
            topic = info.get("topic", "概念解释")
            code_cmd = f"python scripts/run_claude_code_router.py '{analysis_file}' '{output_dir}/code/{topic}'"
            if run_command(code_cmd, "生成概念解释动画代码"):
                logger.info("🎉 概念解释处理完成！")
                return True
            else:
                logger.error("概念解释代码生成失败")
                return False
        else:
            return False
    elif classification_type == "chat":
        analysis_file = process_chat_content(classification, project_name, output_dir)
    else:
        logger.error(f"不支持的处理类型: {classification_type}")
        return False

    if not analysis_file:
        logger.error(f"内容处理失败: {classification_type}")
        return False

    # 第二步：生成项目介绍素材（除了chat和concept_explain，其他类型都需要）
    if classification_type not in ["chat", "concept_explain"]:
        info = classification["extracted_info"]
        purpose = info.get("purpose", "分析内容并生成素材")

        intro_md_path = f"{output_dir}/{project_name}_intro.md"
        if not os.path.exists(intro_md_path):
            enhancement_str = ""
            enhancement_config = build_enhancement_config(args)
            if enhancement_config:
                enhancement_options = []
                if enhancement_config.get("video_recording"):
                    enhancement_options.append("--enable-video")
                if enhancement_config.get("image_generation"):
                    enhancement_options.append("--enable-image")
                if enhancement_config.get("audio_synthesis"):
                    enhancement_options.append("--enable-audio")
                enhancement_str = " ".join(enhancement_options)

            # 将classification数据转换为JSON字符串
            import json

            classification_json = json.dumps(classification, ensure_ascii=False)

            step2_cmd = (
                f"python -m agents.material_agent_refactored "
                f"--material {analysis_file} "
                f'--purpose "{purpose}" '
                f"--output {intro_md_path} "
                f"--classification-data '{classification_json}' "
                f"{enhancement_str}"
            ).strip()

            if not run_command(step2_cmd, "生成项目介绍素材"):
                logger.error("项目介绍素材生成失败")
                return False

            if not os.path.exists(intro_md_path):
                logger.error(f"项目介绍文件未生成: {intro_md_path}")
                return False

        analysis_file = intro_md_path  # 更新为最终的素材文件

    # 第三步：生成动画脚本故事板（除了concept_explain，其他类型都需要）
    if classification_type not in ["concept_explain"]:
        info = classification["extracted_info"]
        purpose = info.get("purpose", "生成动画视频")

        storyboard_path = f"{output_dir}/{project_name}_storyboard.json"
        if not os.path.exists(storyboard_path):
            step3_cmd = (
                f"python -m agents.generate_manim_dsl_agent_refactored "
                f"--markdown {analysis_file} "
                f'--purpose "{purpose}" '
                f"--output {storyboard_path}"
            )

            if not run_command(step3_cmd, "生成动画脚本故事板"):
                logger.error("故事板生成失败")
                return False

            if not os.path.exists(storyboard_path):
                logger.error(f"故事板文件未生成: {storyboard_path}")
                return False

        # 第四步：渲染最终视频（除了concept_explain，其他类型都需要）
        video_output_dir = f"output/{project_name}/videos"
        if not (os.path.exists(video_output_dir) and len(os.listdir(video_output_dir)) > 0):
            step4_cmd = f"sh run_storyboards.sh -f {storyboard_path} -q h --project-name {project_name}"
            if not run_command(step4_cmd, "渲染最终视频"):
                logger.error("视频渲染失败")
                return False

    logger.info("🎉 工作流执行成功！")
    logger.info(f"📁 项目目录: output/{project_name}")

    # 根据不同类型显示不同的输出信息
    if classification_type in ["concept_explain"]:
        # concept_explain类型直接生成解释文件和代码
        if analysis_file:
            logger.info(f"📄 解释文件: {analysis_file}")
        # 尝试找到生成的代码文件
        info = classification["extracted_info"]
        topic = info.get("topic", "内容")
        code_dir = f"output/{topic}/code"
        if os.path.exists(code_dir):
            logger.info(f"💻 代码目录: {code_dir}")
        else:
            logger.info("💻 代码文件: 请查看项目目录中的代码文件")
    else:
        # 其他类型的标准输出（包括math_problem）
        if analysis_file:
            logger.info(f"📄 素材文件: {analysis_file}")
        video_dir = f"output/{project_name}/videos"
        if os.path.exists(video_dir):
            logger.info(f"🎬 视频目录: {video_dir}")
        else:
            logger.info("🎬 视频文件: 请查看项目目录中的视频文件")

    return True


def main():
    """主函数，执行完整工作流"""
    args = parse_args()

    try:
        # 设置日志级别
        if args.verbose:
            logger.remove()
            logger.add(sys.stderr, level="DEBUG")

        # 加载配置
        config = load_config(args.config)
        args.max_num_pages = config.get("material", {}).get("pdf", {}).get("max_num_pages", 30)
        logger.info(f"PDF最大页数: {args.max_num_pages}")

        # 检查是否需要智能意图分类
        material_config = config.get("material", {})
        intent_input = material_config.get("intent_input", {})
        has_intent_input = any(
            [
                intent_input.get("url", "").strip(),
                intent_input.get("file", "").strip(),
                intent_input.get("chat", "").strip(),
            ]
        )

        if has_intent_input and not args.skip_intent:
            logger.info("🚀 检测到intent_input配置，启用智能意图分类模式")

            # 特殊处理theorem模式
            if args.theorem_mode:
                logger.info("🧮 启用定理/概念解释专用模式")
                logger.info("📋 专用于处理概念/定理/原理解释类型的内容")

            # 步骤0: 智能意图分类
            success, classification = run_intent_classification(args.config)
            if not success:
                logger.error("智能意图分类失败，终止工作流")
                sys.exit(1)

            # theorem模式下验证分类类型
            if args.theorem_mode:
                classification_type = classification["type"]
                if classification_type not in ["concept_explain"]:
                    logger.warning(f"⚠️  当前内容类型为 '{classification_type}'，不是概念解释类型")
                    logger.warning("💡 theorem模式专用于处理概念/定理/原理解释(concept_explain)类型的内容")
                    logger.warning("🔄 将继续以普通模式处理...")
                else:
                    logger.info(f"✅ 检测到合适的类型 '{classification_type}'，继续处理")

            # 生成项目名称
            project_name = generate_project_name(classification)
            logger.info(f"📂 项目名称: {project_name}")
            logger.info("=" * 60)

            # 根据分类结果执行对应工作流
            success = run_workflow_for_classification(classification, project_name, args)
            if success:
                print("\n✨ 视频生成成功完成！")
                print(f"📁 项目目录: output/{project_name}")
                if args.theorem_mode:
                    if classification["type"] == "concept_explain":
                        print("🧮 概念解释处理完成！")
                        print("💻 代码文件已生成，可直接运行查看动画效果")
                    else:
                        print(f"🎬 视频目录: output/{project_name}/videos")
                else:
                    if classification["type"] == "concept_explain":
                        print("💻 代码文件已生成，可直接运行查看动画效果")
                    else:
                        print(f"🎬 视频目录: output/{project_name}/videos")
            else:
                logger.error("工作流执行失败")
                sys.exit(1)

        else:
            logger.info("🔄 使用传统配置模式")
            logger.error("传统配置模式暂未实现，请使用intent_input配置")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.warning("⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.exception(f"工作流执行过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logger.exception(f"工作流执行过程中发生错误: {str(e)}")
        sys.exit(1)
