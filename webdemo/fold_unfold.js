// 折叠/展开功能实现
function setupFoldUnfold() {
    console.log("开始设置折叠/展开功能...");
    
    // 获取所有子类别部分
    const subcategories = document.querySelectorAll('.subcategory');
    console.log(`找到 ${subcategories.length} 个子类别`);
    
    if (!subcategories || subcategories.length === 0) {
        console.warn("没有找到子类别元素，折叠功能无法初始化");
        return;
    }
    
    // 遍历每个子类别
    subcategories.forEach(subcategory => {
        // 获取当前子类别下的所有项目
        const projects = subcategory.querySelectorAll('.project-card');
        console.log(`子类别 ${subcategory.querySelector('h3').textContent} 下有 ${projects.length} 个项目`);
        
        // 如果项目数量大于9，只显示前9个，并添加"显示更多"按钮
        if (projects.length > 9) {
            // 创建"显示更多"按钮
            const showMoreBtn = document.createElement('div');
            showMoreBtn.className = 'show-more-btn';
            showMoreBtn.innerHTML = '<i class="fas fa-chevron-down"></i> 显示更多项目';
            
            // 创建"显示更少"按钮（默认隐藏）
            const showLessBtn = document.createElement('div');
            showLessBtn.className = 'show-less-btn hidden';
            showLessBtn.innerHTML = '<i class="fas fa-chevron-up"></i> 收起项目';
            
            // 隐藏超过9个的项目
            for (let i = 9; i < projects.length; i++) {
                projects[i].classList.add('hidden-project');
            }
            
            // 在子类别底部添加按钮
            subcategory.appendChild(showMoreBtn);
            subcategory.appendChild(showLessBtn);
            
            // 添加显示更多按钮的点击事件
            showMoreBtn.addEventListener('click', () => {
                console.log("点击了显示更多按钮");
                // 显示所有项目
                projects.forEach(project => {
                    project.classList.remove('hidden-project');
                });
                
                // 隐藏"显示更多"按钮，显示"显示更少"按钮
                showMoreBtn.classList.add('hidden');
                showLessBtn.classList.remove('hidden');
                
                // 平滑滚动到子类别
                subcategory.scrollIntoView({ behavior: 'smooth' });
            });
            
            // 添加显示更少按钮的点击事件
            showLessBtn.addEventListener('click', () => {
                console.log("点击了收起项目按钮");
                // 隐藏超过9个的项目
                for (let i = 9; i < projects.length; i++) {
                    projects[i].classList.add('hidden-project');
                }
                
                // 显示"显示更多"按钮，隐藏"显示更少"按钮
                showMoreBtn.classList.remove('hidden');
                showLessBtn.classList.add('hidden');
                
                // 平滑滚动到子类别
                subcategory.scrollIntoView({ behavior: 'smooth' });
            });
        }
    });
    
    console.log("折叠/展开功能设置完成");
}

// 在页面加载完成后执行初始化
document.addEventListener('DOMContentLoaded', function() {
    // 延迟执行确保页面元素都已加载
    setTimeout(setupFoldUnfold, 1500);
});
