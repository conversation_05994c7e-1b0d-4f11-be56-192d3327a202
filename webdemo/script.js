// 项目数据 - 第一部分（模型开发与研究类）
const projectsData = {
    "1.1": [
        {
            name: "DeepSeek-Coder",
            description: "强大的代码生成和理解模型，支持100多种编程语言",
            url: "https://github.com/deepseek-ai/DeepSeek-Coder",
            stars: "6.2k",
            language: "Python"
        },
        {
            name: "LLaMA",
            description: "Meta AI的大型语言模型，开放源代码和权重",
            url: "https://github.com/facebookresearch/llama",
            stars: "47.2k",
            language: "Python"
        },
        {
            name: "Transformers",
            description: "Hugging Face的先进自然语言处理库，提供各种预训练模型",
            url: "https://github.com/huggingface/transformers", 
            stars: "118k",
            language: "Python"
        },
        {
            name: "DeepSpeed",
            description: "微软开发的深度学习优化库，用于大规模模型训练",
            url: "https://github.com/microsoft/DeepSpeed",
            stars: "28.3k", 
            language: "Python"
        },
        {
            name: "Stable Diffusion",
            description: "文本到图像的生成模型，可创建高质量图像",
            url: "https://github.com/CompVis/stable-diffusion",
            stars: "61.2k",
            language: "Python"
        }
    ],
    "1.2": [
        {
            name: "LLaMA-Factory",
            description: "统一、高效的LLM微调框架，支持多种微调方法",
            url: "https://github.com/hiyouga/LLaMA-Factory",
            stars: "13.5k",
            language: "Python"
        },
        {
            name: "PEFT",
            description: "Hugging Face的参数高效微调库，支持LoRA、Prefix Tuning等",
            url: "https://github.com/huggingface/peft",
            stars: "12.7k",
            language: "Python"
        },
        {
            name: "FastChat",
            description: "用于训练、服务和评估LLM的开放平台",
            url: "https://github.com/lm-sys/FastChat",
            stars: "30.3k",
            language: "Python"
        },
        {
            name: "LoRA",
            description: "微软开发的低秩适应方法，高效微调大型模型",
            url: "https://github.com/microsoft/LoRA",
            stars: "9.2k",
            language: "Python"
        },
        {
            name: "unsloth",
            description: "加速LLM微调的工具，降低显存需求并提高训练速度",
            url: "https://github.com/unslothai/unsloth",
            stars: "5.7k",
            language: "Python"
        }
    ],
    "1.3": [
        {
            name: "vLLM",
            description: "高吞吐量且内存高效的LLM推理和服务引擎",
            url: "https://github.com/vllm-project/vllm",
            stars: "14.3k",
            language: "Python"
        },
        {
            name: "TensorRT-LLM",
            description: "NVIDIA的LLM推理优化工具包",
            url: "https://github.com/NVIDIA/TensorRT-LLM",
            stars: "4.7k",
            language: "Python"
        },
        {
            name: "llama.cpp",
            description: "纯C/C++实现的LLaMA模型推理库，无需GPU",
            url: "https://github.com/ggerganov/llama.cpp",
            stars: "49.1k",
            language: "C++"
        },
        {
            name: "CTranslate2",
            description: "快速批量CPU/GPU推理的转换器库",
            url: "https://github.com/OpenNMT/CTranslate2",
            stars: "1.9k",
            language: "C++"
        },
        {
            name: "NVIDIA TensorRT",
            description: "NVIDIA提供的高性能深度学习推理优化库",
            url: "https://github.com/NVIDIA/TensorRT",
            stars: "8.6k",
            language: "C++"
        }
    ],
    "1.4": [
        {
            name: "DatasetGPT",
            description: "使用GPT创建高质量的指令调优数据集",
            url: "https://github.com/explodinggradients/DatasetGPT",
            stars: "3.5k",
            language: "Python"
        },
        {
            name: "FastDup",
            description: "快速查找大型数据集中的重复和近似重复内容",
            url: "https://github.com/visual-layer/fastdup",
            stars: "2.8k",
            language: "Python"
        },
        {
            name: "CleanLab",
            description: "机器学习数据集中错误标签的检测和修正工具",
            url: "https://github.com/cleanlab/cleanlab",
            stars: "7.1k",
            language: "Python"
        },
        {
            name: "Ditto",
            description: "数据清洗和转换工具，专注于数据质量",
            url: "https://github.com/ditto-lang/ditto",
            stars: "1.2k",
            language: "Haskell"
        },
        {
            name: "Label Studio",
            description: "多类型数据标注平台，支持文本、图像、音频等",
            url: "https://github.com/HumanSignal/label-studio",
            stars: "15.2k",
            language: "Python"
        }
    ],
    "1.5": [
        {
            name: "LLaVA",
            description: "大型语言与视觉助手，视觉理解与对话能力",
            url: "https://github.com/haotian-liu/LLaVA",
            stars: "12.7k",
            language: "Python"
        },
        {
            name: "BLIP",
            description: "Bootstrap Language-Image Pre-training，跨模态预训练",
            url: "https://github.com/salesforce/BLIP",
            stars: "9.1k",
            language: "Python"
        },
        {
            name: "AudioCraft",
            description: "Meta AI开发的音频生成模型库，包括MusicGen",
            url: "https://github.com/facebookresearch/audiocraft",
            stars: "18.3k",
            language: "Python"
        },
        {
            name: "Video-LLaMA",
            description: "多模态视频理解与生成大型语言模型",
            url: "https://github.com/DAMO-NLP-SG/Video-LLaMA",
            stars: "2.1k",
            language: "Python"
        },
        {
            name: "CLIPSeg",
            description: "基于CLIP的零样本图像分割模型",
            url: "https://github.com/timojl/clipseg",
            stars: "2.5k",
            language: "Python"
        }
    ],
    "1.6": [
        {
            name: "BLOOM",
            description: "多语言大型语言模型，支持46种自然语言和13种编程语言",
            url: "https://github.com/bigscience-workshop/bloom",
            stars: "8.2k",
            language: "Python"
        },
        {
            name: "Helsinki-NLP/Opus-MT",
            description: "开源神经机器翻译系统，支持多种语言对",
            url: "https://github.com/Helsinki-NLP/Opus-MT",
            stars: "2.3k",
            language: "Python"
        },
        {
            name: "M2M-100",
            description: "Facebook的多对多多语言翻译模型，支持100种语言",
            url: "https://github.com/fairinternal/fairseq-py/tree/m2m-100",
            stars: "3.8k",
            language: "Python"
        },
        {
            name: "NLLB",
            description: "No Language Left Behind，多语言翻译项目",
            url: "https://github.com/facebookresearch/fairseq/tree/nllb",
            stars: "5.9k",
            language: "Python"
        },
        {
            name: "mBERT",
            description: "多语言BERT模型，预训练于104种语言",
            url: "https://github.com/google-research/bert/blob/master/multilingual.md",
            stars: "4.7k",
            language: "Python"
        }
    ],
    "1.7": [
        {
            name: "CodeLlama",
            description: "Meta AI的代码语言模型，基于Llama 2",
            url: "https://github.com/facebookresearch/codellama",
            stars: "12.5k",
            language: "Python"
        },
        {
            name: "WizardCoder",
            description: "代码大语言模型，在CodeLlama基础上微调",
            url: "https://github.com/nlpxucan/WizardLM/tree/main/WizardCoder",
            stars: "7.3k",
            language: "Python"
        },
        {
            name: "CodeGen",
            description: "Salesforce开发的代码生成模型",
            url: "https://github.com/salesforce/CodeGen",
            stars: "4.2k",
            language: "Python"
        },
        {
            name: "StarCoder",
            description: "Hugging Face和ServiceNow的开源代码大语言模型",
            url: "https://github.com/bigcode-project/starcoder",
            stars: "5.8k",
            language: "Python"
        },
        {
            name: "CodeBERT",
            description: "预训练的编程语言和自然语言理解模型",
            url: "https://github.com/microsoft/CodeBERT",
            stars: "8.9k",
            language: "Python"
        }
    ]
};

// 项目数据 - 第二部分（AI构建与集成类）
projectsData["2.1"] = [
    {
        name: "LangChain",
        description: "构建LLM应用的框架，支持链式调用、代理和记忆",
        url: "https://github.com/langchain-ai/langchain",
        stars: "72.5k",
        language: "Python"
    },
    {
        name: "Haystack",
        description: "DeepSet开发的端到端NLP框架，用于构建语义搜索系统",
        url: "https://github.com/deepset-ai/haystack",
        stars: "12.7k",
        language: "Python"
    },
    {
        name: "LlamaIndex",
        description: "构建LLM应用的数据框架，提供结构化检索与增强",
        url: "https://github.com/run-llama/llama_index",
        stars: "26.8k",
        language: "Python"
    },
    {
        name: "Semantic Kernel",
        description: "微软开发的AI编程SDK，集成LLM到应用程序",
        url: "https://github.com/microsoft/semantic-kernel",
        stars: "16.4k",
        language: "C#"
    },
    {
        name: "Guardrails AI",
        description: "LLM输出的验证与安全框架",
        url: "https://github.com/guardrails-ai/guardrails",
        stars: "3.8k",
        language: "Python"
    }
];

projectsData["2.2"] = [
    {
        name: "AutoGPT",
        description: "自主的GPT-4代理，能够完成复杂任务链",
        url: "https://github.com/Significant-Gravitas/AutoGPT",
        stars: "155k",
        language: "Python"
    },
    {
        name: "LangGraph",
        description: "构建和部署LLM代理的库，基于有限状态机",
        url: "https://github.com/langchain-ai/langgraph",
        stars: "3.2k",
        language: "Python"
    },
    {
        name: "BabyAGI",
        description: "基于LLM的自主任务管理系统",
        url: "https://github.com/yoheinakajima/babyagi",
        stars: "18.6k",
        language: "Python"
    },
    {
        name: "CrewAI",
        description: "企业级多代理协作框架",
        url: "https://github.com/joaomdmoura/crewAI",
        stars: "5.9k",
        language: "Python"
    },
    {
        name: "AgentGPT",
        description: "浏览器中运行的自主AI代理，无需本地设置",
        url: "https://github.com/reworkd/AgentGPT",
        stars: "27.9k",
        language: "TypeScript"
    }
];

projectsData["2.3"] = [
    {
        name: "Gradio",
        description: "用于快速创建ML模型Web界面的Python库",
        url: "https://github.com/gradio-app/gradio",
        stars: "25.3k",
        language: "Python"
    },
    {
        name: "Streamlit",
        description: "数据应用快速构建工具，广泛用于AI演示",
        url: "https://github.com/streamlit/streamlit",
        stars: "28.6k",
        language: "Python"
    },
    {
        name: "Botpress",
        description: "开源对话AI平台，用于构建复杂对话机器人",
        url: "https://github.com/botpress/botpress",
        stars: "11.8k",
        language: "TypeScript"
    },
    {
        name: "ChatBot UI",
        description: "开源ChatGPT克隆，漂亮的响应式界面",
        url: "https://github.com/mckaywrigley/chatbot-ui",
        stars: "19.7k",
        language: "TypeScript"
    },
    {
        name: "Flowise",
        description: "拖放式UI构建LLM工作流和应用",
        url: "https://github.com/FlowiseAI/Flowise",
        stars: "18.5k",
        language: "TypeScript"
    }
];

projectsData["2.4"] = [
    {
        name: "DSPy",
        description: "用于优化LLM提示和推理的框架，基于可编程流程",
        url: "https://github.com/stanfordnlp/dspy",
        stars: "6.7k",
        language: "Python"
    },
    {
        name: "LMQL",
        description: "LLM编程语言，提供结构化查询和约束",
        url: "https://github.com/eth-sri/lmql",
        stars: "4.9k",
        language: "Python"
    },
    {
        name: "PromptTools",
        description: "开源平台用于测试和评估提示及模型",
        url: "https://github.com/hegelai/prompttools",
        stars: "3.1k",
        language: "Python"
    },
    {
        name: "Outlines",
        description: "通过约束引导LLM生成高质量输出",
        url: "https://github.com/outlines-dev/outlines",
        stars: "5.2k",
        language: "Python"
    },
    {
        name: "Guidance",
        description: "使用简洁高效的方式控制LLM生成内容",
        url: "https://github.com/guidance-ai/guidance",
        stars: "14.3k",
        language: "Python"
    }
];

projectsData["2.5"] = [
    {
        name: "OpenCompass",
        description: "全面的LLM评估平台，包含多维度测试",
        url: "https://github.com/open-compass/opencompass",
        stars: "3.7k",
        language: "Python"
    },
    {
        name: "HELM",
        description: "斯坦福开发的全面语言模型评估平台",
        url: "https://github.com/stanford-crfm/helm",
        stars: "2.8k",
        language: "Python"
    },
    {
        name: "LangSmith",
        description: "LangChain开发的应用和LLM调试与评估平台",
        url: "https://github.com/langchain-ai/langsmith",
        stars: "1.5k",
        language: "Python"
    },
    {
        name: "TruLens",
        description: "评估和跟踪LLM应用表现的框架",
        url: "https://github.com/truera/trulens",
        stars: "2.2k",
        language: "Python"
    },
    {
        name: "Ragas",
        description: "评估RAG系统性能的框架",
        url: "https://github.com/explodinggradients/ragas",
        stars: "3.9k",
        language: "Python"
    }
];

projectsData["2.6"] = [
    {
        name: "Chroma",
        description: "开源嵌入式数据库，为RAG应用设计",
        url: "https://github.com/chroma-core/chroma",
        stars: "11.2k",
        language: "Python"
    },
    {
        name: "txtai",
        description: "强大的搜索和NLP引擎，专注于RAG",
        url: "https://github.com/neuml/txtai",
        stars: "5.1k",
        language: "Python"
    },
    {
        name: "NVIDIA NeMo Guardrails",
        description: "为会话AI应用添加保障机制的工具箱",
        url: "https://github.com/NVIDIA/NeMo-Guardrails",
        stars: "3.8k",
        language: "Python"
    },
    {
        name: "Milvus",
        description: "向量数据库，广泛用于构建RAG系统",
        url: "https://github.com/milvus-io/milvus",
        stars: "24.6k",
        language: "Go"
    },
    {
        name: "GPTCache",
        description: "LLM的语义缓存系统，减少API调用",
        url: "https://github.com/zilliztech/gptcache",
        stars: "3.4k",
        language: "Python"
    }
];

// 项目数据 - 第三部分（AI实用工具类）
projectsData["3.1"] = [
    {
        name: "aider",
        description: "用于与LLM协作进行软件开发的工具",
        url: "https://github.com/paul-gauthier/aider",
        stars: "9.4k",
        language: "Python"
    },
    {
        name: "CodeWhisperer",
        description: "AI编程工具，类似于GitHub Copilot",
        url: "https://github.com/aws/aws-toolkit-vscode",
        stars: "4.2k",
        language: "TypeScript"
    },
    {
        name: "Continue",
        description: "为任意IDE提供的开源编码助手",
        url: "https://github.com/continuedev/continue",
        stars: "7.1k",
        language: "TypeScript"
    },
    {
        name: "Tabby",
        description: "自托管的AI编码助手，支持私有代码库",
        url: "https://github.com/TabbyML/tabby",
        stars: "14.3k",
        language: "Rust"
    },
    {
        name: "Cursor",
        description: "基于GPT的编辑器，专为AI辅助编程设计",
        url: "https://github.com/getcursor/cursor",
        stars: "16.7k",
        language: "TypeScript"
    }
];

projectsData["3.2"] = [
    {
        name: "ChatGPT Retrieval Plugin",
        description: "OpenAI文档检索插件，增强对话系统",
        url: "https://github.com/openai/chatgpt-retrieval-plugin",
        stars: "18.3k",
        language: "Python"
    },
    {
        name: "Perplexity",
        description: "基于LLM的搜索引擎，提供信息检索与综合",
        url: "https://github.com/perplexity-ai/perplexity",
        stars: "5.1k",
        language: "TypeScript"
    },
    {
        name: "SearchGPT",
        description: "基于GPT的本地文件和网页搜索引擎",
        url: "https://github.com/williamfzc/searchgpt",
        stars: "3.4k",
        language: "Python"
    },
    {
        name: "GPTRouter",
        description: "智能路由请求到不同的LLM提供商",
        url: "https://github.com/zylon-ai/gpt-router",
        stars: "2.9k",
        language: "Python"
    },
    {
        name: "NLP Cloud",
        description: "用于生产环境的NLP API服务",
        url: "https://github.com/nlpcloud/nlpcloud",
        stars: "1.2k",
        language: "Python"
    }
];

projectsData["3.3"] = [
    {
        name: "n8n",
        description: "工作流自动化平台，支持集成数百种服务",
        url: "https://github.com/n8n-io/n8n",
        stars: "37.9k",
        language: "TypeScript"
    },
    {
        name: "Activepieces",
        description: "开源业务流程自动化平台，类似Zapier",
        url: "https://github.com/activepieces/activepieces",
        stars: "5.8k",
        language: "TypeScript"
    },
    {
        name: "Robocorp",
        description: "开源自动化工具，用于流程自动化",
        url: "https://github.com/robocorp/robocorp",
        stars: "2.7k",
        language: "Python"
    },
    {
        name: "AutoPilot",
        description: "AI驱动的流程自动化解决方案",
        url: "https://github.com/autopilot-ai/autopilot",
        stars: "1.6k",
        language: "Python"
    },
    {
        name: "Huginn",
        description: "构建代理监控和处理事件的系统",
        url: "https://github.com/huginn/huginn",
        stars: "39.2k",
        language: "Ruby"
    }
];

// 项目数据 - 第四部分（后续类别）
projectsData["3.4"] = [
    {
        name: "Open-Assistant",
        description: "开源对话AI助手，类似ChatGPT",
        url: "https://github.com/LAION-AI/Open-Assistant",
        stars: "35.2k",
        language: "Python"
    },
    {
        name: "LocalAI",
        description: "在本地运行LLM的API，兼容OpenAI API",
        url: "https://github.com/go-skynet/LocalAI",
        stars: "17.5k",
        language: "Go"
    },
    {
        name: "Jan",
        description: "将AI聊天带到桌面端的应用",
        url: "https://github.com/janhq/jan",
        stars: "8.6k",
        language: "TypeScript"
    },
    {
        name: "GPT4All",
        description: "本地运行的助手，无需网络连接",
        url: "https://github.com/nomic-ai/gpt4all",
        stars: "59.3k",
        language: "C++"
    },
    {
        name: "ChatRWKV",
        description: "类似ChatGPT但基于RWKV架构的聊天机器人",
        url: "https://github.com/BlinkDL/ChatRWKV",
        stars: "9.7k",
        language: "Python"
    }
];

// 添加剩余的类别项目数据
// AI实用工具-图像创作工具
projectsData["3.5"] = [
    {
        name: "Stable Diffusion Web UI",
        description: "流行的Stable Diffusion模型Web界面",
        url: "https://github.com/AUTOMATIC1111/stable-diffusion-webui",
        stars: "114k",
        language: "Python"
    },
    {
        name: "ComfyUI",
        description: "Stable Diffusion的强大GUI，支持节点编辑",
        url: "https://github.com/comfyanonymous/ComfyUI",
        stars: "20.8k",
        language: "Python"
    },
    {
        name: "Upscayl",
        description: "AI图像放大工具，提高图像质量",
        url: "https://github.com/upscayl/upscayl",
        stars: "23.5k",
        language: "TypeScript"
    },
    {
        name: "InvokeAI",
        description: "开源图像生成系统，基于Stable Diffusion",
        url: "https://github.com/invoke-ai/InvokeAI",
        stars: "21.3k",
        language: "Python"
    },
    {
        name: "ControlNet",
        description: "Stable Diffusion的控制模块，增强控制能力",
        url: "https://github.com/lllyasviel/ControlNet",
        stars: "26.7k",
        language: "Python"
    }
];

// AI部署与基础设施-向量数据库
projectsData["4.1"] = [
    {
        name: "Qdrant",
        description: "向量相似性搜索引擎",
        url: "https://github.com/qdrant/qdrant",
        stars: "16.4k",
        language: "Rust"
    },
    {
        name: "Milvus",
        description: "开源向量数据库，亿级搜索",
        url: "https://github.com/milvus-io/milvus",
        stars: "24.6k",
        language: "Go"
    },
    {
        name: "Weaviate",
        description: "开源向量搜索引擎",
        url: "https://github.com/weaviate/weaviate",
        stars: "9.2k",
        language: "Go"
    },
    {
        name: "Faiss",
        description: "Facebook AI的高效相似性搜索库",
        url: "https://github.com/facebookresearch/faiss",
        stars: "26.8k",
        language: "C++"
    },
    {
        name: "Elasticsearch",
        description: "分布式搜索引擎，增加了向量搜索功能",
        url: "https://github.com/elastic/elasticsearch",
        stars: "67.1k",
        language: "Java"
    }
];

// AI部署与基础设施-模型部署工具
projectsData["4.2"] = [
    {
        name: "Triton Inference Server",
        description: "NVIDIA的高性能推理服务器",
        url: "https://github.com/triton-inference-server/server",
        stars: "7.2k",
        language: "C++"
    },
    {
        name: "BentoML",
        description: "ML模型服务框架",
        url: "https://github.com/bentoml/BentoML",
        stars: "6.3k",
        language: "Python"
    },
    {
        name: "Ray Serve",
        description: "可扩展模型部署框架",
        url: "https://github.com/ray-project/ray",
        stars: "29.1k",
        language: "Python"
    },
    {
        name: "TF Serving",
        description: "TensorFlow模型部署系统",
        url: "https://github.com/tensorflow/serving",
        stars: "6.1k",
        language: "C++"
    },
    {
        name: "Torchserve",
        description: "PyTorch模型服务框架",
        url: "https://github.com/pytorch/serve",
        stars: "3.9k",
        language: "Python"
    }
];

// AI学习与资源-教程与课程
projectsData["5.1"] = [
    {
        name: "Awesome LLM",
        description: "LLM相关资源的精选列表",
        url: "https://github.com/Hannibal046/Awesome-LLM",
        stars: "12.3k",
        language: "Markdown"
    },
    {
        name: "LLM Course",
        description: "免费的大型语言模型课程",
        url: "https://github.com/mlabonne/llm-course",
        stars: "19.6k",
        language: "Jupyter Notebook"
    },
    {
        name: "AI Guidelines",
        description: "各种AI模型使用的最佳实践指南",
        url: "https://github.com/LAION-AI/Open-Assistant/wiki/AI-Guidelines",
        stars: "3.7k",
        language: "Markdown"
    },
    {
        name: "Hugging Face Course",
        description: "Hugging Face官方NLP课程",
        url: "https://github.com/huggingface/course",
        stars: "5.9k",
        language: "Jupyter Notebook"
    },
    {
        name: "Prompt Engineering Guide",
        description: "提示工程指南，最佳实践和技巧",
        url: "https://github.com/dair-ai/Prompt-Engineering-Guide",
        stars: "40.1k",
        language: "Markdown"
    }
];

// AI实用工具-视频
projectsData["3.6"] = [
    {
        name: "Descript",
        description: "视频编辑和文本生成工具",
        url: "https://github.com/descriptinc/descript-audio-codec",
        stars: "1.4k",
        language: "Python"
    },
    {
        name: "Runway",
        description: "AI视频生成和编辑工具",
        url: "https://github.com/runwayml/RunwayML",
        stars: "6.3k",
        language: "JavaScript"
    },
    {
        name: "Remotion",
        description: "使用React创建视频的工具",
        url: "https://github.com/remotion-dev/remotion",
        stars: "19.1k",
        language: "TypeScript"
    },
    {
        name: "Sora-Demos",
        description: "OpenAI Sora模型视频生成展示",
        url: "https://github.com/hpcaitech/Open-Sora",
        stars: "7.2k",
        language: "Python"
    },
    {
        name: "AnimatedDrawings",
        description: "将儿童绘画转换为动画角色",
        url: "https://github.com/facebookresearch/AnimatedDrawings",
        stars: "12.5k",
        language: "Python"
    }
];

// AI实用工具-文本
projectsData["3.7"] = [
    {
        name: "NovelAI",
        description: "AI辅助的小说和故事创作工具",
        url: "https://github.com/NovelAI/novelai-web",
        stars: "2.4k",
        language: "TypeScript"
    },
    {
        name: "GPT-Writer",
        description: "基于GPT的文本创作助手",
        url: "https://github.com/EssayKillerBrain/EssayKiller",
        stars: "8.6k",
        language: "Python"
    },
    {
        name: "Dramatron",
        description: "AI辅助戏剧和剧本创作工具",
        url: "https://github.com/deepmind/dramatron",
        stars: "3.1k",
        language: "Python"
    },
    {
        name: "AI Dungeon",
        description: "AI驱动的交互式文字冒险游戏",
        url: "https://github.com/AIDungeon/AIDungeon",
        stars: "7.5k",
        language: "JavaScript"
    },
    {
        name: "PolyglotBot",
        description: "翻译和多语言内容创作助手",
        url: "https://github.com/liou666/polyglot",
        stars: "4.3k",
        language: "TypeScript"
    }
];

// AI实用工具-数据管理与组织
projectsData["3.8"] = [
    {
        name: "LabelImg",
        description: "图形图像标注工具",
        url: "https://github.com/HumanSignal/labelImg",
        stars: "20.9k",
        language: "Python"
    },
    {
        name: "DVC",
        description: "数据版本控制系统",
        url: "https://github.com/iterative/dvc",
        stars: "12.4k",
        language: "Python"
    },
    {
        name: "Weights & Biases",
        description: "机器学习实验跟踪工具",
        url: "https://github.com/wandb/wandb",
        stars: "6.3k",
        language: "Python"
    },
    {
        name: "Datasette",
        description: "探索和发布数据的工具",
        url: "https://github.com/simonw/datasette",
        stars: "8.1k",
        language: "Python"
    },
    {
        name: "Metabase",
        description: "简单数据可视化和分析工具",
        url: "https://github.com/metabase/metabase",
        stars: "35.6k",
        language: "Clojure"
    }
];

// AI部署与基础设施-计算资源管理
projectsData["4.3"] = [
    {
        name: "Kubernetes",
        description: "容器编排系统，广泛用于AI工作负载",
        url: "https://github.com/kubernetes/kubernetes",
        stars: "103k",
        language: "Go"
    },
    {
        name: "Ray",
        description: "分布式计算框架，优化AI工作负载",
        url: "https://github.com/ray-project/ray",
        stars: "29.1k",
        language: "Python"
    },
    {
        name: "Horovod",
        description: "Uber开发的分布式深度学习训练框架",
        url: "https://github.com/horovod/horovod",
        stars: "13.8k",
        language: "Python"
    },
    {
        name: "Slurm",
        description: "高性能计算集群作业调度系统",
        url: "https://github.com/SchedMD/slurm",
        stars: "2.3k",
        language: "C"
    },
    {
        name: "KubeFlow",
        description: "Kubernetes上的机器学习工具包",
        url: "https://github.com/kubeflow/kubeflow",
        stars: "13.6k",
        language: "Python"
    }
];

// AI部署与基础设施-监控与可观测性
projectsData["4.4"] = [
    {
        name: "MLflow",
        description: "机器学习生命周期管理平台",
        url: "https://github.com/mlflow/mlflow",
        stars: "16.4k",
        language: "Python"
    },
    {
        name: "Prometheus",
        description: "监控系统和时间序列数据库",
        url: "https://github.com/prometheus/prometheus",
        stars: "51.2k",
        language: "Go"
    },
    {
        name: "Grafana",
        description: "可观测性和数据可视化平台",
        url: "https://github.com/grafana/grafana",
        stars: "58.3k",
        language: "TypeScript"
    },
    {
        name: "WhyLogs",
        description: "AI数据和模型监控工具",
        url: "https://github.com/whylabs/whylogs",
        stars: "2.8k",
        language: "Python"
    },
    {
        name: "DeepChecks",
        description: "测试和验证ML模型和数据",
        url: "https://github.com/deepchecks/deepchecks",
        stars: "3.5k",
        language: "Python"
    }
];

// AI部署与基础设施-开发工具与集成
projectsData["4.5"] = [
    {
        name: "VS Code",
        description: "微软的代码编辑器，支持多种AI工具和扩展",
        url: "https://github.com/microsoft/vscode",
        stars: "152k",
        language: "TypeScript"
    },
    {
        name: "JupyterLab",
        description: "交互式开发环境，广泛用于数据科学和AI",
        url: "https://github.com/jupyterlab/jupyterlab",
        stars: "14.6k",
        language: "TypeScript"
    },
    {
        name: "Airflow",
        description: "工作流管理平台，用于编排复杂的AI管道",
        url: "https://github.com/apache/airflow",
        stars: "32.7k",
        language: "Python"
    },
    {
        name: "DVC Studio",
        description: "数据版本控制和ML工作流管理",
        url: "https://github.com/iterative/dvc",
        stars: "12.4k",
        language: "Python"
    },
    {
        name: "Visual Studio AI Tools",
        description: "Visual Studio中的AI开发工具",
        url: "https://github.com/microsoft/vs-tools-for-ai",
        stars: "1.9k",
        language: "C#"
    }
];

// AI部署与基础设施-数据管理平台
projectsData["4.6"] = [
    {
        name: "Pachyderm",
        description: "数据版本控制和数据管道",
        url: "https://github.com/pachyderm/pachyderm",
        stars: "6.3k",
        language: "Go"
    },
    {
        name: "dbt",
        description: "数据转换工具",
        url: "https://github.com/dbt-labs/dbt-core",
        stars: "8.7k",
        language: "Python"
    },
    {
        name: "Feast",
        description: "特征存储，用于机器学习",
        url: "https://github.com/feast-dev/feast",
        stars: "5.1k",
        language: "Python"
    },
    {
        name: "Great Expectations",
        description: "数据质量验证和文档工具",
        url: "https://github.com/great-expectations/great_expectations",
        stars: "9.2k",
        language: "Python"
    },
    {
        name: "Kedro",
        description: "数据科学项目的生产就绪代码",
        url: "https://github.com/kedro-org/kedro",
        stars: "9.1k",
        language: "Python"
    }
];

// AI学习与资源-资源集合与列表
projectsData["5.2"] = [
    {
        name: "Awesome Machine Learning",
        description: "精选机器学习框架、库和软件列表",
        url: "https://github.com/josephmisiti/awesome-machine-learning",
        stars: "62.3k",
        language: "Markdown"
    },
    {
        name: "Papers with Code",
        description: "机器学习论文及其实现代码",
        url: "https://github.com/paperswithcode/paperswithcode-data",
        stars: "5.9k",
        language: "Python"
    },
    {
        name: "Awesome Deep Learning",
        description: "深度学习资源精选列表",
        url: "https://github.com/ChristosChristofidis/awesome-deep-learning",
        stars: "23.7k",
        language: "Markdown"
    },
    {
        name: "Awesome LLM",
        description: "LLM相关资源的精选列表",
        url: "https://github.com/Hannibal046/Awesome-LLM",
        stars: "12.3k",
        language: "Markdown"
    },
    {
        name: "ML-YouTube-Courses",
        description: "机器学习相关YouTube课程列表",
        url: "https://github.com/dair-ai/ML-YouTube-Courses",
        stars: "12.1k",
        language: "Markdown"
    }
];

// AI学习与资源-示例与演示
projectsData["5.3"] = [
    {
        name: "ML-Showcase",
        description: "机器学习项目和演示集合",
        url: "https://github.com/huggingface/transformers",
        stars: "118k",
        language: "Python"
    },
    {
        name: "TensorFlow Examples",
        description: "TensorFlow官方示例集合",
        url: "https://github.com/tensorflow/examples",
        stars: "13.7k",
        language: "Python"
    },
    {
        name: "PyTorch Examples",
        description: "PyTorch官方示例和教程",
        url: "https://github.com/pytorch/examples",
        stars: "20.3k",
        language: "Python"
    },
    {
        name: "HuggingFace Spaces",
        description: "机器学习应用演示集合",
        url: "https://github.com/huggingface/api-inference-community",
        stars: "1.2k",
        language: "Python"
    },
    {
        name: "Scikit-learn Examples",
        description: "Scikit-learn示例集合",
        url: "https://github.com/scikit-learn/scikit-learn/tree/main/examples",
        stars: "57.1k",
        language: "Python"
    }
];

// 将星标数量字符串（如"6.2k"）转换为数字
function parseStarCount(starsStr) {
    let cleanStr = starsStr.toLowerCase().trim();
    if (cleanStr.endsWith('k')) {
        // 处理千级单位，如 "6.2k" => 6200
        return parseFloat(cleanStr.replace('k', '')) * 1000;
    } else if (cleanStr.endsWith('m')) {
        // 处理百万级单位，如 "1.2m" => 1200000
        return parseFloat(cleanStr.replace('m', '')) * 1000000;
    } else {
        // 直接返回数字
        return parseInt(cleanStr.replace(/[^\d]/g, ''));
    }
}

// 创建项目元素
function createProjectElement(project) {
    const projectCard = document.createElement('div');
    projectCard.className = 'project-card';
    
    const projectName = document.createElement('h4');
    projectName.className = 'project-name';
    projectName.innerHTML = `<a href="${project.url}" target="_blank">${project.name}</a>`;
    
    const projectDesc = document.createElement('p');
    projectDesc.className = 'project-description';
    projectDesc.textContent = project.description;
    
    const projectMeta = document.createElement('div');
    projectMeta.className = 'project-meta';
    
    const stars = document.createElement('span');
    stars.className = 'stars';
    stars.innerHTML = `<i class="fas fa-star"></i> ${project.stars}`;
    
    const lang = document.createElement('span');
    lang.className = 'language';
    lang.textContent = project.language;
    
    projectMeta.appendChild(stars);
    projectMeta.appendChild(lang);
    
    projectCard.appendChild(projectName);
    projectCard.appendChild(projectDesc);
    projectCard.appendChild(projectMeta);
    
    // 解析星标数量并设置为数据属性
    const starCount = parseStarCount(project.stars);
    projectCard.setAttribute('data-stars', starCount);
    projectCard.setAttribute('data-name', project.name.toLowerCase());
    projectCard.setAttribute('data-language', project.language);
    
    return projectCard;
}

// 从CSV文件加载的额外项目
document.write('<script src="add_projects.js"></script>');
document.write('<script src="fold_unfold.js"></script>');
document.write('<link rel="stylesheet" href="fold_unfold.css">');

// 文档加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log("文档加载完成，开始初始化...");
    loadInitialProjects();
    
    // 初始化搜索、筛选和排序功能
    document.getElementById('search-input').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        filterProjects(searchTerm);
    });
    
    document.getElementById('language-filter').addEventListener('change', function() {
        const selectedLanguage = this.value;
        filterProjectsByLanguage(selectedLanguage);
    });
    
    document.getElementById('sort-option').addEventListener('change', function() {
        const sortOption = this.value;
        console.log(`排序方式改变为: ${sortOption}`);
        sortAllProjects(sortOption);
    });
    
    // 初始化Tab切换
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const category = this.getAttribute('data-category');
            console.log(`点击了分类标签: ${category}`);
            filterByCategory(category);
        });
    });
});

// 按编程语言筛选项目
function filterProjectsByLanguage(language) {
    const projectCards = document.querySelectorAll('.project-card');
    
    projectCards.forEach(card => {
        if (language === 'all' || card.getAttribute('data-language') === language) {
            card.style.display = '';
        } else {
            card.style.display = 'none';
        }
    });
}

// 加载初始项目数据
function loadInitialProjects() {
    console.log("开始加载预定义项目数据...");
    console.log(`项目数据集中包含 ${Object.keys(projectsData).length} 个子类别`);
    
    // 先检查所有子类别的容器是否存在
    for (const subcategoryId in projectsData) {
        // 转义点号，因为CSS选择器中点号有特殊含义
        const escapedId = subcategoryId.replace(".", "\\.");
        const selector = `#subcategory-${escapedId} .projects-container`;
        const container = document.querySelector(selector);
        if (!container) {
            console.error(`未找到子类别容器: ${selector}`);
        } else {
            console.log(`找到子类别容器: ${selector}`);
        }
    }
    
    let totalProjects = 0;
    
    // 遍历所有子类别，向每个子类别添加项目
    for (const subcategoryId in projectsData) {
        const subcategoryProjects = projectsData[subcategoryId];
        // 转义点号，因为CSS选择器中点号有特殊含义
        const escapedId = subcategoryId.replace(".", "\\.");
        const projectsContainer = document.querySelector(`#subcategory-${escapedId} .projects-container`);
        
        if (projectsContainer) {
            console.log(`为子类别 ${subcategoryId} 添加 ${subcategoryProjects.length} 个项目`);
            
            subcategoryProjects.forEach(project => {
                const projectElement = createProjectElement(project);
                
                projectsContainer.appendChild(projectElement);
                totalProjects++;
            });
        } else {
            console.error(`未找到子类别 ${subcategoryId} 的项目容器`);
        }
    }
    
    console.log(`总共添加了 ${totalProjects} 个项目`);
    
    // 确保首个分类的所有内容都被显示
    document.querySelectorAll('.category').forEach(category => {
        category.classList.remove('active');
    });
    document.getElementById('category-1').classList.add('active');
    
    // 确保对应的TAB被激活
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector('.tab-btn[data-category="1"]').classList.add('active');
    
    // 默认排序为星数降序
    sortAllProjects('stars-desc');
}

// 初始化过滤器
function initializeFilters() {
    // 如果过滤器已经存在实现，保留现有逻辑
}

// 初始化排序
function initializeSort() {
    // 如果排序已经存在实现，保留现有逻辑
}

// 基于搜索词过滤项目
function filterProjects(searchTerm) {
    const projectCards = document.querySelectorAll('.project-card');
    
    projectCards.forEach(card => {
        const name = card.getAttribute('data-name');
        if (name.includes(searchTerm)) {
            card.style.display = '';
        } else {
            card.style.display = 'none';
        }
    });
}

// 基于分类过滤项目
function filterByCategory(category) {
    const categories = document.querySelectorAll('.category');
    
    // 先移除所有tab的active类
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // 给当前点击的tab添加active类
    document.querySelector(`.tab-btn[data-category="${category}"]`).classList.add('active');
    
    // 隐藏所有category内容
    categories.forEach(cat => {
        cat.classList.remove('active');
    });
    
    // 显示对应的category内容
    document.getElementById(`category-${category}`).classList.add('active');
}

// 排序所有项目
function sortAllProjects(sortType) {
    const containers = document.querySelectorAll('.projects-container');
    
    containers.forEach(container => {
        const projects = Array.from(container.children);
        
        projects.sort((a, b) => {
            switch(sortType) {
                case 'stars-desc':
                    return parseInt(b.getAttribute('data-stars')) - parseInt(a.getAttribute('data-stars'));
                case 'stars-asc':
                    return parseInt(a.getAttribute('data-stars')) - parseInt(b.getAttribute('data-stars'));
                case 'name-asc':
                    return a.getAttribute('data-name').localeCompare(b.getAttribute('data-name'));
                case 'name-desc':
                    return b.getAttribute('data-name').localeCompare(a.getAttribute('data-name'));
                default:
                    return 0;
            }
        });
        
        // 清空容器并重新添加排序后的项目
        container.innerHTML = '';
        projects.forEach(project => {
            container.appendChild(project);
        });
    });
}