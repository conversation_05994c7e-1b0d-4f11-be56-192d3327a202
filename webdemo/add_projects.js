// 从CSV文件生成的额外项目数据

// 为子类别 1.2 添加 119 个项目
projectsData["1.2"] = [
    {
        name: "tensorflow",
        description: "An 开源机器学习框架 for Everyone",
        url: "https://github.com/tensorflow/tensorflow",
        stars: "189.2k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "transformers",
        description: "🤗 Transformer模型s: State-of-the-art 机器学习 for Pytorch, TensorFlow, and JAX.",
        url: "https://github.com/huggingface/transformers",
        stars: "142.5k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "pytorch",
        description: "优质开源AI项目",
        url: "https://github.com/pytorch/pytorch",
        stars: "88.7k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "LLaMA-Factory",
        description: "Unified Efficient 模型微调 of 100+ 大型语言模型s & VLMs (ACL 2024)",
        url: "https://github.com/hiyouga/LLaMA-Factory",
        stars: "46.1k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "ColossalAI",
        description: "优质开源AI项目",
        url: "https://github.com/hpcaitech/ColossalAI",
        stars: "40.7k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "TTS",
        description: "🐸💬 - a 深度学习 toolkit for 文本到语音转换, battle-tested in research and production (工具)",
        url: "https://github.com/coqui-ai/TTS",
        stars: "39.1k",
        language: "TypeScript",
        visible_by_default: true
    },
    {
        name: "FastChat",
        description: "An open platform for training, 模型部署, and evaluating 大型语言模型s. Release repo for Vicuna and Chatbot Arena.",
        url: "https://github.com/lm-sys/FastChat",
        stars: "38.3k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "DeepSpeed",
        description: "DeepSpeed is a 深度学习 optimization library that makes distributed training and 推理优化 easy, efficient, and effective. (库)",
        url: "https://github.com/microsoft/DeepSpeed",
        stars: "37.8k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "ray",
        description: "优质开源AI项目",
        url: "https://github.com/ray-project/ray",
        stars: "36.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "google-research",
        description: "优质开源AI项目",
        url: "https://github.com/google-research/google-research",
        stars: "35.3k",
        language: "Go",
        visible_by_default: false
    },
    {
        name: "jax",
        description: "开源AI项目：TPU, and more",
        url: "https://github.com/google/jax",
        stars: "31.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "mmdetection",
        description: "优质开源AI项目",
        url: "https://github.com/open-mmlab/mmdetection",
        stars: "30.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "tinygrad",
        description: "优质开源AI项目",
        url: "https://github.com/tinygrad/tinygrad",
        stars: "28.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "diffusers",
        description: "优质开源AI项目",
        url: "https://github.com/huggingface/diffusers",
        stars: "28.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "DeepSpeech",
        description: "DeepSpeech is an 开源 embedded (offline, on-device) 语音到文本转换 engine which can run in real time on devices ranging from a Raspberry Pi 4 to high power GPU...",
        url: "https://github.com/mozilla/DeepSpeech",
        stars: "26.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "mojo",
        description: "优质开源AI项目",
        url: "https://github.com/modularml/mojo",
        stars: "23.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "unilm",
        description: "优质开源AI项目",
        url: "https://github.com/microsoft/unilm",
        stars: "21.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "mlx",
        description: "优质开源AI项目",
        url: "https://github.com/ml-explore/mlx",
        stars: "20.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Bend",
        description: "优质开源AI项目",
        url: "https://github.com/HigherOrderCO/Bend",
        stars: "18.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "minimind",
        description: "🚀🚀 「大模型」2小时完全从0训练26M的小参数GPT！🌏 Train a 26M-parameter GPT from scratch in just 2h!",
        url: "https://github.com/jingyaogong/minimind",
        stars: "18.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "peft",
        description: "优质开源AI项目",
        url: "https://github.com/huggingface/peft",
        stars: "18.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "candle",
        description: "优质开源AI项目",
        url: "https://github.com/huggingface/candle",
        stars: "17.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "NeMo",
        description: "A scalable generative AI框架 built for researchers and developers working on 大型语言模型s, 多模态, and Speech AI (Automatic Speech Recognition and 文本到语音转换)",
        url: "https://github.com/NVIDIA/NeMo",
        stars: "13.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "trl",
        description: "优质开源AI项目",
        url: "https://github.com/huggingface/trl",
        stars: "13.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "PaddleNLP",
        description: "Easy-to-use and powerful 大型语言模型 and SLM library with awesome model zoo. (库)",
        url: "https://github.com/PaddlePaddle/PaddleNLP",
        stars: "12.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "litgpt",
        description: "20+ high-performance 大型语言模型s with recipes to pretrain, finetune and deploy at scale.",
        url: "https://github.com/Lightning-AI/litgpt",
        stars: "11.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "PaddleSpeech",
        description: "开源AI项目：Streaming ASR with punctuation, Streaming TTS with text frontend, Speaker Verification System, End-to-End Speech Translation and Keyword Spotti...",
        url: "https://github.com/PaddlePaddle/PaddleSpeech",
        stars: "11.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LAVIS",
        description: "优质开源AI项目",
        url: "https://github.com/salesforce/LAVIS",
        stars: "10.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "qlora",
        description: "QLoRA: Efficient Finetuning of Quantized 大型语言模型s",
        url: "https://github.com/artidoro/qlora",
        stars: "10.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "burn",
        description: "Burn is a new comprehensive dynamic 深度学习 Framework built using Rust with extreme flexibility, compute efficiency and portability as its primary goals....",
        url: "https://github.com/tracel-ai/burn",
        stars: "10.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "nerfstudio",
        description: "优质开源AI项目",
        url: "https://github.com/nerfstudio-project/nerfstudio",
        stars: "10.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "sonnet",
        description: "优质开源AI项目",
        url: "https://github.com/google-deepmind/sonnet",
        stars: "9.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "TTS",
        description: "开源AI项目：tts)",
        url: "https://github.com/mozilla/TTS",
        stars: "9.8k",
        language: "TypeScript",
        visible_by_default: false
    },
    {
        name: "xformers",
        description: "优质开源AI项目",
        url: "https://github.com/facebookresearch/xformers",
        stars: "9.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "FlagEmbedding",
        description: "Retrieval and Retrieval-augmented 大型语言模型s",
        url: "https://github.com/FlagOpen/FlagEmbedding",
        stars: "9.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "pytorch3d",
        description: "PyTorch3D is FAIR's library of reusable components for 深度学习 with 3D data (库)",
        url: "https://github.com/facebookresearch/pytorch3d",
        stars: "9.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "machinelearning",
        description: "ML.NET is an 开源 and cross-platform 机器学习 framework for .NET. (框架)",
        url: "https://github.com/dotnet/machinelearning",
        stars: "9.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "axolotl",
        description: "优质开源AI项目",
        url: "https://github.com/OpenAccess-AI-Collective/axolotl",
        stars: "9.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Amphion",
        description: "开源AI项目：) is a toolkit for Audio, Music, and Speech Generation. Its purpose is to support reproducible research and help junior researchers and enginee...",
        url: "https://github.com/open-mmlab/Amphion",
        stars: "8.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "accelerate",
        description: "优质开源AI项目",
        url: "https://github.com/huggingface/accelerate",
        stars: "8.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LMFlow",
        description: "An Extensible Toolkit for Finetuning and 推理优化 of Large Foundation Models. Large Models for All. (工具)",
        url: "https://github.com/OptimalScale/LMFlow",
        stars: "8.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "BELLE",
        description: "BELLE: Be Everyone's 大型语言模型 Engine（开源中文对话大模型）",
        url: "https://github.com/LianjiaTech/BELLE",
        stars: "8.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "oumi",
        description: "优质开源AI项目",
        url: "https://github.com/oumi-ai/oumi",
        stars: "7.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "lora",
        description: "Using 低秩适应微调 to quickly fine-tune diffusion models.",
        url: "https://github.com/cloneofsimo/lora",
        stars: "7.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "lab",
        description: "优质开源AI项目",
        url: "https://github.com/google-deepmind/lab",
        stars: "7.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "gpt-neox",
        description: "优质开源AI项目",
        url: "https://github.com/EleutherAI/gpt-neox",
        stars: "7.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "mmagic",
        description: "OpenMMLab 多模态 Advanced, Generative, and Intelligent Creation Toolbox. Unlock the magic 🪄: Generative-AI (AIGC), easy-to-use APIs, awsome model zoo, di...",
        url: "https://github.com/open-mmlab/mmagic",
        stars: "7.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "BERTopic",
        description: "Leve检索增强生成ing BERT and c-TF-IDF to create easily interpretable topics.",
        url: "https://github.com/Maartengr/BERTopic",
        stars: "6.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Firefly",
        description: "Firefly: 大模型训练工具，支持训练Qwen2.5、Qwen2、Yi1.5、Phi-3、Llama3、Gemma、MiniCPM、Yi、Deepseek、Orion、Xverse、Mixtral-8x7B、Zephyr、Mistral、Baichuan2、大型语言模型a2、Llama、Qwen...",
        url: "https://github.com/yangjianxin1/Firefly",
        stars: "6.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "MONAI",
        description: "优质开源AI项目",
        url: "https://github.com/Project-MONAI/MONAI",
        stars: "6.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "verl",
        description: "verl: Volcano Engine Reinforcement Learning for 大型语言模型s",
        url: "https://github.com/volcengine/verl",
        stars: "6.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "OpenRLHF",
        description: "优质开源AI项目",
        url: "https://github.com/OpenLLMAI/OpenRLHF",
        stars: "6.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "graphcast",
        description: "开源AI项目",
        url: "https://github.com/google-deepmind/graphcast",
        stars: "6.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "mergekit",
        description: "Tools for merging pretrained 大型语言模型s. (工具)",
        url: "https://github.com/cg123/mergekit",
        stars: "5.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "composer",
        description: "优质开源AI项目",
        url: "https://github.com/mosaicml/composer",
        stars: "5.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "torchtune",
        description: "优质开源AI项目",
        url: "https://github.com/pytorch/torchtune",
        stars: "5.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "DeepSeek-VL2",
        description: "优质开源AI项目",
        url: "https://github.com/deepseek-ai/DeepSeek-VL2",
        stars: "4.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "trlx",
        description: "优质开源AI项目",
        url: "https://github.com/CarperAI/trlx",
        stars: "4.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "open_spiel",
        description: "开源AI项目：planning in games.",
        url: "https://github.com/google-deepmind/open_spiel",
        stars: "4.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "xtuner",
        description: "An efficient, flexible and full-featured toolkit for 模型微调 大型语言模型 (InternLM2, Llama3, Phi3, Qwen, Mistral, ...) (工具)",
        url: "https://github.com/InternLM/xtuner",
        stars: "4.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "autotrain-advanced",
        description: "优质开源AI项目",
        url: "https://github.com/huggingface/autotrain-advanced",
        stars: "4.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "llm-foundry",
        description: "大型语言模型 training code for Databricks foundation models",
        url: "https://github.com/mosaicml/llm-foundry",
        stars: "4.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "warp-ctc",
        description: "优质开源AI项目",
        url: "https://github.com/baidu-research/warp-ctc",
        stars: "4.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "spark-nlp",
        description: "优质开源AI项目",
        url: "https://github.com/JohnSnowLabs/spark-nlp",
        stars: "4.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "open_flamingo",
        description: "An open-source framework for training large 多模态 models. (框架)",
        url: "https://github.com/mlfoundations/open_flamingo",
        stars: "3.9k",
        language: "Go",
        visible_by_default: false
    },
    {
        name: "FlagAI",
        description: "优质开源AI项目",
        url: "https://github.com/FlagAI-Open/FlagAI",
        stars: "3.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "MedicalGPT",
        description: "MedicalGPT: Training Your Own Medical GPT Model with ChatGPT Training Pipeline. 训练医疗大模型，实现了包括增量预训练(PT)、有监督微调(SFT)、RLHF、DPO、ORPO、GRPO。",
        url: "https://github.com/shibing624/MedicalGPT",
        stars: "3.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "acme",
        description: "优质开源AI项目",
        url: "https://github.com/google-deepmind/acme",
        stars: "3.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "mmpretrain",
        description: "优质开源AI项目",
        url: "https://github.com/open-mmlab/mmpretrain",
        stars: "3.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "scenic",
        description: "优质开源AI项目",
        url: "https://github.com/google-research/scenic",
        stars: "3.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "scikit-llm",
        description: "Seamlessly integrate 大型语言模型s into scikit-learn.",
        url: "https://github.com/iryna-kondr/scikit-llm",
        stars: "3.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "fairscale",
        description: "优质开源AI项目",
        url: "https://github.com/facebookresearch/fairscale",
        stars: "3.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "GLM",
        description: "优质开源AI项目",
        url: "https://github.com/THUDM/GLM",
        stars: "3.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "alpa",
        description: "优质开源AI项目",
        url: "https://github.com/alpa-projects/alpa",
        stars: "3.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "torchscale",
        description: "Foundation Architecture for (M)大型语言模型s",
        url: "https://github.com/microsoft/torchscale",
        stars: "3.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "dm-haiku",
        description: "优质开源AI项目",
        url: "https://github.com/google-deepmind/dm-haiku",
        stars: "3.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Eureka",
        description: "Official Repository for \"Eureka: Human-Level Reward Design via Coding 大型语言模型s\" (ICLR 2024)",
        url: "https://github.com/eureka-research/Eureka",
        stars: "2.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "open-instruct",
        description: "优质开源AI项目",
        url: "https://github.com/allenai/open-instruct",
        stars: "2.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "optimum",
        description: "🚀 Accelerate 推理优化 and training of 🤗 Transformer模型s, Diffusers, TIMM and Sentence Transformer模型s with easy to use hardware optimization tools (工具)",
        url: "https://github.com/huggingface/optimum",
        stars: "2.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "big_vision",
        description: "优质开源AI项目",
        url: "https://github.com/google-research/big_vision",
        stars: "2.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "t5x",
        description: "开源AI项目",
        url: "https://github.com/google-research/t5x",
        stars: "2.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LLaMA2-Accessory",
        description: "An Open-source Toolkit for 大型语言模型 Development (工具)",
        url: "https://github.com/Alpha-VLLM/LLaMA2-Accessory",
        stars: "2.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Alpaca-CoT",
        description: "We unified the interfaces of instruction-tuning data (e.g., CoT data), multiple 大型语言模型s and parameter-efficient methods (e.g., lora, p-tuning) togethe...",
        url: "https://github.com/PhoebusSi/Alpaca-CoT",
        stars: "2.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "adapters",
        description: "优质开源AI项目",
        url: "https://github.com/adapter-hub/adapters",
        stars: "2.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "xTuring",
        description: "Build, customize and control you own 大型语言模型s. From data pre-processing to 模型微调, xTuring provides an easy way to personalize open-source 大型语言模型s. Join ...",
        url: "https://github.com/stochasticai/xTuring",
        stars: "2.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "OpenPipe",
        description: "优质开源AI项目",
        url: "https://github.com/OpenPipe/OpenPipe",
        stars: "2.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "lamini",
        description: "优质开源AI项目",
        url: "https://github.com/lamini-ai/lamini",
        stars: "2.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "weak-to-strong",
        description: "开源AI项目",
        url: "https://github.com/openai/weak-to-strong",
        stars: "2.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "direct-preference-optimization",
        description: "优质开源AI项目",
        url: "https://github.com/eric-mitchell/direct-preference-optimization",
        stars: "2.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "setfit",
        description: "优质开源AI项目",
        url: "https://github.com/huggingface/setfit",
        stars: "2.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "numpyro",
        description: "开源AI项目：CPU.",
        url: "https://github.com/pyro-ppl/numpyro",
        stars: "2.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "RL4LMs",
        description: "优质开源AI项目",
        url: "https://github.com/allenai/RL4LMs",
        stars: "2.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "hyperlearn",
        description: "优质开源AI项目",
        url: "https://github.com/unslothai/hyperlearn",
        stars: "2.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "simple-llm-finetuner",
        description: "Simple UI for 大型语言模型 Model Finetuning",
        url: "https://github.com/lxe/simple-llm-finetuner",
        stars: "2.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "P-tuning-v2",
        description: "优质开源AI项目",
        url: "https://github.com/THUDM/P-tuning-v2",
        stars: "2.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "privacy",
        description: "Library for training 机器学习 models with privacy for training data (库)",
        url: "https://github.com/tensorflow/privacy",
        stars: "2.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "instructor-embedding",
        description: "优质开源AI项目",
        url: "https://github.com/xlang-ai/instructor-embedding",
        stars: "1.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ctrl",
        description: "优质开源AI项目",
        url: "https://github.com/salesforce/ctrl",
        stars: "1.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "optax",
        description: "优质开源AI项目",
        url: "https://github.com/google-deepmind/optax",
        stars: "1.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "training-operator",
        description: "优质开源AI项目",
        url: "https://github.com/kubeflow/training-operator",
        stars: "1.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "penzai",
        description: "优质开源AI项目",
        url: "https://github.com/google-deepmind/penzai",
        stars: "1.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "i-Code",
        description: "开源AI项目",
        url: "https://github.com/microsoft/i-Code",
        stars: "1.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ALBEF",
        description: "优质开源AI项目",
        url: "https://github.com/salesforce/ALBEF",
        stars: "1.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Llama-X",
        description: "Open Academic Research on Improving LLaMA to SOTA 大型语言模型",
        url: "https://github.com/AetherCortex/Llama-X",
        stars: "1.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "multimodal",
        description: "Torch多模态 is a PyTorch library for training 最先进的 多模态 multi-task models at scale. (库)",
        url: "https://github.com/facebookresearch/multimodal",
        stars: "1.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "katib",
        description: "优质开源AI项目",
        url: "https://github.com/kubeflow/katib",
        stars: "1.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "finetuner",
        description: "优质开源AI项目",
        url: "https://github.com/jina-ai/finetuner",
        stars: "1.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "CodeTF",
        description: "CodeTF: One-stop Transformer模型 Library for State-of-the-art Code 大型语言模型 (库)",
        url: "https://github.com/salesforce/CodeTF",
        stars: "1.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "yarn",
        description: "YaRN: Efficient Context Window Extension of 大型语言模型s",
        url: "https://github.com/jquesnelle/yarn",
        stars: "1.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "safe-rlhf",
        description: "优质开源AI项目",
        url: "https://github.com/PKU-Alignment/safe-rlhf",
        stars: "1.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "nerfacc",
        description: "优质开源AI项目",
        url: "https://github.com/nerfstudio-project/nerfacc",
        stars: "1.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "self-rewarding-lm-pytorch",
        description: "优质开源AI项目",
        url: "https://github.com/lucidrains/self-rewarding-lm-pytorch",
        stars: "1.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "DAMO-ConvAI",
        description: "优质开源AI项目",
        url: "https://github.com/AlibabaResearch/DAMO-ConvAI",
        stars: "1.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "MOSS-RLHF",
        description: "Secrets of RLHF in 大型语言模型s Part I: PPO",
        url: "https://github.com/OpenLMLab/MOSS-RLHF",
        stars: "1.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "cramming",
        description: "优质开源AI项目",
        url: "https://github.com/JonasGeiping/cramming",
        stars: "1.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "megablocks",
        description: "开源AI项目",
        url: "https://github.com/databricks/megablocks",
        stars: "1.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LLM-Adapters",
        description: "Code for our EMNLP 2023 Paper: \"大型语言模型-Adapters: An Adapter Family for Parameter-Efficient 模型微调 of 大型语言模型s\"",
        url: "https://github.com/AGI-Edgerunners/LLM-Adapters",
        stars: "1.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Fabrik",
        description: "优质开源AI项目",
        url: "https://github.com/Cloud-CV/Fabrik",
        stars: "1.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LlamaGym",
        description: "Fine-tune 大型语言模型 agents with online reinforcement learning",
        url: "https://github.com/KhoomeiK/LlamaGym",
        stars: "1.1k",
        language: "Python",
        visible_by_default: false
    },
];

// 为子类别 2.2 添加 58 个项目
projectsData["2.2"] = [
    {
        name: "AutoGPT",
        description: "优质开源AI项目",
        url: "https://github.com/Significant-Gravitas/AutoGPT",
        stars: "174.2k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "MetaGPT",
        description: "优质开源AI项目",
        url: "https://github.com/geekan/MetaGPT",
        stars: "54.2k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "browser-use",
        description: "优质开源AI项目",
        url: "https://github.com/browser-use/browser-use",
        stars: "53.3k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "autogen",
        description: "开源AI项目：autogen-officehour (框架)",
        url: "https://github.com/microsoft/autogen",
        stars: "42.7k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "AgentGPT",
        description: "优质开源AI项目",
        url: "https://github.com/reworkd/AgentGPT",
        stars: "33.7k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "crewAI",
        description: "Framework for orchestrating role-playing, autonomous AI智能代理s. By fostering collaborative intelligence, CrewAI empowers agents to work together seamles...",
        url: "https://github.com/joaomdmoura/crewAI",
        stars: "29.6k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "mem0",
        description: "优质开源AI项目",
        url: "https://github.com/mem0ai/mem0",
        stars: "27.2k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "composio",
        description: "Composio equip's your AI智能代理s & 大型语言模型s with 100+ high-quality integrations via function calling",
        url: "https://github.com/composiohq/composio",
        stars: "24.9k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "JARVIS",
        description: "JARVIS, a system to connect 大型语言模型s with ML community. Paper: https://arxiv.org/pdf/2303.17580.pdf",
        url: "https://github.com/microsoft/JARVIS",
        stars: "24.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "phidata",
        description: "A lightweight library for building 多模态 Agents. Give 大型语言模型s superpowers like memory, knowledge, tools and reasoning. (库)",
        url: "https://github.com/phidatahq/phidata",
        stars: "24.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "semantic-kernel",
        description: "Integrate cutting-edge 大型语言模型 technology quickly and easily into your apps",
        url: "https://github.com/microsoft/semantic-kernel",
        stars: "23.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "babyagi",
        description: "开源AI项目",
        url: "https://github.com/yoheinakajima/babyagi",
        stars: "21.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "swarm",
        description: "优质开源AI项目",
        url: "https://github.com/openai/swarm",
        stars: "19.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "smolagents",
        description: "优质开源AI项目",
        url: "https://github.com/huggingface/smolagents",
        stars: "16.5k",
        language: "TypeScript",
        visible_by_default: false
    },
    {
        name: "SuperAGI",
        description: "<⚡️> Supe检索增强生成I - A dev-first 开源 autonomous AI智能代理 framework. Enabling developers to build, manage & run useful autonomous agents quickly and reliabl...",
        url: "https://github.com/TransformerOptimus/SuperAGI",
        stars: "16.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "MemGPT",
        description: "优质开源AI项目",
        url: "https://github.com/cpacker/MemGPT",
        stars: "15.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "botpress",
        description: "The open-source hub to build & deploy GPT/大型语言模型 Agents ⚡️",
        url: "https://github.com/botpress/botpress",
        stars: "13.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "developer",
        description: "优质开源AI项目",
        url: "https://github.com/smol-ai/developer",
        stars: "11.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "langgraph",
        description: "优质开源AI项目",
        url: "https://github.com/langchain-ai/langgraph",
        stars: "11.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "XAgent",
        description: "An Autonomous 大型语言模型 Agent for Complex Task Solving",
        url: "https://github.com/OpenBMB/XAgent",
        stars: "8.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "e2b",
        description: "Secure 开源 cloud runtime for AI apps & AI智能代理s",
        url: "https://github.com/e2b-dev/e2b",
        stars: "7.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "agentscope",
        description: "Start building 大型语言模型-empowered multi-agent applications in an easier way.",
        url: "https://github.com/modelscope/agentscope",
        stars: "6.9k",
        language: "TypeScript",
        visible_by_default: false
    },
    {
        name: "agent-zero",
        description: "优质开源AI项目",
        url: "https://github.com/frdel/agent-zero",
        stars: "6.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Qwen-Agent",
        description: "Agent framework and applications built upon Qwen>=2.0, featuring Function Calling, Code Interpreter, 检索增强生成, and Chrome extension. (框架)",
        url: "https://github.com/QwenLM/Qwen-Agent",
        stars: "6.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "superagent",
        description: "优质开源AI项目",
        url: "https://github.com/homanp/superagent",
        stars: "5.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "agents",
        description: "优质开源AI项目",
        url: "https://github.com/aiwaves-cn/agents",
        stars: "5.6k",
        language: "TypeScript",
        visible_by_default: false
    },
    {
        name: "vision-agent",
        description: "优质开源AI项目",
        url: "https://github.com/landing-ai/vision-agent",
        stars: "4.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "AgentVerse",
        description: "🤖 AgentVerse 🪐 is designed to facilitate the deployment of multiple 大型语言模型-based agents in various applications, which primarily provides two framewor...",
        url: "https://github.com/OpenBMB/AgentVerse",
        stars: "4.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "agentops",
        description: "Python SDK for AI智能代理 监控系统, 大型语言模型 cost tracking, benchmarking, and more. Integrates with most 大型语言模型s and agent frameworks including OpenAI智能代理s SDK,...",
        url: "https://github.com/AgentOps-AI/agentops",
        stars: "4.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Auto-GPT-Plugins",
        description: "优质开源AI项目",
        url: "https://github.com/Significant-Gravitas/Auto-GPT-Plugins",
        stars: "3.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "rivet",
        description: "优质开源AI项目",
        url: "https://github.com/Ironclad/rivet",
        stars: "3.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "langroid",
        description: "Harness 大型语言模型s with Multi-Agent Programming",
        url: "https://github.com/langroid/langroid",
        stars: "3.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "AGiXT",
        description: "优质开源AI项目",
        url: "https://github.com/Josh-XT/AGiXT",
        stars: "3.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "databerry",
        description: "The no-code platform for building custom 大型语言模型 Agents",
        url: "https://github.com/gmpetrov/databerry",
        stars: "2.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "mini-agi",
        description: "优质开源AI项目",
        url: "https://github.com/muellerberndt/mini-agi",
        stars: "2.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "PettingZoo",
        description: "优质开源AI项目",
        url: "https://github.com/Farama-Foundation/PettingZoo",
        stars: "2.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "BMTools",
        description: "优质开源AI项目",
        url: "https://github.com/OpenBMB/BMTools",
        stars: "2.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "moa",
        description: "优质开源AI项目",
        url: "https://github.com/togethercomputer/moa",
        stars: "2.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "reflexion",
        description: "优质开源AI项目",
        url: "https://github.com/noahshinn/reflexion",
        stars: "2.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "BotSharp",
        description: "优质开源AI项目",
        url: "https://github.com/SciSharp/BotSharp",
        stars: "2.6k",
        language: "TypeScript",
        visible_by_default: false
    },
    {
        name: "agent-service-toolkit",
        description: "Full toolkit for running an AI智能代理 service built with LangGraph, FastAPI and Streamlit (工具)",
        url: "https://github.com/JoshuaC215/agent-service-toolkit",
        stars: "2.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "datachain",
        description: "优质开源AI项目",
        url: "https://github.com/iterative/datachain",
        stars: "2.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "nextpy",
        description: "优质开源AI项目",
        url: "https://github.com/dot-agent/nextpy",
        stars: "2.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ag2",
        description: "开源AI项目：pAbnFJrkgZ",
        url: "https://github.com/ag2ai/ag2",
        stars: "2.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "OpenAGI",
        description: "OpenAGI: When 大型语言模型 Meets Domain Experts",
        url: "https://github.com/agiresearch/OpenAGI",
        stars: "2.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "lagent",
        description: "A lightweight framework for building 大型语言模型-based agents (框架)",
        url: "https://github.com/InternLM/lagent",
        stars: "2.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "MineDojo",
        description: "优质开源AI项目",
        url: "https://github.com/MineDojo/MineDojo",
        stars: "1.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "AutoChain",
        description: "AutoChain: Build lightweight, extensible, and testable 大型语言模型 Agents",
        url: "https://github.com/Forethought-Technologies/AutoChain",
        stars: "1.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "agentkit",
        description: "优质开源AI项目",
        url: "https://github.com/BCG-X-Official/agentkit",
        stars: "1.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "thinkgpt",
        description: "Agent techniques to augment your 大型语言模型 and push it beyong its limits",
        url: "https://github.com/jina-ai/thinkgpt",
        stars: "1.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "loopgpt",
        description: "优质开源AI项目",
        url: "https://github.com/farizrahman4u/loopgpt",
        stars: "1.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "chatarena",
        description: "ChatArena (or Chat Arena) is a Multi-Agent Language Game Environments for 大型语言模型s. The goal is to develop communication and collaboration capabilities...",
        url: "https://github.com/Farama-Foundation/chatarena",
        stars: "1.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "AgentTuning",
        description: "AgentTuning: Enabling Generalized Agent Abilities for 大型语言模型s",
        url: "https://github.com/THUDM/AgentTuning",
        stars: "1.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ai-legion",
        description: "An 大型语言模型-powered autonomous agent platform",
        url: "https://github.com/eumemic/ai-legion",
        stars: "1.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "RestGPT",
        description: "An 大型语言模型-based autonomous agent controlling real-world applications via RESTful APIs",
        url: "https://github.com/Yifan-Song793/RestGPT",
        stars: "1.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "AutoAgents",
        description: "优质开源AI项目",
        url: "https://github.com/Link-AGI/AutoAgents",
        stars: "1.3k",
        language: "TypeScript",
        visible_by_default: false
    },
    {
        name: "agent-protocol",
        description: "Common interface for interacting with AI智能代理s. The protocol is tech stack agnostic - you can use it with any framework for building agents. (框架)",
        url: "https://github.com/AI-Engineer-Foundation/agent-protocol",
        stars: "1.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "chameleon-llm",
        description: "Codes for \"Chameleon: Plug-and-Play Compositional Reasoning with 大型语言模型s\".",
        url: "https://github.com/lupantech/chameleon-llm",
        stars: "1.1k",
        language: "Python",
        visible_by_default: false
    },
];

// 为子类别 3.5 添加 23 个项目
projectsData["3.5"] = [
    {
        name: "stable-diffusion-webui",
        description: "优质开源AI项目",
        url: "https://github.com/AUTOMATIC1111/stable-diffusion-webui",
        stars: "150.7k",
        language: "JavaScript",
        visible_by_default: true
    },
    {
        name: "Fooocus",
        description: "优质开源AI项目",
        url: "https://github.com/lllyasviel/Fooocus",
        stars: "44.2k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "upscayl",
        description: "优质开源AI项目",
        url: "https://github.com/upscayl/upscayl",
        stars: "36.2k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "roop",
        description: "优质开源AI项目",
        url: "https://github.com/s0md3v/roop",
        stars: "29.6k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "InvokeAI",
        description: "优质开源AI项目",
        url: "https://github.com/invoke-ai/InvokeAI",
        stars: "24.8k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "facefusion",
        description: "优质开源AI项目",
        url: "https://github.com/facefusion/facefusion",
        stars: "22.3k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "lama-cleaner",
        description: "优质开源AI项目",
        url: "https://github.com/Sanster/lama-cleaner",
        stars: "20.9k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "sd-webui-controlnet",
        description: "优质开源AI项目",
        url: "https://github.com/Mikubill/sd-webui-controlnet",
        stars: "17.5k",
        language: "JavaScript",
        visible_by_default: true
    },
    {
        name: "stable-diffusion-webui-colab",
        description: "优质开源AI项目",
        url: "https://github.com/camenduru/stable-diffusion-webui-colab",
        stars: "15.8k",
        language: "JavaScript",
        visible_by_default: false
    },
    {
        name: "diffusionbee-stable-diffusion-ui",
        description: "优质开源AI项目",
        url: "https://github.com/divamgupta/diffusionbee-stable-diffusion-ui",
        stars: "13.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "VideoLingo",
        description: "Netflix-level subtitle cutting, translation, alignment, and even dubbing - one-click fully automated AI video subtitle team | Netflix级字幕切割、翻译、对齐、甚至加上配...",
        url: "https://github.com/Huanshere/VideoLingo",
        stars: "12.2k",
        language: "Go",
        visible_by_default: false
    },
    {
        name: "paper2gui",
        description: "Convert AI papers to GUI，Make it easy and convenient for everyone to use artificial intelligence technology。让每个人都简单方便的使用前沿人工智能技术",
        url: "https://github.com/Baiyuetribe/paper2gui",
        stars: "10.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "easydiffusion",
        description: "优质开源AI项目",
        url: "https://github.com/easydiffusion/easydiffusion",
        stars: "9.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "StableStudio",
        description: "优质开源AI项目",
        url: "https://github.com/Stability-AI/StableStudio",
        stars: "9.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "dream-textures",
        description: "优质开源AI项目",
        url: "https://github.com/carson-katri/dream-textures",
        stars: "8.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "fast-stable-diffusion",
        description: "优质开源AI项目",
        url: "https://github.com/TheLastBen/fast-stable-diffusion",
        stars: "7.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "MochiDiffusion",
        description: "优质开源AI项目",
        url: "https://github.com/godly-devotion/MochiDiffusion",
        stars: "7.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "OutfitAnyone",
        description: "优质开源AI项目",
        url: "https://github.com/HumanAIGC/OutfitAnyone",
        stars: "5.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "dot",
        description: "优质开源AI项目",
        url: "https://github.com/sensity-ai/dot",
        stars: "4.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "headshots-starter",
        description: "开源AI项目",
        url: "https://github.com/leap-ai/headshots-starter",
        stars: "4.2k",
        language: "TypeScript",
        visible_by_default: false
    },
    {
        name: "restorePhotos",
        description: "优质开源AI项目",
        url: "https://github.com/Nutlope/restorePhotos",
        stars: "4.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "web-stable-diffusion",
        description: "优质开源AI项目",
        url: "https://github.com/mlc-ai/web-stable-diffusion",
        stars: "3.6k",
        language: "JavaScript",
        visible_by_default: false
    },
    {
        name: "aicover",
        description: "优质开源AI项目",
        url: "https://github.com/all-in-aigc/aicover",
        stars: "1.7k",
        language: "Python",
        visible_by_default: false
    },
];

// 为子类别 2.3 添加 40 个项目
projectsData["2.3"] = [
    {
        name: "ollama",
        description: "Get up and running with Llama 3.3, DeepSeek-R1, Phi-4, Gemma 3, and other 大型语言模型s.",
        url: "https://github.com/jmorganca/ollama",
        stars: "136.1k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "ChatGPT-Next-Web",
        description: "优质开源AI项目",
        url: "https://github.com/ChatGPTNextWeb/ChatGPT-Next-Web",
        stars: "82.5k",
        language: "JavaScript",
        visible_by_default: true
    },
    {
        name: "gpt4free",
        description: "优质开源AI项目",
        url: "https://github.com/xtekky/gpt4free",
        stars: "64.0k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "GPT-SoVITS",
        description: "优质开源AI项目",
        url: "https://github.com/RVC-Boss/GPT-SoVITS",
        stars: "43.7k",
        language: "TypeScript",
        visible_by_default: true
    },
    {
        name: "text-generation-webui",
        description: "A Gradio web UI for 大型语言模型s with support for multiple 推理优化 backends.",
        url: "https://github.com/oobabooga/text-generation-webui",
        stars: "43.1k",
        language: "JavaScript",
        visible_by_default: true
    },
    {
        name: "gradio",
        description: "优质开源AI项目",
        url: "https://github.com/gradio-app/gradio",
        stars: "37.3k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "chatbot-ui",
        description: "优质开源AI项目",
        url: "https://github.com/mckaywrigley/chatbot-ui",
        stars: "30.8k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "openai-python",
        description: "优质开源AI项目",
        url: "https://github.com/openai/openai-python",
        stars: "26.2k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "LibreChat",
        description: "优质开源AI项目",
        url: "https://github.com/danny-avila/LibreChat",
        stars: "24.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "CopilotKit",
        description: "优质开源AI项目",
        url: "https://github.com/CopilotKit/CopilotKit",
        stars: "18.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ChatALL",
        description: "Concurrently chat with ChatGPT, Bing Chat, Bard, Alpaca, Vicuna, Claude, ChatGLM, MOSS, 讯飞星火, 文心一言 and more, discover the best answers",
        url: "https://github.com/sunner/ChatALL",
        stars: "15.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ChuanhuChatGPT",
        description: "GUI for ChatGPT API and many 大型语言模型s. Supports agents, file-based QA, GPT finetuning and query with web search. All with a neat UI.",
        url: "https://github.com/GaiZhenbiao/ChuanhuChatGPT",
        stars: "15.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "web-llm",
        description: "High-performance In-browser 大型语言模型 推理优化 Engine",
        url: "https://github.com/mlc-ai/web-llm",
        stars: "15.1k",
        language: "JavaScript",
        visible_by_default: false
    },
    {
        name: "SillyTavern",
        description: "大型语言模型 Frontend for Power Users.",
        url: "https://github.com/SillyTavern/SillyTavern",
        stars: "13.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "clip-as-service",
        description: "优质开源AI项目",
        url: "https://github.com/jina-ai/clip-as-service",
        stars: "12.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Fay",
        description: "fay是一个帮助数字人（2.5d、3d、移动、pc、网页）或大语言模型（openai兼容、deepseek）连通业务系统的agent框架。",
        url: "https://github.com/TheRamU/Fay",
        stars: "10.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "chathub",
        description: "优质开源AI项目",
        url: "https://github.com/chathub-dev/chathub",
        stars: "10.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "go-openai",
        description: "优质开源AI项目",
        url: "https://github.com/sashabaranov/go-openai",
        stars: "9.8k",
        language: "Go",
        visible_by_default: false
    },
    {
        name: "openai-node",
        description: "开源AI项目： TypeScript library for the OpenAI API (库)",
        url: "https://github.com/openai/openai-node",
        stars: "8.9k",
        language: "JavaScript",
        visible_by_default: false
    },
    {
        name: "AI4Animation",
        description: "优质开源AI项目",
        url: "https://github.com/sebastianstarke/AI4Animation",
        stars: "8.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "wukong-robot",
        description: "🤖 wukong-robot 是一个简单、灵活、优雅的中文语音对话机器人/智能音箱项目，支持ChatGPT多轮对话能力，还可能是首个支持脑机交互的开源智能音箱项目。",
        url: "https://github.com/wzpan/wukong-robot",
        stars: "6.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "xiaogpt",
        description: "Play ChatGPT and other 大型语言模型 with Xiaomi AI Speaker",
        url: "https://github.com/yihong0618/xiaogpt",
        stars: "6.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "openplayground",
        description: "An 大型语言模型 playground you can run on your laptop",
        url: "https://github.com/nat/openplayground",
        stars: "6.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "postgresml",
        description: "开源AI项目：AI apps.",
        url: "https://github.com/postgresml/postgresml",
        stars: "6.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "RealChar",
        description: "🎙️🤖Create, Customize and Talk to your AI Character/Companion in Realtime (All in One Codebase!). Have a natural seamless conversation with AI everywhe...",
        url: "https://github.com/Shaunwei/RealChar",
        stars: "6.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "lollms-webui",
        description: "优质开源AI项目",
        url: "https://github.com/ParisNeo/lollms-webui",
        stars: "4.6k",
        language: "JavaScript",
        visible_by_default: false
    },
    {
        name: "plugins-quickstart",
        description: "优质开源AI项目",
        url: "https://github.com/openai/plugins-quickstart",
        stars: "4.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "bot-on-anything",
        description: "优质开源AI项目",
        url: "https://github.com/zhayujie/bot-on-anything",
        stars: "4.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "coze-discord-proxy",
        description: "代理Discord对话Coze-Bot，实现以API形式请求GPT4模型，提供对话、文生图、图生文、知识库检索等功能。",
        url: "https://github.com/deanxv/coze-discord-proxy",
        stars: "3.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ollama-js",
        description: "优质开源AI项目",
        url: "https://github.com/ollama/ollama-js",
        stars: "3.2k",
        language: "JavaScript",
        visible_by_default: false
    },
    {
        name: "vocode-python",
        description: "🤖 Build voice-based 大型语言模型 agents. Modular + 开源.",
        url: "https://github.com/vocodedev/vocode-python",
        stars: "3.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "InternGPT",
        description: "InternGPT (iGPT) is an 开源 demo platform where you can easily showcase your AI models. Now it supports D检索增强生成GAN, ChatGPT, ImageBind, 多模态 chat like GP...",
        url: "https://github.com/OpenGVLab/InternGPT",
        stars: "3.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ruby-openai",
        description: "优质开源AI项目",
        url: "https://github.com/alexrudall/ruby-openai",
        stars: "3.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "langui",
        description: "UI for your AI. Open Source Tailwind components tailored for your GPT, generative AI, and 大型语言模型 projects.",
        url: "https://github.com/ahmadbilaldev/langui",
        stars: "2.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "api-for-open-llm",
        description: "Openai style api for open 大型语言模型s, using 大型语言模型s just as chatgpt! Support for LLaMA, LLaMA-2, BLOOM, Falcon, Baichuan, Qwen, Xverse, SqlCoder, CodeLLa...",
        url: "https://github.com/xusenlinzy/api-for-open-llm",
        stars: "2.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "chat-with-gpt",
        description: "优质开源AI项目",
        url: "https://github.com/cogentapps/chat-with-gpt",
        stars: "2.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "anse",
        description: "优质开源AI项目",
        url: "https://github.com/anse-app/anse",
        stars: "1.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LLMFarm",
        description: "llama and other  大型语言模型s on iOS and MacOS offline using GGML library. (库)",
        url: "https://github.com/guinmoon/LLMFarm",
        stars: "1.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "aidea-server",
        description: "AIdea 是一款支持 GPT  以及国产大语言模型通义千问、文心一言等，支持 Stable Diffusion 文生图、图生图、 SDXL1.0、超分辨率、图片上色的全能型 APP。",
        url: "https://github.com/mylxsw/aidea-server",
        stars: "1.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "openai-kotlin",
        description: "优质开源AI项目",
        url: "https://github.com/aallam/openai-kotlin",
        stars: "1.7k",
        language: "Python",
        visible_by_default: false
    },
];

// 为子类别 5.2 添加 20 个项目
projectsData["5.2"] = [
    {
        name: "awesome-chatgpt-prompts",
        description: "This repo includes ChatGPT prompt curation to use ChatGPT and other 大型语言模型 tools better. (工具)",
        url: "https://github.com/f/awesome-chatgpt-prompts",
        stars: "122.6k",
        language: "TypeScript",
        visible_by_default: true
    },
    {
        name: "funNLP",
        description: "中英文敏感词、语言检测、中外手机/电话归属地/运营商查询、名字推断性别、手机号抽取、身份证抽取、邮箱抽取、中日文人名库、中文缩写库、拆字词典、词汇情感值、停用词、反动词表、暴恐词表、繁简体转换、英文模拟中文发音、汪峰歌词生成器、职业名称词库、同义词库、反义词库、否定词库、汽车品牌词库、汽车零件词库、...",
        url: "https://github.com/fighting41love/funNLP",
        stars: "72.2k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "awesome-chatgpt-prompts-zh",
        description: "ChatGPT 中文调教指南。各种场景使用指南。学习怎么让它听你的话。",
        url: "https://github.com/PlexPt/awesome-chatgpt-prompts-zh",
        stars: "54.7k",
        language: "TypeScript",
        visible_by_default: true
    },
    {
        name: "GPTs",
        description: "优质开源AI项目",
        url: "https://github.com/linexjlin/GPTs",
        stars: "29.6k",
        language: "TypeScript",
        visible_by_default: true
    },
    {
        name: "awesome-llm-apps",
        description: "Collection of awesome 大型语言模型 apps with AI智能代理s and 检索增强生成 using OpenAI, Anthropic, Gemini and opensource models.",
        url: "https://github.com/Shubhamsaboo/awesome-llm-apps",
        stars: "27.2k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "awesome-ai-agents",
        description: "优质开源AI项目",
        url: "https://github.com/e2b-dev/awesome-ai-agents",
        stars: "16.8k",
        language: "TypeScript",
        visible_by_default: true
    },
    {
        name: "open-llms",
        description: "📋 A list of open 大型语言模型s available for commercial use.",
        url: "https://github.com/eugeneyan/open-llms",
        stars: "11.9k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "LLMSurvey",
        description: "The official GitHub page for the survey paper \"A Survey of 大型语言模型s\".",
        url: "https://github.com/RUCAIBox/LLMSurvey",
        stars: "11.3k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "chatgpt_system_prompt",
        description: "开源AI项目：leaking knowledge.",
        url: "https://github.com/LouisShark/chatgpt_system_prompt",
        stars: "8.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "awesome-generative-ai",
        description: "优质开源AI项目",
        url: "https://github.com/steven2358/awesome-generative-ai",
        stars: "8.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LLM-Agent-Paper-List",
        description: "The paper list of the 86-page paper \"The Rise and Potential of 大型语言模型 Based Agents: A Survey\" by Zhiheng Xi et al.",
        url: "https://github.com/WooooDyy/LLM-Agent-Paper-List",
        stars: "7.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LLMs-In-China",
        description: "中国大模型",
        url: "https://github.com/wgwang/LLMs-In-China",
        stars: "6.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "awesome-pretrained-chinese-nlp-models",
        description: "Awesome Pretrained Chinese NLP Models，高质量中文预训练模型&大模型&多模态模型&大语言模型集合",
        url: "https://github.com/lonePatient/awesome-pretrained-chinese-nlp-models",
        stars: "5.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Awesome-LLMOps",
        description: "An awesome & curated list of best 大型语言模型Ops tools for developers (工具)",
        url: "https://github.com/tensorchord/Awesome-LLMOps",
        stars: "4.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "awesome-RLHF",
        description: "优质开源AI项目",
        url: "https://github.com/opendilab/awesome-RLHF",
        stars: "3.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "DecryptPrompt",
        description: "总结Prompt&大型语言模型论文，开源数据&模型，AIGC应用",
        url: "https://github.com/DSXiangLi/DecryptPrompt",
        stars: "3.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Medical_NLP",
        description: "优质开源AI项目",
        url: "https://github.com/FreedomIntelligence/Medical_NLP",
        stars: "2.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "prompt-in-context-learning",
        description: "Awesome resources for in-context learning and 提示词工程: Mastery of the 大型语言模型s such as ChatGPT, GPT-3, and FlanT5, with up-to-date and cutting-edge updat...",
        url: "https://github.com/EgoAlpha/prompt-in-context-learning",
        stars: "1.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "awesome-gpts",
        description: "优质开源AI项目",
        url: "https://github.com/taranjeet/awesome-gpts",
        stars: "1.3k",
        language: "TypeScript",
        visible_by_default: false
    },
    {
        name: "openprompt.co",
        description: "优质开源AI项目",
        url: "https://github.com/timqian/openprompt.co",
        stars: "1.2k",
        language: "Python",
        visible_by_default: false
    },
];

// 为子类别 2.1 添加 81 个项目
projectsData["2.1"] = [
    {
        name: "langchain",
        description: "优质开源AI项目",
        url: "https://github.com/langchain-ai/langchain",
        stars: "105.0k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "dify",
        description: "Dify is an open-source 大型语言模型 app development platform. Dify's intuitive interface combines AI workflow, 检索增强生成 pipeline, agent capabilities, model ma...",
        url: "https://github.com/langgenius/dify",
        stars: "89.4k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "ComfyUI",
        description: "开源AI项目：nodes interface.",
        url: "https://github.com/comfyanonymous/ComfyUI",
        stars: "73.5k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "gpt4all",
        description: "GPT4All: Run Local 大型语言模型s on Any Device. Open-source and available for commercial use.",
        url: "https://github.com/nomic-ai/gpt4all",
        stars: "73.0k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "lobe-chat",
        description: "🤯 Lobe Chat - an open-source, modern-design AI chat framework. Supports Multi AI Providers( OpenAI / Claude 3 / Gemini / Ollama / DeepSeek / Qwen), Kn...",
        url: "https://github.com/lobehub/lobe-chat",
        stars: "58.6k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "langflow",
        description: "优质开源AI项目",
        url: "https://github.com/logspace-ai/langflow",
        stars: "54.2k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "ragflow",
        description: "检索增强生成Flow is an open-source 检索增强生成 (Retrieval-Augmented Generation) engine based on deep document understanding.",
        url: "https://github.com/infiniflow/ragflow",
        stars: "48.0k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "llama_index",
        description: "LlamaIndex is the leading framework for building 大型语言模型-powered agents over your data. (框架)",
        url: "https://github.com/run-llama/llama_index",
        stars: "40.7k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "Flowise",
        description: "D检索增强生成 & drop UI to build your customized 大型语言模型 flow",
        url: "https://github.com/FlowiseAI/Flowise",
        stars: "36.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Langchain-Chatchat",
        description: "Langchain-Chatchat（原Langchain-ChatGLM）基于 Langchain 与 ChatGLM, Qwen 与 Llama 等语言模型的 检索增强生成 与 Agent 应用 | Langchain-Chatchat (formerly langchain-ChatGLM),...",
        url: "https://github.com/chatchat-space/Langchain-Chatchat",
        stars: "34.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LocalAI",
        description: ":robot: The free, Open Source alternative to OpenAI, Claude and others. Self-hosted and local-first. Drop-in replacement for OpenAI,  running on consu...",
        url: "https://github.com/go-skynet/LocalAI",
        stars: "31.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "servers",
        description: "优质开源AI项目",
        url: "https://github.com/modelcontextprotocol/servers",
        stars: "31.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "mindsdb",
        description: "优质开源AI项目",
        url: "https://github.com/mindsdb/mindsdb",
        stars: "27.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "embedchain",
        description: "优质开源AI项目",
        url: "https://github.com/embedchain/embedchain",
        stars: "27.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "one-api",
        description: "大型语言模型 API 管理 & 分发系统，支持 OpenAI、Azure、Anthropic Claude、Google Gemini、DeepSeek、字节豆包、ChatGLM、文心一言、讯飞星火、通义千问、360 智脑、腾讯混元等主流模型，统一 API 适配，可用于 key 管理与二次分发。单可...",
        url: "https://github.com/songquanpeng/one-api",
        stars: "24.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "FastGPT",
        description: "FastGPT is a knowledge-based platform built on the 大型语言模型s, offers a comprehensive suite of out-of-the-box capabilities such as data processing, 检索增强生...",
        url: "https://github.com/labring/FastGPT",
        stars: "23.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "kotaemon",
        description: "An open-source 检索增强生成-based tool for chatting with your documents. (工具)",
        url: "https://github.com/Cinnamon/kotaemon",
        stars: "21.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "litellm",
        description: "Python SDK, Proxy Server (大型语言模型 Gateway) to call 100+ 大型语言模型 APIs in OpenAI format - [Bedrock, Azure, OpenAI, VertexAI, Cohere, Anthropic, Sagemaker,...",
        url: "https://github.com/BerriAI/litellm",
        stars: "20.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "haystack",
        description: "AI orchestration framework to build customizable, production-ready 大型语言模型 applications. Connect components (models, vector DBs, file converters) to pi...",
        url: "https://github.com/deepset-ai/haystack",
        stars: "20.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "flair",
        description: "A very simple framework for 最先进的 Natural Language Processing (NLP) (框架)",
        url: "https://github.com/flairNLP/flair",
        stars: "14.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "langchainjs",
        description: "优质开源AI项目",
        url: "https://github.com/langchain-ai/langchainjs",
        stars: "14.1k",
        language: "JavaScript",
        visible_by_default: false
    },
    {
        name: "transformers.js",
        description: "State-of-the-art 机器学习 for the web. Run 🤗 Transformer模型s directly in your browser, with no need for a server!",
        url: "https://github.com/xenova/transformers.js",
        stars: "13.4k",
        language: "JavaScript",
        visible_by_default: false
    },
    {
        name: "ai",
        description: "优质开源AI项目",
        url: "https://github.com/vercel/ai",
        stars: "13.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "QAnything",
        description: "优质开源AI项目",
        url: "https://github.com/netease-youdao/QAnything",
        stars: "13.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "llmware",
        description: "Unified framework for building enterprise 检索增强生成 pipelines with small, specialized models (框架)",
        url: "https://github.com/llmware-ai/llmware",
        stars: "12.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "aisuite",
        description: "优质开源AI项目",
        url: "https://github.com/andrewyng/aisuite",
        stars: "11.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "h2ogpt",
        description: "开源AI项目：",
        url: "https://github.com/h2oai/h2ogpt",
        stars: "11.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ludwig",
        description: "Low-code framework for building custom 大型语言模型s, neural networks, and other AI models (框架)",
        url: "https://github.com/ludwig-ai/ludwig",
        stars: "11.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "unstructured",
        description: "优质开源AI项目",
        url: "https://github.com/Unstructured-IO/unstructured",
        stars: "10.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "promptflow",
        description: "Build high-quality 大型语言模型 apps - from prototyping, testing to production deployment and 监控系统.",
        url: "https://github.com/microsoft/promptflow",
        stars: "10.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "chainlit",
        description: "优质开源AI项目",
        url: "https://github.com/Chainlit/chainlit",
        stars: "9.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "chatnio",
        description: "🚀 Next Generation AI One-Stop Internationalization Solution. 🚀 下一代 AI 一站式 B/C 端解决方案，支持 OpenAI，Midjourney，Claude，讯飞星火，Stable Diffusion，DALL·E，ChatGLM，通...",
        url: "https://github.com/Deeptrain-Community/chatnio",
        stars: "8.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "search_with_lepton",
        description: "优质开源AI项目",
        url: "https://github.com/leptonai/search_with_lepton",
        stars: "8.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "bisheng",
        description: "BISHENG is an open 大型语言模型 devops platform for next generation Enterprise AI applications. Powerful and comprehensive features include: GenAI workflow,...",
        url: "https://github.com/dataelement/bisheng",
        stars: "8.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "modelscope",
        description: "优质开源AI项目",
        url: "https://github.com/modelscope/modelscope",
        stars: "7.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "langchain4j",
        description: "优质开源AI项目",
        url: "https://github.com/langchain4j/langchain4j",
        stars: "6.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "DeepPavlov",
        description: "An 开源 library for 深度学习 end-to-end dialog systems and chatbots. (库)",
        url: "https://github.com/deeppavlov/DeepPavlov",
        stars: "6.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "opengpts",
        description: "开源AI项目",
        url: "https://github.com/langchain-ai/opengpts",
        stars: "6.6k",
        language: "TypeScript",
        visible_by_default: false
    },
    {
        name: "R2R",
        description: "SoTA production-ready AI retrieval system. Agentic Retrieval-Augmented Generation (检索增强生成) with a RESTful API.",
        url: "https://github.com/SciPhi-AI/R2R",
        stars: "6.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "llm",
        description: "[Unmaintained, see README] An ecosystem of Rust libraries for working with 大型语言模型s",
        url: "https://github.com/rustformers/llm",
        stars: "6.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "RWKV-Runner",
        description: "A RWKV management and startup tool, full automation, only 8MB. And provides an interface compatible with the OpenAI API. RWKV is a 大型语言模型 that is full...",
        url: "https://github.com/josStorer/RWKV-Runner",
        stars: "5.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "agents",
        description: "A powerful framework for building realtime voice AI智能代理s 🤖🎙️📹 (框架)",
        url: "https://github.com/livekit/agents",
        stars: "5.5k",
        language: "TypeScript",
        visible_by_default: false
    },
    {
        name: "TaskingAI",
        description: "优质开源AI项目",
        url: "https://github.com/TaskingAI/TaskingAI",
        stars: "5.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ToolBench",
        description: "[ICLR'24 spotlight] An open platform for training, 模型部署, and evaluating 大型语言模型 for tool learning. (工具)",
        url: "https://github.com/OpenBMB/ToolBench",
        stars: "5.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "pgai",
        description: "A suite of tools to develop 检索增强生成, semantic search, and other AI applications more easily with PostgreSQL (工具)",
        url: "https://github.com/timescale/pgai",
        stars: "4.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "FLAML",
        description: "开源AI项目：Cppx2vSPVP. (库)",
        url: "https://github.com/microsoft/FLAML",
        stars: "4.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "lmops",
        description: "General technology for enabling AI capabilities w/ 大型语言模型s and M大型语言模型s",
        url: "https://github.com/microsoft/lmops",
        stars: "3.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "llm-workflow-engine",
        description: "Power CLI and Workflow manager for 大型语言模型s (core package)",
        url: "https://github.com/llm-workflow-engine/llm-workflow-engine",
        stars: "3.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "casibase",
        description: "开源AI项目：ai-admin.casibase.com",
        url: "https://github.com/casibase/casibase",
        stars: "3.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "graphiti",
        description: "优质开源AI项目",
        url: "https://github.com/getzep/graphiti",
        stars: "3.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "zep",
        description: "优质开源AI项目",
        url: "https://github.com/getzep/zep",
        stars: "3.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LLMZoo",
        description: "⚡大型语言模型 Zoo is a project that provides data, models, and 评估工具 benchmark for 大型语言模型s.⚡",
        url: "https://github.com/FreedomIntelligence/LLMZoo",
        stars: "2.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "pezzo",
        description: "🕹️ Open-source, developer-first 大型语言模型Ops platform designed to streamline prompt design, version management, instant delivery, collaboration, troubles...",
        url: "https://github.com/pezzolabs/pezzo",
        stars: "2.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "leptonai",
        description: "优质开源AI项目",
        url: "https://github.com/leptonai/leptonai",
        stars: "2.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "core",
        description: "优质开源AI项目",
        url: "https://github.com/cheshire-cat-ai/core",
        stars: "2.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "semantic-router",
        description: "优质开源AI项目",
        url: "https://github.com/aurelio-labs/semantic-router",
        stars: "2.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LlamaIndexTS",
        description: "Data framework for your 大型语言模型 applications. Focus on server side solution (框架)",
        url: "https://github.com/run-llama/LlamaIndexTS",
        stars: "2.5k",
        language: "TypeScript",
        visible_by_default: false
    },
    {
        name: "RasaGPT",
        description: "💬 RasaGPT is the first headless 大型语言模型 chatbot platform built on top of Rasa and Langchain. Built w/ Rasa, FastAPI, Langchain, LlamaIndex, SQLModel, p...",
        url: "https://github.com/paulpierre/RasaGPT",
        stars: "2.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "agenta",
        description: "The open-source 大型语言模型Ops platform: prompt playground, prompt management, 大型语言模型 评估工具, and 大型语言模型 Observability all in one place.",
        url: "https://github.com/Agenta-AI/agenta",
        stars: "2.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "griptape",
        description: "Modular Python framework for AI智能代理s and workflows with chain-of-thought reasoning, tools, and memory. (框架)",
        url: "https://github.com/griptape-ai/griptape",
        stars: "2.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "vdp",
        description: "优质开源AI项目",
        url: "https://github.com/instill-ai/vdp",
        stars: "2.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "intel-extension-for-transformers",
        description: "⚡ Build your chatbot within minutes on your favorite device; offer SOTA compression techniques for 大型语言模型s; run 大型语言模型s efficiently on Intel Platforms...",
        url: "https://github.com/intel/intel-extension-for-transformers",
        stars: "2.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "trieve",
        description: "All-in-one infrastructure for search, recommendations, 检索增强生成, and analytics offered via API",
        url: "https://github.com/devflowinc/trieve",
        stars: "2.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LLMStack",
        description: "No-code multi-agent framework to build 大型语言模型 Agents, workflows and applications with your data (框架)",
        url: "https://github.com/trypromptly/LLMStack",
        stars: "1.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Time-LLM",
        description: "[ICLR 2024] Official implementation of \" 🦙 Time-大型语言模型: Time Series Forecasting by Reprogramming 大型语言模型s\"",
        url: "https://github.com/KimMeen/Time-LLM",
        stars: "1.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ctransformers",
        description: "开源AI项目：C++ using GGML library. (库)",
        url: "https://github.com/marella/ctransformers",
        stars: "1.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "floneum",
        description: "优质开源AI项目",
        url: "https://github.com/floneum/floneum",
        stars: "1.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "RPG-DiffusionMaster",
        description: "[ICML 2024] Mastering 文本到图像生成 Diffusion: Recaptioning, Planning, and Generating with 多模态 大型语言模型s (RPG)",
        url: "https://github.com/YangLing0818/RPG-DiffusionMaster",
        stars: "1.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "langchainrb",
        description: "Build 大型语言模型-powered applications in Ruby",
        url: "https://github.com/andreibondarev/langchainrb",
        stars: "1.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LLMCompiler",
        description: "[ICML 2024] 大型语言模型Compiler: An 大型语言模型 Compiler for Parallel Function Calling",
        url: "https://github.com/SqueezeAILab/LLMCompiler",
        stars: "1.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "langchain-serve",
        description: "优质开源AI项目",
        url: "https://github.com/jina-ai/langchain-serve",
        stars: "1.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "burr",
        description: "优质开源AI项目",
        url: "https://github.com/DAGWorks-Inc/burr",
        stars: "1.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "fastRAG",
        description: "优质开源AI项目",
        url: "https://github.com/IntelLabs/fastRAG",
        stars: "1.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "rag-stack",
        description: "🤖 Deploy a private ChatGPT alternative hosted within your VPC. 🔮 Connect it to your organization's knowledge base and use it as a corporate oracle. Su...",
        url: "https://github.com/psychic-api/rag-stack",
        stars: "1.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "llm-chain",
        description: "`大型语言模型-chain` is a powerful rust crate for building chains in 大型语言模型s allowing you to summarise text and complete complex tasks",
        url: "https://github.com/sobelio/llm-chain",
        stars: "1.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "windows-ai-studio",
        description: "开源AI项目",
        url: "https://github.com/microsoft/windows-ai-studio",
        stars: "1.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "modelfusion",
        description: "优质开源AI项目",
        url: "https://github.com/vercel/modelfusion",
        stars: "1.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "raptor",
        description: "优质开源AI项目",
        url: "https://github.com/parthsarthi03/raptor",
        stars: "1.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "RAGxplorer",
        description: "Open-source tool to visualise your 检索增强生成 🔮 (工具)",
        url: "https://github.com/gabrielchua/RAGxplorer",
        stars: "1.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "axflow",
        description: "优质开源AI项目",
        url: "https://github.com/axflow/axflow",
        stars: "1.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "generative-ai-swift",
        description: "优质开源AI项目",
        url: "https://github.com/google/generative-ai-swift",
        stars: "1.1k",
        language: "Python",
        visible_by_default: false
    },
];

// 为子类别 1.1 添加 184 个项目
projectsData["1.1"] = [
    {
        name: "DeepSeek-V3",
        description: "开源AI项目",
        url: "https://github.com/deepseek-ai/DeepSeek-V3",
        stars: "95.1k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "DeepSeek-R1",
        description: "开源AI项目",
        url: "https://github.com/deepseek-ai/DeepSeek-R1",
        stars: "88.1k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "whisper",
        description: "优质开源AI项目",
        url: "https://github.com/openai/whisper",
        stars: "79.5k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "stable-diffusion",
        description: "A latent 文本到图像生成 diffusion model",
        url: "https://github.com/CompVis/stable-diffusion",
        stars: "70.3k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "llama",
        description: "优质开源AI项目",
        url: "https://github.com/facebookresearch/llama",
        stars: "58.0k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "grok-1",
        description: "优质开源AI项目",
        url: "https://github.com/xai-org/grok-1",
        stars: "50.3k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "PaddleOCR",
        description: "Awesome 多语言 OCR toolkits based on PaddlePaddle (practical ultra lightweight OCR system, support 80+ languages recognition, provide data annotation and...",
        url: "https://github.com/PaddlePaddle/PaddleOCR",
        stars: "48.1k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "ChatGLM-6B",
        description: "ChatGLM-6B: An Open Bilingual Dialogue Language Model | 开源双语对话语言模型",
        url: "https://github.com/THUDM/ChatGLM-6B",
        stars: "41.0k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "stablediffusion",
        description: "优质开源AI项目",
        url: "https://github.com/Stability-AI/stablediffusion",
        stars: "40.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "nanoGPT",
        description: "开源AI项目：finetuning medium-sized GPTs.",
        url: "https://github.com/karpathy/nanoGPT",
        stars: "40.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "bark",
        description: "优质开源AI项目",
        url: "https://github.com/suno-ai/bark",
        stars: "37.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "GFPGAN",
        description: "优质开源AI项目",
        url: "https://github.com/TencentARC/GFPGAN",
        stars: "36.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ControlNet",
        description: "优质开源AI项目",
        url: "https://github.com/lllyasviel/ControlNet",
        stars: "31.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "stanford_alpaca",
        description: "优质开源AI项目",
        url: "https://github.com/tatsu-lab/stanford_alpaca",
        stars: "29.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "llama3",
        description: "优质开源AI项目",
        url: "https://github.com/meta-llama/llama3",
        stars: "28.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "CLIP",
        description: "优质开源AI项目",
        url: "https://github.com/openai/CLIP",
        stars: "28.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Open-Sora",
        description: "优质开源AI项目",
        url: "https://github.com/hpcaitech/Open-Sora",
        stars: "26.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "MoneyPrinterTurbo",
        description: "利用AI大模型，一键生成高清短视频 Generate short videos with one click using AI 大型语言模型.",
        url: "https://github.com/harry0703/MoneyPrinterTurbo",
        stars: "25.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "generative-models",
        description: "优质开源AI项目",
        url: "https://github.com/Stability-AI/generative-models",
        stars: "25.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "vit-pytorch",
        description: "优质开源AI项目",
        url: "https://github.com/lucidrains/vit-pytorch",
        stars: "22.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LLaVA",
        description: "优质开源AI项目",
        url: "https://github.com/haotian-liu/LLaVA",
        stars: "22.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "fish-speech",
        description: "优质开源AI项目",
        url: "https://github.com/fishaudio/fish-speech",
        stars: "20.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "OmniLMM",
        description: "MiniCPM-o 2.6: A GPT-4o Level M大型语言模型 for Vision, Speech and 多模态 Live Streaming on Your Phone",
        url: "https://github.com/OpenBMB/OmniLMM",
        stars: "19.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "MiniCPM-o",
        description: "MiniCPM-o 2.6: A GPT-4o Level M大型语言模型 for Vision, Speech and 多模态 Live Streaming on Your Phone",
        url: "https://github.com/OpenBMB/MiniCPM-o",
        stars: "19.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Chinese-LLaMA-Alpaca",
        description: "中文LLaMA&Alpaca大语言模型+本地CPU/GPU训练部署 (Chinese LLaMA & Alpaca 大型语言模型s)",
        url: "https://github.com/ymcui/Chinese-LLaMA-Alpaca",
        stars: "18.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Qwen",
        description: "The official repo of Qwen (通义千问) chat & pretrained 大型语言模型 proposed by Alibaba Cloud.",
        url: "https://github.com/QwenLM/Qwen",
        stars: "17.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ml-stable-diffusion",
        description: "优质开源AI项目",
        url: "https://github.com/apple/ml-stable-diffusion",
        stars: "17.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Janus",
        description: "优质开源AI项目",
        url: "https://github.com/deepseek-ai/Janus",
        stars: "17.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Qwen1.5",
        description: "Qwen2.5 is the 大型语言模型 series developed by Qwen team, Alibaba Cloud.",
        url: "https://github.com/QwenLM/Qwen1.5",
        stars: "16.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "sentence-transformers",
        description: "优质开源AI项目",
        url: "https://github.com/UKPLab/sentence-transformers",
        stars: "16.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "codellama",
        description: "优质开源AI项目",
        url: "https://github.com/facebookresearch/codellama",
        stars: "16.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "FinGPT",
        description: "FinGPT: Open-Source Financial 大型语言模型s!  Revolutionize 🔥    We release the trained model on HuggingFace.",
        url: "https://github.com/AI4Finance-Foundation/FinGPT",
        stars: "15.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "dalle-mini",
        description: "优质开源AI项目",
        url: "https://github.com/borisdayma/dalle-mini",
        stars: "14.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Llama2-Chinese",
        description: "Llama中文社区，Llama3在线体验和微调模型已开放，实时汇总最新Llama3学习资料，已将所有代码更新适配Llama3，构建最好的中文Llama大模型，完全开源可商用",
        url: "https://github.com/FlagAlpha/Llama2-Chinese",
        stars: "14.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "mamba",
        description: "优质开源AI项目",
        url: "https://github.com/state-spaces/mamba",
        stars: "14.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ChatGLM3",
        description: "ChatGLM3 series: Open Bilingual Chat 大型语言模型s | 开源双语对话语言模型",
        url: "https://github.com/THUDM/ChatGLM3",
        stars: "13.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "deepmind-research",
        description: "优质开源AI项目",
        url: "https://github.com/google-deepmind/deepmind-research",
        stars: "13.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "RWKV-LM",
        description: "RWKV (pronounced RwaKuv) is an RNN with great 大型语言模型 performance, which can also be directly trained like a GPT Transformer模型 (parallelizable). We are...",
        url: "https://github.com/BlinkDL/RWKV-LM",
        stars: "13.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "alphafold",
        description: "优质开源AI项目",
        url: "https://github.com/google-deepmind/alphafold",
        stars: "13.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "latent-diffusion",
        description: "优质开源AI项目",
        url: "https://github.com/CompVis/latent-diffusion",
        stars: "12.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "MOSS",
        description: "优质开源AI项目",
        url: "https://github.com/OpenMOSS/MOSS",
        stars: "12.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Megatron-LM",
        description: "优质开源AI项目",
        url: "https://github.com/NVIDIA/Megatron-LM",
        stars: "12.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Open-Sora-Plan",
        description: "优质开源AI项目",
        url: "https://github.com/PKU-YuanGroup/Open-Sora-Plan",
        stars: "11.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "shap-e",
        description: "优质开源AI项目",
        url: "https://github.com/openai/shap-e",
        stars: "11.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "TinyZero",
        description: "优质开源AI项目",
        url: "https://github.com/Jiayi-Pan/TinyZero",
        stars: "11.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "seamless_communication",
        description: "优质开源AI项目",
        url: "https://github.com/facebookresearch/seamless_communication",
        stars: "11.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "open_clip",
        description: "优质开源AI项目",
        url: "https://github.com/mlfoundations/open_clip",
        stars: "11.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "AnimateDiff",
        description: "优质开源AI项目",
        url: "https://github.com/guoyww/AnimateDiff",
        stars: "11.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "DALLE2-pytorch",
        description: "Implementation of DALL-E 2, OpenAI's updated 文本到图像生成 synthesis neural network,  in Pytorch",
        url: "https://github.com/lucidrains/DALLE2-pytorch",
        stars: "11.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "vision_transformer",
        description: "开源AI项目",
        url: "https://github.com/google-research/vision_transformer",
        stars: "11.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "CogVideo",
        description: "优质开源AI项目",
        url: "https://github.com/THUDM/CogVideo",
        stars: "11.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "DALL-E",
        description: "优质开源AI项目",
        url: "https://github.com/openai/DALL-E",
        stars: "10.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "magic-animate",
        description: "优质开源AI项目",
        url: "https://github.com/magic-research/magic-animate",
        stars: "10.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "mistral-src",
        description: "Official 推理优化 library for Mistral models (库)",
        url: "https://github.com/mistralai/mistral-src",
        stars: "10.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "AudioGPT",
        description: "优质开源AI项目",
        url: "https://github.com/AIGC-Audio/AudioGPT",
        stars: "10.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "denoising-diffusion-pytorch",
        description: "优质开源AI项目",
        url: "https://github.com/lucidrains/denoising-diffusion-pytorch",
        stars: "9.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "stable-dreamfusion",
        description: "优质开源AI项目",
        url: "https://github.com/ashawkey/stable-dreamfusion",
        stars: "8.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "CodeGeeX",
        description: "CodeGeeX: An Open 多语言 代码生成 Model (KDD 2023)",
        url: "https://github.com/THUDM/CodeGeeX",
        stars: "8.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "insanely-fast-whisper",
        description: "开源AI项目",
        url: "https://github.com/Vaibhavs10/insanely-fast-whisper",
        stars: "8.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "imagen-pytorch",
        description: "Implementation of Imagen, Google's 文本到图像生成 Neural Network, in Pytorch",
        url: "https://github.com/lucidrains/imagen-pytorch",
        stars: "8.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "jukebox",
        description: "优质开源AI项目",
        url: "https://github.com/openai/jukebox",
        stars: "8.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "EmotiVoice",
        description: "优质开源AI项目",
        url: "https://github.com/netease-youdao/EmotiVoice",
        stars: "7.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "IF",
        description: "开源AI项目",
        url: "https://github.com/deep-floyd/IF",
        stars: "7.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "GroundingDINO",
        description: "优质开源AI项目",
        url: "https://github.com/IDEA-Research/GroundingDINO",
        stars: "7.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "PaLM-rlhf-pytorch",
        description: "优质开源AI项目",
        url: "https://github.com/lucidrains/PaLM-rlhf-pytorch",
        stars: "7.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "EMO",
        description: "优质开源AI项目",
        url: "https://github.com/HumanAIGC/EMO",
        stars: "7.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "open_llama",
        description: "优质开源AI项目",
        url: "https://github.com/openlm-research/open_llama",
        stars: "7.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Depth-Anything",
        description: "优质开源AI项目",
        url: "https://github.com/LiheYoung/Depth-Anything",
        stars: "7.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "VAR",
        description: "[NeurIPS 2024 Best Paper][GPT beats diffusion🔥] [scaling laws in visual generation📈] Official impl. of \"Visual Autoregressive Modeling: Scalable 图像生成 ...",
        url: "https://github.com/FoundationVision/VAR",
        stars: "7.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "MiniCPM",
        description: "MiniCPM3-4B: An edge-side 大型语言模型 that surpasses GPT-3.5-Turbo.",
        url: "https://github.com/OpenBMB/MiniCPM",
        stars: "7.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LWM",
        description: "优质开源AI项目",
        url: "https://github.com/LargeWorldModel/LWM",
        stars: "7.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Chinese-LLaMA-Alpaca-2",
        description: "中文LLaMA-2 & Alpaca-2大模型二期项目 + 64K超长上下文模型 (Chinese LLaMA-2 & Alpaca-2 大型语言模型s with 64K long context models)",
        url: "https://github.com/ymcui/Chinese-LLaMA-Alpaca-2",
        stars: "7.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "InternLM",
        description: "优质开源AI项目",
        url: "https://github.com/InternLM/InternLM",
        stars: "6.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "guided-diffusion",
        description: "开源AI项目",
        url: "https://github.com/openai/guided-diffusion",
        stars: "6.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "threestudio",
        description: "优质开源AI项目",
        url: "https://github.com/threestudio-project/threestudio",
        stars: "6.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "point-e",
        description: "优质开源AI项目",
        url: "https://github.com/openai/point-e",
        stars: "6.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "CogVLM",
        description: "a 最先进的-level open visual language model | 多模态预训练模型",
        url: "https://github.com/THUDM/CogVLM",
        stars: "6.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "text-to-text-transfer-transformer",
        description: "优质开源AI项目",
        url: "https://github.com/google-research/text-to-text-transfer-transformer",
        stars: "6.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "consistency_models",
        description: "优质开源AI项目",
        url: "https://github.com/openai/consistency_models",
        stars: "6.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "DeepSeek-LLM",
        description: "DeepSeek 大型语言模型: Let there be answers",
        url: "https://github.com/deepseek-ai/DeepSeek-LLM",
        stars: "6.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "OOTDiffusion",
        description: "优质开源AI项目",
        url: "https://github.com/levihsu/OOTDiffusion",
        stars: "6.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "donut",
        description: "优质开源AI项目",
        url: "https://github.com/clovaai/donut",
        stars: "6.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "lit-llama",
        description: "优质开源AI项目",
        url: "https://github.com/Lightning-AI/lit-llama",
        stars: "6.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LLaMA-Adapter",
        description: "优质开源AI项目",
        url: "https://github.com/OpenGVLab/LLaMA-Adapter",
        stars: "5.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Qwen-VL",
        description: "优质开源AI项目",
        url: "https://github.com/QwenLM/Qwen-VL",
        stars: "5.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "StyleTTS2",
        description: "StyleTTS 2: Towards Human-Level 文本到语音转换 through Style Diffusion and Adversarial Training with Large Speech Language Models",
        url: "https://github.com/yl4579/StyleTTS2",
        stars: "5.6k",
        language: "TypeScript",
        visible_by_default: false
    },
    {
        name: "DeepSeek-Coder-V2",
        description: "优质开源AI项目",
        url: "https://github.com/deepseek-ai/DeepSeek-Coder-V2",
        stars: "5.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "OLMo",
        description: "优质开源AI项目",
        url: "https://github.com/allenai/OLMo",
        stars: "5.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "gemma_pytorch",
        description: "优质开源AI项目",
        url: "https://github.com/google/gemma_pytorch",
        stars: "5.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LLM4Decompile",
        description: "Reverse Engineering: Decompiling Binary Code with 大型语言模型s",
        url: "https://github.com/albertan017/LLM4Decompile",
        stars: "5.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "DALI",
        description: "A GPU-accelerated library containing highly optimized building blocks and an execution engine for data processing to accelerate 深度学习 training and 推理优化...",
        url: "https://github.com/NVIDIA/DALI",
        stars: "5.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "openchat",
        description: "优质开源AI项目",
        url: "https://github.com/imoneoi/openchat",
        stars: "5.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "silero-models",
        description: "Silero Models: pre-trained 语音到文本转换, 文本到语音转换 and text-enhancement models made embarrassingly simple",
        url: "https://github.com/snakers4/silero-models",
        stars: "5.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "x-transformers",
        description: "优质开源AI项目",
        url: "https://github.com/lucidrains/x-transformers",
        stars: "5.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "BLIP",
        description: "优质开源AI项目",
        url: "https://github.com/salesforce/BLIP",
        stars: "5.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "CodeGen",
        description: "优质开源AI项目",
        url: "https://github.com/salesforce/CodeGen",
        stars: "5.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "DragGAN",
        description: "Unofficial Implementation of D检索增强生成GAN - \"D检索增强生成 Your GAN: Interactive Point-based Manipulation on the Generative Image Manifold\" （D检索增强生成GAN 全功能实现，...",
        url: "https://github.com/OpenGVLab/DragGAN",
        stars: "5.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Qwen2.5-Coder",
        description: "Qwen2.5-Coder is the code version of Qwen2.5, the 大型语言模型 series developed by Qwen team, Alibaba Cloud.",
        url: "https://github.com/QwenLM/Qwen2.5-Coder",
        stars: "4.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Huatuo-Llama-Med-Chinese",
        description: "Repo for BenTsao [original name: HuaTuo (华驼)], Instruction-tuning 大型语言模型s with Chinese Medical Knowledge. 本草（原名：华驼）模型仓库，基于中文医学知识的大语言模型指令微调",
        url: "https://github.com/SCIR-HI/Huatuo-Llama-Med-Chinese",
        stars: "4.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "latent-consistency-model",
        description: "优质开源AI项目",
        url: "https://github.com/luosiallen/latent-consistency-model",
        stars: "4.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "alphageometry",
        description: "开源AI项目",
        url: "https://github.com/google-deepmind/alphageometry",
        stars: "4.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "BioGPT",
        description: "开源AI项目",
        url: "https://github.com/microsoft/BioGPT",
        stars: "4.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "simclr",
        description: "优质开源AI项目",
        url: "https://github.com/google-research/simclr",
        stars: "4.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Chinese-Vicuna",
        description: "Chinese-Vicuna: A Chinese Instruction-following LLaMA-based Model —— 一个中文低资源的llama+lora方案，结构参考alpaca",
        url: "https://github.com/Facico/Chinese-Vicuna",
        stars: "4.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "VisualGLM-6B",
        description: "Chinese and English 多模态 conversational language model | 多模态中英双语对话语言模型",
        url: "https://github.com/THUDM/VisualGLM-6B",
        stars: "4.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "grok",
        description: "开源AI项目",
        url: "https://github.com/openai/grok",
        stars: "4.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "metavoice-src",
        description: "优质开源AI项目",
        url: "https://github.com/metavoiceio/metavoice-src",
        stars: "4.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "MobileAgent",
        description: "优质开源AI项目",
        url: "https://github.com/X-PLUG/MobileAgent",
        stars: "4.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Sana",
        description: "优质开源AI项目",
        url: "https://github.com/NVlabs/Sana",
        stars: "3.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Doctor-Dignity",
        description: "Doctor Dignity is an 大型语言模型 that can pass the US Medical Licensing Exam. It works offline, it's cross-platform, & your health data stays private.",
        url: "https://github.com/llSourcell/Doctor-Dignity",
        stars: "3.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ultravox",
        description: "A fast 多模态 大型语言模型 for real-time voice",
        url: "https://github.com/fixie-ai/ultravox",
        stars: "3.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "DeepSeek-VL",
        description: "优质开源AI项目",
        url: "https://github.com/deepseek-ai/DeepSeek-VL",
        stars: "3.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "multinerf",
        description: "优质开源AI项目",
        url: "https://github.com/google-research/multinerf",
        stars: "3.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Luotuo-Chinese-LLM",
        description: "骆驼(Luotuo): Open Sourced Chinese Language Models. Developed by 陈启源 @ 华中师范大学 & 李鲁鲁 @ 商汤科技 & 冷子昂 @ 商汤科技",
        url: "https://github.com/LC1332/Luotuo-Chinese-LLM",
        stars: "3.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "glide-text2im",
        description: "优质开源AI项目",
        url: "https://github.com/openai/glide-text2im",
        stars: "3.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "improved-diffusion",
        description: "优质开源AI项目",
        url: "https://github.com/openai/improved-diffusion",
        stars: "3.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "NExT-GPT",
        description: "Code and models for NExT-GPT: Any-to-Any 多模态 大型语言模型",
        url: "https://github.com/NExT-GPT/NExT-GPT",
        stars: "3.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Vim",
        description: "优质开源AI项目",
        url: "https://github.com/hustvl/Vim",
        stars: "3.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "picoGPT",
        description: "优质开源AI项目",
        url: "https://github.com/jaymody/picoGPT",
        stars: "3.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "albert",
        description: "优质开源AI项目",
        url: "https://github.com/google-research/albert",
        stars: "3.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "MiniGemini",
        description: "优质开源AI项目",
        url: "https://github.com/dvlab-research/MiniGemini",
        stars: "3.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Otter",
        description: "优质开源AI项目",
        url: "https://github.com/Luodian/Otter",
        stars: "3.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "musiclm-pytorch",
        description: "优质开源AI项目",
        url: "https://github.com/lucidrains/musiclm-pytorch",
        stars: "3.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Ask-Anything",
        description: "优质开源AI项目",
        url: "https://github.com/OpenGVLab/Ask-Anything",
        stars: "3.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "SkyThought",
        description: "优质开源AI项目",
        url: "https://github.com/NovaSky-AI/SkyThought",
        stars: "3.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "TabPFN",
        description: "优质开源AI项目",
        url: "https://github.com/PriorLabs/TabPFN",
        stars: "3.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "gemma",
        description: "Gemma open-weight 大型语言模型 library, from Google DeepMind (库)",
        url: "https://github.com/google-deepmind/gemma",
        stars: "3.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "lida",
        description: "Automatic Generation of Visualizations and Infographics using 大型语言模型s",
        url: "https://github.com/microsoft/lida",
        stars: "3.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "frame-interpolation",
        description: "优质开源AI项目",
        url: "https://github.com/google-research/frame-interpolation",
        stars: "3.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "CodeT5",
        description: "Home of CodeT5: Open Code 大型语言模型s for Code Understanding and Generation",
        url: "https://github.com/salesforce/CodeT5",
        stars: "3.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "jepa",
        description: "优质开源AI项目",
        url: "https://github.com/facebookresearch/jepa",
        stars: "2.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "InternLM-XComposer",
        description: "优质开源AI项目",
        url: "https://github.com/InternLM/InternLM-XComposer",
        stars: "2.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "galai",
        description: "优质开源AI项目",
        url: "https://github.com/paperswithcode/galai",
        stars: "2.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LongLoRA",
        description: "优质开源AI项目",
        url: "https://github.com/dvlab-research/LongLoRA",
        stars: "2.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "InternImage",
        description: "优质开源AI项目",
        url: "https://github.com/OpenGVLab/InternImage",
        stars: "2.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "s4",
        description: "优质开源AI项目",
        url: "https://github.com/state-spaces/s4",
        stars: "2.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "GeneFace",
        description: "优质开源AI项目",
        url: "https://github.com/yerfor/GeneFace",
        stars: "2.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Painter",
        description: "优质开源AI项目",
        url: "https://github.com/baaivision/Painter",
        stars: "2.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "dbrx",
        description: "Code examples and resources for DBRX, a 大型语言模型 developed by Databricks",
        url: "https://github.com/databricks/dbrx",
        stars: "2.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "VMamba",
        description: "优质开源AI项目",
        url: "https://github.com/MzeroMiko/VMamba",
        stars: "2.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "EVA",
        description: "优质开源AI项目",
        url: "https://github.com/baaivision/EVA",
        stars: "2.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "pythia",
        description: "优质开源AI项目",
        url: "https://github.com/EleutherAI/pythia",
        stars: "2.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "TigerBot",
        description: "TigerBot: A multi-language multi-task 大型语言模型",
        url: "https://github.com/TigerResearch/TigerBot",
        stars: "2.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "deepscaler",
        description: "Democratizing Reinforcement Learning for 大型语言模型s",
        url: "https://github.com/agentica-project/deepscaler",
        stars: "2.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "consistencydecoder",
        description: "优质开源AI项目",
        url: "https://github.com/openai/consistencydecoder",
        stars: "2.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LISA",
        description: "Project Page for \"LISA: Reasoning Segmentation via 大型语言模型\"",
        url: "https://github.com/dvlab-research/LISA",
        stars: "2.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "GLIGEN",
        description: "优质开源AI项目",
        url: "https://github.com/gligen/GLIGEN",
        stars: "2.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "magicoder",
        description: "优质开源AI项目",
        url: "https://github.com/ise-uiuc/magicoder",
        stars: "2.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "meditron",
        description: "Meditron is a suite of open-source medical 大型语言模型s (大型语言模型s).",
        url: "https://github.com/epfLLM/meditron",
        stars: "2.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "FinGLM",
        description: "FinGLM: 致力于构建一个开放的、公益的、持久的金融大模型项目，利用开源开放来促进「AI+金融」。",
        url: "https://github.com/MetaGLM/FinGLM",
        stars: "1.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LGM",
        description: "优质开源AI项目",
        url: "https://github.com/3DTopia/LGM",
        stars: "1.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "scispacy",
        description: "开源AI项目：biomedical documents.",
        url: "https://github.com/allenai/scispacy",
        stars: "1.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "InternVideo",
        description: "优质开源AI项目",
        url: "https://github.com/OpenGVLab/InternVideo",
        stars: "1.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ml-4m",
        description: "优质开源AI项目",
        url: "https://github.com/apple/ml-4m",
        stars: "1.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Emu",
        description: "优质开源AI项目",
        url: "https://github.com/baaivision/Emu",
        stars: "1.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "BCEmbedding",
        description: "Netease Youdao's open-source embedding and reranker models for 检索增强生成 products.",
        url: "https://github.com/netease-youdao/BCEmbedding",
        stars: "1.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "maxtext",
        description: "A simple, performant and scalable Jax 大型语言模型!",
        url: "https://github.com/google/maxtext",
        stars: "1.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "language",
        description: "优质开源AI项目",
        url: "https://github.com/google-research/language",
        stars: "1.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "MotionGPT",
        description: "[NeurIPS 2023] MotionGPT: Human Motion as a Foreign Language, a unified motion-language generation model using 大型语言模型s",
        url: "https://github.com/OpenMotionLab/MotionGPT",
        stars: "1.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "OneFormer",
        description: "优质开源AI项目",
        url: "https://github.com/SHI-Labs/OneFormer",
        stars: "1.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "FLAN",
        description: "开源AI项目",
        url: "https://github.com/google-research/FLAN",
        stars: "1.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Video-Pre-Training",
        description: "优质开源AI项目",
        url: "https://github.com/openai/Video-Pre-Training",
        stars: "1.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "lag-llama",
        description: "优质开源AI项目",
        url: "https://github.com/time-series-foundation-models/lag-llama",
        stars: "1.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ImageReward",
        description: "优质开源AI项目",
        url: "https://github.com/THUDM/ImageReward",
        stars: "1.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "evo",
        description: "优质开源AI项目",
        url: "https://github.com/evo-design/evo",
        stars: "1.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Taiwan-LLM",
        description: "Traditional Mandarin 大型语言模型s for Taiwan",
        url: "https://github.com/MiuLab/Taiwan-LLM",
        stars: "1.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Versatile-Diffusion",
        description: "开源AI项目： ICCV 2023",
        url: "https://github.com/SHI-Labs/Versatile-Diffusion",
        stars: "1.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Show-o",
        description: "[ICLR 2025] Repository for Show-o, One Single Transformer模型 to Unify 多模态 Understanding and Generation.",
        url: "https://github.com/showlab/Show-o",
        stars: "1.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "lm-human-preferences",
        description: "优质开源AI项目",
        url: "https://github.com/openai/lm-human-preferences",
        stars: "1.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "KnowLM",
        description: "An Open-sourced Knowledgable 大型语言模型 Framework. (框架)",
        url: "https://github.com/zjunlp/KnowLM",
        stars: "1.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Skywork",
        description: "Skywork series models are pre-trained on 3.2TB of high-quality 多语言 (mainly Chinese and English) and code data. We have open-sourced the model, trainin...",
        url: "https://github.com/SkyworkAI/Skywork",
        stars: "1.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "multilingual-t5",
        description: "开源AI项目",
        url: "https://github.com/google-research/multilingual-t5",
        stars: "1.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "OMG-Seg",
        description: "优质开源AI项目",
        url: "https://github.com/lxtGH/OMG-Seg",
        stars: "1.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ml-aim",
        description: "优质开源AI项目",
        url: "https://github.com/apple/ml-aim",
        stars: "1.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ProteinMPNN",
        description: "优质开源AI项目",
        url: "https://github.com/dauparas/ProteinMPNN",
        stars: "1.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "following-instructions-human-feedback",
        description: "开源AI项目",
        url: "https://github.com/openai/following-instructions-human-feedback",
        stars: "1.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ELLA",
        description: "ELLA: Equip Diffusion Models with 大型语言模型 for Enhanced Semantic Alignment",
        url: "https://github.com/ELLA-Diffusion/ELLA",
        stars: "1.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "tapas",
        description: "优质开源AI项目",
        url: "https://github.com/google-research/tapas",
        stars: "1.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "tango",
        description: "优质开源AI项目",
        url: "https://github.com/declare-lab/tango",
        stars: "1.2k",
        language: "Go",
        visible_by_default: false
    },
    {
        name: "Dromedary",
        description: "Dromedary: towards helpful, ethical and reliable 大型语言模型s.",
        url: "https://github.com/IBM/Dromedary",
        stars: "1.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Neighborhood-Attention-Transformer",
        description: "开源AI项目： CVPR 2023. Dilated Neighborhood Attention Transformer模型, arxiv 2022",
        url: "https://github.com/SHI-Labs/Neighborhood-Attention-Transformer",
        stars: "1.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "uni2ts",
        description: "优质开源AI项目",
        url: "https://github.com/SalesforceAIResearch/uni2ts",
        stars: "1.1k",
        language: "TypeScript",
        visible_by_default: false
    },
    {
        name: "SwissArmyTransformer",
        description: "SwissArmyTransformer模型 is a flexible and powerful library to develop your own Transformer模型 variants. (库)",
        url: "https://github.com/THUDM/SwissArmyTransformer",
        stars: "1.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "VisCPM",
        description: "[ICLR'24 spotlight] Chinese and English 多模态 Large Model Series (Chat and Paint) | 基于CPM基础模型的中英双语多模态大模型系列",
        url: "https://github.com/OpenBMB/VisCPM",
        stars: "1.1k",
        language: "Python",
        visible_by_default: false
    },
];

// 为子类别 5.1 添加 29 个项目
projectsData["5.1"] = [
    {
        name: "generative-ai-for-beginners",
        description: "开源AI项目：",
        url: "https://github.com/microsoft/generative-ai-for-beginners",
        stars: "77.7k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "openai-cookbook",
        description: "优质开源AI项目",
        url: "https://github.com/openai/openai-cookbook",
        stars: "62.7k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "Prompt-Engineering-Guide",
        description: "🐙 Guides, papers, lecture, notebooks and resources for 提示词工程",
        url: "https://github.com/dair-ai/Prompt-Engineering-Guide",
        stars: "54.7k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "llm-course",
        description: "Course to get into 大型语言模型s (大型语言模型s) with roadmaps and Colab notebooks.",
        url: "https://github.com/mlabonne/llm-course",
        stars: "48.9k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "LLMs-from-scratch",
        description: "Implement a ChatGPT-like 大型语言模型 in PyTorch from scratch, step by step",
        url: "https://github.com/rasbt/LLMs-from-scratch",
        stars: "43.6k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "netron",
        description: "Visualizer for neural network, 深度学习 and 机器学习 models",
        url: "https://github.com/lutzroeder/netron",
        stars: "29.8k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "prompt-engineering-for-developers",
        description: "面向开发者的 大型语言模型 入门教程，吴恩达大模型系列课程中文版",
        url: "https://github.com/datawhalechina/prompt-engineering-for-developers",
        stars: "16.8k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "llm-action",
        description: "本项目旨在分享大模型相关技术原理以及实战经验（大模型工程化、大模型应用落地）",
        url: "https://github.com/liguodongiot/llm-action",
        stars: "16.1k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "ml-engineering",
        description: "优质开源AI项目",
        url: "https://github.com/stas00/ml-engineering",
        stars: "13.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Self-Hosting-Guide",
        description: "Self-Hosting Guide. Learn all about  locally hosting (on premises & private web servers) and managing software applications by yourself or your organi...",
        url: "https://github.com/mikeroyal/Self-Hosting-Guide",
        stars: "12.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "generative-ai",
        description: "优质开源AI项目",
        url: "https://github.com/GoogleCloudPlatform/generative-ai",
        stars: "10.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LLMsPracticalGuide",
        description: "A curated list of practical guide resources of 大型语言模型s (大型语言模型s Tree, Examples, Papers)",
        url: "https://github.com/Mooler0410/LLMsPracticalGuide",
        stars: "9.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "prompt-engineering",
        description: "Tips and tricks for working with 大型语言模型s like OpenAI's GPT-4.",
        url: "https://github.com/brexhq/prompt-engineering",
        stars: "8.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "awesome-langchain",
        description: "优质开源AI项目",
        url: "https://github.com/kyrolabs/awesome-langchain",
        stars: "8.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "DeepSpeedExamples",
        description: "优质开源AI项目",
        url: "https://github.com/microsoft/DeepSpeedExamples",
        stars: "6.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "promptbase",
        description: "All things 提示词工程",
        url: "https://github.com/microsoft/promptbase",
        stars: "5.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "alignment-handbook",
        description: "优质开源AI项目",
        url: "https://github.com/huggingface/alignment-handbook",
        stars: "5.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "llm-viz",
        description: "3D Visualization of an GPT-style 大型语言模型",
        url: "https://github.com/bbycroft/llm-viz",
        stars: "4.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Learn_Prompting",
        description: "提示词工程, Generative AI, and 大型语言模型 Guide by Learn Prompting | Join our discord for the largest 提示词工程 learning community",
        url: "https://github.com/trigaten/Learn_Prompting",
        stars: "4.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "llm-numbers",
        description: "Numbers every 大型语言模型 developer should know",
        url: "https://github.com/ray-project/llm-numbers",
        stars: "4.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Awesome-AIGC-Tutorials",
        description: "Curated tutorials and resources for 大型语言模型s, AI Painting, and more.",
        url: "https://github.com/luban-agi/Awesome-AIGC-Tutorials",
        stars: "4.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "awesome-ai-in-finance",
        description: "🔬 A curated list of awesome 大型语言模型s & 深度学习 strategies & tools in financial market. (工具)",
        url: "https://github.com/georgezouq/awesome-ai-in-finance",
        stars: "3.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "aie-book",
        description: "优质开源AI项目",
        url: "https://github.com/chiphuyen/aie-book",
        stars: "3.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "unlocking-the-power-of-llms",
        description: "使用 Prompts 和 Chains 让 ChatGPT 成为神奇的生产力工具！Unlocking the power of 大型语言模型s.",
        url: "https://github.com/howl-anderson/unlocking-the-power-of-llms",
        stars: "2.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LLM-Finetuning",
        description: "大型语言模型 Finetuning with peft",
        url: "https://github.com/ashishpatel26/LLM-Finetuning",
        stars: "2.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "llm-applications",
        description: "A comprehensive guide to building 检索增强生成-based 大型语言模型 applications for production.",
        url: "https://github.com/ray-project/llm-applications",
        stars: "1.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "state-of-open-source-ai",
        description: "优质开源AI项目",
        url: "https://github.com/premAI-io/state-of-open-source-ai",
        stars: "1.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "chatgpt-exporter",
        description: "优质开源AI项目",
        url: "https://github.com/pionxzh/chatgpt-exporter",
        stars: "1.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "spreadsheets-are-all-you-need",
        description: "开源AI项目",
        url: "https://github.com/ianand/spreadsheets-are-all-you-need",
        stars: "1.5k",
        language: "TypeScript",
        visible_by_default: false
    },
];

// 为子类别 1.3 添加 43 个项目
projectsData["1.3"] = [
    {
        name: "llama.cpp",
        description: "大型语言模型 推理优化 in C/C++",
        url: "https://github.com/ggerganov/llama.cpp",
        stars: "77.7k",
        language: "C++",
        visible_by_default: true
    },
    {
        name: "whisper.cpp",
        description: "开源AI项目：C++",
        url: "https://github.com/ggerganov/whisper.cpp",
        stars: "39.0k",
        language: "C++",
        visible_by_default: true
    },
    {
        name: "unsloth",
        description: "Finetune Llama 4, DeepSeek-R1, Gemma 3 & Reasoning 大型语言模型s 2x faster with 70% less memory! 🦥",
        url: "https://github.com/unslothai/unsloth",
        stars: "36.6k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "llm.c",
        description: "大型语言模型 training in simple, raw C/CUDA",
        url: "https://github.com/karpathy/llm.c",
        stars: "26.2k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "llamafile",
        description: "Distribute and run 大型语言模型s with a single file.",
        url: "https://github.com/Mozilla-Ocho/llamafile",
        stars: "22.1k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "mlc-llm",
        description: "Universal 大型语言模型 Deployment Engine with ML Compilation",
        url: "https://github.com/mlc-ai/mlc-llm",
        stars: "20.3k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "llama2.c",
        description: "优质开源AI项目",
        url: "https://github.com/karpathy/llama2.c",
        stars: "18.3k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "flash-attention",
        description: "优质开源AI项目",
        url: "https://github.com/Dao-AILab/flash-attention",
        stars: "16.7k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "triton",
        description: "优质开源AI项目",
        url: "https://github.com/openai/triton",
        stars: "15.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "BitNet",
        description: "Official 推理优化 framework for 1-bit 大型语言模型s (框架)",
        url: "https://github.com/microsoft/BitNet",
        stars: "12.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ggml",
        description: "Tensor library for 机器学习 (库)",
        url: "https://github.com/ggerganov/ggml",
        stars: "12.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "TensorRT",
        description: "NVIDIA® TensorRT™ is an SDK for high-performance 深度学习 推理优化 on NVIDIA GPUs. This repository contains the 开源 components of TensorRT.",
        url: "https://github.com/NVIDIA/TensorRT",
        stars: "11.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "TensorRT-LLM",
        description: "TensorRT-大型语言模型 provides users with an easy-to-use Python API to define 大型语言模型s (大型语言模型s) and build TensorRT engines that contain 最先进的 optimizations t...",
        url: "https://github.com/NVIDIA/TensorRT-LLM",
        stars: "10.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "petals",
        description: "🌸 Run 大型语言模型s at home, BitTorrent-style. 模型微调 and 推理优化 up to 10x faster than offloading",
        url: "https://github.com/bigscience-workshop/petals",
        stars: "9.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ipex-llm",
        description: "Accelerate local 大型语言模型 推理优化 and finetuning (LLaMA, Mistral, ChatGLM, Qwen, DeepSeek, Mixtral, Gemma, Phi, MiniCPM, Qwen-VL, MiniCPM-V, etc.) on Intel...",
        url: "https://github.com/intel-analytics/ipex-llm",
        stars: "7.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "BigDL",
        description: "Accelerate local 大型语言模型 推理优化 and finetuning (LLaMA, Mistral, ChatGLM, Qwen, DeepSeek, Mixtral, Gemma, Phi, MiniCPM, Qwen-VL, MiniCPM-V, etc.) on Intel...",
        url: "https://github.com/intel-analytics/BigDL",
        stars: "7.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "cutlass",
        description: "优质开源AI项目",
        url: "https://github.com/NVIDIA/cutlass",
        stars: "7.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "bitsandbytes",
        description: "Accessible 大型语言模型s via k-bit quantization for PyTorch.",
        url: "https://github.com/TimDettmers/bitsandbytes",
        stars: "6.9k",
        language: "TypeScript",
        visible_by_default: false
    },
    {
        name: "gemma.cpp",
        description: "优质开源AI项目",
        url: "https://github.com/google/gemma.cpp",
        stars: "6.3k",
        language: "C++",
        visible_by_default: false
    },
    {
        name: "gpt-fast",
        description: "Simple and efficient pytorch-native Transformer模型 文本生成 in <1000 LOC of python.",
        url: "https://github.com/pytorch-labs/gpt-fast",
        stars: "5.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "AutoGPTQ",
        description: "An easy-to-use 大型语言模型s quantization package with user-friendly apis, based on GPTQ algorithm.",
        url: "https://github.com/PanQiWei/AutoGPTQ",
        stars: "4.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "exllamav2",
        description: "A fast 推理优化 library for running 大型语言模型s locally on modern consumer-class GPUs (库)",
        url: "https://github.com/turboderp/exllamav2",
        stars: "4.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "CTranslate2",
        description: "Fast 推理优化 engine for Transformer模型 models",
        url: "https://github.com/OpenNMT/CTranslate2",
        stars: "3.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "fastllm",
        description: "fast大型语言模型是c++实现，后端无依赖（仅依赖CUDA，无需依赖PyTorch）的高性能大模型推理库。  可实现单4090推理DeepSeek R1 671B INT4模型，单路可达20+tps。",
        url: "https://github.com/ztxz16/fastllm",
        stars: "3.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "vector-quantize-pytorch",
        description: "优质开源AI项目",
        url: "https://github.com/lucidrains/vector-quantize-pytorch",
        stars: "3.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "GPTQ-for-LLaMa",
        description: "优质开源AI项目",
        url: "https://github.com/qwopqwop200/GPTQ-for-LLaMa",
        stars: "3.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "chatglm.cpp",
        description: "优质开源AI项目",
        url: "https://github.com/li-plus/chatglm.cpp",
        stars: "3.0k",
        language: "C++",
        visible_by_default: false
    },
    {
        name: "Torch-Pruning",
        description: "优质开源AI项目",
        url: "https://github.com/VainF/Torch-Pruning",
        stars: "3.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "llm-awq",
        description: "[MLSys 2024 Best Paper Award] AWQ: Activation-aware Weight Quantization for 大型语言模型 Compression and Acceleration",
        url: "https://github.com/mit-han-lab/llm-awq",
        stars: "2.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "exllama",
        description: "优质开源AI项目",
        url: "https://github.com/turboderp/exllama",
        stars: "2.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Medusa",
        description: "Medusa: Simple Framework for Accelerating 大型语言模型 Generation with Multiple Decoding Heads (框架)",
        url: "https://github.com/FasterDecoding/Medusa",
        stars: "2.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "JittorLLMs",
        description: "计图大模型推理库，具有高性能、配置要求低、中文支持好、可移植等特点",
        url: "https://github.com/Jittor/JittorLLMs",
        stars: "2.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ThunderKittens",
        description: "优质开源AI项目",
        url: "https://github.com/HazyResearch/ThunderKittens",
        stars: "2.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "sparseml",
        description: "优质开源AI项目",
        url: "https://github.com/neuralmagic/sparseml",
        stars: "2.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "gptq",
        description: "优质开源AI项目",
        url: "https://github.com/IST-DASLab/gptq",
        stars: "2.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ao",
        description: "优质开源AI项目",
        url: "https://github.com/pytorch-labs/ao",
        stars: "1.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "uTensor",
        description: "TinyML AI 推理优化 library (库)",
        url: "https://github.com/uTensor/uTensor",
        stars: "1.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "rwkv.cpp",
        description: "开源AI项目：INT8 and FP16 推理优化 on CPU for RWKV language model",
        url: "https://github.com/saharNooby/rwkv.cpp",
        stars: "1.5k",
        language: "C++",
        visible_by_default: false
    },
    {
        name: "smoothquant",
        description: "[ICML 2023] SmoothQuant: Accurate and Efficient Post-Training Quantization for 大型语言模型s",
        url: "https://github.com/mit-han-lab/smoothquant",
        stars: "1.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "lightning-thunder",
        description: "优质开源AI项目",
        url: "https://github.com/Lightning-AI/lightning-thunder",
        stars: "1.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "AQLM",
        description: "Official Pytorch repository for Extreme Compression of 大型语言模型s via Additive Quantization https://arxiv.org/pdf/2401.06118.pdf and PV-Tuning: Beyond St...",
        url: "https://github.com/Vahe1994/AQLM",
        stars: "1.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LookaheadDecoding",
        description: "[ICML 2024] Break the Sequential Dependency of 大型语言模型 推理优化 Using Lookahead Decoding",
        url: "https://github.com/hao-ai-lab/LookaheadDecoding",
        stars: "1.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "segment-anything-fast",
        description: "优质开源AI项目",
        url: "https://github.com/pytorch-labs/segment-anything-fast",
        stars: "1.2k",
        language: "Python",
        visible_by_default: false
    },
];

// 为子类别 3.1 添加 60 个项目
projectsData["3.1"] = [
    {
        name: "screenshot-to-code",
        description: "开源AI项目：Vue)",
        url: "https://github.com/abi/screenshot-to-code",
        stars: "69.4k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "gpt-engineer",
        description: "开源AI项目：lovable.dev",
        url: "https://github.com/gpt-engineer-org/gpt-engineer",
        stars: "53.8k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "OpenHands",
        description: "优质开源AI项目",
        url: "https://github.com/All-Hands-AI/OpenHands",
        stars: "52.4k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "gpt-pilot",
        description: "优质开源AI项目",
        url: "https://github.com/Pythagora-io/gpt-pilot",
        stars: "32.6k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "tabby",
        description: "优质开源AI项目",
        url: "https://github.com/TabbyML/tabby",
        stars: "30.7k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "aider",
        description: "优质开源AI项目",
        url: "https://github.com/paul-gauthier/aider",
        stars: "30.6k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "cursor",
        description: "优质开源AI项目",
        url: "https://github.com/getcursor/cursor",
        stars: "29.1k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "ChatDev",
        description: "Create Customized Software using Natural Language Idea (through 大型语言模型-powered Multi-Agent Collaboration)",
        url: "https://github.com/OpenBMB/ChatDev",
        stars: "26.6k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "continue",
        description: "优质开源AI项目",
        url: "https://github.com/continuedev/continue",
        stars: "25.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "devika",
        description: "优质开源AI项目",
        url: "https://github.com/stitionai/devika",
        stars: "19.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "pandas-ai",
        description: "Chat with your database or your datalake (SQL, CSV, parquet). PandasAI makes data analysis conversational using 大型语言模型s and 检索增强生成.",
        url: "https://github.com/Sinaptik-AI/pandas-ai",
        stars: "18.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Screenshot-to-code",
        description: "优质开源AI项目",
        url: "https://github.com/emilwallner/Screenshot-to-code",
        stars: "16.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "DB-GPT",
        description: "优质开源AI项目",
        url: "https://github.com/eosphoros-ai/DB-GPT",
        stars: "16.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "SWE-agent",
        description: "优质开源AI项目",
        url: "https://github.com/princeton-nlp/SWE-agent",
        stars: "15.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "fauxpilot",
        description: "优质开源AI项目",
        url: "https://github.com/fauxpilot/fauxpilot",
        stars: "14.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "vanna",
        description: "🤖 Chat with your SQL database 📊. Accurate Text-to-SQL Generation via 大型语言模型s using 检索增强生成 🔄.",
        url: "https://github.com/vanna-ai/vanna",
        stars: "14.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "draw-a-ui",
        description: "优质开源AI项目",
        url: "https://github.com/SawyerHood/draw-a-ui",
        stars: "13.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "gorilla",
        description: "Gorilla: Training and Evaluating 大型语言模型s for Function Calls (Tool Calls) (工具)",
        url: "https://github.com/ShishirPatil/gorilla",
        stars: "11.9k",
        language: "Go",
        visible_by_default: false
    },
    {
        name: "TabNine",
        description: "优质开源AI项目",
        url: "https://github.com/codota/TabNine",
        stars: "10.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "shell_gpt",
        description: "A command-line productivity tool powered by AI 大型语言模型s like GPT-4, will help you accomplish your tasks faster and more efficiently. (工具)",
        url: "https://github.com/TheR1D/shell_gpt",
        stars: "10.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "aicommits",
        description: "优质开源AI项目",
        url: "https://github.com/Nutlope/aicommits",
        stars: "8.3k",
        language: "TypeScript",
        visible_by_default: false
    },
    {
        name: "PentestGPT",
        description: "优质开源AI项目",
        url: "https://github.com/GreyDGL/PentestGPT",
        stars: "8.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "sweep",
        description: "优质开源AI项目",
        url: "https://github.com/sweepai/sweep",
        stars: "7.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "WrenAI",
        description: "优质开源AI项目",
        url: "https://github.com/Canner/WrenAI",
        stars: "7.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "opencommit",
        description: "GPT wrapper for git — generate commit messages with an 大型语言模型 in 1 sec — works best with Claude 3.5 — supports local models too",
        url: "https://github.com/di-sukharev/opencommit",
        stars: "6.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "DevOpsGPT",
        description: "Multi agent system for AI-driven software development. Combine 大型语言模型 with DevOps tools to convert natural language requirements into working software...",
        url: "https://github.com/kuafuai/DevOpsGPT",
        stars: "5.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "llamacoder",
        description: "优质开源AI项目",
        url: "https://github.com/Nutlope/llamacoder",
        stars: "5.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "how2",
        description: "优质开源AI项目",
        url: "https://github.com/santinic/how2",
        stars: "5.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "sqlchat",
        description: "优质开源AI项目",
        url: "https://github.com/sqlchat/sqlchat",
        stars: "5.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "codeium.vim",
        description: "优质开源AI项目",
        url: "https://github.com/Exafunction/codeium.vim",
        stars: "4.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "GraphGPT",
        description: "优质开源AI项目",
        url: "https://github.com/varunshenoy/GraphGPT",
        stars: "4.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ai-code-translator",
        description: "优质开源AI项目",
        url: "https://github.com/mckaywrigley/ai-code-translator",
        stars: "4.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "adrenaline",
        description: "优质开源AI项目",
        url: "https://github.com/shobrook/adrenaline",
        stars: "3.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "sqlcoder",
        description: "SoTA 大型语言模型 for converting natural language questions to SQL queries",
        url: "https://github.com/defog-ai/sqlcoder",
        stars: "3.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "aiac",
        description: "优质开源AI项目",
        url: "https://github.com/gofireflyio/aiac",
        stars: "3.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "gpt-code-ui",
        description: "优质开源AI项目",
        url: "https://github.com/ricklamers/gpt-code-ui",
        stars: "3.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "gptscript",
        description: "优质开源AI项目",
        url: "https://github.com/gptscript-ai/gptscript",
        stars: "3.2k",
        language: "TypeScript",
        visible_by_default: false
    },
    {
        name: "potpie",
        description: "优质开源AI项目",
        url: "https://github.com/potpie-ai/potpie",
        stars: "3.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "gpt-repository-loader",
        description: "Convert code repos into an 大型语言模型 prompt-friendly format. Mostly built by GPT-4.",
        url: "https://github.com/mpoon/gpt-repository-loader",
        stars: "3.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "backend-GPT",
        description: "开源AI项目",
        url: "https://github.com/RootbeerComputer/backend-GPT",
        stars: "2.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "fern",
        description: "优质开源AI项目",
        url: "https://github.com/fern-api/fern",
        stars: "2.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "auto-code-rover",
        description: "优质开源AI项目",
        url: "https://github.com/nus-apr/auto-code-rover",
        stars: "2.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "autodoc",
        description: "Experimental toolkit for auto-generating codebase documentation using 大型语言模型s (工具)",
        url: "https://github.com/context-labs/autodoc",
        stars: "2.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "scribe",
        description: "优质开源AI项目",
        url: "https://github.com/knuckleswtf/scribe",
        stars: "2.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "code-review-gpt",
        description: "Code review powered by 大型语言模型s (OpenAI GPT4, Sonnet 3.5) & 嵌入向量 ⚡️ Improve code quality and catch bugs before you break production 🚀 Lives in your Git...",
        url: "https://github.com/mattzcarey/code-review-gpt",
        stars: "1.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "readme-ai",
        description: "优质开源AI项目",
        url: "https://github.com/eli64s/readme-ai",
        stars: "1.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "refact",
        description: "AI智能代理 that handles engineering tasks end-to-end: integrates with developers’ tools, plans, executes, and iterates until it achieves a successful resu...",
        url: "https://github.com/smallcloudai/refact",
        stars: "1.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "dev-gpt",
        description: "优质开源AI项目",
        url: "https://github.com/jina-ai/dev-gpt",
        stars: "1.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "SolidGPT",
        description: "优质开源AI项目",
        url: "https://github.com/AI-Citizen/SolidGPT",
        stars: "1.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "pythagora",
        description: "Generate automated tests for your Node.js app via 大型语言模型s without developers having to write a single line of code.",
        url: "https://github.com/Pythagora-io/pythagora",
        stars: "1.8k",
        language: "Go",
        visible_by_default: false
    },
    {
        name: "Agentless",
        description: "优质开源AI项目",
        url: "https://github.com/OpenAutoCoder/Agentless",
        stars: "1.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "react-agent",
        description: "The open-source React.js Autonomous 大型语言模型 Agent",
        url: "https://github.com/eylonmiz/react-agent",
        stars: "1.6k",
        language: "JavaScript",
        visible_by_default: false
    },
    {
        name: "coffee",
        description: "优质开源AI项目",
        url: "https://github.com/Coframe/coffee",
        stars: "1.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "gorilla-cli",
        description: "大型语言模型s for your CLI",
        url: "https://github.com/gorilla-llm/gorilla-cli",
        stars: "1.3k",
        language: "Go",
        visible_by_default: false
    },
    {
        name: "AutoPR",
        description: "优质开源AI项目",
        url: "https://github.com/irgolic/AutoPR",
        stars: "1.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "llm-vscode",
        description: "大型语言模型 powered development for VSCode",
        url: "https://github.com/huggingface/llm-vscode",
        stars: "1.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "oss-fuzz-gen",
        description: "大型语言模型 powered fuzzing via OSS-Fuzz.",
        url: "https://github.com/google/oss-fuzz-gen",
        stars: "1.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "sudolang-llm-support",
        description: "SudoLang 大型语言模型 Support for VSCode",
        url: "https://github.com/paralleldrive/sudolang-llm-support",
        stars: "1.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "shell-ai",
        description: "优质开源AI项目",
        url: "https://github.com/ricklamers/shell-ai",
        stars: "1.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "SeaGOAT",
        description: "优质开源AI项目",
        url: "https://github.com/kantord/SeaGOAT",
        stars: "1.1k",
        language: "Go",
        visible_by_default: false
    },
];

// 为子类别 3.2 添加 32 个项目
projectsData["3.2"] = [
    {
        name: "gpt_academic",
        description: "为GPT/GLM等大型语言模型大语言模型提供实用化交互接口，特别优化论文阅读/润色/写作体验，模块化设计，支持自定义快捷按钮&函数插件，支持Python和C++等项目剖析&自译解功能，PDF/LaTex论文翻译&总结功能，支持并行问询多种大型语言模型模型，支持chatglm3等本地模型。接入通义千问...",
        url: "https://github.com/binary-husky/gpt_academic",
        stars: "68.1k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "privateGPT",
        description: "优质开源AI项目",
        url: "https://github.com/imartinez/privateGPT",
        stars: "55.6k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "anything-llm",
        description: "The all-in-one Desktop & Docker AI application with built-in 检索增强生成, AI智能代理s, No-code agent builder, MCP compatibility,  and more.",
        url: "https://github.com/Mintplex-Labs/anything-llm",
        stars: "42.3k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "OpenBB",
        description: "优质开源AI项目",
        url: "https://github.com/OpenBB-finance/OpenBB",
        stars: "40.1k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "khoj",
        description: "Your AI second brain. Self-hostable. Get answers from the web or your docs. Build custom agents, schedule automations, do deep research. Turn any onli...",
        url: "https://github.com/khoj-ai/khoj",
        stars: "28.5k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "storm",
        description: "An 大型语言模型-powered knowledge curation system that researches a topic and generates a full-length report with citations.",
        url: "https://github.com/stanford-oval/storm",
        stars: "23.7k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "gpt-researcher",
        description: "大型语言模型 based autonomous agent that conducts deep local and web research on any topic and generates a long report with citations.",
        url: "https://github.com/assafelovic/gpt-researcher",
        stars: "20.7k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "localGPT",
        description: "优质开源AI项目",
        url: "https://github.com/PromtEngineer/localGPT",
        stars: "20.4k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "ChatPaper",
        description: "Use ChatGPT to summarize the arXiv papers. 全流程加速科研，利用chatgpt进行论文全文总结+专业翻译+润色+审稿+审稿回复",
        url: "https://github.com/kaixindelele/ChatPaper",
        stars: "18.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "DocsGPT",
        description: "优质开源AI项目",
        url: "https://github.com/arc53/DocsGPT",
        stars: "15.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "gpt4-pdf-chatbot-langchain",
        description: "优质开源AI项目",
        url: "https://github.com/mayooear/gpt4-pdf-chatbot-langchain",
        stars: "15.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "deep-research",
        description: "An AI-powered research assistant that performs iterative, deep research on any topic by combining search engines, web scraping, and 大型语言模型s.  The goal...",
        url: "https://github.com/dzhng/deep-research",
        stars: "15.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "danswer",
        description: "优质开源AI项目",
        url: "https://github.com/danswer-ai/danswer",
        stars: "12.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "chatGPTBox",
        description: "优质开源AI项目",
        url: "https://github.com/josStorer/chatGPTBox",
        stars: "10.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "nougat",
        description: "优质开源AI项目",
        url: "https://github.com/facebookresearch/nougat",
        stars: "9.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "FreeAskInternet",
        description: "FreeAskInternet is a completely free, PRIVATE and LOCALLY running search aggregator & answer generate using MULTI 大型语言模型s, without GPU needed. The use...",
        url: "https://github.com/nashsu/FreeAskInternet",
        stars: "8.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "pdfGPT",
        description: "优质开源AI项目",
        url: "https://github.com/bhaskatripathi/pdfGPT",
        stars: "7.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Verba",
        description: "检索增强生成 (检索增强生成) chatbot powered by Weaviate",
        url: "https://github.com/weaviate/Verba",
        stars: "7.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "rags",
        description: "优质开源AI项目",
        url: "https://github.com/run-llama/rags",
        stars: "6.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "zotero-gpt",
        description: "优质开源AI项目",
        url: "https://github.com/MuiseDestiny/zotero-gpt",
        stars: "6.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "open-deep-research",
        description: "An 开源 deep research clone. AI智能代理 that reasons large amounts of web data extracted with Firecrawl",
        url: "https://github.com/nickscamara/open-deep-research",
        stars: "5.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "myGPTReader",
        description: "优质开源AI项目",
        url: "https://github.com/madawei2699/myGPTReader",
        stars: "4.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "swirl-search",
        description: "AI Search & 检索增强生成 Without Moving Your Data. Get instant answers from your company's knowledge across 100+ apps while keeping data secure. Deploy in m...",
        url: "https://github.com/swirlai/swirl-search",
        stars: "2.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "mindforger",
        description: "优质开源AI项目",
        url: "https://github.com/dvorka/mindforger",
        stars: "2.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "manga-ocr",
        description: "优质开源AI项目",
        url: "https://github.com/kha-white/manga-ocr",
        stars: "2.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "open-deep-research",
        description: "优质开源AI项目",
        url: "https://github.com/btahir/open-deep-research",
        stars: "1.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "docetl",
        description: "A system for agentic 大型语言模型-powered data processing and ETL",
        url: "https://github.com/ucbepic/docetl",
        stars: "1.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "llmsherpa",
        description: "Developer APIs to Accelerate 大型语言模型 Projects",
        url: "https://github.com/nlmatics/llmsherpa",
        stars: "1.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "knowledge",
        description: "优质开源AI项目",
        url: "https://github.com/KnowledgeCanvas/knowledge",
        stars: "1.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "gpt-newspaper",
        description: "优质开源AI项目",
        url: "https://github.com/rotemweiss57/gpt-newspaper",
        stars: "1.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "nlm-ingestor",
        description: "This repo provides the server side code for 大型语言模型sherpa API to connect. It includes parsers for various file formats.",
        url: "https://github.com/nlmatics/nlm-ingestor",
        stars: "1.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "mokuro",
        description: "优质开源AI项目",
        url: "https://github.com/kha-white/mokuro",
        stars: "1.1k",
        language: "Python",
        visible_by_default: false
    },
];

// 为子类别 3.3 添加 22 个项目
projectsData["3.3"] = [
    {
        name: "open-interpreter",
        description: "优质开源AI项目",
        url: "https://github.com/KillianLucas/open-interpreter",
        stars: "59.0k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "quivr",
        description: "Opiniated 检索增强生成 for integrating GenAI in your apps 🧠   Focus on your product rather than the 检索增强生成. Easy integration in existing products with custo...",
        url: "https://github.com/StanGirard/quivr",
        stars: "37.7k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "fabric",
        description: "优质开源AI项目",
        url: "https://github.com/danielmiessler/fabric",
        stars: "30.5k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "openai-translator",
        description: "基于 ChatGPT API 的划词翻译浏览器插件和跨平台桌面端应用    -    Browser extension and cross-platform desktop application for translation based on ChatGPT API.",
        url: "https://github.com/openai-translator/openai-translator",
        stars: "24.3k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "skyvern",
        description: "Automate browser-based workflows with 大型语言模型s and Computer Vision",
        url: "https://github.com/Skyvern-AI/skyvern",
        stars: "12.9k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "activepieces",
        description: "Open Source AI Automation ✨ All our 280+ pieces are now available as MCP to use with 大型语言模型s",
        url: "https://github.com/activepieces/activepieces",
        stars: "12.5k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "self-operating-computer",
        description: "A framework to enable 多模态 models to operate a computer. (框架)",
        url: "https://github.com/OthersideAI/self-operating-computer",
        stars: "9.5k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "bilingual_book_maker",
        description: "优质开源AI项目",
        url: "https://github.com/yihong0618/bilingual_book_maker",
        stars: "8.4k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "UFO",
        description: "优质开源AI项目",
        url: "https://github.com/microsoft/UFO",
        stars: "6.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "page-assist",
        description: "优质开源AI项目",
        url: "https://github.com/n4ze3m/page-assist",
        stars: "6.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LaVague",
        description: "优质开源AI项目",
        url: "https://github.com/lavague-ai/LaVague",
        stars: "6.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "TagUI",
        description: "优质开源AI项目",
        url: "https://github.com/aisingapore/TagUI",
        stars: "5.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "OpenChat",
        description: "大型语言模型s custom-chatbots console ⚡",
        url: "https://github.com/openchatai/OpenChat",
        stars: "5.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "01",
        description: "优质开源AI项目",
        url: "https://github.com/KillianLucas/01",
        stars: "5.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "sparrow",
        description: "Data processing with ML, 大型语言模型 and Vision 大型语言模型",
        url: "https://github.com/katanaml/sparrow",
        stars: "4.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "OpenAgents",
        description: "优质开源AI项目",
        url: "https://github.com/xlang-ai/OpenAgents",
        stars: "4.2k",
        language: "TypeScript",
        visible_by_default: false
    },
    {
        name: "OpenAI_Agent_Swarm",
        description: "优质开源AI项目",
        url: "https://github.com/daveshap/OpenAI_Agent_Swarm",
        stars: "3.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Cradle",
        description: "优质开源AI项目",
        url: "https://github.com/BAAI-Agents/Cradle",
        stars: "2.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "OpenDAN-Personal-AI-OS",
        description: "优质开源AI项目",
        url: "https://github.com/fiatrete/OpenDAN-Personal-AI-OS",
        stars: "1.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "smartgpt",
        description: "A program that provides 大型语言模型s with the ability to complete complex tasks using plugins.",
        url: "https://github.com/Cormanz/smartgpt",
        stars: "1.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "FRIDAY",
        description: "优质开源AI项目",
        url: "https://github.com/OS-Copilot/FRIDAY",
        stars: "1.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Lumos",
        description: "A 检索增强生成 大型语言模型 co-pilot for browsing the web, powered by local 大型语言模型s",
        url: "https://github.com/andrewnguonly/Lumos",
        stars: "1.5k",
        language: "Python",
        visible_by_default: false
    },
];

// 为子类别 3.4 添加 46 个项目
projectsData["3.4"] = [
    {
        name: "ChatGPT",
        description: "优质开源AI项目",
        url: "https://github.com/lencx/ChatGPT",
        stars: "53.7k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "Open-Assistant",
        description: "优质开源AI项目",
        url: "https://github.com/LAION-AI/Open-Assistant",
        stars: "37.3k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "chatgpt-on-wechat",
        description: "基于大模型搭建的聊天机器人，同时支持 微信公众号、企业微信应用、飞书、钉钉 等接入，可选择GPT3.5/GPT-4o/GPT-o1/ DeepSeek/Claude/文心一言/讯飞星火/通义千问/ Gemini/GLM-4/Claude/Kimi/LinkAI，能处理文本、语音和图片，访问操作系统和...",
        url: "https://github.com/zhayujie/chatgpt-on-wechat",
        stars: "36.1k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "chatbox",
        description: "User-friendly Desktop Client App for AI Models/大型语言模型s (GPT, Claude, Gemini, Ollama...)",
        url: "https://github.com/Bin-Huang/chatbox",
        stars: "33.9k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "chatgpt-web",
        description: "用 Express 和  Vue3 搭建的 ChatGPT 演示网页",
        url: "https://github.com/Chanzhaoyu/chatgpt-web",
        stars: "31.9k",
        language: "JavaScript",
        visible_by_default: true
    },
    {
        name: "jan",
        description: "优质开源AI项目",
        url: "https://github.com/janhq/jan",
        stars: "28.3k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "chatgpt-retrieval-plugin",
        description: "优质开源AI项目",
        url: "https://github.com/openai/chatgpt-retrieval-plugin",
        stars: "21.2k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "generative_agents",
        description: "优质开源AI项目",
        url: "https://github.com/joonspk-research/generative_agents",
        stars: "18.8k",
        language: "TypeScript",
        visible_by_default: true
    },
    {
        name: "ml-agents",
        description: "The Unity 机器学习 Agents Toolkit (ML-Agents) is an open-source project that enables games and simulations to serve as environments for training intellige...",
        url: "https://github.com/Unity-Technologies/ml-agents",
        stars: "17.9k",
        language: "TypeScript",
        visible_by_default: false
    },
    {
        name: "chatgpt-api",
        description: "AI智能代理 stdlib that works with any 大型语言模型 and TypeScript AI SDK.",
        url: "https://github.com/transitive-bullshit/chatgpt-api",
        stars: "17.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "leon",
        description: "优质开源AI项目",
        url: "https://github.com/leon-ai/leon",
        stars: "16.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "chatgpt-mirai-qq-bot",
        description: "🤖 可 DIY 的 多模态 AI 聊天机器人 | 🚀 快速接入 微信、 QQ、Telegram、等聊天平台 | 🦈支持DeepSeek、Grok、Claude、Ollama、Gemini、OpenAI | 工作流系统、网页搜索、AI画图、人设调教、虚拟女仆、语音对话 |",
        url: "https://github.com/lss233/chatgpt-mirai-qq-bot",
        stars: "14.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "KeepChatGPT",
        description: "这是一款提高ChatGPT的数据安全能力和效率的插件。并且免费共享大量创新功能，如：自动刷新、保持活跃、数据安全、取消审计、克隆对话、言无不尽、净化页面、展示大屏、拦截跟踪、日新月异、明察秋毫等。让我们的AI体验无比安全、顺畅、丝滑、高效、简洁。",
        url: "https://github.com/xcanwin/KeepChatGPT",
        stars: "14.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "dalai",
        description: "优质开源AI项目",
        url: "https://github.com/cocktailpeanut/dalai",
        stars: "13.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "camel",
        description: "开源AI项目：www.camel-ai.org (框架)",
        url: "https://github.com/camel-ai/camel",
        stars: "11.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "llama-gpt",
        description: "优质开源AI项目",
        url: "https://github.com/getumbrel/llama-gpt",
        stars: "11.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LangBot",
        description: "😎简单易用、🧩丰富生态 - 大模型原生即时通信机器人平台 | 适配 QQ / 微信（企业微信、个人微信）/ 飞书 / 钉钉 / Discord / Telegram / Slack 等平台 | 支持 ChatGPT、DeepSeek、Dify、Claude、Gemini、xAI Grok、Ollam...",
        url: "https://github.com/RockChinQ/LangBot",
        stars: "10.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "EverydayWechat",
        description: "微信助手：1.每日定时给好友（女友）发送定制消息。2.机器人自动回复好友。3.群助手功能（例如：查询垃圾分类、天气、日历、电影实时票房、快递物流、PM2.5等）",
        url: "https://github.com/sfyc23/EverydayWechat",
        stars: "10.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ChatRWKV",
        description: "优质开源AI项目",
        url: "https://github.com/BlinkDL/ChatRWKV",
        stars: "9.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "BetterChatGPT",
        description: "优质开源AI项目",
        url: "https://github.com/ztjhz/BetterChatGPT",
        stars: "8.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ai-town",
        description: "优质开源AI项目",
        url: "https://github.com/a16z-infra/ai-town",
        stars: "8.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "chatgpt-demo",
        description: "优质开源AI项目",
        url: "https://github.com/anse-app/chatgpt-demo",
        stars: "8.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "gpt-ai-assistant",
        description: "优质开源AI项目",
        url: "https://github.com/memochou1993/gpt-ai-assistant",
        stars: "7.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "morphic",
        description: "优质开源AI项目",
        url: "https://github.com/miurla/morphic",
        stars: "7.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "llm",
        description: "Access 大型语言模型s from the command-line",
        url: "https://github.com/simonw/llm",
        stars: "6.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "RealtimeSTT",
        description: "A robust, efficient, low-latency 语音到文本转换 library with advanced voice activity detection, wake word activation and instant transcription. (库)",
        url: "https://github.com/KoljaB/RealtimeSTT",
        stars: "6.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "chatgpt-advanced",
        description: "优质开源AI项目",
        url: "https://github.com/interstellard/chatgpt-advanced",
        stars: "6.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "street-fighter-ai",
        description: "优质开源AI项目",
        url: "https://github.com/linyiLYi/street-fighter-ai",
        stars: "6.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "aichat",
        description: "All-in-one 大型语言模型 CLI tool featuring Shell Assistant, Chat-REPL, 检索增强生成, AI Tools & Agents, with access to OpenAI, Claude, Gemini, Ollama, Groq, and m...",
        url: "https://github.com/sigoden/aichat",
        stars: "6.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Voyager",
        description: "An Open-Ended Embodied Agent with 大型语言模型s",
        url: "https://github.com/MineDojo/Voyager",
        stars: "6.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "companion-app",
        description: "优质开源AI项目",
        url: "https://github.com/a16z-infra/companion-app",
        stars: "5.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Feishu-OpenAI",
        description: "🎒 飞书  ×（GPT-4 + GPT-4V + DALL·E-3 + Whisper）=  飞一般的工作体验  🚀 语音对话、角色扮演、多话题讨论、图片创作、表格分析、文档导出 🚀",
        url: "https://github.com/ConnectAI-E/Feishu-OpenAI",
        stars: "5.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "nofwl",
        description: "优质开源AI项目",
        url: "https://github.com/lencx/nofwl",
        stars: "4.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "olivia",
        description: "优质开源AI项目",
        url: "https://github.com/olivia-ai/olivia",
        stars: "3.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ChatDoctor",
        description: "开源AI项目",
        url: "https://github.com/Kent0n-Li/ChatDoctor",
        stars: "3.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LLM-As-Chatbot",
        description: "大型语言模型 as a Chatbot Service",
        url: "https://github.com/deep-diver/LLM-As-Chatbot",
        stars: "3.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "chatgpt-chrome-extension",
        description: "优质开源AI项目",
        url: "https://github.com/gragland/chatgpt-chrome-extension",
        stars: "2.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "RealtimeTTS",
        description: "优质开源AI项目",
        url: "https://github.com/KoljaB/RealtimeTTS",
        stars: "2.8k",
        language: "TypeScript",
        visible_by_default: false
    },
    {
        name: "FreedomGPT",
        description: "This codebase is for a React and Electron-based app that executes the FreedomGPT 大型语言模型 locally (offline and private) on Mac and Windows using a chat-...",
        url: "https://github.com/ohmplatform/FreedomGPT",
        stars: "2.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "SoraWebui",
        description: "优质开源AI项目",
        url: "https://github.com/SoraWebui/SoraWebui",
        stars: "2.4k",
        language: "JavaScript",
        visible_by_default: false
    },
    {
        name: "gptel",
        description: "A simple 大型语言模型 client for Emacs",
        url: "https://github.com/karthink/gptel",
        stars: "2.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "gpt-discord-bot",
        description: "优质开源AI项目",
        url: "https://github.com/openai/gpt-discord-bot",
        stars: "1.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "alpaca-electron",
        description: "The simplest way to run Alpaca (and other LLaMA-based local 大型语言模型s) on your own computer",
        url: "https://github.com/ItsPi3141/alpaca-electron",
        stars: "1.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "DI-star",
        description: "优质开源AI项目",
        url: "https://github.com/opendilab/DI-star",
        stars: "1.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "languagemodels",
        description: "Explore 大型语言模型s in 512MB of RAM",
        url: "https://github.com/jncraton/languagemodels",
        stars: "1.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Camel-AutoGPT",
        description: "🚀 Introducing 🐪 CAMEL: a game-changing role-playing approach for 大型语言模型s and auto-agents like BabyAGI & AutoGPT! Watch two agents 🤝 collaborate and so...",
        url: "https://github.com/SamurAIGPT/Camel-AutoGPT",
        stars: "1.2k",
        language: "Python",
        visible_by_default: false
    },
];

// 为子类别 3.6 添加 2 个项目
projectsData["3.6"] = [
    {
        name: "Deep-Live-Cam",
        description: "优质开源AI项目",
        url: "https://github.com/hacksider/Deep-Live-Cam",
        stars: "49.6k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "ShortGPT",
        description: "开源AI项目： tiktok channel automation",
        url: "https://github.com/RayVentura/ShortGPT",
        stars: "6.3k",
        language: "Python",
        visible_by_default: true
    },
];

// 为子类别 4.2 添加 27 个项目
projectsData["4.2"] = [
    {
        name: "vllm",
        description: "A high-throughput and memory-efficient 推理优化 and 模型部署 engine for 大型语言模型s",
        url: "https://github.com/vllm-project/vllm",
        stars: "43.6k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "faster-whisper",
        description: "优质开源AI项目",
        url: "https://github.com/guillaumekln/faster-whisper",
        stars: "15.2k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "OpenLLM",
        description: "Run any open-source 大型语言模型s, such as DeepSeek and Llama, as OpenAI compatible API endpoint in the cloud.",
        url: "https://github.com/bentoml/OpenLLM",
        stars: "11.1k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "text-generation-inference",
        description: "大型语言模型 文本生成 推理优化",
        url: "https://github.com/huggingface/text-generation-inference",
        stars: "10.0k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "FlexGen",
        description: "Running 大型语言模型s on a single GPU for throughput-oriented scenarios.",
        url: "https://github.com/FMInference/FlexGen",
        stars: "9.3k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "server",
        description: "优质开源AI项目",
        url: "https://github.com/triton-inference-server/server",
        stars: "9.0k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "jetson-inference",
        description: "优质开源AI项目",
        url: "https://github.com/dusty-nv/jetson-inference",
        stars: "8.2k",
        language: "TypeScript",
        visible_by_default: true
    },
    {
        name: "openvino",
        description: "OpenVINO™ is an 开源 toolkit for optimizing and deploying AI 推理优化 (工具)",
        url: "https://github.com/openvinotoolkit/openvino",
        stars: "8.1k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "gateway",
        description: "A blazing fast AI Gateway with integrated guardrails. Route to 200+ 大型语言模型s, 50+ AI Guardrails with 1 fast & friendly API.",
        url: "https://github.com/Portkey-AI/gateway",
        stars: "7.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "GPTCache",
        description: "Semantic cache for 大型语言模型s. Fully integrated with LangChain and llama_index.",
        url: "https://github.com/zilliztech/GPTCache",
        stars: "7.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "inference",
        description: "Replace OpenAI GPT with another 大型语言模型 in your app by changing a single line of code. X推理优化 gives you the freedom to use any 大型语言模型 you need. With X推理...",
        url: "https://github.com/xorbitsai/inference",
        stars: "7.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "serving",
        description: "A flexible, high-performance 模型部署 system for 机器学习 models",
        url: "https://github.com/tensorflow/serving",
        stars: "6.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "lmdeploy",
        description: "LMDeploy is a toolkit for compressing, deploying, and 模型部署 大型语言模型s. (工具)",
        url: "https://github.com/InternLM/lmdeploy",
        stars: "6.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "clearml",
        description: "ClearML - Auto-Magical CI/CD to streamline your AI workload. Experiment Management, Data Management, Pipeline, Orchestration, Scheduling & 模型部署 in one...",
        url: "https://github.com/allegroai/clearml",
        stars: "5.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "WhisperKit",
        description: "优质开源AI项目",
        url: "https://github.com/argmaxinc/WhisperKit",
        stars: "4.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "kserve",
        description: "优质开源AI项目",
        url: "https://github.com/kserve/kserve",
        stars: "4.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "text-embeddings-inference",
        description: "A blazing fast 推理优化 solution for text 嵌入向量 models",
        url: "https://github.com/huggingface/text-embeddings-inference",
        stars: "3.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "deepsparse",
        description: "Sparsity-aware 深度学习 推理优化 runtime for CPUs",
        url: "https://github.com/neuralmagic/deepsparse",
        stars: "3.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "lightllm",
        description: "Light大型语言模型 is a Python-based 大型语言模型 (大型语言模型) 推理优化 and 模型部署 framework, notable for its lightweight design, easy scalability, and high-speed performanc...",
        url: "https://github.com/ModelTC/lightllm",
        stars: "3.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "lorax",
        description: "Multi-LoRA 推理优化 server that scales to 1000s of fine-tuned 大型语言模型s",
        url: "https://github.com/predibase/lorax",
        stars: "2.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "mmdeploy",
        description: "优质开源AI项目",
        url: "https://github.com/open-mmlab/mmdeploy",
        stars: "2.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "langserve",
        description: "优质开源AI项目",
        url: "https://github.com/langchain-ai/langserve",
        stars: "2.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "infinity",
        description: "Infinity is a high-throughput, low-latency 模型部署 engine for text-嵌入向量, reranking models, clip, clap and colpali",
        url: "https://github.com/michaelfeil/infinity",
        stars: "2.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "S-LoRA",
        description: "优质开源AI项目",
        url: "https://github.com/S-LoRA/S-LoRA",
        stars: "1.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "inference",
        description: "优质开源AI项目",
        url: "https://github.com/roboflow/inference",
        stars: "1.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "aphrodite-engine",
        description: "Large-scale 大型语言模型 推理优化 engine",
        url: "https://github.com/PygmalionAI/aphrodite-engine",
        stars: "1.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ray-llm",
        description: "Ray大型语言模型 - 大型语言模型s on Ray (Archived). Read README for more info.",
        url: "https://github.com/ray-project/ray-llm",
        stars: "1.3k",
        language: "Python",
        visible_by_default: false
    },
];

// 为子类别 3.8 添加 3 个项目
projectsData["3.8"] = [
    {
        name: "photoprism",
        description: "优质开源AI项目",
        url: "https://github.com/photoprism/photoprism",
        stars: "36.9k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "semantra",
        description: "优质开源AI项目",
        url: "https://github.com/freedmand/semantra",
        stars: "2.6k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "NaLLM",
        description: "Repository for the Na大型语言模型 project",
        url: "https://github.com/neo4j/NaLLM",
        stars: "1.4k",
        language: "Python",
        visible_by_default: true
    },
];

// 为子类别 4.1 添加 15 个项目
projectsData["4.1"] = [
    {
        name: "faiss",
        description: "优质开源AI项目",
        url: "https://github.com/facebookresearch/faiss",
        stars: "34.1k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "milvus",
        description: "Milvus is a high-performance, cloud-native 向量数据库 built for scalable vector ANN search",
        url: "https://github.com/milvus-io/milvus",
        stars: "33.9k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "qdrant",
        description: "Qdrant - High-performance, massive-scale 向量数据库 and Vector Search Engine for the next generation of AI. Also available in the cloud https://cloud.qdran...",
        url: "https://github.com/qdrant/qdrant",
        stars: "22.9k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "chroma",
        description: "优质开源AI项目",
        url: "https://github.com/chroma-core/chroma",
        stars: "19.1k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "annoy",
        description: "开源AI项目：saving to disk",
        url: "https://github.com/spotify/annoy",
        stars: "13.6k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "weaviate",
        description: "Weaviate is an open-source 向量数据库 that stores both objects and vectors, allowing for the combination of vector search with structured filtering with th...",
        url: "https://github.com/weaviate/weaviate",
        stars: "13.0k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "txtai",
        description: "💡 All-in-one open-source 嵌入向量 database for semantic search, 大型语言模型 orchestration and language model workflows",
        url: "https://github.com/neuml/txtai",
        stars: "10.7k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "deeplake",
        description: "Database for AI. Store Vectors, Images, Texts, Videos, etc. Use with 大型语言模型s/LangChain. Store, query, version, & visualize any AI data. Stream data in...",
        url: "https://github.com/activeloopai/deeplake",
        stars: "8.5k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "vespa",
        description: "开源AI项目：vespa.ai",
        url: "https://github.com/vespa-engine/vespa",
        stars: "6.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "lancedb",
        description: "优质开源AI项目",
        url: "https://github.com/lancedb/lancedb",
        stars: "6.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "marqo",
        description: "优质开源AI项目",
        url: "https://github.com/marqo-ai/marqo",
        stars: "4.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "hnswlib",
        description: "开源AI项目：python library for fast approximate nearest neighbors (库)",
        url: "https://github.com/nmslib/hnswlib",
        stars: "4.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "usearch",
        description: "优质开源AI项目",
        url: "https://github.com/unum-cloud/usearch",
        stars: "2.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "pgvecto.rs",
        description: "优质开源AI项目",
        url: "https://github.com/tensorchord/pgvecto.rs",
        stars: "2.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "voyager",
        description: "优质开源AI项目",
        url: "https://github.com/spotify/voyager",
        stars: "1.4k",
        language: "Python",
        visible_by_default: false
    },
];

// 为子类别 4.3 添加 8 个项目
projectsData["4.3"] = [
    {
        name: "exo",
        description: "优质开源AI项目",
        url: "https://github.com/exo-explore/exo",
        stars: "27.4k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "kubeflow",
        description: "机器学习 Toolkit for Kubernetes (工具)",
        url: "https://github.com/kubeflow/kubeflow",
        stars: "14.8k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "metaflow",
        description: "开源AI项目：ML Systems",
        url: "https://github.com/Netflix/metaflow",
        stars: "8.7k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "skypilot",
        description: "优质开源AI项目",
        url: "https://github.com/skypilot-org/skypilot",
        stars: "7.6k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "gpuweb",
        description: "优质开源AI项目",
        url: "https://github.com/gpuweb/gpuweb",
        stars: "5.0k",
        language: "JavaScript",
        visible_by_default: true
    },
    {
        name: "zenml",
        description: "开源AI项目：zenml.io.",
        url: "https://github.com/zenml-io/zenml",
        stars: "4.5k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "higgsfield",
        description: "Fault-tolerant, highly scalable GPU orchestration, and a 机器学习 framework designed for training models with billions to trillions of parameters (框架)",
        url: "https://github.com/higgsfield-ai/higgsfield",
        stars: "3.3k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "AI-Horde",
        description: "优质开源AI项目",
        url: "https://github.com/Haidra-Org/AI-Horde",
        stars: "1.2k",
        language: "Python",
        visible_by_default: true
    },
];

// 为子类别 2.4 添加 45 个项目
projectsData["2.4"] = [
    {
        name: "dspy",
        description: "优质开源AI项目",
        url: "https://github.com/stanfordnlp/dspy",
        stars: "22.9k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "guidance",
        description: "A guidance language for controlling 大型语言模型s.",
        url: "https://github.com/guidance-ai/guidance",
        stars: "20.0k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "sglang",
        description: "SGLang is a fast 模型部署 framework for 大型语言模型s and vision language models. (框架)",
        url: "https://github.com/sgl-project/sglang",
        stars: "12.9k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "outlines",
        description: "优质开源AI项目",
        url: "https://github.com/outlines-dev/outlines",
        stars: "11.2k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "instructor",
        description: "structured outputs for 大型语言模型s",
        url: "https://github.com/jxnl/instructor",
        stars: "10.0k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "gpt-prompt-engineer",
        description: "开源AI项目",
        url: "https://github.com/mshumer/gpt-prompt-engineer",
        stars: "9.5k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "TypeChat",
        description: "优质开源AI项目",
        url: "https://github.com/microsoft/TypeChat",
        stars: "8.4k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "pydantic-ai",
        description: "Agent Framework / shim to use Pydantic with 大型语言模型s (框架)",
        url: "https://github.com/pydantic/pydantic-ai",
        stars: "8.1k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "streaming-llm",
        description: "优质开源AI项目",
        url: "https://github.com/mit-han-lab/streaming-llm",
        stars: "6.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ChatGPT-AutoExpert",
        description: "优质开源AI项目",
        url: "https://github.com/spdustin/ChatGPT-AutoExpert",
        stars: "6.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "civitai",
        description: "优质开源AI项目",
        url: "https://github.com/civitai/civitai",
        stars: "6.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "OpenPromptStudio",
        description: "🥣 AIGC 提示词可视化编辑器  | OPS | Open Prompt Studio",
        url: "https://github.com/Moonvy/OpenPromptStudio",
        stars: "6.2k",
        language: "TypeScript",
        visible_by_default: false
    },
    {
        name: "ChatGPT-Shortcut",
        description: "🚀💪Maximize your efficiency and productivity, support for English,中文,Español,العربية. 让生产力加倍的AI快捷指令。更有效地定制、保存和分享自己的提示词。在提示词分享社区中，轻松找到适用于不同场景的指令。",
        url: "https://github.com/rockbenben/ChatGPT-Shortcut",
        stars: "6.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "promptfoo",
        description: "Test your prompts, agents, and 检索增强生成s. Red teaming, pentesting, and vulnerability scanning for 大型语言模型s. Compare performance of GPT, Claude, Gemini, L...",
        url: "https://github.com/promptfoo/promptfoo",
        stars: "6.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "marvin",
        description: "优质开源AI项目",
        url: "https://github.com/PrefectHQ/marvin",
        stars: "5.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "tree-of-thought-llm",
        description: "[NeurIPS 2023] Tree of Thoughts: Deliberate Problem Solving with 大型语言模型s",
        url: "https://github.com/princeton-nlp/tree-of-thought-llm",
        stars: "5.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "guardrails",
        description: "Adding guardrails to 大型语言模型s.",
        url: "https://github.com/guardrails-ai/guardrails",
        stars: "4.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "jsonformer",
        description: "优质开源AI项目",
        url: "https://github.com/1rgs/jsonformer",
        stars: "4.7k",
        language: "JavaScript",
        visible_by_default: false
    },
    {
        name: "OpenPrompt",
        description: "优质开源AI项目",
        url: "https://github.com/thunlp/OpenPrompt",
        stars: "4.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "lmql",
        description: "A language for constraint-guided and efficient 大型语言模型 programming.",
        url: "https://github.com/eth-sri/lmql",
        stars: "3.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "Promptify",
        description: "提示词工程 | Prompt Versioning | Use GPT or other prompt based models to get structured output. Join our discord for Prompt-Engineering, 大型语言模型s and other ...",
        url: "https://github.com/promptslab/Promptify",
        stars: "3.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "PromptWizard",
        description: "优质开源AI项目",
        url: "https://github.com/microsoft/PromptWizard",
        stars: "3.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "gpt3-sandbox",
        description: "优质开源AI项目",
        url: "https://github.com/shreyashankar/gpt3-sandbox",
        stars: "2.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "prompttools",
        description: "Open-source tools for prompt testing and experimentation, with support for both 大型语言模型s (e.g. OpenAI, LLaMA) and 向量数据库s (e.g. Chroma, Weaviate, LanceD...",
        url: "https://github.com/hegelai/prompttools",
        stars: "2.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "promptsource",
        description: "优质开源AI项目",
        url: "https://github.com/bigscience-workshop/promptsource",
        stars: "2.8k",
        language: "TypeScript",
        visible_by_default: false
    },
    {
        name: "prompt-engine",
        description: "A library for helping developers craft prompts for 大型语言模型s (库)",
        url: "https://github.com/microsoft/prompt-engine",
        stars: "2.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ChainForge",
        description: "An open-source visual programming environment for battle-testing prompts to 大型语言模型s.",
        url: "https://github.com/ianarawjo/ChainForge",
        stars: "2.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ReAct",
        description: "优质开源AI项目",
        url: "https://github.com/ysymyth/ReAct",
        stars: "2.5k",
        language: "JavaScript",
        visible_by_default: false
    },
    {
        name: "genaiscript",
        description: "优质开源AI项目",
        url: "https://github.com/Microsoft/genaiscript",
        stars: "2.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "textgrad",
        description: "TextGrad: Automatic ''Differentiation'' via Text -- using 大型语言模型s to backpropagate textual gradients.",
        url: "https://github.com/zou-group/textgrad",
        stars: "2.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "graph-of-thoughts",
        description: "Official Implementation of \"Graph of Thoughts: Solving Elaborate Problems with 大型语言模型s\"",
        url: "https://github.com/spcl/graph-of-thoughts",
        stars: "2.3k",
        language: "TypeScript",
        visible_by_default: false
    },
    {
        name: "magentic",
        description: "Seamlessly integrate 大型语言模型s as Python functions",
        url: "https://github.com/jackmpcollins/magentic",
        stars: "2.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "EasyEdit",
        description: "[ACL 2024] An Easy-to-use Knowledge Editing Framework for 大型语言模型s. (框架)",
        url: "https://github.com/zjunlp/EasyEdit",
        stars: "2.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "leaked-system-prompts",
        description: "优质开源AI项目",
        url: "https://github.com/jujumilk3/leaked-system-prompts",
        stars: "2.2k",
        language: "TypeScript",
        visible_by_default: false
    },
    {
        name: "sd-dynamic-prompts",
        description: "开源AI项目：stable-diffusion-webui to implement a tiny template language for random prompt generation",
        url: "https://github.com/adieyal/sd-dynamic-prompts",
        stars: "2.1k",
        language: "TypeScript",
        visible_by_default: false
    },
    {
        name: "YiVal",
        description: "Your Automatic 提示词工程 Assistant for GenAI Applications",
        url: "https://github.com/YiVal/YiVal",
        stars: "2.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "aici",
        description: "优质开源AI项目",
        url: "https://github.com/microsoft/aici",
        stars: "2.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "PromptCraft-Robotics",
        description: "Community for applying 大型语言模型s to robotics and a robot simulator with ChatGPT integration",
        url: "https://github.com/microsoft/PromptCraft-Robotics",
        stars: "2.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "llm-security",
        description: "New ways of breaking app-integrated 大型语言模型s",
        url: "https://github.com/greshake/llm-security",
        stars: "1.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "lm-format-enforcer",
        description: "优质开源AI项目",
        url: "https://github.com/noamgat/lm-format-enforcer",
        stars: "1.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "kor",
        description: "大型语言模型(😽)",
        url: "https://github.com/eyurtsev/kor",
        stars: "1.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "llm-guard",
        description: "The Security Toolkit for 大型语言模型 Interactions (工具)",
        url: "https://github.com/laiyer-ai/llm-guard",
        stars: "1.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "FastEdit",
        description: "🩹Editing 大型语言模型s within 10 seconds⚡",
        url: "https://github.com/hiyouga/FastEdit",
        stars: "1.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "rebuff",
        description: "大型语言模型 Prompt Injection Detector",
        url: "https://github.com/protectai/rebuff",
        stars: "1.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "spacy-llm",
        description: "🦙 Integrating 大型语言模型s into structured NLP pipelines",
        url: "https://github.com/explosion/spacy-llm",
        stars: "1.2k",
        language: "Python",
        visible_by_default: false
    },
];

// 为子类别 4.5 添加 10 个项目
projectsData["4.5"] = [
    {
        name: "jina",
        description: "优质开源AI项目",
        url: "https://github.com/jina-ai/jina",
        stars: "21.5k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "dvc",
        description: "优质开源AI项目",
        url: "https://github.com/iterative/dvc",
        stars: "14.3k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "ivy",
        description: "Convert 机器学习 Code Between Frameworks (框架)",
        url: "https://github.com/unifyai/ivy",
        stars: "14.1k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "marimo",
        description: "优质开源AI项目",
        url: "https://github.com/marimo-team/marimo",
        stars: "12.1k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "HVM",
        description: "优质开源AI项目",
        url: "https://github.com/HigherOrderCO/HVM",
        stars: "10.9k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "einops",
        description: "优质开源AI项目",
        url: "https://github.com/arogozhnikov/einops",
        stars: "8.8k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "cog",
        description: "优质开源AI项目",
        url: "https://github.com/replicate/cog",
        stars: "8.5k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "stable-diffusion-webui-docker",
        description: "优质开源AI项目",
        url: "https://github.com/AbdBarho/stable-diffusion-webui-docker",
        stars: "7.1k",
        language: "JavaScript",
        visible_by_default: true
    },
    {
        name: "safetensors",
        description: "优质开源AI项目",
        url: "https://github.com/huggingface/safetensors",
        stars: "3.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "mnn-llm",
        description: "大型语言模型 deploy project based mnn. This project has merged into MNN.",
        url: "https://github.com/wangzhaode/mnn-llm",
        stars: "1.6k",
        language: "Python",
        visible_by_default: false
    },
];

// 为子类别 1.4 添加 29 个项目
projectsData["1.4"] = [
    {
        name: "gpt-crawler",
        description: "优质开源AI项目",
        url: "https://github.com/BuilderIO/gpt-crawler",
        stars: "21.3k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "datasets",
        description: "优质开源AI项目",
        url: "https://github.com/huggingface/datasets",
        stars: "19.9k",
        language: "TypeScript",
        visible_by_default: true
    },
    {
        name: "tiktoken",
        description: "优质开源AI项目",
        url: "https://github.com/openai/tiktoken",
        stars: "14.0k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "cleanlab",
        description: "优质开源AI项目",
        url: "https://github.com/cleanlab/cleanlab",
        stars: "10.4k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "tokenizers",
        description: "优质开源AI项目",
        url: "https://github.com/huggingface/tokenizers",
        stars: "9.6k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "minbpe",
        description: "Minimal, clean code for the Byte Pair Encoding (BPE) algorithm commonly used in 大型语言模型 tokenization.",
        url: "https://github.com/karpathy/minbpe",
        stars: "9.6k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "s1",
        description: "优质开源AI项目",
        url: "https://github.com/simplescaling/s1",
        stars: "6.1k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "snorkel",
        description: "优质开源AI项目",
        url: "https://github.com/snorkel-team/snorkel",
        stars: "5.8k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "RedPajama-Data",
        description: "The RedPajama-Data repository contains code for preparing large datasets for training 大型语言模型s.",
        url: "https://github.com/togethercomputer/RedPajama-Data",
        stars: "4.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "argilla",
        description: "优质开源AI项目",
        url: "https://github.com/argilla-io/argilla",
        stars: "4.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "GPT-4-LLM",
        description: "优质开源AI项目",
        url: "https://github.com/Instruction-Tuning-with-GPT-4/GPT-4-LLM",
        stars: "4.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "data-juicer",
        description: "优质开源AI项目",
        url: "https://github.com/alibaba/data-juicer",
        stars: "4.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "gpt-llm-trainer",
        description: "开源AI项目",
        url: "https://github.com/mshumer/gpt-llm-trainer",
        stars: "4.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "towhee",
        description: "优质开源AI项目",
        url: "https://github.com/towhee-io/towhee",
        stars: "3.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "TextAttack",
        description: "开源AI项目： (框架)",
        url: "https://github.com/QData/TextAttack",
        stars: "3.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "docta",
        description: "优质开源AI项目",
        url: "https://github.com/Docta-ai/docta",
        stars: "3.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "distilabel",
        description: "优质开源AI项目",
        url: "https://github.com/argilla-io/distilabel",
        stars: "2.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "UltraChat",
        description: "优质开源AI项目",
        url: "https://github.com/thunlp/UltraChat",
        stars: "2.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "datatrove",
        description: "优质开源AI项目",
        url: "https://github.com/huggingface/datatrove",
        stars: "2.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "autolabel",
        description: "Label, clean and enrich text datasets with 大型语言模型s.",
        url: "https://github.com/refuel-ai/autolabel",
        stars: "2.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "uda",
        description: "优质开源AI项目",
        url: "https://github.com/google-research/uda",
        stars: "2.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "lazynlp",
        description: "优质开源AI项目",
        url: "https://github.com/chiphuyen/lazynlp",
        stars: "2.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "prm800k",
        description: "800,000 step-level correctness labels on 大型语言模型 solutions to MATH problems",
        url: "https://github.com/openai/prm800k",
        stars: "2.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "hh-rlhf",
        description: "优质开源AI项目",
        url: "https://github.com/anthropics/hh-rlhf",
        stars: "1.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "WikiSQL",
        description: "优质开源AI项目",
        url: "https://github.com/salesforce/WikiSQL",
        stars: "1.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "FinRL-Meta",
        description: "优质开源AI项目",
        url: "https://github.com/AI4Finance-Foundation/FinRL-Meta",
        stars: "1.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "text",
        description: "优质开源AI项目",
        url: "https://github.com/tensorflow/text",
        stars: "1.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "control-flag",
        description: "优质开源AI项目",
        url: "https://github.com/IntelLabs/control-flag",
        stars: "1.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "dolma",
        description: "优质开源AI项目",
        url: "https://github.com/allenai/dolma",
        stars: "1.2k",
        language: "Python",
        visible_by_default: false
    },
];

// 为子类别 2.5 添加 42 个项目
projectsData["2.5"] = [
    {
        name: "evals",
        description: "Evals is a framework for evaluating 大型语言模型s and 大型语言模型 systems, and an open-source registry of benchmarks. (框架)",
        url: "https://github.com/openai/evals",
        stars: "15.8k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "ragas",
        description: "Supercharge Your 大型语言模型 Application 评估工具s 🚀",
        url: "https://github.com/explodinggradients/ragas",
        stars: "8.7k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "lm-evaluation-harness",
        description: "A framework for few-shot 评估工具 of language models. (框架)",
        url: "https://github.com/EleutherAI/lm-evaluation-harness",
        stars: "8.5k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "deepeval",
        description: "The 大型语言模型 评估工具 Framework (框架)",
        url: "https://github.com/confident-ai/deepeval",
        stars: "5.8k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "phoenix",
        description: "优质开源AI项目",
        url: "https://github.com/Arize-ai/phoenix",
        stars: "5.3k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "ann-benchmarks",
        description: "优质开源AI项目",
        url: "https://github.com/erikbern/ann-benchmarks",
        stars: "5.2k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "adversarial-robustness-toolbox",
        description: "Adversarial Robustness Toolbox (ART) - Python Library for 机器学习 Security - Evasion, Poisoning, Extraction, 推理优化 - Red and Blue Teams (库)",
        url: "https://github.com/Trusted-AI/adversarial-robustness-toolbox",
        stars: "5.2k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "opencompass",
        description: "OpenCompass is an 大型语言模型 评估工具 platform, supporting a wide range of models (Llama3, Mistral, InternLM2,GPT-4,LLaMa2, Qwen,GLM, Claude, etc) over 100+ d...",
        url: "https://github.com/open-compass/opencompass",
        stars: "5.1k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "NeMo-Guardrails",
        description: "NeMo Guardrails is an open-source toolkit for easily adding programmable guardrails to 大型语言模型-based conversational systems. (工具)",
        url: "https://github.com/NVIDIA/NeMo-Guardrails",
        stars: "4.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "giskard",
        description: "🐢 Open-Source 评估工具 & Testing for AI & 大型语言模型 systems",
        url: "https://github.com/Giskard-AI/giskard",
        stars: "4.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "ARC",
        description: "优质开源AI项目",
        url: "https://github.com/fchollet/ARC",
        stars: "4.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "garak",
        description: "the 大型语言模型 vulnerability scanner",
        url: "https://github.com/leondz/garak",
        stars: "4.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "transformer-debugger",
        description: "开源AI项目",
        url: "https://github.com/openai/transformer-debugger",
        stars: "4.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "chinese-llm-benchmark",
        description: "目前已囊括203个大模型，覆盖chatgpt、gpt-4o、o3-mini、谷歌gemini、Claude3.5、智谱GLM-Zero、文心一言、qwen-max、百川、讯飞星火、商汤senseChat、minimax等商用模型， 以及DeepSeek-R1、qwq-32b、deepseek-v3、...",
        url: "https://github.com/jeinlee1991/chinese-llm-benchmark",
        stars: "3.9k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "llm-attacks",
        description: "优质开源AI项目",
        url: "https://github.com/llm-attacks/llm-attacks",
        stars: "3.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "BIG-bench",
        description: "优质开源AI项目",
        url: "https://github.com/google/BIG-bench",
        stars: "3.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "PurpleLlama",
        description: "Set of tools to assess and improve 大型语言模型 security. (工具)",
        url: "https://github.com/meta-llama/PurpleLlama",
        stars: "3.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "SWE-bench",
        description: "优质开源AI项目",
        url: "https://github.com/princeton-nlp/SWE-bench",
        stars: "2.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "human-eval",
        description: "Code for the paper \"Evaluating 大型语言模型s Trained on Code\"",
        url: "https://github.com/openai/human-eval",
        stars: "2.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "simple-evals",
        description: "开源AI项目",
        url: "https://github.com/openai/simple-evals",
        stars: "2.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "AgentBench",
        description: "A Comprehensive Benchmark to Evaluate 大型语言模型s as Agents (ICLR'24)",
        url: "https://github.com/THUDM/AgentBench",
        stars: "2.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "trulens",
        description: "评估工具 and Tracking for 大型语言模型 Experiments",
        url: "https://github.com/truera/trulens",
        stars: "2.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "mteb",
        description: "优质开源AI项目",
        url: "https://github.com/embeddings-benchmark/mteb",
        stars: "2.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "PyRIT",
        description: "优质开源AI项目",
        url: "https://github.com/Azure/PyRIT",
        stars: "2.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "decaNLP",
        description: "优质开源AI项目",
        url: "https://github.com/salesforce/decaNLP",
        stars: "2.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "torchmetrics",
        description: "优质开源AI项目",
        url: "https://github.com/Lightning-AI/torchmetrics",
        stars: "2.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "evaluate",
        description: "🤗 Evaluate: A library for easily evaluating 机器学习 models and datasets. (库)",
        url: "https://github.com/huggingface/evaluate",
        stars: "2.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "helm",
        description: "Holistic 评估工具 of Language Models (HELM), a framework to increase the transparency of language models (https://arxiv.org/abs/2211.09110). This framewor...",
        url: "https://github.com/stanford-crfm/helm",
        stars: "2.1k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "hallucination-leaderboard",
        description: "Leaderboard Comparing 大型语言模型 Performance at Producing Hallucinations when Summarizing Short Documents",
        url: "https://github.com/vectara/hallucination-leaderboard",
        stars: "2.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "TransformerLens",
        description: "优质开源AI项目",
        url: "https://github.com/TransformerLensOrg/TransformerLens",
        stars: "2.0k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "EvalAI",
        description: "优质开源AI项目",
        url: "https://github.com/Cloud-CV/EvalAI",
        stars: "1.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "beir",
        description: "优质开源AI项目",
        url: "https://github.com/beir-cellar/beir",
        stars: "1.8k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "alpaca_eval",
        description: "优质开源AI项目",
        url: "https://github.com/tatsu-lab/alpaca_eval",
        stars: "1.7k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "CodeXGLUE",
        description: "优质开源AI项目",
        url: "https://github.com/microsoft/CodeXGLUE",
        stars: "1.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "WeightWatcher",
        description: "优质开源AI项目",
        url: "https://github.com/CalculatedContent/WeightWatcher",
        stars: "1.6k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "bsuite",
        description: "优质开源AI项目",
        url: "https://github.com/google-deepmind/bsuite",
        stars: "1.5k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "evalplus",
        description: "Rigourous 评估工具 of 大型语言模型-synthesized code - NeurIPS 2023 & COLM 2024",
        url: "https://github.com/evalplus/evalplus",
        stars: "1.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "LAMA",
        description: "优质开源AI项目",
        url: "https://github.com/facebookresearch/LAMA",
        stars: "1.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "test",
        description: "优质开源AI项目",
        url: "https://github.com/hendrycks/test",
        stars: "1.4k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "inference",
        description: "优质开源AI项目",
        url: "https://github.com/mlcommons/inference",
        stars: "1.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "grade-school-math",
        description: "开源AI项目",
        url: "https://github.com/openai/grade-school-math",
        stars: "1.2k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "auto-evaluator",
        description: "评估工具 tool for 大型语言模型 QA chains (工具)",
        url: "https://github.com/rlancemartin/auto-evaluator",
        stars: "1.1k",
        language: "Python",
        visible_by_default: false
    },
];

// 为子类别 3.7 添加 8 个项目
projectsData["3.7"] = [
    {
        name: "novel",
        description: "优质开源AI项目",
        url: "https://github.com/steven-tey/novel",
        stars: "14.3k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "reor",
        description: "优质开源AI项目",
        url: "https://github.com/reorproject/reor",
        stars: "7.8k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "pycorrector",
        description: "pycorrector is a toolkit for text error correction. 文本纠错，实现了Kenlm，T5，MacBERT，ChatGLM3，Qwen2.5等模型应用在纠错场景，开箱即用。 (工具)",
        url: "https://github.com/shibing624/pycorrector",
        stars: "5.9k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "WriteGPT",
        description: "由图灵的猫开发，基于开源GPT2.0的初代创作型人工智能 | 可扩展、可进化",
        url: "https://github.com/EssayKillerBrain/WriteGPT",
        stars: "5.3k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "AI-Writer",
        description: "AI 写小说，生成玄幻和言情网文等等。中文预训练生成模型。采用我的 RWKV 模型，类似 GPT-2 。AI写作。RWKV for Chinese novel generation.",
        url: "https://github.com/BlinkDL/AI-Writer",
        stars: "3.1k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "gpt-author",
        description: "开源AI项目",
        url: "https://github.com/mshumer/gpt-author",
        stars: "2.5k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "twitterbio",
        description: "优质开源AI项目",
        url: "https://github.com/Nutlope/twitterbio",
        stars: "1.7k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "obsidian-textgenerator-plugin",
        description: "优质开源AI项目",
        url: "https://github.com/nhaouari/obsidian-textgenerator-plugin",
        stars: "1.7k",
        language: "Python",
        visible_by_default: true
    },
];

// 为子类别 4.4 添加 10 个项目
projectsData["4.4"] = [
    {
        name: "langfuse",
        description: "🪢 Open source 大型语言模型 engineering platform: 大型语言模型 Observability, metrics, evals, prompt management, playground, datasets. Integrates with OpenTelemetr...",
        url: "https://github.com/langfuse/langfuse",
        stars: "10.1k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "evidently",
        description: "Evidently is ​​an open-source ML and 大型语言模型 observability framework. Evaluate, test, and monitor any AI-powered system or data pipeline. From tabular ...",
        url: "https://github.com/evidentlyai/evidently",
        stars: "6.0k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "openllmetry",
        description: "Open-source observability for your 大型语言模型 application, based on OpenTelemetry",
        url: "https://github.com/traceloop/openllmetry",
        stars: "5.6k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "helicone",
        description: "🧊 Open source 大型语言模型 observability platform. One line of code to monitor, evaluate, and experiment. YC W23 🍓",
        url: "https://github.com/Helicone/helicone",
        stars: "3.6k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "whylogs",
        description: "An open-source data logging library for 机器学习 models and data pipelines. 📚 Provides visibility into data quality & model performance over time. 🛡️ Supp...",
        url: "https://github.com/whylabs/whylogs",
        stars: "2.7k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "uptrain",
        description: "优质开源AI项目",
        url: "https://github.com/uptrain-ai/uptrain",
        stars: "2.3k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "labml",
        description: "优质开源AI项目",
        url: "https://github.com/labmlai/labml",
        stars: "2.1k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "lmnr",
        description: "优质开源AI项目",
        url: "https://github.com/lmnr-ai/lmnr",
        stars: "1.8k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "lunary",
        description: "The production toolkit for 大型语言模型s. Observability, prompt management and 评估工具s. (工具)",
        url: "https://github.com/lunary-ai/lunary",
        stars: "1.3k",
        language: "Python",
        visible_by_default: false
    },
    {
        name: "llmonitor",
        description: "The production toolkit for 大型语言模型s. Observability, prompt management and 评估工具s. (工具)",
        url: "https://github.com/llmonitor/llmonitor",
        stars: "1.3k",
        language: "Python",
        visible_by_default: false
    },
];

// 为子类别 4.6 添加 6 个项目
projectsData["4.6"] = [
    {
        name: "ibis",
        description: "优质开源AI项目",
        url: "https://github.com/ibis-project/ibis",
        stars: "5.7k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "superduperdb",
        description: "优质开源AI项目",
        url: "https://github.com/SuperDuperDB/superduperdb",
        stars: "5.0k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "llama-hub",
        description: "A library of data loaders for 大型语言模型s made by the community -- to be used with LlamaIndex and/or LangChain (库)",
        url: "https://github.com/run-llama/llama-hub",
        stars: "3.5k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "webdataset",
        description: "开源AI项目：O system for large (and small) 深度学习 problems, with strong support for PyTorch.",
        url: "https://github.com/webdataset/webdataset",
        stars: "2.5k",
        language: "JavaScript",
        visible_by_default: true
    },
    {
        name: "aistore",
        description: "AIStore: scalable sto检索增强生成e for AI applications",
        url: "https://github.com/NVIDIA/aistore",
        stars: "1.5k",
        language: "Python",
        visible_by_default: true
    },
    {
        name: "streaming",
        description: "优质开源AI项目",
        url: "https://github.com/mosaicml/streaming",
        stars: "1.3k",
        language: "Python",
        visible_by_default: true
    },
];

