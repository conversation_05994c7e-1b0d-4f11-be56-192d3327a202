GitHub链接,一级分类,二级分类,Stars数量,项目名称,描述
https://github.com/tensorflow/tensorflow,模型开发与研究,训练与微调,189230,tensorflow/tensorflow,An Open Source Machine Learning Framework for Everyone
https://github.com/Significant-Gravitas/AutoGPT,AI构建与集成,AI代理系统,174196,Significant-Gravitas/AutoGPT,"AutoGPT is the vision of accessible AI for everyone, to use and to build on. Our mission is to provide the tools, so that you can focus on what matters."
https://github.com/AUTOMATIC1111/stable-diffusion-webui,AI实用工具,图像创作工具,150721,AUTOMATIC1111/stable-diffusion-webui,Stable Diffusion web UI
https://github.com/huggingface/transformers,模型开发与研究,训练与微调,142477,huggingface/transformers,"🤗 Transformers: State-of-the-art Machine Learning for Pytorch, TensorFlow, and JAX."
https://github.com/jmorganca/ollama,AI构建与集成,AI接口工具,136130,jmorganca/ollama,"Get up and running with Llama 3.3, DeepSeek-R1, Phi-4, Gemma 3, and other large language models."
https://github.com/f/awesome-chatgpt-prompts,AI学习与资源,资源集合,122606,f/awesome-chatgpt-prompts,This repo includes ChatGPT prompt curation to use ChatGPT and other LLM tools better.
https://github.com/langchain-ai/langchain,AI构建与集成,AI开发框架,105031,langchain-ai/langchain,🦜🔗 Build context-aware reasoning applications
https://github.com/deepseek-ai/DeepSeek-V3,模型开发与研究,模型库/仓库,95102,deepseek-ai/DeepSeek-V3,
https://github.com/langgenius/dify,AI构建与集成,AI开发框架,89444,langgenius/dify,"Dify is an open-source LLM app development platform. Dify's intuitive interface combines AI workflow, RAG pipeline, agent capabilities, model management, observability features and more, letting you quickly go from prototype to production."
https://github.com/pytorch/pytorch,模型开发与研究,训练与微调,88676,pytorch/pytorch,Tensors and Dynamic neural networks in Python with strong GPU acceleration
https://github.com/deepseek-ai/DeepSeek-R1,模型开发与研究,模型库/仓库,88084,deepseek-ai/DeepSeek-R1,
https://github.com/ChatGPTNextWeb/ChatGPT-Next-Web,AI构建与集成,AI接口工具,82497,ChatGPTNextWeb/ChatGPT-Next-Web,✨ Light and Fast AI Assistant. Support: Web | iOS | MacOS | Android |  Linux | Windows
https://github.com/openai/whisper,模型开发与研究,模型库/仓库,79460,openai/whisper,Robust Speech Recognition via Large-Scale Weak Supervision
https://github.com/microsoft/generative-ai-for-beginners,AI学习与资源,教程与课程,77744,microsoft/generative-ai-for-beginners,"21 Lessons, Get Started Building with Generative AI  🔗 https://microsoft.github.io/generative-ai-for-beginners/"
https://github.com/ggerganov/llama.cpp,模型开发与研究,推理优化,77688,ggerganov/llama.cpp,LLM inference in C/C++
https://github.com/comfyanonymous/ComfyUI,AI构建与集成,AI开发框架,73453,comfyanonymous/ComfyUI,"The most powerful and modular diffusion model GUI, api and backend with a graph/nodes interface."
https://github.com/nomic-ai/gpt4all,AI构建与集成,AI开发框架,73015,nomic-ai/gpt4all,GPT4All: Run Local LLMs on Any Device. Open-source and available for commercial use.
https://github.com/fighting41love/funNLP,AI学习与资源,资源集合,72199,fighting41love/funNLP,"中英文敏感词、语言检测、中外手机/电话归属地/运营商查询、名字推断性别、手机号抽取、身份证抽取、邮箱抽取、中日文人名库、中文缩写库、拆字词典、词汇情感值、停用词、反动词表、暴恐词表、繁简体转换、英文模拟中文发音、汪峰歌词生成器、职业名称词库、同义词库、反义词库、否定词库、汽车品牌词库、汽车零件词库、连续英文切割、各种中文词向量、公司名字大全、古诗词库、IT词库、财经词库、成语词库、地名词库、历史名人词库、诗词词库、医学词库、饮食词库、法律词库、汽车词库、动物词库、中文聊天语料、中文谣言数据、百度中文问答数据集、句子相似度匹配算法集合、bert资源、文本生成&摘要相关工具、cocoNLP信息抽取工具、国内电话号码正则匹配、清华大学XLORE:中英文跨语言百科知识图谱、清华大学人工智能技术系列报告、自然语言生成、NLU太难了系列、自动对联数据及机器人、用户名黑名单列表、罪名法务名词及分类模型、微信公众号语料、cs224n深度学习自然语言处理课程、中文手写汉字识别、中文自然语言处理 语料/数据集、变量命名神器、分词语料库+代码、任务型对话英文数据集、ASR 语音数据集 + 基于深度学习的中文语音识别系统、笑声检测器、Microsoft多语言数字/单位/如日期时间识别包、中华新华字典数据库及api(包括常用歇后语、成语、词语和汉字)、文档图谱自动生成、SpaCy 中文模型、Common Voice语音识别数据集新版、神经网络关系抽取、基于bert的命名实体识别、关键词(Keyphrase)抽取包pke、基于医疗领域知识图谱的问答系统、基于依存句法与语义角色标注的事件三元组抽取、依存句法分析4万句高质量标注数据、cnocr：用来做中文OCR的Python3包、中文人物关系知识图谱项目、中文nlp竞赛项目及代码汇总、中文字符数据、speech-aligner: 从“人声语音”及其“语言文本”产生音素级别时间对齐标注的工具、AmpliGraph: 知识图谱表示学习(Python)库：知识图谱概念链接预测、Scattertext 文本可视化(python)、语言/知识表示工具：BERT & ERNIE、中文对比英文自然语言处理NLP的区别综述、Synonyms中文近义词工具包、HarvestText领域自适应文本挖掘工具（新词发现-情感分析-实体链接等）、word2word：(Python)方便易用的多语言词-词对集：62种语言/3,564个多语言对、语音识别语料生成工具：从具有音频/字幕的在线视频创建自动语音识别(ASR)语料库、构建医疗实体识别的模型（包含词典和语料标注）、单文档非监督的关键词抽取、Kashgari中使用gpt-2语言模型、开源的金融投资数据提取工具、文本自动摘要库TextTeaser: 仅支持英文、人民日报语料处理工具集、一些关于自然语言的基本模型、基于14W歌曲知识库的问答尝试--功能包括歌词接龙and已知歌词找歌曲以及歌曲歌手歌词三角关系的问答、基于Siamese bilstm模型的相似句子判定模型并提供训练数据集和测试数据集、用Transformer编解码模型实现的根据Hacker News文章标题自动生成评论、用BERT进行序列标记和文本分类的模板代码、LitBank：NLP数据集——支持自然语言处理和计算人文学科任务的100部带标记英文小说语料、百度开源的基准信息抽取系统、虚假新闻数据集、Facebook: LAMA语言模型分析，提供Transformer-XL/BERT/ELMo/GPT预训练语言模型的统一访问接口、CommonsenseQA：面向常识的英文QA挑战、中文知识图谱资料、数据及工具、各大公司内部里大牛分享的技术文档 PDF 或者 PPT、自然语言生成SQL语句（英文）、中文NLP数据增强（EDA）工具、英文NLP数据增强工具 、基于医药知识图谱的智能问答系统、京东商品知识图谱、基于mongodb存储的军事领域知识图谱问答项目、基于远监督的中文关系抽取、语音情感分析、中文ULMFiT-情感分析-文本分类-语料及模型、一个拍照做题程序、世界各国大规模人名库、一个利用有趣中文语料库 qingyun 训练出来的中文聊天机器人、中文聊天机器人seqGAN、省市区镇行政区划数据带拼音标注、教育行业新闻语料库包含自动文摘功能、开放了对话机器人-知识图谱-语义理解-自然语言处理工具及数据、中文知识图谱：基于百度百科中文页面-抽取三元组信息-构建中文知识图谱、masr: 中文语音识别-提供预训练模型-高识别率、Python音频数据增广库、中文全词覆盖BERT及两份阅读理解数据、ConvLab：开源多域端到端对话系统平台、中文自然语言处理数据集、基于最新版本rasa搭建的对话系统、基于TensorFlow和BERT的管道式实体及关系抽取、一个小型的证券知识图谱/知识库、复盘所有NLP比赛的TOP方案、OpenCLaP：多领域开源中文预训练语言模型仓库、UER：基于不同语料+编码器+目标任务的中文预训练模型仓库、中文自然语言处理向量合集、基于金融-司法领域(兼有闲聊性质)的聊天机器人、g2pC：基于上下文的汉语读音自动标记模块、Zincbase 知识图谱构建工具包、诗歌质量评价/细粒度情感诗歌语料库、快速转化「中文数字」和「阿拉伯数字」、百度知道问答语料库、基于知识图谱的问答系统、jieba_fast 加速版的jieba、正则表达式教程、中文阅读理解数据集、基于BERT等最新语言模型的抽取式摘要提取、Python利用深度学习进行文本摘要的综合指南、知识图谱深度学习相关资料整理、维基大规模平行文本语料、StanfordNLP 0.2.0：纯Python版自然语言处理包、NeuralNLP-NeuralClassifier：腾讯开源深度学习文本分类工具、端到端的封闭域对话系统、中文命名实体识别：NeuroNER vs. BertNER、新闻事件线索抽取、2019年百度的三元组抽取比赛：“科学空间队”源码、基于依存句法的开放域文本知识三元组抽取和知识库构建、中文的GPT2训练代码、ML-NLP - 机器学习(Machine Learning)NLP面试中常考到的知识点和代码实现、nlp4han:中文自然语言处理工具集(断句/分词/词性标注/组块/句法分析/语义分析/NER/N元语法/HMM/代词消解/情感分析/拼写检查、XLM：Facebook的跨语言预训练语言模型、用基于BERT的微调和特征提取方法来进行知识图谱百度百科人物词条属性抽取、中文自然语言处理相关的开放任务-数据集-当前最佳结果、CoupletAI - 基于CNN+Bi-LSTM+Attention 的自动对对联系统、抽象知识图谱、MiningZhiDaoQACorpus - 580万百度知道问答数据挖掘项目、brat rapid annotation tool: 序列标注工具、大规模中文知识图谱数据：1.4亿实体、数据增强在机器翻译及其他nlp任务中的应用及效果、allennlp阅读理解:支持多种数据和模型、PDF表格数据提取工具 、 Graphbrain：AI开源软件库和科研工具，目的是促进自动意义提取和文本理解以及知识的探索和推断、简历自动筛选系统、基于命名实体识别的简历自动摘要、中文语言理解测评基准，包括代表性的数据集&基准模型&语料库&排行榜、树洞 OCR 文字识别 、从包含表格的扫描图片中识别表格和文字、语声迁移、Python口语自然语言处理工具集(英文)、 similarity：相似度计算工具包，java编写、海量中文预训练ALBERT模型 、Transformers 2.0 、基于大规模音频数据集Audioset的音频增强 、Poplar：网页版自然语言标注工具、图片文字去除，可用于漫画翻译 、186种语言的数字叫法库、Amazon发布基于知识的人-人开放领域对话数据集 、中文文本纠错模块代码、繁简体转换 、 Python实现的多种文本可读性评价指标、类似于人名/地名/组织机构名的命名体识别数据集 、东南大学《知识图谱》研究生课程(资料)、. 英文拼写检查库 、 wwsearch是企业微信后台自研的全文检索引擎、CHAMELEON：深度学习新闻推荐系统元架构 、 8篇论文梳理BERT相关模型进展与反思、DocSearch：免费文档搜索引擎、 LIDA：轻量交互式对话标注工具 、aili - the fastest in-memory index in the East 东半球最快并发索引 、知识图谱车音工作项目、自然语言生成资源大全 、中日韩分词库mecab的Python接口库、中文文本摘要/关键词提取、汉字字符特征提取器 (featurizer)，提取汉字的特征（发音特征、字形特征）用做深度学习的特征、中文生成任务基准测评 、中文缩写数据集、中文任务基准测评 - 代表性的数据集-基准(预训练)模型-语料库-baseline-工具包-排行榜、PySS3：面向可解释AI的SS3文本分类器机器可视化工具 、中文NLP数据集列表、COPE - 格律诗编辑程序、doccano：基于网页的开源协同多语言文本标注工具 、PreNLP：自然语言预处理库、简单的简历解析器，用来从简历中提取关键信息、用于中文闲聊的GPT2模型：GPT2-chitchat、基于检索聊天机器人多轮响应选择相关资源列表(Leaderboards、Datasets、Papers)、(Colab)抽象文本摘要实现集锦(教程 、词语拼音数据、高效模糊搜索工具、NLP数据增广资源集、微软对话机器人框架 、 GitHub Typo Corpus：大规模GitHub多语言拼写错误/语法错误数据集、TextCluster：短文本聚类预处理模块 Short text cluster、面向语音识别的中文文本规范化、BLINK：最先进的实体链接库、BertPunc：基于BERT的最先进标点修复模型、Tokenizer：快速、可定制的文本词条化库、中文语言理解测评基准，包括代表性的数据集、基准(预训练)模型、语料库、排行榜、spaCy 医学文本挖掘与信息提取 、 NLP任务示例项目代码集、 python拼写检查库、chatbot-list - 行业内关于智能客服、聊天机器人的应用和架构、算法分享和介绍、语音质量评价指标(MOSNet, BSSEval, STOI, PESQ, SRMR)、 用138GB语料训练的法文RoBERTa预训练语言模型 、BERT-NER-Pytorch：三种不同模式的BERT中文NER实验、无道词典 - 有道词典的命令行版本，支持英汉互查和在线查询、2019年NLP亮点回顾、 Chinese medical dialogue data 中文医疗对话数据集 、最好的汉字数字(中文数字)-阿拉伯数字转换工具、 基于百科知识库的中文词语多词义/义项获取与特定句子词语语义消歧、awesome-nlp-sentiment-analysis - 情感分析、情绪原因识别、评价对象和评价词抽取、LineFlow：面向所有深度学习框架的NLP数据高效加载器、中文医学NLP公开资源整理 、MedQuAD：(英文)医学问答数据集、将自然语言数字串解析转换为整数和浮点数、Transfer Learning in Natural Language Processing (NLP) 、面向语音识别的中文/英文发音辞典、Tokenizers：注重性能与多功能性的最先进分词器、CLUENER 细粒度命名实体识别 Fine Grained Named Entity Recognition、 基于BERT的中文命名实体识别、中文谣言数据库、NLP数据集/基准任务大列表、nlp相关的一些论文及代码, 包括主题模型、词向量(Word Embedding)、命名实体识别(NER)、文本分类(Text Classificatin)、文本生成(Text Generation)、文本相似性(Text Similarity)计算等，涉及到各种与nlp相关的算法，基于keras和tensorflow 、Python文本挖掘/NLP实战示例、 Blackstone：面向非结构化法律文本的spaCy pipeline和NLP模型通过同义词替换实现文本“变脸” 、中文 预训练 ELECTREA 模型: 基于对抗学习 pretrain Chinese Model 、albert-chinese-ner - 用预训练语言模型ALBERT做中文NER 、基于GPT2的特定主题文本生成/文本增广、开源预训练语言模型合集、多语言句向量包、编码、标记和实现：一种可控高效的文本生成方法、 英文脏话大列表 、attnvis：GPT2、BERT等transformer语言模型注意力交互可视化、CoVoST：Facebook发布的多语种语音-文本翻译语料库，包括11种语言(法语、德语、荷兰语、俄语、西班牙语、意大利语、土耳其语、波斯语、瑞典语、蒙古语和中文)的语音、文字转录及英文译文、Jiagu自然语言处理工具 - 以BiLSTM等模型为基础，提供知识图谱关系抽取 中文分词 词性标注 命名实体识别 情感分析 新词发现 关键词 文本摘要 文本聚类等功能、用unet实现对文档表格的自动检测，表格重建、NLP事件提取文献资源列表 、 金融领域自然语言处理研究资源大列表、CLUEDatasetSearch - 中英文NLP数据集：搜索所有中文NLP数据集，附常用英文NLP数据集 、medical_NER - 中文医学知识图谱命名实体识别 、(哈佛)讲因果推理的免费书、知识图谱相关学习资料/数据集/工具资源大列表、Forte：灵活强大的自然语言处理pipeline工具集 、Python字符串相似性算法库、PyLaia：面向手写文档分析的深度学习工具包、TextFooler：针对文本分类/推理的对抗文本生成模块、Haystack：灵活、强大的可扩展问答(QA)框架、中文关键短语抽取工具"
https://github.com/CompVis/stable-diffusion,模型开发与研究,模型库/仓库,70254,CompVis/stable-diffusion,A latent text-to-image diffusion model
https://github.com/abi/screenshot-to-code,AI实用工具,编程助手,69432,abi/screenshot-to-code,Drop in a screenshot and convert it to clean code (HTML/Tailwind/React/Vue)
https://github.com/binary-husky/gpt_academic,AI实用工具,信息聚合工具,68095,binary-husky/gpt_academic,"为GPT/GLM等LLM大语言模型提供实用化交互接口，特别优化论文阅读/润色/写作体验，模块化设计，支持自定义快捷按钮&函数插件，支持Python和C++等项目剖析&自译解功能，PDF/LaTex论文翻译&总结功能，支持并行问询多种LLM模型，支持chatglm3等本地模型。接入通义千问, deepseekcoder, 讯飞星火, 文心一言, llama2, rwkv, claude2, moss等。"
https://github.com/xtekky/gpt4free,AI构建与集成,AI接口工具,63963,xtekky/gpt4free,"The official gpt4free repository | various collection of powerful language models | o3 and deepseek r1, gpt-4.5"
https://github.com/openai/openai-cookbook,AI学习与资源,教程与课程,62659,openai/openai-cookbook,Examples and guides for using the OpenAI API
https://github.com/KillianLucas/open-interpreter,AI实用工具,工作流自动化,59016,KillianLucas/open-interpreter,A natural language interface for computers
https://github.com/lobehub/lobe-chat,AI构建与集成,AI开发框架,58580,lobehub/lobe-chat,"🤯 Lobe Chat - an open-source, modern-design AI chat framework. Supports Multi AI Providers( OpenAI / Claude 3 / Gemini / Ollama / DeepSeek / Qwen), Knowledge Base (file upload / knowledge management / RAG ), Multi-Modals (Plugins/Artifacts) and Thinking. One-click FREE deployment of your private ChatGPT/ Claude / DeepSeek application."
https://github.com/facebookresearch/llama,模型开发与研究,模型库/仓库,58006,facebookresearch/llama,Inference code for Llama models
https://github.com/imartinez/privateGPT,AI实用工具,信息聚合工具,55556,imartinez/privateGPT,"Interact with your documents using the power of GPT, 100% privately, no data leaks"
https://github.com/dair-ai/Prompt-Engineering-Guide,AI学习与资源,教程与课程,54696,dair-ai/Prompt-Engineering-Guide,"🐙 Guides, papers, lecture, notebooks and resources for prompt engineering"
https://github.com/PlexPt/awesome-chatgpt-prompts-zh,AI学习与资源,资源集合,54672,PlexPt/awesome-chatgpt-prompts-zh,ChatGPT 中文调教指南。各种场景使用指南。学习怎么让它听你的话。
https://github.com/geekan/MetaGPT,AI构建与集成,AI代理系统,54215,geekan/MetaGPT,"🌟 The Multi-Agent Framework: First AI Software Company, Towards Natural Language Programming"
https://github.com/logspace-ai/langflow,AI构建与集成,AI开发框架,54159,logspace-ai/langflow,Langflow is a powerful tool for building and deploying AI-powered agents and workflows.
https://github.com/gpt-engineer-org/gpt-engineer,AI实用工具,编程助手,53781,gpt-engineer-org/gpt-engineer,CLI platform to experiment with codegen. Precursor to: https://lovable.dev
https://github.com/lencx/ChatGPT,AI实用工具,聊天机器人,53671,lencx/ChatGPT,"🔮 ChatGPT Desktop Application (Mac, Windows and Linux)"
https://github.com/browser-use/browser-use,AI构建与集成,AI代理系统,53306,browser-use/browser-use,Make websites accessible for AI agents
https://github.com/All-Hands-AI/OpenHands,AI实用工具,编程助手,52433,All-Hands-AI/OpenHands,"🙌 OpenHands: Code Less, Make More"
https://github.com/xai-org/grok-1,模型开发与研究,模型库/仓库,50253,xai-org/grok-1,Grok open release
https://github.com/hacksider/Deep-Live-Cam,AI实用工具,视频制作工具,49604,hacksider/Deep-Live-Cam,real time face swap and one-click video deepfake with only a single image
https://github.com/mlabonne/llm-course,AI学习与资源,教程与课程,48945,mlabonne/llm-course,Course to get into Large Language Models (LLMs) with roadmaps and Colab notebooks.
https://github.com/PaddlePaddle/PaddleOCR,模型开发与研究,模型库/仓库,48054,PaddlePaddle/PaddleOCR,"Awesome multilingual OCR toolkits based on PaddlePaddle (practical ultra lightweight OCR system, support 80+ languages recognition, provide data annotation and synthesis tools, support training and deployment among server, mobile, embedded and IoT devices)"
https://github.com/infiniflow/ragflow,AI构建与集成,AI开发框架,47978,infiniflow/ragflow,RAGFlow is an open-source RAG (Retrieval-Augmented Generation) engine based on deep document understanding.
https://github.com/hiyouga/LLaMA-Factory,模型开发与研究,训练与微调,46103,hiyouga/LLaMA-Factory,Unified Efficient Fine-Tuning of 100+ LLMs & VLMs (ACL 2024)
https://github.com/lllyasviel/Fooocus,AI实用工具,图像创作工具,44168,lllyasviel/Fooocus,Focus on prompting and generating
https://github.com/RVC-Boss/GPT-SoVITS,AI构建与集成,AI接口工具,43652,RVC-Boss/GPT-SoVITS,1 min voice data can also be used to train a good TTS model! (few shot voice cloning)
https://github.com/rasbt/LLMs-from-scratch,AI学习与资源,教程与课程,43629,rasbt/LLMs-from-scratch,"Implement a ChatGPT-like LLM in PyTorch from scratch, step by step"
https://github.com/vllm-project/vllm,AI部署与基础设施,模型部署工具,43553,vllm-project/vllm,A high-throughput and memory-efficient inference and serving engine for LLMs
https://github.com/oobabooga/text-generation-webui,AI构建与集成,AI接口工具,43073,oobabooga/text-generation-webui,A Gradio web UI for Large Language Models with support for multiple inference backends.
https://github.com/microsoft/autogen,AI构建与集成,AI代理系统,42699,microsoft/autogen,A programming framework for agentic AI 🤖 PyPi: autogen-agentchat Discord: https://aka.ms/autogen-discord Office Hour: https://aka.ms/autogen-officehour
https://github.com/Mintplex-Labs/anything-llm,AI实用工具,信息聚合工具,42285,Mintplex-Labs/anything-llm,"The all-in-one Desktop & Docker AI application with built-in RAG, AI agents, No-code agent builder, MCP compatibility,  and more."
https://github.com/THUDM/ChatGLM-6B,模型开发与研究,模型库/仓库,41026,THUDM/ChatGLM-6B,ChatGLM-6B: An Open Bilingual Dialogue Language Model | 开源双语对话语言模型
https://github.com/hpcaitech/ColossalAI,模型开发与研究,训练与微调,40730,hpcaitech/ColossalAI,"Making large AI models cheaper, faster and more accessible"
https://github.com/run-llama/llama_index,AI构建与集成,AI开发框架,40709,run-llama/llama_index,LlamaIndex is the leading framework for building LLM-powered agents over your data.
https://github.com/Stability-AI/stablediffusion,模型开发与研究,模型库/仓库,40698,Stability-AI/stablediffusion,High-Resolution Image Synthesis with Latent Diffusion Models
https://github.com/karpathy/nanoGPT,模型开发与研究,模型库/仓库,40490,karpathy/nanoGPT,"The simplest, fastest repository for training/finetuning medium-sized GPTs."
https://github.com/OpenBB-finance/OpenBB,AI实用工具,信息聚合工具,40091,OpenBB-finance/OpenBB,"Investment Research for Everyone, Everywhere."
https://github.com/coqui-ai/TTS,模型开发与研究,训练与微调,39102,coqui-ai/TTS,"🐸💬 - a deep learning toolkit for Text-to-Speech, battle-tested in research and production"
https://github.com/ggerganov/whisper.cpp,模型开发与研究,推理优化,38984,ggerganov/whisper.cpp,Port of OpenAI's Whisper model in C/C++
https://github.com/lm-sys/FastChat,模型开发与研究,训练与微调,38291,lm-sys/FastChat,"An open platform for training, serving, and evaluating large language models. Release repo for Vicuna and Chatbot Arena."
https://github.com/microsoft/DeepSpeed,模型开发与研究,训练与微调,37759,microsoft/DeepSpeed,"DeepSpeed is a deep learning optimization library that makes distributed training and inference easy, efficient, and effective."
https://github.com/StanGirard/quivr,AI实用工具,工作流自动化,37660,StanGirard/quivr,"Opiniated RAG for integrating GenAI in your apps 🧠   Focus on your product rather than the RAG. Easy integration in existing products with customisation!  Any LLM: GPT4, Groq, Llama. Any Vectorstore: PGVector, Faiss. Any Files. Anyway you want."
https://github.com/unclecode/crawl4ai,未分类,未分类,37416,unclecode/crawl4ai,"🚀🤖 Crawl4AI: Open-source LLM Friendly Web Crawler & Scraper. Don't be shy, join here: https://discord.gg/jP8KfhDhyN"
https://github.com/suno-ai/bark,模型开发与研究,模型库/仓库,37379,suno-ai/bark,🔊 Text-Prompted Generative Audio Model
https://github.com/gradio-app/gradio,AI构建与集成,AI接口工具,37289,gradio-app/gradio,"Build and share delightful machine learning apps, all in Python. 🌟 Star to support our work!"
https://github.com/LAION-AI/Open-Assistant,AI实用工具,聊天机器人,37288,LAION-AI/Open-Assistant,"OpenAssistant is a chat-based assistant that understands tasks, can interact with third-party systems, and retrieve information dynamically to do so."
https://github.com/FlowiseAI/Flowise,AI构建与集成,AI开发框架,36946,FlowiseAI/Flowise,Drag & drop UI to build your customized LLM flow
https://github.com/photoprism/photoprism,AI实用工具,数据组织工具,36893,photoprism/photoprism,AI-Powered Photos App for the Decentralized Web 🌈💎✨
https://github.com/unslothai/unsloth,模型开发与研究,推理优化,36582,unslothai/unsloth,"Finetune Llama 4, DeepSeek-R1, Gemma 3 & Reasoning LLMs 2x faster with 70% less memory! 🦥"
https://github.com/TencentARC/GFPGAN,模型开发与研究,模型库/仓库,36533,TencentARC/GFPGAN,GFPGAN aims at developing Practical Algorithms for Real-world Face Restoration.
https://github.com/ray-project/ray,模型开发与研究,训练与微调,36397,ray-project/ray,Ray is an AI compute engine. Ray consists of a core distributed runtime and a set of AI Libraries for accelerating ML workloads.
https://github.com/upscayl/upscayl,AI实用工具,图像创作工具,36163,upscayl/upscayl,"🆙 Upscayl - #1 Free and Open Source AI Image Upscaler for Linux, MacOS and Windows."
https://github.com/zhayujie/chatgpt-on-wechat,AI实用工具,聊天机器人,36105,zhayujie/chatgpt-on-wechat,基于大模型搭建的聊天机器人，同时支持 微信公众号、企业微信应用、飞书、钉钉 等接入，可选择GPT3.5/GPT-4o/GPT-o1/ DeepSeek/Claude/文心一言/讯飞星火/通义千问/ Gemini/GLM-4/Claude/Kimi/LinkAI，能处理文本、语音和图片，访问操作系统和互联网，支持基于自有知识库进行定制企业智能客服。
https://github.com/google-research/google-research,模型开发与研究,训练与微调,35284,google-research/google-research,Google Research
https://github.com/chatchat-space/Langchain-Chatchat,AI构建与集成,AI开发框架,34534,chatchat-space/Langchain-Chatchat,"Langchain-Chatchat（原Langchain-ChatGLM）基于 Langchain 与 ChatGLM, Qwen 与 Llama 等语言模型的 RAG 与 Agent 应用 | Langchain-Chatchat (formerly langchain-ChatGLM), local knowledge based LLM (like ChatGLM, Qwen and Llama) RAG and Agent app with langchain"
https://github.com/mendableai/firecrawl,未分类,未分类,34174,mendableai/firecrawl,"🔥 Turn entire websites into LLM-ready markdown or structured data. Scrape, crawl and extract with a single API."
https://github.com/facebookresearch/faiss,AI部署与基础设施,向量数据库,34129,facebookresearch/faiss,A library for efficient similarity search and clustering of dense vectors.
https://github.com/Bin-Huang/chatbox,AI实用工具,聊天机器人,33930,Bin-Huang/chatbox,"User-friendly Desktop Client App for AI Models/LLMs (GPT, Claude, Gemini, Ollama...)"
https://github.com/milvus-io/milvus,AI部署与基础设施,向量数据库,33869,milvus-io/milvus,"Milvus is a high-performance, cloud-native vector database built for scalable vector ANN search"
https://github.com/reworkd/AgentGPT,AI构建与集成,AI代理系统,33661,reworkd/AgentGPT,"🤖 Assemble, configure, and deploy autonomous AI Agents in your browser."
https://github.com/Pythagora-io/gpt-pilot,AI实用工具,编程助手,32561,Pythagora-io/gpt-pilot,The first real AI developer
https://github.com/Chanzhaoyu/chatgpt-web,AI实用工具,聊天机器人,31930,Chanzhaoyu/chatgpt-web,用 Express 和  Vue3 搭建的 ChatGPT 演示网页
https://github.com/lllyasviel/ControlNet,模型开发与研究,模型库/仓库,31922,lllyasviel/ControlNet,Let us control diffusion models!
https://github.com/google/jax,模型开发与研究,训练与微调,31846,google/jax,"Composable transformations of Python+NumPy programs: differentiate, vectorize, JIT to GPU/TPU, and more"
https://github.com/go-skynet/LocalAI,AI构建与集成,AI开发框架,31515,go-skynet/LocalAI,":robot: The free, Open Source alternative to OpenAI, Claude and others. Self-hosted and local-first. Drop-in replacement for OpenAI,  running on consumer-grade hardware. No GPU required. Runs gguf, transformers, diffusers and many more models architectures. Features: Generate Text, Audio, Video, Images, Voice Cloning, Distributed, P2P inference"
https://github.com/modelcontextprotocol/servers,AI构建与集成,AI开发框架,31389,modelcontextprotocol/servers,Model Context Protocol Servers
https://github.com/mckaywrigley/chatbot-ui,AI构建与集成,AI接口工具,30791,mckaywrigley/chatbot-ui,AI chat for any model.
https://github.com/open-mmlab/mmdetection,模型开发与研究,训练与微调,30721,open-mmlab/mmdetection,OpenMMLab Detection Toolbox and Benchmark
https://github.com/TabbyML/tabby,AI实用工具,编程助手,30674,TabbyML/tabby,Self-hosted AI coding assistant
https://github.com/paul-gauthier/aider,AI实用工具,编程助手,30633,paul-gauthier/aider,aider is AI pair programming in your terminal
https://github.com/danielmiessler/fabric,AI实用工具,工作流自动化,30480,danielmiessler/fabric,fabric is an open-source framework for augmenting humans using AI. It provides a modular framework for solving specific problems using a crowdsourced set of AI prompts that can be used anywhere.
https://github.com/tatsu-lab/stanford_alpaca,模型开发与研究,模型库/仓库,29909,tatsu-lab/stanford_alpaca,"Code and documentation to train Stanford's Alpaca models, and generate the data."
https://github.com/lutzroeder/netron,AI学习与资源,教程与课程,29836,lutzroeder/netron,"Visualizer for neural network, deep learning and machine learning models"
https://github.com/linexjlin/GPTs,AI学习与资源,资源集合,29599,linexjlin/GPTs,leaked prompts of GPTs
https://github.com/s0md3v/roop,AI实用工具,图像创作工具,29576,s0md3v/roop,one-click face swap
https://github.com/joaomdmoura/crewAI,AI构建与集成,AI代理系统,29564,joaomdmoura/crewAI,"Framework for orchestrating role-playing, autonomous AI agents. By fostering collaborative intelligence, CrewAI empowers agents to work together seamlessly, tackling complex tasks."
https://github.com/JushBJJ/Mr.-Ranedeer-AI-Tutor,未分类,未分类,29478,JushBJJ/Mr.-Ranedeer-AI-Tutor,A GPT-4 AI Tutor Prompt for customizable personalized learning experiences.
https://github.com/getcursor/cursor,AI实用工具,编程助手,29098,getcursor/cursor,The AI Code Editor
https://github.com/meta-llama/llama3,模型开发与研究,模型库/仓库,28576,meta-llama/llama3,The official Meta Llama 3 GitHub site
https://github.com/tinygrad/tinygrad,模型开发与研究,训练与微调,28531,tinygrad/tinygrad,You like pytorch? You like micrograd? You love tinygrad! ❤️
https://github.com/khoj-ai/khoj,AI实用工具,信息聚合工具,28511,khoj-ai/khoj,"Your AI second brain. Self-hostable. Get answers from the web or your docs. Build custom agents, schedule automations, do deep research. Turn any online or local LLM into your personal, autonomous AI (gpt, claude, gemini, llama, qwen, mistral). Get started - free."
https://github.com/huggingface/diffusers,模型开发与研究,训练与微调,28427,huggingface/diffusers,"🤗 Diffusers: State-of-the-art diffusion models for image, video, and audio generation in PyTorch and FLAX."
https://github.com/janhq/jan,AI实用工具,聊天机器人,28338,janhq/jan,Jan is an open source alternative to ChatGPT that runs 100% offline on your computer
https://github.com/openai/CLIP,模型开发与研究,模型库/仓库,28284,openai/CLIP,"CLIP (Contrastive Language-Image Pretraining),  Predict the most relevant text snippet given an image"
https://github.com/mindsdb/mindsdb,AI构建与集成,AI开发框架,27615,mindsdb/mindsdb,AI's query engine - Platform for building AI that can learn and answer questions over large scale federated data.
https://github.com/exo-explore/exo,AI部署与基础设施,计算资源管理,27423,exo-explore/exo,Run your own AI cluster at home with everyday devices 📱💻 🖥️⌚
https://github.com/Shubhamsaboo/awesome-llm-apps,AI学习与资源,资源集合,27215,Shubhamsaboo/awesome-llm-apps,"Collection of awesome LLM apps with AI Agents and RAG using OpenAI, Anthropic, Gemini and opensource models."
https://github.com/embedchain/embedchain,AI构建与集成,AI开发框架,27214,embedchain/embedchain,The Memory layer for AI Agents
https://github.com/mem0ai/mem0,AI构建与集成,AI代理系统,27214,mem0ai/mem0,The Memory layer for AI Agents
https://github.com/OpenBMB/ChatDev,AI实用工具,编程助手,26625,OpenBMB/ChatDev,Create Customized Software using Natural Language Idea (through LLM-powered Multi-Agent Collaboration)
https://github.com/karpathy/llm.c,模型开发与研究,推理优化,26249,karpathy/llm.c,"LLM training in simple, raw C/CUDA"
https://github.com/openai/openai-python,AI构建与集成,AI接口工具,26181,openai/openai-python,The official Python library for the OpenAI API
https://github.com/mozilla/DeepSpeech,模型开发与研究,训练与微调,26179,mozilla/DeepSpeech,"DeepSpeech is an open source embedded (offline, on-device) speech-to-text engine which can run in real time on devices ranging from a Raspberry Pi 4 to high power GPU servers."
https://github.com/DS4SD/docling,未分类,未分类,26056,DS4SD/docling,Get your documents ready for gen AI
https://github.com/hpcaitech/Open-Sora,模型开发与研究,模型库/仓库,26012,hpcaitech/Open-Sora,Open-Sora: Democratizing Efficient Video Production for All
https://github.com/harry0703/MoneyPrinterTurbo,模型开发与研究,模型库/仓库,25865,harry0703/MoneyPrinterTurbo,利用AI大模型，一键生成高清短视频 Generate short videos with one click using AI LLM.
https://github.com/Stability-AI/generative-models,模型开发与研究,模型库/仓库,25646,Stability-AI/generative-models,Generative Models by Stability AI
https://github.com/continuedev/continue,AI实用工具,编程助手,25250,continuedev/continue,"⏩ Create, share, and use custom AI code assistants with our open-source IDE extensions and hub of models, rules, prompts, docs, and other building blocks"
https://github.com/composiohq/composio,AI构建与集成,AI代理系统,24875,composiohq/composio,Composio equip's your AI agents & LLMs with 100+ high-quality integrations via function calling
https://github.com/invoke-ai/InvokeAI,AI实用工具,图像创作工具,24785,invoke-ai/InvokeAI,"Invoke is a leading creative engine for Stable Diffusion models, empowering professionals, artists, and enthusiasts to generate and create visual media using the latest AI-driven technologies. The solution offers an industry leading WebUI, and serves as the foundation for multiple commercial products."
https://github.com/songquanpeng/one-api,AI构建与集成,AI开发框架,24386,songquanpeng/one-api,"LLM API 管理 & 分发系统，支持 OpenAI、Azure、Anthropic Claude、Google Gemini、DeepSeek、字节豆包、ChatGLM、文心一言、讯飞星火、通义千问、360 智脑、腾讯混元等主流模型，统一 API 适配，可用于 key 管理与二次分发。单可执行文件，提供 Docker 镜像，一键部署，开箱即用。LLM API management & key redistribution system, unifying multiple providers under a single API. Single binary, Docker-ready, with an English UI."
https://github.com/openai-translator/openai-translator,AI实用工具,工作流自动化,24314,openai-translator/openai-translator,基于 ChatGPT API 的划词翻译浏览器插件和跨平台桌面端应用    -    Browser extension and cross-platform desktop application for translation based on ChatGPT API.
https://github.com/danny-avila/LibreChat,AI构建与集成,AI接口工具,24126,danny-avila/LibreChat,"Enhanced ChatGPT Clone: Features Agents, DeepSeek, Anthropic, AWS, OpenAI, Assistants API, Azure, Groq, o1, GPT-4o, Mistral, OpenRouter, Vertex AI, Gemini, Artifacts, AI model switching, message search, Code Interpreter, langchain, DALL-E-3, OpenAPI Actions, Functions, Secure Multi-User Auth, Presets, open-source for self-hosting. Active project."
https://github.com/microsoft/JARVIS,AI构建与集成,AI代理系统,24076,microsoft/JARVIS,"JARVIS, a system to connect LLMs with ML community. Paper: https://arxiv.org/pdf/2303.17580.pdf"
https://github.com/phidatahq/phidata,AI构建与集成,AI代理系统,23983,phidatahq/phidata,"A lightweight library for building Multimodal Agents. Give LLMs superpowers like memory, knowledge, tools and reasoning."
https://github.com/microsoft/semantic-kernel,AI构建与集成,AI代理系统,23855,microsoft/semantic-kernel,Integrate cutting-edge LLM technology quickly and easily into your apps
https://github.com/modularml/mojo,模型开发与研究,训练与微调,23839,modularml/mojo,The MAX Platform (includes Mojo)
https://github.com/stanford-oval/storm,AI实用工具,信息聚合工具,23718,stanford-oval/storm,An LLM-powered knowledge curation system that researches a topic and generates a full-length report with citations.
https://github.com/labring/FastGPT,AI构建与集成,AI开发框架,23353,labring/FastGPT,"FastGPT is a knowledge-based platform built on the LLMs, offers a comprehensive suite of out-of-the-box capabilities such as data processing, RAG retrieval, and visual AI workflow orchestration, letting you easily develop and deploy complex question-answering systems without the need for extensive setup or configuration."
https://github.com/stanfordnlp/dspy,AI构建与集成,提示工程工具,22885,stanfordnlp/dspy,DSPy: The framework for programming—not prompting—language models
https://github.com/qdrant/qdrant,AI部署与基础设施,向量数据库,22865,qdrant/qdrant,"Qdrant - High-performance, massive-scale Vector Database and Vector Search Engine for the next generation of AI. Also available in the cloud https://cloud.qdrant.io/"
https://github.com/lucidrains/vit-pytorch,模型开发与研究,模型库/仓库,22337,lucidrains/vit-pytorch,"Implementation of Vision Transformer, a simple way to achieve SOTA in vision classification with only a single transformer encoder, in Pytorch"
https://github.com/facefusion/facefusion,AI实用工具,图像创作工具,22286,facefusion/facefusion,Industry leading face manipulation platform
https://github.com/Mozilla-Ocho/llamafile,模型开发与研究,推理优化,22109,Mozilla-Ocho/llamafile,Distribute and run LLMs with a single file.
https://github.com/haotian-liu/LLaVA,模型开发与研究,模型库/仓库,22094,haotian-liu/LLaVA,[NeurIPS'23 Oral] Visual Instruction Tuning (LLaVA) built towards GPT-4V level capabilities and beyond.
https://github.com/Cinnamon/kotaemon,AI构建与集成,AI开发框架,21898,Cinnamon/kotaemon,An open-source RAG-based tool for chatting with your documents.
https://github.com/jina-ai/jina,AI部署与基础设施,开发工具集成,21500,jina-ai/jina,☁️ Build multimodal AI applications with cloud-native stack
https://github.com/yoheinakajima/babyagi,AI构建与集成,AI代理系统,21313,yoheinakajima/babyagi,
https://github.com/BuilderIO/gpt-crawler,模型开发与研究,数据工程,21301,BuilderIO/gpt-crawler,Crawl a site to generate knowledge files to create your own custom GPT from a URL
https://github.com/openai/chatgpt-retrieval-plugin,AI实用工具,聊天机器人,21160,openai/chatgpt-retrieval-plugin,The ChatGPT Retrieval Plugin lets you easily find personal or work documents by asking questions in natural language.
https://github.com/microsoft/unilm,模型开发与研究,训练与微调,21021,microsoft/unilm,"Large-scale Self-supervised Pre-training Across Tasks, Languages, and Modalities"
https://github.com/Sanster/lama-cleaner,AI实用工具,图像创作工具,20862,Sanster/lama-cleaner,"Image inpainting tool powered by SOTA AI Model. Remove any unwanted object, defect, people from your pictures or erase and replace(powered by stable diffusion) any thing on your pictures."
https://github.com/assafelovic/gpt-researcher,AI实用工具,信息聚合工具,20737,assafelovic/gpt-researcher,LLM based autonomous agent that conducts deep local and web research on any topic and generates a long report with citations.
https://github.com/fishaudio/fish-speech,模型开发与研究,模型库/仓库,20484,fishaudio/fish-speech,SOTA Open Source TTS
https://github.com/PromtEngineer/localGPT,AI实用工具,信息聚合工具,20433,PromtEngineer/localGPT,Chat with your documents on your local device using GPT models. No data leaves your device and 100% private.
https://github.com/mlc-ai/mlc-llm,模型开发与研究,推理优化,20326,mlc-ai/mlc-llm,Universal LLM Deployment Engine with ML Compilation
https://github.com/BerriAI/litellm,AI构建与集成,AI开发框架,20255,BerriAI/litellm,"Python SDK, Proxy Server (LLM Gateway) to call 100+ LLM APIs in OpenAI format - [Bedrock, Azure, OpenAI, VertexAI, Cohere, Anthropic, Sagemaker, HuggingFace, Replicate, Groq]"
https://github.com/deepset-ai/haystack,AI构建与集成,AI开发框架,20168,deepset-ai/haystack,"AI orchestration framework to build customizable, production-ready LLM applications. Connect components (models, vector DBs, file converters) to pipelines or agents that can interact with your data. With advanced retrieval methods, it's best suited for building RAG, question answering, semantic search or conversational agent chatbots."
https://github.com/ml-explore/mlx,模型开发与研究,训练与微调,20077,ml-explore/mlx,MLX: An array framework for Apple silicon
https://github.com/guidance-ai/guidance,AI构建与集成,提示工程工具,19996,guidance-ai/guidance,A guidance language for controlling large language models.
https://github.com/huggingface/datasets,模型开发与研究,数据工程,19943,huggingface/datasets,"🤗 The largest hub of ready-to-use datasets for ML models with fast, easy-to-use and efficient data manipulation tools"
https://github.com/openai/swarm,AI构建与集成,AI代理系统,19546,openai/swarm,"Educational framework exploring ergonomic, lightweight multi-agent orchestration. Managed by OpenAI Solution team."
https://github.com/OpenBMB/OmniLMM,模型开发与研究,模型库/仓库,19151,OpenBMB/OmniLMM,"MiniCPM-o 2.6: A GPT-4o Level MLLM for Vision, Speech and Multimodal Live Streaming on Your Phone"
https://github.com/OpenBMB/MiniCPM-o,模型开发与研究,模型库/仓库,19151,OpenBMB/MiniCPM-o,"MiniCPM-o 2.6: A GPT-4o Level MLLM for Vision, Speech and Multimodal Live Streaming on Your Phone"
https://github.com/chroma-core/chroma,AI部署与基础设施,向量数据库,19114,chroma-core/chroma,the AI-native open-source embedding database
https://github.com/stitionai/devika,AI实用工具,编程助手,19113,stitionai/devika,"Devika is an Agentic AI Software Engineer that can understand high-level human instructions, break them down into steps, research relevant information, and write code to achieve the given objective. Devika aims to be a competitive open-source alternative to Devin by Cognition AI. [⚠️ DEVIKA DOES NOT HAVE AN OFFICIAL WEBSITE ⚠️]"
https://github.com/Sinaptik-AI/pandas-ai,AI实用工具,编程助手,18868,Sinaptik-AI/pandas-ai,"Chat with your database or your datalake (SQL, CSV, parquet). PandasAI makes data analysis conversational using LLMs and RAG."
https://github.com/kaixindelele/ChatPaper,AI实用工具,信息聚合工具,18827,kaixindelele/ChatPaper,Use ChatGPT to summarize the arXiv papers. 全流程加速科研，利用chatgpt进行论文全文总结+专业翻译+润色+审稿+审稿回复
https://github.com/joonspk-research/generative_agents,AI实用工具,聊天机器人,18786,joonspk-research/generative_agents,Generative Agents: Interactive Simulacra of Human Behavior
https://github.com/ymcui/Chinese-LLaMA-Alpaca,模型开发与研究,模型库/仓库,18783,ymcui/Chinese-LLaMA-Alpaca,中文LLaMA&Alpaca大语言模型+本地CPU/GPU训练部署 (Chinese LLaMA & Alpaca LLMs)
https://github.com/HigherOrderCO/Bend,模型开发与研究,训练与微调,18610,HigherOrderCO/Bend,"A massively parallel, high-level programming language"
https://github.com/jingyaogong/minimind,模型开发与研究,训练与微调,18370,jingyaogong/minimind,🚀🚀 「大模型」2小时完全从0训练26M的小参数GPT！🌏 Train a 26M-parameter GPT from scratch in just 2h!
https://github.com/karpathy/llama2.c,模型开发与研究,推理优化,18261,karpathy/llama2.c,Inference Llama 2 in one file of pure C
https://github.com/joke2k/faker,未分类,未分类,18234,joke2k/faker,Faker is a Python package that generates fake data for you.
https://github.com/CopilotKit/CopilotKit,AI构建与集成,AI接口工具,18048,CopilotKit/CopilotKit,"React UI + elegant infrastructure for AI Copilots, AI chatbots, and in-app AI agents. The Agentic last-mile 🪁"
https://github.com/huggingface/peft,模型开发与研究,训练与微调,18024,huggingface/peft,🤗 PEFT: State-of-the-art Parameter-Efficient Fine-Tuning.
https://github.com/Unity-Technologies/ml-agents,AI实用工具,聊天机器人,17922,Unity-Technologies/ml-agents,The Unity Machine Learning Agents Toolkit (ML-Agents) is an open-source project that enables games and simulations to serve as environments for training intelligent agents using deep reinforcement learning and imitation learning.
https://github.com/QwenLM/Qwen,模型开发与研究,模型库/仓库,17749,QwenLM/Qwen,The official repo of Qwen (通义千问) chat & pretrained large language model proposed by Alibaba Cloud.
https://github.com/Mikubill/sd-webui-controlnet,AI实用工具,图像创作工具,17504,Mikubill/sd-webui-controlnet,WebUI extension for ControlNet
https://github.com/transitive-bullshit/chatgpt-api,AI实用工具,聊天机器人,17264,transitive-bullshit/chatgpt-api,AI agent stdlib that works with any LLM and TypeScript AI SDK.
https://github.com/apple/ml-stable-diffusion,模型开发与研究,模型库/仓库,17259,apple/ml-stable-diffusion,Stable Diffusion with Core ML on Apple Silicon
https://github.com/deepseek-ai/Janus,模型开发与研究,模型库/仓库,17028,deepseek-ai/Janus,Janus-Series: Unified Multimodal Understanding and Generation Models
https://github.com/huggingface/candle,模型开发与研究,训练与微调,16958,huggingface/candle,Minimalist ML framework for Rust
https://github.com/datawhalechina/prompt-engineering-for-developers,AI学习与资源,教程与课程,16798,datawhalechina/prompt-engineering-for-developers,面向开发者的 LLM 入门教程，吴恩达大模型系列课程中文版
https://github.com/e2b-dev/awesome-ai-agents,AI学习与资源,资源集合,16760,e2b-dev/awesome-ai-agents,A list of AI autonomous agents
https://github.com/Dao-AILab/flash-attention,模型开发与研究,推理优化,16713,Dao-AILab/flash-attention,Fast and memory-efficient exact attention
https://github.com/QwenLM/Qwen1.5,模型开发与研究,模型库/仓库,16523,QwenLM/Qwen1.5,"Qwen2.5 is the large language model series developed by Qwen team, Alibaba Cloud."
https://github.com/emilwallner/Screenshot-to-code,AI实用工具,编程助手,16520,emilwallner/Screenshot-to-code,A neural network that transforms a design mock-up into a static website.
https://github.com/huggingface/smolagents,AI构建与集成,AI代理系统,16452,huggingface/smolagents,🤗 smolagents: a barebones library for agents that think in python code.
https://github.com/UKPLab/sentence-transformers,模型开发与研究,模型库/仓库,16390,UKPLab/sentence-transformers,State-of-the-Art Text Embeddings
https://github.com/facebookresearch/codellama,模型开发与研究,模型库/仓库,16264,facebookresearch/codellama,Inference code for CodeLlama models
https://github.com/TransformerOptimus/SuperAGI,AI构建与集成,AI代理系统,16162,TransformerOptimus/SuperAGI,"<⚡️> SuperAGI - A dev-first open source autonomous AI agent framework. Enabling developers to build, manage & run useful autonomous agents quickly and reliably."
https://github.com/liguodongiot/llm-action,AI学习与资源,教程与课程,16130,liguodongiot/llm-action,本项目旨在分享大模型相关技术原理以及实战经验（大模型工程化、大模型应用落地）
https://github.com/leon-ai/leon,AI实用工具,聊天机器人,16126,leon-ai/leon,🧠 Leon is your open-source personal assistant.
https://github.com/eosphoros-ai/DB-GPT,AI实用工具,编程助手,15973,eosphoros-ai/DB-GPT,AI Native Data App Development framework with AWEL(Agentic Workflow Expression Language) and Agents
https://github.com/openai/evals,AI构建与集成,评估工具,15826,openai/evals,"Evals is a framework for evaluating LLMs and LLM systems, and an open-source registry of benchmarks."
https://github.com/camenduru/stable-diffusion-webui-colab,AI实用工具,图像创作工具,15813,camenduru/stable-diffusion-webui-colab,stable diffusion webui colab
https://github.com/cpacker/MemGPT,AI构建与集成,AI代理系统,15760,cpacker/MemGPT,"Letta (formerly MemGPT) is the stateful agents framework with memory, reasoning, and context management."
https://github.com/AI4Finance-Foundation/FinGPT,模型开发与研究,模型库/仓库,15725,AI4Finance-Foundation/FinGPT,FinGPT: Open-Source Financial Large Language Models!  Revolutionize 🔥    We release the trained model on HuggingFace.
https://github.com/sunner/ChatALL,AI构建与集成,AI接口工具,15706,sunner/ChatALL,"Concurrently chat with ChatGPT, Bing Chat, Bard, Alpaca, Vicuna, Claude, ChatGLM, MOSS, 讯飞星火, 文心一言 and more, discover the best answers"
https://github.com/arc53/DocsGPT,AI实用工具,信息聚合工具,15506,arc53/DocsGPT,"DocsGPT is an open-source genAI tool that helps users get reliable answers from knowledge source, while avoiding hallucinations. It enables private and reliable information retrieval, with tooling and agentic system capability built in."
https://github.com/GaiZhenbiao/ChuanhuChatGPT,AI构建与集成,AI接口工具,15408,GaiZhenbiao/ChuanhuChatGPT,"GUI for ChatGPT API and many LLMs. Supports agents, file-based QA, GPT finetuning and query with web search. All with a neat UI."
https://github.com/mayooear/gpt4-pdf-chatbot-langchain,AI实用工具,信息聚合工具,15336,mayooear/gpt4-pdf-chatbot-langchain,AI PDF chatbot agent built with LangChain & LangGraph
https://github.com/princeton-nlp/SWE-agent,AI实用工具,编程助手,15292,princeton-nlp/SWE-agent,"SWE-agent takes a GitHub issue and tries to automatically fix it, using GPT-4, or your LM of choice. It can also be employed for offensive cybersecurity or competitive coding challenges. [NeurIPS 2024]"
https://github.com/dzhng/deep-research,AI实用工具,信息聚合工具,15283,dzhng/deep-research,"An AI-powered research assistant that performs iterative, deep research on any topic by combining search engines, web scraping, and large language models.  The goal of this repo is to provide the simplest implementation of a deep research agent - e.g. an agent that can refine its research direction overtime and deep dive into a topic."
https://github.com/guillaumekln/faster-whisper,AI部署与基础设施,模型部署工具,15238,guillaumekln/faster-whisper,Faster Whisper transcription with CTranslate2
https://github.com/mlc-ai/web-llm,AI构建与集成,AI接口工具,15149,mlc-ai/web-llm,High-performance In-browser LLM Inference Engine
https://github.com/openai/triton,模型开发与研究,推理优化,15091,openai/triton,Development repository for the Triton language and compiler
https://github.com/lss233/chatgpt-mirai-qq-bot,AI实用工具,聊天机器人,14924,lss233/chatgpt-mirai-qq-bot,🤖 可 DIY 的 多模态 AI 聊天机器人 | 🚀 快速接入 微信、 QQ、Telegram、等聊天平台 | 🦈支持DeepSeek、Grok、Claude、Ollama、Gemini、OpenAI | 工作流系统、网页搜索、AI画图、人设调教、虚拟女仆、语音对话 |
https://github.com/kubeflow/kubeflow,AI部署与基础设施,计算资源管理,14834,kubeflow/kubeflow,Machine Learning Toolkit for Kubernetes
https://github.com/xcanwin/KeepChatGPT,AI实用工具,聊天机器人,14803,xcanwin/KeepChatGPT,这是一款提高ChatGPT的数据安全能力和效率的插件。并且免费共享大量创新功能，如：自动刷新、保持活跃、数据安全、取消审计、克隆对话、言无不尽、净化页面、展示大屏、拦截跟踪、日新月异、明察秋毫等。让我们的AI体验无比安全、顺畅、丝滑、高效、简洁。
https://github.com/borisdayma/dalle-mini,模型开发与研究,模型库/仓库,14801,borisdayma/dalle-mini,DALL·E Mini - Generate images from a text prompt
https://github.com/fauxpilot/fauxpilot,AI实用工具,编程助手,14691,fauxpilot/fauxpilot,FauxPilot - an open-source alternative to GitHub Copilot server
https://github.com/vanna-ai/vanna,AI实用工具,编程助手,14644,vanna-ai/vanna,🤖 Chat with your SQL database 📊. Accurate Text-to-SQL Generation via LLMs using RAG 🔄.
https://github.com/FlagAlpha/Llama2-Chinese,模型开发与研究,模型库/仓库,14520,FlagAlpha/Llama2-Chinese,Llama中文社区，Llama3在线体验和微调模型已开放，实时汇总最新Llama3学习资料，已将所有代码更新适配Llama3，构建最好的中文Llama大模型，完全开源可商用
https://github.com/state-spaces/mamba,模型开发与研究,模型库/仓库,14480,state-spaces/mamba,Mamba SSM architecture
https://github.com/steven-tey/novel,AI实用工具,文本创作工具,14346,steven-tey/novel,Notion-style WYSIWYG editor with AI-powered autocompletion.
https://github.com/iterative/dvc,AI部署与基础设施,开发工具集成,14338,iterative/dvc,🦉 Data Versioning and ML Experiments
https://github.com/unifyai/ivy,AI部署与基础设施,开发工具集成,14141,unifyai/ivy,Convert Machine Learning Code Between Frameworks
https://github.com/flairNLP/flair,AI构建与集成,AI开发框架,14129,flairNLP/flair,A very simple framework for state-of-the-art Natural Language Processing (NLP)
https://github.com/langchain-ai/langchainjs,AI构建与集成,AI开发框架,14113,langchain-ai/langchainjs,🦜🔗 Build context-aware reasoning applications 🦜🔗
https://github.com/openai/tiktoken,模型开发与研究,数据工程,14037,openai/tiktoken,tiktoken is a fast BPE tokeniser for use with OpenAI's models.
https://github.com/THUDM/ChatGLM3,模型开发与研究,模型库/仓库,13677,THUDM/ChatGLM3,ChatGLM3 series: Open Bilingual Chat LLMs | 开源双语对话语言模型
https://github.com/google-deepmind/deepmind-research,模型开发与研究,模型库/仓库,13675,google-deepmind/deepmind-research,This repository contains implementations and illustrative code to accompany DeepMind publications
https://github.com/spotify/annoy,AI部署与基础设施,向量数据库,13644,spotify/annoy,Approximate Nearest Neighbors in C++/Python optimized for memory usage and loading/saving to disk
https://github.com/NVIDIA/NeMo,模型开发与研究,训练与微调,13549,NVIDIA/NeMo,"A scalable generative AI framework built for researchers and developers working on Large Language Models, Multimodal, and Speech AI (Automatic Speech Recognition and Text-to-Speech)"
https://github.com/botpress/botpress,AI构建与集成,AI代理系统,13498,botpress/botpress,The open-source hub to build & deploy GPT/LLM Agents ⚡️
https://github.com/SawyerHood/draw-a-ui,AI实用工具,编程助手,13490,SawyerHood/draw-a-ui,Draw a mockup and generate html for it
https://github.com/BlinkDL/RWKV-LM,模型开发与研究,模型库/仓库,13478,BlinkDL/RWKV-LM,"RWKV (pronounced RwaKuv) is an RNN with great LLM performance, which can also be directly trained like a GPT transformer (parallelizable). We are at RWKV-7 ""Goose"". So it's combining the best of RNN and transformer - great performance, linear time, constant space (no kv-cache), fast training, infinite ctx_len, and free sentence embedding."
https://github.com/google-deepmind/alphafold,模型开发与研究,模型库/仓库,13377,google-deepmind/alphafold,Open source code for AlphaFold 2.
https://github.com/xenova/transformers.js,AI构建与集成,AI开发框架,13364,xenova/transformers.js,"State-of-the-art Machine Learning for the web. Run 🤗 Transformers directly in your browser, with no need for a server!"
https://github.com/stas00/ml-engineering,AI学习与资源,教程与课程,13321,stas00/ml-engineering,Machine Learning Engineering Open Book
https://github.com/vercel/ai,AI构建与集成,AI开发框架,13169,vercel/ai,"The AI Toolkit for TypeScript. From the creators of Next.js, the AI SDK is a free open-source library for building AI-powered applications and agents"
https://github.com/SillyTavern/SillyTavern,AI构建与集成,AI接口工具,13153,SillyTavern/SillyTavern,LLM Frontend for Power Users.
https://github.com/divamgupta/diffusionbee-stable-diffusion-ui,AI实用工具,图像创作工具,13143,divamgupta/diffusionbee-stable-diffusion-ui,Diffusion Bee is the easiest way to run Stable Diffusion locally on your M1 Mac. Comes with a one-click installer. No dependencies or technical knowledge needed.
https://github.com/cocktailpeanut/dalai,AI实用工具,聊天机器人,13080,cocktailpeanut/dalai,The simplest way to run LLaMA on your local machine
https://github.com/huggingface/trl,模型开发与研究,训练与微调,13039,huggingface/trl,Train transformer language models with reinforcement learning.
https://github.com/weaviate/weaviate,AI部署与基础设施,向量数据库,12999,weaviate/weaviate,"Weaviate is an open-source vector database that stores both objects and vectors, allowing for the combination of vector search with structured filtering with the fault tolerance and scalability of a cloud-native database​."
https://github.com/netease-youdao/QAnything,AI构建与集成,AI开发框架,12982,netease-youdao/QAnything,Question and Answer based on Anything.
https://github.com/mikeroyal/Self-Hosting-Guide,AI学习与资源,教程与课程,12923,mikeroyal/Self-Hosting-Guide,"Self-Hosting Guide. Learn all about  locally hosting (on premises & private web servers) and managing software applications by yourself or your organization. Including Cloud, LLMs, WireGuard, Automation, Home Assistant, and Networking."
https://github.com/Skyvern-AI/skyvern,AI实用工具,工作流自动化,12885,Skyvern-AI/skyvern,Automate browser-based workflows with LLMs and Computer Vision
https://github.com/sgl-project/sglang,AI构建与集成,提示工程工具,12870,sgl-project/sglang,SGLang is a fast serving framework for large language models and vision language models.
https://github.com/microsoft/BitNet,模型开发与研究,推理优化,12864,microsoft/BitNet,Official inference framework for 1-bit LLMs
https://github.com/CompVis/latent-diffusion,模型开发与研究,模型库/仓库,12638,CompVis/latent-diffusion,High-Resolution Image Synthesis with Latent Diffusion Models
https://github.com/jina-ai/clip-as-service,AI构建与集成,AI接口工具,12623,jina-ai/clip-as-service,"🏄 Scalable embedding, reasoning, ranking for images and sentences with CLIP"
https://github.com/danswer-ai/danswer,AI实用工具,信息聚合工具,12618,danswer-ai/danswer,Gen-AI Chat for Teams - Think ChatGPT if it had access to your team's unique knowledge.
https://github.com/llmware-ai/llmware,AI构建与集成,AI开发框架,12610,llmware-ai/llmware,"Unified framework for building enterprise RAG pipelines with small, specialized models"
https://github.com/activepieces/activepieces,AI实用工具,工作流自动化,12546,activepieces/activepieces,Open Source AI Automation ✨ All our 280+ pieces are now available as MCP to use with LLMs
https://github.com/PaddlePaddle/PaddleNLP,模型开发与研究,训练与微调,12485,PaddlePaddle/PaddleNLP,Easy-to-use and powerful LLM and SLM library with awesome model zoo.
https://github.com/ggerganov/ggml,模型开发与研究,推理优化,12246,ggerganov/ggml,Tensor library for machine learning
https://github.com/Huanshere/VideoLingo,AI实用工具,图像创作工具,12166,Huanshere/VideoLingo,"Netflix-level subtitle cutting, translation, alignment, and even dubbing - one-click fully automated AI video subtitle team | Netflix级字幕切割、翻译、对齐、甚至加上配音，一键全自动视频搬运AI字幕组"
https://github.com/marimo-team/marimo,AI部署与基础设施,开发工具集成,12101,marimo-team/marimo,"A reactive notebook for Python — run reproducible experiments, query with SQL, execute as a script, deploy as an app, and version with git. All in a modern, AI-native editor."
https://github.com/OpenMOSS/MOSS,模型开发与研究,模型库/仓库,12040,OpenMOSS/MOSS,An open-source tool-augmented conversational language model from Fudan University
https://github.com/NVIDIA/Megatron-LM,模型开发与研究,模型库/仓库,11992,NVIDIA/Megatron-LM,Ongoing research training transformer models at scale
https://github.com/ShishirPatil/gorilla,AI实用工具,编程助手,11950,ShishirPatil/gorilla,Gorilla: Training and Evaluating LLMs for Function Calls (Tool Calls)
https://github.com/PKU-YuanGroup/Open-Sora-Plan,模型开发与研究,模型库/仓库,11941,PKU-YuanGroup/Open-Sora-Plan,"This project aim to reproduce Sora (Open AI T2V model), we wish the open source community contribute to this project."
https://github.com/Lightning-AI/litgpt,模型开发与研究,训练与微调,11923,Lightning-AI/litgpt,"20+ high-performance LLMs with recipes to pretrain, finetune and deploy at scale."
https://github.com/smol-ai/developer,AI构建与集成,AI代理系统,11919,smol-ai/developer,the first library to let you embed a developer agent in your own app!
https://github.com/eugeneyan/open-llms,AI学习与资源,资源集合,11875,eugeneyan/open-llms,📋 A list of open LLMs available for commercial use.
https://github.com/andrewyng/aisuite,AI构建与集成,AI开发框架,11874,andrewyng/aisuite,"Simple, unified interface to multiple Generative AI providers"
https://github.com/openai/shap-e,模型开发与研究,模型库/仓库,11856,openai/shap-e,Generate 3D objects conditioned on text or images
https://github.com/h2oai/h2ogpt,AI构建与集成,AI开发框架,11750,h2oai/h2ogpt,"Private chat with local GPT with document, images, video, etc. 100% private, Apache 2.0. Supports oLLaMa, Mixtral, llama.cpp, and more. Demo: https://gpt.h2o.ai/ https://gpt-docs.h2o.ai/"
https://github.com/PaddlePaddle/PaddleSpeech,模型开发与研究,训练与微调,11744,PaddlePaddle/PaddleSpeech,"Easy-to-use Speech Toolkit including Self-Supervised Learning model, SOTA/Streaming ASR with punctuation, Streaming TTS with text frontend, Speaker Verification System, End-to-End Speech Translation and Keyword Spotting. Won NAACL2022 Best Demo Award."
https://github.com/camel-ai/camel,AI实用工具,聊天机器人,11506,camel-ai/camel,🐫 CAMEL: The first and the best multi-agent framework. Finding the Scaling Law of Agents. https://www.camel-ai.org
https://github.com/Jiayi-Pan/TinyZero,模型开发与研究,模型库/仓库,11493,Jiayi-Pan/TinyZero,"Clean, minimal, accessible reproduction of DeepSeek R1-Zero"
https://github.com/facebookresearch/seamless_communication,模型开发与研究,模型库/仓库,11469,facebookresearch/seamless_communication,Foundational Models for State-of-the-Art Speech and Text Translation
https://github.com/mlfoundations/open_clip,模型开发与研究,模型库/仓库,11448,mlfoundations/open_clip,An open source implementation of CLIP.
https://github.com/NVIDIA/TensorRT,模型开发与研究,推理优化,11422,NVIDIA/TensorRT,NVIDIA® TensorRT™ is an SDK for high-performance deep learning inference on NVIDIA GPUs. This repository contains the open source components of TensorRT.
https://github.com/ludwig-ai/ludwig,AI构建与集成,AI开发框架,11410,ludwig-ai/ludwig,"Low-code framework for building custom LLMs, neural networks, and other AI models"
https://github.com/RUCAIBox/LLMSurvey,AI学习与资源,资源集合,11313,RUCAIBox/LLMSurvey,"The official GitHub page for the survey paper ""A Survey of Large Language Models""."
https://github.com/guoyww/AnimateDiff,模型开发与研究,模型库/仓库,11257,guoyww/AnimateDiff,Official implementation of AnimateDiff.
https://github.com/lucidrains/DALLE2-pytorch,模型开发与研究,模型库/仓库,11252,lucidrains/DALLE2-pytorch,"Implementation of DALL-E 2, OpenAI's updated text-to-image synthesis neural network,  in Pytorch"
https://github.com/outlines-dev/outlines,AI构建与集成,提示工程工具,11242,outlines-dev/outlines,Structured Text Generation
https://github.com/google-research/vision_transformer,模型开发与研究,模型库/仓库,11164,google-research/vision_transformer,
https://github.com/THUDM/CogVideo,模型开发与研究,模型库/仓库,11123,THUDM/CogVideo,text and image to video generation: CogVideoX (2024) and CogVideo (ICLR 2023)
https://github.com/langchain-ai/langgraph,AI构建与集成,AI代理系统,11108,langchain-ai/langgraph,Build resilient language agents as graphs.
https://github.com/bentoml/OpenLLM,AI部署与基础设施,模型部署工具,11084,bentoml/OpenLLM,"Run any open-source LLMs, such as DeepSeek and Llama, as OpenAI compatible API endpoint in the cloud."
https://github.com/getumbrel/llama-gpt,AI实用工具,聊天机器人,10958,getumbrel/llama-gpt,"A self-hosted, offline, ChatGPT-like chatbot. Powered by Llama 2. 100% private, with no data leaving your device. New: Code Llama support!"
https://github.com/HigherOrderCO/HVM,AI部署与基础设施,开发工具集成,10944,HigherOrderCO/HVM,"A massively parallel, optimal functional runtime in Rust"
https://github.com/openai/DALL-E,模型开发与研究,模型库/仓库,10841,openai/DALL-E,PyTorch package for the discrete VAE used for DALL·E.
https://github.com/getomni-ai/zerox,未分类,未分类,10815,getomni-ai/zerox,OCR & Document Extraction using vision models
https://github.com/Unstructured-IO/unstructured,AI构建与集成,AI开发框架,10785,Unstructured-IO/unstructured,"Open source libraries and APIs to build custom preprocessing pipelines for labeling, training, or production machine learning pipelines."
https://github.com/codota/TabNine,AI实用工具,编程助手,10749,codota/TabNine,AI Code Completions
https://github.com/magic-research/magic-animate,模型开发与研究,模型库/仓库,10720,magic-research/magic-animate,[CVPR 2024] MagicAnimate: Temporally Consistent Human Image Animation using Diffusion Model
https://github.com/TheRamU/Fay,AI构建与集成,AI接口工具,10705,TheRamU/Fay,fay是一个帮助数字人（2.5d、3d、移动、pc、网页）或大语言模型（openai兼容、deepseek）连通业务系统的agent框架。
https://github.com/neuml/txtai,AI部署与基础设施,向量数据库,10677,neuml/txtai,"💡 All-in-one open-source embeddings database for semantic search, LLM orchestration and language model workflows"
https://github.com/TheR1D/shell_gpt,AI实用工具,编程助手,10651,TheR1D/shell_gpt,"A command-line productivity tool powered by AI large language models like GPT-4, will help you accomplish your tasks faster and more efficiently."
https://github.com/Baiyuetribe/paper2gui,AI实用工具,图像创作工具,10516,Baiyuetribe/paper2gui,Convert AI papers to GUI，Make it easy and convenient for everyone to use artificial intelligence technology。让每个人都简单方便的使用前沿人工智能技术
https://github.com/josStorer/chatGPTBox,AI实用工具,信息聚合工具,10458,josStorer/chatGPTBox,"Integrating ChatGPT into your browser deeply, everything you need is here"
https://github.com/cleanlab/cleanlab,模型开发与研究,数据工程,10438,cleanlab/cleanlab,"The standard data-centric AI package for data quality and machine learning with messy, real-world data and labels."
https://github.com/salesforce/LAVIS,模型开发与研究,训练与微调,10416,salesforce/LAVIS,LAVIS - A One-stop Library for Language-Vision Intelligence
https://github.com/artidoro/qlora,模型开发与研究,训练与微调,10360,artidoro/qlora,QLoRA: Efficient Finetuning of Quantized LLMs
https://github.com/RockChinQ/LangBot,AI实用工具,聊天机器人,10279,RockChinQ/LangBot,"😎简单易用、🧩丰富生态 - 大模型原生即时通信机器人平台 | 适配 QQ / 微信（企业微信、个人微信）/ 飞书 / 钉钉 / Discord / Telegram / Slack 等平台 | 支持 ChatGPT、DeepSeek、Dify、Claude、Gemini、xAI Grok、Ollama、LM Studio、阿里云百炼、火山方舟、SiliconFlow、Qwen、Moonshot、ChatGLM、SillyTraven、MCP 等 LLM 的机器人 / Agent | LLM-based instant messaging bots platform, supports Discord, Telegram, WeChat, Lark, DingTalk, QQ, Slack"
https://github.com/chathub-dev/chathub,AI构建与集成,AI接口工具,10241,chathub-dev/chathub,All-in-one chatbot client
https://github.com/microsoft/promptflow,AI构建与集成,AI开发框架,10188,microsoft/promptflow,"Build high-quality LLM apps - from prototyping, testing to production deployment and monitoring."
https://github.com/tracel-ai/burn,模型开发与研究,训练与微调,10178,tracel-ai/burn,"Burn is a new comprehensive dynamic Deep Learning Framework built using Rust with extreme flexibility, compute efficiency and portability as its primary goals."
https://github.com/mistralai/mistral-src,模型开发与研究,模型库/仓库,10152,mistralai/mistral-src,Official inference library for Mistral models
https://github.com/sfyc23/EverydayWechat,AI实用工具,聊天机器人,10132,sfyc23/EverydayWechat,微信助手：1.每日定时给好友（女友）发送定制消息。2.机器人自动回复好友。3.群助手功能（例如：查询垃圾分类、天气、日历、电影实时票房、快递物流、PM2.5等）
https://github.com/AIGC-Audio/AudioGPT,模型开发与研究,模型库/仓库,10124,AIGC-Audio/AudioGPT,"AudioGPT: Understanding and Generating Speech, Music, Sound, and Talking Head"
https://github.com/langfuse/langfuse,AI部署与基础设施,监控与可观测性,10123,langfuse/langfuse,"🪢 Open source LLM engineering platform: LLM Observability, metrics, evals, prompt management, playground, datasets. Integrates with OpenTelemetry, Langchain, OpenAI SDK, LiteLLM, and more. 🍊YC W23"
https://github.com/NVIDIA/TensorRT-LLM,模型开发与研究,推理优化,10107,NVIDIA/TensorRT-LLM,TensorRT-LLM provides users with an easy-to-use Python API to define Large Language Models (LLMs) and build TensorRT engines that contain state-of-the-art optimizations to perform inference efficiently on NVIDIA GPUs. TensorRT-LLM also contains components to create Python and C++ runtimes that execute those TensorRT engines.
https://github.com/nerfstudio-project/nerfstudio,模型开发与研究,训练与微调,10053,nerfstudio-project/nerfstudio,A collaboration friendly studio for NeRFs
https://github.com/GoogleCloudPlatform/generative-ai,AI学习与资源,教程与课程,10031,GoogleCloudPlatform/generative-ai,"Sample code and notebooks for Generative AI on Google Cloud, with Gemini on Vertex AI"
https://github.com/jxnl/instructor,AI构建与集成,提示工程工具,10016,jxnl/instructor,structured outputs for llms
https://github.com/huggingface/text-generation-inference,AI部署与基础设施,模型部署工具,9971,huggingface/text-generation-inference,Large Language Model Text Generation Inference
https://github.com/easydiffusion/easydiffusion,AI实用工具,图像创作工具,9873,easydiffusion/easydiffusion,"An easy 1-click way to create beautiful artwork on your PC using AI, with no tech knowledge. Provides a browser UI for generating images from text prompts and images. Just enter your text prompt, and see the generated image."
https://github.com/google-deepmind/sonnet,模型开发与研究,训练与微调,9836,google-deepmind/sonnet,TensorFlow-based neural network library
https://github.com/sashabaranov/go-openai,AI构建与集成,AI接口工具,9811,sashabaranov/go-openai,"OpenAI ChatGPT, GPT-3, GPT-4, DALL·E, Whisper API wrapper for Go"
https://github.com/Mooler0410/LLMsPracticalGuide,AI学习与资源,教程与课程,9811,Mooler0410/LLMsPracticalGuide,"A curated list of practical guide resources of LLMs (LLMs Tree, Examples, Papers)"
https://github.com/mozilla/TTS,模型开发与研究,训练与微调,9760,mozilla/TTS,:robot: :speech_balloon: Deep learning for Text to Speech  (Discussion forum: https://discourse.mozilla.org/c/tts)
https://github.com/huggingface/tokenizers,模型开发与研究,数据工程,9560,huggingface/tokenizers,💥 Fast State-of-the-Art Tokenizers optimized for Research and Production
https://github.com/karpathy/minbpe,模型开发与研究,数据工程,9555,karpathy/minbpe,"Minimal, clean code for the Byte Pair Encoding (BPE) algorithm commonly used in LLM tokenization."
https://github.com/bigscience-workshop/petals,模型开发与研究,推理优化,9543,bigscience-workshop/petals,"🌸 Run LLMs at home, BitTorrent-style. Fine-tuning and inference up to 10x faster than offloading"
https://github.com/OthersideAI/self-operating-computer,AI实用工具,工作流自动化,9524,OthersideAI/self-operating-computer,A framework to enable multimodal models to operate a computer.
https://github.com/mshumer/gpt-prompt-engineer,AI构建与集成,提示工程工具,9495,mshumer/gpt-prompt-engineer,
https://github.com/BlinkDL/ChatRWKV,AI实用工具,聊天机器人,9469,BlinkDL/ChatRWKV,"ChatRWKV is like ChatGPT but powered by RWKV (100% RNN) language model, and open source."
https://github.com/facebookresearch/nougat,AI实用工具,信息聚合工具,9377,facebookresearch/nougat,Implementation of Nougat Neural Optical Understanding for Academic Documents
https://github.com/FMInference/FlexGen,AI部署与基础设施,模型部署工具,9296,FMInference/FlexGen,Running large language models on a single GPU for throughput-oriented scenarios.
https://github.com/facebookresearch/xformers,模型开发与研究,训练与微调,9280,facebookresearch/xformers,"Hackable and optimized Transformers building blocks, supporting a composable construction."
https://github.com/FlagOpen/FlagEmbedding,模型开发与研究,训练与微调,9228,FlagOpen/FlagEmbedding,Retrieval and Retrieval-augmented LLMs
https://github.com/facebookresearch/pytorch3d,模型开发与研究,训练与微调,9165,facebookresearch/pytorch3d,PyTorch3D is FAIR's library of reusable components for deep learning with 3D data
https://github.com/Chainlit/chainlit,AI构建与集成,AI开发框架,9145,Chainlit/chainlit,Build Conversational AI in minutes ⚡️
https://github.com/lucidrains/denoising-diffusion-pytorch,模型开发与研究,模型库/仓库,9142,lucidrains/denoising-diffusion-pytorch,Implementation of Denoising Diffusion Probabilistic Model in Pytorch
https://github.com/dotnet/machinelearning,模型开发与研究,训练与微调,9137,dotnet/machinelearning,ML.NET is an open source and cross-platform machine learning framework for .NET.
https://github.com/triton-inference-server/server,AI部署与基础设施,模型部署工具,9020,triton-inference-server/server,The Triton Inference Server provides an optimized cloud and edge inferencing solution.
https://github.com/OpenAccess-AI-Collective/axolotl,模型开发与研究,训练与微调,9012,OpenAccess-AI-Collective/axolotl,Go ahead and axolotl questions
https://github.com/Stability-AI/StableStudio,AI实用工具,图像创作工具,8967,Stability-AI/StableStudio,Community interface for generative AI
https://github.com/openai/openai-node,AI构建与集成,AI接口工具,8937,openai/openai-node,Official JavaScript / TypeScript library for the OpenAI API
https://github.com/open-mmlab/Amphion,模型开发与研究,训练与微调,8897,open-mmlab/Amphion,"Amphion (/æmˈfaɪən/) is a toolkit for Audio, Music, and Speech Generation. Its purpose is to support reproducible research and help junior researchers and engineers get started in the field of audio, music, and speech generation research and development."
https://github.com/arogozhnikov/einops,AI部署与基础设施,开发工具集成,8829,arogozhnikov/einops,"Flexible and powerful tensor operations for readable and reliable code (for pytorch, jax, TF and others)"
https://github.com/LouisShark/chatgpt_system_prompt,AI学习与资源,资源集合,8763,LouisShark/chatgpt_system_prompt,A collection of GPT system prompts and various prompt injection/leaking knowledge.
https://github.com/brexhq/prompt-engineering,AI学习与资源,教程与课程,8723,brexhq/prompt-engineering,Tips and tricks for working with Large Language Models like OpenAI's GPT-4.
https://github.com/Netflix/metaflow,AI部署与基础设施,计算资源管理,8697,Netflix/metaflow,"Build, Manage and Deploy AI/ML Systems"
https://github.com/explodinggradients/ragas,AI构建与集成,评估工具,8684,explodinggradients/ragas,Supercharge Your LLM Application Evaluations 🚀
https://github.com/nashsu/FreeAskInternet,AI实用工具,信息聚合工具,8610,nashsu/FreeAskInternet,"FreeAskInternet is a completely free, PRIVATE and LOCALLY running search aggregator & answer generate using MULTI LLMs, without GPU needed. The user can ask a question and the system will  make a multi engine search and combine the search result to LLM and generate the answer based on search results. It's all FREE to use."
https://github.com/huggingface/accelerate,模型开发与研究,训练与微调,8583,huggingface/accelerate,"🚀 A simple way to launch, train, and use PyTorch models on almost any device and distributed configuration, automatic mixed precision (including fp8), and easy-to-configure FSDP and DeepSpeed support"
https://github.com/ashawkey/stable-dreamfusion,模型开发与研究,模型库/仓库,8568,ashawkey/stable-dreamfusion,Text-to-3D & Image-to-3D & Mesh Exportation with NeRF + Diffusion.
https://github.com/replicate/cog,AI部署与基础设施,开发工具集成,8523,replicate/cog,Containers for machine learning
https://github.com/EleutherAI/lm-evaluation-harness,AI构建与集成,评估工具,8520,EleutherAI/lm-evaluation-harness,A framework for few-shot evaluation of language models.
https://github.com/activeloopai/deeplake,AI部署与基础设施,向量数据库,8510,activeloopai/deeplake,"Database for AI. Store Vectors, Images, Texts, Videos, etc. Use with LLMs/LangChain. Store, query, version, & visualize any AI data. Stream data in real-time to PyTorch/TensorFlow. https://activeloop.ai"
https://github.com/THUDM/CodeGeeX,模型开发与研究,模型库/仓库,8446,THUDM/CodeGeeX,CodeGeeX: An Open Multilingual Code Generation Model (KDD 2023)
https://github.com/microsoft/TypeChat,AI构建与集成,提示工程工具,8413,microsoft/TypeChat,TypeChat is a library that makes it easy to build natural language interfaces using types.
https://github.com/OptimalScale/LMFlow,模型开发与研究,训练与微调,8389,OptimalScale/LMFlow,An Extensible Toolkit for Finetuning and Inference of Large Foundation Models. Large Models for All.
https://github.com/yihong0618/bilingual_book_maker,AI实用工具,工作流自动化,8369,yihong0618/bilingual_book_maker,Make bilingual epub books Using AI translate
https://github.com/ztjhz/BetterChatGPT,AI实用工具,聊天机器人,8361,ztjhz/BetterChatGPT,An amazing UI for OpenAI's ChatGPT (Website + Windows + MacOS + Linux)
https://github.com/Nutlope/aicommits,AI实用工具,编程助手,8341,Nutlope/aicommits,A CLI that writes your git commit messages for you with AI
https://github.com/a16z-infra/ai-town,AI实用工具,聊天机器人,8302,a16z-infra/ai-town,"A MIT-licensed, deployable starter kit for building and customizing your own version of AI town - a virtual town where AI characters live, chat and socialize."
https://github.com/OpenBMB/XAgent,AI构建与集成,AI代理系统,8283,OpenBMB/XAgent,An Autonomous LLM Agent for Complex Task Solving
https://github.com/Vaibhavs10/insanely-fast-whisper,模型开发与研究,模型库/仓库,8277,Vaibhavs10/insanely-fast-whisper,
https://github.com/lucidrains/imagen-pytorch,模型开发与研究,模型库/仓库,8226,lucidrains/imagen-pytorch,"Implementation of Imagen, Google's Text-to-Image Neural Network, in Pytorch"
https://github.com/dusty-nv/jetson-inference,AI部署与基础设施,模型部署工具,8209,dusty-nv/jetson-inference,Hello AI World guide to deploying deep-learning inference networks and deep vision primitives with TensorRT and NVIDIA Jetson.
https://github.com/kyrolabs/awesome-langchain,AI学习与资源,教程与课程,8188,kyrolabs/awesome-langchain,😎 Awesome list of tools and projects with the awesome LangChain framework
https://github.com/Deeptrain-Community/chatnio,AI构建与集成,AI开发框架,8174,Deeptrain-Community/chatnio,🚀 Next Generation AI One-Stop Internationalization Solution. 🚀 下一代 AI 一站式 B/C 端解决方案，支持 OpenAI，Midjourney，Claude，讯飞星火，Stable Diffusion，DALL·E，ChatGLM，通义千问，腾讯混元，360 智脑，百川 AI，火山方舟，新必应，Gemini，Moonshot 等模型，支持对话分享，自定义预设，云端同步，模型市场，支持弹性计费和订阅计划模式，支持图片解析，支持联网搜索，支持模型缓存，丰富美观的后台管理与仪表盘数据统计。
https://github.com/pydantic/pydantic-ai,AI构建与集成,提示工程工具,8136,pydantic/pydantic-ai,Agent Framework / shim to use Pydantic with LLMs
https://github.com/LianjiaTech/BELLE,模型开发与研究,训练与微调,8108,LianjiaTech/BELLE,BELLE: Be Everyone's Large Language model Engine（开源中文对话大模型）
https://github.com/steven2358/awesome-generative-ai,AI学习与资源,资源集合,8080,steven2358/awesome-generative-ai,A curated list of modern Generative Artificial Intelligence projects and services
https://github.com/GreyDGL/PentestGPT,AI实用工具,编程助手,8075,GreyDGL/PentestGPT,A GPT-empowered penetration testing tool
https://github.com/openvinotoolkit/openvino,AI部署与基础设施,模型部署工具,8075,openvinotoolkit/openvino,OpenVINO™ is an open source toolkit for optimizing and deploying AI inference
https://github.com/leptonai/search_with_lepton,AI构建与集成,AI开发框架,8074,leptonai/search_with_lepton,Building a quick conversation-based search demo with Lepton AI.
https://github.com/sebastianstarke/AI4Animation,AI构建与集成,AI接口工具,8070,sebastianstarke/AI4Animation,Bringing Characters to Life with Computer Brains in Unity
https://github.com/anse-app/chatgpt-demo,AI实用工具,聊天机器人,8019,anse-app/chatgpt-demo,Minimal web UI for ChatGPT.
https://github.com/dataelement/bisheng,AI构建与集成,AI开发框架,7975,dataelement/bisheng,"BISHENG is an open LLM devops platform for next generation Enterprise AI applications. Powerful and comprehensive features include: GenAI workflow, RAG, Agent, Unified model management, Evaluation, SFT, Dataset Management, Enterprise-level System Management, Observability and more."
https://github.com/carson-katri/dream-textures,AI实用工具,图像创作工具,7974,carson-katri/dream-textures,Stable Diffusion built-in to Blender
https://github.com/openai/jukebox,模型开发与研究,模型库/仓库,7964,openai/jukebox,"Code for the paper ""Jukebox: A Generative Model for Music"""
https://github.com/e2b-dev/e2b,AI构建与集成,AI代理系统,7897,e2b-dev/e2b,Secure open source cloud runtime for AI apps & AI agents
https://github.com/netease-youdao/EmotiVoice,模型开发与研究,模型库/仓库,7825,netease-youdao/EmotiVoice,EmotiVoice 😊: a Multi-Voice and Prompt-Controlled TTS Engine
https://github.com/oumi-ai/oumi,模型开发与研究,训练与微调,7817,oumi-ai/oumi,"Everything you need to build state-of-the-art foundation models, end-to-end."
https://github.com/reorproject/reor,AI实用工具,文本创作工具,7781,reorproject/reor,Private & local AI personal knowledge management app for high entropy people.
https://github.com/deep-floyd/IF,模型开发与研究,模型库/仓库,7778,deep-floyd/IF,
https://github.com/IDEA-Research/GroundingDINO,模型开发与研究,模型库/仓库,7777,IDEA-Research/GroundingDINO,"[ECCV 2024] Official implementation of the paper ""Grounding DINO: Marrying DINO with Grounded Pre-Training for Open-Set Object Detection"""
https://github.com/lucidrains/PaLM-rlhf-pytorch,模型开发与研究,模型库/仓库,7776,lucidrains/PaLM-rlhf-pytorch,Implementation of RLHF (Reinforcement Learning with Human Feedback) on top of the PaLM architecture. Basically ChatGPT but with PaLM
https://github.com/intel-analytics/ipex-llm,模型开发与研究,推理优化,7699,intel-analytics/ipex-llm,"Accelerate local LLM inference and finetuning (LLaMA, Mistral, ChatGLM, Qwen, DeepSeek, Mixtral, Gemma, Phi, MiniCPM, Qwen-VL, MiniCPM-V, etc.) on Intel XPU (e.g., local PC with iGPU and NPU, discrete GPU such as Arc, Flex and Max); seamlessly integrate with llama.cpp, Ollama, HuggingFace, LangChain, LlamaIndex, vLLM, DeepSpeed, Axolotl, etc."
https://github.com/intel-analytics/BigDL,模型开发与研究,推理优化,7699,intel-analytics/BigDL,"Accelerate local LLM inference and finetuning (LLaMA, Mistral, ChatGLM, Qwen, DeepSeek, Mixtral, Gemma, Phi, MiniCPM, Qwen-VL, MiniCPM-V, etc.) on Intel XPU (e.g., local PC with iGPU and NPU, discrete GPU such as Arc, Flex and Max); seamlessly integrate with llama.cpp, Ollama, HuggingFace, LangChain, LlamaIndex, vLLM, DeepSpeed, Axolotl, etc."
https://github.com/TheLastBen/fast-stable-diffusion,AI实用工具,图像创作工具,7695,TheLastBen/fast-stable-diffusion,fast-stable-diffusion + DreamBooth
https://github.com/modelscope/modelscope,AI构建与集成,AI开发框架,7656,modelscope/modelscope,ModelScope: bring the notion of Model-as-a-Service to life.
https://github.com/godly-devotion/MochiDiffusion,AI实用工具,图像创作工具,7655,godly-devotion/MochiDiffusion,Run Stable Diffusion on Mac natively
https://github.com/memochou1993/gpt-ai-assistant,AI实用工具,聊天机器人,7620,memochou1993/gpt-ai-assistant,OpenAI + LINE + Vercel = GPT AI Assistant
https://github.com/HumanAIGC/EMO,模型开发与研究,模型库/仓库,7612,HumanAIGC/EMO,Emote Portrait Alive: Generating Expressive Portrait Videos with Audio2Video Diffusion Model under Weak Conditions
https://github.com/skypilot-org/skypilot,AI部署与基础设施,计算资源管理,7611,skypilot-org/skypilot,"SkyPilot: Run AI and batch jobs on any infra (Kubernetes or 16+ clouds). Get unified execution, cost savings, and high GPU availability via a simple interface."
https://github.com/Portkey-AI/gateway,AI部署与基础设施,模型部署工具,7575,Portkey-AI/gateway,"A blazing fast AI Gateway with integrated guardrails. Route to 200+ LLMs, 50+ AI Guardrails with 1 fast & friendly API."
https://github.com/sweepai/sweep,AI实用工具,编程助手,7538,sweepai/sweep,Sweep: AI coding assistant for JetBrains
https://github.com/zilliztech/GPTCache,AI部署与基础设施,模型部署工具,7495,zilliztech/GPTCache,Semantic cache for LLMs. Fully integrated with LangChain and llama_index.
https://github.com/openlm-research/open_llama,模型开发与研究,模型库/仓库,7470,openlm-research/open_llama,"OpenLLaMA, a permissively licensed open source reproduction of Meta AI’s LLaMA 7B trained on the RedPajama dataset"
https://github.com/LiheYoung/Depth-Anything,模型开发与研究,模型库/仓库,7430,LiheYoung/Depth-Anything,[CVPR 2024] Depth Anything: Unleashing the Power of Large-Scale Unlabeled Data. Foundation Model for Monocular Depth Estimation
https://github.com/xorbitsai/inference,AI部署与基础设施,模型部署工具,7377,xorbitsai/inference,"Replace OpenAI GPT with another LLM in your app by changing a single line of code. Xinference gives you the freedom to use any LLM you need. With Xinference, you're empowered to run inference with any open-source language models, speech recognition models, and multimodal models, whether in the cloud, on-premises, or even on your laptop."
https://github.com/WooooDyy/LLM-Agent-Paper-List,AI学习与资源,资源集合,7373,WooooDyy/LLM-Agent-Paper-List,"The paper list of the 86-page paper ""The Rise and Potential of Large Language Model Based Agents: A Survey"" by Zhiheng Xi et al."
https://github.com/Canner/WrenAI,AI实用工具,编程助手,7364,Canner/WrenAI,"🤖 Open-source GenBI AI Agent that empowers data-driven teams to chat with their data to generate Text-to-SQL, charts, spreadsheets, reports, dashboards and BI. 📈📊📋🧑‍💻"
https://github.com/FoundationVision/VAR,模型开发与研究,模型库/仓库,7341,FoundationVision/VAR,"[NeurIPS 2024 Best Paper][GPT beats diffusion🔥] [scaling laws in visual generation📈] Official impl. of ""Visual Autoregressive Modeling: Scalable Image Generation via Next-Scale Prediction"". An *ultra-simple, user-friendly yet state-of-the-art* codebase for autoregressive image generation!"
https://github.com/jessevig/bertviz,未分类,未分类,7294,jessevig/bertviz,"BertViz: Visualize Attention in NLP Models (BERT, GPT2, BART, etc.)"
https://github.com/OpenBMB/MiniCPM,模型开发与研究,模型库/仓库,7293,OpenBMB/MiniCPM,MiniCPM3-4B: An edge-side LLM that surpasses GPT-3.5-Turbo.
https://github.com/cloneofsimo/lora,模型开发与研究,训练与微调,7287,cloneofsimo/lora,Using Low-rank adaptation to quickly fine-tune diffusion models.
https://github.com/miurla/morphic,AI实用工具,聊天机器人,7270,miurla/morphic,An AI-powered search engine with a generative UI
https://github.com/LargeWorldModel/LWM,模型开发与研究,模型库/仓库,7266,LargeWorldModel/LWM,Large World Model -- Modeling Text and Video with Millions Context
https://github.com/NVIDIA/cutlass,模型开发与研究,推理优化,7233,NVIDIA/cutlass,CUDA Templates for Linear Algebra Subroutines
https://github.com/google-deepmind/lab,模型开发与研究,训练与微调,7201,google-deepmind/lab,A customisable 3D platform for agent-based AI research
https://github.com/ymcui/Chinese-LLaMA-Alpaca-2,模型开发与研究,模型库/仓库,7156,ymcui/Chinese-LLaMA-Alpaca-2,中文LLaMA-2 & Alpaca-2大模型二期项目 + 64K超长上下文模型 (Chinese LLaMA-2 & Alpaca-2 LLMs with 64K long context models)
https://github.com/EleutherAI/gpt-neox,模型开发与研究,训练与微调,7154,EleutherAI/gpt-neox,"An implementation of model parallel autoregressive transformers on GPUs, based on the Megatron and DeepSpeed libraries"
https://github.com/open-mmlab/mmagic,模型开发与研究,训练与微调,7125,open-mmlab/mmagic,"OpenMMLab Multimodal Advanced, Generative, and Intelligent Creation Toolbox. Unlock the magic 🪄: Generative-AI (AIGC), easy-to-use APIs, awsome model zoo, diffusion models, for text-to-image generation, image/video restoration/enhancement, etc."
https://github.com/bhaskatripathi/pdfGPT,AI实用工具,信息聚合工具,7099,bhaskatripathi/pdfGPT,PDF GPT allows you to chat with the contents of your PDF file by using GPT capabilities. The most effective open source solution to turn your pdf files in a chatbot!
https://github.com/AbdBarho/stable-diffusion-webui-docker,AI部署与基础设施,开发工具集成,7096,AbdBarho/stable-diffusion-webui-docker,Easy Docker setup for Stable Diffusion with user-friendly UI
https://github.com/weaviate/Verba,AI实用工具,信息聚合工具,7003,weaviate/Verba,Retrieval Augmented Generation (RAG) chatbot powered by Weaviate
https://github.com/simonw/llm,AI实用工具,聊天机器人,6897,simonw/llm,Access large language models from the command-line
https://github.com/modelscope/agentscope,AI构建与集成,AI代理系统,6895,modelscope/agentscope,Start building LLM-empowered multi-agent applications in an easier way.
https://github.com/TimDettmers/bitsandbytes,模型开发与研究,推理优化,6883,TimDettmers/bitsandbytes,Accessible large language models via k-bit quantization for PyTorch.
https://github.com/langchain4j/langchain4j,AI构建与集成,AI开发框架,6851,langchain4j/langchain4j,Java version of LangChain
https://github.com/mit-han-lab/streaming-llm,AI构建与集成,提示工程工具,6846,mit-han-lab/streaming-llm,[ICLR 2024] Efficient Streaming Language Models with Attention Sinks
https://github.com/deeppavlov/DeepPavlov,AI构建与集成,AI开发框架,6846,deeppavlov/DeepPavlov,An open source library for deep learning end-to-end dialog systems and chatbots.
https://github.com/InternLM/InternLM,模型开发与研究,模型库/仓库,6842,InternLM/InternLM,"Official release of InternLM series (InternLM, InternLM2, InternLM2.5, InternLM3)."
https://github.com/wzpan/wukong-robot,AI构建与集成,AI接口工具,6764,wzpan/wukong-robot,🤖 wukong-robot 是一个简单、灵活、优雅的中文语音对话机器人/智能音箱项目，支持ChatGPT多轮对话能力，还可能是首个支持脑机交互的开源智能音箱项目。
https://github.com/openai/guided-diffusion,模型开发与研究,模型库/仓库,6687,openai/guided-diffusion,
https://github.com/threestudio-project/threestudio,模型开发与研究,模型库/仓库,6686,threestudio-project/threestudio,A unified framework for 3D content generation.
https://github.com/openai/point-e,模型开发与研究,模型库/仓库,6683,openai/point-e,Point cloud diffusion for 3D model synthesis
https://github.com/microsoft/UFO,AI实用工具,工作流自动化,6661,microsoft/UFO,A UI-Focused Agent for Windows OS Interaction.
https://github.com/spdustin/ChatGPT-AutoExpert,AI构建与集成,提示工程工具,6660,spdustin/ChatGPT-AutoExpert,🚀🧠💬 Supercharged Custom Instructions for ChatGPT (non-coding) and ChatGPT Advanced Data Analysis (coding).
https://github.com/Maartengr/BERTopic,模型开发与研究,训练与微调,6635,Maartengr/BERTopic,Leveraging BERT and c-TF-IDF to create easily interpretable topics.
https://github.com/langchain-ai/opengpts,AI构建与集成,AI开发框架,6614,langchain-ai/opengpts,
https://github.com/KoljaB/RealtimeSTT,AI实用工具,聊天机器人,6584,KoljaB/RealtimeSTT,"A robust, efficient, low-latency speech-to-text library with advanced voice activity detection, wake word activation and instant transcription."
https://github.com/di-sukharev/opencommit,AI实用工具,编程助手,6559,di-sukharev/opencommit,GPT wrapper for git — generate commit messages with an LLM in 1 sec — works best with Claude 3.5 — supports local models too
https://github.com/civitai/civitai,AI构建与集成,提示工程工具,6527,civitai/civitai,"A repository of models, textual inversions, and more"
https://github.com/yihong0618/xiaogpt,AI构建与集成,AI接口工具,6519,yihong0618/xiaogpt,Play ChatGPT and other LLM with Xiaomi AI Speaker
https://github.com/interstellard/chatgpt-advanced,AI实用工具,聊天机器人,6491,interstellard/chatgpt-advanced,WebChatGPT: A browser extension that augments your ChatGPT prompts with web results.
https://github.com/linyiLYi/street-fighter-ai,AI实用工具,聊天机器人,6467,linyiLYi/street-fighter-ai,This is an AI agent for Street Fighter II Champion Edition.
https://github.com/frdel/agent-zero,AI构建与集成,AI代理系统,6463,frdel/agent-zero,Agent Zero AI framework
https://github.com/THUDM/CogVLM,模型开发与研究,模型库/仓库,6459,THUDM/CogVLM,a state-of-the-art-level open visual language model | 多模态预训练模型
https://github.com/run-llama/rags,AI实用工具,信息聚合工具,6445,run-llama/rags,"Build ChatGPT over your data, all with natural language"
https://github.com/QwenLM/Qwen-Agent,AI构建与集成,AI代理系统,6438,QwenLM/Qwen-Agent,"Agent framework and applications built upon Qwen>=2.0, featuring Function Calling, Code Interpreter, RAG, and Chrome extension."
https://github.com/microsoft/DeepSpeedExamples,AI学习与资源,教程与课程,6403,microsoft/DeepSpeedExamples,Example models using DeepSpeed
https://github.com/RayVentura/ShortGPT,AI实用工具,视频制作工具,6344,RayVentura/ShortGPT,🚀🎬 ShortGPT - Experimental AI framework for youtube shorts / tiktok channel automation
https://github.com/nat/openplayground,AI构建与集成,AI接口工具,6342,nat/openplayground,An LLM playground you can run on your laptop
https://github.com/google/gemma.cpp,模型开发与研究,推理优化,6334,google/gemma.cpp,"lightweight, standalone C++ inference engine for Google's Gemma models."
https://github.com/yangjianxin1/Firefly,模型开发与研究,训练与微调,6317,yangjianxin1/Firefly,Firefly: 大模型训练工具，支持训练Qwen2.5、Qwen2、Yi1.5、Phi-3、Llama3、Gemma、MiniCPM、Yi、Deepseek、Orion、Xverse、Mixtral-8x7B、Zephyr、Mistral、Baichuan2、Llma2、Llama、Qwen、Baichuan、ChatGLM2、InternLM、Ziya2、Vicuna、Bloom等大模型
https://github.com/google-research/text-to-text-transfer-transformer,模型开发与研究,模型库/仓库,6315,google-research/text-to-text-transfer-transformer,"Code for the paper ""Exploring the Limits of Transfer Learning with a Unified Text-to-Text Transformer"""
https://github.com/openai/consistency_models,模型开发与研究,模型库/仓库,6306,openai/consistency_models,Official repo for consistency models.
https://github.com/sigoden/aichat,AI实用工具,聊天机器人,6293,sigoden/aichat,"All-in-one LLM CLI tool featuring Shell Assistant, Chat-REPL, RAG, AI Tools & Agents, with access to OpenAI, Claude, Gemini, Ollama, Groq, and more."
https://github.com/Project-MONAI/MONAI,模型开发与研究,训练与微调,6282,Project-MONAI/MONAI,AI Toolkit for Healthcare Imaging
https://github.com/deepseek-ai/DeepSeek-LLM,模型开发与研究,模型库/仓库,6272,deepseek-ai/DeepSeek-LLM,DeepSeek LLM: Let there be answers
https://github.com/tensorflow/serving,AI部署与基础设施,模型部署工具,6260,tensorflow/serving,"A flexible, high-performance serving system for machine learning models"
https://github.com/postgresml/postgresml,AI构建与集成,AI接口工具,6221,postgresml/postgresml,Postgres with GPUs for ML/AI apps.
https://github.com/SciPhi-AI/R2R,AI构建与集成,AI开发框架,6198,SciPhi-AI/R2R,SoTA production-ready AI retrieval system. Agentic Retrieval-Augmented Generation (RAG) with a RESTful API.
https://github.com/Moonvy/OpenPromptStudio,AI构建与集成,提示工程工具,6195,Moonvy/OpenPromptStudio,🥣 AIGC 提示词可视化编辑器  | OPS | Open Prompt Studio
https://github.com/volcengine/verl,模型开发与研究,训练与微调,6190,volcengine/verl,verl: Volcano Engine Reinforcement Learning for LLMs
https://github.com/levihsu/OOTDiffusion,模型开发与研究,模型库/仓库,6188,levihsu/OOTDiffusion,"[AAAI 2025] Official implementation of ""OOTDiffusion: Outfitting Fusion based Latent Diffusion for Controllable Virtual Try-on"""
https://github.com/rockbenben/ChatGPT-Shortcut,AI构建与集成,提示工程工具,6166,rockbenben/ChatGPT-Shortcut,"🚀💪Maximize your efficiency and productivity, support for English,中文,Español,العربية. 让生产力加倍的AI快捷指令。更有效地定制、保存和分享自己的提示词。在提示词分享社区中，轻松找到适用于不同场景的指令。"
https://github.com/clovaai/donut,模型开发与研究,模型库/仓库,6141,clovaai/donut,"Official Implementation of OCR-free Document Understanding Transformer (Donut) and Synthetic Document Generator (SynthDoG), ECCV 2022"
https://github.com/n4ze3m/page-assist,AI实用工具,工作流自动化,6139,n4ze3m/page-assist,Use your locally running AI models to assist you in your web browsing
https://github.com/microsoft/TinyTroupe,未分类,未分类,6129,microsoft/TinyTroupe,LLM-powered multiagent persona simulation for imagination enhancement and business insights.
https://github.com/Shaunwei/RealChar,AI构建与集成,AI接口工具,6126,Shaunwei/RealChar,"🎙️🤖Create, Customize and Talk to your AI Character/Companion in Realtime (All in One Codebase!). Have a natural seamless conversation with AI everywhere (mobile, web and terminal) using LLM OpenAI GPT3.5/4, Anthropic Claude2, Chroma Vector DB, Whisper Speech2Text, ElevenLabs Text2Speech🎙️🤖"
https://github.com/simplescaling/s1,模型开发与研究,数据工程,6119,simplescaling/s1,s1: Simple test-time scaling
https://github.com/OpenLLMAI/OpenRLHF,模型开发与研究,训练与微调,6102,OpenLLMAI/OpenRLHF,"An Easy-to-use, Scalable and High-performance RLHF Framework (70B+ PPO Full Tuning & Iterative DPO & LoRA & RingAttention & RFT)"
https://github.com/vespa-engine/vespa,AI部署与基础设施,向量数据库,6101,vespa-engine/vespa,"AI + Data, online. https://vespa.ai"
https://github.com/rustformers/llm,AI构建与集成,AI开发框架,6100,rustformers/llm,"[Unmaintained, see README] An ecosystem of Rust libraries for working with large language models"
https://github.com/MuiseDestiny/zotero-gpt,AI实用工具,信息聚合工具,6095,MuiseDestiny/zotero-gpt,GPT Meet Zotero.
https://github.com/promptfoo/promptfoo,AI构建与集成,提示工程工具,6094,promptfoo/promptfoo,"Test your prompts, agents, and RAGs. Red teaming, pentesting, and vulnerability scanning for LLMs. Compare performance of GPT, Claude, Gemini, Llama, and more. Simple declarative configs with command line and CI/CD integration."
https://github.com/Lightning-AI/lit-llama,模型开发与研究,模型库/仓库,6044,Lightning-AI/lit-llama,"Implementation of the LLaMA language model based on nanoGPT. Supports flash attention, Int8 and GPTQ 4bit quantization, LoRA and LLaMA-Adapter fine-tuning, pre-training. Apache 2.0-licensed."
https://github.com/wgwang/LLMs-In-China,AI学习与资源,资源集合,6040,wgwang/LLMs-In-China,中国大模型
https://github.com/lancedb/lancedb,AI部署与基础设施,向量数据库,6037,lancedb/lancedb,"Developer-friendly, embedded retrieval engine for multimodal AI. Search More; Manage Less."
https://github.com/MineDojo/Voyager,AI实用工具,聊天机器人,6016,MineDojo/Voyager,An Open-Ended Embodied Agent with Large Language Models
https://github.com/lavague-ai/LaVague,AI实用工具,工作流自动化,6000,lavague-ai/LaVague,Large Action Model framework to develop AI Web Agents
https://github.com/InternLM/lmdeploy,AI部署与基础设施,模型部署工具,5999,InternLM/lmdeploy,"LMDeploy is a toolkit for compressing, deploying, and serving LLMs."
https://github.com/evidentlyai/evidently,AI部署与基础设施,监控与可观测性,5972,evidentlyai/evidently,"Evidently is ​​an open-source ML and LLM observability framework. Evaluate, test, and monitor any AI-powered system or data pipeline. From tabular data to Gen AI. 100+ metrics."
https://github.com/google-deepmind/graphcast,模型开发与研究,训练与微调,5950,google-deepmind/graphcast,
https://github.com/QuivrHQ/MegaParse,未分类,未分类,5929,QuivrHQ/MegaParse,"File Parser optimised for LLM Ingestion with no loss 🧠 Parse PDFs, Docx, PPTx in a format that is ideal for LLMs."
https://github.com/arsenetar/dupeguru,未分类,未分类,5920,arsenetar/dupeguru,Find duplicate files
https://github.com/allegroai/clearml,AI部署与基础设施,模型部署工具,5920,allegroai/clearml,"ClearML - Auto-Magical CI/CD to streamline your AI workload. Experiment Management, Data Management, Pipeline, Orchestration, Scheduling & Serving in one MLOps/LLMOps solution"
https://github.com/pytorch-labs/gpt-fast,模型开发与研究,推理优化,5905,pytorch-labs/gpt-fast,Simple and efficient pytorch-native transformer text generation in <1000 LOC of python.
https://github.com/shibing624/pycorrector,AI实用工具,文本创作工具,5898,shibing624/pycorrector,pycorrector is a toolkit for text error correction. 文本纠错，实现了Kenlm，T5，MacBERT，ChatGLM3，Qwen2.5等模型应用在纠错场景，开箱即用。
https://github.com/aisingapore/TagUI,AI实用工具,工作流自动化,5886,aisingapore/TagUI,Free RPA tool by AI Singapore
https://github.com/kuafuai/DevOpsGPT,AI实用工具,编程助手,5879,kuafuai/DevOpsGPT,Multi agent system for AI-driven software development. Combine LLM with DevOps tools to convert natural language requirements into working software. Supports any development language and extends the existing code.
https://github.com/OpenGVLab/LLaMA-Adapter,模型开发与研究,模型库/仓库,5850,OpenGVLab/LLaMA-Adapter,[ICLR 2024] Fine-tuning LLaMA to follow Instructions within 1 Hour and 1.2M Parameters
https://github.com/confident-ai/deepeval,AI构建与集成,评估工具,5846,confident-ai/deepeval,The LLM Evaluation Framework
https://github.com/snorkel-team/snorkel,模型开发与研究,数据工程,5844,snorkel-team/snorkel,A system for quickly generating training data with weak supervision
https://github.com/Nutlope/llamacoder,AI实用工具,编程助手,5834,Nutlope/llamacoder,Open source Claude Artifacts – built with Llama 3.1 405B
https://github.com/HumanAIGC/OutfitAnyone,AI实用工具,图像创作工具,5824,HumanAIGC/OutfitAnyone,Outfit Anyone: Ultra-high quality virtual try-on for Any Clothing and Any Person
https://github.com/a16z-infra/companion-app,AI实用工具,聊天机器人,5824,a16z-infra/companion-app,AI companions with memory: a lightweight stack to create and host your own AI companions
https://github.com/josStorer/RWKV-Runner,AI构建与集成,AI开发框架,5765,josStorer/RWKV-Runner,"A RWKV management and startup tool, full automation, only 8MB. And provides an interface compatible with the OpenAI API. RWKV is a large language model that is fully open source and available for commercial use."
https://github.com/santinic/how2,AI实用工具,编程助手,5745,santinic/how2,AI for the Command Line
https://github.com/QwenLM/Qwen-VL,模型开发与研究,模型库/仓库,5735,QwenLM/Qwen-VL,The official repo of Qwen-VL (通义千问-VL) chat & pretrained large vision language model proposed by Alibaba Cloud.
https://github.com/homanp/superagent,AI构建与集成,AI代理系统,5725,homanp/superagent,🥷 Run AI-agents with an API
https://github.com/ibis-project/ibis,AI部署与基础设施,数据管理平台,5667,ibis-project/ibis,the portable Python dataframe library
https://github.com/yl4579/StyleTTS2,模型开发与研究,模型库/仓库,5611,yl4579/StyleTTS2,StyleTTS 2: Towards Human-Level Text-to-Speech through Style Diffusion and Adversarial Training with Large Speech Language Models
https://github.com/traceloop/openllmetry,AI部署与基础设施,监控与可观测性,5610,traceloop/openllmetry,"Open-source observability for your LLM application, based on OpenTelemetry"
https://github.com/PrefectHQ/marvin,AI构建与集成,提示工程工具,5598,PrefectHQ/marvin,✨ AI agents that spark joy
https://github.com/microsoft/promptbase,AI学习与资源,教程与课程,5590,microsoft/promptbase,All things prompt engineering
https://github.com/ConnectAI-E/Feishu-OpenAI,AI实用工具,聊天机器人,5570,ConnectAI-E/Feishu-OpenAI,🎒 飞书  ×（GPT-4 + GPT-4V + DALL·E-3 + Whisper）=  飞一般的工作体验  🚀 语音对话、角色扮演、多话题讨论、图片创作、表格分析、文档导出 🚀
https://github.com/deepseek-ai/DeepSeek-Coder-V2,模型开发与研究,模型库/仓库,5569,deepseek-ai/DeepSeek-Coder-V2,DeepSeek-Coder-V2: Breaking the Barrier of Closed-Source Models in Code Intelligence
https://github.com/aiwaves-cn/agents,AI构建与集成,AI代理系统,5554,aiwaves-cn/agents,"An Open-source Framework for Data-centric, Self-evolving Autonomous Language Agents"
https://github.com/cg123/mergekit,模型开发与研究,训练与微调,5513,cg123/mergekit,Tools for merging pretrained large language models.
https://github.com/allenai/OLMo,模型开发与研究,模型库/仓库,5468,allenai/OLMo,"Modeling, training, eval, and inference code for OLMo"
https://github.com/livekit/agents,AI构建与集成,AI开发框架,5460,livekit/agents,A powerful framework for building realtime voice AI agents 🤖🎙️📹
https://github.com/google/gemma_pytorch,模型开发与研究,模型库/仓库,5413,google/gemma_pytorch,The official PyTorch implementation of Google's Gemma models
https://github.com/albertan017/LLM4Decompile,模型开发与研究,模型库/仓库,5389,albertan017/LLM4Decompile,Reverse Engineering: Decompiling Binary Code with Large Language Models
https://github.com/NVIDIA/DALI,模型开发与研究,模型库/仓库,5347,NVIDIA/DALI,A GPU-accelerated library containing highly optimized building blocks and an execution engine for data processing to accelerate deep learning training and inference applications.
https://github.com/EssayKillerBrain/WriteGPT,AI实用工具,文本创作工具,5335,EssayKillerBrain/WriteGPT,由图灵的猫开发，基于开源GPT2.0的初代创作型人工智能 | 可扩展、可进化
https://github.com/mosaicml/composer,模型开发与研究,训练与微调,5324,mosaicml/composer,Supercharge Your Model Training
https://github.com/imoneoi/openchat,模型开发与研究,模型库/仓库,5321,imoneoi/openchat,OpenChat: Advancing Open-source Language Models with Imperfect Data
https://github.com/nickscamara/open-deep-research,AI实用工具,信息聚合工具,5281,nickscamara/open-deep-research,An open source deep research clone. AI Agent that reasons large amounts of web data extracted with Firecrawl
https://github.com/Arize-ai/phoenix,AI构建与集成,评估工具,5277,Arize-ai/phoenix,AI Observability & Evaluation
https://github.com/openchatai/OpenChat,AI实用工具,工作流自动化,5233,openchatai/OpenChat,LLMs custom-chatbots console ⚡
https://github.com/snakers4/silero-models,模型开发与研究,模型库/仓库,5212,snakers4/silero-models,"Silero Models: pre-trained speech-to-text, text-to-speech and text-enhancement models made embarrassingly simple"
https://github.com/erikbern/ann-benchmarks,AI构建与集成,评估工具,5207,erikbern/ann-benchmarks,Benchmarks of approximate nearest neighbor libraries in Python
https://github.com/lonePatient/awesome-pretrained-chinese-nlp-models,AI学习与资源,资源集合,5204,lonePatient/awesome-pretrained-chinese-nlp-models,Awesome Pretrained Chinese NLP Models，高质量中文预训练模型&大模型&多模态模型&大语言模型集合
https://github.com/princeton-nlp/tree-of-thought-llm,AI构建与集成,提示工程工具,5201,princeton-nlp/tree-of-thought-llm,[NeurIPS 2023] Tree of Thoughts: Deliberate Problem Solving with Large Language Models
https://github.com/lucidrains/x-transformers,模型开发与研究,模型库/仓库,5188,lucidrains/x-transformers,A concise but complete full-attention transformer with a set of promising experimental features from various papers
https://github.com/Trusted-AI/adversarial-robustness-toolbox,AI构建与集成,评估工具,5169,Trusted-AI/adversarial-robustness-toolbox,"Adversarial Robustness Toolbox (ART) - Python Library for Machine Learning Security - Evasion, Poisoning, Extraction, Inference - Red and Blue Teams"
https://github.com/salesforce/BLIP,模型开发与研究,模型库/仓库,5154,salesforce/BLIP,PyTorch code for BLIP: Bootstrapping Language-Image Pre-training for Unified Vision-Language Understanding and Generation
https://github.com/huggingface/alignment-handbook,AI学习与资源,教程与课程,5111,huggingface/alignment-handbook,Robust recipes to align language models with human and AI preferences
https://github.com/open-compass/opencompass,AI构建与集成,评估工具,5098,open-compass/opencompass,"OpenCompass is an LLM evaluation platform, supporting a wide range of models (Llama3, Mistral, InternLM2,GPT-4,LLaMa2, Qwen,GLM, Claude, etc) over 100+ datasets."
https://github.com/TaskingAI/TaskingAI,AI构建与集成,AI开发框架,5095,TaskingAI/TaskingAI,The open source platform for AI-native application development.
https://github.com/pytorch/torchtune,模型开发与研究,训练与微调,5063,pytorch/torchtune,PyTorch native post-training library
https://github.com/sqlchat/sqlchat,AI实用工具,编程助手,5059,sqlchat/sqlchat,Chat-based SQL Client and Editor for the next decade
https://github.com/salesforce/CodeGen,模型开发与研究,模型库/仓库,5054,salesforce/CodeGen,CodeGen is a family of open-source model for program synthesis. Trained on TPU-v4. Competitive with OpenAI Codex.
https://github.com/KillianLucas/01,AI实用工具,工作流自动化,5054,KillianLucas/01,"The #1 open-source voice interface for desktop, mobile, and ESP32 chips."
https://github.com/SuperDuperDB/superduperdb,AI部署与基础设施,数据管理平台,5021,SuperDuperDB/superduperdb,Superduper: End-to-end framework for building custom AI applications and agents.
https://github.com/OpenGVLab/DragGAN,模型开发与研究,模型库/仓库,4988,OpenGVLab/DragGAN,"Unofficial Implementation of DragGAN - ""Drag Your GAN: Interactive Point-based Manipulation on the Generative Image Manifold"" （DragGAN 全功能实现，在线Demo，本地部署试用，代码、模型已全部开源，支持Windows, macOS, Linux）"
https://github.com/gpuweb/gpuweb,AI部署与基础设施,计算资源管理,4974,gpuweb/gpuweb,Where the GPU for the Web work happens!
https://github.com/OpenBMB/ToolBench,AI构建与集成,AI开发框架,4969,OpenBMB/ToolBench,"[ICLR'24 spotlight] An open platform for training, serving, and evaluating large language model for tool learning."
https://github.com/marqo-ai/marqo,AI部署与基础设施,向量数据库,4814,marqo-ai/marqo,Unified embedding generation and search engine. Also available on cloud - cloud.marqo.ai
https://github.com/PanQiWei/AutoGPTQ,模型开发与研究,推理优化,4787,PanQiWei/AutoGPTQ,"An easy-to-use LLMs quantization package with user-friendly apis, based on GPTQ algorithm."
https://github.com/QwenLM/Qwen2.5-Coder,模型开发与研究,模型库/仓库,4781,QwenLM/Qwen2.5-Coder,"Qwen2.5-Coder is the code version of Qwen2.5, the large language model series developed by Qwen team, Alibaba Cloud."
https://github.com/Exafunction/codeium.vim,AI实用工具,编程助手,4766,Exafunction/codeium.vim,"Free, ultrafast Copilot alternative for Vim and Neovim"
https://github.com/SCIR-HI/Huatuo-Llama-Med-Chinese,模型开发与研究,模型库/仓库,4732,SCIR-HI/Huatuo-Llama-Med-Chinese,"Repo for BenTsao [original name: HuaTuo (华驼)], Instruction-tuning Large Language Models with Chinese Medical Knowledge. 本草（原名：华驼）模型仓库，基于中文医学知识的大语言模型指令微调"
https://github.com/guardrails-ai/guardrails,AI构建与集成,提示工程工具,4729,guardrails-ai/guardrails,Adding guardrails to large language models.
https://github.com/togethercomputer/RedPajama-Data,模型开发与研究,数据工程,4690,togethercomputer/RedPajama-Data,The RedPajama-Data repository contains code for preparing large datasets for training large language models.
https://github.com/tensorchord/Awesome-LLMOps,AI学习与资源,资源集合,4676,tensorchord/Awesome-LLMOps,An awesome & curated list of best LLMOps tools for developers
https://github.com/1rgs/jsonformer,AI构建与集成,提示工程工具,4674,1rgs/jsonformer,A Bulletproof Way to Generate Structured JSON from Language Models
https://github.com/deepseek-ai/DeepSeek-VL2,模型开发与研究,训练与微调,4673,deepseek-ai/DeepSeek-VL2,DeepSeek-VL2: Mixture-of-Experts Vision-Language Models for Advanced Multimodal Understanding
https://github.com/timescale/pgai,AI构建与集成,AI开发框架,4630,timescale/pgai,"A suite of tools to develop RAG, semantic search, and other AI applications more easily with PostgreSQL"
https://github.com/nmslib/hnswlib,AI部署与基础设施,向量数据库,4620,nmslib/hnswlib,Header-only C++/python library for fast approximate nearest neighbors
https://github.com/CarperAI/trlx,模型开发与研究,训练与微调,4615,CarperAI/trlx,A repo for distributed training of language models with Reinforcement Learning via Human Feedback (RLHF)
https://github.com/ParisNeo/lollms-webui,AI构建与集成,AI接口工具,4600,ParisNeo/lollms-webui,Lord of Large Language and Multi modal Systems Web User Interface
https://github.com/NVIDIA/NeMo-Guardrails,AI构建与集成,评估工具,4599,NVIDIA/NeMo-Guardrails,NeMo Guardrails is an open-source toolkit for easily adding programmable guardrails to LLM-based conversational systems.
https://github.com/bbycroft/llm-viz,AI学习与资源,教程与课程,4595,bbycroft/llm-viz,3D Visualization of an GPT-style LLM
https://github.com/thunlp/OpenPrompt,AI构建与集成,提示工程工具,4532,thunlp/OpenPrompt,An Open-Source Framework for Prompt-Learning.
https://github.com/lk-geimfari/mimesis,未分类,未分类,4518,lk-geimfari/mimesis,Mimesis is a robust data generator for Python that can produce a wide range of fake data in multiple languages.
https://github.com/zenml-io/zenml,AI部署与基础设施,计算资源管理,4504,zenml-io/zenml,ZenML 🙏: The bridge between ML and Ops. https://zenml.io.
https://github.com/luosiallen/latent-consistency-model,模型开发与研究,模型库/仓库,4494,luosiallen/latent-consistency-model,Latent Consistency Models: Synthesizing High-Resolution Images with Few-Step Inference
https://github.com/landing-ai/vision-agent,AI构建与集成,AI代理系统,4474,landing-ai/vision-agent,Vision agent
https://github.com/google-deepmind/open_spiel,模型开发与研究,训练与微调,4466,google-deepmind/open_spiel,OpenSpiel is a collection of environments and algorithms for research in general reinforcement learning and search/planning in games.
https://github.com/argmaxinc/WhisperKit,AI部署与基础设施,模型部署工具,4457,argmaxinc/WhisperKit,On-device Speech Recognition for Apple Silicon
https://github.com/katanaml/sparrow,AI实用工具,工作流自动化,4455,katanaml/sparrow,"Data processing with ML, LLM and Vision LLM"
https://github.com/InternLM/xtuner,模型开发与研究,训练与微调,4452,InternLM/xtuner,"An efficient, flexible and full-featured toolkit for fine-tuning LLM (InternLM2, Llama3, Phi3, Qwen, Mistral, ...)"
https://github.com/OpenBMB/AgentVerse,AI构建与集成,AI代理系统,4452,OpenBMB/AgentVerse,"🤖 AgentVerse 🪐 is designed to facilitate the deployment of multiple LLM-based agents in various applications, which primarily provides two frameworks: task-solving and simulation"
https://github.com/Giskard-AI/giskard,AI构建与集成,评估工具,4445,Giskard-AI/giskard,🐢 Open-Source Evaluation & Testing for AI & LLM systems
https://github.com/madawei2699/myGPTReader,AI实用工具,信息聚合工具,4442,madawei2699/myGPTReader,A community-driven way to read and chat with AI bots - powered by chatGPT.
https://github.com/google-deepmind/alphageometry,模型开发与研究,模型库/仓库,4436,google-deepmind/alphageometry,
https://github.com/argilla-io/argilla,模型开发与研究,数据工程,4423,argilla-io/argilla,Argilla is a collaboration tool for AI engineers and domain experts to build high-quality datasets
https://github.com/trigaten/Learn_Prompting,AI学习与资源,教程与课程,4407,trigaten/Learn_Prompting,"Prompt Engineering, Generative AI, and LLM Guide by Learn Prompting | Join our discord for the largest Prompt Engineering learning community"
https://github.com/varunshenoy/GraphGPT,AI实用工具,编程助手,4394,varunshenoy/GraphGPT,Extrapolating knowledge graphs from unstructured text using GPT-3 🕵️‍♂️
https://github.com/microsoft/BioGPT,模型开发与研究,模型库/仓库,4392,microsoft/BioGPT,
https://github.com/fchollet/ARC,AI构建与集成,评估工具,4352,fchollet/ARC,The Abstraction and Reasoning Corpus
https://github.com/huggingface/autotrain-advanced,模型开发与研究,训练与微调,4348,huggingface/autotrain-advanced,🤗 AutoTrain Advanced
https://github.com/yizhongw/self-instruct,未分类,未分类,4333,yizhongw/self-instruct,Aligning pretrained language models with instruction data generated by themselves.
https://github.com/Instruction-Tuning-with-GPT-4/GPT-4-LLM,模型开发与研究,数据工程,4293,Instruction-Tuning-with-GPT-4/GPT-4-LLM,Instruction Tuning with GPT-4
https://github.com/sensity-ai/dot,AI实用工具,图像创作工具,4288,sensity-ai/dot,The Deepfake Offensive Toolkit
https://github.com/dedupeio/dedupe,未分类,未分类,4261,dedupeio/dedupe,":id: A python library for accurate and scalable fuzzy matching, record deduplication and entity-resolution."
https://github.com/openai/plugins-quickstart,AI构建与集成,AI接口工具,4237,openai/plugins-quickstart,Get a ChatGPT plugin up and running in under 5 minutes!
https://github.com/leondz/garak,AI构建与集成,评估工具,4236,leondz/garak,the LLM vulnerability scanner
https://github.com/lencx/nofwl,AI实用工具,聊天机器人,4234,lencx/nofwl,NoFWL Desktop Application
https://github.com/google-research/simclr,模型开发与研究,模型库/仓库,4233,google-research/simclr,SimCLRv2 - Big Self-Supervised Models are Strong Semi-Supervised Learners
https://github.com/xlang-ai/OpenAgents,AI实用工具,工作流自动化,4220,xlang-ai/OpenAgents,[COLM 2024] OpenAgents: An Open Platform for Language Agents in the Wild
https://github.com/mosaicml/llm-foundry,模型开发与研究,训练与微调,4199,mosaicml/llm-foundry,LLM training code for Databricks foundation models
https://github.com/ray-project/llm-numbers,AI学习与资源,教程与课程,4196,ray-project/llm-numbers,Numbers every LLM developer should know
https://github.com/leap-ai/headshots-starter,AI实用工具,图像创作工具,4184,leap-ai/headshots-starter,
https://github.com/AgentOps-AI/agentops,AI构建与集成,AI代理系统,4170,AgentOps-AI/agentops,"Python SDK for AI agent monitoring, LLM cost tracking, benchmarking, and more. Integrates with most LLMs and agent frameworks including OpenAI Agents SDK, CrewAI, Langchain, Autogen, AG2, and CamelAI"
https://github.com/Facico/Chinese-Vicuna,模型开发与研究,模型库/仓库,4152,Facico/Chinese-Vicuna,Chinese-Vicuna: A Chinese Instruction-following LLaMA-based Model —— 一个中文低资源的llama+lora方案，结构参考alpaca
https://github.com/THUDM/VisualGLM-6B,模型开发与研究,模型库/仓库,4150,THUDM/VisualGLM-6B,Chinese and English multimodal conversational language model | 多模态中英双语对话语言模型
https://github.com/openai/grok,模型开发与研究,模型库/仓库,4141,openai/grok,
https://github.com/luban-agi/Awesome-AIGC-Tutorials,AI学习与资源,教程与课程,4138,luban-agi/Awesome-AIGC-Tutorials,"Curated tutorials and resources for Large Language Models, AI Painting, and more."
https://github.com/alibaba/data-juicer,模型开发与研究,数据工程,4122,alibaba/data-juicer,Data processing for and with foundation models!  🍎 🍋 🌽 ➡️ ➡️🍸 🍹 🍷
https://github.com/mshumer/gpt-llm-trainer,模型开发与研究,数据工程,4104,mshumer/gpt-llm-trainer,
https://github.com/microsoft/FLAML,AI构建与集成,AI开发框架,4096,microsoft/FLAML,A fast library for AutoML and tuning. Join our Discord: https://discord.gg/Cppx2vSPVP.
https://github.com/turboderp/exllamav2,模型开发与研究,推理优化,4095,turboderp/exllamav2,A fast inference library for running LLMs locally on modern consumer-class GPUs
https://github.com/metavoiceio/metavoice-src,模型开发与研究,模型库/仓库,4082,metavoiceio/metavoice-src,"Foundational model for human-like, expressive TTS"
https://github.com/baidu-research/warp-ctc,模型开发与研究,训练与微调,4077,baidu-research/warp-ctc,Fast parallel CTC.
https://github.com/openai/transformer-debugger,AI构建与集成,评估工具,4073,openai/transformer-debugger,
https://github.com/Nutlope/restorePhotos,AI实用工具,图像创作工具,4054,Nutlope/restorePhotos,Restoring old and blurry face photos with AI.
https://github.com/zhayujie/bot-on-anything,AI构建与集成,AI接口工具,4053,zhayujie/bot-on-anything,"A large model-based chatbot builder that can quickly integrate AI models (including ChatGPT, Claude, Gemini) into various software applications (such as Telegram, Gmail, Slack, and websites)."
https://github.com/mckaywrigley/ai-code-translator,AI实用工具,编程助手,4043,mckaywrigley/ai-code-translator,Use AI to translate code from one language to another.
https://github.com/kserve/kserve,AI部署与基础设施,模型部署工具,4042,kserve/kserve,Standardized Serverless ML Inference Platform on Kubernetes
https://github.com/X-PLUG/MobileAgent,模型开发与研究,模型库/仓库,4003,X-PLUG/MobileAgent,Mobile-Agent: The Powerful Mobile Device Operation Assistant Family
https://github.com/JohnSnowLabs/spark-nlp,模型开发与研究,训练与微调,3951,JohnSnowLabs/spark-nlp,State of the Art Natural Language Processing
https://github.com/jeinlee1991/chinese-llm-benchmark,AI构建与集成,评估工具,3939,jeinlee1991/chinese-llm-benchmark,目前已囊括203个大模型，覆盖chatgpt、gpt-4o、o3-mini、谷歌gemini、Claude3.5、智谱GLM-Zero、文心一言、qwen-max、百川、讯飞星火、商汤senseChat、minimax等商用模型， 以及DeepSeek-R1、qwq-32b、deepseek-v3、qwen2.5、llama3.3、phi-4、glm4、gemma3、mistral、书生internLM2.5等开源大模型。不仅提供能力评分排行榜，也提供所有模型的原始输出结果！
https://github.com/georgezouq/awesome-ai-in-finance,AI学习与资源,教程与课程,3926,georgezouq/awesome-ai-in-finance,🔬 A curated list of awesome LLMs & deep learning strategies & tools in financial market.
https://github.com/microsoft/lmops,AI构建与集成,AI开发框架,3916,microsoft/lmops,General technology for enabling AI capabilities w/ LLMs and MLLMs
https://github.com/NVlabs/Sana,模型开发与研究,模型库/仓库,3897,NVlabs/Sana,SANA: Efficient High-Resolution Image Synthesis with Linear Diffusion Transformer
https://github.com/Significant-Gravitas/Auto-GPT-Plugins,AI构建与集成,AI代理系统,3882,Significant-Gravitas/Auto-GPT-Plugins,Plugins for Auto-GPT
https://github.com/eth-sri/lmql,AI构建与集成,提示工程工具,3881,eth-sri/lmql,A language for constraint-guided and efficient LLM programming.
https://github.com/mlfoundations/open_flamingo,模型开发与研究,训练与微调,3876,mlfoundations/open_flamingo,An open-source framework for training large multimodal models.
https://github.com/opendilab/awesome-RLHF,AI学习与资源,资源集合,3864,opendilab/awesome-RLHF,A curated list of reinforcement learning with human feedback resources (continually updated)
https://github.com/llSourcell/Doctor-Dignity,模型开发与研究,模型库/仓库,3864,llSourcell/Doctor-Dignity,"Doctor Dignity is an LLM that can pass the US Medical Licensing Exam. It works offline, it's cross-platform, & your health data stays private."
https://github.com/FlagAI-Open/FlagAI,模型开发与研究,训练与微调,3861,FlagAI-Open/FlagAI,"FlagAI (Fast LArge-scale General AI models) is a fast, easy-to-use and extensible toolkit for large-scale model."
https://github.com/llm-attacks/llm-attacks,AI构建与集成,评估工具,3820,llm-attacks/llm-attacks,Universal and Transferable Attacks on Aligned Language Models
https://github.com/fixie-ai/ultravox,模型开发与研究,模型库/仓库,3797,fixie-ai/ultravox,A fast multimodal LLM for real-time voice
https://github.com/shobrook/adrenaline,AI实用工具,编程助手,3790,shobrook/adrenaline,Chat with (and visualize) your codebase
https://github.com/shibing624/MedicalGPT,模型开发与研究,训练与微调,3786,shibing624/MedicalGPT,MedicalGPT: Training Your Own Medical GPT Model with ChatGPT Training Pipeline. 训练医疗大模型，实现了包括增量预训练(PT)、有监督微调(SFT)、RLHF、DPO、ORPO、GRPO。
https://github.com/deepseek-ai/DeepSeek-VL,模型开发与研究,模型库/仓库,3754,deepseek-ai/DeepSeek-VL,DeepSeek-VL: Towards Real-World Vision-Language Understanding
https://github.com/OpenNMT/CTranslate2,模型开发与研究,推理优化,3724,OpenNMT/CTranslate2,Fast inference engine for Transformer models
https://github.com/google-research/multinerf,模型开发与研究,模型库/仓库,3708,google-research/multinerf,"A Code Release for Mip-NeRF 360, Ref-NeRF, and RawNeRF"
https://github.com/defog-ai/sqlcoder,AI实用工具,编程助手,3694,defog-ai/sqlcoder,SoTA LLM for converting natural language questions to SQL queries
https://github.com/olivia-ai/olivia,AI实用工具,聊天机器人,3693,olivia-ai/olivia,💁‍♀️Your new best friend powered by an artificial neural network
https://github.com/deanxv/coze-discord-proxy,AI构建与集成,AI接口工具,3691,deanxv/coze-discord-proxy,代理Discord对话Coze-Bot，实现以API形式请求GPT4模型，提供对话、文生图、图生文、知识库检索等功能。
https://github.com/llm-workflow-engine/llm-workflow-engine,AI构建与集成,AI开发框架,3690,llm-workflow-engine/llm-workflow-engine,Power CLI and Workflow manager for LLMs (core package)
https://github.com/chiphuyen/aie-book,AI学习与资源,教程与课程,3686,chiphuyen/aie-book,"[WIP] Resources for AI engineers. Also contains supporting materials for the book AI Engineering (Chip Huyen, 2025)"
https://github.com/Ironclad/rivet,AI构建与集成,AI代理系统,3653,Ironclad/rivet,The open-source visual AI programming environment and TypeScript library
https://github.com/mlc-ai/web-stable-diffusion,AI实用工具,图像创作工具,3649,mlc-ai/web-stable-diffusion,Bringing stable diffusion models to web browsers. Everything runs inside the browser with no server support.
https://github.com/LC1332/Luotuo-Chinese-LLM,模型开发与研究,模型库/仓库,3638,LC1332/Luotuo-Chinese-LLM,骆驼(Luotuo): Open Sourced Chinese Language Models. Developed by 陈启源 @ 华中师范大学 & 李鲁鲁 @ 商汤科技 & 冷子昂 @ 商汤科技
https://github.com/gofireflyio/aiac,AI实用工具,编程助手,3637,gofireflyio/aiac,Artificial Intelligence Infrastructure-as-Code Generator.
https://github.com/google-deepmind/acme,模型开发与研究,训练与微调,3630,google-deepmind/acme,A library of reinforcement learning components and agents
https://github.com/open-mmlab/mmpretrain,模型开发与研究,训练与微调,3614,open-mmlab/mmpretrain,OpenMMLab Pre-training Toolbox and Benchmark
https://github.com/openai/glide-text2im,模型开发与研究,模型库/仓库,3606,openai/glide-text2im,GLIDE: a diffusion-based text-conditional image synthesis model
https://github.com/ricklamers/gpt-code-ui,AI实用工具,编程助手,3584,ricklamers/gpt-code-ui,An open source implementation of OpenAI's ChatGPT Code interpreter
https://github.com/Helicone/helicone,AI部署与基础设施,监控与可观测性,3554,Helicone/helicone,"🧊 Open source LLM observability platform. One line of code to monitor, evaluate, and experiment. YC W23 🍓"
https://github.com/Kent0n-Li/ChatDoctor,AI实用工具,聊天机器人,3548,Kent0n-Li/ChatDoctor,
https://github.com/openai/improved-diffusion,模型开发与研究,模型库/仓库,3501,openai/improved-diffusion,Release for Improved Denoising Diffusion Probabilistic Models
https://github.com/google-research/scenic,模型开发与研究,训练与微调,3493,google-research/scenic,Scenic: A Jax Library for Computer Vision Research and Beyond
https://github.com/NExT-GPT/NExT-GPT,模型开发与研究,模型库/仓库,3477,NExT-GPT/NExT-GPT,Code and models for NExT-GPT: Any-to-Any Multimodal Large Language Model
https://github.com/ztxz16/fastllm,模型开发与研究,推理优化,3474,ztxz16/fastllm,fastllm是c++实现，后端无依赖（仅依赖CUDA，无需依赖PyTorch）的高性能大模型推理库。  可实现单4090推理DeepSeek R1 671B INT4模型，单路可达20+tps。
https://github.com/run-llama/llama-hub,AI部署与基础设施,数据管理平台,3474,run-llama/llama-hub,A library of data loaders for LLMs made by the community -- to be used with LlamaIndex and/or LangChain
https://github.com/promptslab/Promptify,AI构建与集成,提示工程工具,3461,promptslab/Promptify,"Prompt Engineering | Prompt Versioning | Use GPT or other prompt based models to get structured output. Join our discord for Prompt-Engineering, LLMs and other latest research"
https://github.com/life4/textdistance,未分类,未分类,3460,life4/textdistance,"📐 Compute distance between sequences. 30+ algorithms, pure python implementation, common interface, optional external libs usage."
https://github.com/casibase/casibase,AI构建与集成,AI开发框架,3452,casibase/casibase,"⚡️AI Cloud OS: Open-source enterprise-level AI knowledge base and Manus-like agent management platform with admin UI, user management and Single-Sign-On⚡️, supports ChatGPT, Claude, DeepSeek R1, Llama, Ollama, HuggingFace, etc., chat bot demo: https://ai.casibase.com, admin UI demo: https://ai-admin.casibase.com"
https://github.com/iryna-kondr/scikit-llm,模型开发与研究,训练与微调,3438,iryna-kondr/scikit-llm,Seamlessly integrate LLMs into scikit-learn.
https://github.com/huggingface/text-embeddings-inference,AI部署与基础设施,模型部署工具,3386,huggingface/text-embeddings-inference,A blazing fast inference solution for text embeddings models
https://github.com/towhee-io/towhee,模型开发与研究,数据工程,3344,towhee-io/towhee,Towhee is a framework that is dedicated to making neural data processing pipelines simple and fast.
https://github.com/hustvl/Vim,模型开发与研究,模型库/仓库,3342,hustvl/Vim,[ICML 2024] Vision Mamba: Efficient Visual Representation Learning with Bidirectional State Space Model
https://github.com/jaymody/picoGPT,模型开发与研究,模型库/仓库,3337,jaymody/picoGPT,An unnecessarily tiny implementation of GPT-2 in NumPy.
https://github.com/higgsfield-ai/higgsfield,AI部署与基础设施,计算资源管理,3334,higgsfield-ai/higgsfield,"Fault-tolerant, highly scalable GPU orchestration, and a machine learning framework designed for training models with billions to trillions of parameters"
https://github.com/deep-diver/LLM-As-Chatbot,AI实用工具,聊天机器人,3312,deep-diver/LLM-As-Chatbot,LLM as a Chatbot Service
https://github.com/getzep/graphiti,AI构建与集成,AI开发框架,3311,getzep/graphiti,Build Real-Time Knowledge Graphs for AI Agents
https://github.com/facebookresearch/fairscale,模型开发与研究,训练与微调,3290,facebookresearch/fairscale,PyTorch extensions for high performance and large scale training.
https://github.com/google-research/albert,模型开发与研究,模型库/仓库,3271,google-research/albert,ALBERT: A Lite BERT for Self-supervised Learning of Language Representations
https://github.com/dvlab-research/MiniGemini,模型开发与研究,模型库/仓库,3262,dvlab-research/MiniGemini,"Official repo for ""Mini-Gemini: Mining the Potential of Multi-modality Vision Language Models"""
https://github.com/Luodian/Otter,模型开发与研究,模型库/仓库,3246,Luodian/Otter,"🦦 Otter, a multi-modal model based on OpenFlamingo (open-sourced version of DeepMind's Flamingo), trained on MIMIC-IT and showcasing improved instruction-following and in-context learning ability."
https://github.com/ollama/ollama-js,AI构建与集成,AI接口工具,3245,ollama/ollama-js,Ollama JavaScript library
https://github.com/lucidrains/musiclm-pytorch,模型开发与研究,模型库/仓库,3242,lucidrains/musiclm-pytorch,"Implementation of MusicLM, Google's new SOTA model for music generation using attention networks, in Pytorch"
https://github.com/vocodedev/vocode-python,AI构建与集成,AI接口工具,3238,vocodedev/vocode-python,🤖 Build voice-based LLM agents. Modular + open source.
https://github.com/THUDM/GLM,模型开发与研究,训练与微调,3231,THUDM/GLM,GLM (General Language Model)
https://github.com/getzep/zep,AI构建与集成,AI开发框架,3222,getzep/zep,Zep | The Memory Foundation For Your AI Stack
https://github.com/OpenGVLab/InternGPT,AI构建与集成,AI接口工具,3216,OpenGVLab/InternGPT,"InternGPT (iGPT) is an open source demo platform where you can easily showcase your AI models. Now it supports DragGAN, ChatGPT, ImageBind, multimodal chat like GPT-4, SAM, interactive image editing, etc. Try it at igpt.opengvlab.com (支持DragGAN、ChatGPT、ImageBind、SAM的在线Demo系统)"
https://github.com/OpenGVLab/Ask-Anything,模型开发与研究,模型库/仓库,3209,OpenGVLab/Ask-Anything,"[CVPR2024 Highlight][VideoChatGPT] ChatGPT with video understanding! And many more supported LMs such as miniGPT4, StableLM, and MOSS."
https://github.com/langroid/langroid,AI构建与集成,AI代理系统,3208,langroid/langroid,Harness LLMs with Multi-Agent Programming
https://github.com/gptscript-ai/gptscript,AI实用工具,编程助手,3206,gptscript-ai/gptscript,Build AI assistants that interact with your systems
https://github.com/huggingface/safetensors,AI部署与基础设施,开发工具集成,3204,huggingface/safetensors,"Simple, safe way to store and distribute tensors"
https://github.com/NovaSky-AI/SkyThought,模型开发与研究,模型库/仓库,3178,NovaSky-AI/SkyThought,Sky-T1: Train your own O1 preview model within $450
https://github.com/seatgeek/thefuzz,未分类,未分类,3150,seatgeek/thefuzz,Fuzzy String Matching in Python
https://github.com/PriorLabs/TabPFN,模型开发与研究,模型库/仓库,3148,PriorLabs/TabPFN,⚡ TabPFN: Foundation Model for Tabular Data ⚡
https://github.com/BlinkDL/AI-Writer,AI实用工具,文本创作工具,3138,BlinkDL/AI-Writer,AI 写小说，生成玄幻和言情网文等等。中文预训练生成模型。采用我的 RWKV 模型，类似 GPT-2 。AI写作。RWKV for Chinese novel generation.
https://github.com/QData/TextAttack,模型开发与研究,数据工程,3128,QData/TextAttack,"TextAttack 🐙  is a Python framework for adversarial attacks, data augmentation, and model training in NLP https://textattack.readthedocs.io/en/master/"
https://github.com/neuralmagic/deepsparse,AI部署与基础设施,模型部署工具,3126,neuralmagic/deepsparse,Sparsity-aware deep learning inference runtime for CPUs
https://github.com/alpa-projects/alpa,模型开发与研究,训练与微调,3122,alpa-projects/alpa,Training and serving large-scale neural networks with auto parallelization.
https://github.com/google-deepmind/gemma,模型开发与研究,模型库/仓库,3118,google-deepmind/gemma,"Gemma open-weight LLM library, from Google DeepMind"
https://github.com/microsoft/PromptWizard,AI构建与集成,提示工程工具,3110,microsoft/PromptWizard,Task-Aware Agent-driven Prompt Optimization Framework
https://github.com/lucidrains/vector-quantize-pytorch,模型开发与研究,推理优化,3105,lucidrains/vector-quantize-pytorch,"Vector (and Scalar) Quantization, in Pytorch"
https://github.com/ModelTC/lightllm,AI部署与基础设施,模型部署工具,3089,ModelTC/lightllm,"LightLLM is a Python-based LLM (Large Language Model) inference and serving framework, notable for its lightweight design, easy scalability, and high-speed performance."
https://github.com/microsoft/torchscale,模型开发与研究,训练与微调,3067,microsoft/torchscale,Foundation Architecture for (M)LLMs
https://github.com/Docta-ai/docta,模型开发与研究,数据工程,3066,Docta-ai/docta,A Doctor for your data
https://github.com/daveshap/OpenAI_Agent_Swarm,AI实用工具,工作流自动化,3064,daveshap/OpenAI_Agent_Swarm,"HAAS = Hierarchical Autonomous Agent Swarm - ""Resistance is futile!"""
https://github.com/qwopqwop200/GPTQ-for-LLaMa,模型开发与研究,推理优化,3047,qwopqwop200/GPTQ-for-LLaMa,4 bits quantization of LLaMA using GPTQ
https://github.com/potpie-ai/potpie,AI实用工具,编程助手,3041,potpie-ai/potpie,Prompt-To-Agent : Create custom engineering agents for your codebase
https://github.com/alexrudall/ruby-openai,AI构建与集成,AI接口工具,3020,alexrudall/ruby-openai,OpenAI API + Ruby! 🤖❤️ Now with Responses API + DeepSeek!
https://github.com/google/BIG-bench,AI构建与集成,评估工具,3011,google/BIG-bench,Beyond the Imitation Game collaborative benchmark for measuring and extrapolating the capabilities of language models
https://github.com/meta-llama/PurpleLlama,AI构建与集成,评估工具,3003,meta-llama/PurpleLlama,Set of tools to assess and improve LLM security.
https://github.com/google-deepmind/dm-haiku,模型开发与研究,训练与微调,3002,google-deepmind/dm-haiku,JAX-based neural network library
https://github.com/microsoft/lida,模型开发与研究,模型库/仓库,3001,microsoft/lida,Automatic Generation of Visualizations and Infographics using Large Language Models
https://github.com/li-plus/chatglm.cpp,模型开发与研究,推理优化,2973,li-plus/chatglm.cpp,C++ implementation of ChatGLM-6B & ChatGLM2-6B & ChatGLM3 & GLM4(V)
https://github.com/DSXiangLi/DecryptPrompt,AI学习与资源,资源集合,2972,DSXiangLi/DecryptPrompt,总结Prompt&LLM论文，开源数据&模型，AIGC应用
https://github.com/Josh-XT/AGiXT,AI构建与集成,AI代理系统,2962,Josh-XT/AGiXT,"AGiXT is a dynamic AI Agent Automation Platform that seamlessly orchestrates instruction management and complex task execution across diverse AI providers. Combining adaptive memory, smart features, and a versatile plugin system, AGiXT delivers efficient and comprehensive AI solutions."
https://github.com/mpoon/gpt-repository-loader,AI实用工具,编程助手,2961,mpoon/gpt-repository-loader,Convert code repos into an LLM prompt-friendly format. Mostly built by GPT-4.
https://github.com/VainF/Torch-Pruning,模型开发与研究,推理优化,2957,VainF/Torch-Pruning,[CVPR 2023] DepGraph: Towards Any Structural Pruning
https://github.com/google-research/frame-interpolation,模型开发与研究,模型库/仓库,2955,google-research/frame-interpolation,"FILM: Frame Interpolation for Large Motion, In ECCV 2022."
https://github.com/salesforce/CodeT5,模型开发与研究,模型库/仓库,2950,salesforce/CodeT5,Home of CodeT5: Open Code LLMs for Code Understanding and Generation
https://github.com/RootbeerComputer/backend-GPT,AI实用工具,编程助手,2947,RootbeerComputer/backend-GPT,
https://github.com/gragland/chatgpt-chrome-extension,AI实用工具,聊天机器人,2943,gragland/chatgpt-chrome-extension,A ChatGPT Chrome extension. Integrates ChatGPT into every text box on the internet.
https://github.com/FreedomIntelligence/LLMZoo,AI构建与集成,AI开发框架,2941,FreedomIntelligence/LLMZoo,"⚡LLM Zoo is a project that provides data, models, and evaluation benchmark for large language models.⚡"
https://github.com/eureka-research/Eureka,模型开发与研究,训练与微调,2939,eureka-research/Eureka,"Official Repository for ""Eureka: Human-Level Reward Design via Coding Large Language Models"" (ICLR 2024)"
https://github.com/gmpetrov/databerry,AI构建与集成,AI代理系统,2931,gmpetrov/databerry,The no-code platform for building custom LLM Agents
https://github.com/fern-api/fern,AI实用工具,编程助手,2927,fern-api/fern,Input OpenAPI. Output SDKs and Docs.
https://github.com/predibase/lorax,AI部署与基础设施,模型部署工具,2910,predibase/lorax,Multi-LoRA inference server that scales to 1000s of fine-tuned LLMs
https://github.com/nus-apr/auto-code-rover,AI实用工具,编程助手,2909,nus-apr/auto-code-rover,A project structure aware autonomous software engineer aiming for autonomous program improvement. Resolved 37.3% tasks (pass@1) in SWE-bench lite and 46.2% tasks (pass@1) in SWE-bench verified with each task costs less than $0.7.
https://github.com/open-mmlab/mmdeploy,AI部署与基础设施,模型部署工具,2905,open-mmlab/mmdeploy,OpenMMLab Model Deployment Framework
https://github.com/mit-han-lab/llm-awq,模型开发与研究,推理优化,2900,mit-han-lab/llm-awq,[MLSys 2024 Best Paper Award] AWQ: Activation-aware Weight Quantization for LLM Compression and Acceleration
https://github.com/shreyashankar/gpt3-sandbox,AI构建与集成,提示工程工具,2893,shreyashankar/gpt3-sandbox,The goal of this project is to enable users to create cool web demos using the newly released OpenAI GPT-3 API with just a few lines of Python.
https://github.com/facebookresearch/jepa,模型开发与研究,模型库/仓库,2888,facebookresearch/jepa,PyTorch code and models for V-JEPA self-supervised learning from video.
https://github.com/allenai/open-instruct,模型开发与研究,训练与微调,2878,allenai/open-instruct,AllenAI's post-training codebase
https://github.com/ahmadbilaldev/langui,AI构建与集成,AI接口工具,2859,ahmadbilaldev/langui,"UI for your AI. Open Source Tailwind components tailored for your GPT, generative AI, and LLM projects."
https://github.com/muellerberndt/mini-agi,AI构建与集成,AI代理系统,2848,muellerberndt/mini-agi,MiniAGI is a simple general-purpose autonomous agent based on the OpenAI API.
https://github.com/turboderp/exllama,模型开发与研究,推理优化,2848,turboderp/exllama,A more memory-efficient rewrite of the HF transformers implementation of Llama for use with quantized weights.
https://github.com/Farama-Foundation/PettingZoo,AI构建与集成,AI代理系统,2847,Farama-Foundation/PettingZoo,"An API standard for multi-agent reinforcement learning environments, with popular reference environments and related utilities"
https://github.com/huggingface/optimum,模型开发与研究,训练与微调,2833,huggingface/optimum,"🚀 Accelerate inference and training of 🤗 Transformers, Diffusers, TIMM and Sentence Transformers with easy to use hardware optimization tools"
https://github.com/hegelai/prompttools,AI构建与集成,提示工程工具,2827,hegelai/prompttools,"Open-source tools for prompt testing and experimentation, with support for both LLMs (e.g. OpenAI, LLaMA) and vector databases (e.g. Chroma, Weaviate, LanceDB)."
https://github.com/KoljaB/RealtimeTTS,AI实用工具,聊天机器人,2813,KoljaB/RealtimeTTS,Converts text to speech in realtime
https://github.com/bigscience-workshop/promptsource,AI构建与集成,提示工程工具,2812,bigscience-workshop/promptsource,"Toolkit for creating, sharing and using natural language prompts."
https://github.com/InternLM/InternLM-XComposer,模型开发与研究,模型库/仓库,2806,InternLM/InternLM-XComposer,InternLM-XComposer2.5-OmniLive: A Comprehensive Multimodal System for Long-term Streaming Video and Audio Interactions
https://github.com/google-research/big_vision,模型开发与研究,训练与微调,2784,google-research/big_vision,"Official codebase used to develop Vision Transformer, SigLIP, MLP-Mixer, LiT and more."
https://github.com/google-research/t5x,模型开发与研究,训练与微调,2779,google-research/t5x,
https://github.com/pezzolabs/pezzo,AI构建与集成,AI开发框架,2777,pezzolabs/pezzo,"🕹️ Open-source, developer-first LLMOps platform designed to streamline prompt design, version management, instant delivery, collaboration, troubleshooting, observability and more."
https://github.com/OpenBMB/BMTools,AI构建与集成,AI代理系统,2777,OpenBMB/BMTools,"Tool Learning for Big Models, Open-Source Solutions of ChatGPT-Plugins"
https://github.com/Alpha-VLLM/LLaMA2-Accessory,模型开发与研究,训练与微调,2767,Alpha-VLLM/LLaMA2-Accessory,An Open-source Toolkit for LLM Development
https://github.com/princeton-nlp/SWE-bench,AI构建与集成,评估工具,2740,princeton-nlp/SWE-bench,SWE-bench [Multimodal]: Can Language Models Resolve Real-world Github Issues?
https://github.com/leptonai/leptonai,AI构建与集成,AI开发框架,2727,leptonai/leptonai,A Pythonic framework to simplify AI service building
https://github.com/swirlai/swirl-search,AI实用工具,信息聚合工具,2725,swirlai/swirl-search,"AI Search & RAG Without Moving Your Data. Get instant answers from your company's knowledge across 100+ apps while keeping data secure. Deploy in minutes, not months."
https://github.com/PhoebusSi/Alpaca-CoT,模型开发与研究,训练与微调,2724,PhoebusSi/Alpaca-CoT,"We unified the interfaces of instruction-tuning data (e.g., CoT data), multiple LLMs and parameter-efficient methods (e.g., lora, p-tuning) together for easy use. We welcome open-source enthusiasts to initiate any meaningful PR on this repo and integrate as many LLM related technologies as possible. 我们打造了方便研究人员上手和使用大模型等微调平台，我们欢迎开源爱好者发起任何有意义的pr！"
https://github.com/togethercomputer/moa,AI构建与集成,AI代理系统,2719,togethercomputer/moa,Together Mixture-Of-Agents (MoA) –  65.1% on AlpacaEval with OSS models
https://github.com/paperswithcode/galai,模型开发与研究,模型库/仓库,2713,paperswithcode/galai,Model API for GALACTICA
https://github.com/cheshire-cat-ai/core,AI构建与集成,AI开发框架,2710,cheshire-cat-ai/core,AI agent microservice
https://github.com/whylabs/whylogs,AI部署与基础设施,监控与可观测性,2702,whylabs/whylogs,"An open-source data logging library for machine learning models and data pipelines. 📚 Provides visibility into data quality & model performance over time. 🛡️ Supports privacy-preserving data collection, ensuring safety & robustness. 📈"
https://github.com/adapter-hub/adapters,模型开发与研究,训练与微调,2678,adapter-hub/adapters,A Unified Library for Parameter-Efficient and Modular Transfer Learning
https://github.com/openai/human-eval,AI构建与集成,评估工具,2671,openai/human-eval,"Code for the paper ""Evaluating Large Language Models Trained on Code"""
https://github.com/ekzhu/datasketch,未分类,未分类,2664,ekzhu/datasketch,"MinHash, LSH, LSH Forest, Weighted MinHash, HyperLogLog, HyperLogLog++, LSH Ensemble and HNSW"
https://github.com/dvlab-research/LongLoRA,模型开发与研究,模型库/仓库,2652,dvlab-research/LongLoRA,Code and documents of LongLoRA and LongAlpaca (ICLR 2024 Oral)
https://github.com/microsoft/prompt-engine,AI构建与集成,提示工程工具,2650,microsoft/prompt-engine,A library for helping developers craft prompts for Large Language Models
https://github.com/noahshinn/reflexion,AI构建与集成,AI代理系统,2648,noahshinn/reflexion,[NeurIPS 2023] Reflexion: Language Agents with Verbal Reinforcement Learning
https://github.com/stochasticai/xTuring,模型开发与研究,训练与微调,2640,stochasticai/xTuring,"Build, customize and control you own LLMs. From data pre-processing to fine-tuning, xTuring provides an easy way to personalize open-source LLMs. Join our discord community: https://discord.gg/TgHXuSJEk6"
https://github.com/ohmplatform/FreedomGPT,AI实用工具,聊天机器人,2637,ohmplatform/FreedomGPT,This codebase is for a React and Electron-based app that executes the FreedomGPT LLM locally (offline and private) on Mac and Windows using a chat-based interface
https://github.com/OpenGVLab/InternImage,模型开发与研究,模型库/仓库,2631,OpenGVLab/InternImage,[CVPR 2023 Highlight] InternImage: Exploring Large-Scale Vision Foundation Models with Deformable Convolutions
https://github.com/unum-cloud/usearch,AI部署与基础设施,向量数据库,2627,unum-cloud/usearch,"Fast Open-Source Search & Clustering engine × for Vectors & 🔜 Strings × in C++, C, Python, JavaScript, Rust, Java, Objective-C, Swift, C#, GoLang, and Wolfram 🔍"
https://github.com/SciSharp/BotSharp,AI构建与集成,AI代理系统,2620,SciSharp/BotSharp,AI Multi-Agent Framework in .NET
https://github.com/argilla-io/distilabel,模型开发与研究,数据工程,2616,argilla-io/distilabel,"Distilabel is a framework for synthetic data and AI feedback for engineers who need fast, reliable and scalable pipelines based on verified research papers."
https://github.com/JoshuaC215/agent-service-toolkit,AI构建与集成,AI代理系统,2606,JoshuaC215/agent-service-toolkit,"Full toolkit for running an AI agent service built with LangGraph, FastAPI and Streamlit"
https://github.com/state-spaces/s4,模型开发与研究,模型库/仓库,2598,state-spaces/s4,Structured state space sequence models
https://github.com/yerfor/GeneFace,模型开发与研究,模型库/仓库,2595,yerfor/GeneFace,GeneFace: Generalized and High-Fidelity 3D Talking Face Synthesis; ICLR 2023; Official code
https://github.com/freedmand/semantra,AI实用工具,数据组织工具,2592,freedmand/semantra,Multi-tool for semantic search
https://github.com/baaivision/Painter,模型开发与研究,模型库/仓库,2560,baaivision/Painter,Painter & SegGPT Series: Vision Foundation Models from BAAI
https://github.com/OpenPipe/OpenPipe,模型开发与研究,训练与微调,2560,OpenPipe/OpenPipe,Turn expensive prompts into cheap fine-tuned models
https://github.com/ianarawjo/ChainForge,AI构建与集成,提示工程工具,2559,ianarawjo/ChainForge,An open-source visual programming environment for battle-testing prompts to LLMs.
https://github.com/databricks/dbrx,模型开发与研究,模型库/仓库,2546,databricks/dbrx,"Code examples and resources for DBRX, a large language model developed by Databricks"
https://github.com/thunlp/UltraChat,模型开发与研究,数据工程,2531,thunlp/UltraChat,"Large-scale, Informative, and Diverse Multi-round Chat Data (and Models)"
https://github.com/webdataset/webdataset,AI部署与基础设施,数据管理平台,2528,webdataset/webdataset,"A high-performance Python-based I/O system for large (and small) deep learning problems, with strong support for PyTorch."
https://github.com/lamini-ai/lamini,模型开发与研究,训练与微调,2528,lamini-ai/lamini,The Official Python Client for Lamini's API
https://github.com/openai/weak-to-strong,模型开发与研究,训练与微调,2524,openai/weak-to-strong,
https://github.com/mshumer/gpt-author,AI实用工具,文本创作工具,2513,mshumer/gpt-author,
https://github.com/aurelio-labs/semantic-router,AI构建与集成,AI开发框架,2512,aurelio-labs/semantic-router,Superfast AI decision making and intelligent processing of multi-modal data.
https://github.com/MzeroMiko/VMamba,模型开发与研究,模型库/仓库,2509,MzeroMiko/VMamba,VMamba: Visual State Space Models，code is based on mamba
https://github.com/ysymyth/ReAct,AI构建与集成,提示工程工具,2504,ysymyth/ReAct,[ICLR 2023] ReAct: Synergizing Reasoning and Acting in Language Models
https://github.com/eric-mitchell/direct-preference-optimization,模型开发与研究,训练与微调,2498,eric-mitchell/direct-preference-optimization,Reference implementation for DPO (Direct Preference Optimization)
https://github.com/howl-anderson/unlocking-the-power-of-llms,AI学习与资源,教程与课程,2495,howl-anderson/unlocking-the-power-of-llms,使用 Prompts 和 Chains 让 ChatGPT 成为神奇的生产力工具！Unlocking the power of LLMs.
https://github.com/openai/simple-evals,AI构建与集成,评估工具,2494,openai/simple-evals,
https://github.com/run-llama/LlamaIndexTS,AI构建与集成,AI开发框架,2489,run-llama/LlamaIndexTS,Data framework for your LLM applications. Focus on server side solution
https://github.com/iterative/datachain,AI构建与集成,AI代理系统,2484,iterative/datachain,"ETL, Analytics, Versioning for Unstructured Data"
https://github.com/FasterDecoding/Medusa,模型开发与研究,推理优化,2482,FasterDecoding/Medusa,Medusa: Simple Framework for Accelerating LLM Generation with Multiple Decoding Heads
https://github.com/THUDM/AgentBench,AI构建与集成,评估工具,2481,THUDM/AgentBench,A Comprehensive Benchmark to Evaluate LLMs as Agents (ICLR'24)
https://github.com/baaivision/EVA,模型开发与研究,模型库/仓库,2462,baaivision/EVA,EVA Series: Visual Representation Fantasies from BAAI
https://github.com/Microsoft/genaiscript,AI构建与集成,提示工程工具,2456,Microsoft/genaiscript,Automatable GenAI Scripting
https://github.com/EleutherAI/pythia,模型开发与研究,模型库/仓库,2441,EleutherAI/pythia,The hub for EleutherAI's work on interpretability and learning dynamics
https://github.com/huggingface/setfit,模型开发与研究,训练与微调,2433,huggingface/setfit,Efficient few-shot learning with Sentence Transformers
https://github.com/xusenlinzy/api-for-open-llm,AI构建与集成,AI接口工具,2427,xusenlinzy/api-for-open-llm,"Openai style api for open large language models, using LLMs just as chatgpt! Support for LLaMA, LLaMA-2, BLOOM, Falcon, Baichuan, Qwen, Xverse, SqlCoder, CodeLLaMA, ChatGLM, ChatGLM2, ChatGLM3 etc. 开源大模型的统一后端接口"
https://github.com/pyro-ppl/numpyro,模型开发与研究,训练与微调,2421,pyro-ppl/numpyro,Probabilistic programming with NumPy powered by JAX for autograd and JIT compilation to GPU/TPU/CPU.
https://github.com/Jittor/JittorLLMs,模型开发与研究,推理优化,2417,Jittor/JittorLLMs,计图大模型推理库，具有高性能、配置要求低、中文支持好、可移植等特点
https://github.com/paulpierre/RasaGPT,AI构建与集成,AI开发框架,2411,paulpierre/RasaGPT,"💬 RasaGPT is the first headless LLM chatbot platform built on top of Rasa and Langchain. Built w/ Rasa, FastAPI, Langchain, LlamaIndex, SQLModel, pgvector, ngrok, telegram"
https://github.com/truera/trulens,AI构建与集成,评估工具,2409,truera/trulens,Evaluation and Tracking for LLM Experiments
https://github.com/ashishpatel26/LLM-Finetuning,AI学习与资源,教程与课程,2402,ashishpatel26/LLM-Finetuning,LLM Finetuning with peft
https://github.com/zou-group/textgrad,AI构建与集成,提示工程工具,2393,zou-group/textgrad,TextGrad: Automatic ''Differentiation'' via Text -- using large language models to backpropagate textual gradients.
https://github.com/embeddings-benchmark/mteb,AI构建与集成,评估工具,2377,embeddings-benchmark/mteb,MTEB: Massive Text Embedding Benchmark
https://github.com/Azure/PyRIT,AI构建与集成,评估工具,2366,Azure/PyRIT,The Python Risk Identification Tool for generative AI (PyRIT) is an open source framework built to empower security professionals and engineers to proactively identify risks in generative AI systems.
https://github.com/SoraWebui/SoraWebui,AI实用工具,聊天机器人,2358,SoraWebui/SoraWebui,"SoraWebui is an open-source Sora web client, enabling users to easily create videos from text with OpenAI's Sora model."
https://github.com/salesforce/decaNLP,AI构建与集成,评估工具,2348,salesforce/decaNLP,The Natural Language Decathlon: A Multitask Challenge for NLP
https://github.com/cogentapps/chat-with-gpt,AI构建与集成,AI接口工具,2347,cogentapps/chat-with-gpt,An open-source ChatGPT app with a voice
https://github.com/huggingface/datatrove,模型开发与研究,数据工程,2340,huggingface/datatrove,Freeing data processing from scripting madness by providing a set of platform-agnostic customizable pipeline processing blocks.
https://github.com/dvorka/mindforger,AI实用工具,信息聚合工具,2330,dvorka/mindforger,Thinking notebook and Markdown editor.
https://github.com/spcl/graph-of-thoughts,AI构建与集成,提示工程工具,2328,spcl/graph-of-thoughts,"Official Implementation of ""Graph of Thoughts: Solving Elaborate Problems with Large Language Models"""
https://github.com/Agenta-AI/agenta,AI构建与集成,AI开发框架,2310,Agenta-AI/agenta,"The open-source LLMOps platform: prompt playground, prompt management, LLM evaluation, and LLM Observability all in one place."
https://github.com/allenai/RL4LMs,模型开发与研究,训练与微调,2295,allenai/RL4LMs,A modular RL library to fine-tune language models to human preferences
https://github.com/karthink/gptel,AI实用工具,聊天机器人,2279,karthink/gptel,A simple LLM client for Emacs
https://github.com/dot-agent/nextpy,AI构建与集成,AI代理系统,2273,dot-agent/nextpy,🤖Self-Modifying Framework from the Future 🔮 World's First AMS
https://github.com/FreedomIntelligence/Medical_NLP,AI学习与资源,资源集合,2262,FreedomIntelligence/Medical_NLP,"Medical NLP Competition, dataset, large models, paper"
https://github.com/TigerResearch/TigerBot,模型开发与研究,模型库/仓库,2258,TigerResearch/TigerBot,TigerBot: A multi-language multi-task LLM
https://github.com/uptrain-ai/uptrain,AI部署与基础设施,监控与可观测性,2258,uptrain-ai/uptrain,"UpTrain is an open-source unified platform to evaluate and improve Generative AI applications. We provide grades for 20+ preconfigured checks (covering language, code, embedding use-cases), perform root cause analysis on failure cases and give insights on how to resolve them."
https://github.com/griptape-ai/griptape,AI构建与集成,AI开发框架,2241,griptape-ai/griptape,"Modular Python framework for AI agents and workflows with chain-of-thought reasoning, tools, and memory."
https://github.com/jackmpcollins/magentic,AI构建与集成,提示工程工具,2241,jackmpcollins/magentic,Seamlessly integrate LLMs as Python functions
https://github.com/instill-ai/vdp,AI构建与集成,AI开发框架,2239,instill-ai/vdp,"🔮 Instill Core is a full-stack AI infrastructure tool for data, model and pipeline orchestration, designed to streamline every aspect of building versatile AI-first applications"
https://github.com/ag2ai/ag2,AI构建与集成,AI代理系统,2235,ag2ai/ag2,AG2 (formerly AutoGen): The Open-Source AgentOS. Join us at: https://discord.gg/pAbnFJrkgZ
https://github.com/Lightning-AI/torchmetrics,AI构建与集成,评估工具,2234,Lightning-AI/torchmetrics,"Machine learning metrics for distributed, scalable PyTorch applications."
https://github.com/HazyResearch/ThunderKittens,模型开发与研究,推理优化,2225,HazyResearch/ThunderKittens,Tile primitives for speedy kernels
https://github.com/refuel-ai/autolabel,模型开发与研究,数据工程,2200,refuel-ai/autolabel,"Label, clean and enrich text datasets with LLMs."
https://github.com/agentica-project/deepscaler,模型开发与研究,模型库/仓库,2198,agentica-project/deepscaler,Democratizing Reinforcement Learning for LLMs
https://github.com/google-research/uda,模型开发与研究,数据工程,2188,google-research/uda,Unsupervised Data Augmentation (UDA)
https://github.com/chiphuyen/lazynlp,模型开发与研究,数据工程,2183,chiphuyen/lazynlp,Library to scrape and clean web pages to create massive datasets.
https://github.com/huggingface/evaluate,AI构建与集成,评估工具,2173,huggingface/evaluate,🤗 Evaluate: A library for easily evaluating machine learning models and datasets.
https://github.com/zjunlp/EasyEdit,AI构建与集成,提示工程工具,2171,zjunlp/EasyEdit,[ACL 2024] An Easy-to-use Knowledge Editing Framework for LLMs.
https://github.com/openai/consistencydecoder,模型开发与研究,模型库/仓库,2171,openai/consistencydecoder,Consistency Distilled Diff VAE
https://github.com/jujumilk3/leaked-system-prompts,AI构建与集成,提示工程工具,2170,jujumilk3/leaked-system-prompts,Collection of leaked system prompts
https://github.com/intel/intel-extension-for-transformers,AI构建与集成,AI开发框架,2165,intel/intel-extension-for-transformers,⚡ Build your chatbot within minutes on your favorite device; offer SOTA compression techniques for LLMs; run LLMs efficiently on Intel Platforms⚡
https://github.com/stanford-crfm/helm,AI构建与集成,评估工具,2149,stanford-crfm/helm,"Holistic Evaluation of Language Models (HELM), a framework to increase the transparency of language models (https://arxiv.org/abs/2211.09110). This framework is also used to evaluate text-to-image models in HEIM (https://arxiv.org/abs/2311.04287) and vision-language models in VHELM (https://arxiv.org/abs/2410.07112)."
https://github.com/adieyal/sd-dynamic-prompts,AI构建与集成,提示工程工具,2145,adieyal/sd-dynamic-prompts,A custom script for AUTOMATIC1111/stable-diffusion-webui to implement a tiny template language for random prompt generation
https://github.com/labmlai/labml,AI部署与基础设施,监控与可观测性,2134,labmlai/labml,🔎 Monitor deep learning model training and hardware usage from your mobile phone 📱
https://github.com/google-deepmind/code_contests,未分类,未分类,2133,google-deepmind/code_contests,
https://github.com/dvlab-research/LISA,模型开发与研究,模型库/仓库,2123,dvlab-research/LISA,"Project Page for ""LISA: Reasoning Segmentation via Large Language Model"""
https://github.com/neuralmagic/sparseml,模型开发与研究,推理优化,2121,neuralmagic/sparseml,"Libraries for applying sparsification recipes to neural networks with a few lines of code, enabling faster and smaller models"
https://github.com/agiresearch/OpenAGI,AI构建与集成,AI代理系统,2111,agiresearch/OpenAGI,OpenAGI: When LLM Meets Domain Experts
https://github.com/context-labs/autodoc,AI实用工具,编程助手,2105,context-labs/autodoc,Experimental toolkit for auto-generating codebase documentation using LLMs
https://github.com/gligen/GLIGEN,模型开发与研究,模型库/仓库,2103,gligen/GLIGEN,Open-Set Grounded Text-to-Image Generation
https://github.com/InternLM/lagent,AI构建与集成,AI代理系统,2102,InternLM/lagent,A lightweight framework for building LLM-based agents
https://github.com/unslothai/hyperlearn,模型开发与研究,训练与微调,2098,unslothai/hyperlearn,"2-2000x faster ML algos, 50% less memory usage, works on all hardware - new and old."
https://github.com/YiVal/YiVal,AI构建与集成,提示工程工具,2094,YiVal/YiVal,Your Automatic Prompt Engineering Assistant for GenAI Applications
https://github.com/IST-DASLab/gptq,模型开发与研究,推理优化,2073,IST-DASLab/gptq,"Code for the ICLR 2023 paper ""GPTQ: Accurate Post-training Quantization of Generative Pretrained Transformers""."
https://github.com/lxe/simple-llm-finetuner,模型开发与研究,训练与微调,2063,lxe/simple-llm-finetuner,Simple UI for LLM Model Finetuning
https://github.com/langchain-ai/langserve,AI部署与基础设施,模型部署工具,2059,langchain-ai/langserve,LangServe 🦜️🏓
https://github.com/BAAI-Agents/Cradle,AI实用工具,工作流自动化,2056,BAAI-Agents/Cradle,"The Cradle framework is a first attempt at General Computer Control (GCC). Cradle supports agents to ace any computer task by enabling strong reasoning abilities, self-improvment, and skill curation, in a standardized general environment with minimal requirements."
https://github.com/devflowinc/trieve,AI构建与集成,AI开发框架,2053,devflowinc/trieve,"All-in-one infrastructure for search, recommendations, RAG, and analytics offered via API"
https://github.com/vectara/hallucination-leaderboard,AI构建与集成,评估工具,2048,vectara/hallucination-leaderboard,Leaderboard Comparing LLM Performance at Producing Hallucinations when Summarizing Short Documents
https://github.com/THUDM/P-tuning-v2,模型开发与研究,训练与微调,2027,THUDM/P-tuning-v2,An optimized deep prompt tuning strategy comparable to fine-tuning across scales and tasks
https://github.com/kha-white/manga-ocr,AI实用工具,信息聚合工具,2022,kha-white/manga-ocr,"Optical character recognition for Japanese text, with the main focus being Japanese manga"
https://github.com/TransformerLensOrg/TransformerLens,AI构建与集成,评估工具,2021,TransformerLensOrg/TransformerLens,A library for mechanistic interpretability of GPT-style language models
https://github.com/michaelfeil/infinity,AI部署与基础设施,模型部署工具,2020,michaelfeil/infinity,"Infinity is a high-throughput, low-latency serving engine for text-embeddings, reranking models, clip, clap and colpali"
https://github.com/microsoft/aici,AI构建与集成,提示工程工具,2013,microsoft/aici,AICI: Prompts as (Wasm) Programs
https://github.com/ise-uiuc/magicoder,模型开发与研究,模型库/仓库,2011,ise-uiuc/magicoder,[ICML'24] Magicoder: Empowering Code Generation with OSS-Instruct
https://github.com/epfLLM/meditron,模型开发与研究,模型库/仓库,1999,epfLLM/meditron,Meditron is a suite of open-source medical Large Language Models (LLMs).
https://github.com/tensorchord/pgvecto.rs,AI部署与基础设施,向量数据库,1991,tensorchord/pgvecto.rs,"Scalable, Low-latency and Hybrid-enabled Vector Search in Postgres. Revolutionize Vector Search, not Database."
https://github.com/microsoft/PromptCraft-Robotics,AI构建与集成,提示工程工具,1991,microsoft/PromptCraft-Robotics,Community for applying LLMs to robotics and a robot simulator with ChatGPT integration
https://github.com/openai/prm800k,模型开发与研究,数据工程,1965,openai/prm800k,"800,000 step-level correctness labels on LLM solutions to MATH problems"
https://github.com/knuckleswtf/scribe,AI实用工具,编程助手,1961,knuckleswtf/scribe,Generate API documentation for humans from your Laravel codebase.✍
https://github.com/tensorflow/privacy,模型开发与研究,训练与微调,1960,tensorflow/privacy,Library for training machine learning models with privacy for training data
https://github.com/mattzcarey/code-review-gpt,AI实用工具,编程助手,1947,mattzcarey/code-review-gpt,"Code review powered by LLMs (OpenAI GPT4, Sonnet 3.5) & Embeddings ⚡️ Improve code quality and catch bugs before you break production 🚀 Lives in your Github/GitLab/Azure DevOps CI"
https://github.com/MetaGLM/FinGLM,模型开发与研究,模型库/仓库,1937,MetaGLM/FinGLM,FinGLM: 致力于构建一个开放的、公益的、持久的金融大模型项目，利用开源开放来促进「AI+金融」。
https://github.com/pytorch-labs/ao,模型开发与研究,推理优化,1936,pytorch-labs/ao,PyTorch native quantization and sparsity for training and inference
https://github.com/xlang-ai/instructor-embedding,模型开发与研究,训练与微调,1932,xlang-ai/instructor-embedding,"[ACL 2023] One Embedder, Any Task: Instruction-Finetuned Text Embeddings"
https://github.com/MineDojo/MineDojo,AI构建与集成,AI代理系统,1925,MineDojo/MineDojo,Building Open-Ended Embodied Agents with Internet-Scale Knowledge
https://github.com/anse-app/anse,AI构建与集成,AI接口工具,1921,anse-app/anse,"Supercharged experience for multiple models such as ChatGPT, DALL-E and Stable Diffusion."
https://github.com/trypromptly/LLMStack,AI构建与集成,AI开发框架,1911,trypromptly/LLMStack,"No-code multi-agent framework to build LLM Agents, workflows and applications with your data"
https://github.com/greshake/llm-security,AI构建与集成,提示工程工具,1910,greshake/llm-security,New ways of breaking app-integrated LLMs
https://github.com/KimMeen/Time-LLM,AI构建与集成,AI开发框架,1908,KimMeen/Time-LLM,"[ICLR 2024] Official implementation of "" 🦙 Time-LLM: Time Series Forecasting by Reprogramming Large Language Models"""
https://github.com/eli64s/readme-ai,AI实用工具,编程助手,1902,eli64s/readme-ai,"README file generator, powered by AI."
https://github.com/salesforce/ctrl,模型开发与研究,训练与微调,1880,salesforce/ctrl,Conditional Transformer Language Model for Controllable Generation
https://github.com/google-deepmind/mathematics_dataset,未分类,未分类,1866,google-deepmind/mathematics_dataset,"This dataset code generates mathematical question and answer pairs, from a range of question types at roughly school-level difficulty."
https://github.com/3DTopia/LGM,模型开发与研究,模型库/仓库,1856,3DTopia/LGM,[ECCV 2024 Oral] LGM: Large Multi-View Gaussian Model for High-Resolution 3D Content Creation.
https://github.com/marella/ctransformers,AI构建与集成,AI开发框架,1856,marella/ctransformers,Python bindings for the Transformer models implemented in C/C++ using GGML library.
https://github.com/smallcloudai/refact,AI实用工具,编程助手,1855,smallcloudai/refact,"AI Agent that handles engineering tasks end-to-end: integrates with developers’ tools, plans, executes, and iterates until it achieves a successful result."
https://github.com/google-deepmind/optax,模型开发与研究,训练与微调,1851,google-deepmind/optax,Optax is a gradient processing and optimization library for JAX.
https://github.com/Forethought-Technologies/AutoChain,AI构建与集成,AI代理系统,1846,Forethought-Technologies/AutoChain,"AutoChain: Build lightweight, extensible, and testable LLM Agents"
https://github.com/jina-ai/dev-gpt,AI实用工具,编程助手,1838,jina-ai/dev-gpt,Your Virtual Development Team
https://github.com/Nutlope/llamatutor,未分类,未分类,1830,Nutlope/llamatutor,An AI personal tutor built with Llama 3.1
https://github.com/Cloud-CV/EvalAI,AI构建与集成,评估工具,1828,Cloud-CV/EvalAI,:cloud: :rocket: :bar_chart: :chart_with_upwards_trend: Evaluating state of the art in AI
https://github.com/floneum/floneum,AI构建与集成,AI开发框架,1815,floneum/floneum,"Instant, controllable, local pre-trained AI models in Rust"
https://github.com/S-LoRA/S-LoRA,AI部署与基础设施,模型部署工具,1812,S-LoRA/S-LoRA,S-LoRA: Serving Thousands of Concurrent LoRA Adapters
https://github.com/openai/gpt-discord-bot,AI实用工具,聊天机器人,1808,openai/gpt-discord-bot,"Example Discord bot written in Python that uses the completions API to have conversations with the `text-davinci-003` model, and the moderations API to filter the messages."
https://github.com/fiatrete/OpenDAN-Personal-AI-OS,AI实用工具,工作流自动化,1803,fiatrete/OpenDAN-Personal-AI-OS,"OpenDAN is an open source Personal AI OS , which consolidates various AI modules in one place for your personal use."
https://github.com/uTensor/uTensor,模型开发与研究,推理优化,1791,uTensor/uTensor,TinyML AI inference library
https://github.com/ray-project/llm-applications,AI学习与资源,教程与课程,1787,ray-project/llm-applications,A comprehensive guide to building RAG-based LLM applications for production.
https://github.com/YangLing0818/RPG-DiffusionMaster,AI构建与集成,AI开发框架,1785,YangLing0818/RPG-DiffusionMaster,"[ICML 2024] Mastering Text-to-Image Diffusion: Recaptioning, Planning, and Generating with Multimodal LLMs (RPG)"
https://github.com/allenai/scispacy,模型开发与研究,模型库/仓库,1784,allenai/scispacy,A full spaCy pipeline and models for scientific/biomedical documents.
https://github.com/AI-Citizen/SolidGPT,AI实用工具,编程助手,1784,AI-Citizen/SolidGPT,Developer AI Persona Search Agent
https://github.com/OpenGVLab/InternVideo,模型开发与研究,模型库/仓库,1781,OpenGVLab/InternVideo,[ECCV2024] Video Foundation Models & Data for Multimodal Understanding
https://github.com/lmnr-ai/lmnr,AI部署与基础设施,监控与可观测性,1773,lmnr-ai/lmnr,"Laminar - open-source all-in-one platform for engineering AI products. Crate data flywheel for you AI app. Traces, Evals, Datasets, Labels. YC S24."
https://github.com/Pythagora-io/pythagora,AI实用工具,编程助手,1769,Pythagora-io/pythagora,Generate automated tests for your Node.js app via LLMs without developers having to write a single line of code.
https://github.com/noamgat/lm-format-enforcer,AI构建与集成,提示工程工具,1765,noamgat/lm-format-enforcer,"Enforce the output format (JSON Schema, Regex etc) of a language model"
https://github.com/beir-cellar/beir,AI构建与集成,评估工具,1761,beir-cellar/beir,"A Heterogeneous Benchmark for Information Retrieval. Easy to use, evaluate your models across 15+ diverse IR datasets."
https://github.com/btahir/open-deep-research,AI实用工具,信息聚合工具,1760,btahir/open-deep-research,Open source alternative to Gemini Deep Research. Generate reports with AI based on search results.
https://github.com/Cormanz/smartgpt,AI实用工具,工作流自动化,1760,Cormanz/smartgpt,A program that provides LLMs with the ability to complete complex tasks using plugins.
https://github.com/kubeflow/training-operator,模型开发与研究,训练与微调,1749,kubeflow/training-operator,Distributed ML Training and Fine-Tuning on Kubernetes
https://github.com/google-deepmind/penzai,模型开发与研究,训练与微调,1749,google-deepmind/penzai,"A JAX research toolkit for building, editing, and visualizing neural networks."
https://github.com/Nutlope/twitterbio,AI实用工具,文本创作工具,1737,Nutlope/twitterbio,Generate your Twitter bio with AI
https://github.com/ucbepic/docetl,AI实用工具,信息聚合工具,1735,ucbepic/docetl,A system for agentic LLM-powered data processing and ETL
https://github.com/all-in-aigc/aicover,AI实用工具,图像创作工具,1731,all-in-aigc/aicover,ai cover generator
https://github.com/BCG-X-Official/agentkit,AI构建与集成,AI代理系统,1718,BCG-X-Official/agentkit,"Starter-kit to build constrained agents with Nextjs, FastAPI and Langchain"
https://github.com/andreibondarev/langchainrb,AI构建与集成,AI开发框架,1713,andreibondarev/langchainrb,Build LLM-powered applications in Ruby
https://github.com/anthropics/hh-rlhf,模型开发与研究,数据工程,1713,anthropics/hh-rlhf,"Human preference data for ""Training a Helpful and Harmless Assistant with Reinforcement Learning from Human Feedback"""
https://github.com/salesforce/WikiSQL,模型开发与研究,数据工程,1713,salesforce/WikiSQL,A large annotated semantic parsing corpus for developing natural language interfaces.
https://github.com/tatsu-lab/alpaca_eval,AI构建与集成,评估工具,1710,tatsu-lab/alpaca_eval,"An automatic evaluator for instruction-following language models. Human-validated, high-quality, cheap, and fast."
https://github.com/apple/ml-4m,模型开发与研究,模型库/仓库,1708,apple/ml-4m,4M: Massively Multimodal Masked Modeling
https://github.com/baaivision/Emu,模型开发与研究,模型库/仓库,1704,baaivision/Emu,Emu Series: Generative Multimodal Models from BAAI
https://github.com/netease-youdao/BCEmbedding,模型开发与研究,模型库/仓库,1700,netease-youdao/BCEmbedding,Netease Youdao's open-source embedding and reranker models for RAG products.
https://github.com/microsoft/i-Code,模型开发与研究,训练与微调,1696,microsoft/i-Code,
https://github.com/google/maxtext,模型开发与研究,模型库/仓库,1673,google/maxtext,"A simple, performant and scalable Jax LLM!"
https://github.com/guinmoon/LLMFarm,AI构建与集成,AI接口工具,1668,guinmoon/LLMFarm,llama and other  large language models on iOS and MacOS offline using GGML library.
https://github.com/google-research/language,模型开发与研究,模型库/仓库,1664,google-research/language,Shared repository for open-sourced projects from the Google AI Language team.
https://github.com/eyurtsev/kor,AI构建与集成,提示工程工具,1662,eyurtsev/kor,LLM(😽)
https://github.com/mylxsw/aidea-server,AI构建与集成,AI接口工具,1660,mylxsw/aidea-server,AIdea 是一款支持 GPT  以及国产大语言模型通义千问、文心一言等，支持 Stable Diffusion 文生图、图生图、 SDXL1.0、超分辨率、图片上色的全能型 APP。
https://github.com/nhaouari/obsidian-textgenerator-plugin,AI实用工具,文本创作工具,1653,nhaouari/obsidian-textgenerator-plugin,"Text Generator is a versatile plugin for Obsidian that allows you to generate text content using various AI providers, including OpenAI, Anthropic, Google and local models."
https://github.com/SqueezeAILab/LLMCompiler,AI构建与集成,AI开发框架,1652,SqueezeAILab/LLMCompiler,[ICML 2024] LLMCompiler: An LLM Compiler for Parallel Function Calling
https://github.com/aallam/openai-kotlin,AI构建与集成,AI接口工具,1652,aallam/openai-kotlin,OpenAI API client for Kotlin with multiplatform and coroutines capabilities.
https://github.com/microsoft/CodeXGLUE,AI构建与集成,评估工具,1644,microsoft/CodeXGLUE,CodeXGLUE
https://github.com/OS-Copilot/FRIDAY,AI实用工具,工作流自动化,1638,OS-Copilot/FRIDAY,An self-improving embodied conversational agent seamlessly integrated into the operating system to automate our daily tasks.
https://github.com/salesforce/ALBEF,模型开发与研究,训练与微调,1627,salesforce/ALBEF,Code for ALBEF: a new vision-language pre-training method
https://github.com/jina-ai/langchain-serve,AI构建与集成,AI开发框架,1625,jina-ai/langchain-serve,⚡ Langchain apps in production using Jina & FastAPI
https://github.com/nlmatics/llmsherpa,AI实用工具,信息聚合工具,1623,nlmatics/llmsherpa,Developer APIs to Accelerate LLM Projects
https://github.com/OpenMotionLab/MotionGPT,模型开发与研究,模型库/仓库,1618,OpenMotionLab/MotionGPT,"[NeurIPS 2023] MotionGPT: Human Motion as a Foreign Language, a unified motion-language generation model using LLMs"
https://github.com/AetherCortex/Llama-X,模型开发与研究,训练与微调,1618,AetherCortex/Llama-X,Open Academic Research on Improving LLaMA to SOTA LLM
https://github.com/roboflow/inference,AI部署与基础设施,模型部署工具,1611,roboflow/inference,Turn any computer or edge device into a command center for your computer vision projects.
https://github.com/OpenAutoCoder/Agentless,AI实用工具,编程助手,1605,OpenAutoCoder/Agentless,Agentless🐱:  an agentless approach to automatically solve software development problems
https://github.com/SHI-Labs/OneFormer,模型开发与研究,模型库/仓库,1585,SHI-Labs/OneFormer,[CVPR 2023] OneFormer: One Transformer to Rule Universal Image Segmentation
https://github.com/wangzhaode/mnn-llm,AI部署与基础设施,开发工具集成,1573,wangzhaode/mnn-llm,llm deploy project based mnn. This project has merged into MNN.
https://github.com/EgoAlpha/prompt-in-context-learning,AI学习与资源,资源集合,1571,EgoAlpha/prompt-in-context-learning,"Awesome resources for in-context learning and prompt engineering: Mastery of the LLMs such as ChatGPT, GPT-3, and FlanT5, with up-to-date and cutting-edge updates."
https://github.com/eylonmiz/react-agent,AI实用工具,编程助手,1570,eylonmiz/react-agent,The open-source React.js Autonomous LLM Agent
https://github.com/jina-ai/thinkgpt,AI构建与集成,AI代理系统,1570,jina-ai/thinkgpt,Agent techniques to augment your LLM and push it beyong its limits
https://github.com/CalculatedContent/WeightWatcher,AI构建与集成,评估工具,1567,CalculatedContent/WeightWatcher,The WeightWatcher tool for predicting the accuracy of   Deep Neural Networks
https://github.com/laiyer-ai/llm-guard,AI构建与集成,提示工程工具,1565,laiyer-ai/llm-guard,The Security Toolkit for LLM Interactions
https://github.com/facebookresearch/multimodal,模型开发与研究,训练与微调,1565,facebookresearch/multimodal,TorchMultimodal is a PyTorch library for training state-of-the-art multimodal multi-task models at scale.
https://github.com/kubeflow/katib,模型开发与研究,训练与微调,1560,kubeflow/katib,Automated Machine Learning on Kubernetes
https://github.com/DAGWorks-Inc/burr,AI构建与集成,AI开发框架,1558,DAGWorks-Inc/burr,"Build applications that make decisions (chatbots, agents, simulations, etc...). Monitor, trace, persist, and execute on your own infrastructure."
https://github.com/premAI-io/state-of-open-source-ai,AI学习与资源,教程与课程,1554,premAI-io/state-of-open-source-ai,:closed_book: Clarity in the current fast-paced mess of Open Source innovation
https://github.com/moj-analytical-services/splink,未分类,未分类,1540,moj-analytical-services/splink,"Fast, accurate and scalable probabilistic data linkage with support for multiple SQL backends"
https://github.com/pionxzh/chatgpt-exporter,AI学习与资源,教程与课程,1518,pionxzh/chatgpt-exporter,Export and Share your ChatGPT conversation history
https://github.com/google-deepmind/bsuite,AI构建与集成,评估工具,1518,google-deepmind/bsuite,bsuite is a collection of carefully-designed experiments that investigate core capabilities of a reinforcement learning (RL) agent
https://github.com/google-research/FLAN,模型开发与研究,模型库/仓库,1510,google-research/FLAN,
https://github.com/IntelLabs/fastRAG,AI构建与集成,AI开发框架,1507,IntelLabs/fastRAG,Efficient Retrieval Augmentation and Generation Framework
https://github.com/andrewnguonly/Lumos,AI实用工具,工作流自动化,1498,andrewnguonly/Lumos,"A RAG LLM co-pilot for browsing the web, powered by local LLMs"
https://github.com/saharNooby/rwkv.cpp,模型开发与研究,推理优化,1495,saharNooby/rwkv.cpp,INT4/INT5/INT8 and FP16 inference on CPU for RWKV language model
https://github.com/psychic-api/rag-stack,AI构建与集成,AI开发框架,1493,psychic-api/rag-stack,"🤖 Deploy a private ChatGPT alternative hosted within your VPC. 🔮 Connect it to your organization's knowledge base and use it as a corporate oracle. Supports open-source LLMs like Llama 2, Falcon, and GPT4All."
https://github.com/jina-ai/finetuner,模型开发与研究,训练与微调,1492,jina-ai/finetuner,":dart: Task-oriented embedding tuning for BERT, CLIP, etc."
https://github.com/Coframe/coffee,AI实用工具,编程助手,1488,Coframe/coffee,Build and iterate on your UI 10x faster with AI - right from your own IDE ☕️
https://github.com/AI4Finance-Foundation/FinRL-Meta,模型开发与研究,数据工程,1485,AI4Finance-Foundation/FinRL-Meta,FinRL­®-Meta: Dynamic datasets and market environments for FinRL.
https://github.com/salesforce/CodeTF,模型开发与研究,训练与微调,1474,salesforce/CodeTF,CodeTF: One-stop Transformer Library for State-of-the-art Code LLM
https://github.com/sobelio/llm-chain,AI构建与集成,AI开发框架,1459,sobelio/llm-chain,`llm-chain` is a powerful rust crate for building chains in large language models allowing you to summarise text and complete complex tasks
https://github.com/ianand/spreadsheets-are-all-you-need,AI学习与资源,教程与课程,1458,ianand/spreadsheets-are-all-you-need,
https://github.com/jquesnelle/yarn,模型开发与研究,训练与微调,1457,jquesnelle/yarn,YaRN: Efficient Context Window Extension of Large Language Models
https://github.com/NVIDIA/aistore,AI部署与基础设施,数据管理平台,1454,NVIDIA/aistore,AIStore: scalable storage for AI applications
https://github.com/farizrahman4u/loopgpt,AI构建与集成,AI代理系统,1451,farizrahman4u/loopgpt,Modular Auto-GPT Framework
https://github.com/PKU-Alignment/safe-rlhf,模型开发与研究,训练与微调,1443,PKU-Alignment/safe-rlhf,Safe RLHF: Constrained Value Alignment via Safe Reinforcement Learning from Human Feedback
https://github.com/Farama-Foundation/chatarena,AI构建与集成,AI代理系统,1442,Farama-Foundation/chatarena,ChatArena (or Chat Arena) is a Multi-Agent Language Game Environments for LLMs. The goal is to develop communication and collaboration capabilities of AIs.
https://github.com/openai/Video-Pre-Training,模型开发与研究,模型库/仓库,1436,openai/Video-Pre-Training,Video PreTraining (VPT): Learning to Act by Watching Unlabeled Online Videos
https://github.com/code-kern-ai/refinery,未分类,未分类,1433,code-kern-ai/refinery,"The data scientist's open-source choice to scale, assess and maintain natural language data. Treat training data like a software artifact."
https://github.com/dleemiller/WordLlama,未分类,未分类,1432,dleemiller/WordLlama,Things you can do with the token embeddings of an LLM
https://github.com/evalplus/evalplus,AI构建与集成,评估工具,1425,evalplus/evalplus,Rigourous evaluation of LLM-synthesized code - NeurIPS 2023 & COLM 2024
https://github.com/nerfstudio-project/nerfacc,模型开发与研究,训练与微调,1424,nerfstudio-project/nerfacc,A General NeRF Acceleration Toolbox in PyTorch.
https://github.com/KnowledgeCanvas/knowledge,AI实用工具,信息聚合工具,1416,KnowledgeCanvas/knowledge,"Knowledge is a tool for saving, searching, accessing, exploring and chatting with all of your favorite websites, documents and files."
https://github.com/THUDM/AgentTuning,AI构建与集成,AI代理系统,1414,THUDM/AgentTuning,AgentTuning: Enabling Generalized Agent Abilities for LLMs
https://github.com/spotify/voyager,AI部署与基础设施,向量数据库,1414,spotify/voyager,"🛰️ An approximate nearest-neighbor search library for Python and Java with a focus on ease of use, simplicity, and deployability."
https://github.com/time-series-foundation-models/lag-llama,模型开发与研究,模型库/仓库,1410,time-series-foundation-models/lag-llama,Lag-Llama: Towards Foundation Models for Probabilistic Time Series Forecasting
https://github.com/microsoft/windows-ai-studio,AI构建与集成,AI开发框架,1405,microsoft/windows-ai-studio,
https://github.com/eumemic/ai-legion,AI构建与集成,AI代理系统,1397,eumemic/ai-legion,An LLM-powered autonomous agent platform
https://github.com/mit-han-lab/smoothquant,模型开发与研究,推理优化,1377,mit-han-lab/smoothquant,[ICML 2023] SmoothQuant: Accurate and Efficient Post-Training Quantization for Large Language Models
https://github.com/lucidrains/self-rewarding-lm-pytorch,模型开发与研究,训练与微调,1376,lucidrains/self-rewarding-lm-pytorch,"Implementation of the training framework proposed in Self-Rewarding Language Model, from MetaAI"
https://github.com/facebookresearch/LAMA,AI构建与集成,评估工具,1376,facebookresearch/LAMA,LAnguage Model Analysis
https://github.com/hendrycks/test,AI构建与集成,评估工具,1371,hendrycks/test,Measuring Massive Multitask Language Understanding | ICLR 2021
https://github.com/PygmalionAI/aphrodite-engine,AI部署与基础设施,模型部署工具,1371,PygmalionAI/aphrodite-engine,Large-scale LLM inference engine
https://github.com/THUDM/ImageReward,模型开发与研究,模型库/仓库,1366,THUDM/ImageReward,[NeurIPS 2023] ImageReward: Learning and Evaluating Human Preferences for Text-to-image Generation
https://github.com/neo4j/NaLLM,AI实用工具,数据组织工具,1366,neo4j/NaLLM,Repository for the NaLLM project
https://github.com/AlibabaResearch/DAMO-ConvAI,模型开发与研究,训练与微调,1362,AlibabaResearch/DAMO-ConvAI,DAMO-ConvAI: The official repository which contains the codebase for Alibaba DAMO Conversational AI.
https://github.com/evo-design/evo,模型开发与研究,模型库/仓库,1357,evo-design/evo,Biological foundation modeling from molecular to genome scale
https://github.com/Yifan-Song793/RestGPT,AI构建与集成,AI代理系统,1357,Yifan-Song793/RestGPT,An LLM-based autonomous agent controlling real-world applications via RESTful APIs
https://github.com/OpenLMLab/MOSS-RLHF,模型开发与研究,训练与微调,1348,OpenLMLab/MOSS-RLHF,Secrets of RLHF in Large Language Models Part I: PPO
https://github.com/mlcommons/inference,AI构建与集成,评估工具,1348,mlcommons/inference,Reference implementations of MLPerf™ inference benchmarks
https://github.com/Link-AGI/AutoAgents,AI构建与集成,AI代理系统,1347,Link-AGI/AutoAgents,[IJCAI 2024] Generate different roles for GPTs to form a collaborative entity for complex tasks.
https://github.com/gorilla-llm/gorilla-cli,AI实用工具,编程助手,1335,gorilla-llm/gorilla-cli,LLMs for your CLI
https://github.com/MiuLab/Taiwan-LLM,模型开发与研究,模型库/仓库,1329,MiuLab/Taiwan-LLM,Traditional Mandarin LLMs for Taiwan
https://github.com/SHI-Labs/Versatile-Diffusion,模型开发与研究,模型库/仓库,1328,SHI-Labs/Versatile-Diffusion,"Versatile Diffusion: Text, Images and Variations All in One Diffusion Model, arXiv 2022 / ICCV 2023"
https://github.com/JonasGeiping/cramming,模型开发与研究,训练与微调,1326,JonasGeiping/cramming,Cramming the training of a (BERT-type) language model into limited compute.
https://github.com/showlab/Show-o,模型开发与研究,模型库/仓库,1321,showlab/Show-o,"[ICLR 2025] Repository for Show-o, One Single Transformer to Unify Multimodal Understanding and Generation."
https://github.com/hiyouga/FastEdit,AI构建与集成,提示工程工具,1321,hiyouga/FastEdit,🩹Editing large language models within 10 seconds⚡
https://github.com/Lightning-AI/lightning-thunder,模型开发与研究,推理优化,1320,Lightning-AI/lightning-thunder,"Thunder gives you PyTorch models superpowers for training and inference. Unlock out-of-the-box optimizations for performance, memory and parallelism, or roll out your own."
https://github.com/databricks/megablocks,模型开发与研究,训练与微调,1316,databricks/megablocks,
https://github.com/openai/lm-human-preferences,模型开发与研究,模型库/仓库,1315,openai/lm-human-preferences,Code for the paper Fine-Tuning Language Models from Human Preferences
https://github.com/irgolic/AutoPR,AI实用工具,编程助手,1312,irgolic/AutoPR,Run AI-powered workflows over your codebase
https://github.com/ItsPi3141/alpaca-electron,AI实用工具,聊天机器人,1308,ItsPi3141/alpaca-electron,The simplest way to run Alpaca (and other LLaMA-based local LLMs) on your own computer
https://github.com/zjunlp/KnowLM,模型开发与研究,模型库/仓库,1302,zjunlp/KnowLM,An Open-sourced Knowledgable Large Language Model Framework.
https://github.com/taranjeet/awesome-gpts,AI学习与资源,资源集合,1297,taranjeet/awesome-gpts,Collection of all the GPTs created by the community
https://github.com/huggingface/llm-vscode,AI实用工具,编程助手,1287,huggingface/llm-vscode,LLM powered development for VSCode
https://github.com/SkyworkAI/Skywork,模型开发与研究,模型库/仓库,1286,SkyworkAI/Skywork,"Skywork series models are pre-trained on 3.2TB of high-quality multilingual (mainly Chinese and English) and code data. We have open-sourced the model, training data, evaluation data, evaluation methods, etc."
https://github.com/rotemweiss57/gpt-newspaper,AI实用工具,信息聚合工具,1276,rotemweiss57/gpt-newspaper,GPT based autonomous agent designed to create personalized newspapers tailored to user preferences.
https://github.com/google-research/multilingual-t5,模型开发与研究,模型库/仓库,1270,google-research/multilingual-t5,
https://github.com/lxtGH/OMG-Seg,模型开发与研究,模型库/仓库,1267,lxtGH/OMG-Seg,OMG-LLaVA and OMG-Seg codebase [CVPR-24 and NeurIPS-24]
https://github.com/mosaicml/streaming,AI部署与基础设施,数据管理平台,1266,mosaicml/streaming,A Data Streaming Library for Efficient Neural Network Training
https://github.com/opendilab/DI-star,AI实用工具,聊天机器人,1264,opendilab/DI-star,An artificial intelligence platform for the StarCraft II with large-scale distributed training and grand-master agents.
https://github.com/ray-project/ray-llm,AI部署与基础设施,模型部署工具,1263,ray-project/ray-llm,RayLLM - LLMs on Ray (Archived). Read README for more info.
https://github.com/apple/ml-aim,模型开发与研究,模型库/仓库,1257,apple/ml-aim,This repository provides the code and model checkpoints for AIMv1 and AIMv2 research projects.
https://github.com/lunary-ai/lunary,AI部署与基础设施,监控与可观测性,1256,lunary-ai/lunary,"The production toolkit for LLMs. Observability, prompt management and evaluations."
https://github.com/llmonitor/llmonitor,AI部署与基础设施,监控与可观测性,1256,llmonitor/llmonitor,"The production toolkit for LLMs. Observability, prompt management and evaluations."
https://github.com/tensorflow/text,模型开发与研究,数据工程,1253,tensorflow/text,Making text a first-class citizen in TensorFlow.
https://github.com/vercel/modelfusion,AI构建与集成,AI开发框架,1245,vercel/modelfusion,The TypeScript library for building AI applications.
https://github.com/IntelLabs/control-flag,模型开发与研究,数据工程,1243,IntelLabs/control-flag,A system to flag anomalous source code expressions by learning typical expressions from training data
https://github.com/openai/grade-school-math,AI构建与集成,评估工具,1241,openai/grade-school-math,
https://github.com/Vahe1994/AQLM,模型开发与研究,推理优化,1235,Vahe1994/AQLM,Official Pytorch repository for Extreme Compression of Large Language Models via Additive Quantization https://arxiv.org/pdf/2401.06118.pdf and PV-Tuning: Beyond Straight-Through Estimation for Extreme LLM Compression https://arxiv.org/abs/2405.14852
https://github.com/protectai/rebuff,AI构建与集成,提示工程工具,1230,protectai/rebuff,LLM Prompt Injection Detector
https://github.com/hao-ai-lab/LookaheadDecoding,模型开发与研究,推理优化,1229,hao-ai-lab/LookaheadDecoding,[ICML 2024] Break the Sequential Dependency of LLM Inference Using Lookahead Decoding
https://github.com/pytorch-labs/segment-anything-fast,模型开发与研究,推理优化,1226,pytorch-labs/segment-anything-fast,A batched offline inference oriented version of segment-anything
https://github.com/explosion/spacy-llm,AI构建与集成,提示工程工具,1223,explosion/spacy-llm,🦙 Integrating LLMs into structured NLP pipelines
https://github.com/dauparas/ProteinMPNN,模型开发与研究,模型库/仓库,1220,dauparas/ProteinMPNN,Code for the ProteinMPNN paper
https://github.com/nlmatics/nlm-ingestor,AI实用工具,信息聚合工具,1209,nlmatics/nlm-ingestor,This repo provides the server side code for llmsherpa API to connect. It includes parsers for various file formats.
https://github.com/openai/following-instructions-human-feedback,模型开发与研究,模型库/仓库,1205,openai/following-instructions-human-feedback,
https://github.com/google-research/deduplicate-text-datasets,未分类,未分类,1200,google-research/deduplicate-text-datasets,
https://github.com/timqian/openprompt.co,AI学习与资源,资源集合,1197,timqian/openprompt.co,Create. Use. Share. ChatGPT prompts
https://github.com/jncraton/languagemodels,AI实用工具,聊天机器人,1189,jncraton/languagemodels,Explore large language models in 512MB of RAM
https://github.com/Haidra-Org/AI-Horde,AI部署与基础设施,计算资源管理,1184,Haidra-Org/AI-Horde,A crowdsourced distributed cluster for AI art and text generation
https://github.com/ELLA-Diffusion/ELLA,模型开发与研究,模型库/仓库,1179,ELLA-Diffusion/ELLA,ELLA: Equip Diffusion Models with LLM for Enhanced Semantic Alignment
https://github.com/allenai/dolma,模型开发与研究,数据工程,1178,allenai/dolma,Data and tools for generating and inspecting OLMo pre-training data.
https://github.com/SamurAIGPT/Camel-AutoGPT,AI实用工具,聊天机器人,1176,SamurAIGPT/Camel-AutoGPT,"🚀 Introducing 🐪 CAMEL: a game-changing role-playing approach for LLMs and auto-agents like BabyAGI & AutoGPT! Watch two agents 🤝 collaborate and solve tasks together, unlocking endless possibilities in #ConversationalAI, 🎮 gaming, 📚 education, and more! 🔥"
https://github.com/google-research/tapas,模型开发与研究,模型库/仓库,1169,google-research/tapas,End-to-end neural table-text understanding models.
https://github.com/google/oss-fuzz-gen,AI实用工具,编程助手,1164,google/oss-fuzz-gen,LLM powered fuzzing via OSS-Fuzz.
https://github.com/parthsarthi03/raptor,AI构建与集成,AI开发框架,1162,parthsarthi03/raptor,The official implementation of RAPTOR: Recursive Abstractive Processing for Tree-Organized Retrieval
https://github.com/AI-Engineer-Foundation/agent-protocol,AI构建与集成,AI代理系统,1160,AI-Engineer-Foundation/agent-protocol,Common interface for interacting with AI agents. The protocol is tech stack agnostic - you can use it with any framework for building agents.
https://github.com/paralleldrive/sudolang-llm-support,AI实用工具,编程助手,1157,paralleldrive/sudolang-llm-support,SudoLang LLM Support for VSCode
https://github.com/declare-lab/tango,模型开发与研究,模型库/仓库,1157,declare-lab/tango,A family of diffusion models for text-to-audio generation.
https://github.com/kha-white/mokuro,AI实用工具,信息聚合工具,1150,kha-white/mokuro,Read Japanese manga inside browser with selectable text.
https://github.com/AGI-Edgerunners/LLM-Adapters,模型开发与研究,训练与微调,1145,AGI-Edgerunners/LLM-Adapters,"Code for our EMNLP 2023 Paper: ""LLM-Adapters: An Adapter Family for Parameter-Efficient Fine-Tuning of Large Language Models"""
https://github.com/IBM/Dromedary,模型开发与研究,模型库/仓库,1140,IBM/Dromedary,"Dromedary: towards helpful, ethical and reliable LLMs."
https://github.com/Cloud-CV/Fabrik,模型开发与研究,训练与微调,1127,Cloud-CV/Fabrik,":factory: Collaboratively build, visualize, and design neural nets in browser"
https://github.com/lupantech/chameleon-llm,AI构建与集成,AI代理系统,1126,lupantech/chameleon-llm,"Codes for ""Chameleon: Plug-and-Play Compositional Reasoning with Large Language Models""."
https://github.com/gabrielchua/RAGxplorer,AI构建与集成,AI开发框架,1119,gabrielchua/RAGxplorer,Open-source tool to visualise your RAG 🔮
https://github.com/axflow/axflow,AI构建与集成,AI开发框架,1116,axflow/axflow,The TypeScript framework for AI development
https://github.com/SHI-Labs/Neighborhood-Attention-Transformer,模型开发与研究,模型库/仓库,1104,SHI-Labs/Neighborhood-Attention-Transformer,"Neighborhood Attention Transformer, arxiv 2022 / CVPR 2023. Dilated Neighborhood Attention Transformer, arxiv 2022"
https://github.com/KhoomeiK/LlamaGym,模型开发与研究,训练与微调,1100,KhoomeiK/LlamaGym,Fine-tune LLM agents with online reinforcement learning
https://github.com/ricklamers/shell-ai,AI实用工具,编程助手,1092,ricklamers/shell-ai,LangChain powered shell command generator and runner CLI
https://github.com/SalesforceAIResearch/uni2ts,模型开发与研究,模型库/仓库,1076,SalesforceAIResearch/uni2ts,Unified Training of Universal Time Series Forecasting Transformers
https://github.com/THUDM/SwissArmyTransformer,模型开发与研究,模型库/仓库,1074,THUDM/SwissArmyTransformer,SwissArmyTransformer is a flexible and powerful library to develop your own Transformer variants.
https://github.com/rlancemartin/auto-evaluator,AI构建与集成,评估工具,1073,rlancemartin/auto-evaluator,Evaluation tool for LLM QA chains
https://github.com/google/generative-ai-swift,AI构建与集成,AI开发框架,1067,google/generative-ai-swift,The official Swift library for the Google Gemini API
https://github.com/kantord/SeaGOAT,AI实用工具,编程助手,1055,kantord/SeaGOAT,local-first semantic code search engine
https://github.com/OpenBMB/VisCPM,模型开发与研究,模型库/仓库,1055,OpenBMB/VisCPM,[ICLR'24 spotlight] Chinese and English Multimodal Large Model Series (Chat and Paint) | 基于CPM基础模型的中英双语多模态大模型系列
