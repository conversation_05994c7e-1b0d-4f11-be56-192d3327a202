<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI开源项目分类目录</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <div class="container">
            <h1><i class="fas fa-project-diagram"></i> AI开源项目分类目录</h1>
            <p>按照五大类别组织的优质GitHub项目资源集</p>
        </div>
    </header>

    <div class="container unified-container">
        <!-- 搜索、筛选和排序功能 -->
        <div class="search-container">
            <div class="search-box">
                <input type="text" id="search-input" placeholder="搜索项目...">
                <i class="fas fa-search search-icon"></i>
            </div>
            <div class="filter-sort-container">
                <div class="filter-dropdown">
                    <select id="language-filter">
                        <option value="all">所有语言</option>
                        <option value="Python">Python</option>
                        <option value="JavaScript">JavaScript</option>
                        <option value="TypeScript">TypeScript</option>
                        <option value="C++">C++</option>
                        <option value="Java">Java</option>
                        <option value="Go">Go</option>
                        <option value="Rust">Rust</option>
                    </select>
                </div>
                <div class="sort-dropdown">
                    <select id="sort-option">
                        <option value="stars-desc">按星标数量 (多到少)</option>
                        <option value="stars-asc">按星标数量 (少到多)</option>
                        <option value="name-asc">按名称排序 (A-Z)</option>
                        <option value="name-desc">按名称排序 (Z-A)</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- 分类Tab导航 -->
        <div class="category-tabs">
            <div class="tabs-container">
                <button class="tab-btn active" data-category="1"><i class="fas fa-laptop-code"></i> 模型开发与研究</button>
                <button class="tab-btn" data-category="2"><i class="fas fa-robot"></i> AI构建与集成</button>
                <button class="tab-btn" data-category="3"><i class="fas fa-database"></i> AI实用工具</button>
                <button class="tab-btn" data-category="4"><i class="fas fa-server"></i> AI部署与基础设施</button>
                <button class="tab-btn" data-category="5"><i class="fas fa-cogs"></i> AI学习与资源</button>
            </div>
        </div>
        
        <!-- 内容区域 -->
        <div class="content-area">
            <section class="category active" id="category-1">
                <div class="subcategories">
                    <div class="subcategory" id="subcategory-1.1">
                        <h3>1.1 模型库/仓库</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag easy">难度: 低</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">预训练性能</span>
                                <span class="tag tag-dimension">泛化能力</span>
                                <span class="tag tag-dimension">任务适应性</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 通过标准基准测试（MMLU/HumanEval）与同类模型对比性能，评估模型在未见任务上的表现
                        </div>
                        <div class="projects-container"></div>
                    </div>
                    <div class="subcategory" id="subcategory-1.2">
                        <h3>1.2 训练与微调</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag medium">难度: 中</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">训练效率</span>
                                <span class="tag tag-dimension">资源需求</span>
                                <span class="tag tag-dimension">适应性</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 评估训练速度、收敛性和资源消耗，比较不同微调方法的效果与效率
                        </div>
                        <div class="projects-container"></div>
                    </div>
                    <div class="subcategory" id="subcategory-1.3">
                        <h3>1.3 推理优化</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag medium-hard">难度: 中高</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">推理速度</span>
                                <span class="tag tag-dimension">内存效率</span>
                                <span class="tag tag-dimension">准确性保留</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 测量推理延迟、吞吐量、内存使用和精度损失，在不同硬件平台上进行比较
                        </div>
                        <div class="projects-container"></div>
                    </div>
                    <div class="subcategory" id="subcategory-1.4">
                        <h3>1.4 数据工程</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag medium">难度: 中</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">数据质量</span>
                                <span class="tag tag-dimension">扩展性</span>
                                <span class="tag tag-dimension">多样性</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 评估数据集的多样性、代表性、质量和对模型性能的影响
                        </div>
                        <div class="projects-container"></div>
                    </div>
                    <div class="subcategory" id="subcategory-1.5">
                        <h3>1.5 多模态模型</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag hard">难度: 高</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">跨模态理解</span>
                                <span class="tag tag-dimension">多模态融合</span>
                                <span class="tag tag-dimension">泛化能力</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 测试多种模态输入的理解和生成能力，评估跨模态学习的效果
                        </div>
                        <div class="projects-container"></div>
                    </div>
                    <div class="subcategory" id="subcategory-1.6">
                        <h3>1.6 多语言模型</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag medium-hard">难度: 中高</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">语言覆盖度</span>
                                <span class="tag tag-dimension">跨语言表现</span>
                                <span class="tag tag-dimension">低资源语言支持</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 在多种语言上测试性能，特别关注低资源语言和跨语言任务的表现
                        </div>
                        <div class="projects-container"></div>
                    </div>
                    <div class="subcategory" id="subcategory-1.7">
                        <h3>1.7 代码模型</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag medium-hard">难度: 中高</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">代码生成</span>
                                <span class="tag tag-dimension">代码理解</span>
                                <span class="tag tag-dimension">多语言支持</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 使用HumanEval等基准测试代码生成能力，评估代码理解和多语言编程能力
                        </div>
                        <div class="projects-container"></div>
                    </div>
                </div>
            </section>

            <section class="category" id="category-2">
                <div class="subcategories">
                    <div class="subcategory" id="subcategory-2.1">
                        <h3>2.1 AI开发框架</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag medium">难度: 中</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">易用性</span>
                                <span class="tag tag-dimension">功能完整性</span>
                                <span class="tag tag-dimension">扩展性</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 评估API设计、文档质量、功能完整性和构建复杂应用的能力
                        </div>
                        <div class="projects-container"></div>
                    </div>
                    <div class="subcategory" id="subcategory-2.2">
                        <h3>2.2 AI代理系统</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag hard">难度: 高</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">自主性</span>
                                <span class="tag tag-dimension">任务执行</span>
                                <span class="tag tag-dimension">推理能力</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 测试代理的自主性、任务规划、工具使用和长期目标追踪能力
                        </div>
                        <div class="projects-container"></div>
                    </div>
                    <div class="subcategory" id="subcategory-2.3">
                        <h3>2.3 AI接口工具</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag medium">难度: 中</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">UI/UX</span>
                                <span class="tag tag-dimension">交互设计</span>
                                <span class="tag tag-dimension">技术兼容性</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 评估用户体验、界面设计、响应速度和多平台适配能力
                        </div>
                        <div class="projects-container"></div>
                    </div>
                    <div class="subcategory" id="subcategory-2.4">
                        <h3>2.4 提示工程工具</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag medium">难度: 中</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">提示优化</span>
                                <span class="tag tag-dimension">可复用性</span>
                                <span class="tag tag-dimension">结果一致性</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 测试提示模板的效果、可迁移性和对不同模型的适应性
                        </div>
                        <div class="projects-container"></div>
                    </div>
                    <div class="subcategory" id="subcategory-2.5">
                        <h3>2.5 评估工具</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag medium">难度: 中</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">全面性</span>
                                <span class="tag tag-dimension">可靠性</span>
                                <span class="tag tag-dimension">可比性</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 评估测试覆盖面、指标选择的合理性和评估结果的一致性
                        </div>
                        <div class="projects-container"></div>
                    </div>
                    <div class="subcategory" id="subcategory-2.6">
                        <h3>2.6 RAG系统</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag medium-hard">难度: 中高</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">检索准确性</span>
                                <span class="tag tag-dimension">知识整合</span>
                                <span class="tag tag-dimension">回答质量</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 评估信息检索的准确性、相关性、回答生成质量和处理复杂查询的能力
                        </div>
                        <div class="projects-container"></div>
                    </div>
                </div>
            </section>

            <section class="category" id="category-3">
                <div class="subcategories">
                    <div class="subcategory" id="subcategory-3.1">
                        <h3>3.1 编程助手</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag medium">难度: 中</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">代码质量</span>
                                <span class="tag tag-dimension">语言支持</span>
                                <span class="tag tag-dimension">开发体验</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 评估代码生成准确性、代码质量、多语言支持和与IDE集成的效果
                        </div>
                        <div class="projects-container"></div>
                    </div>
                    <div class="subcategory" id="subcategory-3.2">
                        <h3>3.2 信息聚合工具</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag medium">难度: 中</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">信息准确性</span>
                                <span class="tag tag-dimension">知识广度</span>
                                <span class="tag tag-dimension">总结能力</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 测试信息检索准确性、信息综合能力和对复杂信息的解析能力
                        </div>
                        <div class="projects-container"></div>
                    </div>
                    <div class="subcategory" id="subcategory-3.3">
                        <h3>3.3 工作流自动化</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag medium">难度: 中</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">自动化程度</span>
                                <span class="tag tag-dimension">适应性</span>
                                <span class="tag tag-dimension">可靠性</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 评估自动化流程的稳定性、错误处理能力和与现有系统的集成效果
                        </div>
                        <div class="projects-container"></div>
                    </div>
                    <div class="subcategory" id="subcategory-3.4">
                        <h3>3.4 聊天机器人</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag medium">难度: 中</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">对话能力</span>
                                <span class="tag tag-dimension">上下文理解</span>
                                <span class="tag tag-dimension">个性化</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 评估对话流畅度、上下文理解、长对话能力和任务执行准确性
                        </div>
                        <div class="projects-container"></div>
                    </div>
                    <div class="subcategory" id="subcategory-3.5">
                        <h3>3.5 图像创作工具</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag medium-hard">难度: 中高</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">图像质量</span>
                                <span class="tag tag-dimension">创意控制</span>
                                <span class="tag tag-dimension">生成速度</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 评估图像质量、文本提示符遵循能力和用户控制丰富度
                        </div>
                        <div class="projects-container"></div>
                    </div>
                    <div class="subcategory" id="subcategory-3.6">
                        <h3>3.6 视频制作工具</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag hard">难度: 高</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">视频质量</span>
                                <span class="tag tag-dimension">时间一致性</span>
                                <span class="tag tag-dimension">编辑灵活性</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 评估视频质量、连贯性、时间一致性和用户控制能力
                        </div>
                        <div class="projects-container"></div>
                    </div>
                    <div class="subcategory" id="subcategory-3.7">
                        <h3>3.7 文本创作工具</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag medium">难度: 中</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">文本质量</span>
                                <span class="tag tag-dimension">风格控制</span>
                                <span class="tag tag-dimension">创意支持</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 评估文本质量、风格适应性、创意支持和用户交互体验
                        </div>
                        <div class="projects-container"></div>
                    </div>
                    <div class="subcategory" id="subcategory-3.8">
                        <h3>3.8 数据组织工具</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag medium">难度: 中</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">组织效率</span>
                                <span class="tag tag-dimension">数据处理</span>
                                <span class="tag tag-dimension">可视化能力</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 评估数据处理能力、组织逻辑性和可视化效果
                        </div>
                        <div class="projects-container"></div>
                    </div>
                </div>
            </section>

            <section class="category" id="category-4">
                <div class="subcategories">
                    <div class="subcategory" id="subcategory-4.1">
                        <h3>4.1 向量数据库</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag medium-hard">难度: 中高</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">查询性能</span>
                                <span class="tag tag-dimension">扩展性</span>
                                <span class="tag tag-dimension">索引效率</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 评估查询速度、扩展性、数据导入性能和索引质量
                        </div>
                        <div class="projects-container"></div>
                    </div>
                    <div class="subcategory" id="subcategory-4.2">
                        <h3>4.2 模型部署工具</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag medium-hard">难度: 中高</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">部署简易度</span>
                                <span class="tag tag-dimension">性能优化</span>
                                <span class="tag tag-dimension">可扩展性</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 评估部署流程简便性、资源利用效率和服务可靠性
                        </div>
                        <div class="projects-container"></div>
                    </div>
                    <div class="subcategory" id="subcategory-4.3">
                        <h3>4.3 计算资源管理</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag hard">难度: 高</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">资源利用</span>
                                <span class="tag tag-dimension">调度效率</span>
                                <span class="tag tag-dimension">容错能力</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 评估资源利用率、调度效率、扩展性和故障恢复能力
                        </div>
                        <div class="projects-container"></div>
                    </div>
                    <div class="subcategory" id="subcategory-4.4">
                        <h3>4.4 监控与可观测性</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag medium">难度: 中</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">监控全面性</span>
                                <span class="tag tag-dimension">可视化</span>
                                <span class="tag tag-dimension">告警机制</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 评估监控覆盖范围、可视化质量、告警机制和问题诊断能力
                        </div>
                        <div class="projects-container"></div>
                    </div>
                    <div class="subcategory" id="subcategory-4.5">
                        <h3>4.5 开发工具集成</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag medium">难度: 中</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">集成度</span>
                                <span class="tag tag-dimension">开发体验</span>
                                <span class="tag tag-dimension">功能完整性</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 评估与现有工具的集成度、开发流程改进和功能完整性
                        </div>
                        <div class="projects-container"></div>
                    </div>
                    <div class="subcategory" id="subcategory-4.6">
                        <h3>4.6 数据管理平台</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag medium-hard">难度: 中高</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">数据处理</span>
                                <span class="tag tag-dimension">版本控制</span>
                                <span class="tag tag-dimension">协作支持</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 评估数据处理能力、版本控制机制和多人协作支持
                        </div>
                        <div class="projects-container"></div>
                    </div>
                </div>
            </section>

            <section class="category" id="category-5">
                <div class="subcategories">
                    <div class="subcategory" id="subcategory-5.1">
                        <h3>5.1 教程与课程</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag easy">难度: 低</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">内容质量</span>
                                <span class="tag tag-dimension">覆盖范围</span>
                                <span class="tag tag-dimension">教学效果</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 评估内容质量、覆盖范围、教学方法和学习效果
                        </div>
                        <div class="projects-container"></div>
                    </div>
                    <div class="subcategory" id="subcategory-5.2">
                        <h3>5.2 资源集合</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag easy">难度: 低</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">全面性</span>
                                <span class="tag tag-dimension">组织结构</span>
                                <span class="tag tag-dimension">实用价值</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 评估资源收集的全面性、组织结构的合理性和实用价值
                        </div>
                        <div class="projects-container"></div>
                    </div>
                    <div class="subcategory" id="subcategory-5.3">
                        <h3>5.3 示例与演示</h3>
                        <div class="subcategory-info">
                            <div class="difficulty-tag easy-medium">难度: 低中</div>
                            <div class="tags-container">
                                <span class="tag tag-capability">示例质量</span>
                                <span class="tag tag-dimension">覆盖广度</span>
                                <span class="tag tag-dimension">实用性</span>
                            </div>
                        </div>
                        <div class="evaluation-suggestion">
                            <strong>评测建议:</strong> 评估示例的质量、覆盖范围和实际应用价值
                        </div>
                        <div class="projects-container"></div>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <footer>
        <div class="container">
            <p>© 2025 AI开源项目分类目录 | 基于GitHub优质项目整理</p>
        </div>
    </footer>

    <div class="back-to-top">
        <i class="fas fa-arrow-up"></i>
    </div>

    <script src="script.js"></script>
</body>
</html> 