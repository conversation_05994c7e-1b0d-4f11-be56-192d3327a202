/* 折叠/展开相关样式 */
.hidden-project {
    display: none !important;
}

.hidden {
    display: none !important;
}

.show-more-btn, .show-less-btn {
    width: 100%;
    margin: 15px auto;
    padding: 10px;
    background-color: #f0f0f0;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.show-more-btn:hover, .show-less-btn:hover {
    background-color: #e0e0e0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.show-more-btn i, .show-less-btn i {
    margin-right: 5px;
    transition: transform 0.3s ease;
}

.show-more-btn:active, .show-less-btn:active {
    transform: translateY(1px);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 移动设备适配 */
@media (max-width: 768px) {
    .show-more-btn, .show-less-btn {
        padding: 8px;
        font-size: 0.9rem;
    }
}
