﻿repo,category,subcat,stars,star_1d,star_1d_pct,star_7d,star_7d_pct,forks,description,top_devs,contributors,created_at,updated_at,downloads*
modelcontextprotocol/servers,AI engineering,AIE framework,31389,436,1.40%,3969,12.64%,3316,Model Context Protocol Servers,"jspahrsummers, tadasant, dsp-ant",100+,2024-11-19,2025-04-04,0
unclecode/crawl4ai,AI engineering,Dataset engineering,37416,359,0.96%,2962,7.92%,3295,"🚀🤖 Crawl4AI: Open-source LLM Friendly Web Crawler & Scraper. Don't be shy, join here: https://discord.gg/jP8KfhDhyN","unclecode, aravindkarnam, bizrockman",31,2024-05-09,2025-03-31,0
browser-use/browser-use,AI engineering,Agent,53306,343,0.64%,3097,5.81%,5644,Make websites accessible for AI agents,"Mag<PERSON>ueller, pirate, gregpr07",100+,2024-10-31,2025-04-05,5
chiphuyen/aie-book,Tutorials,,3686,132,3.67%,627,17.01%,438,"[WIP] Resources for AI engineers. Also contains supporting materials for the book AI Engineering (Chip Huyen, 2025)","chiphuyen, chris-alexiuk, strickvl",5,2024-12-03,2025-01-15,0
datawhalechina/prompt-engineering-for-developers,Tutorials,,16798,129,0.77%,277,1.65%,2095,面向开发者的 LLM 入门教程，吴恩达大模型系列课程中文版,"logan-zou, Beyondzjl, xuhu0115",35,2023-04-28,2025-02-20,4491
Shubhamsaboo/awesome-llm-apps,Lists,,27215,128,0.47%,2107,7.74%,3063,"Collection of awesome LLM apps with AI Agents and RAG using OpenAI, Anthropic, Gemini and opensource models.","Shubhamsaboo, Madhuvod, CodeWithCharan",24,2024-04-29,2025-03-29,0
tensorflow/tensorflow,Model development,Modeling & training,189230,120,0.06%,338,0.18%,74630,An Open Source Machine Learning Framework for Everyone,"ezhulenev, mihaimaruseac, akuegel",100+,2015-11-07,2025-04-06,0
langgenius/dify,AI engineering,AIE framework,89444,105,0.12%,1758,1.97%,13316,"Dify is an open-source LLM app development platform. Dify's intuitive interface combines AI workflow, RAG pipeline, agent capabilities, model management, observability features and more, letting you quickly go from prototype to production.","takatost, laipz8200, iamjoel",100+,2023-04-12,2025-04-05,0
jmorganca/ollama,AI engineering,AI interface,136130,91,0.07%,813,0.60%,11324,"Get up and running with Llama 3.3, DeepSeek-R1, Phi-4, Gemma 3, and other large language models.","mxyng, jmorganca, dhiltgen",100+,2023-06-26,2025-04-05,2445404
phidatahq/phidata,AI engineering,Agent,23983,88,0.37%,868,3.62%,3057,"A lightweight library for building Multimodal Agents. Give LLMs superpowers like memory, knowledge, tools and reasoning.","ashpreetbedi, ysolanky, dirkbrnd",100+,2022-05-04,2025-04-05,0
logspace-ai/langflow,AI engineering,AIE framework,54159,81,0.15%,831,1.53%,5933,Langflow is a powerful tool for building and deploying AI-powered agents and workflows.,"ogabrielluiz, anovazzi1, lucaseduoli",100+,2023-02-08,2025-04-03,1207
mendableai/firecrawl,AI engineering,Dataset engineering,34174,80,0.23%,737,2.16%,2974,"🔥 Turn entire websites into LLM-ready markdown or structured data. Scrape, crawl and extract with a single API.","nickscamara, mogery, rafaelsideguide",81,2024-04-15,2025-04-04,0
comfyanonymous/ComfyUI,AI engineering,AIE framework,73453,60,0.08%,704,0.96%,7961,"The most powerful and modular diffusion model GUI, api and backend with a graph/nodes interface.","comfyanonymous, pythongosssss, huchenlei",100+,2023-01-17,2023-05-31,650477
infiniflow/ragflow,AI engineering,AIE framework,47978,59,0.12%,879,1.83%,4478,RAGFlow is an open-source RAG (Retrieval-Augmented Generation) engine based on deep document understanding.,"KevinHuSh, cike8899, writinwaters",100+,2023-12-12,2025-04-03,0
hacksider/Deep-Live-Cam,Applications,Video production,49604,59,0.12%,861,1.74%,7287,real time face swap and one-click video deepfake with only a single image,"hacksider, KRSHH, vic4key",37,2023-09-24,2025-03-24,0
openai/whisper,Model repo,,79460,53,0.07%,351,0.44%,9550,Robust Speech Recognition via Large-Scale Weak Supervision,"jongwook, ryanheise, cclauss",72,2022-09-16,2025-01-04,0
All-Hands-AI/OpenHands,Applications,Coding,52433,50,0.10%,859,1.64%,5814,"🙌 OpenHands: Code Less, Make More","xingyaoww, rbren, enyst",100+,2024-03-13,2025-04-06,0
vllm-project/vllm,Infrastructure,Serving,43553,49,0.11%,521,1.20%,6648,A high-throughput and memory-efficient inference and serving engine for LLMs,"WoosukKwon, youkaichao, DarkLight1337",100+,2023-02-09,2025-02-06,9027
huggingface/smolagents,AI engineering,Agent,16452,47,0.29%,460,2.80%,1446,🤗 smolagents: a barebones library for agents that think in python code.,"aymeric-roucher, albertvillanova, keetrap",100+,2024-12-05,2025-02-06,0
pydantic/pydantic-ai,AI engineering,Prompt engineering,8136,44,0.54%,288,3.54%,699,Agent Framework / shim to use Pydantic with LLMs,"samuelcolvin, Kludex, dmontagu",87,2024-06-21,2025-04-05,0
Mintplex-Labs/anything-llm,Applications,Info aggregation,42285,43,0.10%,381,0.90%,4086,"The all-in-one Desktop & Docker AI application with built-in RAG, AI agents, No-code agent builder, MCP compatibility,  and more.","timothycarambat, shatfield4, MrSimonC",100+,2023-06-04,2025-04-04,0
llmware-ai/llmware,AI engineering,AIE framework,12610,42,0.33%,481,3.81%,1799,"Unified framework for building enterprise RAG pipelines with small, specialized models","doberst, MacOS, turnham",76,2023-09-29,2025-03-18,0
AUTOMATIC1111/stable-diffusion-webui,Applications,Image production,150721,42,0.03%,526,0.35%,28067,Stable Diffusion web UI,"AUTOMATIC1111, w-e-w, dfaker",100+,2022-08-22,2025-03-04,531852
jingyaogong/minimind,Model development,Modeling & training,18370,40,0.22%,925,5.04%,2067,🚀🚀 「大模型」2小时完全从0训练26M的小参数GPT！🌏 Train a 26M-parameter GPT from scratch in just 2h!,"jingyaogong, iomgaa-ycz, chuanzhubin",4,2024-07-27,2024-09-20,0
paul-gauthier/aider,Applications,Coding,30633,40,0.13%,423,1.38%,2778,aider is AI pair programming in your terminal,"paul-gauthier, joshuavial, fry69",100+,2023-05-09,2025-04-05,0
DS4SD/docling,AI engineering,Dataset engineering,26056,39,0.15%,436,1.67%,1568,Get your documents ready for gen AI,"dolfim-ibm, vagenas, cau-git",57,2024-07-09,2025-04-03,0
unslothai/unsloth,Model development,Inference optimization,36582,39,0.11%,467,1.28%,2838,"Finetune Llama 4, DeepSeek-R1, Gemma 3 & Reasoning LLMs 2x faster with 70% less memory! 🦥","danielhanchen, shimmyshimmer, jeromeku",34,2023-11-29,2025-04-03,4
deepseek-ai/DeepSeek-V3,Model repo,,95102,39,0.04%,475,0.50%,15404,,"mowentian, GeeeekExplorer, DeepSeekDDM",18,2024-12-26,2025-02-18,0
BerriAI/litellm,AI engineering,AIE framework,20255,38,0.19%,383,1.89%,2559,"Python SDK, Proxy Server (LLM Gateway) to call 100+ LLM APIs in OpenAI format - [Bedrock, Azure, OpenAI, VertexAI, Cohere, Anthropic, Sagemaker, HuggingFace, Replicate, Groq]","ishaan-jaff, krrishdholakia, Manouchehri",100+,2023-07-27,2025-04-05,1337
microsoft/generative-ai-for-beginners,Tutorials,,77744,38,0.05%,1316,1.69%,40092,"21 Lessons, Get Started Building with Generative AI  🔗 https://microsoft.github.io/generative-ai-for-beginners/","koreyspace, john0isaac, yoshioterada",100+,2023-06-19,2025-03-27,0
OpenBB-finance/OpenBB,Applications,Info aggregation,40091,37,0.09%,297,0.74%,3577,"Investment Research for Everyone, Everywhere.","jmaslek, deeleeramone, montezdesousa",100+,2020-12-20,2025-04-04,155931
hiyouga/LLaMA-Factory,Model development,Modeling & training,46103,36,0.08%,484,1.05%,5629,Unified Efficient Fine-Tuning of 100+ LLMs & VLMs (ACL 2024),"hiyouga, BUAADreamer, codemayq",100+,2023-05-28,2025-04-03,45
langchain-ai/langchain,AI engineering,AIE framework,105031,36,0.03%,464,0.44%,17038,🦜🔗 Build context-aware reasoning applications,"baskaryan, hwchase17, eyurtsev",100+,2022-10-17,2025-04-04,4816
e2b-dev/awesome-ai-agents,Lists,,16760,35,0.21%,287,1.71%,1265,A list of AI autonomous agents,"tizkovatereza, mlejva, zhimin-z",54,2023-06-19,2023-06-23,0
danny-avila/LibreChat,AI engineering,AI interface,24126,34,0.14%,293,1.21%,4061,"Enhanced ChatGPT Clone: Features Agents, DeepSeek, Anthropic, AWS, OpenAI, Assistants API, Azure, Groq, o1, GPT-4o, Mistral, OpenRouter, Vertex AI, Gemini, Artifacts, AI model switching, message search, Code Interpreter, langchain, DALL-E-3, OpenAPI Actions, Functions, Secure Multi-User Auth, Presets, open-source for self-hosting. Active project.","danny-avila, berry-13, wtlyu",100+,2023-02-12,2025-04-04,0
joaomdmoura/crewAI,AI engineering,Agent,29564,33,0.11%,334,1.13%,4002,"Framework for orchestrating role-playing, autonomous AI agents. By fostering collaborative intelligence, CrewAI empowers agents to work together seamlessly, tackling complex tasks.","joaomdmoura, bhancockio, lorenzejay",100+,2023-10-27,2025-04-05,0
rasbt/LLMs-from-scratch,Tutorials,,43629,32,0.07%,381,0.87%,6036,"Implement a ChatGPT-like LLM in PyTorch from scratch, step by step","rasbt, d-kleine, Intelligence-Manifesto",38,2023-07-23,2025-04-05,0
danielmiessler/fabric,Applications,Workflow automation,30480,32,0.11%,174,0.57%,3145,fabric is an open-source framework for augmenting humans using AI. It provides a modular framework for solving specific problems using a crowdsourced set of AI prompts that can be used anywhere.,"danielmiessler, eugeis, xssdoctor",100+,2024-01-03,2025-03-26,5970
deepseek-ai/DeepSeek-R1,Model repo,,88084,32,0.04%,331,0.38%,11378,,"Konano, stack-heap-overflow, DeepSeekPH",8,2025-01-20,2025-02-08,0
Sinaptik-AI/pandas-ai,Applications,Coding,18868,31,0.16%,192,1.02%,1772,"Chat with your database or your datalake (SQL, CSV, parquet). PandasAI makes data analysis conversational using LLMs and RAG.","gventuri, ArslanSaleem, scaliseraoul",100+,2023-04-22,2025-03-20,0
go-skynet/LocalAI,AI engineering,AIE framework,31515,31,0.10%,216,0.69%,2393,":robot: The free, Open Source alternative to OpenAI, Claude and others. Self-hosted and local-first. Drop-in replacement for OpenAI,  running on consumer-grade hardware. No GPU required. Runs gguf, transformers, diffusers and many more models architectures. Features: Generate Text, Audio, Video, Images, Voice Cloning, Distributed, P2P inference","mudler, localai-bot, ci-robbot",100+,2023-03-18,2025-04-05,7069
microsoft/autogen,AI engineering,Agent,42699,30,0.07%,333,0.78%,6394,A programming framework for agentic AI 🤖 PyPi: autogen-agentchat Discord: https://aka.ms/autogen-discord Office Hour: https://aka.ms/autogen-officehour,"jackgerrits, ekzhu, sonichi",100+,2023-08-18,2025-04-04,0
geekan/MetaGPT,AI engineering,Agent,54215,30,0.06%,393,0.72%,6427,"🌟 The Multi-Agent Framework: First AI Software Company, Towards Natural Language Programming","geekan, garylin2099, better629",100+,2023-06-30,2024-08-07,583
pytorch/pytorch,Model development,Modeling & training,88676,30,0.03%,303,0.34%,23785,Tensors and Dynamic neural networks in Python with strong GPU acceleration,"ezyang, malfet, zou3519",100+,2016-08-13,2025-04-04,29899
getzep/graphiti,AI engineering,AIE framework,3311,29,0.88%,328,9.91%,257,Build Real-Time Knowledge Graphs for AI Agents,"prasmussen15, danielchalef, paul-paliychuk",8,2024-08-08,2025-04-04,0
langchain-ai/langgraph,AI engineering,Agent,11108,29,0.26%,297,2.67%,1856,Build resilient language agents as graphs.,"nfcampos, vbarda, hinthornw",100+,2023-08-09,2025-04-04,0
Significant-Gravitas/AutoGPT,AI engineering,Agent,174196,29,0.02%,251,0.14%,45525,"AutoGPT is the vision of accessible AI for everyone, to use and to build on. Our mission is to provide the tools, so that you can focus on what matters.","Pwuts, waynehamadi, Torantulino",100+,2023-03-16,2025-04-04,3510
langfuse/langfuse,Infrastructure,Monitoring,10123,27,0.27%,241,2.38%,925,"🪢 Open source LLM engineering platform: LLM Observability, metrics, evals, prompt management, playground, datasets. Integrates with OpenTelemetry, Langchain, OpenAI SDK, LiteLLM, and more. 🍊YC W23 ","marcklingen, maxdeichmann, Steffen911",66,2023-05-18,2025-04-05,0
embedchain/embedchain,AI engineering,AIE framework,27214,25,0.09%,276,1.01%,2590,The Memory layer for AI Agents,"Dev-Khant, taranjeet, deshraj",100+,2023-06-20,2025-04-03,0
mem0ai/mem0,AI engineering,Agent,27214,25,0.09%,276,1.01%,2590,The Memory layer for AI Agents,"Dev-Khant, taranjeet, deshraj",100+,2023-06-20,2025-04-03,0
ggerganov/llama.cpp,Model development,Inference optimization,77688,25,0.03%,334,0.43%,11325,LLM inference in C/C++,"ggerganov, slaren, JohannesGaessler",100+,2023-03-10,2025-04-05,400937
f/awesome-chatgpt-prompts,Lists,Prompt engineering,122606,25,0.02%,434,0.35%,16433,This repo includes ChatGPT prompt curation to use ChatGPT and other LLM tools better.,"f, iuzn, fengkiej",100+,2022-12-05,2025-03-14,0
SciPhi-AI/R2R,AI engineering,AIE framework,6198,24,0.39%,218,3.52%,470,SoTA production-ready AI retrieval system. Agentic Retrieval-Augmented Generation (RAG) with a RESTful API.,"emrgnt-cmplxty, NolanTrem, jcllobet",49,2024-02-12,2025-04-04,0
SillyTavern/SillyTavern,AI engineering,AI interface,13153,24,0.18%,199,1.51%,3029,LLM Frontend for Power Users.,"Cohee1207, Wolfsblvt, RossAscends",100+,2023-02-09,2025-04-04,9
RVC-Boss/GPT-SoVITS,AI engineering,AI interface,43652,24,0.05%,331,0.76%,4857,1 min voice data can also be used to train a good TTS model! (few shot voice cloning),"RVC-Boss, KamioRinn, XXXXRT666",82,2024-01-14,2025-03-31,0
Skyvern-AI/skyvern,Applications,Workflow automation,12885,23,0.18%,94,0.73%,996,Automate browser-based workflows with LLMs and Computer Vision,"wintonzheng, ykeremy, LawyZheng",35,2024-02-28,2025-04-06,0
liguodongiot/llm-action,Tutorials,,16130,22,0.14%,275,1.70%,1874,本项目旨在分享大模型相关技术原理以及实战经验（大模型工程化、大模型应用落地）,liguodongiot,1,2023-05-23,1000-01-01,0
vercel/ai,AI engineering,AIE framework,13169,22,0.17%,153,1.16%,2035,"The AI Toolkit for TypeScript. From the creators of Next.js, the AI SDK is a free open-source library for building AI-powered applications and agents ","lgrammel, shaper, jaredpalmer",100+,2023-05-23,2025-04-05,0
FlowiseAI/Flowise,AI engineering,AIE framework,36946,22,0.06%,242,0.66%,19248,Drag & drop UI to build your customized LLM flow,"HenryHengZJ, chungyau97, vinodkiran",100+,2023-03-31,2025-04-04,0
volcengine/verl,Model development,Modeling & training,6190,21,0.34%,321,5.19%,635,verl: Volcano Engine Reinforcement Learning for LLMs,"eric-haibin-lin, PeterSH6, vermouth1992",100+,2024-10-31,2025-04-06,0
camel-ai/camel,Applications,Bots,11506,21,0.18%,235,2.04%,1215,🐫 CAMEL: The first and the best multi-agent framework. Finding the Scaling Law of Agents. https://www.camel-ai.org,"Wendong-Fan, lightaime, dmitrii-khizbullin",100+,2023-03-17,2025-04-06,188
run-llama/llama_index,AI engineering,AIE framework,40709,21,0.05%,216,0.53%,5798,LlamaIndex is the leading framework for building LLM-powered agents over your data.,"logan-markewich, jerryjliu, Disiok",100+,2022-11-02,2025-04-04,1388
fighting41love/funNLP,Lists,,72199,21,0.03%,175,0.24%,14778,"中英文敏感词、语言检测、中外手机/电话归属地/运营商查询、名字推断性别、手机号抽取、身份证抽取、邮箱抽取、中日文人名库、中文缩写库、拆字词典、词汇情感值、停用词、反动词表、暴恐词表、繁简体转换、英文模拟中文发音、汪峰歌词生成器、职业名称词库、同义词库、反义词库、否定词库、汽车品牌词库、汽车零件词库、连续英文切割、各种中文词向量、公司名字大全、古诗词库、IT词库、财经词库、成语词库、地名词库、历史名人词库、诗词词库、医学词库、饮食词库、法律词库、汽车词库、动物词库、中文聊天语料、中文谣言数据、百度中文问答数据集、句子相似度匹配算法集合、bert资源、文本生成&摘要相关工具、cocoNLP信息抽取工具、国内电话号码正则匹配、清华大学XLORE:中英文跨语言百科知识图谱、清华大学人工智能技术系列报告、自然语言生成、NLU太难了系列、自动对联数据及机器人、用户名黑名单列表、罪名法务名词及分类模型、微信公众号语料、cs224n深度学习自然语言处理课程、中文手写汉字识别、中文自然语言处理 语料/数据集、变量命名神器、分词语料库+代码、任务型对话英文数据集、ASR 语音数据集 + 基于深度学习的中文语音识别系统、笑声检测器、Microsoft多语言数字/单位/如日期时间识别包、中华新华字典数据库及api(包括常用歇后语、成语、词语和汉字)、文档图谱自动生成、SpaCy 中文模型、Common Voice语音识别数据集新版、神经网络关系抽取、基于bert的命名实体识别、关键词(Keyphrase)抽取包pke、基于医疗领域知识图谱的问答系统、基于依存句法与语义角色标注的事件三元组抽取、依存句法分析4万句高质量标注数据、cnocr：用来做中文OCR的Python3包、中文人物关系知识图谱项目、中文nlp竞赛项目及代码汇总、中文字符数据、speech-aligner: 从“人声语音”及其“语言文本”产生音素级别时间对齐标注的工具、AmpliGraph: 知识图谱表示学习(Python)库：知识图谱概念链接预测、Scattertext 文本可视化(python)、语言/知识表示工具：BERT & ERNIE、中文对比英文自然语言处理NLP的区别综述、Synonyms中文近义词工具包、HarvestText领域自适应文本挖掘工具（新词发现-情感分析-实体链接等）、word2word：(Python)方便易用的多语言词-词对集：62种语言/3,564个多语言对、语音识别语料生成工具：从具有音频/字幕的在线视频创建自动语音识别(ASR)语料库、构建医疗实体识别的模型（包含词典和语料标注）、单文档非监督的关键词抽取、Kashgari中使用gpt-2语言模型、开源的金融投资数据提取工具、文本自动摘要库TextTeaser: 仅支持英文、人民日报语料处理工具集、一些关于自然语言的基本模型、基于14W歌曲知识库的问答尝试--功能包括歌词接龙and已知歌词找歌曲以及歌曲歌手歌词三角关系的问答、基于Siamese bilstm模型的相似句子判定模型并提供训练数据集和测试数据集、用Transformer编解码模型实现的根据Hacker News文章标题自动生成评论、用BERT进行序列标记和文本分类的模板代码、LitBank：NLP数据集——支持自然语言处理和计算人文学科任务的100部带标记英文小说语料、百度开源的基准信息抽取系统、虚假新闻数据集、Facebook: LAMA语言模型分析，提供Transformer-XL/BERT/ELMo/GPT预训练语言模型的统一访问接口、CommonsenseQA：面向常识的英文QA挑战、中文知识图谱资料、数据及工具、各大公司内部里大牛分享的技术文档 PDF 或者 PPT、自然语言生成SQL语句（英文）、中文NLP数据增强（EDA）工具、英文NLP数据增强工具 、基于医药知识图谱的智能问答系统、京东商品知识图谱、基于mongodb存储的军事领域知识图谱问答项目、基于远监督的中文关系抽取、语音情感分析、中文ULMFiT-情感分析-文本分类-语料及模型、一个拍照做题程序、世界各国大规模人名库、一个利用有趣中文语料库 qingyun 训练出来的中文聊天机器人、中文聊天机器人seqGAN、省市区镇行政区划数据带拼音标注、教育行业新闻语料库包含自动文摘功能、开放了对话机器人-知识图谱-语义理解-自然语言处理工具及数据、中文知识图谱：基于百度百科中文页面-抽取三元组信息-构建中文知识图谱、masr: 中文语音识别-提供预训练模型-高识别率、Python音频数据增广库、中文全词覆盖BERT及两份阅读理解数据、ConvLab：开源多域端到端对话系统平台、中文自然语言处理数据集、基于最新版本rasa搭建的对话系统、基于TensorFlow和BERT的管道式实体及关系抽取、一个小型的证券知识图谱/知识库、复盘所有NLP比赛的TOP方案、OpenCLaP：多领域开源中文预训练语言模型仓库、UER：基于不同语料+编码器+目标任务的中文预训练模型仓库、中文自然语言处理向量合集、基于金融-司法领域(兼有闲聊性质)的聊天机器人、g2pC：基于上下文的汉语读音自动标记模块、Zincbase 知识图谱构建工具包、诗歌质量评价/细粒度情感诗歌语料库、快速转化「中文数字」和「阿拉伯数字」、百度知道问答语料库、基于知识图谱的问答系统、jieba_fast 加速版的jieba、正则表达式教程、中文阅读理解数据集、基于BERT等最新语言模型的抽取式摘要提取、Python利用深度学习进行文本摘要的综合指南、知识图谱深度学习相关资料整理、维基大规模平行文本语料、StanfordNLP 0.2.0：纯Python版自然语言处理包、NeuralNLP-NeuralClassifier：腾讯开源深度学习文本分类工具、端到端的封闭域对话系统、中文命名实体识别：NeuroNER vs. BertNER、新闻事件线索抽取、2019年百度的三元组抽取比赛：“科学空间队”源码、基于依存句法的开放域文本知识三元组抽取和知识库构建、中文的GPT2训练代码、ML-NLP - 机器学习(Machine Learning)NLP面试中常考到的知识点和代码实现、nlp4han:中文自然语言处理工具集(断句/分词/词性标注/组块/句法分析/语义分析/NER/N元语法/HMM/代词消解/情感分析/拼写检查、XLM：Facebook的跨语言预训练语言模型、用基于BERT的微调和特征提取方法来进行知识图谱百度百科人物词条属性抽取、中文自然语言处理相关的开放任务-数据集-当前最佳结果、CoupletAI - 基于CNN+Bi-LSTM+Attention 的自动对对联系统、抽象知识图谱、MiningZhiDaoQACorpus - 580万百度知道问答数据挖掘项目、brat rapid annotation tool: 序列标注工具、大规模中文知识图谱数据：1.4亿实体、数据增强在机器翻译及其他nlp任务中的应用及效果、allennlp阅读理解:支持多种数据和模型、PDF表格数据提取工具 、 Graphbrain：AI开源软件库和科研工具，目的是促进自动意义提取和文本理解以及知识的探索和推断、简历自动筛选系统、基于命名实体识别的简历自动摘要、中文语言理解测评基准，包括代表性的数据集&基准模型&语料库&排行榜、树洞 OCR 文字识别 、从包含表格的扫描图片中识别表格和文字、语声迁移、Python口语自然语言处理工具集(英文)、 similarity：相似度计算工具包，java编写、海量中文预训练ALBERT模型 、Transformers 2.0 、基于大规模音频数据集Audioset的音频增强 、Poplar：网页版自然语言标注工具、图片文字去除，可用于漫画翻译 、186种语言的数字叫法库、Amazon发布基于知识的人-人开放领域对话数据集 、中文文本纠错模块代码、繁简体转换 、 Python实现的多种文本可读性评价指标、类似于人名/地名/组织机构名的命名体识别数据集 、东南大学《知识图谱》研究生课程(资料)、. 英文拼写检查库 、 wwsearch是企业微信后台自研的全文检索引擎、CHAMELEON：深度学习新闻推荐系统元架构 、 8篇论文梳理BERT相关模型进展与反思、DocSearch：免费文档搜索引擎、 LIDA：轻量交互式对话标注工具 、aili - the fastest in-memory index in the East 东半球最快并发索引 、知识图谱车音工作项目、自然语言生成资源大全 、中日韩分词库mecab的Python接口库、中文文本摘要/关键词提取、汉字字符特征提取器 (featurizer)，提取汉字的特征（发音特征、字形特征）用做深度学习的特征、中文生成任务基准测评 、中文缩写数据集、中文任务基准测评 - 代表性的数据集-基准(预训练)模型-语料库-baseline-工具包-排行榜、PySS3：面向可解释AI的SS3文本分类器机器可视化工具 、中文NLP数据集列表、COPE - 格律诗编辑程序、doccano：基于网页的开源协同多语言文本标注工具 、PreNLP：自然语言预处理库、简单的简历解析器，用来从简历中提取关键信息、用于中文闲聊的GPT2模型：GPT2-chitchat、基于检索聊天机器人多轮响应选择相关资源列表(Leaderboards、Datasets、Papers)、(Colab)抽象文本摘要实现集锦(教程 、词语拼音数据、高效模糊搜索工具、NLP数据增广资源集、微软对话机器人框架 、 GitHub Typo Corpus：大规模GitHub多语言拼写错误/语法错误数据集、TextCluster：短文本聚类预处理模块 Short text cluster、面向语音识别的中文文本规范化、BLINK：最先进的实体链接库、BertPunc：基于BERT的最先进标点修复模型、Tokenizer：快速、可定制的文本词条化库、中文语言理解测评基准，包括代表性的数据集、基准(预训练)模型、语料库、排行榜、spaCy 医学文本挖掘与信息提取 、 NLP任务示例项目代码集、 python拼写检查库、chatbot-list - 行业内关于智能客服、聊天机器人的应用和架构、算法分享和介绍、语音质量评价指标(MOSNet, BSSEval, STOI, PESQ, SRMR)、 用138GB语料训练的法文RoBERTa预训练语言模型 、BERT-NER-Pytorch：三种不同模式的BERT中文NER实验、无道词典 - 有道词典的命令行版本，支持英汉互查和在线查询、2019年NLP亮点回顾、 Chinese medical dialogue data 中文医疗对话数据集 、最好的汉字数字(中文数字)-阿拉伯数字转换工具、 基于百科知识库的中文词语多词义/义项获取与特定句子词语语义消歧、awesome-nlp-sentiment-analysis - 情感分析、情绪原因识别、评价对象和评价词抽取、LineFlow：面向所有深度学习框架的NLP数据高效加载器、中文医学NLP公开资源整理 、MedQuAD：(英文)医学问答数据集、将自然语言数字串解析转换为整数和浮点数、Transfer Learning in Natural Language Processing (NLP) 、面向语音识别的中文/英文发音辞典、Tokenizers：注重性能与多功能性的最先进分词器、CLUENER 细粒度命名实体识别 Fine Grained Named Entity Recognition、 基于BERT的中文命名实体识别、中文谣言数据库、NLP数据集/基准任务大列表、nlp相关的一些论文及代码, 包括主题模型、词向量(Word Embedding)、命名实体识别(NER)、文本分类(Text Classificatin)、文本生成(Text Generation)、文本相似性(Text Similarity)计算等，涉及到各种与nlp相关的算法，基于keras和tensorflow 、Python文本挖掘/NLP实战示例、 Blackstone：面向非结构化法律文本的spaCy pipeline和NLP模型通过同义词替换实现文本“变脸” 、中文 预训练 ELECTREA 模型: 基于对抗学习 pretrain Chinese Model 、albert-chinese-ner - 用预训练语言模型ALBERT做中文NER 、基于GPT2的特定主题文本生成/文本增广、开源预训练语言模型合集、多语言句向量包、编码、标记和实现：一种可控高效的文本生成方法、 英文脏话大列表 、attnvis：GPT2、BERT等transformer语言模型注意力交互可视化、CoVoST：Facebook发布的多语种语音-文本翻译语料库，包括11种语言(法语、德语、荷兰语、俄语、西班牙语、意大利语、土耳其语、波斯语、瑞典语、蒙古语和中文)的语音、文字转录及英文译文、Jiagu自然语言处理工具 - 以BiLSTM等模型为基础，提供知识图谱关系抽取 中文分词 词性标注 命名实体识别 情感分析 新词发现 关键词 文本摘要 文本聚类等功能、用unet实现对文档表格的自动检测，表格重建、NLP事件提取文献资源列表 、 金融领域自然语言处理研究资源大列表、CLUEDatasetSearch - 中英文NLP数据集：搜索所有中文NLP数据集，附常用英文NLP数据集 、medical_NER - 中文医学知识图谱命名实体识别 、(哈佛)讲因果推理的免费书、知识图谱相关学习资料/数据集/工具资源大列表、Forte：灵活强大的自然语言处理pipeline工具集 、Python字符串相似性算法库、PyLaia：面向手写文档分析的深度学习工具包、TextFooler：针对文本分类/推理的对抗文本生成模块、Haystack：灵活、强大的可扩展问答(QA)框架、中文关键短语抽取工具","fighting41love, wainshine, imhuster",11,2018-08-21,2023-08-24,0
continuedev/continue,Applications,Coding,25250,20,0.08%,210,0.83%,2547,"⏩ Create, share, and use custom AI code assistants with our open-source IDE extensions and hub of models, rules, prompts, docs, and other building blocks","sestinj, Patrick-Erichsen, RomneyDa",100+,2023-05-24,2025-04-06,1549
exo-explore/exo,Infrastructure,Compute management,27423,20,0.07%,169,0.62%,1692,Run your own AI cluster at home with everyday devices 📱💻 🖥️⌚,"AlexCheema, cadenmackenzie, blindcrone",52,2024-06-24,2025-03-21,135
CopilotKit/CopilotKit,AI engineering,AI interface,18048,19,0.11%,220,1.22%,2586,"React UI + elegant infrastructure for AI Copilots, AI chatbots, and in-app AI agents. The Agentic last-mile 🪁","ataibarkai, mme, arielweinberger",81,2023-06-19,2025-04-04,0
getcursor/cursor,Applications,Coding,29098,19,0.07%,155,0.53%,1830,The AI Code Editor,"truell20, Sanger2000, terror",28,2023-03-12,2023-04-06,0
coqui-ai/TTS,Model development,Modeling & training,39102,19,0.05%,183,0.47%,4929,"🐸💬 - a deep learning toolkit for Text-to-Speech, battle-tested in research and production","erogol, Edresson, WeberJulian",100+,2020-05-20,2023-10-19,1739417
mlabonne/llm-course,Tutorials,,48945,18,0.04%,275,0.56%,5234,Course to get into Large Language Models (LLMs) with roadmaps and Colab notebooks.,"mlabonne, pitmonticone",2,2023-06-17,2024-01-02,0
ggerganov/whisper.cpp,Model development,Inference optimization,38984,18,0.05%,153,0.39%,4077,Port of OpenAI's Whisper model in C/C++,"ggerganov, slaren, JohannesGaessler",100+,2022-09-25,2025-04-02,77144
lobehub/lobe-chat,AI engineering,AIE framework,58580,18,0.03%,216,0.37%,12429,"🤯 Lobe Chat - an open-source, modern-design AI chat framework. Supports Multi AI Providers( OpenAI / Claude 3 / Gemini / Ollama / DeepSeek / Qwen), Knowledge Base (file upload / knowledge management / RAG ), Multi-Modals (Plugins/Artifacts) and Thinking. One-click FREE deployment of your private ChatGPT/ Claude / DeepSeek application.","arvinxx, lobehubbot, canisminor1990",100+,2023-05-21,2025-04-06,861
assafelovic/gpt-researcher,Applications,Info aggregation,20737,17,0.08%,147,0.71%,2684,LLM based autonomous agent that conducts deep local and web research on any topic and generates a long report with citations.,"assafelovic, ElishaKay, rotemweiss57",100+,2023-05-12,2025-04-05,0
cpacker/MemGPT,AI engineering,Agent,15760,17,0.11%,105,0.67%,1637,"Letta (formerly MemGPT) is the stateful agents framework with memory, reasoning, and context management.","cpacker, sarahwooders, mattzh72",100+,2023-10-11,2025-03-31,0
qdrant/qdrant,Infrastructure,VectorDB,22865,17,0.07%,100,0.44%,1570,"Qdrant - High-performance, massive-scale Vector Database and Vector Search Engine for the next generation of AI. Also available in the cloud https://cloud.qdrant.io/","generall, agourlay, timvisee",100+,2020-05-30,2025-04-04,18303
huggingface/transformers,Model development,Modeling & training,142477,17,0.01%,340,0.24%,28545,"🤗 Transformers: State-of-the-art Machine Learning for Pytorch, TensorFlow, and JAX.","thomwolf, sgugger, ydshieh",100+,2018-10-29,2025-04-05,1820
openai/openai-cookbook,Tutorials,,62659,17,0.03%,134,0.21%,10157,Examples and guides for using the OpenAI API,"ted-at-openai, simonpfish, colin-openai",100+,2022-03-11,2025-04-01,0
khoj-ai/khoj,Applications,Info aggregation,28511,16,0.06%,228,0.80%,1577,"Your AI second brain. Self-hostable. Get answers from the web or your docs. Build custom agents, schedule automations, do deep research. Turn any online or local LLM into your personal, autonomous AI (gpt, claude, gemini, llama, qwen, mistral). Get started - free.","debanjum, sabaimran, MythicalCow",52,2021-08-16,2025-04-04,43089
fishaudio/fish-speech,Model repo,Multimodal,20484,16,0.08%,138,0.67%,1619,SOTA Open Source TTS,"leng-yue, AnyaCoder, Stardust-minus",69,2023-10-10,2025-03-20,0
songquanpeng/one-api,AI engineering,AIE framework,24386,15,0.06%,241,0.99%,5070,"LLM API 管理 & 分发系统，支持 OpenAI、Azure、Anthropic Claude、Google Gemini、DeepSeek、字节豆包、ChatGLM、文心一言、讯飞星火、通义千问、360 智脑、腾讯混元等主流模型，统一 API 适配，可用于 key 管理与二次分发。单可执行文件，提供 Docker 镜像，一键部署，开箱即用。LLM API management & key redistribution system, unifying multiple providers under a single API. Single binary, Docker-ready, with an English UI.","songquanpeng, mrhaoji, Laisky",100+,2023-04-22,2024-12-22,3141
composiohq/composio,AI engineering,Agent,24875,15,0.06%,215,0.86%,4399,Composio equip's your AI agents & LLMs with 100+ high-quality integrations via function calling,"kaavee315, utkarsh-dixit, angrybayblade",54,2024-02-23,2025-04-04,293
milvus-io/milvus,Infrastructure,VectorDB,33869,15,0.04%,173,0.51%,3135,"Milvus is a high-performance, cloud-native vector database built for scalable vector ANN search","congqixia, JinHai-CN, bigsheeper",100+,2019-09-16,2025-04-05,169769
hpcaitech/Open-Sora,Model repo,Multimodal,26012,15,0.06%,117,0.45%,2502,Open-Sora: Democratizing Efficient Video Production for All,"zhengzangw, Shen-Chenhui, FrankLeeeee",57,2024-02-20,2025-03-27,0
zhayujie/chatgpt-on-wechat,Applications,Bots,36105,15,0.04%,93,0.26%,9061,基于大模型搭建的聊天机器人，同时支持 微信公众号、企业微信应用、飞书、钉钉 等接入，可选择GPT3.5/GPT-4o/GPT-o1/ DeepSeek/Claude/文心一言/讯飞星火/通义千问/ Gemini/GLM-4/Claude/Kimi/LinkAI，能处理文本、语音和图片，访问操作系统和互联网，支持基于自有知识库进行定制企业智能客服。,"zhayujie, lanvent, 6vision",76,2022-08-07,2025-03-30,221
sgl-project/sglang,AI engineering,Prompt engineering,12870,14,0.11%,245,1.90%,1434,SGLang is a fast serving framework for large language models and vision language models.,"merrymercy, zhyncs, Ying1123",100+,2024-01-08,2025-04-05,0
activepieces/activepieces,Applications,Workflow automation,12546,14,0.11%,168,1.34%,1666,Open Source AI Automation ✨ All our 280+ pieces are now available as MCP to use with LLMs,"abuaboud, AbdulTheActivePiecer, khaledmashaly",100+,2022-12-03,2025-04-05,0
guillaumekln/faster-whisper,Infrastructure,Serving,15238,14,0.09%,149,0.98%,1277,Faster Whisper transcription with CTranslate2,"guillaumekln, MahmoudAshraf97, Purfview",43,2023-02-11,2025-03-20,0
upscayl/upscayl,Applications,Image production,36163,14,0.04%,139,0.38%,1665,"🆙 Upscayl - #1 Free and Open Source AI Image Upscaler for Linux, MacOS and Windows.","NayamAmarshe, aaronliu0130, TGS963",39,2022-07-30,2025-03-28,5578725
gradio-app/gradio,AI engineering,AI interface,37289,14,0.04%,138,0.37%,2831,"Build and share delightful machine learning apps, all in Python. 🌟 Star to support our work!","abidlabs, pngwn, aliabd",100+,2018-12-19,2025-04-05,0
lllyasviel/Fooocus,Applications,Image production,44168,14,0.03%,128,0.29%,6762,Focus on prompting and generating,"lllyasviel, mashb1t, MoonRide303",59,2023-08-09,2025-01-24,178670
jxnl/instructor,AI engineering,Prompt engineering,10016,13,0.13%,85,0.85%,771,structured outputs for llms ,"jxnl, ivanleomk, shreya-51",100+,2023-06-14,2023-06-23,0
lucidrains/vit-pytorch,Model repo,,22337,13,0.06%,102,0.46%,3234,"Implementation of Vision Transformer, a simple way to achieve SOTA in vision classification with only a single transformer encoder, in Pytorch","lucidrains, zankner, developer0hye",22,2020-10-03,2025-01-19,0
facebookresearch/faiss,Infrastructure,VectorDB,34129,13,0.04%,130,0.38%,3827,A library for efficient similarity search and clustering of dense vectors.,"mdouze, beauby, r-barnes",100+,2017-02-07,1000-01-01,0
promptfoo/promptfoo,AI engineering,Prompt engineering,6094,12,0.20%,83,1.36%,504,"Test your prompts, agents, and RAGs. Red teaming, pentesting, and vulnerability scanning for LLMs. Compare performance of GPT, Claude, Gemini, Llama, and more. Simple declarative configs with command line and CI/CD integration.","typpo, mldangelo, sklein12",100+,2023-04-28,2025-04-06,307
GoogleCloudPlatform/generative-ai,Tutorials,,10031,12,0.12%,110,1.10%,2838,"Sample code and notebooks for Generative AI on Google Cloud, with Gemini on Vertex AI","holtskinner, renovate-bot, koverholt",100+,2023-05-05,2025-04-04,0
karpathy/nanoGPT,Model repo,,40490,12,0.03%,126,0.31%,6683,"The simplest, fastest repository for training/finetuning medium-sized GPTs.","karpathy, apivovarov, danielgross",36,2022-12-28,2024-12-09,0
ChatGPTNextWeb/ChatGPT-Next-Web,AI engineering,AI interface,82497,12,0.01%,157,0.19%,61067,✨ Light and Fast AI Assistant. Support: Web | iOS | MacOS | Android |  Linux | Windows,"Yidadaa, lloydzhou, Dogtiti",100+,2023-03-10,2025-02-17,173159
nomic-ai/gpt4all,AI engineering,AIE framework,73015,12,0.02%,81,0.11%,7962,GPT4All: Run Local LLMs on Any Device. Open-source and available for commercial use.,"manyoso, cebtenzzre, AndriyMulyar",100+,2023-03-27,2025-02-21,10643
OpenLLMAI/OpenRLHF,Model development,Modeling & training,6102,11,0.18%,133,2.18%,601,"An Easy-to-use, Scalable and High-performance RLHF Framework (70B+ PPO Full Tuning & Iterative DPO & LoRA & RingAttention & RFT)","hijkzzz, xiaoxigua999, openllmai0",63,2023-07-30,2025-04-03,0
Nutlope/llamacoder,Applications,Coding,5834,11,0.19%,91,1.56%,1309,Open source Claude Artifacts – built with Llama 3.1 405B,"samselikoff, Nutlope, ryanto",3,2024-07-25,2025-01-21,0
RockChinQ/LangBot,Applications,Bots,10279,11,0.11%,160,1.56%,749,"😎简单易用、🧩丰富生态 - 大模型原生即时通信机器人平台 | 适配 QQ / 微信（企业微信、个人微信）/ 飞书 / 钉钉 / Discord / Telegram / Slack 等平台 | 支持 ChatGPT、DeepSeek、Dify、Claude、Gemini、xAI Grok、Ollama、LM Studio、阿里云百炼、火山方舟、SiliconFlow、Qwen、Moonshot、ChatGLM、SillyTraven、MCP 等 LLM 的机器人 / Agent | LLM-based instant messaging bots platform, supports Discord, Telegram, WeChat, Lark, DingTalk, QQ, Slack","RockChinQ, wangcham, fdc310",37,2022-12-07,2025-04-03,4786
livekit/agents,AI engineering,AIE framework,5460,11,0.20%,64,1.17%,740,A powerful framework for building realtime voice AI agents 🤖🎙️📹 ,"theomonnom, keepingitneil, davidzhao",92,2023-10-19,2025-04-06,22
Portkey-AI/gateway,Infrastructure,Serving,7575,11,0.15%,73,0.96%,561,"A blazing fast AI Gateway with integrated guardrails. Route to 200+ LLMs, 50+ AI Guardrails with 1 fast & friendly API.","VisargD, narengogi, roh26it",61,2023-08-23,2025-03-31,0
princeton-nlp/SWE-agent,Applications,Coding,15292,11,0.07%,122,0.80%,1554,"SWE-agent takes a GitHub issue and tries to automatically fix it, using GPT-4, or your LM of choice. It can also be employed for offensive cybersecurity or competitive coding challenges. [NeurIPS 2024] ","klieret, carlosejimenez, ofirpress",70,2024-04-02,2025-04-04,0
Jiayi-Pan/TinyZero,Model repo,,11493,11,0.10%,75,0.65%,1452,"Clean, minimal, accessible reproduction of DeepSeek R1-Zero","Jiayi-Pan, PeterSH6, eric-haibin-lin",14,2025-01-21,2025-01-24,0
AI4Finance-Foundation/FinGPT,Model repo,,15725,11,0.07%,102,0.65%,2208,FinGPT: Open-Source Financial Large Language Models!  Revolutionize 🔥    We release the trained model on HuggingFace.,"BruceYanghy, YangletLiu, oliverwang15",30,2023-02-11,2024-12-25,0
lss233/chatgpt-mirai-qq-bot,Applications,Bots,14924,11,0.07%,86,0.58%,1653,🤖 可 DIY 的 多模态 AI 聊天机器人 | 🚀 快速接入 微信、 QQ、Telegram、等聊天平台 | 🦈支持DeepSeek、Grok、Claude、Ollama、Gemini、OpenAI | 工作流系统、网页搜索、AI画图、人设调教、虚拟女仆、语音对话 | ,"lss233, Haibersut, WangEdward",39,2022-12-05,2025-04-04,75830
OpenAccess-AI-Collective/axolotl,Model development,Modeling & training,9012,11,0.12%,46,0.51%,987,Go ahead and axolotl questions,"winglian, NanoCode012, tmm1",100+,2023-04-14,2025-04-05,0
openai/CLIP,Model repo,Multimodal,28284,11,0.04%,112,0.40%,3527,"CLIP (Contrastive Language-Image Pretraining),  Predict the most relevant text snippet given an image","jongwook, jiahuei, or-toledano",21,2020-12-16,2024-06-04,0
langchain4j/langchain4j,AI engineering,AIE framework,6851,10,0.15%,131,1.91%,1273,Java version of LangChain,"dliubarskyi, jdubois, crutcher",100+,2023-06-20,2025-03-12,0
simonw/llm,Applications,Bots,6897,10,0.15%,107,1.55%,406,Access large language models from the command-line,"simonw, cmungall, amjith",38,2023-04-01,2025-04-06,0
Helicone/helicone,Infrastructure,Monitoring,3554,10,0.28%,51,1.44%,355,"🧊 Open source LLM observability platform. One line of code to monitor, evaluate, and experiment. YC W23 🍓","chitalian, colegottdank, ScottMktn",69,2023-01-31,2025-04-05,0
marimo-team/marimo,Infrastructure,Toolings,12101,10,0.08%,165,1.36%,471,"A reactive notebook for Python — run reproducible experiments, query with SQL, execute as a script, deploy as an app, and version with git. All in a modern, AI-native editor.","mscolnick, akshayka, Light2Dark",100+,2023-08-14,2025-04-05,2
chroma-core/chroma,Infrastructure,VectorDB,19114,10,0.05%,149,0.78%,1559,the AI-native open-source embedding database,"HammadB, jeffchuber, codetheweb",100+,2022-10-05,2025-04-06,49
QwenLM/Qwen1.5,Model repo,,16523,10,0.06%,91,0.55%,1159,"Qwen2.5 is the large language model series developed by Qwen team, Alibaba Cloud.","jklj077, JustinLin610, bug-orz",35,2024-02-05,2025-02-08,0
Bin-Huang/chatbox,Applications,Bots,33930,10,0.03%,156,0.46%,3236,"User-friendly Desktop Client App for AI Models/LLMs (GPT, Claude, Gemini, Ollama...)","Bin-Huang, hiNISAL, josueggh",43,2023-03-06,2025-01-10,452571
microsoft/semantic-kernel,AI engineering,Agent,23855,10,0.04%,101,0.42%,3669,Integrate cutting-edge LLM technology quickly and easily into your apps,"markwallace-microsoft, moonbox3, stephentoub",100+,2023-02-27,2025-04-04,180
PaddlePaddle/PaddleOCR,Model repo,Multimodal,48054,10,0.02%,172,0.36%,8118,"Awesome multilingual OCR toolkits based on PaddlePaddle (practical ultra lightweight OCR system, support 80+ languages recognition, provide data annotation and synthesis tools, support training and deployment among server, mobile, embedded and IoT devices)","LDOUBLEV, WenmuZhou, MissPenguin",100+,2020-05-08,2025-04-02,0
Cinnamon/kotaemon,AI engineering,AIE framework,21898,10,0.05%,65,0.30%,1724,An open-source RAG-based tool for chatting with your documents.,"taprosoft, trducng, lone17",32,2024-03-25,2025-04-01,4796
modelscope/agentscope,AI engineering,Agent,6895,9,0.13%,117,1.70%,400,Start building LLM-empowered multi-agent applications in an easier way.,"DavdGao, pan-x-c, rayrayraykk",35,2024-01-12,2025-04-01,0
princeton-nlp/SWE-bench,AI engineering,Evals,2740,9,0.33%,43,1.57%,466,SWE-bench [Multimodal]: Can Language Models Resolve Real-world Github Issues?,"john-b-yang, carlosejimenez, klieret",39,2023-10-04,2025-03-28,0
nickscamara/open-deep-research,Applications,Info aggregation,5281,9,0.17%,73,1.38%,654,An open source deep research clone. AI Agent that reasons large amounts of web data extracted with Firecrawl,"nickscamara, Neoathenian, yogasanas",11,2025-02-03,2025-02-23,0
dzhng/deep-research,Applications,Info aggregation,15283,9,0.06%,189,1.24%,1568,"An AI-powered research assistant that performs iterative, deep research on any topic by combining search engines, web scraping, and large language models.  The goal of this repo is to provide the simplest implementation of a deep research agent - e.g. an agent that can refine its research direction overtime and deep dive into a topic.","dzhng, 238SAMIxD, bennasedkin",14,2025-02-04,2025-03-14,0
sigoden/aichat,Applications,Bots,6293,9,0.14%,73,1.16%,407,"All-in-one LLM CLI tool featuring Shell Assistant, Chat-REPL, RAG, AI Tools & Agents, with access to OpenAI, Claude, Gemini, Ollama, Groq, and more.","sigoden, cramosc, mtul0729",28,2023-03-03,2025-03-28,4859
QwenLM/Qwen-Agent,AI engineering,Agent,6438,9,0.14%,70,1.09%,589,"Agent framework and applications built upon Qwen>=2.0, featuring Function Calling, Code Interpreter, RAG, and Chrome extension.","tuhahaha, JianxinMa, zhenruzhang",23,2023-09-22,2025-03-21,0
Huanshere/VideoLingo,Applications,Image production,12166,9,0.07%,88,0.72%,1195,"Netflix-level subtitle cutting, translation, alignment, and even dubbing - one-click fully automated AI video subtitle team | Netflix级字幕切割、翻译、对齐、甚至加上配音，一键全自动视频搬运AI字幕组","Huanshere, yxc0915, nexmoe",15,2024-08-09,2025-04-04,0
outlines-dev/outlines,AI engineering,Prompt engineering,11242,9,0.08%,73,0.65%,581,Structured Text Generation,"rlouf, lapp0, brandonwillard",100+,2023-03-17,2025-04-03,0
mikeroyal/Self-Hosting-Guide,Tutorials,,12923,9,0.07%,63,0.49%,672,"Self-Hosting Guide. Learn all about  locally hosting (on premises & private web servers) and managing software applications by yourself or your organization. Including Cloud, LLMs, WireGuard, Automation, Home Assistant, and Networking.","mikeroyal, Antoine-lb, bsamadi",13,2022-02-06,2024-02-11,0
deepset-ai/haystack,AI engineering,AIE framework,20168,9,0.04%,97,0.48%,2123,"AI orchestration framework to build customizable, production-ready LLM applications. Connect components (models, vector DBs, file converters) to pipelines or agents that can interact with your data. With advanced retrieval methods, it's best suited for building RAG, question answering, semantic search or conversational agent chatbots.","ZanSara, silvanocerza, anakin87",100+,2019-11-14,2025-04-04,23
Sanster/lama-cleaner,Applications,Image production,20862,9,0.04%,80,0.38%,2124,"Image inpainting tool powered by SOTA AI Model. Remove any unwanted object, defect, people from your pictures or erase and replace(powered by stable diffusion) any thing on your pictures.","Sanster, blessedcoolant, loretoparisi",19,2021-11-15,2025-03-17,1143
janhq/jan,Applications,Bots,28338,9,0.03%,96,0.34%,1674,Jan is an open source alternative to ChatGPT that runs 100% offline on your computer,"louis-menlo, urmauur, 0xHieu01",66,2023-08-17,2025-04-04,561330
stanford-oval/storm,Applications,Info aggregation,23718,9,0.04%,79,0.33%,2074,An LLM-powered knowledge curation system that researches a topic and generates a full-length report with citations.,"shaoyijia, Yucheng-Jiang, zenith110",23,2024-03-24,2025-01-23,0
mckaywrigley/chatbot-ui,AI engineering,AI interface,30791,9,0.03%,97,0.32%,8620,AI chat for any model.,"mckaywrigley, fkesheh, faraday",47,2023-03-11,2024-06-22,0
lllyasviel/ControlNet,Model repo,,31922,9,0.03%,54,0.17%,2853,Let us control diffusion models!,"lllyasviel, eltociear, sethupavan12",6,2023-02-01,2023-04-08,0
abi/screenshot-to-code,Applications,Coding,69432,9,0.01%,96,0.14%,8559,Drop in a screenshot and convert it to clean code (HTML/Tailwind/React/Vue),"abi, clean99, kachbit",25,2023-11-14,2025-03-17,0
suno-ai/bark,Model repo,Multimodal,37379,9,0.02%,51,0.14%,4427,🔊 Text-Prompted Generative Audio Model,"gkucsko, kmfreyberg, mcamac",16,2023-04-07,2023-04-22,0
jujumilk3/leaked-system-prompts,AI engineering,Prompt engineering,2170,8,0.37%,93,4.29%,268,Collection of leaked system prompts,"jujumilk3, accupham, OneTop4458",17,2023-05-16,2025-03-27,0
Docta-ai/docta,Model development,Dataset engineering,3066,8,0.26%,98,3.20%,231,A Doctor for your data,"zwzhu-d, weijiaheng",2,2023-05-02,1000-01-01,0
FoundationVision/VAR,Model repo,Multimodal,7341,8,0.11%,134,1.83%,459,"[NeurIPS 2024 Best Paper][GPT beats diffusion🔥] [scaling laws in visual generation📈] Official impl. of ""Visual Autoregressive Modeling: Scalable Image Generation via Next-Scale Prediction"". An *ultra-simple, user-friendly yet state-of-the-art* codebase for autoregressive image generation!","keyu-tian, enjoyyi00, NielsRogge",3,2024-04-01,2024-04-08,0
xorbitsai/inference,Infrastructure,Serving,7377,8,0.11%,97,1.31%,610,"Replace OpenAI GPT with another LLM in your app by changing a single line of code. Xinference gives you the freedom to use any LLM you need. With Xinference, you're empowered to run inference with any open-source language models, speech recognition models, and multimodal models, whether in the cloud, on-premises, or even on your laptop.","ChengjieLi28, qinxuye, codingl2k1",99,2023-06-14,2025-04-04,0
lancedb/lancedb,Infrastructure,VectorDB,6037,8,0.13%,56,0.93%,436,"Developer-friendly, embedded retrieval engine for multimodal AI. Search More; Manage Less.","wjones127, changhiskhan, eddyxu",100+,2023-02-28,2025-04-04,0
GreyDGL/PentestGPT,Applications,Coding,8075,8,0.10%,70,0.87%,1012,A GPT-empowered penetration testing tool,"GreyDGL, vmayoral, deepsource-autofix[bot]",22,2023-02-27,2024-11-04,0
open-compass/opencompass,AI engineering,Evals,5098,8,0.16%,44,0.86%,531,"OpenCompass is an LLM evaluation platform, supporting a wide range of models (Llama3, Mistral, InternLM2,GPT-4,LLaMa2, Qwen,GLM, Claude, etc) over 100+ datasets.","MaiziXiao, tonysy, bittersweet1999",100+,2023-06-15,2025-04-03,3631
eosphoros-ai/DB-GPT,Applications,Coding,15973,8,0.05%,103,0.64%,2160,AI Native Data App Development framework with AWEL(Agentic Workflow Expression Language) and Agents,"Aries-ckt, csunny, fangyinc",100+,2023-04-13,2025-04-03,0
labring/FastGPT,AI engineering,AIE framework,23353,8,0.03%,140,0.60%,6032,"FastGPT is a knowledge-based platform built on the LLMs, offers a comprehensive suite of out-of-the-box capabilities such as data processing, RAG retrieval, and visual AI workflow orchestration, letting you easily develop and deploy complex question-answering systems without the need for extensive setup or configuration.","c121914yu, newfish-cmyk, FinleyGe",100+,2023-02-23,2025-04-04,0
oumi-ai/oumi,Model development,Modeling & training,7817,8,0.10%,42,0.54%,565,"Everything you need to build state-of-the-art foundation models, end-to-end.","nikg4, oelachqar, taenin",21,2024-05-07,2025-04-06,0
THUDM/CogVideo,Model repo,,11123,8,0.07%,56,0.50%,1059,text and image to video generation: CogVideoX (2024) and CogVideo (ICLR 2023),"zRzRzRzRzRzRzR, OleehyO, wenyihong",34,2022-05-29,2025-03-25,0
openai/tiktoken,Model development,Dataset engineering,14037,8,0.06%,68,0.48%,1000,tiktoken is a fast BPE tokeniser for use with OpenAI's models.,"hauntsaninja, l0rinc, alvarobartt",16,2022-12-01,2025-03-09,0
NVIDIA/NeMo,Model development,Modeling & training,13549,8,0.06%,62,0.46%,2768,"A scalable generative AI framework built for researchers and developers working on Large Language Models, Multimodal, and Speech AI (Automatic Speech Recognition and Text-to-Speech)","okuchaiev, blisc, titu1994",100+,2019-08-05,2025-04-06,104939
steven-tey/novel,Applications,Writing,14346,8,0.06%,54,0.38%,1184,Notion-style WYSIWYG editor with AI-powered autocompletion.,"steven-tey, andrewdoro, haydenbleasel",55,2023-03-21,2025-01-17,0
ray-project/ray,Model development,Modeling & training,36397,8,0.02%,122,0.34%,6190,Ray is an AI compute engine. Ray consists of a core distributed runtime and a set of AI Libraries for accelerating ML workloads.,"ericl, sven1977, robertnishihara",100+,2016-10-25,2025-04-06,135
RUCAIBox/LLMSurvey,Lists,,11313,8,0.07%,37,0.33%,882,"The official GitHub page for the survey paper ""A Survey of Large Language Models"".","EliverQ, StevenTang1998, turboLJY",14,2023-03-14,2025-03-11,0
openai/openai-python,AI engineering,AI interface,26181,8,0.03%,85,0.32%,3780,The official Python library for the OpenAI API,"stainless-app[bot], RobertCraigie, hallacy",100+,2020-10-25,2025-04-03,0
dair-ai/Prompt-Engineering-Guide,Tutorials,Prompt engineering,54696,8,0.01%,158,0.29%,5376,"🐙 Guides, papers, lecture, notebooks and resources for prompt engineering","omarsar, behrends, ThunderCatXp",100+,2022-12-16,2025-04-05,0
yoheinakajima/babyagi,AI engineering,Agent,21313,8,0.04%,38,0.18%,2784,,yoheinakajima,1,2023-04-03,1000-01-01,0
Stability-AI/stablediffusion,Model repo,,40698,8,0.02%,58,0.14%,5203,High-Resolution Image Synthesis with Latent Diffusion Models,"rromb, hardmaru, apolinario",17,2022-11-23,2023-03-24,0
s0md3v/roop,Applications,Image production,29576,8,0.03%,42,0.14%,6701,one-click face swap,"henryruhs, s0md3v, K1llMan",20,2023-05-28,2023-08-02,0
linexjlin/GPTs,Lists,,29599,8,0.03%,38,0.13%,4006,leaked prompts of GPTs,"linexjlin, anchit-h, michaelskyba",33,2023-11-11,2024-09-27,0
CompVis/stable-diffusion,Model repo,,70254,8,0.01%,82,0.12%,10384,A latent text-to-image diffusion model,"rromb, pesser, patrickvonplaten",7,2022-08-10,2022-08-22,0
potpie-ai/potpie,Applications,Coding,3041,7,0.23%,43,1.41%,300,Prompt-To-Agent : Create custom engineering agents for your codebase,"dhirenmathur, vineetshar, GodReaper",13,2024-08-12,2025-04-05,0
KoljaB/RealtimeSTT,Applications,Bots,6584,7,0.11%,79,1.20%,535,"A robust, efficient, low-latency speech-to-text library with advanced voice activity detection, wake word activation and instant transcription.","KoljaB, johnmalek312, SlickTorpedo",20,2023-08-29,2025-02-28,0
google-deepmind/gemma,Model repo,Multimodal,3118,7,0.22%,36,1.15%,423,"Gemma open-weight LLM library, from Google DeepMind","Conchylicultor, texasmichelle, alimuldal",16,2024-02-20,2025-04-02,0
huggingface/trl,Model development,Modeling & training,13039,7,0.05%,130,1.00%,1769,Train transformer language models with reinforcement learning.,"qgallouedec, younesbelkada, lewtun",100+,2020-03-27,2025-04-05,0
stanfordnlp/dspy,AI engineering,Prompt engineering,22885,7,0.03%,183,0.80%,1750,DSPy: The framework for programming—not prompting—language models,"okhat, arnavsinghvi11, krypticmouse",100+,2023-01-09,2025-04-03,0
dataelement/bisheng,AI engineering,AIE framework,7975,7,0.09%,54,0.68%,1335,"BISHENG is an open LLM devops platform for next generation Enterprise AI applications. Powerful and comprehensive features include: GenAI workflow, RAG, Agent, Unified model management, Evaluation, SFT, Dataset Management, Enterprise-level System Management, Observability and more.","zgqgit, yaojin3616, dolphin0618",27,2023-08-28,2025-04-03,0
ml-explore/mlx,Model development,Modeling & training,20077,7,0.03%,103,0.51%,1152,MLX: An array framework for Apple silicon,"awni, angeloskath, zcbenz",100+,2023-11-28,2025-04-06,0
weaviate/weaviate,Infrastructure,VectorDB,12999,7,0.05%,64,0.49%,914,"Weaviate is an open-source vector database that stores both objects and vectors, allowing for the combination of vector search with structured filtering with the fault tolerance and scalability of a cloud-native database​.","etiennedi, antas-marcin, dirkkul",100+,2016-03-30,2025-04-04,183326
huggingface/peft,Model development,Modeling & training,18024,7,0.04%,87,0.48%,1811,🤗 PEFT: State-of-the-art Parameter-Efficient Fine-Tuning.,"pacman100, BenjaminBossan, younesbelkada",100+,2022-11-25,2025-04-04,0
QwenLM/Qwen,Model repo,,17749,7,0.04%,79,0.45%,1462,The official repo of Qwen (通义千问) chat & pretrained large language model proposed by Alibaba Cloud.,"JustinLin610, yangapku, JianxinMa",31,2023-08-03,2024-05-27,0
arsenetar/dupeguru,AI engineering,Dataset engineering,5920,7,0.12%,25,0.42%,429,Find duplicate files,"arsenetar, glubsy, jpvlsmv",29,2013-06-22,2024-08-23,1404528
RayVentura/ShortGPT,Applications,Video production,6344,7,0.11%,26,0.41%,832,🚀🎬 ShortGPT - Experimental AI framework for youtube shorts / tiktok channel automation,"RayVentura, transonit, test12376",11,2023-06-27,2024-02-11,0
huggingface/diffusers,Model development,Modeling & training,28427,7,0.02%,109,0.38%,5824,"🤗 Diffusers: State-of-the-art diffusion models for image, video, and audio generation in PyTorch and FLAX.","patrickvonplaten, sayakpaul, patil-suraj",100+,2022-05-30,2025-04-05,0
facefusion/facefusion,Applications,Image production,22286,7,0.03%,84,0.38%,3386,Industry leading face manipulation platform,"henryruhs, sklart",2,2023-08-17,2025-03-11,0
harry0703/MoneyPrinterTurbo,Model repo,Multimodal,25865,7,0.03%,93,0.36%,3782,利用AI大模型，一键生成高清短视频 Generate short videos with one click using AI LLM.,"harry0703, yyhhyyyyyy, vuisme",20,2024-03-11,2025-03-23,10345
homanp/superagent,AI engineering,Agent,5725,7,0.12%,19,0.33%,868,🥷 Run AI-agents with an API,"homanp, elisalimli, dsinghvi",58,2023-05-10,2024-10-20,0
UKPLab/sentence-transformers,Model repo,Multilingual,16390,7,0.04%,49,0.30%,2571,State-of-the-Art Text Embeddings,"nreimers, tomaarsen, milistu",100+,2019-07-24,2025-04-03,0
karpathy/llm.c,Model development,Inference optimization,26249,7,0.03%,76,0.29%,3021,"LLM training in simple, raw C/CUDA","karpathy, ngc92, gordicaleksa",64,2024-04-08,2024-06-03,0
transitive-bullshit/chatgpt-api,Applications,Bots,17264,7,0.04%,49,0.28%,2201,AI agent stdlib that works with any LLM and TypeScript AI SDK.,"transitive-bullshit, waylaidwanderer, optionsx",100+,2022-12-03,2025-04-03,0
reworkd/AgentGPT,AI engineering,Agent,33661,7,0.02%,85,0.25%,9388,"🤖 Assemble, configure, and deploy autonomous AI Agents in your browser.","asim-shrestha, awtkns, shahrishabh7",69,2023-04-07,2025-03-10,0
OpenBMB/ChatDev,Applications,Coding,26625,7,0.03%,50,0.19%,3372,Create Customized Software using Natural Language Idea (through LLM-powered Multi-Agent Collaboration),"thinkwee, Alphamasterliu, qianc62",56,2023-08-28,2025-04-03,0
photoprism/photoprism,Applications,Data organization,36893,7,0.02%,61,0.17%,2041,AI-Powered Photos App for the Decentralized Web 🌈💎✨,"lastzero, graciousgrey, weblate",100+,2018-01-27,2025-04-04,351
imartinez/privateGPT,Applications,Info aggregation,55556,7,0.01%,35,0.06%,7451,"Interact with your documents using the power of GPT, 100% privately, no data leaks","imartinez, jaluma, lopagela",89,2023-05-02,2023-08-18,0
ysymyth/ReAct,AI engineering,Prompt engineering,2504,6,0.24%,94,3.75%,260,[ICLR 2023] ReAct: Synergizing Reasoning and Acting in Language Models,"ysymyth, john-b-yang",2,2022-11-13,2023-07-14,0
PriorLabs/TabPFN,Model repo,Multimodal,3148,6,0.19%,63,2.00%,270,⚡ TabPFN: Foundation Model for Tabular Data ⚡,"noahho, LeoGrin, SamuelGabriel",21,2022-07-01,2025-03-26,0
lmnr-ai/lmnr,Infrastructure,Monitoring,1773,6,0.34%,28,1.58%,105,"Laminar - open-source all-in-one platform for engineering AI products. Crate data flywheel for you AI app. Traces, Evals, Datasets, Labels. YC S24.","dinmukhamedm, skull8888888, olzhik11",16,2024-08-29,2025-04-04,2
EleutherAI/lm-evaluation-harness,AI engineering,Evals,8520,6,0.07%,82,0.96%,2273,A framework for few-shot evaluation of language models.,"lintangsutawika, haileyschoelkopf, leogao2",100+,2020-08-28,2025-04-04,0
vanna-ai/vanna,Applications,Coding,14644,6,0.04%,135,0.92%,1306,🤖 Chat with your SQL database 📊. Accurate Text-to-SQL Generation via LLMs using RAG 🔄.,"zainhoda, andreped, arslanhashmi",67,2023-05-13,2024-03-15,0
openai/openai-node,AI engineering,AI interface,8937,6,0.07%,65,0.73%,1016,Official JavaScript / TypeScript library for the OpenAI API,"stainless-app[bot], schnerd, RobertCraigie",35,2021-12-14,2025-04-04,0
Dao-AILab/flash-attention,Model development,Inference optimization,16713,6,0.04%,103,0.62%,1585,Fast and memory-efficient exact attention,"tridao, piercefreeman, ksivaman",100+,2022-05-19,2025-04-01,473164
albertan017/LLM4Decompile,Model repo,Coding,5389,6,0.11%,27,0.50%,366,Reverse Engineering: Decompiling Binary Code with Large Language Models,"albertan017, rocky-lq",2,2024-02-28,1000-01-01,0
danswer-ai/danswer,Applications,Info aggregation,12618,6,0.05%,60,0.48%,1624,Gen-AI Chat for Teams - Think ChatGPT if it had access to your team's unique knowledge.,"Weves, pablonyx, yuhongsun96",100+,2023-04-27,2025-04-06,0
erikbern/ann-benchmarks,AI engineering,Evals,5207,6,0.12%,24,0.46%,795,Benchmarks of approximate nearest neighbor libraries in Python,"erikbern, maumueller, ale-f",77,2015-05-28,2025-04-03,0
xenova/transformers.js,AI engineering,AIE framework,13364,6,0.04%,44,0.33%,895,"State-of-the-art Machine Learning for the web. Run 🤗 Transformers directly in your browser, with no need for a server!","xenova, chelouche9, BritishWerewolf",54,2023-02-13,2025-04-02,0
TheR1D/shell_gpt,Applications,Coding,10651,6,0.06%,30,0.28%,840,"A command-line productivity tool powered by AI large language models like GPT-4, will help you accomplish your tasks faster and more efficiently.","TheR1D, eric-glb, cosmojg",31,2023-01-18,2025-02-17,803
Stability-AI/generative-models,Model repo,,25646,6,0.02%,47,0.18%,2845,Generative Models by Stability AI,"timudk, voletiv, akx",18,2023-06-22,2023-07-26,0
mozilla/DeepSpeech,Model development,Modeling & training,26179,6,0.02%,35,0.13%,4037,"DeepSpeech is an open source embedded (offline, on-device) speech-to-text engine which can run in real time on devices ranging from a Raspberry Pi 4 to high power GPU servers.","reuben, lissyx, kdavis-mozilla",100+,2016-06-02,2021-04-16,1121459
PlexPt/awesome-chatgpt-prompts-zh,Lists,Prompt engineering,54672,6,0.01%,64,0.12%,13571,ChatGPT 中文调教指南。各种场景使用指南。学习怎么让它听你的话。,"PlexPt, ImgBotApp, PeterDaveHello",24,2022-12-12,2024-07-01,0
oobabooga/text-generation-webui,AI engineering,AI interface,43073,6,0.01%,43,0.10%,5551,A Gradio web UI for Large Language Models with support for multiple inference backends.,"oobabooga, jllllll, mcmonkey4eva",100+,2022-12-21,2025-04-02,106573
meta-llama/llama3,Model repo,,28576,6,0.02%,19,0.07%,3344,The official Meta Llama 3 GitHub site,"jspisak, ruanslv, astonzhang",26,2024-03-15,2024-04-03,0
xtekky/gpt4free,AI engineering,AI interface,63963,6,0.01%,21,0.03%,13590,"The official gpt4free repository | various collection of powerful language models | o3 and deepseek r1, gpt-4.5","hlohaus, xtekky, kqlio67",100+,2023-03-29,2025-04-05,348
agentica-project/deepscaler,Model repo,,2198,5,0.23%,39,1.77%,191,Democratizing Reinforcement Learning for LLMs,"michaelzhiluo, jeffreysijuntan, eric-haibin-lin",4,2025-01-26,2025-02-16,0
alibaba/data-juicer,Model development,Dataset engineering,4122,5,0.12%,50,1.21%,225,Data processing for and with foundation models!  🍎 🍋 🌽 ➡️ ➡️🍸 🍹 🍷,"HYLcool, BeachWang, yxdyc",29,2023-08-01,2025-04-04,6
Arize-ai/phoenix,AI engineering,Evals,5277,5,0.09%,57,1.08%,386,AI Observability & Evaluation,"mikeldking, RogerHYang, axiomofjoy",80,2022-11-09,2025-04-05,0
Chainlit/chainlit,AI engineering,AIE framework,9145,5,0.05%,89,0.97%,1234,Build Conversational AI in minutes ⚡️,"willydouhard, alimtunc, ramnes",100+,2023-03-14,2025-03-07,0
FlagOpen/FlagEmbedding,Model development,Modeling & training,9228,5,0.05%,80,0.87%,661,Retrieval and Retrieval-augmented LLMs,"hanhainebula, 545999961, staoxiao",60,2023-08-02,2025-03-20,0
Unstructured-IO/unstructured,AI engineering,AIE framework,10785,5,0.05%,90,0.83%,897,"Open source libraries and APIs to build custom preprocessing pipelines for labeling, training, or production machine learning pipelines. ","MthwRobinson, scanny, christinestraub",100+,2022-09-26,2025-04-04,0
explodinggradients/ragas,AI engineering,Evals,8684,5,0.06%,68,0.78%,880,Supercharge Your LLM Application Evaluations 🚀,"shahules786, jjmachan, sahusiddharth",100+,2023-05-08,2025-04-04,0
MuiseDestiny/zotero-gpt,Applications,Info aggregation,6095,5,0.08%,46,0.75%,253,GPT Meet Zotero.,"MuiseDestiny, windingwind, zfb132",9,2023-03-28,2023-06-24,178622
netease-youdao/EmotiVoice,Model repo,Multimodal,7825,5,0.06%,42,0.54%,672,EmotiVoice 😊: a Multi-Voice and Prompt-Controlled TTS Engine,"syq163, netease-youdao, huaxuanW",11,2023-11-08,2023-12-01,87
openai/triton,Model development,Inference optimization,15091,5,0.03%,72,0.48%,1904,Development repository for the Triton language and compiler,"ptillet, Jokeren, ThomasRaoux",100+,2014-08-30,2025-04-05,0
tracel-ai/burn,Model development,Modeling & training,10178,5,0.05%,47,0.46%,528,"Burn is a new comprehensive dynamic Deep Learning Framework built using Rust with extreme flexibility, compute efficiency and portability as its primary goals.","nathanielsimard, laggui, antimora",100+,2022-07-18,2025-04-04,0
Deeptrain-Community/chatnio,AI engineering,AIE framework,8174,5,0.06%,36,0.44%,1094,🚀 Next Generation AI One-Stop Internationalization Solution. 🚀 下一代 AI 一站式 B/C 端解决方案，支持 OpenAI，Midjourney，Claude，讯飞星火，Stable Diffusion，DALL·E，ChatGLM，通义千问，腾讯混元，360 智脑，百川 AI，火山方舟，新必应，Gemini，Moonshot 等模型，支持对话分享，自定义预设，云端同步，模型市场，支持弹性计费和订阅计划模式，支持图片解析，支持联网搜索，支持模型缓存，丰富美观的后台管理与仪表盘数据统计。,"zmh-program, Sh1n3zZ, XiaomaiTX",18,2023-07-17,2025-03-06,455
georgezouq/awesome-ai-in-finance,Tutorials,,3926,5,0.13%,17,0.43%,465,🔬 A curated list of awesome LLMs & deep learning strategies & tools in financial market.,"georgezouq, anmorgan24, jiminHuang",20,2018-08-29,2025-03-23,0
fchollet/ARC,AI engineering,Evals,4352,5,0.11%,16,0.37%,654,The Abstraction and Reasoning Corpus,"fchollet, ageron, kaalam",21,2019-11-05,2024-06-19,0
LouisShark/chatgpt_system_prompt,Lists,Prompt engineering,8763,5,0.06%,32,0.37%,1256,A collection of GPT system prompts and various prompt injection/leaking knowledge.,"0xeb, LouisShark, WooodHead",10,2023-11-01,2024-09-30,0
mindsdb/mindsdb,AI engineering,AIE framework,27615,5,0.02%,97,0.35%,4953,AI's query engine - Platform for building AI that can learn and answer questions over large scale federated data.,"StpMax, ZoranPandovski, George3d6",100+,2018-08-02,2025-04-04,0
huggingface/candle,Model development,Modeling & training,16958,5,0.03%,54,0.32%,1074,Minimalist ML framework for Rust,"LaurentMazare, Narsil, ivarflakstad",100+,2023-06-19,2025-04-05,0
OpenBMB/OmniLMM,Model repo,Multimodal,19151,5,0.03%,59,0.31%,1382,"MiniCPM-o 2.6: A GPT-4o Level MLLM for Vision, Speech and Multimodal Live Streaming on Your Phone","yiranyyu, iceflame89, LDLINGLINGLING",28,2024-01-29,2025-02-17,0
OpenBMB/MiniCPM-o,Model repo,Multimodal,19151,5,0.03%,59,0.31%,1382,"MiniCPM-o 2.6: A GPT-4o Level MLLM for Vision, Speech and Multimodal Live Streaming on Your Phone","yiranyyu, iceflame89, LDLINGLINGLING",28,2024-01-29,2025-02-17,0
Project-MONAI/MONAI,Model development,Modeling & training,6282,5,0.08%,15,0.24%,1163,AI Toolkit for Healthcare Imaging,"wyli, Nic-Ma, KumoLiu",100+,2019-10-11,2025-03-31,0
microsoft/DeepSpeed,Model development,Modeling & training,37759,5,0.01%,85,0.23%,4330,"DeepSpeed is a deep learning optimization library that makes distributed training and inference easy, efficient, and effective.","jeffra, loadams, tjruwase",100+,2020-01-23,2025-04-03,0
TimDettmers/bitsandbytes,Model development,Inference optimization,6883,5,0.07%,15,0.22%,681,Accessible large language models via k-bit quantization for PyTorch.,"TimDettmers, Titus-von-Koeller, matthewdouglas",88,2021-06-04,2025-04-02,5373
nerfstudio-project/nerfstudio,Model development,Modeling & training,10053,5,0.05%,20,0.20%,1388,A collaboration friendly studio for NeRFs,"tancik, evonneng, ethanweber",100+,2022-05-31,2025-04-04,0
lm-sys/FastChat,Model development,Modeling & training,38291,5,0.01%,61,0.16%,4679,"An open platform for training, serving, and evaluating large language models. Release repo for Vicuna and Chatbot Arena.","merrymercy, infwinston, suquark",100+,2023-03-19,2025-03-24,0
hpcaitech/ColossalAI,Model development,Modeling & training,40730,5,0.01%,38,0.09%,4490,"Making large AI models cheaper, faster and more accessible","FrankLeeeee, ver217, feifeibear",100+,2021-10-28,2025-03-28,0
ag2ai/ag2,AI engineering,Agent,2235,4,0.18%,67,3.00%,284,AG2 (formerly AutoGen): The Open-Source AgentOS. Join us at: https://discord.gg/pAbnFJrkgZ,"marklysze, sonichi, davorrunje",100+,2024-11-11,2025-04-05,0
NVlabs/Sana,Model repo,Multimodal,3897,4,0.10%,84,2.16%,242,SANA: Efficient High-Resolution Image Synthesis with Linear Diffusion Transformer,"lawrence-cj, yujincheng08, xieenze",13,2024-10-11,2025-04-06,0
KoljaB/RealtimeTTS,Applications,Bots,2813,4,0.14%,47,1.67%,277,Converts text to speech in realtime,"KoljaB, sorohere, NikhilKalloli",30,2023-08-26,2025-04-04,0
devflowinc/trieve,AI engineering,AIE framework,2053,4,0.20%,33,1.61%,181,"All-in-one infrastructure for search, recommendations, RAG, and analytics offered via API","skeptrunedev, densumesh, cdxker",50,2023-03-26,2025-04-05,2
microsoft/PromptWizard,AI engineering,Prompt engineering,3110,4,0.13%,47,1.51%,259,Task-Aware Agent-driven Prompt Optimization Framework,"raghav-2002-os, microsoftopensource, vivek-dani",8,2024-05-30,2024-11-26,0
SciSharp/BotSharp,AI engineering,Agent,2620,4,0.15%,34,1.30%,527,AI Multi-Agent Framework in .NET,"Oceania2018, iceljc, hchen2020",45,2017-12-17,2025-04-05,0
predibase/lorax,Infrastructure,Serving,2910,4,0.14%,32,1.10%,208,Multi-LoRA inference server that scales to 1000s of fine-tuned LLMs,"OlivierDehaene, tgaddair, magdyksaleh",64,2023-10-20,2025-03-07,66
lunary-ai/lunary,Infrastructure,Monitoring,1256,4,0.32%,11,0.88%,150,"The production toolkit for LLMs. Observability, prompt management and evaluations. ","hughcrt, vincelwt, 7HR4IZ3",9,2023-05-12,2025-03-26,0
llmonitor/llmonitor,Infrastructure,Monitoring,1256,4,0.32%,11,0.88%,150,"The production toolkit for LLMs. Observability, prompt management and evaluations. ","hughcrt, vincelwt, 7HR4IZ3",9,2023-05-12,2025-03-26,0
embeddings-benchmark/mteb,AI engineering,Evals,2377,4,0.17%,19,0.80%,360,MTEB: Massive Text Embedding Benchmark,"KennethEnevoldsen, Muennighoff, NouamaneTazi",100+,2022-04-05,2025-04-05,1211
n4ze3m/page-assist,Applications,Workflow automation,6139,4,0.07%,49,0.80%,538,Use your locally running AI models to assist you in your web browsing,"n4ze3m, Abubakar115e, yz778",28,2023-04-09,2025-04-05,1653
getomni-ai/zerox,Applications,,10815,4,0.04%,81,0.75%,711,OCR & Document Extraction using vision models,"annapo23, tylermaran, kailingding",18,2024-07-21,2025-04-01,0
yihong0618/bilingual_book_maker,Applications,Workflow automation,8369,4,0.05%,59,0.70%,1149,Make bilingual epub books Using AI translate,"yihong0618, hleft, mkXultra",63,2023-03-02,2024-12-22,0
miurla/morphic,Applications,Bots,7270,4,0.06%,42,0.58%,1957,An AI-powered search engine with a generative UI,"miurla, casistack, leerob",25,2024-04-05,2025-03-30,0
steven2358/awesome-generative-ai,Lists,,8080,4,0.05%,46,0.57%,898,A curated list of modern Generative Artificial Intelligence projects and services,"steven2358, sonoroboto, engineer-taro",64,2022-10-20,2025-03-11,0
e2b-dev/e2b,AI engineering,Agent,7897,4,0.05%,44,0.56%,524,Secure open source cloud runtime for AI apps & AI agents,"ValentaTomas, mlejva, jakubno",32,2023-03-04,2025-04-05,0
intel-analytics/ipex-llm,Model development,Inference optimization,7699,4,0.05%,41,0.53%,1339,"Accelerate local LLM inference and finetuning (LLaMA, Mistral, ChatGLM, Qwen, DeepSeek, Mixtral, Gemma, Phi, MiniCPM, Qwen-VL, MiniCPM-V, etc.) on Intel XPU (e.g., local PC with iGPU and NPU, discrete GPU such as Arc, Flex and Max); seamlessly integrate with llama.cpp, Ollama, HuggingFace, LangChain, LlamaIndex, vLLM, DeepSpeed, Axolotl, etc.","Oscilloscope98, MeouSker77, rnwang04",100+,2016-08-29,2024-08-22,653
intel-analytics/BigDL,Model development,Inference optimization,7699,4,0.05%,41,0.53%,1339,"Accelerate local LLM inference and finetuning (LLaMA, Mistral, ChatGLM, Qwen, DeepSeek, Mixtral, Gemma, Phi, MiniCPM, Qwen-VL, MiniCPM-V, etc.) on Intel XPU (e.g., local PC with iGPU and NPU, discrete GPU such as Arc, Flex and Max); seamlessly integrate with llama.cpp, Ollama, HuggingFace, LangChain, LlamaIndex, vLLM, DeepSpeed, Axolotl, etc.","Oscilloscope98, MeouSker77, rnwang04",100+,2016-08-29,2024-08-22,370
OpenLMLab/MOSS-RLHF,Model development,Modeling & training,1348,4,0.30%,7,0.52%,98,Secrets of RLHF in Large Language Models Part I: PPO,"Ablustrund, fakerbaby, CiaranZhou",5,2023-07-05,2024-03-03,0
NVIDIA/cutlass,Model development,Inference optimization,7233,4,0.06%,37,0.51%,1192,CUDA Templates for Linear Algebra Subroutines,"hwu36, kerrmudgeon, ANIKET-SHIVAM",100+,2017-11-30,2024-10-22,0
lucidrains/denoising-diffusion-pytorch,Model repo,,9142,4,0.04%,46,0.50%,1118,Implementation of Denoising Diffusion Probabilistic Model in Pytorch,"lucidrains, Adversarian, AlejandroSantorum",23,2020-08-26,2024-05-30,0
BuilderIO/gpt-crawler,Model development,Dataset engineering,21301,4,0.02%,107,0.50%,2275,Crawl a site to generate knowledge files to create your own custom GPT from a URL,"steve8708, marcelovicentegc, guillermoscript",29,2023-11-14,2024-08-06,0
deepseek-ai/Janus,Model repo,,17028,4,0.02%,81,0.48%,2224,Janus-Series: Unified Multimodal Understanding and Generation Models,"learningpro, hills-code, soloice",8,2024-10-18,2024-10-31,0
IntelLabs/fastRAG,AI engineering,AIE framework,1507,4,0.27%,7,0.46%,138,Efficient Retrieval Augmentation and Generation Framework,"danielfleischer, peteriz, mosheber",12,2023-01-23,2024-11-25,0
stas00/ml-engineering,Tutorials,,13321,4,0.03%,54,0.41%,810,Machine Learning Engineering Open Book,"stas00, Quentin-Anthony, pitmonticone",40,2020-09-02,2025-04-04,0
openai/swarm,AI engineering,Agent,19546,4,0.02%,73,0.37%,2081,"Educational framework exploring ergonomic, lightweight multi-agent orchestration. Managed by OpenAI Solution team.","ibigio, shyamal-anadkat, jhills20",14,2024-02-22,2024-10-15,0
google-deepmind/alphageometry,Model repo,Multimodal,4436,4,0.09%,15,0.34%,504,,thtrieu,1,2023-10-09,1000-01-01,0
MineDojo/Voyager,Applications,Bots,6016,4,0.07%,20,0.33%,570,An Open-Ended Embodied Agent with Large Language Models,"xieleo5, cmaranes, FOLLGAD",12,2023-05-25,2023-07-27,0
haotian-liu/LLaVA,Model repo,Multimodal,22094,4,0.02%,71,0.32%,2428,[NeurIPS'23 Oral] Visual Instruction Tuning (LLaVA) built towards GPT-4V level capabilities and beyond.,"haotian-liu, ChunyuanLI, mattmazzola",48,2023-04-17,2023-11-27,0
higgsfield-ai/higgsfield,Infrastructure,Compute management,3334,4,0.12%,10,0.30%,558,"Fault-tolerant, highly scalable GPU orchestration, and a machine learning framework designed for training models with billions to trillions of parameters","arpanetus, higgsfield, zxcjhg",4,2018-05-26,2024-03-23,0
bentoml/OpenLLM,Infrastructure,Serving,11084,4,0.04%,33,0.30%,706,"Run any open-source LLMs, such as DeepSeek and Llama, as OpenAI compatible API endpoint in the cloud.","aarnphm, bojiang, Sherlock113",31,2023-04-19,2025-04-01,9777
mlc-ai/web-llm,AI engineering,AI interface,15149,4,0.03%,45,0.30%,990,High-performance In-browser LLM Inference Engine ,"CharlieFRuan, Neet-Nestor, tqchen",45,2023-04-13,2025-01-21,0
BlinkDL/RWKV-LM,Model repo,,13478,4,0.03%,40,0.30%,906,"RWKV (pronounced RwaKuv) is an RNN with great LLM performance, which can also be directly trained like a GPT transformer (parallelizable). We are at RWKV-7 ""Goose"". So it's combining the best of RNN and transformer - great performance, linear time, constant space (no kv-cache), fast training, infinite ctx_len, and free sentence embedding.","BlinkDL, www, saharNooby",5,2021-08-08,2023-10-05,580
Lightning-AI/litgpt,Model development,Modeling & training,11923,4,0.03%,35,0.29%,1207,"20+ high-performance LLMs with recipes to pretrain, finetune and deploy at scale.","carmocca, rasbt, awaelchli",96,2023-05-04,2025-04-05,0
unifyai/ivy,Infrastructure,Toolings,14141,4,0.03%,39,0.28%,5681,Convert Machine Learning Code Between Frameworks,"djl11, vedpatwardhan, Ishticode",100+,2021-01-19,2025-02-20,0
jessevig/bertviz,Model development,Toolings,7294,4,0.05%,18,0.25%,808,"BertViz: Visualize Attention in NLP Models (BERT, GPT2, BART, etc.) ","jessevig, cyber-raskolnikov, martinsotir",5,2018-12-16,2022-07-24,0
zenml-io/zenml,Infrastructure,Compute management,4504,4,0.09%,11,0.24%,499,ZenML 🙏: The bridge between ML and Ops. https://zenml.io.,"htahir1, bcdurak, strickvl",100+,2020-11-19,2025-04-04,1
sashabaranov/go-openai,AI engineering,AI interface,9811,4,0.04%,22,0.22%,1539,"OpenAI ChatGPT, GPT-3, GPT-4, DALL·E, Whisper API wrapper for Go","sashabaranov, vvatanabe, liushuangls",100+,2020-08-18,2025-03-04,0
TransformerOptimus/SuperAGI,AI engineering,Agent,16162,4,0.02%,34,0.21%,1949,"<⚡️> SuperAGI - A dev-first open source autonomous AI agent framework. Enabling developers to build, manage & run useful autonomous agents quickly and reliably.","NishantBorthakur, Fluder-Paradyne, luciferlinx101",62,2023-05-13,2024-01-12,0
Netflix/metaflow,Infrastructure,Compute management,8697,4,0.05%,18,0.21%,824,"Build, Manage and Deploy AI/ML Systems","savingoyal, romain-intel, saikonen",100+,2019-09-17,2025-04-04,0
invoke-ai/InvokeAI,Applications,Image production,24785,4,0.02%,49,0.20%,2516,"Invoke is a leading creative engine for Stable Diffusion models, empowering professionals, artists, and enthusiasts to generate and create visual media using the latest AI-driven technologies. The solution offers an industry leading WebUI, and serves as the foundation for multiple commercial products.","psychedelicious, lstein, blessedcoolant",100+,2022-08-17,2025-04-04,165546
mlc-ai/mlc-llm,Model development,Inference optimization,20326,4,0.02%,38,0.19%,1706,Universal LLM Deployment Engine with ML Compilation,"MasterJH5574, junrushao, tqchen",100+,2023-04-29,2025-03-27,0
leon-ai/leon,Applications,Bots,16126,4,0.02%,25,0.16%,1336,🧠 Leon is your open-source personal assistant.,"louistiti, theoludwig, iifeoluwa",19,2019-02-10,2021-04-06,38319
StanGirard/quivr,Applications,Workflow automation,37660,4,0.01%,46,0.12%,3636,"Opiniated RAG for integrating GenAI in your apps 🧠   Focus on your product rather than the RAG. Easy integration in existing products with customisation!  Any LLM: GPT4, Groq, Llama. Any Vectorstore: PGVector, Faiss. Any Files. Anyway you want. ","StanGirard, mamadoudicko, Zewed",100+,2023-05-12,2025-04-02,0
HigherOrderCO/Bend,Model development,Modeling & training,18610,4,0.02%,22,0.12%,458,"A massively parallel, high-level programming language","imaqtkatt, developedby, LunaAmora",42,2023-08-29,2025-02-22,0
karpathy/llama2.c,Model development,Inference optimization,18261,4,0.02%,19,0.10%,2229,Inference Llama 2 in one file of pure C,"karpathy, mpcusack-color, atamurad",87,2023-07-23,2024-02-13,0
binary-husky/gpt_academic,Applications,Info aggregation,68095,4,0.01%,50,0.07%,8331,"为GPT/GLM等LLM大语言模型提供实用化交互接口，特别优化论文阅读/润色/写作体验，模块化设计，支持自定义快捷按钮&函数插件，支持Python和C++等项目剖析&自译解功能，PDF/LaTex论文翻译&总结功能，支持并行问询多种LLM模型，支持chatglm3等本地模型。接入通义千问, deepseekcoder, 讯飞星火, 文心一言, llama2, rwkv, claude2, moss等。","binary-husky, binary-sky, Skyzayre",100+,2023-03-20,2025-03-24,14573
michaelfeil/infinity,Infrastructure,Serving,2020,3,0.15%,62,3.07%,134,"Infinity is a high-throughput, low-latency serving engine for text-embeddings, reranking models, clip, clap and colpali","michaelfeil, wirthual, chiragjn",29,2023-10-11,2025-03-28,0
btahir/open-deep-research,Applications,Info aggregation,1760,3,0.17%,40,2.27%,166,Open source alternative to Gemini Deep Research. Generate reports with AI based on search results.,btahir,1,2024-12-24,2025-02-22,0
JoshuaC215/agent-service-toolkit,AI engineering,Agent,2606,3,0.12%,48,1.84%,377,"Full toolkit for running an AI agent service built with LangGraph, FastAPI and Streamlit","JoshuaC215, gbaian10, xytian315",15,2024-08-04,2025-03-22,0
Canner/WrenAI,Applications,Coding,7364,3,0.04%,114,1.55%,687,"🤖 Open-source GenBI AI Agent that empowers data-driven teams to chat with their data to generate Text-to-SQL, charts, spreadsheets, reports, dashboards and BI. 📈📊📋🧑‍💻","cyyeh, onlyjackfrost, wwwy3y3",35,2024-03-13,2025-04-03,3577
casibase/casibase,AI engineering,AIE framework,3452,3,0.09%,41,1.19%,406,"⚡️AI Cloud OS: Open-source enterprise-level AI knowledge base and Manus-like agent management platform with admin UI, user management and Single-Sign-On⚡️, supports ChatGPT, Claude, DeepSeek R1, Llama, Ollama, HuggingFace, etc., chat bot demo: https://ai.casibase.com, admin UI demo: https://ai-admin.casibase.com","hsluoyz, MartinRepo, Sherlocksuper",36,2020-05-29,2025-04-05,0
Agenta-AI/agenta,AI engineering,AIE framework,2310,3,0.13%,27,1.17%,284,"The open-source LLMOps platform: prompt playground, prompt management, LLM evaluation, and LLM Observability all in one place.","mmabrouk, aybruhm, bekossy",65,2023-04-26,2025-03-28,0
Giskard-AI/giskard,AI engineering,Evals,4445,3,0.07%,41,0.92%,314,🐢 Open-Source Evaluation & Testing for AI & LLM systems,"kevinmessiaen, andreybavt, mattbit",50,2022-03-06,2025-04-01,266
eli64s/readme-ai,Applications,Coding,1902,3,0.16%,17,0.89%,194,"README file generator, powered by AI.","eli64s, diekotto, emmanuel-ferdman",6,2023-01-16,2025-03-18,0
TheRamU/Fay,AI engineering,AI interface,10705,3,0.03%,90,0.84%,1988,fay是一个帮助数字人（2.5d、3d、移动、pc、网页）或大语言模型（openai兼容、deepseek）连通业务系统的agent框架。,"xszyou, wangzai23333, TheRamU",5,2022-06-13,2024-04-19,0
smallcloudai/refact,Applications,Coding,1855,3,0.16%,15,0.81%,127,"AI Agent that handles engineering tasks end-to-end: integrates with developers’ tools, plans, executes, and iterates until it achieves a successful result.","olegklimov, MarcMcIntosh, JegernOUTT",24,2023-04-19,2025-04-06,2
pionxzh/chatgpt-exporter,Tutorials,,1518,3,0.20%,12,0.79%,132,Export and Share your ChatGPT conversation history,"pionxzh, michaelskyba, Notarin",23,2022-12-05,2025-02-02,0
AgentOps-AI/agentops,AI engineering,Agent,4170,3,0.07%,28,0.67%,376,"Python SDK for AI agent monitoring, LLM cost tracking, benchmarking, and more. Integrates with most LLMs and agent frameworks including OpenAI Agents SDK, CrewAI, Langchain, Autogen, AG2, and CamelAI","areibman, HowieG, bboynton97",39,2023-08-15,2025-04-02,0
Exafunction/codeium.vim,Applications,Coding,4766,3,0.06%,28,0.59%,179,"Free, ultrafast Copilot alternative for Vim and Neovim","fortenforge, pqn, sullivan-sean",45,2023-01-03,2025-04-04,0
QData/TextAttack,Model development,Dataset engineering,3128,3,0.10%,18,0.58%,415,"TextAttack 🐙  is a Python framework for adversarial attacks, data augmentation, and model training in NLP https://textattack.readthedocs.io/en/master/","jxmorris12, qiyanjun, jinyongyoo",55,2019-10-15,2024-07-25,0
eric-mitchell/direct-preference-optimization,Model development,Modeling & training,2498,3,0.12%,14,0.56%,203,Reference implementation for DPO (Direct Preference Optimization),eric-mitchell,1,2023-06-22,2023-07-23,0
frdel/agent-zero,AI engineering,Agent,6463,3,0.05%,36,0.56%,1408,Agent Zero AI framework,"frdel, 3clyp50, Jbollenbacher",12,2024-06-10,2025-04-01,1646
tensorchord/Awesome-LLMOps,Lists,,4676,3,0.06%,26,0.56%,457,An awesome & curated list of best LLMOps tools for developers,"gaocegege, WarrenWen666, NeroHin",98,2022-04-15,2025-03-27,0
facebookresearch/jepa,Model repo,,2888,3,0.10%,16,0.55%,283,PyTorch code and models for V-JEPA self-supervised learning from video.,"MidoAssran, bryant1410, orena1",5,2024-02-12,2025-02-27,0
beir-cellar/beir,AI engineering,Evals,1761,3,0.17%,9,0.51%,203,"A Heterogeneous Benchmark for Information Retrieval. Easy to use, evaluate your models across 15+ diverse IR datasets.","thakur-nandan, NouamaneTazi, nreimers",15,2021-01-18,2025-02-25,0
Ironclad/rivet,AI engineering,Agent,3653,3,0.08%,18,0.49%,303,The open-source visual AI programming environment and TypeScript library,"abrenneke, gogwilt, viveknair",35,2023-04-22,2025-04-05,34561
NVIDIA/Megatron-LM,Model repo,,11992,3,0.03%,59,0.49%,2689,Ongoing research training transformer models at scale,"jaredcasper, ko3n1g, lmcafee-nvidia",100+,2019-03-21,1000-01-01,0
google-research/big_vision,Model development,Modeling & training,2784,3,0.11%,13,0.47%,179,"Official codebase used to develop Vision Transformer, SigLIP, MLP-Mixer, LiT and more.","akolesnikoff, lucasb-eyer, andsteing",12,2022-04-04,2024-03-21,0
yl4579/StyleTTS2,Model repo,Multimodal,5611,3,0.05%,26,0.46%,527,StyleTTS 2: Towards Human-Level Text-to-Speech through Style Diffusion and Adversarial Training with Large Speech Language Models,"yl4579, fakerybakery, kmn1024",11,2023-06-14,2024-03-07,0
langchain-ai/langchainjs,AI engineering,AIE framework,14113,3,0.02%,64,0.45%,2406,🦜🔗 Build context-aware reasoning applications 🦜🔗,"jacoblee93, bracesproul, nfcampos",100+,2023-02-06,2025-04-04,0
bbycroft/llm-viz,Tutorials,,4595,3,0.07%,20,0.44%,518,3D Visualization of an GPT-style LLM,"bbycroft, iblech",2,2023-03-01,2023-12-05,0
modelscope/modelscope,AI engineering,AIE framework,7656,3,0.04%,33,0.43%,785,ModelScope: bring the notion of Model-as-a-Service to life.,"wenmengzhou, tastelikefeet, wangxingjun778",100+,2022-07-25,2025-04-06,0
huggingface/text-embeddings-inference,Infrastructure,Serving,3386,3,0.09%,14,0.41%,236,A blazing fast inference solution for text embeddings models,"OlivierDehaene, Narsil, alvarobartt",39,2023-10-13,2025-03-26,0
google-research/vision_transformer,Model repo,Multimodal,11164,3,0.03%,44,0.39%,1365,,"andsteing, akolesnikoff, sayakpaul",21,2020-10-21,2024-02-06,0
state-spaces/mamba,Model repo,,14480,3,0.02%,56,0.39%,1263,Mamba SSM architecture,"tridao, albertfgu, epicfilemcnulty",36,2023-12-01,2025-04-01,4815
deepseek-ai/DeepSeek-LLM,Model repo,,6272,3,0.05%,24,0.38%,969,DeepSeek LLM: Let there be answers,"DeepSeekPH, stack-heap-overflow, zdaxie",7,2023-11-29,2024-02-04,0
BlinkDL/AI-Writer,Applications,Writing,3138,3,0.10%,11,0.35%,527,AI 写小说，生成玄幻和言情网文等等。中文预训练生成模型。采用我的 RWKV 模型，类似 GPT-2 。AI写作。RWKV for Chinese novel generation.,"BlinkDL, www, Wrg1t",3,2021-07-26,2022-01-15,27271
allenai/OLMo,Model repo,,5468,3,0.05%,19,0.35%,589,"Modeling, training, eval, and inference code for OLMo","dirkgr, epwalsh, 2015aroras",49,2023-02-20,2025-03-21,45
weaviate/Verba,Applications,Info aggregation,7003,3,0.04%,24,0.34%,764,Retrieval Augmented Generation (RAG) chatbot powered by Weaviate,"thomashacker, erika-cardenas, dudanogueira",36,2023-07-28,2025-03-24,48
chatchat-space/Langchain-Chatchat,AI engineering,AIE framework,34534,3,0.01%,105,0.30%,5830,"Langchain-Chatchat（原Langchain-ChatGLM）基于 Langchain 与 ChatGLM, Qwen 与 Llama 等语言模型的 RAG 与 Agent 应用 | Langchain-Chatchat (formerly langchain-ChatGLM), local knowledge based LLM (like ChatGLM, Qwen and Llama) RAG and Agent app with langchain ","imClumsyPanda, liunux4odoo, glide-the",100+,2023-03-31,2025-03-25,0
vespa-engine/vespa,Infrastructure,VectorDB,6101,3,0.05%,17,0.28%,627,"AI + Data, online. https://vespa.ai","baldersheim, jonmv, bjorncs",92,2016-06-03,2025-04-04,39816
bigscience-workshop/petals,Model development,Inference optimization,9543,3,0.03%,25,0.26%,550,"🌸 Run LLMs at home, BitTorrent-style. Fine-tuning and inference up to 10x faster than offloading","borzunov, justheuristic, dbaranchuk",17,2022-06-12,2024-08-25,0
sensity-ai/dot,Applications,Image production,4288,3,0.07%,11,0.26%,437,The Deepfake Offensive Toolkit,"ajndkr, Ghassen-Chaabouni, giorgiop",6,2022-06-04,2024-06-14,5397
neuml/txtai,Infrastructure,VectorDB,10677,3,0.03%,26,0.24%,677,"💡 All-in-one open-source embeddings database for semantic search, LLM orchestration and language model workflows","davidmezzetti, saucam, 0206pdh",18,2020-08-09,2025-01-02,8248
HigherOrderCO/HVM,Infrastructure,Toolings,10944,3,0.03%,25,0.23%,420,"A massively parallel, optimal functional runtime in Rust","VictorTaelin, enricozb, kings177",18,2022-01-03,2024-08-21,0
PrefectHQ/marvin,AI engineering,Prompt engineering,5598,3,0.05%,12,0.21%,360,✨ AI agents that spark joy,"jlowin, zzstoatzz, aaazzam",52,2023-03-10,2025-03-31,0
dedupeio/dedupe,AI engineering,Dataset engineering,4261,3,0.07%,9,0.21%,558,":id: A python library for accurate and scalable fuzzy matching, record deduplication and entity-resolution.","fgregg, derekeder, NickCrews",53,2012-04-20,2024-11-01,0
rockbenben/ChatGPT-Shortcut,AI engineering,Prompt engineering,6166,3,0.05%,13,0.21%,719,"🚀💪Maximize your efficiency and productivity, support for English,中文,Español,العربية. 让生产力加倍的AI快捷指令。更有效地定制、保存和分享自己的提示词。在提示词分享社区中，轻松找到适用于不同场景的指令。","rockbenben, francisafu, CharlieJCJ",6,2023-02-24,2025-04-03,32
kubeflow/kubeflow,Infrastructure,Compute management,14834,3,0.02%,30,0.20%,2480,Machine Learning Toolkit for Kubernetes,"jlewi, kunmingg, lluunn",100+,2017-11-30,2025-03-26,79387
karpathy/minbpe,Model development,Dataset engineering,9555,3,0.03%,19,0.20%,910,"Minimal, clean code for the Byte Pair Encoding (BPE) algorithm commonly used in LLM tokenization.","karpathy, ViswanathaReddyGajjala, ahmedivy",10,2024-02-16,2024-04-22,0
openvinotoolkit/openvino,Infrastructure,Serving,8075,3,0.04%,14,0.17%,2533,OpenVINO™ is an open source toolkit for optimizing and deploying AI inference,"ilya-lavrenov, ilyachur, rkazants",100+,2018-10-15,2025-04-04,256
google-research/google-research,Model development,Modeling & training,35284,3,0.01%,61,0.17%,8056,Google Research,"joel-shor, rybakov, vratnakar",100+,2018-10-04,1000-01-01,0
cleanlab/cleanlab,Model development,Dataset engineering,10438,3,0.03%,17,0.16%,821,"The standard data-centric AI package for data quality and machine learning with messy, real-world data and labels.","cgnorthcutt, jwmueller, elisno",54,2018-05-11,2025-02-27,0
Mozilla-Ocho/llamafile,Model development,Inference optimization,22109,3,0.01%,36,0.16%,1161,Distribute and run LLMs with a single file.,"jart, cjpais, stlhood",58,2023-09-10,2025-04-02,8185
1rgs/jsonformer,AI engineering,Prompt engineering,4674,3,0.06%,7,0.15%,175,A Bulletproof Way to Generate Structured JSON from Language Models,"1rgs, danielcorin, ishmandoo",6,2023-04-29,2023-05-30,0
togethercomputer/moa,AI engineering,Agent,2719,3,0.11%,4,0.15%,364,Together Mixture-Of-Agents (MoA) –  65.1% on AlpacaEval with OSS models,"Nutlope, LorrinWWW, IsThatYou",6,2024-06-04,2025-01-07,0
jina-ai/jina,Infrastructure,Toolings,21500,3,0.01%,27,0.13%,2221,☁️ Build multimodal AI applications with cloud-native stack,"hanxiao, JoanFM, nan-wang",100+,2020-02-13,2025-03-24,0
iterative/dvc,Infrastructure,Toolings,14338,3,0.02%,16,0.11%,1208,🦉 Data Versioning and ML Experiments,"efiop, skshetry, pmrowla",100+,2017-03-04,2025-03-25,296166
arc53/DocsGPT,Applications,Info aggregation,15506,3,0.02%,15,0.10%,1656,"DocsGPT is an open-source genAI tool that helps users get reliable answers from knowledge source, while avoiding hallucinations. It enables private and reliable information retrieval, with tooling and agentic system capability built in.","dartpain, ManishMadan2882, siiddhantt",100+,2023-02-02,2025-04-04,89
facebookresearch/llama,Model repo,,58006,3,0.01%,48,0.08%,9726,Inference code for Llama models,"jspisak, ruanslv, sekyondaMeta",48,2023-02-14,2024-07-23,0
LAION-AI/Open-Assistant,Applications,Bots,37288,3,0.01%,16,0.04%,3268,"OpenAssistant is a chat-based assistant that understands tasks, can interact with third-party systems, and retrieve information dynamically to do so.","yk, fozziethebeat, andreaskoepf",100+,2022-12-13,2024-01-06,0
lencx/ChatGPT,Applications,Bots,53671,3,0.01%,18,0.03%,6059,"🔮 ChatGPT Desktop Application (Mac, Windows and Linux)","lencx, yixinBC, tk103331",29,2022-12-07,2024-08-29,4122234
openai/chatgpt-retrieval-plugin,Applications,Bots,21160,3,0.01%,5,0.02%,3691,The ChatGPT Retrieval Plugin lets you easily find personal or work documents by asking questions in natural language.,"isafulf, logankilpatrick, pablocastro",38,2023-03-23,2024-04-24,0
allenai/open-instruct,Model development,Modeling & training,2878,2,0.07%,35,1.22%,370,AllenAI's post-training codebase,"hamishivi, vwxyzjn, yizhongw",25,2023-06-09,2025-04-05,0
run-llama/LlamaIndexTS,AI engineering,AIE framework,2489,2,0.08%,27,1.08%,420,Data framework for your LLM applications. Focus on server side solution,"yisding, himself65, marcusschiesser",100+,2023-06-13,2025-04-05,0
QwenLM/Qwen2.5-Coder,Model repo,Coding,4781,2,0.04%,50,1.05%,378,"Qwen2.5-Coder is the code version of Qwen2.5, the large language model series developed by Qwen team, Alibaba Cloud.","huybery, CSJianYang, cyente",26,2024-04-16,2025-03-19,0
OpenGVLab/InternVideo,Model repo,Multimodal,1781,2,0.11%,17,0.95%,107,[ECCV2024] Video Foundation Models & Data for Multimodal Understanding,"shepnerd, yinanhe, leexinhao",20,2022-11-23,2025-02-13,0
landing-ai/vision-agent,AI engineering,Agent,4474,2,0.04%,37,0.83%,504,Vision agent,"AsiaCao, dillonalaird, humpydonkey",28,2024-02-13,2025-04-02,0
dauparas/ProteinMPNN,Model repo,,1220,2,0.16%,10,0.82%,343,Code for the ProteinMPNN paper,"dauparas, sokrypton, roccomoretti",6,2022-05-26,2022-11-29,0
deepseek-ai/DeepSeek-VL2,Model development,Modeling & training,4673,2,0.04%,38,0.81%,1704,DeepSeek-VL2: Mixture-of-Experts Vision-Language Models for Advanced Multimodal Understanding,"StevenLiuWen, charlesCXK, CUHKSZzxy",7,2024-12-13,2025-02-20,0
ModelTC/lightllm,Infrastructure,Serving,3089,2,0.06%,25,0.81%,244,"LightLLM is a Python-based LLM (Large Language Model) inference and serving framework, notable for its lightweight design, easy scalability, and high-speed performance.","hiworldwzj, shihaobai, WANDY666",34,2023-07-22,2025-04-06,0
ollama/ollama-js,AI engineering,AI interface,3245,2,0.06%,25,0.77%,287,Ollama JavaScript library,"BruceMacD, saul-jb, jmorganca",29,2023-09-13,2025-02-24,0
InternLM/lmdeploy,Infrastructure,Serving,5999,2,0.03%,45,0.75%,518,"LMDeploy is a toolkit for compressing, deploying, and serving LLMs.","lvhan028, AllentDan, grimoire",97,2023-06-15,2025-04-04,260
NVIDIA/aistore,Infrastructure,Data management,1454,2,0.14%,10,0.69%,199,AIStore: scalable storage for AI applications,"alex-aizman, VirrageS, VladimirMarkelov",37,2017-12-14,2025-03-02,2156
aurelio-labs/semantic-router,AI engineering,AIE framework,2512,2,0.08%,17,0.68%,248,Superfast AI decision making and intelligent processing of multi-modal data.,"jamescalam, Siraj-Aizlewood, simjak",41,2023-10-30,2025-04-03,0
guardrails-ai/guardrails,AI engineering,Prompt engineering,4729,2,0.04%,30,0.63%,376,Adding guardrails to large language models.,"zsimjee, CalebCourier, dtam",64,2023-01-29,2025-03-28,0
cheshire-cat-ai/core,AI engineering,AIE framework,2710,2,0.07%,17,0.63%,367,AI agent microservice,"pieroit, Pingdred, zAlweNy26",74,2023-02-08,2025-04-03,0
simplescaling/s1,Model development,Dataset engineering,6119,2,0.03%,33,0.54%,716,s1: Simple test-time scaling,"Muennighoff, invictus717, swj0419",13,2025-02-01,2025-02-13,0
THUDM/AgentBench,AI engineering,Evals,2481,2,0.08%,13,0.52%,184,A Comprehensive Benchmark to Evaluate LLMs as Agents (ICLR'24),"HenryCai11, Xiao9905, Longin-Yu",12,2023-07-28,2025-01-30,0
AlibabaResearch/DAMO-ConvAI,Model development,Modeling & training,1362,2,0.15%,7,0.51%,213,DAMO-ConvAI: The official repository which contains the codebase for Alibaba DAMO Conversational AI.,"huybery, tnlin, AIRobotZhang",19,2022-07-15,2025-03-20,0
cg123/mergekit,Model development,Modeling & training,5513,2,0.04%,28,0.51%,523,Tools for merging pretrained large language models.,"cg123, MonsterAzi, nyxkrage",30,2023-08-21,2025-04-05,0
alexrudall/ruby-openai,AI engineering,AI interface,3020,2,0.07%,15,0.50%,359,OpenAI API + Ruby! 🤖❤️ Now with Responses API + DeepSeek!,"alexrudall, dependabot-preview[bot], willywg",66,2020-08-03,2025-03-30,0
traceloop/openllmetry,Infrastructure,Monitoring,5610,2,0.04%,27,0.48%,717,"Open-source observability for your LLM application, based on OpenTelemetry","nirga, galkleinman, dinmukhamedm",66,2023-09-02,2025-03-28,0
NVIDIA/NeMo-Guardrails,AI engineering,Evals,4599,2,0.04%,22,0.48%,458,NeMo Guardrails is an open-source toolkit for easily adding programmable guardrails to LLM-based conversational systems.,"drazvan, schuellc-nvidia, Pouyanpi",77,2023-04-18,2025-04-05,0
andrewyng/aisuite,AI engineering,AIE framework,11874,2,0.02%,56,0.47%,1160,"Simple, unified interface to multiple Generative AI providers ","ksolo, standsleeping, rohitprasad15",26,2024-06-30,2025-03-06,184
stanford-crfm/helm,AI engineering,Evals,2149,2,0.09%,10,0.47%,285,"Holistic Evaluation of Language Models (HELM), a framework to increase the transparency of language models (https://arxiv.org/abs/2211.09110). This framework is also used to evaluate text-to-image models in HEIM (https://arxiv.org/abs/2311.04287) and vision-language models in VHELM (https://arxiv.org/abs/2410.07112).","teetone, yifanmai, percyliang",100+,2021-11-29,2025-04-06,0
sqlchat/sqlchat,Applications,Coding,5059,2,0.04%,23,0.45%,440,Chat-based SQL Client and Editor for the next decade,"boojack, tianzhou, CorrectRoadH",12,2023-03-16,2024-06-07,0
Lightning-AI/lightning-thunder,Model development,Inference optimization,1320,2,0.15%,6,0.45%,92,"Thunder gives you PyTorch models superpowers for training and inference. Unlock out-of-the-box optimizations for performance, memory and parallelism, or roll out your own.","t-vi, mruberry, IvanYashchuk",60,2024-03-18,2025-04-04,0
EleutherAI/pythia,Model repo,,2441,2,0.08%,11,0.45%,181,The hub for EleutherAI's work on interpretability and learning dynamics,"haileyschoelkopf, StellaAthena, lintangsutawika",20,2021-12-25,2025-03-10,0
seatgeek/thefuzz,AI engineering,Dataset engineering,3150,2,0.06%,14,0.44%,147,Fuzzy String Matching in Python,"maxbachmann, johnthedebs, hugovk",13,2021-03-05,2025-03-03,0
labmlai/labml,Infrastructure,Monitoring,2134,2,0.09%,9,0.42%,139,🔎 Monitor deep learning model training and hardware usage from your mobile phone 📱,"vpj, lakshith-403, hnipun",8,2018-11-16,2025-02-22,0
Farama-Foundation/PettingZoo,AI engineering,Agent,2847,2,0.07%,11,0.39%,430,"An API standard for multi-agent reinforcement learning environments, with popular reference environments and related utilities","jkterry1, benblack769, jjshoots",100+,2020-01-20,2025-03-28,0
huggingface/accelerate,Model development,Modeling & training,8583,2,0.02%,33,0.38%,1064,"🚀 A simple way to launch, train, and use PyTorch models on almost any device and distributed configuration, automatic mixed precision (including fp8), and easy-to-configure FSDP and DeepSpeed support","muellerzr, sgugger, pacman100",100+,2020-10-30,2025-04-03,0
katanaml/sparrow,Applications,Workflow automation,4455,2,0.04%,17,0.38%,447,"Data processing with ML, LLM and Vision LLM","abaranovskis-redsamurai, RockyNiu, ankurksingh1312",3,2022-01-08,2024-12-10,0
ashishpatel26/LLM-Finetuning,Tutorials,,2402,2,0.08%,9,0.37%,651,LLM Finetuning with peft,ashishpatel26,1,2023-06-08,1000-01-01,0
huggingface/setfit,Model development,Modeling & training,2433,2,0.08%,9,0.37%,237,Efficient few-shot learning with Sentence Transformers,"tomaarsen, lewtun, danielkorat",51,2022-06-30,2025-04-03,0
swirlai/swirl-search,Applications,Info aggregation,2725,2,0.07%,10,0.37%,250,"AI Search & RAG Without Moving Your Data. Get instant answers from your company's knowledge across 100+ apps while keeping data secure. Deploy in minutes, not months.","erikspears, dnicodemus, sid-swirl",30,2022-04-08,2025-04-04,0
mit-han-lab/smoothquant,Model development,Inference optimization,1377,2,0.15%,5,0.36%,167,[ICML 2023] SmoothQuant: Accurate and Efficient Post-Training Quantization for Large Language Models,"Guangxuan-Xiao, tonylins, songhan",3,2022-11-17,2023-02-10,0
MetaGLM/FinGLM,Model repo,,1937,2,0.10%,7,0.36%,287,FinGLM: 致力于构建一个开放的、公益的、持久的金融大模型项目，利用开源开放来促进「AI+金融」。,"jiawei243, dictmap, JamePeng",5,2023-09-25,2023-11-17,0
guinmoon/LLMFarm,AI engineering,AI interface,1668,2,0.12%,6,0.36%,120,llama and other  large language models on iOS and MacOS offline using GGML library.,guinmoon,1,2023-06-14,1000-01-01,208
deepseek-ai/DeepSeek-Coder-V2,Model repo,Coding,5569,2,0.04%,20,0.36%,844,DeepSeek-Coder-V2: Breaking the Barrier of Closed-Source Models in Code Intelligence,"guoday, soloice, luofuli",4,2024-06-14,2024-09-24,0
facebookresearch/xformers,Model development,Modeling & training,9280,2,0.02%,33,0.36%,654,"Hackable and optimized Transformers building blocks, supporting a composable construction.","danthe3rd, blefaudeux, bottler",95,2021-10-13,2025-03-25,0
mlfoundations/open_clip,Model repo,,11448,2,0.02%,40,0.35%,1083,An open source implementation of CLIP.,"rwightman, rom1504, gabrielilharco",52,2021-07-28,2025-03-17,327134
wgwang/LLMs-In-China,Lists,,6040,2,0.03%,21,0.35%,513,中国大模型,"wgwang, zhiweihu1103, shizhediao",7,2023-05-22,2024-11-30,0
vocodedev/vocode-python,AI engineering,AI interface,3238,2,0.06%,11,0.34%,555,🤖 Build voice-based LLM agents. Modular + open source.,"ajar98, Kian1354, HHousen",56,2023-02-24,2024-11-15,0
ggerganov/ggml,Model development,Inference optimization,12246,2,0.02%,40,0.33%,1197,Tensor library for machine learning,"ggerganov, slaren, JohannesGaessler",100+,2022-09-18,2025-04-04,0
ibis-project/ibis,Infrastructure,Data management,5667,2,0.04%,18,0.32%,628,the portable Python dataframe library,"cpcloud, ibis-squawk-bot[bot], kszucs",100+,2015-04-17,2025-04-05,141
NovaSky-AI/SkyThought,Model repo,,3178,2,0.06%,10,0.31%,320,Sky-T1: Train your own O1 preview model within $450,"SumanthRH, lynnliu030, DachengLi1",12,2025-01-09,2025-03-31,0
CompVis/latent-diffusion,Model repo,,12638,2,0.02%,38,0.30%,1594,High-Resolution Image Synthesis with Latent Diffusion Models,"rromb, crowsonkb, AK391",4,2021-12-20,2022-07-26,0
microsoft/lida,Model repo,Multimodal,3001,2,0.07%,9,0.30%,330,Automatic Generation of Visualizations and Infographics using Large Language Models,"victordibia, Alfred-Onuada, bx-h",14,2023-03-09,2024-03-03,0
openai/guided-diffusion,Model repo,Multimodal,6687,2,0.03%,20,0.30%,853,,"unixpickle, LeeDoYup, prafullasd",5,2021-04-27,2022-07-15,0
google/maxtext,Model repo,,1673,2,0.12%,5,0.30%,336,"A simple, performant and scalable Jax LLM!","gobbleturk, khatwanimohit, raymondzouu",100+,2023-02-28,2025-04-05,0
clovaai/donut,Model repo,Multimodal,6141,2,0.03%,17,0.28%,496,"Official Implementation of OCR-free Document Understanding Transformer (Donut) and Synthetic Document Generator (SynthDoG), ECCV 2022","gwkrsrch, moonbings, eltociear",7,2022-07-20,2022-10-05,0
salesforce/BLIP,Model repo,Multimodal,5154,2,0.04%,14,0.27%,680,PyTorch code for BLIP: Bootstrapping Language-Image Pre-training for Unified Vision-Language Understanding and Generation  ,AK391,1,2022-01-25,2022-02-08,0
hustvl/Vim,Model repo,Multimodal,3342,2,0.06%,9,0.27%,228,[ICML 2024] Vision Mamba: Efficient Visual Representation Learning with Bidirectional State Space Model,"Unrealluver, LegendBC, SeanSong-amd",5,2024-01-15,2025-02-13,0
SCIR-HI/Huatuo-Llama-Med-Chinese,Model repo,,4732,2,0.04%,12,0.25%,477,"Repo for BenTsao [original name: HuaTuo (华驼)], Instruction-tuning Large Language Models with Chinese Medical Knowledge. 本草（原名：华驼）模型仓库，基于中文医学知识的大语言模型指令微调","s65b40, thinksoso, FlowolfzzZ",8,2023-03-31,2025-02-21,0
OthersideAI/self-operating-computer,Applications,Workflow automation,9524,2,0.02%,24,0.25%,1285,A framework to enable multimodal models to operate a computer.,"joshbickett, michaelhhogue, klxu03",26,2023-11-04,2025-02-28,0
microsoft/UFO,Applications,Workflow automation,6661,2,0.03%,16,0.24%,854,A UI-Focused Agent for Windows OS Interaction.,"vyokky, Mac0q, MightyGaga",16,2024-01-08,2025-03-28,0
allegroai/clearml,Infrastructure,Serving,5920,2,0.03%,14,0.24%,672,"ClearML - Auto-Magical CI/CD to streamline your AI workload. Experiment Management, Data Management, Pipeline, Orchestration, Scheduling & Serving in one MLOps/LLMOps solution","pollfly, alex-burlacu-clear-ml, clearml-bot",96,2019-06-10,2025-03-16,2251
adieyal/sd-dynamic-prompts,AI engineering,Prompt engineering,2145,2,0.09%,5,0.23%,277,A custom script for AUTOMATIC1111/stable-diffusion-webui to implement a tiny template language for random prompt generation,"adieyal, akx, TheAwesomeGoat",27,2022-10-08,2024-05-26,0
taranjeet/awesome-gpts,Lists,,1297,2,0.15%,3,0.23%,89,Collection of all the GPTs created by the community,"aryankhanna475, taranjeet, ljgonzalez1",26,2023-11-10,2023-12-06,0
civitai/civitai,AI engineering,Prompt engineering,6527,2,0.03%,15,0.23%,648,"A repository of models, textual inversions, and more","JustMaier, bkdiehl, lrojas94",27,2022-10-11,2025-04-04,0
microsoft/TinyTroupe,Applications,Agent,6129,2,0.03%,14,0.23%,500,LLM-powered multiagent persona simulation for imagination enhancement and business insights.,"paulosalem, ewheeler, eltociear",8,2024-03-25,2025-03-26,0
huggingface/datasets,Model development,Dataset engineering,19943,2,0.01%,45,0.23%,2803,"🤗 The largest hub of ready-to-use datasets for ML models with fast, easy-to-use and efficient data manipulation tools","lhoestq, albertvillanova, mariosasko",100+,2020-03-26,2025-03-28,0
TabbyML/tabby,Applications,Coding,30674,2,0.01%,68,0.22%,1430,Self-hosted AI coding assistant,"wsxiaoys, icycodes, liangfung",100+,2023-03-16,2025-04-03,6103
huggingface/tokenizers,Model development,Dataset engineering,9560,2,0.02%,21,0.22%,870,💥 Fast State-of-the-Art Tokenizers optimized for Research and Production,"n1t0, Narsil, Pierrci",99,2019-11-01,2025-03-18,33
google/jax,Model development,Modeling & training,31846,2,0.01%,68,0.21%,2971,"Composable transformations of Python+NumPy programs: differentiate, vectorize, JIT to GPU/TPU, and more","hawkinsp, mattjj, jakevdp",100+,2018-10-25,2025-04-05,0
facebookresearch/seamless_communication,Model repo,Multimodal,11469,2,0.02%,24,0.21%,1136,Foundational Models for State-of-the-Art Speech and Text Translation,"cndn, kauterry, mavlyutovr",36,2023-08-01,2024-11-14,0
josStorer/RWKV-Runner,AI engineering,AIE framework,5765,2,0.03%,12,0.21%,545,"A RWKV management and startup tool, full automation, only 8MB. And provides an interface compatible with the OpenAI API. RWKV is a large language model that is fully open source and available for commercial use.","josStorer, HaloWang, eltociear",11,2023-05-05,2025-03-29,35949
NVIDIA/DALI,Model repo,,5347,2,0.04%,11,0.21%,630,A GPU-accelerated library containing highly optimized building blocks and an execution engine for data processing to accelerate deep learning training and inference applications.,"JanuszL, mzient, jantonguirao",89,2018-06-01,2025-04-04,0
WooooDyy/LLM-Agent-Paper-List,Lists,,7373,2,0.03%,15,0.20%,433,"The paper list of the 86-page paper ""The Rise and Potential of Large Language Model Based Agents: A Survey"" by Zhiheng Xi et al.","WooooDyy, zsxmwjz, Yiwen-Ding",36,2023-09-12,2024-06-07,0
zhayujie/bot-on-anything,AI engineering,AI interface,4053,2,0.05%,8,0.20%,924,"A large model-based chatbot builder that can quickly integrate AI models (including ChatGPT, Claude, Gemini) into various software applications (such as Telegram, Gmail, Slack, and websites).","zhayujie, RegimenArsenic, mouyong",23,2023-02-09,2025-01-03,0
THUDM/P-tuning-v2,Model development,Modeling & training,2027,2,0.10%,4,0.20%,202,An optimized deep prompt tuning strategy comparable to fine-tuning across scales and tasks,"Xiao9905, rainatam, sofyc",3,2021-10-14,2021-12-13,0
artidoro/qlora,Model development,Modeling & training,10360,2,0.02%,20,0.19%,842,QLoRA: Efficient Finetuning of Quantized LLMs,"artidoro, TimDettmers, tobi",15,2023-05-11,2023-07-24,0
sunner/ChatALL,AI engineering,AI interface,15706,2,0.01%,29,0.18%,1672," Concurrently chat with ChatGPT, Bing Chat, Bard, Alpaca, Vicuna, Claude, ChatGLM, MOSS, 讯飞星火, 文心一言 and more, discover the best answers","sunner, PeterDaveHello, tanchekwei",41,2023-04-08,2025-03-12,561804
Unity-Technologies/ml-agents,Applications,Bots,17922,2,0.01%,33,0.18%,4248,The Unity Machine Learning Agents Toolkit (ML-Agents) is an open-source project that enables games and simulations to serve as environments for training intelligent agents using deep reinforcement learning and imitation learning.,"vincentpierre, awjuliani, surfnerd",100+,2017-09-08,2024-10-28,87
skypilot-org/skypilot,Infrastructure,Compute management,7611,2,0.03%,14,0.18%,604,"SkyPilot: Run AI and batch jobs on any infra (Kubernetes or 16+ clouds). Get unified execution, cost savings, and high GPU availability via a simple interface.","Michaelvll, concretevitamin, romilbhardwaj",100+,2021-08-11,2025-04-05,0
salesforce/LAVIS,Model development,Modeling & training,10416,2,0.02%,19,0.18%,1013,LAVIS - A One-stop Library for Language-Vision Intelligence,"dxli94, henryhungle, artemisp",22,2022-08-24,2024-11-18,0
OpenBMB/XAgent,AI engineering,Agent,8283,2,0.02%,15,0.18%,880,An Autonomous LLM Agent for Complex Task Solving,"luyaxi, Umpire2018, sailaoda",34,2023-10-16,2024-03-04,0
openai/consistency_models,Model repo,Multimodal,6306,2,0.03%,11,0.17%,426,Official repo for consistency models.,"yang-song, sayakpaul, discus0434",9,2023-02-26,2023-08-12,0
microsoft/DeepSpeedExamples,Tutorials,,6403,2,0.03%,11,0.17%,1078,Example models using DeepSpeed,"lekurile, jeffra, awan-10",84,2020-01-29,2025-03-27,0
lutzroeder/netron,Tutorials,,29836,2,0.01%,51,0.17%,2877,"Visualizer for neural network, deep learning and machine learning models",lutzroeder,1,2010-12-26,1000-01-01,210743
openai/evals,AI engineering,Evals,15826,2,0.01%,27,0.17%,2683,"Evals is a framework for evaluating LLMs and LLM systems, and an open-source registry of benchmarks.","andrew-openai, rlbayes, jwang47",100+,2023-01-23,2024-12-18,0
allenai/scispacy,Model repo,,1784,2,0.11%,3,0.17%,236,A full spaCy pipeline and models for scientific/biomedical documents.,"dakinggg, DeNeutoy, ibeltagy",25,2018-09-24,2024-11-23,0
botpress/botpress,AI engineering,Agent,13498,2,0.01%,22,0.16%,1945,The open-source hub to build & deploy GPT/LLM Agents ⚡️,"franklevasseur, AbrahamLopez10, pascal-botpress",29,2016-11-16,2025-04-01,0
llm-workflow-engine/llm-workflow-engine,AI engineering,AIE framework,3690,2,0.05%,6,0.16%,469,Power CLI and Workflow manager for LLMs (core package),"thehunmonkgroup, mmabrouk, Tecuya",25,2022-12-03,2024-01-17,0
tensorflow/text,Model development,Dataset engineering,1253,2,0.16%,2,0.16%,350,Making text a first-class citizen in TensorFlow.,"broken, tf-text-github-robot, MarkDaoust",100+,2019-05-29,2025-03-24,0
guidance-ai/guidance,AI engineering,Prompt engineering,19996,2,0.01%,31,0.16%,1097,A guidance language for controlling large language models.,"slundberg, marcotcr, riedgar-ms",74,2022-11-10,2025-03-19,0
facebookresearch/pytorch3d,Model development,Modeling & training,9165,2,0.02%,14,0.15%,1358,PyTorch3D is FAIR's library of reusable components for deep learning with 3D data,"bottler, nikhilaravi, davnov134",100+,2019-10-25,1000-01-01,0
Mikubill/sd-webui-controlnet,Applications,Image production,17504,2,0.01%,26,0.15%,2005,WebUI extension for ControlNet,"lllyasviel, huchenlei, Mikubill",93,2023-02-12,2024-05-04,0
microsoft/unilm,Model development,Modeling & training,21021,2,0.01%,29,0.14%,2616,"Large-scale Self-supervised Pre-training Across Tasks, Languages, and Modalities","gitnlp, donglixp, wolfshow",61,2019-07-23,2025-03-04,7
OpenBMB/MiniCPM,Model repo,,7293,2,0.03%,10,0.14%,463,MiniCPM3-4B: An edge-side LLM that surpasses GPT-3.5-Turbo.,"LDLINGLINGLING, THUCSTHanxu13, ShengdingHu",25,2024-01-29,2024-11-06,0
easydiffusion/easydiffusion,Applications,Image production,9873,2,0.02%,13,0.13%,815,"An easy 1-click way to create beautiful artwork on your PC using AI, with no tech knowledge. Provides a browser UI for generating images from text prompts and images. Just enter your text prompt, and see the generated image.","cmdr2, JeLuF, madrang",42,2022-08-23,2025-02-07,902736
postgresml/postgresml,AI engineering,AI interface,6221,2,0.03%,8,0.13%,313,Postgres with GPUs for ML/AI apps.,"levkk, montanalow, SilasMarvin",44,2022-04-11,2025-01-22,0
mistralai/mistral-src,Model repo,,10152,2,0.02%,13,0.13%,911,Official inference library for Mistral models,"patrickvonplaten, timlacroix, pandora-s-git",26,2023-09-27,2025-03-20,0
leptonai/search_with_lepton,AI engineering,AIE framework,8074,2,0.02%,9,0.11%,1026,Building a quick conversation-based search demo with Lepton AI.,"Yangqing, vthinkxie, leoshi01",7,2024-01-23,2025-04-01,0
lk-geimfari/mimesis,AI engineering,Dataset engineering,4518,2,0.04%,5,0.11%,339,Mimesis is a robust data generator for Python that can produce a wide range of fake data in multiple languages.,"lk-geimfari, dependabot-preview[bot], sobolevn",100+,2016-09-09,2024-11-26,635
muellerberndt/mini-agi,AI engineering,Agent,2848,2,0.07%,3,0.11%,297,MiniAGI is a simple general-purpose autonomous agent based on the OpenAI API.,"muellerberndt, alaeddine-13, ismaelc",12,2023-04-14,2023-05-05,0
google-deepmind/alphafold,Model repo,Multimodal,13377,2,0.01%,14,0.10%,2363,Open source code for AlphaFold 2.,"Augustin-Zidek, Htomlinson14, tomwardio",17,2021-06-17,1000-01-01,0
mit-han-lab/streaming-llm,AI engineering,Prompt engineering,6846,2,0.03%,7,0.10%,381,[ICLR 2024] Efficient Streaming Language Models with Attention Sinks,"Guangxuan-Xiao, songhan, cosmojg",5,2023-09-29,2023-10-05,0
gpuweb/gpuweb,Infrastructure,Compute management,4974,2,0.04%,5,0.10%,325,Where the GPU for the Web work happens!,"kainino0x, dneto0, toji",100+,2017-02-10,2025-04-02,0
Shaunwei/RealChar,AI engineering,AI interface,6126,2,0.03%,6,0.10%,751,"🎙️🤖Create, Customize and Talk to your AI Character/Companion in Realtime (All in One Codebase!). Have a natural seamless conversation with AI everywhere (mobile, web and terminal) using LLM OpenAI GPT3.5/4, Anthropic Claude2, Chroma Vector DB, Whisper Speech2Text, ElevenLabs Text2Speech🎙️🤖","pycui, Shaunwei, lynchee-owo",36,2023-06-26,2024-02-03,0
openai/jukebox,Model repo,Multimodal,7964,2,0.03%,7,0.09%,1444,"Code for the paper ""Jukebox: A Generative Model for Music""","prafullasd, heewooj, jongwook",7,2020-04-29,2020-11-12,0
GaiZhenbiao/ChuanhuChatGPT,AI engineering,AI interface,15408,2,0.01%,12,0.08%,2288,"GUI for ChatGPT API and many LLMs. Supports agents, file-based QA, GPT finetuning and query with web search. All with a neat UI.","GaiZhenbiao, Keldos-Li, MZhao-ouo",51,2023-03-02,2024-09-23,1398
joke2k/faker,AI engineering,Dataset engineering,18234,2,0.01%,14,0.08%,1979,Faker is a Python package that generates fake data for you.,"fcurella, joke2k, jremes-foss",100+,2012-11-12,2025-01-03,0
spotify/annoy,Infrastructure,VectorDB,13644,2,0.01%,10,0.07%,1187,Approximate Nearest Neighbors in C++/Python optimized for memory usage and loading/saving to disk,"erikbern, eddelbuettel, chdsbd",66,2013-04-01,2020-12-03,0
modularml/mojo,Model development,Modeling & training,23839,2,0.01%,16,0.07%,2596,The MAX Platform (includes Mojo),"abduld, lattner, modularbot",100+,2023-04-28,2025-04-02,0
OpenMOSS/MOSS,Model repo,Agent,12040,2,0.02%,8,0.07%,1144,An open-source tool-augmented conversational language model from Fudan University,"txsun1997, xyltt, piglaker",16,2023-04-15,2023-05-16,0
openai/shap-e,Model repo,Multimodal,11856,2,0.02%,7,0.06%,990,Generate 3D objects conditioned on text or images,"unixpickle, heewooj, ahmadmustafaanis",8,2023-04-19,2023-11-08,0
mlc-ai/web-stable-diffusion,Applications,Image production,3649,2,0.05%,1,0.03%,231,Bringing stable diffusion models to web browsers. Everything runs inside the browser with no server support. ,"MasterJH5574, tqchen, eltociear",10,2023-03-06,2024-03-12,0
vectara/hallucination-leaderboard,AI engineering,Evals,2048,1,0.05%,152,7.42%,70,Leaderboard Comparing LLM Performance at Producing Hallucinations when Summarizing Short Documents,"ofermend, simonhughes22, mbae26",14,2023-10-31,2025-03-26,0
showlab/Show-o,Model repo,Multimodal,1321,1,0.08%,26,1.97%,58,"[ICLR 2025] Repository for Show-o, One Single Transformer to Unify Multimodal Understanding and Generation.","Sierkinhane, JosephPai, maobenz",7,2024-08-09,2025-03-09,0
Azure/PyRIT,AI engineering,Evals,2366,1,0.04%,29,1.23%,461,The Python Risk Identification Tool for generative AI (PyRIT) is an open source framework built to empower security professionals and engineers to proactively identify risks in generative AI systems.,"rlundeen2, romanlutz, rdheekonda",68,2023-12-12,2025-04-06,0
jeinlee1991/chinese-llm-benchmark,AI engineering,Evals,3939,1,0.03%,45,1.14%,168,目前已囊括203个大模型，覆盖chatgpt、gpt-4o、o3-mini、谷歌gemini、Claude3.5、智谱GLM-Zero、文心一言、qwen-max、百川、讯飞星火、商汤senseChat、minimax等商用模型， 以及DeepSeek-R1、qwq-32b、deepseek-v3、qwen2.5、llama3.3、phi-4、glm4、gemma3、mistral、书生internLM2.5等开源大模型。不仅提供能力评分排行榜，也提供所有模型的原始输出结果！,jeinlee1991,1,2023-06-04,1000-01-01,0
leondz/garak,AI engineering,Evals,4236,1,0.02%,42,0.99%,410,the LLM vulnerability scanner,"leondz, jmartin-tech, erickgalinkin",36,2023-05-10,2025-04-03,0
laiyer-ai/llm-guard,AI engineering,Prompt engineering,1565,1,0.06%,15,0.96%,190,The Security Toolkit for LLM Interactions,"asofter, CandiedCode, fgrosse",18,2023-07-27,2025-03-17,0
hendrycks/test,AI engineering,Evals,1371,1,0.07%,13,0.95%,99,Measuring Massive Multitask Language Understanding | ICLR 2021,"hendrycks, Helw150, xksteven",5,2020-09-07,2023-04-24,0
shibing624/MedicalGPT,Model development,Modeling & training,3786,1,0.03%,35,0.92%,554,MedicalGPT: Training Your Own Medical GPT Model with ChatGPT Training Pipeline. 训练医疗大模型，实现了包括增量预训练(PT)、有监督微调(SFT)、RLHF、DPO、ORPO、GRPO。,"shibing624, LIE-24, princepride",10,2023-06-02,2024-08-25,0
google/generative-ai-swift,AI engineering,AIE framework,1067,1,0.09%,9,0.84%,167,The official Swift library for the Google Gemini API,"andrewheard, peterfriese, keertk",18,2023-04-25,2024-12-17,0
ianand/spreadsheets-are-all-you-need,Tutorials,,1458,1,0.07%,12,0.82%,216,,ianand,1,2023-09-23,1000-01-01,15967
apple/ml-aim,Model repo,Multimodal,1257,1,0.08%,10,0.80%,61,This repository provides the code and model checkpoints for AIMv1 and AIMv2 research projects.,"aelnouby, hisloppy",2,2024-01-12,2024-01-19,0
kha-white/mokuro,Applications,Info aggregation,1150,1,0.09%,9,0.78%,80,Read Japanese manga inside browser with selectable text.,"kha-white, imsamuka, justremember",10,2022-04-16,2025-01-28,5
AI-Engineer-Foundation/agent-protocol,AI engineering,Agent,1160,1,0.09%,9,0.78%,141,Common interface for interacting with AI agents. The protocol is tech stack agnostic - you can use it with any framework for building agents.,"waynehamadi, jakubno, jzanecook",15,2023-07-06,2024-06-11,0
SalesforceAIResearch/uni2ts,Model repo,,1076,1,0.09%,8,0.74%,124,Unified Training of Universal Time Series Forecasting Transformers,"gorold, liu-jc, chenghaoliu89",8,2024-02-07,2025-02-24,0
ztxz16/fastllm,Model development,Inference optimization,3474,1,0.03%,24,0.69%,354,fastllm是c++实现，后端无依赖（仅依赖CUDA，无需依赖PyTorch）的高性能大模型推理库。  可实现单4090推理DeepSeek R1 671B INT4模型，单路可达20+tps。,"ztxz16, wildkid1024, TylunasLi",40,2023-05-13,2025-03-12,0
protectai/rebuff,AI engineering,Prompt engineering,1230,1,0.08%,8,0.65%,96,LLM Prompt Injection Detector,"woop, shrumm, seanpmorgan",9,2023-04-24,2024-01-25,0
argilla-io/distilabel,Model development,Dataset engineering,2616,1,0.04%,17,0.65%,189,"Distilabel is a framework for synthetic data and AI feedback for engineers who need fast, reliable and scalable pipelines based on verified research papers.","gabrielmbmb, plaguss, alvarobartt",34,2023-10-16,2025-02-27,0
tatsu-lab/alpaca_eval,AI engineering,Evals,1710,1,0.06%,11,0.64%,265,"An automatic evaluator for instruction-following language models. Human-validated, high-quality, cheap, and fast.","YannDubs, rtaori, lxuechen",80,2023-05-25,2024-08-26,0
EgoAlpha/prompt-in-context-learning,Lists,,1571,1,0.06%,10,0.64%,96,"Awesome resources for in-context learning and prompt engineering: Mastery of the LLMs such as ChatGPT, GPT-3, and FlanT5, with up-to-date and cutting-edge updates.","EgoAlpha, cyfedu-dlut, GeshengSunDUT",4,2023-03-08,2023-11-01,0
evidentlyai/evidently,Infrastructure,Monitoring,5972,1,0.02%,36,0.60%,659,"Evidently is ​​an open-source ML and LLM observability framework. Evaluate, test, and monitor any AI-powered system or data pipeline. From tabular data to Gen AI. 100+ metrics.","elenasamuylova, emeli-dral, Liraim",77,2020-11-25,2025-04-04,72
tensorchord/pgvecto.rs,Infrastructure,VectorDB,1991,1,0.05%,12,0.60%,75,"Scalable, Low-latency and Hybrid-enabled Vector Search in Postgres. Revolutionize Vector Search, not Database.","usamoi, kemingy, VoVAllen",20,2023-04-15,2025-02-26,1093
Haidra-Org/AI-Horde,Infrastructure,Compute management,1184,1,0.08%,7,0.59%,144,A crowdsourced distributed cluster for AI art and text generation,"db0, tazlin, donaldanixon",35,2022-09-13,2025-02-08,0
DAGWorks-Inc/burr,AI engineering,AIE framework,1558,1,0.06%,9,0.58%,76,"Build applications that make decisions (chatbots, agents, simulations, etc...). Monitor, trace, persist, and execute on your own infrastructure.","elijahbenizzy, skrawcz, zilto",18,2024-01-29,2025-03-29,0
DSXiangLi/DecryptPrompt,Lists,,2972,1,0.03%,17,0.57%,295,总结Prompt&LLM论文，开源数据&模型，AIGC应用,"DSXiangLi, gordonhu608, aFlyBird0",6,2023-02-10,2024-11-10,0
pytorch/torchtune,Model development,Modeling & training,5063,1,0.02%,28,0.55%,567,PyTorch native post-training library,"joecummings, ebsmothers, RdoubleA",100+,2023-10-20,2025-04-05,0
AI4Finance-Foundation/FinRL-Meta,Model development,Dataset engineering,1485,1,0.07%,8,0.54%,644,FinRL­®-Meta: Dynamic datasets and market environments for FinRL.,"zhumingpassional, rayrui312, YangletLiu",36,2021-02-16,2025-03-28,0
unum-cloud/usearch,Infrastructure,VectorDB,2627,1,0.04%,14,0.53%,173,"Fast Open-Source Search & Clustering engine × for Vectors & 🔜 Strings × in C++, C, Python, JavaScript, Rust, Java, Objective-C, Swift, C#, GoLang, and Wolfram 🔍","ashvardanian, gurgenyegoryan, mgevor",61,2023-02-22,2025-02-07,2124
IDEA-Research/GroundingDINO,Model repo,Multimodal,7777,1,0.01%,41,0.53%,785,"[ECCV 2024] Official implementation of the paper ""Grounding DINO: Marrying DINO with Grounded Pre-Training for Open-Set Object Detection""","SlongLiu, rentainhe, SkalskiP",27,2023-03-09,2024-03-11,141157
andreibondarev/langchainrb,AI engineering,AIE framework,1713,1,0.06%,9,0.53%,230,Build LLM-powered applications in Ruby,"andreibondarev, alchaplinsky, rickychilcott",100+,2023-04-18,2025-03-31,0
InternLM/lagent,AI engineering,Agent,2102,1,0.05%,11,0.52%,220,A lightweight framework for building LLM-based agents,"liujiangning30, Harold-lkk, braisedpork1964",34,2023-08-20,2025-03-14,0
InternLM/xtuner,Model development,Modeling & training,4452,1,0.02%,23,0.52%,337,"An efficient, flexible and full-featured toolkit for fine-tuning LLM (InternLM2, Llama3, Phi3, Qwen, Mistral, ...)","LZHgrla, HIT-cwh, pppppM",31,2023-07-11,2025-04-02,0
pytorch-labs/ao,Model development,Inference optimization,1936,1,0.05%,10,0.52%,235,PyTorch native quantization and sparsity for training and inference,"jerryzh168, vkuzo, msaroufim",100+,2023-11-03,2025-04-06,0
CalculatedContent/WeightWatcher,AI engineering,Evals,1567,1,0.06%,8,0.51%,130,The WeightWatcher tool for predicting the accuracy of   Deep Neural Networks,"charlesmartin14, cdhinrichs, reserena",6,2018-11-28,2023-02-12,0
noamgat/lm-format-enforcer,AI engineering,Prompt engineering,1765,1,0.06%,9,0.51%,76,"Enforce the output format (JSON Schema, Regex etc) of a language model","noamgat, gcalmettes, aw632",11,2023-09-21,2025-02-26,0
QwenLM/Qwen-VL,Model repo,Multimodal,5735,1,0.02%,29,0.51%,436,The official repo of Qwen-VL (通义千问-VL) chat & pretrained large vision language model proposed by Alibaba Cloud.,"ShuaiBai623, jinze1994, simonJJJ",13,2023-08-21,2023-09-14,0
pezzolabs/pezzo,AI engineering,AIE framework,2777,1,0.04%,14,0.50%,242,"🕹️ Open-source, developer-first LLMOps platform designed to streamline prompt design, version management, instant delivery, collaboration, troubleshooting, observability and more.","arielweinberger, ItayElgazar, ranst91",20,2023-04-22,2023-06-21,0
SqueezeAILab/LLMCompiler,AI engineering,AIE framework,1652,1,0.06%,8,0.48%,124,[ICML 2024] LLMCompiler: An LLM Compiler for Parallel Function Calling,"kssteven418, hwchase17, eltociear",4,2023-12-06,2024-07-10,0
kserve/kserve,Infrastructure,Serving,4042,1,0.02%,19,0.47%,1149,Standardized Serverless ML Inference Platform on Kubernetes,"yuzisun, sivanantha321, andyi2it",100+,2019-03-27,2025-04-05,1110292
langroid/langroid,AI engineering,Agent,3208,1,0.03%,15,0.47%,310,Harness LLMs with Multi-Agent Programming,"pchalasani, Mohannadcse, nilspalumbo",27,2023-04-16,2025-04-06,1654
fern-api/fern,Applications,Coding,2927,1,0.03%,13,0.44%,190,Input OpenAPI. Output SDKs and Docs.,"dsinghvi, zachkirsch, amckinney",100+,2022-04-01,2025-04-04,0
iterative/datachain,AI engineering,Agent,2484,1,0.04%,11,0.44%,109,"ETL, Analytics, Versioning for Unstructured Data","mattseddon, skshetry, ilongin",26,2024-06-25,2025-04-04,0
allenai/dolma,Model development,Dataset engineering,1178,1,0.08%,5,0.42%,132,Data and tools for generating and inspecting OLMo pre-training data. ,"soldni, chris-ha458, undfined",27,2023-06-20,2025-04-04,0
THUDM/AgentTuning,AI engineering,Agent,1414,1,0.07%,6,0.42%,98,AgentTuning: Enabling Generalized Agent Abilities for LLMs,"lr-tsinghua11, Sengxian, Btlmd",8,2023-10-18,2023-10-28,0
lucidrains/x-transformers,Model repo,,5188,1,0.02%,22,0.42%,447,A concise but complete full-attention transformer with a set of promising experimental features from various papers,"lucidrains, gurvindersingh, jbcdnr",26,2020-10-24,2025-01-23,0
turboderp/exllamav2,Model development,Inference optimization,4095,1,0.02%,17,0.42%,306,A fast inference library for running LLMs locally on modern consumer-class GPUs,"turboderp, SinanAkkoyun, awtrisk",50,2023-08-30,2025-03-10,306149
explosion/spacy-llm,AI engineering,Prompt engineering,1223,1,0.08%,5,0.41%,94,🦙 Integrating LLMs into structured NLP pipelines,"rmitsch, svlandeg, ljvmiranda921",17,2023-03-16,2025-01-08,6
getzep/zep,AI engineering,AIE framework,3222,1,0.03%,13,0.40%,459,Zep | The Memory Foundation For Your AI Stack,"danielchalef, paul-paliychuk, rsharath",13,2023-04-29,2025-04-02,0
openai/improved-diffusion,Model repo,Multimodal,3501,1,0.03%,14,0.40%,506,Release for Improved Denoising Diffusion Probabilistic Models,"unixpickle, NoahSchiro",2,2021-02-08,2024-03-13,0
huggingface/optimum,Model development,Modeling & training,2833,1,0.04%,11,0.39%,518,"🚀 Accelerate inference and training of 🤗 Transformers, Diffusers, TIMM and Sentence Transformers with easy to use hardware optimization tools","fxmarty, echarlaix, JingyaHuang",100+,2021-07-20,2025-03-07,0
irgolic/AutoPR,Applications,Coding,1312,1,0.08%,5,0.38%,83,Run AI-powered workflows over your codebase,"irgolic, tjazerzen, PireIre",11,2023-03-18,2024-06-23,0
yangjianxin1/Firefly,Model development,Modeling & training,6317,1,0.02%,24,0.38%,572,Firefly: 大模型训练工具，支持训练Qwen2.5、Qwen2、Yi1.5、Phi-3、Llama3、Gemma、MiniCPM、Yi、Deepseek、Orion、Xverse、Mixtral-8x7B、Zephyr、Mistral、Baichuan2、Llma2、Llama、Qwen、Baichuan、ChatGLM2、InternLM、Ziya2、Vicuna、Bloom等大模型,"yangjianxin1, LDLINGLINGLING",2,2023-04-02,2024-10-24,0
MiuLab/Taiwan-LLM,Model repo,Multilingual,1329,1,0.08%,5,0.38%,110,Traditional Mandarin LLMs for Taiwan,"adamlin120, yvchen, WangRongsheng",10,2023-08-10,2024-08-18,0
OpenNMT/CTranslate2,Model development,Inference optimization,3724,1,0.03%,14,0.38%,346,Fast inference engine for Transformer models,"guillaumekln, minhthuc2502, vince62s",50,2019-09-23,2025-02-25,0
lonePatient/awesome-pretrained-chinese-nlp-models,Lists,,5204,1,0.02%,19,0.37%,491,Awesome Pretrained Chinese NLP Models，高质量中文预训练模型&大模型&多模态模型&大语言模型集合,"lonePatient, sliderSun, yangapku",6,2019-05-25,2023-12-27,0
opendilab/awesome-RLHF,Lists,,3864,1,0.03%,14,0.36%,237,A curated list of reinforcement learning with human feedback resources (continually updated),"ruoyuGao, PaParaZz1, Cloud-Pku",29,2023-02-13,2025-02-19,0
openai/prm800k,Model development,Dataset engineering,1965,1,0.05%,7,0.36%,116,"800,000 step-level correctness labels on LLM solutions to MATH problems",Huntrr,1,2023-04-13,1000-01-01,0
google/oss-fuzz-gen,Applications,Coding,1164,1,0.09%,4,0.34%,169,LLM powered fuzzing via OSS-Fuzz.,"DavidKorczynski, DonggeLiu, arthurscchan",22,2024-01-25,2025-04-03,0
sobelio/llm-chain,AI engineering,AIE framework,1459,1,0.07%,5,0.34%,137,`llm-chain` is a powerful rust crate for building chains in large language models allowing you to summarise text and complete complex tasks,"williamhogman, Juzov, danbev",39,2023-03-24,2024-10-31,0
google-research/tapas,Model repo,Multimodal,1169,1,0.09%,4,0.34%,220,End-to-end neural table-text understanding models.,"eisenjulian, SyrineKrichene, ChayanBansal",8,2020-03-31,2021-08-09,0
netease-youdao/QAnything,AI engineering,AIE framework,12982,1,0.01%,44,0.34%,1257,Question and Answer based on Anything.,"xixihahaliu, successren, vsou",21,2024-01-03,2025-03-12,0
jncraton/languagemodels,Applications,Bots,1189,1,0.08%,4,0.34%,81,Explore large language models in 512MB of RAM,"jncraton, munoztd0, eltociear",3,2023-05-07,2023-06-20,0
saharNooby/rwkv.cpp,Model development,Inference optimization,1495,1,0.07%,5,0.33%,102,INT4/INT5/INT8 and FP16 inference on CPU for RWKV language model,"ggerganov, saharNooby, MollySophia",91,2023-03-30,2025-03-23,3739
context-labs/autodoc,Applications,Coding,2105,1,0.05%,7,0.33%,132,Experimental toolkit for auto-generating codebase documentation using LLMs,"samheutmaker, andrewhong5297, 0xturboblitz",9,2023-03-22,2024-07-29,0
snakers4/silero-models,Model repo,Multimodal,5212,1,0.02%,17,0.33%,334,"Silero Models: pre-trained speech-to-text, text-to-speech and text-enhancement models made embarrassingly simple","snakers4, Islanna, evrrn",13,2020-09-11,2023-10-18,0
InternLM/InternLM-XComposer,Model repo,Multimodal,2806,1,0.04%,9,0.32%,171,InternLM-XComposer2.5-OmniLive: A Comprehensive Multimodal System for Long-term Streaming Video and Audio Interactions,"myownskyW7, panzhang0212, xiaoachen98",15,2023-09-26,2024-04-23,0
knuckleswtf/scribe,Applications,Coding,1961,1,0.05%,6,0.31%,335,Generate API documentation for humans from your Laravel codebase.✍,"shalvah, mpociot, james2doyle",100+,2020-05-02,2025-03-11,0
Josh-XT/AGiXT,AI engineering,Agent,2962,1,0.03%,9,0.30%,396,"AGiXT is a dynamic AI Agent Automation Platform that seamlessly orchestrates instruction management and complex task execution across diverse AI providers. Combining adaptive memory, smart features, and a versatile plugin system, AGiXT delivers efficient and comprehensive AI solutions.","Josh-XT, JamesonRGrieve, birdup000",39,2023-04-17,2025-04-05,0
Vaibhavs10/insanely-fast-whisper,Model repo,,8277,1,0.01%,25,0.30%,593,,"Vaibhavs10, patrick91, felixcarmona",20,2023-10-10,2023-11-12,0
huggingface/text-generation-inference,Infrastructure,Serving,9971,1,0.01%,30,0.30%,1177,Large Language Model Text Generation Inference,"OlivierDehaene, Narsil, danieldk",100+,2022-10-08,2025-04-03,0
huggingface/autotrain-advanced,Model development,Modeling & training,4348,1,0.02%,13,0.30%,560,🤗 AutoTrain Advanced,"abhishekkrthakur, SBrandeis, liveaverage",22,2020-12-15,2024-12-09,0
google-research/scenic,Model development,Modeling & training,3493,1,0.03%,10,0.29%,453,Scenic: A Jax Library for Computer Vision Research and Beyond,"MostafaDehghani, anuragarnab, mjlm",78,2021-07-12,2025-03-29,0
threestudio-project/threestudio,Model repo,Multimodal,6686,1,0.01%,19,0.28%,517,A unified framework for 3D content generation.,"bennyguo, thuliu-yt16, DSaurus",31,2023-04-06,2024-12-16,0
wzpan/wukong-robot,AI engineering,AI interface,6764,1,0.01%,19,0.28%,1380,🤖 wukong-robot 是一个简单、灵活、优雅的中文语音对话机器人/智能音箱项目，支持ChatGPT多轮对话能力，还可能是首个支持脑机交互的开源智能音箱项目。,"wzpan, phoenixsfly, tonytan1701",19,2019-01-16,2024-10-25,0
ahmadbilaldev/langui,AI engineering,AI interface,2859,1,0.03%,8,0.28%,146,"UI for your AI. Open Source Tailwind components tailored for your GPT, generative AI, and LLM projects.","ahmadbilaldev, ahmadawais, wenyang0",4,2023-05-24,2023-10-26,0
THUDM/SwissArmyTransformer,Model repo,,1074,1,0.09%,3,0.28%,99,SwissArmyTransformer is a flexible and powerful library to develop your own Transformer variants.,"1049451037, dm-thu, Sleepychord",24,2021-10-06,2024-02-01,0
triton-inference-server/server,Infrastructure,Serving,9020,1,0.01%,25,0.28%,1555,The Triton Inference Server provides an optimized cloud and edge inferencing solution. ,"GuanLuo, tanmayv25, CoderHam",100+,2018-10-04,2025-04-04,205816
huggingface/alignment-handbook,Tutorials,,5111,1,0.02%,14,0.27%,438,Robust recipes to align language models with human and AI preferences,"lewtun, kashif, edbeeching",28,2023-08-25,2024-11-21,0
paralleldrive/sudolang-llm-support,Applications,Coding,1157,1,0.09%,3,0.26%,80,SudoLang LLM Support for VSCode,"ericelliott, bliuadobe, dwiyatci",5,2023-03-31,2025-01-17,0
joonspk-research/generative_agents,Applications,Bots,18786,1,0.01%,48,0.26%,2502,Generative Agents: Interactive Simulacra of Human Behavior,"joonspk-research, tripathiarpan20, eltociear",5,2023-07-23,2023-08-11,0
microsoft/PromptCraft-Robotics,AI engineering,Prompt engineering,1991,1,0.05%,5,0.25%,212,Community for applying LLMs to robotics and a robot simulator with ChatGPT integration,"yuan-alex, rogeriobonatti, saihv",3,2023-02-08,2023-03-17,847
ise-uiuc/magicoder,Model repo,,2011,1,0.05%,5,0.25%,165,[ICML'24] Magicoder: Empowering Code Generation with OSS-Instruct,"UniverseFly, natedingyifeng, zhewang2001",4,2023-11-10,2023-12-08,0
THUDM/CogVLM,Model repo,Multimodal,6459,1,0.02%,16,0.25%,428,a state-of-the-art-level open visual language model | 多模态预训练模型,"zRzRzRzRzRzRzR, 1049451037, wenyihong",18,2023-09-18,2024-05-29,0
opendilab/DI-star,Applications,Bots,1264,1,0.08%,3,0.24%,119,An artificial intelligence platform for the StarCraft II with large-scale distributed training and grand-master agents.,"upia99, lkwargs, ain-soph",7,2021-07-04,2025-03-13,0
NExT-GPT/NExT-GPT,Model repo,Multimodal,3477,1,0.03%,8,0.23%,348,Code and models for NExT-GPT: Any-to-Any Multimodal Large Language Model,"ChocoWu, scofield7419, NExT-GPT",4,2023-08-30,2023-09-18,0
eugeneyan/open-llms,Lists,,11875,1,0.01%,27,0.23%,830,📋 A list of open LLMs available for commercial use.,"eugeneyan, ozppupbg, Muhtasham",32,2023-05-05,2025-02-13,0
neuralmagic/deepsparse,Infrastructure,Serving,3126,1,0.03%,7,0.22%,182,Sparsity-aware deep learning inference runtime for CPUs,"mgoin, bfineran, dbogunowicz",39,2020-12-14,2024-07-19,1634
guoyww/AnimateDiff,Model repo,,11257,1,0.01%,25,0.22%,918,Official implementation of AnimateDiff.,"yuwei849, anyirao, limbo0000",6,2023-06-17,2023-07-19,0
PaddlePaddle/PaddleSpeech,Model development,Modeling & training,11744,1,0.01%,26,0.22%,1900,"Easy-to-use Speech Toolkit including Self-Supervised Learning model, SOTA/Streaming ASR with punctuation, Streaming TTS with text frontend, Speaker Verification System, End-to-End Speech Translation and Keyword Spotting. Won NAACL2022 Best Demo Award.","zh794390558, yt605155624, Jackwaterveg",100+,2017-11-14,2025-04-01,0
aisingapore/TagUI,Applications,Workflow automation,5886,1,0.02%,13,0.22%,610,Free RPA tool by AI Singapore,"kensoh, siowyisheng, ruthtxh",18,2016-12-06,2024-09-06,79032
PhoebusSi/Alpaca-CoT,Model development,Modeling & training,2724,1,0.04%,6,0.22%,252,"We unified the interfaces of instruction-tuning data (e.g., CoT data), multiple LLMs and parameter-efficient methods (e.g., lora, p-tuning) together for easy use. We welcome open-source enthusiasts to initiate any meaningful PR on this repo and integrate as many LLM related technologies as possible. 我们打造了方便研究人员上手和使用大模型等微调平台，我们欢迎开源爱好者发起任何有意义的pr！","PhoebusSi, dkqkxx, re-burn",29,2023-03-24,2023-12-12,0
QuivrHQ/MegaParse,AI engineering,Dataset engineering,5929,1,0.02%,13,0.22%,296,"File Parser optimised for LLM Ingestion with no loss 🧠 Parse PDFs, Docx, PPTx in a format that is ideal for LLMs. ","StanGirard, chloedia, AmineDiro",6,2024-05-29,2025-02-21,0
bigscience-workshop/promptsource,AI engineering,Prompt engineering,2812,1,0.04%,6,0.21%,364,"Toolkit for creating, sharing and using natural language prompts.","VictorSanh, stephenbach, craffel",62,2021-05-19,2023-10-23,0
di-sukharev/opencommit,Applications,Coding,6559,1,0.02%,13,0.20%,350,GPT wrapper for git — generate commit messages with an LLM in 1 sec — works best with Claude 3.5 — supports local models too,"di-sukharev, matscube, JMN09",79,2023-03-06,2025-03-17,0
aiwaves-cn/agents,AI engineering,Agent,5554,1,0.02%,11,0.20%,437,"An Open-source Framework for Data-centric, Self-evolving Autonomous Language Agents","jianghuyihei, callanwu, MikeDean2367",24,2023-07-18,2024-09-26,0
mayooear/gpt4-pdf-chatbot-langchain,Applications,Info aggregation,15336,1,0.01%,30,0.20%,3050,AI PDF chatbot agent built with LangChain & LangGraph ,"mayooear, jacoblee93, ankri",3,2023-03-17,2025-02-20,0
mosaicml/llm-foundry,Model development,Modeling & training,4199,1,0.02%,8,0.19%,558,LLM training code for Databricks foundation models,"dakinggg, vchiley, irenedea",95,2023-04-28,2025-04-02,0
OpenGVLab/InternImage,Model repo,Multimodal,2631,1,0.04%,5,0.19%,244,[CVPR 2023 Highlight] InternImage: Exploring Large-Scale Vision Foundation Models with Deformable Convolutions,"czczup, Zeqiang-Lai, YeShenglong1",14,2022-11-10,2025-01-06,7069
ashawkey/stable-dreamfusion,Model repo,Multimodal,8568,1,0.01%,16,0.19%,749,Text-to-3D & Image-to-3D & Mesh Exportation with NeRF + Diffusion.,"ashawkey, claforte, abesmon",17,2022-10-06,2023-05-17,0
lavague-ai/LaVague,Applications,Workflow automation,6000,1,0.02%,11,0.18%,544,Large Action Model framework to develop AI Web Agents,"lyie28, JoFrost, mbrunel",26,2024-02-26,2025-01-21,0
griptape-ai/griptape,AI engineering,AIE framework,2241,1,0.04%,4,0.18%,189,"Modular Python framework for AI agents and workflows with chain-of-thought reasoning, tools, and memory. ","collindutter, vasinov, andrewfrench",33,2023-01-14,2025-01-03,0
AGI-Edgerunners/LLM-Adapters,Model development,Modeling & training,1145,1,0.09%,2,0.17%,113,"Code for our EMNLP 2023 Paper: ""LLM-Adapters: An Adapter Family for Parameter-Efficient Fine-Tuning of Large Language Models""","HZQ950419, demoleiwang, LYH-YF",5,2023-03-29,2024-03-10,0
microsoft/FLAML,AI engineering,AIE framework,4096,1,0.02%,7,0.17%,528,A fast library for AutoML and tuning. Join our Discord: https://discord.gg/Cppx2vSPVP.,"sonichi, skzhang1, liususan091219",90,2020-08-20,2025-03-14,0
eureka-research/Eureka,Model development,Modeling & training,2939,1,0.03%,5,0.17%,267,"Official Repository for ""Eureka: Human-Level Reward Design via Coding Large Language Models"" (ICLR 2024)",JasonMa2016,1,2023-09-25,1000-01-01,0
google-deepmind/acme,Model development,Modeling & training,3630,1,0.03%,6,0.17%,459,A library of reinforcement learning components and agents,"bshahr, sinopalnikov, qstanczyk",68,2020-05-01,2022-01-08,0
zilliztech/GPTCache,Infrastructure,Serving,7495,1,0.01%,12,0.16%,531,Semantic cache for LLMs. Fully integrated with LangChain and llama_index. ,"SimFG, cxie, jaelgu",41,2023-03-24,2024-09-03,289
gptscript-ai/gptscript,Applications,Coding,3206,1,0.03%,5,0.16%,294,Build AI assistants that interact with your systems,"ibuildthecloud, thedadams, g-linville",30,2024-01-29,2024-02-13,3584
mlfoundations/open_flamingo,Model development,Modeling & training,3876,1,0.03%,6,0.15%,301,An open-source framework for training large multimodal models.,"anas-awadalla, jpgard, i-gao",19,2022-10-20,2023-12-02,0
HumanAIGC/OutfitAnyone,Applications,Image production,5824,1,0.02%,9,0.15%,439,Outfit Anyone: Ultra-high quality virtual try-on for Any Clothing and Any Person,,0,2023-12-12,1000-01-01,0
microsoft/lmops,AI engineering,AIE framework,3916,1,0.03%,6,0.15%,303,General technology for enabling AI capabilities w/ LLMs and MLLMs,"donglixp, t1101675, buaahsh",23,2022-12-13,2025-01-11,0
activeloopai/deeplake,Infrastructure,VectorDB,8510,1,0.01%,13,0.15%,656,"Database for AI. Store Vectors, Images, Texts, Videos, etc. Use with LLMs/LangChain. Store, query, version, & visualize any AI data. Stream data in real-time to PyTorch/TensorFlow. https://activeloop.ai","FayazRahman, AbhinavTuli, davidbuniat",100+,2019-08-09,2025-03-29,0
ShishirPatil/gorilla,Applications,Coding,11950,1,0.01%,18,0.15%,1077,Gorilla: Training and Evaluating LLMs for Function Calls (Tool Calls),"HuanzhiMao, ShishirPatil, Fanjia-Yan",96,2023-05-19,2025-04-03,0
PanQiWei/AutoGPTQ,Model development,Inference optimization,4787,1,0.02%,7,0.15%,512,"An easy-to-use LLMs quantization package with user-friendly apis, based on GPTQ algorithm.","PanQiWei, qwopqwop200, fxmarty",49,2023-04-13,2024-03-28,859712
PaddlePaddle/PaddleNLP,Model development,Modeling & training,12485,1,0.01%,18,0.14%,3008,Easy-to-use and powerful LLM and SLM library with awesome model zoo.,"ZHUI, wawltor, sijunhe",100+,2021-02-05,2025-01-06,0
TheLastBen/fast-stable-diffusion,Applications,Image production,7695,1,0.01%,11,0.14%,1331,fast-stable-diffusion + DreamBooth,"TheLastBen, Maw-Fox, daswer123",19,2022-09-21,2023-04-13,0
neuralmagic/sparseml,Model development,Inference optimization,2121,1,0.05%,3,0.14%,153,"Libraries for applying sparsification recipes to neural networks with a few lines of code, enabling faster and smaller models","bfineran, markurtz, rahul-tuli",41,2020-12-11,2024-07-03,260
google-deepmind/code_contests,AI engineering,Dataset engineering,2133,1,0.05%,3,0.14%,214,,felixgimeno,1,2022-01-31,1000-01-01,0
openai/glide-text2im,Model repo,Multimodal,3606,1,0.03%,5,0.14%,507,GLIDE: a diffusion-based text-conditional image synthesis model,"unixpickle, prafullasd, lochiego",4,2021-12-10,2022-03-21,0
mosaicml/composer,Model development,Modeling & training,5324,1,0.02%,7,0.13%,437,Supercharge Your Model Training,"mvpatel2000, ravi-mosaicml, dakinggg",100+,2021-10-12,2025-04-06,0
memochou1993/gpt-ai-assistant,Applications,Bots,7620,1,0.01%,10,0.13%,9793,OpenAI + LINE + Vercel = GPT AI Assistant,"memochou1993, Jakevin, cdcd72",6,2022-12-09,2024-07-09,0
pytorch-labs/gpt-fast,Model development,Inference optimization,5905,1,0.02%,7,0.12%,547,Simple and efficient pytorch-native transformer text generation in <1000 LOC of python.,"yanboliang, BoyuanFeng, Chillee",21,2023-10-17,2024-12-14,0
Nutlope/twitterbio,Applications,Writing,1737,1,0.06%,2,0.12%,483,Generate your Twitter bio with AI,"Nutlope, smaeda-ks, chuanyu0201",6,2023-01-16,2025-01-20,0
Moonvy/OpenPromptStudio,AI engineering,Prompt engineering,6195,1,0.02%,7,0.11%,735,🥣 AIGC 提示词可视化编辑器  | OPS | Open Prompt Studio,"yArna, sinlov, 0xLLLLH",8,2023-03-25,2023-04-24,0
run-llama/rags,Applications,Info aggregation,6445,1,0.02%,7,0.11%,661,"Build ChatGPT over your data, all with natural language","jerryjliu, alexfilothodoros, anoopshrma",5,2023-11-16,2023-12-05,0
stitionai/devika,Applications,Coding,19113,1,0.01%,20,0.10%,2515,"Devika is an Agentic AI Software Engineer that can understand high-level human instructions, break them down into steps, research relevant information, and write code to achieve the given objective. Devika aims to be a competitive open-source alternative to Devin by Cognition AI. [⚠️ DEVIKA DOES NOT HAVE AN OFFICIAL WEBSITE ⚠️]","mufeedvh, ARajgor, nalaso",46,2024-03-21,2024-09-19,0
apple/ml-stable-diffusion,Model repo,,17259,1,0.01%,18,0.10%,976,Stable Diffusion with Core ML on Apple Silicon,"atiorh, TobyRoseman, pcuenca",37,2022-11-16,2023-10-07,0
marqo-ai/marqo,Infrastructure,VectorDB,4814,1,0.02%,5,0.10%,202,Unified embedding generation and search engine. Also available on cloud - cloud.marqo.ai,"pandu-k, wanliAlex, papa99do",32,2022-08-01,2025-04-04,0
a16z-infra/companion-app,Applications,Bots,5824,1,0.02%,6,0.10%,968,AI companions with memory: a lightweight stack to create and host your own AI companions,"ykhli, timqian, jmoore994",11,2023-06-22,2023-09-19,0
h2oai/h2ogpt,AI engineering,AIE framework,11750,1,0.01%,12,0.10%,1295,"Private chat with local GPT with document, images, video, etc. 100% private, Apache 2.0. Supports oLLaMa, Mixtral, llama.cpp, and more. Demo: https://gpt.h2o.ai/ https://gpt-docs.h2o.ai/","pseudotensor, arnocandel, achraf-mer",70,2023-03-24,2025-03-17,448
openai/transformer-debugger,AI engineering,Evals,4073,1,0.02%,4,0.10%,244,,"henktillman, WuTheFWasThat, machina-source",12,2024-03-11,2024-06-04,0
YiVal/YiVal,AI engineering,Prompt engineering,2094,1,0.05%,2,0.10%,325,Your Automatic Prompt Engineering Assistant for GenAI Applications,"windoliver, crazycth, uni-zhuan",32,2023-07-15,2024-02-06,0
google-research/text-to-text-transfer-transformer,Model repo,,6315,1,0.02%,6,0.10%,765,"Code for the paper ""Exploring the Limits of Transfer Learning with a Unified Text-to-Text Transformer""","adarob, sharannarang, hwchung27",56,2019-10-17,2025-02-27,0
microsoft/BitNet,Model development,Inference optimization,12864,1,0.01%,12,0.09%,910,Official inference framework for 1-bit LLMs,"potassiummmm, younesbelkada, dawnmsg",12,2024-08-05,2024-12-20,0
CarperAI/trlx,Model development,Modeling & training,4615,1,0.02%,4,0.09%,475,A repo for distributed training of language models with Reinforcement Learning via Human Feedback (RLHF),"maxreciprocate, jon-tow, Dahoas",51,2022-10-03,2024-01-08,0
vercel/modelfusion,AI engineering,AIE framework,1245,1,0.08%,1,0.08%,88,The TypeScript library for building AI applications.,"lgrammel, JakeDetels, bearjaws",12,2023-05-25,1000-01-01,0
tensorflow/serving,Infrastructure,Serving,6260,1,0.02%,5,0.08%,2199,"A flexible, high-performance serving system for machine learning models","kirilg, netfs, vinuraja",100+,2016-01-26,2025-04-03,0
baaivision/Painter,Model repo,,2560,1,0.04%,2,0.08%,179,Painter & SegGPT Series: Vision Foundation Models from BAAI,"WXinlong, zhangxiaosong18, caoyue10",4,2022-12-05,2023-07-06,0
langchain-ai/opengpts,AI engineering,AIE framework,6614,1,0.02%,5,0.08%,885,,"nfcampos, bakar-io, mkorpela",27,2023-11-04,2025-02-21,0
Nutlope/restorePhotos,Applications,Image production,4054,1,0.02%,3,0.07%,646,Restoring old and blurry face photos with AI.,"Nutlope, ljwagerfield, Nick-h4ck3r",6,2023-01-08,2023-12-21,0
THUDM/ChatGLM3,Model repo,Multilingual,13677,1,0.01%,10,0.07%,1605,ChatGLM3 series: Open Bilingual Chat LLMs | 开源双语对话语言模型,"zRzRzRzRzRzRzR, duzx16, Btlmd",39,2023-10-26,2025-01-13,0
KnowledgeCanvas/knowledge,Applications,Info aggregation,1416,1,0.07%,1,0.07%,101,"Knowledge is a tool for saving, searching, accessing, exploring and chatting with all of your favorite websites, documents and files.","RobRoyce, jedbrooke",3,2021-07-10,2024-02-01,0
turboderp/exllama,Model development,Inference optimization,2848,1,0.04%,2,0.07%,220,A more memory-efficient rewrite of the HF transformers implementation of Llama for use with quantized weights.,"turboderp, QuarticCat, Kerushii",15,2023-05-04,2023-09-11,0
salesforce/CodeTF,Model development,Modeling & training,1474,1,0.07%,1,0.07%,100,CodeTF: One-stop Transformer Library for State-of-the-art Code LLM,"bdqnghi, Paul-B98, sfdc-ospo-bot",5,2023-05-02,2024-02-28,13
microsoft/JARVIS,AI engineering,Agent,24076,1,0.00%,16,0.07%,2007,"JARVIS, a system to connect LLMs with ML community. Paper: https://arxiv.org/pdf/2303.17580.pdf","tricktreat, siyuyuan, StillKeepTry",23,2023-03-30,2024-01-05,0
lucidrains/PaLM-rlhf-pytorch,Model repo,,7776,1,0.01%,5,0.06%,679,Implementation of RLHF (Reinforcement Learning with Human Feedback) on top of the PaLM architecture. Basically ChatGPT but with PaLM,"lucidrains, conceptofmind, ell-hol",5,2022-12-09,2024-12-24,0
google-deepmind/sonnet,Model development,Modeling & training,9836,1,0.01%,6,0.06%,1301,TensorFlow-based neural network library,"tomhennigan, malcolmreynolds, diegolascasas",57,2017-04-03,2025-02-14,0
smol-ai/developer,AI engineering,Agent,11919,1,0.01%,7,0.06%,1052,the first library to let you embed a developer agent in your own app!,"swyxio, thatliuser, jakubno",19,2023-05-13,2023-09-25,0
FlagAlpha/Llama2-Chinese,Model repo,Multilingual,14520,1,0.01%,8,0.06%,1299,Llama中文社区，Llama3在线体验和微调模型已开放，实时汇总最新Llama3学习资料，已将所有代码更新适配Llama3，构建最好的中文Llama大模型，完全开源可商用,"Rayrtfr, LlamaFamily, ZHangZHengEric",15,2023-07-19,2024-07-25,0
tensorflow/privacy,Model development,Modeling & training,1960,1,0.05%,1,0.05%,456,Library for training machine learning models with privacy for training data,"schien1729, galenmandrew, michaelreneer",56,2018-12-21,2025-03-26,128
flairNLP/flair,AI engineering,AIE framework,14129,1,0.01%,7,0.05%,2111,A very simple framework for state-of-the-art Natural Language Processing (NLP),"alanakbik, helpmefindaname, tabergma",100+,2018-06-11,2025-03-31,0
ymcui/Chinese-LLaMA-Alpaca,Model repo,Multilingual,18783,1,0.01%,9,0.05%,1890,中文LLaMA&Alpaca大语言模型+本地CPU/GPU训练部署 (Chinese LLaMA & Alpaca LLMs),"ymcui, airaria, iMountTai",10,2023-03-15,2023-07-18,0
linyiLYi/street-fighter-ai,Applications,Bots,6467,1,0.02%,3,0.05%,1400,This is an AI agent for Street Fighter II Champion Edition.,"linyiLYi, kianmeng, arch-fan",3,2023-03-27,2023-04-21,0
chiphuyen/lazynlp,Model development,Dataset engineering,2183,1,0.05%,1,0.05%,312,Library to scrape and clean web pages to create massive datasets.,"ss18, chiphuyen, cclauss",4,2019-02-27,2019-10-07,0
microsoft/BioGPT,Model repo,,4392,1,0.02%,2,0.05%,458,,"renqianluo, microsoftopensource, katielink",9,2022-08-15,2023-02-13,0
spdustin/ChatGPT-AutoExpert,AI engineering,Prompt engineering,6660,1,0.02%,3,0.05%,473,🚀🧠💬 Supercharged Custom Instructions for ChatGPT (non-coding) and ChatGPT Advanced Data Analysis (coding). ,"spdustin, michaelskyba, Sanyam-2026",6,2023-09-29,2024-01-17,0
bhaskatripathi/pdfGPT,Applications,Info aggregation,7099,1,0.01%,3,0.04%,847,PDF GPT allows you to chat with the contents of your PDF file by using GPT capabilities. The most effective open source solution to turn your pdf files in a chatbot!,"bhaskatripathi, deepankarm, Kalmaegi",11,2023-03-07,2023-09-04,0
PKU-YuanGroup/Open-Sora-Plan,Model repo,Multimodal,11941,1,0.01%,5,0.04%,1054,"This project aim to reproduce Sora (Open AI T2V model), we wish the open source community contribute to this project.","LinB203, qqingzheng, clownrat6",36,2024-02-20,2025-04-02,0
mozilla/TTS,Model development,Modeling & training,9760,1,0.01%,4,0.04%,1293,:robot: :speech_balloon: Deep learning for Text to Speech  (Discussion forum: https://discourse.mozilla.org/c/tts),"erogol, Edresson, lexkoro",42,2018-01-23,2021-02-18,7970
paperswithcode/galai,Model repo,,2713,1,0.04%,1,0.04%,277,Model API for GALACTICA,"rstojnic, AnthonyHartshorn, grenkoca",5,2022-11-15,2022-12-09,0
JushBJJ/Mr.-Ranedeer-AI-Tutor,Applications,Education,29478,1,0.00%,10,0.03%,3375,A GPT-4 AI Tutor Prompt for customizable personalized learning experiences.,"JushBJJ, angelkurten, Livshitz",12,2023-03-31,2023-11-09,1864
OpenGVLab/InternGPT,AI engineering,AI interface,3216,1,0.03%,1,0.03%,230,"InternGPT (iGPT) is an open source demo platform where you can easily showcase your AI models. Now it supports DragGAN, ChatGPT, ImageBind, multimodal chat like GPT-4, SAM, interactive image editing, etc. Try it at igpt.opengvlab.com (支持DragGAN、ChatGPT、ImageBind、SAM的在线Demo系统)","liu-zhy, whai362, Zeqiang-Lai",12,2023-05-08,2023-11-24,0
Luodian/Otter,Model repo,Multimodal,3246,1,0.03%,1,0.03%,212,"🦦 Otter, a multi-modal model based on OpenFlamingo (open-sourced version of DeepMind's Flamingo), trained on MIMIC-IT and showcasing improved instruction-following and in-context learning ability.","Luodian, ZhangYuanhan-AI, king159",12,2023-04-01,2023-11-19,0
tatsu-lab/stanford_alpaca,Model repo,,29909,1,0.00%,6,0.02%,4051,"Code and documentation to train Stanford's Alpaca models, and generate the data.","lxuechen, rtaori, YannDubs",5,2023-03-10,2023-04-16,0
Chanzhaoyu/chatgpt-web,Applications,Bots,31930,1,0.00%,6,0.02%,11214,用 Express 和  Vue3 搭建的 ChatGPT 演示网页,"Chanzhaoyu, PeterDaveHello, yi-ge",75,2023-02-09,2023-04-24,0
EssayKillerBrain/WriteGPT,Applications,Writing,5335,1,0.02%,1,0.02%,909,由图灵的猫开发，基于开源GPT2.0的初代创作型人工智能 | 可扩展、可进化,"Y1ran, trustyboy, TrellixVulnTeam",3,2020-09-29,2023-02-16,0
nat/openplayground,AI engineering,AI interface,6342,1,0.02%,1,0.02%,492,An LLM playground you can run on your laptop,"AlexanderLourenco, nat, max",15,2023-02-26,2023-05-18,0
anse-app/chatgpt-demo,Applications,Bots,8019,1,0.01%,1,0.01%,3758,Minimal web UI for ChatGPT. ,"ddiu8081, yzh990918, Robin021",38,2023-03-02,2023-11-20,0
THUDM/ChatGLM-6B,Model repo,Multilingual,41026,1,0.00%,5,0.01%,5228,ChatGLM-6B: An Open Bilingual Dialogue Language Model | 开源双语对话语言模型,"duzx16, rainatam, Xiao9905",46,2023-03-13,2024-06-27,0
xai-org/grok-1,Model repo,,50253,1,0.00%,4,0.01%,8361,Grok open release,"ibab, syzymon, mane",6,2024-03-17,2024-03-19,0
freedmand/semantra,Applications,Data organization,2592,1,0.04%,0,0.00%,153,Multi-tool for semantic search,"freedmand, yych42, kianmeng",5,2023-03-31,2024-08-15,0
fiatrete/OpenDAN-Personal-AI-OS,Applications,Workflow automation,1803,1,0.06%,0,0.00%,160,"OpenDAN is an open source Personal AI OS , which consolidates various AI modules in one place for your personal use.","waterflier, fiatrete, photosssa",15,2023-05-11,2024-04-23,0
Facico/Chinese-Vicuna,Model repo,Multilingual,4152,1,0.02%,0,0.00%,419,Chinese-Vicuna: A Chinese Instruction-following LLaMA-based Model —— 一个中文低资源的llama+lora方案，结构参考alpaca,"Facico, LZY-the-boys, Chuge0335",7,2023-03-23,2023-11-09,0
google-research/simclr,Model repo,,4233,1,0.02%,0,0.00%,638,SimCLRv2 - Big Self-Supervised Models are Strong Semi-Supervised Learners,"chentingpc, saxenasaurabh, williamFalcon",4,2020-03-10,2020-12-03,0
ztjhz/BetterChatGPT,Applications,Bots,8361,1,0.01%,0,0.00%,2784,An amazing UI for OpenAI's ChatGPT (Website + Windows + MacOS + Linux),"ztjhz, ayaka14732, tnga",43,2023-03-03,2024-05-14,91561
zou-group/textgrad,AI engineering,Prompt engineering,2393,0,0.00%,39,1.63%,198,TextGrad: Automatic ''Differentiation'' via Text -- using large language models to backpropagate textual gradients.,"vinid, mertyg, lupantech",17,2024-06-11,2025-03-26,0
Microsoft/genaiscript,AI engineering,Prompt engineering,2456,0,0.00%,35,1.43%,169,Automatable GenAI Scripting,"pelikhan, bzorn, mmoskal",29,2023-08-17,2025-04-05,742
KimMeen/Time-LLM,AI engineering,AIE framework,1908,0,0.00%,25,1.31%,331,"[ICLR 2024] Official implementation of "" 🦙 Time-LLM: Time Series Forecasting by Reprogramming Large Language Models""","KimMeen, qingsongedu, kwuking",7,2024-01-20,2024-04-23,0
HazyResearch/ThunderKittens,Model development,Inference optimization,2225,0,0.00%,29,1.30%,133,Tile primitives for speedy kernels,"benjaminfspector, Aaryan0404, simran-arora",15,2024-03-04,2025-04-03,0
karthink/gptel,Applications,Bots,2279,0,0.00%,28,1.23%,215,A simple LLM client for Emacs,"karthink, pabl0, psionic-k",76,2023-03-06,2025-04-02,0
confident-ai/deepeval,AI engineering,Evals,5846,0,0.00%,68,1.16%,502,The LLM Evaluation Framework,"penguine-ip, jwongster2, kritinv",100+,2023-08-10,2025-04-04,0
TransformerLensOrg/TransformerLens,AI engineering,Evals,2021,0,0.00%,21,1.04%,361,A library for mechanistic interpretability of GPT-style language models,"neelnanda-io, bryce13950, alan-cooney",96,2022-08-26,2025-02-25,0
lucidrains/vector-quantize-pytorch,Model development,Inference optimization,3105,0,0.00%,31,1.00%,251,"Vector (and Scalar) Quantization, in Pytorch","lucidrains, sekstini, MisterBourbaki",20,2020-06-09,2025-03-23,0
kha-white/manga-ocr,Applications,Info aggregation,2022,0,0.00%,19,0.94%,96,"Optical character recognition for Japanese text, with the main focus being Japanese manga","kha-white, aalhendi, AuroraWright",8,2022-01-15,2025-01-01,17
Link-AGI/AutoAgents,AI engineering,Agent,1347,0,0.00%,11,0.82%,159,[IJCAI 2024] Generate different roles for GPTs to form a collaborative entity for complex tasks.,"iCGY96, shiyemin, s1w3",10,2023-08-21,2024-04-16,0
dvlab-research/LISA,Model repo,Multimodal,2123,0,0.00%,17,0.80%,147,"Project Page for ""LISA: Reasoning Segmentation via Large Language Model""","X-Lai, tianzhuotao, yukang2017",10,2023-08-01,2023-10-30,0
parthsarthi03/raptor,AI engineering,AIE framework,1162,0,0.00%,9,0.77%,160,The official implementation of RAPTOR: Recursive Abstractive Processing for Tree-Organized Retrieval,"parthsarthi03, ExtReMLapin, LLLeoLi",4,2024-02-27,2024-09-03,0
kubeflow/training-operator,Model development,Modeling & training,1749,0,0.00%,13,0.74%,767,Distributed ML Training and Fine-Tuning on Kubernetes,"gaocegege, jlewi, tenzen-y",100+,2017-06-28,2025-03-30,0
aallam/openai-kotlin,AI engineering,AI interface,1652,0,0.00%,12,0.73%,210,OpenAI API client for Kotlin with multiplatform and coroutines capabilities.,"aallam, voqaldev, ahmedmirza994",40,2021-03-06,2025-02-02,0
openai/lm-human-preferences,Model repo,,1315,0,0.00%,9,0.68%,168,Code for the paper Fine-Tuning Language Models from Human Preferences,"WuTheFWasThat, leondz, karthik-rangarajan",3,2019-09-14,2023-04-14,0
huggingface/datatrove,Model development,Dataset engineering,2340,0,0.00%,16,0.68%,177,Freeing data processing from scripting madness by providing a set of platform-agnostic customizable pipeline processing blocks.,"guipenedo, alexchapeaux, hynky1999",41,2023-06-14,2025-03-04,0
THUDM/ImageReward,Model repo,Multimodal,1366,0,0.00%,9,0.66%,71,[NeurIPS 2023] ImageReward: Learning and Evaluating Human Preferences for Text-to-image Generation,"xujz18, Xiao9905, tongyx361",8,2023-04-01,2024-09-26,0
timescale/pgai,AI engineering,AIE framework,4630,0,0.00%,28,0.60%,242,"A suite of tools to develop RAG, semantic search, and other AI applications more easily with PostgreSQL","jgpruitt, JamesGuthrie, Askir",29,2024-05-16,2025-04-04,0
meta-llama/PurpleLlama,AI engineering,Evals,3003,0,0.00%,18,0.60%,504,Set of tools to assess and improve LLM security.,"SimonWan, dwjsong, cynikolai",29,2023-12-06,2024-04-18,0
mlcommons/inference,AI engineering,Evals,1348,0,0.00%,8,0.59%,544,Reference implementations of MLPerf™ inference benchmarks,"guschmue, arjunsuresh, psyhtest",100+,2018-09-13,2025-03-13,421
moj-analytical-services/splink,AI engineering,Dataset engineering,1540,0,0.00%,9,0.58%,171,"Fast, accurate and scalable probabilistic data linkage with support for multiple SQL backends","RobinL, ADBond, RossKen",69,2019-11-22,2025-03-26,0
fixie-ai/ultravox,Model repo,Multimodal,3797,0,0.00%,22,0.58%,280,A fast multimodal LLM for real-time voice,"farzadab, juberti, zqhuang211",14,2024-05-29,2025-02-14,0
ucbepic/docetl,Applications,Info aggregation,1735,0,0.00%,10,0.58%,165,A system for agentic LLM-powered data processing and ETL,"shreyashankar, orban, staru09",19,2024-07-09,2025-04-04,0
unslothai/hyperlearn,Model development,Modeling & training,2098,0,0.00%,12,0.57%,135,"2-2000x faster ML algos, 50% less memory usage, works on all hardware - new and old.","danielhanchen, shimmyshimmer, laykea",4,2018-08-27,2024-11-19,0
SHI-Labs/OneFormer,Model repo,,1585,0,0.00%,9,0.57%,135,[CVPR 2023] OneFormer: One Transformer to Rule Universal Image Segmentation,"praeclarumjj3, honghuis, alihassanijr",5,2022-11-02,2023-04-18,0
Vahe1994/AQLM,Model development,Inference optimization,1235,0,0.00%,7,0.57%,182,Official Pytorch repository for Extreme Compression of Large Language Models via Additive Quantization https://arxiv.org/pdf/2401.06118.pdf and PV-Tuning: Beyond Straight-Through Estimation for Extreme LLM Compression https://arxiv.org/abs/2405.14852,"BlackSamorez, justheuristic, Godofnothing",12,2024-01-12,2025-03-03,0
openai/Video-Pre-Training,Model repo,Multimodal,1436,0,0.00%,8,0.56%,146,Video PreTraining (VPT): Learning to Act by Watching Unlabeled Online Videos,"brandonhoughton, Miffyli, Matoi647",5,2022-06-22,2024-06-10,0
OpenMotionLab/MotionGPT,Model repo,Multimodal,1618,0,0.00%,9,0.56%,103,"[NeurIPS 2023] MotionGPT: Human Motion as a Foreign Language, a unified motion-language generation model using LLMs","billl-jiang, ChenFengYe, baitian752",7,2023-06-20,2024-01-05,0
zjunlp/EasyEdit,AI engineering,Prompt engineering,2171,0,0.00%,12,0.55%,266,[ACL 2024] An Easy-to-use Knowledge Editing Framework for LLMs.,"pengzju, XeeKee, tbozhong",46,2023-05-09,2025-04-03,0
google-deepmind/optax,Model development,Modeling & training,1851,0,0.00%,10,0.54%,228,Optax is a gradient processing and optimization library for JAX.,"fabianp, mtthss, vroulet",100+,2020-06-12,2025-04-04,0
netease-youdao/BCEmbedding,Model repo,,1700,0,0.00%,9,0.53%,115,Netease Youdao's open-source embedding and reranker models for RAG products.,"shenlei1020, tpoisonooo, Guochengjie",6,2024-01-02,2024-11-30,0
trypromptly/LLMStack,AI engineering,AIE framework,1911,0,0.00%,10,0.52%,276,"No-code multi-agent framework to build LLM Agents, workflows and applications with your data","ajhai, vegito22, Balu-Varanasi",8,2023-08-06,2024-12-04,0
MzeroMiko/VMamba,Model repo,Multimodal,2509,0,0.00%,13,0.52%,171,VMamba: Visual State Space Models，code is based on mamba,"MzeroMiko, sunsmarterjie, neverbiasu",4,2024-01-11,2024-10-28,4403
nlmatics/nlm-ingestor,Applications,Info aggregation,1209,0,0.00%,6,0.50%,184,This repo provides the server side code for llmsherpa API to connect. It includes parsers for various file formats.,"jamesvillarrubia, ansukla, kiran-nlmatics",13,2024-01-17,2025-03-28,0
openai/human-eval,AI engineering,Evals,2671,0,0.00%,13,0.49%,381,"Code for the paper ""Evaluating Large Language Models Trained on Code""","heewooj, mpokrass, qimingyuan",4,2021-07-06,2025-01-17,0
openai/grade-school-math,AI engineering,Evals,1241,0,0.00%,6,0.48%,171,,"vineetsk10, kcobbe",2,2021-10-20,2021-11-03,0
argilla-io/argilla,Model development,Dataset engineering,4423,0,0.00%,21,0.47%,420,Argilla is a collaboration tool for AI engineers and domain experts to build high-quality datasets,"frascuchon, alvarobartt, damianpumar",97,2021-04-28,2025-02-17,0
deepseek-ai/DeepSeek-VL,Model repo,Multimodal,3754,0,0.00%,17,0.45%,559,DeepSeek-VL: Towards Real-World Vision-Language Understanding,"Benjamin-eecs, RERV, Fodark",7,2024-03-07,2024-04-24,0
llm-attacks/llm-attacks,AI engineering,Evals,3820,0,0.00%,17,0.45%,517,Universal and Transferable Attacks on Aligned Language Models,"zifanw505, andyzoujm, justinwangx",3,2023-07-27,1000-01-01,0
openai/simple-evals,AI engineering,Evals,2494,0,0.00%,11,0.44%,228,,"etr2460, kzl-openai, jmcgraph-oai",9,2024-04-11,2025-03-20,0
floneum/floneum,AI engineering,AIE framework,1815,0,0.00%,8,0.44%,93,"Instant, controllable, local pre-trained AI models in Rust","ealmloff, newfla, KerfuffleV2",9,2023-05-24,2025-03-22,362
huggingface/safetensors,Infrastructure,Toolings,3204,0,0.00%,14,0.44%,238,"Simple, safe way to store and distribute tensors","Narsil, mishig25, thomasw21",58,2022-09-22,2025-03-18,0
truera/trulens,AI engineering,Evals,2409,0,0.00%,10,0.42%,211,Evaluation and Tracking for LLM Experiments,"joshreini1, piotrm0, rshih32",56,2020-11-02,2025-04-03,0
xlang-ai/OpenAgents,Applications,Workflow automation,4220,0,0.00%,17,0.40%,470,[COLM 2024] OpenAgents: An Open Platform for Language Agents in the Wild,"taoyds, Timothyxxx, BlankCheng",16,2023-08-08,2024-05-28,0
andrewnguonly/Lumos,Applications,Workflow automation,1498,0,0.00%,6,0.40%,106,"A RAG LLM co-pilot for browsing the web, powered by local LLMs","andrewnguonly, billykern, eltociear",4,2023-11-13,2025-01-26,346
webdataset/webdataset,Infrastructure,Data management,2528,0,0.00%,10,0.40%,205,"A high-performance Python-based I/O system for large (and small) deep learning problems, with strong support for PyTorch.","tmbdev, tmbnv, jacobbieker",45,2019-08-07,2025-02-07,0
mosaicml/streaming,Infrastructure,Data management,1266,0,0.00%,5,0.39%,159,A Data Streaming Library for Efficient Neural Network Training,"karan6181, knighton, snarayan21",58,2022-06-09,2025-04-04,15
YangLing0818/RPG-DiffusionMaster,AI engineering,AIE framework,1785,0,0.00%,7,0.39%,102,"[ICML 2024] Mastering Text-to-Image Diffusion: Recaptioning, Planning, and Generating with Multimodal LLMs (RPG)","YangLing0818, ZhaochenYu0201, Cominclip",3,2024-01-22,1000-01-01,0
BAAI-Agents/Cradle,Applications,Workflow automation,2056,0,0.00%,8,0.39%,184,"The Cradle framework is a first attempt at General Computer Control (GCC). Cradle supports agents to ace any computer task by enabling strong reasoning abilities, self-improvment, and skill curation, in a standardized general environment with minimal requirements.","DVampire, tellarin, XinrunXu",7,2024-03-03,2024-11-07,0
eylonmiz/react-agent,Applications,Coding,1570,0,0.00%,6,0.38%,142,The open-source React.js Autonomous LLM Agent,"eylonmiz, adriandlam, areibman",6,2023-05-11,2023-11-14,0
argmaxinc/WhisperKit,Infrastructure,Serving,4457,0,0.00%,17,0.38%,376,On-device Speech Recognition for Apple Silicon,"ZachNagengast, jkrukowski, finnvoor",30,2024-01-26,2025-04-03,0
noahshinn/reflexion,AI engineering,Agent,2648,0,0.00%,10,0.38%,254,[NeurIPS 2023] Reflexion: Language Agents with Verbal Reinforcement Learning,"cassanof, noahshinn, noahshinn024",6,2023-03-22,2023-08-04,0
VainF/Torch-Pruning,Model development,Inference optimization,2957,0,0.00%,11,0.37%,348,[CVPR 2023] DepGraph: Towards Any Structural Pruning,"VainF, Hyunseok-Kim0, HollyLee2000",17,2019-12-15,2025-03-24,252
ParisNeo/lollms-webui,AI engineering,AI interface,4600,0,0.00%,17,0.37%,566,Lord of Large Language and Multi modal Systems Web User Interface,"ParisNeo, andzejsp, blasphemousjohn",39,2023-04-06,2025-03-12,5438
huggingface/evaluate,AI engineering,Evals,2173,0,0.00%,8,0.37%,272,🤗 Evaluate: A library for easily evaluating machine learning models and datasets.,"lhoestq, albertvillanova, lvwerra",100+,2022-03-30,2025-01-10,0
promptslab/Promptify,AI engineering,Prompt engineering,3461,0,0.00%,12,0.35%,268,"Prompt Engineering | Prompt Versioning | Use GPT or other prompt based models to get structured output. Join our discord for Prompt-Engineering, LLMs and other latest research","monk1337, kamalkraj, eren23",14,2022-12-12,2025-02-12,0
Maartengr/BERTopic,Model development,Modeling & training,6635,0,0.00%,23,0.35%,803,Leveraging BERT and c-TF-IDF to create easily interpretable topics. ,"MaartenGr, afuetterer, freddyheppell",83,2020-09-22,2025-03-28,0
PKU-Alignment/safe-rlhf,Model development,Modeling & training,1443,0,0.00%,5,0.35%,119,Safe RLHF: Constrained Value Alignment via Safe Reinforcement Learning from Human Feedback,"XuehaiPan, rockmagma02, calico-1226",4,2023-05-15,2024-06-13,0
salesforce/CodeT5,Model repo,,2950,0,0.00%,10,0.34%,449,Home of CodeT5: Open Code LLMs for Code Understanding and Generation,"yuewang-cuhk, svc-scm",2,2021-08-16,1000-01-01,0
Cloud-CV/EvalAI,AI engineering,Evals,1828,0,0.00%,6,0.33%,852,:cloud: :rocket: :bar_chart: :chart_with_upwards_trend: Evaluating state of the art in AI,"RishabhJain2018, deshraj, Ram81",100+,2016-10-21,2025-04-02,0
hao-ai-lab/LookaheadDecoding,Model development,Inference optimization,1229,0,0.00%,4,0.33%,74,[ICML 2024] Break the Sequential Dependency of LLM Inference Using Lookahead Decoding,"Viol2000, jiqing-feng, zhisbug",5,2023-11-21,2024-02-14,0
kubeflow/katib,Model development,Modeling & training,1560,0,0.00%,5,0.32%,467,Automated Machine Learning on Kubernetes,"andreyvelich, gaocegege, hougangliu",100+,2018-04-03,2025-03-29,135
thunlp/UltraChat,Model development,Dataset engineering,2531,0,0.00%,8,0.32%,126,"Large-scale, Informative, and Diverse Multi-round Chat Data (and Models)","ningding97, yulinchen99",3,2023-04-03,2023-04-24,0
lxtGH/OMG-Seg,Model repo,,1267,0,0.00%,4,0.32%,47,OMG-LLaVA and OMG-Seg codebase [CVPR-24 and NeurIPS-24],"lxtGH, zhang-tao-whu, HarborYuan",5,2024-01-05,2024-07-04,0
OpenAutoCoder/Agentless,Applications,Coding,1605,0,0.00%,5,0.31%,170,Agentless🐱:  an agentless approach to automatically solve software development problems,"brutalsavage, dengyinlin, sorendunn",4,2024-06-30,2024-12-08,123
mit-han-lab/llm-awq,Model development,Inference optimization,2900,0,0.00%,9,0.31%,242,[MLSys 2024 Best Paper Award] AWQ: Activation-aware Weight Quantization for LLM Compression and Acceleration,"ys-2020, kentang-mit, tonylins",11,2023-06-01,2025-03-25,0
thunlp/OpenPrompt,AI engineering,Prompt engineering,4532,0,0.00%,14,0.31%,462,An Open-Source Framework for Prompt-Learning.,"ShengdingHu, Achazwl, yulinchen99",17,2021-09-30,2023-05-06,0
zjunlp/KnowLM,Model repo,Multilingual,1302,0,0.00%,4,0.31%,132,An Open-sourced Knowledgable Large Language Model Framework.,"MikeDean2367, zxlzr, 12lxr",26,2023-04-01,2024-12-28,0
databricks/megablocks,Model development,Modeling & training,1316,0,0.00%,4,0.30%,189,,"tgale96, mvpatel2000, sashaDoubov",24,2023-01-26,2025-03-12,0
deanxv/coze-discord-proxy,AI engineering,AI interface,3691,0,0.00%,11,0.30%,1021,代理Discord对话Coze-Bot，实现以API形式请求GPT4模型，提供对话、文生图、图生文、知识库检索等功能。,"deanxv, k8scat",2,2024-01-26,2024-02-06,743
evo-design/evo,Model repo,Multimodal,1357,0,0.00%,4,0.29%,164,Biological foundation modeling from molecular to genome scale,"brianhie, Zymrael, athms",9,2024-02-17,2024-12-18,0
apple/ml-4m,Model repo,,1708,0,0.00%,5,0.29%,103,4M: Massively Multimodal Masked Modeling,"roman-bachmann, dmizr, ofkar",7,2024-04-08,2024-07-05,0
PygmalionAI/aphrodite-engine,Infrastructure,Serving,1371,0,0.00%,4,0.29%,150,Large-scale LLM inference engine,"AlpinDale, 50h100a, StefanGliga",39,2023-06-23,2025-03-28,401
langchain-ai/langserve,Infrastructure,Serving,2059,0,0.00%,6,0.29%,237,LangServe 🦜️🏓,"eyurtsev, nfcampos, dqbd",25,2023-09-29,2024-12-27,107
IST-DASLab/gptq,Model development,Inference optimization,2073,0,0.00%,6,0.29%,165,"Code for the ICLR 2023 paper ""GPTQ: Accurate Post-training Quantization of Generative Pretrained Transformers"".","efrantar, sashkboos, brian-fb",5,2022-10-19,2023-07-10,0
eumemic/ai-legion,AI engineering,Agent,1397,0,0.00%,4,0.29%,164,An LLM-powered autonomous agent platform,"eumemic, MalikMAlna, TheBitmonkey",6,2023-04-10,2023-04-16,0
time-series-foundation-models/lag-llama,Model repo,,1410,0,0.00%,4,0.28%,169,Lag-Llama: Towards Foundation Models for Probabilistic Time Series Forecasting,"ashok-arjun, kashif, araghukas",5,2024-02-07,2024-09-08,0
evalplus/evalplus,AI engineering,Evals,1425,0,0.00%,4,0.28%,145,Rigourous evaluation of LLM-synthesized code - NeurIPS 2023 & COLM 2024,"ganler, Kristoff-starling, brutalsavage",20,2023-04-15,2024-12-26,2240
Farama-Foundation/chatarena,AI engineering,Agent,1442,0,0.00%,4,0.28%,140,ChatArena (or Chat Arena) is a Multi-Agent Language Game Environments for LLMs. The goal is to develop communication and collaboration capabilities of AIs.,"yuxiang-wu, elliottower, ZhengyaoJiang",15,2023-03-06,2024-05-27,0
Nutlope/llamatutor,Applications,Education,1830,0,0.00%,5,0.27%,291,An AI personal tutor built with Llama 3.1,"Nutlope, samselikoff, Mustafa-Esoofally",4,2024-07-19,2024-07-21,0
KhoomeiK/LlamaGym,Model development,Modeling & training,1100,0,0.00%,3,0.27%,51,Fine-tune LLM agents with online reinforcement learning,KhoomeiK,1,2024-03-01,2024-03-10,0
NVIDIA/TensorRT,Model development,Inference optimization,11422,0,0.00%,31,0.27%,2178,NVIDIA® TensorRT™ is an SDK for high-performance deep learning inference on NVIDIA GPUs. This repository contains the open source components of TensorRT.,"rajeevsrao, kevinch-nv, shuyuelan",77,2019-05-02,2025-03-11,0
3DTopia/LGM,Model repo,Multimodal,1856,0,0.00%,5,0.27%,126,[ECCV 2024 Oral] LGM: Large Multi-View Gaussian Model for High-Resolution 3D Content Creation.,"ashawkey, brentyi, laz-kaedim",3,2024-02-06,2024-02-29,0
leap-ai/headshots-starter,Applications,Image production,4184,0,0.00%,11,0.26%,752,,"Marfuen, claudfuen, alexschachne",16,2023-09-05,2025-03-31,0
facebookresearch/multimodal,Model development,Modeling & training,1565,0,0.00%,4,0.26%,152,TorchMultimodal is a PyTorch library for training state-of-the-art multimodal multi-task models at scale.,"langong347, ebsmothers, ankitade",34,2022-01-27,1000-01-01,0
ELLA-Diffusion/ELLA,Model repo,Multimodal,1179,0,0.00%,3,0.25%,61,ELLA: Equip Diffusion Models with LLM for Enhanced Semantic Alignment,"budui, fangyixiao18, melohux",4,2024-03-07,2024-07-17,0
shibing624/pycorrector,Applications,Writing,5898,0,0.00%,15,0.25%,1129,pycorrector is a toolkit for text error correction. 文本纠错，实现了Kenlm，T5，MacBERT，ChatGLM3，Qwen2.5等模型应用在纠错场景，开箱即用。,"shibing624, okcd00, cclauss",19,2018-03-01,2024-10-28,1857
epfLLM/meditron,Model repo,,1999,0,0.00%,5,0.25%,191,Meditron is a suite of open-source medical Large Language Models (LLMs).,"eric11eca, AGBonnet, jpcorb20",11,2023-11-23,2024-04-10,0
google-research/deduplicate-text-datasets,AI engineering,Dataset engineering,1200,0,0.00%,3,0.25%,123,,"carlini, daphnei, alistairewj",5,2021-07-16,2024-05-21,0
OpenGVLab/Ask-Anything,Model repo,Multimodal,3209,0,0.00%,8,0.25%,261,"[CVPR2024 Highlight][VideoChatGPT] ChatGPT with video understanding! And many more supported LMs such as miniGPT4, StableLM, and MOSS.","yinanhe, Andy1621, shepnerd",11,2023-04-19,2024-04-05,0
microsoft/aici,AI engineering,Prompt engineering,2013,0,0.00%,5,0.25%,83,AICI: Prompts as (Wasm) Programs,"mmoskal, emrekiciman, microsoftopensource",10,2023-09-26,2024-11-10,61
nlmatics/llmsherpa,Applications,Info aggregation,1623,0,0.00%,4,0.25%,158,Developer APIs to Accelerate LLM Projects,"ansukla, AaryanTR, IoanaDragan",10,2023-10-12,2024-10-18,0
pytorch-labs/segment-anything-fast,Model development,Inference optimization,1226,0,0.00%,3,0.24%,72,A batched offline inference oriented version of segment-anything,"cpuhrsch, yanbing-j, kit1980",6,2023-08-30,2024-09-13,0
nus-apr/auto-code-rover,Applications,Coding,2909,0,0.00%,7,0.24%,321,A project structure aware autonomous software engineer aiming for autonomous program improvement. Resolved 37.3% tasks (pass@1) in SWE-bench lite and 46.2% tasks (pass@1) in SWE-bench verified with each task costs less than $0.7.,"Marti2203, crhf, yuntongzhang",14,2024-04-08,2025-03-26,0
rotemweiss57/gpt-newspaper,Applications,Info aggregation,1276,0,0.00%,3,0.24%,177,GPT based autonomous agent designed to create personalized newspapers tailored to user preferences. ,"rotemweiss57, gabimitchell4, filiurskyi",4,2024-01-20,2024-02-25,0
ianarawjo/ChainForge,AI engineering,Prompt engineering,2559,0,0.00%,6,0.23%,206,An open-source visual programming environment for battle-testing prompts to LLMs.,"ianarawjo, priyanmuthu, eglassman",8,2023-03-26,2025-04-02,0
all-in-aigc/aicover,Applications,Image production,1731,0,0.00%,4,0.23%,364,ai cover generator,"idoubi, eltociear",2,2024-01-28,2024-02-03,0
luosiallen/latent-consistency-model,Model repo,,4494,0,0.00%,10,0.22%,233,Latent Consistency Models: Synthesizing High-Resolution Images with Few-Step Inference,"luosiallen, tyq1024, chenxwh",12,2023-10-06,2023-11-11,0
FreedomIntelligence/Medical_NLP,Lists,,2262,0,0.00%,5,0.22%,421,"Medical NLP Competition, dataset, large models, paper","lrs1353281004, wangxidong06, WangRongsheng",8,2019-11-14,2024-06-07,0
google/gemma.cpp,Model development,Inference optimization,6334,0,0.00%,14,0.22%,534,"lightweight, standalone C++ inference engine for Google's Gemma models.","jan-wassenberg, copybara-github, danielkeysers",36,2024-02-13,2025-03-28,0
nmslib/hnswlib,Infrastructure,VectorDB,4620,0,0.00%,10,0.22%,696,Header-only C++/python library for fast approximate nearest neighbors,"yurymalkov, dyashuni, dbespalov",49,2017-07-06,2024-07-21,0
dvorka/mindforger,Applications,Info aggregation,2330,0,0.00%,5,0.21%,142,Thinking notebook and Markdown editor.,"dvorka, vlastahajek, COMPUTER102",9,2017-12-25,2023-12-25,24610
tinygrad/tinygrad,Model development,Modeling & training,28531,0,0.00%,61,0.21%,3280,You like pytorch? You like micrograd? You love tinygrad! ❤️ ,"geohot, chenyuxyz, Qazalin",100+,2020-10-18,2025-04-05,0
spotify/voyager,Infrastructure,VectorDB,1414,0,0.00%,3,0.21%,69,"🛰️ An approximate nearest-neighbor search library for Python and Java with a focus on ease of use, simplicity, and deployability.","psobot, dylanrb123, stephen29xie",8,2023-04-13,2025-03-26,0
princeton-nlp/tree-of-thought-llm,AI engineering,Prompt engineering,5201,0,0.00%,11,0.21%,495,[NeurIPS 2023] Tree of Thoughts: Deliberate Problem Solving with Large Language Models,"ysymyth, karthikncode, nirabo",6,2023-05-17,2025-01-16,0
MineDojo/MineDojo,AI engineering,Agent,1925,0,0.00%,4,0.21%,173,Building Open-Ended Embodied Agents with Internet-Scale Knowledge,"yunfanjiang, DrJimFan, wangguanzhi",8,2022-06-18,2023-08-29,0
dusty-nv/jetson-inference,Infrastructure,Serving,8209,0,0.00%,17,0.21%,3033,Hello AI World guide to deploying deep-learning inference networks and deep vision primitives with TensorRT and NVIDIA Jetson.,"dusty-nv, tokk-nv, asierarranz",21,2016-07-30,2024-03-12,85218
eth-sri/lmql,AI engineering,Prompt engineering,3881,0,0.00%,8,0.21%,207,A language for constraint-guided and efficient LLM programming.,"lbeurerkellner, Viehzeug, charles-dyfis-net",35,2022-11-24,2024-05-09,0
google/gemma_pytorch,Model repo,Multimodal,5413,0,0.00%,11,0.20%,535,The official PyTorch implementation of Google's Gemma models,"pengchongjin, michaelmoynihan, Mon-ius",16,2024-02-20,2025-01-06,0
baaivision/EVA,Model repo,Multimodal,2462,0,0.00%,5,0.20%,183,EVA Series: Visual Representation Fantasies from BAAI,"Yuxin-CV, Quan-Sun, caoyue10",10,2022-11-14,2024-08-01,0
google-deepmind/open_spiel,Model development,Modeling & training,4466,0,0.00%,9,0.20%,977,OpenSpiel is a collection of environments and algorithms for research in general reinforcement learning and search/planning in games.,"lanctot, michalsustr, Jazeem",100+,2019-07-22,2025-03-07,88
FasterDecoding/Medusa,Model development,Inference optimization,2482,0,0.00%,5,0.20%,175,Medusa: Simple Framework for Accelerating LLM Generation with Multiple Decoding Heads,"leeyeehoo, ctlllll, harveyp123",10,2023-09-10,2024-02-27,0
google-research/FLAN,Model repo,,1510,0,0.00%,3,0.20%,160,,"lehougoogle, Shayne13, shayne-longpre",6,2021-08-21,2023-06-02,0
levihsu/OOTDiffusion,Model repo,Multimodal,6188,0,0.00%,12,0.19%,895,"[AAAI 2025] Official implementation of ""OOTDiffusion: Outfitting Fusion based Latent Diffusion for Controllable Virtual Try-on""","levihsu, T-Gu",2,2024-01-24,2024-03-08,0
cloneofsimo/lora,Model development,Modeling & training,7287,0,0.00%,14,0.19%,488,Using Low-rank adaptation to quickly fine-tune diffusion models.,"cloneofsimo, hdon96, levi",18,2022-12-08,2023-01-29,0
open-mmlab/mmdetection,Model development,Modeling & training,30721,0,0.00%,58,0.19%,9612,OpenMMLab Detection Toolbox and Benchmark,"hellock, hhaAndroid, ZwwWayne",100+,2018-08-22,2023-01-04,0
facebookresearch/nougat,Applications,Info aggregation,9377,0,0.00%,17,0.18%,608,Implementation of Nougat Neural Optical Understanding for Academic Documents,"lukas-blecher, rajveer43, erip",14,2023-06-07,2023-09-18,86700
SHI-Labs/Neighborhood-Attention-Transformer,Model repo,,1104,0,0.00%,2,0.18%,88,"Neighborhood Attention Transformer, arxiv 2022 / CVPR 2023. Dilated Neighborhood Attention Transformer, arxiv 2022","alihassanijr, honghuis, stevenwalton",6,2022-04-14,2024-05-15,0
google-research/language,Model repo,,1664,0,0.00%,3,0.18%,352,Shared repository for open-sourced projects from the Google AI Language team.,"kentonl, dhgarrette, jhclark-google",10,2018-10-16,1000-01-01,0
OpenBMB/AgentVerse,AI engineering,Agent,4452,0,0.00%,8,0.18%,442,"🤖 AgentVerse 🪐 is designed to facilitate the deployment of multiple LLM-based agents in various applications, which primarily provides two frameworks: task-solving and simulation","chenweize1998, yushengsu-thu, chanchimin",21,2023-05-06,2024-04-25,0
Lightning-AI/torchmetrics,AI engineering,Evals,2234,0,0.00%,4,0.18%,425,"Machine learning metrics for distributed, scalable PyTorch applications.","Borda, SkafteNicki, stancld",100+,2020-12-22,2025-04-03,5254
jackmpcollins/magentic,AI engineering,Prompt engineering,2241,0,0.00%,4,0.18%,118,Seamlessly integrate LLMs as Python functions,"jackmpcollins, ananis25, alexchandel",11,2023-06-18,2025-03-02,0
lupantech/chameleon-llm,AI engineering,Agent,1126,0,0.00%,2,0.18%,93,"Codes for ""Chameleon: Plug-and-Play Compositional Reasoning with Large Language Models"".","lupantech, guspan-tanadi",2,2023-04-19,2023-12-23,0
hegelai/prompttools,AI engineering,Prompt engineering,2827,0,0.00%,5,0.18%,239,"Open-source tools for prompt testing and experimentation, with support for both LLMs (e.g. OpenAI, LLaMA) and vector databases (e.g. Chroma, Weaviate, LanceDB).","steventkrawczyk, NivekT, HashemAlsaket",12,2023-06-25,2024-03-29,22
baaivision/Emu,Model repo,Multimodal,1704,0,0.00%,3,0.18%,86,Emu Series: Generative Multimodal Models from BAAI,"yqy2001, Quan-Sun, ryanzhangfan",7,2023-07-11,2023-12-26,0
BCG-X-Official/agentkit,AI engineering,Agent,1718,0,0.00%,3,0.17%,234,"Starter-kit to build constrained agents with Nextjs, FastAPI and Langchain","drivian, kaikun213, harticode",8,2024-01-25,2024-07-21,0
open-mmlab/mmdeploy,Infrastructure,Serving,2905,0,0.00%,5,0.17%,659,OpenMMLab Model Deployment Framework,"RunningLeon, AllentDan, grimoire",100+,2021-12-24,2023-08-25,34202
brexhq/prompt-engineering,Tutorials,Prompt engineering,8723,0,0.00%,15,0.17%,417,Tips and tricks for working with Large Language Models like OpenAI's GPT-4.,"stevekrenzel, eltociear, santiagobasulto",3,2023-04-21,2023-05-17,0
google-research/frame-interpolation,Model repo,Multimodal,2955,0,0.00%,5,0.17%,296,"FILM: Frame Interpolation for Large Motion, In ECCV 2022.","fitsumreda, chenxwh, AK391",5,2022-01-12,2022-12-22,0
mpoon/gpt-repository-loader,Applications,Coding,2961,0,0.00%,5,0.17%,234,Convert code repos into an LLM prompt-friendly format. Mostly built by GPT-4.,"mpoon, tnvmadhav, zackees",3,2023-03-16,2023-03-20,0
ray-project/llm-applications,Tutorials,,1787,0,0.00%,3,0.17%,249,A comprehensive guide to building RAG-based LLM applications for production.,"GokuMohandas, pcmoritz, maxpumperla",5,2023-08-16,2024-04-02,0
openai/following-instructions-human-feedback,Model repo,,1205,0,0.00%,2,0.17%,143,,"clwainwright, ryan-lowe",2,2022-01-25,1000-01-01,0
Forethought-Technologies/AutoChain,AI engineering,Agent,1846,0,0.00%,3,0.16%,101,"AutoChain: Build lightweight, extensible, and testable LLM Agents","yyiilluu, tiangolo, xingweitian",9,2023-05-19,1000-01-01,0
google-deepmind/mathematics_dataset,AI engineering,Dataset engineering,1866,0,0.00%,3,0.16%,255,"This dataset code generates mathematical question and answer pairs, from a range of question types at roughly school-level difficulty.","davidsaxton, chrisgorgo, felixboelter",6,2019-03-27,2024-12-23,0
deeppavlov/DeepPavlov,AI engineering,AIE framework,6846,0,0.00%,11,0.16%,1160,An open source library for deep learning end-to-end dialog systems and chatbots.,"dilyararimovna, yoptar, my-master",70,2017-11-17,2024-08-12,0
google-research/multilingual-t5,Model repo,Multilingual,1270,0,0.00%,2,0.16%,129,,"lintingxue, adarob, nconstant-google",11,2020-10-22,2022-12-15,0
a16z-infra/ai-town,Applications,Bots,8302,0,0.00%,13,0.16%,809,"A MIT-licensed, deployable starter kit for building and customizing your own version of AI town - a virtual town where AI characters live, chat and socialize.","ianmacartney, sujayakar, ykhli",37,2023-07-16,2025-02-11,0
SkyworkAI/Skywork,Model repo,Multilingual,1286,0,0.00%,2,0.16%,113,"Skywork series models are pre-trained on 3.2TB of high-quality multilingual (mainly Chinese and English) and code data. We have open-sourced the model, training data, evaluation data, evaluation methods, etc.  ","zhao1iang, chengtbf, TianwenWei",6,2023-10-10,2025-03-07,0
xlang-ai/instructor-embedding,Model development,Modeling & training,1932,0,0.00%,3,0.16%,147,"[ACL 2023] One Embedder, Any Task: Instruction-Finetuned Text Embeddings","Harry-hash, taoyds, hongjin-su",14,2022-12-17,2024-08-25,0
AbdBarho/stable-diffusion-webui-docker,Infrastructure,Toolings,7096,0,0.00%,11,0.16%,1208,Easy Docker setup for Stable Diffusion with user-friendly UI,"AbdBarho, DevilaN, KagurazakaNyaa",23,2022-08-27,2024-06-23,186839
reorproject/reor,Applications,Writing,7781,0,0.00%,12,0.15%,466,Private & local AI personal knowledge management app for high entropy people.,"samlhuillier, joseplayero, milaiwi",23,2023-11-27,2025-04-05,14922
state-spaces/s4,Model repo,,2598,0,0.00%,4,0.15%,314,Structured state space sequence models,"albertfgu, krandiash, ad12",8,2021-11-03,2023-07-11,0
Mooler0410/LLMsPracticalGuide,Tutorials,,9811,0,0.00%,15,0.15%,764,"A curated list of practical guide resources of LLMs (LLMs Tree, Examples, Papers)","Mooler0410, JingfengYang, trx14",13,2023-04-23,2023-08-06,0
adapter-hub/adapters,Model development,Modeling & training,2678,0,0.00%,4,0.15%,358,A Unified Library for Parameter-Efficient and Modular Transfer Learning ,"calpt, lenglaender, hSterz",16,2020-04-21,2025-03-09,0
luban-agi/Awesome-AIGC-Tutorials,Tutorials,,4138,0,0.00%,6,0.14%,275,"Curated tutorials and resources for Large Language Models, AI Painting, and more. ",zht1130,1,2023-08-22,1000-01-01,0
agiresearch/OpenAGI,AI engineering,Agent,2111,0,0.00%,3,0.14%,182,OpenAGI: When LLM Meets Domain Experts,"TobyGE, evison, dongyuanjushi",16,2023-04-08,2024-06-19,0
OpenBMB/ToolBench,AI engineering,AIE framework,4969,0,0.00%,7,0.14%,431,"[ICLR'24 spotlight] An open platform for training, serving, and evaluating large language model for tool learning.","pooruss, thuqinyj16, luyaxi",16,2023-05-28,2024-11-14,0
EleutherAI/gpt-neox,Model development,Modeling & training,7154,0,0.00%,10,0.14%,1048,"An implementation of model parallel autoregressive transformers on GPUs, based on the Megatron and DeepSpeed libraries","StellaAthena, sdtblck, joshlk",100+,2020-12-22,2024-09-10,0
jquesnelle/yarn,Model development,Modeling & training,1457,0,0.00%,2,0.14%,120,YaRN: Efficient Context Window Extension of Large Language Models,"jquesnelle, bloc97, honglu2875",4,2023-06-26,2023-11-20,0
OpenGVLab/LLaMA-Adapter,Model repo,,5850,0,0.00%,8,0.14%,380,[ICLR 2024] Fine-tuning LLaMA to follow Instructions within 1 Hour and 1.2M Parameters,"ZrrSkywalker, csuhan, ChrisLiu6",12,2023-03-19,2023-07-10,5086
trigaten/Learn_Prompting,Tutorials,,4407,0,0.00%,6,0.14%,645,"Prompt Engineering, Generative AI, and LLM Guide by Learn Prompting | Join our discord for the largest Prompt Engineering learning community","trigaten, wahub-ahmed, emilygilmore231",100+,2022-10-25,2025-01-14,0
arogozhnikov/einops,Infrastructure,Toolings,8829,0,0.00%,12,0.14%,368,"Flexible and powerful tensor operations for readable and reliable code (for pytorch, jax, TF and others)","arogozhnikov, MilesCranmer, cgarciae",30,2018-09-22,2025-02-04,0
psychic-api/rag-stack,AI engineering,AIE framework,1493,0,0.00%,2,0.13%,142,"🤖 Deploy a private ChatGPT alternative hosted within your VPC. 🔮 Connect it to your organization's knowledge base and use it as a corporate oracle. Supports open-source LLMs like Llama 2, Falcon, and GPT4All.","Ayan-Bandyopadhyay, ambardhesi, jasonwcfan",6,2023-07-12,2023-09-11,0
google-deepmind/dm-haiku,Model development,Modeling & training,3002,0,0.00%,4,0.13%,242,JAX-based neural network library,"tomhennigan, LenaMartens, copybara-github",77,2020-02-18,2025-03-13,0
Nutlope/aicommits,Applications,Coding,8341,0,0.00%,11,0.13%,418,A CLI that writes your git commit messages for you with AI,"privatenumber, Nutlope, yusefren",24,2023-02-14,2023-02-15,0
Jittor/JittorLLMs,Model development,Inference optimization,2417,0,0.00%,3,0.12%,189,计图大模型推理库，具有高性能、配置要求低、中文支持好、可移植等特点,"cjld, lzhengning, zjp-shadow",7,2023-04-02,2025-02-22,0
pyro-ppl/numpyro,Model development,Modeling & training,2421,0,0.00%,3,0.12%,250,Probabilistic programming with NumPy powered by JAX for autograd and JIT compilation to GPU/TPU/CPU.,"fehiepsi, neerajprad, OlaRonning",100+,2019-02-13,2025-01-25,0
THUDM/GLM,Model development,Modeling & training,3231,0,0.00%,4,0.12%,325,GLM (General Language Model),"duzx16, thomas0809, tuteng0915",11,2021-03-18,2022-12-28,0
metavoiceio/metavoice-src,Model repo,Multimodal,4082,0,0.00%,5,0.12%,683,"Foundational model for human-like, expressive TTS","sidroopdaska, lucapericlp, vatsalaggarwal",10,2024-02-06,2024-07-18,0
mshumer/gpt-llm-trainer,Model development,Dataset engineering,4104,0,0.00%,5,0.12%,541,,mshumer,1,2023-08-09,1000-01-01,0
microsoft/CodeXGLUE,AI engineering,Evals,1644,0,0.00%,2,0.12%,379,CodeXGLUE ,"tangduyu, guody5, celbree",24,2020-08-31,2023-07-31,0
mylxsw/aidea-server,AI engineering,AI interface,1660,0,0.00%,2,0.12%,455,AIdea 是一款支持 GPT  以及国产大语言模型通义千问、文心一言等，支持 Stable Diffusion 文生图、图生图、 SDXL1.0、超分辨率、图片上色的全能型 APP。,"mylxsw, guanyiyao, huangxi1020",9,2023-08-30,2025-02-21,93
mshumer/gpt-author,Applications,Writing,2513,0,0.00%,3,0.12%,364,,mshumer,1,2023-06-20,1000-01-01,0
microsoft/i-Code,Model development,Modeling & training,1696,0,0.00%,2,0.12%,161,,"zinengtang, CoDi-Gen, m-khad",11,2022-12-07,2024-03-11,0
TaskingAI/TaskingAI,AI engineering,AIE framework,5095,0,0.00%,6,0.12%,320,The open source platform for AI-native application development.,"jameszyao, LinkW77, taskingaiwww",17,2024-01-08,2024-04-07,0
Instruction-Tuning-with-GPT-4/GPT-4-LLM,Model development,Dataset engineering,4293,0,0.00%,5,0.12%,305,Instruction Tuning with GPT-4,"ChunyuanLI, Instruction-Tuning-with-GPT-4, 152334H",5,2023-04-06,2023-04-08,0
carson-katri/dream-textures,Applications,Image production,7974,0,0.00%,9,0.11%,436,Stable Diffusion built-in to Blender,"carson-katri, NullSenseStudio, blastframe",13,2022-09-08,2023-09-18,204237
uTensor/uTensor,Model development,Inference optimization,1791,0,0.00%,2,0.11%,232,TinyML AI inference library,"mbartling, neil-tan, Knight-X",18,2017-09-21,2024-11-01,0
open-mmlab/mmpretrain,Model development,Modeling & training,3614,0,0.00%,4,0.11%,1080,OpenMMLab Pre-training Toolbox and Benchmark,"mzr1996, Ezra-Yu, LXXXXR",100+,2020-07-09,2021-09-29,0
lucidrains/imagen-pytorch,Model repo,Multimodal,8226,0,0.00%,9,0.11%,782,"Implementation of Imagen, Google's Text-to-Image Neural Network, in Pytorch","lucidrains, Netruk44, animebing",20,2022-05-23,2024-01-08,0
jina-ai/dev-gpt,Applications,Coding,1838,0,0.00%,2,0.11%,168,Your Virtual Development Team,"florian-hoenicke, joschkabraun, francescor",7,2023-03-17,2023-06-06,0
Alpha-VLLM/LLaMA2-Accessory,Model development,Modeling & training,2767,0,0.00%,3,0.11%,175,An Open-source Toolkit for LLM Development,"ChrisLiu6, Enderfga, kriskrisliu",19,2023-07-21,2024-05-24,0
google-research/t5x,Model development,Modeling & training,2779,0,0.00%,3,0.11%,316,,"adarob, cpgaffney1, hwchung27",100+,2021-11-01,2025-03-26,0
marella/ctransformers,AI engineering,AIE framework,1856,0,0.00%,2,0.11%,142,Python bindings for the Transformer models implemented in C/C++ using GGML library.,"marella, abacaj, jncraton",6,2023-05-14,2023-08-08,0
chathub-dev/chathub,AI engineering,AI interface,10241,0,0.00%,11,0.11%,1071,All-in-one chatbot client,"wong2, okkidwi, 10cl",13,2023-03-04,2025-03-10,43580
greshake/llm-security,AI engineering,Prompt engineering,1910,0,0.00%,2,0.10%,130,New ways of breaking app-integrated LLMs ,"greshake, shhra, eltociear",3,2023-02-19,2023-04-24,0
anse-app/anse,AI engineering,AI interface,1921,0,0.00%,2,0.10%,431,"Supercharged experience for multiple models such as ChatGPT, DALL-E and Stable Diffusion.","ddiu8081, yzh990918, eliassebastian",24,2023-04-26,2024-03-12,0
FreedomIntelligence/LLMZoo,AI engineering,AIE framework,2941,0,0.00%,3,0.10%,199,"⚡LLM Zoo is a project that provides data, models, and evaluation benchmark for large language models.⚡","zhjohnchan, wabyking, fjiangAI",9,2023-04-01,2023-07-25,0
google/BIG-bench,AI engineering,Evals,3011,0,0.00%,3,0.10%,601,Beyond the Imitation Game collaborative benchmark for measuring and extrapolating the capabilities of language models,"Sohl-Dickstein, sarahadlerrous, guygurari",100+,2021-01-15,2023-06-13,0
LianjiaTech/BELLE,Model development,Modeling & training,8108,0,0.00%,8,0.10%,768,BELLE: Be Everyone's Large Language model Engine（开源中文对话大模型）,"tjadamlee, wen2cheng, mabaochang",24,2023-03-17,2024-10-16,2524
daveshap/OpenAI_Agent_Swarm,Applications,Workflow automation,3064,0,0.00%,3,0.10%,395,"HAAS = Hierarchical Autonomous Agent Swarm - ""Resistance is futile!""","daveshap, guillermo-delrio, OWigginsHay",18,2023-11-07,2023-12-07,0
lxe/simple-llm-finetuner,Model development,Modeling & training,2063,0,0.00%,2,0.10%,130,Simple UI for LLM Model Finetuning,"lxe, vadi2, 64-bit",5,2023-03-22,2023-04-23,0
openchatai/OpenChat,Applications,Workflow automation,5233,0,0.00%,5,0.10%,641,LLMs custom-chatbots console ⚡,"lvalics, codebanesr, gharbat",13,2023-05-30,2023-08-22,0
ray-project/llm-numbers,Tutorials,,4196,0,0.00%,4,0.10%,140,Numbers every LLM developer should know,"waleedkadous, scottsun94, ufmayer",7,2023-05-17,2023-08-27,0
gligen/GLIGEN,Model repo,Multimodal,2103,0,0.00%,2,0.10%,158,Open-Set Grounded Text-to-Image Generation,"Yuheng-Li, haotian-liu, ChunyuanLI",5,2023-01-13,2023-03-30,0
kantord/SeaGOAT,Applications,Coding,1055,0,0.00%,1,0.09%,66,local-first semantic code search engine,"kantord, last-partizan, photonbit",22,2023-06-20,2025-04-05,0
OpenBMB/VisCPM,Model repo,Multimodal,1055,0,0.00%,1,0.09%,93,[ICLR'24 spotlight] Chinese and English Multimodal Large Model Series (Chat and Paint) | 基于CPM基础模型的中英双语多模态大模型系列,"JamesHujy, Cppowboy, Cuiunbo",5,2023-06-30,2023-09-07,0
THUDM/CodeGeeX,Model repo,,8446,0,0.00%,8,0.09%,630,CodeGeeX: An Open Multilingual Code Generation Model (KDD 2023),"Stanislas0, BBuf, ss18",12,2022-09-17,2023-03-06,0
openai/consistencydecoder,Model repo,Multimodal,2171,0,0.00%,2,0.09%,76,Consistency Distilled Diff VAE,gabgoh,1,2023-11-02,1000-01-01,0
openai/point-e,Model repo,,6683,0,0.00%,6,0.09%,781,Point cloud diffusion for 3D model synthesis,"unixpickle, heewooj",2,2022-12-06,2022-12-20,0
instill-ai/vdp,AI engineering,AIE framework,2239,0,0.00%,2,0.09%,112,"🔮 Instill Core is a full-stack AI infrastructure tool for data, model and pipeline orchestration, designed to streamline every aspect of building versatile AI-first applications","pinglin, donch1989, droplet-bot",25,2022-01-13,2025-04-03,0
TigerResearch/TigerBot,Model repo,Multilingual,2258,0,0.00%,2,0.09%,192,TigerBot: A multi-language multi-task LLM,"Vivicai1005, chentigerye, mint-vip",13,2023-05-12,2024-03-18,0
dot-agent/nextpy,AI engineering,Agent,2273,0,0.00%,2,0.09%,166,🤖Self-Modifying Framework from the Future 🔮 World's First AMS,"anubrag, shravya-34, Raj4646",12,2023-08-07,2024-03-25,0
InternLM/InternLM,Model repo,Multilingual,6842,0,0.00%,6,0.09%,482,"Official release of InternLM series (InternLM, InternLM2, InternLM2.5, InternLM3).","huangting4201, sunpengsdu, yingtongxiong",61,2023-07-06,2025-02-07,0
declare-lab/tango,Model repo,Multimodal,1157,0,0.00%,1,0.09%,98,A family of diffusion models for text-to-audio generation.,"soujanyaporia, deepanwayx, nmder",7,2023-04-10,2024-05-02,0
kuafuai/DevOpsGPT,Applications,Coding,5879,0,0.00%,5,0.09%,717,Multi agent system for AI-driven software development. Combine LLM with DevOps tools to convert natural language requirements into working software. Supports any development language and extends the existing code.,"booboosui, yakeJiang, charging-kuafuai",13,2023-07-12,2023-12-12,0
microsoft/TypeChat,AI engineering,Prompt engineering,8413,0,0.00%,7,0.08%,397,TypeChat is a library that makes it easy to build natural language interfaces using types.,"DanielRosenwasser, steveluc, umeshma",30,2023-06-20,2023-11-28,0
paulpierre/RasaGPT,AI engineering,AIE framework,2411,0,0.00%,2,0.08%,239,"💬 RasaGPT is the first headless LLM chatbot platform built on top of Rasa and Langchain. Built w/ Rasa, FastAPI, Langchain, LlamaIndex, SQLModel, pgvector, ngrok, telegram","paulpierre, PythonicNinja, eltociear",6,2023-04-25,2023-05-18,0
google-research/multinerf,Model repo,,3708,0,0.00%,3,0.08%,345,"A Code Release for Mip-NeRF 360, Ref-NeRF, and RawNeRF","jonbarron, Sarasra, bmild",8,2022-07-22,2023-08-29,0
IntelLabs/control-flag,Model development,Dataset engineering,1243,0,0.00%,1,0.08%,114,A system to flag anomalous source code expressions by learning typical expressions from training data,"nhasabni, jgottschlich, jsoref",6,2021-08-04,2024-03-02,0
sweepai/sweep,Applications,Coding,7538,0,0.00%,6,0.08%,445,Sweep: AI coding assistant for JetBrains,"kevinlu1248, wwzeng1, lukejagg",34,2023-06-14,2025-02-27,74
salesforce/CodeGen,Model repo,,5054,0,0.00%,4,0.08%,393,CodeGen is a family of open-source model for program synthesis. Trained on TPU-v4. Competitive with OpenAI Codex.,"enijkamp, rooa, bpucla",11,2022-03-28,2023-08-15,0
AIGC-Audio/AudioGPT,Model repo,,10124,0,0.00%,8,0.08%,863,"AudioGPT: Understanding and Generating Speech, Music, Sound, and Talking Head","lmzjms, Rongjiehuang, PeppaPiggeee",11,2023-03-16,2023-05-05,0
OpenPipe/OpenPipe,Model development,Modeling & training,2560,0,0.00%,2,0.08%,138,Turn expensive prompts into cheap fine-tuned models,"corbt, arcticfly, Kovbo",12,2023-06-20,2024-03-19,0
yerfor/GeneFace,Model repo,Multimodal,2595,0,0.00%,2,0.08%,295,GeneFace: Generalized and High-Fidelity 3D Talking Face Synthesis; ICLR 2023; Official code,"yerfor, SouthTea, xk-huang",4,2023-02-03,2024-07-08,6647
JohnSnowLabs/spark-nlp,Model development,Modeling & training,3951,0,0.00%,3,0.08%,722,State of the Art Natural Language Processing,"maziyarpanahi, saif-ellafi, DevinTDHa",91,2017-09-24,2025-04-03,0
hiyouga/FastEdit,AI engineering,Prompt engineering,1321,0,0.00%,1,0.08%,98,🩹Editing large language models within 10 seconds⚡,"hiyouga, BUAADreamer",2,2023-07-09,2023-07-09,0
microsoft/prompt-engine,AI engineering,Prompt engineering,2650,0,0.00%,2,0.08%,113,A library for helping developers craft prompts for Large Language Models,"abhimasand, ryanvolum, microsoftopensource",5,2022-06-17,2022-10-12,0
JonasGeiping/cramming,Model development,Modeling & training,1326,0,0.00%,1,0.08%,102,Cramming the training of a (BERT-type) language model into limited compute.,"JonasGeiping, Randl, ss18",7,2022-12-29,2024-02-18,0
SHI-Labs/Versatile-Diffusion,Model repo,,1328,0,0.00%,1,0.08%,85,"Versatile Diffusion: Text, Images and Variations All in One Diffusion Model, arXiv 2022 / ICCV 2023","xingqian2018, honghuis, anton-l",4,2022-11-02,2022-12-02,0
FMInference/FlexGen,Infrastructure,Serving,9296,0,0.00%,7,0.08%,569,Running large language models on a single GPU for throughput-oriented scenarios.,"Ying1123, merrymercy, zhangce",19,2023-02-15,2024-10-28,0
imoneoi/openchat,Model repo,,5321,0,0.00%,4,0.08%,411,OpenChat: Advancing Open-source Language Models with Imperfect Data,"imoneoi, AdaCheng, alpayariyak",15,2023-05-25,2024-05-24,0
ekzhu/datasketch,AI engineering,Dataset engineering,2664,0,0.00%,2,0.08%,296,"MinHash, LSH, LSH Forest, Weighted MinHash, HyperLogLog, HyperLogLog++, LSH Ensemble and HNSW","ekzhu, vmarkovtsev, aastafiev",27,2015-03-20,2024-03-26,27
gorilla-llm/gorilla-cli,Applications,Coding,1335,0,0.00%,1,0.07%,76,LLMs for your CLI,"ShishirPatil, philipmat, royh02",5,2023-06-20,2023-10-19,0
Yifan-Song793/RestGPT,AI engineering,Agent,1357,0,0.00%,1,0.07%,103,An LLM-based autonomous agent controlling real-world applications via RESTful APIs,"Yifan-Song793, WeiminXiong, Jdelbarcogarza",4,2023-08-26,2023-09-11,0
leptonai/leptonai,AI engineering,AIE framework,2727,0,0.00%,2,0.07%,178,A Pythonic framework to simplify AI service building,"Yangqing, bddppq, xlu451",29,2023-09-06,2025-03-26,0
neo4j/NaLLM,Applications,Data organization,1366,0,0.00%,1,0.07%,276,Repository for the NaLLM project,"noahmay, tomasonjo, oskarhane",4,2023-05-02,2023-07-13,0
THUDM/VisualGLM-6B,Model repo,Multimodal,4150,0,0.00%,3,0.07%,424,Chinese and English multimodal conversational language model | 多模态中英双语对话语言模型,"Sleepychord, 1049451037, lykeven",8,2023-04-23,2023-05-31,0
ludwig-ai/ludwig,AI engineering,AIE framework,11410,0,0.00%,8,0.07%,1204,"Low-code framework for building custom LLMs, neural networks, and other AI models","w4nderlust, jimthompson5802, tgaddair",100+,2018-12-27,1000-01-01,0
dleemiller/WordLlama,AI engineering,Dataset engineering,1432,0,0.00%,1,0.07%,53,Things you can do with the token embeddings of an LLM,"dleemiller, erickpeirson, jgbarah",5,2024-06-12,2024-10-14,0
code-kern-ai/refinery,Applications,,1433,0,0.00%,1,0.07%,71,"The data scientist's open-source choice to scale, assess and maintain natural language data. Treat training data like a software artifact.","jhoetter, JWittmeyer, FelixKirsch",10,2022-07-04,2024-12-09,0
yizhongw/self-instruct,AI engineering,Dataset engineering,4333,0,0.00%,3,0.07%,505,Aligning pretrained language models with instruction data generated by themselves.,"yizhongw, yeganehkordi",2,2022-12-20,2023-03-10,0
TencentARC/GFPGAN,Model repo,,36533,0,0.00%,25,0.07%,6053,GFPGAN aims at developing Practical Algorithms for Real-world Face Restoration.,"xinntao, amckenna41, AK391",11,2021-03-19,2022-02-14,5235104
RootbeerComputer/backend-GPT,Applications,Coding,2947,0,0.00%,2,0.07%,223,,"evanmays, TheAppleTucker, Teddy55Codes",4,2023-01-21,2023-02-04,0
Pythagora-io/gpt-pilot,Applications,Coding,32561,0,0.00%,22,0.07%,3305,The first real AI developer,"LeonOstrez, nalbion, senko",47,2023-08-16,2025-03-04,0
jina-ai/finetuner,Model development,Modeling & training,1492,0,0.00%,1,0.07%,69,":dart: Task-oriented embedding tuning for BERT, CLIP, etc.","hanxiao, bwanglzu, LMMilliken",29,2021-08-11,2023-07-26,0
openlm-research/open_llama,Model repo,,7470,0,0.00%,5,0.07%,396,"OpenLLaMA, a permissively licensed open source reproduction of Meta AI’s LLaMA 7B trained on the RedPajama dataset","young-geng, mjul",2,2023-04-28,2023-05-04,0
qwopqwop200/GPTQ-for-LLaMa,Model development,Inference optimization,3047,0,0.00%,2,0.07%,461,4 bits quantization of LLaMA using GPTQ,"qwopqwop200, tpoisonooo, MasterTaffer",26,2023-03-06,2023-06-23,0
alpa-projects/alpa,Model development,Modeling & training,3122,0,0.00%,2,0.06%,361,Training and serving large-scale neural networks with auto parallelization.,"merrymercy, zhisbug, ZYHowell",43,2021-02-22,2023-12-09,5137
jina-ai/thinkgpt,AI engineering,Agent,1570,0,0.00%,1,0.06%,137,Agent techniques to augment your LLM and push it beyong its limits,"alaeddine-13, hanxiao, eltociear",3,2023-04-14,2023-04-30,0
AetherCortex/Llama-X,Model development,Modeling & training,1618,0,0.00%,1,0.06%,103,Open Academic Research on Improving LLaMA to SOTA LLM,"AetherCortex, victorsungo, Wangpeiyi9979",13,2023-03-30,2023-05-03,0
lucidrains/musiclm-pytorch,Model repo,Multimodal,3242,0,0.00%,2,0.06%,264,"Implementation of MusicLM, Google's new SOTA model for music generation using attention networks, in Pytorch",lucidrains,1,2023-01-27,2023-03-31,0
jina-ai/langchain-serve,AI engineering,AIE framework,1625,0,0.00%,1,0.06%,138,⚡ Langchain apps in production using Jina & FastAPI,"deepankarm, zac-li, hanxiao",6,2023-03-21,2023-08-22,0
yihong0618/xiaogpt,AI engineering,AI interface,6519,0,0.00%,4,0.06%,904,Play ChatGPT and other LLM with Xiaomi AI Speaker,"yihong0618, frostming, pjq",46,2023-02-16,2025-03-19,0
OS-Copilot/FRIDAY,Applications,Workflow automation,1638,0,0.00%,1,0.06%,181,An self-improving embodied conversational agent seamlessly integrated into the operating system to automate our daily tasks. ,"heroding77, hccngu, Tamiflu233",12,2024-02-02,2024-09-09,0
nhaouari/obsidian-textgenerator-plugin,Applications,Writing,1653,0,0.00%,1,0.06%,152,"Text Generator is a versatile plugin for Obsidian that allows you to generate text content using various AI providers, including OpenAI, Anthropic, Google and local models.","haouarihk, nhaouari, ckt1031",21,2022-02-27,2025-02-27,524252
deep-diver/LLM-As-Chatbot,Applications,Bots,3312,0,0.00%,2,0.06%,378,LLM as a Chatbot Service,"deep-diver, peterschmidt85, ErivanDev",7,2023-02-27,2023-07-26,0
eyurtsev/kor,AI engineering,Prompt engineering,1662,0,0.00%,1,0.06%,92,LLM(😽) ,"eyurtsev, BorisWilhelms, chaimt",12,2023-02-16,2024-11-25,0
jaymody/picoGPT,Model repo,,3337,0,0.00%,2,0.06%,433,An unnecessarily tiny implementation of GPT-2 in NumPy.,"jaymody, certik, aletheap",5,2023-01-21,2023-04-04,0
towhee-io/towhee,Model development,Dataset engineering,3344,0,0.00%,2,0.06%,258,Towhee is a framework that is dedicated to making neural data processing pipelines simple and fast.,"Chiiizzzy, junjiejiangjjj, shiyu22",35,2021-07-13,2024-10-17,2613
iryna-kondr/scikit-llm,Model development,Modeling & training,3438,0,0.00%,2,0.06%,278,Seamlessly integrate LLMs into scikit-learn.,"OKUA1, iryna-kondr, AndreasKarasenko",12,2023-05-12,2025-01-04,0
life4/textdistance,AI engineering,Dataset engineering,3460,0,0.00%,2,0.06%,252,"📐 Compute distance between sequences. 30+ algorithms, pure python implementation, common interface, optional external libs usage.","orsinium, juliangilbey, musicinmybrain",14,2017-05-05,2024-09-09,1015
google-deepmind/penzai,Model development,Modeling & training,1749,0,0.00%,1,0.06%,59,"A JAX research toolkit for building, editing, and visualizing neural networks.","danieldjohnson, amifalk, Fedzbar",5,2024-04-04,2024-12-16,0
Pythagora-io/pythagora,Applications,Coding,1769,0,0.00%,1,0.06%,99,Generate automated tests for your Node.js app via LLMs without developers having to write a single line of code.,"LeonOstrez, zvone187, pavel-pythagora",6,2023-01-21,2023-10-19,0
Kent0n-Li/ChatDoctor,Applications,Bots,3548,0,0.00%,2,0.06%,417,,"Kent0n-Li, saharmor",2,2023-03-21,2023-03-30,0
AI-Citizen/SolidGPT,Applications,Coding,1784,0,0.00%,1,0.06%,129,Developer AI Persona Search Agent,"wwwwww1020, alexgong14, Xinchunran",8,2023-08-08,2025-01-12,0
facebookresearch/codellama,Model repo,,16264,0,0.00%,9,0.06%,1905,Inference code for CodeLlama models,"jspisak, jgehring, mpu",11,2023-08-24,2024-05-21,0
salesforce/ctrl,Model development,Modeling & training,1880,0,0.00%,1,0.05%,207,Conditional Transformer Language Model for Controllable Generation,"keskarnitish, voidism, julien-c",4,2019-08-29,2019-10-23,0
camenduru/stable-diffusion-webui-colab,Applications,Image production,15813,0,0.00%,8,0.05%,2633,stable diffusion webui colab,"camenduru, anuragrajput40904, etherealxx",14,2022-10-05,2024-10-15,546022
openai/grok,Model repo,,4141,0,0.00%,2,0.05%,554,,"aletheap, yburda, eltociear",3,2021-04-12,2024-03-16,0
intel/intel-extension-for-transformers,AI engineering,AIE framework,2165,0,0.00%,1,0.05%,210,⚡ Build your chatbot within minutes on your favorite device; offer SOTA compression techniques for LLMs; run LLMs efficiently on Intel Platforms⚡,"VincyZhang, changwangss, lvliang-intel",100+,2022-11-11,2024-08-13,0
varunshenoy/GraphGPT,Applications,Coding,4394,0,0.00%,2,0.05%,393,Extrapolating knowledge graphs from unstructured text using GPT-3 🕵️‍♂️,"varunshenoy, ramonwenger, katsi",3,2023-01-31,2023-03-03,0
refuel-ai/autolabel,Model development,Dataset engineering,2200,0,0.00%,1,0.05%,155,"Label, clean and enrich text datasets with LLMs.","rajasbansal, DhruvaBansal00, nihit",19,2023-03-23,2025-02-24,31
Stability-AI/StableStudio,Applications,Image production,8967,0,0.00%,4,0.04%,913,Community interface for generative AI,"cruhl, jtydhr88, KAJdev",6,2023-04-21,2023-05-24,432
allenai/RL4LMs,Model development,Modeling & training,2295,0,0.00%,1,0.04%,195,A modular RL library to fine-tune language models to human preferences,"rajcscw, jmhessel, rajammanabrolu",6,2022-08-18,2023-03-30,0
spcl/graph-of-thoughts,AI engineering,Prompt engineering,2328,0,0.00%,1,0.04%,166,"Official Implementation of ""Graph of Thoughts: Solving Elaborate Problems with Large Language Models""","nblach, rgersten, aleskubicek",4,2023-08-18,2024-12-11,0
togethercomputer/RedPajama-Data,Model development,Dataset engineering,4690,0,0.00%,2,0.04%,355,The RedPajama-Data repository contains code for preparing large datasets for training large language models.,"mauriceweber, xzyaoi, zhangce",8,2023-04-14,2024-12-07,0
cogentapps/chat-with-gpt,AI engineering,AI interface,2347,0,0.00%,1,0.04%,496,An open-source ChatGPT app with a voice,"cogentapps, ZauberNerd, schmidp",8,2023-03-06,2023-07-15,0
SoraWebui/SoraWebui,Applications,Bots,2358,0,0.00%,1,0.04%,524,"SoraWebui is an open-source Sora web client, enabling users to easily create videos from text with OpenAI's Sora model.","littletry, yangchuansheng, eltociear",5,2024-02-18,2024-02-29,0
xusenlinzy/api-for-open-llm,AI engineering,AI interface,2427,0,0.00%,1,0.04%,278,"Openai style api for open large language models, using LLMs just as chatgpt! Support for LLaMA, LLaMA-2, BLOOM, Falcon, Baichuan, Qwen, Xverse, SqlCoder, CodeLLaMA, ChatGLM, ChatGLM2, ChatGLM3 etc. 开源大模型的统一后端接口","xusenlinzy, lzhfe, FreeRotate",13,2023-05-23,2024-09-26,0
KillianLucas/01,Applications,Workflow automation,5054,0,0.00%,2,0.04%,538,"The #1 open-source voice interface for desktop, mobile, and ESP32 chips.","KillianLucas, tyfiero, MikeBirdTech",45,2024-01-11,2024-10-02,0
lamini-ai/lamini,Model development,Modeling & training,2528,0,0.00%,1,0.04%,151,The Official Python Client for Lamini's API,"edamamez, sharonzhou, gdiamos",13,2023-04-20,2025-03-25,0
ohmplatform/FreedomGPT,Applications,Bots,2637,0,0.00%,1,0.04%,355,This codebase is for a React and Electron-based app that executes the FreedomGPT LLM locally (offline and private) on Mac and Windows using a chat-based interface,"holdenchristiansen, bhattaraijay05",2,2023-03-21,2023-05-05,404793
whylabs/whylogs,Infrastructure,Monitoring,2702,0,0.00%,1,0.04%,125,"An open-source data logging library for machine learning models and data pipelines. 📚 Provides visibility into data quality & model performance over time. 🛡️ Supports privacy-preserving data collection, ensuring safety & robustness. 📈","jamie256, FelipeAdachi, richard-rogers",23,2020-08-14,2021-08-26,122
OpenBMB/BMTools,AI engineering,Agent,2777,0,0.00%,1,0.04%,258,"Tool Learning for Big Models, Open-Source Solutions of ChatGPT-Plugins","thuqinyj16, Kunlun-Zhu, congxin95",16,2023-03-31,2023-06-06,0
shreyashankar/gpt3-sandbox,AI engineering,Prompt engineering,2893,0,0.00%,1,0.03%,873,The goal of this project is to enable users to create cool web demos using the newly released OpenAI GPT-3 API with just a few lines of Python.,"shreyashankar, buyumaz, bora-uyumazturk",13,2020-07-19,2023-02-24,0
snorkel-team/snorkel,Model development,Dataset engineering,5844,0,0.00%,2,0.03%,858,A system for quickly generating training data with weak supervision,"ajratner, henryre, stephenbach",69,2016-02-26,2024-01-11,1055
gmpetrov/databerry,AI engineering,Agent,2931,0,0.00%,1,0.03%,430,The no-code platform for building custom LLM Agents,gmpetrov,1,2023-03-27,2023-09-25,0
li-plus/chatglm.cpp,Model development,Inference optimization,2973,0,0.00%,1,0.03%,333,C++ implementation of ChatGLM-6B & ChatGLM2-6B & ChatGLM3 & GLM4(V),"li-plus, wizardbyron, eltociear",14,2023-05-23,2024-07-31,832
dvlab-research/MiniGemini,Model repo,Multimodal,3262,0,0.00%,1,0.03%,282,"Official repo for ""Mini-Gemini: Mining the Potential of Multi-modality Vision Language Models""","yanwei-li, wcy1122, JulianJuaner",5,2024-03-26,2024-04-17,0
olivia-ai/olivia,Applications,Bots,3693,0,0.00%,1,0.03%,355,💁‍♀️Your new best friend powered by an artificial neural network,"hugolgst, Aury88, A-Yamout",16,2018-06-05,2021-02-25,0
borisdayma/dalle-mini,Model repo,Multimodal,14801,0,0.00%,4,0.03%,1221,DALL·E Mini - Generate images from a text prompt,"borisdayma, pcuenca, khalidsaifullaah",26,2021-07-03,2023-08-22,0
shobrook/adrenaline,Applications,Coding,3790,0,0.00%,1,0.03%,317,Chat with (and visualize) your codebase,"shobrook, Mbladra, wizard-waqas",7,2022-12-07,2023-08-24,0
FlagAI-Open/FlagAI,Model development,Modeling & training,3861,0,0.00%,1,0.03%,417,"FlagAI (Fast LArge-scale General AI models) is a fast, easy-to-use and extensible toolkit for large-scale model.","ftgreat, BAAI-OpenPlatform, shunxing1234",31,2022-05-16,2024-09-29,0
jina-ai/clip-as-service,AI engineering,AI interface,12623,0,0.00%,3,0.02%,2074,"🏄 Scalable embedding, reasoning, ranking for images and sentences with CLIP","hanxiao, ZiniuYu, numb3r3",53,2018-11-12,2022-12-08,0
openai/plugins-quickstart,AI engineering,AI interface,4237,0,0.00%,1,0.02%,728,Get a ChatGPT plugin up and running in under 5 minutes!,"logankilpatrick, joedevon",2,2023-04-06,2023-06-05,0
dotnet/machinelearning,Model development,Modeling & training,9137,0,0.00%,2,0.02%,1906,ML.NET is an open source and cross-platform machine learning framework for .NET.,"codemzs, michaelgsharp, Ivanidzo4ka",100+,2018-05-03,2025-03-05,0
BlinkDL/ChatRWKV,Applications,Bots,9469,0,0.00%,2,0.02%,706,"ChatRWKV is like ChatGPT but powered by RWKV (100% RNN) language model, and open source.","www, BlinkDL, daquexian",17,2023-01-13,2023-11-03,0
mshumer/gpt-prompt-engineer,AI engineering,Prompt engineering,9495,0,0.00%,2,0.02%,662,,"mshumer, vrushankportkey, alexfazio",7,2023-07-04,2024-04-12,0
codota/TabNine,Applications,Coding,10749,0,0.00%,2,0.02%,506,AI Code Completions,"zxqfl, dimacodota, amircodota",18,2018-11-06,2024-07-03,0
openai/DALL-E,Model repo,Multimodal,10841,0,0.00%,2,0.02%,1932,PyTorch package for the discrete VAE used for DALL·E.,adityaramesh,1,2021-02-23,1000-01-01,0
ConnectAI-E/Feishu-OpenAI,Applications,Bots,5570,0,0.00%,1,0.02%,958,🎒 飞书  ×（GPT-4 + GPT-4V + DALL·E-3 + Whisper）=  飞一般的工作体验  🚀 语音对话、角色扮演、多话题讨论、图片创作、表格分析、文档导出 🚀,"Leizhenpeng, DDMeaqua, aaakoako",32,2023-02-07,2023-11-20,2974
Lightning-AI/lit-llama,Model repo,,6044,0,0.00%,1,0.02%,522,"Implementation of the LLaMA language model based on nanoGPT. Supports flash attention, Int8 and GPTQ 4bit quantization, LoRA and LLaMA-Adapter fine-tuning, pre-training. Apache 2.0-licensed.","carmocca, lantiga, awaelchli",31,2023-03-22,2024-09-06,0
SawyerHood/draw-a-ui,Applications,Coding,13490,0,0.00%,2,0.01%,1617,Draw a mockup and generate html for it,"SawyerHood, shtepcell, 0x-sen",4,2023-11-07,2023-11-13,0
google-deepmind/lab,Model development,Modeling & training,7201,0,0.00%,1,0.01%,1374,A customisable 3D platform for agent-based AI research,"tkoeppe, charlesbeattie, decster",7,2016-11-30,2023-01-04,0
sfyc23/EverydayWechat,Applications,Bots,10132,0,0.00%,1,0.01%,2316,微信助手：1.每日定时给好友（女友）发送定制消息。2.机器人自动回复好友。3.群助手功能（例如：查询垃圾分类、天气、日历、电影实时票房、快递物流、PM2.5等）,"sfyc23, yaochaorui, L1cardo",13,2019-03-11,2019-12-14,0
emilwallner/Screenshot-to-code,Applications,Coding,16520,0,0.00%,1,0.01%,1562,A neural network that transforms a design mock-up into a static website.,"emilwallner, Masa-Shin, bhageena",6,2017-10-16,2019-09-10,0
axflow/axflow,AI engineering,AIE framework,1116,0,0.00%,0,0.00%,45,The TypeScript framework for AI development,"benjreinhart, nichochar, ricki-epsilla",3,2023-07-02,2023-12-21,0
timqian/openprompt.co,Lists,Prompt engineering,1197,0,0.00%,0,0.00%,59,Create. Use. Share. ChatGPT prompts,timqian,2,2023-03-22,1000-01-01,0
lucidrains/self-rewarding-lm-pytorch,Model development,Modeling & training,1376,0,0.00%,0,0.00%,71,"Implementation of the training framework proposed in Self-Rewarding Language Model, from MetaAI","lucidrains, Control-derek, Dyke-F",5,2024-01-19,2024-04-11,0
premAI-io/state-of-open-source-ai,Tutorials,,1554,0,0.00%,0,0.00%,91,:closed_book: Clarity in the current fast-paced mess of Open Source innovation,"casperdcl, nsosio, biswaroop1547",16,2023-08-10,2025-01-20,0
rlancemartin/auto-evaluator,AI engineering,Evals,1073,0,0.00%,0,0.00%,92,Evaluation tool for LLM QA chains,"rlancemartin, eltociear, prem2012",4,2023-04-14,2023-04-21,0
ray-project/ray-llm,Infrastructure,Serving,1263,0,0.00%,0,0.00%,93,RayLLM - LLMs on Ray (Archived). Read README for more info.,"Yard1, richardliaw, shrekris-anyscale",22,2023-05-31,2024-05-28,0
gabrielchua/RAGxplorer,AI engineering,AIE framework,1119,0,0.00%,0,0.00%,101,Open-source tool to visualise your RAG 🔮,"gabrielchua, vince-lam, dan-s-mueller",9,2024-01-11,2024-02-04,0
nerfstudio-project/nerfacc,Model development,Modeling & training,1424,0,0.00%,0,0.00%,117,A General NeRF Acceleration Toolbox in PyTorch.,"liruilong940607, tancik, 97littleleaf11",23,2022-09-08,2024-10-02,0
Cormanz/smartgpt,Applications,Workflow automation,1760,0,0.00%,0,0.00%,126,A program that provides LLMs with the ability to complete complex tasks using plugins.,"Cormanz, philip06, orvitpng",8,2023-04-11,2023-06-02,0
farizrahman4u/loopgpt,AI engineering,Agent,1451,0,0.00%,0,0.00%,131,Modular Auto-GPT Framework,"FayazRahman, farizrahman4u, iskandarreza",10,2023-04-14,2024-03-10,0
huggingface/llm-vscode,Applications,Coding,1287,0,0.00%,0,0.00%,138,LLM powered development for VSCode,"McPatate, antoinejeannot, HennerM",6,2023-02-16,2024-07-17,0
anthropics/hh-rlhf,Model development,Dataset engineering,1713,0,0.00%,0,0.00%,138,"Human preference data for ""Training a Helpful and Harmless Assistant with Reinforcement Learning from Human Feedback""","8enmann, dganguli, NinoRisteski",4,2022-04-10,2023-09-19,0
howl-anderson/unlocking-the-power-of-llms,Tutorials,,2495,0,0.00%,0,0.00%,157,使用 Prompts 和 Chains 让 ChatGPT 成为神奇的生产力工具！Unlocking the power of LLMs.,howl-anderson,1,2023-02-16,1000-01-01,0
wangzhaode/mnn-llm,Infrastructure,Toolings,1573,0,0.00%,0,0.00%,173,llm deploy project based mnn. This project has merged into MNN.,"wangzhaode, Tlntin, WhisperChi",10,2023-03-17,2024-09-27,55541
google-deepmind/bsuite,AI engineering,Evals,1518,0,0.00%,0,0.00%,187,bsuite is a collection of carefully-designed experiments that investigate core capabilities of a reinforcement learning (RL) agent,"aslanides, iosband, yilei",13,2019-08-02,2020-08-10,0
facebookresearch/LAMA,AI engineering,Evals,1376,0,0.00%,0,0.00%,190, LAnguage Model Analysis,"fabiopetroni, cwenner, darrengarvey",6,2019-03-29,2020-02-18,0
microsoft/torchscale,Model development,Modeling & training,3067,0,0.00%,0,0.00%,215,Foundation Architecture for (M)LLMs,"shumingma, gitnlp, donglixp",15,2022-11-17,2023-12-25,0
Cloud-CV/Fabrik,Model development,Modeling & training,1127,0,0.00%,0,0.00%,238,":factory: Collaboratively build, visualize, and design neural nets in browser","utsavgarg, Ram81, gauravgupta22",44,2016-05-28,2018-12-12,0
databricks/dbrx,Model repo,,2546,0,0.00%,0,0.00%,242,"Code examples and resources for DBRX, a large language model developed by Databricks","bandish-shah, hanlint, asnelling",7,2024-03-26,2024-04-19,0
LC1332/Luotuo-Chinese-LLM,Model repo,Multilingual,3638,0,0.00%,0,0.00%,248,骆驼(Luotuo): Open Sourced Chinese Language Models. Developed by 陈启源 @ 华中师范大学 & 李鲁鲁 @ 商汤科技 & 冷子昂 @ 商汤科技,"LC1332, qychen2001, SirlyDreamer",5,2023-03-21,2023-05-10,0
gofireflyio/aiac,Applications,Coding,3637,0,0.00%,0,0.00%,280,Artificial Intelligence Infrastructure-as-Code Generator.,"ido50, goreleaserbot, eran-infralight",8,2022-12-07,2024-10-29,7484
dvlab-research/LongLoRA,Model repo,,2652,0,0.00%,0,0.00%,283,Code and documents of LongLoRA and LongAlpaca (ICLR 2024 Oral),"yukang2017, naubull2, gianlucamacri",16,2023-09-21,2023-11-19,0
openai/weak-to-strong,Model development,Modeling & training,2524,0,0.00%,0,0.00%,309,,"WuTheFWasThat, AdrienLE, srivhash",10,2023-12-13,2024-01-23,0
google-research/uda,Model development,Dataset engineering,2188,0,0.00%,0,0.00%,313,Unsupervised Data Augmentation (UDA),"michaelpulsewidth, chiragjn, varunnair18",5,2019-06-19,2019-10-25,0
lencx/nofwl,Applications,Bots,4234,0,0.00%,0,0.00%,400,NoFWL Desktop Application,"lencx, RoyalCrafter, razvanazamfirei",10,2023-02-22,2023-05-05,85442
llSourcell/Doctor-Dignity,Model repo,,3864,0,0.00%,0,0.00%,412,"Doctor Dignity is an LLM that can pass the US Medical Licensing Exam. It works offline, it's cross-platform, & your health data stays private.","MasterJH5574, yzh119, tqchen",56,2023-08-06,2023-09-20,0
ricklamers/gpt-code-ui,Applications,Coding,3584,0,0.00%,0,0.00%,446,An open source implementation of OpenAI's ChatGPT Code interpreter,"ricklamers, dasmy, tunayokumus",6,2023-05-10,2023-07-29,0
salesforce/decaNLP,AI engineering,Evals,2348,0,0.00%,0,0.00%,469,The Natural Language Decathlon: A Multitask Challenge for NLP,"bmccann, huangyh1108, daminisatya",9,2018-06-20,2018-06-22,0
OpenGVLab/DragGAN,Model repo,Multimodal,4988,0,0.00%,0,0.00%,488,"Unofficial Implementation of DragGAN - ""Drag Your GAN: Interactive Point-based Manipulation on the Generative Image Manifold"" （DragGAN 全功能实现，在线Demo，本地部署试用，代码、模型已全部开源，支持Windows, macOS, Linux）","Zeqiang-Lai, baydarov42, egbaydarov",11,2023-05-20,2023-07-02,0
SuperDuperDB/superduperdb,Infrastructure,Data management,5021,0,0.00%,0,0.00%,493,Superduper: End-to-end framework for building custom AI applications and agents.,"blythed, kartik4949, rec",43,2022-08-30,2025-03-28,298
Significant-Gravitas/Auto-GPT-Plugins,AI engineering,Agent,3882,0,0.00%,0,0.00%,566,Plugins for Auto-GPT,"ntindle, riensen, sidewaysthought",47,2023-04-09,2023-06-29,0
google-research/albert,Model repo,,3271,0,0.00%,0,0.00%,570,ALBERT: A Lite BERT for Self-supervised Learning of Language Representations,"0x0539, Danny-Google, albert-copybara",14,2019-11-26,2022-04-25,0
openai/gpt-discord-bot,Applications,Bots,1808,0,0.00%,0,0.00%,667,"Example Discord bot written in Python that uses the completions API to have conversations with the `text-davinci-003` model, and the moderations API to filter the messages.","YufeiG, Kav-K, Veylkh",3,2022-12-21,2024-01-09,0
getumbrel/llama-gpt,Applications,Bots,10958,0,0.00%,0,0.00%,715,"A self-hosted, offline, ChatGPT-like chatbot. Powered by Llama 2. 100% private, with no data leaving your device. New: Code Llama support!","mckaywrigley, mayankchhabra, thomasleveil",92,2023-07-22,2023-10-09,0
run-llama/llama-hub,Infrastructure,Data management,3474,0,0.00%,0,0.00%,732,A library of data loaders for LLMs made by the community -- to be used with LlamaIndex and/or LangChain,"jerryjliu, emptycrown, nerdai",100+,2023-02-01,2024-02-13,0
OptimalScale/LMFlow,Model development,Modeling & training,8389,0,0.00%,0,0.00%,833,An Extensible Toolkit for Finetuning and Inference of Large Foundation Models. Large Models for All.,"research4pan, wheresmyhair, shizhediao",35,2023-03-27,2025-03-20,0
interstellard/chatgpt-advanced,Applications,Bots,6491,0,0.00%,0,0.00%,846,WebChatGPT: A browser extension that augments your ChatGPT prompts with web results.,"qunash, thefourcraft, okkidwi",14,2022-12-03,2023-05-08,2657
nashsu/FreeAskInternet,Applications,Info aggregation,8610,0,0.00%,0,0.00%,911,"FreeAskInternet is a completely free, PRIVATE and LOCALLY running search aggregator & answer generate using MULTI LLMs, without GPU needed. The user can ask a question and the system will  make a multi engine search and combine the search result to LLM and generate the answer based on search results. It's all FREE to use. ",nashsu,1,2024-04-05,1000-01-01,0
baidu-research/warp-ctc,Model development,Modeling & training,4077,0,0.00%,0,0.00%,1038,Fast parallel CTC.,"ekelsen, jaredcasper, gangliao",20,2016-01-14,2016-01-14,0
cocktailpeanut/dalai,Applications,Bots,13080,0,0.00%,0,0.00%,1402,The simplest way to run LLaMA on your local machine,"cocktailpeanut, marcuswestin, keldenl",20,2023-03-12,2023-03-22,0
X-PLUG/MobileAgent,Model repo,Multimodal,4003,-1,-0.02%,47,1.17%,398,Mobile-Agent: The Powerful Mobile Device Operation Assistant Family,"junyangwang0410, zhangxi1997, MikeWangWZHL",12,2024-01-26,2025-04-01,0
NVIDIA/TensorRT-LLM,Model development,Inference optimization,10107,-1,-0.01%,76,0.75%,1310,TensorRT-LLM provides users with an easy-to-use Python API to define Large Language Models (LLMs) and build TensorRT engines that contain state-of-the-art optimizations to perform inference efficiently on NVIDIA GPUs. TensorRT-LLM also contains components to create Python and C++ runtimes that execute those TensorRT engines.,"kaiyux, Shixiaowei02, Funatiq",82,2023-08-16,2025-04-01,0
mattzcarey/code-review-gpt,Applications,Coding,1947,-1,-0.05%,9,0.46%,202,"Code review powered by LLMs (OpenAI GPT4, Sonnet 3.5) & Embeddings ⚡️ Improve code quality and catch bugs before you break production 🚀 Lives in your Github/GitLab/Azure DevOps CI","lizacullis, SEBRATHEZEBRA, mattzcarey",35,2023-07-06,2025-02-23,0
kyrolabs/awesome-langchain,Tutorials,,8188,-1,-0.01%,37,0.45%,572,😎 Awesome list of tools and projects with the awesome LangChain framework,"sbusso, MCKevmeister, seawatts",49,2023-02-28,2025-03-05,0
microsoft/windows-ai-studio,AI engineering,AIE framework,1405,-1,-0.07%,5,0.36%,99,,"a1exwang, sffamily, XiaofuHuang",16,2023-12-08,2024-12-12,0
defog-ai/sqlcoder,Applications,Coding,3694,-1,-0.03%,13,0.35%,238,SoTA LLM for converting natural language questions to SQL queries,"rishsriv, wendy-aw, cds-1993",5,2023-08-17,2024-05-23,0
open-mmlab/Amphion,Model development,Modeling & training,8897,-1,-0.01%,29,0.33%,697,"Amphion (/æmˈfaɪən/) is a toolkit for Audio, Music, and Speech Generation. Its purpose is to support reproducible research and help junior researchers and engineers get started in the field of audio, music, and speech generation research and development.","lmxue, HeCheng0625, yuantuo666",31,2023-11-15,2025-01-15,0
roboflow/inference,Infrastructure,Serving,1611,-1,-0.06%,5,0.31%,164,Turn any computer or edge device into a command center for your computer vision projects.,"PawelPeczek-Roboflow, grzegorz-roboflow, probicheaux",62,2023-07-31,2025-04-04,0
microsoft/promptflow,AI engineering,AIE framework,10188,-1,-0.01%,24,0.24%,960,"Build high-quality LLM apps - from prototyping, testing to production deployment and monitoring.","zhengfeiwang, D-W-, brynn-code",96,2023-06-30,2025-03-27,133
LiheYoung/Depth-Anything,Model repo,,7430,-1,-0.01%,15,0.20%,564,[CVPR 2024] Depth Anything: Unleashing the Power of Large-Scale Unlabeled Data. Foundation Model for Monocular Depth Estimation,"LiheYoung, 1ssb",2,2024-01-22,2024-07-07,0
Trusted-AI/adversarial-robustness-toolbox,AI engineering,Evals,5169,-1,-0.02%,10,0.19%,1200,"Adversarial Robustness Toolbox (ART) - Python Library for Machine Learning Security - Evasion, Poisoning, Extraction, Inference - Red and Blue Teams","beat-buesser, minhitbk, killianlevacher",100+,2018-03-15,2022-05-19,0
replicate/cog,Infrastructure,Toolings,8523,-1,-0.01%,16,0.19%,595,Containers for machine learning,"bfirsh, andreasjansson, nickstenning",66,2021-02-26,2025-04-04,56284
salesforce/ALBEF,Model development,Modeling & training,1627,-1,-0.06%,3,0.18%,205,Code for ALBEF: a new vision-language pre-training method,"LiJunnan1992, chenxwh, svc-scm",3,2021-07-13,2022-03-21,0
salesforce/WikiSQL,Model development,Dataset engineering,1713,-1,-0.06%,3,0.18%,326,A large annotated semantic parsing corpus for developing natural language interfaces.,"vzhong, sk210892, todpole3",20,2017-08-11,2023-03-23,0
open-mmlab/mmagic,Model development,Modeling & training,7125,-1,-0.01%,11,0.15%,1077,"OpenMMLab Multimodal Advanced, Generative, and Intelligent Creation Toolbox. Unlock the magic 🪄: Generative-AI (AIGC), easy-to-use APIs, awsome model zoo, diffusion models, for text-to-image generation, image/video restoration/enhancement, etc.","Yshuo-Li, hejm37, Z-Fran",100+,2019-08-23,2023-12-15,0
facebookresearch/fairscale,Model development,Modeling & training,3290,-1,-0.03%,5,0.15%,287,PyTorch extensions for high performance and large scale training.,"blefaudeux, min-xu-ai, msbaines",68,2020-07-07,2025-01-12,0
divamgupta/diffusionbee-stable-diffusion-ui,Applications,Image production,13143,-1,-0.01%,18,0.14%,664,Diffusion Bee is the easiest way to run Stable Diffusion locally on your M1 Mac. Comes with a one-click installer. No dependencies or technical knowledge needed.,"divamgupta, AEtheve, CrudeDiatribe",17,2022-09-06,2023-12-28,735216
gragland/chatgpt-chrome-extension,Applications,Bots,2943,-1,-0.03%,4,0.14%,507,A ChatGPT Chrome extension. Integrates ChatGPT into every text box on the internet.,"gragland, josephvtesta, Cadienvan",6,2022-12-04,2023-02-11,0
microsoft/promptbase,Tutorials,Prompt engineering,5590,-1,-0.02%,7,0.13%,304,All things prompt engineering,"riedgar-ms, Harsha-Nori, microsoftopensource",7,2023-12-12,2024-06-04,0
S-LoRA/S-LoRA,Infrastructure,Serving,1812,-1,-0.06%,2,0.11%,107,S-LoRA: Serving Thousands of Concurrent LoRA Adapters,"Ying1123, merrymercy, caoshiyi",5,2023-11-05,2024-01-04,0
google-deepmind/deepmind-research,Model repo,Agent,13675,-1,-0.01%,15,0.11%,2654,This repository contains implementations and illustrative code to accompany DeepMind publications,"derpson, Augustin-Zidek, alimuldal",73,2019-01-15,1000-01-01,0
godly-devotion/MochiDiffusion,Applications,Image production,7655,-1,-0.01%,8,0.10%,354,Run Stable Diffusion on Mac natively,"godly-devotion, vzsg, gdbing",22,2022-12-15,2024-10-28,55065
sebastianstarke/AI4Animation,AI engineering,AI interface,8070,-1,-0.01%,8,0.10%,1075,Bringing Characters to Life with Computer Brains in Unity,"sebastianstarke, paulstarke, doctorpangloss",3,2017-07-26,2023-06-28,0
KillianLucas/open-interpreter,Applications,Workflow automation,59016,-1,-0.00%,57,0.10%,5026,A natural language interface for computers,"KillianLucas, Notnaton, MikeBirdTech",100+,2023-07-14,2025-03-30,41
ricklamers/shell-ai,Applications,Coding,1092,-1,-0.09%,1,0.09%,72,LangChain powered shell command generator and runner CLI,"ricklamers, ksfi, Elijas",9,2023-08-20,2023-11-17,0
Baiyuetribe/paper2gui,Applications,Image production,10516,-1,-0.01%,8,0.08%,870,Convert AI papers to GUI，Make it easy and convenient for everyone to use artificial intelligence technology。让每个人都简单方便的使用前沿人工智能技术,"Baiyuetribe, KTK27YT, pengzhendong",3,2021-10-10,2022-09-11,33161
LargeWorldModel/LWM,Model repo,,7266,-1,-0.01%,5,0.07%,556,Large World Model -- Modeling Text and Video with Millions Context,"wilson1yan, haoliuhl, eltociear",3,2024-02-08,2024-02-21,0
deep-floyd/IF,Model repo,Multimodal,7778,-1,-0.01%,5,0.06%,510,,"shonenkov, zeroshot-ai, apolinario",8,2023-01-20,2023-06-02,0
kaixindelele/ChatPaper,Applications,Info aggregation,18827,-1,-0.01%,10,0.05%,1952,Use ChatGPT to summarize the arXiv papers. 全流程加速科研，利用chatgpt进行论文全文总结+专业翻译+润色+审稿+审稿回复,"kaixindelele, WangRongsheng, MrPeterJin",13,2023-03-08,2023-08-13,0
lucidrains/DALLE2-pytorch,Model repo,,11252,-1,-0.01%,5,0.04%,1092,"Implementation of DALL-E 2, OpenAI's updated text-to-image synthesis neural network,  in Pytorch","lucidrains, nousr, Veldrovive",17,2022-04-07,2023-10-04,0
magic-research/magic-animate,Model repo,Multimodal,10720,-1,-0.01%,4,0.04%,1095,[CVPR 2024] MagicAnimate: Temporally Consistent Human Image Animation using Diffusion Model,zcxu-eric,1,2023-11-21,1000-01-01,0
PromtEngineer/localGPT,Applications,Info aggregation,20433,-1,-0.00%,7,0.03%,2265,Chat with your documents on your local device using GPT models. No data leaves your device and 100% private. ,"PromtEngineer, imjwang, siddhivelankar23",45,2023-05-24,2025-03-02,0
fauxpilot/fauxpilot,Applications,Coding,14691,-1,-0.01%,4,0.03%,630,FauxPilot - an open-source alternative to GitHub Copilot server,"moyix, fdegier, leemgs",13,2022-08-03,2024-02-07,0
mckaywrigley/ai-code-translator,Applications,Coding,4043,-1,-0.02%,1,0.02%,596,Use AI to translate code from one language to another.,"mckaywrigley, freestatman, dreamYiZ",7,2023-03-31,2023-04-05,0
Coframe/coffee,Applications,Coding,1488,-1,-0.07%,0,0.00%,69,Build and iterate on your UI 10x faster with AI - right from your own IDE ☕️,"joshpxyne, tunahfishy, 1um",8,2023-11-04,1000-01-01,0
uptrain-ai/uptrain,Infrastructure,Monitoring,2258,-1,-0.04%,0,0.00%,199,"UpTrain is an open-source unified platform to evaluate and improve Generative AI applications. We provide grades for 20+ preconfigured checks (covering language, code, embedding use-cases), perform root cause analysis on failure cases and give insights on how to resolve them.","sourabhagr, Dominastorm, vipgupta",38,2022-11-07,2024-07-29,0
stochasticai/xTuring,Model development,Modeling & training,2640,-1,-0.04%,0,0.00%,206,"Build, customize and control you own LLMs. From data pre-processing to fine-tuning, xTuring provides an easy way to personalize open-source LLMs. Join our discord community: https://discord.gg/TgHXuSJEk6","StochasticRomanAgeev, tushar2407, sarthaklangde",18,2023-03-19,2024-09-23,0
madawei2699/myGPTReader,Applications,Info aggregation,4442,-1,-0.02%,0,0.00%,450,A community-driven way to read and chat with AI bots - powered by chatGPT.,"madawei2699, lycfyi, ImgBotApp",8,2023-03-08,2024-04-24,0
gpt-engineer-org/gpt-engineer,Applications,Coding,53781,-2,-0.00%,93,0.17%,7048,CLI platform to experiment with codegen. Precursor to: https://lovable.dev,"ATheorell, AntonOsika, similato87",100+,2023-04-29,2024-11-17,0
google-deepmind/graphcast,Model development,Modeling & training,5950,-2,-0.03%,8,0.13%,738,,"andrewlkd, alvarosg, voctav",8,2023-07-14,2024-01-03,0
openai-translator/openai-translator,Applications,Workflow automation,24314,-2,-0.01%,17,0.07%,1779,基于 ChatGPT API 的划词翻译浏览器插件和跨平台桌面端应用    -    Browser extension and cross-platform desktop application for translation based on ChatGPT API.,"yetone, maltoze, OverflowCat",90,2023-03-04,2024-11-16,1467877
josStorer/chatGPTBox,Applications,Info aggregation,10458,-2,-0.02%,7,0.07%,799,"Integrating ChatGPT into your browser deeply, everything you need is here","josStorer, PeterDaveHello, okkidwi",39,2023-03-15,2024-12-14,36608
xcanwin/KeepChatGPT,Applications,Bots,14803,-2,-0.01%,5,0.03%,731,这是一款提高ChatGPT的数据安全能力和效率的插件。并且免费共享大量创新功能，如：自动刷新、保持活跃、数据安全、取消审计、克隆对话、言无不尽、净化页面、展示大屏、拦截跟踪、日新月异、明察秋毫等。让我们的AI体验无比安全、顺畅、丝滑、高效、简洁。,"xcanwin, Ezra-Chan, neoOpus",12,2023-03-28,2024-05-03,0
HumanAIGC/EMO,Model repo,,7612,,,1,0.01%,929,Emote Portrait Alive: Generating Expressive Portrait Videos with Audio2Video Diffusion Model under Weak Conditions,lucaskingjade,1,2024-02-27,1000-01-01,0
IBM/Dromedary,Model repo,,1140,,,0,0.00%,91,"Dromedary: towards helpful, ethical and reliable LLMs.","Edward-Sun, codeaimcts, dchichkov",4,2023-05-03,2023-05-23,0
SamurAIGPT/Camel-AutoGPT,Applications,Bots,1176,,,0,0.00%,113,"🚀 Introducing 🐪 CAMEL: a game-changing role-playing approach for LLMs and auto-agents like BabyAGI & AutoGPT! Watch two agents 🤝 collaborate and solve tasks together, unlocking endless possibilities in #ConversationalAI, 🎮 gaming, 📚 education, and more! 🔥","Anil-matcha, vadootvpeer",2,2023-04-21,1000-01-01,0
ItsPi3141/alpaca-electron,Applications,Bots,1308,,,0,0.00%,147,The simplest way to run Alpaca (and other LLaMA-based local LLMs) on your own computer,"ItsPi3141, W48B1T, 8bit-coder",7,2023-03-24,2024-04-04,39955
santinic/how2,Applications,Coding,5745,,,0,0.00%,151,AI for the Command Line,"santinic, PaoloCifariello, danyshaanan",13,2016-02-13,2022-09-01,1596
rustformers/llm,AI engineering,AIE framework,6100,,,0,0.00%,369,"[Unmaintained, see README] An ecosystem of Rust libraries for working with large language models","philpax, LLukas22, danforbes",46,2023-03-13,2023-12-12,889
ymcui/Chinese-LLaMA-Alpaca-2,Model repo,Multilingual,7156,,,0,0.00%,575,中文LLaMA-2 & Alpaca-2大模型二期项目 + 64K超长上下文模型 (Chinese LLaMA-2 & Alpaca-2 LLMs with 64K long context models),"ymcui, iMountTai, airaria",8,2023-07-18,2023-12-27,0