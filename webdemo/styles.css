:root {
    --primary-color: #6366f1;
    --primary-light: #818cf8;
    --primary-dark: #4f46e5;
    --secondary-color: #10b981;
    --accent-color: #f97316;
    --dark-color: #1e293b;
    --light-color: #f8fafc;
    --gray-color: #64748b;
    --gray-light: #e2e8f0;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --radius: 8px;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background-color: #f9fafb;
    color: var(--dark-color);
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 60px 0 40px;
    text-align: center;
    box-shadow: var(--shadow);
    margin-bottom: 30px;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* 统一容器样式 */
.unified-container {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 搜索容器 */
.search-container {
    padding: 1.25rem 1.5rem;
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
    background-color: #f8f9fa;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    border-bottom: 1px solid #e9ecef;
}

.search-box {
    flex: 1;
    position: relative;
    min-width: 200px;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border-radius: 4px;
    border: 1px solid #ddd;
    font-size: 1rem;
    outline: none;
    transition: all 0.3s;
}

.search-box input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #777;
}

.filter-sort-container {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.filter-dropdown select,
.sort-dropdown select {
    padding: 0.75rem;
    border-radius: 4px;
    border: 1px solid #ddd;
    background-color: white;
    font-size: 0.9rem;
    min-width: 150px;
    outline: none;
    cursor: pointer;
    transition: all 0.3s;
}

.filter-dropdown select:focus,
.sort-dropdown select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* 分类Tab导航 */
.category-tabs {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.tabs-container {
    display: flex;
    flex-wrap: wrap;
}

.tab-btn {
    padding: 1rem 1.5rem;
    border: none;
    background: transparent;
    font-size: 1rem;
    font-weight: 500;
    color: #555;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
    outline: none;
}

.tab-btn:hover {
    color: var(--primary-color);
}

.tab-btn.active {
    color: var(--primary-color);
    font-weight: bold;
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 3px 3px 0 0;
}

.tab-btn i {
    margin-right: 0.5rem;
}

/* 内容区域 */
.content-area {
    padding: 1.5rem;
    background-color: white;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

/* 将星标改为黄色 */
.project-meta .stars {
    display: flex;
    align-items: center;
    margin-right: 1rem;
    color: #555;
}

.project-meta .stars i {
    color: #FFD700; /* 黄色星标 */
    margin-right: 0.25rem;
}

/* 分类显示控制 */
.category {
    display: none;
}

.category.active {
    display: block;
}

.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: var(--light-color);
    border-bottom: 1px solid var(--gray-light);
    cursor: pointer;
}

.category-header h2 {
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.category-header h2 i {
    color: var(--primary-color);
}

.expand-btn {
    background: none;
    border: none;
    color: var(--gray-color);
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition);
}

.expand-btn:hover {
    color: var(--primary-color);
}

.subcategories {
    padding: 20px;
    max-height: none;
    overflow: visible;
}

.category.expanded .subcategories {
    max-height: 2000px;
    padding: 20px;
}

.category.expanded .expand-btn i {
    transform: rotate(180deg);
}

.subcategory {
    background-color: white;
    border-radius: var(--radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--gray-light);
    max-height: none;
    overflow-y: visible;
}

.subcategory-info {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 10px;
    gap: 10px;
}

.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-left: 8px;
}

.tag {
    padding: 2px 10px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    color: white;
    display: inline-flex;
    align-items: center;
}

.tag-capability {
    background-color: #ec4899; /* 粉色 */
}

.tag-dimension {
    background-color: #8b5cf6; /* 紫色 */
}

.evaluation-suggestion {
    background-color: #f8fafc;
    border-left: 3px solid var(--primary-color);
    padding: 10px 15px;
    margin: 10px 0 15px;
    font-size: 0.9rem;
    color: var(--gray-color);
    border-radius: 0 var(--radius) var(--radius) 0;
}

.evaluation-suggestion strong {
    color: var(--primary-dark);
}

/* 美化滚动条 */
.subcategory::-webkit-scrollbar {
    width: 8px;
}

.subcategory::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.subcategory::-webkit-scrollbar-thumb {
    background: #c0c0c0;
    border-radius: 10px;
}

.subcategory::-webkit-scrollbar-thumb:hover {
    background: #a0a0a0;
}

/* 项目容器 */
.projects-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
    margin-top: 1.5rem;
}

/* 项目卡片 */
.project-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    padding: 1.25rem;
    transition: all 0.3s ease;
}

.project-card:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-3px);
}

.project-name {
    font-size: 1.1rem;
    margin-top: 0;
    margin-bottom: 0.5rem;
}

.project-name a {
    color: var(--primary-color);
    text-decoration: none;
}

.project-name a:hover {
    text-decoration: underline;
}

.project-description {
    font-size: 0.9rem;
    color: #555;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.project-meta {
    display: flex;
    font-size: 0.85rem;
    color: #666;
}

.project-footer {
    margin-top: auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: var(--gray-color);
}

.project-stars {
    display: flex;
    align-items: center;
    gap: 5px;
}

.project-stars i {
    color: #f59e0b;
}

.project-language {
    display: flex;
    align-items: center;
    gap: 5px;
}

.language-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--accent-color);
}

footer {
    background-color: var(--dark-color);
    color: white;
    padding: 30px 0;
    text-align: center;
    margin-top: 50px;
}

.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 45px;
    height: 45px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow);
    opacity: 0;
    visibility: hidden;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background-color: var(--primary-dark);
    transform: translateY(-3px);
}

/* 主内容区域样式调整 */
main.container {
    background-color: #ffffff;
    border-radius: 0 0 var(--radius) var(--radius);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    padding-top: 15px;
}

.subcategory h3 {
    color: var(--primary-dark);
    margin-bottom: 15px;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.difficulty-tag {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 500;
    color: white;
    height: 28px;
}

.easy {
    background-color: #10b981;
}

.easy-medium {
    background-color: #60a5fa;
}

.medium {
    background-color: #3b82f6;
}

.medium-hard {
    background-color: #f59e0b;
}

.hard {
    background-color: #ef4444;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .search-container {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box, .filter-container, .sort-container {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .subcategory {
        max-height: 500px;
    }
    
    .subcategory-info {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .tags-container {
        margin-left: 0;
        margin-top: 5px;
    }
    
    .tabs-container {
        flex-wrap: wrap;
    }
    
    .tab-btn {
        flex: 1 0 auto;
        padding: 8px 12px;
        margin: 3px;
        font-size: 0.9rem;
    }
} 