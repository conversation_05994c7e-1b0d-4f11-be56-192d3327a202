#!/usr/bin/env python3
"""
DSPy智能Prompt优化器

基于现有的教学例子生成prompt和评估标准，构建自动优化机制
专注于创造"醍醐灌顶"的学习体验，适应各个领域和目标受众
"""

import json
from pathlib import Path
from typing import Any

import dspy
from loguru import logger


class TeachingAdapter(dspy.Adapter):
    """专门用于教学例子生成的DSPy适配器"""

    def __call__(self, lm, lm_kwargs, signature, demos, inputs):
        """调用LLM并返回解析后的结果"""
        system_content = signature.instructions

        # 如果有示例，添加到系统消息中
        if demos:
            system_content += "\n\n" + self._format_demos(demos)

        messages = [
            {"role": "system", "content": system_content},
            {"role": "user", "content": inputs["prompt"]},
        ]

        outputs = lm(messages=messages, **lm_kwargs)
        return [{"generation": outputs[0]}]

    def _format_demos(self, demos):
        """格式化高质量教学例子示例"""
        parts = ["## 高质量教学例子示例", "以下是能够创造'醍醐灌顶'体验的优秀例子：", ""]

        for i, demo in enumerate(demos, 1):
            parts += [
                f"### 示例 {i}",
                f"**用户需求**: {demo['prompt']}",
                "",
                "**优秀回答**:",
                demo["generation"],
                "",
                "---",
                "",
            ]

        return "\n".join(parts)


class CustomPredict(dspy.Predict):
    """使用教学适配器的Predict类"""

    def forward(self, **kwargs):
        adapter = TeachingAdapter()
        with dspy.settings.context(adapter=adapter):
            return super().forward(**kwargs)


class TeachingQualityJudge(dspy.Signature):
    """教学例子质量评估专家 - 基于prompt_battle_tool的评估标准，增加适配性评估"""

    """你是一位资深的教学设计评估专家，专门评估教学例子的质量。

    请根据以下四个维度评估教学例子的质量（总分100分）：

    ### 1. 例子具体性与数据完整性（30分）
    - **数据具体性**（12分）：每个步骤是否包含详细的具体数据（如具体数值、矩阵、向量必须包含具体数字），关键数据是否省略？
    - **步骤详实性**（10分）：每个步骤的操作是否具体明确，有没有遗漏关键操作？
    - **量化表达**（8分）：结果是否量化，有具体的测量指标或数值？

    ### 2. 动画描述质量与连贯性（30分）
    - **关键变化描述**（12分）：关键元素的位置、颜色、大小变化是否详细描述？是否缺少关键变化元素？
    - **Manim适配性**（10分）：动画描述是否适合Manim生成，元素变化是否可编程实现？
    - **整体连贯性**（8分）：动画步骤之间是否连贯，有无跳跃过大的变化？

    ### 3. 讲解完整性与本质揭示（25分）
    - **概念本质描述**（12分）：是否直接了当地揭示概念本质，避免绕弯子？
    - **讲解完整性**（8分）：是否完整覆盖核心概念，逻辑是否清晰？
    - **原理阐述深度**（5分）：是否深入解释为什么这样做，机制是否清楚？

    ### 4. 目标适配性与教学效果（15分）
    - **受众适配性**（8分）：例子的难度、语言风格、类比选择是否适合目标受众的知识背景和理解能力？
    - **目的契合度**（7分）：例子是否紧密围绕教学目的，能否有效达成预期的学习目标？

    特别关注：
    - 是否能创造"Aha! Moment"和"醍醐灌顶"的学习体验
    - 是否遵循"洞察 > 解释"的原则，让学习者自己发现和顿悟
    - 类比是否生动、本土化，符合中国人的思维方式

    请给出0-100的整数评分。
    """

    topic = dspy.InputField(desc="教学主题")
    purpose = dspy.InputField(desc="教学目的")
    target_audience = dspy.InputField(desc="目标受众")
    example_content = dspy.InputField(desc="生成的教学例子内容")
    detailed_feedback = dspy.OutputField(desc="详细的评估反馈，包含各维度分析")
    score: int = dspy.OutputField(desc="0-100的整数评分")


class DSPyPromptOptimizer:
    """DSPy智能Prompt优化器"""

    def __init__(self):
        """初始化优化器"""
        self.setup_dspy()
        self.quality_judge = dspy.Predict(TeachingQualityJudge)

        # 输出目录
        self.output_dir = Path("output/dspy_optimization")
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 设置日志
        logger.add("logs/dspy_optimizer.log", rotation="1 day", retention="30 days", level="INFO")

    def setup_dspy(self):
        """设置DSPy语言模型"""
        try:
            # 使用配置的API
            dspy_lm = dspy.LM(
                model="openrouter/gemini-2.5-pro",
                api_base="https://jeniya.cn/v1",
                api_key="sk-k2GsBDwwE1JREL45OiSZP31A7Io481ZhMBB55dJoktYCYXo1",
                max_tokens=65535,
            )
            dspy.configure(lm=dspy_lm)
            logger.info("DSPy配置完成")

        except Exception as e:
            logger.error(f"DSPy配置失败: {e}")
            raise

    def load_base_prompt_template(self) -> str:
        """加载并转换基础prompt模板为DSPy格式"""
        try:
            with open("prompts/example_gen_prompt.py", encoding="utf-8") as f:
                content = f.read()

            # 提取prompt变量
            if 'prompt = """' in content:
                start = content.find('prompt = """') + len('prompt = """')
                end = content.rfind('"""')
                original_prompt = content[start:end].strip()
            else:
                original_prompt = content

            # 转换变量格式：{主题} -> {topic}, {目的} -> {purpose}, {目标群体} -> {audience}
            # 同时添加DSPy需要的输入说明
            dspy_prompt = self._convert_to_dspy_format(original_prompt)

            logger.info("成功加载并转换原始prompt模板")
            return dspy_prompt

        except Exception as e:
            logger.error(f"加载基础prompt模板失败: {e}")
            # 返回简化版本
            return """你是一位首席教育体验设计师，专门创造"Aha! Moment"的学习体验。

根据用户提供的主题、目的和目标群体，生成一个高质量的教学例子。

要求：
1. 设计能引发"醍醐灌顶"的认知跃迁体验
2. 包含具体的数据和详细步骤
3. 适合制作成Manim动画
4. 揭示概念的本质，避免平铺直叙"""

    def _convert_to_dspy_format(self, original_prompt: str) -> str:
        """将原始prompt转换为DSPy兼容格式"""

        # 替换变量格式
        dspy_prompt = original_prompt.replace("{主题}", "{topic}")
        dspy_prompt = dspy_prompt.replace("{目的}", "{purpose}")
        dspy_prompt = dspy_prompt.replace("{目标群体}", "{audience}")

        # 在prompt开头添加DSPy输入说明
        dspy_header = """根据用户输入的主题(topic)、目的(purpose)和目标群体(audience)，生成高质量的教学例子。

用户输入格式：请为主题'[topic]'生成一个教学例子，目的是[purpose]，面向[audience]。

"""

        # 组合完整的DSPy prompt
        full_prompt = dspy_header + dspy_prompt

        return full_prompt

    def create_training_examples(self) -> list[dspy.Example]:
        """创建高质量的训练例子 - 参考原始prompt模板的结构和质量"""
        logger.info("创建高质量训练例子")

        # 按照原始prompt模板标准创建的高质量教学例子
        training_data = [
            {
                "topic": "深度学习中的梯度下降",
                "purpose": "帮助机器学习初学者理解优化算法的核心思想",
                "audience": "刚接触机器学习的程序员",
                "example": """<SCENE_OUTLINE>
## 分镜0: 全局设定与开场
**全局视觉设定**: 损失函数用3D山峰表示(红色高，蓝色低)，参数用小球表示，梯度用箭头表示，学习率用步长大小表示
**核心洞见**: 梯度下降就是一个盲人下山的过程——感受脚下坡度，选择最陡方向，小步前进
**教学类比**: 盲人登山者在浓雾中下山找最低点，只能通过脚感判断坡度方向。避免误导：强调是"感受局部坡度"而非"看到全局"
**旁白**: 想象你是一个登山者，在浓雾弥漫的山顶迷失了方向。你看不到山的全貌，只能感受脚下的坡度。你的目标是找到山脚的最低点。这就是梯度下降算法面临的挑战。
</SCENE_OUTLINE>

<SCENE_OUTLINE>
## 分镜1: 快速建立直觉 (The Intuition) - 盲人下山
**核心目标**: 通过盲人下山的类比，让观众直观理解梯度下降的核心思想
**场景**: 3D山峰模型，一个小球(代表参数)位于山顶某处，周围浓雾环绕
**核心冲突**: 小球需要到达最低点，但无法看到全局，只能感受当前位置的坡度
**核心视觉变换**: 小球脚下出现坡度箭头(梯度)，指向最陡下降方向，小球沿箭头方向移动一小步，到达新位置后重新感受坡度，箭头方向更新，如此反复
**旁白**: 就像盲人下山一样，算法感受当前的"坡度"，选择最陡的下降方向，迈出一小步，然后重新评估。
</SCENE_OUTLINE>

<SCENE_OUTLINE>
## 分镜2: 演示原理 (How & What) - 数学机制详解

### 步骤1：计算梯度(感受坡度)
**旁白**: 在当前位置，算法计算损失函数的梯度，就像用脚感受坡度
**数据**: 当前参数 θ = [2.5, 1.8]，损失值 L = 4.2，梯度 ∇L = [1.2, -0.8]
**操作**: 计算偏导数，得到每个参数方向的斜率
**结果**: 梯度向量指向损失增加最快的方向
**核心视觉变换**: 在3D表面上，当前点处出现红色箭头(梯度)，箭头长度表示坡度陡峭程度，方向指向上坡

### 步骤2：确定下降方向
**旁白**: 我们要下山，所以选择梯度的反方向
**数据**: 下降方向 = -∇L = [-1.2, 0.8]
**操作**: 将梯度向量取反
**结果**: 蓝色箭头指向损失减少最快的方向
**核心视觉变换**: 红色箭头翻转180度变成蓝色箭头，指向下坡方向

### 步骤3：迈出一步(参数更新)
**旁白**: 沿着下降方向迈出一小步，步长由学习率控制
**数据**: 学习率 α = 0.1，新参数 θ_new = θ - α∇L = [2.5, 1.8] - 0.1×[1.2, -0.8] = [2.38, 1.88]
**操作**: θ_new = θ - α∇L
**结果**: 参数更新，损失从4.2降到3.7
**核心视觉变换**: 小球沿蓝色箭头移动，留下轨迹，到达新位置，损失值数字从4.2变为3.7

### 步骤4：重复迭代
**旁白**: 在新位置重新计算梯度，继续下降
**数据**: 新梯度 ∇L_new = [0.9, -0.6]，继续更新...
**操作**: 重复步骤1-3
**结果**: 逐步接近最优解
**核心视觉变换**: 小球在山峰上画出螺旋下降的轨迹，每次停顿都重新计算箭头方向
</SCENE_OUTLINE>

<SCENE_OUTLINE>
## 分镜3: 关联应用 (Where)
**旁白**: 这个下山过程在机器学习中无处不在：训练神经网络、线性回归、逻辑回归都在使用
**核心视觉变换**: 山峰变形为神经网络图标，然后变为回归曲线，最后变为分类边界，展示梯度下降的广泛应用
</SCENE_OUTLINE>

<SCENE_OUTLINE>
## 分镜4: 探讨边界 (When & When not)
**旁白**: 但盲人下山也有局限性——可能陷入局部最低点
**典型陷阱**: 复杂地形中的局部最小值
**数据(陷阱)**: 多峰函数，小球陷入局部最小值，损失=1.5，而全局最小值损失=0.2
**结果(陷阱)**: 算法收敛到次优解，无法找到真正的最优解
**改进方案**: 随机梯度下降、动量法、Adam优化器等改进算法
**核心视觉变换**: 山峰出现多个凹陷，小球掉入较浅的坑中停止，而更深的坑在远处闪烁，然后展示改进算法如何跳出局部最优
</SCENE_OUTLINE>

<SCENE_OUTLINE>
## 分镜5: 总结升华 (Recap)
**旁白**: 梯度下降的智慧在于：即使看不到全局，通过感受局部信息和持续迭代，也能找到接近最优的解
**核心视觉变换**: 整个下山过程快速回放，从迷雾中的山顶到清晰的山脚，小球的轨迹形成一条智慧之路，最后轨迹发光并定格
</SCENE_OUTLINE>""",
            },
            {
                "topic": "量子纠缠现象",
                "purpose": "让物理爱好者理解量子世界的神奇关联",
                "audience": "对量子物理感兴趣的大学生",
                "example": """<SCENE_OUTLINE>
## 分镜0: 全局设定与开场
**全局视觉设定**: 量子粒子用发光小球表示，纠缠态用连接线表示，测量用探测器表示，概率用颜色深浅表示(深蓝=高概率，浅蓝=低概率)
**核心洞见**: 量子纠缠是两个粒子共享同一个"命运"——测量一个瞬间决定另一个的状态，无论距离多远
**教学类比**: 一对神奇的硬币，无论分开多远，翻转其中一个，另一个会瞬间显示相反面。避免误导：强调这不是信息传递，而是关联性的体现
**旁白**: 想象有一对神奇的硬币，无论你把它们分开多远，当你翻转其中一个时，另一个会瞬间显示相反的面。这就是量子纠缠——爱因斯坦称之为"幽灵般的超距作用"。
</SCENE_OUTLINE>

<SCENE_OUTLINE>
## 分镜1: 快速建立直觉 (The Intuition) - 神奇硬币
**核心目标**: 通过神奇硬币类比，让观众感受量子纠缠的神奇关联性
**场景**: 两枚发光的硬币，中间有一条能量连接线，分别位于屏幕两端
**核心冲突**: 经典物理认为物体状态是确定的，但量子世界中两个粒子可以共享不确定的状态
**核心视觉变换**: 左边硬币开始旋转(叠加态)，右边硬币同步旋转，连接线闪烁。当左边硬币停止并显示正面时，右边硬币瞬间停止并显示反面，连接线发出亮光
**旁白**: 在量子世界中，这两个粒子就像一对神奇的硬币，它们共享着同一个"命运"。
</SCENE_OUTLINE>

<SCENE_OUTLINE>
## 分镜2: 演示原理 (How & What) - 纠缠态的形成与测量

### 步骤1：创建纠缠态
**旁白**: 两个粒子通过特殊的相互作用形成纠缠态
**数据**: 初始状态：粒子A和B都处于叠加态 |ψ⟩ = (1/√2)(|↑↓⟩ + |↓↑⟩)
**操作**: 通过激光脉冲或其他量子操作创建纠缠
**结果**: 两粒子形成不可分离的量子态
**核心视觉变换**: 两个独立的小球靠近，发生碰撞，产生光芒，然后被一条量子连接线连接，开始同步振动

### 步骤2：分离纠缠粒子
**旁白**: 将纠缠的粒子分开，但它们的关联性保持不变
**数据**: 粒子A移动到北京，粒子B移动到上海，距离1000公里
**操作**: 物理分离但保持量子关联
**结果**: 空间分离但量子态仍然纠缠
**核心视觉变换**: 两个小球沿相反方向飞出，连接线拉长但不断裂，背景显示地图上北京和上海的位置

### 步骤3：测量第一个粒子
**旁白**: 在北京测量粒子A的自旋方向
**数据**: 测量结果：粒子A自旋向上(↑)，概率50%
**操作**: 使用探测器测量自旋
**结果**: 粒子A坍缩到确定状态
**核心视觉变换**: 北京的探测器发出光束照射粒子A，粒子A停止振动，显示向上箭头，连接线剧烈闪烁

### 步骤4：瞬间关联效应
**旁白**: 在同一瞬间，上海的粒子B自动变为相反状态
**数据**: 粒子B瞬间变为自旋向下(↓)，无需测量
**操作**: 无需任何操作，自动关联
**结果**: 两粒子状态完全反关联
**核心视觉变换**: 连接线的闪光瞬间传播到上海，粒子B立即停止振动并显示向下箭头，整个过程用慢镜头展示"瞬间性"
</SCENE_OUTLINE>

<SCENE_OUTLINE>
## 分镜3: 关联应用 (Where)
**旁白**: 量子纠缠是量子计算、量子通信、量子密码学的基础
**核心视觉变换**: 纠缠粒子变形为量子计算机芯片，然后变为通信卫星，最后变为密码锁，展示量子纠缠的应用前景
</SCENE_OUTLINE>

<SCENE_OUTLINE>
## 分镜4: 探讨边界 (When & When not)
**旁白**: 但量子纠缠也有局限性——它不能用来传递信息
**典型陷阱**: 误以为可以通过纠缠进行超光速通信
**数据(陷阱)**: 测量结果是随机的，无法控制得到↑还是↓
**结果(陷阱)**: 无法编码信息，只能观察到关联性
**改进方案**: 量子隐形传态等协议可以传递量子态信息
**核心视觉变换**: 显示尝试用纠缠发送信息的失败过程：随机的测量结果无法构成有意义的信息，然后展示正确的量子通信协议
</SCENE_OUTLINE>

<SCENE_OUTLINE>
## 分镜5: 总结升华 (Recap)
**旁白**: 量子纠缠揭示了宇宙最深层的秘密——万物皆有关联，即使在最微观的尺度上
**核心视觉变换**: 从两个纠缠粒子开始，逐渐扩展到整个量子网络，最后形成宇宙中无数关联的量子系统，象征着量子世界的神奇关联性
</SCENE_OUTLINE>""",
            },
        ]

        # 转换为DSPy格式
        examples = []
        for item in training_data:
            # 构建输入prompt - 使用新的格式
            input_prompt = (
                f"请为主题'{item['topic']}'生成一个教学例子，目的是{item['purpose']}，面向{item['audience']}。"
            )

            example = dspy.Example(
                prompt=input_prompt,
                generation=item["example"],
                topic=item["topic"],
                purpose=item["purpose"],
                target_audience=item["audience"],
            )
            examples.append(example)

        # 转换为训练集
        trainset = [ex.with_inputs("prompt") for ex in examples]

        logger.info(f"创建了{len(trainset)}个训练例子")
        return trainset

    def create_quality_metric(self):
        """创建基于现有评估标准的质量指标"""

        def teaching_quality_metric(example, prediction, trace=None):
            """
            评估教学例子的质量
            基于prompt_battle_tool的三维评估标准
            """
            try:
                # 提取信息
                topic = getattr(example, "topic", "未知主题")
                purpose = getattr(example, "purpose", "教学目的")
                target_audience = getattr(example, "target_audience", "学习者")
                generated_content = prediction.get("generation", "")

                if not generated_content.strip():
                    return 0.0

                # 使用LLM评估质量
                result = self.quality_judge(
                    topic=topic, purpose=purpose, target_audience=target_audience, example_content=generated_content
                )

                # 转换为0-1范围
                score = float(result.score) / 100.0

                logger.info(f"质量评估 - 主题: {topic}, 分数: {result.score}/100")
                logger.debug(f"详细反馈: {result.detailed_feedback}")

                return max(0.0, min(1.0, score))

            except Exception as e:
                logger.warning(f"质量评估失败: {e}")
                return 0.0

        return teaching_quality_metric

    def optimize_prompt(self, base_prompt: str = None, max_few_shots: int = 2, optimizer_type: str = "MIPROv2") -> str:
        """
        优化教学prompt

        Args:
            base_prompt: 基础prompt模板
            max_few_shots: 最大few-shot示例数量
            optimizer_type: 优化器类型

        Returns:
            优化后的prompt
        """
        logger.info(f"开始使用{optimizer_type}优化教学prompt")

        # 加载基础prompt
        if base_prompt is None:
            base_prompt = self.load_base_prompt_template()
        logger.info(f"基础prompt:\n{base_prompt}")

        # 构建训练集
        trainset = self.create_training_examples()

        # 创建质量评估指标
        quality_metric = self.create_quality_metric()

        # 创建DSPy程序
        class TeachingSignature(dspy.Signature):
            f"""{base_prompt}"""
            prompt = dspy.InputField()
            generation = dspy.OutputField()

        program = CustomPredict(TeachingSignature)

        # 选择优化器
        if optimizer_type == "MIPROv2":
            optimizer = dspy.MIPROv2(
                quality_metric, max_bootstrapped_demos=max_few_shots, max_labeled_demos=max_few_shots
            )
        elif optimizer_type == "SIMBA":
            optimizer = dspy.SIMBA(metric=quality_metric, bsize=min(4, len(trainset)))
        else:
            raise ValueError(f"不支持的优化器类型: {optimizer_type}")

        # 运行优化
        try:
            optimized_program = optimizer.compile(program, trainset=[], requires_permission_to_run=False)
            logger.info("优化完成, optimized_program:\n{optimized_program}")

            # 提取优化后的prompt
            optimized_prompt = optimized_program.signature.instructions.strip()

            # 如果有few-shot示例，添加到prompt中
            if hasattr(optimized_program, "demos") and optimized_program.demos:
                adapter = TeachingAdapter()
                optimized_prompt += "\n\n" + adapter._format_demos(optimized_program.demos)

            logger.info("Prompt优化完成")
            return optimized_prompt

        except Exception as e:
            logger.error(f"Prompt优化失败: {e}")
            return base_prompt

    def test_optimized_prompt(self, optimized_prompt: str, test_cases: list[dict] = None) -> list[dict]:
        """测试优化后的prompt效果"""
        logger.info("测试优化后的prompt效果")

        if test_cases is None:
            test_cases = [
                {
                    "topic": "量子纠缠",
                    "purpose": "让物理爱好者理解量子纠缠的神奇之处",
                    "audience": "对物理感兴趣的大学生",
                },
                {
                    "topic": "机器学习中的过拟合",
                    "purpose": "帮助数据科学初学者避免过拟合陷阱",
                    "audience": "刚入门的数据科学家",
                },
                {
                    "topic": "经济学中的边际效用",
                    "purpose": "让商科学生理解消费者行为的经济学原理",
                    "audience": "商学院学生",
                },
            ]

        # 创建测试程序
        class TestSignature(dspy.Signature):
            f"""{optimized_prompt}"""
            prompt = dspy.InputField()
            generation = dspy.OutputField()

        test_program = CustomPredict(TestSignature)

        results = []
        for test_case in test_cases:
            try:
                # 构建测试输入
                test_input = f"请为主题'{test_case['topic']}'生成一个高质量的教学例子，目的是{test_case['purpose']}，面向{test_case['audience']}。"

                # 生成结果
                result = test_program(prompt=test_input)
                generated = result.generation

                # 评估质量
                quality_result = self.quality_judge(
                    topic=test_case["topic"],
                    purpose=test_case["purpose"],
                    target_audience=test_case["audience"],
                    example_content=generated,
                )

                test_result = {
                    "test_case": test_case,
                    "generated_example": generated,
                    "quality_score": quality_result.score,
                    "feedback": quality_result.detailed_feedback,
                }

                results.append(test_result)

                logger.info(f"测试完成 - {test_case['topic']}: {quality_result.score}/100")

            except Exception as e:
                logger.error(f"测试失败 - {test_case['topic']}: {e}")
                continue

        return results

    def iterative_optimization(self, iterations: int = 3, test_domains: list[str] = None) -> dict[str, Any]:
        """
        迭代优化prompt，针对不同领域和受众

        Args:
            iterations: 迭代次数
            test_domains: 测试领域列表

        Returns:
            优化结果报告
        """
        logger.info(f"开始迭代优化，共{iterations}轮")

        if test_domains is None:
            test_domains = ["人工智能", "量子物理", "经济学", "生物学", "心理学"]

        # 加载初始prompt
        current_prompt = self.load_base_prompt_template()

        results = {"iterations": [], "best_prompt": current_prompt, "best_avg_score": 0.0, "domain_performance": {}}

        for iteration in range(iterations):
            logger.info(f"第{iteration + 1}轮优化")

            # 优化prompt
            optimized_prompt = self.optimize_prompt(
                base_prompt=current_prompt, max_few_shots=2, optimizer_type="MIPROv2"
            )

            # 跨领域测试
            domain_scores = []
            domain_results = {}

            for domain in test_domains:
                test_cases = self._generate_domain_test_cases(domain)
                test_results = self.test_optimized_prompt(optimized_prompt, test_cases)

                # 计算该领域的平均分数
                domain_score = sum(r["quality_score"] for r in test_results) / len(test_results) if test_results else 0
                domain_scores.append(domain_score)
                domain_results[domain] = {"avg_score": domain_score, "test_results": test_results}

                logger.info(f"领域{domain}平均分数: {domain_score:.2f}")

            # 计算总体平均分数
            avg_score = sum(domain_scores) / len(domain_scores) if domain_scores else 0

            iteration_result = {
                "iteration": iteration + 1,
                "prompt": optimized_prompt,
                "avg_score": avg_score,
                "domain_scores": dict(zip(test_domains, domain_scores)),
                "improvement": avg_score - results["best_avg_score"] if results["best_avg_score"] > 0 else 0.0,
            }

            results["iterations"].append(iteration_result)
            results["domain_performance"][f"iteration_{iteration + 1}"] = domain_results

            # 更新最佳prompt
            if avg_score > results["best_avg_score"]:
                results["best_prompt"] = optimized_prompt
                results["best_avg_score"] = avg_score
                logger.info(f"发现更好的prompt，平均分数: {avg_score:.2f}")

            # 为下一轮迭代更新当前prompt
            current_prompt = optimized_prompt

            logger.info(f"第{iteration + 1}轮完成，平均分数: {avg_score:.2f}")

        # 保存优化结果
        self._save_optimization_results(results)

        return results

    def _generate_domain_test_cases(self, domain: str) -> list[dict]:
        """为特定领域生成测试用例"""
        domain_cases = {
            "人工智能": [
                {
                    "topic": "Transformer注意力机制",
                    "purpose": "让AI研究者理解注意力的工作原理",
                    "audience": "机器学习研究生",
                },
                {
                    "topic": "强化学习中的探索与利用",
                    "purpose": "帮助算法工程师平衡探索和利用",
                    "audience": "AI算法工程师",
                },
            ],
            "量子物理": [
                {"topic": "薛定谔的猫", "purpose": "让物理爱好者理解量子叠加", "audience": "对物理感兴趣的高中生"},
                {"topic": "量子隧道效应", "purpose": "解释微观粒子的神奇行为", "audience": "物理系本科生"},
            ],
            "经济学": [
                {"topic": "供需平衡", "purpose": "让商科学生理解市场机制", "audience": "经济学专业学生"},
                {"topic": "通胀与货币政策", "purpose": "解释央行如何调控经济", "audience": "金融从业者"},
            ],
            "生物学": [
                {"topic": "DNA复制机制", "purpose": "让医学生理解遗传信息传递", "audience": "医学院学生"},
                {"topic": "免疫系统识别机制", "purpose": "解释身体如何抵御病原体", "audience": "生物学爱好者"},
            ],
            "心理学": [
                {"topic": "认知偏差", "purpose": "帮助人们识别思维陷阱", "audience": "心理学入门者"},
                {"topic": "条件反射机制", "purpose": "解释行为形成的心理学原理", "audience": "教育工作者"},
            ],
        }

        return domain_cases.get(
            domain,
            [
                {
                    "topic": f"{domain}核心概念",
                    "purpose": f"帮助学习者理解{domain}的基本原理",
                    "audience": f"{domain}初学者",
                }
            ],
        )

    def _save_optimization_results(self, results: dict[str, Any]):
        """保存优化结果"""
        try:
            import datetime

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存详细结果
            result_file = self.output_dir / f"optimization_results_{timestamp}.json"
            with open(result_file, "w", encoding="utf-8") as f:
                json.dump(results, f, ensure_ascii=False, indent=2)

            # 保存最佳prompt
            best_prompt_file = self.output_dir / f"best_prompt_{timestamp}.txt"
            with open(best_prompt_file, "w", encoding="utf-8") as f:
                f.write(results["best_prompt"])

            logger.info(f"优化结果已保存: {result_file}")
            logger.info(f"最佳prompt已保存: {best_prompt_file}")

        except Exception as e:
            logger.error(f"保存优化结果失败: {e}")


def main():
    """主函数 - 演示DSPy智能优化"""
    print("🚀 DSPy智能Prompt优化器")
    print("=" * 60)

    try:
        # 创建优化器
        optimizer = DSPyPromptOptimizer()

        print("✅ 优化器初始化完成")

        # 执行单次优化
        print("\n🔧 执行单次优化...")
        optimized_prompt = optimizer.optimize_prompt()

        print("\n📝 优化后的prompt预览:")
        print("-" * 40)
        print(optimized_prompt[:500] + "..." if len(optimized_prompt) > 500 else optimized_prompt)
        print("-" * 40)

        # 测试优化效果
        print("\n🧪 测试优化效果...")
        test_results = optimizer.test_optimized_prompt(optimized_prompt)

        print("\n📊 测试结果:")
        for result in test_results:
            topic = result["test_case"]["topic"]
            score = result["quality_score"]
            print(f"  • {topic}: {score}/100")

        avg_score = sum(r["quality_score"] for r in test_results) / len(test_results) if test_results else 0
        print(f"\n🎯 平均分数: {avg_score:.2f}/100")

        # 执行迭代优化
        print("\n🔄 执行迭代优化...")
        iteration_results = optimizer.iterative_optimization(iterations=2)

        print("\n🏆 迭代优化完成!")
        print(f"最佳平均分数: {iteration_results['best_avg_score']:.2f}/100")

        # 显示各轮结果
        for iteration_result in iteration_results["iterations"]:
            print(f"第{iteration_result['iteration']}轮: {iteration_result['avg_score']:.2f}/100")

        print(f"\n💾 结果已保存到: {optimizer.output_dir}")

        return True

    except Exception as e:
        print(f"❌ 优化失败: {e}")
        logger.error(f"优化失败: {e}")
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 DSPy智能优化完成！")
    else:
        print("\n💥 优化失败，请检查日志")
