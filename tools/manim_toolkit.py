import os
import subprocess
from typing import List, Optional, Any
import logging

logger = logging.getLogger(__name__)

class ManimToolkit:
    """Manim渲染和视频处理工具包"""
    
    def __init__(self):
        """初始化Manim工具包"""
        pass
        
    def render_scene(self, code_path: str, media_dir: str, quality: str = "h") -> Any:
        """
        渲染Manim场景
        
        Args:
            code_path: Manim代码文件路径
            media_dir: 媒体输出目录
            quality: 渲染质量 (l: low, m: medium, h: high)
            
        Returns:
            Any: 渲染结果
        """
        logger.info(f"渲染场景: {code_path}")
        
        try:
            # 构建命令
            cmd = [
                "manim", f"-q{quality}", 
                code_path, 
                "--media_dir", media_dir,
                "--progress_bar", "none"
            ]
            
            # 执行渲染
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True
            )
            
            logger.info(f"渲染成功: {code_path}")
            return result
        except subprocess.CalledProcessError as e:
            logger.error(f"渲染失败: {str(e)}")
            logger.error(f"错误输出: {e.stderr}")
            raise RuntimeError(f"渲染失败: {e.stderr}")
    
    def combine_videos(self, video_paths: List[str], output_path: str) -> None:
        """
        合并多个视频文件
        
        Args:
            video_paths: 要合并的视频文件路径列表
            output_path: 输出文件路径
        """
        logger.info(f"合并视频: {len(video_paths)}个文件")
        
        if not video_paths:
            raise ValueError("没有提供视频文件")
        
        try:
            # 创建输出目录
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 创建临时文件列表
            list_file = os.path.join(os.path.dirname(output_path), "video_list.txt")
            with open(list_file, 'w') as f:
                for video_path in video_paths:
                    f.write(f"file '{os.path.abspath(video_path)}'\n")
            
            # 使用FFmpeg合并视频
            cmd = [
                "ffmpeg", "-y", "-f", "concat", 
                "-safe", "0", 
                "-i", list_file, 
                "-c", "copy", 
                output_path
            ]
            
            subprocess.run(cmd, check=True, capture_output=True)
            
            # 删除临时文件
            os.remove(list_file)
            
            logger.info(f"视频合并成功: {output_path}")
        except Exception as e:
            logger.error(f"视频合并失败: {str(e)}")
            raise
    
    def extract_frame(self, video_path: str, output_path: str, time: float = 0.0) -> Optional[str]:
        """
        从视频中提取帧
        
        Args:
            video_path: 视频文件路径
            output_path: 输出图像路径
            time: 提取时间点(秒)
            
        Returns:
            Optional[str]: 成功时返回输出路径，失败时返回None
        """
        logger.info(f"从视频提取帧: {video_path}")
        
        try:
            # 创建输出目录
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 使用FFmpeg提取帧
            cmd = [
                "ffmpeg", "-y",
                "-ss", str(time),
                "-i", video_path,
                "-vframes", "1",
                "-q:v", "2",
                output_path
            ]
            
            subprocess.run(cmd, check=True, capture_output=True)
            
            logger.info(f"帧提取成功: {output_path}")
            return output_path
        except Exception as e:
            logger.error(f"帧提取失败: {str(e)}")
            return None 