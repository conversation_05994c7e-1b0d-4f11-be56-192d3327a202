import os
import json
import logging
from typing import List, Dict, Any, Optional, Union
import re
from pathlib import Path

# 这里应该导入实际使用的向量存储和嵌入模型库
# 以下是示例导入
try:
    import chromadb
    from chromadb.utils import embedding_functions
    import requests
    import ollama
except ImportError as e:
    print(f"依赖库未安装: {e}")
    print("请安装必要依赖: pip install chromadb ollama requests")
    raise

logger = logging.getLogger(__name__)

class OllamaEmbeddingFunction:
    """Ollama嵌入函数类"""
    
    def __init__(self, model_name: str = "nomic-embed-text", ollama_url: str = "http://localhost:11434"):
        """
        初始化Ollama嵌入函数
        
        Args:
            model_name: Ollama模型名称
            ollama_url: Ollama服务URL
        """
        self.model_name = model_name
        self.ollama_url = ollama_url
        self._embedding_dim = None
        self.client = ollama.Client(host=self.ollama_url)
        
    def _get_embedding_dimension(self) -> int:
        """
        获取嵌入向量维度
        
        Returns:
            int: 嵌入向量维度
        """
        if self._embedding_dim is None:
            try:
                # 使用测试文本获取维度
                response = self.client.embeddings(model=self.model_name, prompt="test")
                self._embedding_dim = len(response['embedding'])
                logger.info(f"检测到嵌入维度: {self._embedding_dim}")
            except Exception as e:
                logger.warning(f"无法检测嵌入维度，使用默认值768: {str(e)}")
                self._embedding_dim = 768  # 默认维度
                return self._embedding_dim
        
        return self._embedding_dim
        
    def __call__(self, input: List[str]) -> List[List[float]]:
        """
        生成文本嵌入
        
        Args:
            input: 输入文本列表
            
        Returns:
            List[List[float]]: 嵌入向量列表
        """
        embeddings = []
        for text in input:
            try:
                # 使用ollama库生成嵌入
                response = self.client.embeddings(model=self.model_name, prompt=text)
                embeddings.append(response['embedding'])
                logger.debug(f"生成嵌入成功，维度: {len(response['embedding'])}")
            except Exception as e:
                logger.error(f"生成嵌入失败: {str(e)}")
                # 如果失败，返回零向量
                dim = self._get_embedding_dimension()
                embeddings.append([0.0] * dim)
        
        return embeddings

class RAGToolkit:
    """检索增强生成工具包"""
    
    def __init__(
        self, 
        chroma_db_path: str = "data/rag/chroma_db",
        manim_docs_path: str = "data/rag/manim_docs",
        embedding_model: str = "nomic-embed-text",
        ollama_url: str = "http://localhost:11434",
        session_id: str = None,
        use_langfuse: bool = False
    ):
        """
        初始化RAG工具包
        
        Args:
            chroma_db_path: ChromaDB存储路径
            manim_docs_path: Manim文档路径
            embedding_model: Ollama嵌入模型名称 (默认: nomic-embed-text)
            ollama_url: Ollama服务URL
            session_id: 会话ID
            use_langfuse: 是否使用Langfuse日志
        """
        self.chroma_db_path = chroma_db_path
        self.manim_docs_path = manim_docs_path
        self.embedding_model = embedding_model
        self.ollama_url = ollama_url
        self.session_id = session_id
        self.use_langfuse = use_langfuse
        
        # 初始化向量存储
        self._initialize_vector_store()
        
        # 加载插件信息
        self.plugins_info = self._load_plugins_info()
        
        # 检查是否需要加载文档
        self._check_and_load_documents()
    
    def _initialize_vector_store(self):
        """初始化向量存储"""
        try:
            # 创建ChromaDB客户端
            self.client = chromadb.PersistentClient(path=self.chroma_db_path)
            
            # 初始化Ollama嵌入函数
            self.embedding_function = OllamaEmbeddingFunction(
                model_name=self.embedding_model,
                ollama_url=self.ollama_url
            )
            
            # 获取或创建集合
            self.collection = self.client.get_or_create_collection(
                name="manim_docs",
                embedding_function=self.embedding_function
            )
            
            logger.info(f"向量存储初始化成功: {self.chroma_db_path}")
            logger.info(f"使用Ollama嵌入模型: {self.embedding_model}")
        except Exception as e:
            logger.error(f"向量存储初始化失败: {str(e)}")
            # 创建一个空实现，以便代码可以继续运行
            self.client = None
            self.collection = None
    
    def _load_plugins_info(self) -> Dict[str, Dict[str, Any]]:
        """
        加载Manim插件信息
        
        Returns:
            Dict: 插件信息字典
        """
        # 插件信息文件路径
        plugins_file = os.path.join(os.path.dirname(self.manim_docs_path), "plugins_info.json")
        
        # 默认插件信息
        default_plugins = {
            "manim_circuit": {
                "description": "电路图绘制插件",
                "keywords": ["电路", "电子", "电压", "电流", "开关", "电阻", "电容"]
            },
            "manim_physics": {
                "description": "物理模拟插件",
                "keywords": ["物理", "力学", "重力", "弹性", "碰撞", "流体", "轨迹"]
            },
            "manim_chemistry": {
                "description": "化学分子结构绘制插件",
                "keywords": ["化学", "分子", "原子", "反应", "键", "结构"]
            },
            "manim_dsa": {
                "description": "数据结构和算法可视化插件",
                "keywords": ["算法", "数据结构", "排序", "搜索", "图", "树", "链表"]
            },
            "manim_ml": {
                "description": "机器学习可视化插件",
                "keywords": ["机器学习", "深度学习", "神经网络", "优化", "激活函数", "权重"]
            }
        }
        
        try:
            if os.path.exists(plugins_file):
                with open(plugins_file, 'r') as f:
                    return json.load(f)
            return default_plugins
        except Exception as e:
            logger.error(f"加载插件信息失败: {str(e)}")
            return default_plugins
    
    def search_relevant_context(self, query: str, limit: int = 5) -> str:
        """
        搜索相关上下文
        
        Args:
            query: 查询文本
            limit: 返回结果数量限制
            
        Returns:
            str: 格式化的上下文文本
        """
        if not self.collection:
            return "RAG未正确初始化，无法执行搜索"
        
        try:
            # 执行查询
            results = self.collection.query(
                query_texts=[query],
                n_results=limit
            )
            
            # 格式化结果
            if results["documents"] and results["documents"][0]:
                formatted_results = "相关上下文：\n\n"
                for i, doc in enumerate(results["documents"][0], 1):
                    formatted_results += f"{i}. {doc}\n\n"
                return formatted_results
            
            return "未找到相关上下文"
        except Exception as e:
            logger.error(f"搜索相关上下文失败: {str(e)}")
            return f"搜索过程中出错: {str(e)}"
    
    def detect_relevant_plugins(self, topic: str, description: str) -> List[str]:
        """
        检测相关插件
        
        Args:
            topic: 主题
            description: 描述
            
        Returns:
            List[str]: 相关插件列表
        """
        combined_text = f"{topic} {description}".lower()
        relevant_plugins = []
        
        for plugin_name, plugin_info in self.plugins_info.items():
            keywords = plugin_info.get("keywords", [])
            for keyword in keywords:
                if keyword.lower() in combined_text:
                    relevant_plugins.append(plugin_name)
                    break
        
        logger.info(f"检测到的相关插件: {relevant_plugins}")
        return relevant_plugins
    
    def search_with_queries(self, queries: List[str], limit_per_query: int = 3) -> str:
        """
        使用多个查询搜索上下文
        
        Args:
            queries: 查询列表
            limit_per_query: 每个查询的结果数量限制
            
        Returns:
            str: 格式化的上下文文本
        """
        if not queries:
            return "未提供查询"
        
        # 去重并限制查询数量
        unique_queries = list(set(queries))[:5]  # 最多5个查询
        
        all_results = []
        for query in unique_queries:
            # 执行单个查询
            context = self.search_relevant_context(query, limit=limit_per_query)
            if "未找到相关上下文" not in context and "RAG未正确初始化" not in context:
                all_results.append(f"查询: {query}\n{context}")
        
        if all_results:
            return "\n".join(all_results)
        return "未找到相关上下文"
    
    def generate_queries_for_vision_storyboard(
        self, 
        scene_plan: str, 
        relevant_plugins: List[str],
        topic: str = None,
        scene_number: int = None,
        session_id: str = None
    ) -> List[str]:
        """
        为视觉故事板生成查询
        
        Args:
            scene_plan: 场景计划
            relevant_plugins: 相关插件列表
            topic: 主题
            scene_number: 场景编号
            session_id: 会话ID
            
        Returns:
            List[str]: 查询列表
        """
        # 从场景计划中提取关键词
        keywords = self._extract_keywords(scene_plan)
        
        # 生成查询
        queries = [
            f"Manim实现 {topic} 可视化",
            f"Manim如何创建{keywords[:3] if len(keywords) > 3 else keywords}的动画"
        ]
        
        # 添加插件相关查询
        for plugin in relevant_plugins:
            queries.append(f"如何使用{plugin}插件在Manim中创建{topic}可视化")
        
        # 去重
        return list(set(queries))
    
    def generate_queries_for_technical(
        self, 
        storyboard: str, 
        relevant_plugins: List[str],
        topic: str = None,
        scene_number: int = None,
        session_id: str = None
    ) -> List[str]:
        """
        为技术实现生成查询
        
        Args:
            storyboard: 视觉故事板
            relevant_plugins: 相关插件列表
            topic: 主题
            scene_number: 场景编号
            session_id: 会话ID
            
        Returns:
            List[str]: 查询列表
        """
        # 从故事板中提取关键词
        keywords = self._extract_keywords(storyboard)
        
        # 提取可能的Manim对象
        manim_objects = self._extract_manim_objects(storyboard)
        
        # 生成查询
        queries = [
            f"Manim技术实现 {topic} 动画",
            f"Manim如何创建{keywords[:3] if len(keywords) > 3 else keywords}的技术实现"
        ]
        
        # 添加对象相关查询
        for obj in manim_objects[:3]:  # 最多3个对象
            queries.append(f"Manim中{obj}对象的属性和方法")
        
        # 添加插件相关查询
        for plugin in relevant_plugins:
            queries.append(f"{plugin}插件的核心类和方法")
        
        # 去重
        return list(set(queries))
    
    def generate_queries_for_narration(
        self, 
        storyboard: str, 
        relevant_plugins: List[str],
        topic: str = None,
        scene_number: int = None,
        session_id: str = None
    ) -> List[str]:
        """
        为动画叙述生成查询
        
        Args:
            storyboard: 视觉故事板
            relevant_plugins: 相关插件列表
            topic: 主题
            scene_number: 场景编号
            session_id: 会话ID
            
        Returns:
            List[str]: 查询列表
        """
        # 从故事板中提取关键词
        keywords = self._extract_keywords(storyboard)
        
        # 生成查询
        queries = [
            f"Manim VoiceoverScene使用方法",
            f"{topic} 教学讲解关键点",
            f"如何解释{keywords[:3] if len(keywords) > 3 else keywords}概念"
        ]
        
        # 去重
        return list(set(queries))
    
    def generate_queries_for_code(
        self, 
        implementation_plan: str, 
        relevant_plugins: List[str],
        topic: str = None,
        scene_number: int = None,
        session_id: str = None
    ) -> List[str]:
        """
        为代码生成生成查询
        
        Args:
            implementation_plan: 技术实现计划
            relevant_plugins: 相关插件列表
            topic: 主题
            scene_number: 场景编号
            session_id: 会话ID
            
        Returns:
            List[str]: 查询列表
        """
        # 提取可能的Manim对象
        manim_objects = self._extract_manim_objects(implementation_plan)
        
        # 提取可能的动画方法
        animation_methods = self._extract_animation_methods(implementation_plan)
        
        # 生成查询
        queries = [
            f"Manim代码实现 {topic} 场景{scene_number}",
            "Manim VoiceoverScene同步动画和语音"
        ]
        
        # 添加对象相关查询
        for obj in manim_objects[:3]:  # 最多3个对象
            queries.append(f"Manim {obj} 类的完整代码示例")
        
        # 添加动画方法相关查询
        for method in animation_methods[:3]:  # 最多3个方法
            queries.append(f"Manim {method} 方法参数和使用示例")
        
        # 添加插件相关查询
        for plugin in relevant_plugins:
            queries.append(f"{plugin} 插件的代码使用示例")
        
        # 去重
        return list(set(queries))
    
    def generate_queries_for_fix_error(
        self, 
        error: str, 
        code: str,
        topic: str = None,
        scene_number: int = None,
        session_id: str = None
    ) -> List[str]:
        """
        为错误修复生成查询
        
        Args:
            error: 错误信息
            code: 代码
            topic: 主题
            scene_number: 场景编号
            session_id: 会话ID
            
        Returns:
            List[str]: 查询列表
        """
        # 提取错误关键词
        error_keywords = self._extract_keywords(error)
        
        # 从错误中提取可能的类名和方法名
        error_classes = re.findall(r'[A-Z][a-zA-Z]+', error)
        error_methods = re.findall(r'[a-z][a-zA-Z_]+\(', error)
        error_methods = [m[:-1] for m in error_methods]  # 移除括号
        
        # 生成查询
        queries = [
            f"Manim错误: {' '.join(error_keywords[:5])}"
        ]
        
        # 添加类和方法相关查询
        for cls in error_classes[:2]:
            queries.append(f"Manim {cls} 类常见错误")
        
        for method in error_methods[:2]:
            queries.append(f"Manim {method} 方法参数和使用示例")
        
        # 去重
        return list(set(queries))
    
    def _extract_keywords(self, text: str) -> List[str]:
        """
        从文本中提取关键词
        
        Args:
            text: 输入文本
            
        Returns:
            List[str]: 关键词列表
        """
        # 简单实现，实际应用中应该使用更复杂的关键词提取算法
        words = re.findall(r'\b[a-zA-Z\u4e00-\u9fa5]{2,}\b', text)
        # 过滤常见停用词
        stopwords = {"的", "地", "得", "和", "与", "或", "the", "and", "in", "on", "of", "for", "to", "a", "an"}
        keywords = [word for word in words if word.lower() not in stopwords]
        # 返回出现频率最高的前10个关键词
        from collections import Counter
        return [word for word, _ in Counter(keywords).most_common(10)]
    
    def _extract_manim_objects(self, text: str) -> List[str]:
        """
        从文本中提取可能的Manim对象
        
        Args:
            text: 输入文本
            
        Returns:
            List[str]: Manim对象列表
        """
        # Manim常见对象列表
        common_objects = {
            "Circle", "Square", "Rectangle", "Triangle", "Polygon", "Line", "Arrow", 
            "Text", "Tex", "MathTex", "VGroup", "Graph", "Axes", "NumberPlane", 
            "CoordinateSystem", "Dot", "Point", "SVGMobject", "ImageMobject", "Scene",
            "VoiceoverScene", "MovingCameraScene", "ThreeDScene"
        }
        
        # 查找文本中提到的Manim对象
        found_objects = []
        for obj in common_objects:
            if obj in text:
                found_objects.append(obj)
        
        return found_objects
    
    def _extract_animation_methods(self, text: str) -> List[str]:
        """
        从文本中提取可能的动画方法
        
        Args:
            text: 输入文本
            
        Returns:
            List[str]: 动画方法列表
        """
        # Manim常见动画方法
        common_animations = {
            "Write", "Create", "FadeIn", "FadeOut", "Transform", "ReplacementTransform",
            "MoveAlongPath", "Rotate", "Scale", "GrowFromCenter", "DrawBorderThenFill",
            "Indicate", "Flash", "AnimationGroup", "Succession", "LaggedStart"
        }
        
        # 查找文本中提到的动画方法
        found_animations = []
        for anim in common_animations:
            if anim in text:
                found_animations.append(anim)
        
        return found_animations
    
    def _check_and_load_documents(self):
        """检查是否需要加载文档到向量数据库"""
        if not self.collection:
            logger.warning("向量存储未初始化，跳过文档加载")
            return
        
        try:
            # 检查集合中是否已有文档
            count = self.collection.count()
            if count > 0:
                logger.info(f"向量数据库已包含 {count} 个文档，跳过加载")
                return
            
            # 如果文档路径存在，自动加载文档
            if os.path.exists(self.manim_docs_path):
                logger.info(f"开始自动加载文档: {self.manim_docs_path}")
                self.load_documents_from_directory()
            else:
                logger.warning(f"文档路径不存在: {self.manim_docs_path}")
                
        except Exception as e:
            logger.error(f"检查文档时出错: {str(e)}")
    
    def load_documents_from_directory(self, docs_dir: str = None) -> bool:
        """
        从目录中加载文档到向量数据库
        
        Args:
            docs_dir: 文档目录路径，如果为None则使用初始化时的路径
            
        Returns:
            bool: 是否成功加载
        """
        if not self.collection:
            logger.error("向量存储未初始化，无法加载文档")
            return False
        
        docs_path = docs_dir or self.manim_docs_path
        if not os.path.exists(docs_path):
            logger.error(f"文档路径不存在: {docs_path}")
            return False
        
        try:
            # 加载文档
            documents = self._load_documents_from_path(docs_path)
            if not documents:
                logger.warning("没有找到可加载的文档")
                return False
            
            logger.info(f"准备加载 {len(documents)} 个文档块")
            
            # 准备数据
            contents = [doc['content'] for doc in documents]
            metadatas = [doc['metadata'] for doc in documents]
            ids = [f"doc_{i}" for i in range(len(documents))]
            
            # 分批添加文档（避免一次性添加太多）
            batch_size = 50
            total_batches = (len(contents) + batch_size - 1) // batch_size
            
            for i in range(0, len(contents), batch_size):
                batch_contents = contents[i:i+batch_size]
                batch_metadatas = metadatas[i:i+batch_size]
                batch_ids = ids[i:i+batch_size]
                
                self.collection.add(
                    documents=batch_contents,
                    metadatas=batch_metadatas,
                    ids=batch_ids
                )
                
                batch_num = i // batch_size + 1
                logger.info(f"已添加文档批次 {batch_num}/{total_batches}")
            
            logger.info(f"成功将 {len(documents)} 个文档添加到向量数据库")
            return True
            
        except Exception as e:
            logger.error(f"加载文档时出错: {str(e)}")
            return False
    
    def _load_documents_from_path(self, docs_path: str) -> List[Dict[str, Any]]:
        """
        从路径中加载文档
        
        Args:
            docs_path: 文档路径
            
        Returns:
            List[Dict]: 文档列表，每个文档包含 content 和 metadata
        """
        documents = []
        path = Path(docs_path)
        
        # 遍历所有 .md 文件
        for md_file in path.rglob("*.md"):
            try:
                with open(md_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 按段落分割文档（每个段落作为一个文档块）
                paragraphs = [p.strip() for p in content.split('\n\n') 
                             if p.strip() and len(p.strip()) > 50]
                
                for i, paragraph in enumerate(paragraphs):
                    documents.append({
                        'content': paragraph,
                        'metadata': {
                            'source': str(md_file.relative_to(path)),
                            'file': md_file.name,
                            'paragraph_id': i,
                            'category': md_file.parent.name
                        }
                    })
                    
            except Exception as e:
                logger.warning(f"读取文件 {md_file} 时出错: {e}")
                
        return documents
    
    def clear_documents(self) -> bool:
        """
        清空向量数据库中的所有文档
        
        Returns:
            bool: 是否成功清空
        """
        if not self.collection:
            logger.error("向量存储未初始化，无法清空文档")
            return False
        
        try:
            # 删除集合中的所有文档
            all_ids = self.collection.get()['ids']
            if all_ids:
                self.collection.delete(ids=all_ids)
                logger.info(f"成功清空 {len(all_ids)} 个文档")
            else:
                logger.info("向量数据库已为空")
            return True
            
        except Exception as e:
            logger.error(f"清空文档时出错: {str(e)}")
            return False
    
    def reload_documents(self, docs_dir: str = None) -> bool:
        """
        重新加载文档（先清空再加载）
        
        Args:
            docs_dir: 文档目录路径，如果为None则使用初始化时的路径
            
        Returns:
            bool: 是否成功重新加载
        """
        logger.info("开始重新加载文档...")
        
        # 先清空现有文档
        if not self.clear_documents():
            return False
        
        # 重新加载文档
        return self.load_documents_from_directory(docs_dir)
    
    def get_document_count(self) -> int:
        """
        获取向量数据库中的文档数量
        
        Returns:
            int: 文档数量，如果出错返回-1
        """
        if not self.collection:
            logger.error("向量存储未初始化")
            return -1
        
        try:
            return self.collection.count()
        except Exception as e:
            logger.error(f"获取文档数量时出错: {str(e)}")
            return -1 