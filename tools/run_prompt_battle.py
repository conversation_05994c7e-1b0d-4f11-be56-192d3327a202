#!/usr/bin/env python3
"""
Prompt对战工具运行脚本
"""

import sys
from pathlib import Path

# 添加tools目录到路径
#sys.path.append(str(Path(__file__).parent / "tools"))

from tools.prompt_battle_tool import PromptBattleTool


def main():
    """主函数"""
    print("🥊 Prompt对战工具")
    print("=" * 50)

    # 创建对战工具实例
    tool = PromptBattleTool()

    # 获取用户输入
    if len(sys.argv) >= 4:
        topic = sys.argv[1]
        purpose = sys.argv[2]
        audience = sys.argv[3]
    else:
        print("参数少于3个，需要输入主题、目的、目标受众")
        topic = input("请输入主题: ").strip()
        purpose = input("请输入目的: ").strip()
        audience = input("请输入目标受众: ").strip()

    if not topic or not purpose:
        print("❌ 主题和目的不能为空！")
        return

    print(f"\n📋 主题: {topic}")
    print(f"🎯 目的: {purpose}")
    print(f"👥 受众: {audience}")
    print("\n🚀 开始对战...")

    # 执行对战
    result = tool.battle(topic, purpose, audience)

    # 显示结果
    tool.print_battle_summary(result)

    # 询问是否查看详细结果
    if "error" not in result:
        print("\n" + "=" * 80)
        print("📊 详细分析结果")
        print("=" * 80)

        analysis = result.get("analysis", {})

        # 显示优缺点分析
        if "strengths_and_weaknesses" in analysis:
            sw = analysis["strengths_and_weaknesses"]

            print("\n🔍 优缺点分析:")
            print("\n📈 Agent例子:")
            print("  优点:", "".join(sw.get("example_A", {}).get("strengths", [])))
            print("  缺点:", "".join(sw.get("example_A", {}).get("weaknesses", [])))

            print("\n📈 Prompt例子:")
            print("  优点:", "".join(sw.get("example_B", {}).get("strengths", [])))
            print("  缺点:", "".join(sw.get("example_B", {}).get("weaknesses", [])))

        # 显示改进建议
        if "improvement_suggestions" in analysis:
            suggestions = analysis["improvement_suggestions"]

            print("\n💡 改进建议:")
            print("\n🔧 Agent例子改进建议:")
            for i, suggestion in enumerate(suggestions.get("example_A", []), 1):
                print(f"  {i}. {suggestion}")

            print("\n🔧 Prompt例子改进建议:")
            for i, suggestion in enumerate(suggestions.get("example_B", []), 1):
                print(f"  {i}. {suggestion}")


if __name__ == "__main__":
    main()
