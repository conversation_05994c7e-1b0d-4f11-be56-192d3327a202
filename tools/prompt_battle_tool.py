#!/usr/bin/env python3
"""
Prompt对战工具 - 比较两种不同prompt生成的例子质量
"""

import datetime
import json
import os
import sys
from pathlib import Path
from typing import Any

import yaml
from loguru import logger

# 添加父目录到导入路径
#sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from camel.agents import ChatAgent
from camel.messages import BaseMessage

# 导入现有的agent
from agents.example_explain_agent_refactor import ExampleExplainAgent
from camel.types import RoleType

from utils.create_llm_model import create_model


class PromptBattleTool:
    """Prompt对战工具类"""

    def __init__(self, config_path: str = "config/config.yaml"):
        """初始化对战工具"""
        self.config_path = config_path
        self.config = self._load_config(config_path)
        self.model = create_model(config_path)
        self.battle_results_dir = Path("output/prompt_battles")
        self.battle_results_dir.mkdir(parents=True, exist_ok=True)

        # 设置日志
        logger.add("logs/prompt_battle.log", rotation="1 day", retention="30 days", level="INFO")

    def _load_config(self, config_path: str) -> dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, encoding="utf-8") as f:
                config = yaml.safe_load(f)
            logger.info(f"从 {config_path} 加载配置")
            return config
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            raise

    def _load_prompt_template(self, prompt_file: str) -> str:
        """加载prompt模板"""
        try:
            with open(prompt_file, encoding="utf-8") as f:
                content = f.read()

            # 提取prompt变量（假设格式为 prompt = """..."""）
            if 'prompt = """' in content:
                start = content.find('prompt = """') + len('prompt = """')
                end = content.rfind('"""')
                return content[start:end].strip()
            else:
                return content
        except Exception as e:
            logger.error(f"加载prompt模板失败: {e}")
            raise

    def _generate_example_with_agent(self, topic: str, purpose: str) -> str:
        """使用现有agent生成例子"""
        try:
            # 检查agent是否可用
            if ExampleExplainAgent is None:
                raise ImportError("ExampleExplainAgent未能正确导入")

            # 临时修改配置
            temp_config = self.config.copy()
            temp_config["example_explain"] = {
                "topic": topic,
                "purpose": purpose,
                "max_rounds": 1,
                "quality_threshold": "良好",
            }

            # 创建临时配置文件
            import tempfile

            with tempfile.NamedTemporaryFile(mode="w", suffix=".yaml", delete=False) as f:
                yaml.dump(temp_config, f)
                temp_config_path = f.name

            try:
                agent = ExampleExplainAgent(config_path=temp_config_path)
                result = agent.run()

                # 提取生成的例子内容
                if isinstance(result, dict):
                    if "saved_file" in result:
                        return open(result["saved_file"]).read()
                    elif "error" in result:
                        return f"Agent生成失败: {result['error']}"
                    else:
                        return str(result)
                else:
                    return str(result)

            finally:
                # 清理临时文件
                import os

                try:
                    os.unlink(temp_config_path)
                except Exception:
                    pass

        except Exception as e:
            logger.error(f"使用agent生成例子失败: {e}")
            return f"生成失败: {str(e)}"

    def _generate_example_with_prompt(self, topic: str, purpose: str, audience: str, prompt_template: str) -> str:
        """使用新prompt模板生成例子"""
        try:
            # 格式化prompt
            formatted_prompt = prompt_template.format(主题=topic, 目的=purpose, 目标群体=audience)

            # 创建ChatAgent
            system_message = BaseMessage(
                role_name="例子生成专家", role_type=RoleType.ASSISTANT, meta_dict=None, content=formatted_prompt
            )

            agent = ChatAgent(system_message=system_message, model=self.model, message_window_size=10)

            user_prompt = "根据要求生成高质量例子，严格按上述格式输出。"

            user_message = BaseMessage(role_name="用户", role_type=RoleType.USER, meta_dict=None, content=user_prompt)

            response = agent.step(user_message)
            return response.msg.content

        except Exception as e:
            logger.error(f"使用新prompt生成例子失败: {e}")
            return f"生成失败: {str(e)}"

    def _analyze_examples(self, topic: str, purpose: str, example1: str, example2: str) -> dict[str, Any]:
        """使用大模型分析对比两个例子"""

        analysis_prompt = f"""
你是一位资深的教学设计评估专家，请对比分析以下两个针对主题\"{topic}\"的教学例子。

评估目的：{purpose}

## 例子A（现有Agent生成）：
{example1}

## 例子B（新Prompt生成）：
{example2}

请从以下维度进行详细对比分析：

### 1. 例子具体性与数据完整性（35分）
- **数据具体性**（15分）：每个步骤是否包含详细的具体数据（如具体数值、矩阵、向量必须包含具体数字），关键数据是否省略？
- **步骤详实性**（10分）：每个步骤的操作是否具体明确，有没有遗漏关键操作？
- **量化表达**（10分）：结果是否量化，有具体的测量指标或数值？

### 2. 动画描述质量与连贯性（35分）
- **关键变化描述**（15分）：关键元素的位置、颜色、大小变化是否详细描述？是否缺少关键变化元素？
- **Manim适配性**（10分）：动画描述是否适合Manim生成，元素变化是否可编程实现？
- **整体连贯性**（10分）：动画步骤之间是否连贯，有无跳跃过大的变化？

### 3. 讲解完整性与本质揭示（30分）
- **概念本质描述**（15分）：是否直接了当地揭示概念本质，避免绕弯子？
- **讲解完整性**（10分）：是否完整覆盖核心概念，逻辑是否清晰？
- **原理阐述深度**（5分）：是否深入解释为什么这样做，机制是否清楚？

请按以下格式输出分析结果：

```json
{{
    "overall_winner": "A" | "B" | "平局",
    "overall_score": {{
        "example_A": 83,
        "example_B": 84
    }},
    "detailed_analysis": {{
        "example_specificity_and_data_completeness": {{
            "winner": "A" | "B" | "平局",
            "score_A": 28,
            "score_B": 32,
            "analysis": "详细分析数据具体性、步骤详实性、量化表达..."
        }},
        "animation_quality_and_coherence": {{
            "winner": "A" | "B" | "平局",
            "score_A": 30,
            "score_B": 28,
            "analysis": "详细分析关键变化描述、Manim适配性、整体连贯性..."
        }},
        "explanation_completeness_and_essence": {{
            "winner": "A" | "B" | "平局",
            "score_A": 25,
            "score_B": 24,
            "analysis": "详细分析概念本质描述、讲解完整性、原理阐述深度..."
        }}
    }},
    "strengths_and_weaknesses": {{
        "example_A": {{
            "strengths": ["优点1", "优点2"],
            "weaknesses": ["缺点1", "缺点2"]
        }},
        "example_B": {{
            "strengths": ["优点1", "优点2"],
            "weaknesses": ["缺点1", "缺点2"]
        }}
    }},
    "improvement_suggestions": {{
        "example_A": ["建议1", "建议2"],
        "example_B": ["建议1", "建议2"]
    }},
    "conclusion": "综合结论和推荐理由"
}}
```
"""

        try:
            # 创建分析agent
            system_message = BaseMessage(
                role_name="教学评估专家",
                role_type=RoleType.ASSISTANT,
                meta_dict=None,
                content="你是一位资深的教学设计评估专家，擅长客观公正地评估教学内容的质量。",
            )

            critic_model = self.model

            agent = ChatAgent(system_message=system_message, model=critic_model, message_window_size=10)

            user_message = BaseMessage(
                role_name="用户", role_type=RoleType.USER, meta_dict=None, content=analysis_prompt
            )

            response = agent.step(user_message)

            # 尝试提取JSON
            content = response.msg.content
            import re

            json_pattern = r"```json\s*(.*?)\s*```"
            match = re.search(json_pattern, content, re.DOTALL)
            if match:
                json_str = match.group(1).strip()
                try:
                    return json.loads(json_str)
                except json.JSONDecodeError:
                    logger.error(f"JSON解析错误: {json_str}")
                    return {"raw_analysis": content}
            else:
                # 如果没有JSON格式，返回原始内容
                return {"raw_analysis": content}

        except Exception as e:
            logger.error(f"分析例子失败: {e}")
            import traceback

            logger.error(traceback.format_exc())
            return {"error": str(e)}

    def analyze_examples_from_files(
        self, topic: str, purpose: str, agent_file_path: str, prompt_file_path: str
    ) -> dict[str, Any]:
        """从文件读取例子内容并进行分析对比

        Args:
            topic: 主题
            purpose: 目的
            agent_file_path: agent例子文件路径
            prompt_file_path: prompt例子文件路径

        Returns:
            分析结果字典
        """
        try:
            # 读取agent例子文件
            with open(agent_file_path, encoding="utf-8") as f:
                example_agent = f.read()

            # 读取prompt例子文件
            with open(prompt_file_path, encoding="utf-8") as f:
                example_prompt = f.read()

            logger.info(f"从文件读取例子 - agent: {agent_file_path}, prompt: {prompt_file_path}")

            # 调用分析方法
            return self._analyze_examples(topic, purpose, example_agent, example_prompt)

        except FileNotFoundError as e:
            error_msg = f"文件未找到: {e}"
            logger.error(error_msg)
            return {"error": error_msg}
        except Exception as e:
            error_msg = f"分析例子失败: {e}"
            logger.error(error_msg)
            return {"error": error_msg}

    def battle(
        self, topic: str, purpose: str, audience: str, new_prompt_file: str = "prompts/example_gen_prompt.py"
    ) -> dict[str, Any]:
        """执行对战"""
        logger.info(f"开始对战 - 主题: {topic}, 目的: {purpose}, 受众: {audience}")
        output_dir = Path(f"output/{topic}")
        output_dir.mkdir(parents=True, exist_ok=True)

        # 生成时间戳
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

        try:
            # 1. 使用现有agent生成例子
            logger.info("使用现有Agent生成例子...")
            example_agent = self._generate_example_with_agent(topic, purpose)

            # 2. 使用新prompt生成例子
            logger.info("使用新Prompt生成例子...")
            prompt_template = self._load_prompt_template(new_prompt_file)
            example_prompt = self._generate_example_with_prompt(topic, purpose, audience, prompt_template)
            with open(f"{output_dir}/example_prompt.md", "w", encoding="utf-8") as f:
                f.write(example_prompt)
            logger.info(f"新Prompt生成例子已保存到: {output_dir}/example_prompt.md")

            # 3. 分析对比
            logger.info("分析对比两个例子...")
            analysis = self._analyze_examples(topic, purpose, example_agent, example_prompt)

            # 4. 整理结果
            battle_result = {
                "metadata": {
                    "timestamp": timestamp,
                    "topic": topic,
                    "purpose": purpose,
                    "new_prompt_file": new_prompt_file,
                },
                "examples": {"agent_example": example_agent, "prompt_example": example_prompt},
                "analysis": analysis,
            }

            # 5. 保存结果
            self._save_battle_result(battle_result, timestamp)

            logger.info("对战完成！")
            return battle_result

        except Exception as e:
            logger.error(f"对战执行失败: {e}")
            return {"error": str(e)}

    def _save_battle_result(self, result: dict[str, Any], timestamp: str):
        """保存对战结果"""
        try:
            # 保存详细结果
            result_file = self.battle_results_dir / f"battle_{timestamp}.json"
            with open(result_file, "w", encoding="utf-8") as f:
                json.dump(result, f, ensure_ascii=False, indent=2)

            # 保存简要摘要到汇总文件
            summary_file = self.battle_results_dir / "battle_summary.jsonl"
            summary = {
                "timestamp": timestamp,
                "topic": result["metadata"]["topic"],
                "purpose": result["metadata"]["purpose"],
                "winner": result["analysis"].get("overall_winner", "未知"),
                "scores": result["analysis"].get("overall_score", {}),
                "conclusion": result["analysis"].get("conclusion", ""),
            }

            with open(summary_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(summary, ensure_ascii=False) + "\n")

            logger.info(f"对战结果已保存到: {result_file}")

        except Exception as e:
            logger.error(f"保存对战结果失败: {e}")

    def get_battle_history(self, limit: int = 10) -> list[dict[str, Any]]:
        """获取对战历史"""
        try:
            summary_file = self.battle_results_dir / "battle_summary.jsonl"
            if not summary_file.exists():
                return []

            history = []
            with open(summary_file, encoding="utf-8") as f:
                lines = f.readlines()
                for line in lines[-limit:]:
                    history.append(json.loads(line.strip()))

            return history

        except Exception as e:
            logger.error(f"获取对战历史失败: {e}")
            return []

    def print_battle_summary(self, result: dict[str, Any]):
        """打印对战摘要"""
        if "error" in result:
            print(f"❌ 对战失败: {result['error']}")
            return

        metadata = result["metadata"]
        analysis = result["analysis"]

        print("\n" + "=" * 80)
        print("🥊 PROMPT 对战结果")
        print("=" * 80)
        print(f"📋 主题: {metadata['topic']}")
        print(f"🎯 目的: {metadata['purpose']}")
        print(f"⏰ 时间: {metadata['timestamp']}")
        print()

        if "overall_winner" in analysis:
            winner = analysis["overall_winner"]
            scores = analysis.get("overall_score", {})

            print(f"🏆 获胜者: {winner}")
            print(f"📊 得分: Agent例子 {scores.get('example_A', 'N/A')} vs Prompt例子 {scores.get('example_B', 'N/A')}")
            print()

            # 详细分析
            if "detailed_analysis" in analysis:
                print("📈 详细分析:")
                for category, details in analysis["detailed_analysis"].items():
                    category_name = {
                        "example_specificity_and_data_completeness": "例子具体性与数据完整性",
                        "animation_quality_and_coherence": "动画描述质量与连贯性",
                        "explanation_completeness_and_essence": "讲解完整性与本质揭示",
                    }.get(category, category)

                    print(
                        f"  • {category_name}: {details.get('winner', 'N/A')} "
                        f"({details.get('score_A', 'N/A')} vs {details.get('score_B', 'N/A')})"
                    )

            print()
            if "conclusion" in analysis:
                print(f"💡 结论: {analysis['conclusion']}")

        print("=" * 80)


def main():
    """主函数 - 命令行接口"""
    import argparse

    parser = argparse.ArgumentParser(description="Prompt对战工具")
    parser.add_argument("topic", help="主题")
    parser.add_argument("purpose", help="目的")
    parser.add_argument("--prompt-file", default="prompts/example_gen_prompt.py", help="新prompt文件路径")
    parser.add_argument("--history", action="store_true", help="显示对战历史")

    args = parser.parse_args()

    tool = PromptBattleTool()

    if args.history:
        history = tool.get_battle_history()
        print("\n📚 最近对战历史:")
        for i, battle in enumerate(history, 1):
            print(f"{i}. {battle['timestamp']} - {battle['topic']} - 获胜者: {battle['winner']}")
        return

    # 执行对战
    result = tool.battle(args.topic, args.purpose, args.prompt_file)
    tool.print_battle_summary(result)


def do_analysis():
    tool = PromptBattleTool()
    result = tool.analyze_examples_from_files(
        topic="BPE算法",
        purpose="给对大模型感兴趣的人科普相关知识",
        agent_file_path="output/BPE算法/example_explain.md",
        prompt_file_path="output/BPE算法/example_prompt.md",
    )
    print(json.dumps(result, ensure_ascii=False, indent=2))


def test_model():
    critic_model = create_model()
    res = critic_model.run(
        [
            {"role": "user", "content": "请用100字以内的内容介绍大语言模型的原理和应用"},
        ]
    )
    print(res)


if __name__ == "__main__":
    # main()
    do_analysis()
    # test_model()
