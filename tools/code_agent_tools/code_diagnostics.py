"""
Code Diagnostics Tool for Code Agent

Synchronous code analysis and issue detection using the existing DiagnosticsTool.
This tool provides comprehensive code quality checking and error detection.
"""

import os
import sys

from loguru import logger

# Add the project root to the path to import DiagnosticsTool
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", ".."))
from tools.diagnostics_tool import DiagnosticSeverity, DiagnosticsTool


def check_code_issues(file_paths: list[str], min_severity: str = "error") -> str:
    """
    Analyze code files for syntax errors, import issues, undefined variables, and other problems.

    This tool is essential for ensuring code quality and catching errors before execution.
    It performs static analysis on Python files to detect various types of issues.
    Use this tool when you need to:
    - Validate generated code for syntax errors
    - Check for undefined variables or functions
    - Detect import problems and missing modules
    - Identify type-related issues
    - Verify code quality before execution
    - Debug compilation or runtime errors
    - Ensure code follows Python best practices

    The tool analyzes multiple types of issues:
    - Syntax errors (invalid Python syntax)
    - Import errors (missing modules, circular imports)
    - Name errors (undefined variables, functions, classes)
    - Type errors (type mismatches, invalid operations)
    - Style issues (PEP 8 violations, code smells)
    - Logic errors (unreachable code, unused variables)

    Severity levels:
    - error: Critical issues that prevent code execution
    - warning: Potential problems that may cause runtime issues
    - info: Style and best practice suggestions
    - hint: Minor improvements and optimizations

    Args:
        file_paths: List of absolute or relative file paths to analyze
        min_severity: Minimum severity level to report ("error", "warning", "info", "hint")
                     Default is "error" to show errors only

    Returns:
        Formatted diagnostic report with issue details, locations, and context,
        or error message if analysis fails

    Examples:
        - check_code_issues(["/path/to/script.py"], "info") - Check single file for all issues
        - check_code_issues(["/path/to/script.py"], "error") - Only show critical errors
        - check_code_issues(["file1.py", "file2.py"], "warning") - Check multiple files for warnings and errors

    Output format:
        The tool returns a detailed report showing:
        - File path and line numbers
        - Issue severity and type
        - Descriptive error messages
        - Code context around issues
        - Suggestions for fixes when available
    """
    logger.info(f"[tool] Checking code issues: {file_paths}")
    # Map string severity to enum
    severity_map = {
        "error": DiagnosticSeverity.ERROR,
        "warning": DiagnosticSeverity.WARNING,
        "info": DiagnosticSeverity.INFO,
        "hint": DiagnosticSeverity.HINT,
    }

    min_sev = severity_map.get(min_severity.lower(), DiagnosticSeverity.INFO)

    try:
        # Validate input
        if not file_paths:
            return "Error: No file paths provided for analysis"

        # Check if files exist
        missing_files = []
        for file_path in file_paths:
            if not os.path.exists(file_path):
                missing_files.append(file_path)

        if missing_files:
            return f"Error: Files not found: {', '.join(missing_files)}"

        # Initialize diagnostics tool
        diagnostics_tool = DiagnosticsTool()
        diagnostics_tool.min_severity = min_sev

        # Run diagnostics
        diagnostics = diagnostics_tool.get_diagnostics(file_paths)

        # Format and return results
        formatted_result = diagnostics_tool.format_diagnostics(diagnostics, show_context=True)

        # Add summary information
        if "No diagnostics found" in formatted_result:
            res = f"✅ Code analysis completed successfully.\n\nFiles analyzed: {len(file_paths)}\nResult: No issues found at {min_severity} level or above."
        else:
            res = f"📊 Code analysis completed.\n\nFiles analyzed: {len(file_paths)}\n\n{formatted_result}"
        return res

    except ImportError as e:
        return f"Error: Failed to import diagnostics tool: {str(e)}"

    except Exception as e:
        return f"Error during code analysis: {str(e)}"
