"""
Code Agent Tools Package

This package contains synchronous tools for the enhanced scene code generation agent.
All tools are designed to work with the camel framework's FunctionTool wrapper.
Smolagents-compatible adapters are also available in the smolagents_adapters module.
"""

from .bash_execution import bash_execute
from .code_diagnostics import check_code_issues
from .context7_tools import get_library_docs, resolve_library_id
from .file_operations import file_create, file_view
from .file_search import search_files
from .precise_file_editor import replace_in_file
from .sequential_thinking import sequential_thinking

# Smolagents adapters (optional import)
try:
    from . import smolagents_adapters  # noqa: F401

    SMOLAGENTS_ADAPTERS_AVAILABLE = True
except ImportError:
    SMOLAGENTS_ADAPTERS_AVAILABLE = False

__all__ = [
    "sequential_thinking",
    "file_view",
    "file_create",
    "search_files",
    "replace_in_file",
    "bash_execute",
    "check_code_issues",
    "resolve_library_id",
    "get_library_docs",
]

if SMOLAGENTS_ADAPTERS_AVAILABLE:
    __all__.append("smolagents_adapters")
