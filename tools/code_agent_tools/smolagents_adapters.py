"""
Smolagents Tool Adapters

This module provides adapters to convert existing code agent tools to smolagents-compatible tools.
All tools are wrapped with the @tool decorator to make them compatible with smolagents framework.
"""

from typing import Optional

from smolagents import tool

from .bash_execution import bash_execute as _bash_execute
from .code_diagnostics import check_code_issues as _check_code_issues
from .context7_tools import get_library_docs as _get_library_docs
from .context7_tools import resolve_library_id as _resolve_library_id
from .file_operations import file_create as _file_create
from .file_operations import file_view as _file_view
from .file_operations import list_files as _list_files
from .file_search import search_files as _search_files
from .precise_file_editor import replace_in_file as _replace_in_file

# Import existing tools
from .sequential_thinking import sequential_thinking as _sequential_thinking


@tool
def sequential_thinking(
    thought: str,
    thought_number: int,
    total_thoughts: int,
    next_thought_needed: bool = True,
    is_revision: bool = False,
    revises_thought: Optional[int] = None,
    branch_from_thought: Optional[int] = None,
    branch_id: Optional[str] = None,
    needs_more_thoughts: bool = False,
) -> str:
    """
    A detailed tool for dynamic and reflective problem-solving through thoughts.
    This tool helps analyze problems through a flexible thinking process that can adapt and evolve.
    Each thought can build on, question, or revise previous insights as understanding deepens.

    When to use this tool:
    - Breaking down complex problems into steps
    - Planning and design with room for revision
    - Analysis that might need course correction
    - Problems where the full scope might not be clear initially
    - Problems that require a multi-step solution
    - Tasks that need to maintain context over multiple steps
    - Situations where irrelevant information needs to be filtered out

    Key features:
    - You can adjust total_thoughts up or down as you progress
    - You can question or revise previous thoughts
    - You can add more thoughts even after reaching what seemed like the end
    - You can express uncertainty and explore alternative approaches
    - Not every thought needs to build linearly - you can branch or backtrack
    - Generates a solution hypothesis
    - Verifies the hypothesis based on the Chain of Thought steps
    - Repeats the process until satisfied
    - Provides a correct answer

    Parameters explained:
    - thought: Your current thinking step, which can include:
      * Regular analytical steps
      * Revisions of previous thoughts
      * Questions about previous decisions
      * Realizations about needing more analysis
      * Changes in approach
      * Hypothesis generation
      * Hypothesis verification
    - next_thought_needed: True if you need more thinking, even if at what seemed like the end
    - thought_number: Current number in sequence (can go beyond initial total if needed)
    - total_thoughts: Current estimate of thoughts needed (can be adjusted up/down)
    - is_revision: A boolean indicating if this thought revises previous thinking
    - revises_thought: If is_revision is true, which thought number is being reconsidered
    - branch_from_thought: If branching, which thought number is the branching point
    - branch_id: Identifier for the current branch (if any)
    - needs_more_thoughts: If reaching end but realizing more thoughts needed

    You should:
    1. Start with an initial estimate of needed thoughts, but be ready to adjust
    2. Feel free to question or revise previous thoughts
    3. Don't hesitate to add more thoughts if needed, even at the "end"
    4. Express uncertainty when present
    5. Mark thoughts that revise previous thinking or branch into new paths
    6. Ignore information that is irrelevant to the current step
    7. Generate a solution hypothesis when appropriate
    8. Verify the hypothesis based on the Chain of Thought steps
    9. Repeat the process until satisfied with the solution
    10. Provide a single, ideally correct answer as the final output
    11. Only set next_thought_needed to false when truly done and a satisfactory answer is reached

    Args:
        thought: Current thinking step
        thought_number: Current thought number (minimum 1)
        total_thoughts: Estimated total thoughts needed (minimum 1)
        next_thought_needed: Whether another thought step is needed
        is_revision: Whether this revises previous thinking
        revises_thought: Which thought is being reconsidered (minimum 1 if provided)
        branch_from_thought: Branching point thought number (minimum 1 if provided)
        branch_id: Branch identifier
        needs_more_thoughts: If more thoughts are needed

    Returns:
        Thinking result and status information
    """
    return _sequential_thinking(
        thought=thought,
        thought_number=thought_number,
        total_thoughts=total_thoughts,
        next_thought_needed=next_thought_needed,
        is_revision=is_revision,
        revises_thought=revises_thought,
        branch_from_thought=branch_from_thought,
        branch_id=branch_id,
        needs_more_thoughts=needs_more_thoughts,
    )


@tool
def file_view(path: str, view_range: Optional[list[int]] = None) -> str:
    """
    View file content with optional line range specification.

    This tool is essential for understanding existing code structure, reading configuration files,
    and examining generated code before making modifications. Use this tool when you need to:
    - Understand the current state of a file before editing
    - Read configuration or template files
    - Examine generated code for debugging
    - Check file content after modifications

    Args:
        path: Absolute or relative file path to view
        view_range: Optional list [start_line, end_line] to view specific lines.
                   Use -1 as end_line to view from start_line to end of file.
                   Line numbers are 1-based and inclusive.

    Returns:
        File content as string, or error message if file cannot be read

    Examples:
        - file_view("/path/to/file.py") - View entire file
        - file_view("/path/to/file.py", [10, 20]) - View lines 10-20
        - file_view("/path/to/file.py", [50, -1]) - View from line 50 to end
    """
    return _file_view(path=path, view_range=view_range)


@tool
def file_create(path: str, content: str) -> str:
    """
    Create a new file with specified content.

    This tool is used for generating new code files, configuration files, or any text-based files.
    The tool automatically creates parent directories if they don't exist. Use this tool when you need to:
    - Generate new Python/code files from scratch
    - Create configuration files
    - Generate documentation or README files
    - Create test files or example scripts

    Important: This tool will overwrite existing files without warning. Use file_view first to check
    if a file exists if you want to avoid overwriting.

    Args:
        path: Absolute or relative file path where the file should be created
        content: Complete file content as a string

    Returns:
        Success message with file path, or error message if creation fails

    Examples:
        - file_create("/path/to/new_script.py", "print('Hello World')")
        - file_create("config.json", '{"setting": "value"}')
    """
    return _file_create(path=path, content=content)


@tool
def search_files(
    path: str, regex: str, file_pattern: Optional[str] = None, context_lines: int = 2, max_matches: int = 100
) -> str:
    """
    Perform a regex search across files in a directory or search within a single file.

    This tool searches for patterns or specific content across multiple files in a directory,
    or within a single file if a file path is provided. It displays each match with
    encapsulating context.

    Args:
        path: The path to search in (relative or absolute). Can be:
              - A directory: will recursively search all files in the directory
              - A single file: will search only within that file
        regex: The regular expression pattern to search for. Uses Python regex syntax.
               For literal text search, use re.escape() or simple strings without special chars.
        file_pattern: Optional glob pattern to filter files (e.g., '*.py' for Python files).
                     Only used when path is a directory. If not provided, searches all text files.
        context_lines: Number of context lines to show before and after each match (default: 2).
        max_matches: Maximum number of matches to return to avoid overwhelming output (default: 100).

    Returns:
        Formatted string containing search results with context, or error message if search fails.

    Examples:
        - search_files("src", "def.*function") - Find function definitions in src directory
        - search_files(".", "import.*torch", "*.py") - Find torch imports in Python files
        - search_files("docs", "TODO|FIXME", "*.md") - Find TODO/FIXME comments in markdown files
        - search_files("main.py", "class.*:") - Find class definitions in main.py file
        - search_files("config.json", "database") - Find "database" text in config.json
    """
    return _search_files(
        path=path,
        regex=regex,
        file_pattern=file_pattern,
        context_lines=context_lines,
        max_matches=max_matches,
    )


@tool
def replace_in_file(path: str, diff: str) -> str:
    """
    Replace sections of content in an existing file using SEARCH/REPLACE blocks.

    This tool makes targeted edits to specific parts of an existing file without overwriting
    the entire file. It follows Cline's specification for precise file editing.

    SEARCH/REPLACE block format:
    ```
    <<<<<<< SEARCH
    [exact content to find]
    =======
    [new content to replace with]
    >>>>>>> REPLACE
    ```

    Args:
        path: The path of the file to modify (absolute or relative)
        diff: One or more SEARCH/REPLACE blocks following the exact format

    Returns:
        Success message with file path, or detailed error message if edit fails
    """
    return _replace_in_file(path=path, diff=diff)


@tool
def bash_execute(command: str, timeout: Optional[int] = None) -> str:
    """
    Execute bash commands synchronously with comprehensive error handling and output capture.

    This tool enables the agent to interact with the system environment, run build tools,
    execute scripts, and perform system operations. Use this tool when you need to:
    - Run Python scripts or other executables to test generated code
    - Execute build commands (pip install, npm install, etc.)
    - Run linters, formatters, or code analysis tools
    - Execute git commands for version control operations
    - Run system utilities (ls, find, grep, etc.)
    - Test command-line tools or scripts
    - Execute Manim rendering commands
    - Run unit tests or integration tests

    Security considerations:
    - Commands are executed in the current working directory
    - No shell injection protection - use carefully with user input
    - Commands run with current user permissions
    - Timeout prevents hanging processes

    Args:
        command: Bash command to execute (string)
        timeout: Maximum execution time in seconds (default: None)

    Returns:
        Command output (stdout) on success, or error message with stderr on failure

    Examples:
        - bash_execute("python --version") - Check Python version
        - bash_execute("pip install manim", timeout=60) - Install Python package
        - bash_execute("manim scene.py SceneName -ql", timeout=180) - Render Manim scene
        - bash_execute("python -m pytest tests/") - Run tests
        - bash_execute("ls -la") - List directory contents
        - bash_execute("git status") - Check git status
    """
    return _bash_execute(command=command, timeout=timeout)


@tool
def check_code_issues(file_paths: list[str], min_severity: str = "error") -> str:
    """
    Analyze code files for syntax errors, import issues, undefined variables, and other problems.

    This tool is essential for ensuring code quality and catching errors before execution.
    It performs static analysis on Python files to detect various types of issues.
    Use this tool when you need to:
    - Validate generated code for syntax errors
    - Check for undefined variables or functions
    - Detect import problems and missing modules
    - Identify type-related issues
    - Verify code quality before execution
    - Debug compilation or runtime errors
    - Ensure code follows Python best practices

    The tool analyzes multiple types of issues:
    - Syntax errors (invalid Python syntax)
    - Import errors (missing modules, circular imports)
    - Name errors (undefined variables, functions, classes)
    - Type errors (type mismatches, invalid operations)
    - Style issues (PEP 8 violations, code smells)
    - Logic errors (unreachable code, unused variables)

    Severity levels:
    - error: Critical issues that prevent code execution
    - warning: Potential problems that may cause runtime issues
    - info: Style and best practice suggestions
    - hint: Minor improvements and optimizations

    Args:
        file_paths: List of absolute or relative file paths to analyze
        min_severity: Minimum severity level to report ("error", "warning", "info", "hint")
                     Default is "error" to show errors only

    Returns:
        Formatted diagnostic report with issue details, locations, and context,
        or error message if analysis fails

    Examples:
        - check_code_issues(["/path/to/script.py"], "info") - Check single file for all issues
        - check_code_issues(["/path/to/script.py"], "error") - Only show critical errors
        - check_code_issues(["file1.py", "file2.py"], "warning") - Check multiple files for warnings and errors

    Output format:
        The tool returns a detailed report showing:
        - File path and line numbers
        - Issue severity and type
        - Descriptive error messages
        - Code context around issues
        - Suggestions for fixes when available
    """
    return _check_code_issues(file_paths=file_paths, min_severity=min_severity)


@tool
def resolve_library_id(library_name: str) -> str:
    """
    Resolves a package/product name to a Context7-compatible library ID and returns a list of matching libraries.

    You MUST call this function before 'get_library_docs' to obtain a valid Context7-compatible library ID
    UNLESS the user explicitly provides a library ID in the format '/org/project' or '/org/project/version'
    in their query.

    Selection Process:
    1. Analyze the query to understand what library/package the user is looking for
    2. Return the most relevant match based on:
    - Name similarity to the query (exact matches prioritized)
    - Description relevance to the query's intent
    - Documentation coverage (prioritize libraries with higher Code Snippet counts)
    - Trust score (consider libraries with scores of 7-10 more authoritative)

    Response Format:
    - Return the selected library ID in a clearly marked section
    - Provide a brief explanation for why this library was chosen
    - If multiple good matches exist, acknowledge this but proceed with the most relevant one
    - If no good matches exist, clearly state this and suggest query refinements

    For ambiguous queries, request clarification before proceeding with a best-guess match.

    Args:
        library_name: Library name to search for and retrieve a Context7-compatible library ID.

    Returns:
        Formatted search results with library information
    """
    return _resolve_library_id(library_name=library_name)


@tool
def get_library_docs(
    context7_compatible_library_id: str,
    tokens: int = 10000,
    topic: str = "",
) -> str:
    """
    Fetches up-to-date documentation for a library. You must call 'resolve_library_id' first to obtain
    the exact Context7-compatible library ID required to use this tool, UNLESS the user explicitly
    provides a library ID in the format '/org/project' or '/org/project/version' in their query.

    Args:
        context7_compatible_library_id: Exact Context7-compatible library ID (e.g., '/mongodb/docs',
                                      '/vercel/next.js', '/supabase/supabase', '/vercel/next.js/v14.3.0-canary.87')
                                      retrieved from 'resolve_library_id' or directly from user query
                                      in the format '/org/project' or '/org/project/version'.
        tokens: Optional Maximum number of tokens of documentation to retrieve (default: 10000).
               Higher values provide more context but consume more tokens.
        topic: Topic to focus documentation on (e.g., 'hooks', 'routing').

    Returns:
        Library documentation text or error message
    """
    return _get_library_docs(
        context7_compatible_library_id=context7_compatible_library_id,
        tokens=tokens,
        topic=topic,
    )


@tool
def list_files(path: str, recursive: bool = False) -> str:
    """
    List files and directories within the specified directory.

    This tool is essential for understanding project structure, exploring codebases,
    and discovering files for further analysis. Use this tool when you need to:
    - Get an overview of project structure
    - Find specific files or directories
    - Explore unfamiliar codebases
    - Check if files exist in a directory
    - Understand directory organization

    Args:
        path: Directory path to list contents for (relative to current working directory).
              Use "." for current directory, or specify relative/absolute paths.
        recursive: Whether to list files recursively. If True, lists all files and directories
                  recursively. If False (default), only lists top-level contents.

    Returns:
        Formatted string listing of files and directories, or error message if listing fails.
        Directories are shown with trailing "/" to distinguish from files.

    Usage Examples:
        - list_files(".") - List current directory contents
        - list_files("src") - List contents of src directory
        - list_files("src", recursive=True) - Recursively list all files in src directory
        - list_files("../other_project") - List contents of sibling directory
    """
    return _list_files(path=path, recursive=recursive)
