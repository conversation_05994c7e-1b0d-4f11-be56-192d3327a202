"""
Sequential Thinking Tool for Complex Problem Analysis

This tool helps analyze problems through a flexible thinking process that can adapt and evolve.
Each thought can build on, question, or revise previous insights as understanding deepens.
"""

import json
import time
from dataclasses import dataclass
from typing import Optional


@dataclass
class ThoughtData:
    """Data structure for storing thought information."""

    thought: str
    thought_number: int
    total_thoughts: int
    next_thought_needed: bool
    is_revision: bool = False
    revises_thought: Optional[int] = None
    branch_from_thought: Optional[int] = None
    branch_id: Optional[str] = None
    needs_more_thoughts: bool = False
    timestamp: float = 0.0


def _format_thought(thought_data: ThoughtData) -> str:
    """Format a thought for display with visual styling."""
    prefix = ""
    context = ""

    if thought_data.is_revision:
        prefix = "🔄 Revision"
        context = f" (revising thought {thought_data.revises_thought})"
    elif thought_data.branch_from_thought:
        prefix = "🌿 Branch"
        context = f" (from thought {thought_data.branch_from_thought}, ID: {thought_data.branch_id})"
    else:
        prefix = "💭 Thought"
        context = ""

    header = f"{prefix} {thought_data.thought_number}/{thought_data.total_thoughts}{context}"
    border_length = max(len(header), *[len(line) for line in thought_data.thought.split("\n")]) + 4
    border = "─" * border_length

    return f"""
┌{border}┐
│ {header.ljust(border_length - 2)} │
├{border}┤
│ {thought_data.thought.ljust(border_length - 2)} │
└{border}┘"""


def sequential_thinking(
    thought: str,
    thought_number: int,
    total_thoughts: int,
    next_thought_needed: bool = True,
    is_revision: bool = False,
    revises_thought: Optional[int] = None,
    branch_from_thought: Optional[int] = None,
    branch_id: Optional[str] = None,
    needs_more_thoughts: bool = False,
) -> str:
    """
    A detailed tool for dynamic and reflective problem-solving through thoughts.
    This tool helps analyze problems through a flexible thinking process that can adapt and evolve.
    Each thought can build on, question, or revise previous insights as understanding deepens.

    When to use this tool:
    - Breaking down complex problems into steps
    - Planning and design with room for revision
    - Analysis that might need course correction
    - Problems where the full scope might not be clear initially
    - Problems that require a multi-step solution
    - Tasks that need to maintain context over multiple steps
    - Situations where irrelevant information needs to be filtered out

    Key features:
    - You can adjust total_thoughts up or down as you progress
    - You can question or revise previous thoughts
    - You can add more thoughts even after reaching what seemed like the end
    - You can express uncertainty and explore alternative approaches
    - Not every thought needs to build linearly - you can branch or backtrack
    - Generates a solution hypothesis
    - Verifies the hypothesis based on the Chain of Thought steps
    - Repeats the process until satisfied
    - Provides a correct answer

    Parameters explained:
    - thought: Your current thinking step, which can include:
      * Regular analytical steps
      * Revisions of previous thoughts
      * Questions about previous decisions
      * Realizations about needing more analysis
      * Changes in approach
      * Hypothesis generation
      * Hypothesis verification
    - next_thought_needed: True if you need more thinking, even if at what seemed like the end
    - thought_number: Current number in sequence (can go beyond initial total if needed)
    - total_thoughts: Current estimate of thoughts needed (can be adjusted up/down)
    - is_revision: A boolean indicating if this thought revises previous thinking
    - revises_thought: If is_revision is true, which thought number is being reconsidered
    - branch_from_thought: If branching, which thought number is the branching point
    - branch_id: Identifier for the current branch (if any)
    - needs_more_thoughts: If reaching end but realizing more thoughts needed

    You should:
    1. Start with an initial estimate of needed thoughts, but be ready to adjust
    2. Feel free to question or revise previous thoughts
    3. Don't hesitate to add more thoughts if needed, even at the "end"
    4. Express uncertainty when present
    5. Mark thoughts that revise previous thinking or branch into new paths
    6. Ignore information that is irrelevant to the current step
    7. Generate a solution hypothesis when appropriate
    8. Verify the hypothesis based on the Chain of Thought steps
    9. Repeat the process until satisfied with the solution
    10. Provide a single, ideally correct answer as the final output
    11. Only set next_thought_needed to false when truly done and a satisfactory answer is reached

    Args:
        thought: Current thinking step
        thought_number: Current thought number (minimum 1)
        total_thoughts: Estimated total thoughts needed (minimum 1)
        next_thought_needed: Whether another thought step is needed
        is_revision: Whether this revises previous thinking
        revises_thought: Which thought is being reconsidered (minimum 1 if provided)
        branch_from_thought: Branching point thought number (minimum 1 if provided)
        branch_id: Branch identifier
        needs_more_thoughts: If more thoughts are needed

    Returns:
        Thinking result and status information
    """
    # Simple state management using function attribute
    if not hasattr(sequential_thinking, "thought_history"):
        sequential_thinking.thought_history = []

    # Validate inputs
    if thought_number < 1:
        return "Error: thought_number must be at least 1"
    if total_thoughts < 1:
        return "Error: total_thoughts must be at least 1"
    if revises_thought is not None and revises_thought < 1:
        return "Error: revises_thought must be at least 1 if provided"
    if branch_from_thought is not None and branch_from_thought < 1:
        return "Error: branch_from_thought must be at least 1 if provided"

    # Adjust total thoughts if current thought number exceeds it
    if thought_number > total_thoughts:
        total_thoughts = thought_number

    # Store thought data using ThoughtData dataclass
    thought_data = ThoughtData(
        thought=thought,
        thought_number=thought_number,
        total_thoughts=total_thoughts,
        next_thought_needed=next_thought_needed,
        is_revision=is_revision,
        revises_thought=revises_thought,
        branch_from_thought=branch_from_thought,
        branch_id=branch_id,
        needs_more_thoughts=needs_more_thoughts,
        timestamp=time.time(),
    )

    sequential_thinking.thought_history.append(thought_data)

    # Format and display the thought for debugging
    formatted_thought = _format_thought(thought_data)
    print(formatted_thought, flush=True)  # Print to stdout for immediate feedback

    # Format response with additional debugging info
    status = {
        "thought_number": thought_data.thought_number,
        "total_thoughts": thought_data.total_thoughts,
        "next_thought_needed": thought_data.next_thought_needed,
        "is_revision": thought_data.is_revision,
        "revises_thought": thought_data.revises_thought,
        "branch_from_thought": thought_data.branch_from_thought,
        "branch_id": thought_data.branch_id,
        "thought_history_length": len(sequential_thinking.thought_history),
        "timestamp": thought_data.timestamp,
    }

    return f"Sequential thinking step completed.\n\nStatus:\n{json.dumps(status, indent=2, ensure_ascii=False)}"
