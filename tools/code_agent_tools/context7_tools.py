"""
Context7 Documentation Tools

This module provides local tools for accessing Context7 documentation API,
converted from the original MCP server implementation.
"""

from dataclasses import dataclass
from enum import Enum
from typing import Optional

import requests
from loguru import logger

# Constants
CONTEXT7_API_BASE_URL = "https://context7.com/api"
DEFAULT_DOC_TOKENS = 1500
DEFAULT_TYPE = "txt"
REQUEST_TIMEOUT = 30  # seconds


class DocumentState(Enum):
    """Document state enumeration."""

    INITIAL = "initial"
    FINALIZED = "finalized"
    ERROR = "error"
    DELETE = "delete"


@dataclass
class SearchResult:
    """Data structure for search result information."""

    id: str
    title: str
    description: str
    branch: str
    lastUpdateDate: str
    state: DocumentState
    totalTokens: int
    totalSnippets: int
    totalPages: int
    stars: Optional[int] = None
    trustScore: Optional[int] = None
    versions: Optional[list[str]] = None


@dataclass
class SearchResponse:
    """Data structure for search response."""

    results: list[SearchResult]
    error: Optional[str] = None


def _search_libraries_api(query: str) -> SearchResponse:
    """
    Internal function to call Context7 search API.

    Args:
        query: The search query string

    Returns:
        SearchResponse object with results or error
    """
    logger.info(f"[tool] Searching for libraries matching query: {query}")
    try:
        url = f"{CONTEXT7_API_BASE_URL}/v1/search"
        params = {"query": query}

        response = requests.get(url, params=params, timeout=REQUEST_TIMEOUT)

        if response.status_code == 429:
            return SearchResponse(results=[], error="Rate limited due to too many requests. Please try again later.")

        if not response.ok:
            return SearchResponse(
                results=[],
                error=f"Failed to search libraries. Please try again later. Error code: {response.status_code}",
            )

        data = response.json()

        # Convert raw data to SearchResult objects
        results = []
        for item in data.get("results", []):
            try:
                result = SearchResult(
                    id=item.get("id", ""),
                    title=item.get("title", ""),
                    description=item.get("description", ""),
                    branch=item.get("branch", ""),
                    lastUpdateDate=item.get("lastUpdateDate", ""),
                    state=DocumentState(item.get("state", "initial")),
                    totalTokens=item.get("totalTokens", 0),
                    totalSnippets=item.get("totalSnippets", 0),
                    totalPages=item.get("totalPages", 0),
                    stars=item.get("stars"),
                    trustScore=item.get("trustScore"),
                    versions=item.get("versions"),
                )
                results.append(result)
            except (KeyError, ValueError):
                # Skip malformed results
                continue

        return SearchResponse(results=results)

    except requests.RequestException as e:
        return SearchResponse(results=[], error=f"Error searching libraries: {str(e)}")
    except Exception as e:
        return SearchResponse(results=[], error=f"Unexpected error searching libraries: {str(e)}")


def _fetch_library_documentation_api(
    library_id: str, tokens: int = DEFAULT_DOC_TOKENS, topic: str = ""
) -> Optional[str]:
    """
    Internal function to fetch library documentation from Context7 API.

    Args:
        library_id: The library ID (with or without leading slash)
        tokens: Maximum number of tokens to retrieve
        topic: Optional topic to focus on

    Returns:
        Documentation text or None if failed
    """
    logger.info(f"[tool] Fetching documentation for library: {library_id}, tokens: {tokens}, topic: {topic}")
    if tokens > 10000:
        tokens = 10000
        logger.info("[tool] Reducing tokens to 10000 to save cost")
    try:
        # Remove leading slash if present
        if library_id.startswith("/"):
            library_id = library_id[1:]

        url = f"{CONTEXT7_API_BASE_URL}/v1/{library_id}"
        params = {"type": DEFAULT_TYPE, "tokens": str(tokens)}

        if topic:
            params["topic"] = topic

        headers = {"X-Context7-Source": "mcp-server"}

        response = requests.get(url, params=params, headers=headers, timeout=REQUEST_TIMEOUT)

        if response.status_code == 429:
            return "Rate limited due to too many requests. Please try again later."

        if not response.ok:
            return f"Failed to fetch documentation. Please try again later. Error code: {response.status_code}"

        text = response.text

        if not text or text == "No content available" or text == "No context data available":
            return None

        return text

    except requests.RequestException as e:
        return f"Error fetching library documentation. Please try again later. {str(e)}"
    except Exception as e:
        return f"Unexpected error fetching library documentation. Please try again later. {str(e)}"


def _format_search_result(result: SearchResult) -> str:
    """
    Format a search result into a human-readable string representation.
    Only shows code snippet count and trust score when available.

    Args:
        result: The SearchResult object to format

    Returns:
        A formatted string with library information
    """
    formatted_result = [
        f"- Title: {result.title}",
        f"- Context7-compatible library ID: {result.id}",
        f"- Description: {result.description}",
    ]

    # Only add code snippets count if it's a valid value
    if result.totalSnippets != -1 and result.totalSnippets is not None:
        formatted_result.append(f"- Code Snippets: {result.totalSnippets}")

    # Only add trust score if it's a valid value
    if result.trustScore is not None and result.trustScore != -1:
        formatted_result.append(f"- Trust Score: {result.trustScore}")

    # Only add versions if available
    if result.versions and len(result.versions) > 0:
        formatted_result.append(f"- Versions: {', '.join(result.versions)}")

    return "\n".join(formatted_result)


def _format_search_results(search_response: SearchResponse) -> str:
    """
    Format a search response into a human-readable string representation.

    Args:
        search_response: The SearchResponse object to format

    Returns:
        A formatted string with search results
    """
    if not search_response.results or len(search_response.results) == 0:
        return "No documentation libraries found matching your query."

    formatted_results = [_format_search_result(result) for result in search_response.results]
    return "\n----------\n".join(formatted_results)


def resolve_library_id(library_name: str) -> str:
    """
    Resolves a package/product name to a Context7-compatible library ID and returns a list of matching libraries.

    You MUST call this function before 'get_library_docs' to obtain a valid Context7-compatible library ID
    UNLESS the user explicitly provides a library ID in the format '/org/project' or '/org/project/version'
    in their query.

    Selection Process:
    1. Analyze the query to understand what library/package the user is looking for
    2. Return the most relevant match based on:
    - Name similarity to the query (exact matches prioritized)
    - Description relevance to the query's intent
    - Documentation coverage (prioritize libraries with higher Code Snippet counts)
    - Trust score (consider libraries with scores of 7-10 more authoritative)

    Response Format:
    - Return the selected library ID in a clearly marked section
    - Provide a brief explanation for why this library was chosen
    - If multiple good matches exist, acknowledge this but proceed with the most relevant one
    - If no good matches exist, clearly state this and suggest query refinements

    For ambiguous queries, request clarification before proceeding with a best-guess match.

    Args:
        library_name: Library name to search for and retrieve a Context7-compatible library ID.

    Returns:
        Formatted search results with library information
    """
    search_response = _search_libraries_api(library_name)

    if search_response.error:
        return search_response.error

    if not search_response.results or len(search_response.results) == 0:
        return "Failed to retrieve library documentation data from Context7"

    results_text = _format_search_results(search_response)

    return f"""Available Libraries (top matches):

Each result includes:
- Library ID: Context7-compatible identifier (format: /org/project)
- Name: Library or package name
- Description: Short summary
- Code Snippets: Number of available code examples
- Trust Score: Authority indicator
- Versions: List of versions if available. Use one of those versions if and only if the user explicitly provides a version in their query.

For best results, select libraries based on name match, trust score, snippet coverage, and relevance to your use case.

----------

{results_text}"""


def get_library_docs(context7_compatible_library_id: str, tokens: int = DEFAULT_DOC_TOKENS, topic: str = "") -> str:
    """
    Fetches up-to-date documentation for a library. You must call 'resolve_library_id' first to obtain
    the exact Context7-compatible library ID required to use this tool, UNLESS the user explicitly
    provides a library ID in the format '/org/project' or '/org/project/version' in their query.

    Args:
        context7_compatible_library_id: Exact Context7-compatible library ID (e.g., '/mongodb/docs',
                                      '/vercel/next.js', '/supabase/supabase', '/vercel/next.js/v14.3.0-canary.87')
                                      retrieved from 'resolve_library_id' or directly from user query
                                      in the format '/org/project' or '/org/project/version'.
        tokens: Optional Maximum number of tokens of documentation to retrieve (default: 10000).
               Higher values provide more context but consume more tokens.
        topic: Topic to focus documentation on (e.g., 'hooks', 'routing').

    Returns:
        Library documentation text or error message
    """
    fetch_docs_response = _fetch_library_documentation_api(context7_compatible_library_id, tokens=tokens, topic=topic)

    if fetch_docs_response is None:
        return (
            "Documentation not found or not finalized for this library. This might have happened "
            "because you used an invalid Context7-compatible library ID. To get a valid "
            "Context7-compatible library ID, use the 'resolve_library_id' with the package name "
            "you wish to retrieve documentation for."
        )

    return fetch_docs_response
