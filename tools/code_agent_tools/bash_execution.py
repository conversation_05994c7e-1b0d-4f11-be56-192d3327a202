"""
Bash Command Execution Tool for Code Agent

Synchronous bash command execution with timeout and error handling.
This tool enables the agent to run system commands, execute scripts, and interact with the environment.
"""

import subprocess
from typing import Optional

from loguru import logger


def bash_execute(command: str, timeout: Optional[int] = None) -> str:
    """
    Execute bash commands synchronously with comprehensive error handling and output capture.

    This tool enables the agent to interact with the system environment, run build tools,
    execute scripts, and perform system operations. Use this tool when you need to:
    - Run Python scripts or other executables to test generated code
    - Execute build commands (pip install, npm install, etc.)
    - Run linters, formatters, or code analysis tools
    - Execute git commands for version control operations
    - Run system utilities (ls, find, grep, etc.)
    - Test command-line tools or scripts
    - Execute Manim rendering commands
    - Run unit tests or integration tests

    Security considerations:
    - Commands are executed in the current working directory
    - No shell injection protection - use carefully with user input
    - Commands run with current user permissions
    - Timeout prevents hanging processes

    Args:
        command: Bash command to execute (string)
        timeout: Maximum execution time in seconds (default: None)

    Returns:
        Command output (stdout) on success, or error message with stderr on failure

    Examples:
        - bash_execute("python --version") - Check Python version
        - bash_execute("pip install manim", timeout=60) - Install Python package
        - bash_execute("manim scene.py SceneName -ql", timeout=180) - Render Manim scene
        - bash_execute("python -m pytest tests/") - Run tests
        - bash_execute("ls -la") - List directory contents
        - bash_execute("git status") - Check git status
    """
    logger.info(f"[tool] Executing bash command: {command}")
    try:
        # Execute command with timeout
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout,
            cwd=None,  # Use current working directory
        )

        # Check return code
        if result.returncode != 0:
            error_msg = f"Command failed (exit code {result.returncode})"
            if result.stderr:
                error_msg += f"\nStderr: {result.stderr.strip()}"
            if result.stdout:
                error_msg += f"\nStdout: {result.stdout.strip()}"
            return error_msg

        # Return stdout or success message
        output = "Command executed successfully\n" + result.stdout.strip() if result.stdout else ""

        return output

    except subprocess.TimeoutExpired:
        return f"Command timed out after {timeout} seconds: {command}"

    except subprocess.CalledProcessError as e:
        error_msg = f"Command failed with exit code {e.returncode}: {command}"
        if e.stderr:
            error_msg += f"\nStderr: {e.stderr}"
        if e.stdout:
            error_msg += f"\nStdout: {e.stdout}"
        return error_msg

    except FileNotFoundError:
        return f"Command not found or shell not available: {command}"

    except PermissionError:
        return f"Permission denied executing command: {command}"

    except Exception as e:
        return f"Unexpected error executing command '{command}': {str(e)}"
