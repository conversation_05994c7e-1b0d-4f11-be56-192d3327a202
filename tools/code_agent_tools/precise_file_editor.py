"""
Precise File Editor Tool for Code Agent

This module implements the replace_in_file tool following <PERSON><PERSON>'s specification.
It provides precise, safe file editing using SEARCH/REPLACE blocks with exact matching.
"""

import os
import re

from loguru import logger


def replace_in_file(path: str, diff: str) -> str:
    """
    Replace sections of content in an existing file using SEARCH/REPLACE blocks.

    This tool makes targeted edits to specific parts of an existing file without overwriting
    the entire file. When editing file, ALWAYS use `replace_in_file` tool,
    don't manually construct string replace calls.

    SEARCH/REPLACE block format:
    ```
    <<<<<<< SEARCH
    [exact content to find]
    =======
    [new content to replace with]
    >>>>>>> REPLACE
    ```

    Critical rules:
    1. SEARCH content must match the file section EXACTLY:
       - Character-for-character including whitespace, indentation, line endings
       - Include all comments, docstrings, etc.
    2. SEARCH/REPLACE blocks will ONLY replace the first match occurrence
    3. Keep SEARCH/REPLACE blocks concise:
       - Include just the changing lines, and a few surrounding lines if needed for uniqueness
       - Each line must be complete. Never truncate lines mid-way through
    4. Special operations:
       - To move code: Use two SEARCH/REPLACE blocks (one to delete + one to insert)
       - To delete code: Use empty REPLACE section

    Args:
        path: The path of the file to modify (absolute or relative)
        diff: One or more SEARCH/REPLACE blocks following the exact format

    Returns:
        Success message with file path, or detailed error message if edit fails

    Examples:
        # Simple replacement
        diff = '''
        <<<<<<< SEARCH
        import os
        =======
        import os
        import sys
        >>>>>>> REPLACE
        '''

        # Multiple replacements
        diff = '''
        <<<<<<< SEARCH
        def old_function():
            return False
        =======
        def new_function():
            return True
        >>>>>>> REPLACE

        <<<<<<< SEARCH
        print("Hello")
        =======
        print("Hello World")
        >>>>>>> REPLACE
        '''

        # Delete code (empty REPLACE section)
        diff = '''
        <<<<<<< SEARCH
        # This comment will be deleted
        old_code_to_remove()
        =======
        >>>>>>> REPLACE
        '''
    """
    logger.info(f"[tool] Replacing content in file: {path}")

    try:
        # Check if file exists
        if not os.path.exists(path):
            return f"Error: File not found: {path}"

        # Read original file content
        try:
            with open(path, encoding="utf-8") as f:
                original_content = f.read()
        except UnicodeDecodeError:
            return f"Error: Cannot decode file as UTF-8: {path}"
        except PermissionError:
            return f"Error: Permission denied reading file: {path}"

        # Parse SEARCH/REPLACE blocks
        search_replace_blocks = _parse_search_replace_blocks(diff)

        if not search_replace_blocks:
            return "Error: No valid SEARCH/REPLACE blocks found in diff content"

        # Apply all SEARCH/REPLACE operations
        new_content = original_content
        replacements_made = 0

        for i, (search_text, replace_text) in enumerate(search_replace_blocks):
            if search_text in new_content:
                # Replace only the first occurrence
                new_content = new_content.replace(search_text, replace_text, 1)
                replacements_made += 1
                logger.debug(
                    f"Applied SEARCH/REPLACE block {i+1}: replaced {len(search_text)} chars with {len(replace_text)} chars"
                )
            else:
                # Provide helpful error message with context
                return f"Error: Could not find SEARCH content in file (block {i+1}):\n\nSEARCH content:\n{search_text[:200]}{'...' if len(search_text) > 200 else ''}\n\nPlease ensure the SEARCH content matches exactly, including whitespace and indentation."

        # Write the modified content back to file
        try:
            with open(path, "w", encoding="utf-8") as f:
                f.write(new_content)
        except PermissionError:
            return f"Error: Permission denied writing to file: {path}"

        logger.success(f"Successfully applied {replacements_made} SEARCH/REPLACE operations to {path}")
        return f"File edited successfully: {path} ({replacements_made} replacements made)"

    except Exception as e:
        logger.error(f"Error in replace_in_file: {e}")
        return f"Error editing file {path}: {str(e)}"


def _parse_search_replace_blocks(diff: str) -> list[tuple[str, str]]:
    """
    Parse SEARCH/REPLACE blocks from diff content.

    Args:
        diff: String containing one or more SEARCH/REPLACE blocks

    Returns:
        List of (search_text, replace_text) tuples
    """
    blocks = []

    # Split the diff into individual SEARCH/REPLACE blocks
    # Use regex to find all blocks
    pattern = r"<{7}\s*SEARCH\s*\n(.*?)\n={7}\s*\n(.*?)\n>{7}\s*REPLACE"
    matches = re.findall(pattern, diff, re.DOTALL)

    for search_content, replace_content in matches:
        # Clean up the content (remove leading/trailing whitespace from the block boundaries)
        search_text = search_content.rstrip("\n")
        replace_text = replace_content.rstrip("\n")

        blocks.append((search_text, replace_text))

    return blocks
