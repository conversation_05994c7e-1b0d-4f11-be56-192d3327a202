"""
File Search Tools for Code Agent

This module provides file search functionality with regex pattern matching
across multiple files in a directory, similar to Cline's search_files tool.
"""

import os
import re
from pathlib import Path
from typing import Optional

from loguru import logger


def search_files(
    path: str, regex: str, file_pattern: Optional[str] = None, context_lines: int = 2, max_matches: int = 100
) -> str:
    """
    Perform a regex search across files in a directory or search within a single file.

    This tool searches for patterns or specific content across multiple files in a directory,
    or within a single file if a file path is provided. It displays each match with
    encapsulating context.

    Args:
        path: The path to search in (relative or absolute). Can be:
              - A directory: will recursively search all files in the directory
              - A single file: will search only within that file
        regex: The regular expression pattern to search for. Uses Python regex syntax.
               For literal text search, use re.escape() or simple strings without special chars.
        file_pattern: Optional glob pattern to filter files (e.g., '*.py' for Python files).
                     Only used when path is a directory. If not provided, searches all text files.
        context_lines: Number of context lines to show before and after each match (default: 2).
        max_matches: Maximum number of matches to return to avoid overwhelming output (default: 100).

    Returns:
        Formatted string containing search results with context, or error message if search fails.

    Examples:
        - search_files("src", "def.*function") - Find function definitions in src directory
        - search_files(".", "import.*torch", "*.py") - Find torch imports in Python files
        - search_files("docs", "TODO|FIXME", "*.md") - Find TODO/FIXME comments in markdown files
        - search_files("main.py", "class.*:") - Find class definitions in main.py file
        - search_files("config.json", "database") - Find "database" text in config.json
    """
    logger.info(f"[tool] Searching files in: {path}, pattern: {regex}, file_pattern: {file_pattern}")

    try:
        # Convert to Path object and resolve
        search_path = Path(path).resolve()

        if not search_path.exists():
            return f"Error: Path not found: {path}"

        # Check if it's a single file or directory
        is_single_file = search_path.is_file()
        is_directory = search_path.is_dir()

        if not is_single_file and not is_directory:
            return f"Error: Path is neither a file nor a directory: {path}"

        # Compile regex pattern
        try:
            pattern = re.compile(regex, re.MULTILINE)
        except re.error as e:
            return f"Error: Invalid regex pattern '{regex}': {str(e)}"

        results = []
        total_matches = 0
        files_searched = 0

        # Handle single file search
        if is_single_file:
            return _search_single_file(search_path, pattern, context_lines, max_matches)

        # Handle directory search
        # Determine file extensions to search if no pattern provided
        if file_pattern is None:
            # Default to common text file extensions
            text_extensions = {
                ".py",
                ".js",
                ".ts",
                ".jsx",
                ".tsx",
                ".java",
                ".cpp",
                ".c",
                ".h",
                ".cs",
                ".php",
                ".rb",
                ".go",
                ".rs",
                ".swift",
                ".kt",
                ".scala",
                ".html",
                ".css",
                ".scss",
                ".less",
                ".xml",
                ".json",
                ".yaml",
                ".yml",
                ".md",
                ".txt",
                ".rst",
                ".tex",
                ".sql",
                ".sh",
                ".bat",
                ".ps1",
                ".dockerfile",
                ".gitignore",
                ".env",
                ".cfg",
                ".ini",
                ".conf",
            }

        # Walk through directory recursively
        for root, dirs, files in os.walk(search_path):
            # Skip hidden directories and common build/cache directories
            dirs[:] = [
                d
                for d in dirs
                if not d.startswith(".")
                and d not in {"__pycache__", "node_modules", "build", "dist", "target", "bin", "obj"}
            ]

            for file in files:
                # Skip hidden files
                if file.startswith("."):
                    continue

                file_path = Path(root) / file

                # Apply file pattern filter
                if file_pattern:
                    if not file_path.match(file_pattern):
                        continue
                else:
                    # Check if file has a text extension
                    if file_path.suffix.lower() not in text_extensions:
                        continue

                # Search in this file
                file_results, file_matches = _search_in_file_content(
                    file_path, pattern, context_lines, max_matches - total_matches, search_path
                )

                if file_results:
                    files_searched += 1
                    results.extend(file_results)
                    total_matches += file_matches

                    if total_matches >= max_matches:
                        break

            if total_matches >= max_matches:
                break

        # Format final results
        if not results:
            return f"No matches found for pattern '{regex}' in {files_searched} files searched in {path}"

        header = f"🔍 Found {total_matches} matches for pattern '{regex}' in {files_searched} files:\n"
        header += "=" * 60

        return header + "".join(results)

    except Exception as e:
        logger.error(f"Error in search_files: {e}")
        return f"Error searching files: {str(e)}"


def _search_in_file_content(
    file_path: Path, pattern: re.Pattern, context_lines: int, max_matches: int, search_path: Optional[Path] = None
) -> tuple[list[str], int]:
    """
    Search for pattern matches within a file's content.

    Args:
        file_path: Path to the file to search
        pattern: Compiled regex pattern
        context_lines: Number of context lines to show
        max_matches: Maximum number of matches to return
        search_path: Base search path for relative path calculation (None for single file)

    Returns:
        Tuple of (results list, total_matches count)
    """
    try:
        # Read file content
        with open(file_path, encoding="utf-8", errors="ignore") as f:
            content = f.read()

        results = []
        total_matches = 0

        # Search for matches
        lines = content.split("\n")
        for line_num, line in enumerate(lines, 1):
            if pattern.search(line):
                if total_matches >= max_matches:
                    results.append(f"\n... (truncated after {max_matches} matches)")
                    break

                # Get context lines
                start_line = max(0, line_num - 1 - context_lines)
                end_line = min(len(lines), line_num + context_lines)

                context = []
                for i in range(start_line, end_line):
                    prefix = ">" if i == line_num - 1 else " "
                    context.append(f"{prefix} {i+1:4d}: {lines[i]}")

                # Format result with appropriate icon and path
                if search_path:
                    # Directory search - show relative path
                    relative_path = file_path.relative_to(search_path)
                    result = f"\n� {relative_path}:{line_num}\n" + "\n".join(context)
                else:
                    # Single file search - show just filename
                    result = f"\n�📄 {file_path.name}:{line_num}\n" + "\n".join(context)

                results.append(result)
                total_matches += 1

        return results, total_matches

    except (UnicodeDecodeError, PermissionError, OSError):
        # Return empty results for files that can't be read
        return [], 0


def _search_single_file(file_path: Path, pattern: re.Pattern, context_lines: int, max_matches: int) -> str:
    """
    Search within a single file for the given pattern.

    Args:
        file_path: Path to the file to search
        pattern: Compiled regex pattern
        context_lines: Number of context lines to show
        max_matches: Maximum number of matches to return

    Returns:
        Formatted search results
    """
    results, total_matches = _search_in_file_content(file_path, pattern, context_lines, max_matches)

    # Format final results
    if not results:
        return f"No matches found for pattern in file: {file_path}"

    header = f"🔍 Found {total_matches} matches in file '{file_path.name}':\n"
    header += "=" * 60

    return header + "".join(results)
