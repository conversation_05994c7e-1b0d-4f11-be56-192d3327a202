"""
File Operations Tools for Code Agent

Synchronous file operations including viewing, creating, editing files, and listing directories.
These tools are optimized for code generation and editing workflows.
"""

import os
from pathlib import Path
from typing import Optional

from loguru import logger


def file_view(path: str, view_range: Optional[list[int]] = None) -> str:
    """
    View file content with optional line range specification.

    This tool is essential for understanding existing code structure, reading configuration files,
    and examining generated code before making modifications. Use this tool when you need to:
    - Understand the current state of a file before editing
    - Read configuration or template files
    - Examine generated code for debugging
    - Check file content after modifications

    Args:
        path: Absolute or relative file path to view
        view_range: Optional list [start_line, end_line] to view specific lines.
                   Use -1 as end_line to view from start_line to end of file.
                   Line numbers are 1-based and inclusive.

    Returns:
        File content as string, or error message if file cannot be read

    Examples:
        - file_view("/path/to/file.py") - View entire file
        - file_view("/path/to/file.py", [10, 20]) - View lines 10-20
        - file_view("/path/to/file.py", [50, -1]) - View from line 50 to end
    """
    logger.info(f"[tool] Viewing file: {path}, view_range: {view_range}")
    try:
        with open(path, encoding="utf-8") as f:
            content = f.read()

        if view_range:
            lines = content.split("\n")
            start, end = view_range
            if end == -1:
                end = len(lines)
            # Convert to 0-based indexing for slicing
            content = "\n".join(lines[start - 1 : end])

        return content
    except FileNotFoundError:
        return f"Error: File not found: {path}"
    except PermissionError:
        return f"Error: Permission denied accessing file: {path}"
    except UnicodeDecodeError:
        return f"Error: Cannot decode file as UTF-8: {path}"
    except Exception as e:
        return f"Error reading file: {str(e)}"


def file_create(path: str, content: str) -> str:
    """
    Create a new file with specified content.

    This tool is used for generating new code files, configuration files, or any text-based files.
    The tool automatically creates parent directories if they don't exist. Use this tool when you need to:
    - Generate new Python/code files from scratch
    - Create configuration files
    - Generate documentation or README files
    - Create test files or example scripts

    Important: This tool will overwrite existing files without warning. Use file_view first to check
    if a file exists if you want to avoid overwriting.

    Args:
        path: Absolute or relative file path where the file should be created
        content: Complete file content as a string

    Returns:
        Success message with file path, or error message if creation fails

    Examples:
        - file_create("/path/to/new_script.py", "print('Hello World')")
        - file_create("config.json", '{"setting": "value"}')
    """
    logger.info(f"[tool] Creating file: {path}")
    try:
        # Create parent directories if they don't exist
        os.makedirs(os.path.dirname(path), exist_ok=True)

        with open(path, "w", encoding="utf-8") as f:
            f.write(content)

        return f"File created successfully: {path}"
    except PermissionError:
        return f"Error: Permission denied creating file: {path}"
    except OSError as e:
        return f"Error: Cannot create file {path}: {str(e)}"
    except Exception as e:
        return f"Error creating file: {str(e)}"


def list_files(path: str, recursive: bool = False) -> str:
    """
    List files and directories within the specified directory.

    This tool is essential for understanding project structure, exploring codebases,
    and discovering files for further analysis. Use this tool when you need to:
    - Get an overview of project structure
    - Find specific files or directories
    - Explore unfamiliar codebases
    - Check if files exist in a directory
    - Understand directory organization

    Args:
        path: Directory path to list contents for (relative to current working directory)
        recursive: Whether to list files recursively. If True, lists all files and directories
                  recursively. If False, only lists top-level contents.

    Returns:
        Formatted string listing of files and directories, or error message if listing fails

    Examples:
        - list_files(".") - List current directory contents
        - list_files("src", recursive=True) - Recursively list all files in src directory
        - list_files("../other_project") - List contents of sibling directory
    """
    logger.info(f"[tool] Listing files in: {path}, recursive: {recursive}")
    try:
        target_path = Path(path).resolve()

        if not target_path.exists():
            return f"Error: Directory not found: {path}"

        if not target_path.is_dir():
            return f"Error: Path is not a directory: {path}"

        result_lines = []

        if recursive:
            # Recursive listing
            result_lines.append(f"Recursive listing of {target_path}:")
            result_lines.append("")

            for root, dirs, files in os.walk(target_path):
                root_path = Path(root)
                # Calculate relative path from target_path
                try:
                    rel_path = root_path.relative_to(target_path)
                    if str(rel_path) == ".":
                        prefix = ""
                    else:
                        prefix = str(rel_path) + "/"
                except ValueError:
                    prefix = str(root_path) + "/"

                # Add directories
                for dir_name in sorted(dirs):
                    result_lines.append(f"{prefix}{dir_name}/")

                # Add files
                for file_name in sorted(files):
                    result_lines.append(f"{prefix}{file_name}")

                if dirs or files:
                    result_lines.append("")
        else:
            # Top-level listing only
            result_lines.append(f"Contents of {target_path}:")
            result_lines.append("")

            items = []
            for item in target_path.iterdir():
                if item.is_dir():
                    items.append(f"{item.name}/")
                else:
                    items.append(item.name)

            for item in sorted(items):
                result_lines.append(item)

        return "\n".join(result_lines)

    except PermissionError:
        return f"Error: Permission denied accessing directory: {path}"
    except Exception as e:
        return f"Error listing directory: {str(e)}"
