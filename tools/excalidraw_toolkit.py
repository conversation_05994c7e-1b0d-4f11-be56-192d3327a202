from dotenv import load_dotenv

load_dotenv()

import hashlib
import os
import re
import subprocess

from camel.agents import ChatAgent
from camel.models import BaseModelBackend
from camel.toolkits.base import BaseToolkit
from camel.toolkits.function_tool import FunctionTool
from loguru import logger

from utils.create_llm_model import create_model

LAYOUT_PLANNING_PROMPT = """
根据以下描述，为 Excalidraw 图表规划一个清晰、逻辑合理的布局。
请识别出关键元素、它们的类型（如矩形、圆形、箭头）、分组关系和连接方式。
输出一个布局计划，使用 Markdown 列表或类似结构描述元素的大致位置（例如：A 在左上角，B 在 A 的右侧，C 在 A 的下方）和连接关系（例如：从 A 到 B 的箭头）。

描述:
<content_description>
{content_description}
</content_description>

布局计划:
"""

CODE_GENERATION_PROMPT = """
你的任务是根据用户提供的 `<content_description>` 和预先生成的 `<layout_plan>`，利用 `pyexcalidraw` 库生成 Python 代码，该代码执行后会输出一个 Excalidraw JSON 文件。请务必仔细理解用户需求和布局规划，确保最终生成的图表布局清晰、逻辑准确、包含所有必要的细节。

参考以下文档和示例，通过 pyexcalidraw 库生成一个 excalidraw 的 json 文件。

<api_reference>
# PyExcalidraw

PyExcalidraw is a pure Python library for creating and editing Excalidraw files
and converting Excalidraw files to animated SVG. It is completely rewritten in
Python and does not depend on Node.js or any JavaScript libraries.

## Features

- Create and edit Excalidraw files
- Support for all basic Excalidraw element types: rectangles, ellipses, diamonds, lines, arrows, text, and images
- Pure Python implementation, no Node.js or JavaScript dependencies

## API Documentation

### Creating Static Excalidraw Images

#### Creating an Excalidraw Document

```python
from pyexcalidraw import ExcalidrawDocument

# Create a new document
doc = ExcalidrawDocument(
    version=2,  # Optional, defaults to 2
    source="https://excalidraw.com"  # Optional
)

# Set application state (optional)
doc.set_app_state(
    gridSize=20,
    viewBackgroundColor="#ffffff",
    zoom=1.0,
)

# Save document
doc.save("my_drawing.excalidraw")

# Convert to dictionary or JSON
data_dict = doc.to_dict()
json_str = doc.to_json(indent=2)
```

#### Adding Elements

```python
from pyexcalidraw import (
    Rectangle, Ellipse, Diamond, Text, Line, Arrow, Image
)

# Add a rectangle with bound text
rectangle = Rectangle(
    x=100,
    y=100,
    width=120,
    height=60,
    backgroundColor="#d4e8ff",
    strokeColor="#1e1e1e",
    strokeWidth=1,
    roundness={{"type": 3, "value": 20}},
    text="Rectangle",  # This will create a bound text element
    fontSize=20,
)
doc.add_element(rectangle)

# Add an ellipse with bound text
ellipse = Ellipse(
    x=300,
    y=100,
    width=120,
    height=60,
    backgroundColor="#ffffd4",
    strokeColor="#1e1e1e",
    strokeWidth=1,
    text="Ellipse",
    fontSize=20,
)
doc.add_element(ellipse)

# Add a diamond with bound text
diamond = Diamond(
    x=500,
    y=100,
    width=120,
    height=60,
    backgroundColor="#d4ffea",
    strokeColor="#1e1e1e",
    strokeWidth=1,
    text="Diamond",
    fontSize=20,
)
doc.add_element(diamond)

# Add standalone text
text = Text(
    x=100,
    y=200,
    text="Standalone Text",
    fontSize=16,
    textAlign="center",  # left, center, right
    verticalAlign="middle",  # top, middle, bottom
    width=150,  # Optional, calculated automatically if not provided
    height=20,  # Optional, calculated automatically if not provided
    autoResize=True,  # Optional, defaults to True
)
doc.add_element(text)

# Add a line
line = Line(
    x=100,
    y=250,
    points=[[0, 0], [100, 0]],  # List of relative points
    strokeColor="#1e1e1e",
    strokeWidth=2,
)
doc.add_element(line)

# Add an arrow with points
arrow = Arrow(
    x=100,
    y=300,
    points=[[0, 0], [100, 50]],
    strokeColor="#1e1e1e",
    strokeWidth=2,
    startArrowhead=None,  # Optional: null, arrow, triangle, bar, dot
    endArrowhead="arrow",  # Optional: null, arrow, triangle, bar, dot
)
doc.add_element(arrow)

# Add an arrow between objects
arrow_between_objects = Arrow(
    from_obj=rectangle,  # Source object
    to_obj=ellipse,     # Target object
    strokeColor="#1e1e1e",
    strokeWidth=2,
    endArrowhead="arrow",
)
doc.add_element(arrow_between_objects)

# Add an image
from pyexcalidraw import FileManager

# Create a file entry from a PIL image
file_id, file_data = FileManager.create_file_entry_from_pil_image(pil_image)

# Add the file to the document
doc.add_file(file_id, file_data)

# Add the image element
image = Image(
    x=300,
    y=300,
    file_id=file_id,
    width=100,
    height=100,
)
doc.add_element(image)
```

#### Loading Existing Documents

```python
# Load from file
doc = ExcalidrawDocument.load("existing_drawing.excalidraw")

# Get element by ID
element = doc.get_element("element_id")

# Remove element by ID
doc.remove_element("element_id")
```
</api_reference>

<example>
from pyexcalidraw import ExcalidrawDocument, Rectangle, Arrow, Diamond, Text
import argparse

def generate_flowchart(args):
    # Create document
    doc = ExcalidrawDocument()

    # Add title
    title = Text(
        x=200,
        y=50,
        text="流程图",
        fontSize=24,
        textAlign="center",
        verticalAlign="middle",
        width=100,
        height=30,
    )
    doc.add_element(title)

    # Add rectangle with bound text - Start
    start = Rectangle(
        x=100,
        y=150,
        width=120,
        height=60,
        backgroundColor="#d4e8ff",
        strokeColor="#1e1e1e",
        text="Start",
        fontSize=20,
    )
    doc.add_element(start)

    # Add rectangle with bound text - Process
    process = Rectangle(
        x=300,
        y=150,
        width=120,
        height=60,
        backgroundColor="#fff2cc",
        strokeColor="#1e1e1e",
        text="Process",
        fontSize=20,
    )
    doc.add_element(process)

    # Add rectangle with bound text - End
    end = Rectangle(
        x=500,
        y=150,
        width=120,
        height=60,
        backgroundColor="#d4ffea",
        strokeColor="#1e1e1e",
        text="End",
        fontSize=20,
    )
    doc.add_element(end)

    # Create arrow between objects - Start to Process
    arrow1 = Arrow(
        from_obj=start,  # From "Start" rectangle
        to_obj=process,      # To "Process" rectangle
        strokeColor="#1e1e1e",
        strokeWidth=2,
        endArrowhead="arrow",
    )
    doc.add_element(arrow1)

    # Create arrow between objects - Process to End
    arrow2 = Arrow(
        from_obj=process,  # From "Process" rectangle
        to_obj=end,      # To "End" rectangle
        strokeColor="#1e1e1e",
        strokeWidth=2,
        endArrowhead="arrow",
    )
    doc.add_element(arrow2)

    # Save document
    doc.save(args.output)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Generate an Excalidraw flowchart.")
    parser.add_argument("--output", type=str, default="output.excalidraw",
                        help="The filename for the output Excalidraw JSON file.")

    args = parser.parse_args()
    generate_flowchart(args)
</example>

要生成的内容描述：
<content_description>
{content_description}
</content_description>

布局规划：
<layout_plan>
{layout_plan}
</layout_plan>

**生成代码的要求与建议：**
1.  **遵循布局规划:** 请严格按照上面提供的 `<layout_plan>` 来安排元素的位置和连接。布局规划优先于直接从 `<content_description>` 推断位置。
2.  **代码结构:** 生成的代码需要包含一个接受 `args` 参数的函数 (例如 `generate_diagram(args)`)，并在 `if __name__ == "__main__":` 中调用它。`args` 参数需要包含 `output` 属性，用于指定输出文件名。代码最后需要调用 `doc.save(args.output)` 保存文件。
3.  **细节和完整性:** 在遵循布局规划的前提下，确保代码实现 `<content_description>` 中要求的所有细节（如特定形状、颜色、文本内容）。
4.  **API 特性:** 使用 `from_obj` 和 `to_obj` 参数创建对象间的箭头，以确保连接的准确性。为形状添加 `text` 参数以创建绑定的文本标签。对于独立文本，使用 `Text` 类，并合理设置 `textAlign`, `verticalAlign`, `fontSize` 等参数。根据需要调整元素的 `backgroundColor`, `strokeColor`, `strokeWidth` 等样式属性。
5.  **避免重叠:** 再次强调，生成的 excalidraw 布局要美观且合理，不能出现元素重叠的情况。
"""


class ExcalidrawToolkit(BaseToolkit):
    r"""A toolkit for generating excalidraw videos."""

    def _plan_layout(self, content_description: str, model: BaseModelBackend) -> str:
        """Generates a layout plan based on the content description."""
        # TODO: Implement the logic to call the LLM with LAYOUT_PLANNING_PROMPT
        agent = ChatAgent(model=model)
        prompt = LAYOUT_PLANNING_PROMPT.format(content_description=content_description)
        logger.info("Generating layout plan...")
        response = agent.step(prompt)
        layout_plan = response.msgs[0].content
        logger.info(f"Generated layout plan:\n{layout_plan}")
        # Potentially add some parsing/validation of the plan here
        return layout_plan

    def generate_excalidraw_video(self, content_description: str, step: int = 1) -> str:
        """根据给定的提示生成视频

        Args:
            content_description (str): 要生成的内容的描述。
            step (int): 当前分镜的序号，用于命名输出视频文件
        """
        model: BaseModelBackend = create_model(model_type="google/gemini-2.5-flash-preview-05-20")

        content_hash = hashlib.md5(content_description.encode()).hexdigest()[:8]
        base_name = f"output_{content_hash}"
        script_file = f"{base_name}.py"
        target_video_file = f"output/{base_name}.mp4"
        if os.path.exists(target_video_file):
            logger.info(f"Video file {target_video_file} already exists, skipping generation.")
            return target_video_file

        if not os.path.exists(script_file):
            # Step 1: Plan Layout
            layout_plan = self._plan_layout(content_description, model)

            # Step 2: Generate Code based on description and plan
            agent = ChatAgent(model=model)  # Reuse or re-create agent? Re-creating for simplicity now.
            prompt = CODE_GENERATION_PROMPT.format(
                content_description=content_description,
                layout_plan=layout_plan,
            )
            logger.info("Generating Excalidraw code based on description and plan...")
            response = agent.step(prompt)
            response_content = response.msgs[0].content
            # extract the first python code block from response_content, which starts with ```python or ```py and ends with ```
            match = re.search(r"```(?:python|py)(.*?)```", response_content, re.DOTALL)
            if not match:
                logger.error(f"No Python code block found in the response: {response_content}")
                return None
            code = match.group(1).strip()  # Extract the captured group and strip leading/trailing whitespace
            with open(script_file, "w") as f:
                f.write(code)
        subprocess.run(
            ["uv", "run", "pyexcalidraw", "run-all", "--input", script_file, "--base-name", base_name], check=True
        )
        return target_video_file

    def get_tools(self) -> list[FunctionTool]:
        """Returns a list of FunctionTool objects representing the
        functions in the toolkit.

        Returns:
            List[FunctionTool]: A list of FunctionTool objects
                representing the functions in the toolkit.
        """
        return [
            FunctionTool(self.generate_excalidraw_video),
        ]


if __name__ == "__main__":
    toolkit = ExcalidrawToolkit()
    content_description = "给小朋友解释为什么天空是蓝色的"
    toolkit.generate_excalidraw_video(content_description)
