import os
import re
import time
import urllib.parse
from pathlib import Path
from typing import Any, Dict, List, Optional
import requests
from bs4 import BeautifulSoup
import imghdr  # 添加图片格式检测
from urllib.parse import urljoin
from markdownify import markdownify  # 添加HTML到Markdown转换

# 简化导入，避免复杂的依赖检查
import sys
sys.path.append('..')

from agents.video_agent.tools.info_collector_toolkit import InfoCollectorToolkit


def dependencies_required(*deps):
    """简化的依赖装饰器"""
    def decorator(cls):
        return cls
    return decorator


class FunctionTool:
    """简化的FunctionTool类"""
    def __init__(self, func):
        self.func = func


class BaseToolkit:
    """Base toolkit class."""
    pass

class WebpageToolkit(BaseToolkit):
    r"""A toolkit for extracting and processing webpage content with multimedia assets.
    
    This toolkit downloads webpage content including images, videos and other media,
    and generates a markdown file with proper media references.
    Enhanced with ArchiveBoxSaver's intelligent image detection and URL processing.
    """

    @dependencies_required("bs4", "requests", "markdownify")
    def __init__(
        self, 
        timeout: Optional[float] = None, 
        image_filter_config: Optional[dict] = None,
        use_cache: bool = True,
        user_agent: Optional[str] = None,
        disable_cache: bool = False,
        proxy: Optional[str] = None,  # 新增代理支持
        enable_archivebox_features: bool = True  # 新增ArchiveBox特性开关
    ) -> None:
        r"""Initialize the WebpageToolkit.

        Args:
            timeout: Request timeout in seconds
            image_filter_config: Configuration for filtering images
            use_cache: Whether to use cache for web content extraction
            user_agent: Custom user agent string for web requests
            disable_cache: Whether to completely disable caching for testing
            proxy: Proxy server address (http://proxy:port)
            enable_archivebox_features: Enable ArchiveBox-inspired features
        """
        self.timeout = timeout or 30
        self.image_filter_config = image_filter_config or {}
        self.use_cache = use_cache and not disable_cache
        self.proxy = proxy
        self.enable_archivebox_features = enable_archivebox_features
        
        # 设置代理配置
        self.proxies = None
        if self.proxy:
            self.proxies = {
                "http": self.proxy,
                "https": self.proxy
            }
            print(f"🌐 使用代理: {self.proxy}")
        
        # 设置默认的用户代理，模拟常见浏览器
        self.user_agent = user_agent or (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
            "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
        
        # Initialize InfoCollectorToolkit
        self.info_toolkit = InfoCollectorToolkit(
            use_cache=self.use_cache,
            timeout=self.timeout
        )

    def _extract_project_name_from_url(self, url: str) -> str:
        """Extract project name from URL for directory naming."""
        try:
            parsed_url = urllib.parse.urlparse(url)
            domain = parsed_url.netloc.lower()
            path = parsed_url.path.strip('/')
            
            # Handle GitHub URLs
            if 'github.com' in domain:
                path_parts = [p for p in path.split('/') if p]  # Filter out empty parts
                if len(path_parts) >= 2:
                    return f"{path_parts[0]}_{path_parts[1]}"
                elif len(path_parts) == 1:
                    return path_parts[0]
                else:
                    return 'github_project'
            
            # Handle other domains
            domain_parts = domain.split('.')
            domain_name = domain_parts[-2] if len(domain_parts) > 1 else domain_parts[0] if domain_parts else 'unknown'
            
            if path:
                # Use last meaningful part of path
                path_parts = [p for p in path.split('/') if p and p != 'index.html']
                if path_parts:
                    return f"{domain_name}_{path_parts[-1]}"
            
            return domain_name
            
        except Exception as e:
            print(f"Error extracting project name from URL: {e}")
            return "webpage_project"

    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for safe file system usage."""
        # Remove or replace unsafe characters
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        filename = re.sub(r'[^\w\-_.]', '_', filename)
        # Remove multiple underscores
        filename = re.sub(r'_+', '_', filename)
        # Remove leading/trailing underscores and dots
        filename = filename.strip('_.')
        
        if not filename:
            filename = "unnamed_file"
            
        return filename

    def _download_media_file(self, media_url: str, output_dir: str, filename: str = None) -> str:
        """Download a media file from URL to local directory."""
        try:
            print(f"    开始下载媒体文件: {media_url}")
            
            # Create output directory if it doesn't exist
            Path(output_dir).mkdir(parents=True, exist_ok=True)
            
            # 为HTTP协议添加额外的请求头
            download_headers = {'User-Agent': self.user_agent}
            if media_url.startswith('http://'):
                print(f"    检测到HTTP协议，添加额外请求头...")
                download_headers.update({
                    'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
                    'Accept-Encoding': 'gzip, deflate',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache',
                    'Sec-Fetch-Dest': 'image',
                    'Sec-Fetch-Mode': 'no-cors',
                    'Sec-Fetch-Site': 'cross-site',
                })
            
            # Download the file
            response = requests.get(
                media_url, 
                timeout=self.timeout, 
                stream=True,
                headers=download_headers,
                verify=False,  # 对于HTTP请求，禁用SSL验证
                allow_redirects=True,  # 允许重定向
                proxies=self.proxies
            )
            response.raise_for_status()
            
            # Generate filename if not provided
            if not filename:
                parsed_url = urllib.parse.urlparse(media_url)
                filename = os.path.basename(parsed_url.path)
                if not filename or '.' not in filename:
                    print(f"    无法从URL获取文件名，使用智能格式检测...")
                    # 使用智能格式检测
                    if self.enable_archivebox_features:
                        ext = self._detect_image_format(response, media_url)
                        print(f"    智能检测文件格式: {ext}")
                    else:
                        # 降级为简单的MIME类型检测
                        content_type = response.headers.get('Content-Type', '')
                        print(f"    检测到MIME类型: {content_type}")
                        if 'image/jpeg' in content_type:
                            ext = '.jpg'
                        elif 'image/png' in content_type:
                            ext = '.png'
                        elif 'image/gif' in content_type:
                            ext = '.gif'
                        elif 'image/svg' in content_type:
                            ext = '.svg'
                        elif 'image/webp' in content_type:
                            ext = '.webp'
                        elif 'video/mp4' in content_type:
                            ext = '.mp4'
                        else:
                            ext = '.bin'
                    
                    filename = f"media_{int(time.time())}{ext}"
                    print(f"    生成文件名: {filename}")
            
            # Sanitize filename
            filename = self._sanitize_filename(filename)
            
            # Avoid filename conflicts
            counter = 1
            original_filename = filename
            filepath = os.path.join(output_dir, filename)
            while os.path.exists(filepath):
                name, ext = os.path.splitext(original_filename)
                filename = f"{name}_{counter}{ext}"
                filepath = os.path.join(output_dir, filename)
                counter += 1
            
            print(f"    保存路径: {filepath}")
            
            # 获取文件大小信息
            content_length = response.headers.get('Content-Length')
            if content_length:
                file_size = int(content_length)
                print(f"    文件大小: {file_size / 1024:.1f} KB")
            
            with open(filepath, 'wb') as f:
                downloaded_size = 0
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
                    downloaded_size += len(chunk)
            
            print(f"    ✅ 成功下载: {filepath} ({downloaded_size / 1024:.1f} KB)")
            return filepath
            
        except requests.exceptions.Timeout:
            print(f"    ❌ 下载超时: {media_url}")
            return None
        except requests.exceptions.ConnectionError:
            print(f"    ❌ 连接错误: {media_url}")
            return None
        except requests.exceptions.HTTPError as e:
            print(f"    ❌ HTTP错误 {e.response.status_code}: {media_url}")
            return None
        except Exception as e:
            print(f"    ❌ 下载失败: {media_url}, 错误: {e}")
            return None

    def _should_filter_media(self, media_url: str, media_alt: str = "") -> bool:
        """Determine if media should be filtered out."""
        filter_config = self.image_filter_config
        
        # Skip if URL contains certain keywords
        skip_keywords = filter_config.get('skip_keywords', ['badge', 'icon', 'logo', 'license'])
        url_lower = media_url.lower()
        alt_lower = media_alt.lower()
        
        for keyword in skip_keywords:
            if keyword in url_lower or keyword in alt_lower:
                print(f"    🚫 过滤图片(关键词 '{keyword}'): {media_url[:50]}...")
                return True
        
        # Skip very small images (likely icons)
        min_size = filter_config.get('min_size_kb', 5)  # 5KB minimum
        try:
            print(f"    📏 检查图片大小: {media_url[:50]}...")
            head_response = requests.head(
                media_url, 
                timeout=10,
                headers={'User-Agent': self.user_agent},
                proxies=self.proxies,  # 使用代理设置
                verify=False,  # 禁用SSL验证
                allow_redirects=True
            )
            content_length = head_response.headers.get('Content-Length')
            if content_length:
                size_kb = int(content_length) / 1024
                print(f"    📊 图片大小: {size_kb:.1f} KB (最小要求: {min_size} KB)")
                if size_kb < min_size:
                    print(f"    🚫 过滤图片(太小): {media_url[:50]}...")
                    return True
                else:
                    print(f"    ✅ 图片大小符合要求: {size_kb:.1f} KB")
            else:
                print(f"    ⚠️  无法获取图片大小，跳过大小检查")
        except Exception as e:
            print(f"    ⚠️  大小检查失败({e})，继续下载: {media_url[:50]}...")
            pass
        
        print(f"    ✅ 图片通过过滤检查: {media_url[:50]}...")
        return False

    def _extract_with_requests(self, url: str) -> Dict[str, Any]:
        """使用requests库作为备用方案直接获取网页内容"""
        import random
        import time
        
        # 多个真实的用户代理，随机选择
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0",
        ]
        
        # 随机选择用户代理
        chosen_ua = random.choice(user_agents)
        
        # 构建更真实的请求头
        headers = {
            'User-Agent': chosen_ua,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
        }
        
        # 如果是知乎，添加特殊的请求头
        if 'zhihu.com' in url:
            headers.update({
                'Referer': 'https://www.zhihu.com/',
                'Origin': 'https://www.zhihu.com',
                'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"Windows"',
            })
        
        # 创建session以支持cookie
        session = requests.Session()
        session.headers.update(headers)
        
        # 设置代理（如果配置了）
        if self.proxies:
            session.proxies.update(self.proxies)
            print(f"🌐 备用方案使用代理: {self.proxy}")
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                print(f"尝试使用requests库获取内容... (第 {attempt + 1}/{max_retries} 次)")
                
                # 随机延迟，模拟人类行为
                if attempt > 0:
                    delay = random.uniform(2, 5)
                    print(f"等待 {delay:.1f} 秒后重试...")
                    time.sleep(delay)
                
                # 如果是知乎，先访问首页获取cookie
                if 'zhihu.com' in url and attempt == 0:
                    try:
                        print("预热请求：先访问知乎首页...")
                        session.get('https://www.zhihu.com/', timeout=10)
                        time.sleep(random.uniform(1, 3))
                    except:
                        print("预热请求失败，继续尝试直接访问...")
                
                response = session.get(url, timeout=self.timeout)
                
                # 检查状态码
                if response.status_code == 403:
                    print(f"收到403禁止访问，尝试其他策略...")
                    if attempt < max_retries - 1:
                        continue
                elif response.status_code == 429:
                    print(f"收到429限流，等待更长时间...")
                    if attempt < max_retries - 1:
                        time.sleep(random.uniform(10, 20))
                        continue
                elif response.status_code >= 400:
                    print(f"HTTP错误 {response.status_code}: {response.reason}")
                    if attempt < max_retries - 1:
                        continue
                
                response.raise_for_status()
                
                # 检测编码
                response.encoding = response.apparent_encoding or 'utf-8'
                html_content = response.text
                
                # 检查是否是错误页面或限制页面
                error_indicators = [
                    '限制本次访问', '访问异常', '暂时限制', 'access denied', 'blocked',
                    '验证码', '人机验证', 'captcha', '登录', 'sign in', '403', '404',
                    '环境异常', '完成验证后即可继续访问', '当前环境异常'  # 微信公众号特有的错误提示
                ]
                
                if any(indicator in html_content.lower() for indicator in error_indicators):
                    print(f"检测到访问限制页面，包含指示词: {[ind for ind in error_indicators if ind in html_content.lower()]}")
                    print(f"页面内容预览: {html_content[:200]}...")
                    if attempt < max_retries - 1:
                        print("尝试不同的策略...")
                        # 修改用户代理重试
                        chosen_ua = random.choice(user_agents)
                        session.headers['User-Agent'] = chosen_ua
                        continue
                
                # 解析内容
                soup = BeautifulSoup(html_content, 'html.parser')
                
                # 获取标题
                title_tag = soup.find("title")
                title = title_tag.get_text().strip() if title_tag else f"网页内容 - {url}"
                
                # 移除脚本和样式
                for tag in soup(["script", "style", "meta", "link", "noscript"]):
                    tag.decompose()
                
                # 获取主要内容
                main_content = soup.get_text(separator='\n', strip=True)
                
                # 检查内容质量
                if len(main_content.strip()) < 100:
                    print(f"内容过短 ({len(main_content)} 字符)，可能是错误页面")
                    if attempt < max_retries - 1:
                        continue
                
                # 限制内容长度
                if len(main_content) > 10000:
                    main_content = main_content[:10000] + "\n\n[内容过长，已截断...]"
                
                print(f"成功获取内容，长度: {len(main_content)} 字符")
                return {
                    'title': title,
                    'content': main_content,
                    'html': html_content,
                    'success': True
                }
                
            except requests.exceptions.Timeout:
                print(f"请求超时 (第 {attempt + 1} 次)")
                if attempt < max_retries - 1:
                    continue
            except requests.exceptions.ConnectionError:
                print(f"连接错误 (第 {attempt + 1} 次)")
                if attempt < max_retries - 1:
                    continue
            except Exception as e:
                print(f"请求失败 (第 {attempt + 1} 次): {e}")
                if attempt < max_retries - 1:
                    continue
        
        # 所有尝试都失败
        print(f"所有备用方案都失败了，尝试次数: {max_retries}")
        return {
            'title': f"内容提取失败 - {url}",
            'content': f"⚠️ 无法绕过网站的访问限制。\n\n该网站可能使用了高级的反爬虫机制，包括：\n- IP封禁\n- 用户代理检测\n- JavaScript验证\n- 验证码保护\n- 登录要求\n\n建议：\n1. 尝试其他公开、对爬虫友好的网站\n2. 或者手动访问该网页并复制内容",
            'html': '',
            'success': False,
            'error': 'Advanced anti-bot protection detected'
        }

    def _process_webpage_content(self, url: str, output_dir: str, project_name: str) -> Dict[str, Any]:
        """Process webpage content and download media assets."""
        try:
            print(f"🌐 开始处理网页: {url}")
            
            # Step 1: 使用InfoCollectorToolkit提取清洁内容
            print(f"📝 使用InfoCollectorToolkit提取清洁内容...")
            web_result = self.info_toolkit.extract_web_content(
                url=url,
                use_javascript=True,
                extract_images=True,
                wait_time=5
            )
            
            print(f"InfoCollectorToolkit result type: {type(web_result)}")
            print(f"InfoCollectorToolkit result keys: {list(web_result.keys()) if isinstance(web_result, dict) else 'Not a dict'}")
            
            # 提取清洁的内容和标题
            clean_content = ''
            title = 'Untitled'
            
            if isinstance(web_result, dict):
                # 检查是否有访问限制
                raw_content = str(web_result.get('content', ''))
                if any(pattern in raw_content for pattern in ['限制本次访问', '访问异常', '暂时限制', 'access denied', 'blocked']):
                    print("⚠️ InfoCollectorToolkit检测到访问限制")
                    clean_content = f"⚠️ 访问受限\n\n由于网站的访问限制，无法完整提取内容。\n原始链接：{url}"
                    title = f"访问受限 - {url}"
                else:
                    # 正常提取内容
                    if not web_result.get('success', True):
                        error_msg = web_result.get('error', 'Unknown error')
                        print(f"InfoCollectorToolkit返回错误: {error_msg}")
                        clean_content = f"❌ 内容提取失败\n\n错误信息：{error_msg}\n原始链接：{url}"
                        title = f"提取失败 - {url}"
                    else:
                        clean_content = raw_content
                        
                        # 提取标题
                        if 'title' in web_result:
                            title = web_result['title']
                        elif clean_content:
                            # 从内容中提取标题
                            lines = clean_content.split('\n')
                            for line in lines:
                                if line.strip() and len(line.strip()) > 5:
                                    title = line.strip()[:100]
                                    break
            
            elif isinstance(web_result, str):
                clean_content = web_result
                title = f"Content from {url}"
            else:
                clean_content = f"❌ 未知的内容格式\n\n无法识别InfoCollectorToolkit返回的内容格式。\n原始链接：{url}"
                title = f"格式错误 - {url}"
            
            if not title or title == 'Untitled':
                title = f"Content from {url}"
            
            print(f"✅ 清洁内容提取完成，长度: {len(clean_content)}")
            print(f"📋 标题: {title}")
            
            # Step 2: 单独获取HTML来提取媒体链接
            print(f"🔍 单独获取HTML来提取媒体链接...")
            html_result = self._extract_with_requests(url)
            html_content = ''
            
            if html_result['success']:
                html_content = html_result['html']
                print(f"✅ HTML获取成功，长度: {len(html_content)} 字符")
            else:
                print(f"❌ HTML获取失败: {html_result.get('error', 'Unknown error')}")
            
            # Step 3: 创建媒体目录
            media_dir = os.path.join(output_dir, 'media')
            Path(media_dir).mkdir(parents=True, exist_ok=True)
            
            # Step 4: 从HTML中提取和下载媒体文件
            downloaded_media = []
            
            if html_content:
                print(f"🖼️  开始从HTML中提取媒体文件...")
                soup = BeautifulSoup(html_content, 'html.parser')
                
                # 提取图片
                img_tags = soup.find_all('img')
                print(f"找到 {len(img_tags)} 个图片标签")
                
                for i, img in enumerate(img_tags):
                    src = img.get('src', '')
                    data_src = img.get('data-src', '')  # 懒加载图片
                    alt = img.get('alt', '')
                    
                    # 优先使用data-src，再使用src
                    image_url = data_src if data_src else src
                    
                    print(f"处理图片 {i+1}/{len(img_tags)}: url='{image_url[:100]}...', alt='{alt}'")
                    
                    if not image_url:
                        print(f"  跳过: 图片{i+1}没有src或data-src属性")
                        continue
                    
                    # 转换为绝对URL
                    if not image_url.startswith(('http://', 'https://')):
                        image_url = urllib.parse.urljoin(url, image_url)
                        print(f"  转换相对URL: {src} -> {image_url}")
                    
                    # 过滤不需要的图片
                    if self._should_filter_media(image_url, alt):
                        print(f"  跳过: 图片{i+1}被过滤")
                        continue
                    
                    # 下载图片
                    print(f"  开始下载图片{i+1}: {image_url}")
                    local_path = self._download_media_file(image_url, media_dir)
                    if local_path:
                        filename = os.path.basename(local_path)
                        rel_path = f"media/{filename}"
                        downloaded_media.append({
                            'type': 'image',
                            'original_url': image_url,
                            'local_path': local_path,
                            'relative_path': rel_path,
                            'alt_text': alt,
                            'filename': filename
                        })
                        print(f"  ✅ 成功下载图片{i+1}: {filename}")
                    else:
                        print(f"  ❌ 图片{i+1}下载失败")
                
                # 提取视频
                video_tags = soup.find_all('video')
                print(f"找到 {len(video_tags)} 个视频标签")
                
                for i, video in enumerate(video_tags):
                    src = video.get('src', '')
                    if not src:
                        # 查找source标签
                        source_tag = video.find('source')
                        if source_tag:
                            src = source_tag.get('src', '')
                    
                    print(f"处理视频 {i+1}/{len(video_tags)}: src='{src}'")
                    
                    if not src:
                        print(f"  跳过: 视频{i+1}没有src属性")
                        continue
                    
                    # 转换为绝对URL
                    if not src.startswith(('http://', 'https://')):
                        src = urllib.parse.urljoin(url, src)
                    
                    # 下载视频
                    print(f"  开始下载视频{i+1}: {src}")
                    local_path = self._download_media_file(src, media_dir)
                    if local_path:
                        filename = os.path.basename(local_path)
                        rel_path = f"media/{filename}"
                        downloaded_media.append({
                            'type': 'video',
                            'original_url': src,
                            'local_path': local_path,
                            'relative_path': rel_path,
                            'alt_text': '',
                            'filename': filename
                        })
                        print(f"  ✅ 成功下载视频{i+1}: {filename}")
                    else:
                        print(f"  ❌ 视频{i+1}下载失败")
            else:
                print("❌ 没有HTML内容可供媒体提取")
            
            print(f"📊 媒体文件提取完成，总共成功下载 {len(downloaded_media)} 个文件")
            
            return {
                'title': title,
                'content': clean_content,  # 使用InfoCollectorToolkit提取的清洁内容
                'html': html_content,      # 原始HTML（仅用于媒体提取）
                'media': downloaded_media,
                'url': url
            }
            
        except Exception as e:
            print(f"❌ 处理网页内容时出错: {e}")
            # 返回错误信息，但仍保持结构完整
            return {
                'title': f"处理失败 - {url}",
                'content': f"❌ 网页处理失败\n\n错误信息：{str(e)}\n原始链接：{url}",
                'html': '',
                'media': [],
                'url': url
            }

    def _create_markdown_with_media(self, webpage_data: Dict[str, Any], output_dir: str) -> str:
        """Create markdown file with embedded media references."""
        try:
            title = webpage_data['title']
            content = webpage_data['content']  # 现在是来自InfoCollectorToolkit的清洁内容
            html_content = webpage_data.get('html', '')
            media_assets = webpage_data['media']
            source_url = webpage_data['url']
            
            # 清理内容，移除无意义的信息
            cleaned_content = self._clean_content(content)
            
            # Create markdown content
            markdown_lines = []
            
            # Add title
            markdown_lines.append(f"# {title}")
            markdown_lines.append("")
            
            # Add source information
            markdown_lines.append(f"**Source:** [{source_url}]({source_url})")
            markdown_lines.append(f"**Extracted:** {time.strftime('%Y-%m-%d %H:%M:%S')}")
            markdown_lines.append("")
            markdown_lines.append("---")
            markdown_lines.append("")
            
            # Add main content header
            markdown_lines.append("## Content")
            markdown_lines.append("")
            
            # 为所有媒体资源创建URL映射
            url_mapping = {}
            for media_info in media_assets:
                if media_info.get('local_path'):
                    # 获取相对于markdown文件的媒体路径
                    relative_media_path = f"media/{os.path.basename(media_info['local_path'])}"
                    original_url = media_info['original_url']
                    
                    # 主要映射：完整URL
                    url_mapping[original_url] = relative_media_path
                    
                    # 添加不带参数的URL映射
                    base_url = original_url.split('?')[0]
                    if base_url != original_url:
                        url_mapping[base_url] = relative_media_path
                    
                    # 对于微信图片，添加更多变体
                    if 'mmbiz.qpic.cn' in original_url:
                        # 尝试http和https版本
                        if original_url.startswith('https://'):
                            http_version = original_url.replace('https://', 'http://')
                            url_mapping[http_version] = relative_media_path
                        elif original_url.startswith('http://'):
                            https_version = original_url.replace('http://', 'https://')
                            url_mapping[https_version] = relative_media_path
                    
                    # 也要处理可能的域名变化或协议变化
                    for variant in self._get_url_variants(original_url):
                        url_mapping[variant] = relative_media_path
                        
                    print(f"📝 映射URL: {original_url[:50]}... -> {relative_media_path}")
            
            # 替换内容中的所有图片URL为本地路径
            content_with_local_media = self._replace_all_image_urls(cleaned_content, url_mapping)
            
            # Add content
            markdown_lines.append(content_with_local_media)
            
            # Add source links
            if webpage_data.get('related_sources'):
                markdown_lines.append("")
                markdown_lines.append("---")
                markdown_lines.append("")
                markdown_lines.append("## 相关来源")
                markdown_lines.append("")
                for i, source in enumerate(webpage_data['related_sources'], 1):
                    markdown_lines.append(f"{i}. [{source}]({source})")
                
            # Add media assets section
            if media_assets:
                markdown_lines.append("")
                markdown_lines.append("---")
                markdown_lines.append("")
                markdown_lines.append("## Media Assets")
                markdown_lines.append("")
                
                # Group media by type
                images = [m for m in media_assets if m.get('type') == 'image']
                videos = [m for m in media_assets if m.get('type') == 'video']
                
                if images:
                    markdown_lines.append("### Images")
                    markdown_lines.append("")
                    for img in images:
                        if img.get('local_path'):
                            filename = os.path.basename(img['local_path'])
                            alt_text = img.get('alt', filename)
                            markdown_lines.append(f"![{alt_text}](media/{filename})")
                            markdown_lines.append(f"*{alt_text}*")
                            markdown_lines.append("")
                
                if videos:
                    markdown_lines.append("### Videos")
                    markdown_lines.append("")
                    for video in videos:
                        if video.get('local_path'):
                            filename = os.path.basename(video['local_path'])
                            alt_text = video.get('alt', filename)
                            markdown_lines.append(f"[{alt_text}](media/{filename})")
                            markdown_lines.append("")
                
                # Add media reference table
                markdown_lines.append("### Media Reference Table")
                markdown_lines.append("")
                markdown_lines.append("| Type | Filename | Original URL | Status |")
                markdown_lines.append("|------|----------|-------------|--------|")
                
                for media in media_assets:
                    media_type = media.get('type', 'Unknown').title()
                    filename = os.path.basename(media['local_path']) if media.get('local_path') else 'N/A'
                    original_url = media.get('original_url', 'N/A')
                    status = "✅ Downloaded" if media.get('local_path') else "❌ Failed"
                    markdown_lines.append(f"| {media_type} | {filename} | {original_url} | {status} |")
            
            # Add extraction summary
            markdown_lines.append("")
            markdown_lines.append("")
            markdown_lines.append("---")
            markdown_lines.append("")
            markdown_lines.append("## 提取信息")
            markdown_lines.append("")
            markdown_lines.append(f"- **提取时间:** {time.strftime('%Y-%m-%d %H:%M:%S')}")
            markdown_lines.append(f"- **媒体文件数量:** {len(media_assets)}")
            markdown_lines.append(f"- **项目目录:** {os.path.basename(output_dir)}")
            
            if media_assets:
                markdown_lines.append("")
                markdown_lines.append("### 下载的媒体文件")
                markdown_lines.append("")
                for media in media_assets:
                    if media.get('local_path'):
                        media_type = media.get('type', 'Unknown').title()
                        filename = os.path.basename(media['local_path'])
                        markdown_lines.append(f"- **{media_type}:** {filename}")
            
            return "\n".join(markdown_lines)
            
        except Exception as e:
            print(f"❌ 创建markdown时出错: {str(e)}")
            return f"# Error\n\n创建markdown文件时出错: {str(e)}"
            
    def _clean_content(self, content: str) -> str:
        """清理内容，移除无意义的信息"""
        import re
        
        # 移除JavaScript代码块
        content = re.sub(r'<script[^>]*>.*?</script>', '', content, flags=re.DOTALL | re.IGNORECASE)
        
        # 移除style标签
        content = re.sub(r'<style[^>]*>.*?</style>', '', content, flags=re.DOTALL | re.IGNORECASE)
        
        # 移除HTML注释
        content = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)
        
        # 移除微信特有的元素
        wechat_patterns = [
            r'预览时标签不可点.*?',
            r'关闭更多名称已清空.*?',
            r'微信扫一扫.*?赞赏作者.*?',
            r'文章.*?喜欢作者.*?',
            r'，轻点两下取消.*?',
            r'可在「公众号.*?找到划线过的内容.*?',
            r'继续滑动看下一个.*?',
            r'轻触阅读原文.*?',
            r'\[取消\]\(javascript:.*?\)',
            r'\[允许\]\(javascript:.*?\)',
            r'javascript:.*?;',
            r'分析\s*×\s*分析.*?',
            r'跳转二维码.*?',
            r'写留言.*?关闭.*?写留言.*?提交.*?',
            r'留言.*?暂无留言.*?',
            r'\d+条留言已无更多数据.*?',
            r'发消息.*?写留言:.*?',
            r'更多\[表情\].*?',
        ]
        
        for pattern in wechat_patterns:
            content = re.sub(pattern, '', content, flags=re.DOTALL | re.IGNORECASE)
        
        # 移除长串的无意义字符（如base64编码的图片数据）
        content = re.sub(r'data:image/[^;]+;base64,[A-Za-z0-9+/=]{100,}', '[Image Data Removed]', content)
        
        # 移除多余的空行
        content = re.sub(r'\n\s*\n\s*\n+', '\n\n', content)
        
        # 移除文章末尾的操作按钮和链接
        footer_patterns = [
            r'赞\s*，轻点两下取消赞.*?$',
            r'在看\s*，轻点两下取消在看.*?$',
            r'分享\s*留言.*?$',
            r'收藏\s*听过.*?$',
        ]
        
        for pattern in footer_patterns:
            content = re.sub(pattern, '', content, flags=re.MULTILINE | re.DOTALL)
        
        # 清理开头和结尾的空白
        content = content.strip()
        
        return content
    
    def _get_url_variants(self, url: str) -> List[str]:
        """获取URL的各种变体（不同协议、域名等）"""
        variants = []
        
        if url.startswith('https://'):
            variants.append(url.replace('https://', 'http://'))
        elif url.startswith('http://'):
            variants.append(url.replace('http://', 'https://'))
            
        # 添加可能的域名变体
        if 'mmbiz.qpic.cn' in url:
            variants.append(url.replace('mmbiz.qpic.cn', 'mmbiz.qlogo.cn'))
            variants.append(url.replace('https://mmbiz.qpic.cn', 'http://mmbiz.qpic.cn'))
            
        return variants
    
    def _replace_all_image_urls(self, content: str, url_mapping: Dict[str, str]) -> str:
        """替换内容中所有的图片URL为本地路径"""
        import re
        
        if not url_mapping:
            return content
            
        # 创建扩展的URL映射，包括URL的各种变体
        extended_mapping = {}
        for original_url, local_path in url_mapping.items():
            # 添加完整URL
            extended_mapping[original_url] = local_path
            
            # 添加不带参数的URL
            base_url = original_url.split('?')[0]
            if base_url != original_url:
                extended_mapping[base_url] = local_path
            
            # 添加用于匹配的核心标识符（如图片ID）
            if 'mmbiz.qpic.cn' in original_url:
                # 提取微信图片的核心标识符
                match = re.search(r'/([A-Za-z0-9_-]+)/', original_url)
                if match:
                    core_id = match.group(1)
                    extended_mapping[core_id] = local_path
        
        # 替换markdown格式的图片
        def replace_markdown_image(match):
            alt_text = match.group(1)
            img_url = match.group(2)
            
            # 首先尝试完全匹配
            if img_url in extended_mapping:
                return f"![{alt_text}]({extended_mapping[img_url]})"
            
            # 然后尝试部分匹配
            for original_url, local_path in extended_mapping.items():
                if original_url in img_url or img_url in original_url:
                    return f"![{alt_text}]({local_path})"
                    
                # 对于微信图片，尝试通过路径的一部分匹配
                if 'mmbiz.qpic.cn' in img_url and 'mmbiz.qpic.cn' in original_url:
                    # 提取图片路径的标识符进行模糊匹配
                    img_match = re.search(r'/([A-Za-z0-9_-]{10,})/', img_url)
                    orig_match = re.search(r'/([A-Za-z0-9_-]{10,})/', original_url)
                    if img_match and orig_match:
                        if img_match.group(1) == orig_match.group(1):
                            return f"![{alt_text}]({local_path})"
                        
            # 如果是data:image的SVG图片，替换为占位符
            if img_url.startswith('data:image/svg+xml'):
                return f"![{alt_text}]([Image Data Removed])"
                
            return match.group(0)  # 如果没找到，保持原样
        
        # 使用正则表达式匹配所有markdown图片
        content = re.sub(r'!\[([^\]]*)\]\(([^)]+)\)', replace_markdown_image, content)
        
        # 替换HTML格式的图片
        def replace_html_image(match):
            full_match = match.group(0)
            src_url = match.group(1)
            
            # 首先尝试完全匹配
            if src_url in extended_mapping:
                return full_match.replace(src_url, extended_mapping[src_url])
            
            # 然后尝试部分匹配
            for original_url, local_path in extended_mapping.items():
                if original_url in src_url or src_url in original_url:
                    return full_match.replace(src_url, local_path)
                    
                # 微信图片的模糊匹配
                if 'mmbiz.qpic.cn' in src_url and 'mmbiz.qpic.cn' in original_url:
                    img_match = re.search(r'/([A-Za-z0-9_-]{10,})/', src_url)
                    orig_match = re.search(r'/([A-Za-z0-9_-]{10,})/', original_url)
                    if img_match and orig_match:
                        if img_match.group(1) == orig_match.group(1):
                            return full_match.replace(src_url, local_path)
                            
            return full_match
        
        content = re.sub(r'<img[^>]+src=["\']([^"\']+)["\'][^>]*>', replace_html_image, content)
        
        # 最后进行直接字符串替换（作为后备）
        for original_url, local_path in url_mapping.items():
            content = content.replace(original_url, local_path)
            
        print(f"🔄 URL替换完成，映射了 {len(url_mapping)} 个媒体文件")
        return content

    def extract_webpage(
        self, 
        url: str, 
        output_base_dir: str = "output",
        project_name: str = None
    ) -> Dict[str, Any]:
        """Extract webpage content and media assets.
        
        Args:
            url: The webpage URL to extract
            output_base_dir: Base output directory
            project_name: Custom project name (auto-extracted if None)
            
        Returns:
            Dictionary containing extraction results and file paths
        """
        try:
            # Extract project name if not provided
            if not project_name:
                project_name = self._extract_project_name_from_url(url)
            
            # Create output directory
            output_dir = os.path.join(output_base_dir, project_name)
            Path(output_dir).mkdir(parents=True, exist_ok=True)
            
            print(f"🌐 开始提取网页: {url}")
            print(f"📁 输出目录: {output_dir}")
            
            # Process webpage content
            webpage_data = self._process_webpage_content(url, output_dir, project_name)
            
            # Create markdown content
            markdown_content = self._create_markdown_with_media(webpage_data, output_dir)
            
            # Save markdown file
            markdown_filename = "webpage_analysis.md"
            markdown_path = os.path.join(output_dir, markdown_filename)
            
            with open(markdown_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            print(f"📄 创建Markdown文件: {markdown_path}")
            
            # Create summary
            summary = {
                'url': url,
                'project_name': project_name,
                'output_dir': output_dir,
                'markdown_file': markdown_path,
                'title': webpage_data['title'],
                'media_count': len(webpage_data['media']),
                'media_files': [
                    {
                        'type': m.get('type', 'unknown'),
                        'filename': os.path.basename(m['local_path']) if m.get('local_path') else 'N/A',
                        'local_path': m.get('local_path', 'N/A')
                    }
                    for m in webpage_data['media'] if m.get('local_path')
                ]
            }
            
            print(f"✅ 网页提取完成!")
            print(f"📄 标题: {summary['title']}")
            print(f"📸 媒体文件下载: {summary['media_count']}")
            print(f"📝 Markdown文件: {markdown_path}")
            
            return summary
            
        except Exception as e:
            error_msg = f"❌ 提取网页时出错: {e}"
            print(error_msg)
            return {
                'error': error_msg,
                'url': url,
                'project_name': project_name or 'unknown'
            }

    def get_tools(self) -> List[FunctionTool]:
        """Get list of available tools."""
        return [
            FunctionTool(self.extract_webpage),
        ]

    def _detect_image_format(self, response: requests.Response, original_url: str) -> str:
        """
        智能检测图片格式，基于ArchiveBoxSaver的实现
        
        Args:
            response: HTTP响应对象
            original_url: 原始图片URL
            
        Returns:
            str: 图片文件扩展名 (如 '.jpg', '.png', '.svg')
        """
        try:
            # 检测图片格式
            content_type = response.headers.get('Content-Type', '').lower()
            url_ext = Path(original_url).suffix.lower()
            
            # 优先处理特殊格式（SVG等）
            if 'svg' in content_type or url_ext == '.svg':
                return '.svg'
            elif 'webp' in content_type or url_ext == '.webp':
                return '.webp'
            elif 'gif' in content_type or url_ext == '.gif':
                return '.gif'
            else:
                # 对于其他格式，使用imghdr检测二进制内容
                img_data = response.content
                detected_type = imghdr.what(None, h=img_data)
                if detected_type:
                    # imghdr返回的格式映射
                    format_mapping = {
                        'jpeg': '.jpg',
                        'png': '.png',
                        'gif': '.gif',
                        'bmp': '.bmp',
                        'webp': '.webp',
                        'tiff': '.tiff'
                    }
                    return format_mapping.get(detected_type, f'.{detected_type}')
                elif url_ext and url_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.svg']:
                    # 如果有原始扩展名且是常见图片格式就使用它
                    return url_ext
                else:
                    # 最后才使用默认值
                    return '.jpg'
                    
        except Exception as e:
            print(f"    图片格式检测失败: {e}")
            # 回退到URL扩展名或默认值
            url_ext = Path(original_url).suffix.lower()
            return url_ext if url_ext else '.jpg'

    def _replace_image_urls_in_markdown(self, markdown_content: str, url_mapping: Dict[str, str]) -> str:
        """
        系统性地替换Markdown中的图片URL，基于ArchiveBoxSaver的实现
        
        Args:
            markdown_content: 原始markdown内容
            url_mapping: 原始URL到本地路径的映射字典
            
        Returns:
            str: 替换后的markdown内容
        """
        if not url_mapping:
            return markdown_content
            
        print(f"🔄 开始替换 {len(url_mapping)} 个图片URL...")
        
        # 替换markdown格式的图片引用: ![alt](url)
        for original_url, local_path in url_mapping.items():
            # 匹配各种可能的图片引用格式
            patterns = [
                # 标准markdown格式 ![alt](url)
                (rf'!\[(.*?)\]\({re.escape(original_url)}\)', rf'![\1]({local_path})'),
                # HTML img标签格式 <img src="url" />
                (rf'<img([^>]*?)src=["\']?{re.escape(original_url)}["\']?([^>]*?)/?>', 
                 rf'<img\1src="{local_path}"\2/>'),
                # 简单的URL替换（作为后备）
                (re.escape(original_url), local_path),
            ]
            
            for pattern, replacement in patterns:
                old_content = markdown_content
                markdown_content = re.sub(pattern, replacement, markdown_content)
                if markdown_content != old_content:
                    print(f"  ✅ 替换成功: {original_url[:50]}... -> {local_path}")
                    break
            else:
                print(f"  ⚠️  未找到引用: {original_url[:50]}...")
        
        return markdown_content

    def _convert_html_to_markdown(self, html_content: str) -> str:
        """
        将HTML转换为清洁的Markdown格式，基于ArchiveBoxSaver的实现
        
        Args:
            html_content: 原始HTML内容
            
        Returns:
            str: 转换后的Markdown内容
        """
        if not html_content:
            return ""
            
        try:
            print("🔄 转换HTML到Markdown...")
            
            # 使用markdownify转换
            markdown_content = markdownify(html_content, 
                                         heading_style="ATX",  # 使用 # 格式的标题
                                         bullets="-",          # 使用 - 作为列表符号
                                         strip=['script', 'style', 'meta', 'link']  # 移除这些标签
                                         )
            
            # 清理格式，基于ArchiveBoxSaver的清理逻辑
            markdown_content = re.sub(r'\n{3,}', '\n\n', markdown_content)  # 移除多余换行
            markdown_content = re.sub(r'(\n\s){3,}', '\n\n', markdown_content)  # 移除多余空行
            markdown_content = markdown_content.replace('\xa0', ' ')  # 替换非断行空格
            markdown_content = markdown_content.strip()  # 移除首尾空白
            
            print(f"✅ HTML转换完成，长度: {len(markdown_content)} 字符")
            return markdown_content
            
        except Exception as e:
            print(f"❌ HTML转换失败: {e}")
            # 如果转换失败，回退到原始HTML或提取文本
            try:
                soup = BeautifulSoup(html_content, 'html.parser')
                # 移除脚本和样式标签
                for tag in soup(["script", "style", "meta", "link", "noscript"]):
                    tag.decompose()
                return soup.get_text(separator='\n', strip=True)
            except:
                return html_content


def main():
    """主函数，用于测试WebpageToolkit功能"""
    import sys
    
    print("🚀 WebpageToolkit 网页内容提取工具")
    print("=" * 60)
    
    # 获取URL输入
    if len(sys.argv) > 1:
        url = sys.argv[1]
        print(f"使用命令行参数URL: {url}")
    else:
        print("\n请输入要提取的网页URL:")
        print("💡 推荐测试URL (这些网站通常对爬虫友好):")
        print("  - https://httpbin.org/html")
        print("  - https://example.com")  
        print("  - https://www.python.org")
        print("  - https://github.com/microsoft/vscode")
        print("  - https://docs.python.org/3/")
        print("  - https://en.wikipedia.org/wiki/Python_(programming_language)")
        print("\n⚠️  注意：某些网站(如知乎、微博等)可能有反爬虫机制")
        url = input("\nURL: ").strip()
        
        if not url:
            url = "https://httpbin.org/html"
            print(f"使用默认测试URL: {url}")
    
    # 验证URL格式
    if not url.startswith(('http://', 'https://')):
        print("❌ 错误: URL必须以 http:// 或 https:// 开头")
        return
    
    # 初始化工具包
    print(f"\n🔧 初始化WebpageToolkit...")
    toolkit = WebpageToolkit(
        timeout=30,
        image_filter_config={
            'skip_keywords': ['badge', 'icon', 'logo', 'license', 'button', 'avatar'],
            'min_size_kb': 5  # 过滤小于5KB的图片
        },
        use_cache=False,  # 禁用缓存以测试最新逻辑
        user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        disable_cache=True,  # 完全禁用缓存
        proxy=None,  # 可根据需要设置代理，如 "http://proxy:8080"
        enable_archivebox_features=True  # 启用ArchiveBox特性
    )
    
    print(f"🌐 开始提取网页内容: {url}")
    print("-" * 60)
    
    try:
        # 执行网页提取
        result = toolkit.extract_webpage(url)
        
        if 'error' in result:
            print(f"❌ 提取失败: {result['error']}")
            return
        
        # 显示结果
        print(f"\n✅ 提取成功！")
        print(f"📝 页面标题: {result['title']}")
        print(f"📁 项目名称: {result['project_name']}")
        print(f"📂 输出目录: {result['output_dir']}")
        print(f"📄 Markdown文件: {result['markdown_file']}")
        print(f"🖼️  媒体文件数量: {result['media_count']}")
        
        # 显示媒体文件详情
        if result['media_files']:
            print(f"\n📎 下载的媒体文件:")
            for i, media in enumerate(result['media_files'], 1):
                print(f"   {i}. {media['type']}: {media['filename']}")
                print(f"      路径: {media['local_path']}")
        else:
            print(f"\n📎 未发现可下载的媒体文件")
        
        # 显示文件结构
        print(f"\n📋 生成的文件结构:")
        output_dir = result['output_dir']
        print(f"   {output_dir}/")
        print(f"   ├── webpage_content.md")
        
        media_dir = os.path.join(output_dir, 'media')
        if os.path.exists(media_dir) and os.listdir(media_dir):
            print(f"   └── media/")
            for filename in os.listdir(media_dir):
                print(f"       ├── {filename}")
        else:
            print(f"   └── media/ (空)")
        
        print(f"\n🎉 完成！请查看生成的文件:")
        print(f"   主文件: {result['markdown_file']}")
        print(f"   目录: {result['output_dir']}")
        
        # 显示部分内容预览
        try:
            with open(result['markdown_file'], 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                if len(lines) > 10:
                    preview = '\n'.join(lines[:10]) + '\n...'
                else:
                    preview = content
                
                print(f"\n📖 内容预览:")
                print("-" * 40)
                print(preview)
                print("-" * 40)
        except Exception as e:
            print(f"⚠️ 无法显示内容预览: {e}")
        
    except KeyboardInterrupt:
        print(f"\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 发生异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 