from typing import Optional

from camel.toolkits.base import BaseToolkit
from camel.toolkits.function_tool import FunctionTool


class RenderKeywordsToolkit(BaseToolkit):
    r"""用于生成关键词垂直排列动画的工具。它可以将一组关键词以动画方式呈现在屏幕上,
    每个关键词带有半透明背景以增强可读性。
    """

    def __init__(self, timeout: Optional[float] = None) -> None:
        r"""初始化用于生成关键词垂直排列动画的工具"""
        super().__init__(timeout=timeout)

    def generate_keywords_animation_code(self, keywords: list[str], step: int = 1) -> list[str]:
        r"""生成用于显示关键词垂直排列动画的Manim代码。

        该函数接收关键词列表，并生成相应的Manim动画代码行。生成的代码会创建一个
        调用render_keywords方法的函数，该方法将关键词以动画形式垂直排列显示。

        Args:
            keywords (List[str]): 字符串列表，包含要在动画中垂直排列显示的关键词或关键短语。
            step (int): 第几个分镜

        Returns:
            List[str]: 返回Manim代码行的字符串列表，这些代码可以被插入到Manim场景类中。
        """
        code_lines = []
        code_lines.append("    def render_keywords_tool(self) -> list:")
        code_lines.append(f"        return self.render_keywords({keywords})")
        return code_lines

    def get_tools(self) -> list[FunctionTool]:
        r"""Returns a list of FunctionTool objects representing the
        functions in the toolkit.

        Returns:
            List[FunctionTool]: A list of FunctionTool objects
                representing the functions in the toolkit.
        """
        return [
            FunctionTool(self.generate_keywords_animation_code),
        ]


if __name__ == "__main__":
    render_keywords_toolkit = RenderKeywordsToolkit()
    render_keywords_toolkit.generate_keywords_animation_code(
        ["局限性", "特定任务", "动作集稀疏", "GAIA任务成本", "OpenAI模型"],
    )
