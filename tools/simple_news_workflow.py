#!/usr/bin/env python3
"""
简化版新闻讲解工作流 - 使用animate_mindmap直接生成视频
重构版本：职责分离、配置化、错误处理完善
"""

import json
import os
import re
import subprocess
import sys
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Optional

from loguru import logger

# 添加父目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from dsl.v2.dsl_to_manim import generate_manim_code
from utils.create_llm_model import create_model


# ==================== 自定义异常 ====================
class NewsWorkflowError(Exception):
    """工作流基础异常"""

    pass


class ContentGenerationError(NewsWorkflowError):
    """内容生成异常"""

    pass


class VideoRenderError(NewsWorkflowError):
    """视频渲染异常"""

    pass


class PostProcessingError(NewsWorkflowError):
    """后处理异常"""

    pass


# ==================== 配置类 ====================
@dataclass
class NewsWorkflowConfig:
    """工作流配置类"""

    # 输入输出配置
    news_file: str = "news.md"
    output_dir: str = "output/simple_news"

    # 文件路径配置
    mindmap_doc_path: str = "docs/animate_mindmap.md"
    bgm_path: str = "assets/bgm_v2.m4a"

    # 视频配置
    video_quality: str = "-qh"  # 高质量
    video_filename: str = "news_mindmap.mp4"
    final_video_filename: str = "news_mindmap_final.mp4"

    # 音频配置
    bgm_volume: float = 0.05

    # FFmpeg配置
    subtitle_style: str = "Alignment=2,MarginV=6,fontcolor=white,Fontsize=16,fontweight=bold,FontName=微软雅黑"

    # DSL配置
    background_color: str = "BLACK"
    title: str = "AI_news"
    author: str = "AI_news_generator"


# ==================== 服务类 ====================
class FileService:
    """文件服务类 - 处理所有文件I/O操作"""

    @staticmethod
    def read_file_content(file_path: str) -> str:
        """读取文件内容"""
        try:
            with open(file_path, encoding="utf-8") as f:
                return f.read()
        except FileNotFoundError:
            raise NewsWorkflowError(f"文件不存在: {file_path}")
        except Exception as e:
            raise NewsWorkflowError(f"读取文件失败 {file_path}: {e}")

    @staticmethod
    def write_json_file(file_path: Path, data: dict[str, Any]) -> None:
        """写入JSON文件"""
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            raise NewsWorkflowError(f"写入JSON文件失败 {file_path}: {e}")

    @staticmethod
    def ensure_directory(directory: Path) -> None:
        """确保目录存在"""
        try:
            directory.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            raise NewsWorkflowError(f"创建目录失败 {directory}: {e}")


class ContentGenerator:
    """内容生成服务类 - 处理LLM相关操作"""

    def __init__(self, model=None):
        self.model = model or create_model()

    def generate_mindmap_data(self, news_content: str, doc: str) -> dict[str, Any]:
        """根据新闻内容生成思维导图数据"""
        prompt = f"""
请根据以下AI新闻内容，生成一个适合用animate_mindmap函数展示的思维导图数据结构。

要求：
1. 提取关键要点，保持简洁
2. 层级不要超过3层，最后一层节点不要只写几个字，可以用2-3个最后一层节点，把该新闻的重点信息都表达清楚
3. 重点突出本期最重要的AI进展
4. 按照重要性组织结构
5. 输出JSON格式的思维导图数据，注意字段名称和格式要严格符合要求，使用“标题”和“子章节”
6. 生成focus_sequence，按顺序聚焦每个二级节点
    1. 每个聚焦点包含节点文本和对应的简短旁白
    2. 总时长控制在2分钟左右
    3. 旁白要详细但不冗长
    4. 整体的旁白和每个聚焦节点的旁白不能有重复，整体旁白以"一图带你了解今天的AI热点新闻"作为开头。
    5. 根结点无需聚焦，用整体的旁白做简单的整体介绍即可，聚焦序列从第一个需要聚焦讲解的节点开始，但最终以根结点结束，并配上总结性的旁白

新闻内容：
{news_content}

animate_mindmap函数说明：
{doc}
"""

        try:
            response = self.model.run([{"role": "user", "content": prompt}])
            content = response.choices[0].message.content

            # 提取JSON部分
            json_match = re.search(r"```json\s*\n(.*?)\n\s*```", content, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(1))
            else:
                # 尝试直接解析
                return json.loads(content)
        except json.JSONDecodeError as e:
            raise ContentGenerationError(f"JSON解析失败: {e}")
        except Exception as e:
            raise ContentGenerationError(f"生成思维导图数据失败: {e}")


class VideoRenderer:
    """视频渲染服务类"""

    def __init__(self, config: NewsWorkflowConfig):
        self.config = config

    def render_video(self, code_path: Path, scene_name: str, output_dir: Path) -> Optional[str]:
        """渲染视频"""
        video_output_dir = output_dir / "videos"
        FileService.ensure_directory(video_output_dir)

        cmd = [
            "python",
            "-m",
            "manim",
            self.config.video_quality,
            "--output_file",
            self.config.video_filename,
            str(code_path),
            scene_name,
        ]

        logger.info(f"执行渲染命令: {' '.join(cmd)}")

        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                logger.info("视频渲染成功")
                # 查找生成的视频文件
                expected_file_path = Path(
                    "media/videos",
                    code_path.stem,
                    "1080p60",
                    self.config.video_filename,
                )
                if expected_file_path.exists():
                    return str(expected_file_path)
                else:
                    logger.error(f"未找到预期的视频文件: {expected_file_path}")
                    return None
            else:
                raise VideoRenderError(f"视频渲染失败: {result.stderr}")
        except Exception as e:
            raise VideoRenderError(f"渲染过程出错: {e}")


class VideoPostProcessor:
    """视频后处理服务类"""

    def __init__(self, config: NewsWorkflowConfig):
        self.config = config

    def add_subtitles_and_music(self, video_path: str, output_dir: Path) -> Optional[str]:
        """添加字幕和背景音乐"""
        if not video_path or not os.path.exists(video_path):
            raise PostProcessingError("视频文件不存在")

        # 处理字幕（简化版本）
        video_with_subtitles = self._add_subtitles(video_path, output_dir)

        # 添加背景音乐
        final_video = self._add_background_music(video_with_subtitles or video_path, output_dir)

        return final_video

    def _add_subtitles(self, video_path: str, output_dir: Path) -> Optional[str]:
        """添加字幕"""
        # TODO: 实现字幕生成逻辑
        subtitle_path = video_path.replace(".mp4", ".srt")
        output_path = output_dir / "news_mindmap_subtitle.mp4"
        ffmpeg_cmd = [
            "ffmpeg",
            "-i",
            str(video_path),
            "-lavfi",
            f"subtitles='{subtitle_path}':force_style='Alignment=2,MarginV=6,fontcolor=white,Fontsize=16,fontweight=bold,FontName=微软雅黑'",
            "-y",
            str(output_path.resolve()),
        ]
        try:
            subprocess.run(ffmpeg_cmd, check=True, capture_output=True, text=True)
            logger.info(f"字幕添加成功: {output_path}")
        except Exception as e:
            logger.error(f"字幕添加失败: {e}")
            return None
        return str(output_path)

    def _add_background_music(self, video_path: str, output_dir: Path) -> Optional[str]:
        """添加背景音乐"""
        if not os.path.exists(self.config.bgm_path):
            logger.warning("背景音乐文件不存在，跳过音乐添加")
            return video_path

        final_video_path = output_dir / self.config.final_video_filename

        cmd = [
            "ffmpeg",
            "-y",
            "-i",
            video_path,
            "-i",
            self.config.bgm_path,
            "-c:v",
            "copy",
            "-filter_complex",
            f"[1:a]volume={self.config.bgm_volume}[bgm];[0:a][bgm]amix=inputs=2:duration=first",
            "-shortest",
            str(final_video_path),
        ]

        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"最终视频已生成: {final_video_path}")
                return str(final_video_path)
            else:
                logger.warning(f"添加背景音乐失败: {result.stderr}")
                return video_path
        except Exception as e:
            logger.warning(f"添加背景音乐出错: {e}")
            return video_path


# ==================== 主要工作流编排类 ====================
class NewsWorkflowOrchestrator:
    """新闻工作流编排器 - 协调各个服务组件"""

    def __init__(self, config: NewsWorkflowConfig):
        self.config = config
        self.output_dir = Path(config.output_dir)

        # 初始化服务组件
        self.file_service = FileService()
        self.content_generator = ContentGenerator()
        self.video_renderer = VideoRenderer(config)
        self.video_post_processor = VideoPostProcessor(config)

        # 确保输出目录存在
        FileService.ensure_directory(self.output_dir)

    def run(self) -> None:
        """运行完整的新闻工作流"""
        try:
            logger.info("开始简化版新闻讲解工作流")
            dsl_file = self.output_dir / "dsl.json"
            if not dsl_file.exists():
                # 步骤1: 读取新闻内容
                logger.info("步骤1: 读取新闻内容")
                news_content = self.file_service.read_file_content(self.config.news_file)
                doc = self.file_service.read_file_content(self.config.mindmap_doc_path)

                # 步骤2: 生成思维导图数据
                logger.info("步骤2: 生成思维导图数据")
                mindmap_data = self.content_generator.generate_mindmap_data(news_content, doc)

                # 步骤3: 创建DSL JSON
                logger.info("步骤3: 创建DSL JSON")
                dsl_json = self._create_dsl_json(mindmap_data)
                self.file_service.write_json_file(dsl_file, dsl_json)
                logger.info(f"DSL JSON已保存到: {dsl_file}")

            # 步骤4: 生成Manim代码
            logger.info("步骤4: 生成Manim代码")
            manim_file = self.output_dir / "news_mindmap.py"
            _, scene_name = generate_manim_code(dsl_file, manim_file)
            logger.info(f"Manim代码已生成: {manim_file}")

            # 步骤5: 渲染视频
            logger.info("步骤5: 渲染视频")
            video_path = self.video_renderer.render_video(manim_file, scene_name, self.output_dir)

            if video_path:
                # 步骤6: 后处理（字幕和背景音乐）
                logger.info("步骤6: 添加字幕和背景音乐")
                final_video = self.video_post_processor.add_subtitles_and_music(video_path, self.output_dir)

                if final_video:
                    logger.info(f"\n✅ 工作流完成！最终视频: {final_video}")
                else:
                    logger.info(f"\n✅ 工作流完成！视频: {video_path}")
            else:
                logger.error("\n❌ 工作流失败：视频渲染未成功")

        except NewsWorkflowError as e:
            logger.error(f"\n❌ 工作流失败: {e}")
            raise
        except Exception as e:
            logger.error(f"\n❌ 工作流出现未预期错误: {e}")
            raise NewsWorkflowError(f"工作流执行失败: {e}")

    def _create_dsl_json(self, mindmap_data: dict[str, Any]) -> dict[str, Any]:
        """创建DSL JSON数据结构"""
        return {
            "metadata": {
                "title": self.config.title,
                "author": self.config.author,
                "background_color": self.config.background_color,
            },
            "actions": [mindmap_data],
        }


# ==================== 兼容性包装类 ====================
class SimpleNewsWorkflow:
    """保持向后兼容的简单包装类"""

    def __init__(self, news_file: str = "news.md", output_dir: str = "output/simple_news"):
        config = NewsWorkflowConfig(news_file=news_file, output_dir=output_dir)
        self.orchestrator = NewsWorkflowOrchestrator(config)

    def run(self) -> None:
        """运行工作流"""
        self.orchestrator.run()


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="简化版新闻讲解工作流")
    parser.add_argument("--news-file", default="news.md", help="新闻文件路径")
    parser.add_argument("--output-dir", default="output/simple_news", help="输出目录")

    args = parser.parse_args()

    workflow = SimpleNewsWorkflow(args.news_file, args.output_dir)
    workflow.run()
