#!/usr/bin/env python3
"""
思维导图工具 - 内容结构化组织类
基于内容自动生成思维导图，将复杂的层次结构信息可视化展示
"""

import os
from typing import Any, Optional

from loguru import logger

from utils.create_llm_model import create_model

from .base_tool import EnhancementTool, ToolCategory


class MindmapTool(EnhancementTool):
    """思维导图工具 - 内容结构化组织类"""

    tool_name = "mindmap_generation"
    tool_description = "基于内容自动生成思维导图，将复杂的层次结构信息可视化展示，帮助观众理解知识体系和概念关系"
    tool_category = ToolCategory.CONTENT_ORGANIZATION

    def __init__(self, config=None):
        self.config = config
        self.agent = None
        if config:
            self._init_model()

    def _init_model(self):
        """初始化Camel模型"""
        try:
            from camel.agents import ChatAgent
            from camel.messages import BaseMessage

            self.model = create_model()

            # 定制化系统提示词
            system_prompt = """你是专业的知识结构分析专家，负责从内容中提取层次化的思维导图结构。
你的任务是：
1. 分析内容是否适合生成思维导图（需要有明确的层次结构和分类关系）
2. 提取主题、子主题和层次关系
3. 生成符合思维导图格式的结构化数据
4. 确保提取的结构清晰、逻辑性强、层次分明

请严格按照要求的JSON格式输出，确保数据的准确性和完整性。"""

            self.agent = ChatAgent(
                system_message=BaseMessage.make_assistant_message("", system_prompt),
                model=self.model,
            )
            logger.info(f"{self.tool_name}模型初始化成功")
        except Exception as e:
            logger.warning(f"{self.tool_name}模型初始化失败: {e}")

    def get_tool_info(self) -> str:
        """获取工具信息 - 供智能选择器决策使用"""
        return """思维导图工具 - 将复杂的层次结构信息可视化展示

**核心作用**：
思维导图是一种可视化的知识组织工具，通过树状结构展示主题与子主题之间的层次关系，帮助观众快速理解复杂知识体系的整体架构和内在逻辑。

**适合的内容类型**：
- 知识体系介绍（如学科框架、技术栈结构）
- 概念分类体系（如产品功能分类、理论体系）
- 组织架构说明（如公司结构、项目模块）
- 多层次的分析框架（如SWOT分析、能力模型）

**典型应用场景**：
✅ 介绍机器学习的各个分支领域及其关系
✅ 展示软件架构的模块组成和层次结构
✅ 梳理课程大纲的章节和知识点体系
✅ 说明产品功能的分类和组织方式

**不适合的内容类型**：
- 时间序列内容（如历史发展、操作步骤）
- 线性流程说明（如算法步骤、安装指南）
- 因果关系链条（如问题分析、故障排除）
- 纯叙述性内容（如故事、案例描述）

**典型不适用场景**：
❌ 讲解算法的执行步骤和流程
❌ 介绍历史事件的时间发展脉络
❌ 说明软件的安装和配置过程
❌ 分析问题的原因和解决方案

**判断标准**：
内容是否包含明确的"主题-子主题"层次结构？是否需要展示分类关系而非时间或因果关系？如果答案是肯定的，则适合使用思维导图工具。

**输出形式**：生成结构化的思维导图数据，支持动画展示和重点突出。"""

    def can_apply(self, content: str, purpose: str, context: dict[str, Any]) -> bool:
        """简化的可用性检查 - 只检查基本配置条件"""
        # 基本检查：配置存在、内容长度、配置开关
        basic_check = (
            self.config
            and len(content) >= 800  # 思维导图需要足够的内容来提取结构
            and self.config.get("material", {}).get("material_enhance", {}).get(self.tool_name, True)
        )

        # 如果需要LLM支持，检查agent是否初始化成功
        if hasattr(self, "agent") and self.agent is None:
            return False

        return basic_check

    def apply_tool(self, content: str, output_dir: str, context: dict[str, Any], focus: str = None) -> Optional[str]:
        """应用工具，执行具体的扩充操作"""
        logger.info(f"🧠 应用{self.tool_name}工具，准备处理内容")

        try:
            os.makedirs(output_dir, exist_ok=True)

            # 生成输出路径
            output_filename = f"{self.tool_name}_output.json"
            output_path = os.path.join(output_dir, output_filename)

            # 检查是否已存在文件
            if os.path.exists(output_path):
                logger.info(f"工具输出已存在: {output_path}")
                with open(output_path, encoding="utf-8") as f:
                    existing_data = f.read()
                return existing_data

            # 执行具体的工具逻辑，使用focus参数而不是context
            result_content = self._process_with_camel(content, focus)

            if not result_content:
                return None

            # 保存结果数据（保持原有格式以便后续处理）
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(result_content)

            logger.info(f"🧠 {self.tool_name}工具处理完成: {output_path}")
            # 返回思维导图内容字符串，符合新的简化要求
            return result_content

        except Exception as e:
            logger.error(f"{self.tool_name}工具处理失败: {e}")
            return None

    def _process_with_camel(self, content: str, focus: str = None) -> Optional[str]:
        """使用Camel进行数据提取的处理逻辑"""
        # 构建结构化提示词，重点利用focus信息
        focus_instruction = ""
        if focus:
            focus_instruction = f"""
**重点关注方向**：{focus}
请在生成思维导图时特别关注这个方向，确保相关内容在思维导图中得到充分体现和突出展示。"""

        prompt = f"""你是专业的知识结构分析专家，需要基于以下内容生成思维导图。

**待分析内容**：
<content>
{content}
</content>

<focus_instruction>
{focus_instruction}
</focus_instruction>

**任务要求**：
1. 根据给定的重点关注方向，从输入内容中提取相关的内容，生成思维导图结构。
2. **思维导图生成**：
   - 识别核心主题作为根节点
   - 提取主要分支（一级子主题），建议3-6个
   - 识别次级分支（二级子主题），每个一级分支下2-4个
   - 层次清晰，最多3层深度
   - 每个节点的文本要能抓住重点，体现这个节点的核心信息，并且简洁精炼

3. **输出格式**：
   直接输出思维导图的markdown格式内容，包含：
   - 清晰的层次结构
   - 简洁明了的节点标题，同时信息密度要高，要能体现核心信息，帮助用户理解要重点表达的内容
   - focus_sequence 是在展示思维导图过程中会按顺序动态聚焦的节点列表（只需要包括要聚焦展示的节点文本，不需要包括父节点的文本），帮助观众理解内容的重点和逻辑关系。如果focus_sequence不为空，narration中需要有相应的讲解。如果不是特别重要，不要聚焦到过深的节点上，尽量控制在一级子主题的层次。

**输出要求**：
请严格按照以下JSON格式输出：

{{
    "suitable": true/false,
    "reason": "适用性判断的原因说明",
    "mindmap_data": {{
        "标题": "核心主题名称",
        "子章节": [
            {{
                "标题": "一级子主题1",
                "子章节": [
                    {{"标题": "二级子主题1"}},
                    {{"标题": "二级子主题2"}}
                ]
            }},
            {{
                "标题": "一级子主题2",
                "子章节": [
                    {{"标题": "二级子主题3"}},
                    {{"标题": "二级子主题4"}}
                ]
            }}
        ]
    }},
    "layout_style": "balance",
    "max_depth": 3,
    "focus_sequence": ["核心主题名称", "一级子主题1", "一级子主题2"],
    "narration": "简短的思维导图介绍文本，作为思维导图展示过程中的旁白介绍。"
}}

请直接输出结果，不要包含额外说明。"""

        logger.info(f"生成思维导图, prompt characters: {len(prompt)}, words: {len(prompt.split())}")

        try:
            # 调用Camel agent进行处理
            response = self.agent.step(prompt)
            response_content = response.msgs[0].content.strip()

            # 检查是否不适合生成思维导图
            if response_content.startswith("该内容不适合生成思维导图"):
                logger.info(f"内容不适合生成思维导图: {response_content}")
                return response_content

            # 返回生成的思维导图内容
            return response_content

        except Exception as e:
            logger.error(f"Camel数据提取失败: {e}")
            return None

    def generate_intro(self, tool_result: dict[str, Any]) -> str:
        """生成工具输出的介绍文本"""
        # 兼容新的返回格式：tool_result现在直接是字符串内容
        if isinstance(tool_result, str):
            result_content = tool_result
        else:
            # 兼容旧格式
            result_content = tool_result.get("data", "") if tool_result else ""

        if not result_content:
            return ""

        # 检查是否不适合生成思维导图
        if result_content.startswith("该内容不适合生成思维导图"):
            return f"## 🧠 思维导图分析\n\n❌ {result_content}\n\n"

        # 生成思维导图介绍
        intro = f"## 🧠 {self.tool_description}\n\n"
        intro += "**生成的思维导图结构**：\n\n"
        intro += result_content
        intro += "\n\n*💡 此思维导图将帮助观众更好地理解内容的层次结构和知识体系*\n\n"

        return intro


if __name__ == "__main__":
    from utils.common import Config

    config = Config()
    tool = MindmapTool(config.config)
    tool.apply_tool(
        open("output/2401.03568/2401.03568.md").read(),
        "./test_output",
        context={},
        focus="根据论文『5 Agent AI Categorization』部分内容，生成Agent AI的分类思维导图，并提取各类别下的核心概念或关键词。",
    )
