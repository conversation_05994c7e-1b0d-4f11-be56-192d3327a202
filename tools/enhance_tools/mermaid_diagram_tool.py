#!/usr/bin/env python3
"""
Mermaid图表生成工具 - 多模态呈现类
基于内容自动生成Mermaid图表代码并渲染为图像，支持流程图、架构图等多种图表类型
"""

import json
import os
import re
import subprocess
from typing import Any, Optional

from loguru import logger

from utils.create_llm_model import create_model

from .base_tool import EnhancementTool, ToolCategory


class MermaidDiagramTool(EnhancementTool):
    """Mermaid图表生成工具 - 多模态呈现类"""

    tool_name = "mermaid_diagram"
    tool_description = "基于内容自动生成Mermaid图表代码并渲染为图像，支持流程图、架构图等多种图表类型"
    tool_category = ToolCategory.MULTIMODAL_PRESENTATION

    def __init__(self, config=None):
        self.config = config
        self.agent = None
        if config:
            self._init_model()

    def _init_model(self):
        """初始化Camel模型 - 用于智能生成Mermaid代码"""
        try:
            from camel.agents import ChatAgent
            from camel.messages import BaseMessage

            self.model = create_model()

            # 定制化系统提示词
            system_prompt = """你是专业的Mermaid图表生成专家，负责根据内容描述生成准确的Mermaid代码。
请严格按照Mermaid语法规范，确保生成的代码可以正确渲染。支持flowchart、sequence、class、state等多种图表类型。"""

            self.agent = ChatAgent(
                system_message=BaseMessage.make_assistant_message("", system_prompt),
                model=self.model,
            )
            logger.info(f"{self.tool_name}模型初始化成功")
        except Exception as e:
            logger.warning(f"{self.tool_name}模型初始化失败: {e}")

    def get_tool_info(self) -> str:
        """获取工具信息 - 供智能选择器决策使用"""
        return """Mermaid图表生成工具 - 将文本描述转化为多种可视化图表

**核心作用**：
Mermaid图表工具能够将文本描述的内容（如流程、序列、状态、结构等）自动转换为多种标准化的图表代码（Mermaid语法），并直接渲染成图像。它擅长快速生成清晰、简洁的可视化表示，便于理解和沟通。

**适合的内容类型**：
- 流程和工作流描述 (生成流程图 flowchart)
- 系统交互和消息序列 (生成序列图 sequenceDiagram)
- 软件或系统状态变迁 (生成状态图 stateDiagram)
- 简单的类和对象关系 (生成类图 classDiagram)
- 项目计划和任务排期 (生成甘特图 gantt)
- 用户操作路径和体验流程 (生成用户旅程图 userJourney)
- 简单的饼图数据展示 (生成饼图 pie)
- 实体关系描述 (生成实体关系图 erDiagram)

**典型应用场景**：
✅ 快速将一段算法描述转化为流程图。
✅ 为API的调用顺序生成序列图。
✅ 展示一个订单处理过程中的不同状态及其转换。
✅ 简要说明几个核心类之间的关系。
✅ 规划一个小型项目的阶段和时间表。

**不适合的内容类型**：
- 高度复杂、细节繁多、需要深度定制的系统架构图 (对于复杂架构的*概念描述*，`architecture_diagram_tool`可能更合适，它生成文本描述；对于直接生成复杂图表，可能需要更专业的工具)。
- 自由形态的、非结构化的信息组织 (如头脑风暴，`mindmap_tool`更合适)。
- 需要高度艺术性或品牌化视觉效果的图表。
- 纯粹的文本叙述、代码块或数学公式，缺乏可图表化的结构或流程。

**典型不适用场景**：
❌ 绘制一个包含数十个微服务及其复杂交互的详细企业级系统架构图 (Mermaid可能难以清晰表达)。
❌ 将一篇研究论文的核心论点组织成发散性的思维导图。
❌ 生成用于营销材料的高度美化的图表。

**判断标准**：
内容是否描述了一个可以用标准图表类型（如流程图、序列图、状态图、甘特图等）清晰表达的结构、流程、序列或关系？是否需要快速从文本生成可直接使用的图表代码和图像？

**与 `architecture_diagram_tool` 的主要区别**：
- **输出形式**：Mermaid工具直接生成可渲染的Mermaid图表代码和最终的图像文件 (如PNG)。而 `architecture_diagram_tool` 旨在生成系统架构的*文本描述*，这个描述本身是文本，可能用于后续的人工理解或作为其他可视化工具的输入。
- **图表类型**：Mermaid支持多种明确的图表类型（流程图、序列图、甘特图等），适合表示具体流程和结构。`architecture_diagram_tool` 更侧重于系统组件、模块、层次和交互的抽象概念描述。
- **直接性**：Mermaid工具的目标是“直接生成图表”；`architecture_diagram_tool` 的目标是“生成架构的描述性文本”。

**输出形式**：生成Mermaid代码文件 (.mmd) 和对应的图像文件 (如 .png)，以及包含这些信息的JSON数据。"""

    def can_apply(self, content: str, purpose: str, context: dict[str, Any]) -> bool:
        """简化的可用性检查 - 只检查基本配置条件"""
        # 基本检查：配置存在、内容长度、配置开关
        basic_check = (
            self.config
            and len(content) >= 300  # Mermaid图表需要足够的内容描述
            and self.config.get("material", {}).get("material_enhance", {}).get(self.tool_name, True)
        )

        # 如果需要LLM支持，检查agent是否初始化成功
        if hasattr(self, "agent") and self.agent is None:
            return False

        return basic_check

    def apply_tool(
        self, content: str, output_dir: str, context: dict[str, Any], focus: str = None
    ) -> Optional[list[dict[str, Any]]]:  # Adjusted return type to match existing logic more closely
        """应用工具，执行具体的扩充操作"""
        logger.info(f"🔧 应用{self.tool_name}工具，准备处理内容")

        try:
            os.makedirs(output_dir, exist_ok=True)

            # 生成输出路径
            output_filename = f"{self.tool_name}_output.json"
            output_path = os.path.join(output_dir, output_filename)

            # 检查是否已存在文件
            if os.path.exists(output_path):
                logger.info(f"工具输出已存在: {output_path}")
                with open(output_path, encoding="utf-8") as f:
                    existing_data = f.read()
                return existing_data

            # 执行具体的工具逻辑
            result_data = self._process_content(content, focus)

            if not result_data:
                return None

            # 保存结果数据
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(result_data)

            logger.info(f"🔧 {self.tool_name}工具处理完成: {output_path}")
        except Exception as e:
            logger.error(f"{self.tool_name}工具处理失败: {e}")
            return None

    def _process_content(self, content: str, focus: str = None) -> Optional[list[dict[str, Any]]]:
        """使用Camel进行智能Mermaid代码生成"""

        focus_instruction = ""
        if focus:
            focus_instruction = f"""\n**重点关注方向**：{focus}
请在生成Mermaid图表时特别关注这个方向，确保相关内容和关系在图表中得到清晰且准确的体现。如果适用，请让图表围绕此焦点展开。"""

        # 构建结构化提示词
        prompt = f"""基于以下内容生成合适的Mermaid图表代码：

**内容材料**：
<content>
{content}
</content>

<focus_instruction>
{focus_instruction}
</focus_instruction>

**输出要求**：
请分析内容并生成合适的Mermaid图表，满足以下要求：
1. 选择最能表达核心信息（特别是符合`重点关注方向`的）的Mermaid图表类型（例如 flowchart, sequenceDiagram, classDiagram, stateDiagram, erDiagram, gantt, pie, userJourney 等）。
2. 为图表提供一个简洁且信息丰富的`title`和`description`。
3. 生成一个符合Mermaid语法的`mermaid_code`。
4. 提供一个语义化的`semantic_name`用于文件名。

请严格按照以下格式输出，不要包含任何额外的解释或Markdown标记之外的内容：

```json
{{
    "semantic_name": "图表的语义化文件名",
    "title": "图表的标题",
    "description": "对图表内容的简要描述，说明它展示了什么",
    "mermaid_code": "符合Mermaid语法的图表代码"
}}
```

**输出示例**：
```json
{{
    "semantic_name": "model_training_flow",
    "title": "模型训练流程图",
    "description": "展示模型训练的流程，包括数据准备、模型训练、评估和部署",
    "mermaid_code": "flowchart TD\\n    A[数据准备] --> B[模型训练] --> C[评估] --> D[部署]"
}}
```

请确保Markdown格式正确无误，文本内容尽量用中文，并且`mermaid_code`是有效的Mermaid语法，注意`mermaid_code`要在同一行。
生成的图表要简洁明晰，视觉上不能太杂乱。

常见的mermaid语法错误：
```markdown
# Mermaid 常见语法错误总结

## 1. 节点标签中的特殊字符问题

文本尽量用引号包裹，避免特殊字符导致的解析错误，尤其是括号等有特殊含义的符号。
### ❌ 错误示例
```mermaid
graph TD
    A[Node (with parentheses)]
```

### ✅ 正确做法
```mermaid
graph TD
    A["Node (with parentheses)"]
```

**常见问题字符：**
- `()` 括号 - 会被解析为特殊语法
- `"` 双引号 - 会干扰字符串解析
- `&` 符号 - 在某些情况下会出错
- `<>` 尖括号 - 可能被误认为HTML标签

## 2. 换行符问题

### ❌ 错误示例
```mermaid
graph TD
    A[Line 1\\nLine 2\\nLine 3]
```

### ✅ 正确做法
```mermaid
graph TD
    A[Line 1<br/>Line 2<br/>Line 3]
```

## 3. Subgraph 语法错误

### ❌ 错误示例
```mermaid
graph TD
    subgraph My Group (with parentheses)
        A --> B
    end
```

### ✅ 正确做法
```mermaid
graph TD
    subgraph "My Group with parentheses"
        A --> B
    end

    # 或者
    subgraph My_Group_with_parentheses
        A --> B
    end
```

## 4. 节点ID命名错误

### ❌ 错误示例
```mermaid
graph TD
    123Node[Start]  # 不能以数字开头
    Node-ID[Middle] # 连字符可能有问题
    Node ID[End]    # 空格会出错
```

### ✅ 正确做法
```mermaid
graph TD
    Node123[Start]
    NodeID[Middle]
    Node_ID[End]
```

## 5. 箭头和连接语法错误

### ❌ 错误示例
```mermaid
graph TD
    A -> B          # 错误的箭头语法
    C -- text -> D  # 混合语法错误
    E ----> F       # 过多的连字符
```

### ✅ 正确做法
```mermaid
graph TD
    A --> B
    C -- text --> D
    E --> F
```

## 6. 样式定义错误

### ❌ 错误示例
```mermaid
graph TD
    A --> B
    style A fill:red,stroke:blue  # 缺少#号
    style B fill:#ff0000,stroke-width:2  # 缺少px单位
```

### ✅ 正确做法
```mermaid
graph TD
    A --> B
    style A fill:#ff0000,stroke:#0000ff
    style B fill:#ff0000,stroke-width:2px
```

## 7. 类定义和应用错误

### ❌ 错误示例
```mermaid
graph TD
    A --> B
    classDef myClass fill:#f9f,stroke:#333,stroke-width:4px;  # 分号错误
    class A,B myClass  # 逗号后有空格
```

### ✅ 正确做法
```mermaid
graph TD
    A --> B
    classDef myClass fill:#f9f,stroke:#333,stroke-width:4px
    class A,B myClass
```

## 8. 流程图方向错误

### ❌ 错误示例
```mermaid
graph LR-TD  # 无效的方向组合
    A --> B
```

### ✅ 正确做法
```mermaid
graph TD    # 或 LR, RL, BT
    A --> B
```

## 9. 注释语法错误

### ❌ 错误示例
```mermaid
graph TD
    A --> B // 这是注释  # 错误的注释语法
    C --> D # 这样也不对
```

### ✅ 正确做法
```mermaid
graph TD
    A --> B
    C --> D
    %% 这是正确的注释语法
```

## 10. 序列图语法错误

### ❌ 错误示例
```mermaid
sequenceDiagram
    Alice->Bob: Hello  # 缺少连字符
    Bob-->Alice: Hi   # 箭头不一致
```

### ✅ 正确做法
```mermaid
sequenceDiagram
    Alice->>Bob: Hello
    Bob->>Alice: Hi
```

## 11. 缩进和格式错误

### ❌ 错误示例
```mermaid
graph TD
A --> B
    C --> D  # 不一致的缩进
        E --> F
```

### ✅ 正确做法
```mermaid
graph TD
    A --> B
    C --> D
    E --> F
```

"""

        try:
            logger.info(f"创建mermaid图表， prompt characters: {len(prompt)}, words: {len(prompt.split())}")
            # 调用Camel agent进行处理
            response = self.agent.step(prompt)
            response_content = response.msgs[0].content

            json_content = re.search(r"```json\n(.*?)\n```", response_content, re.DOTALL)
            if json_content:
                json_content = json_content.group(1)
                logger.info(f"找到Mermaid图表代码\n{json_content}")
                json_content = json.loads(json_content)
                mermaid_result = self._render_diagram(json_content)
                return mermaid_result
            else:
                logger.error("未找到Mermaid图表代码")
                return None

        except Exception as e:
            logger.error(f"Camel Mermaid代码生成失败: {e}")
            return None

    def _render_diagram(self, diagram: dict[str, Any]) -> Optional[str]:
        """渲染Mermaid图表为图像文件"""
        try:
            # 生成语义化文件名
            semantic_name = diagram.get("semantic_name", "mermaid_diagram")
            semantic_name = re.sub(r"[^\w\-_]", "_", semantic_name)  # 清理文件名

            # 创建Mermaid文件
            mmd_filename = f"{semantic_name}.mmd"
            mmd_path = os.path.join("output", mmd_filename)
            logger.info(f"创建Mermaid文件: {mmd_path}")

            # 确保输出目录存在
            os.makedirs(os.path.dirname(mmd_path), exist_ok=True)

            # 写入Mermaid代码
            mermaid_code = diagram.get("mermaid_code", "")
            with open(mmd_path, "w", encoding="utf-8") as f:
                f.write(mermaid_code)

            # 生成PNG图像
            png_filename = f"{semantic_name}.png"
            png_path = os.path.join("output", png_filename)

            # 使用mmdc命令渲染图像
            success = self._render_mermaid_to_png(mmd_path, png_path)

            rendered_diagram = ""
            if success:
                rendered_diagram = f"### title\n\n{diagram.get('title', '')}\n\n"
                rendered_diagram += f"### description\n\n{diagram.get('description', '')}\n\n"
                rendered_diagram += f"### image\n\n![image]({png_path})\n\n"
                rendered_diagram += f"### code\n\n```mermaid\n{mermaid_code}\n```\n\n"
                logger.info(f"✅ 成功渲染图表: {semantic_name}")
            else:
                logger.warning(f"⚠️ 图表渲染失败: {semantic_name}")

        except Exception as e:
            logger.error(f"渲染图表时出错: {e}")
            return ""
        return rendered_diagram

    def _render_mermaid_to_png(self, mmd_path: str, png_path: str) -> bool:
        """使用mmdc命令将Mermaid文件渲染为PNG图像"""
        try:
            # 构建mmdc命令
            cmd = ["mmdc", "-i", mmd_path, "-o", png_path, "-w", "2160", "-s", "4"]

            # 执行命令
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                logger.info(f"成功渲染Mermaid图表: {png_path}")
                return True
            else:
                logger.error(f"mmdc命令执行失败: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.error(f"mmdc命令执行超时: {mmd_path}")
            return False
        except FileNotFoundError:
            logger.error("mmdc命令未找到，请确保已安装mermaid-cli")
            return False
        except Exception as e:
            logger.error(f"执行mmdc命令时出错: {e}")
            return False

    def generate_intro(self, tool_result: dict[str, Any]) -> str:
        """生成工具输出的介绍文本"""
        data = tool_result.get("data", {})

        if not data:
            return ""

        rendered_diagrams = data.get("rendered_diagrams", [])
        diagram_count = data.get("diagram_count", 0)

        intro = f"## 📊 {self.tool_description}\n\n"
        intro += f"**生成图表数量**: {diagram_count}\n\n"

        if rendered_diagrams:
            intro += "### 生成的图表\n\n"
            for diagram in rendered_diagrams:
                title = diagram.get("title", "未命名图表")
                description = diagram.get("description", "")
                image_file = diagram.get("image_file", "")
                diagram_type = diagram.get("type", "unknown")

                intro += f"#### {title}\n\n"
                intro += f"**类型**: {diagram_type}\n\n"
                if description:
                    intro += f"**描述**: {description}\n\n"

                if image_file and os.path.exists(image_file):
                    intro += f"![{title}]({image_file})\n\n"
                    intro += f"**图片路径**: `{image_file}`\n\n"
                else:
                    intro += f"**图片文件**: {image_file} (文件不存在)\n\n"

                intro += "---\n\n"

        intro += "**使用说明**: 上述图表已生成为PNG图像，可直接在Markdown中使用。\n\n"

        return intro


if __name__ == "__main__":
    from utils.common import Config

    config = Config()
    tool = MermaidDiagramTool(config.config)
    tool.apply_tool(
        open("output/2504.07491/2504.07491.md").read(),
        "./test_output",
        context={},
        focus="根据论文中的内容，生成图表展示Kimi-VL的训练过程",
    )
