#!/usr/bin/env python3
"""
时间轴工具 - 内容结构化组织类
基于内容自动生成时间轴，将复杂的时间序列信息结构化展示
"""

import json
import os
from typing import Any, Optional

from loguru import logger

from .base_tool import EnhancementTool, ToolCategory


class TimelineTool(EnhancementTool):
    """时间轴工具 - 内容结构化组织类"""

    tool_name = "timeline_generation"
    tool_description = "基于内容自动生成时间轴，将复杂的时间序列信息结构化展示，帮助观众理解发展脉络和关键节点"
    tool_category = ToolCategory.CONTENT_ORGANIZATION

    def __init__(self, config=None):
        self.config = config

    def get_tool_info(self) -> str:
        """获取工具信息 - 供智能选择器决策使用"""
        return """时间轴工具 - 将复杂的时间序列信息结构化展示

**核心作用**：
时间轴是一种线性、可视化的信息组织工具，通过按时间顺序排列关键事件和里程碑，清晰地展示事物的发展脉络、演变过程和重要节点，帮助观众建立对时间序列的直观理解。

**适合的内容类型**：
- 历史发展介绍（如公司发展史、技术演进历程）
- 项目规划与回顾（如项目里程碑、版本发布记录）
- 事件序列记述（如科学发现过程、历史事件回顾）
- 个人或组织成长历程（如个人传记、团队发展）

**典型应用场景**：
✅ 介绍Python语言从诞生到成为主流的发展历程
✅ 展示某开源项目从启动到发布各版本的关键节点
✅ 回顾人工智能技术在过去几十年的重大突破
✅ 梳理产品从1.0到5.0版本的功能迭代史

**不适合的内容类型**：
- 概念分类体系（如知识图谱、功能模块）
- 空间结构或组织架构（如系统架构图、组织图）
- 逻辑关系分析（如因果链、决策树）
- 纯理论性、非时序性内容（如数学原理、哲学思想）
"""

    def can_apply(self, content: str, purpose: str, context: dict[str, Any]) -> bool:
        return True

    def apply_tool(
        self, content: str, output_dir: str, context: dict[str, Any], focus: str = None
    ) -> Optional[dict[str, Any]]:
        """执行时间轴生成工具"""
        logger.info("📅 应用时间轴工具，准备分析内容")

        try:
            # 生成输出路径
            timeline_filename = "timeline_data.json"
            timeline_path = os.path.join(output_dir, timeline_filename)

            if os.path.exists(timeline_path):
                logger.info(f"时间轴数据已存在: {timeline_path}")
                with open(timeline_path, encoding="utf-8") as f:
                    timeline_data = json.load(f)

                return timeline_data

            # 导入timeline prompt相关功能
            from prompts.timeline_prompt import generate_timeline

            # 调用timeline分析
            timeline_result = generate_timeline(content, focus)

            if not timeline_result or not timeline_result.get("suitable", False):
                return None

            os.makedirs(output_dir, exist_ok=True)

            # 保存时间轴数据
            with open(timeline_path, "w", encoding="utf-8") as f:
                json.dump(timeline_result, f, ensure_ascii=False, indent=4)

            logger.info(f"📅 时间轴数据生成完成: {timeline_path}")

            return timeline_result

        except Exception as e:
            logger.error(f"时间轴生成失败: {e}")
            return None

    def generate_intro(self, tool_result: dict[str, Any]) -> str:
        """生成时间轴的介绍内容"""
        timeline_data = tool_result.get("timeline_data", {})
        events_count = tool_result.get("events_count", 0)

        if not timeline_data or events_count == 0:
            return ""

        intro = "## 📅 时间轴概览\n\n"
        intro += f"基于内容自动生成的时间轴，包含 {events_count} 个关键事件节点。\n\n"

        # 添加intro_narration（如果有）
        if timeline_data.get("intro_narration"):
            intro += f"**概述**: {timeline_data['intro_narration']}\n\n"

        # 简要列出事件
        events = timeline_data.get("events", [])
        if events:
            intro += "**关键节点**:\n"
            for i, event in enumerate(events[:5]):  # 最多显示前5个事件
                year = event.get("year", "未知时间")
                title = event.get("title", "未知事件")
                emoji = event.get("emoji", "📍")
                intro += f"- {emoji} {year}: {title}\n"

            if len(events) > 5:
                intro += f"- ... 以及其他 {len(events) - 5} 个事件\n"
            intro += "\n"

        return intro


if __name__ == "__main__":
    from utils.common import Config

    config = Config()
    tool = TimelineTool(config.config)
    tool.apply_tool(
        open("output/2401.03568/2401.03568.md").read(),
        "./test_output",
        context={},
        focus="生成Agent AI相关技术的发展历程",
    )
