#!/usr/bin/env python3
"""
深度洞察问答工具 - 深度理解增强类
基于内容生成深度洞察问题并提供详细回答，帮助观众获得超越表面内容的深层理解和启发
"""

import os
from typing import Any, Optional

from loguru import logger

from utils.create_llm_model import create_model

from .base_tool import EnhancementTool, ToolCategory


class DeepInsightQATool(EnhancementTool):
    """深度洞察问答工具 - 深度理解增强类"""

    tool_name = "deep_insight_qa"
    tool_description = "基于内容生成深度洞察问题并提供详细回答，帮助观众获得超越表面内容的深层理解和启发"
    tool_category = ToolCategory.DEEP_INSIGHTS

    def __init__(self, config=None):
        self.config = config
        self.agent = None
        if config:
            self._init_model()

    def _init_model(self):
        """初始化Camel模型"""
        try:
            from camel.agents import ChatAgent
            from camel.messages import BaseMessage

            self.model = create_model()

            # 定制化系统提示词
            system_prompt = """你是专业的深度洞察分析专家，负责从内容中挖掘深层价值和启发。
你的任务是：
1. 分析内容是否适合生成深度洞察问答（需要有理论深度和思辨价值）
2. 提出能够揭示本质规律、挑战常规思维的犀利问题
3. 基于材料内容提供有理有据的深度回答
4. 确保问题和答案都能为受众带来真正的收获和启发

请严格按照要求的JSON格式输出，确保数据的准确性和完整性。"""

            self.agent = ChatAgent(
                system_message=BaseMessage.make_assistant_message("", system_prompt),
                model=self.model,
            )
            logger.info(f"{self.tool_name}模型初始化成功")
        except Exception as e:
            logger.warning(f"{self.tool_name}模型初始化失败: {e}")

    def get_tool_info(self) -> str:
        """获取工具信息 - 供智能选择器决策使用"""
        return """深度洞察问答工具 - 挖掘内容的深层价值和启发

**核心作用**：
深度洞察问答通过提出犀利的深度问题，帮助观众理解内容背后的本质规律、核心机制和深层价值，获得超越表面信息的洞察和启发。

**适合的内容类型**：
- 学术理论和研究成果（如机器学习算法原理、科学发现）
- 深度分析和概念解释（如商业模式分析、哲学思辨）
- 复杂系统和机制说明（如经济原理、社会现象）
- 前沿技术和创新思维（如AI发展、技术趋势）

**典型应用场景**：
✅ 解析深度学习算法的核心思想和局限性
✅ 探讨商业模式背后的成功要素和风险
✅ 分析科技发展对社会的深层影响
✅ 挖掘研究成果的实际应用价值

**不适合的内容类型**：
- 纯技术实现和操作步骤（如代码教程、安装指南）
- 简单产品介绍和功能说明（如工具使用、产品特性）
- 新闻资讯和事件报道（如公司动态、市场消息）
- 基础知识和概念定义（如名词解释、入门介绍）

**典型不适用场景**：
❌ 讲解编程语言的语法规则
❌ 介绍软件工具的使用方法
❌ 报道行业新闻和市场动态
❌ 解释基础概念和术语定义

**判断标准**：
内容是否包含深层理论、复杂机制或需要思辨分析？是否能够提出挑战性问题并带来新的思考角度？如果答案是肯定的，则适合使用深度洞察问答工具。

**输出形式**：生成深度问题和详细回答(一般不超过3个)，帮助观众获得深层理解和启发。"""

    def can_apply(self, content: str, purpose: str, context: dict[str, Any]) -> bool:
        """简化的可用性检查 - 只检查基本配置条件"""
        # 基本检查：配置存在、内容长度、配置开关
        basic_check = (
            self.config
            and len(content) >= 600  # 深度问答需要足够的内容来挖掘洞察
            and self.config.get("material", {}).get("material_enhance", {}).get(self.tool_name, True)
        )

        # 如果需要LLM支持，检查agent是否初始化成功
        if hasattr(self, "agent") and self.agent is None:
            return False

        return basic_check

    def apply_tool(self, content: str, output_dir: str, context: dict[str, Any], focus: str = None) -> Optional[str]:
        """应用工具，执行具体的扩充操作"""
        logger.info(f"🤔 应用{self.tool_name}工具，准备处理内容")

        try:
            os.makedirs(output_dir, exist_ok=True)

            # 生成输出路径
            output_filename = f"{self.tool_name}_output.json"
            output_path = os.path.join(output_dir, output_filename)

            # 检查是否已存在文件
            if os.path.exists(output_path):
                logger.info(f"工具输出已存在: {output_path}")
                with open(output_path, encoding="utf-8") as f:
                    existing_data = f.read()
                return existing_data

            # 执行具体的工具逻辑，使用focus参数而不是context
            result_content = self._process_with_camel(content, focus)

            if not result_content:
                return None

            # 保存结果数据（保持原有格式以便后续处理）
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(result_content)

            logger.info(f"🤔 {self.tool_name}工具处理完成: {output_path}")
            # 返回深度问答内容字符串，符合新的简化要求
            return result_content

        except Exception as e:
            logger.error(f"{self.tool_name}工具处理失败: {e}")
            return None

    def _process_with_camel(self, content: str, focus: str = None) -> Optional[str]:
        """使用Camel进行深度问答处理的逻辑"""
        # 构建结构化提示词，重点利用focus信息
        focus_instruction = ""
        if focus:
            focus_instruction = f"""
**重点关注方向**：{focus}
请在生成深度问答时特别关注这个方向，确保相关内容在问题设计和回答中得到充分体现。"""

        prompt = f"""你是专业的深度洞察分析专家，需要基于以下内容生成深度洞察问答。

**待分析内容**：
<content>
{content}
</content>

<focus_instruction>
{focus_instruction}
</focus_instruction>

**任务要求**：
1. 根据给定的重点关注方向，从输入内容中挖掘深层价值，生成深度问答。
2. **问题设计**：
   - 能够揭示核心机制、关键价值或深层影响
   - 挑战常规思维，引发新的思考角度
   - 避免类似的问题，每个问题要独立且有价值
   - 问题简洁有力，8个字以内
   - 问题数量不超过3个

3. **回答要求**：
   - 回答要简洁有力，避免冗长描述，但要符合正常的表达习惯，不能过于缩减影响理解
   - 可以使用类比、举例让抽象概念变得易懂，类比需要和要说明的对象完全同构，尽量用中国人日常生活中熟悉的现象或者事物来类比，便于观众理解
   - 重点突出核心洞察，语言生动易懂
   - 每个回答控制在30个字以内

**输出格式**：
请严格按照以下JSON格式输出：

{{
    "qa_pairs": [
        {{
            "question": "第一个深度问题（8字以内）",
            "answer": "简洁易懂的回答，可以使用类比或举例（严格控制在30字以内）"
        }},
        {{
            "question": "第二个深度问题（8字以内）",
            "answer": "简洁易懂的回答，可以使用类比或举例（严格控制在30字以内）"
        }},
        {{
            "question": "第三个深度问题（8字以内）",
            "answer": "简洁易懂的回答，可以使用类比或举例（严格控制在30字以内）"
        }}
        ...
    ]
}}

请直接输出结果，不要包含额外说明。"""

        logger.info(f"生成深度问答, prompt characters: {len(prompt)}, words: {len(prompt.split())}")

        try:
            # 调用Camel agent进行处理
            response = self.agent.step(prompt)
            response_content = response.msgs[0].content.strip()

            # 检查是否不适合生成深度问答
            if response_content.startswith("该内容不适合生成深度问答"):
                logger.info(f"内容不适合生成深度问答: {response_content}")
                return response_content

            # 返回生成的深度问答内容
            return response_content

        except Exception as e:
            logger.error(f"Camel数据提取失败: {e}")
            return None

    def generate_intro(self, tool_result: dict[str, Any]) -> str:
        """生成工具输出的介绍文本"""
        # 兼容新的返回格式：tool_result现在直接是字符串内容
        if isinstance(tool_result, str):
            result_content = tool_result
        else:
            # 兼容旧格式
            result_content = tool_result.get("data", "") if tool_result else ""

        if not result_content:
            return ""

        # 检查是否不适合生成深度问答
        if result_content.startswith("该内容不适合生成深度问答"):
            return f"## 🤔 深度洞察问答\n\n❌ {result_content}\n\n"

        # 生成深度问答介绍
        intro = f"## 🤔 {self.tool_description}\n\n"
        intro += "**生成的深度洞察问答**：\n\n"
        intro += result_content
        intro += "\n\n*💡 这些问题经过精心设计，旨在引发深度思考，帮助您从不同角度理解内容的深层价值*\n\n"

        return intro


if __name__ == "__main__":
    from utils.common import Config

    config = Config()
    tool = DeepInsightQATool(config.config)
    tool.apply_tool(
        open("output/2506.06395/2506.06395.md").read(),
        "output/2506.06395",
        context={},
        focus="基于论文内容，生成关于RLSC的三个深度洞察问题，例如：RLSC如何处理模型“幻觉”导致的错误自信？该方法在面对非数学推理的开放性任务时效果如何？RLSC的“模式锐化”机制是否可能导致模型过度自信从而牺牲探索性？并提供详细回答",
    )
