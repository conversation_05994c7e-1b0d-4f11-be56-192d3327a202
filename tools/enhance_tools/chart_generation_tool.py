#!/usr/bin/env python3
"""
图表生成工具 - 多模态呈现类
基于内容自动生成图表配置，支持柱状图和折线图，用于animate_chart函数
"""

import json
import os
from typing import Any, Optional

from loguru import logger

from utils.create_llm_model import create_model

from .base_tool import EnhancementTool, ToolCategory


class ChartGenerationTool(EnhancementTool):
    """图表生成工具 - 多模态呈现类"""

    tool_name = "chart_generation"
    tool_description = "基于内容自动生成图表配置，支持柱状图、折线图或雷达图，用于animate_chart函数"
    tool_category = ToolCategory.MULTIMODAL_PRESENTATION

    def __init__(self, config=None):
        self.config = config
        self.agent = None
        if config:
            self._init_model()

    def _init_model(self):
        """初始化Camel模型 - 用于智能数据提取和图表生成"""
        try:
            from camel.agents import ChatAgent
            from camel.messages import BaseMessage

            self.model = create_model()

            # 定制化系统提示词
            system_prompt = """你是专业的数据可视化专家，负责从内容中提取数值数据并生成图表配置。"""

            self.agent = ChatAgent(
                system_message=BaseMessage.make_assistant_message("", system_prompt),
                model=self.model,
            )
            logger.info(f"{self.tool_name}模型初始化成功")
        except Exception as e:
            logger.warning(f"{self.tool_name}模型初始化失败: {e}")

    def get_tool_info(self) -> str:
        """获取工具信息 - 供智能选择器决策使用"""
        return """图表生成工具 - 将数据转化为可视化图表

**核心作用**：
图表生成工具能够将文本内容中蕴含的数值数据、趋势、对比关系等，转化为结构化的图表配置（如柱状图、折线图、雷达图等），用于后续的动画生成，使数据更直观易懂。

**适合的内容类型**：
- 包含明确数值和分类的数据集（如销售数据、用户统计、调查结果）
- 需要对比不同项目或时间点的数据（如产品性能对比、月度增长率）
- 展示数据随时间或其他变量变化的趋势（如股价波动、温度变化）
- 多维度指标评估（如能力雷达图、多指标评分）

**典型应用场景**：
✅ 分析报告中关于市场份额占比的数据，生成饼图或柱状图
✅ 产品介绍中对比不同型号产品在多个性能指标上的差异，生成雷达图或分组柱状图
✅ 经济分析文章中展示GDP历年增长数据，生成折线图
✅ 用户调研报告中关于不同年龄段用户偏好的数据，生成堆叠柱状图

**不适合的内容类型**：
- 纯叙述性、无量化数据的内容（如故事、评论）
- 概念解释或理论阐述（如哲学思想、科学原理）
- 流程步骤或操作指南（如安装教程、食谱）
- 复杂的、非结构化的文本（如法律条文、长篇散文）
"""

    def can_apply(self, content: str, purpose: str, context: dict[str, Any]) -> bool:
        return True

    def apply_tool(
        self, content: str, output_dir: str, context: dict[str, Any], focus: str = None
    ) -> Optional[dict[str, Any]]:
        """应用工具，执行具体的扩充操作"""
        logger.info(f"🔧 应用{self.tool_name}工具，准备处理内容")

        try:
            os.makedirs(output_dir, exist_ok=True)

            # 生成输出路径
            output_filename = f"{self.tool_name}_output.json"
            output_path = os.path.join(output_dir, output_filename)

            # 检查是否已存在文件
            if os.path.exists(output_path):
                logger.info(f"工具输出已存在: {output_path}")
                with open(output_path, encoding="utf-8") as f:
                    existing_data = f.read()
                return existing_data

            # 执行具体的工具逻辑
            result_data = self._process_content(content, focus)

            if not result_data:
                return None

            # 保存结果数据
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(result_data)

            logger.info(f"🔧 {self.tool_name}工具处理完成: {output_path}")

            return result_data

        except Exception as e:
            logger.error(f"{self.tool_name}工具处理失败: {e}")
            return None

    def _process_content(self, content: str, focus: str = None) -> Optional[str]:
        """使用Camel进行智能图表配置生成"""
        purpose = focus

        # 构建结构化提示词
        prompt = f"""基于以下内容生成图表配置：

**内容材料**：
{content}

**处理目标**：{purpose}

**输出要求**：
请分析内容中的数值数据，按照目标要求生成合适的图表配置。支持的图表类型：
- bar: 柱状图，适合对比不同类别的数据
- line: 折线图，适合展示趋势变化
- radar: 雷达图，适合展示多维度指标的对比

每个图表配置应包含：
1. chart_type: "bar" 或 "line" 或 "radar"
2. title: 图表标题
3. data: 数据数组，格式为 [{{"类别1": 值1, "类别2": 值2, ...}}]
4. dataset_names: 数据集名称数组
5. animation_style: "grow", "fadeIn", "draw", 或 "update", "dynamic"，bar优先用 dynamic
6. narration: 图表说明文本

请严格按照以下JSON格式输出：

```json
{{
    "chart_type": "bar",
    "title": "图表标题",
    "data": [{{"指标A": 100, "指标B": 150, "指标C": 80}}, {{"指标A": 120, "指标B": 160, "指标C": 90}}],
    "dataset_names": ["数据集A", "数据集B"],
    "animation_style": "dynamic",
    "narration": "这个图表展示了..."
}}
```"""

        try:
            # 调用Camel agent进行处理
            logger.info(f"图表工具开始处理内容: character length: {len(prompt)}, words: {len(prompt.split())}")
            response = self.agent.step(prompt)
            response_content = response.msgs[0].content

            return response_content

        except Exception as e:
            logger.error(f"Camel图表配置生成失败: {e}")
            return None

    def generate_intro(self, tool_result: dict[str, Any]) -> str:
        """生成工具输出的介绍文本"""
        data = tool_result.get("data", {})

        if not data:
            return ""

        charts = data.get("charts", [])
        chart_count = data.get("metadata", {}).get("chart_count", len(charts))

        intro = f"## 📊 {self.tool_description}\n\n"
        intro += f"**生成图表数量**: {chart_count}\n\n"

        if charts:
            intro += "### 生成的图表配置\n\n"
            for i, chart in enumerate(charts, 1):
                chart_type = chart.get("chart_type", "unknown")
                title = chart.get("title", "未命名图表")
                narration = chart.get("narration", "")
                data_info = chart.get("data", [])

                intro += f"#### 图表 {i}: {title}\n\n"
                intro += f"**类型**: {chart_type} ({'柱状图' if chart_type == 'bar' else '折线图' if chart_type == 'line' else '未知类型'})\n\n"

                if data_info and isinstance(data_info, list) and len(data_info) > 0:
                    first_dataset = data_info[0]
                    if isinstance(first_dataset, dict):
                        intro += f"**数据项数量**: {len(first_dataset)}\n\n"
                        intro += f"**数据项**: {', '.join(first_dataset.keys())}\n\n"

                if narration:
                    intro += f"**说明**: {narration}\n\n"

                intro += "**动画配置**:\n"
                intro += "```json\n"
                intro += "{\n"
                intro += '  "type": "animate_chart",\n'
                intro += '  "params": {\n'
                intro += f'    "chart_type": "{chart_type}",\n'
                intro += f'    "title": "{title}",\n'
                intro += f'    "data": {json.dumps(data_info, ensure_ascii=False)},\n'
                intro += f'    "dataset_names": {json.dumps(chart.get("dataset_names", []), ensure_ascii=False)},\n'
                intro += f'    "animation_style": "{chart.get("animation_style", "grow")}",\n'
                intro += f'    "narration": "{narration}"\n'
                intro += "  }\n"
                intro += "}\n"
                intro += "```\n\n"
                intro += "---\n\n"

        intro += "**使用说明**: 上述配置可直接用于animate_chart函数，生成交互式图表动画。\n\n"

        return intro


if __name__ == "__main__":
    from utils.common import Config

    config = Config()
    tool = ChartGenerationTool(config.config)
    content = open("/Users/<USER>/Documents/agentic-feynman/output/2504.07491/2504.07491.md").read()
    tool.apply_tool(
        content,
        "./test_output",
        {},
        "生成针对Performance of Kimi-VL against proprietary and open-source efficient VLMs; performance of GPT-4o 的对比图表",
    )
