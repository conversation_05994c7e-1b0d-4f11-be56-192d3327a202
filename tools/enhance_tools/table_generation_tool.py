#!/usr/bin/env python3
"""
表格生成工具 - 内容结构化组织类
基于内容自动提取结构化数据并生成表格，将复杂信息以清晰的表格形式展示
"""

import json
import os
import re
from typing import Any, Optional

from loguru import logger

from utils.create_llm_model import create_model

from .base_tool import EnhancementTool, ToolCategory


class TableGenerationTool(EnhancementTool):
    """表格生成工具 - 内容结构化组织类"""

    tool_name = "table_generation"
    tool_description = "从包含具体数据、参数、规格的内容中提取量化信息并生成精简表格，专门处理可对比的数值、技术参数、特性标识等结构化数据"
    tool_category = ToolCategory.CONTENT_ORGANIZATION

    def __init__(self, config=None):
        self.config = config
        self.table_agent = None
        if config:
            self._init_model()

    def _init_model(self):
        """初始化Camel模型"""
        try:
            from camel.agents import ChatAgent
            from camel.messages import BaseMessage

            self.model = create_model()

            # 定制化系统提示词 - 增加精简要求
            system_prompt = """你是专业的数据结构化专家，专门从内容中提取结构化信息并生成精简表格。

**核心要求**：
1. 输出内容必须精简，避免冗余信息
2. 表格数据严格限制字符数量
3. 只保留核心对比要素
4. 使用中文字段名
5. 确保数据准确性

**表格设计原则**：
1. 表格只包含数据、参数、特性，不要描述性文字
2. 设计1-2个数据对比表格，每个表格3-6行
3. 每个单元格严格限制在6个字符以内
4. 表头用简洁标识符
5. 表格内容只能是：数字+单位、特性标识、状态符号、简短代号

**精简输出要求**：
- 表格标题：≤10字符
- 表格说明：≤20字符
- 关键发现：≤15字符/条
- 数据主题：≤8字符
- 对比要点：≤12字符

请严格按照要求的JSON格式输出，确保内容精简且数据准确。"""

            self.table_agent = ChatAgent(
                system_message=BaseMessage.make_assistant_message("", system_prompt),
                model=self.model,
            )
            logger.info(f"{self.tool_name}模型初始化成功")
        except Exception as e:
            logger.warning(f"{self.tool_name}模型初始化失败: {e}")

    def get_tool_info(self) -> str:
        """获取工具信息 - 供智能选择器决策使用"""
        return """表格生成工具 - 从内容中提取结构化数据并生成精简对比表格

**核心作用**：
表格生成工具是一种数据可视化工具，专门从包含具体数值、参数、规格的内容中提取量化信息，并生成清晰的对比表格，帮助观众快速理解和比较不同选项的关键指标和特性。

**适合的内容类型**：
- 产品规格和参数对比（如硬件配置、软件功能、设备性能）
- 技术指标和性能数据（如算法复杂度、测试结果、基准数据）
- 价格和配置信息（如套餐对比、版本差异、成本分析）
- 功能特性清单（如API接口、工具特性、服务对比）
- 评测和测试数据（如性能测试、用户评分、质量指标）

**典型应用场景**：
✅ 介绍多款手机的配置参数 → 生成规格对比表格
✅ 展示不同算法的性能数据 → 生成性能指标表格
✅ 对比多个云服务的价格套餐 → 生成价格配置表格
✅ 分析开源工具的功能特性 → 生成功能对比表格
✅ 展示测试结果和评测数据 → 生成数据汇总表格

**不适合的内容类型**：
- 纯文字叙述和理论阐述（如概念解释、原理说明、历史介绍）
- 缺乏具体数值的定性描述（如使用体验、主观评价、情感表达）
- 单一对象介绍无对比要素（如单个产品介绍、个人经历分享）
- 流程步骤和操作指南（如安装教程、使用手册、配置指南）
- 创意内容和艺术作品（如故事、诗歌、设计作品）

**典型不适用场景**：
❌ 解释机器学习的基本概念和工作原理
❌ 分享个人使用某个工具的心得体验
❌ 介绍单个产品的功能和使用方法
❌ 提供软件安装和配置的详细步骤
❌ 讲述公司发展历程和重要事件

**判断标准**：
内容是否包含具体的数值数据、技术参数或规格信息？是否存在多个可对比的项目或选项？这些数据是否具有结构化特征，适合以表格形式展示？如果答案是肯定的，则适合使用表格生成工具。

**关键识别要点**：
- 重点识别数值数据：价格、尺寸、重量、速度、容量、评分等
- 寻找技术参数：配置规格、性能指标、功能特性、版本差异等
- 确认对比要素：多个产品、不同方案、各种选项的比较
- 评估数据密度：内容中量化信息的丰富程度和结构化程度

**输出形式**：生成1-2个精简的对比表格，每个表格包含3-6行核心数据，严格控制字符数量，只保留最重要的量化信息和对比要素。"""

    def can_apply(self, content: str, purpose: str, context: dict[str, Any]) -> bool:
        """简化的可用性检查 - 只检查基本配置条件"""
        # 基本检查：配置存在、内容长度、配置开关
        basic_check = (
            self.config
            and len(content) >= 600  # 表格生成需要足够的内容来提取结构化数据
            and self.config.get("material", {}).get("material_enhance", {}).get(self.tool_name, True)
        )

        return basic_check

    def _parse_agent_response(self, response_content: str) -> Optional[dict]:
        """解析agent响应，支持多种格式"""
        try:
            # 尝试提取JSON块
            json_match = re.search(r"```json\s*(\{.*?\})\s*```", response_content, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(1))

            # 尝试提取花括号内容
            brace_match = re.search(r"\{[^{}]*\}", response_content)
            if brace_match:
                return json.loads(brace_match.group())

            # 尝试直接解析
            return json.loads(response_content.strip())

        except Exception as e:
            logger.error(f"解析agent响应失败: {e}")
            return None

    def apply_tool(self, content: str, output_dir: str, context: dict[str, Any], focus: str = None) -> Optional[str]:
        """应用工具，执行具体的扩充操作"""
        logger.info(f"🔧 应用{self.tool_name}工具，准备处理内容")

        try:
            os.makedirs(output_dir, exist_ok=True)

            # 生成输出路径
            output_filename = f"{self.tool_name}_output.json"
            output_path = os.path.join(output_dir, output_filename)

            # 检查是否已存在文件
            if os.path.exists(output_path):
                logger.info(f"工具输出已存在: {output_path}")
                with open(output_path, encoding="utf-8") as f:
                    existing_data = f.read()
                return existing_data

            # 执行具体的工具逻辑，传递focus参数
            result_data = self._process_with_agent(content, context, focus)

            if not result_data:
                return None

            # 将结果转换为JSON字符串
            result_json = json.dumps(result_data, ensure_ascii=False, indent=2)

            # 保存结果数据
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(result_json)

            logger.info(f"🔧 {self.tool_name}工具处理完成: {output_path}")

            # 直接返回JSON字符串
            return result_json

        except Exception as e:
            logger.error(f"{self.tool_name}工具处理失败: {e}")
            return None

    def _process_with_agent(self, content: str, context: dict[str, Any], focus: str = None) -> Optional[dict[str, Any]]:
        """使用agent进行数据提取的处理逻辑 - 精简版"""
        purpose = context.get("purpose", "数据结构化展示")

        # 构建focus指令
        focus_instruction = ""
        if focus:
            focus_instruction = f"""
**重点关注方向**：{focus}
请在生成表格时特别关注这个方向，确保相关数据在表格中得到充分体现和突出展示。"""

        # 构建精简化提示词
        prompt = f"""**内容材料**：
{content[:6000]}{'...(内容截断)' if len(content) > 6000 else ''}

**处理目标**：{purpose}

{focus_instruction}

**精简要求**：
1. 表格标题≤10字符
2. 表格说明≤20字符
3. 关键发现≤15字符/条
4. 数据主题≤8字符
5. 对比要点≤12字符

**表格设计**：
- 设计1-2个精简表格，每个3-6行
- 每个单元格≤6字符
- 只包含核心数据：数字+单位、特性标识、状态符号
- 严禁描述性文字和主观评价

**输出格式**：
请严格按照以下JSON格式输出：


{{
    "数据主题": "主题名(≤8字符)",
    "对比要点": "要点(≤12字符)",
    "表格数据": [
        {{
            "标题": "表名(≤10字符)",
            "说明": "简述(≤20字符)",
            "表头": ["字段1", "字段2", "字段3"],
            "数据": [
                ["项目A", "数据1", "特性1"],
                ["项目B", "数据2", "特性2"]
            ],
            "关键发现": ["发现1(≤15字符)", "发现2(≤15字符)"]
        }}
    ]
}}


请严格按照精简要求输出，确保所有字段都符合字符限制。"""

        try:
            # 调用agent进行处理
            response = self.table_agent.step(prompt)
            response_content = response.msgs[0].content

            # 提取JSON
            json_match = re.search(r"```json\s*(\{.*?\})\s*```", response_content, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                json_str = response_content.strip()

            result_data = json.loads(json_str)
            return result_data

        except Exception as e:
            logger.error(f"Agent数据提取失败: {e}")
            return None

    def generate_intro(self, tool_result: dict[str, Any]) -> str:
        """生成工具输出的精简介绍"""
        # 兼容新的返回格式：tool_result现在可能是JSON字符串
        if isinstance(tool_result, str):
            try:
                data = json.loads(tool_result)
            except json.JSONDecodeError:
                return ""
        elif isinstance(tool_result, dict) and "data" in tool_result:
            # 兼容旧格式
            data = tool_result["data"]
        else:
            data = tool_result

        if not data:
            return ""

        # 精简介绍，只显示核心信息
        intro = f"## 📊 {self.tool_description}\n\n"

        # 显示数据主题和对比要点
        if "数据主题" in data:
            intro += f"**主题**: {data['数据主题']}\n"
        if "对比要点" in data:
            intro += f"**要点**: {data['对比要点']}\n\n"

        # 显示表格数量和概览
        tables = data.get("表格数据", [])
        table_count = len(tables)
        intro += f"**生成表格**: {table_count}个\n"

        # 显示表格概览
        if tables:
            intro += "\n### 表格概览\n"
            for i, table in enumerate(tables[:2], 1):  # 最多显示前2个表格
                title = table.get("标题", f"表格{i}")
                description = table.get("说明", "")
                intro += f"- **{title}**: {description}\n"

        intro += "\n*💡 此表格将帮助观众快速理解和比较关键数据指标*\n\n"

        return intro


if __name__ == "__main__":
    from utils.common import Config

    config = Config()
    tool = TableGenerationTool(config.config)

    # 测试内容
    test_content = """
    智能手机性能对比分析

    iPhone 15 Pro Max:
    - 处理器: A17 Pro芯片
    - 内存: 8GB RAM
    - 存储: 256GB/512GB/1TB
    - 屏幕: 6.7英寸 OLED
    - 电池: 4441mAh
    - 价格: $1199起
    - 重量: 221g
    - 摄像头: 48MP主摄 + 12MP超广角 + 12MP长焦

    Samsung Galaxy S24 Ultra:
    - 处理器: Snapdragon 8 Gen 3
    - 内存: 12GB RAM
    - 存储: 256GB/512GB/1TB
    - 屏幕: 6.8英寸 Dynamic AMOLED
    - 电池: 5000mAh
    - 价格: $1299起
    - 重量: 232g
    - 摄像头: 200MP主摄 + 12MP超广角 + 10MP长焦 + 10MP潜望镜

    Google Pixel 8 Pro:
    - 处理器: Tensor G3
    - 内存: 12GB RAM
    - 存储: 128GB/256GB/512GB
    - 屏幕: 6.7英寸 LTPO OLED
    - 电池: 5050mAh
    - 价格: $999起
    - 重量: 213g
    - 摄像头: 50MP主摄 + 48MP超广角 + 48MP长焦
    """

    # 测试focus参数
    test_focus = "重点关注手机的性能参数对比，特别是处理器性能、内存配置和摄像头规格的详细对比"

    result = tool.apply_tool(
        content=test_content, output_dir="./test_output", context={"purpose": "智能手机规格对比分析"}, focus=test_focus
    )

    if result:
        print("表格生成完成！")

        # 解析JSON字符串
        try:
            result_data = json.loads(result)
            print(f"数据主题: {result_data.get('数据主题', 'N/A')}")
            print(f"对比要点: {result_data.get('对比要点', 'N/A')}")
            print(f"表格数量: {len(result_data.get('表格数据', []))}")

            # 显示表格数据
            for i, table in enumerate(result_data.get("表格数据", []), 1):
                print(f"\n表格{i}: {table.get('标题', 'N/A')}")
                print(f"说明: {table.get('说明', 'N/A')}")
                print(f"表头: {table.get('表头', [])}")
                print(f"数据行数: {len(table.get('数据', []))}")

            # 生成介绍文本
            intro = tool.generate_intro(result)
            print("\n生成的介绍文本:")
            print(intro)

            # 输出完整的JSON结果
            print("\n完整JSON输出:")
            print(result)
        except json.JSONDecodeError:
            print("JSON解析失败")
    else:
        print("表格生成失败！")
