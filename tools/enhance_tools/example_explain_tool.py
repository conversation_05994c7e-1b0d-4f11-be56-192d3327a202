#!/usr/bin/env python3
"""
举例说明工具 - 智能交互
主要用来介绍概念、知识点，通过给出详细的例子来讲解细节
不适合项目类、技术报告、经验类的素材
"""

import os
from typing import Any, Optional

from loguru import logger

from utils.create_llm_model import create_model

from .base_tool import EnhancementTool, ToolCategory


class ExampleExplainTool(EnhancementTool):
    """举例说明工具 - 智能交互"""

    # 必需的类属性
    tool_name = "example_explain"
    tool_description = "概念举例说明工具，通过生成详细、具体的例子来讲解抽象概念和知识点"
    tool_category = ToolCategory.SMART_INTERACTION

    # 适用场景描述 - 供LLM智能选择使用
    suitable_content_types = ["概念解释", "理论知识", "原理说明", "学术概念", "抽象理论", "知识点讲解", "科学原理"]
    suitable_purposes = ["教学", "学习", "概念理解", "知识传播", "理论学习", "教育内容", "培训材料"]
    required_conditions = ["包含抽象概念", "需要具体化解释", "理论性内容"]

    # 不适用场景描述 - 帮助LLM做负面判断
    unsuitable_content_types = ["项目报告", "技术文档", "经验总结", "操作手册", "代码实现", "工具使用指南", "实战案例"]
    unsuitable_purposes = ["项目管理", "技术实施", "经验分享", "操作指导", "工具演示"]
    blocking_conditions = ["纯实践内容", "具体项目", "技术实现细节", "经验类材料", "操作流程"]

    def __init__(self, config=None):
        self.config = config
        self.agent = None
        if config:
            self._init_model()

    def _init_model(self):
        """初始化Camel模型"""
        try:
            from camel.agents import ChatAgent
            from camel.messages import BaseMessage

            self.model = create_model()

            # 参考example_explain_agent.py的专业提示词
            system_prompt = """你是一位顶级的例子生成专家，专门为复杂概念创造高质量、细节丰富、易懂的具体例子。

## 核心能力
1. **概念提取与分析**：准确识别核心概念、原理，分析其关键要素和步骤
2. **例子设计**：创造具体、量化、准确的例子来解释抽象概念
3. **原理揭示**：深入揭示概念的本质原理和机制

## 质量标准
- **数据量化**：所有数据必须具体、准确，有明确的数值
- **步骤详细**：每个步骤都要清晰说明，不遗漏关键环节
- **逻辑清晰**：整个过程逻辑连贯，易于理解
- **原理揭示**：深入揭示概念的本质原理和机制
- **易于记忆**：例子要生动具体，便于理解和记忆

请严格按照JSON格式输出结构化的例子内容。"""

            self.agent = ChatAgent(
                system_message=BaseMessage.make_assistant_message("", system_prompt),
                model=self.model,
            )
            logger.info(f"{self.tool_name}模型初始化成功")
        except Exception as e:
            logger.warning(f"{self.tool_name}模型初始化失败: {e}")

    def get_tool_info(self) -> str:
        """获取工具信息 - 供智能选择器决策使用"""
        return """概念举例说明工具 - 通过详细具体的例子来讲解抽象概念

**核心作用**：
通过生成详细、具体的例子来讲解抽象概念和知识点，将复杂理论转化为易于理解的具体案例，帮助深入理解概念的本质原理和应用机制。

**适合的内容类型**：
- 抽象理论和概念解释（如数学定理、物理原理）
- 学术概念和科学原理（如算法原理、化学反应）
- 复杂知识点和理论框架（如经济学理论、管理概念）
- 需要具体化理解的原理说明（如系统设计、工作机制）
- 教学和培训材料中的核心概念（如教育理论、技术原理）

**典型应用场景**：
✅ 为数学公式创建具体的计算例子
✅ 为抽象算法设计详细的执行过程
✅ 为理论概念提供生动的现实案例
✅ 为复杂原理设计步骤化的演示
✅ 为学术概念创建量化的具体示例

**不适合的内容类型**：
- 项目报告和技术文档（如开发文档、API说明）
- 经验总结和操作手册（如使用指南、配置说明）
- 实战案例和工具演示（如项目实施、工具使用）
- 纯实践性内容（如代码实现、操作流程）

**典型不适用场景**：
❌ 解释项目管理流程和实施方案
❌ 说明工具使用方法和配置步骤
❌ 总结实战经验和项目案例
❌ 展示代码实现和技术细节

**输出特点**：
- **数据量化**：所有数据必须具体、准确，有明确的数值
- **步骤详细**：每个步骤都要清晰说明，不遗漏关键环节
- **逻辑清晰**：整个过程逻辑连贯，易于理解
- **原理揭示**：深入揭示概念的本质原理和机制
- **易于记忆**：例子要生动具体，便于理解和记忆

**判断标准**：
内容是否包含需要通过具体例子来理解的抽象概念？是否适合通过量化、步骤化的方式来解释原理？

**输出形式**：生成包含核心概念识别、具体例子、步骤解释、原理揭示的完整说明。"""

    def can_apply(self, content: str, purpose: str, context: dict[str, Any]) -> bool:
        """简化的可用性检查 - 只检查基本配置条件"""
        # 基本检查：配置存在、内容长度、配置开关
        basic_check = (
            self.config
            and len(content) >= 200  # 概念类内容通常较短也可以处理
            and self.config.get("material", {}).get("material_enhance", {}).get(self.tool_name, True)
        )

        # 如果需要LLM支持，检查agent是否初始化成功
        if hasattr(self, "agent") and self.agent is None:
            return False

        return basic_check

    def apply_tool(self, content: str, output_dir: str, context: dict[str, Any], focus: str = None) -> Optional[str]:
        """应用工具，执行具体的扩充操作"""
        logger.info(f"🔧 应用{self.tool_name}工具，准备生成详细例子")

        try:
            os.makedirs(output_dir, exist_ok=True)

            # 生成输出路径
            output_filename = f"{self.tool_name}_output.json"
            output_path = os.path.join(output_dir, output_filename)

            # 检查是否已存在文件
            if os.path.exists(output_path):
                logger.info(f"工具输出已存在: {output_path}")
                with open(output_path, encoding="utf-8") as f:
                    existing_data = f.read()
                return existing_data

            # 执行具体的工具逻辑，使用focus参数而不是context
            result_content = self._process_with_camel(content, focus)

            if not result_content:
                return None

            # 保存结果数据（保持原有格式以便后续处理）
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(result_content)

            logger.info(f"🔧 {self.tool_name}工具处理完成: {output_path}")
            # 返回详细例子内容字符串，符合新的简化要求
            return result_content

        except Exception as e:
            logger.error(f"{self.tool_name}工具处理失败: {e}")
            return None

    def _process_with_camel(self, content: str, focus: str = None) -> Optional[str]:
        """使用Camel进行详细例子处理的逻辑"""
        # 构建结构化提示词，重点利用focus信息
        focus_instruction = ""
        if focus:
            focus_instruction = f"""
**重点关注方向**：{focus}
请紧紧围绕这个焦点方向进行详细例子生成，确保所有例子都与焦点高度相关。"""

        prompt = f"""你是专业的概念举例说明专家，需要基于以下内容生成详细、具体的例子来讲解抽象概念。

**待分析内容**：
<content>
{content}
</content>

<focus_instruction>
{focus_instruction}
</focus_instruction>

**任务要求**：
1. **围绕焦点进行例子生成**：紧紧围绕给定的重点关注方向，确保所有例子都与焦点高度相关。

2. **概念提取与分析**：
   - 准确识别核心概念、原理，分析其关键要素和步骤
   - 找出需要通过具体例子来理解的抽象部分

3. **例子设计要求**：
   - 创造具体、量化、准确的例子来解释抽象概念
   - 所有数据必须具体、准确，有明确的数值
   - 每个步骤都要清晰说明，不遗漏关键环节
   - 整个过程逻辑连贯，易于理解
   - 深入揭示概念的本质原理和机制
   - 例子要生动具体，便于理解和记忆

**输出格式**：
请严格按照以下JSON格式输出：

{{

    "title": "例子标题",
    "background": "背景设定",
    "step_by_step": [
        {{
            "step_name": "步骤名称",
            "description": "该步骤详细说明，不要遗漏关键环节，100字以内",
            "data_values": "具体数值和计算，不要遗漏关键步骤，150字以内",
            "insight": "该步骤的关键洞察，50字以内"
        }}
        ...
    ]
}}

**重要提醒**：
- 必须紧紧围绕给定的焦点方向进行例子生成
- 确保例子具体、量化，有明确的数值和计算
- 每个步骤都要详细说明，便于理解
- 揭示本质原理，提供深层洞察

请直接输出结果，不要包含额外说明。"""

        logger.info(f"生成详细例子分析, prompt characters: {len(prompt)}, words: {len(prompt.split())}")

        try:
            # 调用Camel agent进行处理
            response = self.agent.step(prompt)
            response_content = response.msgs[0].content.strip()

            # 检查是否不适合生成详细例子
            if response_content.startswith("该内容不适合生成详细例子"):
                logger.info(f"内容不适合生成详细例子: {response_content}")
                return response_content

            # 返回生成的详细例子内容
            return response_content

        except Exception as e:
            logger.error(f"Camel数据提取失败: {e}")
            return None

    def generate_intro(self, tool_result: dict[str, Any]) -> str:
        """生成工具输出的介绍文本"""
        # 兼容新的返回格式：tool_result现在直接是字符串内容
        if isinstance(tool_result, str):
            result_content = tool_result
        else:
            # 兼容旧格式
            result_content = tool_result.get("data", "") if tool_result else ""

        if not result_content:
            return ""

        # 检查是否不适合生成详细例子
        if result_content.startswith("该内容不适合生成详细例子"):
            return f"## 🎯 概念举例说明\n\n❌ {result_content}\n\n"

        # 生成详细例子介绍
        intro = f"## 🎯 {self.tool_description}\n\n"
        intro += "**生成的详细例子说明**：\n\n"
        intro += result_content
        intro += "\n\n*💡 这些例子通过具体、量化的方式帮助理解抽象概念的本质原理*\n\n"

        return intro


if __name__ == "__main__":
    from utils.common import Config

    config = Config()
    tool = ExampleExplainTool(config.config)
    tool.apply_tool(
        open("output/2506.06395/2506.06395.md").read(),
        "output/2506.06395",
        context={},
        focus="详细解释“模式锐化”如何通过数学形式转化为自置信度最大化，并用一个简单例子说明：比如一个模型对'2+2=?'回答'4'的置信度从0.6提升到0.9，并且对其他答案的置信度降低，展现其分布锐化过程。",
    )
    # tool.apply_tool(
    #    "公式解释",
    #    "output/chat_20250613_214003",
    #    context={},
    #    focus="BPE算法原理，并给出具体的例子",
    # )
