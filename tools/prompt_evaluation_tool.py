#!/usr/bin/env python3
"""
单独Prompt评估工具 - 使用优化后的prompt生成例子并评估质量
"""

import datetime
import json
import os
import sys
from pathlib import Path
from typing import Any

import yaml
from loguru import logger

# 添加父目录到导入路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from camel.agents import ChatAgent

from utils.create_llm_model import create_model


class PromptEvaluationTool:
    """单独Prompt评估工具类"""

    def __init__(self, config_path: str = "config/config.yaml"):
        """初始化评估工具"""
        self.config_path = config_path
        self.config = self._load_config(config_path)
        self.gemini_model = create_model(config_path)
        self.claude_model = create_model(
            model_type="claude-sonnet-4-20250514", api="sk-JpXbwzhmQEGQGNMTwo9fsTQVjD7BwxEi7tu4z0RWAwOcyq54"
        )

        self.output_dir = Path("output/prompt_evaluations")
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 设置日志
        logger.add("logs/prompt_evaluation.log", rotation="1 day", retention="30 days", level="INFO")

    def _load_config(self, config_path: str) -> dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, encoding="utf-8") as f:
                config = yaml.safe_load(f)
            logger.info(f"从 {config_path} 加载配置")
            return config
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            raise

    def _load_prompt_template(self, prompt_file: str) -> str:
        """加载prompt模板"""
        try:
            with open(prompt_file, encoding="utf-8") as f:
                content = f.read()

            # 提取prompt变量（假设格式为 prompt = """..."""）
            if 'prompt = """' in content:
                start = content.find('prompt = """') + len('prompt = """')
                end = content.rfind('"""')
                return content[start:end].strip()
            else:
                return content
        except Exception as e:
            logger.error(f"加载prompt模板失败: {e}")
            raise

    def generate_example(self, topic: str, purpose: str, audience: str, prompt_file: str) -> str:
        """使用prompt模板生成例子"""
        try:
            # 加载并格式化prompt
            prompt_template = self._load_prompt_template(prompt_file)
            formatted_prompt = prompt_template.format(主题=topic, 目的=purpose, 目标群体=audience)

            # 创建ChatAgent
            agent = ChatAgent(formatted_prompt, self.gemini_model)

            user_prompt = "请根据上述要求生成高质量的教学叙事蓝图，严格按照指定格式输出。"
            response = agent.step(user_prompt)
            return response.msg.content

        except Exception as e:
            logger.error(f"生成例子失败: {e}")
            return f"生成失败: {str(e)}"

    def evaluate_example(self, topic: str, purpose: str, audience: str, example: str) -> dict[str, Any]:
        """使用大模型评估生成的例子质量"""

        evaluation_prompt = f"""
你是一位资深的教学设计评估专家，请对以下针对主题"{topic}"的教学叙事蓝图进行全面质量评估。

**评估目标**: {purpose}
**目标受众**: {audience}

## 待评估的教学叙事蓝图：
{example}

请从以下维度进行详细评估（总分100分）：

### 1. 同构类比质量（25分）
- **结构同构性**（10分）：类比与目标概念的内在逻辑结构是否高度一致？
- **观众熟悉度**（8分）：类比是否选择了目标受众熟悉的领域？
- **认知桥梁效果**（7分）：类比是否能自然地从熟悉领域过渡到陌生概念？

### 2. 教学设计完整性（25分）
- **叙事结构完整性**（10分）：是否包含完整的从引入到总结的教学流程？
- **逻辑连贯性**（8分）：各个分镜之间的逻辑关系是否清晰连贯？
- **内容深度适宜性**（7分）：内容深度是否适合目标受众？

### 3. 边界意识与适用性（20分）
- **局限性识别**（10分）：是否系统性地识别了概念的适用边界？
- **误用场景警示**（5分）：是否提供了典型误用场景的警示？
- **替代方案提及**（5分）：是否在不适用场景下提供了正确的替代方案？

### 4. 步骤总结可操作性（15分）
- **步骤提炼合理性**（8分）：核心步骤是否提炼得当，粒度合适？
- **动画系统兼容性**（4分）：步骤格式是否与animate_step_by_step函数兼容？
- **实用性**（3分）：步骤是否具有实际的教学指导价值？

### 5. 视觉化描述质量（15分）
- **动画描述具体性**（8分）：视觉变换描述是否具体可实现？
- **Manim适配性**（4分）：描述是否适合Manim动画制作？
- **教学效果支撑**（3分）：视觉设计是否有效支撑教学目标？

分项评估之后，给出综合的评估总结，以及改进建议。
"""

        try:
            # 创建评估agent
            system_message = (
                "你是一位资深的教学设计评估专家，擅长客观公正地评估教学内容的质量，特别关注同构类比思维和边界意识。"
            )

            agent = ChatAgent(system_message, self.claude_model)

            response = agent.step(evaluation_prompt)

            return response.msg.content

        except Exception as e:
            logger.error(f"评估例子失败: {e}")
            import traceback

            logger.error(traceback.format_exc())
            return {"error": str(e)}

    def run_evaluation(
        self, topic: str, purpose: str, audience: str, prompt_file: str = "prompts/example_gen_prompt.py"
    ) -> dict[str, Any]:
        """运行完整的评估流程"""
        logger.info(f"开始评估 - 主题: {topic}, 目的: {purpose}, 受众: {audience}")

        # 生成时间戳
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

        try:
            # 1. 生成例子
            logger.info("生成教学例子...")
            example = self.generate_example(topic, purpose, audience, prompt_file)

            # 立即保存生成的例子，便于查看
            topic_dir = self.output_dir / topic.replace("/", "_")
            example_file = topic_dir / f"example_{timestamp}.md"
            self._save_to_file(example_file, example, "生成例子")

            # 2. 评估质量
            logger.info("评估例子质量...")
            evaluation = self.evaluate_example(topic, purpose, audience, example)

            # 3. 整理结果
            result = {
                "metadata": {
                    "timestamp": timestamp,
                    "topic": topic,
                    "purpose": purpose,
                    "audience": audience,
                    "prompt_file": prompt_file,
                },
                "generated_example": example,
                "evaluation": evaluation,
            }

            # 4. 保存完整结果
            topic_dir = self.output_dir / topic.replace("/", "_")
            result_file = topic_dir / f"evaluation_{timestamp}.json"
            self._save_to_file(result_file, result, "评估结果")

            logger.info("评估完成！")
            return result

        except Exception as e:
            logger.error(f"评估执行失败: {e}")
            return {"error": str(e)}

    def _save_to_file(self, file_path: Path, content: Any, description: str):
        """通用文件保存函数"""
        try:
            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)

            with open(file_path, "w", encoding="utf-8") as f:
                if isinstance(content, str):
                    f.write(content)
                else:
                    # 假设是字典或其他可JSON序列化的对象
                    json.dump(content, f, ensure_ascii=False, indent=2)

            logger.info(f"{description}已保存到: {file_path}")

        except Exception as e:
            logger.error(f"保存{description}失败: {e}")

    def print_evaluation_summary(self, result: dict[str, Any]):
        """打印评估摘要"""
        if "error" in result:
            print(f"❌ 评估失败: {result['error']}")
            return

        metadata = result["metadata"]
        evaluation = result["evaluation"]

        print("\n" + "=" * 80)
        print("📊 PROMPT 质量评估结果")
        print("=" * 80)
        print(f"📋 主题: {metadata['topic']}")
        print(f"🎯 目的: {metadata['purpose']}")
        print(f"👥 受众: {metadata['audience']}")
        print(f"⏰ 时间: {metadata['timestamp']}")
        print()
        print(evaluation)

        print("=" * 80)


def main():
    """主函数 - 命令行接口"""
    import argparse

    parser = argparse.ArgumentParser(description="Prompt质量评估工具")
    parser.add_argument("topic", help="主题")
    parser.add_argument("purpose", help="目的")
    parser.add_argument("audience", help="目标受众")
    parser.add_argument("--prompt-file", default="prompts/example_gen_prompt.py", help="prompt文件路径")

    args = parser.parse_args()

    tool = PromptEvaluationTool()

    # 执行评估
    result = tool.run_evaluation(args.topic, args.purpose, args.audience, args.prompt_file)
    tool.print_evaluation_summary(result)


if __name__ == "__main__":
    main()
