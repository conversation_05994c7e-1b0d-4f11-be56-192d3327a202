import os
from pathlib import Path
import subprocess
from typing import Optional, Dict, Any, List, Tuple
from camel.toolkits.base import BaseToolkit
from camel.toolkits.function_tool import FunctionTool
from camel.utils import dependencies_required
from loguru import logger
import yaml
from camel.agents import ChatAgent
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.types import ModelPlatformType
import re

class MermaidToolkit(BaseToolkit):
    """
    一个用于将Mermaid文件（.mmd）转换为PNG图片的工具包。

    功能：
        - 自动化将Mermaid流程图源码文件（.mmd）转换为PNG图片。
        - 支持自定义输入输出路径。
        - 适合文档自动化、知识图谱可视化等场景。
        - 自动修复出错的Mermaid代码。

    方法：
        convert_mermaid_to_png(input_mmd_path, output_png_path=None)

    返回：
        dict，包含输入文件、输出文件、执行状态等。
    """
    @dependencies_required("subprocess", "loguru", "yaml")
    def __init__(self, timeout: Optional[float] = None, config_path: str = "config/config.yaml") -> None:
        super().__init__(timeout=timeout)
        
        # 加载配置
        self.config = self._load_config(config_path)
        
        # 初始化模型和AI代理
        self.model = self._create_model()
        self.repair_agent = self._create_repair_agent()
        
        # 设置最大修复尝试次数
        self.max_repair_attempts = 3

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """从YAML文件加载配置"""
        try:
            with open(config_path) as file:
                config = yaml.safe_load(file)
            # 设置配置属性
            logger.info(f"配置已从 {config_path} 加载")
            return config
        except Exception as e:
            logger.error(f"加载配置时出错: {str(e)}")
            # 设置默认值
            return {
                "model": {
                    "type": "openai/gpt-4o-mini",
                    "temperature": 0.1,
                    "max_tokens": 4096,
                    "api": {
                        "openai_compatibility_api_key": os.environ.get("OPENAI_API_KEY", ""),
                        "openai_compatibility_api_base_url": ""
                    }
                }
            }
    
    def _create_model(self):
        """创建模型实例"""
        # 从配置中获取API设置
        api_config = self.config.get("model", {}).get("api", {})
        
        return ModelFactory.create(
            model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
            model_type=self.config.get("model", {}).get("type", "openai/gpt-4o-mini"),
            api_key=api_config.get("openai_compatibility_api_key"),
            url=api_config.get("openai_compatibility_api_base_url"),
        )

    def _create_repair_agent(self):
        """创建用于修复Mermaid代码的ChatAgent"""
        REPAIR_SYSTEM_PROMPT = """
        你是一个专门修复Mermaid图表代码的专家。你需要严格遵循Mermaid的语法规范修复代码。

        关键语法规则：
        1. 注释规则：
           - 单独的注释行使用 %% 开头
           - 不要在节点定义或连接后跟行内注释
           - 所有注释必须单独成行
        
        2. 节点定义规则：
           - 基本格式：id["显示文本"]
           - 嵌套文本使用圆括号，不用方括号：id["名称(说明)"]
           - 每个节点定义后直接换行
        
        3. 连接规则：
           - 基本连接：A --> B
           - 带文本连接：A -- "说明文本" --> B
           - 每个连接定义后直接换行
        
        4. 子图规则：
           - 格式：subgraph id["显示名称"]
           - 子图内的节点缩进两个空格
           - end 单独一行表示子图结束
           - 避免子图ID与节点ID冲突
        
        5. 样式规则：
           - classDef 定义：classDef className fill:#color,stroke:#color
           - class 应用：class nodeId className
        
        修复步骤：
        1. 识别错误类型：
           - 注释错误：将行内注释移到单独行
           - 嵌套方括号：改用圆括号表示嵌套
           - 子图冲突：重命名冲突的ID
           - 连接错误：规范化连接语法
        
        2. 应用修复：
           - 保持节点和连接的基本结构不变
           - 将所有注释移到单独行
           - 确保每个语句后没有多余字符
           - 保持一致的缩进格式
        
        3. 代码格式：
           - 使用两个空格缩进
           - 节点定义、连接、注释各占一行
           - 相关节点和连接分组放置
           - 子图内容保持缩进
        
        你的回复格式：
        ```
        [错误分析]
        1. 具体错误1及其位置
        2. 具体错误2及其位置
        ...

        [修复策略]
        1. 针对错误1的修复方法
        2. 针对错误2的修复方法
        ...

        [修复代码]
        完整的修复后代码
        ```

        注意：
        1. 修复时必须保持图的结构和功能不变
        2. 注释内容要保留，但必须移到单独行
        3. 所有行末不能有多余的分号或空格
        4. 子图ID不能与节点ID冲突
        5. 标识符中避免使用特殊字符
        """
        
        return ChatAgent(model=self.model, system_message=REPAIR_SYSTEM_PROMPT)

    def _repair_mermaid_code(self, mmd_content: str, error_info: str) -> str:
        """
        使用ChatAgent修复Mermaid代码
        
        参数：
            mmd_content (str): 原始Mermaid代码
            error_info (str): 错误信息
            
        返回：
            str: 修复后的Mermaid代码
        """
        try:
            logger.info("开始修复Mermaid代码...")
            
            # 构建提示词
            prompt = f"""
            请分析并修复以下Mermaid代码中的错误。

            错误信息:
            {error_info}

            当前代码:
            ```mermaid
            {mmd_content}
            ```

            请严格按照以下步骤修复：
            1. 分析错误信息，找出具体的语法问题
            2. 检查并修复所有行内注释（必须移到单独行）
            3. 检查并修复所有嵌套方括号（改用圆括号）
            4. 检查并解决子图ID冲突
            5. 确保每个语句后没有多余字符
            6. 保持一致的缩进格式

            如果发现这是环境配置问题（如缺少Chrome、Puppeteer配置问题等）而非代码问题，请明确指出。
            """
            
            # 创建用户消息
            user_message = BaseMessage.make_user_message(role_name="User", content=prompt)
            
            # 获取ChatAgent响应
            agent_response = self.repair_agent.step(user_message)
            response_text = agent_response.msg.content
            
            # 如果检测到环境错误，记录并返回原始代码
            if "环境配置问题" in response_text or "环境错误" in response_text:
                logger.warning("检测到环境配置问题，而非代码错误")
                logger.info(response_text.split("[修复代码]")[0] if "[修复代码]" in response_text else response_text)
                return mmd_content
            
            # 提取修复后的代码
            fixed_code = self._extract_code_from_response(response_text)
            
            logger.info("Mermaid代码已修复")
            return fixed_code
            
        except Exception as e:
            logger.error(f"修复Mermaid代码时出错: {str(e)}")
            # 返回原始代码
            return mmd_content
            
    def _extract_code_from_response(self, response: str) -> str:
        """
        从大模型响应中提取Mermaid代码
        
        参数：
            response (str): 大模型响应文本
            
        返回：
            str: 提取出的Mermaid代码
        """
        # 先检查是否有[修复代码]标记
        if "[修复代码]" in response:
            code_section = response.split("[修复代码]")[1].strip()
            # 如果修复代码部分有代码块，提取它
            if "```mermaid" in code_section and "```" in code_section[code_section.find("```mermaid") + 10:]:
                start = code_section.find("```mermaid") + 10
                end = code_section.find("```", start)
                return code_section[start:end].strip()
            # 否则使用整个修复代码部分
            return code_section.strip()
            
        # 检查是否有代码块标记
        if "```mermaid" in response and "```" in response[response.find("```mermaid") + 10:]:
            start = response.find("```mermaid") + 10
            end = response.find("```", start)
            return response[start:end].strip()
        
        # 如果没有代码块标记，则假设整个响应就是代码
        # 但是需要清理一些常见的前缀
        prefixes = ["以下是修复后的代码:", "修复后的代码:", "这是修复后的代码:"]
        clean_response = response
        for prefix in prefixes:
            if response.startswith(prefix):
                clean_response = response[len(prefix):].strip()
                break
                
        return clean_response

    def convert_mermaid_to_png(self, input_mmd_path: str, output_png_path: Optional[str] = None) -> Dict[str, Any]:
        """
        将Mermaid文件（.mmd）转换为PNG图片。
        如果遇到错误，将自动尝试修复Mermaid代码并重新转换。

        参数：
            input_mmd_path (str): 输入的Mermaid文件路径。
            output_png_path (str, 可选): 输出的PNG文件路径，默认为与输入同名的.png。
        返回：
            dict: 包含输入、输出文件、状态等信息。
        """
        try:
            # 将功能直接实现到这里，不再依赖外部模块
            input_path = Path(input_mmd_path).resolve()
            
            if not input_path.exists():
                logger.error(f"输入文件 '{input_path}' 不存在。")
                return {
                    "status": "error",
                    "input_mmd_path": input_mmd_path,
                    "output_png_path": output_png_path,
                    "error": f"输入文件不存在: {input_path}"
                }
                
            if not output_png_path:
                # 如果未指定输出路径，则使用相同的文件名但扩展名为.png
                output_path = input_path.with_suffix(".png")
            else:
                output_path = Path(output_png_path).resolve()
            
            logger.info(f"正在将 {input_path} 转换为 {output_path}")
            
            # 执行mmdc命令将mermaid文件转换为PNG
            success, error_msg = self._run_mmdc(input_path, output_path)
            
            # 如果转换成功，直接返回结果
            if success:
                return {
                    "status": "success",
                    "input_mmd_path": str(input_path),
                    "output_png_path": str(output_path),
                    "message": f"PNG图片已生成: {output_path}"
                }
                
            # 检查是否是环境错误，如果是则不尝试修复代码
            if error_msg.startswith("环境"):
                logger.error("检测到环境配置问题，不尝试修复代码")
                return {
                    "status": "error",
                    "input_mmd_path": str(input_path),
                    "output_png_path": str(output_path),
                    "message": "Mermaid转PNG失败，环境配置问题",
                    "error": error_msg,
                    "suggestion": "请检查mermaid-cli安装和Chrome/Puppeteer配置"
                }
            
            # 如果是语法错误，尝试修复
            logger.info("Mermaid转PNG失败，尝试自动修复...")
            
            # 读取原始mermaid代码
            with open(input_path, 'r', encoding='utf-8') as f:
                original_mmd = f.read()
                
            # 初始化修复计数器和修复后的代码
            repair_count = 0
            fixed_mmd = original_mmd
            repair_history = []
            
            # 不断尝试修复和转换，直到成功或达到最大尝试次数
            while repair_count < self.max_repair_attempts:
                repair_count += 1
                logger.info(f"第 {repair_count} 次修复尝试...")
                
                # 使用大模型修复代码
                fixed_mmd = self._repair_mermaid_code(fixed_mmd, error_msg)
                
                # 记录修复历史
                repair_history.append({
                    "attempt": repair_count,
                    "fixed_code": fixed_mmd
                })
                
                # 保存修复后的代码到临时文件
                fixed_input_path = input_path.with_name(f"{input_path.stem}_fixed_{repair_count}{input_path.suffix}")
                with open(fixed_input_path, 'w', encoding='utf-8') as f:
                    f.write(fixed_mmd)
                    
                logger.info(f"保存修复后的代码到 {fixed_input_path}")
                
                # 再次尝试转换
                success, error_msg = self._run_mmdc(fixed_input_path, output_path)
                
                # 如果成功，返回结果
                if success:
                    logger.info(f"在第 {repair_count} 次修复尝试后成功")
                    return {
                        "status": "success",
                        "input_mmd_path": str(input_path),
                        "fixed_mmd_path": str(fixed_input_path),
                        "output_png_path": str(output_path),
                        "message": f"经过 {repair_count} 次修复尝试后，PNG图片已生成: {output_path}",
                        "repair_count": repair_count,
                        "repair_history": repair_history
                    }
            
            # 如果达到最大尝试次数仍然失败
            logger.error(f"达到最大修复尝试次数 ({self.max_repair_attempts})，修复失败")
            return {
                "status": "error",
                "input_mmd_path": str(input_path),
                "output_png_path": str(output_path),
                "message": f"在 {self.max_repair_attempts} 次修复尝试后，Mermaid转PNG仍然失败",
                "error": error_msg,
                "repair_count": repair_count,
                "repair_history": repair_history
            }
                
        except Exception as e:
            logger.error(f"Mermaid转PNG失败: {e}")
            return {
                "status": "error",
                "input_mmd_path": input_mmd_path,
                "output_png_path": output_png_path,
                "error": str(e)
            }
            
    def _run_mmdc(self, input_mmd_path, output_png_path):
        """
        运行mmdc命令将mermaid文件转换为PNG。
        
        返回:
            Tuple[bool, str]: (成功状态, 错误信息)
        """
        try:
            # 确保输出目录存在
            output_png_path.parent.mkdir(parents=True, exist_ok=True)

            # 构建mmdc命令
            command = ["mmdc", "-i", str(input_mmd_path), "-o", str(output_png_path), "-w", "1920", "-s", "4"]
            logger.info(f"运行命令: {' '.join(command)}")

            # 执行命令
            result = subprocess.run(command, capture_output=True, text=True, check=True, encoding="utf-8")
            logger.info(f"成功生成 {output_png_path}")
            if result.stderr:
                logger.warning(f"mmdc stderr: {result.stderr}")
            return True, ""
        except FileNotFoundError:
            error_msg = "环境错误: 找不到'mmdc'命令。请确保已安装mermaid-cli并添加到PATH中。"
            logger.error(error_msg)
            logger.error("通常可以使用以下命令安装: npm install -g @mermaid-js/mermaid-cli")
            return False, error_msg
        except subprocess.CalledProcessError as e:
            # 分析错误信息，区分环境问题和语法问题
            stderr = e.stderr
            stdout = e.stdout
            
            # 更准确地检测是否是环境问题
            is_env_error = False
            
            # 确定的环境错误关键词和上下文
            critical_env_errors = [
                "Could not find Chrome",
                "installation before running",
                "cache path is incorrectly configured",
                "npm install -g",
                "command not found",
                "No such file or directory"
            ]
            
            # 更宽松的环境关键词（仅当没有语法错误指示时考虑）
            soft_env_keywords = [
                "puppeteer",
                "npm",
                "node",
                "chromium",
                "browser"
            ]
            
            # 语法错误关键词
            syntax_error_keywords = [
                "Parse error",
                "Syntax error",
                "Expecting",
                "syntax error:",
                "Error: Parse",
                "Invalid",
                "Unknown",
                "not allowed"
            ]
            
            # 首先检查是否有明确的语法错误指示
            has_syntax_error = False
            for keyword in syntax_error_keywords:
                if keyword.lower() in stderr.lower() or keyword.lower() in stdout.lower():
                    has_syntax_error = True
                    break
                    
            # 如果有明确的语法错误，那么这不是环境问题
            if has_syntax_error:
                is_env_error = False
            else:
                # 检查是否有确定的环境错误
                for keyword in critical_env_errors:
                    if keyword.lower() in stderr.lower() or keyword.lower() in stdout.lower():
                        is_env_error = True
                        break
                        
                # 如果没有找到确定的环境错误，检查软环境关键词
                if not is_env_error:
                    for keyword in soft_env_keywords:
                        # 排除常见的误判情况，例如"Browser console:"通常是输出日志而非环境错误
                        if (keyword.lower() in stderr.lower() or keyword.lower() in stdout.lower()) and \
                           not "Browser console:" in stdout and not "Browser console:" in stderr:
                            is_env_error = True
                            break
            
            if is_env_error:
                error_msg = f"环境配置错误:\n{stderr}\n\n可能需要安装Chrome或配置Puppeteer，例如运行:\nnpx puppeteer browsers install chrome-headless-shell"
            else:
                # 尝试从错误信息中提取有关Mermaid语法错误的部分
                syntax_error_msg = ""
                
                # 读取原始文件内容
                mmd_content = ""
                try:
                    with open(input_mmd_path, encoding="utf-8") as f_err:
                        mmd_content = f_err.read()
                        logger.error(f"输入的Mermaid代码:\n{mmd_content}")
                except Exception as read_err:
                    logger.error(f"无法读取mmd文件 {input_mmd_path}: {read_err}")
                
                # 提取可能的语法错误信息
                if "Error: Parse error" in stderr or "Parse error" in stderr:
                    syntax_error_msg = f"Mermaid语法错误:\n{stderr}"
                elif "syntax error:" in stderr.lower():
                    syntax_error_msg = f"Mermaid语法错误:\n{stderr}"
                elif "Expecting" in stderr:
                    syntax_error_msg = f"Mermaid语法错误 - 期望不同的标记:\n{stderr}"
                elif "Unknown" in stderr and "token" in stderr.lower():
                    syntax_error_msg = f"Mermaid语法错误 - 未知标记:\n{stderr}"
                else:
                    syntax_error_msg = f"可能是Mermaid语法错误:\n{stderr}"
                
                # 尝试定位具体错误行
                if "line" in stderr.lower() and mmd_content:
                    try:
                        # 尝试提取行号
                        line_match = re.search(r"line\s+(\d+)", stderr.lower())
                        if line_match:
                            line_num = int(line_match.group(1))
                            lines = mmd_content.split("\n")
                            if 0 < line_num <= len(lines):
                                problematic_line = lines[line_num-1]
                                syntax_error_msg += f"\n\n问题行 ({line_num}): {problematic_line}"
                    except Exception as e:
                        logger.error(f"尝试定位错误行时出错: {e}")
                
                error_msg = syntax_error_msg
            
            logger.error(f"运行mmdc时出错，输出文件: {output_png_path}:")
            logger.error(f"命令: {' '.join(e.cmd)}")
            logger.error(f"返回码: {e.returncode}")
            logger.error(f"stdout: {stdout}")
            logger.error(f"stderr: {stderr}")
            logger.error(f"错误类型: {'环境配置问题' if is_env_error else 'Mermaid语法错误'}")
                
            return False, error_msg
        except Exception as e:
            error_msg = f"处理 {output_png_path} 时发生意外错误: {e}"
            logger.error(error_msg)
            return False, error_msg

    def get_tools(self):
        """
        返回工具列表，供大模型自动发现和调用。
        """
        return [
            FunctionTool(self.convert_mermaid_to_png),
        ]

if __name__ == "__main__":
    # 创建示例Mermaid文件进行测试
    test_dir = Path("test_mermaid")
    test_dir.mkdir(exist_ok=True)
    
    # 正确的Mermaid示例
    correct_mmd = """
graph TD
    A[开始] --> B[第二步]
    B --> C[第三步]
    C --> D[结束]
    """
    
    correct_file = test_dir / "correct.mmd"
    with open(correct_file, "w", encoding="utf-8") as f:
        f.write(correct_mmd)
    
    # 错误的Mermaid示例（缺少闭合括号）
    error_mmd = """
graph TD
    A[开始 --> B[第二步]
    B --> C[第三步
    C --> D[结束]
    """
    
    error_file = test_dir / "error.mmd"
    with open(error_file, "w", encoding="utf-8") as f:
        f.write(error_mmd)
    
    # 测试工具包
    toolkit = MermaidToolkit()
    
    # 测试正确的Mermaid文件
    print("\n=== 测试正确的Mermaid文件 ===")
    result = toolkit.convert_mermaid_to_png(
        input_mmd_path=str(correct_file),
        output_png_path=str(test_dir / "correct.png")
    )
    print(f"转换结果: {result['status']}")
    print(f"消息: {result.get('message', '')}")
    
    # 测试错误的Mermaid文件（需要自动修复）
    print("\n=== 测试错误的Mermaid文件（需要自动修复） ===")
    result = toolkit.convert_mermaid_to_png(
        input_mmd_path=str(error_file),
        output_png_path=str(test_dir / "error_fixed.png")
    )
    print(f"转换结果: {result['status']}")
    print(f"消息: {result.get('message', '')}")
    print(f"修复尝试次数: {result.get('repair_count', 0)}")
    
    if result['status'] == 'success' and 'fixed_mmd_path' in result:
        print(f"修复后的文件: {result['fixed_mmd_path']}")
        # 打印修复后的代码
        with open(result['fixed_mmd_path'], 'r', encoding='utf-8') as f:
            print("\n修复后的Mermaid代码:")
            print(f.read()) 