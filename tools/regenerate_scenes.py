#!/usr/bin/env python3
"""
分镜重新生成脚本

用于在手动修改DSL文件后，重新生成指定分镜的视频，并与其他分镜拼接成完整视频。

功能特点：
1. 支持指定单个或多个分镜进行重新生成
2. 支持指定处理阶段（默认：code,render,subtitles）
3. 自动读取storyboard.json获取总分镜数量
4. 智能拼接：使用重新生成的分镜替换原分镜，其他分镜使用现有视频
5. 自动添加背景音乐和转场效果

用法示例：
# 重新生成第3个分镜
python regenerate_scenes.py --scenes 3 --storyboard output/project/storyboard.json

# 重新生成第1,3,5个分镜
python regenerate_scenes.py --scenes 1,3,5 --storyboard output/project/storyboard.json

# 指定处理阶段（不包含字幕）
python regenerate_scenes.py --scenes 2 --stages code,render --storyboard output/project/storyboard.json
"""

import argparse
import json
import sys
from pathlib import Path
from typing import Any, Optional

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.append(str(project_root))

from loguru import logger

from process_storyboard import StoryboardProcessor


class SceneRegenerator:
    """分镜重新生成器"""

    def __init__(
        self,
        storyboard_file: str,
        output_dir: Optional[str] = None,
        quality: str = "l",
        project_name: Optional[str] = None,
    ):
        """
        初始化分镜重新生成器

        Args:
            storyboard_file: storyboard.json文件路径
            output_dir: 输出目录，如果未指定则从storyboard文件路径推断
            quality: 渲染质量
            project_name: 项目名称
        """
        self.storyboard_file = Path(storyboard_file)
        if not self.storyboard_file.exists():
            raise FileNotFoundError(f"Storyboard file not found: {storyboard_file}")

        # 推断输出目录和项目名称
        if output_dir is None:
            self.output_dir = self.storyboard_file.parent
        else:
            self.output_dir = Path(output_dir)

        if project_name is None:
            self.project_name = self.output_dir.name
        else:
            self.project_name = project_name

        self.quality = quality

        # 加载storyboard数据
        self.storyboard_data = self._load_storyboard()
        self.total_scenes = len(self.storyboard_data)

        logger.info(f"📋 Storyboard: {self.storyboard_file}")
        logger.info(f"📁 Output dir: {self.output_dir}")
        logger.info(f"🎬 Project name: {self.project_name}")
        logger.info(f"🎯 Total scenes: {self.total_scenes}")

    def _load_storyboard(self) -> list[dict[str, Any]]:
        """加载storyboard数据"""
        try:
            with open(self.storyboard_file, encoding="utf-8") as f:
                data = json.load(f)

            if isinstance(data, dict):
                storyboard = data.get("storyboard", [])
            else:
                storyboard = data

            if not storyboard:
                raise ValueError("No storyboard entries found")

            return storyboard
        except Exception as e:
            logger.error(f"Failed to load storyboard: {e}")
            raise

    def _find_existing_videos(self) -> dict[int, str]:
        """查找现有的视频文件"""
        existing_videos = {}

        # 查找视频目录
        videos_dir = Path("media/videos")
        if not videos_dir.exists():
            logger.warning(f"Videos directory not found: {videos_dir}")
            return existing_videos

        # 质量映射
        quality_folders = {"l": "480p15", "m": "720p30", "h": "1080p60", "k": "2160p60"}
        quality_folder = quality_folders.get(self.quality, "480p15")

        # 查找每个分镜的视频文件
        for scene_idx in range(1, self.total_scenes + 1):
            # 可能的视频文件路径
            possible_paths = [
                # 带字幕的视频
                project_root
                / "media"
                / "videos"
                / f"storyboard_{scene_idx}_attempt_1_dsl"
                / quality_folder
                / f"Storyboard_{scene_idx}_subtitle.mp4",
                # 原始渲染视频
                project_root
                / "media"
                / "videos"
                / f"storyboard_{scene_idx}_attempt_1_dsl"
                / quality_folder
                / f"Storyboard_{scene_idx}.mp4",
                # 其他可能的位置
                videos_dir / f"scene_{scene_idx}.mp4",
                videos_dir / f"storyboard_{scene_idx}.mp4",
            ]

            for path in possible_paths:
                if path.exists():
                    existing_videos[scene_idx] = str(path)
                    logger.info(f"✅ Found existing video for scene {scene_idx}: {path.name}")
                    break
            else:
                logger.warning(f"⚠️  No existing video found for scene {scene_idx}")

        return existing_videos

    def _check_dsl_files(self, scene_numbers: list[int]) -> dict[int, str]:
        """检查指定分镜的DSL文件是否存在"""
        dsl_files = {}

        for scene_num in scene_numbers:
            if scene_num < 1 or scene_num > self.total_scenes:
                logger.error(f"❌ Scene number {scene_num} out of range [1, {self.total_scenes}]")
                continue

            # 查找DSL文件
            dsl_path = self.output_dir / f"storyboard_{scene_num}_attempt_1_dsl.json"
            if dsl_path.exists():
                dsl_files[scene_num] = str(dsl_path)
                logger.info(f"✅ Found DSL file for scene {scene_num}: {dsl_path.name}")
            else:
                logger.error(f"❌ DSL file not found for scene {scene_num}: {dsl_path}")

        return dsl_files

    def regenerate_scenes(self, scene_numbers: list[int], stages: str = "code,render,subtitles") -> bool:
        """
        重新生成指定分镜

        Args:
            scene_numbers: 要重新生成的分镜编号列表（1-based）
            stages: 处理阶段，默认"code,render,subtitles"

        Returns:
            bool: 是否成功
        """
        logger.info(f"🔄 Starting regeneration for scenes: {scene_numbers}")
        logger.info(f"📋 Stages: {stages}")

        # 检查DSL文件
        dsl_files = self._check_dsl_files(scene_numbers)
        if not dsl_files:
            logger.error("❌ No valid DSL files found for regeneration")
            return False

        # 为每个需要重新生成的分镜创建StoryboardProcessor
        success_count = 0

        for scene_num in sorted(dsl_files.keys()):
            logger.info(f"🎬 Regenerating scene {scene_num}...")

            try:
                # 创建处理器，专门处理这个分镜
                processor = StoryboardProcessor(
                    storyboard_file=str(self.storyboard_file),
                    output_dir=str(self.output_dir),
                    max_workers=1,  # 单个分镜处理，不需要并行
                    quality=self.quality,
                    stages_str=stages,
                    project_name=self.project_name,
                    clear_state=False,
                )

                # 处理单个分镜（scene_num是1-based，转为0-based）
                scene_idx = scene_num - 1
                results = processor.process_all(start_idx=scene_idx, end_idx=scene_idx + 1)

                if results:
                    logger.success(f"✅ Scene {scene_num} regenerated successfully")
                    success_count += 1
                else:
                    logger.error(f"❌ Failed to regenerate scene {scene_num}")

            except Exception as e:
                logger.error(f"❌ Error regenerating scene {scene_num}: {e}")

        if success_count > 0:
            logger.success(f"🎉 Successfully regenerated {success_count}/{len(dsl_files)} scenes")
            return True
        else:
            logger.error("❌ Failed to regenerate any scenes")
            return False

    def create_final_video(self, regenerated_scenes: list[int] = None) -> Optional[str]:
        """
        创建最终视频，包含所有分镜（重新生成的和现有的）

        Args:
            regenerated_scenes: 已重新生成的分镜编号列表

        Returns:
            str: 最终视频路径，失败返回None
        """
        logger.info("🎬 Creating final video with all scenes...")

        # 查找所有分镜的视频文件
        existing_videos = self._find_existing_videos()

        # 按顺序收集所有分镜的视频路径
        video_sequence = []
        missing_scenes = []

        for scene_num in range(1, self.total_scenes + 1):
            if scene_num in existing_videos:
                video_sequence.append(existing_videos[scene_num])
                status = "🔄 regenerated" if regenerated_scenes and scene_num in regenerated_scenes else "✅ existing"
                logger.info(f"Scene {scene_num}: {status} - {Path(existing_videos[scene_num]).name}")
            else:
                missing_scenes.append(scene_num)
                logger.warning(f"⚠️  Scene {scene_num}: missing")

        if missing_scenes:
            logger.warning(f"Missing scenes: {missing_scenes}")
            if len(video_sequence) == 0:
                logger.error("❌ No videos available for concatenation")
                return None

        if not video_sequence:
            logger.error("❌ No video files found for concatenation")
            return None

        # 使用StoryboardProcessor进行拼接（包含背景音乐和转场）
        try:
            processor = StoryboardProcessor(
                storyboard_file=str(self.storyboard_file),
                output_dir=str(self.output_dir),
                max_workers=1,
                quality=self.quality,
                stages_str="concat",  # 只执行拼接阶段
                project_name=self.project_name,
            )

            # 直接调用拼接方法
            final_video = processor._concatenate_videos(video_sequence)

            if final_video:
                logger.success(f"🎉 Final video created: {final_video}")
                return final_video
            else:
                logger.error("❌ Failed to create final video")
                return None

        except Exception as e:
            logger.error(f"❌ Error creating final video: {e}")
            import traceback

            logger.error(traceback.format_exc())
            return None


def parse_scene_numbers(scenes_str: str) -> list[int]:
    """解析分镜编号字符串"""
    scene_numbers = []

    for part in scenes_str.split(","):
        part = part.strip()
        if "-" in part:
            # 处理范围，如 "1-3"
            start, end = map(int, part.split("-"))
            scene_numbers.extend(range(start, end + 1))
        else:
            # 处理单个数字
            scene_numbers.append(int(part))

    return sorted(list(set(scene_numbers)))  # 去重并排序


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Regenerate specified scenes after manual DSL modification",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Regenerate scene 3
  python regenerate_scenes.py --scenes 3 --storyboard output/project/storyboard.json

  # Regenerate scenes 1, 3, and 5
  python regenerate_scenes.py --scenes 1,3,5 --storyboard output/project/storyboard.json

  # Regenerate scenes 2-4 (range)
  python regenerate_scenes.py --scenes 2-4 --storyboard output/project/storyboard.json

  # Regenerate with custom stages (no subtitles)
  python regenerate_scenes.py --scenes 2 --stages code,render --storyboard output/project/storyboard.json
        """,
    )

    parser.add_argument(
        "--scenes",
        "-s",
        required=True,
        help="Scene numbers to regenerate (1-based). Support single (3), multiple (1,3,5), or range (1-3)",
    )

    parser.add_argument("--storyboard", "-f", required=True, help="Path to storyboard.json file")

    parser.add_argument("--output-dir", "-o", help="Output directory (default: inferred from storyboard file location)")

    parser.add_argument(
        "--stages", default="code,render,subtitles", help="Processing stages (default: code,render,subtitles)"
    )

    parser.add_argument(
        "--quality", "-q", default="l", choices=["l", "m", "h", "k"], help="Render quality (default: l)"
    )

    parser.add_argument("--project-name", "-p", help="Project name for final video filename")

    parser.add_argument("--no-concat", action="store_true", help="Skip final video concatenation")

    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose logging")

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logger.remove()
        logger.add(sys.stderr, level="DEBUG")

    try:
        # 解析分镜编号
        scene_numbers = parse_scene_numbers(args.scenes)
        logger.info(f"🎯 Target scenes: {scene_numbers}")

        # 创建重新生成器
        regenerator = SceneRegenerator(
            storyboard_file=args.storyboard,
            output_dir=args.output_dir,
            quality=args.quality,
            project_name=args.project_name,
        )

        # 重新生成指定分镜
        success = regenerator.regenerate_scenes(scene_numbers=scene_numbers, stages=args.stages)

        if not success:
            logger.error("❌ Scene regeneration failed")
            return 1

        # 创建最终视频（除非用户指定跳过）
        if not args.no_concat:
            final_video = regenerator.create_final_video(regenerated_scenes=scene_numbers)
            if final_video:
                print(f"\n🎉 Success! Final video: {final_video}")
            else:
                logger.warning("⚠️  Scene regeneration succeeded but final video creation failed")
                return 1
        else:
            logger.info("ℹ️  Skipping final video concatenation as requested")

        return 0

    except Exception as e:
        logger.error(f"❌ Error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
