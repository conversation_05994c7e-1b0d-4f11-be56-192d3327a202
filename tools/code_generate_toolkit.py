import datetime
import json
import os
import re
import shutil
import subprocess
import tempfile
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import yaml
from camel.agents import ChatAgent
from camel.toolkits.base import BaseToolkit
from camel.toolkits.function_tool import FunctionTool
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.types import ModelPlatformType
from loguru import logger


class CodeGenerateToolkit(BaseToolkit):
    r"""基于开源项目和用户需求生成代码的工具包。"""

    def __init__(self, config_path: str = "config/config.yaml", timeout: Optional[float] = None) -> None:
        r"""初始化代码生成工具包。
        
        Args:
            config_path: 配置文件路径
            timeout: 请求超时时间
        """
        super().__init__(timeout=timeout)
        self.load_config(config_path)
        
        # 仓库相关属性初始化为None或空值
        self.repo_path = None
        self.project_name = ""
        self.repo_url = ""
        self.temp_dir = None  # 用于存储克隆的临时仓库
        self.has_repo = False  # 标记是否有仓库
        
        self.model = self._create_model()
        self.agent = self._create_agent()
        
        # 创建输出目录
        self.output_dir = Path(f"output/codegen")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"代码生成工具包初始化完成")
        
        # 从code_generation配置中获取仓库信息
        repo_path = self.codegen_config.get("repo_path")
        repo_url = self.codegen_config.get("repo_url")
        
        # 设置仓库（如果配置中有）
        if repo_path and Path(repo_path).exists():
            self.set_repo_path(repo_path)
            logger.info(f"从配置加载仓库路径: {repo_path}")
        elif repo_url:
            self.set_repo_url(repo_url)
            logger.info(f"从配置加载仓库URL: {repo_url}")
        else:
            logger.info("配置中未指定仓库信息，将不使用仓库")

    def load_config(self, config_path: str):
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
        """
        with open(config_path, encoding="utf-8") as f:
            self.config = yaml.safe_load(f)
        self.model_config = self.config.get("model", {})
        self.codegen_config = self.config.get("code_generation", {})
        logger.info(f"从 {config_path} 加载配置")

    def _create_model(self):
        """
        创建模型实例
        
        Returns:
            创建的模型实例
        """
        api_config = self.model_config.get("api", {})
        return ModelFactory.create(
            model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
            model_type=self.model_config.get("type", "google/gemini-2.5-flash-preview"),
            api_key=api_config.get("openai_compatibility_api_key"),
            url=api_config.get("openai_compatibility_api_base_url"),
        )

    def _create_agent(self):
        """
        创建聊天Agent
        
        Returns:
            创建的ChatAgent实例
        """
        system_message = """你是一位专业的代码工程师，擅长阅读和理解开源项目，并根据用户需求生成可运行的代码示例。
请遵循以下原则：
1. 生成的代码必须能够直接运行，包含必要的导入和依赖关系
2. 添加必要简洁的中文注释，使代码易于理解，不要长篇大论
3. 确保代码遵循项目的设计模式和编码风格
4. 代码尽量简洁，不要冗余
5. 理解并分析用户需求，并在代码实现中满足
6. 不需要try-except处理异常
7. 如果额外的参考文件是python文件，请遵循参考文件中的模型配置信息和日志打印方式(不要重新创建打印类)
8. 如果额外的参考文件是help文件，在使用对应类时必须遵循文件说明
"""
        return ChatAgent(model=self.model, system_message=system_message)

    def set_repo_url(self, repo_url: str):
        """
        设置GitHub仓库URL并克隆到临时目录
        
        Args:
            repo_url: GitHub仓库URL
        """
        self.repo_url = repo_url
        
        # 从URL中提取项目名称
        self.project_name = repo_url.rstrip("/").split("/")[-1]
        if self.project_name.endswith(".git"):
            self.project_name = self.project_name[:-4]
        
        self.has_repo = True  # 标记有仓库
        logger.info(f"设置仓库URL: {self.repo_url}, 项目名称: {self.project_name}")
        
    def set_repo_path(self, repo_path: str):
        """
        设置仓库路径
        
        Args:
            repo_path: 仓库本地路径
        """
        self.repo_path = Path(repo_path)
        self.project_name = self.repo_path.name
        self.has_repo = True  # 标记有仓库
        logger.info(f"设置仓库路径: {self.repo_path}")
    
    def clone_repo(self) -> bool:
        """
        克隆远程仓库到临时目录
        
        Returns:
            克隆是否成功
        """
        if not self.repo_url:
            logger.error("未设置仓库URL，无法克隆")
            return False
            
        # 清理之前的临时目录
        if self.temp_dir and Path(self.temp_dir).exists():
            shutil.rmtree(self.temp_dir)
            
        # 创建新的临时目录
        self.temp_dir = tempfile.mkdtemp(prefix=f"repo_{self.project_name}_")
        self.repo_path = Path(self.temp_dir)
        
        logger.info(f"开始克隆仓库 {self.repo_url} 到 {self.temp_dir}")
        
        try:
            # 克隆仓库
            result = subprocess.run(
                ["git", "clone", "--depth=1", self.repo_url, self.temp_dir],
                check=True,
                capture_output=True,
                text=True
            )
            logger.info(f"仓库克隆成功: {self.temp_dir}")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"克隆仓库失败: {e.stderr}")
            return False
    
    def collect_repo_info(self) -> Dict[str, Any]:
        """
        收集仓库信息，包括README和示例代码
        
        Returns:
            包含仓库信息的字典，如果没有设置仓库则返回空的基本信息
        """
        # 如果没有设置仓库，返回基本信息
        if not self.has_repo:
            logger.info("未设置仓库，跳过收集仓库信息")
            return {
                "project_name": "",
                "repo_url": "",
                "readme": "",
                "examples": [],
                "project_structure": {"important_files": [], "directory_structure": {"name": "", "type": "directory", "items": []}}
            }
            
        # 如果设置了远程URL但还没克隆，先克隆
        if self.repo_url and (not self.repo_path or not self.repo_path.exists()):
            if not self.clone_repo():
                return {"error": f"克隆仓库 {self.repo_url} 失败"}
        
        if not self.repo_path.exists():
            logger.error(f"仓库路径 {self.repo_path} 不存在")
            return {"error": f"仓库路径 {self.repo_path} 不存在"}
            
        repo_info = {
            "project_name": self.project_name,
            "repo_url": self.repo_url,
            "readme": self._get_readme(),
            "examples": self._collect_examples(),
            "project_structure": self._analyze_project_structure()
        }
        
        # 保存收集到的信息
        with open(self.output_dir / "repo_info.json", "w", encoding="utf-8") as f:
            json.dump(repo_info, f, ensure_ascii=False, indent=2)
            
        return repo_info
        
    def _get_readme(self) -> str:
        """
        获取README文件内容
        
        Returns:
            README文件内容，如果不存在则返回空字符串
        """
        if not self.has_repo or not self.repo_path:
            return ""
            
        readme_candidates = ["README.md", "Readme.md", "readme.md", "README.txt", "readme.txt"]
        for candidate in readme_candidates:
            readme_path = self.repo_path / candidate
            if readme_path.exists():
                try:
                    with open(readme_path, encoding="utf-8") as f:
                        content = f.read()
                    logger.info(f"成功读取 {readme_path}")
                    return content
                except Exception as e:
                    logger.warning(f"读取 {readme_path} 失败: {e}")
        
        logger.warning("未找到README文件")
        return ""
        
    def _collect_examples(self) -> List[Dict[str, Any]]:
        """
        收集示例代码
        
        Returns:
            示例代码列表
        """
        if not self.has_repo or not self.repo_path:
            return []
            
        examples = []
        example_dirs = ["examples", "example", "samples", "sample", "demo", "demos", "tutorial", "tutorials"]
        
        # 在第一层和第二层目录中查找示例目录
        found_examples_dir = None
        
        # 检查第一层
        for dirname in example_dirs:
            example_dir = self.repo_path / dirname
            if example_dir.exists() and example_dir.is_dir():
                found_examples_dir = example_dir
                break
                
        # 如果第一层没找到，检查第二层
        if not found_examples_dir:
            for subdir in self.repo_path.iterdir():
                if subdir.is_dir():
                    for dirname in example_dirs:
                        example_dir = subdir / dirname
                        if example_dir.exists() and example_dir.is_dir():
                            found_examples_dir = example_dir
                            break
                    if found_examples_dir:
                        break
        
        if not found_examples_dir:
            logger.warning("未找到示例目录")
            return examples
            
        logger.info(f"找到示例目录: {found_examples_dir}")
        
        # 收集示例
        for example_path in found_examples_dir.iterdir():
            example_info = {
                "name": example_path.name,
                "path": str(example_path.relative_to(self.repo_path)),
                "files": [],
                "readme": "",
            }
            
            # 如果是目录，收集其中的文件
            if example_path.is_dir():
                # 收集README
                for readme_name in ["README.md", "Readme.md", "readme.md"]:
                    readme_path = example_path / readme_name
                    if readme_path.exists():
                        with open(readme_path, encoding="utf-8") as f:
                            example_info["readme"] = f.read()
                        break
                
                # 收集示例文件
                for file_path in example_path.rglob("*"):
                    if file_path.is_file() and not file_path.name.startswith("."):
                        rel_path = str(file_path.relative_to(self.repo_path))
                        file_info = {
                            "name": file_path.name,
                            "path": rel_path,
                            "content": "",
                        }
                        
                        # 读取文件内容
                        try:
                            with open(file_path, encoding="utf-8") as f:
                                file_info["content"] = f.read()
                        except Exception as e:
                            logger.warning(f"读取文件 {file_path} 失败: {e}")
                            file_info["content"] = f"[读取失败: {str(e)}]"
                            
                        example_info["files"].append(file_info)
            
            # 如果是单个文件
            elif example_path.is_file() and not example_path.name.startswith("."):
                rel_path = str(example_path.relative_to(self.repo_path))
                file_info = {
                    "name": example_path.name,
                    "path": rel_path,
                    "content": "",
                }
                
                # 读取文件内容
                try:
                    with open(example_path, encoding="utf-8") as f:
                        file_info["content"] = f.read()
                except Exception as e:
                    logger.warning(f"读取文件 {example_path} 失败: {e}")
                    file_info["content"] = f"[读取失败: {str(e)}]"
                    
                example_info["files"].append(file_info)
            
            examples.append(example_info)
            
        return examples
        
    def _analyze_project_structure(self) -> Dict[str, Any]:
        """
        分析项目结构
        
        Returns:
            项目结构信息
        """
        if not self.has_repo or not self.repo_path:
            return {"important_files": [], "directory_structure": {"name": "", "type": "directory", "items": []}}
            
        # 收集重要文件和目录
        important_files = []
        
        # 查找setup.py, package.json等重要配置文件
        config_files = [
            "setup.py", "requirements.txt", "package.json", "Cargo.toml", 
            "pom.xml", "build.gradle", "CMakeLists.txt"
        ]
        
        for config_file in config_files:
            file_path = self.repo_path / config_file
            if file_path.exists():
                try:
                    with open(file_path, encoding="utf-8") as f:
                        content = f.read()
                    important_files.append({
                        "name": config_file,
                        "path": config_file,
                        "content": content
                    })
                except Exception as e:
                    logger.warning(f"读取文件 {file_path} 失败: {e}")
        
        # 分析目录结构，最多两层
        def analyze_dir(dir_path, max_depth=2, current_depth=0):
            if current_depth > max_depth:
                return {"name": dir_path.name, "type": "directory"}
                
            result = {
                "name": dir_path.name,
                "type": "directory",
                "items": []
            }
            
            try:
                for item in dir_path.iterdir():
                    if item.name.startswith("."):
                        continue
                        
                    if item.is_dir():
                        result["items"].append(analyze_dir(item, max_depth, current_depth + 1))
                    else:
                        result["items"].append({"name": item.name, "type": "file"})
            except Exception as e:
                logger.warning(f"读取目录 {dir_path} 失败: {e}")
                
            return result
            
        structure = analyze_dir(self.repo_path)
        
        return {
            "important_files": important_files,
            "directory_structure": structure
        }
        
    def _fix_code(self, code_file: str, error_output: str, original_prompt: str, repo_info: Dict[str, Any]) -> Tuple[bool, str]:
        """
        根据错误信息修复代码
        
        Args:
            code_file: 需要修复的代码文件路径
            error_output: 运行时的错误输出
            original_prompt: 原始的用户需求
            repo_info: 仓库信息
            
        Returns:
            (是否修复成功, 修复后的代码文件路径)
        """
        # 读取原始代码
        with open(code_file, 'r', encoding='utf-8') as f:
            original_code = f.read()
            
        # 构建修复提示
        fix_prompt = f"""
## 原始需求
{original_prompt}

## 生成的代码（运行失败）
```python
{original_code}
```

## 错误信息
{error_output}

请根据以上错误信息修复代码。修复时请注意：
1. 生成的代码必须能够直接运行，包含必要的导入和依赖关系
2. 添加必要简洁的中文注释，使代码易于理解，不要长篇大论
3. 确保代码遵循项目的设计模式和编码风格
4. 代码尽量简洁，不要冗余
5. 理解并分析用户需求，并在代码实现中满足
6. 不需要try-except处理异常
7. 如果额外的参考文件是python文件，请遵循参考文件中的模型配置信息和日志打印方式(不要重新创建打印类)
8. 如果额外的参考文件是help文件，在使用对应类时必须遵循文件说明

请在代码前面提供详细的中文解释，说明代码的功能和如何使用。
```python
# 修复后的代码
```
"""
        
        # 获取修复建议
        user_message = BaseMessage.make_user_message(role_name="User", content=fix_prompt)
        response = self.agent.step(user_message)
        fixed_code_content = response.msg.content
        
        # 提取修复后的代码
        fixed_blocks = re.findall(r"```python\s*([\s\S]*?)```", fixed_code_content)
        if not fixed_blocks:
            fixed_blocks = re.findall(r"```\s*([\s\S]*?)```", fixed_code_content)
            
        if not fixed_blocks:
            logger.error("无法从修复建议中提取代码")
            return False, code_file
            
        # 保存修复后的代码
        fixed_code = fixed_blocks[0].strip()
        fixed_file = str(Path(code_file).parent / f"fixed_{Path(code_file).name}")
        with open(fixed_file, 'w', encoding='utf-8') as f:
            f.write(fixed_code)
            
        # 验证修复后的代码
        run_result = self.run_generated_code(fixed_file)
        has_error =  "Error" in run_result or "Traceback" in run_result
        
        if not has_error:
            # 如果修复成功，替换原文件
            shutil.move(fixed_file, code_file)
            logger.info(f"代码修复成功: {code_file}")
            return True, code_file
        else:
            # 如果修复失败，删除临时文件
            if Path(fixed_file).exists():
                Path(fixed_file).unlink()
            logger.warning(f"代码修复失败: {code_file}")
            return False, code_file

    def generate_code(self, user_prompt: str, repo_info: Optional[Dict[str, Any]] = None, 
                   additional_files: Optional[List[Dict[str, str]]] = None) -> Dict[str, Any]:
        """
        根据用户需求和仓库信息生成代码
        
        Args:
            user_prompt: 用户需求描述
            repo_info: 仓库信息，如果为None则使用已收集的信息（如果有仓库）
            additional_files: 额外的文件列表，每个文件是一个字典，包含 'name', 'path' 和 'content' 键
            
        Returns:
            生成结果
        """
        # 只有在未提供repo_info且有仓库时，才收集仓库信息
        if repo_info is None:
            if self.has_repo:
                repo_info = self.collect_repo_info()
            else:
                # 创建一个空的仓库信息结构
                repo_info = {
                    "project_name": "",
                    "repo_url": "",
                    "readme": "",
                    "examples": [],
                    "project_structure": {"important_files": [], "directory_structure": {"name": "", "type": "directory", "items": []}}
                }
            
        # 构建提示
        prompt = f"""
## 用户需求
{user_prompt}
"""

        # 只有在有项目信息时才添加项目相关内容
        if repo_info.get("project_name") or repo_info.get("readme") or repo_info.get("examples") or repo_info.get("project_structure", {}).get("important_files"):
            prompt += f"""
## 项目信息
项目名称: {repo_info.get('project_name', '未知')}

## README内容
{repo_info.get('readme', '无README内容')}

## 示例代码
{self._format_examples_for_prompt(repo_info.get('examples', []))}

## 项目结构
{self._format_structure_for_prompt(repo_info.get('project_structure', {}))}
"""

        # 添加额外的文件内容
        if additional_files:
            prompt += "\n## 额外的参考文件\n"
            for file in additional_files:
                file_name = file.get('name', '未命名文件')
                file_path = file.get('path', '未知路径')
                file_content = file.get('content', '')
                
                if len(file_content) > 2000:
                    file_content = file_content[:2000] + "...(内容已截断)"
                    
                prompt += f"\n文件: {file_path}\n```\n{file_content}\n```\n"

        prompt += """
请根据以上信息，生成满足用户需求的代码。代码应该：
1. 生成的代码必须能够直接运行，包含必要的导入和依赖关系
2. 添加必要简洁的中文注释，使代码易于理解，不要长篇大论
3. 确保代码遵循项目的设计模式和编码风格
4. 代码尽量简洁，不要冗余
5. 理解理解并分析用户需求，并在代码实现中满足
6. 不需要try-except处理异常
7. 如果额外的参考文件是python文件，请遵循参考文件中的模型配置信息和日志打印方式(不要重新创建打印类)
8. 如果额外的参考文件是help文件，在使用对应类时必须遵循文件说明


代码输出格式:
```python
# 代码内容
```

请在代码前面提供详细的中文解释，说明代码的功能和如何使用。
"""
        
        # 记录提示
        with open(self.output_dir / "prompt.txt", "w", encoding="utf-8") as f:
            f.write(prompt)
            
        # 获取代码生成结果
        user_message = BaseMessage.make_user_message(role_name="User", content=prompt)
        response = self.agent.step(user_message)
        code_gen_content = response.msg.content
        
        # 记录原始回复
        with open(self.output_dir / "response.txt", "w", encoding="utf-8") as f:
            f.write(code_gen_content)
            
        # 保存生成的代码
        code_files = []
        max_retries = 3  # 最大重试次数
        
        # 首先尝试查找显式标记为Python的代码块
        python_blocks = re.findall(r"```python\s*([\s\S]*?)```", code_gen_content)
        
        # 如果没有找到明确标记为Python的代码块，则检查未标记语言的代码块
        if not python_blocks:
            # 查找所有代码块及其可能的语言标记
            all_blocks = re.findall(r"```(\w*)\s*([\s\S]*?)```", code_gen_content)
            for lang, block in all_blocks:
                # 如果没有指定语言或者语言是python，则保存
                if not lang or lang.lower() == 'python':
                    python_blocks.append(block)
        
        # 保存所有Python代码块
        for i, code_block in enumerate(python_blocks):
            code_file = self.output_dir / f"generated_code_{i+1}.py"
            with open(code_file, "w", encoding="utf-8") as f:
                f.write(code_block.strip())
            code_files.append(str(code_file))
            
        # 验证生成的代码，并在失败时尝试修复
        verification_results = []
        for code_file in code_files:
            logger.info(f"验证代码文件: {code_file}")
            retry_count = 0
            is_success = False
            
            while retry_count < max_retries and not is_success:
                if retry_count > 0:
                    logger.info(f"第 {retry_count} 次尝试修复代码: {code_file}")
                    
                run_result = self.run_generated_code(code_file)
                has_error =  "Error" in run_result or "Traceback" in run_result
                
                if not has_error:
                    is_success = True
                    logger.info(f"代码验证成功: {code_file}")
                else:
                    if retry_count < max_retries - 1:  # 如果还有重试机会
                        is_success, code_file = self._fix_code(code_file, run_result, user_prompt, repo_info)
                        if is_success:
                            logger.info(f"代码修复成功: {code_file}")
                        else:
                            logger.warning(f"代码修复失败，将进行第 {retry_count + 2} 次尝试")
                    
                retry_count += 1
                
            verification_results.append({
                "file": code_file,
                "output": run_result,
                "success": is_success,
                "retry_count": retry_count - 1  # 减去初始尝试
            })
            
        # 保存所有结果
        result = {
            "user_prompt": user_prompt,
            "code_files": code_files,
            "verification_results": verification_results,
            "response": code_gen_content,
            "timestamp": datetime.datetime.now().isoformat()
        }
        
        with open(self.output_dir / "result.json", "w", encoding="utf-8") as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
            
        return result
        
    def _format_examples_for_prompt(self, examples: List[Dict[str, Any]]) -> str:
        """
        将示例格式化为提示信息
        
        Args:
            examples: 示例列表
            
        Returns:
            格式化后的字符串
        """
        if not examples:
            return "无示例代码"
            
        result = []
        
        # 只选择前3个示例，避免提示过长
        for i, example in enumerate(examples[:3]):
            example_text = f"示例 {i+1}: {example.get('name', '')}\n"
            
            if example.get("readme"):
                example_text += f"说明文档:\n{example['readme'][:500]}...\n\n"
                
            # 显示最多3个文件
            for j, file in enumerate(example.get("files", [])[:3]):
                file_content = file.get("content", "")
                # 限制文件内容长度
                if len(file_content) > 1000:
                    file_content = file_content[:1000] + "...(内容已截断)"
                    
                example_text += f"文件 {j+1}: {file.get('path', '')}\n```\n{file_content}\n```\n\n"
                
            result.append(example_text)
            
        return "\n".join(result)
        
    def _format_structure_for_prompt(self, structure: Dict[str, Any]) -> str:
        """
        将项目结构格式化为提示信息
        
        Args:
            structure: 项目结构信息
            
        Returns:
            格式化后的字符串
        """
        result = []
        
        # 添加重要文件内容
        important_files = structure.get("important_files", [])
        if important_files:
            result.append("重要配置文件:")
            for file in important_files:
                file_content = file.get("content", "")
                # 限制文件内容长度
                if len(file_content) > 500:
                    file_content = file_content[:500] + "...(内容已截断)"
                    
                result.append(f"文件名: {file.get('name', '')}\n```\n{file_content}\n```\n")
                
        # 添加目录结构
        dir_structure = structure.get("directory_structure", {})
        if dir_structure:
            result.append("目录结构:")
            
            def format_dir(dir_info, indent=0):
                lines = []
                name = dir_info.get("name", "")
                lines.append("  " * indent + f"📁 {name}")
                
                for item in dir_info.get("items", []):
                    if item.get("type") == "directory":
                        lines.extend(format_dir(item, indent + 1))
                    else:
                        lines.append("  " * (indent + 1) + f"📄 {item.get('name', '')}")
                        
                return lines
                
            result.extend(format_dir(dir_structure))
            
        return "\n".join(result)
        
    def run_generated_code(self, code_file_path: str) -> str:
        """
        运行生成的代码
        
        Args:
            code_file_path: 代码文件路径
            
        Returns:
            运行结果
        """
        try:
            # 获取相对路径和模块名
            code_path = Path(code_file_path)
            # 将路径转换为模块路径 (将 / 替换为 .)
            module_path = str(code_path.with_suffix('')).replace('/', '.').replace('\\', '.')
            
            logger.info(f"以模块方式运行: python3 -m {module_path}")
            
            result = subprocess.run(
                ["python3", "-m", module_path], 
                capture_output=True, 
                text=True, 
                check=False,
                timeout=60
            )
            
            output = f"--- 标准输出 ---\n{result.stdout}\n\n--- 错误输出 ---\n{result.stderr}\n"
            
            # 保存运行结果
            run_results_file = self.output_dir / "run_results.txt"
            with open(run_results_file, "a", encoding="utf-8") as f:
                f.write(f"\n\n=== 运行模块: {module_path} ===\n")
                f.write(output)
                
            return output
        except Exception as e:
            error_msg = f"运行代码出错: {e}"
            logger.error(error_msg)
            return error_msg

    def get_tools(self) -> list[FunctionTool]:
        r"""返回工具包中的功能工具列表。

        Returns:
            List[FunctionTool]: 代表工具包中功能的FunctionTool对象列表
        """
        return [
            FunctionTool(self.set_repo_url),
            FunctionTool(self.set_repo_path),
            FunctionTool(self.collect_repo_info),
            FunctionTool(self.generate_code),
        ]

    def code_generate_verify(self, config_path: str) -> Dict[str, Any]:
        """
        根据配置文件执行代码生成、验证和输出处理的完整流程
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            Dict[str, Any]: 包含生成结果的字典
        """
        # 从配置文件加载
        with open(config_path, encoding="utf-8") as f:
            config = yaml.safe_load(f)
        
        # 获取code_generation配置参数
        code_gen_config = config.get("code_generation", {})
        prompt = code_gen_config.get("prompt")
        reference_files = code_gen_config.get("reference_files", [])
        
        if not prompt:
            logger.warning("配置中未找到提示信息(prompt)，请在配置文件中添加或直接传递参数给generate_code方法")
            return {"error": "未提供提示信息"}
            
        # 准备额外参考文件
        additional_files = []
        for file_path in reference_files:
            try:
                path_obj = Path(file_path)
                if path_obj.exists():
                    with open(path_obj, 'r', encoding='utf-8') as f:
                        content = f.read()
                    additional_files.append({
                        'name': path_obj.name,
                        'path': file_path,
                        'content': content
                    })
                    logger.info(f"已添加参考文件: {file_path}")
                else:
                    logger.warning(f"警告: 文件 {file_path} 不存在，已跳过")
            except Exception as e:
                logger.error(f"读取文件 {file_path} 失败: {e}")
        
        # 生成代码
        logger.info("开始生成代码...")
        result = self.generate_code(prompt, additional_files=additional_files)
        logger.info(f"生成的代码文件: {result['code_files']}")
        logger.info(f"输出目录: {self.output_dir}")
        
        # 处理输出文件
        output_md = code_gen_config.get("output_md")
        output_py = code_gen_config.get("output_py")
        
        # 处理 markdown 输出
        if output_md:
            md_path = Path(output_md)
            md_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(md_path, "w", encoding="utf-8") as f:
                f.write(f"# 代码生成结果\n\n")
                f.write(f"## 用户需求\n\n{prompt}\n\n")
                
                # 添加模型原始输出
                f.write(f"## Code Agent输出\n\n```\n{result['response']}\n```\n\n")
                
                # 添加运行日志
                f.write(f"## 运行日志\n\n")
                for verification in result.get("verification_results", []):
                    file_path = verification.get("file", "")
                    success = verification.get("success", False)
                    retry_count = verification.get("retry_count", 0)
                    output = verification.get("output", "")
                    
                    status = "成功" if success else "失败"
                    retry_info = f"（经过 {retry_count} 次修复）" if retry_count > 0 else ""
                
                    f.write("```\n")
                    f.write(output)
                    f.write("\n```\n\n")
                
                # 添加例子运行录屏（如果配置中有指定视频文件）
                video_file = code_gen_config.get("video_file")
                if video_file:
                    f.write(f"## 录屏视频路径\n\n")
                    f.write(f"{video_file}\n\n")
            
            logger.info(f"生成的文档已保存到: {output_md}")
        
        # 处理 Python 文件输出
        if output_py and result["code_files"]:
            # 使用第一个成功的代码文件
            successful_files = [v["file"] for v in result["verification_results"] if v["success"]]
            source_file = successful_files[0] if successful_files else result["code_files"][0]
            
            py_path = Path(output_py)
            py_path.parent.mkdir(parents=True, exist_ok=True)
            
            try:
                shutil.copy(source_file, py_path)
                logger.info(f"生成的Python文件已保存到: {output_py}")
            except Exception as e:
                logger.error(f"保存Python文件失败: {str(e)}")
                
        return result

    def __del__(self):
        """
        析构函数，清理临时目录
        """
        if self.temp_dir and Path(self.temp_dir).exists():
            try:
                shutil.rmtree(self.temp_dir)
                logger.info(f"已清理临时目录: {self.temp_dir}")
            except Exception as e:
                logger.warning(f"清理临时目录 {self.temp_dir} 失败: {e}")


if __name__ == "__main__":
    # 示例用法
    config_path = "config/config.yaml"
    
    # 初始化工具包（会自动使用配置中的仓库路径）
    toolkit = CodeGenerateToolkit(config_path=config_path)
    
    # 执行代码生成、验证和输出处理
    result = toolkit.code_generate_verify(config_path)
    
    # 显示代码验证和修复结果
    print("\n代码验证和修复结果:")
    for verification in result.get("verification_results", []):
        file_path = verification.get("file", "")
        success = verification.get("success", False)
        retry_count = verification.get("retry_count", 0)
        
        # 显示文件状态
        status = "成功" if success else "失败"
        retry_info = f"（经过 {retry_count} 次修复）" if retry_count > 0 else ""
        print(f"- {Path(file_path).name}: {status}{retry_info}") 