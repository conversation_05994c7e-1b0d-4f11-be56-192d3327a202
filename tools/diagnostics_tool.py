#!/usr/bin/env python3
"""
完整的Python代码诊断工具实现
支持语法检查、导入分析、未定义变量检测、代码质量检查等
"""

import ast
import builtins
import importlib
import importlib.util
import os
import sys
from dataclasses import dataclass
from enum import Enum
from typing import Any, Optional


class DiagnosticSeverity(Enum):
    """诊断严重程度"""

    ERROR = "error"
    WARNING = "warning"
    INFO = "info"
    HINT = "hint"


@dataclass
class Diagnostic:
    """诊断结果数据类"""

    file_path: str
    line_start: int
    line_end: int
    column_start: int
    column_end: int
    severity: DiagnosticSeverity
    message: str
    code: Optional[str] = None
    source: Optional[str] = None


class ImportChecker:
    """导入检查器"""

    def __init__(self):
        self.additional_paths = []
        self.project_root = None

    def check(self, file_path: str, content: str, tree: ast.AST) -> list[Diagnostic]:
        """检查导入相关问题"""
        diagnostics = []

        # 首先分析代码中的 sys.path 修改
        self._analyze_sys_path_modifications(tree, file_path)

        # 如果没有找到明确的路径修改，添加文件所在目录和当前工作目录
        import os

        if not self.additional_paths:
            file_dir = os.path.dirname(os.path.abspath(file_path))
            cwd = os.getcwd()

            # 添加文件所在目录
            if file_dir not in self.additional_paths:
                self.additional_paths.append(file_dir)

            # 添加当前工作目录（如果不同）
            if cwd != file_dir and cwd not in self.additional_paths:
                self.additional_paths.append(cwd)

            if not self.project_root:
                self.project_root = cwd

        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    if not self._can_import(alias.name):
                        diagnostic = Diagnostic(
                            file_path=file_path,
                            line_start=node.lineno,
                            line_end=node.lineno,
                            column_start=node.col_offset,
                            column_end=node.col_offset + len(alias.name),
                            severity=DiagnosticSeverity.ERROR,
                            message=f'Cannot resolve import "{alias.name}"',
                            code="import-error",
                            source="imports",
                        )
                        diagnostics.append(diagnostic)

            elif isinstance(node, ast.ImportFrom):
                if node.module and not self._can_import(node.module):
                    diagnostic = Diagnostic(
                        file_path=file_path,
                        line_start=node.lineno,
                        line_end=node.lineno,
                        column_start=node.col_offset,
                        column_end=node.col_offset + len(node.module),
                        severity=DiagnosticSeverity.ERROR,
                        message=f'Cannot resolve import "{node.module}"',
                        code="import-error",
                        source="imports",
                    )
                    diagnostics.append(diagnostic)

        return diagnostics

    def _analyze_sys_path_modifications(self, tree: ast.AST, file_path: str):
        """分析代码中的 sys.path 修改，提取可能的项目路径"""
        import os

        # 首先收集变量赋值，特别是路径相关的
        path_variables = {}

        for node in ast.walk(tree):
            # 收集可能的路径变量赋值
            if isinstance(node, ast.Assign):
                for target in node.targets:
                    if isinstance(target, ast.Name):
                        var_name = target.id
                        if "path" in var_name.lower() or "root" in var_name.lower():
                            resolved_path = self._resolve_path_expression(node.value, file_path)
                            if resolved_path:
                                path_variables[var_name] = resolved_path

        for node in ast.walk(tree):
            # 查找 sys.path.append() 调用
            if (
                isinstance(node, ast.Call)
                and isinstance(node.func, ast.Attribute)
                and isinstance(node.func.value, ast.Attribute)
                and isinstance(node.func.value.value, ast.Name)
                and node.func.value.value.id == "sys"
                and node.func.value.attr == "path"
                and node.func.attr == "append"
            ):
                # 尝试解析添加的路径
                if node.args:
                    path_arg = node.args[0]
                    resolved_path = None

                    # 如果是变量引用，查找变量值
                    if (
                        isinstance(path_arg, ast.Call)
                        and isinstance(path_arg.func, ast.Name)
                        and path_arg.func.id == "str"
                        and len(path_arg.args) == 1
                        and isinstance(path_arg.args[0], ast.Name)
                    ):
                        var_name = path_arg.args[0].id
                        if var_name in path_variables:
                            resolved_path = path_variables[var_name]
                    else:
                        resolved_path = self._resolve_path_expression(path_arg, file_path)

                    if resolved_path and os.path.exists(resolved_path):
                        self.additional_paths.append(resolved_path)
                        if not self.project_root:
                            self.project_root = resolved_path

    def _resolve_path_expression(self, node: ast.AST, file_path: str) -> Optional[str]:
        """尝试解析路径表达式"""
        import os

        try:
            # 处理 Path(__file__).resolve().parent 这种模式
            if isinstance(node, ast.Attribute) and node.attr == "parent":
                # 检查是否是 Path(__file__).resolve().parent 模式
                if (
                    isinstance(node.value, ast.Call)
                    and isinstance(node.value.func, ast.Attribute)
                    and node.value.func.attr == "resolve"
                ):
                    # 进一步检查是否是 Path(__file__)
                    path_call = node.value.value
                    if (
                        isinstance(path_call, ast.Call)
                        and isinstance(path_call.func, ast.Name)
                        and path_call.func.id == "Path"
                        and len(path_call.args) == 1
                        and isinstance(path_call.args[0], ast.Name)
                        and path_call.args[0].id == "__file__"
                    ):
                        # 这是 Path(__file__).resolve().parent
                        current_file_dir = os.path.dirname(os.path.abspath(file_path))
                        return current_file_dir

            # 处理 str(Path(__file__).resolve().parent) 这种模式
            elif (
                isinstance(node, ast.Call)
                and isinstance(node.func, ast.Name)
                and node.func.id == "str"
                and len(node.args) == 1
            ):
                inner_node = node.args[0]
                # 递归解析内部表达式
                return self._resolve_path_expression(inner_node, file_path)

            # 处理简单的字符串字面量
            elif isinstance(node, ast.Constant) and isinstance(node.value, str):
                path = node.value
                if os.path.isabs(path):
                    return path
                else:
                    # 相对路径，相对于当前文件
                    current_file_dir = os.path.dirname(os.path.abspath(file_path))
                    return os.path.join(current_file_dir, path)

        except Exception:
            pass

        return None

    def _can_import(self, module_name: str) -> bool:
        """检查模块是否可以导入"""
        try:
            # 检查标准库
            if module_name in sys.stdlib_module_names:
                return True

            # 尝试查找模块
            try:
                spec = importlib.util.find_spec(module_name)
                if spec is not None:
                    return True
            except (ImportError, ValueError, ModuleNotFoundError):
                pass

            # 如果标准查找失败，尝试在额外路径中查找
            if self.additional_paths:
                original_path = sys.path.copy()
                try:
                    # 临时添加额外路径
                    for path in self.additional_paths:
                        if path not in sys.path:
                            sys.path.insert(0, path)

                    # 再次尝试查找
                    try:
                        spec = importlib.util.find_spec(module_name)
                        if spec is not None:
                            return True
                    except (ImportError, ValueError, ModuleNotFoundError):
                        pass

                finally:
                    # 恢复原始路径
                    sys.path[:] = original_path

            # 最后尝试：直接检查文件是否存在
            return self._check_module_file_exists(module_name)

        except (ImportError, ValueError, ModuleNotFoundError):
            return False

    def _check_module_file_exists(self, module_name: str) -> bool:
        """直接检查模块文件是否存在"""
        import os

        # 将模块名转换为文件路径
        module_parts = module_name.split(".")

        # 在所有可能的路径中查找
        search_paths = self.additional_paths + [os.getcwd()]

        for base_path in search_paths:
            # 构建模块路径
            module_path = os.path.join(base_path, *module_parts)

            # 检查是否存在 __init__.py（包）
            init_file = os.path.join(module_path, "__init__.py")
            if os.path.exists(init_file):
                return True

            # 检查是否存在 .py 文件（模块）
            py_file = module_path + ".py"
            if os.path.exists(py_file):
                return True

        return False


class DynamicSymbolResolver:
    """动态符号解析器 - 使用动态策略处理import *"""

    def __init__(self):
        self.symbol_table: dict[str, bool] = {}  # name -> is_defined
        self.star_import_modules: list[str] = []
        self.module_cache: dict[str, Any] = {}
        self.builtin_names = set(dir(builtins))

        # 添加常见的全局名称
        self.builtin_names.update(
            ["__name__", "__file__", "__doc__", "__package__", "__spec__", "__loader__", "__cached__"]
        )

    def resolve_symbols(self, tree: ast.AST) -> dict[str, bool]:
        """解析代码中的所有符号"""
        # 第一遍：收集导入和局部定义
        self._collect_definitions(tree)

        # 第二遍：解析星号导入的符号
        self._resolve_star_imports()

        return self.symbol_table

    def _collect_definitions(self, tree: ast.AST):
        """收集所有定义的符号"""
        for node in ast.walk(tree):
            # 处理赋值
            if isinstance(node, ast.Assign):
                for target in node.targets:
                    self._handle_assignment_target(target)

            # 处理类型注解赋值
            elif isinstance(node, ast.AnnAssign):
                self._handle_assignment_target(node.target)

            # 处理函数定义
            elif isinstance(node, ast.FunctionDef):
                self.symbol_table[node.name] = True
                # 函数参数
                for arg in node.args.args:
                    self.symbol_table[arg.arg] = True
                # **kwargs 参数
                if node.args.kwarg:
                    self.symbol_table[node.args.kwarg.arg] = True
                # *args 参数
                if node.args.vararg:
                    self.symbol_table[node.args.vararg.arg] = True

            # 处理类定义
            elif isinstance(node, ast.ClassDef):
                self.symbol_table[node.name] = True

            # 处理for循环变量
            elif isinstance(node, ast.For):
                self._handle_assignment_target(node.target)

            # 处理列表推导式和生成器表达式
            elif isinstance(node, (ast.ListComp, ast.SetComp, ast.DictComp, ast.GeneratorExp)):
                for generator in node.generators:
                    self._handle_assignment_target(generator.target)

            # 处理导入
            elif isinstance(node, ast.Import):
                for alias in node.names:
                    name = alias.asname if alias.asname else alias.name
                    self.symbol_table[name] = True
                    # 对于 import a.b.c 形式，也要添加 a 到符号表
                    if "." in alias.name and not alias.asname:
                        root_module = alias.name.split(".")[0]
                        self.symbol_table[root_module] = True

            elif isinstance(node, ast.ImportFrom):
                if node.names[0].name == "*":
                    # 星号导入
                    if node.module:
                        self.star_import_modules.append(node.module)
                else:
                    # 具体导入
                    for alias in node.names:
                        name = alias.asname if alias.asname else alias.name
                        self.symbol_table[name] = True

    def _handle_assignment_target(self, target):
        """处理赋值目标（包括解包）"""
        if isinstance(target, ast.Name):
            self.symbol_table[target.id] = True
        elif isinstance(target, ast.Tuple) or isinstance(target, ast.List):
            for elt in target.elts:
                self._handle_assignment_target(elt)

    def _resolve_star_imports(self):
        """解析星号导入的符号"""
        for module_name in self.star_import_modules:
            try:
                module = self._get_module(module_name)
                if module is None:
                    continue

                # 获取导出的名称
                exported_names = self._get_exported_names(module)

                for name in exported_names:
                    self.symbol_table[name] = True

            except Exception:
                # 导入失败时忽略
                continue

    def _get_module(self, module_name: str) -> Optional[Any]:
        """获取模块（带缓存）"""
        if module_name in self.module_cache:
            return self.module_cache[module_name]

        try:
            module = importlib.import_module(module_name)
            self.module_cache[module_name] = module
            return module
        except ImportError:
            self.module_cache[module_name] = None
            return None

    def _get_exported_names(self, module: Any) -> list[str]:
        """获取模块导出的名称"""
        if hasattr(module, "__all__"):
            return list(module.__all__)
        else:
            return [name for name in dir(module) if not name.startswith("_")]

    def is_symbol_defined(self, name: str) -> bool:
        """检查符号是否已定义"""
        # 检查内置名称
        if name in self.builtin_names:
            return True

        # 检查符号表
        return self.symbol_table.get(name, False)


class VariableChecker:
    """变量检查器 - 使用动态策略检查未定义和未使用的变量"""

    def check(self, file_path: str, content: str, tree: ast.AST) -> list[Diagnostic]:
        """检查变量相关问题"""
        diagnostics = []

        # 使用动态符号解析器
        resolver = DynamicSymbolResolver()
        resolver.resolve_symbols(tree)

        # 分析变量使用情况
        analyzer = VariableAnalyzer(resolver)
        analyzer.visit(tree)

        # 检查未定义的变量
        for name, locations in analyzer.undefined_vars.items():
            for lineno, col_offset in locations:
                diagnostic = Diagnostic(
                    file_path=file_path,
                    line_start=lineno,
                    line_end=lineno,
                    column_start=col_offset,
                    column_end=col_offset + len(name),
                    severity=DiagnosticSeverity.ERROR,
                    message=f'Undefined name "{name}"',
                    code="undefined-variable",
                    source="variables",
                )
                diagnostics.append(diagnostic)

        # 完成分析
        analyzer.finalize()

        # 检查未使用的变量
        for name, location in analyzer.unused_vars.items():
            lineno, col_offset = location
            diagnostic = Diagnostic(
                file_path=file_path,
                line_start=lineno,
                line_end=lineno,
                column_start=col_offset,
                column_end=col_offset + len(name),
                severity=DiagnosticSeverity.WARNING,
                message=f'Unused variable "{name}"',
                code="unused-variable",
                source="variables",
            )
            diagnostics.append(diagnostic)

        return diagnostics


class VariableAnalyzer(ast.NodeVisitor):
    """AST访问器，用于分析变量使用情况"""

    def __init__(self, resolver: DynamicSymbolResolver):
        self.resolver = resolver
        self.used_vars = set()
        self.undefined_vars = {}  # name -> [(lineno, col_offset)]
        self.unused_vars = {}  # name -> (lineno, col_offset)
        self.assignments = {}  # name -> (lineno, col_offset)

        # 作用域栈，用于跟踪变量的作用域
        self.scope_stack = [set()]  # 每个作用域是一个变量名集合
        self.current_scope_assignments = {}  # 当前作用域的赋值

        # 跟踪方法定义
        self.method_definitions = set()  # 存储方法名
        self.in_class = False  # 是否在类定义中

    def _is_variable_defined_in_scope(self, name: str) -> bool:
        """检查变量是否在当前作用域链中定义"""
        # 检查内置名称
        if self.resolver.is_symbol_defined(name):
            return True

        # 检查作用域栈中的变量
        for scope in reversed(self.scope_stack):
            if name in scope:
                return True

        # 检查全局赋值
        return name in self.assignments

    def visit_Name(self, node):
        """访问名称节点"""
        if isinstance(node.ctx, ast.Store):
            # 变量赋值
            self.assignments[node.id] = (node.lineno, node.col_offset)
            # 添加到当前作用域
            if self.scope_stack:
                self.scope_stack[-1].add(node.id)
        elif isinstance(node.ctx, ast.Load):
            # 变量使用
            self.used_vars.add(node.id)

            # 检查变量是否定义
            if not self._is_variable_defined_in_scope(node.id):
                if node.id not in self.undefined_vars:
                    self.undefined_vars[node.id] = []
                self.undefined_vars[node.id].append((node.lineno, node.col_offset))

        self.generic_visit(node)

    def visit_Attribute(self, node):
        """访问属性节点，如 a.b.c"""
        # 对于属性访问，我们需要标记根对象为已使用
        # 例如：concurrent.futures.ThreadPoolExecutor 中的 concurrent
        root_name = self._get_root_name(node)
        if root_name:
            self.used_vars.add(root_name)

        self.generic_visit(node)

    def _get_root_name(self, node):
        """获取属性访问链的根名称"""
        while isinstance(node, ast.Attribute):
            node = node.value
        if isinstance(node, ast.Name):
            return node.id
        return None

    def visit_Import(self, node):
        """访问import语句"""
        for alias in node.names:
            name = alias.asname if alias.asname else alias.name
            self.assignments[name] = (node.lineno, node.col_offset)
            # 对于 import a.b.c 形式，也要添加 a 到赋值表
            if "." in alias.name and not alias.asname:
                root_module = alias.name.split(".")[0]
                self.assignments[root_module] = (node.lineno, node.col_offset)
        self.generic_visit(node)

    def visit_ImportFrom(self, node):
        """访问from import语句"""
        if node.names[0].name != "*":  # 不处理星号导入
            for alias in node.names:
                name = alias.asname if alias.asname else alias.name
                self.assignments[name] = (node.lineno, node.col_offset)
        self.generic_visit(node)

    def visit_FunctionDef(self, node):
        """访问函数定义"""
        self.assignments[node.name] = (node.lineno, node.col_offset)
        if self.scope_stack:
            self.scope_stack[-1].add(node.name)

        # 如果在类中，这是一个方法
        if self.in_class:
            self.method_definitions.add(node.name)

        # 进入函数作用域
        self.scope_stack.append(set())

        # 处理函数参数
        for arg in node.args.args:
            self.scope_stack[-1].add(arg.arg)
        if node.args.kwarg:
            self.scope_stack[-1].add(node.args.kwarg.arg)
        if node.args.vararg:
            self.scope_stack[-1].add(node.args.vararg.arg)

        self.generic_visit(node)

        # 退出函数作用域
        self.scope_stack.pop()

    def visit_Lambda(self, node):
        """访问lambda函数定义"""
        # 进入lambda作用域
        self.scope_stack.append(set())

        # 处理lambda参数
        for arg in node.args.args:
            self.scope_stack[-1].add(arg.arg)
        if node.args.kwarg:
            self.scope_stack[-1].add(node.args.kwarg.arg)
        if node.args.vararg:
            self.scope_stack[-1].add(node.args.vararg.arg)

        # 访问lambda体（单个表达式）
        self.visit(node.body)

        # 退出lambda作用域
        self.scope_stack.pop()

    def visit_ClassDef(self, node):
        """访问类定义"""
        self.assignments[node.name] = (node.lineno, node.col_offset)
        if self.scope_stack:
            self.scope_stack[-1].add(node.name)

        # 进入类作用域
        old_in_class = self.in_class
        self.in_class = True
        self.scope_stack.append(set())
        self.generic_visit(node)
        # 退出类作用域
        self.scope_stack.pop()
        self.in_class = old_in_class

    def visit_ExceptHandler(self, node):
        """访问异常处理器"""
        if node.name:
            # except Exception as e: 中的 e 变量
            if hasattr(node.name, "id"):  # Python 3.x
                var_name = node.name.id
            else:  # 可能是字符串（较老版本）
                var_name = node.name

            # 将异常变量添加到当前作用域
            if self.scope_stack:
                self.scope_stack[-1].add(var_name)
            self.assignments[var_name] = (node.lineno, node.col_offset)

        self.generic_visit(node)

    def visit_With(self, node):
        """访问with语句"""
        # 处理 with 语句中的变量绑定
        for item in node.items:
            if item.optional_vars:
                self._handle_assignment_target(item.optional_vars)

        self.generic_visit(node)

    def visit_AugAssign(self, node):
        """访问增强赋值 (+=, -=, etc.)"""
        if isinstance(node.target, ast.Name):
            # 增强赋值既是使用也是赋值
            self.used_vars.add(node.target.id)
            self.assignments[node.target.id] = (node.lineno, node.col_offset)
            if self.scope_stack:
                self.scope_stack[-1].add(node.target.id)

        self.generic_visit(node)

    def visit_AnnAssign(self, node):
        """访问类型注解赋值 (x: int = 5 或 x: int)"""
        # 处理赋值目标
        if isinstance(node.target, ast.Name):
            # 类型注解赋值定义了一个变量
            self.assignments[node.target.id] = (node.lineno, node.col_offset)
            if self.scope_stack:
                self.scope_stack[-1].add(node.target.id)
        else:
            # 复杂目标（如属性、下标等）
            self._handle_assignment_target(node.target)

        # 如果有值，也需要访问它（可能引用其他变量）
        if node.value:
            self.visit(node.value)

        # 访问注解（可能引用类型变量）
        if node.annotation:
            self.visit(node.annotation)

    def _handle_assignment_target(self, target):
        """处理赋值目标（包括解包）"""
        if isinstance(target, ast.Name):
            self.assignments[target.id] = (getattr(target, "lineno", 0), getattr(target, "col_offset", 0))
            if self.scope_stack:
                self.scope_stack[-1].add(target.id)
        elif isinstance(target, ast.Tuple) or isinstance(target, ast.List):
            for elt in target.elts:
                self._handle_assignment_target(elt)

    def finalize(self):
        """完成分析，计算未使用的变量"""
        # 排除一些特殊情况
        exclude_patterns = {
            "self",
            "cls",  # 方法参数
            "__init__",
            "__new__",
            "__del__",  # 特殊方法
            "construct",  # manim特殊方法
        }

        for name, location in self.assignments.items():
            # 检查是否是方法定义
            is_method = name in self.method_definitions

            # 检查是否是通过根模块使用的导入
            is_used_via_root = self._is_used_via_root_module(name)

            if (
                name not in self.used_vars
                and not name.startswith("_")
                and name not in exclude_patterns
                and not is_method
                and not is_used_via_root
            ):
                self.unused_vars[name] = location

    def _is_used_via_root_module(self, name: str) -> bool:
        """检查是否通过根模块使用了这个导入"""
        # 对于 import a.b.c 形式的导入，如果 a 被使用了，那么 a.b.c 也算被使用
        if "." in name:
            root_module = name.split(".")[0]
            return root_module in self.used_vars
        return False


class CodeQualityChecker:
    """代码质量检查器"""

    def check(self, file_path: str, content: str, tree: ast.AST) -> list[Diagnostic]:
        """检查代码质量问题"""
        diagnostics = []
        lines = content.splitlines()

        for i, line in enumerate(lines, 1):
            # 检查行长度
            if len(line) > 88:  # PEP 8建议88字符
                diagnostic = Diagnostic(
                    file_path=file_path,
                    line_start=i,
                    line_end=i,
                    column_start=89,
                    column_end=len(line),
                    severity=DiagnosticSeverity.WARNING,
                    message=f"Line too long ({len(line)} > 88 characters)",
                    code="line-too-long",
                    source="quality",
                )
                diagnostics.append(diagnostic)

            # 检查尾随空格
            if line.rstrip() != line:
                diagnostic = Diagnostic(
                    file_path=file_path,
                    line_start=i,
                    line_end=i,
                    column_start=len(line.rstrip()) + 1,
                    column_end=len(line),
                    severity=DiagnosticSeverity.INFO,
                    message="Trailing whitespace",
                    code="trailing-whitespace",
                    source="quality",
                )
                diagnostics.append(diagnostic)

        return diagnostics


class DiagnosticsTool:
    """主要的诊断工具类"""

    def __init__(self, min_severity: DiagnosticSeverity = DiagnosticSeverity.INFO):
        self.import_checker = ImportChecker()
        self.checkers = [self.import_checker, VariableChecker()]
        self.min_severity = min_severity

        # 严重程度排序（用于过滤）
        self.severity_order = {
            DiagnosticSeverity.HINT: 0,
            DiagnosticSeverity.INFO: 1,
            DiagnosticSeverity.WARNING: 2,
            DiagnosticSeverity.ERROR: 3,
        }

    def get_diagnostics(self, file_paths: list[str]) -> list[Diagnostic]:
        """获取指定文件的诊断信息"""
        all_diagnostics = []

        for file_path in file_paths:
            if not os.path.exists(file_path):
                diagnostic = Diagnostic(
                    file_path=file_path,
                    line_start=1,
                    line_end=1,
                    column_start=1,
                    column_end=1,
                    severity=DiagnosticSeverity.ERROR,
                    message=f"File not found: {file_path}",
                    code="file-not-found",
                    source="system",
                )
                all_diagnostics.append(diagnostic)
                continue

            try:
                with open(file_path, encoding="utf-8") as f:
                    content = f.read()
            except Exception as e:
                diagnostic = Diagnostic(
                    file_path=file_path,
                    line_start=1,
                    line_end=1,
                    column_start=1,
                    column_end=1,
                    severity=DiagnosticSeverity.ERROR,
                    message=f"Cannot read file: {str(e)}",
                    code="file-read-error",
                    source="system",
                )
                all_diagnostics.append(diagnostic)
                continue

            # 首先进行语法检查
            try:
                tree = ast.parse(content, filename=file_path)
            except SyntaxError as e:
                diagnostic = Diagnostic(
                    file_path=file_path,
                    line_start=e.lineno or 1,
                    line_end=e.lineno or 1,
                    column_start=e.offset or 1,
                    column_end=(e.offset or 1) + 1,
                    severity=DiagnosticSeverity.ERROR,
                    message=f"Syntax error: {e.msg}",
                    code="syntax-error",
                    source="syntax",
                )
                all_diagnostics.append(diagnostic)
                continue  # 语法错误，跳过其他检查
            except Exception as e:
                diagnostic = Diagnostic(
                    file_path=file_path,
                    line_start=1,
                    line_end=1,
                    column_start=1,
                    column_end=1,
                    severity=DiagnosticSeverity.ERROR,
                    message=f"Parse error: {str(e)}",
                    code="parse-error",
                    source="syntax",
                )
                all_diagnostics.append(diagnostic)
                continue

            # 运行所有检查器
            for checker in self.checkers:
                try:
                    diagnostics = checker.check(file_path, content, tree)
                    all_diagnostics.extend(diagnostics)
                except Exception as e:
                    # 如果某个检查器失败，记录错误但继续其他检查
                    diagnostic = Diagnostic(
                        file_path=file_path,
                        line_start=1,
                        line_end=1,
                        column_start=1,
                        column_end=1,
                        severity=DiagnosticSeverity.ERROR,
                        message=f"Checker error ({checker.__class__.__name__}): {str(e)}",
                        code="checker-error",
                        source="system",
                    )
                    all_diagnostics.append(diagnostic)

        # 根据最小严重程度过滤结果
        filtered_diagnostics = []
        for diag in all_diagnostics:
            if self.severity_order[diag.severity] >= self.severity_order[self.min_severity]:
                filtered_diagnostics.append(diag)

        return filtered_diagnostics

    def format_diagnostics(self, diagnostics: list[Diagnostic], show_context: bool = True) -> str:
        """格式化诊断结果为可读字符串"""
        if not diagnostics:
            return "No diagnostics found."

        # 按文件分组
        by_file = {}
        for diag in diagnostics:
            if diag.file_path not in by_file:
                by_file[diag.file_path] = []
            by_file[diag.file_path].append(diag)

        output = []
        for file_path, file_diagnostics in by_file.items():
            output.append(f"{file_path}")

            # 按行号排序
            file_diagnostics.sort(key=lambda d: (d.line_start, d.column_start))

            for diag in file_diagnostics:
                # 格式化行号范围
                if diag.line_start == diag.line_end:
                    line_range = f"L{diag.line_start}-{diag.line_start}"
                else:
                    line_range = f"L{diag.line_start}-{diag.line_end}"

                # 格式化严重程度图标
                severity_icon = {
                    DiagnosticSeverity.ERROR: "❌",
                    DiagnosticSeverity.WARNING: "⚠️",
                    DiagnosticSeverity.INFO: "ℹ️",
                    DiagnosticSeverity.HINT: "💡",
                }.get(diag.severity, "•")

                output.append(f"{line_range}: {severity_icon} {diag.message}")

                # 显示上下文代码
                if show_context:
                    context = self._get_context_lines(file_path, diag.line_start, 2)
                    for line_num, line_content in context:
                        marker = ">" if line_num == diag.line_start else " "
                        output.append(f"    {marker}{line_num:3d}│{line_content}")

                        # 在错误行下方显示指示符
                        if line_num == diag.line_start and diag.column_start > 0:
                            pointer_line = " " * (diag.column_start - 1) + "^"
                            if diag.column_end > diag.column_start:
                                pointer_line += "~" * (diag.column_end - diag.column_start - 1)
                            output.append(f"    {' ':>3}│{pointer_line}")

                output.append("")  # 空行分隔

        return "\n".join(output)

    def _get_context_lines(self, file_path: str, target_line: int, context_size: int = 2) -> list[tuple[int, str]]:
        """获取指定行的上下文"""
        try:
            with open(file_path, encoding="utf-8") as f:
                lines = f.readlines()
        except Exception:
            return []

        start = max(0, target_line - context_size - 1)
        end = min(len(lines), target_line + context_size)

        context = []
        for i in range(start, end):
            line_num = i + 1
            line_content = lines[i].rstrip()
            context.append((line_num, line_content))

        return context

    def run_diagnostics(self, file_paths: list[str], output_format: str = "text") -> str:
        """运行诊断并返回格式化结果（已弃用，建议直接使用 get_diagnostics + format_diagnostics）"""
        diagnostics = self.get_diagnostics(file_paths)

        if output_format == "text":
            return self.format_diagnostics(diagnostics)
        elif output_format == "json":
            import json

            return json.dumps(
                [
                    {
                        "file": d.file_path,
                        "line_start": d.line_start,
                        "line_end": d.line_end,
                        "column_start": d.column_start,
                        "column_end": d.column_end,
                        "severity": d.severity.value,
                        "message": d.message,
                        "code": d.code,
                        "source": d.source,
                    }
                    for d in diagnostics
                ],
                indent=2,
                ensure_ascii=False,
            )
        else:
            raise ValueError(f"不支持的输出格式: {output_format}")


def main():
    """命令行接口"""
    import argparse

    parser = argparse.ArgumentParser(description="Python Code Diagnostics Tool")
    parser.add_argument("files", nargs="+", help="Python files to check")
    parser.add_argument("--format", choices=["text", "json"], default="text", help="Output format")
    parser.add_argument("--no-context", action="store_true", help="Don't show code context")
    parser.add_argument(
        "--severity",
        "-s",
        choices=["error", "warning", "info", "hint"],
        default="info",
        help="Minimum severity level to show",
    )

    args = parser.parse_args()

    # 转换严重程度参数
    severity_map = {
        "error": DiagnosticSeverity.ERROR,
        "warning": DiagnosticSeverity.WARNING,
        "info": DiagnosticSeverity.INFO,
        "hint": DiagnosticSeverity.HINT,
    }
    min_severity = severity_map[args.severity]

    tool = DiagnosticsTool(min_severity=min_severity)

    try:
        # 只调用一次 get_diagnostics
        diagnostics = tool.get_diagnostics(args.files)

        if args.format == "text":
            result = tool.format_diagnostics(diagnostics, show_context=not args.no_context)
        else:
            import json

            result = json.dumps(
                [
                    {
                        "file": d.file_path,
                        "line_start": d.line_start,
                        "line_end": d.line_end,
                        "column_start": d.column_start,
                        "column_end": d.column_end,
                        "severity": d.severity.value,
                        "message": d.message,
                        "code": d.code,
                        "source": d.source,
                    }
                    for d in diagnostics
                ],
                indent=2,
                ensure_ascii=False,
            )

        print(result)

        # 如果有错误，返回非零退出码
        has_errors = any(d.severity == DiagnosticSeverity.ERROR for d in diagnostics)
        sys.exit(1 if has_errors else 0)

    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
