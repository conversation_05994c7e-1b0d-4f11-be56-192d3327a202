from dotenv import load_dotenv

load_dotenv()

import os
import re

from camel.agents import ChatAgent
from camel.models import BaseModelBackend, ModelFactory
from camel.toolkits.base import BaseToolkit
from camel.toolkits.function_tool import FunctionTool
from camel.types import ModelPlatformType
from loguru import logger

PROMPT_TEMPLATE = """
参考以下说明和示例，通过 manim 库为当前分镜生成 manim 代码。

<function_reference>
<function>
<name>
render_media_display
</name>
<description>
生成用于显示媒体内容的Manim动画代码。

该函数接收一个图片文件路径列表，并生成相应的Manim动画。

Args:
    images(List[str]): 要在动画中显示的图片文件路径列表，如果输入的分镜没有图片，则传入空列表，不要自行编造不存在的图片地址，也不要传入占位符
Returns:
    list:
</description>
</function>

<function>
<name>
render_keywords
</name>
<description>
生成关键词垂直排列的动画效果。

为每个关键词创建带有背景的文本对象，并将它们垂直排列显示。
第一个关键词位于画面中心偏上的位置，后续关键词垂直向下排列。

Args:
    keywords: list[str]: 要显示的关键词列表

Returns:
    animations: list: 包含动画列表的元组:
</description>
</function>
</function_reference>

<example>
from manim import *

from manim_funcs.anim_funcs import *
from utils.edgetts_service import EdgeTTSService

config.pixel_height = 1920
config.pixel_width = 1090
config.frame_height = 16.0
config.frame_width = 9.0


class GeneratedAnimation(FeynmanScene):
    def construct(self):
        self.set_speech_service(
            EdgeTTSService(global_speed=1.2, voice="zh-CN-YunxiNeural"),
            create_subcaption=True,
        )

        with self.voiceover("<content>") as tracker:
            # tracker.duration
            self.display_slogo("<title>", step=<step>)
            pre_mobjects = self.mobjects
            anim1 = self.render_keywords(["局限性", "特定任务", "动作集稀疏", "GAIA任务成本", "OpenAI模型"]
            anim2 = self.render_media_display(
                [
                    "pdf_output/2411.01747v1_artifacts/page_3_image_0.png",
                    "pdf_output/2411.01747v1_artifacts/page_7_image_6.png",
                ]
            )

            anims1 = anim1 if anim1 else []
            anims2 = anim2 if anim2 else []

            len1 = len(anims1)
            len2 = len(anims2)
            max_len = max(len1, len2)

            for i in range(max_len):
                kw_anim = anims1[i] if i < len1 else None
                media_item = anims2[i] if i < len2 else None

                if kw_anim and media_item:
                    if media_item["obj_type"] == "Video":
                        self.add(media_item["obj"])
                        self.play(kw_anim)
                    else:
                        self.play(media_item["obj"], kw_anim)
                elif kw_anim:
                    self.play(kw_anim)
                elif media_item:
                    if media_item["obj_type"] == "Video":
                        self.add(media_item["obj"])
                    else:
                        self.play(media_item["obj"])

        self.scene_clear_display(pre_mobjects)
</example>

<step>
{step}
</step>

<title>
{title}
</title>

<content>
{content}
</content>

<storyboard>
{storyboard}
</storyboard>

注意：
1. 生成的代码中必须包括config相关的设置，以及display_slogo的调用
2. 根据视频描述，判断应该调用哪些工具函数，以及需要传入的参数
3. 从FeynmanScene继承，并重写construct方法
"""


class GenManimVideoToolkit(BaseToolkit):
    def generate_manim_code(self, title: str, content: str, storyboard: str, step: int = 1) -> str:
        r"""根据给定的分镜描述提示生成视频

        Args:
            title (str): 视频的标题
            content (str): 视频的介绍文案
            storyboard (str): 要生成的内容的描述。
            step (int): 当前分镜的序号，用于命名输出视频文件
        """
        model: BaseModelBackend = ModelFactory.create(
            model_platform=ModelPlatformType.OPENROUTER,
            model_type="deepseek/deepseek-chat:free",
            api_key=os.getenv("OPENAI_COMPATIBILIY_API_KEY"),
            url=os.getenv("OPENAI_COMPATIBILIY_API_BASE_URL"),
        )
        agent = ChatAgent(model=model)
        prompt = PROMPT_TEMPLATE.format(step=step, title=title, content=content, storyboard=storyboard)
        response = agent.step(prompt)
        response_content = response.msgs[0].content
        # extract python code from response_content, which starts with ```python or ```py and ends with ```
        code = re.findall(r"```(?:python|py)(.*)```", response_content, re.DOTALL)
        if len(code) == 0:
            logger.error(f"No code found in the response: {response_content}")
            return None
        with open(f"render_storyboard_{step}.py", "w") as f:
            f.write(code[0])
        return f"render_storyboard_{step}.py"

    def get_tools(self) -> list[FunctionTool]:
        return [
            FunctionTool(self.generate_manim_code),
        ]


if __name__ == "__main__":
    toolkit = GenManimVideoToolkit()
    storyboard = """{
    "分镜名": "背景阐述：LLM代理的挑战",
    "内容要点": "LLM代理的背景，固定动作集的局限性，三个主要缺点",
    "讲解文案": "自主代理在AI研究中扮演着越来越重要的角色。LLM凭借其强大的推理能力和丰富的世界知识，为解决复杂现实世界问题提供了新的可能性。然而，现有的LLM代理系统通常依赖于固定和预定义的动作集。这种方法存在三个主要缺点：首先，它限制了代理的灵活性，使其无法执行预定义范围之外的动作。其次，需要大量的人工工作来枚举和实现所有可能的动作，这在复杂环境中变得不切实际。第三，它限制了代理从过去经验中学习的能力，因为代理无法动态地创建和组合新的动作。",
    "素材名": "pdf_output/2411.01747v1_artifacts/page_3_image_0.png",
    "视觉动效建议": "使用图表或动画，展示固定动作集的局限性。先展示图片，再展示关键词"
}"""
    toolkit.generate_manim_code(
        "LLM代理的挑战",
        "自主代理在AI研究中扮演着越来越重要的角色。LLM凭借其强大的推理能力和丰富的世界知识，为解决复杂现实世界问题提供了新的可能性。然而，现有的LLM代理系统通常依赖于固定和预定义的动作集。这种方法存在三个主要缺点：首先，它限制了代理的灵活性，使其无法执行预定义范围之外的动作。其次，需要大量的人工工作来枚举和实现所有可能的动作，这在复杂环境中变得不切实际。第三，它限制了代理从过去经验中学习的能力，因为代理无法动态地创建和组合新的动作。",
        storyboard,
        step=1,
    )
