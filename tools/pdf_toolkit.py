import json
import os
import sys
import urllib.parse
import urllib.request
from pathlib import Path
from typing import Optional

from camel.toolkits.base import BaseToolkit
from camel.toolkits.function_tool import FunctionTool
from camel.utils import dependencies_required
from loguru import logger

# Handle relative import issue when running as script
try:
    from .markdown_exporter import export_to_markdown  # noqa: F401
except ImportError:
    pass


class PDFToolkit(BaseToolkit):
    r"""A toolkit for extracting content from PDF files, converting them to
    markdown and extracting images.
    """

    @dependencies_required("docling")
    def __init__(self, timeout: Optional[float] = None, image_filter_config: Optional[dict] = None) -> None:
        r"""Initializes the PDFToolkit.

        Args:
            timeout: Request timeout
            image_filter_config: Configuration for image filtering (simplified version)
                - min_size: Minimum image size (width, height), default (50, 50)
                - max_aspect_ratio: Maximum aspect ratio, default 20
                - filter_low_resolution: Whether to filter low resolution images, default True
                - min_resolution: Minimum resolution (width or height), default 100
                - filter_logo: Whether to filter logo images, default True
                - logo_max_size: Maximum size of logo images, default (150, 150)
                - logo_position_threshold: Logo position detection threshold (top 30% of page), default 0.3
        """
        super().__init__(timeout=timeout)
        self.image_resolution_scale = 5.0

        # 设置默认的图片过滤配置
        default_filter_config = {
            "min_size": (100, 100),  # 最小图片尺寸，过滤太小的图
            "max_aspect_ratio": 20,  # 最大长宽比，过滤异常长宽比的图
            "filter_low_resolution": True,  # 是否过滤低分辨率图片
            "min_resolution": 120,  # 最小分辨率（宽度或高度），过滤低分辨率图片
            "filter_logo": True,  # 是否过滤logo图片
            "logo_max_size": (150, 150),  # logo的最大尺寸阈值
            "logo_position_threshold": 0.3,  # logo位置检测阈值（页面上部30%区域）
        }

        # 用户自定义配置覆盖默认配置
        if image_filter_config:
            default_filter_config.update(image_filter_config)

        self.filter_config = default_filter_config

    def _extract_project_name_from_url(self, url: str) -> str:
        r"""Extracts project name from URL.

        Args:
            url (str): The URL to extract project name from.

        Returns:
            str: The extracted project name.
        """
        try:
            # Parse the URL
            parsed_url = urllib.parse.urlparse(url)

            # Handle different URL patterns
            if "arxiv.org" in parsed_url.netloc:
                # For arxiv URLs like https://arxiv.org/pdf/2503.10628 or https://arxiv.org/pdf/2504.07491v2
                path_parts = parsed_url.path.strip("/").split("/")
                if len(path_parts) >= 2 and path_parts[0] == "pdf":
                    project_name = path_parts[1]
                    # Remove .pdf extension if present
                    if project_name.endswith(".pdf"):
                        project_name = project_name[:-4]

                    # Remove version suffix (v1, v2, etc.) from arxiv IDs to get base ID
                    # Pattern: YYYY.NNNNN[vN] -> YYYY.NNNNN
                    import re

                    if re.match(r"^\d{4}\.\d{5}v\d+$", project_name):
                        # Remove version suffix
                        project_name = re.sub(r"v\d+$", "", project_name)
                        logger.info(f"Removed version suffix from arxiv ID, using base ID: {project_name}")

                    return project_name
                elif len(path_parts) >= 2 and path_parts[0] == "abs":
                    # Handle abstract URLs like https://arxiv.org/abs/2503.10628
                    project_name = path_parts[1]

                    # Remove version suffix from abstract URLs too
                    import re

                    if re.match(r"^\d{4}\.\d{5}v\d+$", project_name):
                        project_name = re.sub(r"v\d+$", "", project_name)
                        logger.info(f"Removed version suffix from arxiv abstract ID, using base ID: {project_name}")

                    return project_name
                else:
                    # Fallback: use the last part of the path
                    return path_parts[-1] if path_parts else "unknown"
            else:
                # For other URLs, extract filename from path
                path = parsed_url.path
                filename = os.path.basename(path)
                if filename:
                    # Remove extension
                    name, _ = os.path.splitext(filename)
                    return name if name else "unknown"
                else:
                    # Use domain name as fallback
                    return parsed_url.netloc.replace(".", "_")

        except Exception as e:
            logger.warning(f"Failed to extract project name from URL {url}: {e}")
            return "unknown"

    def _download_pdf_file(self, pdf_url: str, output_dir: str, project_name: str) -> str:
        r"""Downloads PDF file from URL and saves it locally.

        Args:
            pdf_url (str): URL of the PDF file.
            output_dir (str): Directory to save the PDF file.
            project_name (str): Name of the project.

        Returns:
            str: Path to the downloaded PDF file.
        """
        try:
            # Create output directory if it doesn't exist
            os.makedirs(output_dir, exist_ok=True)

            # Generate local PDF file path
            pdf_filename = f"{project_name}.pdf"
            local_pdf_path = os.path.join(output_dir, pdf_filename)

            # Skip download if file already exists
            if os.path.exists(local_pdf_path):
                logger.info(f"PDF file already exists: {local_pdf_path}")
                return local_pdf_path

            # Download the PDF file
            logger.info(f"Downloading PDF from {pdf_url}...")
            urllib.request.urlretrieve(pdf_url, local_pdf_path)
            logger.info(f"PDF downloaded successfully to {local_pdf_path}")

            return local_pdf_path

        except Exception as e:
            logger.error(f"Failed to download PDF from {pdf_url}: {e}")
            # Return the original URL as fallback
            return pdf_url

    def _parse_pdf_to_markdown(
        self, input_doc_path: str, output_dir: str, project_name: str = None, max_num_pages: int = sys.maxsize
    ) -> str:
        r"""Parses a PDF file to markdown and extracts images.

        Args:
            input_doc_path (str): Path to the PDF file, or a URL.
            output_dir (str): Directory to save the extracted content.
            project_name (str): Project name to use for file naming (optional).

        Returns:
            str: Path to the JSON file containing image information.
        """
        import time

        from docling.datamodel.base_models import InputFormat
        from docling.datamodel.pipeline_options import PdfPipelineOptions
        from docling.document_converter import (
            DocumentConverter,
            PdfFormatOption,
        )

        # Create output directory if it doesn't exist
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        # Configure pipeline options
        pipeline_options = PdfPipelineOptions()
        pipeline_options.images_scale = self.image_resolution_scale
        pipeline_options.generate_page_images = True
        pipeline_options.generate_picture_images = True

        # Initialize document converter
        doc_converter = DocumentConverter(
            format_options={InputFormat.PDF: PdfFormatOption(pipeline_options=pipeline_options)},
        )

        start_time = time.time()

        logger.info(f"Starting PDF conversion: {input_doc_path}, max_num_pages: {max_num_pages}")
        conv_res = doc_converter.convert(input_doc_path, page_range=(1, max_num_pages))
        logger.info("PDF conversion completed")

        # Use provided project_name if available, otherwise fall back to file stem
        if project_name:
            doc_filename = project_name
        else:
            doc_filename = conv_res.input.file.stem

        # Save images and their captions
        image_info_file = self._save_image_path_and_caption(conv_res.document, doc_filename, output_path)

        # Create a complete markdown file from PDF content
        markdown_content = self._create_basic_markdown(conv_res.document, doc_filename, output_path)
        markdown_file = output_path / f"{doc_filename}.md"
        with open(markdown_file, "w", encoding="utf-8") as f:
            f.write(markdown_content)

        end_time = time.time() - start_time
        logger.info(f"Document converted and figures exported in {end_time:.2f} seconds.")

        return image_info_file, markdown_file

    def _save_image_path_and_caption(self, document, doc_filename: str, output_dir: Path) -> str:
        r"""Saves images from the document and their captions.

        Args:
            document: The DoclingDocument object.
            doc_filename (str): The filename of the document.
            output_dir (Path): Directory to save the images.

        Returns:
            str: Path to the JSON file containing image information.
        """
        from docling_core.types.doc import PictureItem, TableItem

        # Create medias directory (changed from artifacts)
        medias_dir = output_dir / "medias"
        medias_dir.mkdir(parents=True, exist_ok=True)

        img_count = 0  # 只用于保存的图片命名
        total_processed = 0  # 用于调试标识符的总计数器
        image_info = []
        filtered_count = 0

        # Extract and save images
        for item, level in document.iterate_items(with_groups=False):
            if isinstance(item, (PictureItem, TableItem)):
                if item.image is None:
                    item.image = item.get_image(document)
                    image = item.image
                else:
                    image = item.image.pil_image

                total_processed += 1  # 每处理一个图片就递增

                # 图片过滤逻辑，使用total_processed作为调试计数器
                should_filter = self._should_filter_image(item, image, document, total_processed)
                if should_filter:
                    filtered_count += 1
                    logger.debug(
                        f"Filtered out image on page {item.prov[0].page_no}: likely decorative or background image"
                    )
                    # 确保被过滤的图片不设置uri，不会在markdown中出现
                    if hasattr(item, "image") and item.image:
                        item.image.uri = None
                    continue

                # 只有未被过滤的图片才会执行到这里
                page_no = item.prov[0].page_no
                prefix = "image" if isinstance(item, PictureItem) else "table"
                filename = f"page_{page_no}_{prefix}_{img_count}.png"  # 使用img_count确保保存的文件名连续
                loc_path = medias_dir / filename

                # 计算相对于markdown文件的相对路径
                relative_path = f"medias/{filename}"

                # 双重检查：保存前再次确认不应该过滤（防御性编程）
                if self._should_filter_image(item, image, document, total_processed):
                    logger.warning(
                        f"Double-check detected: Image should be filtered but reached save point! Skipping save. Page {page_no}, size: {image.width}x{image.height}"
                    )
                    filtered_count += 1
                    continue

                # 记录将要保存的图片信息
                logger.debug(f"Saving image: {filename}, size: {image.width}x{image.height}, page: {page_no}")

                try:
                    image.save(loc_path)
                    # 设置uri为相对路径，用于markdown生成
                    item.image.uri = relative_path
                    logger.debug(f"Successfully saved image: {filename}")
                except Exception as e:
                    logger.error(f"Failed to save image {filename}: {e}")
                    continue

                caption = item.caption_text(document)
                image_info.append(
                    {
                        "path": str(loc_path),  # 绝对路径，用于文件操作
                        "relative_path": relative_path,  # 相对路径，用于markdown
                        "caption": caption,
                        "type": "image" if isinstance(item, PictureItem) else "table",
                        "page": page_no,
                        "filename": filename,
                    }
                )

                img_count += 1  # 只有成功保存的图片才递增

        # Save image information to JSON file
        image_info_file = output_dir / f"{doc_filename}_images.json"
        with open(image_info_file, "w") as f:
            json.dump(image_info, f, indent=4, ensure_ascii=False)

        # 验证medias文件夹中的图片数量与预期一致
        saved_files = list(medias_dir.glob("*.png")) if medias_dir.exists() else []
        if len(saved_files) != img_count:
            logger.warning(
                f"Inconsistency detected: Expected {img_count} images in medias folder, but found {len(saved_files)} files"
            )
            logger.warning(f"Files in medias folder: {[f.name for f in saved_files]}")
        else:
            logger.debug(f"Verification passed: {img_count} images saved correctly to medias folder")

        logger.info(f"Saved {img_count} images info in {image_info_file}")
        if filtered_count > 0:
            logger.info(
                f"Filtered out {filtered_count} decorative/background images (processed {total_processed} total images)"
            )
        return str(image_info_file)

    def _should_filter_image(self, item, image, document=None, debug_counter=0) -> bool:
        r"""判断是否应该过滤掉该图片（简化版：只过滤明显无内容含义的图）

        核心过滤条件：
        1. Logo图片（小且方正，通常在页面顶部）
        2. 太小的图片（明显没有实际内容）
        3. 低分辨率图片（质量太差）
        4. 长宽比太夸张的图片（装饰线条等）

        Args:
            item: DoclingDocument中的图片项
            image: PIL图片对象
            document: 文档对象
            debug_counter: 调试计数器，用于生成唯一的调试标识符

        Returns:
            bool: True表示应该过滤掉，False表示保留
        """
        try:
            # 生成调试用的图片标识（使用debug_counter确保唯一性）
            page_no = item.prov[0].page_no if hasattr(item, "prov") and item.prov else 0
            from docling_core.types.doc import PictureItem

            prefix = "image" if isinstance(item, PictureItem) else "table"
            img_identifier = f"page_{page_no}_{prefix}_debug{debug_counter}({image.width}x{image.height})"

            # 1. 太小的图片过滤
            min_width, min_height = self.filter_config.get("min_size", (50, 50))
            if image.width < min_width or image.height < min_height:
                logger.debug(f"Filtering too small image: {image.width}x{image.height} ({img_identifier})")
                return True

            # 2. 低分辨率图片过滤
            if self.filter_config.get("filter_low_resolution", True):
                min_resolution = self.filter_config.get("min_resolution", 100)
                if image.width < min_resolution or image.height < min_resolution:
                    logger.debug(
                        f"Filtering low resolution image: {image.width}x{image.height} < {min_resolution} ({img_identifier})"
                    )
                    return True

            # 3. 长宽比过滤：过滤异常长宽比的图片（装饰线条等）
            max_aspect_ratio = self.filter_config.get("max_aspect_ratio", 20)
            aspect_ratio = max(image.width / image.height, image.height / image.width)
            if aspect_ratio > max_aspect_ratio:
                logger.debug(f"Filtering extreme aspect ratio image: {aspect_ratio:.1f} ({img_identifier})")
                return True

            # 4. Logo图片检测和过滤
            if self.filter_config.get("filter_logo", True):
                logo_max_width, logo_max_height = self.filter_config.get("logo_max_size", (150, 150))
                logo_position_threshold = self.filter_config.get("logo_position_threshold", 0.3)

                # 检查是否符合logo特征（小且相对方正）
                is_small_size = image.width <= logo_max_width and image.height <= logo_max_height
                is_square_ish = abs(image.width - image.height) / max(image.width, image.height) < 0.6  # 长宽比不太极端

                if is_small_size and is_square_ish:
                    # 检查位置是否在页面顶部（logo通常在页面顶部）
                    if hasattr(item, "prov") and item.prov and len(item.prov) > 0:
                        prov = item.prov[0]
                        if hasattr(prov, "bbox") and prov.bbox:
                            bbox = prov.bbox
                            page_height = getattr(prov, "page_height", 800)  # 默认页面高度
                            relative_position = bbox.t / page_height

                            # 如果在页面顶部区域的小方图片，很可能是logo
                            if relative_position < logo_position_threshold:
                                logger.debug(
                                    f"Filtering potential logo: size={image.width}x{image.height}, position={relative_position:.2f} ({img_identifier})"
                                )
                                return True

                    # 第一页的小方图片更容易是logo
                    if page_no <= 1:
                        logger.debug(
                            f"Filtering potential first page logo: {image.width}x{image.height} ({img_identifier})"
                        )
                        return True

            # 所有过滤条件都不满足，保留图片
            return False

        except Exception as e:
            logger.warning(f"Error in image filtering: {e}")
            # 出错时默认保留图片
            return False

    def _create_basic_markdown(self, document, doc_filename: str, output_dir: Path) -> str:
        r"""Creates a complete markdown file from PDF content.

        Args:
            document: The DoclingDocument object.
            doc_filename (str): The filename of the document.
            output_dir (Path): Directory to save the markdown file.

        Returns:
            str: The created markdown content.
        """
        from docling_core.types.doc import PictureItem, SectionHeaderItem, TableItem, TextItem

        markdown_content = f"# {doc_filename}\n\n"

        # Iterate through all document items to create a complete markdown
        for item, level in document.iterate_items():
            # Handle titles/headings
            if isinstance(item, SectionHeaderItem):
                # Convert level to markdown heading level (add 1 since we used # for document title)
                heading_level = min(level + 2, 6)  # Limit to h6
                markdown_content += f"{'#' * heading_level} {item.text}\n\n"

            # Handle regular text
            elif isinstance(item, TextItem):
                text = item.text.strip()
                if text:
                    markdown_content += f"{text}\n\n"

            # Handle images
            elif isinstance(item, PictureItem):
                caption = item.caption_text(document)
                if item.image and item.image.uri:
                    if caption:
                        markdown_content += f"![{caption}]({item.image.uri})\n\n"
                        markdown_content += f"*{caption}*\n\n"
                    else:
                        markdown_content += f"![Image]({item.image.uri})\n\n"

            # Handle tables
            elif isinstance(item, TableItem):
                caption = item.caption_text(document)

                # Add table image if available
                if item.image and item.image.uri:
                    if caption:
                        markdown_content += f"![{caption}]({item.image.uri})\n\n"
                        markdown_content += f"*{caption}*\n\n"
                    else:
                        markdown_content += f"![Table]({item.image.uri})\n\n"

                # Also try to add table data if available
                if hasattr(item, "data") and item.data and hasattr(item.data, "grid"):
                    try:
                        rows = item.data.grid
                        if len(rows) > 1 and len(rows[0]) > 0:
                            # Create markdown table
                            # Headers
                            headers = [
                                cell.text.replace("\n", " ") if hasattr(cell, "text") else str(cell) for cell in rows[0]
                            ]
                            markdown_content += "| " + " | ".join(headers) + " |\n"
                            markdown_content += "| " + " | ".join(["---"] * len(headers)) + " |\n"

                            # Data rows
                            for row in rows[1:]:
                                cells = [
                                    cell.text.replace("\n", " ") if hasattr(cell, "text") else str(cell) for cell in row
                                ]
                                markdown_content += "| " + " | ".join(cells) + " |\n"
                            markdown_content += "\n"
                    except Exception as e:
                        logger.warning(f"Failed to convert table to markdown: {e}")

        return markdown_content

    def extract_pdf(
        self, pdf_path: str, output_base_dir: str = "output", max_num_pages: int = sys.maxsize
    ) -> dict[str, list[dict[str, str]]]:
        r"""Extracts content from a PDF file, including text and images.

        Args:
            pdf_path (str): Path to the PDF file, or a URL.
            output_base_dir (str): Base directory to save the extracted content (default: "output").

        Returns:
            Dict[str, List[Dict[str, str]]]: A dictionary containing the extracted
                images information with their captions.
        """
        try:
            # Extract project name from URL or file path
            if pdf_path.startswith(("http://", "https://")):
                project_name = self._extract_project_name_from_url(pdf_path)
                is_url = True
            else:
                # For local files, use filename without extension
                project_name = os.path.splitext(os.path.basename(pdf_path))[0]
                is_url = False

            # Create project-specific output directory: output/项目名/
            project_output_dir = os.path.join(output_base_dir, project_name)
            os.makedirs(project_output_dir, exist_ok=True)

            logger.info(f"Project name: {project_name}")
            logger.info(f"Output directory: {project_output_dir}")

            # Download PDF file if it's a URL
            if is_url:
                local_pdf_path = self._download_pdf_file(pdf_path, project_output_dir, project_name)
            else:
                local_pdf_path = pdf_path

            # Parse PDF to markdown and extract images
            image_info_file, markdown_file = self._parse_pdf_to_markdown(
                local_pdf_path, project_output_dir, project_name, max_num_pages
            )

            # Load image information
            with open(image_info_file) as f:
                image_info = json.load(f)

            # Return extracted content
            return {
                "pdf_path": pdf_path,
                "local_pdf_path": local_pdf_path if is_url else pdf_path,
                "project_name": project_name,
                "output_dir": project_output_dir,
                "image_info_file": image_info_file,
                "markdown_file": str(markdown_file),
                "medias_dir": os.path.join(project_output_dir, "medias"),
                "images": image_info,
                "total_images": len(image_info),
                "note": "图片路径在markdown中使用相对路径 (medias/filename.png)，方便文件移动和分享",
            }

        except Exception as e:
            logger.error(f"Error extracting PDF content: {e}")
            import traceback

            logger.error(traceback.format_exc())
            # Try to extract project name even on error for better error reporting
            try:
                if pdf_path.startswith(("http://", "https://")):
                    project_name = self._extract_project_name_from_url(pdf_path)
                else:
                    project_name = os.path.splitext(os.path.basename(pdf_path))[0]
                project_output_dir = os.path.join(output_base_dir, project_name)
            except Exception:
                project_name = "unknown"
                project_output_dir = output_base_dir

            return {
                "pdf_path": pdf_path,
                "project_name": project_name,
                "output_dir": project_output_dir,
                "error": str(e),
                "images": [],
                "markdown_file": None,
                "total_images": 0,
            }

    def get_tools(self) -> list[FunctionTool]:
        r"""Returns a list of FunctionTool objects representing the
        functions in the toolkit.

        Returns:
            List[FunctionTool]: A list of FunctionTool objects
                representing the functions in the toolkit.
        """
        return [
            FunctionTool(self.extract_pdf),
        ]


if __name__ == "__main__":
    print("=== 测试PDF工具包的图片过滤功能 ===\n")

    # 测试1: 默认过滤设置
    print("1. 使用默认过滤设置:")
    pdf_toolkit = PDFToolkit()
    result = pdf_toolkit.extract_pdf("https://arxiv.org/pdf/2104.09864", max_num_pages=9)
    print(f"   项目名称: {result.get('project_name', 'unknown')}")
    print(f"   提取的图片数量: {result.get('total_images', 0)}")
