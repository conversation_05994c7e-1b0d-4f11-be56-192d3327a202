from typing import Optional

from camel.toolkits.base import BaseToolkit
from camel.toolkits.function_tool import FunctionTool


class RenderMediaToolkit(BaseToolkit):
    r"""是一个用于生成 Manim 动画代码的工具类，专门用于处理和显示多种媒体文件（图片、视频、GIF）。
    该工具包为上层应用提供了简单的接口，使其能够轻松地将媒体内容集成到 Manim 动画中
    """

    def __init__(self, timeout: Optional[float] = None) -> None:
        r"""多种媒体文件（图片、视频、GIF）展示工具"""
        super().__init__(timeout=timeout)

    def generate_media_display_code(self, images: list[str], step: int = 1) -> list[str]:
        """生成用于显示媒体内容的Manim动画代码。

        该函数接收一个图片文件路径列表，并生成相应的Manim代码行，这些代码行定义了
        一个能够显示这些媒体内容的render_media_tool方法。生成的代码使用
        render_media_display方法来实际处理和显示媒体内容。

        Args:
            images(List[str]): 要在动画中显示的图片文件路径列表
            step (int): 第几个分镜
        Returns:
            List[str]: 包含Manim代码行的列表，这些代码行定义了用于显示媒体的方法
        """
        code_lines = []
        code_lines.append("    def render_media_tool(self) -> list:")
        code_lines.append(f"        return self.render_media_display({images})")
        return code_lines

    def get_tools(self) -> list[FunctionTool]:
        r"""Returns a list of FunctionTool objects representing the
        functions in the toolkit.

        Returns:
            List[FunctionTool]: A list of FunctionTool objects
                representing the functions in the toolkit.
        """
        return [
            FunctionTool(self.generate_media_display_code),
        ]


if __name__ == "__main__":
    render_media_toolkit = RenderMediaToolkit()
    render_media_toolkit.generate_media_display_code(["1.png"])
